<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://xmlns.jcp.org/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:f="http://xmlns.jcp.org/jsf/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">


    <h:head>

        <!--12-03-2024 13327 ESCALATIONS CRM-7879   Instance URLs should not show up in search results-->
        <meta name="robots" content="noindex, nofollow"/>
        <meta http-equiv="Cache-Control" content="no-cache, no-store,must-revalidate"/>
        <meta http-equiv="Pragma" content="no-cache"/>
        <meta http-equiv="Expires" content="0"/>

        <h:outputStylesheet library="theme" name="style.css"/>
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['images/icon.png']}" />
        <!--//22-06-2023 : #11507 : CRM-7035   Repfabric Rebranding: URL Changes-->
        <title>#{fabricClient.clientName} CRM: Login Page</title>
        <meta charset="utf-8"/>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"/>
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
 <!--       <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon-16x16.png']}" sizes="16x16"/>
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon-32x32.png']}" sizes="32x32"/>
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon-96x96.png']}" sizes="96x96"/>-->
        <meta name="theme-color" content="#444"/>
        <meta name="mobile-web-app-capable" content="yes"/>

        <f:metadata>
            <f:viewParam id="logout" name="logout" value="#{loginForm.logoutStatus}"/> 
            <f:viewAction  action="#{loginForm.checkLogoutStatus()}" />

        </f:metadata>

        <script>
            $(document).ready(function () {
                $('#frmLoginFrom\\:uname').focus();
            });

            //#757: CRM-2660: walkme snippets
//            (function () {
//                var walkme = document.createElement('script');
//                walkme.type = 'text/javascript';
//                walkme.async = true;
//                walkme.src = 'https://cdn.walkme.com/users/5b6f9ef3088649ad8d1a1e2818fc87ec/test/walkme_5b6f9ef3088649ad8d1a1e2818fc87ec_https.js';
//                var s = document.getElementsByTagName('script')[0];
//                s.parentNode.insertBefore(walkme, s);
//                window._walkmeConfig = {smartLoad: true};
//            })();
        </script>
    </h:head> 

    <h:body styleClass="hold-transition login-page">
        <!--        <p:growl  sticky="true"/>
                <div id="loader" class="load-bar" style="display: none">
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                </div>
                <script>
                    window.name = "login";
                </script>
        
                <p:dialog id="adminStatusDialog" modal="true" widgetVar="statusDialog" draggable="false" closable="false"
                          resizable="false" responsive="true" showHeader="false" appendTo="@(body)">
                    <p:graphicImage library="images" name="#{adminConfig.loadingImage}"/>
                </p:dialog>
        
                <div class="login-box">
                    <div class="login-logo">
         <p:link href="index.xhtml"><b>Rep</b>fabric</p:link> 
                    </div>
                     /.login-logo 
                    <div class="box login-box-body">
                        <h:form>
                            <p class="login-box-msg">Sign in to start your session</p>
                            <p:messages closable="true"/>
        
                            <div class="form-group has-feedback">
                                <p:inputText value="#{logonMB.email}" type="email" styleClass="form-control" placeholder="Just type any value"
                                             required="true"
                                             requiredMessage="Email is required."/>
                                <i class="fa fa-envelope form-control-feedback" ></i>
                            </div>
                            <div class="form-group has-feedback">
                                <p:inputText value="#{logonMB.password}" type="password" styleClass="form-control"
                                             placeholder="Just type any value" required="true"
                                             requiredMessage="Password is required."/>
                                <i class="fa fa-lock form-control-feedback" style="font-size: 18px"></i>
                            </div>
                            <div class="row">
                                <div class="col-xs-12">
                                    <p:selectBooleanCheckbox itemLabel="Remember Me" value="#{logonMB.remember}"/>
                                </div>
                                <p:spacer height="10"/>
                                <div class="col-xs-12">
                                    <p:commandButton styleClass="btn btn-success btn-block" action="#{logonMB.login}" onclick="showBar()" oncomplete="if(args.validationFailed){hideBar()}"
                                                     value="Sign In" update="@form"/>
                                </div>
                            </div>
                        </h:form>
        
                        <div class="social-auth-links text-center">
                            <p>- OR -</p>
                            <a href="#" class="btn btn-block btn-social btn-facebook btn-flat"><i
                                    class="fa fa-facebook"></i> Sign in using
                                Facebook</a>
                            <a href="#" class="btn btn-block btn-social btn-google btn-flat"><i
                                    class="fa fa-google-plus"></i> Sign in using
                                Google+</a>
                        </div>
                        <a href="#">I forgot my password</a><br/>
                        <a href="#" class="text-center">Register a new membership</a>
                    </div>
                </div>
                
                <h:outputScript library="js" name="admintemplate.js" target="head"/>-->
        <!--<f:view transient="true">-->
        <div class="login-form">
            <div class="box-inner">
                <!--//12-06-2023 : #11375 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
                 <!--12246-->
                <h:graphicImage library="images" name="#{fabricClient.clientType}/logo_bg.png" style="margin-top: -2px; margin-left: -2px; margin-right: -2px; width: 480px;"/>      
                <ui:include src="/config/tabs.xhtml"/>
                <!--#2733 CRM-1729: Fwd: Cosmetic Changes-->
                <h:form id="frmLoginFrom"  style="margin-top:10px"> 
                    <!--Task #7527 Primefaces Up-gradation issue fixes-->
                    <!--                    <p:defaultCommand target="lgn_btn"/>-->
                    <p:remoteCommand name="rc" actionListener="#{loginForm.postLogin()}" autoRun="true"/>
                    <!--#6784: User Onboard: Handle Login to Repfabric using Google/Outlook-->  
                    <p:remoteCommand name="rc1" actionListener="#{loginForm.postLogin()}" autoRun="false" update=":frmLoginFrom"/>
                    <p:growl id="msgs" showDetail="true"/>
                    <table>
                        <tbody>
                            <tr>
                                <td class="title">
                                    <label for="">Login Id</label>
                                </td>
                                <td class="login_input">
                                    <p:inputText value="#{loginForm.userLogin}" id="uname" tabindex="1" 

                                                 label="User login">
                                        <!--17-03-2022 7537 User Onboard: Login Screen: Move Forgot Password outside blue box-->
                                        <f:ajax execute="@form" render=":frmLoginFrom:uname" />
                                    </p:inputText>                                   
                                </td>
                            </tr>
                            <tr>
                                <td class="title">
                                    <label for="">Password</label>
                                </td>
                                <td class="login_input">
                                    <p:password value="#{loginForm.userPassword}" id="pwd"  tabindex="2"/> 
                                </td>
                            </tr>
                            <!--17-03-2022 7537 User Onboard: Login Screen: Move Forgot Password outside blue box[moved from here to bottom]-->
                        </tbody>
                    </table>


                    <p  class="formbuttons">
                        <!--Task #7527 Primefaces Up-gradation issue fixes-->
                        <!--6784 User Onboard: Handle Login to Repfabric using Google/Outlook-->
                        <!--Feature #8450: CRM-5589   Handle Login Failure-->
                        <!--Bug #8464: CRM-5641 #8260: Investigative findings-->
                        <p:commandButton  value="Login" tabindex="3" onclick="PF('stDlg').show();" action="#{loginForm.loginV2(1)}" oncomplete="PF('stDlg').show();window.open('Index.xhtml','login');" update=":frmLoginFrom:msgs :frmLoginFrom" ajax="false" id="lgn_btn"/> 
                    </p>
                    <!--/*Feature #6760: Login Screen: Add options to Sign in using Google/Microsoft*/-->
                    <div class="wrap">
                        <h2 class="centre-line"><span>Or</span></h2>
                    </div>
                    <p class="socialbox">
                        <button class="btnStyle" onclick="window.open('https://centralhost.repfabric.com/rf-auth-dev/user_auth_process?clientId=#{loginForm.paramSubsId}&amp;fn=1&amp;reqType=3', '_self'); rc1();"><a href="#"> 
                                <p:graphicImage  width="20" height="20" library="images" id="imgGoogle" name="googleIcon.png" style="margin-right:5px"/>Sign in With Google
                            </a> 
                        </button>
                        <button class="btnStyle" onclick="window.open('https://centralhost.repfabric.com/rf-auth-dev/user_auth_process?clientId=#{loginForm.paramSubsId}&amp;fn=2&amp;reqType=3', '_self'); rc1();"><a href="#"> 
                                <h:graphicImage width="20" height="20" library="images" id="imgOutlook" style="margin-right:5px" name="microsoft.png"/>Sign in With Outlook
                            </a> 
                        </button>
                    </p>
                    <!--17-03-2022 7537 User Onboard: Login Screen: Move Forgot Password outside blue box-->
                    <p:remoteCommand id="rcForgotPass" name="rcForgotPass"  onstart="PF('stDlg').show()"
                                     actionListener="#{loginForm.forgotPasswordAction()}" update=":frmLoginFrom:msgs" oncomplete="PF('stDlg').hide()" />
                </h:form>
            </div>
            <div class="box-bottom">

            </div>

            <div class="bottomline">
                <!--17-03-2022 7537 User Onboard: Login Screen: Move Forgot Password outside blue box-->
                <h:form id="forgotPass">
                    <div style="margin-top:-20px; margin-bottom: 15px; font-size: 14px;" >
                        <p:commandLink onclick="rcForgotPass();"
                                       tabindex="4"  value="Forgot Password?" style="color:#786e60">
                        </p:commandLink>
                    </div>
                </h:form>
                <!--//12-06-2023 : #11375 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
                <a href="http://#{fabricClient.clientUrl}" target="_blank">#{fabricClient.clientUrl} </a>
            </div>
        </div>

        <!--forgot password dialog-->
        <p:dialog header="Forgot Password" modal="true" widgetVar="forgotPwdDialog" id="pwdDialog"  showEffect="clip" hideEffect="clip">
            <p:ajax event="close" listener="#{loginForm.closeEmailDialog}"/>
            <h:form id="formForgotPwd">
                <p:outputPanel style="width: 330px">
                    Your password will be sent by mail to the registered E-mail Id.
                    If you do not receive the mail, please contact the Admin.
                </p:outputPanel>
                <p:separator/>
                <p:outputPanel style="text-align: center">          
                    <p:commandButton value="Send"  id="btnSubmit" actionListener="#{loginForm.emailDialogAction}"
                                     class="btn btn-primary" onclick="forgotPwdDialog.hide();" update=":frmLoginFrom:msgs"/>
                </p:outputPanel>
            </h:form>
        </p:dialog>

        <!--Bug #8464: CRM-5641 #8260: Investigative findings-->
        <p:dialog    widgetVar="stDlg" closable="false" modal="true"
                     resizable="false" showHeader="false">
            <h:graphicImage  library="images" name="ajax-loader.gif"/>
            <h:outputLabel value=" Loading.."/>
        </p:dialog>
        <!--    </f:view>-->
    </h:body>

    <!--/*Feature #6760: Login Screen: Add options to Sign in using Google/Microsoft*/-->
    <style>
        .wrap {
            height: 30px;
            position: relative;
            margin: 5px;
        }
        h2.centre-line {
            text-align: center;
            position: absolute;
            text-height: 14px;
            font-size: 14px;
            padding: 5px;
            padding-top: 1px;
            left: 50%;
            width: 94%;
            transform: translate(-50%, -50%);
            color: white;
        }
        h2.centre-line:before {
            content: "";
            position: absolute;
            width: 100%;
            height: 1px;
            top: 50%;
            left: 0;
            z-index: -1;
            background: white;

        }
        h2.centre-line span {
            background-color: #123755;
            padding: 0.5rem;
            display: inline-block;
            height: 15px;
        }

        .socialbox{
            padding: 5px;
            width: 100%;
            border: none;
            border-radius: 5px;
            text-align: center;
        }

        .btnStyle{
            padding: 5px;
            width: 45%;
            box-sizing: border-box;
            border: none;
            font-size: 1.3em;
            border-radius: 5px;
            font-weight: bold;
            margin-left: 7px;
            margin-right: 7px;

        }
        button a{
            text-decoration: none;
            color: #000;
            padding-bottom: 5px;
        }
        button:hover{
            background: #0078D7;
        }
        .fa{
            font-size: 1.3em;
            font-weight: bold;
            padding-bottom: 5px;
            padding-top: 2px;
        }
    </style>
</html>