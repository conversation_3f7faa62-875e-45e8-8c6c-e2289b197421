<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://xmlns.jcp.org/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:f="http://xmlns.jcp.org/jsf/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
<!--6784 User Onboard: Handle Login to Repfabric using Google/Outlook-->
    <ui:define name="metadata">
         <f:metadata>
             <f:viewAction action="#{loginForm.authLoginV2()}" />  
            <f:viewParam id="loginSts" name="loginStatus" value="#{userAuth.loginStatus}"   /> 
            <f:viewParam id="serviceTyp" name="token" value="#{userAuth.userToken}"   /> 
            <!--//Feature #8450: CRM-5589   Handle Login Failure-->
            <f:viewParam id="fn" name="fn" value="#{userAuth.fn}"   /> 
        </f:metadata>
    </ui:define>
    <h:head>
        <!--<meta http-equiv="X-Frame-Options" content="allow"/>-->
        <meta http-equiv="Cache-Control" content="no-cache, no-store,must-revalidate"/>
        <meta http-equiv="Pragma" content="no-cache"/>
        <meta http-equiv="Expires" content="0"/>
<!--        <h:outputStylesheet library="theme" name="style.css"/>
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['images/icon.png']}" />-->

        <meta charset="utf-8"/>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<!--        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"/>
        <meta name="theme-color" content="#444"/>
        <meta name="mobile-web-app-capable" content="yes"/>  
        <meta charset="ISO-8859-1"></meta>-->
        <!--<meta name="google-signin-scope" content="profile email">-->
<!--        <meta name="google-signin-client_id" content="244223340094-0kaqeo4np39hchtem6e64vgvdbl0idot.apps.googleusercontent.com"></meta>
        <script async='async' src="https://apis.google.com/js/platform.js"></script>-->
    </h:head> 
    <h:body styleClass="hold-transition"> 
        <p:progressBar id="intmdte" mode="indeterminate" style="position: fixed; top: 50%; width: 150em; height: 0.5em;"/>
    </h:body>
</html>