<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: TestRebranding.xhtml
// 
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
* //21-06-2023 : #11485 : Internal - CRM-7035: Create test page
*/-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">
    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>

    <ui:define name="head">
   <script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css" />
            <!--<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.4.1/js/bootstrap.min.js"></script>-->
            <link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote.css" rel="stylesheet"></link>

        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
    </ui:define>

    <ui:define name="metadata">
        <f:metadata> 
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('SUB_TABLES'))}" />
        </f:metadata>
    </ui:define>
     <ui:define name="title">
    <ui:include src="/help/Help.xhtml"/>
    </ui:define>
    <ui:define name="menu">

    </ui:define>
    <ui:define name="body">
           <f:event listener="#{subtableService.isPageAccessible}" type="preRenderView"/> 
        <div class="box box-info box-body">  
            <h:form id="frmLoad">
                <div class="div-center" style="margin-top:20px;margin-bottom:20px;">
                    <p:commandButton value="Load rebranding settings" id="btnLoadRebranding" 
                                     styleClass="btn btn-primary btn-xs"  
                                     actionListener="#{fabricClient.loadRebrandingSettings()}"/> 
                </div>
            </h:form>
            
            
             

 

        </div>
             
            
    </ui:define>


</ui:composition>

