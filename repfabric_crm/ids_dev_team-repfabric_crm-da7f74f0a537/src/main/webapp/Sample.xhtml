<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: Setup.xhtml
// Author: Sarayu
//*********************************************************
/*
*
* Copyright (c) 2015 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>

    <ui:define name="metadata">
        <f:metadata> 

        </f:metadata>
    </ui:define>

    <ui:define name="title">

    </ui:define>

    <ui:define name="body">
        <h:form id="sampleForm">
            <p:remoteCommand autoRun="true" actionListener="#{viewCompLookupService.listAll('applyCompany')}" update=":sampleForm:companyLookUp" />
            <p:dataTable id="companyLookUp" widgetVar="compLookup" reflow="true" draggableColumns="true" 
                         value="#{viewCompLookupService.companies}"  var="comp"  
                         filteredValue="#{lookupFilters.companies}" selection="#{viewCompLookupService.selectedCompany}"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="5,10,15" selectionMode="single"
                         filterEvent="keyup" pageLinks="4" 
                         rowKey="#{comp.compId}"  paginator="true"  rows="10" paginatorPosition="top" >
                <!--<p:ajax event="rowSelect" oncomplete="#{viewCompLookupService.remoteCmdName}(); PF('lookupComp').hide()" />-->
                <p:ajax event="colReorder" listener="#{viewCompLookupService.reOrder()}"  />
                <p:column id="name"  filterMatchMode="contains" filterBy="#{comp.compName}" headerText="Name" sortBy="#{comp.compName}"    >
                    <f:facet name="filter">
                        <p:chips value="#{viewCompLookupService.tags}" >
                            <p:ajax event="itemSelect"  listener="#{viewCompLookupService.reOrder()}"/>
                        </p:chips>
                    </f:facet>                  
                    #{comp.compName}
                </p:column>    
                <p:column  filterMatchMode="contains" filterBy="#{comp.compTypeName}" headerText="Type" sortBy="#{comp.compTypeName}" >
                    #{comp.compTypeName}
                </p:column>   
                <p:column  filterMatchMode="contains" filterBy="#{comp.smanName}" headerText="Sales Team" sortBy="#{comp.smanName}" >
                    #{comp.smanName}
                </p:column>  
                <p:column  filterMatchMode="contains" filterBy="#{comp.compPhone1}" headerText="Phone" sortBy="#{comp.compPhone1}" >
                    #{comp.compPhone1}
                </p:column>
                <p:column  filterMatchMode="contains" filterBy="#{comp.regionName}" headerText="Region" sortBy="#{comp.regionName}" >
                    #{comp.regionName}
                </p:column>
                <p:column  filterMatchMode="contains" filterBy="#{comp.compCity}" headerText="City" sortBy="#{comp.compCity}" >
                    #{comp.compCity}
                </p:column>
                <p:column  filterMatchMode="contains" filterBy="#{comp.compZipCode}" headerText="Zip" sortBy="#{comp.compZipCode}" >
                    #{comp.compZipCode}
                </p:column>

<!--                <p:columns value="#{dtColumnsView.columns}" var="column" columnIndexVar="colIndex" sortBy="#{car[column.property]}" filterBy="#{car[column.property]}">
                    <f:facet name="header">
                        <h:outputText value="#{column.header}" />
                    </f:facet>
                    <h:outputText value="#{car[column.property]}" />
                </p:columns>-->

            </p:dataTable>
        </h:form>

    </ui:define>
    <style type="text/css">
        .ui-filter-column .ui-column-customfilter .custom-filter {
            width: 100%;
            box-sizing: border-box;
        }
    </style>
</ui:composition>