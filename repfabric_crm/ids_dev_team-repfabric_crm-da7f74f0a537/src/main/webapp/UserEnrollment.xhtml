<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://xmlns.jcp.org/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:f="http://xmlns.jcp.org/jsf/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

    <ui:define name="metadata">
        <f:metadata>
            <f:viewAction action="#{userEnrollmentService.displayUsrEnrlProcessWizard}" />
            <f:viewParam id="validityCode" name="vldCode" value="#{userEnrollmentService.vldCode}"   /> 
            <f:viewParam id="tabRedirectIndex" name="tabIndex" value="#{userEnrollmentService.tabIndex}"   /> 
            <f:viewParam id="loginSts" name="loginStatus" value="#{userEnrollmentService.loginStatus}"   /> 
            <f:viewParam id="serviceTyp" name="serviceType" value="#{userEnrollmentService.serviceType}"   /> 
        </f:metadata>
    </ui:define>
    <h:head>
        <!--<meta http-equiv="X-Frame-Options" content="allow"/>-->
        <meta http-equiv="Cache-Control" content="no-cache, no-store,must-revalidate"/>
        <meta http-equiv="Pragma" content="no-cache"/>
        <meta http-equiv="Expires" content="0"/>
        <h:outputStylesheet library="theme" name="style.css"/>
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['images/icon.png']}" />
        <!--//20-06-2023 : #11482 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
        <title>#{fabricClient.clientName} CRM User Enrollment Wizard</title>
        <meta charset="utf-8"/>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"/>
        <meta name="theme-color" content="#444"/>
        <meta name="mobile-web-app-capable" content="yes"/>  
        <meta charset="ISO-8859-1"></meta>
        <!--<meta name="google-signin-scope" content="profile email">-->
        <meta name="google-signin-client_id" content="244223340094-0kaqeo4np39hchtem6e64vgvdbl0idot.apps.googleusercontent.com"></meta>
        <script async='async' src="https://apis.google.com/js/platform.js"></script>
    </h:head> 
    <h:body styleClass="hold-transition"> 
        <!--#7693 Fwd: Onboarding Flow updates and questions-->
        <div id="boxes">

            <!--<div class="box">3</div>-->
            <div   style="width: 100%;">

                <!--<div class="card">-->
                <h:form id="usrWizardForm">

                    <p:growl id="growl1" for="pwd1" showDetail="true"/>
                    <p:growl id="growl2" for="pwd2" showDetail="true"/>
                    <p:panelGrid columns="3" columnClasses="ui-grid-col-3,ui-grid-col-6,ui-grid-col-3" 
                                 styleClass="box-primary no-border ui-fluid" layout="grid"
                                 style="text-align: center!important; align-items: center!important; padding-top: 0px!important;padding-left: 25px!important;padding-right: 25px!important;">
                        <!--//12-06-2023 : #11375 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
                        <h:graphicImage library="images" name="#{fabricClient.clientType}/brand_logo.png"  class="#{fabricClient.clientType == 'Repfabric' ? 'ui-replogo' : 'ui-othrlogos'}"/>
                        <!--#7693 User onboard-->
                        <p:outputLabel style="color: black;font-weight: 600; font-size: x-large; margin-top: 35px" value="LET'S COMPLETE YOUR PROFILE" />
                        <p:panel > 
                            <p:row>
                                <p:outputLabel style="color: black;font-weight: 200; font-size: large; float:right; margin-top: -31px; margin-right: -6%"  
                                               value="#{userEnrollmentService.usrName}" />
                            </p:row>
                            <p:row>
                                <p:progressBar id="progress_bar" widgetVar="pbAjax" ajax="true" style="margin-left: 45px;"
                                               value="#{userEnrollmentService.progress}" 
                                               labelTemplate="{value}%" styleClass="animated" global="false">
                                <!--        <p:ajax event="complete" listener="#{UserWizard.onComplete}" update="growl" oncomplete="PF('startButton2').enable()"/>-->
                                    <p:ajax listener="#{userEnrollmentService.onFlowProcess}" update="@this"/>
                                </p:progressBar>
                            </p:row> 
                        </p:panel>
                    </p:panelGrid>  <br/>
                    <p:growl id="growlMsg"  showDetail="false"/>
                    <p:wizard id="usrEnrlmntWzrd" widgetVar="usrEnrlWzrd" flowListener="#{userEnrollmentService.onFlowProcess}"
                              style="color: black; min-height: 364px" >
                        <p:tab id="tcTab" title="Terms &amp; Conditions"  titleStyleClass="middleTab" >
                            <div class="tab-contents">
                                <div class="inline" style="margin-left: 18%;">
                                    <div class="info-msg">
                                        Read and Accept the Terms and Conditions to proceed with the User Enrollment process.
                                    </div>  
                                </div>
                                <p:messages id="msgs1" globalOnly="false" redisplay="false" >
                                    <p:autoUpdate/>
                                </p:messages>
                                <br/>
                                <p:panel style="margin-left: -30px;">
                                    <!--//20-06-2023 : #11482 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
                                    <p:outputLabel style="color: black; font-weight: 200; font-size: large" 
                                                   value="Welcome to #{fabricClient.clientName} !!!"/>     
                                    <br/>
                                    <p:outputLabel style="color: black; font-weight: 200; font-size: large" 
                                                   value="The CRM and Sales Data Management Platform Built for Manufacturers' Sales Reps"/>     
                                    <br/>
                                    <br/>
                                    <br/>
                                    <p:outputLabel style="color: black; font-weight: 200; font-size: large" 
                                                   value="Please read the "/>    <p:spacer width="4"/> 
                                    <p:commandLink style="font-size: large;" 
                                                   value="Terms and conditions"  onclick="PF('helpDlg').show();" process="@this" actionListener="#{userEnrollmentService.readTandC}" update=""/>   <p:spacer width="4"/>   
                                    <p:outputLabel style="color: black; font-weight: 200; font-size: large" 
                                                   value=" first and proceed with the enrollment process"/>     
                                    <br/>
                                    <br/>
                                    <p:selectBooleanCheckbox value="#{userEnrollmentService.acceptRemCnds}">
                                        <p:ajax event="change" process="@this"  update=":usrWizardForm:btnTrmsCdtn" 
                                                listener="#{userEnrollmentService.updateAcceptTermsCnds(userEnrollmentService.acceptRemCnds)}" 
                                                oncomplete="$('#usrWizardForm\\:msgs1').hide()"/>
                                    </p:selectBooleanCheckbox>
                                    <p:spacer width="4"/>
                                    <p:outputLabel style="color: black; font-weight: 200; font-size: large" 
                                                   value=" I have read and agree to the"/>    <p:spacer width="4"/> 
                                    <p:commandLink style="font-size: large;" 
                                                   value="Terms and conditions."  onclick="PF('helpDlg').show();"  process="@this" actionListener="#{userEnrollmentService.readTandC}"  update=""/> 
                                    <p:spacer width="4"/>
                                </p:panel>
                            </div>
                        </p:tab>
                        <!--#7693 User onboard-->
                        <p:tab id="authenticationTab" title="Authentication" titleStyleClass="middleTab">
                            <!--//12-02-2024 : #13043 : ESCALATIONS CRM-7709   User enrollment is letting new users create INVALID passwords and does not RE-->
                            <div class="tab-contents" style="margin-bottom:20px">
                                <!--//20-06-2023 : #11482 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
                                <div class="inline" ><div class="info-msg">Create the password you will use to sign in to #{fabricClient.clientName}. It must have minimum 8 character including</div> <div class="info-msg-b" style="margin-left:3px"> a capital letter, a number, and a special character.</div> 
                                    <!--//12-02-2024 : #13043 : ESCALATIONS CRM-7709   User enrollment is letting new users create INVALID passwords and does not RE-->
                                </div><div class="info-msg" style="margin-top:-10px">Please store this password securely.</div>
                                <!--<p:messages id="msgs2" globalOnly="false" redisplay="false"  autoUpdate="true"/>-->
                                <!--                      <p:growl  id="gmsgs2" globalOnly="false" redisplay="false"/>-->
                                <div style="text-align:center">
                                    <!--#7569 CRM-5479  useronboarding: uat: shouldn't user not choose a rf password if logging in with google/mic-->
                                    <p:messages  id="msgsA" globalOnly="false" redisplay="false" >
                                        <p:autoUpdate/>
                                    </p:messages>
    <!--                                <p:outputLabel style="color: black; font-weight: 400; font-size: large" rendered="#{userEnrollmentService.userEnrTmp.userAuthMode eq 0}" value="Sign In using" /> 
                                    <p:outputLabel style="color: black; font-weight: 400; font-size: large" rendered="#{userEnrollmentService.userEnrTmp.userAuthMode ne 0}" value="Signed In with" />
                                    <ui:fragment rendered="#{userEnrollmentService.userEnrTmp.userAuthMode eq 0 or userEnrollmentService.userEnrTmp.userAuthMode eq 2}"><br/><br/></ui:fragment>-->



<!--                                <p:commandButton rendered="#{userEnrollmentService.userEnrTmp.userAuthMode eq 0 or userEnrollmentService.userEnrTmp.userAuthMode eq 2}" value="#{userEnrollmentService.serviceType eq 1 and userEnrollmentService.loginStatus ne 0?'Signed in with Google':'Google'}" styleClass="btn btn-danger" immediate="true" 
                                                 title="Google" actionListener="#{userEnrollmentService.hideRFPanelFromG}"
                                                 update=":usrWizardForm" style="width: 165px;"
                                                 action="#{userEnrollmentService.redirectAuthLogin(1)}" /><ui:fragment rendered="#{userEnrollmentService.userEnrTmp.userAuthMode eq 0 or userEnrollmentService.userEnrTmp.userAuthMode eq 3}"><br/><br/></ui:fragment>
                                <p:commandButton rendered="#{userEnrollmentService.userEnrTmp.userAuthMode eq 0 or userEnrollmentService.userEnrTmp.userAuthMode eq 3}" value="#{userEnrollmentService.serviceType eq 2 and userEnrollmentService.loginStatus ne 0?'Signed in with Outlook':'Outlook'}" styleClass="btn btn-primary"  immediate="true" 
                                                 title="Outlook" actionListener="#{userEnrollmentService.hideRFPanelFromO}"
                                                 update=":usrWizardForm" style="width: 165px;"
                                                 action="#{userEnrollmentService.redirectAuthLogin(2)}" /><ui:fragment rendered="#{userEnrollmentService.userEnrTmp.userAuthMode eq 0 or userEnrollmentService.userEnrTmp.userAuthMode eq 1}"><br/><br/></ui:fragment>
                                <p:commandButton rendered="#{userEnrollmentService.userEnrTmp.userAuthMode eq 0 or userEnrollmentService.userEnrTmp.userAuthMode eq 1}" value="Repfabric" styleClass="btn btn-info"  immediate="true" 
                                                 actionListener="#{userEnrollmentService.displayRFPanel}" 
                                                 update=":usrWizardForm" style="width: 165px;"/>-->
                                    <!--<br/>-->
    <!--                                <p:commandLink rendered="#{userEnrollmentService.userEnrTmp.userAuthMode ne 0}" 
                                                   value="Change authentication mode" oncomplete="PF('authModeDlg').show();"/>-->
                                    <!--<br/><br/>--> 
                                    <!--#{userEnrollmentService.userEnrTmp.userAuthMode}-->
                                </div>
                                <!--<p:panel header="Personal Details" rendered="#{userEnrollmentService.userEnrTmp.userAuthMode eq 1}">--> 
                                <!--//12-02-2024 : #13043 : ESCALATIONS CRM-7709   User enrollment is letting new users create INVALID passwords and does not RE-->
                                <p:panel id="authWizard"  style="border: none;margin-top:-10px;" >    


                                    <!--<p:outputLabel style="color: black"  value="Repfabric account password"/><br/>-->
                                    <!--<h:panelGrid columns="2" columnClasses="label, value" style="padding: 10px">-->
                                    <!--<fieldset style="border: 1px solid;"><legend>Repfabric Login</legend>-->

                                    <div >
                                        <!--                                    <h4>Repfabric Account Sign In</h4>-->

                                        <br/>

                                        <h:panelGrid columns="3" style="width: 100%">
                                            <p:panel id="panel22"  style="height:200px;  width: 540px; border-style: solid; border-color: #f17d54; border-width: medium; text-align: right; line-height: 46px;">
                                                <p:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" 
                                                             styleClass="box-primary no-border ui-fluid" layout="grid"  id="authWizardGrd" >

                                                    <p:outputLabel style="color: black"  value="Login ID "/>
                                                    <p:inputText label="Login ID"  value="#{userEnrollmentService.userEnrTmp.userLoginId}" required="true" readonly="true"/>

                                                    <p:outputLabel style="color: black" for="pwd1" value="Password" />
                                                    <!--//12-02-2024 : #13043 : ESCALATIONS CRM-7709   User enrollment is letting new users create INVALID passwords and does not RE-->
                                                    <p:inputText id="pwd1" value="#{userEnrollmentService.userEnrTmp.userPassword}" 
                                                                 type="password" label="Password" disabled="#{userEnrollmentService.inputPassBol}" > 
                                                    </p:inputText>
            <!--                                         <p:inputText id="pwd1" value="#{userEnrollmentService.userEnrTmp.userPassword}" 
                                                                 validator="#{userEnrollmentService.validatePasswordCorrectz}" 
                                                                 type="password" label="Password" >
                                                        <f:validateLength minimum="8" maximum="15"/>
                                                    </p:inputText>-->
                                                    <!--<p:message id="message1" for="pwd1" />-->

 <!--<p:inputText value="#{userEnrollmentService.user.lastname}"    label="Password" type="password" required="true"/>--> 

                                                    <p:outputLabel style="color: black" for="pwd2" value="Confirm  Password" />
                                                    <!--//12-02-2024 : #13043 : ESCALATIONS CRM-7709   User enrollment is letting new users create INVALID passwords and does not RE-->
                                                    <p:inputText id="pwd2" value="#{userEnrollmentService.userEnrTmp.userCnfPassword}"
                                                                 type="password" disabled="#{userEnrollmentService.inputPassBol}"> 
                                                    </p:inputText>
            <!--                                        <p:inputText id="pwd2" value="#{userEnrollmentService.userEnrTmp.userCnfPassword}" 
                                                                 validator="#{userEnrollmentService.validatePasswordCorrect}" 
                                                                 type="password" >
                                                        <f:validateLength minimum="8" maximum="15"/>
                                                    </p:inputText>-->
                                                    <!--<p:message id="message2" for="pwd2" />--> 


<!--<p:inputText value="#{userEnrollmentService.user.lastname}" label="Confirm Password" type="password" required="true"/>-->

                                                    <!--#7569 CRM-5479  useronboarding: uat: shouldn't user not choose a rf password if logging in with google/mic-->

                                                    <!--                                                                            <p:outputLabel style="color: black" value="Skip to last: "/>
                                                                                                                                <h:selectBooleanCheckbox value="#{userEnrollmentService.skip}"/>-->
                                                </p:panelGrid> 
                                            </p:panel>
                                            <p:outputLabel value="OR" style="font-weight: 1000; font-size: large;padding: 10px;"/>  
                                            <p:panel id="panel222" style="height:200px; width: 540px; border-style: solid;border-color: #f17d54; border-width: medium;">
                                                <h:panelGrid columns="2" style="width: 100%">
                                                    <p:panel id="panel33"  style="font-size: large; text-align: left;" class="info-msg">
                                                        <!--//20-06-2023 : #11482 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
                                                        Want to use your Microsoft or Gmail
                                                        password for #{fabricClient.clientName}? If so, you can go
                                                        ahead and skip this step.

                                                        <br/>
                                                        <br/>
                                                    </p:panel>
                                                    <p:panel id="panel333"  style="height:170px;">

                                                        <h:graphicImage library="images" name="outlook_icon.png" style="width:50%; padding: 10px!important; margin-left: 40px;"/>
                                                        <br/> 
                                                        <h:graphicImage library="images" name="gmail_icon.png" style="width:50%; padding: 10px!important; margin-left: 45px;"/>

                                                    </p:panel>
                                                </h:panelGrid>
                                            </p:panel>          
                                        </h:panelGrid>
                                        <!--//12-02-2024 : #13043 : ESCALATIONS CRM-7709   User enrollment is letting new users create INVALID passwords and does not RE-->
                                        <div style="display: flex; justify-content: center;margin-top: 2px">
                                            <p:panel id="chkBoxAuth" style="border: none; padding-left: 0px;">
                                                <p:selectBooleanCheckbox value="#{userEnrollmentService.skipAuthFlag}" id="checkSkipAuth" tabindex="5" disabled="#{userEnrollmentService.userEnrTmp.userConfigEmail==1 || userEnrollmentService.userEnrTmp.userConfigEmail==2}">
                                                    <p:ajax event="change" process="@this" listener="#{userEnrollmentService.chnageFlag(userEnrollmentService.skipFlag)}" 
                                                            update=":usrWizardForm:curStatus :usrWizardForm:panel22" oncomplete="$('#usrWizardForm\\:msgsA').hide();" />
                                                    <!--:usrWizardForm:pwd1 :usrWizardForm:pwd2-->
                                                </p:selectBooleanCheckbox> 
                                                <p:spacer width="4"/>
                                                <p:outputLabel for="checkSkipAuth" style="font-weight: 1000;" value="SKIP THIS STEP" />
                                            </p:panel>
                                        </div>

                                    </div>

                                </p:panel>

                            </div>


                        </p:tab>
                        <!--#7693 User onboard Your Contact Details-->
                        <p:tab id="userDetailsTab" title="User Details"  titleStyleClass="middleTab">
                            <div class="tab-contents">
                                <!--                                <div class="info-msg">
                                                                                                        <i class="fa fa-info-circle"></i>
                                                                    Enter your Contact details.
                                                                </div>-->


                                <p:panel >
                                    <p:messages id="msgs3" globalOnly="false" redisplay="false"   style="margin-left: -23px">
                                        <p:autoUpdate/>
                                    </p:messages>
                                    <h:panelGrid columns="2">
                                        <p:panel id="panel11"  style="height:352px; width: 180px;">

                                            <h:graphicImage library="images" name="user_details_graphic.jpg" style="margin-left: -32px!important;width: 120%; margin-top: -10px"/>


                                        </p:panel>
                                        <p:panel id="panel111" style="height:352px;  width:898px;  border-style: solid; border-color: #f17d54; border-width: medium;  text-align: right;  line-height: 38px;">



                                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" 
                                                         styleClass="box-primary no-border ui-fluid" layout="grid" style="border: none;">
                                                <p:outputLabel style="color: black" value="Login ID: " />
                                                <!--<p:inputText value="#{userEnrollmentService.userEnrTmp.userLoginId}" disabled="true"/>-->
                                                <p:inputText label="Login ID"  value="#{userEnrollmentService.userEnrTmp.userLoginId}" readonly="true"/>
                                                <p:outputLabel style="color: black" value="Company Name: " />
                                                <!--<p:inputText value="#{userEnrollmentService.userEnrTmp.userLoginId}" disabled="true"/>-->
                                                <p:inputText label="Company Name"  value="#{userEnrollmentService.companyName}" readonly="true"/>

                                                <p:outputLabel style="color: black" value="First Name:" class="required"/>
                                                <p:inputText value="#{userEnrollmentService.userEnrTmp.userFirstName}" maxlength="30"/>

                                                <p:outputLabel style="color: black" value="Last Name: "/>
                                                <p:inputText value="#{userEnrollmentService.userEnrTmp.userLastName}" maxlength="30"/>

                                                <p:outputLabel style="color: black" value="Street: "/>
                                                <p:inputText value="#{userEnrollmentService.userEnrTmp.userStreet}"  maxlength="180"/>

                                                <p:outputLabel style="color: black" value="City: "/>
                                                <p:inputText value="#{userEnrollmentService.userEnrTmp.userCity}"  maxlength="40"/>
                                                <!--8171 CRM-5773  UAT: reverse the address entry for tabbing by city and zip-->
                                                <p:outputLabel style="color: black" value="State: "/>
                                                <p:inputText value="#{userEnrollmentService.userEnrTmp.userState}"  maxlength="40"/>

                                                <p:outputLabel style="color: black" value="Zip: "/>
                                                <p:inputText value="#{userEnrollmentService.userEnrTmp.userZipCode}"  maxlength="12"/>

                                                <p:outputLabel style="color: black" value="Country: "/>
                                                <p:inputText value="#{userEnrollmentService.userEnrTmp.userCountry}"  maxlength="40"/>

                                                <p:outputLabel style="color: black" value="Mobile: "/>
                                                <p:inputText value="#{userEnrollmentService.userEnrTmp.userMobile}"  maxlength="40"/>

                                                <p:outputLabel style="color: black" value="Business Email: "/>
                                                <p:inputText value="#{userEnrollmentService.userEnrTmp.userBusiEmail}"  maxlength="100"/>

                                                <p:outputLabel style="color: black" value="Business Phone: "/>
                                                <p:inputText value="#{userEnrollmentService.userEnrTmp.userBusiPhone}"  maxlength="40"/>

                                                <p:outputLabel style="color: black" value="Alternate Email: "/>
                                                <p:inputText value="#{userEnrollmentService.userEnrTmp.userAltEmail}"  maxlength="100"/>

                                                <p:outputLabel style="color: black" value="Alternate Phone: "/>
                                                <p:inputText value="#{userEnrollmentService.userEnrTmp.userAltPhone}"  maxlength="40"/>
                                            </p:panelGrid>

                                        </p:panel>
                                    </h:panelGrid>
                                </p:panel>
                            </div>

                        </p:tab>

                        <p:tab id="emailConfigurationTab" title="Email Configuration"  titleStyleClass="middleTab">
                            <div class="tab-contents">
                                <div class="inline">
                                    <div class="info-msg">
                                        <!--                                    <i class="fa fa-info-circle"></i>-->
                                        <!--//20-06-2023 : #11482 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
                                        Please sign into your work email (if Microsoft, choose "Work or School"). Completing this step will link your work email with your #{fabricClient.clientName}
                                        account. Linking both together will enable you to utilize all the features of #{fabricClient.clientName}, including email sync, calendar sync, and contact sync.</div></div>
                                <p:panel id="emailConfigTab" style="border: none;">


                                    <p:messages  id="msgs4" globalOnly="false" redisplay="false" style="margin-top: -20px;margin-bottom: -10px;">
                                        <p:autoUpdate/>
                                    </p:messages>


                                    <p:growl  id="gmsgs4" globalOnly="false" redisplay="false"  showDetail="false">
                                        <p:autoUpdate/>
                                    </p:growl>

                                    <h:panelGrid columns="3">
                                        <p:panel id="panel44" style="height:250px; width: 360px; padding-top: 10px; border-style: solid; border-color: #f17d54; border-width: medium;">
                                            <p:panelGrid columns="1" columnClasses="ui-grid-col-12" 
                                                         styleClass="box-primary no-border ui-fluid" layout="grid" style="border: none; text-align: center;">

                                                <p:panel id="curStatus" style="border: none;">
                                                    <p:outputLabel style="color: black; font-weight: 400; font-size: large"  value="Current Status" />   :   <p:outputLabel style="color: black; font-weight: 400; font-size: large"  value="#{userEnrollmentService.userEnrTmp.userConfigEmail==0?'None':userEnrollmentService.userEnrTmp.userConfigEmail==3?'No Email Linked':'Email Account Linked to '.concat(userEnrollmentService.usrName)}" />    
                                                </p:panel>

                                                <p:commandButton id="actvEml" value="#{userEnrollmentService.userEnrTmp.userConfigEmail==0?'Activate Email Account':userEnrollmentService.userEnrTmp.userConfigEmail==3?'Activate Email Account':'Email Account Activated'}" styleClass="btn btn-primary"
                                                                 immediate="true"  actionListener="#{userEnrollmentService.changeEmailStatus}"
                                                                 update=":usrWizardForm:curStatus :usrWizardForm:chkBox :usrWizardForm:actvEml" style="width: 176px;"
                                                                 oncomplete="$('#usrWizardForm\\:msgs4').hide()"/>

                                                <p:panel id="chkBox" style="border: none;">
                                                    <!--<p:selectBooleanCheckbox value="#{userEnrollmentService.userEnrTmp.userConfigEmail==2}"--> 
                                                    <!--                                                                     id="checkSkip" tabindex="5"
                                                                                                                         valueChangeListener="#{userEnrollmentService.changeListener}">
                                                                                                    <p:ajax event="change" listener="#{userEnrollmentService.changeListener}" />
                                                                                                </p:selectBooleanCheckbox> -->

                                                    <p:selectBooleanCheckbox value="#{userEnrollmentService.skipFlag}" id="checkSkip" tabindex="5" disabled="#{userEnrollmentService.userEnrTmp.userConfigEmail==1 || userEnrollmentService.userEnrTmp.userConfigEmail==2}">
                                                        <p:ajax event="change" process="@this" listener="#{userEnrollmentService.chnageFlag(userEnrollmentService.skipFlag)}" 
                                                                update=":usrWizardForm:curStatus" oncomplete="$('#usrWizardForm\\:msgs4').hide()"/>
                                                    </p:selectBooleanCheckbox> 
                                                    <p:spacer width="4"/>
                                                    <p:outputLabel for="checkSkip" value="Skip" />
                                                </p:panel>

                                            </p:panelGrid>
                                        </p:panel>
                                        <p:panel id="panel444" style="height:250px; width: 360px; border-style: solid; border-color: #319299; border-width: medium; margin-left: 10px; margin-right: 10px; ">
                                            <div style="height: 125px; background: #319299;  color: white; margin: -12px; text-align: center;">
                                                <br/>
                                                <div style="font-weight: 400; font-size: 20px;">
                                                    <!--//20-06-2023 : #11482 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
                                                    READY TO INSTALL #{fabricClient.clientName} IN YOUR INBOX?
                                                </div>
                                                <br/>
                                                Select your email provider for instructions
                                                <br/>
                                            </div>
                                            <div style="display: flex;">
                                                <div style="text-align: center; padding-top: 30px">
                                                    <h:graphicImage library="images" name="outlook_icon.png" style="width:40%;  cursor: pointer;" 
                                                                    onclick="window.open('https://learning.repfabric.com/setting-up-sync-users/?autologin_code=AS3QQmG6YDBQYHIPwYW1dCHCPzdA4XkR', '_blank')"/>
                                                    <br/>
                                                    Outlook      
                                                </div>
                                                <div style="text-align: center; padding-top: 30px">
                                                    <h:graphicImage library="images" name="gmail_icon.png" style="width:40%;  cursor: pointer;" 
                                                                    onclick="window.open('https://learning.repfabric.com/setting-up-sync-gmail-users/?autologin_code=AS3QQmG6YDBQYHIPwYW1dCHCPzdA4XkR', '_blank')"/>
                                                    <br/>
                                                    Gmail 
                                                </div>
                                            </div>
                                        </p:panel>
                                        <p:panel id="panel4444" style="height:250px; width: 360px; ">

                                            <h:graphicImage library="images" name="email_configuration_graphic.png" style="width:90%; margin-top: -10px!important;"/>

                                        </p:panel>
                                    </h:panelGrid>













                                    <!--                                    <div class="ui-g ui-fluid">
                                                                        <div class="ui-sm-12 ui-md-12 ui-lg-12">
                                                                        </div> 
                                                                        </div>-->
                                </p:panel>


                            </div>

                        </p:tab>



                        <p:tab id="syncSettingsTab" title="Sync Settings"  titleStyleClass="middleTab">
                            <div class="tab-contents">
                                <div class="inline" style="margin-left: 17%;">
                                    <div class="info-msg">
                                        <!--                                    <i class="fa fa-info-circle"></i>-->
                                        Choose which sales team(s) you will want to include in your contact sync.<br/>
                                        Full sync capability may not be available at this time, but will be enabled in the future based on your selections.
                                    </div></div>
                                <p:remoteCommand action="#{syncAcctDtls.populateConfiguredAccounts(userEnrollmentService.usr_Id)}" name="rcSync" autoRun="true" update=":usrWizardForm:syncPnl" />

                                <p:panel style="border: none;" id="syncPnl">
                                    <p:messages id="msgs5" globalOnly="false" redisplay="false"  >
                                        <p:autoUpdate/>
                                    </p:messages>
                                    <!--<h:panelGrid columns="2" columnClasses="label, value">-->
                                    <!--                                    <p:outputPanel id="pnlYoxelInfo">
                                                                            <p:panelGrid  columns="2"  rendered="#{syncAcctDtls.acctYoxel.acctConfigured}" columnClasses="ui-grid-col-2,ui-grid-col-4"
                                                                                          style="min-height: 100px"       layout="grid" styleClass="box box-info no-border ui-fluid">
                                    
                                                                                                                            <p:outputLabel value="Sync Enabled"/>
                                                                                
                                                                                                                            <h:panelGroup >
                                                                                                                                <p:selectBooleanCheckbox itemLabel="#{syncAcctDtls.acctYoxel.acctSyncEnabled == 1? 'On' : 'Off'}" id="chkYoxelSync" value="#{syncAcctDtls.acctYoxel.syncFlag}" 
                                                                                                                                                         disabled="#{loginBean.userAdminFlag eq 0}">
                                                                                                                                    <p:ajax onstart="PF('syncFlagProgressDlg').show();" update=":usrWizardForm:chkYoxelSync" listener="#{syncAcctDtls.syncYoxelContacts()}" oncomplete="PF('syncFlagProgressDlg').hide();"/>
                                                                                                                                </p:selectBooleanCheckbox>              
                                                                                                                            </h:panelGroup>
                                    
                                                                                <p:outputLabel value="Default Sales Team"/>
                                    
                                                                                <h:panelGroup>
                                                                                    <p:outputLabel value="Not Assigned" style="color: red" rendered="#{userEnrollmentService.userTmp.userSmanId ==0}"/>
                                                                                    <p:outputLabel value="#{userEnrollmentService.userTmp.salesTeam}"  />
                                                                                </h:panelGroup>
                                    
                                                                            </p:panelGrid>
                                                                            <p:panelGrid columns="1">
                                                                                <p:outputLabel rendered="#{!syncAcctDtls.acctYoxel.acctConfigured}" value="No API token generated."/>
                                                                            </p:panelGrid>
                                                                        </p:outputPanel>-->

                                    <!--                                    <p:outputPanel class="sub_title" id="pnlSalesSync" >
                                                                            <p:outputLabel styleClass="acct-name-hdr" value="Sync enabled status for Sales team"/>
                                                                        </p:outputPanel>-->
                                    <h:panelGrid columns="2">
                                        <p:panel id="panel55" style="height:259px; width: 540px; margin-top: 10px; border-style: solid; border-color: #f17d54; border-width: medium;">

                                            <p:outputPanel style="height:234px; width: 520px;overflow-x: scroll;overflow-x: hidden; position: relative" id="pnlSalesInfo" >
            <!--style="width: 500px;#{loginBean.loggedInUser.userAdminFlag eq 0 or users.editable eq false?'pointer-events: none':''}"-->
                                                <p:dataTable  id="dtSalesSyncId" widgetVar="dtSalesSync" style="width: 500px;"
                                                              value="#{salesGroup.listsalesTeam}" var="salsTm"  >
                                                    <p:column headerText="Sales Team">
                                                        #{salsTm.sgtsman}
                                                    </p:column>
                                                    <p:column headerText="Sync Flag Enabled?" >
                                                        <p:selectBooleanCheckbox value="#{salsTm.syncFlag}" id="checkFlag">
                                                            <p:ajax event="change"  listener="#{salesGroup.ChnageContSyncFlagSetWizard(salsTm.sgtSmanId, salsTm.sgtMemberId, salsTm.syncFlag)}" />
                                                            <!--onstart="PF('syncFlagProgressDlg').show();" oncomplete="PF('syncFlagProgressDlg').hide();"-->

                                                        </p:selectBooleanCheckbox>
                                                    </p:column>

                                                </p:dataTable>
                                            </p:outputPanel> 
                                        </p:panel>
                                        <p:panel id="panel555" style="height:250px; width: 560px;">
                                            <h:graphicImage library="images" name="sync_settings_graphic.jpg" style="width:97%; margin-top:-10px; margin-left:10px;"/>

                                        </p:panel>
                                    </h:panelGrid>
                                    <!--</h:panelGrid>-->
                                </p:panel>

                            </div>                            
                        </p:tab>

                        <p:tab id="scheduleTrainingTab" title="Schedule Training"  titleStyleClass="middleTab">                           
                            <div class="tab-contents">
                                <div class="inline" style="margin-left: 18%;">
                                    <div class="info-msg">
                                        <!--                                    <i class="fa fa-info-circle"></i>-->

                                        Last but certainly not least, schedule your training with a member of our team to start learning the platform.<br/>
                                        <!--//20-06-2023 : #11482 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
                                        We also have on-demand videos in the #{fabricClient.clientName} Knowledge Base that you can start watching right away!
                                    </div>
                                </div>
                                <div style="text-align: center; width: 1122px;">
                                    <!--8041  CRM-5557   JM: UAT: onboarding: schedule training tab: if within 30 days of instance launch point to--> 
                                    <h:commandLink action="#{userEnrollmentService.updateEnrollStepStatus(userEnrollmentService.usr_Id,10)}" rendered="#{userEnrollmentService.exceedsLaunchDt==false}">
                                        <h:graphicImage library="images" name="schedule.jpg" style="width:25%; padding: 10px!important; margin-left: 45px; cursor: pointer;"
                                                        onclick="window.open('https://learning.repfabric.com/your-training-is-scheduled-already/?autologin_code=AS3QQmG6YDBQYHIPwYW1dCHCPzdA4XkR', '_blank')"/>
                                    </h:commandLink>

                                    <h:commandLink action="#{userEnrollmentService.updateEnrollStepStatus(userEnrollmentService.usr_Id,10)}" rendered="#{userEnrollmentService.exceedsLaunchDt==true}">
                                        <h:graphicImage library="images" name="schedule.jpg" style="width:25%; padding: 10px!important; margin-left: 45px; cursor: pointer;"
                                                        onclick="window.open('https://learning.repfabric.com/new-user-training-links/?autologin_code=AS3QQmG6YDBQYHIPwYW1dCHCPzdA4XkR', '_blank')"/>
                                    </h:commandLink>
                                    <p:spacer width="4"/>
                                    <h:commandLink action="#{userEnrollmentService.updateEnrollStepStatus(userEnrollmentService.usr_Id,9)}">  
                                        <h:graphicImage library="images" name="watch_video.jpg" style="width:25%; padding: 10px!important; margin-left: 0px;  cursor: pointer;" 
                                                        onclick="window.open('https://learning.repfabric.com/all-classes-video-recordings/?autologin_code=AS3QQmG6YDBQYHIPwYW1dCHCPzdA4XkR', '_blank')"/>
                                    </h:commandLink>


                                    <br/>
                                    <!--//20-06-2023 : #11482 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
                                    <p:commandButton value="LOG IN TO #{fabricClient.clientName}" styleClass="btn btn-primary" action="#{userEnrollmentService.redirectLogin}" /><br/><br/> 
                                </div>
                                <p:panel>
                                    <p:messages id="msgs6" globalOnly="false" redisplay="false" >
                                        <p:autoUpdate/>
                                    </p:messages>
                                    <!--7145 CRM-5303  User Automation notes-->
                                    <!--                                    <div style="text-align:center">
                                                                            <p:commandButton value="Schedule Training" styleClass="btn btn-info"  actionListener="#{userEnrollmentService.updateEnrollStepStatus(userEnrollmentService.usr_Id,10)}"
                                                                                             onclick="window.open('https://learning.repfabric.com/new-user-training-links/?autologin_code=AS3QQmG6YDBQYHIPwYW1dCHCPzdA4XkR', '_blank')" />  <br/><br/> 
                                                                        </div>
                                                                        <div style="text-align:center">
                                                                            <p:commandButton value="Learning Center" styleClass="btn btn-info"  actionListener="#{userEnrollmentService.updateEnrollStepStatus(userEnrollmentService.usr_Id,9)}" 
                                                                                             onclick="window.open('https://learning.repfabric.com/', '_blank')" /> <br/><br/> 
                                                                        </div><br/><br/> 
                                                                        <div style="text-align:center">
                                                                            <p:commandButton value="Done" styleClass="btn btn-primary" action="#{userEnrollmentService.redirectLogin}" /><br/><br/> 
                                                                        </div><br/><br/> -->
                                </p:panel>
                            </div>

                        </p:tab>

                        <!--                        <p:tab id="confirmationTab" title="Confirmation">
                                                    <div style="height: 350px;">
                                                        <p:panel header="Confirmation">
                                                            <h:panelGrid id="confirmation" columns="3" columnClasses="grid,grid,grid">
                                                                <h:panelGrid columns="2" columnClasses="label, value">
                                                                    <p:outputLabel style="color: black" value="Firstname: "/>
                                                                    <p:outputLabel style="color: black" value="#{userEnrollmentService.userEnrTmp.firstname}" styleClass="outputLabel"/>
                        
                                                                    <p:outputLabel style="color: black" value="Lastname: "/>
                                                                    <p:outputLabel style="color: black" value="#{userEnrollmentService.userEnrTmp.lastname}" styleClass="outputLabel"/>
                        
                                                                    <p:outputLabel style="color: black" value="Age: "/>
                                                                    <p:outputLabel style="color: black" value="#{userEnrollmentService.userEnrTmp.age}" styleClass="outputLabel"/>
                                                                </h:panelGrid>
                        
                                                                <h:panelGrid columns="2" columnClasses="label, value">
                                                                    <p:outputLabel style="color: black" value="Street: "/>
                                                                    <p:outputLabel style="color: black" value="#{userEnrollmentService.userEnrTmp.street}" styleClass="outputLabel"/>
                        
                                                                    <p:outputLabel style="color: black" value="Postal: "/>
                                                                    <p:outputLabel style="color: black" value="#{userEnrollmentService.userEnrTmp.postalCode}" styleClass="outputLabel"/>
                        
                                                                    <p:outputLabel style="color: black" value="City: "/>
                                                                    <p:outputLabel style="color: black" value="#{userEnrollmentService.userEnrTmp.city}" styleClass="outputLabel"/>
                                                                </h:panelGrid>
                        
                                                                <h:panelGrid columns="2" columnClasses="label, value">
                                                                    <p:outputLabel style="color: black" value="Email: "/>
                                                                    <p:outputLabel style="color: black" value="#{userEnrollmentService.userEnrTmp.email}" styleClass="outputLabel"/>
                        
                                                                    <p:outputLabel style="color: black" value="Phone "/>
                                                                    <p:outputLabel style="color: black" value="#{userEnrollmentService.userEnrTmp.phone}" styleClass="outputLabel"/>
                        
                                                                    <p:outputLabel style="color: black" value="Info: "/>
                                                                    <p:outputLabel style="color: black" value="#{userEnrollmentService.userEnrTmp.info}" styleClass="outputLabel"/>
                        
                                                                    <p:outputLabel style="color: black"/>
                                                                    <p:outputLabel style="color: black"/>
                                                                </h:panelGrid>
                                                            </h:panelGrid>
                        
                                                            <p:commandButton value="Submit" action="#{userEnrollmentService.save}" update="growl" process="@this"/>
                                                        </p:panel>
                                                    </div>
                                                </p:tab>-->
                    </p:wizard>


                    <!--visible="true"--> 

                    <p:dialog widgetVar="helpDlg" id="hlpDlg" header="Terms and Conditions" 
                              onShow="PF('helpDlg').initPosition()" resizable="false"
                              height="530" width="900"  closable="false" position="center center" >
                        <!--<p:ajax event="close" update="@all" oncomplete="zxz()"/>-->

                        <div style="text-align:center;">
                            <!--//12-06-2023 : #11375 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
                            <a target="_blank" href="http://#{fabricClient.clientUrl}/">
                                <h:graphicImage library="images" name="#{fabricClient.clientType}/brand_logo.png"  class="#{fabricClient.clientType == 'Repfabric' ? '' : 'ui-usrenrlmnt-othrlogodlg'}"  />
                            </a> 
                        </div>

                        <!--
                                                <p:outputLabel style="color: black; font-weight: 200; font-size: large" 
                                                               value="Welcome to Repfabric !!!"/>     -->
                        <br/>
                        <p:scrollPanel id="scrlPnl" mode="native" style="width:872px;height:400px">
                            <iframe id="iframeId" name="iframe" frameborder="0" scrolling="auto" style="width: 870px; height: 390px"
                                    src="https://repfabric.com/terms_conditions.html">
                                <p>Something went wrong.</p>
                            </iframe>
                        </p:scrollPanel>
                        <!--
                                                <br/>
                                                <br/>-->




                        <div id="someId"> 
                            <!--                            <iframe id="iframeId" name="iframe" frameborder="0" scrolling="auto" style="width: 850px; height: 400px"
                                                                src="https://www.business-standard.com/terms-conditions">
                                                            <p>Something went wrong.</p>
                                                        </iframe>-->
                        </div> <br/><br/>
                        <p:commandButton value="Ok" process="@this" styleClass="btn btn-success btn-md"   id="btnTrmsCdtn" onclick="PF('helpDlg').hide();"/>
                        <!--        <br/><br/>
                                <b>Guidelines</b>
                                <font size="2">
                                    <ul>
                                        <li>
                                            Provide a header column with column titles in the import file.
                                        </li>
                                        <li>
                                            'Start import from line #' refers to the row from which the data begins. 
                                        </li>
                                        <li>
                                            Make sure all the fields are present and in the same order as given below.
                                        </li>
                                        <li>
                                            Provide blank value in case no data is available for a data cell.
                                        </li>
                        
                                    </ul>
                                </font>
                                <fieldset>
                                    <legend style="font-weight: bold">Column Order</legend>
                                    User Data<br/>
                                    <font size="2" >
                                        Login ID <font color="red" >|</font>
                                        User Name <font color="red" >|</font>
                                        Password <font color="red" >|</font>
                                        Personal Email <font color="red" >|</font>
                                        Admin? <font color="red" >|</font>
                                        Category <font color="red" >|</font>
                                        Email Login <font color="red" >|</font>
                                        Email Password <font color="red" >|</font>
                                        Reply Email <font color="red" >|</font>                 
                                        Email Server <font color="red" >|</font>
                                        Email Port <font color="red" >|</font>
                                        Email Protocol <font color="red" >|</font>
                                        SMTP Host <font color="red" >|</font>
                                        SMTP Port <font color="red" >|</font>
                                        SMTP Secure  </font>
                        
                                </fieldset>
                                <br/>
                        -->


                    </p:dialog>

                    <p:dialog widgetVar="helpDlg1" header="Invalid Request" position="center center" resizable="false" responsive="false" fitViewport="true"
                              closable="false"   height="530" width="900" modal="true" visible="true" 
                              rendered="#{userEnrollmentService.validRequest==0}" onShow="PF('helpDlg1').initPosition()">

                        <!--        <p:dialog widgetVar="helpDlg1" header="Invalid Request" position="center center"
                                                  height="530" width="900" modal="true" visible="true" onShow="PF('helpDlg1').initPosition()"
                                                  closable="false"  rendered="#{userEnrollmentService.validRequest==0}">-->
                        <br/> <br/> <br/> 
                        <div style="text-align:center;">
                            <!--//12-06-2023 : #11375 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
                            <a target="_blank" href="http://#{fabricClient.clientUrl}/">
                                <h:graphicImage library="images" name="#{fabricClient.clientType}/brand_logo.png"  class="#{fabricClient.clientType == 'Repfabric' ? 'ui-usrenrlmnt-replogo' : 'ui-usrenrlmnt-othrlogo'}"/>
                            </a><br/> 
                            <p:outputLabel style="color: black;font-weight: 200; font-size: large"  value="Invalid:Unable to process request. Please contact Administrator to generate new request." 
                                           /> <br/>
                        </div>

                    </p:dialog>


                    <p:confirmDialog header="Processing" global="false" 
                                     message="Linking and Authenticating" widgetVar="noRcAlert" width="300" style="font-size:  21px" > 
                        <p:commandButton value="OK" class="btn btn-primary btn-xs "  
                                         actionListener="#{userEnrollmentService.changeEmailStatusOk}"
                                         update=":usrWizardForm:curStatus :usrWizardForm:chkBox"
                                         oncomplete="PF('noRcAlert').hide()" style="font-size: 14px" /> 
                    </p:confirmDialog>

                    <p:dialog header="Signin to Email using"  resizable="false"
                              widgetVar="emlAuthAlert" width="374" 
                              style="font-size:  21px; z-index: 9999 !important; background: rgb(224 224 224);"
                              onShow="ovBodyHidden();" onHide="ovBodyAuto();"> 
                        <!--                        <div style="text-align:center;">
                                                    <f:facet name="message"  >
                                                        "#{userEnrollmentService.serviceType eq 1 and userEnrollmentService.loginStatus ne 0?'Signed in with Google':'Google'}"
                                                        <p:commandButton value="Google" styleClass="btn btn-danger" immediate="true" 
                                                                         title="Google" actionListener="#{userEnrollmentService.hideRFPanelFromG}"
                                                                         update=":usrWizardForm" style="width: 165px;"
                                                                         action="#{userEnrollmentService.redirectEmailLogin(1)}" onsuccess="PF('noRcAlert').hide()"/>
                                                        image="imgClassName" 
                                                        <br/> <br/>
                        #{userEnrollmentService.serviceType eq 2 and userEnrollmentService.loginStatus ne 0?'Signed in with Outlook':'Outlook'}
                        <p:commandButton  value="Outlook" styleClass="btn btn-primary"  immediate="true" 
                                          title="Outlook" actionListener="#{userEnrollmentService.hideRFPanelFromO}"
                                          update=":usrWizardForm" style="width: 165px;"  
                                          action="#{userEnrollmentService.redirectEmailLogin(2)}" onsuccess="PF('noRcAlert').hide()"/>

                    </f:facet>
                </div>-->

                        <div style=" text-align: center; margin-top:10px;">
                            <!--styleClass="openorViewOpp"<p:graphicImage  width="20" height="20" library="images" id="imgGoogle" name="googleIcon.png" style="margin-right:5px"/>Sign in With Google-->

                            <h:commandLink  styleClass="btn"  immediate="true"  actionListener="#{userEnrollmentService.hideRFPanelFromG}"
                                            style="width: 165px; border: 1px; border-style: solid; background: white;"
                                            action="#{userEnrollmentService.redirectEmailLogin(1)}" 
                                            onclick="PF('noRcAlert').hide()">
                                <p:graphicImage  width="20" height="20" library="images" 
                                                 id="imgGoogdle" name="googleIcon.png"
                                                 style="margin-right:5px"/>Sign in With Google 

                            </h:commandLink>        
                            <p:spacer width="4px"/>
                            <h:commandLink styleClass="btn"  immediate="true" 
                                           title="Outlook" actionListener="#{userEnrollmentService.hideRFPanelFromO}"
                                           style="width: 165px; border: 1px; border-style: solid;  background: white;"  
                                           action="#{userEnrollmentService.redirectEmailLogin(2)}" 
                                           onclick="PF('noRcAlert').hide()">
                                <p:graphicImage  width="20" height="20" library="images" 
                                                 id="imgMicdle" name="microsoft.png"
                                                 style="margin-right:5px"/>Sign in With Outlook 
                            </h:commandLink>


                        </div>
                        <!--                    <p:commandButton value="OK" class="btn btn-primary btn-xs "  
                                                             actionListener="#{userEnrollmentService.changeEmailStatusOk}"
                                                             update=":usrWizardForm:curStatus :usrWizardForm:chkBox"
                                                             oncomplete="PF('noRcAlert').hide()" style="font-size: 14px" /> -->
                    </p:dialog>

                    <p:confirmDialog header="Confirmation" widgetVar="authModeDlg" closable="false" >
                        <f:facet name="message" >
                            <p:panel style="font-size: 14px" > Your previous signed up configuration will be lost. 
                                Are you sure to proceed?
                            </p:panel>   
                        </f:facet>
                        <div  class="div-center" >
                            <p:commandButton   styleClass="ui-confirmdialog-yes btn btn-success btn-xs" 
                                               actionListener="#{userEnrollmentService.changeAuthMode}"                                            
                                               value="Yes" immediate="true"
                                               update=":usrWizardForm" oncomplete="PF('authModeDlg').hide();" />
                            <p:spacer width="4px" />
                            <p:commandButton value="No" 
                                             styleClass="ui-confirmdialog-no btn btn-danger btn-xs"  
                                             oncomplete="PF('authModeDlg').hide();"  />
                        </div>
                    </p:confirmDialog>
                    <!--<div class="g-signin2" data-onsuccess="onSignIn" data-theme="dark" ></div>-->
                </h:form>
                <!--</div>-->
            </div>
        </div>

        <style>

            .login-forms{
                /*                position:relative;
                                width:700px;*/
                margin:20ex auto 2ex auto;
            }
            body .ui-panel.ui-widget {
                border: none;
            }
            .box {
                /*                margin: 50px;
                                width: 400px;
                                height: 200px;*/
                border: 1px solid black;
            }
            .box > h4 {
                position: absolute;
                background: white;
                height: 20px;
                margin-top: -12px;
                margin-left: 10px;
                padding: 0 10px;
            }

            .ui-wizard-step-title {
                /*width: 16% !important;*/
                text-align: center !important;
                margin-left: 2px !important;
                /*background: #d1dadb;*/
                background: #f7beaa;
                /*line-height: 65px;*/
                font-size: 15px !important;
                /*font-weight: bold !important;*/
                /*d*/
                /*                font-size: 15px !important;
                                font-weight: bold !important;
                                line-height: 215% !important;*/
                overflow-wrap: break-word !important;
                word-break: break-word !important;
                word-wrap: break-word !important;
            }
            .ui-wizard li.ui-wizard-step-title.ui-state-highlight { 
                background: #f17d54 !important;
                color: white !important;
                border-color: #f17d54 !important;
            }

            .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-back {
                /*margin: 4px;*/
                margin-left: 4px;
                margin-right: 4px;
                margin-bottom:  4px;
                margin-top: 10px;
            }

            .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-next {
                /*margin: 4px;*/
                margin-left: 4px;
                margin-right: 4px;
                margin-bottom:  4px;
                margin-top: 10px;
            } 

            .info-msg {
                /*color: #059;*/
                /*background-color: #d7edf4;*/ 
                font-size: medium;
                text-align: center;
                font-weight: 300;
            }
            .info-msg-b {
                /*color: #059;*/
                /*background-color: #d7edf4;*/ 
                font-size: medium;
                text-align: center;
                font-weight: 600;
            }
            .tab-contents {
                height: 60vh;
                /*                padding-left: 10px;
                                padding-right: 10px;*/
            }
            body{
                overflow: hidden;
            }

            /*            .ui-wizard-navbar {
                            bottom: 0;
                            position: absolute;
                        }*/
            .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-back {
                /*margin: 4px;*/
                margin-left: 4px;
                margin-right: 4px;
                margin-bottom:  4px;
                margin-top: 10px;
            }

            .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-next {
                /*margin: 4px;*/
                margin-left: 4px;
                margin-right: 4px;
                margin-bottom:  4px;
                margin-top: 10px;
            } 
            .ui-wizard-navbar {
                text-align: right;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .ui-wizard-step-titles,.ui-helper-reset,.ui-helper-clearfix{
                display: flex!important;
                align-items: center!important;
                justify-content: center!important;
            }
            .ui-progressbar-determinate,.ui-progressbar-label {
                margin-top: 3px!important;
                color: white!important;
            }
            .ui-progressbar {
                height: 2em!important;
                width:  15em!important;
            }
            .required:after{content:" * ";color: red;}
            body .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content { 
                text-align: center!important;
            }
            .uiii{
                /*                 font-size: 15px !important;
                                font-weight: bold !important;*/
                /*line-height: 1.8!important;*/
            }
            .imgClassName{ 
                /*                                    background: url("#{request.contextPath}/resources/images/link-add.png") !important;
                                                    background: url("#{request.contextPath}/resources/images/excel27.png") !important;
                                                    padding: 48px;
                                                    margin-top: -10px;
                                                    margin-left: -13px;                */
            }



            .firstTab{
                line-height: 43px;
                text-align:center;
                height:60px;
                width:210px;
                -webkit-clip-path: polygon(0% 0%, 90% 0%, 100% 50%, 90% 100%, 0% 100%);
                clip-path: polygon(0% 0%, 90% 0%, 100% 50%, 90% 100%, 0% 100%);
            }

            .middleTab{
                line-height: 43px;
                text-align:center;
                margin-left: -30px !important;
                height:60px;
                width:210px;
                -webkit-clip-path: polygon(90% 0%, 100% 50%, 90% 100%, 5% 100%, 15% 50%, 5% 0%);
                clip-path: polygon(90% 0%, 100% 50%, 90% 100%, 5% 100%, 15% 50%, 5% 0%);
            }

            .lastTab{
                line-height: 43px;
                text-align:center;
                margin-left: -85px !important;
                height:60px;
                width:220px;
                -webkit-clip-path: polygon(25% 0%, 100% 1%, 100% 100%, 25% 100%, 55% 50%);
                clip-path: polygon(25% 0%, 100% 0%, 100% 100%, 25% 100%, 50% 50%);
            }

            #header {
                width: 100%;
                background-color: red;
            }
            .container {
                width: 300px;
                margin: auto;
            }

            #first {
                width: 100px;
                float: left;
                height: 300px;
                background-color: blue;
            }

            #second {
                width: 200px;
                float: left;
                height: 300px;
                background-color: green;
                color: white;
            }

            #clear {
                clear: both;
            }
            a {
                color: white;
            }
            #first, #second {
                float: left;
            }

            #boxes {
                /*    width:100%;*/
                display: flex;
                position: absolute;
                top: 50%;
                left: 50%;
                margin-right: 18%;
                transform: translate(-50%, -50%);
            }

            #boxes .box {
                /*height: 90px;*/
                margin: 20px;
                width: 500px;
                line-height: 50px;
                /*    text-align: center;
                    border: 1px solid Black;*/
            }

            #boxes .elm {
                font-size: 0;
            }
            .inline { 
                display: inline-flex; 
                /*margin-left: 10%;*/
                padding: 15px;
/*                //12-02-2024 : #13043 : ESCALATIONS CRM-7709   User enrollment is letting new users create INVALID passwords and does not RE-->*/
                margin-top:-10px;
                /*line-height: 35px;*/
            }
            .ui-progressbar .ui-progressbar-value {
                border: 1px solid #319299;
                background: #319299;
            }
            .ui-progressbar-determinate .ui-progressbar-label {
                text-align: left; padding-left:10px;}

            .ui-progressbar-determinate .ui-progressbar-value {
                margin: 0px;}

            .btn-primary {
                color: #fff !important;
                background-color: #f17d54 !important;
                border-color: #f17d54 !important;
            }

            .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-back {
                color: #fff;
                background-color: #319299 !important;
                border-color: #319299 !important;
            }
            .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-next {
                color: #fff !important;
                background-color: #f17d54 !important;
                border-color: #f17d54 !important;
            }
            .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-back:active, .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-back.ui-state-active {
                background-color: #319299 !important;
            }
            .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-back:hover, .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-back.ui-state-hover {
                background-color: #319299 !important;
            }
            .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-back:focus, .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-back.ui-state-focus {
                background-color: #319299 !important;
            }
            .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-next:active, .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-next.ui-state-active {
                background-color: #f17d54 !important;
            }
            .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-next:hover, .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-next.ui-state-hover {
                background-color: #f17d54 !important;
            }
            .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-next:focus, .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-next.ui-state-focus {
                background-color: #f17d54 !important;
            }
            button.ui-button.btn-primary:active, button.ui-button.btn-primary.ui-state-active {
                background-color: #f17d54 !important;
            }
            button.ui-button.btn-primary:hover, button.ui-button.btn-primary.ui-state-hover {
                background-color: #f17d54 !important;
            }
            button.ui-button.btn-primary:focus, button.ui-button.btn-primary.ui-state-focus {
                background-color: #f17d54 !important;
            }
            .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-back.ui-state-disabled:active, .ui-wizard .ui-wizard-navbar button.ui-wizard-nav-back.ui-state-disabled.ui-state-active {
                background-color: #319299 !important;
            }
            .hide-btn{
                display: none !important;

            }

            /*//12-06-2023 : #11375 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-*/
            .ui-replogo{
                padding: 10px!important; 
                margin-top: -28px;  
                margin-left: -85px;
            }

            .ui-othrlogos{
                padding: 10px!important;
                margin-top: -28px; 
                margin-left: -85px;
                width:230px;
                height:70px;
            }

            .ui-usrenrlmnt-replogo{
                padding: 10px!important;
            }
            .ui-usrenrlmnt-othrlogo{
                padding: 10px!important;
                width:230px;
                height:70px;
            }
            .ui-usrenrlmnt-othrlogodlg{
                width:230px;
                height:70px;
            }
        </style>
        <script>
            function zxz() {
                location.reload(true);
                document.getElementByClassName("ui-wizard-nav-back")[0].removeAttribute('ui-state-disabled');
                $("div.ui-wizard-navbar>button.ui-wizard-nav-back").css("display", "none");


            }
        </script>


        <script>

            function onSignIn(googleUser) {
                // Useful data for your client-side scripts:
                var profile = googleUser.getBasicProfile();
                const firstName = profile.getGivenName();
                const lastName = profile.getFamilyName();
                const email = profile.getEmail();
                console.log("firstName + " + firstName);
                console.log("lastName + " + lastName);
                console.log("email + " + email);
                console.log("User is " + JSON.stringify(profile));
                console.log("ID: " + profile.getId()); // Don't send this directly to your server!
                console.log('Full Name: ' + profile.getName());
                console.log('Given Name: ' + profile.getGivenName());
                console.log('Family Name: ' + profile.getFamilyName());
                console.log("Image URL: " + profile.getImageUrl());
                console.log("Email: " + profile.getEmail());

                // The ID token you need to pass to your backend:
                var id_token = googleUser.getAuthResponse().id_token;
                var access_token = googleUser.getAuthResponse(true).access_token;
                console.log("ID Token: " + id_token);
                console.log("ID access_token:  " + access_token);

//location.replace("http://**************:8080/gooAuth/index.xhtml")
            }

            function ovBodyAuto() {
                $('html, body').css('overflow', 'auto');
            }

            function ovBodyHidden() {
                $('html, body').css('overflow', 'hidden');
            }
        </script>
    </h:body>
</html>