<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
      <!--    Bug #7814 CRM-5082 CHRIS: Support Ticket - emails/Pitches/Opp with Action - Font size doesn't match-->
    <script>
//        $(".ui-editor-button").on('click', function() {
//            if ($(this).attr("title") == "Show Source") {
//                console.log("show");
//                $(".ui-overlay-visible .ui-editor iframe").attr("style", "display: inline !important");
//            }
//            if ($(this).attr("title") == "Show Rich Text") {
//                console.log("hide");
//                $(".ui-overlay-visible .ui-editor iframe").attr("style", "display: none !important");
//            }
//        });

        function loadSummerNotes(a) {
      
            switch (a) {
                case 'CUSTOM_EMAIL':
                    $('#emailSummerNote').summernote({
                        toolbar: [
                            ['style', ['style']],
                            ['font', ['bold', 'italic', 'underline', 'clear', 'strikethrough', 'superscript', 'subscript']],
                            ['fontname', ['fontname']],
                            ['fontsize', ['fontsize']],
                            // ['fontsizeunit', ['fontsizeunit']],
                            ['color', ['color']],
                            ['para', ['ul', 'ol', 'paragraph']],
                            ['height', ['height']],
                            ['table', ['table']],
                            ['insert', ['link', 'picture', 'video']],
                            ['view', ['fullscreen', 'undo', 'redo', 'hr', 'codeview', 'help']]
                        ], fontSizes: ['8', '9', '10', '11', '12', '14', '18', '24', '36', '48', '64', '82', '150'], airMode: false, height: 250, minHeight: 200, maxHeight: 250, disableResizeEditor: true

                    });

                    $('div.note-editable').height(250);

                    break;

                default:
                    $('#emailSummerNote').summernote({

                        toolbar: [
                            ['style', ['style']],
                            ['font', ['bold', 'italic', 'underline', 'clear', 'strikethrough', 'superscript', 'subscript']],
                            ['fontname', ['fontname']],
                            ['fontsize', ['fontsize']],
                            // ['fontsizeunit', ['fontsizeunit']],
                            ['color', ['color']],
                            ['para', ['ul', 'ol', 'paragraph']],
                            ['height', ['height']],
                            ['table', ['table']],
                            ['insert', ['link', 'picture', 'video']],
                            ['view', ['fullscreen', 'undo', 'redo', 'hr', 'codeview', 'help']]
                        ], fontSizes: ['8', '9', '10', '11', '12', '14', '18', '24', '36', '48', '64', '82', '150'], airMode: false, height: 250, minHeight: 200, maxHeight: 250, disableResizeEditor: true

                    });


                    $('div.note-editable').height(250);
                    $('#emailSummerNote').summernote('disable');
                    break;
            }
        }
        function loadEditorContent3(content, summerNoteId) {

            $(summerNoteId).html(content);
            $(summerNoteId).summernote('code', content);
        }
        function saveEmailContents1(summNoteId,formId) {
          
            var m = $(summNoteId).summernote('code');
     
            document.getElementById(formId + ':hiddenSummerNoteContents').value = m;
        }
        function disableEditor(){
            
            $('#emailSummerNote').summernote({
                        toolbar: [
                            ['style', ['style']],
                            ['font', ['bold', 'italic', 'underline', 'clear', 'strikethrough', 'superscript', 'subscript']],
                            ['fontname', ['fontname']],
                            ['fontsize', ['fontsize']],
                            // ['fontsizeunit', ['fontsizeunit']],
                            ['color', ['color']],
                            ['para', ['ul', 'ol', 'paragraph']],
                            ['height', ['height']],
                            ['table', ['table']],
                            ['insert', ['link', 'picture', 'video']],
                            ['view', ['fullscreen', 'undo', 'redo', 'hr', 'codeview', 'help']]
                        ], fontSizes: ['8', '9', '10', '11', '12', '14', '18', '24', '36', '48', '64', '82', '150'], airMode: false, height: 250, minHeight: 200, maxHeight: 250, disableResizeEditor: true

                    });
                    
                    $('div.note-editable').height(250);
                    $('#emailSummerNote').summernote('disable');
        }


    </script>
     <style>
         .modal-backdrop {
            z-index: 0;
        }
        ul li{
            /*    list-style: disc !important;*/
            list-style-position: inside;
            margin-left: 10px;
        }
        ol li{
            /*    list-style: decimal;*/
            list-style-position: inside;
            margin-left: 10px;
        }

        .note-btn{
            min-width: 50px !important;

        }
        
    </style>
</ui:composition>
