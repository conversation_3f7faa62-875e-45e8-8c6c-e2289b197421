<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: TestRebranding.xhtml
// 
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*  18-07-2023 : #11513 : CRM-7035   Repfabric Rebranding: Logo/URL changes for 404 page
*/-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">
    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>

    <ui:define name="head">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote.js"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css" />
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.4.1/js/bootstrap.min.js"></script>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote.css" rel="stylesheet"></link>

        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
    </ui:define>

    <ui:define name="metadata">
        <f:metadata> 
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('SUB_TABLES'))}" />
        </f:metadata>
    </ui:define>
    <ui:define name="title">
        <ui:include src="/help/Help.xhtml"/>
    </ui:define>
    <ui:define name="menu">

    </ui:define>
    <ui:define name="body">

        <div class="msg">
            <div class="top">
                <h:graphicImage library="images" name="#{fabricClient.clientType}/logo_bg.png" style=""/>
            </div>
            <div class="stregth-line"></div>
            <div class="h2">Internal server error occurred</div>
            <div style="text-align: center;font-size: 14px;">
                Go back to <a href="#{loginForm.getServerUrl()}/rfnextgen/">Home</a>
            </div>
            <div class="line2"></div>
            <a href="http://#{fabricClient.clientUrl}" target="_blank">#{fabricClient.clientUrl} </a>
        </div>
        <!--        <div class="box box-info box-body" style="position: absolute;top: 50%;-ms-transform: translateY(-50%);transform: translateY(-50%);">  
                    <h:form id="frmLoad">
                        <div class="ui-g ui-fluid">
                            <div class="ui-sm-12 ui-md-4 ui-lg-4" style="background-color: #ecf0f5;"></div>
                            <div class="ui-sm-12 ui-md-4 ui-lg-4" style="border-width: 1px 1px 1px 1px;border-color: red;border-style: solid;border-radius: 5px;">
                                <h:graphicImage library="images" name="#{fabricClient.clientType}/logo_bg.png" style="width:455px;margin-left: -7px;margin-top: -3px;"/>
                                <div class="line"></div>
                                <div class="info-msg">
                                    404 Error Exception
                                </div>  
                                <div class="line"></div>
                                <div style="text-align:center;margin-bottom: 10px;margin-top: 10px;padding: 10px;">
                                    <a href="http://#{fabricClient.clientUrl}" target="_blank">#{fabricClient.clientUrl} </a>
                                </div>
                            </div>
                            <div class="ui-sm-4 ui-md-12 ui-lg-4" style="background-color: #ecf0f5;"></div>
                        </div>
        
        
                    </h:form>
        
                </div>-->
        <style>
            .line {
                border-bottom: 1px solid black;
                margin-right: -7px;
                margin-left: -7px;
            }
            .info-msg {
                font-size: 30px !important;
                font-size: medium;
                text-align: center;
                padding: 20px;
                /*font-weight: bold;*/
            }
            .msg{
                height: 305px;
                margin: 10% auto 0;
                width: 570px;
                background: #fafafa;
                border-radius: 8px;
                -moz-border-radius: 8px;
                -webkit-border-radius: 8px;
                border: 1px solid #800000;
                text-align: center;
                box-shadow: 3px 3px 4px #961414;
            }
            .top{
                width: 100%;
                background: #fff;
                height: 85px;
                border-radius: 8px;
                -moz-border-radius: 8px;
                -webkit-border-radius: 8px;
            }
            .stregth-line{
                display: block;
                unicode-bidi: isolate;
                margin-block-start: 0.5em;
                margin-block-end: 0.5em;
                margin-inline-start: auto;
                margin-inline-end: auto;
                overflow: hidden;
                border-style: inset;
                border-width: 1px;
            }
            .h2{
                text-align: center;
                color: #990000;
                margin-top: 30px;
            }
            .line2{
                display: block;
                unicode-bidi: isolate;
                margin-block-start: 0.5em;
                margin-block-end: 0.5em;
                margin-inline-start: auto;
                margin-inline-end: auto;
                overflow: hidden;
                border-style: inset;
                border-width: 1px;
            }
        </style>

    </ui:define>


</ui:composition>

