<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:p="http://primefaces.org/ui">

    <p:outputLabel value="Attendees" style="font-weight: bold" />
    <p:dataTable rowSelectMode="add" style="height:215px;width:100%;" var="inv" value="#{orgCalendar.attendees}" selection="#{orgCalendar.selInvitee}" id="invtable" scrollable="true" scrollHeight="250"  rowKey="#{inv.recId}" resizableColumns="false">
        <!--//      #12787 CRM-7588	Calendar: events from Repfabric calendar entry not bringing contacts onto Outlook/gmail cal-->
        <p:column selectionMode="multiple" visible="#{loginBean.userId == orgCalendar.userId}" style="width:10px;text-align: center" rendered="#{orgCalendar.pastEventFlag and orgCalendar.event.eventParentId==0}"/>
        <p:ajax event="rowSelectCheckbox" process="@this"  listener="#{orgCalendar.inviteRendr()}" />
        <p:ajax event="rowUnselectCheckbox" process="@this"   listener="#{orgCalendar.inviteRendr()}"  />
        <p:ajax event="rowUnselect" process="@this"   listener="#{orgCalendar.inviteRendr()}"  />
        <p:ajax event="rowSelect" process="@this"   listener="#{orgCalendar.inviteRendr()}"  />
        <p:ajax event="toggleSelect" process="@this "  listener="#{orgCalendar.inviteRendr()}"/> 
        <p:column width="100" headerText="Name">
            #{inv.inviContFullName}
        </p:column>            
        <p:column width="100" headerText="Company">
            #{inv.inviContCompany}
        </p:column>
        <p:column width="80" headerText="Email"> 
            #{inv.inviContEmail1}
        </p:column>
        <p:column  width="20"  headerText="Reply">
            <div class="cntr"><h:graphicImage library="images" name="greenFlag.png" rendered="#{inv.inviContReply==1}" width="16" height="16" title="Confirmed"/>
                <h:graphicImage library="images" name="redFlag.png" rendered="#{inv.inviContReply==3}" width="16" height="16"  title="Declined"/>
                <h:graphicImage library="images" name="orangeFlag.png" rendered="#{inv.inviContReply==2}" width="16" height="16" title='Tentative'/>
            </div>
        </p:column> 
        <!--//      #12787 CRM-7588	Calendar: events from Repfabric calendar entry not bringing contacts onto Outlook/gmail cal-->
        <p:column width="20">                                   
            <p:commandButton styleClass="btn-danger btn-xs" disabled="#{loginBean.userId == orgCalendar.userId and orgCalendar.event.eventParentId!=0}" icon="fa fa-trash" actionListener="#{orgCalendar.setContactToDel(inv)}" oncomplete="PF('delDlg').show();"  style="height:25px;width:25px;" />    
        </p:column>  

    </p:dataTable> 
<br/><br/><br/><br/><br/>
        <!--//      #12787 CRM-7588	Calendar: events from Repfabric calendar entry not bringing contacts onto Outlook/gmail cal-->
    <p:commandButton id="addinv"  value="Existing Contacts" disabled="#{loginBean.userId == orgCalendar.userId and orgCalendar.event.eventParentId!=0}" styleClass="btn-primary btn-xs"  oncomplete="PF('contactTable').clearFilters(); PF('contactDialog').show()" />

    <p:spacer width="4"/>
            <!--//      #12787 CRM-7588	Calendar: events from Repfabric calendar entry not bringing contacts onto Outlook/gmail cal-->
    <p:commandButton  value="Email Address" disabled="#{loginBean.userId == orgCalendar.userId  and orgCalendar.event.eventParentId!=0}" styleClass="btn-primary btn-xs"  action="#{orgCalendar.clearEmail()}"  oncomplete="PF('emailDialog').show();" id="addemail" />                                    
    <p:spacer width="4"/>
            <!--//      #12787 CRM-7588	Calendar: events from Repfabric calendar entry not bringing contacts onto Outlook/gmail cal-->
    <p:commandButton  value="User" styleClass="btn-primary btn-xs" disabled="#{loginBean.userId == orgCalendar.userId  and orgCalendar.event.eventParentId!=0}"   oncomplete="PF('userDialog').show();PF('userTable').clearFilters()" id="adduser" action="#{orgCalendar.resetSelectedUsers()}" onclick="PF('userTable').clearFilters()"/>                                    
    <p:spacer width="4"/>
            <!--//      #12787 CRM-7588	Calendar: events from Repfabric calendar entry not bringing contacts onto Outlook/gmail cal-->
    <p:commandButton   value="Invite" styleClass="btn-primary btn-xs" disabled="#{loginBean.userId == orgCalendar.userId  and orgCalendar.event.eventParentId!=0}"   rendered="#{orgCalendar.sendinvite}" id="invite"  action="#{orgCalendar.send()}" >

    </p:commandButton>

    <p:spacer width="4"/>
            <!--//      #12787 CRM-7588	Calendar: events from Repfabric calendar entry not bringing contacts onto Outlook/gmail cal-->
    <!--// 22-03-2022 : #7545 : Calendar search>Search user which has single quote in his username, Calendar search not working-->
    <p:commandButton value="Done" disabled="#{loginBean.userId == orgCalendar.userId  and orgCalendar.event.eventParentId!=0}" update=":formCal" action="#{orgCalendar.eventLoad(null, null)}" styleClass="btn-primary btn-xs"  onclick="PF('eventDialog').hide();" />                                    
    <br/>
    <style>
        .cntr{
            text-align:center;
            padding: 9px;
        }
    </style>

</ui:composition>

