<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui">
<p:dialog  id="PrinciEventDialog" header="Select Contacts" widgetVar="dlgContact" modal="true">
        <h:form id="contForm">
            <p:dataTable style="width: 433px" value="#{orgTasks.contactList}" var="cont" filteredValue="#{orgTasks.filteredContactList}"
                         selection="#{orgTasks.selectedContactList}" rowKey="#{cont.recId}" paginator="true" paginatorPosition="top"
                         rows="10" widgetVar="contDt" rowSelectMode="add"  lazy="true">
                <p:column selectionMode="multiple" style="width:16px;text-align: center"/>
                <p:column headerText="Name" filterBy="#{cont.contFullName}" filterValue="#{cont.contFullName}" 
                          filterMatchMode="contains" sortBy="#{cont.contFullName}">
                    #{cont.contFullName}
                </p:column>
              
            </p:dataTable>
            <!--#1412 - Task Updates > #1 Enable the selection of any company/contact - sharvani - 10-08-2020-->
            <center>
                <!--<p:commandButton value="OK" style="margin-top: 10px" oncomplete="PF('dlgContact').hide()" update=":addTaskForm:displyContact" />-->
            </center>
        </h:form>
    </p:dialog>
</ui:composition>