
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:f="http://java.sun.com/jsf/core">
     <!--<h4 class="sub_title">#{custom.labels.get('IDS_OPPS')} Information</h4>-->

    <p:dialog id="dlgoppTsk" widgetVar="oppTskDlg"  modal="true" width="1100"  header="Link #{custom.labels.get('IDS_OPPS')}"
              height="auto" maximizable="true" responsive="true" 
              closeOnEscape="true">
        <h:form id="frmOppTsk">

            <p:growl id="msg1" showDetail="false"/>
            <p:dataTable  id="dtOppTskList" widgetVar="oppTskListTable" 
                          value="#{orgTasks.oppLst}" var="opp" selectionMode="single" 
                          rowKey="#{opp.oppId}" selection="#{orgTasks.selOpp}" 
                          emptyMessage="No open #{custom.labels.get('IDS_OPPS')} found." 
                          paginator="true" rows="10" 
                          paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                          rowsPerPageTemplate="5,10,15" 
                          paginatorAlwaysVisible="true" class="tblmain">
                <p:ajax event="rowSelect"  listener="#{orgTasks.linkToOpp(orgTasks.selOpp)}" />
                <p:column filterBy="#{opp.custName}" filterMatchMode="contains" sortBy="#{opp.custName}" >
                    <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet> 
    <!--                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                    #{opp.getCompNameById(opp.oppCustomer)}
                </h:link>-->
                    #{opp.custName}
                </p:column>

                <p:column filterBy="#{opp.principalName}" filterMatchMode="contains" sortBy="#{opp.principalName}">
                    <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet> 
    <!--                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                    #{opp.getCompNameById(opp.oppPrincipal)}
                </h:link>-->
                    #{opp.principalName}
                </p:column>

                <p:column filterBy="#{opp.distriName}" filterMatchMode="contains" sortBy="#{opp.distriName}">
                    <f:facet name="header">#{custom.labels.get('IDS_DISTRI')}</f:facet> 
    <!--                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                    #{opp.distriName}
                </h:link>-->
                    #{opp.distriName}
                </p:column>


                <p:column filterBy="#{opp.oppCustProgram}" filterMatchMode="contains" sortBy="#{opp.oppCustProgram}">
                    <f:facet name="header">#{custom.labels.get('IDS_PROGRAM')}</f:facet> 
    <!--                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                    #{opp.oppCustProgram}
                </h:link>-->
                    #{opp.oppCustProgram}
                </p:column> 

                <p:column filterBy="#{opp.oppActivity}" filterMatchMode="contains" sortBy="#{opp.oppActivity}">
                    <f:facet name="header">#{custom.labels.get('IDS_ACTIVITY')}</f:facet>  
    <!--                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                    #{opp.oppActivity}
                </h:link>-->
                    #{opp.oppActivity}
                </p:column>
              <!--#13178  ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
                <p:column filterBy="#{opp.oppStatus}" filterMatchMode="contains" sortBy="#{opp.oppStatus}">
                    <f:facet name="header">#{custom.labels.get('IDS_ACT_STATUS')}</f:facet>  
    <!--                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                    #{opp.oppStatus}
                </h:link>-->
                    #{opp.oppStatus}
                </p:column>
                <p:column filterBy="#{opp.oppFollowUp}" filterMatchMode="contains" sortBy="#{opp.oppFollowUp}">
                    <f:facet name="header">#{custom.labels.get('IDS_FOLLOW_UP')}</f:facet> 
    <!--                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                    -->                        <p:outputLabel value="#{opp.oppFollowUp}" >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                    </p:outputLabel><!--

                </h:link>-->
                </p:column>
                <!--Feature #5025 RE: Repfabric / AVX CRMSync / Relabeling Fields / "Next Step" (Pending)-->
                <!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
                <p:column filterBy="#{opp.oppNextStep}" filterMatchMode="contains" sortBy="#{opp.oppNextStep}" styleClass="might-overflow">
                    <f:facet name="header">#{custom.labels.get('IDS_NEXT_STEP')}</f:facet> 
    <!--                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                    #{opp.oppNextStep}
                </h:link>-->
                    #{opp.oppNextStep}
                </p:column>
                <!--            Bug 1215:#388851-KAI closed opps showing up under company profile-->
                <p:column  filterMatchMode="contains" sortBy="#{opp.oppCloseStatus}">
                    <f:facet name="header">Status</f:facet> 
                    <p:outputLabel value="#{opp.oppCloseStatus==0?'Open':'Closed'}" />
                </p:column>
            </p:dataTable>
            <!--            <center>
                            <p:commandButton id="btnProcess" widgetVar="btnBulkProcess" styleClass="btn btn-primary btn-xs" value="Add">
            
                            </p:commandButton>
                        </center>-->
        </h:form>
    </p:dialog>



</ui:composition>


