<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui">

    <p:dialog header="#{custom.labels.get('IDS_CUSTOMER')} Lookup" widgetVar="dlgOrganizerCust"  
              modal="true">
        <h:form id="dtCustForm2">
            <p:dataTable value="#{viewCustomerMst.customerList}" filteredValue="#{viewCustomerMst.filteredCustomers}"
                         selection="#{lookupSelection.customer}"  rowKey="#{cust.custId}" selectionMode="single"
                         emptyMessage="No #{custom.labels.get('IDS_PRINCI')} found." paginator="true" rows="17" paginatorAlwaysVisible="false"
                         paginatorPosition="top"
                         var="cust" id="dtPrinc" styleClass="dt-lookup">
                <p:ajax event="rowSelect" update="@(.custBox)" oncomplete="PF('dlgOrganizerCust').hide();" listener="#{orgTasks.updateCustDlg}" />

                <p:column filterBy="#{cust.custName}"  filterMatchMode="contains" rendered="#{!(!lookupSelection.showAll and cust.custId==0)}">
                    <h:outputText value="#{cust.custName}" style="text-transform: capitalize;"/>
                </p:column>
            </p:dataTable>
        </h:form>
    </p:dialog>
</ui:composition>