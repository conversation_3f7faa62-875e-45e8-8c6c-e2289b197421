<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui">

    <p:dialog header="#{custom.labels.get('IDS_PRINCI')} Lookup" widgetVar="dlgOrganizerPrinci"  
              modal="true">
        <h:form id="dtPrincipalForm">
            <p:dataTable value="#{viewPrincipalMst.princiList}" filteredValue="#{viewPrincipalMst.filteredPrinciList}"
                         selection="#{lookupSelection.principal}"  rowKey="#{princi.princiId}" selectionMode="single"
                         emptyMessage="No #{custom.labels.get('IDS_PRINCI')} found." paginator="true" rows="17" paginatorAlwaysVisible="false"
                         paginatorPosition="top"
                         var="princi" id="dtPrinc" styleClass="dt-lookup">
                <p:ajax event="rowSelect" update="@(.princiBox)" oncomplete="PF('dlgOrganizerPrinci').hide();" listener="#{orgTasks.updatePrinci}" />

                <p:column filterBy="#{princi.princiName}"  filterMatchMode="contains" rendered="#{!(!lookupSelection.showAll and princi.princiId==0)}">
                    <h:outputText value="#{princi.princiName}" style="text-transform: capitalize;"/>
                </p:column>
            </p:dataTable>
        </h:form>
    </p:dialog>
</ui:composition>