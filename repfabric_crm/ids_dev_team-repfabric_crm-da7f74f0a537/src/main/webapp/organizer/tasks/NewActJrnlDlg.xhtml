<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:f="http://java.sun.com/jsf/core">
    <p:dialog id="dlgNewActJrnlTsk" widgetVar="newActJrnlDlg"  modal="true" width="500"  header="Create #{custom.labels.get('IDS_ACT_JOURNALS')}"
              height="auto" responsive="true" 
              closeOnEscape="true">
       

<!--            //  Feature #6473 CRM-5008: need to be able to create <PERSON> from a Task-->
        <h:form id="addActJrnlForm" >
            <p:remoteCommand name="upButtons"  autoRun="false" update=":addTaskForm:butnRn :addTaskForm:selComp :addTaskForm:pnlContComp"  />
            <p:remoteCommand   name="applyCRMCust" actionListener="#{orgTasks.applyCustomerSelect(viewCompLookupService.selectedCompany)}" update=":addActJrnlForm:inpTxtCompName" />
            <p:remoteCommand name="applyActivePrinci" 
                             actionListener="#{orgTasks.applyPrinci(viewCompLookupService.selectedCompany)}" 

                             update=":addActJrnlForm:inpTxtPrinci" />
            <p:remoteCommand name="applyContacts" actionListener="#{orgTasks.applyTaskContacts(viewContLookupService.selectedContacts)}" update=":addActJrnlForm:lstContacts :addActJrnlForm:cntClr" />
            <p:growl id="msgs" life="6000"/> 

            <p:panelGrid id="pgAddActJrnl" layout="grid"  styleClass="box-primary no-border ui-fluid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-9">
                <h:outputText id="ajTxtDate" value="Date" styleClass="required" />
                <p:calendar   pattern="#{globalParams.dateFormat}" value="#{orgTasks.ajDate}" navigator="true"
                              id="date" requiredMessage="date not entered" converterMessage="Invalid date" >

                </p:calendar>
                <h:outputText id="ajTxtUser" value="User" styleClass="required" />
                <p:selectOneMenu id="usrLst" value="#{activityJournalHdr.acjhUser}"  widgetVar="usrLst"  style="width:225px">
                    <f:selectItems value="#{activityJournalHdr.listUsers}" var="lstUsr" itemLabel="#{lstUsr.userName}" itemValue="#{lstUsr.userId}" />
                    <p:ajax event="change" update=":addActJrnlForm:usrLst" />

                </p:selectOneMenu>
                <h:outputText id="ajTxtCom" value="Company" />
                <h:panelGroup class="ui-inputgroup">  
                    <p:inputText 
                        value="#{orgTasks.ajCustomerName}"
                        readonly="true" 
                        placeholder="[Not selected]" 
                        required="true" 
                        id="inpTxtCompName"   />

                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{viewCompLookupService.listCRM(2, 'applyCRMCust')}" 
                                      oncomplete="PF('lookupComp').show()" update=":formCompLookup"
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>
                <h:outputText id="ajTxtContact" value="Attendeees"  />
                <h:panelGroup class="ui-inputgroup"  >

                    <h:selectOneListbox  size="2" style="width: 100%!important;" styleClass="sel-conts" id="lstContacts"   >
                        <f:selectItems value="#{orgTasks.selectedContactList}" var="cont"  
                                       itemValue="#{cont.contId}"
                                       itemLabel="#{cont.contFullName}"  />

                    </h:selectOneListbox>
                    <p:commandButton  icon="fa fa-search" title="Choose Contacts" immediate="true" 
                                      actionListener="#{viewContLookupService.listAll('applyContacts')}"
                                      update=":frmContsLookup" oncomplete="PF('lookupContsDlg').show();"  
                                      styleClass="btn-info btn-xs" />
                    <p:commandLink id="cntClr"  value="Clear" actionListener="#{orgTasks.clearContacts()}" update="addActJrnlForm:lstContacts :addActJrnlForm:cntClr" 
                                   process="@this"  disabled="#{orgTasks.selectedContactList.size() ==null or orgTasks.selectedContactList.size()==0}" />
                </h:panelGroup>

                <h:outputText id="ajSubject" value="#{custom.labels.get('IDS_AJ_SUBJECT')}"  />
                <p:inputText id="txtActTitle" value="#{orgTasks.ajTitle}" maxlength="220" >
                    <p:ajax event="keyup" process="txtActTitle" listener="#{activityJournalHdr.onTitleChange}" />
                </p:inputText>



                <h:outputText id="ajTxtType" value="Type"  />
                <h:selectOneMenu id="type" value="#{activityJournalHdr.acthTypeId}" style="width:96%; background: #fff;margin:2px">
                   
                    <f:selectItems value="#{activityJournalHdr.typeMap}"/>    
                    <p:ajax event="change" update=":addActJrnlForm:type" />
                </h:selectOneMenu>

                <h:outputText id="ajTxtNotes" value="#{custom.labels.get('IDS_ACT_GENERAL_NOTE')}"  />
                <h:inputTextarea  value="#{orgTasks.acthDesc}" id="txtActTitle1" style="width: 96%;margin:2pxh" >
                    <p:ajax process=":addActJrnlForm:txtActTitle1"/>

                </h:inputTextarea>
                <h:outputText id="ajTxtPrinci" value="#{custom.labels.get('IDS_PRINCI')}"  styleClass="required" />
                <h:panelGroup class="ui-inputgroup">
                    <p:inputText value="#{orgTasks.ajPrincipalName}"
                                 readonly="true" 
                                 placeholder="[Not selected]" 

                                 id="inpTxtPrinci"   />

                    <p:commandButton   icon="fa fa-search" title="Choose Company" immediate="true"                                      
                                       actionListener="#{viewCompLookupService.listPrincipals(0, 'applyActivePrinci')}" 
                                       update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                       styleClass="btn-info btn-xs"  />
                </h:panelGroup>
                <h:outputText id="ajTxtFollowUp" value="Follow Up"  />
                <p:calendar    pattern="#{globalParams.dateFormat}" value="#{orgTasks.ajFollowDate}" navigator="true" 
                              id="followDate" requiredMessage="Follow Up date not entered" converterMessage="Invalid date" >
                    

                </p:calendar>

                <h:outputText id="ajTxtComments"  value="Comments" styleClass="required" />

                <p:inputTextarea id="ajInputComments"  immediate="true" rows="4"  value="#{orgTasks.ajComments}"  placeholder="Description"  maxlength="300" autoResize="false">
                    <p:ajax process=":addActJrnlForm:ajInputComments"/>
                </p:inputTextarea>

            </p:panelGrid>

            <h:panelGroup style="display: block;text-align: center">

                <p:commandButton  value="Save" styleClass="btn btn-primary btn-xs"  actionListener="#{orgTasks.saveActJournal()}" 
                                  id="ajLinkSave" update=":addActJrnlForm:txtActTitle :addActJrnlForm:msgs">
               
                </p:commandButton> 

                                 
                
                <p:spacer width="4" />
                <p:commandButton process="@this" value="Cancel" 
                                 id="ajLinkCancel"
                                 onclick="PF('newActJrnlDlg').hide();"
                                 styleClass="btn btn-warning btn-xs" />
            </h:panelGroup>

        </h:form>


    </p:dialog>


</ui:composition>