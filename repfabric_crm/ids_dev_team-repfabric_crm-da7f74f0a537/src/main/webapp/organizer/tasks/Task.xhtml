
<ui:composition 
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:adm="http://github.com/adminfaces"
    template="#{layoutMB.template}"
    xmlns:p="http://primefaces.org/ui">  
    <ui:define name="meta">
        <f:metadata>
            <!--#9022  ESCALATIONS CRM-5764  GEORGE: EIP-Rep instance: Contact Details page delayed response on Microsoft E-->
            <f:viewAction action="#{orgTasks.init()}"/>
            <f:viewParam id="tid" name="tid" value="#{orgTasks.recId}"/>
            <f:viewAction action="#{orgTasks.onTaskSelectFromIndex()}" />
            <!--            //  Feature #6473 CRM-5008: need to be able to create <PERSON> from a Task-->
            <f:viewAction action="#{activityJournalHdr.populateCallTypes()}"/>
            <f:viewAction action="#{activityJournalHdr.setActTypeid()}"/>
            <!--Related - CRM-1521: Tutorial button landing urls - Organizer - 28-11-2019-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('TASK_LIST'))}" />
            <!--29-09-2023  12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('TASK_LIST')}" />


        </f:metadata>
    </ui:define> 
    <!--PMS 357:Page Title - Companies, Contacts, AJ, Tasks, Messages, Calendar, Planner-->
    <ui:define name="head">
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
        <title>Tasks</title>
    </ui:define>

    <ui:define name="title">
        <div class="col-md-6">
            <h:form id="title">


                <p:outputLabel value="All" rendered="#{orgTasks.taskFilterVal==0}" style="font-weight: normal"/>
                <p:outputLabel value="Pending " rendered="#{orgTasks.taskFilterVal==1 and orgTasks.enable==1}" style="font-weight: normal"/>
                <p:outputLabel value="Completed " rendered="#{orgTasks.taskFilterVal==2 and orgTasks.enable==1}" style="font-weight: normal"/>
                <p:outputLabel value="High Priority" rendered="#{orgTasks.taskFilterVal==3 and orgTasks.enable==1}" style="font-weight: normal"/>
                <p:spacer width="4px"/>
                <p:outputLabel value="#{orgTasks.taskHeader}"  style="font-weight: normal"/>
            </h:form>
        </div>
        <ui:include src="/help/Help.xhtml"/>
    </ui:define>

    <ui:define name="body">

        <f:event listener="#{orgTasks.isPageAccessible}" type="preRenderView"/>
        <div class="box box-info box-body">
            <div class="left-column">
                <ui:include src="/organizer/tasks/LeftPanel.xhtml" />
            </div>
            <!--    PMS 2160:Task Enhancements - UI, Link to AJ/Opp-->
            <!--             PMS 2586: CRM-3605:Tasks-->
            <div class="right-column" >
                <ui:include src="/organizer/tasks/TaskList.xhtml" />
                <!--#5882: Remove unnecessary code/method calls-->
                <!--<ui:include src="/organizer/tasks/PrinciDlg.xhtml" />-->
                <!--<ui:include src="/organizer/tasks/CustDlg.xhtml" />-->
                <!--<ui:include src="/organizer/tasks/ContactDlg.xhtml" />-->
            </div>
        </div>
        <!--    PMS 2160:Task Enhancements - UI, Link to AJ/Opp-->
        <ui:include src="../../lookup/CompaniesLookup.xhtml"/>
        <ui:include src="OpportunityLookUp.xhtml"/>
        <ui:include src="AJLookup.xhtml"/>
        <ui:include src="NewActJrnlDlg.xhtml"/>




    </ui:define>

</ui:composition>

