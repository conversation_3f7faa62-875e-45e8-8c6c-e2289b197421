
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:f="http://java.sun.com/jsf/core">

    <p:dialog id="dlgAjTsk" widgetVar="tskAjDlg"  modal="true" width="1100"   header="Link #{custom.labels.get('IDS_ACT_JOURNALS')}"
              height="auto" responsive="true"  onShow="PF('tskAjDlg').initPosition();"
              closeOnEscape="true">
         <!--            //  Feature #6473 CRM-5008: need to be able to create AJ from a Task-->
        <f:facet name="header">
            <h:form>
                Link #{custom.labels.get('IDS_ACT_JOURNALS')}
                <p:spacer width="720" height="0"/>
                <p:commandButton class="btn btn-primary btn-xs button_top" title="Create #{custom.labels.get('IDS_ACT_JOURNALS')}" value="New" actionListener="#{orgTasks.clearQuickctJrnlFields()}" oncomplete="PF('newActJrnlDlg').show()" update=":addActJrnlForm"  >

                </p:commandButton>
            </h:form>
        </f:facet>

        <h:form id="frmAjTsk">
           



            <!--            <p:growl id="msg1" showDetail="false"/>-->
            <p:dataTable  id="dtAjTskList" widgetVar="ajTskListTable" 
                          value="#{orgTasks.ajLst}" var="aj" selectionMode="single" 
                          rowKey="#{aj.recId}" selection="#{orgTasks.selectedAJ}" 
                          emptyMessage="No open #{custom.labels.get('IDS_OPPS')} found." 
                          paginator="true" rows="10" 
                          paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                          rowsPerPageTemplate="5,10,15" 
                          paginatorAlwaysVisible="true" class="tblmain">
                <p:ajax event="rowSelect"  listener="#{orgTasks.linkToAJ(orgTasks.selectedAJ)}" />
                <p:column filterBy="#{aj.acjhDate}" filterMatchMode="contains" sortBy="#{aj.acjhDate}">
                    <f:facet name="header">Date</f:facet> 
                    <p:outputLabel  value="#{aj.acjhDate}">
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                    </p:outputLabel>
                </p:column>
                <p:column filterBy="#{aj.acjhTitle}" filterMatchMode="contains" sortBy="#{aj.acjhTitle}">
                    <f:facet name="header">Subject</f:facet> 
                        #{aj.acjhTitle}
                </p:column>
                <p:column filterBy="#{aj.acjdComment}" filterMatchMode="contains" sortBy="#{aj.acjdComment}">
                    <f:facet name="header">Comment</f:facet> 
                        #{aj.acjdComment}
                </p:column>
                <p:column filterBy="#{aj.custName}" filterMatchMode="contains" sortBy="#{aj.custName}" >
                    <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet> 
                        #{aj.custName}
                </p:column>

                <p:column filterBy="#{aj.princiName}" filterMatchMode="contains" sortBy="#{aj.princiName}">
                    <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet>
                        #{aj.princiName}
                </p:column>
                <p:column filterBy="#{aj.userName}" filterMatchMode="contains" sortBy="#{aj.userName}">
                    <f:facet name="header">User</f:facet> 
                        #{aj.userName}
                </p:column>
            </p:dataTable>
            <p:outputLabel value="Note: Lists only for last 3 months" id="actJournalNote"/>

        </h:form>
    </p:dialog>



</ui:composition>


