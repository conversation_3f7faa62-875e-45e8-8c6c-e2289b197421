<!--//**********************************************************
// Program: RepFabric Sys
// Filename: LeftPanel.xhtml
// Author: Nisha
//*********************************************************
/*
*
* Copyright (c) 2014 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://xmlns.jcp.org/jsf/core">
    <style>
        .ui-datepicker-inline .ui-datepicker .ui-widget .ui-widget-content .ui-helper-clearfix .ui-corner-all
        {
            width:100% !important;
        }
        .caltop{

            padding: 4px;
        }
    </style>
    <h:form id="leftPanelForm">
        <h3 class="sub_title" style="background:#c8d6f9;height:35px;font-size:18px;text-align:center;margin-top:5px;line-height:30px;">Filter By</h3>
        <div class="caltop">
            <p:selectOneMenu id="taskViewFilter" value="#{orgTasks.taskFilterVal}">
                <f:selectItem itemLabel="View All" itemValue="0"/>
                <f:selectItem itemLabel="View Pending" itemValue="1"/>
                <f:selectItem itemLabel="View Done" itemValue="2"/>
                <f:selectItem itemLabel="View High Priority" itemValue="3"/>
                <!--// 23-02-2022 : #7313 : Tasks> Clear filter is not working-->
                <!--//26-05-2022 :  #8061 : tasks->filter date issue-->
                <f:ajax execute="taskViewFilter" listener="#{orgTasks.taskViewDropDown()}" render=":orgTaskForm:dtTaskList :taskHeader :leftPanelForm:calendarSelect" />
                <p:ajax update=":title"/>
            </p:selectOneMenu>
        </div>
        <div class="caltop">
            <p:calendar mode="inline" id="calendarSelect" style="width:100%;"   widgetVar="calendar" value="#{orgTasks.selectedDate}">
                <p:ajax event="dateSelect" listener="#{orgTasks.showTasks('DATE')}" oncomplete="PF('taskListTable1').clearFilters();" update=":orgTaskForm:dtTaskList :title :leftPanelForm:taskViewFilter"/>  
                <p:ajax event="change" listener="#{orgTasks.showTasks('MONTH')}" update=":orgTaskForm:dtTaskList"/>
            </p:calendar>
        </div>

        <!--23-01-2024 12926 CRM-6489/CRM-7659 Tasks: Filtering Not Working Properly #4 Visibility Issue-->
        <div class="caltop" style="margin-top:10px">
            <p:selectOneMenu id="taskSelectUsers" rendered="#{orgTasks.showUserDropDown}"
                             value="#{orgTasks.userId}" filter="true" filterMatchMode="contains">
                <f:selectItems value="#{orgTasks.userList}" var="users" itemLabel="#{users.userName}" itemValue="#{users.userId}"/>
                <p:ajax event="itemSelect" listener="#{orgTasks.onChangeUser}" 
                        update=":orgTaskForm:dtTaskList :title :leftPanelForm:taskViewFilter"
                        oncomplete="PF('taskListTable1').clearFilters();"/>
            </p:selectOneMenu>
        </div>
    </h:form>

</ui:composition>

