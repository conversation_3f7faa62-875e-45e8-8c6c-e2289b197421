<!--//**********************************************************
// Program: RepFabric Sys
// Filename: TaskList.xhtml
// Author: Nisha
//*********************************************************
/*
*
* Copyright (c) 2015 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://xmlns.jcp.org/jsf/core">
    <h:form id="orgTaskForm"> 
        <!--Used for showing task dialog from Home page-->
        <p:remoteCommand autoRun="true" oncomplete="if(#{orgTasks.showTask}) PF('dlgTask').show()"  />
        <!--#5882: Remove unnecessary code/method calls-->
        <!--<p:remoteCommand autoRun="false" id="rcCust" name="loadCust" actionListener="#{viewCustomerMst.loadCustomers()}" update=":dtCustForm2" oncomplete="PF('dlgOrganizerCust').show();" />-->
         <!--<p:remoteCommand autoRun="false" id="rcPrinci1" name="loadPrinci" actionListener="#{viewPrincipalMst.loadPrinciList()}" update=":dtPrincipalForm" oncomplete="PF('dlgOrganizerPrinci').show();" />-->
        <!--#1412 - Task Updates > #1 Enable the selection of any company/contact - sharvani - 10-08-2020-->
        <!--         PMS 2160:Task Enhancements - UI, Link to AJ/Opp-->
        <p:remoteCommand name="applyComps" autoRun="false" actionListener="#{orgTasks.applyComps(lookupService.companies)}" update="@(.sel-comp) :addTaskForm:cmpClr" />
        <p:remoteCommand name="upButtons" autoRun="false" update=":addTaskForm:butnRn :addTaskForm:selComp :addTaskForm:pnlContComp"  />

<!--         <p:remoteCommand name="applyDBDCust" actionListener="#{orgTasks.applyTaskCustomer(viewCompLookupService.selectedCompany)}" update=":addTaskForm:txtCustomerName" />
         <p:remoteCommand name="applyActivePrinci" actionListener="#{orgTasks.applyTaskPrinci(viewCompLookupService.selectedCompany)}" update=":addTaskForm:txtPrinci" />-->
        <!--#1622 - CRM-3200: Tasks: assigning a contact to a task thats already been created not pulling in the contact - sharvani - 09-09-2020-->
        <p:remoteCommand name="applyContacts" actionListener="#{orgTasks.applyTaskContacts(viewContLookupService.selectedContacts)}" update=":addTaskForm:lstContacts :addTaskForm:cntClr" />
<!--<p:remoteCommand name="applyCont" actionListener="#{orgTasks.applyTaskContact(viewContLookupService.selectedContact)}{example.applyContacts(viewContLookupService.selectedContacts)}"  />-->
        <!--Create/ delete buttons-->
        <div class="button_bar">  
            <p:commandButton class="btn btn-primary btn-xs button_top" title="Create #{custom.labels.get('IDS_TASK')}" value="New" actionListener="#{orgTasks.clearFields(false)}" update=":addTaskForm :orgTaskForm:dtTaskList" oncomplete="PF('dlgTask').show()" >
                <f:actionListener binding="#{orgTasks.updateTaskAssignee()}"/>
            </p:commandButton>
            <p:spacer width="4"/>
            <p:commandButton class="btn btn-danger btn-xs button_top" title ="Delete multiple #{custom.labels.get('IDS_TASKS')}" value="Delete" actionListener="#{orgTasks.clearFields(true)}" update=":delTaskForm" oncomplete="PF('dlgDelTask').show();"/>
            <!--//26-05-2022 :  #8061 : tasks->filter date issue-->
            <p:commandButton actionListener="#{orgTasks.setSelectedDate(null)}" action="#{orgTasks.clearFilter()}" class="btn btn-primary btn-xs button_top" style="float: right" value="Clear filter" update=" :orgTaskForm:dtTaskList :leftPanelForm :title"/>
        </div>
        
        
        <!--Task list-->
        <p:dataTable id="dtTaskList" widgetVar="taskListTable1"  paginator="true"                     
                     value="#{orgTasks.taskList}" 

                     filterEvent="keyup"
                     filteredValue="#{orgTasks.filteredTasks}"
                     var="task"
                     rowKey="#{task.recId}"
                     selection="#{orgTasks.selectedTask}" 
                     selectionMode="single"                                       
                     paginatorAlwaysVisible="false"
                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                     rows="50"   class="tblmain"
                     emptyMessage="No #{custom.labels.get('IDS_TASKS')} found"
                     draggableColumns="true">


            <p:ajax event="rowSelect" listener="#{orgTasks.onTaskSelect}" update=":addTaskForm" oncomplete="PF('dlgTask').show()"/>

            <!--2341 - CRM-1416: Fwd: Search Issue? - 09-12-2019 - sharvani-->
            <!--01-12-2023 12622 CRM-7579 Tasks: Sort by Date not working-->
            <p:column style="width:10%;text-align: center;" id="taskDate" headerText="Due Date"  
                      filterBy="#{task.getFormattedDate(task.taskDate, 'da')}" filterMatchMode="contains" 
                      sortBy="#{task.taskDate}">            
                <h:outputText  value="#{task.taskDate}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                </h:outputText>
            </p:column>
            <!--                Task #6648 CRM-5102 : Opportunity Task Notifications-->
            <p:column headerText="Title" style="word-wrap: break-word;white-space: normal;width:25%" filterBy="#{task.taskTitle}" filterMatchMode="contains" sortBy="#{task.taskTitle}">
                <h:outputText  value="#{task.taskTitle}" />   
            </p:column>

            <p:column headerText="Description" style="word-wrap: break-word;white-space: normal;width:25%" filterBy="#{task.taskDescription}" filterMatchMode="contains" sortBy="#{task.taskDescription}" >
                <h:outputText value="#{task.taskDescription}" />
            </p:column>

            <p:column headerText="Assigned Users" style="width:25%" filterBy="#{task.taskAssigneeName}" filterMatchMode="contains" sortBy="#{task.taskAssigneeName}" >

                <h:outputText value="#{task.taskAssigneeName}"/>

            </p:column>

            <p:column style="width:80px;" headerText="Priority" sortBy="#{task.taskPriority}">
                <p:rating value="#{task.taskPriority}" readonly="true"/>
            </p:column>
            <p:column sortBy="#{task.taskDoneFlag}" headerText="Done" style="text-align: center;width:40px;" >
                <p:selectBooleanCheckbox id="cbDone" value="#{task.doneFlag}" >
                    <p:ajax process="cbDone" update=":orgTaskForm" event="change" listener="#{task.updateStatusFromOrganizer(task.recId,orgTasks)}"/>
                </p:selectBooleanCheckbox>

            </p:column>
            <p:column style="width:40px;">

                <p:commandButton class="btn-danger btn-xs"  action="#{orgTasks.deleteTask(task.recId)}" title="Delete #{custom.labels.get('IDS_TASK')}" icon="fa fa-trash" update=":orgTaskForm:dtTaskList" style="height:25px;width:25px;">
                    <p:confirm header="Confirmation" message="Are you sure to delete?" icon="ui-icon-alert" />
                </p:commandButton>

            </p:column>
        </p:dataTable>
    </h:form>

    <!--Confirm dialog-->
    <h:form>
        <div class="cntr">
            <p:confirmDialog id="cdTaskDelete" global="true" showEffect="fade" width="250px" >
                <div class="cntr"> <p:commandButton value="Yes" type="button" styleClass="btn btn-success btn-xs ui-confirmdialog-yes" />
                    <p:spacer width="4"/>
                    <p:commandButton value="No" type="button" styleClass="btn btn-warning btn-xs ui-confirmdialog-no"  /></div>
            </p:confirmDialog>
        </div>
    </h:form>
    <style>
        .first-col{
            margin: 0 auto;
        }
        .cntr{
            text-align:center;
            padding: 9px;
        }
    </style>

    <!--Add/Edit Task Dialog-->
    <h:form id="addTaskForm" >
        <p:growl id="growsl"/>
        <!--#1412 - Task Updates > #1 Enable the selection of any company/contact - sharvani - 10-08-2020-->
        <!--        PMS 2160:Task Enhancements - UI, Link to AJ/Opp-->
        <!--//22-10-2021 #6088: CRM-4866: Screen View Issue by Vithin-->
        <p:dialog id="taskDlg" header="#{orgTasks.dlgTaskHeader}" widgetVar="dlgTask" width="800" style="position:absolute;"
                  closeOnEscape="true" closable="true"  modal="true" responsive="true" height="500" resizable="false">
            <p:panelGrid id="taskDetails" columns="2"  layout="grid" columnClasses="ui-grid-col-6,ui-grid-col-12"  >
                <p:outputLabel  value="Title" styleClass="required"/>
                <!--                Task #6648 CRM-5102 : Opportunity Task Notifications-->
                <p:inputText  id="title" value="#{orgTasks.taskTitle}" style="width:100%" maxlength="120">
                    <p:ajax onstart="convertTitleCase()" />
                </p:inputText>

                <p:outputLabel  value="Due Date" />

                <p:calendar value="#{orgTasks.taskDate}" pattern="#{globalParams.dateFormat}" style="width:100%"  >
                </p:calendar>

                <p:outputLabel value="Description"/>

                <p:inputTextarea value="#{orgTasks.taskDescription}" style="width:100%" rows="5" autoResize="false"/>

                <p:outputLabel value="Priority"/>

                <p:rating value="#{orgTasks.taskPriority}" cancel="false"/>

                <!--        Feature  #7516    CRM-5149 CHRIS O:AJB: e-mail reminders for tasks in AJ-->

                <!--                 <p:outputLabel value="Notify by Email"/>-->

                <!--                <h:panelGroup>
                                    <p:selectOneRadio  widgetVar="emailNotify" value="#{orgTasks.taskEmailNotify}" style="width:200px;">
                                    <p:ajax process="@this" update="remDate"/>
                                    <f:selectItem itemValue="0" itemLabel="No" />    
                                    <f:selectItem itemValue="1" itemLabel="Yes"/>       
                                    
                                </p:selectOneRadio>-->

                <!--                </h:panelGroup>                     -->



                <p:outputLabel value="Status"/>

                <p:selectOneMenu value="#{orgTasks.taskDoneFlag}">
                    <f:selectItem itemValue="0" itemLabel="Not Done"/>
                    <f:selectItem itemValue="2" itemLabel="In Progress"/>
                    <f:selectItem itemValue="1" itemLabel="Done"/>
                </p:selectOneMenu>
                <p:outputLabel value="Created By"/>

                <!--CRM-107: Created By-->
                <p:outputLabel value="#{orgTasks.showCreatedBy(orgTasks.taskUserId)}"/>
                <p:outputLabel value="Assigned Users"/>







                <h:panelGroup class="ui-inputgroup"  >
                    <h:selectOneListbox size="3" style="width: 100%"  styleClass="sel-type" id="selType"  >
                        <f:selectItems value="#{orgTasks.selectedUsers}" var="user" itemLabel="#{user.userName}" itemValue="#{user.userName}" />  
                    </h:selectOneListbox>
                    <p:commandButton icon="fa fa-search" update=":useradd" immediate="true" styleClass="btn-info btn-xs" oncomplete="PF('userDialog').show();PF('userTable').clearFilters()"  />
                </h:panelGroup>






            </p:panelGrid>
            <!--#1412 - Task Updates > #1 Enable the selection of any company/contact - sharvani - 10-08-2020-->
               <!--<p:panel rendered="#{orgTasks.taskJournalHeaderId!=0}">-->
            <p:panel >
                <!--<p:panelGrid columns="2" layout="grid">-->
                <div class="ui-fluid">
                    <!--                    PMS 2160:Task Enhancements - UI, Link to AJ/Opp-->
                    <p:panelGrid id="pnlContComp" styleClass="box-primary no-border ui-fluid" layout="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-12">


                        <!--                     <p:outputLabel value="Contacts" />
                                            #1412 - Task Updates > #1 Enable the selection of any company/contact - sharvani - 10-08-2020
                                            <h:panelGroup class="ui-inputgroup"  >
                        -->                    
                        <!--                    <p:outputPanel id="displyContact" class="ui-selectonemenu-label ui-inputfield ui-corner-all potential-css " >
                                                <p:dataList value="#{orgTasks.selectedContactList}" var="cont" emptyMessage="No Contacts selected" class="grpBox">
                        #{cont.contFullName}
                    </p:dataList>
                </p:outputPanel>-->
                        <p:outputLabel for="lstContacts" value="Contacts"  />
                        <h:panelGroup class="ui-inputgroup" id="contPnl"  >

                            <h:selectOneListbox  size="2" style="width: 100%!important;" styleClass="sel-conts" id="lstContacts"   >
                                <f:selectItems value="#{orgTasks.selectedContactList}" var="cont"  
                                               itemValue="#{cont.contId}"
                                               itemLabel="#{cont.contFullName}"  />

                            </h:selectOneListbox>
                            <p:commandButton  icon="fa fa-search" title="Choose Contacts" immediate="true" 
                                              actionListener="#{viewContLookupService.listAll('applyContacts')}"
                                              update=":frmContsLookup" oncomplete="PF('lookupContsDlg').show();"  
                                              styleClass="btn-info btn-xs" />
                            <p:commandLink id="cntClr"  value="Clear" actionListener="#{orgTasks.clearContacts()}" update="addTaskForm:pnlContComp :addTaskForm:cntClr" 
                                           process="@this"  disabled="#{orgTasks.selectedContactList.size() ==null or orgTasks.selectedContactList.size()==0}" />
                        </h:panelGroup>
                        <!--                        PMS 2160:Task Enhancements - UI, Link to AJ/Opp-->
                        <p:outputLabel for="selComp" value="Companies"  />
                        <h:panelGroup class="ui-inputgroup" id="compPnl" >
                            <h:selectOneListbox  size="2" style="width: 100%!important;" styleClass="sel-comp" id="selComp"   >
                                <f:selectItems value="#{orgTasks.selectedCompanies}" var="comp"  
                                               itemValue="#{comp.compId}"
                                               itemLabel="#{comp.compName}"  />

                            </h:selectOneListbox>
                            <!--Task #1342:contact->export->buying from and companies filter refresh issue-->
                            <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select Company"
                                             actionListener="#{viewCompLookupService.listAll('applyComps')}" oncomplete="PF('lookupComps').show(); PF('dlgCompLookup').filter();"
                                             update=":formCompsLookup" />
                            <p:commandLink id="cmpClr"  actionListener="#{orgTasks.clearCompanies()}" value="Clear" update=":addTaskForm:pnlContComp :addTaskForm:cmpClr"  disabled="#{orgTasks.selectedCompanies.size()==0}" process="@this" />
                        </h:panelGroup> 





                        <!--                              #1412 - Task Updates > #1 Enable the selection of any company/contact - sharvani - 10-08-2020
                                                      <p:commandButton  icon="fa fa-search" title="Choose Contact" immediate="true" 
                                                              actionListener="#{viewContLookupService.listAll('applyCont')}" 
                                                              update=":formContLookup" oncomplete="PF('lookupCont').show()"  
                                                              styleClass="btn-info btn-xs" />-->
                        <!--</h:panelGroup>-->
    <!--                    <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}"/>
                   
                        #1412 - Task Updates > #1 Enable the selection of any company/contact - sharvani - 10-08-2020
                        <h:panelGroup class="ui-inputgroup"  >
                            <p:inputText value="#{orgTasks.taskCustName}" widgetVar="custId" styleClass="custBox" type="hidden"/>
                        <p:inputText value="#{orgTasks.taskCustName}"
                                                                 
                                     placeholder="[Not selected]"
                                     id="txtCustomerName"
                                     disabled="true"
                                     required="true" validatorMessage="Please select Customer"
                                     styleClass="custBox"
                                     />
                        <p:inputText value="#{orgTasks.taskCustName}" id="txtCustomerName" readonly="true"/>
                        #1412 - Task Updates > #1 Enable the selection of any company/contact - sharvani - 10-08-2020
                         <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_CUSTOMER')}" immediate="true" 
                                          actionListener="#{viewCompLookupService.listAll('applyDBDCust',2,0,1)}" 
                                          update=":formCompLookup"   oncomplete="PF('lookupComp').show()"
                                          styleClass="btn-info btn-xs" />
                        <p:spacer width="3" />
    
                    </h:panelGroup>
                       <p:outputLabel value="#{custom.labels.get('IDS_PRINCI')}"  />
                 
                    #1412 - Task Updates > #1 Enable the selection of any company/contact - sharvani - 10-08-2020
                        <h:panelGroup class="ui-inputgroup"  >
                        <p:inputText value="#{orgTasks.taskPrinciName}" widgetVar="princiId" styleClass="princiBox" type="hidden"/>
                        <p:inputText value="#{orgTasks.taskPrinciName}"
                                                                  
                                     placeholder="[Not selected]"
                                     id="txtPrinci"
                                     disabled="true"
                                     required="true" validatorMessage="Please select Principal"
                                     styleClass="princiBox"
                                     />
                        <p:inputText value="#{orgTasks.taskPrinciName}" id ="txtPrinci" readonly="true" />
                        #1412 - Task Updates > #1 Enable the selection of any company/contact - sharvani - 10-08-2020
                        <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_PRINCI')}" immediate="true" 
                                          actionListener="#{viewCompLookupService.listPrincipals(0, 'applyActivePrinci')}" 
                                          update=":formCompLookup" oncomplete="PF('lookupComp').show()"
                                          style="width:65px "
                                          styleClass="btn-info btn-xs" />
                        <p:spacer width="3" />
    
    
                       
                    </h:panelGroup>-->

                    </p:panelGrid>
                </div>
            </p:panel>
            <!--         PMS 2094 :Task Enhancements: Comments-->
            <div class="ui-fluid">
                <p:panelGrid id="taskCmt" styleClass="box-primary no-border ui-fluid" columns="2"  layout="grid" columnClasses="ui-grid-col-6,ui-grid-col-12">
                    <p:outputLabel  value="Comments" style="margin-left: 11px" />

                </p:panelGrid>
                <p:inputTextarea id="tskCmts" value="#{orgTasks.taskComments}" style="width:100%;" rows="5" autoResize="false"/>
            </div>
            <p:outputPanel id="butnRn">  
                <p:commandButton id="addButton" value="Save" class="btn btn-success btn-xs" action="#{orgTasks.saveTask('save')}" update=":orgTaskForm:dtTaskList">
                </p:commandButton>
                <p:spacer width="2px"/>
                <p:commandButton id="deleteButton" value="Delete" class="btn btn-danger btn-xs" action="#{orgTasks.saveTask('delete')}" oncomplete="PF('dlgTask').hide();" update=":orgTaskForm" rendered="#{orgTasks.recId!=0}">
                    <p:confirm header="Confirmation" message="Do you want to delete this #{custom.labels.get('IDS_TASK')}?" />
                </p:commandButton>      

                <p:spacer width="2px"/>
                <p:commandButton value="Cancel" class="btn btn-warning btn-xs" oncomplete="PF('dlgTask').hide();"  id="cancel"/>  

                <p:spacer width="20px"/>
                <!--#1414 - Task Updates:  #2: Use icon buttons to view linked Opp/AJ - sharvani - 13-08-2020-->
    <!--              <h:link target="_blank" onclick="assignWinName('/opploop/opportunity/OpportunityView.xhtml?opid=#{orgTasks.taskOppId}', 'opportunities', event)"
                        rendered="#{orgTasks.taskOppId != 0}">Open #{custom.labels.get('IDS_OPP')}</h:link>-->


                <!--#7606 ys-204 opportunity icon-->
                <p:commandButton   class="openorViewOpp" styleClass="openorViewOpp" style="width:25px; height:25px"
                                   rendered="#{orgTasks.taskOppId != 0}" 
                                   title="View  #{custom.labels.get('IDS_OPP')}"
                                   id="oppLnk"  
                                   onclick="assignWinName('/opploop/opportunity/OpportunityView.xhtml?opid=#{orgTasks.taskOppId}', 'opportunities', event)"/>   
                <!--            PMS 2160:Task Enhancements - UI, Link to AJ/Opp-->
                <!--#7606 ys-204 opportunity icon-->
                <p:commandButton   class="linkorCreateOpp" styleClass="linkorCreateOpp" style="width:25px; height:25px;" update=":frmOppTsk"
                                   rendered="#{orgTasks.taskOppId == 0 and orgTasks.recId!=0}" 
                                   id="oppNewLnk"  title="Link to #{custom.labels.get('IDS_OPP')}" action="#{orgTasks.loadOpportunities()}" />

                <p:spacer width="4px" />
                <!--#1414 - Task Updates:  #2: Use icon buttons to view linked Opp/AJ - sharvani - 13-08-2020-->
    <!--            <h:link target="_blank" onclick="assignWinName('/Journal/JournalEntry.xhtml?id=#{orgTasks.taskJournalHeaderId}', 'organizer', event)"
                        rendered="#{orgTasks.taskJournalHeaderId!=0 and orgTasks.taskJournalHeaderId!=null}">Open Journal</h:link>-->
                <p:commandButton    class="btn-info btn-xs" style="width:25px; height:25px"
                                    rendered="#{orgTasks.taskJournalHeaderId!=0 and orgTasks.taskJournalHeaderId!=null}" 
                                    icon="fa fa-book"  
                                    title="View #{custom.labels.get('IDS_ACT_JOURNAL')}" 
                                    onclick="assignWinName('/Journal/JournalEntry.xhtml?id=#{orgTasks.taskJournalHeaderId}', 'organizer', event)"/>
                <!--            PMS 2160:Task Enhancements - UI, Link to AJ/Opp-->
                <p:commandButton    style="width:25px; height:25px; background-color: grey !important;" class="btn-info btn-xs" 
                                    rendered="#{orgTasks.taskJournalHeaderId == 0 and  orgTasks.recId!=0 or  orgTasks.taskJournalHeaderId==null and orgTasks.recId!=0}" 
                                    icon="fa fa-book" actionListener="#{orgTasks.loadAJ()}" update=":dlgAjTsk"
                                    title="Link to #{custom.labels.get('IDS_ACT_JOURNAL')}"  />
            </p:outputPanel> 

        </p:dialog>
    </h:form>

    <!--Delete dialog-->
    <h:form id="delTaskForm" >
        <p:dialog id="delTaskDlg" resizable="false" width="500" modal="true" header="Delete #{custom.labels.get('IDS_TASKS')}" widgetVar="dlgDelTask"  >
            <p:panelGrid id="pgDelTask" layout="grid" columns="2">

                <p:outputLabel  value="Before Date" />

                <p:calendar value="#{orgTasks.beforeTaskDate}" pattern="#{globalParams.dateFormat}" >
                </p:calendar>

                <p:outputLabel value="Priority"/>
                <p:rating value="#{orgTasks.taskPriority}" cancel="false"/>

                <p:outputLabel value="Status"/>
                <p:selectOneMenu value="#{orgTasks.taskDoneFlag}"> 
                    <f:selectItem itemLabel="All" itemValue=""/>
                    <f:selectItem itemLabel="Done" itemValue="1"/>
                </p:selectOneMenu>

            </p:panelGrid>
            <br/>

            <p:commandButton value="Delete" class="btn btn-danger btn-xs" action="#{orgTasks.deleteTasks(1)}" oncomplete="PF('dlgDelTask').hide();" update=":orgTaskForm:dtTaskList">
                <p:confirm header="Confirmation" message="Are you sure to delete these #{custom.labels.get('IDS_TASKS')}?" icon="ui-icon-alert"/>
            </p:commandButton>
            <p:spacer width="4"/>

            <p:commandButton value="Cancel" class="btn btn-warning btn-xs" oncomplete="PF('dlgDelTask').hide();"  id="cancel"/> 
            <p:spacer width="4"/>
            <p:commandButton value="Delete All" class="btn btn-danger btn-xs" action="#{orgTasks.deleteTasks(0)}" oncomplete="PF('dlgDelTask').hide();" update=":orgTaskForm:dtTaskList">
                <p:confirm header="Confirmation" message="Are you sure to delete all #{custom.labels.get('IDS_TASKS')}?" icon="ui-icon-alert"/>
            </p:commandButton>
        </p:dialog>
    </h:form>



    <!--//22-10-2021 #6088: CRM-4866: Screen View Issue by Vithin-->
    <p:dialog widgetVar="userDialog" header="Users" modal="true" showEffect="clip" hideEffect="clip" resizable="false" width="800" height="550"  >                    
        <h:form id="useradd">
            <p:commandButton value="Add" action="#{orgTasks.onadd()}" update=" :addTaskForm:selType"   oncomplete=" PF('userDialog').hide(); PF('userTable').clearFilters() "  styleClass="btn btn-primary btn-xs"/>
            <p:dataTable class="dtable" rowSelectMode="add" id="userTable" value="#{users.assignedUsers}" var="user" selection="#{orgTasks.selectedUsers}" rowKey="#{user.recId}"
                         widgetVar="userTable">
                <p:column  selectionMode="multiple"   style="width: 30px;" />
                <p:ajax event="rowSelect"  listener="#{orgTasks.inviteRendr1}" update=":useradd" /> 
                <p:ajax event="rowUnselect"   listener="#{orgTasks.inviteRendr1}" update=":useradd"/> 
                <p:ajax event="toggleSelect"   listener="#{orgTasks.inviteRendr1}" update=":useradd"/> 

                <p:column  filterBy="#{user.userName}" filterMatchMode="contains" sortBy="#{user.userName}" headerText="Name"   >
                    #{user.userName}
                </p:column>
            </p:dataTable>

        </h:form>
    </p:dialog>  
    <h:form id="cnfDlg">
        <p:dialog  id="cnfMsgOp" widgetVar="cnfMsgOp" header="Confirmation" modal="true"   showEffect="fade">
            <div class="cntr">
                <p:outputLabel value="Task opportunity will be overwritten by the opportunity linked to the selected AJ." />
                <br/>
                <p:outputLabel id="cnfLbl" value="Do you want to proceed?" />
                <br/>
                <p:commandButton value="Yes"   class="btn btn-success btn-xs"
                                 oncomplete="PF('cnfMsgOp').hide();upButtons()" update=":addTaskForm:butnRn" 
                                 action="#{orgTasks.linkAJandOp(1)}" />
                <p:spacer width="4px"/>
                <p:commandButton value="No" update=":addTaskForm:butnRn" actionListener="#{orgTasks.clearLinks(2)}"  class="btn btn-danger btn-xs"  oncomplete="PF('cnfMsgOp').hide()" />
            </div>
        </p:dialog>
        <p:dialog  id="cnfMsgAj" widgetVar="cnfMsgAj" header="Confirmation" modal="true" showEffect="fade">
            <div class="cntr">
                <p:outputLabel value="Task Activity Journal will be overwritten by the Activity Journal linked to the selected opportunity."/>
                <br/>
                <p:outputLabel id="cnfAj" value="Do you want to proceed?" />
                <br/>
                <p:commandButton value="Yes"  class="btn btn-success btn-xs"
                                 actionListener="#{orgTasks.linkAJandOp(2)}" 
                                 update=":addTaskForm:butnRn" oncomplete="PF('cnfMsgAj').hide();upButtons()" />
                <p:spacer width="4px"/>
                <p:commandButton value="No" update=":addTaskForm:butnRn" actionListener="#{orgTasks.clearLinks(1)}" class="btn btn-danger btn-xs" oncomplete="PF('cnfMsgAj').hide()" /></div>
        </p:dialog>

    </h:form>
    <!--#1412 - Task Updates > #1 Enable the selection of any company/contact - sharvani - 10-08-2020-->
    <ui:include src="../../lookup/CompanyLookupDlg.xhtml"/>
    <ui:include src="../../lookup/ContactLookupDlg.xhtml"/>
    <ui:include src="../../lookup/ContactsLookup.xhtml" />

    <style>
        /*#7606 ys-204 opportunity icon*/
        .linkorCreateOpp{
            padding: 18px 0px !important;

        }
        .openorViewOpp{
            padding: 18px 0px !important;

        }
        .reducedWidth {
            width:40px; 
        }
        .cntr{
            text-align: center;
        }

    </style>
    <script type="text/javascript">
        function convertTitleCase() {
            var title = document.getElementById('addTaskForm:title');
            var currVal = $(title).val();
            //#352062 - Comp name java script issue with renaming: Saving the cursor position
            var curPos = title.selectionStart;
            $(title).val(currVal.charAt(0).toUpperCase() + currVal.slice(1));
            title.selectionStart = title.selectionEnd = curPos;
        }
    </script>
</ui:composition>

