<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:p="http://primefaces.org/ui">
    <style>
        #dialog .ui-dialog-content{
            overflow: hidden;
        }

        .color-Tomato{
            background-color: #ff6347;
            color: white;
        }

        .color-Tangerine{
            background-color: #f28500;
            color: white;
        }
        .color-Banana{
            background-color: #FFE135;
            color: blue;
        }

        .color-Basil{
            background-color: #579229;
            color: white;
        }

        .color-Sage{
            background-color: #9C9F84;
            color: white;
        }

        .color-Peacock{
            background-color: #326872;
            color: white;
        }

        .color-Blueberry{
            background-color: #4f86f7;
            color: white;
        }

        .color-Lavender{
            background-color: #734f96;
            color: white;
        }
        .color-Grape{
            background-color: #421C52;
            color: white;
        }

        .color-Flamingo{
            background-color: #fc8eac;
            color: white;
        }

        .color-Graphite{
            background-color: #474a51;
            color: white;
        }
        .linkedJrnl{
            text-decoration: underline;
        }

        .cntr{
            text-align:center;
            padding: 9px;
        }
    </style>

    <table style="width: 730px;" >
        <tr>
            <td>
              
                <p:outputLabel value="Invited By" rendered="#{orgCalendar.event.eventParentId !=0}"/>
            </td>
            <td colspan="5">
                <p:inputText value="#{orgCalendar.retrieveOrganizer(orgCalendar.event.eventParentId)}" style="width:87%" maxlength="60" rendered="#{orgCalendar.event.eventParentId !=0}" readonly="true"/>
            </td>
        </tr> 
        <tr style="margin-bottom: 4px;">
            <td>
                <p:outputLabel value="Title"  styleClass="required"/>
            </td>
            <td colspan="5">
                <p:inputText value="#{orgCalendar.event.title}" style="width:87%" maxlength="120" readonly="#{orgCalendar.event.eventParentId !=0}" required="true" requiredMessage="Title required"/>
            </td>
        </tr>  
        
        <tr>
            
            <td  >
                <p:outputLabel value="Location" />
            </td>
            <td colspan="5" style="padding-top: 5px;">
                <p:inputTextarea  id="loc" value="#{orgCalendar.event.eventLocation}" autoResize="false" style="width:560px; height: 100px; resize: none" maxlength="200"  rows="4" scrollHeight="80" disabled="#{orgCalendar.event.eventParentId !=0}"  />
            </td>
        </tr> 
        <tr>
            <td>
                <p:outputLabel value="Category" />
            </td>
            <td colspan="3">
                <p:selectOneMenu value="#{orgCalendar.event.eventType}" style="width:180px;" disabled="#{orgCalendar.event.eventParentId !=0}"> 
                    <f:selectItems value="#{orgCalendar.getTypemap()}"/>
                </p:selectOneMenu>
            </td>
            <td>
                <p:outputLabel value="Priority"  />

            </td>
            <td>
                <p:selectOneMenu value="#{orgCalendar.event.eventPriority}" style="width:180px;" disabled="#{orgCalendar.event.eventParentId !=0}"> 

                    <f:selectItem itemLabel="Low" itemValue="1" />
                    <f:selectItem itemLabel="Medium" itemValue="2" />
                    <f:selectItem itemLabel="High" itemValue="3" />

                </p:selectOneMenu>

            </td>
        </tr>
        <tr>
            <td>
                <p:outputLabel value="All day" />
            </td>
            <td style="padding: 5px;">
                <!--#14742 ESCALATION: CRM-8609repfabric calendar is showing everything on eastern standard time instead of A-->
                <!--//        #13003 YS-457  Calendar: meetings that span multiple days do not sync correctly-->
                <p:selectBooleanCheckbox value="#{orgCalendar.event.allDay}"  disabled="#{orgCalendar.event.eventParentId !=0}">
                    <p:ajax event="change" process="@this" listener="#{orgCalendar.processDate()}" update="startdate enddate  enddateAllDay " />
                </p:selectBooleanCheckbox><p:spacer width="8" /> 
            </td>
            <td></td>
            <td>
                <p:spacer width="10px"/>
                <p:outputLabel value="Private" />
                <p:spacer width="4px"/>
                <p:selectBooleanCheckbox value="#{orgCalendar.event.eventPrivateFlag}"  disabled="#{orgCalendar.event.eventParentId !=0}">
                </p:selectBooleanCheckbox>
            </td>  
        
            <td>
                <p:outputLabel value="Color"  />
            </td>
            <td>
                
                
                
                <p:selectOneMenu value="#{orgCalendar.event.color}" var="c" converter="colorConverter"  style="width:180px;" disabled="#{orgCalendar.event.eventParentId !=0}">
                    <f:selectItems value="#{eventColors.eventColors}" var="color"  itemLabel="#{color.colorName}" itemValue="#{color}" />
                    <p:column style="width: 10%">
                        <h:inputText readonly="true" value="" size="1" style="width:9px;height:9px;border-color:transparent;"  styleClass="#{c.colorClass}"/>
                    </p:column>
                    <p:column style="width: 90%">
                        <h:outputText value="#{c.colorName}"/>
                    </p:column>
                </p:selectOneMenu> 
                
            </td>
        </tr>

        <tr >
            <td>
                <p:outputLabel value="Start" />
            </td>
            <td colspan="3">
                <!--//        #13003 YS-457  Calendar: meetings that span multiple days do not sync correctly-->
                <p:calendar   readonlyInput="#{orgCalendar.readOnly}" showHour="#{orgCalendar.showHour}"   showMinute="#{orgCalendar.showMinute}" showButtonPanel="#{orgCalendar.showCalButton}" value="#{orgCalendar.event.startDate}" pattern="#{globalParams.dateTimeFormat}"  id="startdate" stepMinute="15"  readonly="#{orgCalendar.event.eventParentId !=0}">
                    <p:ajax event="dateSelect" listener="#{orgCalendar.incrementTime}" update="enddate" />
                </p:calendar>
            </td>
            <td>
                <p:outputLabel  value="End"/>
            </td>
            <td colspan="4">
                 <!--#14742 ESCALATION: CRM-8609repfabric calendar is showing everything on eastern standard time instead of A-->
                <!--//        #13003 YS-457  Calendar: meetings that span multiple days do not sync correctly-->
                <h:panelGroup id="enddate">
                <p:calendar  rendered="#{!orgCalendar.allDayFlag}"  defaultHour="#{orgCalendar.event.allDay ? '23': ''}" defaultMinute="#{orgCalendar.event.allDay ? '59': ''}"   readonlyInput="#{orgCalendar.readOnly}" showHour="#{orgCalendar.showHour}"    showMinute="#{orgCalendar.showMinute}" stepMinute="1" showButtonPanel="#{orgCalendar.showCalButton}" value="#{orgCalendar.event.endDate}"  pattern="#{globalParams.dateTimeFormat}"  style="width:100%"  readonly="#{orgCalendar.event.eventParentId !=0}">
                  <!--<p:ajax event="dateSelect" listener="#{orgCalendar.processEndDate}" />-->
                </p:calendar>
                 </h:panelGroup>
                
                 <!--//        #13003 YS-457  Calendar: meetings that span multiple days do not sync correctly-->
                 <h:panelGroup  id="enddateAllDay" >
                 <p:calendar  rendered="#{orgCalendar.allDayFlag}"  defaultHour="#{orgCalendar.event.allDay ? '23': ''}" defaultMinute="#{orgCalendar.event.allDay ? '59': ''}"   readonlyInput="#{orgCalendar.readOnly}" showHour="#{orgCalendar.showHour}"    showMinute="#{orgCalendar.showMinute}" stepMinute="1" showButtonPanel="#{orgCalendar.showCalButton}" value="#{orgCalendar.allDayEndDate}"  pattern="#{globalParams.dateTimeFormat}"   style="width:100%"  readonly="#{orgCalendar.event.eventParentId !=0}">
                  <p:ajax event="dateSelect" listener="#{orgCalendar.processEndDate}" />
                </p:calendar>
                </h:panelGroup>
            </td>

        </tr>
        <tr>
            <td>
                <p:outputLabel value="Reminder" />
            </td>
            <td colspan="3">
                <p:selectOneRadio  widgetVar="reminder"  value="#{orgCalendar.event.eventReminder}" disabled="#{orgCalendar.event.eventParentId !=0}">
                    <p:ajax process="@this" update="lblAlert selectedAlert"/>
                    <f:selectItem itemValue="0" itemLabel="Off"/>    
                    <f:selectItem itemValue="1" itemLabel="On"/>                     
                </p:selectOneRadio>

            </td>
            <td>
                <p:outputLabel id="lblAlert" value="Alert"  />
            </td>

            <td>

                <p:selectOneMenu id="selectedAlert" value="#{orgCalendar.event.eventAlertBefore}" disabled="#{orgCalendar.event.eventReminder ==0 || orgCalendar.event.eventParentId !=0}"  style="width:180px;" > 
                    <f:selectItem itemLabel="0 minutes before" itemValue="00" />
                    <f:selectItem itemLabel="5 minutes before" itemValue="05" />
                    <f:selectItem itemLabel="15 minutes before" itemValue="15" />
                    <f:selectItem itemLabel="30 minutes before" itemValue="30" />
                    <f:selectItem itemLabel="1 hour before" itemValue="60" />
                    <f:selectItem itemLabel="2 hours before" itemValue="120" />
                    <f:selectItem itemLabel="12 hours before" itemValue="720" />
                    <f:selectItem itemLabel="1 day before" itemValue="1440" />
                    <f:selectItem itemLabel="2 days before" itemValue="2880" />
                    <f:selectItem itemLabel="1 week before" itemValue="10080" />
                </p:selectOneMenu>
            </td>
        </tr>

        <tr>
            <td >
                <p:outputLabel value="Description" />
            </td>
            <td colspan="5">
                <p:inputTextarea value="#{orgCalendar.event.eventDesc}" style="width:87%; height: 100px; resize: none"   autoResize="false"  disabled="#{orgCalendar.event.eventParentId !=0}"/>
            </td>
        </tr>
        <tr><td colspan="6" >
                <div class="cntr">
                    <!--////    25-02-2022 : #7190 : CRM-5352  calendar: admin owners can view other calendars-->
                    <p:commandButton id="addButton" value="Save" disabled="#{loginBean.userId != orgCalendar.userId}" action="#{orgCalendar.addEvent()}" rendered="#{orgCalendar.event.eventParentId ==0}" update=":formCal:schedule :form3:message :formCal2 addinv deleteButton invite invtable" styleClass="btn btn-success  btn-xs"/>
                <p:spacer width="4px" rendered="#{orgCalendar.event.eventId != null and orgCalendar.event.eventParentId ==0}"/>
                <!--////    25-02-2022 : #7190 : CRM-5352  calendar: admin owners can view other calendars-->
                <p:commandButton id="deleteButton" value="Delete" disabled="#{loginBean.userId != orgCalendar.userId}"   styleClass="btn btn-danger  btn-xs" rendered="#{orgCalendar.event.eventId != null}">                                    
                    <p:ajax listener="#{orgCalendar.delCheck()}" oncomplete="PF('delDlg2').show(); " update=":form5:deleteDlg2 :form5:delNotify"/>
                </p:commandButton>

                <p:spacer width="4px"/>
<!--                3191    CRM-2000: Calendar: Adding calendar meeting within rf - no way to add contact without reopening-->
                <p:commandButton value="Cancel" onclick="PF('eventDialog').hide();" styleClass="btn btn-warning  btn-xs" type="button"/>
                <p:spacer width="4px"/>
               <!--////    25-02-2022 : #7190 : CRM-5352  calendar: admin owners can view other calendars-->
               <p:commandButton process="@this" styleClass="btn btn-primary  btn-xs" value="Create Journal" disabled="#{loginBean.userId != orgCalendar.userId}" onclick="
                        openJrnlTab('../Journal/JournalEntry.xhtml?eventid=#{orgCalendar.eventId}', 'journal', event)" rendered="#{orgCalendar.event.eventId != null and orgCalendar.jrnlId==0}"/>
                <p:commandButton styleClass="btn btn-primary  btn-xs" value="View Journal" onclick="
                        ;
                        openJrnlTab('../Journal/JournalEntry.xhtml?id=#{orgCalendar.jrnlId}', 'journal', event)" rendered="#{orgCalendar.event.eventId != null and orgCalendar.jrnlId!=0}"/>               
            </div></td></tr>
    </table>

</ui:composition>

