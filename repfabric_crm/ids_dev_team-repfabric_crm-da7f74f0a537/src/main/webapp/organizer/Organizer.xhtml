<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: Roles.xhtml
// Author: Sarayu
//*********************************************************
/*
*
* Copyright (c) 2015 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <!--PMS 357:Page Title - Companies, Contacts, AJ, Tasks, Messages, Calendar, Planner-->
    <ui:define name="head">
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
        <title>Calendar</title>
    </ui:define>

    <ui:define name="metadata">
        <ui:define name="meta">
            <f:metadata>

                <f:viewParam name="event" id="event" value="#{orgCalendar.eventId}"/>
                <f:viewAction action="#{orgCalendar.gotoCalendar()}" />
                <!--Related - CRM-1521: Tutorial button landing urls - Organizer - 28-11-2019-->
                <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('CAL_LIST'))}" />
                <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
                <f:viewAction action="#{helpService.setPageTag('CAL_LIST')}" />

            </f:metadata>           
        </ui:define> 
    </ui:define>

    <ui:define name="title">
        <h:form>
        </h:form>

        Calendar
        <ui:include src="/help/Help.xhtml"/>
    </ui:define>


 <!--14557 Fwd: RE: RS-22294 RE: Repfabric - Recht's Instance / System Audit Request-->
<!--    <ui:define name="menu">
        <h:form>

        </h:form>
        <ul class="sidebar-menu tree" >
            <h:form >

            </h:form>  
        </ul>
    </ui:define>-->



    <ui:define name="body">
        <f:event listener="#{orgCalendar.isPageAccessible}" type="preRenderView"/>

        <div class="box box-info box-body" style="vertical-align: top">


            <div class="left-column">
                <div class="leftCal">
                    <h:form >  
                        <!--14557 Fwd: RE: RS-22294 RE: Repfabric - Recht's Instance / System Audit Request-->
                        <!--25-02-2022 : #7326 :CRM-5400  calendar of other users: must search option for user-->
                        
                        <p:calendar  mode="inline"  widgetVar="calendar" value="#{orgCalendar.initialDate}" >
                            <p:ajax process="@this" event="dateSelect" update=":formCal:schedule"   listener="#{orgCalendar.selectView}"/>                      
                        </p:calendar>
                    </h:form>
                </div>
            </div>

            <div class="right-column">
                <h:form id="formCal">
                    <!--//04-04-2022 : #7653 : Calendar>Delete event which has attendees by clicking 'Delete&Notify' observe the 404 errr-->
                    <p:growl  id="message1"  />
                    <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                        <!--17-02-2022 : #7190 : CRM-5352  calendar: admin owners can view other calendars-->
                        <!--25-02-2022 : #7326 :CRM-5400  calendar of other users: must search option for user-->
<!--                           <p:autoComplete id="autoComp" rendered="#{(loginBean.loggedInUser.userAdminFlag eq 1) or (loginBean.loggedInUser.userCategory ==1)}"
                                         value="#{orgCalendar.userName}" completeMethod="#{orgCalendar.userAutoComplete}" scrollHeight="250">
                               <p:ajax event="itemSelect" listener="#{orgCalendar.changeEvent()}" update=":formCal:schedule"/>
                           </p:autoComplete>-->
                        <!--14557 Fwd: RE: RS-22294 RE: Repfabric - Recht's Instance / System Audit Request-->
                        <p:selectOneMenu id="selectUsers" rendered="#{(loginBean.loggedInUser.userAdminFlag eq 1) or (loginBean.loggedInUser.userCategory ==1)}"
                                         value="#{orgCalendar.userName}" filter="true" filterMatchMode="contains">
                            <f:selectItems value="#{orgCalendar.userList}" var="users" itemLabel="#{users.userName}" itemValue="#{users.userName}"/>
                            <p:ajax process="@this" event="itemSelect" listener="#{orgCalendar.changeEvent()}" update=":formCal:schedule"/>
                        </p:selectOneMenu>
                    </div>
                    <!--14557 Fwd: RE: RS-22294 RE: Repfabric -Recht's Instance / System Audit Request-->
<!--//             #14604  ESCALATIONS CRM-8543   Calendar Sync : Event not showing up on RF calendar-->
                    <p:schedule  id="schedule"  widgetVar="myschedule"   ignoreTimezone="false"   value="#{orgCalendar.eventModel}"  view="#{orgCalendar.view}" initialDate="#{orgCalendar.initialDate}" clientTimeZone="#{orgCalendar.timeZone}"  timeZone="#{orgCalendar.timeZone}" >
                        <p:ajax process="@this"   event="dateSelect" listener="#{orgCalendar.onDateSelect}" oncomplete="PF('eventDialog').show()" update=":formCal2 :formCal:schedule " />
                        <p:ajax process="@this"  event="eventSelect" listener="#{orgCalendar.onEventSelect}" oncomplete="PF('eventDialog').show()" update=":formCal2  :form5 "  />
                        <p:ajax process="@this"  event="eventMove" listener="#{orgCalendar.onEventMove}" update=":formCal:schedule"  />
                        <p:ajax process="@this"  event="eventResize" listener="#{orgCalendar.onEventResize}" update=":formCal:schedule" />
                    </p:schedule>
                </h:form>
                <h:form>
                    <!--14557 Fwd: RE: RS-22294 RE: Repfabric - Recht's Instance / System Audit Request-->
                    <p:remoteCommand autoRun="#{orgCalendar.showEventfromIndex}" oncomplete="if(#{orgCalendar.showEventfromIndex}) PF('eventDialog').show()" />
                </h:form>
            </div>

            <p:dialog widgetVar="eventDialog" header="Event Details" showEffect="clip" hideEffect="clip" id="dialog"  position="center" resizable="false">
                <p:ajax event="close" listener="#{orgCalendar.handleEventClose()}" />
                <h:form id="formCal2" >
                    <!--14557 Fwd: RE: RS-22294 RE: Repfabric -Recht's Instance / System Audit Request-->
                    <p:tabView id="eventtab" dynamic="true" activeIndex="#{orgCalendar.activeTab}" style="height:460px;width: 750px; "> 
                        <p:ajax  event="tabChange"  listener="#{orgCalendar.onTabChange}"    />
                        <p:tab title="Event" id="tab1" >
                            <ui:include src="EventInfoTab.xhtml"/> 
                        </p:tab> 
                        <!--//      #12787 CRM-7588	Calendar: events from Repfabric calendar entry not bringing contacts onto Outlook/gmail cal-->
                        <p:tab title="Invite" id="tab2" rendered="#{orgCalendar.event.eventId!=null}"  >
                            <ui:include src="EvntInviteesTab.xhtml"/>
                        </p:tab>

                    </p:tabView>
                </h:form>   
            </p:dialog>  
            <p:dialog widgetVar="emailDialog" header="Add Invitee" showEffect="clip" hideEffect="clip" id="addEmailDialog" width="350px" resizable="false" modal="true" >
                <h:form id="emailform">
                    <p:spacer width="20px"/>
                    <h:panelGrid columns="5" > 
                        <p:spacer width="40px"/>
                        <p:spacer width="4px"/>
                        <p:outputLabel value="Email" styleClass="required"/>
                        <p:spacer width="4px"/>
                        <p:inputText required="true" id="txtemail" maxlength="60" requiredMessage="Email adress required" value="#{orgCalendar.inviteeEmail}" validatorMessage="Invalid Email" >
                            <f:validateRegex
                                pattern="^[_A-Za-z0-9-\+]+(\.[_A-Za-z0-9-]+)*@[A-Za-z0-9-]+(\.[A-Za-z0-9-]+)*(\.[A-Za-z]{2,})$" />   
                        </p:inputText>
                    </h:panelGrid>
                    <hr/>
                    <div class="cntr">
                        <p:commandButton value="Add" styleClass="btn btn-primary btn-xs"  action="#{orgCalendar.addEmailInvitee()}"  oncomplete="if (args &amp;&amp; !args.validationFailed) PF('emailDialog').hide(); " update=":form3:message :formCal2:eventtab:invtable :formCal2:eventtab:invite :emailform" />
                    </div>
                </h:form>
            </p:dialog>  


            <h:form id="form5"> 
                <p:confirmDialog showEffect="fade" widgetVar="delDlg2" width="300px" header="Confirmation" 
                                 message="#{orgCalendar.message}" id='deleteDlg2'>
                    <div class="cntr">
                        <!--//04-04-2022 : #7653 : Calendar>Delete event which has attendees by clicking 'Delete&Notify' observe the 404 errr-->
                        <p:commandButton type="button" styleClass="btn btn-danger  btn-xs"  value="Delete" 
                                         update=":formCal2:invtable :formCal:message1" id="delEvent">                                    
                            <p:ajax listener="#{orgCalendar.deleteEvent()}" 
                                    oncomplete="PF('myschedule').update();PF('eventDialog').hide(); PF('delDlg2').hide();"  />
                        </p:commandButton>
                        <p:spacer width="4px"/>
                        <!--//04-04-2022 : #7653 : Calendar>Delete event which has attendees by clicking 'Delete&Notify' observe the 404 errr--> 
                        <p:commandButton type="button"  styleClass="btn btn-warning  btn-xs" value="Delete &amp; Notify " update=":formCal2:invtable :formCal:message1" id="delNotify" rendered="#{orgCalendar.pastEventFlag}">                                    
                            <p:ajax listener="#{orgCalendar.deleteEventAndNotify()}" oncomplete="PF('myschedule').update();PF('eventDialog').hide(); PF('delDlg2').hide();"  />
                        </p:commandButton> 
                        <p:spacer width="4"/>
                        <p:commandButton value="No" type="button" styleClass="btn btn-success  btn-xs" >
                            <p:ajax  oncomplete=" PF('delDlg2').hide()"/>
                        </p:commandButton>
                    </div>
                </p:confirmDialog>
            </h:form>



            <p:confirmDialog showEffect="fade" widgetVar="delDlg" header="Confirmation" message="Are you sure to delete this invitee?">
                <h:form id="form4">   
                    <div class="cntr">
                        <p:commandButton type="button"  styleClass="btn btn-success  btn-xs" value="Yes" update=":formCal2:invtable" >                                    
                            <p:ajax listener="#{orgCalendar.deleteInvitee(orgCalendar.remCon)}" update=":formCal2:eventtab:invtable :form3:message :form5" oncomplete="PF('delDlg').hide()"/>
                        </p:commandButton>
                        <p:spacer width="2px"/>
                        <a href="#{loginBean.emailURL}/action.php?action=write&amp;eventid=#{orgCalendar.event.eventId}&amp;reqid=#{loginBean.randomUid}&amp;write=1&amp;random=#{orgCalendar.genRandomNumber()}&amp;composeemail=1" target="_blank" styleClass="btn btn-warning  btn-xs"> 
                            <p:commandButton type="button"   value="Delete &amp; Notify " update=":formCal2:invtable" rendered="#{orgCalendar.pastEventFlag}" styleClass="btn btn-warning  btn-xs">                                    
                                <p:ajax listener="#{orgCalendar.removeInvitee(orgCalendar.remCon)}" update=":formCal2 :useradd" oncomplete="PF('delDlg').hide()"/>
                            </p:commandButton>
                        </a>                                 
                        <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no btn btn-danger btn-xs" >
                            <p:ajax  oncomplete="PF('delDlg').hide()"/>
                        </p:commandButton>
                    </div>
                </h:form>
            </p:confirmDialog>






            <p:dialog resizable="false" widgetVar="contactDialog" header="Contacts" showEffect="clip" hideEffect="clip">                    
                <h:form id="contactform">
                    <p:dataTable id="contactTable" value="#{viewContList.smanContacts}" var="cont" rows="8" selectionMode="single" selection="#{orgCalendar.selectedContact}" rowKey="#{cont.recId}"
                                 widgetVar="contactTable" filterEvent="keyup" filteredValue="#{viewContList.filteredContacts}" paginator="true" paginatorAlwaysVisible="false"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                                 paginatorPosition="top" style="width: 700px">
                        <p:ajax event="rowSelect" listener="#{orgCalendar.onRowSelectContact}" oncomplete="PF('contactDialog').hide(); " update=":form3:message :formCal2:eventtab:invtable :formCal2:eventtab:invite"/>
                        <p:column style="width: 30%" filterBy="#{cont.contFullName}" filterMatchMode="contains" sortBy="#{cont.contFullName}"  headerText="Name"  >
                            #{cont.contFullName}
                        </p:column>
                        <p:column style="width: 30%" filterBy="#{cont.contCompName}" filterMatchMode="contains"  headerText="Company" >
                            #{cont.contCompName}
                        </p:column>
                        <p:column style="width: 30%" filterBy="#{cont.contJobTitle}" filterMatchMode="contains" headerText="Job Title">
                            #{cont.contJobTitle}
                        </p:column>
                        <p:column style="width: 30%" filterBy="#{cont.contEmails}" filterMatchMode="contains" headerText="Business Email">
                            #{cont.contEmails}
                        </p:column>
                    </p:dataTable><br/>

                </h:form>
            </p:dialog>  



            <ui:include src="../lookup/CompanyLookupDlg.xhtml" />
            <ui:include src="../lookup/ContactLookupDlg.xhtml" />



            <h:form id="form3">
                <p:growl id="message"  />
                <p:confirmDialog global="true" showEffect="fade">
                    <p:commandButton value="Yes" type="button" styleClass="btn btn-success  btn-xs"  />
                    <p:commandButton value="No" type="button" styleClass="btn btn-danger  btn-xs" />
                </p:confirmDialog>
            </h:form> 


            <style>
                .no-header thead{
                    display: none;
                }
                .vos { 
                    display:block !important;
                }
                body .ui-panel.ui-widget {
                    height: 550px !important;
                    border: 0px !important;
                }
                .leftCal{
                    padding-top: 14px;
                    padding-left: 3px;
                    padding-right: 3px;

                }
                .viewCal{

                    height: 10px;

                }
                .dtable{
                    width: 600px;
                }

                .color-Tomato{
                    background-color: #ff6347!important;
                    color: white!important;
                }

                .color-Tangerine{
                    background-color: #f28500 !important;
                    color: white!important;
                }
                .color-Banana{
                    background-color: #FFE135!important;
                    color: white!important;
                }

                .color-Basil{
                    background-color: #579229!important;
                    color: white!important;
                }

                .color-Sage{
                    background-color: #9C9F84!important;
                    color: white!important;
                }

                .color-Peacock{
                    background-color: #326872!important;
                    color: white!important;
                }

                .color-Blueberry{
                    background-color: #4f86f7!important;
                    color: white!important;
                }

                .color-Lavender{
                    background-color: #734f96!important;
                    color: white!important;
                }
                .color-Grape{
                    background-color: #421C52!important;
                    color: white!important;
                }

                .color-Flamingo{
                    background-color: #fc8eac!important;
                    color: white!important;
                }

                .color-Graphite{
                    background-color: #474a51!important;
                    color: white!important;
                }

                .cntr{
                    text-align:center;
                    padding: 9px;
                }
            </style>

        </div>
        <div style="width: 10px; height: 10px;">
            <p:dialog widgetVar="userDialog" header="Users" modal="true" showEffect="clip" hideEffect="clip" resizable="false" height="600px">                    
                <h:form id="useradd">
                    <p:commandButton value="Add"  action="#{orgCalendar.onUserAdd()}"  oncomplete="if (args &amp;&amp; !args.validationFailed) PF('userDialog').hide(); PF('userTable').clearFilters() " update=":formCal2:eventtab:invite :formCal2:eventtab:invtable :useradd" styleClass="btn btn-primary btn-xs"/>
                    <p:dataTable class="dtable" rowSelectMode="add" id="userTable" value="#{users.assignedUsers}" var="user" selection="#{orgCalendar.selectedUsers}" rowKey="#{user.recId}"
                                 widgetVar="userTable">
                        <p:column  selectionMode="multiple"  rendered="#{user.userId ne loginBean.userId and orgCalendar.checkUserInvited(user.userId)}" style="width: 30px;" />
                        <p:ajax event="rowSelect"  listener="#{orgCalendar.inviteRendr1}" update=":useradd" /> 
                        <p:ajax event="rowUnselect"   listener="#{orgCalendar.inviteRendr1}" update=":useradd"/> 
                        <p:ajax event="toggleSelect"   listener="#{orgCalendar.inviteRendr1}" update=":useradd"/> 

                        <p:column  filterBy="#{user.userName}" filterMatchMode="contains" sortBy="#{user.userName}" headerText="Name"  rendered="#{user.userId ne loginBean.userId and orgCalendar.checkUserInvited(user.userId)}" >
                            #{user.userName}
                        </p:column>
                    </p:dataTable>
                    <p:outputLabel value="Note: Only uninvited users are displayed"/>
                </h:form>
            </p:dialog>  

        </div>
        <script>
            function send_mail(event_id) {
                var url = '#{loginBean.emailURL}' + "/action.php?action=write&amp;eventid=" + event_id + "&amp;reqid=" + '#{loginBean.randomUid}' + "&amp;write=1&amp;random=" + '#{orgCalendar.genRandomNumber()}' + "&amp;composeemail=1";
                window.open(url, '_blank');
            }


            //used in LayoutHome.xhtml for logo
            function openJrnlTab(url, winName, event) {
                this.target = '_blank';
                var win = window.open(url, winName);
                win.focus();
                var browser = navigator.userAgent.toLowerCase();
                if (browser.indexOf('firefox') > -1) {
                    win.close();
                    var win1 = window.open(url, winName);
                    win1.focus();
                }
                event.preventDefault();
            }

        </script>
    </ui:define> 
</ui:composition>
