<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: Setup.xhtml
// Author: Sarayu
//*********************************************************
/*
*
* Copyright (c) 2015 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>



    <ui:define name="title">

    </ui:define>

<!--<ui:define name="menu">

    </ui:define>-->

    <ui:define name="body">
    </ui:define>  

</ui:composition>
