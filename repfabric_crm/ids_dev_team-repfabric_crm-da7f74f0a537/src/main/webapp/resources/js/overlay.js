//**********************************************************
// Program: Repfabric
// Filename: overlay.js
//
// Author: <PERSON><PERSON><PERSON> K
//
// Creation Date: 17/02/2015
//                   

//                   
//
// Revision: 
// Change Comments:                  
//
// 
//*********************************************************
/*
*
* Copyright (c) 2013 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*
* This software is the confidential and proprietary information of Indea 
* Engineering, A Proprietary Firm ("Confidential Information"). You shall not
* disclose such Confidential Information and shall use it only in
* accordance with the terms of the license agreement you entered into
* with Indea Engineering
*
* INDEA ENGINEERING MAKES NO REPRESENTATIONS OR WARRANTIES, OTHER THEN IN WRITTEN CONTRACT,
* ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED
* TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
* PARTICULAR PURPOSE, OR NON-INFRINGEMENT. INDEA ENGINEERING SHALL NOT BE LIABLE FOR
* ANY DAMAGES SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR
* DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
*/
function ItpOverlay(id) {

    this.id = id;

    /**
     * Show the overlay
     */
    this.show = function(id, msg) {

        if (id) {
            this.id = id;
        }

        // Gets the object of the body tag
        var bgObj = document.getElementById(this.id);

        // Adds a overlay
        var oDiv = document.createElement('div');
        oDiv.setAttribute('id', 'itp_overlay');
        oDiv.setAttribute("class", "black_overlay");
        oDiv.style.display = 'block';
        bgObj.appendChild(oDiv);

        // Adds loading
        var lDiv = document.createElement('div');
        
        /* Modified by Nisha: Adding parameter msg  */
        lDiv.innerHTML ="<h5>"+msg +"</h5>";
        lDiv.style.color = '#00000';
        lDiv.setAttribute('id', 'loading');
        lDiv.setAttribute("class", "loading");
        lDiv.style.display = 'block';
        bgObj.appendChild(lDiv);

    }

    /**
     * Hide the overlay
     */
    this.hide = function(id) {

        if (id) {
            this.id = id;
        }

        var bgObj = document.getElementById(this.id);

        // Removes loading 
        var element = document.getElementById('loading');
        bgObj.removeChild(element);

        // Removes a overlay box
        var element = document.getElementById('itp_overlay');
        bgObj.removeChild(element);
    }

}