/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

function preLoadFunc() {
    $({property: 0}).animate({property: 105}, {
        duration: 1000,
        step: function() {
            var _percent = Math.round(this.property);
            $('#progress').css('width', _percent + "%");
            //  alert(_percent);
            if (_percent == 105) {
                $("#progress").addClass("done");
            }
        },
        complete: function() {
            if ($("#submenu li").length == 0) {
//                $('#ulItem').hide();
                 $('#ulItem').css('display','none');
            } else {
//                 $('#ulItem').css('display','block');
                $('#ulItem').show();
            }
        }
    });
    //  alert("cc");
}



//window.onload = function() { // When the page has finished loading.
//    $(".collect").each(function() { // Check every "nested" class 
//        //  alert($(this).children().length);
//        if ($(this).children().length == 0) { // If this nested class has no children
//            // alert("if");
//            // $(this).hide(); // This will hide it, but not alter the layout
//            //  $(this).css("display", "none"); // This will alter the layout
//        } else {
//            //      alert("else");
//            //  $(this).show();
//            //   $(this).css("display", "none"); // This will alter the layout
//        }
//    });
//
//}




//if(!$('#personaldetails').find('ul:visible').length){
//    $('#personaldetails').hide();
//}
//





window.onpaint = preLoadFunc();

(function($) {
      $('#ulItem').css('display','none');
    $(function() {
        $.ajaxSetup({
            success: function() {
                window.XMLHttpRequest = newXHR;
            }
        });
        var elemWidth, fitCount, varWidth = 0, ctr,$menu = $("ul#menu"), $collectedSet;

        ctr = $menu.children().length;
        $menu.children().each(function() {
            varWidth += $(this).outerWidth();
        });

        collect();
        $(window).resize(collect);

        function collect() {
            elemWidth = $menu.width();
            fitCount = Math.floor((elemWidth / (varWidth)) * ctr) - 1;
            $menu.children().css({"display": "block", "width": "auto"});
            $collectedSet = $menu.children(":gt(" + fitCount + ")");
            $("#submenu").empty().append($collectedSet.clone());
            $collectedSet.css({"display": "none", "width": "0"});
            }
        




    });



})(jQuery);

var oldXHR = window.XMLHttpRequest;

function newXHR() {
    var realXHR = new oldXHR();
    realXHR.addEventListener("readystatechange", function() {
        if (realXHR.readyState == 1) {
            //alert('server connection established');
            return Start();
        }
        if (realXHR.readyState == 2) {
            //alert('request received');
            return set(40);
        }
        if (realXHR.readyState == 3) {
            //  alert('processing request');
            return set(80);
        }
        if (realXHR.readyState == 4) {
            //  alert('request finished and response is ready');
            return complete();
        }
    }, false);
    return realXHR;
}

Progress.configure({"color": ['#f39c12']});
function Start() {
    Progress.start();
}
function set(value) {
    Progress.go(value);
}

function increament() {
    Progress.increament();
}

function complete() {
    Progress.complete();
}
