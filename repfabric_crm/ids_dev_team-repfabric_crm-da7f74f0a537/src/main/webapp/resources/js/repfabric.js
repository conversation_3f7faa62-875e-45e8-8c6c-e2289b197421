/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
//$(document).ready(function() {
//    
//    convertToGoogleMapLink(); 
// 
//}); 

(function(){ convertToGoogleMapLink(); })();

function convertToGoogleMapLink() {
   
    $('.address').each(function() {
        //Feature #1662:CRM-3286: Strange URL Unsecured
        var link = "<a href='https://maps.google.com/maps?q=" + encodeURIComponent($(this).text()) + "'>" + $(this).text() + "</a>";
        $(this).html(link);
    });
}

function conkeyHandler() {
    var searchValue = document.getElementById('contactContentForm:myconFilter_text').value;
    document.getElementById('contactContentForm:dtConList:globalFilter').value = searchValue;
    PF('contactListTable').filter();
}

function setDefaultContactFilter() {
    document.getElementById('contactContentForm:myconFilter_text').value = "";
    contactListTable.clearFilters();
}

function keyHandler() {
    var searchValue = document.getElementById('dtForm:myFilter_text').value;
    document.getElementById('dtForm:dtCompList:globalFilter').value = searchValue;
    PF('companyListTable').filter();
}


function setDefaultFilter() {    
    document.getElementById('dtForm:myFilter_text').value = "";
//    companyListTable.clearFilters();
  PF('companyListTable').clearFilters();
}
function setDefaultFilterForCompList() {    
    document.getElementById('compDetailForm:myFilter_text').value = "";
//    companyListTable.clearFilters();
  PF('dtCompList').clearFilters();
}

//Functions to block the page while data loading and import is in progress
//Updated methods for new overlay - Start
function startLoad() {
    poll.start();
    var overlay = new ItpOverlay();
    overlay.show("body", "Please wait... Data loading is in progress");
}


function stopLoad() {
    poll.stop();
    var overlay = new ItpOverlay();
    overlay.hide("body");
}

function startImport() {
    PF('pbAjax').start();
    PF('startButton2').disable();

    var overlay = new ItpOverlay();
    overlay.show("body", "Please wait... Import is in progress");

}

function stopImport() {
    PF('pbAjax').cancel();
    var overlay = new ItpOverlay();
    overlay.hide("body");
}
//Updated methods for new overlay - End

function startCRMImport() {
    // $.blockUI({message: '<h5> Please wait... Import in progress</h5>'});
    $.blockUI({message: $('#domMessage')});
}

function stopCRMImport() {
    $.unblockUI();
}

/*
 *  Used during Aliasing process
 */
function startAliasing() {
    $.blockUI({message: $('#domMessage3')});
}

function stopAliasing() {
    $.unblockUI();
}

/*
 * Function to convert to title case as the user types the company name in the company view page
 * Example: Test dba company
 */
function convertTitleCase() {
    var comp_name = document.getElementById('compDetailsButtonForm:tvComp:itCompName');
    var currVal = $(comp_name).val();
    //#352062 - Comp name java script issue with renaming: Saving the cursor position
    var curPos = comp_name.selectionStart;
    $(comp_name).val(currVal.charAt(0).toUpperCase() + currVal.slice(1));
    comp_name.selectionStart = comp_name.selectionEnd = curPos;

}

//function convertTitleFNameCase() {
//    var comp_name = document.getElementById('contDetailsButtonForm:tvCont:itFName');
//    var currVal = $(comp_name).val();
//    //#352062 - Comp name java script issue with renaming: Saving the cursor position
//    var curPos = comp_name.selectionStart;
//    $(comp_name).val(currVal.charAt(0).toUpperCase() + currVal.slice(1));
//    comp_name.selectionStart = comp_name.selectionEnd = curPos;
//
//}
