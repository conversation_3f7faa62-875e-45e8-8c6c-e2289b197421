/*	jQuery.flexMenu 1.5
	https://github.com/352Media/flexMenu
	Description: If a list is too long for all items to fit on one line, display a popup menu instead.
	Dependencies: jQuery, Modernizr (optional). Without Modernizr, the menu can only be shown on click (not hover). */

!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):e(jQuery)}(function(e){function i(){e(window).width()===o&&e(window).height()===t||(e(f).each(function(){e(this).flexMenu({undo:!0}).flexMenu(this.options)}),o=e(window).width(),t=e(window).height())}function n(i){var n,l;n=e("li.flexMenu-viewMore.active"),l=n.not(i),l.removeClass("active").find("> ul").hide()}var l,o=e(window).width(),t=e(window).height(),f=[];e(window).resize(function(){clearTimeout(l),l=setTimeout(function(){i()},200)}),e.fn.flexMenu=function(i){var l,o=e.extend({threshold:2,cutoff:2,linkText:"More",linkTitle:"View More",linkTextAll:"Menu",linkTitleAll:"Open/Close Menu",shouldApply:function(){return!0},showOnHover:!0,popupAbsolute:!0,popupClass:"",undo:!1},i);return this.options=o,l=e.inArray(this,f),l>=0?f.splice(l,1):f.push(this),this.each(function(){function i(e){return Math.ceil(e.offset().top)>=w+v}var l,t,f,u,s,d,p,r=e(this),a=r.find("> li"),h=a.first(),c=a.last(),M=r.find("li").length,w=Math.floor(h.offset().top),v=Math.floor(h.outerHeight(!0)),x=!1;if(i(c)&&M>o.threshold&&!o.undo&&r.is(":visible")&&o.shouldApply()){var T=e('<ul class="flexMenu-popup" style="display:none;'+(o.popupAbsolute?" position: absolute;":"")+'"></ul>');for(T.addClass(o.popupClass),p=M;p>1;p--){if(l=r.find("> li:last-child"),t=i(l),p-1<=o.cutoff){e(r.children().get().reverse()).appendTo(T),x=!0;break}if(!t)break;l.appendTo(T)}x?r.append('<li class="flexMenu-viewMore flexMenu-allInPopup"><a href="#" title="'+o.linkTitleAll+'">'+o.linkTextAll+"</a></li>"):r.append('<li class="flexMenu-viewMore"><a href="#" title="'+o.linkTitle+'">'+o.linkText+"</a></li>"),f=r.find("> li.flexMenu-viewMore"),i(f)&&r.find("> li:nth-last-child(2)").appendTo(T),T.children().each(function(e,i){T.prepend(i)}),f.append(T),u=r.find("> li.flexMenu-viewMore > a"),u.click(function(e){n(f),T.toggle(),f.toggleClass("active"),e.preventDefault()}),o.showOnHover&&"undefined"!=typeof Modernizr&&!Modernizr.touch&&f.hover(function(){T.show(),e(this).addClass("active")},function(){T.hide(),e(this).removeClass("active")})}else if(o.undo&&r.find("ul.flexMenu-popup")){for(d=r.find("ul.flexMenu-popup"),s=d.find("li").length,p=1;p<=s;p++)d.find("> li:first-child").appendTo(r);d.remove(),r.find("> li.flexMenu-viewMore").remove()}})}});
