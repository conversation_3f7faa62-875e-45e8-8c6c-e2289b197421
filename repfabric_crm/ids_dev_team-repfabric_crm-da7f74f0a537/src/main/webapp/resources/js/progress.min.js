!function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.Progress=t()}(this,function(){function e(e,t,r){return t>e?t:e>r?r:e}function t(e,t){var r={},n=document.body.style,o="WebkitTransform"in n?"Webkit":"MozTransform"in n?"Moz":"msTransform"in n?"ms":"OTransform"in n?"O":"",i=o+"Transition";return r[i]="width "+t+"s linear",r.width=e+"%",r}function r(e,t){var r=n(e),i=r+t;o(r,t)||(e.className=i.substring(1))}function n(e){return(" "+(e&&e.className||"")+" ").replace(/\s+/gi," ")}function o(e,t){var r="string"==typeof e?e:n(e);return r.indexOf(" "+t+" ")>=0}function i(e){e.style.opacity-=.1,e.style.opacity<0?e.style.opacity=0:setTimeout(i(e),100)}var a={};a.status=null,a.configure=function(e){var t,r;for(t in e)r=e[t],void 0!==r&&e.hasOwnProperty(t)&&(s[t]=r);return this};var s=a.settings={minimum:20,maximum:95,speed:.5,increaseSpeed:50,height:3,defaultColor:"#9400D3",color:["#9400D3"],barSelector:'[role="bar"]',parent:"body",template:'<div id="bar" role="bar"></div>'};a.start=function(e){a.isStarted()||null==a.status&&("undefined"==typeof e?a.go(s.minimum):a.go(e))},a.go=function(e){a.status=1===e?null:e,a.isRendered()||a.render();var r=t(e,s.speed),n=document.getElementById("bar-progress"),o=n.querySelector(s.barSelector);document.getElementById("bar-progress").offsetWidth,u(o,r),setTimeout(function(){if(100===e)i(o),a.remove(),a.status=null;else{var r=t(s.maximum,s.increaseSpeed);u(o,r)}},1e3*parseFloat(s.speed))},a.complete=function(){return a.go(100)},a.increament=function(t){var r=a.status;return r?r>100?void 0:("number"!=typeof t&&(t=r>=0&&20>r?10:r>=20&&50>r?4:r>=50&&80>r?2:r>=80&&99>r?1:0),r=e(r+t,0,99),a.go(r)):a.start(0)},a.render=function(){var e=a.createElement();e.innerHTML=s.template;var t=e.querySelector(s.barSelector),r={};if(r.height=s.height+"px",r.width="0%",r.position="fixed",r.zIndex="1031",r.top="0",r.left="0",r.boxShadow="0 0 2px #29d, 0 0 1px #29d",s.color.length>1){for(var n=["-o-","-ms-","-moz-","-webkit-"],o="",i=0;i<s.color.length;i++)o=o+", "+s.color[i];o=o.substring(1);for(var d=0;d<n.length;d++)r.background=n[d]+"linear-gradient(right,"+o+")"}else 1==s.color.length?r.background=s.color[0]:r.background=s.defaultColor;u(t,r)},a.createElement=function(){var e=null,t=document.querySelector(s.parent);return a.isRendered()||(e=document.createElement("div"),e.id="bar-progress",e.style.width="100%",e.style.zIndex="1031",e.style.position="fixed",e.style.top="0",e.style.background="transparent",t!=document.body&&r(t,"bar-progress-custom-parent"),t.appendChild(e)),document.getElementById("bar-progress")},a.remove=function(){function e(e){e&&e.parentNode&&e.parentNode.removeChild(e)}var t=document.getElementById("bar-progress");e(t)},a.isStarted=function(){return"number"==typeof a.status},a.isRendered=function(){return!!document.getElementById("bar-progress")};var u=function(){function e(e){return e.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(e,t){return t.toUpperCase()})}function t(e){var t=document.body.style;if(e in t)return e;for(var r,n=o.length,i=e.charAt(0).toUpperCase()+e.slice(1);n--;)if(r=o[n]+i,r in t)return r;return e}function r(r){return r=e(r),i[r]||(i[r]=t(r))}function n(e,t,n){var o=r(t);e.style[o]=n}var o=["Webkit","O","Moz","ms"],i={};return function(e,t){var r,o,i=arguments;if(2==i.length)for(r in t)o=t[r],void 0!==o&&t.hasOwnProperty(r)&&n(e,r,o);else n(e,i[1],i[2])}}();return a});