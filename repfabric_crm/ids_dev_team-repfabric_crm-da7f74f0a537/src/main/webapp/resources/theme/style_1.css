

/* ========================== Frame work starts =========================== */
.ui-widget .ui-widget {
    font-size: 14px;
}

body{
    /*background:url("images/linen.jpg");*/
    font: 100% "Trebuchet MS", sans-serif;
    margin: 0px;
}
 
.top_menu{
    width:100%;
    height:30px;
    background: #353744; /* Old browsers */
    background: -moz-linear-gradient(top,  #353744 0%, #535a6b 50%, #535a6b 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#353744), color-stop(50%,#535a6b), color-stop(100%,#535a6b)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #353744 0%,#535a6b 50%,#535a6b 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #353744 0%,#535a6b 50%,#535a6b 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #353744 0%,#535a6b 50%,#535a6b 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #353744 0%,#535a6b 50%,#535a6b 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#353744', endColorstr='#535a6b',GradientType=0 ); /* IE6-9 */
}

.top_menu ul{
    font-size: 14px;
    line-height: 28px;
    margin: 0;
    padding: 0;
}

.top_menu ul li{
    float:left;
    list-style:none;
}

.top_menu ul li a{
    color:#fff;
    margin-left: 4px;
    text-decoration: none;
    padding:6px;
    font-weight: bold;
}

.active{
    background:#2f6295;
}

.top_menu ul li a:hover{
    background:#2f6295;
    padding:6px;
}

.header{
    height:65px;
    border-radius: 5px;
    height: 65px;
    margin: 10px;
    width: 98.5%;
    border:1px solid #7f89a3;

    background: #ffffff; /* Old browsers */
    background: -moz-linear-gradient(top,  #ffffff 43%, #d9e2f7 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(43%,#ffffff), color-stop(100%,#d9e2f7)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #ffffff 43%,#d9e2f7 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #ffffff 43%,#d9e2f7 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #ffffff 43%,#d9e2f7 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #ffffff 43%,#d9e2f7 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#d9e2f7',GradientType=0 ); /* IE6-9 */
}

.header h2{
    float:left;
    color:#112a67;
    padding-left:20px;
    font-size: 19px;
    margin-top:5px;
    line-height: 55px;
}

.logo{
    background: url(images/repfabric_logo.png) no-repeat;
    height:41px;
    width:245px;
    margin-left: 20px;
    margin-top: 12px;
    float:left;
    border-right: 1px solid #9cadd7;
}

.leftpanel{
    /*background: url("images/bg_dot.png");
    border: 1px solid #7f89a3;
    margin-left: 10px;
    width: 19%;
    position:absolute;
    left:0;
    height: 74.3%;*/
    border-bottom-left-radius:8px;
    border-bottom-right-radius:8px;
    width: 250px;
    height: 100%;
}

.title_bar{
    background:#9cadd7;
    height:30px;
    width:100%;
}

.title_bar h3{
    margin:0 auto;
    padding:0;
    text-align:center;
    color:#0d2b75;
    line-height: 30px;
    font-size: 14px;
}

.content{
    /*border: 1px solid #7f89a3;*/
    border-radius: 5px;
    /*height: 74.3%;*/
    /*margin: 0 10px 30px;*/
    padding: 0;
    /*width: 78.3%;*/	
    /*background: url("images/bg_dot.png");*/
    /*position:absolute;*/
    /*right:0;*/
    overflow-x: hidden;
    padding-bottom:20px;
}

.footer{
    width:100%;
    height:30px;
    background:#535a6b;
    font-size: 14px;
    line-height: 27px;
    text-align: center;
    color:#fff;
}

.tds{
    height:20px;
    /*    font-size: 16px;*/
    height: 25px;
    padding: 5px 5px 5px 20px;
    width: 100px;
}

.tds1{
    width:270px;
    /*    font-size: 15px;*/
}

.m{
    height:20px;
    font-size: 16px;
    padding: 5px;
    width: 35px;
}

/*table
{
    border-spacing: 0px;
}*/

input.star{
    width:32px;
    height:32px;
}

label.star{
    width:32px;
    height:32px;
    background-image: url('images/star-grey.png');
}

/*
  Hide radio button (the round disc)
  we will use just the label to create pushbutton effect
*/
input[type=radio] {
    display:none;
    margin:10px;
}

/*
  Change the look'n'feel of labels (which are adjacent to radiobuttons).
  Add some margin, padding to label
*/
input[type=radio] + label {
    background: url('images/star-grey-off.png') no-repeat;
    border-color: #ddd;
    display: inline-block;
    line-height: 13px;
    margin: 4px;
    padding: 4px 0px 4px 30px;
}
/*
 Change background color for label next to checked radio button
 to make it look like highlighted button
*/
input[type=radio]:checked + label {
    background:url(images/star-grey.png) no-repeat;
    line-height: 13px;
    padding-left: 30px;
}

.button {
    background: #ffffff;
    background-image: -webkit-linear-gradient(top, #ffffff, #a6b7e0);
    background-image: -moz-linear-gradient(top, #ffffff, #a6b7e0);
    background-image: -ms-linear-gradient(top, #ffffff, #a6b7e0);
    background-image: -o-linear-gradient(top, #ffffff, #a6b7e0);
    background-image: linear-gradient(to bottom, #ffffff, #a6b7e0);
    -webkit-border-radius: 4;
    -moz-border-radius: 4;
    border-radius: 4px;
    text-shadow: 0px 1px 0px #ffffff;
    font-family: Arial;
    color: #043b87;
    font-size: 13px;
    padding: 2px 10px 2px 10px;
    border: solid #a6b7e0 1px;
    text-decoration: none;
}

.button:hover {
    background: #ffffff;
    background-image: -webkit-linear-gradient(top, #ffffff, #73b2d9);
    background-image: -moz-linear-gradient(top, #ffffff, #73b2d9);
    background-image: -ms-linear-gradient(top, #ffffff, #73b2d9);
    background-image: -o-linear-gradient(top, #ffffff, #73b2d9);
    background-image: linear-gradient(to bottom, #ffffff, #73b2d9);
    text-decoration: none;
}

.rightbutton{
    float:right;
    margin-right: 20px;
    margin-top: 20px;
}

/* ========================== Frame work end =========================== */
.image {
    background: url("images/hedder_bg.png")no-repeat right center #fafcfe;
    /*margin-top: 20px;*/
    height:50px;
    width: 100%;
    padding: 0;
    overflow: hidden;
}

.image h2 {
    font-size: 27px;
    color: #112a67;
    padding-left: 30px;
    padding-top: 5px;
}

.image h3 {
    color: #fff;
    font-size: 18px;
    padding-left: 30px;
    text-shadow: 0px 0px 7px rgba(0, 0, 0, 0.75);
    margin-top: -15px;
}

/*subscriber name*/
.image h4 {
    color:#a9d0f5;
    font-size: 18px;
    padding-left: 30px;
    padding-top: 0px;
    margin-top: 13px
}

.content_home{
    height: auto;
    padding: 0px;
}

.content_home table{
    width:100%;
}

.content_home h4{
    font-size:18px;
    color:#384654;
    padding-left: 8px;
}

.content_home p{
    font-size:14px;
    color:#7b7b7b;
    /*  line-height: 10px; */
    margin-left: 22px;
}

.content_home p a {
    color:#1891c1;
    text-decoration:none;	
}

.content_home p a:hover{
    color:#1891c1;
    text-decoration:underline;	
}

table{
    /*    width:100%;*/
    padding:5px;
    border-spacing: 0px;
    font-size: 14px;
}

.left{
    width:250px;
    height: 588px;
    padding:0;
    margin:0;
    border: 1px solid #5d78b4;
    border-radius:8px;
    background:#fafafa;
    vertical-align: top;
    padding-bottom: 20px;
}

.left select{
    width:100%;
    overflow: hidden;
    border: 1px solid #b7bed3;
    border-radius: 5px;height: 29px;margin-top: 3px; margin-right:4px;
    margin-left: 0;
    background: #ffffff;
}

.left ui-column-filter{
    width:100%;
    margin-left: 0px;
}

.left table tr{
    background: #EEE;
}

.left table{
    font-size:14px;
    width:100%;
}

.left table tr{
    height: 2s0px;
    background: #fff;
}

.hedding{
    background:#c8d6f9;
    text-align:center;
    font-size:18px;
    padding:0;
    color:#384654;
    height: 40px;
    line-height: 40px;
    margin: 0 0 0px;
}

.left .menu{
    vertical-align: top;
    list-style: none outside none;
    margin: 0;
    padding: 10px;
    vertical-align: top;
}

.left .menu a{
    float: left;
    font-size: 18px;
    width:230px;
    background:url("images/menu.png")no-repeat;
    padding: 15px 20px 24px;
    text-decoration: none;
    width: 190px;
    color:#0c617d;
}

.left .menu a:hover{
    background:url("images/menu_hover.png")no-repeat;
}

.left .menu .active{
    background:url("images/menu_hover.png")no-repeat;
}

.left .menu ul li{
    padding:20px;
}

.left .menu ul li a{
    background:#CCC;
}

.left .menu #email_button{
    float: left;
    font-size: 18px;
    width:230px;
    background:url("images/menu.png")no-repeat;
    padding: 15px 20px 24px;
    text-decoration: none;
    color:#0c617d;
    border: 0;
    text-align: left;
}

.left .menu #email_button:hover{
    float: left;
    font-size: 18px;
    width:230px;
    background:url("images/menu_hover.png")no-repeat;
    padding: 15px 20px 24px;
    text-decoration: none;
    color:#0c617d;
    border: 0;
    text-align: left;
}

.right{
    background-color:#efeeea;
    height:auto;
    min-width: 950px;
    padding:0;
    margin:0;
    vertical-align:top;
    margin:5px;
    border:1px solid #5d78b4;
    border-radius:8px;
    background-image:url("images/gear.png");
    background-position: right bottom;
    background-repeat: no-repeat;
}

.right_inside{
    background-color:#fafafa;
    height:auto;
    min-width: 950px;
    padding:0;
    margin:0;
    vertical-align:top;
    margin:5px;
    border:1px solid #5d78b4;
    border-radius:8px;
    background-repeat: no-repeat;
}

.w{
    width:3px;
}

.middle_hedder{
    height:50px;
    width:100%;
    border-bottom:1px solid #fafafa;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;

    -moz-border-top-left-radius: 8px;
    -moz-border-top-right-radius: 8px;

    -webkit-border-top-left-radius: 8px;
    -webkit-border-top-right-radius: 8px;

    -khtml-border-top-left-radius: 8px;
    -khtml-border-top-right-radius: 8px;


    background: #f7f7f7; /* Old browsers */
    background: -moz-linear-gradient(top,  #f7f7f7 0%, #e8ebf4 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f7f7f7), color-stop(100%,#e8ebf4)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #f7f7f7 0%,#e8ebf4 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #f7f7f7 0%,#e8ebf4 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #f7f7f7 0%,#e8ebf4 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #f7f7f7 0%,#e8ebf4 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f7f7f7', endColorstr='#e8ebf4',GradientType=0 ); /* IE6-9 */
}

.middle_hedder h1{
    color:#112a67;
    padding: 8px 1px 1px 10px;
    float: left;
    line-height: 11px;
    font-size: 18px;
 /*Sarayu- width for header*/
   /* overflow: hidden;*/
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 85%;
}

.middle_hedder h2{
    color:#000;
    padding: 0px 10px 5px 18px;
    float: left;
    width: 450px;
    position: absolute;
    font-size:16px;
}

.middle_hedder h3{
    color:#112a67;
    padding: 0px 10px 5px 18px;
    float: left;
    width: 450px;
    position: absolute;
    top: 65px;
}

.middle_hedder h3 .btn {
    background: #ebf7ff;
    background-image: -webkit-linear-gradient(top, #ebf7ff, #93a7c9);
    background-image: -moz-linear-gradient(top, #ebf7ff, #93a7c9);
    background-image: -ms-linear-gradient(top, #ebf7ff, #93a7c9);
    background-image: -o-linear-gradient(top, #ebf7ff, #93a7c9);
    background-image: linear-gradient(to bottom, #ebf7ff, #93a7c9);
    -webkit-border-radius: 5;
    -moz-border-radius: 5;
    border-radius: 5px;
    font-family: Arial;
    color: #000000;
    font-size: 10px;
    padding: 3px 10px 3px 10px;
    border: solid #bbddf0 1px;
    text-decoration: none;
    cursor:pointer;
}

.middle_hedder h3 .btn:hover {
    background: #7aa5bf;
    text-decoration: none;
    color:#fff;
}

.login_icon{
    background:url("images/login_icon.png");
    height: 26px;
    margin-top: 3px;
    width:25px;
    margin-right:10px;
    cursor:pointer;
}

.login_out{
    background:url("images/logout_icon.png");
    height: 26px;
    margin-top: 3px;
    width:25px;
    margin-right:10px;
    cursor:pointer;
}

.message_icon{
    background:url("images/message.png");
    height: 26px;
    margin-top: 3px;
    width:25px;
    margin-right:10px;
    cursor:pointer;
}

.user_icons_top{
    float: right;
}

.user_name{
    color:#fff;
    margin-left:0px;
    margin-right:5px;
    float:left;
    line-height: 30px;
    font-weight: bold;
}

.logout_icon{
    background:url("images/logoout.png");
    height: 26px;
    margin-top: 3px;
    width:75px;
    margin-right:10px;
    cursor:pointer;
    float:left;
}

.user_icon{
    background:url("images/photo.png");
    height: 22px;
    margin-top: 3px;
    width:20px;
    margin-right:10px;
    float:left;
}
 /*//Change margin-right: 5px on Adding 'Tutorials' button*/
.select_icons{
    float:right;margin-right:5px;
    margin-top: 8px;
    height: 36px;
 /*Sarayu- fix width for page header*/
    /*width:540px;*/
    width: 10%;
    font-size: 14px;line-height: 33px;}

.select_icons p{
    float:right;
    margin: 0;
    padding: 0;
}

.select_icons input{
    -webkit-border-radius: 5px 0 0 5px;
    border-radius: 5px 0 0 5px;
    border:1px solid #b8bed4;
    margin: 0 -4px 0 0;
    padding: 4px;
    background:#fafafa;
}

.select_icons .search_btn{
    background:url("images/serch.png");
    border: 0 none;
    border-radius: 0;
    height: 26px;
    margin: 0;
    cursor:pointer;
    padding: 0;
    width: 28px;
}

.select_icons select{
    width:172px;
    overflow: hidden;
    border: 1px solid #b7bed3;
    border-radius: 5px;height: 29px;margin-top: 3px; margin-right:4px;
    margin-left: 0;
    background: #ffffff;
}

.sub_title{
    background: #EFF4FD;
    border-radius: 10px;
    padding: 10px;
    margin : 0 0 5px 0;
}
.sub_title_Dashboard_Import{
    background:#DDD;
    border-radius: 1px;
    padding: 1px;
    margin : 0 0 5px 0;
}

.select_icons button{
    float:right;
}

.select_icons .btn1 {
    background: #ebf7ff;
    background-image: -webkit-linear-gradient(top, #ebf7ff, #93a7c9);
    background-image: -moz-linear-gradient(top, #ebf7ff, #93a7c9);
    background-image: -ms-linear-gradient(top, #ebf7ff, #93a7c9);
    background-image: -o-linear-gradient(top, #ebf7ff, #93a7c9);
    background-image: linear-gradient(to bottom, #ebf7ff, #93a7c9);
    -webkit-border-radius: 5;
    -moz-border-radius: 5;
    border-radius: 5px;
    font-family: Arial;
    color: #000000;
    font-size: 10px;
    padding: 3px 10px 3px 10px;
    border: solid #bbddf0 1px;
    text-decoration: none;
    cursor:pointer;
    font-size: 13px;
}

.select_icons .btn1:hover {
    background: #7aa5bf;
    text-decoration: none;
    color:#fff;
}

.search-form {
    font-size: 14px;
    width: 250px;
    height: 16px;
    float: right;
    margin: 4px !important;
}

#search-form {
    font-size: 14px;
    width: 325px;
    height:30px;
    float:right;
}

.form_class{
    width:700px;
    display: inline-block;
    padding-top: 6px;
}

/*** TEXT BOX ***/
#search-form input[type="text"]{
    background: #fafafa; /* Fallback color for non-css3 browsers */
    border:1px solid #ccc;
    font-size: 10px;
    margin: 4px 4px 4px;
    padding: 5px;
    width: 205px;
    /* Rounded Corners */
}

/*** USER IS FOCUSED ON TEXT BOX ***/
#search-form input[type="text"]:focus{
    outline: none;
    background: #fff; /* Fallback color for non-css3 browsers */
    /* Gradients */
    background: -webkit-gradient( linear, left bottom, left top, color-stop(0, rgb(255,255,255)), color-stop(1, rgb(235,235,235)));
    background: -moz-linear-gradient( center top, rgb(255,255,255) 0%, rgb(235,235,235) 100%);
}

#search-form input[type="submit"]{
    padding:3px;
}

.ui-datatable thead th, .ui-datatable tfoot td{
    font-size: 14px;
}

.button_bar{
    height:32px;
    padding-left: 10px;
    background: #ffffff; /* Old browsers */
    background: -moz-linear-gradient(top,  #ffffff 0%, #e5e5e5 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(100%,#e5e5e5)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #ffffff 0%,#e5e5e5 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #ffffff 0%,#e5e5e5 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #ffffff 0%,#e5e5e5 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #ffffff 0%,#e5e5e5 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#e5e5e5',GradientType=0 ); /* IE6-9 */
}

.button_top{
    margin-top: 2px;
    padding: 4px 14px;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border-radius: 4px;
    border:1px solid #888;
    /*     margin-top: 2px !important;
        padding: 4px 14px!important;
        text-decoration: none!important;
        color:#000 !important;
        font-weight: normal !important;        
        font-size: 14px !important;
        border-radius: 4px;
        border:1px solid #888;*/
    background: #ffffff; /* Old browsers */
    background: -moz-linear-gradient(top,  #ffffff 0%, #f1f1f1 50%, #e1e1e1 51%, #f6f6f6 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(50%,#f1f1f1), color-stop(51%,#e1e1e1), color-stop(100%,#f6f6f6)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #ffffff 0%,#f1f1f1 50%,#e1e1e1 51%,#f6f6f6 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #ffffff 0%,#f1f1f1 50%,#e1e1e1 51%,#f6f6f6 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #ffffff 0%,#f1f1f1 50%,#e1e1e1 51%,#f6f6f6 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #ffffff 0%,#f1f1f1 50%,#e1e1e1 51%,#f6f6f6 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#f6f6f6',GradientType=0 ); /* IE6-9 */
}

.button_top:hover{
    background: #ffffff; /* Old browsers */
    background: -moz-linear-gradient(top,  #ffffff 0%, #f7f7f7 50%, #efefef 51%, #fafafa 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(50%,#f7f7f7), color-stop(51%,#efefef), color-stop(100%,#fafafa)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #ffffff 0%,#f7f7f7 50%,#efefef 51%,#fafafa 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #ffffff 0%,#f7f7f7 50%,#efefef 51%,#fafafa 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #ffffff 0%,#f7f7f7 50%,#efefef 51%,#fafafa 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #ffffff 0%,#f7f7f7 50%,#efefef 51%,#fafafa 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#fafafa',GradientType=0 ); /* IE6-9 */

}

/*changed by rashid*/

.ui-selectonemenu-label{
    width: 100% !important;
}

.table_general{
    width: 100%;
}

.table_general tr{
    height: 40px;
}

.table_general .td_sep{
    width:20px;   
}

.table_general .td_label{
    width:100px;  
}


.table_general .td_input{
    /*    width:150px;   */
}

.table_general .td_sep2{
    width:50px;   
}

.clos_btn{   
    font-size: 12px !important;
    background: #FDFDFE;
    width: 100%;
}

.rpt_btn0{
    background:#d2ebd3;
    width: 98%;
    font-size: 12px !important;
}

.rpt_btn1{
    background: lightgrey;
    width: 98%;
    font-size: 12px !important;
}

.comment_cmnt{
    color:#384654;
    font-size: 14px;
    padding: 0px 22px 0px;
}

.comment_div {
    background: #FCFDEF;
    padding: 6px 0px 20px 4px;  
}

.dtcomnt{
    float: right;
    padding: 0px 5px;
    font: initial;
    font-size: small;
}

.dtnoborder .ui-datatable table {
    border: hidden;
    width: 100% !important;
}

.btnlukup{
    width: 22px !important;
    height: 27px !important;
}

.pnls{
    margin-left: -16px;
    margin-top: -15px;
    border: none;
}
.pnlsmap{
    margin-left: -16px;
    margin-top: -24px;
    border: none;
}

.pnldlgs{
    width: 100%;
    margin: -5px 0px 0px -12px;
    border: none;
}

.btn_tabs{
    font-weight: normal !important;
    font-size: 12px !important;
    color: black !important;
    background: linear-gradient(to bottom, #ffffff 0%,#f1f1f1 50%,#e1e1e1 51%,#f6f6f6 100%) !important;
}


.ui-menu, .ui-menu .ui-menu-child, .ui-menubutton .ui-button{
    background: whitesmoke;
}

.ui-menubutton .ui-button{
    height: 28px !important;
    font-size: 12px;
    border-radius: 10px;
}

.sal_rpt_btns{
    width: 100%;
    height: 35px;
    border-radius: 15px;
}

.sal_rpt_db{
    height: 55px;
    border-radius: 5px;
    font-size: 16px;
    color: gray;
    width: 100%;
}

.rep_icon1{
    background: url('images/dashboard/dash.png') no-repeat left !important;
}

.rep_icon2{
    background: url('images/dashboard/customers-icon.png') no-repeat left !important;
}

.rep_icon3{
    background: url('images/dashboard/comer-icon.png') no-repeat left !important;
}

.rep_icon4{
    background: url('images/dashboard/product-icon.png') no-repeat left !important;
}

.rep_icon5{
    background: url('images/dashboard/plan.png') no-repeat left !important;
}

.rep_icon6{
    background: url('images/dashboard/princ1.png') no-repeat left !important;
}

.rep_icon11{
    background: url('images/dashboard/money4.png') no-repeat left !important;
}
.rep_icon12{
    background: url('images/dashboard/money.png') no-repeat left !important;
}
.rep_icon13{
    background: url('images/dashboard/rep1.png') no-repeat left !important;
}
.rep_icon14{
    background: url('images/dashboard/rep2.png') no-repeat left !important;
}

/*01/02/2018 - For Sales Expanded View*/
.rep_icon15{
    background: url('images/dashboard/calendar.png') no-repeat left !important;
}

/*Sarayu-icons for dashboard, related and products buttons*/
.reltd_comp{
    background: url('images/companies.png') no-repeat left !important;
}

.merge_comp{
    background: url('images/merge.png') no-repeat left !important;
}

.comp_products{
    background: url('images/products.png') no-repeat left !important;
}
.comp_dash{
    background: url('images/dashboard.png') no-repeat left !important;
}


/* remove panel grid border from report leftpanel*/
.sal .ui-panelgrid td, .sal .ui-widget-content{
    border-width:  0px !important;
    border: 0px !important;
}

/* email*/
.emlhw{
  height: 22px;
  width: 27px;
  margin: 1px !important;
}

.eml-rply{
  background: url('images/eml/reply.png') no-repeat center  !important;
}
.eml-rplyall{
  background: url('images/eml/reply_all.png') no-repeat center  !important;
}
.eml-frwad{
  background: url('images/eml/forward.png') no-repeat center !important;
}
.eml-preview{
  background: url('images/eml/eye.png') no-repeat center !important;
}

/*End of rashid changes    */

.table_general .sep_w{
    width:40px;   
}
/*.table_general .sep_1
{
    width:170px;   
}
.table_general .sep_2
{
    width:300px;   
}*/
.table_general input select{
    height: 20px;
    width: 100%;
    background:#fff;
}

.table_general .button_serch{
    height: 30px;
    width: 40px;
    alignment-baseline: baseline;
    vertical-align: bottom;
    background: #ffffff; /* Old browsers */
    background: -moz-linear-gradient(top,  #ffffff 0%, #e5e5e5 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(100%,#e5e5e5)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #ffffff 0%,#e5e5e5 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #ffffff 0%,#e5e5e5 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #ffffff 0%,#e5e5e5 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #ffffff 0%,#e5e5e5 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#e5e5e5',GradientType=0 ); /* IE6-9 */ 
}

/* added by Rashid */

.tblmain{
    /*    white-space: nowrap;*/
    font-size: 14px;
    display:inline-block;
    width: 100%;
}

.tblmain tr{
    height: 26px;

}

.tblmain a {  
    text-decoration: none;  

}

.table_general tr{
    height: 40px;
}

.enquirybutton{
    width: 100% !important;
    background: none !important;
    border: none !important;
    text-align: left !important;
    font-size: 14px !important;
    font-style: inherit !important;
}

.ui-datatable .ui-datatable-header {
    border-bottom: 0px none;
    display: none;
}

.lbl_cnt{    
    color:#044238;
    font-size: 12px ;
}

.lbl_val{    
    font-size: 13px ;
}
/*End-Added by Rashid*/


/*Start - Added by Nisha - style for non-editable mode*/
.ui-inputfield[readonly], .ui-inputfield[readonly].ui-state-focus{
    border:0 !important;
    box-shadow:none !important;
    outline:0 !important;
    color:#4f4f4f;
    background-color: transparent;
}  

.imageUpload .ui-fileupload-buttonbar{
    background:transparent none;
    border: none;
}

.imageUpload .ui-fileupload-content{
    display:none;
}

.imageUpload .ui-fileupload-buttonbar .ui-fileupload-choose{
    margin-top: 2px;
    text-decoration: none;
    width:65px;
    border-radius: 4px;
    border:1px solid #888;
}

.imageUpload .ui-fileupload-buttonbar .ui-fileupload-choose .ui-icon{
    visibility: hidden !important;
}

.imageUpload .ui-fileupload-buttonbar .ui-fileupload-choose .ui-button-text{
    padding-left:1em !important;
    font-weight: bold;
    font-size:12px;
}

.image_box{
    width: 150px;
    border: none;
    background: #f0f0f0;
    /*    padding: 22px 14px;*/
    padding: 11px 11px;
    margin-bottom: 9px;
    float:left;
    border:1px solid #f0f0f0;
}

.clearboth{
    clear:both;
}

.img_p{
    width:120px;
    height:120px;
    border:1px solid #ccc;
    color: grey;
}

.logoUpload .ui-fileupload-buttonbar{
    background:transparent none;
    border: none;
}

.logoUpload .ui-fileupload-content{
    display:none;
}

.button_bar .ui-button {
    margin-top: 2px;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border-radius: 4px;
    border:1px solid #888;
    font-weight: normal;
}

.ui-overlay-visible .ui-editor iframe{
    width:100% !important;
    height:200px!important;
}

.tbs{
    width: 100px;
}

.headerString{
    background-color:#EFF4FD;
    width:100%;
    height: 30px;
}

.tb1{
    width:50px;
}

.tbPlan{
    display:block;
    width:95px;
}

.olPeriod{
    display:block;
    width:20px;
}

.entryTbl thead{
    background-color: #f0f0f0;
}

.red-box {
    width:50px;
    height: 40px;
    display: inline-block;
    background-color: red;
    position: relative;
    top:15px;
}

.green-box {
    width:50px;
    height: 40px;
    display: inline-block;
    background-color: #00cc00;
    position: relative;
    top:15px;
}

.yellow-box {
    width:50px;
    height: 40px;
    display: inline-block;
    background-color: yellow;
    position: relative;
    top:15px;
}

.grpBox .ui-widget-content {
    border:none !important;
}

/****************** styling for overlay - start ***************************/

.black_overlay{
    display: none;
    position: fixed;
    top: 0%;
    left: 0%;
    width: 100%;
    height:100%;
    background-color: black;
    z-index:1001;
    -moz-opacity: 0.2;
    opacity:.20;
    filter: alpha(opacity=20);
}

#loading {
    display: none;
    position: absolute;
    background-color: #ffffff;
    top: 40%;
    left: 40%;
    text-align: center;
    padding: 8px;
    width: 30%;
    z-index:1002;
    overflow: auto;
    background-image: url(images/ajax-loader.gif);
    background-position: center left 10%;
    background-repeat: no-repeat;
    border:3px solid #aaa;
}
/****************** styling for overlay - end ***************************/


/*End - Added by Nisha*/

/*sarayu*/

.access_denied_page{ 
    background-color:#efeeea;
    height:auto;
    min-width: 950px;
    padding:0;
    margin:0;
    margin:5px;
    border-radius:8px;
    background-image:url("images/gear.png");
    background-position: right bottom;
    background-repeat: no-repeat;
}


.left_button_disabled{
    float: left;
    font-size: 18px;
    background: url("images/menu.png") no-repeat scroll 0% 0% transparent;
    background-color: transparent;
    background-image: url("images/menu.png");
    background-repeat: no-repeat;
    background-attachment: scroll;
    background-position: 0% 0%;
    background-clip: border-box;
    background-origin: padding-box;
    background-size: auto auto;
    padding: 15px 20px 24px;
    padding-top: 15px;
    padding-right-value: 20px;
    padding-bottom: 24px;
    padding-left-value: 20px;
    padding-left-ltr-source: physical;
    padding-left-rtl-source: physical;
    padding-right-ltr-source: physical;
    padding-right-rtl-source: physical;
    text-decoration: none;
    -moz-text-blink: none;
    -moz-text-decoration-color: -moz-use-text-color;
    -moz-text-decoration-line: none;
    -moz-text-decoration-style: solid;
    width: 190px;
    opacity: 0.60;
    color: rgb(12, 97, 125);
}

.hide-column-names table thead tr{
    display: none;
}

.home_div {
    border: 1px solid antiquewhite;
    background-color: #f7f7f7;
    padding: 5px 20px 5px 20px;  
    width: 570px
}


.home_msg_div {
    border: 0px solid antiquewhite;
    background-color: #f7f7f7;
    padding: 0px 0px 10px 20px;  
    width: 530px
}


.autocomplete.ui-autocomplete-panel{
    width:300px !important; 
}

.btns_home_msg_div{
    float: right;
    margin-top: 0px;
    margin-right: 8px;
    margin-left: 8px;
    height: 20px;
}

.btns_home_msg{
    height: 17px;
    background: transparent
}

.not-clickable {
    cursor: not-allowed;
    pointer-events: none;
    background-color:  #f0f0f0;
}
/*sarayu*/

/*start: done by priyanka*/
.ui-datepicker{
    width:16em !important;
    /*    width:100%  !important;*/
}

.ui-datepicker table{
    font-size: 0.6em!important;
}

.fc-header .fc-button{
    text-transform: capitalize;
}

.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight{
    background: #c9d1da;
}
.fc-header-left{
    display:none;
}

.fc-header{
    margin-top: -38px;
    font-size: 14px !important;
}

.fc-header-right{
    display:none;
}

.button_next{
    padding: 8px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border-radius: 4px;
    border:1px solid #888;
    background: url("images/next.png") ;
}

.button_prev{
    padding: 8px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border-radius: 4px;
    border:1px solid #888;
    background: url("images/pre.png") !important;
}

.button_active{
    margin-top: 2px;
    padding: 4px 14px;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border-radius: 4px;
    border:1px solid #888;       
    background: #c8d6f9;/* Old browsers */
}

.fc-grid{
    margin-top: -15px;  
}
/* end: done by priyanka*/

/* ============================ Login Css ========================*/

.login-form{
    position:relative;
    width:580px;
    margin:20ex auto 2ex auto;
}

.login-form td.title{
    width:26%;
    white-space:nowrap;
    color:#fff;
    text-shadow:0 1px 1px black;
    text-align:right;
    padding-right:1em;
    height:48px;
    font-size:14px;
}

.login-form td.title a{
    text-align:center;
    text-decoration:none;
    color:#fff;
}

.login-form p.formbuttons{
    text-align:center;
}

.login-form #logo{
    margin-bottom:20px;
    border:0
}

.login-form #message{
    min-height:40px;
    padding:5px 25px;
    text-align:center;
    font-size:1.1em
}

.login-form #message div{
    display:inline-block;padding-right:0;font-size:12px
}

.bottomline{
    font-size:90%;text-align:center;margin-top:1em
}

.login-form .box-inner{
    width:479px;
    background:#133755;
    margin:0 50px;
    padding:0px;
    border:1px solid #5d78b4;
    border-radius:5px;
    box-shadow:inset 0 0 1px #ccc;
    -o-box-shadow:inset 0 0 1px #ccc;
    -webkit-box-shadow:inset 0 0 1px #ccc;
    -moz-box-shadow:inset 0 0 1px #ccc;
}

.login-form .box-bottom{
    /*background:url(images/login_shadow.png) top center no-repeat;*/
                        margin-top:-3px;padding-top:10px
}

.login_input input{
    width: 280px;
}

.forgot_pass a{
    margin-left:86px;
    color:#bbc;
    text-decoration:none;
    text-align: center;
    font-size: 12px;
}

.forgot_pass a:hover{
    text-decoration:underline;
}

.login-form .button{
    color:#444;
    text-shadow:0 1px 1px #fff;
    border-color:#f9f9f9;
    background:#f9f9f9;
    background:-moz-linear-gradient(top,#f9f9f9 0,#e2e2e2 100%);
    background:-webkit-gradient(linear,left top,left bottom,color-stop(0,#f9f9f9),color-stop(100%,#e2e2e2));
    background:-o-linear-gradient(top,#f9f9f9 0,#e2e2e2 100%);
    background:-ms-linear-gradient(top,#f9f9f9 0,#e2e2e2 100%);
    background:linear-gradient(top,#f9f9f9 0,#e2e2e2 100%);
    box-shadow:inset 0 1px 0 0 #fff;
    -moz-box-shadow:inset 0 1px 0 0 #fff;
    -webkit-box-shadow:inset 0 1px 0 0 #fff;
    -o-box-shadow:inset 0 1px 0 0 #fff;
}

/* ============================ Login Css End ========================*/
/* Updates for Contact Group and Product Potentials Box */
.potential-css{
    overflow-y: scroll;
    opacity: 0.8 !important;
    line-height: 20px;
    height: 70px;
    border:2px solid #cbcbcb;
    padding:5px;
    margin-left:-5px!important;
    width:280px;
}

/* ============================ Journal Css ========================*/
.button_add_opp{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url("images/link-add.png") !important;
}
.button_add_opp:hover{
    
}

.button_jrnl_plus{
    padding: 8px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border-radius: 4px;
    border:1px solid #888;
    background: url("images/add.png") !important;
}

.button_link_opp{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url("images/link.png") !important;
}

.button_multi_link_opp{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url("images/multi-link-opp.png") !important;
} 

.button_sales_team_change{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url("images/change-lookup.png") !important;
}

.button_event_disabled{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url("images/event-disabled.png") !important;
}

.button_event_enabled{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;   
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url("images/event-enabled.png") !important;
}

.button_sms_enabled{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;   
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url("images/sms-enabled.png") !important;
}

.button_sms_disabled{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url("images/sms-disabled.png") !important;
}

/* ============================ Journal Css End ========================*/

/* 09/08/2016 - Nisha - Job Title, Dept length extension */
.con-fld-len{
    width: 100%;
    text-overflow: ellipsis;
}


/* ============================ Priatek css ========================*/
.icon_green{
    padding: 8px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border-radius: 4px;
    border:1px solid #888;
    background: url("images/green_icon.png") !important;
}

.icon_red{
    padding: 8px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border-radius: 4px;
    border:1px solid #888;
    background: url("images/red_icon.png") !important;
}

.icon_orange{
    padding: 8px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border-radius: 4px;
    border:1px solid #888;
    background: url("images/orange_icon.png") !important;
}

/*hide scrollable datatable header*/
.scrollhide .ui-widget-header{
    background: none !important;
}

/*p:editor issue on viewing source*/
.ui-editor textarea{
    width:100% !important;
    height: 200px!important
}


