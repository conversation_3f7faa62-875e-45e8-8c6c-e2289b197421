/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 8 May, 2019, 12:40:53 PM
    Author     : nishach
*/

/*Used for applying colour to the header text*/
.sub_title {
    color:#3c8dbc;
    /*    background-color: #ecf0f5;*/
}

/*Used for applying background colour to the header text*/
.header-bar{
    /* background-color: #ecf0f5;
    align-items: center;
    margin-bottom: 6px;
    margin-top: 5px;
    height: 40px;*/
    margin-bottom: 10px;
    align-items: center;
    background-color: #ecf0f5;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0.05);
    height: 40px;
}

/*Used for applying background colour to the btton row*/
.btn-well{
    background-color: #ecf0f5;;
    padding: 6px;
    border-radius: 5px;
}

.sub-header{
    font-weight: bold !important;
    font-size: 11px;
    color:#3c8dbc;
}

.logoUpload .ui-fileupload-buttonbar{
    background:transparent none;
    border: none;
}

.logoUpload .ui-fileupload-content{
    display:none;
}

.pageContent {
    float: left;
    width: 100%;
    /*background-color: black;*/
    position: relative;
    left: 0;
}

.pageContent:before,
.pageContent:after {
    content: " ";
    display: table;
}

.pageContent:after {
    clear: both;
}

.left-column {
    float: left;
    width: 20%;
    min-height: 600px;
    overflow: hidden;
    margin: 0 auto;
    margin-top: -3px !important;
    border-right: 1px solid #dddddd;
    padding-right: 5px;
    padding-left: 5px;
    /*position: fixed;
    background: wheat;*/
}
.left-column:hover{ /* overflow-y:scroll;*/}

.left-column:after {
    content: "";
    /*background-color: red;*/
    position: absolute;
    width:5px;
    height: 100px;     
    display: block;
    /*position: fixed;*/ /*Duplicate: Found by Sonarqube */
    /*background: wheat;*/
}

.right-column {
    float: right;
    width: 80%;
    position: relative;
    padding: 2px;
    margin: 0;     
    /*background: rebeccapurple;*/            
}

.col_right{
    text-align: right ;
}
.col_left{
    text-align: left !important;
}

.content-header>h1 {
    font-size: 20px !important;
    height: 30px !important;
}
.content-header {
    padding: 5px 5px 0 5px;
}
.content {
    min-height: 250px !important;
    padding-top: 6px !important;
    padding: initial !important;
    /*padding-left: none !important;
    padding-right: none !important;*/
}

.box.box-info {
    padding: 0;
}


.left-column .ui-datatable-odd{
    background-color: white !important;
}
.left-column .ui-datatable-even{
    background-color: white !important;
}

.left-column [role="columnheader"]{
    background-color: #c5ddf5 !important;
}

.button_bar{
    padding-left: 6px}

.left-column [role="gridcell"]{
    /*line-height: 5px !important;*/
    padding: 1px !important;
}
.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight, .form-control .ui-state-highlight {
    /* background-color: #c7e0f4!important;
    background-image: linear-gradient(to bottom, #c7e0f4, #c7e0f4)!important;
    color: #464646!important; */
}

.required:after{content:" * ";color: red;}

/*To hide the duplicate PF datatable header*/
.ui-datatable-scrollable-theadclone {
    visibility: collapse;
}

.right-column [role="gridcell"]{
    /*line-height: 3px !important;*/
    padding: 4px 10px !important;
}



/*Used for applying underline style to the text*/
.text-underline{
    text-decoration: underline;
}

/*Used for applying minimum width to the buttons*/
.btn{
    min-width: 80px;
}

.div-center{
    text-align: center;
}

/*.btn-icon-group{
    height:25px;
    width:25px;
}*/

.btn-icon{
    width: 23px !important;
    height: 23px !important;

}

.btn-icon-group{
    height:25px;
    width:25px;
}
.not-clickable {
    cursor: not-allowed;
    pointer-events: none;
    background-color:  #f0f0f0;
}

body .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar {
    background: #c4d9f6 !important;
}

.ui-dialog-title {
    font-weight: bold!important;
}
.imageUpload .ui-fileupload-content{
    display:none;
}

.imageUpload .ui-fileupload-buttonbar .ui-fileupload-choose{
    margin-top: 2px;
    text-decoration: none;
    width:65px;
    border-radius: 4px;
    border:1px solid #888;
}

.imageUpload .ui-fileupload-buttonbar .ui-fileupload-choose .ui-icon{
    visibility: hidden !important;
}

.imageUpload .ui-fileupload-buttonbar .ui-fileupload-choose .ui-button-text{
    padding-left:1em !important;
    font-weight: bold;
    font-size:12px;
}

/*.ui-dialog-titlebar .ui-widget-header
{    
     background: #c4d9f6 !important;
}*/
/*padding of each cell in a page}*/
.ui-lg-1, .ui-lg-2, .ui-lg-3, .ui-lg-4, .ui-lg-5, .ui-lg-6, .ui-lg-7, .ui-lg-8, .ui-lg-9, .ui-lg-10, .ui-lg-11, .ui-lg-12 {
    padding: .5em;
    padding-top: 3px !important;
    padding-bottom: 3px !important;
}
/*input box height}*/
input {
    height: 26px !important;
}
/*select dropsown, multiselct dropdown height}*/
.ui-selectonemenu,.ui-selectcheckboxmenu-multiple-container{
    height: 26px !important;
}
/*multiselect lable alignment}*/
.ui-selectonemenu-label{
    padding-top: inherit !important;
}
/*datatable columntoggler multiselect button}*/
.ui-button.ui-widget.ui-state-default.ui-corner-all.ui-button-text-icon-left{
    height: 26px !important;
    padding-top: inherit !important; 
}
/*dailog footer space reduced*/
body .ui-dialog.ui-widget-content .ui-dialog-buttonpane, body .ui-dialog .ui-dialog-buttonpane{
    margin: 0px 0px 0px 0px !important; 
}
/*dailog upload file div button border and text center*/
div.ui-fileupload {
    -webkit-box-shadow: none !important;
}
.ui-widget-header {
    border: none !important; 
}
.ui-button-text .ui-c{
    padding-top: inherit !important; 
}

.red-box {
    width:50px;
    height: 40px;
    display: inline-block;
    background-color: red;
    position: relative;
    top:15px;
}

.green-box {
    width:50px;
    height: 40px;
    display: inline-block;
    background-color: #00cc00;
    position: relative;
    top:15px;
}

.yellow-box {
    width:50px;
    height: 40px;
    display: inline-block;
    background-color: yellow;
    position: relative;
    top:15px;
}

.div-selected{
    height: 25px;
    width: 100%;
    text-align: center;
    background: #0078d7 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
    background: #0078d7 -moz-linear-gradient(top, rgba(255,255,255,0.8), rgba(255,255,255,0)); /*firefox*/
}

.div-header{
    height: 25px;
    width: 100%;
    text-align: left;
    background: #000 ;

}



.compNamebg{
    padding: 5px;
    color: black;
    font-weight: bold;
    width: 320px;
    text-align: left;
    font-size: 1.2em;
    float: left;
}

.btnRR{
    /*border-style: none !important;*/
    border-color: darkgrey; 
    width:85px;
    height: 30px;
}

.button_add_opp{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url("#{request.contextPath}/resources/images/link-add.png") !important;
}
.button_add_opp:hover{

}
.dialogCSS
{
    z-index: 900 !important;
}

body.skin-blue div.navbar-header button.navbar-toggle-collapsed:hover, body.skin-blue-light div.navbar-header button.navbar-toggle-collapsed:hover, body.skin-blue div.navbar-header button.navbar-toggle:hover, body.skin-blue-light div.navbar-header button.navbar-toggle:hover{
    /*color: #337ab7 ;*/

}

.runRateCol{
    width: 5%;text-align: center;
}


.ui-picklist-button-remove {
    transform: rotate(360deg)scaleX(-1)!important;
}
.footerFont{
    font-weight: bold !important;
}

.col_right{
    text-align: right ;
}

.ui-paginator {
    background-color: #dedede!important;
}
thead {
    background-color: #dedede!important;
}

.lbl_cnt{    
    color:#044238;
    font-size: 12px ;
}

input[readonly],textArea[readonly]{
    background-color: #f3f3f3!important;
    color: black!important;
    cursor:not-allowed!important;
}

option:disabled{
    color: red!important;
    background-color: red!important;
}

/*.navbar-nav>.user-menu>.dropdown-menu>li.user-header {
    height: 56px!important;
        padding: 1px!important;
}*/
/*#7606 ys-204 opportunity icon*/
.linkorCreateOpp{   
    text-decoration: none;
    color:#000;
    font-size: 0px;
    border: none;
    border-radius: 0px;
    border:0px solid #fff !important;
    background: url(#{request.contextPath}/resources/images/rf_opp_icon_plus.svg)! important;
    width:30px!important;
    background-repeat:no-repeat!important;
    background-image:white !important;
    outline: none;
}

/*#7606 ys-204 opportunity icon*/
.openorViewOpp{   
    text-decoration: none;
    color:#000;
    font-size:0px;
    border: none;
    border-radius:0px;
    border:0px solid #fff !important;
    background: url(#{request.contextPath}/resources/images/rf_opp_icon1.svg)! important;
    width:30px!important;
    background-repeat:no-repeat!important;
    background-image:white !important;
    outline: none;
}

/*10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac*/
.might-overflow {
    text-overflow: ellipsis;
    overflow : hidden;
    white-space: nowrap;
    max-width: 200px
}

/*10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac*/
.might-overflow:hover {
    text-overflow: clip;
    white-space: normal;
    word-break: break-all;
    max-width: 200px
}