.button_event_disabled{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url(#{request.contextPath}/resources/images/event-disabled.png) !important;
    width:30px!important;
    background-repeat:no-repeat!important;
    background-image:white !important;
}

.button_event_enabled{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;   
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url(#{request.contextPath}/resources/images/event-enabled.png) !important;
    width:30px!important;
    background-repeat:no-repeat!important;
    background-image:white !important;
}

.button_sms_enabled{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;   
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url(#{request.contextPath}/resources/images/sms-enabled.png) !important;
    width:30px!important;
    background-repeat:no-repeat!important;
    background-image:white !important;
}

.button_sms_disabled{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url(#{request.contextPath}/resources/images/sms-disabled.png) !important;
    width:30px!important;
    background-repeat:no-repeat!important;
    background-image:white !important;
}
/* CRM-107: Used in Tasks */
.button_task_enabled{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;   
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url(#{request.contextPath}/resources/images/task.png) !important;
    width:30px!important;
    background-repeat:no-repeat!important;
    background-image:white !important;
}
/* CRM-400 : Icons in AJ for linked tasks, messges, events*/
.button_task_plus_enabled{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;   
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url(#{request.contextPath}/resources/images/taskdefault.png) !important;
    background-repeat:no-repeat!important;
    width:30px!important;
    background-repeat:no-repeat!important;
    background-image:white !important;
}

.button_sms_plus_enabled{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url(#{request.contextPath}/resources/images/smsdefault.png) !important;
    width:30px!important;
    background-repeat:no-repeat!important;
    background-image:white !important;
}

.button_event_plus_enabled{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;   
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url(#{request.contextPath}/resources/images/eventdefault.png) !important;
    width:30px!important;
    background-repeat:no-repeat!important;

}
.button_add_opp{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url(#{request.contextPath}/resources/images/link-add.png) !important;
    background-repeat:no-repeat !important;
    width:30px;
}
.button_add_opp:hover{

}

.button_jrnl_plus{
    padding: 8px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border-radius: 4px;
    border:1px solid #888;
    background: url(#{request.contextPath}/resources/images/add.png) !important;
}

.button_link_opp
{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url(#{request.contextPath}/resources/images/link.png) !important;
}

.button_multi_link_opp{
    padding: 10px 0px !important;
    text-decoration: none;
    color:#000;
    font-size: 12px;
    border: none;
    border-radius: 20px;
    border:0px solid #888 !important;
    background: url("#{request.contextPath}/resources/images/multi-link-opp.png") !important;
    background-repeat:no-repeat !important;
    width:30px;
}

/*03-01-2023 10316 CRM-6454 AJ web: converting to opp icons very unintuitive*/
.openorViewOppNew{   
    text-decoration: none;
    color:#000;
    font-size:0px;
    border: none;
    border-radius:0px;
    border:0px solid #fff !important;
    background: url(#{request.contextPath}/resources/images/opp_list_64.png)! important;
    width:25px!important;
    height:25px!important;
    background-repeat:no-repeat!important;
    background-image:white !important;
    outline: none;
    background-size: cover!important;
}