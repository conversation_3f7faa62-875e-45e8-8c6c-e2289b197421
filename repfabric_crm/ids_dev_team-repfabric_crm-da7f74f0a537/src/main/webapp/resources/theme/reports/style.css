/*    Generated by http://www.cssportal.com    */

/*@import url("reset.css");*/

body {
  font-family: Verdana, Arial, Helvetica, sans-serif;
  font-size: 13px;
  color:#333
}

p {
  padding: 1px;
}


#header {
  float: left;
  height: 180px;
  width: 100%;    

}

#header_com {
  float: left;
  /*height: 175px;*/
  width: 100%;    

}

#report_top_bar,#header,#header_com,#report_top_bar,#content {
  border:#9B9696 solid 1px;
}

#report_top_bar {
  float: left;
  height: 25px;
  width: 100%;
}

#content {
  float: left;
  background: #FFFFFF;
  width: 100%;
}

#footer {
  height: 20px;
  width: 100%;
  clear: both;
}

/*For PDF output*/
@page{
  size: 29.5cm 20.0cm;
}
.btnRR{
  /*border-style: none !important;*/
  border-color: darkgrey; 
  width:85px;
  height: 30px;
}
.runRateCol{
  width: 100px;text-align: center;
}

/* for table sort col color */
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active{
  background: #F5E64A -webkit-gradient(linear, left top, left bottom, from(rgba(252, 252, 252, 0.39)), to(rgba(132, 133, 126, 0.63))) !important;      
  background: #F5E64A -moz-linear-gradient(top, rgba(252, 252, 252, 0.39), rgba(132, 133, 126, 0.63)) !important; 
}

/*Reports table header */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default{
  background: #FFFAC2 -webkit-gradient(linear, left top, left bottom, from(rgba(252, 252, 252, 0.39)), to(rgba(132, 133, 126, 0.63)));      
  background: #FFFAC2 -moz-linear-gradient(top, rgba(252, 252, 252, 0.39), rgba(132, 133, 126, 0.63)); 

}


/* no table coloumn  */
/*.noborder .ui-datatable tbody td,.noborder .ui-datatable tfoot td{
  border-style: hidden !important;
}*/

.ui-datatable tfoot td {
  background: #FFF7D8 !important;
  border: white !important;
}


.ui-datatable tbody td {
  border-color: white !important;
}
.pva .ui-datatable tbody td {
  border-color: #F8F8F8 !important;
}

.div-prod-all .ui-datatable tbody td{
  border-color: #A29595 !important;
}
.div-prod-all .ui-datatable table{
  /*width: auto;*/
}

.div-prod-dtl .ui-datatable thead th,.div-prod-dtl  .ui-datatable tbody td,.div-prod-dtl  .ui-datatable tfoot td{
  height: 36px;
  padding: 0px 4px !important;
}

.ui-widget-content{
  color: black;
}
.ui-datatable-odd {
  background: #E5EEF7;
}
.pva .ui-datatable-even{
  background: #E5EEF7;
}
/*Reports table css end*/

/*Chart background*/
#header,#header_com,#footer {
  /*background: for Report header and chart are same;*/
  background: #C4C4C4 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));/* Chrome */
  background:  #c4c4c4 -moz-linear-gradient(top, rgba(255,255,255,0.8), rgba(255,255,255,0)); /*firefox*/
}
.transparent .jqplot-series-canvas{
  background: #C4C4C4 -webkit-gradient(linear, left top, left bottom, from(rgba(250, 247, 247, 0.8)), to(rgba(255,255,255,0)));
  background:  #c4c4c4 -moz-linear-gradient(top, rgba(250, 247, 247, 0.8), rgba(255,255,255,0)); /*firefox*/
}
.jqplot-title{
  top: 9px !important;
}


#report_top_bar{
  background: #F1E7D1 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));/* Chrome */
  background:  #F1E7D1 -moz-linear-gradient(top, rgba(255,255,255,0.8), rgba(255,255,255,0)); /*firefox*/
}
.btnExport{
  background: #FFCC33 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));/* Chrome */
  background: #FFCC33 -moz-linear-gradient(top, rgba(255,255,255,0.8), rgba(255,255,255,0)); /*firefox*/
}
.div-selected{
  height: 25px;
  width: 100%;
  text-align: center;
  background: #70A192 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
  background: #70A192 -moz-linear-gradient(top, rgba(255,255,255,0.8), rgba(255,255,255,0)); /*firefox*/
}
.compNamebg{
  padding: 5px;
  color: black;
  font-weight: bold;
  width: 360px;
  text-align: center;
  font-size: 1.2em;
}     


.barPrinc{
  background:#B4CF8A -webkit-gradient(linear, left top, left bottom, from(rgba(95, 87, 87, 0.33)), to(rgba(255, 255, 255, 0.83)));
  background: #70A192 -moz-linear-gradient(top, rgba(95, 87, 87, 0.33), rgba(255, 255, 255, 0.83)); /*firefox*/
}

.jqplot-meterGauge-label{
  color: black !important;
}

/*Product details*/
.tdrh{
  text-align: center;font-weight: bold;
}
.col_left{
  text-align: left !important;
}
.col_right{
  text-align: right ;
}
.col_center{
  text-align: center;
}

a:link {
  outline: none;
  color:#084E9B !important;
  text-decoration: none;
}
a:hover{
  text-decoration: underline;
}
a:visited{
  color: #084E8B;
}

.ui-selectonemenu-filter-container .ui-icon{
  position: absolute;
  top: 13px !important;
  right: 15px !important;
}

.ui-widget-header{
  background: #BBDDDD -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0.98)), to(rgba(240, 250, 250, 0.32)));
}

/*this is for Plan Vs Actual panel grid*/
.pva .ui-datatable tbody td{
  padding:4px 3px !important ;
}

/* Plan vs actual col width */
.pvaWidth{
  width: 100px;
}


/* Report header div */
.rpt-logo1, .rpt-logo2, .rpt-logo3{
  display:inline-block;
  height: auto;
  vertical-align: top;
  padding-left: 2px;
}

.rpt-logo1
{
  width:60%;
}
.rpt-logo2
{
  width:40%;
}
.rpt-logo3{
  width: 30%;
}
.bold{
  font-weight: bold;
  color: brown;
}

.jqplot-highlighter-tooltip, .jqplot-canvasOverlay-tooltip {
  font-size: 1em !important;
  background: #F3F3F3 !important;
}
.filterbox{
  width: 200px !important;
  /*height: 18px;*/
  font-size: 0.90em;
}


.ui-datalist-data
{
  margin-top: 0px;
  margin-bottom: 0px;
}