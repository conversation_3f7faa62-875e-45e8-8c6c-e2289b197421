/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 21 Jan, 2019, 11:30:19 AM
    Author     : nikilkb
*/

 
.ui-datatable .ui-datatable-header {
    text-align: right !important;
    }
    
.m{
    margin-bottom:0 !important;
    position: absolute !important;
    display: none !important;
    }
/*    
* {
    font-family: 'Open Sans', sans-serif;
    font-size:12px;
    }*/

body {
    background-color:#ececec;
}

.container {
  margin: 50px 0 0 100px;
}

.tile {
  width:160px;
  height:180px;
  border-radius:4px;
  box-shadow: 2px 2px 4px 0 rgba(0,0,0,0.15);
  margin-top:20px;
  margin-left:20px;
  float:left;
}

.tile.wide {
  width: 340px;
}

.tile .header {
  height:120px;
  background-color:#f4f4f4;
  border-radius: 4px 4px 0 0;
  color:white;
  font-weight:300;
}

.tile.wide .header .left, .tile.wide .header .right {
  width:160px;
  float:left;
}

.tile .header .count {
  font-size: 48px;
  text-align:center;
  padding:10px 0 0;
}

.tile .header .title {
  font-size: 20px;
  text-align:center;
}

.tile .body {
  height:60px;
  border-radius: 0 0 4px 4px;
  color:#333333;
  background-color:white;
}

.tile .body .title {
    text-align:center;
    font-size:20px;
    padding-top:2%;
}

.tile.wide .body .title {
  padding:4%;
}

.tile.job .header {
  background: linear-gradient(to bottom right, #609931, #87bc27);
}

.tile.job  .body {
  color: #609931;
}

.tile.resource .header {
  background: linear-gradient(to bottom right, #ef7f00, #f7b200);
}

.tile.resource  .body {
  color: #ef7f00;
}

.tile.quote .header {
  background: linear-gradient(to bottom right, #1f6abb, #4f9cf2);
}

.tile.quote  .body {
  color: #1f6abb;
}

.tile.invoice .header {
  background: linear-gradient(to bottom right, #0aa361, #1adc88);
}

.tile.invoice  .body {
  color: #0aa361;
}









 