.sidebar-form {
    border-radius: 3px;
    border: 1px solid #374850;
    margin: 10px 10px;
}
.sidebar-form, .sidebar-menu>li.header {
    overflow: hidden;
    text-overflow: clip;
}

#userImage {
    float: left;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    margin-right: 10px;
    margin-top: -2px;
}

ul.dropdown-menu > li.user-header {
    height: auto!important;
}

a.dropdown-toggle {
    background-color: #3c8dbc!important;
}

a#logout {
    color: #3c8dbc!important;
    background: transparent;
}

@media (max-width: 768px) {
    body .control-sidebar {
        padding-top:100px;
    }
}

/*#content{
    min-height: 700px!important;
}
html,body{
    position: absolute!important;
    left: 0!important;
    top: 0!important;
    width: 100%!important;
     
    overflow: hidden!important;
    margin: 0!important;
    padding: 0!important;
    height: 700px!important;
    min-height: 700px!important;
}*/
/*.wrapper{
    height: 700px!important;
}*/