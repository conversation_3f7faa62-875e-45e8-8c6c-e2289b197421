/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Too<PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 29 Jan, 2019, 11:05:17 AM
    Author     : nikilkb
*/

/*

_outlook_theme

*/

.skin-blue-light .sidebar a {
    color: #2c7abb!important;
    font-weight: 100!important;
}
.skin-blue-light .sidebar-menu>li.header {   
    background: #ffffff!important;
    color: #ada4a4!important;
}
.skin-blue-light a.dropdown-toggle {
    background-color: #0078D7!important;
}
.skin-blue-light .main-header .logo{
    background-color: #ffffff!important;
    border-bottom: 1px solid #ececec!important;
}
.skin-blue-light a.dropdown-toggle {
    background-color: #0078D7!important;
}
.sidebar-menu>li:hover>a, .skin-blue-light .sidebar-menu>li.active>a {
    color: #005a9e!important;
    background: #b3d6f2!important;
}
.skin-blue-light .main-header .navbar .sidebar-toggle:hover {
    background-color: #0d6bb9!important;
}
.skin-blue-light .main-header li.user-header {
    /*background-color: #0078D7!important;*/
background-color: #3cabff!important;
}
.skin-blue-light .wrapper, .skin-blue-light .main-sidebar, .skin-blue-light .left-side {
    background-color: #ffffff!important;
}
.container {
     padding-right: 0px!important;
     padding-left: 0px!important;
}


.main-header .navbar {    
    background-color: #0078D7!important;
}
body .ui-paginator .ui-paginator-page.ui-state-active{
    background: #0078D7!important;
}
.btn-primary {
    background-color: #0078D7 !important;
}
.main-header .navbar {    
    background-color: #0078D7!important;
}

body .ui-paginator .ui-paginator-page.ui-state-active{
    background: #0078D7!important;
}

.box.box-info {
    border-top-color: #ffffff;
}
.ui-widget .ui-outputlabel {
    font-weight: 500!important;
}

table[role="grid"] tbody tr td a {
    color:#444;
}






.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .open>.dropdown-toggle.btn-primary{
    background-color: #0d6bb9!important;
}    
button.ui-button.btn-primary:hover, button.ui-button.btn-primary.ui-state-hover{
    background-color: #0d6bb9!important;
}

.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight, .form-control .ui-state-highlight{
/*    background-color: #c7e0f4!important;
    background-image: linear-gradient(to bottom,#c7e0f4,#c7e0f4)!important;
    color: #464646!important;*/
  background-color: #dfe2e6!important;
    background-image: linear-gradient(to bottom,#dfe2e6,#dfe2e6)!important;
    color: #464646!important;
}
.ui-state-highlight a, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a {
    color: #444!important;
}
.ui-paginator .ui-paginator-top .ui-widget-header{
    background: #d0d0d0!important; 
    /*#d4d4d4     #922923*/
}
thead{
    background-color:#7373731f!important; 
}
.ui-paginator{
    background-color: #7373731f!important; 
}

/*
progress bar css
*/
#progress {
    position: fixed;
    z-index: 2147483647;
    top: 0;
    left: -6px;
    width: 0%;
    height: 2px;
    background: #f39c12;
    -moz-border-radius: 1px;
    -webkit-border-radius: 1px;
    border-radius: 1px;
    -moz-transition: width 500ms ease-out,opacity 400ms linear;
    -ms-transition: width 500ms ease-out,opacity 400ms linear;
    -o-transition: width 500ms ease-out,opacity 400ms linear;
    -webkit-transition: width 500ms ease-out,opacity 400ms linear;
    transition: width 500ms ease-out,opacity 400ms linear
}
#progress.done {
    opacity: 0
}
#progress dd,#progress dt {
    position: absolute;
    top: 0;
    height: 2px;
    -moz-box-shadow: #f39c12 1px 0 6px 1px;
    -ms-box-shadow: #f39c12 1px 0 6px 1px;
    -webkit-box-shadow: #f39c12 1px 0 6px 1px;
    box-shadow: #f39c12 1px 0 6px 1px;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%
}
#progress dd {
    opacity: 1;
    width: 20px;
    right: 0;
    clip: rect(-6px,22px,14px,10px)
}
#progress dt {
    opacity: 1;
    width: 180px;
    right: -80px;
    clip: rect(-6px,90px,14px,-6px)
}
@-moz-keyframes pulse {
    30% {
        opacity: 1
    }
    60% {
        opacity: 0
    }
    100% {
        opacity: 1
    }
}
@-ms-keyframes pulse {
    30% {
        opacity: .6
    }
    60% {
        opacity: 0
    }
    100% {
        opacity: .6
    }
}
@-o-keyframes pulse {
    30% {
        opacity: 1
    }
    60% {
        opacity: 0
    }
    100% {
        opacity: 1
    }
}
@-webkit-keyframes pulse {
    30% {
        opacity: .6
    }
    60% {
        opacity: 0
    }
    100% {
        opacity: .6
    }
}
@keyframes pulse {
    30% {
        opacity: 1
    }
    60% {
        opacity: 0
    }
    100% {
        opacity: 1
    }
}
#progress.waiting dd,#progress.waiting dt {
    -moz-animation: pulse 2s ease-out 0s infinite;
    -ms-animation: pulse 2s ease-out 0s infinite;
    -o-animation: pulse 2s ease-out 0s infinite;
    -webkit-animation: pulse 2s ease-out 0s infinite;
    animation: pulse 2s ease-out 0s infinite
}