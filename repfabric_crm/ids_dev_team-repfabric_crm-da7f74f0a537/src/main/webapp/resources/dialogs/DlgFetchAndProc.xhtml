<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--#6591: <PERSON>rinko CRMSync: Sync Conflicts-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui">
    <!--#6591: Aurinko CRMSync: Sync Conflicts-->
    <p:dialog id="dlgIdFetchRec" widgetVar="dlgFetchRec" closable="false" modal="true"  header="Message" resizable="false" >
        <p:outputPanel >
            <br />
            <h:graphicImage  library="images" name="ajax-loader.gif"   />
            <p:spacer width="5" />
            <p:outputLabel value="Please wait fetching records..." />
            <br /><br />
        </p:outputPanel>
    </p:dialog>

    <!--#6591: Aurinko CRMSync: Sync Conflicts-->
    <!--30-03-2022 7595 CRM-5488: CRMsync: new acct creation in target system-->
    <p:dialog id="dlgIdProcessing" widgetVar="dlgProcessing" closable="false" modal="true" header="Message" onShow="PF('dlgProcessing').initPosition();" resizable="false" >
        <h:form>
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif"   />
                <p:spacer width="5" />
                <p:outputLabel value="Processing please wait..." />
                <br /><br />
            </p:outputPanel>
        </h:form>
    </p:dialog>
</ui:composition>
