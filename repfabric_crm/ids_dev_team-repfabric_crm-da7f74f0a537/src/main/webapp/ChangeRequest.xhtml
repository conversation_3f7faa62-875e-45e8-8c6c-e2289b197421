<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://xmlns.jcp.org/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:f="http://xmlns.jcp.org/jsf/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

    <!--Feature #8385: ESCALATION: CRM-5589:  Password Recovery-->
    <ui:define name="metadata">
        <f:metadata>
            <f:viewParam id="token" name="token" value="#{loginForm.token}"/>
            <f:viewAction action="#{loginForm.pageLoad()}"/>
        </f:metadata>
    </ui:define>
    <h:head>
        <h:outputStylesheet library="theme" name="style.css"/>
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['images/icon.png']}" />
        <title>Change Password </title>
        <meta charset="utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"/>
        <meta name="theme-color" content="#444"/>
        <meta charset="ISO-8859-1"></meta>
    </h:head> 
    <h:body> 
        <div>

            <div   style="width: 100vw;">

                <h:form id="usrWizardForm">
                    <p:growl id="growlGoal" globalOnly="false" keepAlive="true" >
                        <p:autoUpdate />
                    </p:growl>
                    <p:panelGrid columns="1" 
                                 styleClass="box-primary no-border ui-fluid" layout="grid"
                                 style="text-align: center!important; align-items: center!important; padding-top: 50px!important; width: 100vw">
                        <!--//12-06-2023 : #11375 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo-->
                        <p:graphicImage library="images" name="#{fabricClient.clientType}/brand_logo.png"  class="#{fabricClient.clientType == 'Repfabric' ? 'ui-chngRqst-replogo' : 'ui-chngRqst-othrlogo'}"/>
                        <p:outputLabel rendered="#{loginForm.active and loginForm.changed eq false}" style="color: black;font-weight: 600; font-size: x-large; margin-top: 15px" value="RESET PASSWORD " />
                    </p:panelGrid>  <br/>
                    <div >
                        <p:panelGrid>
                            <p:panelGrid rendered="#{loginForm.active and loginForm.changed eq false}">
                                <div class="info-msg">Password must include a capital letter, a number, and a special character.</div> 
                                <br/><br/>
                                <div class="info-msg">Please store this password securely.</div>
                            </p:panelGrid>  

                            <div style=" width: 540px; text-align: center; margin: auto; ">
                                <p:panelGrid rendered="#{loginForm.active and loginForm.changed eq false}" >
                                    <div style=" width: 540px; text-align: center; border-style: solid; border-color: #f17d54; border-width: medium; margin: auto; margin-top: 20px; padding: 10px">
                                        <p:outputLabel style="color: black; width: 150px; font-size: 16px"  value="Login ID "/>
                                        <p:spacer width="15"/>
                                        <p:inputText label="Login ID"  value="#{loginBean.userLogin}" style="width:300px; font-size: 16px; margin-bottom: 10px" required="true" readonly="true"/>
                                        <br/>
                                        <p:outputLabel style="color: black; width: 150px; font-size: 16px" for="pwd1" value="Password" />
                                        <p:spacer width="15"/>
                                        <p:password id="pwd1" value="#{loginBean.resetPass}" maxlength="25" style=" width: 300px; font-size: 16px; margin-bottom: 10px"
                                                    label="Password" > 
                                            <p:ajax event="change" process="@this"/>
                                        </p:password>
                                        <br/>
                                        <p:outputLabel style="color: black; width: 150px; font-size: 16px" for="pwd2" value="Confirm  Password" />
                                        <p:spacer width="15"/>
                                        <p:password id="pwd2" value="#{loginBean.userCnfPassword}" maxlength="25" 
                                                    style=" width: 300px; font-size: 16px; margin-bottom: 10px" > 
                                            <p:ajax event="change" process="@this" />
                                        </p:password> 
                                        <br/>
                                        <p:commandButton immediate="true" value="Save" id="saveBtn" widgetVar="saveButton" update=":usrWizardForm"
                                                         process="@form" style="width:80px; height: 28px; padding: 0px; background-color: #50ad50; color: white;" 
                                                         actionListener="#{loginForm.resetPassword(loginBean.userId, loginBean.resetPass, loginBean.userCnfPassword)}"  />                                                
                                    </div>
                                </p:panelGrid>
                                <p:panelGrid rendered="#{loginForm.changed eq true}">
                                    <div style=" width: 540px; text-align: center; border-style: solid; border-color: #f17d54; border-width: medium; margin: auto; margin-top: 20px; padding: 10px">
                                        <h:link outcome="Login.xhtml" value="Click here to go to Repfabric Login Screen" style=" color: #0000FF;font-weight: 600; font-size:16px; margin: auto; text-align: center; width: 600px" />
                                    </div>
                                </p:panelGrid>   
                                <p:panelGrid rendered="#{loginForm.active eq false}" style=" width: 540px; text-align: center; margin: auto; margin-top: 30px; padding: 10px">
                                    <div style=" width: 540px; text-align: center; margin: auto; margin-top: 20px; padding: 10px">
                                        <p:outputLabel style="color: black;font-weight: 600; font-size: 16px; margin: auto; text-align: center; width: 600px; margin-bottom: 20px" value="Your request is invalid" />
                                        <h:link outcome="Login.xhtml" value="Click here to go to Repfabric Login Screen" style=" color: #0000FF;font-weight: 600; font-size:16px; margin: auto; text-align: center; width: 600px" />
                                    </div>
                                </p:panelGrid>                      
                            </div>
                        </p:panelGrid>
                    </div>

                </h:form>
            </div>
        </div>

        <style>

            .info-msg {
                font-size: medium;
                text-align: center;
                font-weight: 300;
            }

            .inline { 
                display: inline-flex; 
                padding: 15px;
                line-height: 35px;
            }
            /*//12-06-2023 : #11375 : CRM-7035   Repfabric Rebranding: Distifabric, Manufabric, Synchocare, Medfabric - ability replace lo*/
            
            .ui-chngRqst-replogo{
                padding: 10px!important; 
                margin-top: -28px;
            }
            .ui-chngRqst-othrlogo{
                padding: 10px!important;
                margin-top: -28px;
                width:230px;
                height:70px;
            }

        </style>
    </h:body>
</html>