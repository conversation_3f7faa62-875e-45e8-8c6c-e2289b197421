<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: Setup.xhtml
// Author: Sarayu
//*********************************************************
/*
*
* Copyright (c) 2015 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>

    <ui:define name="metadata">


        <f:metadata> 
            <!--<f:viewAction action="#{example.isPageAccessible}"/>-->
        </f:metadata>
    </ui:define>

    <ui:define name="title">

        <ui:param name="title" value="Header"/>
        <div class="row">
            <div class="col-md-6">
                Header 
            </div>
            <div class="col-md-6" align="right">
                <h:form>
                    <p:commandButton value="Export" actionListener="#" styleClass="btn btn-primary btn-xs"  icon="fa fa-external-link"/> 
                    <p:commandButton value="Search" actionListener="#" styleClass="btn btn-primary btn-xs"  icon="fa fa-search"/> 
                    <p:commandButton value="Close" actionListener="#" styleClass="btn btn-danger btn-xs"  icon="fa fa-times"/> 
                    <p:commandButton value="New" actionListener="#" styleClass="btn btn-primary btn-xs"  icon="fa fa-plus"/> 

                </h:form>
            </div>
        </div>
    </ui:define>

    <ui:define name="menu">

    </ui:define>

    <ui:define name="body">
        <!--<f:event listener="#{example.isPageAccessible}" type="preRenderView"/>-->

        <p:spacer height="15px"/>
        <h:form>

            <div class="box box-primary">
                <div class="box-header with-border">
                    <div class="row">
                        <div class="col-md-12">
                            <p:commandButton value="Test Email" actionListener="#{rFUtilities.openEmail('fn=reply&amp;email_id=2320')}" immediate="true" styleClass="btn btn-primary  btn-xs" />
                            <p:spacer width="10"/>
                            <p:commandButton actionListener="#" id="submitButton" value="Save" update="grid" styleClass="btn btn-success  btn-xs"/>                 
                            <p:spacer width="10"/>
                            <p:commandButton value="Cancel" actionListener="#" styleClass="btn btn-warning  btn-xs"/>      
                            <p:spacer width="10"/>
                            <p:commandButton value="Delete" actionListener="#" styleClass="btn btn-danger  btn-xs"/>      
                            <p:spacer width="10"/>
                            <p:commandButton value="Update" actionListener="#" styleClass="btn btn-primary  btn-xs"/>      
                            <p:spacer width="10"/>
                            <p:commandButton value="Cancel" actionListener="#" styleClass="btn btn-warning  btn-xs"/>      
                            <p:spacer width="10"/>
                            <p:commandButton value="Edit" actionListener="#" styleClass="btn btn-info  btn-xs"/> 
                            <p:spacer width="10"/>
                            <p:commandButton  actionListener="#" styleClass="btn-primary btn-xs" title="New" icon="fa fa-plus"/> 
                            <p:spacer width="10"/>
                            <p:commandButton  actionListener="#" styleClass="btn-danger btn-xs" title="Delete" icon="fa fa-trash"/> 
                            <p:spacer width="10"/>
                            <p:commandButton  actionListener="#" styleClass="btn-primary btn-xs" title="Down" icon="fa  fa-arrow-down"/> 
                            <p:spacer width="10"/>
                            <p:commandButton  actionListener="#" styleClass="btn-primary btn-xs" title="Left" icon="fa fa-arrow-left"/> 
                            <p:spacer width="10"/>
                            <p:commandButton  actionListener="#" styleClass="btn-primary btn-xs" title="Right"  icon="fa fa-arrow-right"/> 
                            <p:spacer width="10"/>
                            <p:commandButton  actionListener="#" styleClass="btn-primary btn-xs" title="Up"  icon="fa fa-arrow-up"/> 
                        </div>                        
                    </div> 
                </div>
                <div class="ui-fluid">
                    <p:panelGrid id="grid" columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                 style="min-height: 100px"       layout="grid" styleClass="box-primary no-border ui-fluid">



                        <p:outputLabel for="input" value="Input" styleClass="required" />
                        <p:inputText id="input" required="true"/>

                        <p:outputLabel for="area" value="Textarea"/>
                        <p:inputTextarea id="area"/>

                        <p:outputLabel for="calendar" value="Calendar"/>
                        <p:calendar id="calendar" showOn="button"/>

                        <p:outputLabel for="ac" value="Auto Complete"/>
                        <p:autoComplete id="ac" completeMethod="#{autoCompleteMB.completeText}" dropdown="true"/>

                        <p:outputLabel for="mask" value="Mask"/>
                        <p:inputMask id="mask" mask="99/99/9999"/>

                        <p:outputLabel for="pwd" value="Password"/>
                        <p:password id="pwd"/>

                        <p:outputLabel for="keyboard" value="Keyboard"/>
                        <p:keyboard id="keyboard"/>

                        <p:outputLabel for="onemenu" value="OneMenu"/>
                        <p:selectOneMenu id="onemenu">
                            <f:selectItem itemLabel="Select One" itemValue=""/>
                            <f:selectItem itemLabel="Xbox One" itemValue="Xbox One"/>
                            <f:selectItem itemLabel="PS4" itemValue="PS4"/>
                            <f:selectItem itemLabel="Wii U" itemValue="Wii U"/>
                        </p:selectOneMenu>

                        <p:outputLabel for="spinner" value="Spinner"/>
                        <p:spinner id="spinner"/>

                        <p:outputLabel for="slider" value="Slider"/>
                        <p:outputPanel>
                            <p:inputText id="slider"/>
                            <p:slider for="slider"/>
                        </p:outputPanel>

                        <p:outputLabel for="radio" value="Radio"/>
                        <p:selectOneRadio id="radio" value="#{radioMB.city2}" layout="responsive" columns="3">
                            <f:selectItems value="#{radioMB.cities}" var="c" itemLabel="#{c}" itemValue="#{c}"/>
                        </p:selectOneRadio>

                        <!--                        <p:outputLabel for="chkbox" value="Checkbox"/>
                                                <p:selectManyCheckbox id="chkbox" value="#{checkboxMB.selectedCities2}" layout="responsive"
                                                                      columns="3">
                                                    <f:selectItems value="#{checkboxMB.cities}" var="city" itemLabel="#{city}" itemValue="#{city}"/>
                                                </p:selectManyCheckbox>-->

                        <p:outputLabel for="btn" value="Button"/>
                        <p:commandButton id="btn" value="Edit" icon="fa fa-edit" type="button" styleClass="btn-primary"/>

                        <p:outputLabel for="spbtn" value="SplitButton"/>
                        <p:splitButton id="spbtn" value="Save" type="button" icon="fa fa-save" styleClass="btn-primary">
                            <p:menuitem value="Update" url="#" icon="ui-icon-arrowrefresh-1-w"/>
                            <p:menuitem value="Delete" url="#" icon="ui-icon-close"/>
                            <p:separator/>
                            <p:menuitem value="Homepage" url="http://www.primefaces.org" icon="ui-icon-extlink"/>
                        </p:splitButton>

                        <p:outputLabel for="chkmenu" value="Checkbox Menu"/>
                        <p:selectCheckboxMenu id="chkmenu" label="Cities">
                            <f:selectItems value="#{checkboxMB.cities}"/>
                        </p:selectCheckboxMenu>

                        <p:outputLabel for="booleanBtn" value="Boolean Button"/>
                        <p:selectBooleanButton id="booleanBtn" onLabel="Yes" offLabel="No" onIcon="ui-icon-check"
                                               offIcon="ui-icon-close"/>

                        <p:outputLabel for="oneBtn" value="OneButton"/>
                        <p:selectOneButton id="oneBtn">
                            <f:selectItem itemLabel="XB1" itemValue="XB1"/>
                            <f:selectItem itemLabel="PS4" itemValue="PS4"/>
                        </p:selectOneButton>

                        <p:outputLabel for="manyBtn" value="Many Button"/>
                        <p:selectOneButton id="manyBtn">
                            <f:selectItem itemLabel="Spark" itemValue="Spark"/>
                            <f:selectItem itemLabel="Ronin" itemValue="Ronin"/>
                            <f:selectItem itemLabel="Rio" itemValue="Rio"/>
                        </p:selectOneButton>

                        <p:outputLabel for="colorPick" value="Color Picker"/>
                        <p:colorPicker id="colorPick" value="" />  

                        <p:outputLabel for="listbox" value="Listbox"/>
                        <p:selectOneListbox id="listbox">
                            <f:selectItem itemLabel="Sentinel" itemValue="Sentinel"/>
                            <f:selectItem itemLabel="Spark" itemValue="Spark"/>
                            <f:selectItem itemLabel="Ronin" itemValue="Ronin"/>
                            <f:selectItem itemLabel="Rio" itemValue="Rio"/>
                            <f:selectItem itemLabel="Primus" itemValue="Primus"/>
                        </p:selectOneListbox>


                        <p:spacer/>
                        <p:spacer/>
                    </p:panelGrid>


                </div>
















                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-12 ui-lg-12" style="background-color: #ecf0f5;">
                        <h4 class="sub_title">Basic Information</h4> 
                    </div>
                    <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                  <p:outputLabel for="input1" value="Input"/>       
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                        <p:inputText id="input1"/>            
                    </div>                     
                    <div class="ui-sm-12 ui-md-7 ui-lg-7">   
                    </div>

                    <div class="ui-sm-12 ui-md-2 ui-lg-2">
                        <p:outputLabel for="area1" value="Textarea"/>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:inputTextarea id="area1"/>
                    </div>
                    <div class="ui-sm-12 ui-md-7 ui-lg-7">
                        <div class="ui-g ui-fluid">
                            <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                <p:outputLabel value="Phone 1" />
                            </div>
                            <div class="ui-sm-12 ui-md-3 ui-lg-6" style="padding-left: 4%; padding-right: 4%;">
                                <p:inputText  maxlength="20"></p:inputText>
                            </div>
                            <div class="ui-sm-12 ui-md-3 ui-lg-1" >
                                <p:outputLabel value="   Ext."  /> 
                            </div>
                            <div class="ui-sm-12 ui-md-3 ui-lg-2">
                                <p:inputText style="width:60px;" maxlength="6"></p:inputText>
                            </div>
                            <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                <p:outputLabel value="Phone 2" />
                            </div>
                            <div class="ui-sm-12 ui-md-3 ui-lg-6" style="padding-left: 4%; padding-right: 4%;">
                                <p:inputText  maxlength="20"></p:inputText>
                            </div>
                            <div class="ui-sm-12 ui-md-1 ui-lg-1">
                                <p:outputLabel value="   Ext."   /> 
                            </div>
                            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                <p:inputText style="width:60px;"   maxlength="6">
                                </p:inputText>                                        
                            </div>
                        </div>
                    </div>


                    <div class="ui-sm-12 ui-md-2 ui-lg-2">
                        <p:outputLabel for="calendar1" value="Calendar"/> 
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                        <p:calendar id="calendar1" showOn="button"/>
                    </div>
                    <div class="ui-sm-12 ui-md-2 ui-lg-2">
                        <p:outputLabel for="ac1" value="Auto Complete"/>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                        <p:autoComplete id="ac1" completeMethod="#{autoCompleteMB.completeText}" dropdown="true"/>
                    </div>
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >    
                    </div>


                    <div class="ui-sm-12 ui-md-2 ui-lg-2">
                        <p:outputLabel for="mask1" value="Mask"/>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                        <p:inputMask id="mask1" mask="99/99/9999"/>
                    </div>
                    <div class="ui-sm-12 ui-md-2 ui-lg-2">
                        <p:outputLabel for="pwd1" value="Password"/>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                        <p:password id="pwd1"/>
                    </div>
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >    
                    </div>


                    <div class="ui-sm-12 ui-md-2 ui-lg-2">
                        <p:outputLabel for="keyboard1" value="Keyboard"/>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                        <p:keyboard id="keyboard1"/>
                    </div>
                    <div class="ui-sm-12 ui-md-2 ui-lg-2">
                        <p:outputLabel for="onemenu1" value="OneMenu"/>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3"  >
                        <p:selectOneMenu id="onemenu1">
                            <f:selectItem itemLabel="Select One" itemValue=""/>
                            <f:selectItem itemLabel="Xbox One" itemValue="Xbox One"/>
                            <f:selectItem itemLabel="PS4" itemValue="PS4"/>
                            <f:selectItem itemLabel="Wii U" itemValue="Wii U"/>
                        </p:selectOneMenu>
                    </div>
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >    
                    </div>


                    <div class="ui-sm-12 ui-md-2 ui-lg-2">
                        <p:outputLabel for="spinner1" value="Spinner"/>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                        <p:spinner id="spinner1"/>
                    </div>
                    <div class="ui-sm-12 ui-md-7 ui-lg-7">
                    </div>


                    <div class="ui-sm-12 ui-md-12 ui-lg-12" style="background-color: #ecf0f5; ">
                        <h4 class="sub_title1">Header 2</h4>
                    </div>


                    <div class="ui-sm-12 ui-md-6 ui-lg-6" style="padding-left: 1px;">
                        <div class="ui-g ui-fluid">
                            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                                <p:outputLabel for="slide1r" value="Slider"/>
                            </div>
                            <div class="ui-sm-12 ui-md-8 ui-lg-8">
                                <p:outputPanel>
                                    <p:inputText id="slide1r"/>
                                    <p:slider for="slide1r"/>
                                </p:outputPanel>
                            </div>


                            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                                <p:outputLabel for="chkmenu1" value="Checkbox Menu"/>
                            </div>
                            <div class="ui-sm-12 ui-md-8 ui-lg-8">
                                <p:selectCheckboxMenu id="chkmenu1" label="Cities">
                                    <f:selectItems value="#{checkboxMB.cities}"/>
                                </p:selectCheckboxMenu>
                            </div>


                            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                                <p:outputLabel for="booleanBtn1" value="Boolean Button"/>        
                            </div>
                            <div class="ui-sm-12 ui-md-8 ui-lg-8">
                                <p:selectBooleanButton id="booleanBtn1" onLabel="Yes" offLabel="No" onIcon="ui-icon-check" offIcon="ui-icon-close"/>
                            </div>


                            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                                <p:outputLabel for="btn1" value="Button"/>                                
                            </div>
                            <div class="ui-sm-12 ui-md-8 ui-lg-8">                               
                                <p:commandButton id="btn1" value="Edit" icon="fa fa-edit" type="button" styleClass="btn-primary"/>
                            </div>

                        </div>
                    </div>
                </div>

                <p:panel header="Addons" styleClass="card no-border">
                    <div class="ui-g ui-fluid">
                        <div class="ui-g-12 ui-md-4">
                            <div class="ui-inputgroup">
                                <span class="ui-inputgroup-addon"><em style="font-size: 20px" class="fa fa-user"></em></span>
                                <p:inputText placeholder="Username"/>
                            </div>
                        </div>

                        <div class="ui-g-12 ui-md-4">
                            <div class="ui-inputgroup">
                                <span class="ui-inputgroup-addon">$</span>
                                <p:inputText placeholder="Price"/>
                                <span class="ui-inputgroup-addon">.00</span>
                            </div>
                        </div>

                        <div class="ui-g-12 ui-md-4">
                            <div class="ui-inputgroup">
                                <span class="ui-inputgroup-addon">www</span>
                                <p:inputText placeholder="Website"/>
                            </div>
                        </div>
                    </div>
                </p:panel>

                <p:panel header="Button Addons" styleClass="card no-border">
                    <div class="ui-g ui-fluid">
                        <div class="ui-g-12 ui-md-4">
                            <div class="ui-inputgroup">
                                <p:commandButton styleClass="btn-primary" value="Search"/>
                                <p:inputText placeholder="Keyword"/>
                            </div>
                        </div>

                        <div class="ui-g-12 ui-md-4">
                            <div class="ui-inputgroup">
                                <p:inputText placeholder="Keyboard"/>
                                <p:commandButton icon="fa fa-search" styleClass="btn-info"/>
                            </div>
                        </div>

                        <div class="ui-g-12 ui-md-4">
                            <div class="ui-inputgroup">
                                <p:commandButton icon="fa fa-check" styleClass="btn-success"/>
                                <p:inputText placeholder="Vote"/>
                                <p:commandButton icon="fa fa-close" styleClass="btn-danger"/>
                            </div>
                        </div>
                    </div>
                </p:panel>


            </div>
        </h:form>
        <div class="ui-sm-12 ui-md-12 ui-lg-12" style="background-color: #ecf0f5; ">
            <h4 class="sub_title1">Lookups</h4>
        </div>
        <ui:include src="lookup/SalesTeamLookupDlg.xhtml"/>
        <ui:include src="lookup/UserLookupDlg.xhtml"/>
        <ui:include src="lookup/CompanyLookupDlg.xhtml"/>
        <ui:include src="lookup/ContactLookupDlg.xhtml"/>
        <ui:include src="lookup/PartNumDlg.xhtml"/>
        <ui:include src="lookup/TypesLookup.xhtml"/>
        <ui:include src="lookup/SalesTeamsLookup.xhtml"/>
        <ui:include src="lookup/CategoriesLookup.xhtml"/>
        <ui:include src="lookup/PotentialsLookup.xhtml"/>
        <ui:include src="lookup/ContactGroupsLookup.xhtml"/>
        <ui:include src="lookup/CompaniesLookup.xhtml"/>
        <ui:include src="lookup/StatesLookup.xhtml"/>
        <ui:include src="lookup/CompanyAliasLookupDlg.xhtml"/>
        <h:form id="sampleForm">

            <p:remoteCommand name="applyUser" actionListener="#{example.applyUser(userLookupService.user)}" update=":sampleForm:inputUserName" />
            <p:remoteCommand name="applySalesTeam" actionListener="#{example.applySalesTeam(salesTeamLookupService.salesTeam)}" update=":sampleForm:inputSalesTeam" />
            <p:remoteCommand name="applyAllSalesTeam" actionListener="#{example.applySalesTeam(salesTeamLookupService.salesTeam)}" update=":sampleForm:inputAllSalesTeam" />
            <p:remoteCommand name="applyActivePrinci" actionListener="#{example.applyCompany(viewCompLookupService.selectedCompany)}" update=":sampleForm:inputActivePrincName" />
            <p:remoteCommand name="applyComp" actionListener="#{example.applyCompany(viewCompLookupService.selectedCompany)}" update=":sampleForm:inputCompName" />
            <p:remoteCommand name="applyCRMCust" actionListener="#{example.applyCompany(viewCompLookupService.selectedCompany)}" update=":sampleForm:inputCustName" />
            <p:remoteCommand name="applyDBDCust" actionListener="#{example.applyCompany(viewCompLookupService.selectedCompany)}" update=":sampleForm:inputPrimCustName" />
            <p:remoteCommand name="applyPrinci" actionListener="#{example.applyCompany(viewCompLookupService.selectedCompany)}" update=":sampleForm:inputPrincName" />
            <p:remoteCommand name="applyMerge" actionListener="#{example.applyCompany(viewCompLookupService.selectedCompany)}" update=":sampleForm:inputMergeName" />
            <p:remoteCommand name="applyDistri" actionListener="#{example.applyCompany(viewCompLookupService.selectedCompany)}" update=":sampleForm:inputDistriName" />
            <p:remoteCommand name="applyDropDownPrinci" autoRun="true" actionListener="#{viewCompLookupService.listPrincipals(0, 'test')}" update=":sampleForm:oneMenuPrinci"  />

            <p:remoteCommand name="applyContComp" actionListener="#{example.applyContactCompany(viewCompLookupService.selectedCompany,viewCompLookupService.copyBusinessFields )}" update=":sampleForm:inputContCompName" />
            <p:remoteCommand name="applyUpdContComp" actionListener="#{example.applyContactCompany(viewCompLookupService.selectedCompany,viewCompLookupService.addressOnChange, viewCompLookupService.phoneOnChange, viewCompLookupService.emailOnChange)}" update=":sampleForm:inputUpdContCompName" />
            <p:remoteCommand name="applyCont" actionListener="#{example.applyContact(viewContLookupService.selectedContact)}" update=":sampleForm:inpTxtContName" />
            <p:remoteCommand name="applyProd" actionListener="#{example.applyProduct(viewProductLookUpService.selectedProduct)}" update=":sampleForm:inpTxtProdName" />
            <p:remoteCommand name="applyCustomer" actionListener="#{example.applyCompany(viewCompLookupService.selectedCompany)}" update=":sampleForm:inputCustomer" />

            <p:remoteCommand name="applyComps" actionListener="#{example.applyCompanies(lookupService.customers)}" update="@(.sel-comp)" />
            <p:remoteCommand name="applyPrincis" actionListener="#{example.applyPrincipals(lookupService.principals)}" update="@(.sel-princi)" />
            <p:remoteCommand name="applyCompanies" actionListener="#{example.applyCompanys(lookupService.companies)}" update="@(.sel-princi)" />
            <p:panelGrid id="grid" columns="2" layout="grid" 
                         styleClass="box-primary no-border ui-fluid"
                         columnClasses="ui-grid-col-2,ui-grid-col-10">

                <p:outputLabel for="inputUserName" value="User"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputUserName" value="#{example.user.userName}"  />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{userLookupService.list('applyUser', 'Sales Rep')}"
                                      update=":formUserLookup" oncomplete="PF('lookupUser').show();"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>

                <p:outputLabel for="inputAliasCompName" value="Alias Company"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputAliasCompName" value="#{example.comp.compName}"  />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{aliasCompLookupService.listAlias(2, 4, 'applyAliasComp')}"
                                      update=":formAliasCompLookup" oncomplete="PF('lookupAliasComp').show();"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>

                <p:outputLabel for="inputCustomer" value="Company (Include All)"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputCustomer" value="#{example.comp.compName}"  />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{viewCompLookupService.listActive('applyCustomer', 1, 0, 0)}"
                                      update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>

                <p:outputLabel for="inputSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputSalesTeam" value="#{example.salesTeam.smanName}"  />
                    <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_SALES_TEAM')}" immediate="true" 
                                      actionListener="#{salesTeamLookupService.list('applySalesTeam',0)}" 
                                      update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>
                <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')} (All)"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputAllSalesTeam" value="#{example.salesTeam.smanName}"  />
                    <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_SALES_TEAM')}" immediate="true" 
                                      actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}" 
                                      update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>

                <p:outputLabel for="inputCompName" value="Company"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputCompName" value="#{example.comp.compName}"  />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{viewCompLookupService.setShowNew(1)}"
                                      action="#{viewCompLookupService.listAll('applyComp')}"
                                      update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>


                <p:outputLabel for="inputCustName" value="Customer (Opploop)"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputCustName" value="#{example.comp.compName}" />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{viewCompLookupService.listCRM(2, 'applyCRMCust')}" oncomplete="PF('lookupComp').show()"
                                      update=":formCompLookup" 
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>

                <p:outputLabel for="inputPrimCustName" value="Customer (Dashboard)"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputPrimCustName" value="#{example.comp.compName}" />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{viewCompLookupService.listDBD(2,'applyDBDCust')}" 
                                      update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>

                <p:outputLabel for="inputPrincName" value="Principal (Reports)"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputPrincName" value="#{example.comp.compName}" />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{viewCompLookupService.listPrincipals(1, 'applyPrinci')}" 
                                      update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>

                <p:outputLabel for="inputActivePrincName" value="Principal (Active)"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputActivePrincName" value="#{example.comp.compName}" />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{viewCompLookupService.listPrincipals(0, 'applyActivePrinci')}" 
                                      update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>

                <p:outputLabel for="inputDistriName" value="Distributor"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputDistriName" value="#{example.comp.compName}" />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{viewCompLookupService.listCRM(3, 'applyDistri')}" 
                                      update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                      styleClass="btn-info  btn-xs" />
                </h:panelGroup>

                <p:outputLabel for="inputMergeName" value="Merge Company"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputMergeName" value="#{example.comp.compName}" />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{viewCompLookupService.listBySalesTeam(2, 3, 'applyMerge')}" 
                                      update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>

                <p:outputLabel for="oneMenuPrinci" value="Principal (Active)"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:selectOneMenu  id="oneMenuPrinci" value="#{example.comp.compName}"   >
                        <f:selectItem itemLabel="Default" itemValue="" />
                        <f:selectItems var="princi" value="#{viewCompLookupService.companies}" itemLabel="#{princi.compName}" itemValue="#{princi.compId}" />
                    </p:selectOneMenu>
                </h:panelGroup>

                <p:outputLabel for="inputContCompName" value="Company(For Contact)"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputContCompName" value="#{example.comp.compName}"  />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{viewCompLookupService.listAll('applyContComp',0)}" 
                                      update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>

                <p:outputLabel for="inputUpdContCompName" value="Company(For Existing Contact)"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inputUpdContCompName" value="#{example.comp.compName}"  />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{viewCompLookupService.listAll('applyUpdContComp',1)}" 
                                      update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>

                <p:outputLabel for="inpTxtContName" value="Contact"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inpTxtContName" value="#{example.cont.contFullName}"  />
                    <p:commandButton  icon="fa fa-search" title="Choose Contact" immediate="true" 
                                      actionListener="#{viewContLookupService.listAll('applyCont')}" 
                                      update=":formContLookup" oncomplete="PF('lookupCont').show()"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>
                <p:outputLabel for="inpTxtProdName" value="Part Number"  />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText id="inpTxtProdName" value="#{example.product.prodPrinciPartno}"  />
                    <p:commandButton  icon="fa fa-search" title="Choose Part Number" immediate="true" 
                                      actionListener="#{viewProductLookUpService.list('applyProd',0)}" 
                                      update=":dtPartNumForm" oncomplete="PF('dlgPartNum').show()"  
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>


                <p:outputLabel value="#{custom.labels.get('IDS_COMP_TYPE')}" />
                <h:panelGroup class="ui-inputgroup"  >
                    <h:selectOneListbox size="2" style="width: 100%"  styleClass="sel-type" id="selType"  >
                        <f:selectItems value="#{lookupService.companyTypes}" var="ctype"  
                                       itemValue="#{ctype.compTypeId}"
                                       itemLabel="#{ctype.compTypeName}"  />
                    </h:selectOneListbox>
                    <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_COMP_TYPE')}" actionListener="#{lookupService.list('COMP_TYPE')}" update=":formTypeLookup" oncomplete="PF('lookupType').show();" />
                    <p:commandLink styleClass="sel-type" value="Clear" actionListener="#{lookupService.clear('COMP_TYPE')}" update="@(.sel-type)" disabled="#{!(lookupService.companyTypes!=null and lookupService.companyTypes.size() != 0)}" />
                </h:panelGroup>

                <p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')}" />
                <h:panelGroup class="ui-inputgroup"  >
                    <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width: 100%" id="selSalesTeam">
                        <f:selectItems value="#{lookupService.salesTeams}" var="team"  
                                       itemValue="#{team.smanId}"
                                       itemLabel="#{team.smanName}"  />
                    </h:selectOneListbox>
                    <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.list('SALES_TEAM')}" update=":frmSalesTeamLookup" oncomplete="PF('dlgSalesTeamsLookup').show();" />
                    <p:commandLink styleClass="sel-salesteam" value="Clear" actionListener="#{lookupService.clear('SALES_TEAM')}" update="@(.sel-salesteam)" disabled="#{!(lookupService.salesTeams!=null and lookupService.salesTeams.size() != 0)}" />
                </h:panelGroup>

                <p:outputLabel value="#{custom.labels.get('IDS_CATEGORY')}" />
                <h:panelGroup class="ui-inputgroup"  >
                    <h:selectOneListbox  styleClass="sel-catg"  size="2" style="width: 100%" id="selCatg"     >
                        <f:selectItems value="#{lookupService.companyCategories}" var="catg"  
                                       itemValue="#{catg.compCatgId}"
                                       itemLabel="#{catg.compCatgName}"  />
                    </h:selectOneListbox>
                    <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_CATEGORY')}" actionListener="#{lookupService.list('CATEGORY')}" update=":frmCategoryLookup" oncomplete="PF('dlgCategoryLookup').show();"  />
                    <p:commandLink styleClass="sel-catg" value="Clear" actionListener="#{lookupService.clear('CATEGORY')}" update="@(.sel-catg)" disabled="#{!(lookupService.companyCategories!=null and lookupService.companyCategories.size() != 0)}" />
                </h:panelGroup>

                <p:outputLabel value="#{custom.labels.get('IDS_POTENTIALS')}" />
                <h:panelGroup class="ui-inputgroup"  >
                    <h:selectOneListbox  styleClass="sel-potential"  size="2" style="width: 100%" id="selPotential"     >
                        <f:selectItems value="#{lookupService.potentials}" var="potential"  
                                       itemValue="#{potential.prodPoteName}"
                                       itemLabel="#{potential.prodPoteName}"  />

                    </h:selectOneListbox>
                    <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_POTENTIALS')}" actionListener="#{lookupService.list('POTENTIAL')}" update=":frmPotentialLookup" oncomplete="PF('dlgPotentialLookup').show();"  />
                    <p:commandLink styleClass="sel-potential" value="Clear" actionListener="#{lookupService.clear('POTENTIAL')}" update="@(.sel-potential)" disabled="#{!(lookupService.potentials!=null and lookupService.potentials.size() != 0)}" />
                </h:panelGroup>

                <p:outputLabel value="#{custom.labels.get('IDS_CON_GRP')}:" />
                <h:panelGroup class="ui-inputgroup"  >
                    <h:selectOneListbox  styleClass="sel-group"  size="2" style="width: 100%" id="selGroup"     >
                        <f:selectItems value="#{lookupService.contactGroups}" var="grp"  
                                       itemValue="#{grp.contGroupId}"
                                       itemLabel="#{grp.contGroupName}"  />
                    </h:selectOneListbox>
                    <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_CON_GRP')}" actionListener="#{lookupService.list('CONT_GRP')}" update=":frmContGrpLookup" oncomplete="PF('dlgContGrpLookup').show();"  />
                    <p:commandLink styleClass="sel-group" value="Clear" actionListener="#{lookupService.clear('CONT_GRP')}" update="@(.sel-group)" disabled="#{!(lookupService.contactGroups!=null and lookupService.contactGroups.size() != 0)}" />
                </h:panelGroup>

                <p:outputLabel value="States" />
                <h:panelGroup class="ui-inputgroup"  >
                    <h:selectOneListbox  styleClass="sel-state"  size="2" style="width: 100%" id="selState"     >
                        <f:selectItems value="#{lookupService.states}" var="state"  
                                       itemValue="#{state.stateId}"
                                       itemLabel="#{state.stateName}"  />
                    </h:selectOneListbox>
                    <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select State" actionListener="#{lookupService.list('STATE')}" update=":frmStateLookup" oncomplete="PF('dlgStateLookup').show();"  />
                    <p:commandLink styleClass="sel-state" value="Clear" actionListener="#{lookupService.clear('STATE')}" update="@(.sel-state)" disabled="#{!(lookupService.states!=null and lookupService.states.size() != 0)}" />
                </h:panelGroup>

                <p:outputLabel value="Customers" />
                <h:panelGroup class="ui-inputgroup"  >
                    <h:selectOneListbox  size="2" style="width: 100%!important;" styleClass="sel-comp" id="selComp"   >
                        <f:selectItems value="#{example.customers}" var="comp"  
                                       itemValue="#{comp.compId}"
                                       itemLabel="#{comp.compName}"  />

                    </h:selectOneListbox>
                    <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select Company"
                                     actionListener="#{viewCompLookupService.listCRM(2, 'applyComps')}" oncomplete="PF('lookupComps').show()"
                                     update=":formCompsLookup" />
                    <p:commandLink styleClass="sel-comp" value="Clear" actionListener="#{example.clear()}" update="@(.sel-comp)"  disabled="#{!(example.customers!=null and example.customers.size() != 0)}" />
                </h:panelGroup>

                <p:outputLabel value="Principals" />
                <h:panelGroup class="ui-inputgroup"  >
                    <h:selectOneListbox  size="2" style="width: 100%!important;" styleClass="sel-princi" id="selprinci"   >
                        <f:selectItems value="#{example.principals}" var="comp"  
                                       itemValue="#{comp.compId}"
                                       itemLabel="#{comp.compName}"  />

                    </h:selectOneListbox>
                    <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select Company"
                                     actionListener="#{viewCompLookupService.listCRM(1, 'applyPrincis')}" oncomplete="PF('lookupComps').show()"
                                     update=":formCompsLookup" />
                    <p:commandLink styleClass="sel-princi" value="Clear" actionListener="#{example.clearPrincipals()}" update="@(.sel-princi)"  disabled="#{!(example.principals!=null and example.principals.size() != 0)}" />
                </h:panelGroup>

                <p:outputLabel value="Companies" />
                <h:panelGroup class="ui-inputgroup"  >
                    <h:selectOneListbox  size="2" style="width: 100%!important;" styleClass="sel-princi" id="selcomp"   >
                        <f:selectItems value="#{example.companies}" var="comp"  
                                       itemValue="#{comp.compId}"
                                       itemLabel="#{comp.compName}"  />

                    </h:selectOneListbox>
                    <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select Company"
                                     actionListener="#{viewCompLookupService.listAll('applyCompanies')}" oncomplete="PF('lookupComps').show()"
                                     update=":formCompsLookup" />
                    <p:commandLink styleClass="sel-princi" value="Clear" actionListener="#{example.clearPrincipals()}" update="@(.sel-princi)"  disabled="#{!(example.principals!=null and example.principals.size() != 0)}" />
                </h:panelGroup>

                <!--                <p:outputLabel for="inpTxtContName" value="Contact"  />
                                <h:panelGroup class="ui-inputgroup"  >
                                    <p:inputText id="inpTxtContName" value="#{example.cont.contFullName}"  />
                                    <p:commandButton  icon="fa fa-search" title="Choose Contact" immediate="true" 
                                                      actionListener="#{viewContLookupService.list('applyCont', 0,49,0)}" 
                                                      update=":formContLookup" oncomplete="PF('lookupCont').show()"  
                                                      styleClass="btn-info btn-xs" />
                                </h:panelGroup>-->

<!--                 <p:commandButton actionListener="#{viewContLookupService.list('applyCont', 3, 0, 0)}" id="submitBtn" value="Test Contact Lookup"  
                                  styleClass="btn btn-success  btn-xs" update=":formContLookup" oncomplete="PF('lookupCont').show()" />   -->
            </p:panelGrid>

        </h:form>
        <div class="ui-sm-12 ui-md-12 ui-lg-12" style="background-color: #ecf0f5; ">
            <h4 class="sub_title1">Example</h4>
        </div>
        <h:form>
            <p:commandButton actionListener="#" id="btnSave" value="Save" update="grid" styleClass="btn btn-success  btn-xs" />                 
            <p:panelGrid id="grid" columns="2" layout="grid" 
                         styleClass="box-primary no-border ui-fluid"
                         columnClasses="ui-grid-col-2,ui-grid-col-10">
                <p:outputLabel for="inputLabel" value="Label" styleClass="required"  />
                <p:inputText id="inputLabel" value="#{example.compName}"  />

                <h:outputLabel for="multiple" value="Multiple:" />
                <p:selectCheckboxMenu id="multiple" value="#{example.contGroups}" label="Cities" multiple="true"
                                      filter="true" filterMatchMode="startsWith" panelStyle="width:250px">
                    <f:selectItems value="#{contactGroupMstService.contactGroupLst}" var="contGrp" itemLabel="#{contGrp.contGroupName}" itemValue="#{contGrp.contGroupName}" />
                </p:selectCheckboxMenu>
            </p:panelGrid>
            <!--<p:messages  ><p:autoUpdate /></p:messages>-->
        </h:form>
        <h:form>
            <p:remoteCommand immediate="true"  name="applyCompany" actionListener="#{example.applyCompany(viewCompLookupService.selectedCompany)}" update=":formExample:tabExample:inputCompany"  />
        </h:form>
        <h:form id="formExample">
            <p:growl id="msg"  />
            <p:commandButton actionListener="#" id="submitButton" value="Save"  styleClass="btn btn-success  btn-xs" update=":formExample:msg"/>   
            <p:tabView id="tabExample">
                <p:tab title="Tab 1">
                    <p:panelGrid id="gridExample" columns="2" layout="grid" 
                                 styleClass="box-primary no-border ui-fluid"
                                 columnClasses="ui-grid-col-2,ui-grid-col-10">
                        <p:outputLabel for="inputCompany" value="Company" styleClass="required"  />
                        <h:panelGroup class="ui-inputgroup"  >
                            <p:inputText id="inputCompany"  value="#{example.comp.compName}" required="true"  requiredMessage="Company is required"   />
                            <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                              actionListener="#{viewCompLookupService.listAll('applyCompany')}" 
                                              update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                              styleClass="btn-info btn-xs" />
                        </h:panelGroup>
                    </p:panelGrid>
                </p:tab>
                <p:tab title="Tab 2">

                </p:tab>
            </p:tabView>
        </h:form>
        <h:form>
            <p:commandButton id="btnPitch" value="Pitch" styleClass="btn btn-primary btn-xs" oncomplete="PF('widgetPitch').show()" />
        </h:form>
        <p:dialog id="dlgPitch" modal="true"  responsive="true" widgetVar="widgetPitch" >
            <f:facet name="header" >
                <p:outputLabel id="lblPitch" value="#{compProdPitch.recId ==0 ?'Add': 'Edit'} #{companyServiceImpl.comp.tabName}" />
            </f:facet>
            <h:form id="frmPitch">
                <!--Name-->
                <h:outputLabel value="Name" styleClass="required" />
                <p:spacer width="10px" />
                <p:inputText maxlength="60"  value="#{compProdPitch.compPitchName}" styleClass="required" style="width:40%"
                             required="true" requiredMessage="Please enter a #{companyServiceImpl.comp.pitch} name"/>
                <p:separator />

                <!--Text Editor-->
                <p:textEditor widgetVar="editor1" value="#{compProdPitch.compPitchName}" height="300" style="margin-bottom:10px"/>
            </h:form>

        </p:dialog>

        <!--        <h:form id="grpForm">
                    <p:commandButton actionListener="#{example.testAppParamStatus()}" value="Test params"  styleClass="btn btn-success  btn-xs" />   
                    <p:remoteCommand autoRun="true" actionListener="#{example.loadGroups()}" update=":grpForm:inputGroup" />
                    <p:commandButton actionListener="#{example.saveGroups(example.contact)}" id="submitButton" value="Save"  styleClass="btn btn-success  btn-xs"/>   
                    <p:panelGrid id="gridGroups" columns="2" layout="grid" 
                                 styleClass="box-primary no-border ui-fluid"
                                 columnClasses="ui-grid-col-2,ui-grid-col-10">
                        <p:outputLabel for="inputGroup" value="Contact Groups"  />
        
                        <p:selectCheckboxMenu id="inputGroup" value="#{example.contact.selectContGrp}" label="Groups" multiple="true"
                                              filter="true" filterMatchMode="startsWith" panelStyle="width:250px">
                            <f:selectItems value="#{contactGroupMstService.contactGroupLst}" var="grp" itemLabel="#{grp.contGroupName}" itemValue="#{grp.contGroupId}" />
                        </p:selectCheckboxMenu>   
                    </p:panelGrid>
        
        
                </h:form>-->


        <!--        <h:form>
            <h:panelGrid id="grid" columns="3" cellpadding="5">
                <p:outputLabel for="default" value="Default:" />
                <p:inputText id="default" required="true" />
                <p:message for="default" />
         
                <p:outputLabel for="txt" value="Text:" />
                <p:inputText id="txt" required="true" />
                <p:message for="txt" display="text" />
         
                <p:outputLabel for="icon" value="Icon:" />
                <p:inputText id="icon" required="true" />
                <p:message for="icon" display="icon" />
         
                <p:outputLabel for="tt" value="Tooltip:" />
                <p:inputText id="tt" required="true" />
                <p:message for="tt" display="tooltip" />
            </h:panelGrid>
         
            <p:commandButton id="submitButton" value="Submit" update="grid" />
        </h:form>-->





        <style>
            .content-header{
                height: 51px!important;
            } 
            .content {
                padding: 0!important;
                padding-left: 15px!important;
                padding-right: 15px!important;
                margin-top: -9px!important;
            }
            label {
                font-weight: normal!important;
            }
            .required:after{content:" * ";color: red;}

        </style>

    </ui:define>  

</ui:composition>
