<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<ui:composition  xmlns="http://www.w3.org/1999/xhtml" 
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:f="http://java.sun.com/jsf/core" 
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                 template="#{layoutMB.template}">
    <!--#363 :page title-->
            <ui:define name="head">
<link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
<title>  #{custom.labels.get('IDS_OPP')} Report</title>
</ui:define>

    <ui:define name="title"> #{custom.labels.get('IDS_OPP')} Reporting
        <ui:include src="/help/Help.xhtml"/>
    </ui:define>
    <ui:define name="metadata">
        <f:metadata> 
            <!--#2521:   Related - CRM-1521: Tutorial button landing urls - Sales Reports, Funnel Report-->

            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('OPP_RPT'))}" />
            <!--29-09-2023  12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('OPP_RPT')}" />
        </f:metadata>
    </ui:define>
    <ui:define name="body">

        <f:event listener="#{reportingService.isPageAccessible}" type="preRenderView"/>  


        <div class="box box-info box-body">             

            <div class="left-column"  style="width:25%">
                <ui:include src="PrincipalList.xhtml" />
            </div>
            <div class="divider"/>

            <div class="right-column"  style="width:75%" >
                <ui:include src="reporting.xhtml" />

            </div>

        </div>
    </ui:define>
</ui:composition>

