<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui">


    <h:form id="frmPrincipalList">

        <!--Principal List-->
        <p:remoteCommand name="applyPrinci" autoRun="true" actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}" update=":frmPrincipalList:princilist" />      
        <p:dataTable value="#{viewCompLookupService.companies}" 
                     selection="#{viewCompLookupService.selectedCompany}"  rowKey="#{comp.compId}" selectionMode="single"
                     emptyMessage=" " paginator="true" rows="30" paginatorAlwaysVisible="false"
                     var="comp" id="princilist">

            <p:ajax event="rowSelect"  listener="#{oppRptMap.populateOppRptList(viewCompLookupService.selectedCompany.compId)}" update=":mappingForm" />
            <p:column  filterMatchMode="contains" filterBy="#{comp.compName}" headerText="Name" sortBy="#{comp.compName}"    >
              #{comp.compName}
                <!--#{comp.compName eq 'All' ?('All  '.concat(custom.labels.get('IDS_PRINCI'))) :comp.compName}-->
        
            </p:column>
        </p:dataTable>


    </h:form>
</ui:composition>

