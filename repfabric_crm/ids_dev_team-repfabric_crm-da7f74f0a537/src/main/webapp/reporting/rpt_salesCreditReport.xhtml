<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                xmlns:pe="http://primefaces.org/ui/extensions"
                template="#{layoutMB.template}">

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
         <!--#13168 CRM-7652  Sales Credit Report needs to show ALL orders, not just those with Direct Commissions - add-->
        <title>  Orders Credit Report </title>
    </ui:define>
    <ui:define name="meta">

        <f:metadata>

            <f:viewParam name="c" value="#{reportFilters.customer.compId}" />
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}" />
            <f:viewParam name="g" value="#{mainSalesCreditRep.groupBy}" />
            <f:viewParam name="f" value="#{globalParams.dateFormat}"/>
            <f:viewParam name="fd" value="#{mainSalesCreditRep.fromDate}"/>
            <f:viewParam name="td" value="#{mainSalesCreditRep.toDate}"/>

            <f:viewAction action="#{mainSalesCreditRep.run()}"/>
        </f:metadata>
    </ui:define>

    <ui:define name="title">

    </ui:define>

    <ui:define name="body">
        <div class="box box-info box-body" style="vertical-align: top;" id="salesCreditedPage"  >
            <h:form id="salesCreditedForm" >
                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="salesCreditedForm:inputPrincName"   />

                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="salesCreditedForm:inputCustomer" />       

 <!--//  #13295  #13295 ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO-->
               <p:remoteCommand  name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" />

            <p:remoteCommand  name="applySecondCust" actionListener="#{reportFilters.applySecondCust(viewCompLookupService.selectedCompany)}"/>
                <div id="loading"  class="div-center" style="display:none;">

                    <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                    left: 600px;
                                    top: 300px;
                                    width: 150px;
                                    height: 150px;
                                    z-index: 9999; opacity: .3 !important;" />
                </div>  

                <div class="ui-g ui-fluid">
                      <!--/*#13295   ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO*/-->

                    <div class="ui-sm-12 ui-md-2 ui-lg-3" >
                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="#{loginBean.subscriber_name}" />

                            <br/>
 <!--#13168 CRM-7652  Sales Credit Report needs to show ALL orders, not just those with Direct Commissions - add-->
                            <h:outputLabel value="Orders Credit Report" style="width:600px" />


                        </div>
                        <div class="compNamebg" style="display: inline">
                            <p:outputLabel value="As of :" />  <p:outputLabel style="margin-left: 10px" value="#{mainSalesCreditRep.asOfDate}" id="fdate"/>   
                        </div>
                    </div>
                      <!--/*#13295   ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO*/-->
                    <div class="ui-sm-12 ui-md-3 ui-lg-8" >
                        <p:panelGrid  id="grid2" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                      style="float: left;width:33%;margin-right: 35px"       layout="grid" styleClass="box-primary no-border ui-fluid "  >


                            <p:outputLabel  value="From Date"/>
                            <h:panelGroup>

                                <p:calendar value="#{reportFilters.fdate}" showOn="button" pattern="#{globalParams.dateFormat}" id="fmdt" converterMessage="Please enter the valid From date">

                                    <p:ajax  event="keyup"  process="@this" />
                                    <p:ajax event="dateSelect" process="@this"  />
                                </p:calendar>
                            </h:panelGroup>



                            <p:outputLabel  value="To Date"/>
                            <h:panelGroup >

                                <p:calendar value="#{reportFilters.tdate}" showOn="button" pattern="#{globalParams.dateFormat}" id="todt" widgetVar="todate" converterMessage="Please enter the valid To date">
                                    <p:ajax  event="keyup"  process="@this" />
                                    <p:ajax event="dateSelect" process="@this"  />
                                </p:calendar>
                            </h:panelGroup>

 <!--//  #13295  #13295 ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO-->
                            <p:commandButton process="@this" actionListener="#{mainSalesCreditRep.run()}" update="salesCreditedForm:tbldata2  salesCreditedForm:tbldata3" onclick="viewReport(); processGif()"  value="View Report"   id="viewreport"  styleClass="btn btn-primary btn-xs"   style="width:30px;margin-top:10px" />


 <!--//  #13295  #13295 ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO-->
                            <p:commandButton value="Export"   style="width:30px;margin-top:10px"  
                                             ajax="false"  actionListener="#{mainSalesCreditRep.exportData()}"  
                                             styleClass="btn btn-primary btn-xs" onclick="PrimeFaces.monitorDownload(start, stop);" 
                                             />       
                                    


                       </p:panelGrid>
                        
                        
                       
                  
                        
 <!--//  #13295  #13295 ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO-->
                        <p:panelGrid  id="grid6" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                      style="float: left;width:50%;"       layout="grid" styleClass="box-primary no-border ui-fluid "  >


                            <p:outputLabel for="inputPrincName" style="margin-left: 70px" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  update=":formCompLookup" actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>



                            <p:outputLabel for="inputCustomer" style="margin-left: 70px" value="#{custom.labels.get('IDS_CUSTOMER')}"/>
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  update=":formCompLookup" actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            
                             <p:outputLabel style="margin-left: 70px" value="Sales Rep"  />
                              <!--#13092   ESCALATIONS CRM-7761   Sales Credit Report: Gave user who is a "Manager" report visibility to all us-->
                                    <!--#12840 ESCALATIONS CRM-7652   Sales Credit Report needs to show ALL orders, not just those with Direct Comm-->
                            <h:panelGroup   class="ui-inputgroup" style="width:100%" >
                                <h:selectOneListbox   styleClass="sel-users"  size="2" style="width:-webkit-fill-available" id="selUsers">
                                    <f:selectItems value="#{reportFilters.users}" var="users"  
                                                   itemValue="#{users.userId}"
                                                   itemLabel="#{users.userName}"  />
                                </h:selectOneListbox>
                                  <!--#13092   ESCALATIONS CRM-7761   Sales Credit Report: Gave user who is a "Manager" report visibility to all us-->
                                       <!--#12840 ESCALATIONS CRM-7652   Sales Credit Report needs to show ALL orders, not just those with Direct Comm-->
                                       <p:commandButton  icon="fa fa-search" process="@this"  styleClass="btn-info btn-xs"  title="Choose Sales Rep" action="#{lookupService.listSalesRep()}" update=":frmUsersLookup"  oncomplete="PF('dlgUsersLookup').show();" />
                                <br/>

                            </h:panelGroup>
                            
                               <!--/*#13295   ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO*/-->
                     

                        </p:panelGrid>
                        
                        
 <!--//  #13295  #13295 ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO-->
                        <p:panelGrid style="width:10%;height:5px;float: right"  id="grid4" columns="1" 
                                      layout="grid" >
                            <p:outputLabel/>
                            <p:outputLabel/>
                            <p:outputLabel/>
                            <!--#13092   ESCALATIONS CRM-7761   Sales Credit Report: Gave user who is a "Manager" report visibility to all us-->
                                    <!--#12840 ESCALATIONS CRM-7652   Sales Credit Report needs to show ALL orders, not just those with Direct Comm-->
                            <p:commandLink styleClass="sel-users" value="Clear" actionListener="#{lookupService.clear('USER')}" update="@(.sel-users)" disabled="#{!(reportFilters.users!=null and reportFilters.users.size() != 0) }" style="text-decoration:underline;margin-left: -30px"/>             
                            <!--<p:outputLabel/>-->
                            <!--<p:outputLabel/>-->
                        </p:panelGrid>

                    </div>
                   
                   
                    <!--                    <div style="display: flex">
                                            
                                        <div class="ui-sm-12 ui-md-3 ui-lg-3" style="width:800px">
                                            
                                        </div>
                                            
                                        <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                            <p:panelGrid id="grid" columns="3" 
                                                         style="float: right;width:100%;" columnClasses="ui-grid-col-9,ui-grid-col-6,ui-grid-col-6"    layout="grid" styleClass="box-primary no-border ui-fluid "  >
                    
                                               
                    
                                            </p:panelGrid>
                    
                                      </div>
                                      </div>-->


                </div>
                <h:panelGroup rendered="#{!mainSalesCreditRep.groupBy}"  id="tbldata2">
                   <!--/*#13295   ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO*/-->
                    <p:dataTable style="margin-bottom:0" scrollHeight="43vh" scrollable="true"    lazy="true"  value="#{mainSalesCreditRep.salesCredit}" id="saleCredit1"  paginator="true"
                                 rowIndexVar="index"   rowsPerPageTemplate="50,100,150"  var="salCrt"  paginatorPosition="top"  paginatorAlwaysVisible="true"  paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 emptyMessage="No data found" rows="50" >

                        <p:column  style="width:calc(200px)"  styleClass="ellipsis-column"  sortBy="#{salCrt.repPrinciName}"   headerText="#{custom.labels.get('IDS_PRINCI')}">
                            <p:tooltip   position="right" for="salesCreditedForm:saleCredit1:#{index}:repPrinciName" value="#{salCrt.repPrinciName}"/>

                            <h:commandLink id="repPrinciName" target="_blank"  action="#{customerProductMst.goToCompanyDetailsPage(salCrt.repPrinciId)}" value="#{salCrt.repPrinciName}" style="text-decoration: underline"/>

                            <f:facet name="footer" >
                                <h:outputLabel > </h:outputLabel>
                            </f:facet>

                        </p:column>
                        <p:column style="width:calc(200px)" styleClass="ellipsis-column" sortBy="#{salCrt.repDocNumber}" headerText="PO Number">
                            <p:tooltip  position="right" for="salesCreditedForm:saleCredit1:#{index}:repDocNumber" value="#{salCrt.repDocNumber}"/>
                            
 <!--//  #13295  #13295 ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO-->
                            <h:commandLink id="repDocNumber" target="_blank"  action="#{mainSalesCreditRep.goToPoDetailsPage(salCrt.repDocId)}" value="#{salCrt.repDocNumber}" style="text-decoration: underline"/>
                            <f:facet name="footer" >
                                <h:outputLabel > </h:outputLabel>
                            </f:facet>

                        </p:column>
                        <p:column style="width:calc(100px)" sortBy="#{salCrt.repDocDate}" headerText="PO Date">
                            <p:outputLabel value="#{salCrt.repDocDate}">
                                <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                            </p:outputLabel>

                            <f:facet name="footer" >
                                <h:outputLabel > </h:outputLabel>
                            </f:facet>

                        </p:column>
                        <p:column style="width:calc(200px)" sortBy="#{salCrt.repPrimCustName}" headerText="#{custom.labels.get('IDS_CUSTOMER')}">
                            <p:tooltip  position="right" for="salesCreditedForm:saleCredit1:#{index}:repCustName" value="#{salCrt.repPrimCustName}"/>
                            <h:commandLink id="repCustName" target="_blank"  action="#{customerProductMst.goToCompanyDetailsPage(salCrt.repPrimCustId)}" value="#{salCrt.repPrimCustName}" style="text-decoration: underline"/>

                            <f:facet name="footer" >
                                <h:outputLabel > </h:outputLabel>
                            </f:facet>
                        </p:column>
                        
                      
                        <p:column style="width:calc(150px)" sortBy="#{salCrt.repPrimSmanName}"  headerText="#{custom.labels.get('IDS_SALES_TEAM')}">
                            <p:outputLabel  value="#{salCrt.repPrimSmanName}" ></p:outputLabel>

                            <f:facet name="footer" >
                                <h:outputLabel > </h:outputLabel>

                            </f:facet>

                        </p:column>
                       <!--#13125 CRM-7652  Sales Credit Report needs to show ALL orders, not just those with Direct Commissions - add-->
                        
                          <p:column style="width:calc(200px)" sortBy="#{salCrt.repSecCustName}" headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}">
                            <p:tooltip  position="right" for="salesCreditedForm:saleCredit1:#{index}:repSecCustName" value="#{salCrt.repSecCustName}"/>
                            <h:commandLink id="repSecCustName" target="_blank"  action="#{customerProductMst.goToCompanyDetailsPage(salCrt.repSecCustId)}" value="#{salCrt.repSecCustName}" style="text-decoration: underline"/>

                            <f:facet name="footer" >
                                <h:outputLabel > </h:outputLabel>
                            </f:facet>
                        </p:column>
                         <!--#13125 CRM-7652  Sales Credit Report needs to show ALL orders, not just those with Direct Commissions - add-->
                              <p:column style="width:calc(200px)" sortBy="#{salCrt.repDistriName}" headerText="#{custom.labels.get('IDS_DISTRI')}">
                            <p:tooltip  position="right" for="salesCreditedForm:saleCredit1:#{index}:repDistriName" value="#{salCrt.repDistriName}"/>
                            <h:commandLink id="repDistriName" target="_blank"  action="#{customerProductMst.goToCompanyDetailsPage(salCrt.repDistriId)}" value="#{salCrt.repDistriName}" style="text-decoration: underline"/>

                            <f:facet name="footer" >
                                  <!--/*#13295   ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO*/-->
                                <h:outputLabel value="Order Amount Total:" style="float: left"  class="footerFont" >

                                </h:outputLabel>
                            </f:facet>
                        </p:column>
                        
                        <p:column style="width:calc(150px)" sortBy="#{salCrt.repTotalPrice}" headerText="Order Amount">
                            <p:outputLabel style="float: right" value="#{salCrt.repTotalPrice}">
                                <f:convertNumber pattern="#,##0.00"/>
                            </p:outputLabel>

                            <f:facet name="footer" >
                                <h:outputLabel value="#{mainSalesCreditRep.ordTotalAmt}"  style="float: right"   class="footerFont" >

                                    <f:convertNumber   maxFractionDigits="0"  minFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>
                        <p:column style="width:calc(150px)"  styleClass="ellipsis-column"  sortBy="#{salCrt.repSalesRepName}" headerText="Sales Rep">
                            <p:tooltip   position="right" for="salesCreditedForm:saleCredit1:#{index}:repSalesRepName" value="#{salCrt.repSalesRepName}"/>
                            <p:outputLabel id="repSalesRepName"  value="#{salCrt.repSalesRepName}" ></p:outputLabel>

                            <f:facet name="footer" >
                                <h:outputLabel > </h:outputLabel>
                            </f:facet>
                        </p:column>

                        <p:column style="width:calc(80px)" sortBy="#{salCrt.repSalesRepPct}" headerText="Split %">
                            <p:outputLabel  style="float: right" value="#{salCrt.repSalesRepPct}" ></p:outputLabel>
                            <f:facet name="footer" >
                                <h:outputLabel > </h:outputLabel>
                            </f:facet>
                        </p:column>
                        <p:column style="width:calc(150px)" sortBy="#{salCrt.repSalesRepAmt}" headerText="Split Amount">
                            <p:outputLabel style="float: right"  value="#{salCrt.repSalesRepAmt}">
                                <f:convertNumber pattern="#,##0.00"/>
                            </p:outputLabel>

                            <f:facet name="footer" >
                                <h:outputLabel value="#{mainSalesCreditRep.splitTotalAmt}"  style="float: right"   class="footerFont" >

                                    <f:convertNumber maxFractionDigits="0"  minFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>

                    </p:dataTable>


                </h:panelGroup>

                <h:panelGroup rendered="#{mainSalesCreditRep.groupBy}" id="tbldata3">

                    <p:dataScroller id="datascroll" rowIndexVar="#{scrollIndex}"  value="#{mainSalesCreditRep.saleCredGrpSalesRep}"  var="salescred"   chunkSize="5"   style="margin-bottom:0">
                        <p:column >
                            <div class="div-selected">

                                <a style="color:#323232!important;"  class="header-label">
                                    <h:outputLabel style="font-weight: bold" value="#{salescred.itemName}"/>           
                                </a>

                            </div>

                            <p:dataTable draggableColumns="true"   id="saleCredit2"  value="#{salescred.salesCredit}"    var="salCr"  
                                         paginatorPosition="bottom" rowIndexVar="#{index1}"      lazy="true" rowsPerPageTemplate="50,100,150" rows="50" paginatorAlwaysVisible="true" paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}" paginator="true">

                                <p:column styleClass="ellipsis-column" style="width:calc(200px)"  sortBy="#{salCr.repPrinciName}"   headerText="#{custom.labels.get('IDS_PRINCI')}">


                                    <h:commandLink target="_blank" title="#{salCr.repPrinciName}" id="repPrinNam" action="#{customerProductMst.goToCompanyDetailsPage(salCr.repPrinciId)}" value="#{salCr.repPrinciName}" style="text-decoration: underline"/>
                                    <f:facet  name="footer">
                                        <h:outputLabel/>
                                    </f:facet>
                                </p:column>
                                <p:column styleClass="ellipsis-column" style="width:calc(200px)"  sortBy="#{salCr.repDocNumber}" headerText="PO Number">
                                     <!--<p:tooltip  position="bottom" for="salesCreditedForm:datascroll:#{scrollIndex}:saleCredit2:#{index1}:repDocNum" value="#{salCr.repDocNumber}"/>-->
                                   
 <!--//  #13295  #13295 ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO-->
                                    <h:commandLink id="repDocNum" target="_blank"  action="#{mainSalesCreditRep.goToPoDetailsPage(salCr.repDocId)}" value="#{salCr.repDocNumber}" style="text-decoration: underline"/>
                                    <f:facet  name="footer">
                                        <h:outputLabel/>
                                    </f:facet>
                                </p:column>
                                <p:column  style="width:calc(100px)"  sortBy="#{salCr.repDocDate}"  headerText="PO Date">
                                    <p:outputLabel value="#{salCr.repDocDate}">
                                        <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                    </p:outputLabel>
                                    <f:facet  name="footer">
                                        <h:outputLabel/>
                                    </f:facet>
                                </p:column>
                                <p:column styleClass="ellipsis-column" style="width:calc(200px)"  sortBy="#{salCr.repPrimCustName}" headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}">
                                     <!--<p:tooltip  position="bottom" for="salesCreditedForm:datascroll:#{scrollIndex}:saleCredit2:#{index1}:repCustNam" value="#{salCr.repPrimCustName}"/>-->
                                    <h:commandLink id="repCustNam" title="#{salCr.repPrimCustName}"  target="_blank"  action="#{customerProductMst.goToCompanyDetailsPage(salCr.repPrimCustId)}" value="#{salCr.repPrimCustName}" style="text-decoration: underline"/>
                                    <f:facet  name="footer">
                                        <h:outputLabel/>
                                    </f:facet>
                                </p:column>
                                
                                
                                
                                <p:column style="width:calc(150px)"  sortBy="#{salCr.repPrimSmanName}"  headerText="#{custom.labels.get('IDS_SALES_TEAM')}">
                                    <p:outputLabel  value="#{salCr.repPrimSmanName}" ></p:outputLabel>
                                    <f:facet  name="footer">
                                          <h:outputLabel/>
                                    </f:facet>
                                </p:column>
                                 <!--#13125 CRM-7652  Sales Credit Report needs to show ALL orders, not just those with Direct Commissions - add-->
                                 <p:column styleClass="ellipsis-column" style="width:calc(200px)"  sortBy="#{salCr.repSecCustName}" headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}">
                                     <!--<p:tooltip  position="bottom" for="salesCreditedForm:datascroll:#{scrollIndex}:saleCredit2:#{index1}:repCustNam" value="#{salCr.repSecCustName}"/>-->
                                    <h:commandLink id="repSecCustNam" title="#{salCr.repSecCustName}"  target="_blank"  action="#{customerProductMst.goToCompanyDetailsPage(salCr.repSecCustId)}" value="#{salCr.repSecCustName}" style="text-decoration: underline"/>
                                    <f:facet  name="footer">
                                        <h:outputLabel/>
                                    </f:facet>
                                </p:column>
                                 <!--#13125 CRM-7652  Sales Credit Report needs to show ALL orders, not just those with Direct Commissions - add-->
                                 <p:column styleClass="ellipsis-column" style="width:calc(200px)"  sortBy="#{salCr.repDistriName}" headerText="#{custom.labels.get('IDS_DISTRI')}">
                                     <!--<p:tooltip  position="bottom" for="salesCreditedForm:datascroll:#{scrollIndex}:saleCredit2:#{index1}:repCustNam" value="#{salCr.repPrimCustName}"/>-->
                                    <h:commandLink id="repDistriNam" title="#{salCr.repDistriName}"  target="_blank"  action="#{customerProductMst.goToCompanyDetailsPage(salCr.repDistriId)}" value="#{salCr.repDistriName}" style="text-decoration: underline"/>
                                    <f:facet  name="footer">
                                          <!--/*#13295   ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO*/-->
                                        <h:outputLabel   value="SubTotal : "  style="float:right"  class="footerFont"/>
                                    </f:facet>
                                </p:column>
                                
                                <p:column style="width:calc(150px)"  sortBy="#{salCr.repTotalPrice}" headerText="Order Amount">
                                    <p:outputLabel style="float: right"  value="#{salCr.repTotalPrice}">
                                        <f:convertNumber pattern="#,##0.00"/>
                                    </p:outputLabel>
                                    <f:facet  name="footer">
                                        <h:outputLabel style="float: right"  value="#{salescred.orderSubTotalAmt}"   class="footerFont">
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>

                                <p:column style="width:calc(80px)"  sortBy="#{salCr.repSalesRepPct}" headerText="Split %">
                                    <p:outputLabel style="float: right" value="#{salCr.repSalesRepPct}" ></p:outputLabel>
                                    <f:facet  name="footer">
                                        <h:outputLabel/>
                                    </f:facet>
                                </p:column>
                                <p:column style="width:calc(150px)"  sortBy="#{salCr.repSalesRepAmt}" headerText="Split Amount">
                                    <p:outputLabel style="float: right"  value="#{salCr.repSalesRepAmt}">
                                        <f:convertNumber pattern="#,##0.00"/>
                                    </p:outputLabel>
                                    <f:facet  name="footer">
                                        <h:outputLabel style="float: right"   value="#{salescred.splitSubTotalAmt}"    class="footerFont">
                                            <f:convertNumber maxFractionDigits="0"  minFractionDigits="0"/>
                                        </h:outputLabel> 
                                    </f:facet>
                                </p:column>

                            </p:dataTable>




                        </p:column>

                    </p:dataScroller>
                    <p:dataTable value="#{mainSalesCreditRep.saleCredGrpSalesRep.size()>0}"   emptyMessage=" " id="grandtotals" style="margin-top:-20px">

                        <p:column style="width:calc(200px)" >
                            <f:facet name="footer" >
                                <h:outputLabel >

                                </h:outputLabel>
                            </f:facet>

                        </p:column>  
                        <p:column style="width:calc(200px)" > 

                            <f:facet name="footer">
                                <h:outputLabel >


                                </h:outputLabel>

                            </f:facet>


                        </p:column> 

                        <p:column style="width:calc(100px)" > 

                            <f:facet name="footer">
                                <h:outputLabel  >


                                </h:outputLabel>

                            </f:facet>  


                        </p:column>  
                        <p:column style="width:calc(200px)" > 


                            <f:facet name="footer">
                                <h:outputLabel >

                                </h:outputLabel>

                            </f:facet>   
                        </p:column> 
                          <!--/*#13295   ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO*/-->
                         <p:column style="width:calc(200px)" > 


                            <f:facet name="footer">
                                <h:outputLabel >

                                </h:outputLabel>

                            </f:facet>   
                        </p:column> 
                        
                         <p:column style="width:calc(200px)" > 


                            <f:facet name="footer">
                                <h:outputLabel >

                                </h:outputLabel>

                            </f:facet>   
                        </p:column> 

                        <p:column style="width:calc(150px)" > 

  <!--/*#13295   ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO*/-->
                            <f:facet name="footer">
                                <h:outputLabel value="Total Order Amount:" style="float: right"  class="footerFont" >

                                </h:outputLabel>

                            </f:facet>   
                        </p:column> 
                          <!--/*#13295   ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO*/-->
                        <p:column style="width:calc(150px)"> 
                            <f:facet name="footer">
                                <h:outputLabel value="#{mainSalesCreditRep.orderGrandTotalAmt}" style="float: right"  class="footerFont" >

                                    <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                </h:outputLabel>

                            </f:facet>   
                        </p:column> 
                        <p:column style="width:calc(80px)" > 


                            <f:facet name="footer">
                                <h:outputLabel  >


                                </h:outputLabel>

                            </f:facet>   
                        </p:column> 
                        <p:column style="text-align: right;width:calc(150px)" > 
                            <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->

                            <f:facet name="footer">
                                <h:outputLabel value="#{mainSalesCreditRep.splitGrandTotalAmt}"  style="float: right"   class="footerFont" >

                                    <f:convertNumber maxFractionDigits="0"  minFractionDigits="0"/>
                                </h:outputLabel>

                            </f:facet>   
                        </p:column> 


                    </p:dataTable>

                </h:panelGroup>





            </h:form>
        </div>
        <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
        <ui:include src="/lookup/UsersLookupDlg.xhtml"/>
        <style>
            table[role="grid"]{
                table-layout: fixed !important;;
            }


            .ellipsis-column {
                width: 20px ;/* Set the desired width for the column */
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            
            
            
       /*#13295   ESCALATIONS CRM-7866   Order Credit Report cuts off columns on far right - Needs total for ORDER AMO*/

          @media only screen and (min-height : 600px) and (max-height : 900px)  {

                .ui-datatable-scrollable-body{
                    height: 43vh; 

                }



            }       

            @media only screen and (min-height : 901px) and (max-height : 1152px)  {

                .ui-datatable-scrollable-body{
                    height: 50vh; 

                }


            }
            /*1920 * 1080*/
            @media only screen and (min-height : 1153px) and (max-height : 1440px)  {

                .ui-datatable-scrollable-body{
                    /*task :4250*/
                     /*12325: 17/10/2023: CRM-7444   Commisson Data overview: not using full screen*/
                    height: 64vh; 

                }


            }

            @media only screen and (min-height : 1500px) and (max-height : 1640px)  {

                .ui-datatable-scrollable-body{
                    /*task :4250*/
                     /*12325: 17/10/2023: CRM-7444   Commisson Data overview: not using full screen*/
                    height:64vh; 

                }


            } 
             /*12325: 17/10/2023: CRM-7444   Commisson Data overview: not using full screen*/
              @media only screen and (min-height : 1641px) and (max-height : 2560px)  {
                .ui-datatable-scrollable-body{
                    /*task :4250*/
                    height:79vh; 
                }
            } 



        </style>
    </ui:define>

</ui:composition>
