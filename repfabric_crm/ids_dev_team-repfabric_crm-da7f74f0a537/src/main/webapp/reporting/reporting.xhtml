<?xml version='1.0' encoding='UTF-8' ?>
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://xmlns.jcp.org/jsf/core">


    <h:form id="mappingForm">
        <!--Growl message-->
        <p:growl id="mapGrowl"/>
        <div class="button_bar">
            <div style="float: left;">
                <p:commandButton id="rptBtn" ajax="false" value="Generate Report"  styleClass="btn-primary btn-xs"  action="#{reportingService.generateReport(oppRptMap.oppFieldsList, viewCompLookupService.selectedCompany.compId,viewCompLookupService.selectedCompany.compName )}" disabled="#{oppRptMap.oppFieldsList.size() ==0}" /><p:spacer width="3"/>
                <p:commandButton id="btnExpOppHist" value="Export Opp History" action="#{reportingService.principalExport(1 ,viewCompLookupService.selectedCompany.compId)}"  styleClass="btn-primary btn-xs" ajax="false" title="Only Last modified  date filters are applicable for this export"/> 
            </div>
        </div>
        <div style="float: right;">
            <p:outputPanel id="pnlButtons">
                <!--<p:commandButton class="button_top"  value="Add" actionListener="#{reportingService.populateFieldList()}" oncomplete="PF('dlgAddOppReportingFields').show();"  update=":addOppReportingFieldsForm"/><p:spacer width="3"/>-->

                 <!--Bug #7083 : opportunity reports->not selected any opportunity still add button is active-->
                <p:commandButton class="button_top"  value="Add" styleClass="btn-primary btn-xs" actionListener="#{reportingService.populateFieldList()}" disabled="#{viewCompLookupService.selectedCompany.compId == null}"  oncomplete="PF('dlgAddOppReportingFields').show();" update=":addOppReportingFieldsForm:addOppReportingFieldsDlg"/><p:spacer width="3"/>
                <p:commandButton class="button_top" value="Edit" styleClass="btn-primary btn-xs" actionListener="#{reportingService.populateFieldList()}" oncomplete="PF('dlgUpdateOppReportingFields').show();" update=":updateOppReportingFieldsForm:updateOppReportingFieldsDlg" disabled="#{oppRptMap.selectedField == null}"/><p:spacer width="3"/>
                <p:commandButton class="button_top" value="Delete" styleClass="btn-danger btn-xs" actionListener="#{oppRptMap.deleteField(viewCompLookupService.selectedCompany.compId)}" update="oppFieldsDT rptBtn" disabled="#{oppRptMap.selectedField == null}">
                    <p:confirm header="Confirmation" message="Are you sure to delete?" icon="ui-icon-alert"/>
                </p:commandButton><p:spacer width="3"/>
                <p:commandButton icon="fa fa-arrow-up"   title="Move up" actionListener="#{oppRptMap.moveUp(viewCompLookupService.selectedCompany.compId)}" update="oppFieldsDT"  styleClass="btn-primary btn-xs btn-icon" disabled="#{oppRptMap.selectedField == null}"/>    
                <p:spacer width="3"/>
                <p:commandButton icon="fa fa-arrow-down"   title="Move down" actionListener="#{oppRptMap.moveDown(viewCompLookupService.selectedCompany.compId)}" update="oppFieldsDT"  styleClass="btn-primary btn-xs btn-icon" disabled="#{oppRptMap.selectedField == null}"/>   

            </p:outputPanel>
        </div>


        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                     style="min-height: 50px"       layout="grid" styleClass="box-primary no-border ui-fluid ">
            <!--<p:row>-->
            <p:column><p:outputLabel value="#{custom.labels.get('IDS_PRINCI')}" /></p:column>


            <p:column colspan="4">
                <!--<p:outputLabel value="#{viewCompLookupService.selectedCompany.compName eq 'All' ?('All""'.concat(custom.labels.get('IDS_PRINCI'))): viewCompLookupService.selectedCompany.compName }" style="font-weight: bold;font-size:15px;color:blue;margin-left:-310px" id="princiName"/>--> 
           <!--<p:outputLabel value="#{viewCompLookupService.selectedCompany.compName eq 'All'?('All  '.concat(custom.labels.get('IDS_PRINCI'))) :viewCompLookupService.selectedCompany.compName}" style="font-weight: bold;font-size:15px;color:blue;margin-left:-310px" id="princiName"/>-->
                <p:outputLabel value="#{viewCompLookupService.selectedCompany.compName}"  style="font-weight: bold;font-size:15px;color:blue;margin-left:-310px" id="princiName"/>
            </p:column>
        </p:panelGrid>

        <p:panelGrid id="grid1" columns="5" 
                     style="min-height: 20px"       layout="grid" styleClass="box-primary no-border ui-fluid ">
            <p:column ><p:outputLabel value="Report Type" /></p:column>
            <!--<p:column><p:outputLabel value=":" class="m" style="margin-left:-76.5px"/></p:column>-->
            <p:column style="width:150px;" >
                <p:selectOneRadio id="reportType" value="#{reportingService.reportType}" style="margin-left:81px" disabled="#{viewCompLookupService.selectedCompany.compName == null}">
                    <p:ajax process="@this" listener="#{reportingService.onChangeReportType(viewCompLookupService.selectedCompany.compId)}" />
                    <f:selectItem itemLabel="One #{custom.labels.get('IDS_OPP')} per row" itemValue="1" itemEscaped="true"    >
                    </f:selectItem>
                    <f:selectItem itemLabel="One Row per #{custom.labels.get('IDS_LINE_ITEM')}"   itemValue="2" id="oneRowPerLineItem"  > 
                    </f:selectItem>
                </p:selectOneRadio> 
            </p:column>
            <p:column>
                <p:commandButton icon="fa fa-question-circle" oncomplete="PF('dlgOppReportingHelp').show();" style="width:17px;height:17px;vertical-align: top;margin-top:5px"/>   
            </p:column>

            <p:column  >
                <p:outputLabel value="File Type  :"  />
            </p:column>
            <p:column >
                <p:selectOneRadio id="expType" value="#{reportingService.fileType}" required="true"  requiredMessage="Select any file type"  >
                    <f:selectItem itemLabel="Excel" itemValue="3"/>
                    <f:selectItem itemLabel="CSV" itemValue="2"/>
                </p:selectOneRadio>  
            </p:column>

        </p:panelGrid>


        <!--CRM -106-->   

        <p:panelGrid columns="2"  style="min-height: 30px"   styleClass="box-primary no-border ui-fluid" layout="grid">
            <p:column style="margin-right:100px"  >
                <p:outputLabel value="Filter By "/></p:column>
            <!--<p:column><p:outputLabel value=":" class="m" style="margin-left:-16.5px" /></p:column>-->

            <p:column > 

                <p:selectOneMenu value="#{reportingService.filterby}" id="fb"   widgetVar="filterBy" style="margin-left:-310px;width:55%" >
                    <f:selectItem   itemValue="1" itemLabel="Open and Reporting turned ON"  />                                   
                    <f:selectItem itemValue="2" itemLabel="Open/Closed and Reporting turned ON"/>
                    <!--// CRM-106 All(closed reasons and dates)-->
                    <f:selectItem itemValue="3" itemLabel="All Closed"/>
                    <f:selectItem itemValue="4" itemLabel="All"/>
                </p:selectOneMenu>

            </p:column>
        </p:panelGrid>


        <!--CRM -106-->   
        <p:panelGrid columns="4"  style="min-height: 30px"  styleClass="box-primary no-border ui-fluid" 
                     layout="grid">

            <p:column ><p:outputLabel value="Last Modified Start Date" style="width:150px" /></p:column>

            <p:column > <p:calendar pattern="#{globalParams.dateFormat}"  value="#{reportingService.startDate}" style="margin-left:-70px"  autocomplete="off" >
                </p:calendar></p:column>
            <p:column ><p:outputLabel value="Last Modified End Date" style="width:150px;margin-left:-70px"/></p:column>

            <p:column> <p:calendar pattern="#{globalParams.dateFormat}"  value="#{reportingService.endDate}" style="margin-left:-150px" autocomplete="off" >
                </p:calendar></p:column>

        </p:panelGrid>



        <p:dataTable value="#{oppRptMap.oppFieldsList}" var="oppField" id="oppFieldsDT" scrollable="true" scrollHeight="400"
                     selectionMode="single" selection="#{oppRptMap.selectedField}" rowKey="#{oppField.recId}">
            <p:ajax event="rowSelect" update=":mappingForm:pnlButtons"/>

            <p:column headerText="Seq.#" style="width: 30px">
                <h:outputText value="#{oppField.rptSeqNum}"/>
                  <!--<p:commandButton class="button_top" value="Delete" styleClass="btn-danger btn-xs" actionListener="#{oppRptMap.deleteField()}" update="oppFieldsDT rptBtn">-->

            </p:column>
            <p:column headerText="Field">
                <h:outputText value="#{oppField.columnName}"/>
            </p:column>
            <p:column headerText="Type">
                <h:outputText value="#{custom.labels.get('IDS_OPP')} (#{custom.labels.get('IDS_CUSTOM_FIELDS')})" rendered="#{oppField.rptColType == 'OC'}"/>
                <h:outputText value="#{custom.labels.get('IDS_OPP')} (Standard)" rendered="#{oppField.rptColType == 'OR'}"/>
                <h:outputText value="#{custom.labels.get('IDS_LINE_ITEM')} (#{custom.labels.get('IDS_CUSTOM_FIELDS')})" rendered="#{oppField.rptColType == 'LC'}"/>
                <h:outputText value="#{custom.labels.get('IDS_LINE_ITEM')} (Standard)" rendered="#{oppField.rptColType == 'LR'}"/>
            </p:column>
        </p:dataTable>

        <p:confirmDialog global="true" showEffect="fade" hideEffect="fade" class="div-center">
            <p:commandButton value="Yes" type="button" styleClass="ui-confirmdialog-yes btn btn-success btn-xs" />
            <p:spacer width="4px" />
            <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no btn btn-warning btn-xs" />
        </p:confirmDialog>


    </h:form>
    <p:confirmDialog severity="alert" id="confirmReportTypeDlg" showEffect="fade" hideEffect="fade" header="Confirmation" styleClass="disable-scroll"  widgetVar="dlgConfirmReportType" closable="false" class="div-center" >  
        <f:facet name="message">
            <p:outputLabel value="#{custom.labels.get('IDS_LINE_ITEM')} fields will be deleted. Are you sure to proceed?" />
        </f:facet>
        <h:form id="confirmReportTypeForm" class="div-center">
            <p:commandButton value="Yes" styleClass=" btn btn-danger  btn-xs btn-icon btn-xs"  actionListener="#{reportingService.updateReportType(viewCompLookupService.selectedCompany.compId)}" update=":mappingForm:oppFieldsDT  :mappingForm:rptBtn" oncomplete="PF('dlgConfirmReportType').hide();" />
            <p:spacer width="4px"  />
            <p:commandButton value="No" styleClass="btn btn-warning  btn-xs btn-icon btn-xs"  actionListener="#{reportingService.resetReportType()}" update=":mappingForm:reportType" oncomplete="PF('dlgConfirmReportType').hide();"/>
        </h:form>
    </p:confirmDialog>

    <!--<ui:include src="ExtendedReportMap.xhtml" />-->
    <ui:include src="AddOppReportingFieldsDlg.xhtml"/>
    <ui:include src="UpdateOppRptFieldsDlg.xhtml"/>


</ui:composition>

