<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: CommissionOverview.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->


<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <!--#361:page title-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title> Commission Data Overview </title>
    </ui:define>

    <ui:define name="meta">
        <f:metadata> 

            <f:viewAction action="#{commsMain.resetValues()}"/>
            <f:viewAction action="#{commsMain.defaultValues()}"/>
            <f:viewAction action="#{commsMain.populateCommRecords()}"/>
            <!--2522- Related - CRM-1521: Tutorial button landing urls - Data management - 28-11-2019 - sharvani-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('COMM_OVERVIEW'))}" />
            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('COMM_OVERVIEW')}" />
        </f:metadata>
    </ui:define>

    <ui:define name="title">
        <ui:param name="title" value="Commissionable Transactions"/>
        <div class="row">
            <div class="col-md-6">
                <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                #{custom.labels.get('IDS_COMMISSION')} Data Overview
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>

    <ui:define name="menu">

    </ui:define>


    <ui:define name="body">

        <f:event listener="#{commsMain.isPageAccessible}" type="preRenderView"/> 
        <h:form id="commOverviewForm">
            <div class="box box-info box-body" style="vertical-align: top">
                <!--                <div class="box-header with-border">
                                    <div class="row">
                                        <div class="col-md-5">
                                            <p:commandButton value="Export"  oncomplete="PF('dlgCommsExport').show();" styleClass="btn btn-primary btn-xs" action="#{commsMain.clearExportDlgfilters()}" update="exportDlg"/> 
                
                                        </div>
                                    </div>
                                </div>-->

                <p:panelGrid styleClass="panelgrid"  id="pnlView" style="margin-top:-35px" >
                    <p:row>


                        <p:column >


                            <p:outputLabel value="Filter by"/>
                        </p:column>
                        <p:column  >

                            <p:selectOneMenu id="filterList" value="#{commsMain.filterOption}" style="width:150px; border-radius: 5px; background: white;margin-left: -5px;" >
                                <f:selectItem itemLabel="None" itemValue="4" />
                                <f:selectItem itemLabel="Check Date" itemValue="1" />
                                <f:selectItem itemLabel="Invoice Date" itemValue="2" />
                                <!--<f:selectItem itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}" itemValue="3" />-->
                                <!--                        <f:selectItem itemLabel="Unpaid" itemValue="4" />
                                                        <f:selectItem itemLabel="Paid" itemValue="5" />-->
                                <p:ajax event="change" listener="#{commsMain.showOption()}" process="@this"
                                        
                                        update=":commOverviewForm :commOverviewForm:recordDT :commOverviewForm:pnlView"
                                        />
                                
<!--<p:ajax event="itemSelect" listener="#{commsMain.showOption()}" oncomplete="PF('commDT').clearFilters();" process="@this" update=":commOverviewForm  :commOverviewForm:pnlView" />-->
                            </p:selectOneMenu>
                        </p:column>
                        <p:panel id="filterSummary" style="padding:0;border:0px;" >
                            <p:column>
                                <!--#.2593:Updates to Commission Data Overview - Design Update-->
                                <p:outputLabel value="From :" class="fmt-b" rendered="#{commsMain.filterOption != 4}"/>
                            </p:column>
                            <p:column style="width:10%">
                                <!--<p:outputLabel styleClass="m" value=":" />-->
                                <p:calendar id="startDate" pattern="#{globalParams.dateFormat}"  value="#{commsMain.fromDate}"    showOn="button" style="width:120px"  rendered="#{commsMain.filterOption != 4}">

                                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                </p:calendar>
                            </p:column>

                            <p:spacer width="10" />
                            <p:column>
                                <!--#2593:Updates to Commission Data Overview - Design Update-->
                                <p:outputLabel value="To:" class="fmt-b" rendered="#{commsMain.filterOption != 4}"/>
                            </p:column>
                            <p:column  style="width:10%;">
                                <!--<p:outputLabel styleClass="m" value=":" />-->
                                <p:calendar id="endDate" pattern="#{globalParams.dateFormat}"  value="#{commsMain.toDate}"  showOn="button"  rendered="#{commsMain.filterOption != 4}"  >
                                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                </p:calendar>
                            </p:column>
                            <!--                            PMS 3761: Commission Data Overview  Populate sub totals based on filter changed width-->
                            <p:column  style="width:auto">
                                <p:commandButton value="Filter"  onstart="PF('dlgSalaj').show();" action="#{commsMain.populateCommRecords()}" oncomplete="if (args &amp;&amp; !args.validationFailed) PF('filterDateDlg').hide();PF('commDT').filter();PF('dlgSalaj').hide();" update=":commOverviewForm  :commOverviewForm:pnlView" styleClass="btn btn-primary btn-xs" rendered="#{commsMain.filterOption != 4}"/>
                                <!--PMS 3761: Commission Data Overview  Populate sub totals based on filter-->
                                <p:spacer width="4px" />
                                <p:commandButton value="Show Totals" immediate="true"  id="btnSubTotal" actionListener="#{commsMain.calculateSubTotal()}" 
                                                 styleClass="btn btn-primary btn-xs"
                                                 oncomplete="PF('ovrLaySubTtl').show();" update="commOverviewForm:ovrLaySubTtl" />
                                                                                 
                                <p:spacer width="4px" />
                                <!--12335 : 19/10/2023: CRM-7449 CommDataOverview: mark selected date range paid-->
                                <p:commandButton value="Mark Paid" immediate="true"  id="btnMarkPaid" disabled="#{commsMain.filterRadioOption == 4}"
                                                 styleClass="btn btn-primary btn-xs"
                                                 onclick="PF('markPaidDlg').show();" update="" />
                                
                                <p:overlayPanel  showCloseIcon="true" widgetVar="ovrLaySubTtl" id="ovrLaySubTtl" style="width: auto;height: auto;">
                                    <table>
                                        <tr>
                                            <td>
                                                <p:outputLabel id="olttlSales" value="Sales: " style="font-weight: bold !important;" />
                                            </td>
                                            <td style="text-align: right !important;">
                                                <p:spacer width="4px"/>
                                                <p:outputLabel id="olttlSalesVal"  value="#{commsMain.subTtlCommTotalPrice}" >
                                                    <f:convertNumber maxFractionDigits="2" groupingUsed="true"/>
                                                </p:outputLabel>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                                <p:outputLabel id="olTtlComLbl" value="#{custom.labels.get('IDS_COMMISSION')}:" style="font-weight: bold !important;" />
                                            </td>
                                            <td style="text-align: right !important;">
                                                <p:spacer width="4px"/>
                                                <p:outputLabel id="olTtlComLblVal" value="#{commsMain.subTtlCommAmount}"  >
                                                    <f:convertNumber maxFractionDigits="2" groupingUsed="true"/>
                                                </p:outputLabel>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                                <p:outputLabel id="olttlCompCom" value="Company #{custom.labels.get('IDS_COMMISSION')}:" style="font-weight: bold !important;"/>
                                            </td>
                                            <td style="text-align: right !important;">
                                                <p:spacer width="4px"/>
                                                <p:outputLabel id="olttlCompComVal" value="#{commsMain.subTtlCommCompAmount}" >
                                                    <f:convertNumber maxFractionDigits="2" groupingUsed="true"/>
                                                </p:outputLabel>                                                
                                            </td>
                                        </tr>
                                    </table>
                                </p:overlayPanel>

                            </p:column>
                        </p:panel>



                        <p:column style="float:right;">
                            <p:selectOneRadio style="#{
                                              commsMain.filterOption eq 4 ?  'font-size:14px;margin-left: 170px' 
                                                  : ( commsMain.filterOption ne 4 ? 'font-size:14px;' : 'font-size:14px;')}" value="#{commsMain.filterRadioOption}" >
                                <f:selectItem itemLabel="Paid" itemValue="4"/>
                                <f:selectItem itemLabel="Unpaid" itemValue="5"/>
                                <f:selectItem itemLabel="All" itemValue="6"/>
                                <p:ajax event="change"  onstart="PF('dlgSalaj').show();"   listener="#{commsMain.showRadioFilter()}" oncomplete="PF('commDT').filter();PF('dlgSalaj').hide();" />
                            </p:selectOneRadio>

                            <p:dialog header="Please wait" draggable="false"  widgetVar="dlgSalaj" modal="true" closable="false" resizable="false" >
                                <h:graphicImage  library="images" name="ajax-loader.gif" />
                                <p:spacer width="5"/>
                                <h:outputLabel value="Loading data.."/>
                            </p:dialog>
                            <p:dialog header="Please wait" draggable="false"  widgetVar="dlgProcess" modal="true" closable="false" resizable="false" >
                                <h:graphicImage  library="images" name="ajax-loader.gif" />
                                <p:spacer width="5"/>
                                <h:outputLabel value="Processing.."/>
                            </p:dialog>


                        </p:column>


                        <p:column>
                            <!--#3809: Commission Data Overview > Excel export-->
                            <!--\                            <h:commandLink title="Excel">
                                                            <p:graphicImage name="/images/excel.png" width="24"/>
                                                            <p:dataExporter type="xls" target="recordDT" fileName="CommissionOverview"   />
                                                        </h:commandLink>-->
                            <p:commandButton    ajax="false" id="exp1"   image="imgClassName"
                                                class="imgClassName"  style="border: none!important;" actionListener="#{tableDataExporter.configure('COMM_DATA_OVERVIEW')}">      
                                <p:dataExporter type="xlsx" target="recordDT"  fileName="CommissionOverview"  postProcessor="#{tableDataExporter.postProcessXLS}" preProcessor="#{commsMain.exportpreProc}"    />  
                            </p:commandButton> 

                            <h:commandLink title="CSV">
                                <p:graphicImage name="/images/csv.png" width="24"/>
                                <p:dataExporter type="csv" target="recordDT" fileName="CommissionOverview" />
                            </h:commandLink>
                        </p:column>
                        <!--</div>-->

                        <p:column>


                            <p:commandButton value="Export"  oncomplete="PF('dlgCommsExport').show();" styleClass="btn btn-primary btn-xs" action="#{commsMain.clearExportDlgfilters()}" update="exportDlg"/> 

                        </p:column>
                    </p:row>
                </p:panelGrid>
             <!--#9010   CRM-6054  bnmsales/Commissions Data Overview window makes the scroll bar disappear when you attempt-->
                <!--/*Task Feature #5641:#Commission Data Overview: Sticky Header-->
                <!--//07-02-2022 : 7104 :Commission Data Overview: Implement Lazy loading-->
<!--               12325: 17/10/2023: CRM-7444   Commisson Data overview: not using full screen-->
                <p:dataTable id="recordDT" value="#{commsMain}" var="record" 
                             widgetVar="commDT" lazy="true"
                             filteredValue="#{commsMain.filteredRecords}"
                             selection="#{commsMain.paidCommRecords}"
                             rowKey="#{record.recId}"
                             rowSelectMode="false"
                             paginator="true" multiViewState="true"
                             rows="50" rowsPerPageTemplate="50,100, 150, 200"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             paginatorPosition="top"
                             draggableColumns="true" paginatorAlwaysVisible="true"
                             tableStyle="table-layout:auto" resizableColumns="true" 
                             scrollable="true"
                             scrollHeight="60vh"
                             >
                    <!--liveScroll="true"-->
                    <!--07-02-2022 : 7104 :Commission Data Overview: Implement Lazy loading-->
                    <p:ajax event="rowSelectCheckbox" listener="#{commsMain.onSelect}" onstart="PF('dlgProcess').show();" update=":commOverviewForm:recordDT" oncomplete="PF('dlgProcess').hide();"  />
                    <p:ajax event="rowUnselectCheckbox" listener="#{commsMain.onUnSelect}" onstart="PF('dlgProcess').show();" update=":commOverviewForm:recordDT" oncomplete="PF('dlgProcess').hide();" />
                    <p:ajax event="toggleSelect" listener="#{commsMain.onToggleSelect}" onstart="PF('dlgProcess').show();" oncomplete="PF('dlgProcess').hide();" update=":commOverviewForm:recordDT"  />
                    <p:ajax event="page" listener="#{commsMain.onPagination}" update=":commOverviewForm:recordDT" />

<!--                    <p:ajax event="rowSelectCheckbox" listener="#{commsMain.onPaid}"   />
                    <p:ajax event="rowUnselectCheckbox" listener="#{commsMain.onPaid}"   />
                    <p:ajax event="toggleSelect" listener="#{commsMain.onPaid}"  />-->
                    <!--<p:ajax event="page" update=":commOverviewForm:recordDT" oncomplete="PF('commDT').filter();" />-->
                    
                   <!--02-09-2024 : CRM:8429 : Task:14403 : Renamed the Colum name of "Paid" to "Paid to Rep" in Commision Data Overview -->
                    <p:column selectionMode="multiple" headerText="Paid to Rep" exportable="false"  style="text-align: center;width:100px;"  >
                    </p:column>
                    <p:column headerText="Paid to Rep" visible="false"  >
                        <p:outputLabel value="#{record.commPaid == 1? 'Yes':'No'}"  />

                    </p:column>
                    <!--                       //#2273 - CRM 1386 - 12/11/2019
                                //Add batch # in data management - " data overview
                                //added new field COMM_IMP_BATCH in the query-->
                    <!--07-02-2022 : 7104 :Commission Data Overview: Implement Lazy loading-->
                    <p:column headerText="Batch" sortBy="#{record.commImpBatch}" field="COMM_IMP_BATCH" filterBy="#{record.commImpBatch}" filterMatchMode="contains"  width="60" >
                        <h:outputText value="#{record.commImpBatch}" />
                    </p:column>

                    <!--Task-6424: CRM-5023: Repfabric / CommissionOverview Excel Report-->
                    <p:column headerText="Invoice Date"  sortBy="#{record.commInvcDate}" field="COMM_INVC_DATE"  filterBy="#{rFUtilities.formatDateTime(commsMain.convertDate(record.commInvcDate), 'da')}"  filterMatchMode="contains"   width="110" >
                        <p:outputLabel value="#{rFUtilities.formatDateTime(commsMain.convertDate(record.commInvcDate), 'da')}" />

                    </p:column>
                    <!--/*Task Feature #5641:#Commission Data Overview: Sticky Header-->
                    <p:column headerText="Check Date"  sortBy="#{rFUtilities.convertDate(record.commCheckDate)}" field="COMM_CHECK_DATE" filterBy="#{rFUtilities.convertDate(record.commCheckDate)}"  filterMatchMode="contains"  width="110"  >
                        <h:outputText value="#{rFUtilities.convertDate(record.commCheckDate)}"  >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </h:outputText>
                    </p:column>
                    <!--/*Task Feature #5641:#Commission Data Overview: Sticky Header-->
                    <p:column headerText="Invoice #" sortBy="#{record.commInvcNumber}" field="COMM_INVC_NUMBER"   filterBy="#{record.commInvcNumber}" filterMatchMode="contains" width="100" >
                        <h:outputText value="#{record.commInvcNumber}" title="#{record.commInvcNumber}" />
                    </p:column>
                    <!--/*Task Feature #5641:#Commission Data Overview: Sticky Header-->
                    <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" field="PRINCI_NAME"  sortBy="#{record.princiName}"   filterBy="#{record.princiName}" filterMatchMode="contains"  width="150">
                        <h:outputText value="#{record.princiName}" title="#{record.princiName}" />
                    </p:column>
                    <!--/*Task Feature #5641:#Commission Data Overview: Sticky Header-->
                    <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" field="CUST_NAME" sortBy="#{record.custName}"   filterBy="#{record.custName}" filterMatchMode="contains" width="150"  >
                        <h:outputText value="#{record.custName}" title="#{record.custName}"  />
                    </p:column>
                    <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                    <!--/*Task Feature #5641:#Commission Data Overview: Sticky Header-->
                    <p:column headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" field="SECOND_CUST_NAME" sortBy="#{record.secCustName}"    filterBy="#{record.secCustName}" filterMatchMode="contains" width="150"  >
                        <h:outputText value="#{record.secCustName}"  title="#{record.secCustName}"  />
                    </p:column>
                    <p:column headerText="#{custom.labels.get('IDS_SALES_TEAM')}" field="SALES_TEAM"  sortBy="#{record.salesTeamName}"   filterBy="#{record.salesTeamName}" filterMatchMode="contains" width="150"  >
                        <h:outputText value="#{record.salesTeamName}" title="#{record.salesTeamName}"   />
                    </p:column>
                    <!--/*Task Feature #5641:#Commission Data Overview: Sticky Header-->
                    <p:column headerText="#{custom.labels.get('IDS_DISTRI')}"  field="DISTRI_NAME"    sortBy="#{record.distriName}"    filterBy="#{record.distriName}" filterMatchMode="contains" width="150" >
                        <h:outputText value="#{record.distriName}" title="#{record.distriName}"  />
                    </p:column>
                    <p:column headerText="#{custom.labels.get('IDS_PART_NUM')}" field="COMM_PART_NO"  sortBy="#{record.commPartNo}"    filterBy="#{record.commPartNo}" filterMatchMode="contains" width="200" >
                        <h:outputText value="#{record.commPartNo}" title="#{record.commPartNo}"   />
                    </p:column>
                    <!--/*Task Feature #5641:#Commission Data Overview: Sticky Header-->
                    <p:column headerText="Line Total" style="text-align: right" width="80" field="COMM_TOTAL_PRICE" >
                        <h:outputText value="#{record.commTotalPrice}" />
                    </p:column>
                    <!--/*Task Feature #5641:#Commission Data Overview: Sticky Header-->
                   <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                    <p:column headerText="#{custom.labels.get('IDS_COMMISSION')}" style="text-align: right" field="COMM_AMOUNT" width="80"   >
                        <h:outputText value="#{record.commAmount}" />
                    </p:column>
                    <!--/*Task Feature #5641:#Commission Data Overview: Sticky Header-->
                    <p:column headerText="Quantity" style="text-align: right" field="COMM_QUANTITY"  width="80" >
                        <h:outputText value="#{record.commQuantity}" />
                    </p:column>
                    <!--/*Task Feature #5641:#Commission Data Overview: Sticky Header-->
                    <p:column headerText="Unit Price" style="text-align: right" width="80" field="COMM_UNIT_PRICE"  >
                        <h:outputText value="#{record.commUnitPrice}" />
                    </p:column>
                    <!--/*Task Feature #5641:#Commission Data Overview: Sticky Header-->
                   <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                    <p:column headerText="#{custom.labels.get('IDS_COMM')} Rate" style="text-align: right" width="80" field="COMM_COMM_RATE"  >
                        <h:outputText value="#{record.commCommRate}" />
                    </p:column>
                    <!--//23-03-2022 : #7562 : CRM-5330: Comm. Data Overview: Conversion Rate-->
                    <p:column headerText="Conv. Rate" style="text-align: right" width="80" field="COMM_CONVERSION_RATE"  >
                        <h:outputText value="#{record.commConversionRate}" />
                    </p:column>
                    <!--#1078 :CRM-2778: Tennant Specs: Check # on commissions table-->

                    <p:column headerText="Check Number"  sortBy="#{record.commCheckNum}" field="COMM_CHECK_NUM" 
                              filterBy="#{record.commCheckNum}" filterMatchMode="contains" width="110"  >
                        <h:outputText value="#{record.commCheckNum}" />
                    </p:column>
                </p:dataTable>


            </div>
            <!--12335 : 19/10/2023: CRM-7449 CommDataOverview: mark selected date range paid-->
            <p:confirmDialog header="Confirmation"  width="400" global="false"
                             message="This will mark the selected date range of records paid. Proceed?" widgetVar="markPaidDlg" >
                    <div class="div-center">
                        <p:commandButton value="Yes" immediate="true" 
                                         class="btn btn-danger btn-xs" actionListener="#{commsMain.onPaid()}"   oncomplete="PF('markPaidDlg').hide();dtLoad();" update=":commOverviewForm:recordDT" />
                        <p:spacer width="4"/>
                        <p:commandButton value="No"  class="btn btn-warning btn-xs" onclick="PF('markPaidDlg').hide()" 
                                         type="button"  />
                    </div>
                </p:confirmDialog>

        </h:form>

        <ui:include src="CommsExportDlg.xhtml" />
        <ui:include src="FilterDateDlg.xhtml" />
        <!--12335 : 19/10/2023: CRM-7449 CommDataOverview: mark selected date range paid-->
        <script>
            function dtLoad(){
                $('.ui-datatable-selectable').attr('aria-selected',true);
                 $("[name='commOverviewForm:recordDT_checkbox']").attr('aria-checked',true); 
                  $("[name='commOverviewForm:recordDT_checkbox']").attr('checked','checked');
                  $('.ui-chkbox-box').addClass('ui-state-active');
                  $('.ui-chkbox-icon').removeClass('ui-icon-blank');
                 $('.ui-chkbox-icon').addClass('ui-icon-check');
                 
            }
        </script>
        <style>
            label {
                font-weight: normal!important;
            }
            .ui-panelgrid tr, .ui-panelgrid td{
                border:0!important;
            }
            #commTransListForm\:dtCommtransList\:radio span
            {
                position: relative !important;
                left:-1px !important;
            }
            #commTransListForm\:dtCommTransDtlLst\:radio span
            {
                position: relative !important;
                left:-1px !important;
            }
            #commOverviewForm\:startDate_input
            {
                width:100%;
                margin-left:-25px;
            }
            #commOverviewForm\:endDate_input
            {
                width:100%;
                margin-left:-20px;
            }
            .ui-datatable-scrollable-theadclone {
                visibility: collapse !important;
            }
            .sss{
                overflow-x: scroll;
                overflow-y: scroll;
                overflow: auto;
                position: fixed;
                height: 400px;
            }

            .ui-datatable-tablewrapper {
                overflow: initial !important; 
            }
            .ui-paginator {
                background-color:#ffffff1f!important;
            }
            .q{
                width: 100%!important;
            }
            .p{
                width: 80%!important;
            }

            #commOverviewForm\:menu_button
            {
                background-color: #0078D7;color: white;
            }
            /*#3809: Commission Data Overview > Excel export*/
            .imgClassName{
                //   background-image:url(/images/excel.png);
                /*background: url("#{request.contextPath}/resources/images/link-add.png") !important;*/
                background: url("#{request.contextPath}/resources/images/excel27.png") !important;
                padding: 48px;
                margin-top: -10px;
                margin-left: -13px;

            }

            /*Task #5612:  Feasibility: Commission Data Overview: Sticky Header*/


            .ui-datatable-scrollable table {
                table-layout: fixed !important;
            }

            /*1360*768*/
            @media only screen and (min-height : 600px) and (max-height : 900px)  {

                .ui-datatable-scrollable-body{
                    height: 65vh; 

                }



            }       

            @media only screen and (min-height : 901px) and (max-height : 1152px)  {

                .ui-datatable-scrollable-body{
                    height: 70vh; 

                }


            }
            /*1920 * 1080*/
            @media only screen and (min-height : 1153px) and (max-height : 1440px)  {

                .ui-datatable-scrollable-body{
                    /*task :4250*/
                     /*12325: 17/10/2023: CRM-7444   Commisson Data overview: not using full screen*/
                    height: 70vh; 

                }


            }

            @media only screen and (min-height : 1500px) and (max-height : 1640px)  {

                .ui-datatable-scrollable-body{
                    /*task :4250*/
                     /*12325: 17/10/2023: CRM-7444   Commisson Data overview: not using full screen*/
                    height:80vh; 

                }


            } 
             /*12325: 17/10/2023: CRM-7444   Commisson Data overview: not using full screen*/
              @media only screen and (min-height : 1641px) and (max-height : 2560px)  {
                .ui-datatable-scrollable-body{
                    /*task :4250*/
                    height:80vh; 
                }
            } 

            html,body{
                min-height:120%;

                overflow-y: hidden;
            }

            /*Task Feature #5641:#Commission Data Overview: Sticky Header*/ 
            .ui-datatable-scrollable-header *,
            .ui-datatable-scrollable-theadclone * {
                -moz-box-sizing: content-box;
                -webkit-box-sizing: content-box;
                box-sizing: content-box;
            }

            body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
                width: 15px;
            }


        </style>  
    </ui:define>
</ui:composition>
