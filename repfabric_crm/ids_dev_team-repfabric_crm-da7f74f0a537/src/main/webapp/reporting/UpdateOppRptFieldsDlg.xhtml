<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: AddOppReportingFieldsDlg.xhtml
// Author: Sarayu
//*********************************************************
* Copyright (c) 2017 Indea Design Systems Private Limited, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

   <h:form id="updateOppReportingFieldsForm">
    <p:dialog  id="updateOppReportingFieldsDlg" header="Update Field" resizable="false" styleClass="disable-scroll" width="520" height="510"   modal="true" widgetVar="dlgUpdateOppReportingFields" >  
     
            <p:dataTable value="#{reportingService.fieldList}" var="fieldType" 
                         scrollable="true" scrollHeight="500" style="width: 480px">
                <p:subTable var="fields" value="#{fieldType.fieldsList}" >
                    <f:facet name="header">
                        <h:outputText value="#{fieldType.fieldDispName}"/>
                    </f:facet>
                    <p:column>
                        <p:commandLink value="#{fields.fieldDispName}" actionListener="#{oppRptMap.updateField(fields,viewCompLookupService.selectedCompany.compId)}" oncomplete="PF('dlgUpdateOppReportingFields').hide();" update=":mappingForm:oppFieldsDT"/>
                        <!--<p:outputLabel value="#{fields.fieldDispName}"/>-->
                    </p:column>
                </p:subTable>
            </p:dataTable>
       
    </p:dialog>
   </h:form>
</ui:composition>

