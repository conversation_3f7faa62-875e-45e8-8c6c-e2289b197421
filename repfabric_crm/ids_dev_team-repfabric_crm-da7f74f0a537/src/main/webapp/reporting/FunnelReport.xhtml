<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">
    <!--#363 :page title-->
         <ui:define name="head">
<link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
<title>  Funnel Report </title>
</ui:define>

    <ui:define name="metadata">         
        <f:metadata>
            <f:viewAction action="#{lookupService.list('USER')}" />
            <f:viewAction action="#{lookupService.list('SALES_TEAM')}" />
            <!--<f:viewAction action="#{lookupService.list('REGION')}" />-->
            <f:viewAction action="#{viewCompLookupService.listPrincipals(1,'')}" />
             <!--#2521:   Related - CRM-1521: Tutorial button landing urls - Sales Reports, Funnel Report-->
             <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('FUNNEL_RPT'))}" />
             <!--29-09-2023  12191 CRM-6510 LMS Learning links-->
             <f:viewAction action="#{helpService.setPageTag('FUNNEL_RPT')}" />
        </f:metadata>
    </ui:define>
    <ui:define name="title">

        <ui:param name="title" value="Funnel Report "/>
        <div class="row">
            <div class="col-md-6">
                <p:outputPanel id="opType" >
                    #{funnelReport.pageHeader}
                </p:outputPanel>
            </div>
            <!-- #1456: Use common template in Funnel Report-->
            <div class="col-md-2" ></div>
            <div class="col-md-4 ui-inputgroup"  >
<!--                // #Task 4757 Add Ids for Buttons in Reporting screens-->
                <h:form id="formFunnelHeader">
<!--                    // #Task 4757 Add Ids for Buttons in Reporting screens-->
                    <p:commandButton  id="btnShowCurrent" value="Show Current" styleClass="btn btn-primary btn-xs"  onclick="callFunnelReport(1);"/> 
                    <p:spacer width="4" />
                    <p:commandButton id="btnShowProgress" value="Show Progressive" styleClass="btn btn-primary btn-xs"  onclick="callFunnelReport(2);"/> 
                </h:form>
                
                <p:spacer width="4px"/>
                <ui:include src="/help/Help.xhtml"/>
            </div>
        </div>
    </ui:define>
    <ui:define name="menu"></ui:define>

    <ui:define name="body">
        <!--#1298: Add Page Access Check - Funnel Report-->
        <f:event listener="#{funnelReport.isPageAccessible}" type="preRenderView"/>
        <!--<ui:include src="/lookup/SalesTeamsLookup.xhtml"/>-->
        <!--<ui:include src="/lookup/UsersLookupDlg.xhtml"/>-->
        <!--<ui:include src="/lookup/CompaniesLookup.xhtml"/>-->

        <h:form id="formFunnel">
            <p:remoteCommand name="report"  actionListener="#{funnelReport.runReport}"  update=":formFunnel:pnlFunnel :opType"  /> 
            <!--<p:remoteCommand name="applyPrincis" actionListener="#{example.applyPrincipals(lookupService.principals)}" update="@(.sel-princi)" />-->
            <div class="box box-info box-body" style="vertical-align: top">
                <div class="ui-fluid">
                    <div class="ui-g ui-fluid">
                        <div class="ui-sm-12 ui-md-4 ui-lg-4" >
                            <p:panelGrid id="grid" columns="3" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-4"
                                         layout="grid" styleClass="box-primary no-border ui-fluid">
                                <p:outputLabel for="fromDate" value="From" styleClass="required" />
                                <p:calendar pattern="#{globalParams.dateFormat}" id="fromDate" showOn="button" value="#{funnelReport.fromDate}"/>


                                <p:selectOneMenu id="onemenu" value="#{funnelReport.reportGroup}">
                                    <!--17-02-2022 : #7261 : CRM-5186  QUESTION: "Activity" is used in Funnel Report and "Stage" show-->
                                    <f:selectItem itemLabel="#{custom.labels.get('IDS_ACTIVITY')}" itemValue="1"/>
                                    <f:selectItem itemLabel="#{custom.labels.get('IDS_OPP_STAGE')}" itemValue="2"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="toDate" value="To" styleClass="required" />
                                <p:calendar pattern="#{globalParams.dateFormat}" id="toDate" showOn="button" value="#{funnelReport.toDate}"/>

                                <p:selectOneMenu id="typeMenu" value="#{funnelReport.reportData}">
                                    <f:selectItem itemLabel="Count" itemValue="1"/>
                                    <f:selectItem itemLabel="Value" itemValue="2"/>
                                </p:selectOneMenu>
                            </p:panelGrid>
                            <!--                            <br />
                                                        <div style="text-align: center;">
                                                            <p:commandButton value="Run Report" actionListener="#{funnelReport.runReport()}" styleClass="btn btn-primary btn-xs" update=":formFunnel:pnlFunnel" /> 
                                                        </div>-->
                            <br /> 
                            <p:panel id="pnlFunnel">
                                <div id="funnelPanel" style="text-align:center">
                                    <div id="funnelContainer">
                                        <div id="funnel"></div>
                                    </div>
                                </div>
                            </p:panel>

                        </div>
                        <div class="ui-sm-12 ui-md-2 ui-lg-2" >
                            <p:dataTable id="typeDT" value="#{lookupService.userList}" widgetVar="dtlUsersLookup" var="user" 
                                         filteredValue="#{lookupFilters.users}" scrollHeight="500" scrollable="true" tableStyle="table-layout:auto"
                                         selection="#{funnelReport.users}"  rowSelectMode="add"
                                         rowKey="#{user.userId}"   >
                                <p:column  selectionMode="multiple" />
                                <p:column  filterMatchMode="contains" filterBy="#{user.userName}" headerText="User" sortBy="#{user.userName}" >
                                    #{user.userName}      
                                </p:column>         
                            </p:dataTable>

                            <!--                            <p:panelGrid  columns="1" layout="grid" styleClass="box-primary no-border ui-fluid">
                                                            <h:panelGroup class="ui-inputgroup">
                                                            <h:panelGroup class="ui-inputgroup">
                                                                <p:outputLabel value="Users" /> <p:spacer width="6px"/>
                                                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select User" actionListener="#{lookupService.list('USER')}" update=":frmUsersLookup" oncomplete="PF('dlgUsersLookup').show();" />
                                                            </h:panelGroup>
                            
                                                            <h:selectOneListbox styleClass="sel-users"   size="10" style="width: 100%" >
                                                                <f:selectItems value="#{lookupService.users}" var="user"  
                                                                               itemValue="#{user.userId}"
                                                                               itemLabel="#{user.userName}"  />
                                                            </h:selectOneListbox>
                                                        </p:panelGrid>-->
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                            <p:dataTable  id="teamDT" value="#{lookupService.salesTeamList}" widgetVar="dtlSalesTeamsLookup" var="salesTeam" 
                                          filteredValue="#{lookupFilters.salesteams}"  scrollHeight="500" scrollable="true" tableStyle="table-layout:auto"
                                          selection="#{funnelReport.salesTeams}"  rowSelectMode="add" 
                                          rowKey="#{salesTeam.smanId}"   >
                                <p:column  selectionMode="multiple" />
                                <p:column  filterMatchMode="contains" filterBy="#{salesTeam.smanName}" headerText="#{custom.labels.get('IDS_SALES_TEAM')}" sortBy="#{salesTeam.smanName}">
                                    #{salesTeam.smanName}      
                                </p:column>         
                            </p:dataTable>
                            <!--                            <p:panelGrid  columns="1" layout="grid" styleClass="box-primary no-border ui-fluid">
                                                            <h:panelGroup class="ui-inputgroup">
                                                                <p:outputLabel value="Sales Teams" /><p:spacer width="6px"/>
                            
                                                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.list('SALES_TEAM')}" update=":frmSalesTeamLookup" oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                                            </h:panelGroup>
                                                            <h:selectOneListbox styleClass="sel-salesteam"  size="10" style="width: 100%" id="selSalesTeam">
                                                                <f:selectItems value="#{lookupService.salesTeams}" var="team"  
                                                                               itemValue="#{team.smanId}"
                                                                               itemLabel="#{team.smanName}"  />
                                                            </h:selectOneListbox>
                            
                                                        </p:panelGrid>-->
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                            <p:dataTable id="companiesLookUp" value="#{viewCompLookupService.companies}" widgetVar="dlgCompLookup" var="comp"  filteredValue="#{lookupFilters.companies}"
                                         selection="#{funnelReport.principals}"  rowSelectMode="add" scrollHeight="500" scrollable="true" tableStyle="table-layout:auto"
                                         rowKey="#{comp.compId}" >
                                <p:column  selectionMode="multiple" />
                                <p:column id="name"  filterMatchMode="contains" filterBy="#{comp.compName}" headerText="#{custom.labels.get('IDS_PRINCI')}" sortBy="#{comp.compName}"    >
                                    #{comp.compName}
                                </p:column>  
                            </p:dataTable>
                            <!--                            <p:panelGrid  columns="1" layout="grid" styleClass="box-primary no-border ui-fluid">
                                                            <h:panelGroup class="ui-inputgroup">
                                                                <p:outputLabel value="Principals" /><p:spacer width="6px"/>
                                                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select Company"
                                                                                 actionListener="#{viewCompLookupService.listCRM(1, 'applyPrincis')}" oncomplete="PF('lookupComps').show()"
                                                                                 update=":formCompsLookup" />
                                                            </h:panelGroup>
                                                            <h:selectOneListbox  size="10" style="width: 100%!important;" styleClass="sel-princi" id="selprinci"   >
                                                                <f:selectItems value="#{example.principals}" var="comp"  
                                                                               itemValue="#{comp.compId}"
                                                                               itemLabel="#{comp.compName}"  />
                            
                                                            </h:selectOneListbox>
                            
                                                        </p:panelGrid>-->
                        </div>
                        <!--                        <div class="ui-sm-12 ui-md-2 ui-lg-2" >
                                                    <p:dataTable id="regionsLookUp" value="#{lookupService.regionList}" widgetVar="dlgRegionLookup" var="region"  filteredValue="#{lookupFilters.companies}"
                                                                 selection="#{funnelReport.regions}"  rowSelectMode="add" 
                                                                 styleClass="reg-lookup" 
                                                                 rowKey="#{region.recId}" >
                                                        <p:column  selectionMode="multiple" style="width: 20%;text-align: center" />
                                                        <p:column id="name"  filterMatchMode="contains" filterBy="#{region.compRegion}" headerText="Region" sortBy="#{region.compRegion}"    >
                        #{region.compRegion}
                    </p:column>  
                </p:dataTable>
            </div>-->
                        <!--                        <div class="ui-sm-12 ui-md-1 ui-lg-1" >
                                                    <p:commandButton value="Run Report" actionListener="#{funnelReport.runReport()}" styleClass="btn btn-primary btn-xs" update=":formFunnel:pnlFunnel" /> 
                                                </div>-->
                    </div>
                </div>
            </div>

        </h:form>
        <script src="http://d3js.org/d3.v3.min.js"></script>
        <script src="https://cdn.rawgit.com/jakezatecky/d3-funnel/master/dist/d3-funnel.js?1"></script>
        <script>
                        function callFunnelReport(repType) {
                            var reportType = [
                                {name: 'report_type', value: repType}
                            ];
                            report(reportType);
                            
                        }

                        function runReport(obj) {
                            console.log(obj);
//                console.log($('#rptData').val());
//                var data = [
//                ["Lead", 6000],
//                ["Prospect", 3500],
//                ["Opps", 200],
//                ["Won", 150]
//            ];
//            console.log(data);
                            var width = $("#funnelPanel").width();
                            options.width = width;
                            var funnel = new D3Funnel('#funnelContainer');
                            funnel.draw(obj, options);
                        }
//            var data = [
//                ["Lead", 5000, "5,000"],
//                ["Prospect", 2500, "2,500"],
//                ["Opps", 500, "2,500"],
//                ["Won", 150, "50"]
//            ];
//            console.log(data);

                        width = $('#funnelPanel').width();


                        var options =
                                {
                                    chart: {
                                        width: 400,
                                        height: 400,
                                        bottomWidth: 1 / 3,
                                        bottomPinch: 1,
                                        inverted: false,
                                        horizontal: false,
                                        animate: 0,
                                        curve: {
                                            enabled: false,
                                            height: 20,
                                            shade: -0.4
                                        },
                                        tooltip: {
                                            enabled: true,
                                            format: '{l}: ${f}'
                                        }
                                    }
                                };


                        D3Funnel.defaults.block.dynamicHeight = false;

//            var funnel = new D3Funnel('#funnelContainer');
//            funnel.draw(data, options);

                        $(window).on("resize", function() {
                            var width = $("#funnelPanel").width();
                            options.width = width;
                            var funnel = new D3Funnel('#funnelContainer');
                            funnel.draw(data, options);
                        });


                        /* var options = {
                         
                         chart: {
                         width: 350,
                         height: 400,
                         bottomWidth: 1 / 3,
                         bottomPinch: 0,
                         inverted: false,
                         horizontal: false,
                         animate: 0,
                         curve: {
                         enabled: false,
                         height: 20,
                         shade: -0.4,
                         },
                         totalCount: null,
                         },
                         block: {
                         dynamicHeight: false,
                         dynamicSlope: false,
                         barOverlay: false,
                         fill: {
                         scale: scaleOrdinal(schemeCategory10).domain(range(0, 10)),
                         type: 'solid',
                         },
                         minHeight: 0,
                         highlight: false,
                         },
                         label: {
                         enabled: true,
                         fontFamily: null,
                         fontSize: '14px',
                         fill: '#fff',
                         format: '{l}: {f}',
                         },
                         tooltip: {
                         enabled: false,
                         format: '{l}: {f}',
                         },
                         events: {
                         click: {
                         block: null,
                         },
                         },
                         
                         };
                         
                         */


        </script>
        <style>
            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{
                font-size: 0.75em!important;
                padding: 0px 10px !important;
            }

            .ui-fluid .ui-button {
                width:20%!important;
            }

        </style>
    </ui:define>
</ui:composition>
