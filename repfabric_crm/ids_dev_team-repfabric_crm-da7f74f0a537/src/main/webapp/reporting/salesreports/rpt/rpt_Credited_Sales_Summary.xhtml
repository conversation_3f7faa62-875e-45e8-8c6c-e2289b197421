<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: CustomerSummary.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

                 <!--#363:page title-->
<ui:define name="head">
<link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
<!-- 02/08/2021       Feature #5245 Rename menu or report name-->
<title> Credit Sales Summary   </title>
</ui:define>
    <ui:define name="meta">
        <f:metadata> 

            <f:viewParam name="yr" value="#{reports.repYear}" />
            <f:viewParam name="mt" value="#{reports.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="d" value="#{reportFilters.distributor.compId}"/>
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>
            <f:viewParam name="c" value="#{reportFilters.customer.compId}"/>
            <f:viewAction action="#{creditedSalesRep.run()}"/>
        </f:metadata>
    </ui:define>

    <ui:define name="body">
        <h:form  id="creditedSalesSummary">


            <f:event listener="#{creditedSalesRep.isPageAccessible}" type="preRenderView"/>  
            <!--#3153 Sales Reports - Add gif for loading-->

            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                            left: 600px;
                            top: 300px;
                            width: 150px;
                            height: 150px;
                            z-index: 9999; opacity: .5 !important; " />

            <p:dialog id="dlgPriCustDet" position="center"   resizable="true" closeOnEscape="true"   
                      height="#{reports.repType eq 0? 200:250}" width="#{reports.repType eq 0? 750:800}" widgetVar="dlgPriCustDirSal">
                <f:facet name="header"  id="dlgheaderNm">
                    <h:outputLabel id="dlgHeader" value="#{creditedSalesRep.dlgNm}"  style="font:bold" />

                </f:facet>
                <!--             <h:form id="dlgPrimCustDtls" >-->

  
<!--Feature #3686:    CRM-4002: CREDITED SALES SUMMARY REPORT make columns sortable-->
                <p:dataTable id="rpPriCedit" value="#{creditedSalesRep.priCustDet}" var="pricust" 
                             emptyMessage="No data found" >

                    <p:column headerText="Inv Date" width="90"  sortBy="#{pricust.invDate}">
                        <h:outputLabel value="#{pricust.invDate}" >
                        </h:outputLabel>
                    </p:column>
                    <p:column headerText="Inv Number" width="70"  sortBy="#{pricust.inNumber}" >
                        <h:outputLabel value="#{pricust.inNumber}" style="float:right">
                        </h:outputLabel>
                    </p:column>
                    <p:column headerText="Part No"  width="70"  sortBy="#{pricust.partNo}"  >
                        <h:outputLabel value="#{pricust.partNo}"  style="float:right">

                        </h:outputLabel>
                    </p:column>

                    <p:column headerText="Qty"  width="70"  sortBy="#{pricust.quantity}">
                        <h:outputLabel value="#{pricust.quantity}"  style="float:right">

                        </h:outputLabel>
                    </p:column>

                    <p:column headerText="Unit Price"  width="70" sortBy="#{pricust.unitPrice}">
                        <h:outputLabel value="#{pricust.unitPrice}"  style="float:right">
                            <f:convertNumber minFractionDigits="2"/>
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel value="Total :" style="float: right"  class="footerFont" >


                            </h:outputLabel>

                        </f:facet>    

                    </p:column>
                    <p:column headerText="Total Sales"  sortBy="#{pricust.dlgTotprice}" width="70">
                        <h:outputLabel value="#{pricust.dlgTotprice}"  style="float:right">
                            <f:convertNumber minFractionDigits="2"/>
                        </h:outputLabel>

                        <f:facet name="footer">
                            <h:outputLabel value="#{creditedSalesRep.custTotal}" style="float: right"  class="footerFont" >

                                <f:convertNumber minFractionDigits="2"/>
                            </h:outputLabel>

                        </f:facet>

                    </p:column>

                </p:dataTable>
                <!--</h:form>-->
            </p:dialog>
            <!--#3153 Sales Reports - Add gif for loading-->


            <p:dialog header="Please wait" draggable="false"  widgetVar="dlgcreditedaj" closable="false" resizable="false" modal="true">
                <h:graphicImage  library="images" name="ajax-loader.gif" />
                <p:spacer width="5"/>
                <h:outputLabel value="Loading data.."/>
            </p:dialog>

            <h:panelGroup id="tbldata">


                <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7" id="credSalSumrpt" >  
                    <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="creditedSalesSummary:inputCustomer" />
                    <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update="creditedSalesSummary:inputDistriName" />
                    <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="creditedSalesSummary:inputPrincName" />
                    <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="creditedSalesSummary:inputAllSalesTeam" />
                    <div class="ui-g ui-fluid">
                        <div class="ui-sm-12 ui-md-3 ui-lg-3" >

<!--        Feature #5245 Rename menu or report name-->
                            <div class="compNamebg">
                                <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                                <br/>
                                Credit Sales Summary 
                                <br/>

                            </div>
                            <b>&nbsp;&nbsp;From:</b> January #{reports.repYear}
                            <br/>
                            <b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;To: </b> #{reportFilters.getMonth(reports.repMonth)}  #{reports.repYear}

                        </div>
                        <div class="ui-sm-12 ui-md-5 ui-lg-5" style="margin-left:60px" >


                        </div>  
                        <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:-60px">
                            <p:panelGrid id="grid" columns="2" 
                                         style="float: right;width: 350px;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                                <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                                <h:panelGroup class="ui-inputgroup"  >
            <!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                    <!--                                    <p:spacer width="50px"/>-->
                                    <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"  style="margin-left:-70px"/>
                                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                      actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                      update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                      styleClass="btn-info btn-xs" />
                                </h:panelGroup>
                                <p:outputLabel for="inputDistriName" value="#{custom.labels.get('IDS_DISTRI')}"  />
                                <h:panelGroup class="ui-inputgroup"  >
                                    <p:inputText id="inputDistriName" value="#{reportFilters.distributor.compName}"  placeholder="All" readonly="true" style="margin-left:-70px"/>
                                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                      actionListener="#{viewCompLookupService.listAll('applyDistri', 3, 0, 0)}"
                                                      update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                      styleClass="btn-info btn-xs" />
                                </h:panelGroup>
                                <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                                <h:panelGroup class="ui-inputgroup"  >
                                    <!--#3473:     Bug sales team - Sales team data we can enter manually-->
                                    <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All"  style="margin-left:-70px"  readonly="true"/>
                                    <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_SALES_TEAM')}" immediate="true" 
                                                      actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                      update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  style="width:5px"
                                                      styleClass="btn-info btn-xs" />
                                </h:panelGroup>

                                <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                                <h:panelGroup class="ui-inputgroup"  >

                                    <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true" style="margin-left:-70px"/>
                                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                      actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                      update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                      styleClass="btn-info btn-xs" />
                                </h:panelGroup>


                                <!--#3116:    Task - Customer Summary - Add gif for loading-->
                                <p:commandButton  onclick="processGif();"  value="View Report "  id="viewreport"      styleClass="btn btn-primary btn-xs"  style="width:30px"  actionListener="#{creditedSalesRep.runProcRep()}" />
                             
                                <!--#3400: Task :Sales Reports > Credited Sales Summary > Add export option start -->
                                <!--        Feature #5245 Rename menu or report name-->
                                <p:column>
                                    <p:commandLink id="xls" ajax="false" onclick="PrimeFaces.monitorDownload(start, stop);"  >  
                                        <p:graphicImage name="/images/excel.png" width="24"/>
                                        <!--<f:setPropertyActionListener value="false" target="#{exporterController.customExporter}"   />-->  
                                        <pe:exporter type="xlsx" target="creditedsalesum"  fileName="Credit Sales Summary" subTable="true"  postProcessor="#{creditedSalesRep.postProcessXLS}" />  

                                    </p:commandLink>  

                                    <p:commandLink id="pdf" ajax="false"  onclick="PrimeFaces.monitorDownload(start, stop);"    >  
                                        <p:graphicImage value="/resources/images/pdf.png"  width="24" />  
                                        <!--<f:setPropertyActionListener value="false" target="#{repDetailItem.name}"/>-->  
                                        <pe:exporter type="pdf" target="creditedsalesum" fileName="Credit Sales Summary" subTable="true"     preProcessor="#{creditedSalesRep.preProcessPDF}"   />  

                                    </p:commandLink>
                                </p:column>
                                <!--#3400: Task :Sales Reports > Credited Sales Summary > Add export option end -->
                            </p:panelGrid>    
                        </div></div>



                    <p:dataList  type="definition" id="credDl" value="#{creditedSalesRep.listCreditedSalesReps}" var="credited"  paginator="false"  paginatorAlwaysVisible="false"> 

                        <p:column >
                            <div class="div-selected" style="height:25px;width: 100%;" >
                                <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                                    <!--#3153 Sales Reports - Add gif for loading-->
                                    <p:commandLink process="@this"  style="text-decoration: underline" onstart="PF('dlgcreditedaj').show()"
                                                   value="#{credited.custName}" actionListener="#{creditedSalesRep.setdilog(credited.custName)}" 
                                                   action="#{creditedSalesRep.getPriCustDetails(salesReports.repSessionId ,credited.priCustId )}"
                                                   oncomplete ="PF('dlgPriCustDirSal').show();PF('dlgcreditedaj').hide();"  update=":creditedSalesSummary:dlgPriCustDet"
                                                   >   

                                    </p:commandLink> 
                                </label>               
                            </div>
                            <p:dataTable  value="#{credited.details}" var="crd"  >
                                <!--                                                                            <p:column  style="width: 140px"
                                                                                                                       headerText="Primary #{custom.labels.get('IDS_CUSTOMER')}" class="col_left" sortBy="#{credited.priCustId}" >
                                                                                
                                                                                
                                                                                                                <p:commandLink process="@this" 
                                                                                                                               value="#{crd.priCustName}" 
                                                                                                                               action="#{creditedSalesRep.getPriCustDetails(salesReports.repSessionId ,crd.priCustId )}"
                                                                                                                                 oncomplete ="PF('dlgPriCustDirSal').show()"
                                                                                                                               >   
                                                                                
                                                                                                                </p:commandLink>      
                                                                                
                                                                                
                                                                                                            </p:column>     -->

                                <p:column    sortBy="#{crd.princiName}" headerText="#{custom.labels.get('IDS_PRINCI')}" style="width: 140px "
                                             >
                                    <p:outputLabel value="#{crd.princiName}" />
                                </p:column>
                                <p:column  style="width: 140px"
                                           headerText="#{custom.labels.get('IDS_DISTRI')}" class="col_left" sortBy="#{crd.distiName}"  >
                                    <p:outputLabel value="#{crd.distiName}"  />
                                </p:column>
                                <p:column  style="width: 150px"
                                           headerText="Primary #{custom.labels.get('IDS_SALES_TEAM')}" class="col_left" sortBy="#{crd.steamName}">
                                    <p:outputLabel value="#{crd.steamName}"  /> 
                                </p:column>  
                                <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                <p:column  style="width: 150px" 
                                           headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" class="col_left" sortBy="#{crd.secCustName}"> 
                                    <p:outputLabel value=" #{crd.secCustName}"  /> 
                                </p:column>   
                                <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                <p:column  style="width: 170px" 
                                           headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')} #{custom.labels.get('IDS_SALES_TEAM')}" class="col_left" sortBy="#{crd.secSmanName}"> 
                                    <p:outputLabel value=" #{crd.secSmanName}"  /> 

                                    <f:facet name="footer" >
                                        <h:outputLabel value="Total :" style="float: right"   class="footerFont">

                                        </h:outputLabel>
                                    </f:facet>

                                </p:column>  
                                <p:column  style="width: 140px ; text-align: right;" 
                                           headerText="Direct Sales"  sortBy="#{crd.priDirect}"> 
                                    <p:outputLabel value=" #{crd.priDirect} "  style="text-align:right" /> 
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{credited.totals[1]}" style="float: right"   class="footerFont">

                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>

                                    </f:facet>


                                </p:column> 

                                <p:column  style="width: 140px;text-align: right" 
                                           headerText="Credited Sales"   sortBy="#{crd.priCredited}"> 
                                    <p:outputLabel value="  #{crd.priCredited} " /> 
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{credited.totals[2]}" style="float: right"   class="footerFont">

                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>

                                    </f:facet>  


                                </p:column>  
                                <p:column  style="width: 140px;text-align:right" 
                                           headerText="Total sales"  sortBy="#{crd.priCredited}"> 
                                    <p:outputLabel value="  #{crd.totprice} "  /> 

                                    <f:facet name="footer">
                                        <h:outputLabel value="#{credited.totals[3]}" style="float: right"  class="footerFont" >

                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>

                                    </f:facet>   
                                </p:column> 

                            </p:dataTable>
                        </p:column>
                    </p:dataList>
                    <p:dataTable  emptyMessage=" " id="grandtotals" style="margin-top:-20px">

                        <p:column  style="width: 140px">

                        </p:column >
                        <p:column  style="width: 140px">

                        </p:column>
                        <p:column style="width: 150px" >

                        </p:column>             
                        <p:column style="width: 150px" >

                        </p:column> 


                        <p:column style="width: 170px" >



                            <f:facet name="footer" >
                                <h:outputLabel value="Grand Total :" style="float: right" rendered="#{ commReports.repGrp ne 2}"  class="footerFont">

                                </h:outputLabel>
                            </f:facet>

                        </p:column>  
                        <p:column  style="width: 140px"> 

                            <f:facet name="footer">
                                <h:outputLabel value="#{creditedSalesRep.totpriDirect}" style="float: right"  class="footerFont" >

                                    <f:convertNumber minFractionDigits="2"/>
                                </h:outputLabel>

                            </f:facet>


                        </p:column> 

                        <p:column  style="width: 140px"  > 

                            <f:facet name="footer">
                                <h:outputLabel value="#{creditedSalesRep.totpriCredited}" style="float: right"  class="footerFont" >

                                    <f:convertNumber minFractionDigits="2"/>
                                </h:outputLabel>

                            </f:facet>  


                        </p:column>  
                        <p:column  style="width: 140px"  > 


                            <f:facet name="footer">
                                <h:outputLabel value="#{creditedSalesRep.totalprice}" style="float: right"  class="footerFont" >

                                    <f:convertNumber minFractionDigits="2"/>
                                </h:outputLabel>

                            </f:facet>   
                        </p:column> 

                    </p:dataTable>




<!--#3400: Task :Sales Reports > Credited Sales Summary > Add export option start -->

<p:dataTable value="#{creditedSalesRep.listCreditedSalesReps}" var="credited"  id="creditedsalesum" rendered="false">

                        <f:facet  name="header"  >


                        </f:facet>

                        <p:columnGroup  type="header" class="header"  rendered="#{reportFilters.grp ne 2 || reportFilters.grp ne 1 }" >
                            <p:row>

                                <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                <p:column />
                              <!--        Feature #5245 Rename menu or report name-->
                                <p:column headerText="Credit Sales Summary" />
                                <p:column/>
                                <p:column/>


                                <p:column headerText="From: January #{reports.repYear}" />

                                <p:column />
                                <p:column headerText="To: #{reportFilters.getMonth(reports.repMonth)}  #{reports.repYear}" />

                            </p:row>




                            <p:row></p:row>


                            <p:row>

                                <p:column     headerText="#{custom.labels.get('IDS_PRINCI')}" style="width: 140px "
                                              >

                                </p:column>
                                <p:column  style="width: 140px"
                                           headerText="#{custom.labels.get('IDS_DISTRI')}" class="col_left"   >

                                </p:column>
                                <p:column  style="width: 150px"
                                           headerText="Primary #{custom.labels.get('IDS_SALES_TEAM')}" class="col_left" >

                                </p:column>  
                                <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                <p:column  style="width: 150px" 
                                           headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" class="col_left" > 
                                    <p:outputLabel value=" #{crd.secCustName}"  /> 
                                </p:column>   
                                <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                <p:column  style="width: 170px" 
                                           headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')} #{custom.labels.get('IDS_SALES_TEAM')}" class="col_left" sortBy="#{crd.secSmanName}"> 

                                </p:column>  
                                <p:column  style="width: 140px ; text-align: right;" 
                                           headerText="Direct Sales"  > 

                                </p:column> 

                                <p:column  style="width: 140px;text-align: right" 
                                           headerText="Credited Sales"   > 

                                </p:column>  
                                <p:column  style="width: 140px;text-align:right" 
                                           headerText="Total sales" > 

                                </p:column>   

                            </p:row>
                        </p:columnGroup>


                        <p:subTable  value="#{credited.details}" var="crd"  >
                            <f:facet name="header" >
                                 <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                <p:outputLabel  value="#{custom.labels.get('IDS_DB_CUSTOMER')}" /> : <p:outputLabel  value="#{credited.custName}" />

                            </f:facet>
                            <p:column   style="width: 140px" >
                                <h:outputText value="#{crd.princiName}" />
                            </p:column>
                            <p:column  style="width: 140px"
                                       >
                                <h:outputText value="#{crd.distiName}"  />
                            </p:column>
                            <p:column  style="width: 150px"
                                       >
                                <h:outputText value="#{crd.steamName}"  /> 
                            </p:column>  
                        
                            <p:column  style="width: 150px" 
                                       > 
                                <h:outputText value=" #{crd.secCustName}"  /> 
                            </p:column>   
                          
                            <p:column  style="width: 170px" 
                                       > 
                                <h:outputText value=" #{crd.secSmanName}"  /> 


                            </p:column>  
                            <p:column  style="width: 140px ; text-align: right;" 
                                       > 
                                <h:outputText value=" #{crd.priDirect} "  style="text-align:right" /> 




                            </p:column> 

                            <p:column  style="width: 140px;text-align: right" 
                                       > 
                                <h:outputText value="  #{crd.priCredited} " /> 



                            </p:column>  
                            <p:column  style="width: 140px;text-align:right" 
                                       > 
                                <h:outputText value="  #{crd.totprice} "  /> 


                            </p:column> 

                            <p:columnGroup type="footer">
                                <p:row> 

                                    <p:column  />
                                    <p:column  />
                                    <p:column  />
                                    <p:column  /> 


                                    <p:column footerText=" Total :"
                                              style="float: right"  class="footerFont"/>

                                    <p:column footerText="#{credited.totals[1]}"  style="text-align: right"/>
                                    <p:column footerText="#{credited.totals[2]}"  style="text-align: right" />
                                    <p:column footerText="#{credited.totals[3]}"  style="text-align: right" />


                                </p:row>
                            </p:columnGroup>  


                        </p:subTable>

                        <p:columnGroup type="footer">
                            <p:row> 

                                <p:column  />
                                <p:column  />
                                <p:column  />
                                <p:column  /> 


                                <p:column footerText=" Grand Total :"
                                          style="float: right"  class="footerFont"/>

                                <p:column footerText="#{creditedSalesRep.totpriDirect}"  style="text-align: right"/>
                                <p:column footerText="#{creditedSalesRep.totpriCredited}"  style="text-align: right" />
                                <p:column footerText="#{creditedSalesRep.totalprice}"  style="text-align: right" />


                            </p:row>
                        </p:columnGroup> 
                    </p:dataTable>




<!--#3400: Task :Sales Reports > Credited Sales Summary > Add export option end -->







                </div>
            </h:panelGroup>  



            <!--#3116:    Task - Customer Summary - Add gif for loading start-->
            <script>
                window.onload = function () {
                    //                $('#loading-image').show();
                    setTimeout(function () {
                        var t = performance.timing;
                        console.log(t.loadEventEnd - t.responseEnd);
                        //      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                        document.getElementById("credSalSumrpt").style.pointerEvents = "all";
                        document.getElementById("credSalSumrpt").style.opacity = "1";

                        $('#creditedSalesSummary\\:loading-image').hide();
                        //            $('#prodrpt').css({pointer - events:"all"});
                    }, 0);
                };

                function processGif()
                {

                    document.getElementById("credSalSumrpt").style.pointerEvents = "none";
                    document.getElementById("credSalSumrpt").style.opacity = "0.7";
                    $('#creditedSalesSummary\\:loading-image').show();
                }


                function start() {

                    PF('inCreditedProgressDlg').show();
                }

                function stop() {
                    PF('inCreditedProgressDlg').hide();
                }


            </script>
            <!--#3116:    Task - Customer Summary - Add gif for loading end-->

        </h:form>

<!--#3400: Task :Sales Reports > Credited Sales Summary > Add export option start -->
        <p:dialog widgetVar="inCreditedProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
            <h:form>
                <p:outputPanel >
                    <br />
                    <h:graphicImage  library="images" name="ajax-loader.gif"   />
                    <p:spacer width="5" />
                    <p:outputLabel value="Please wait...Exporting records" />
                    <br /><br />
                    <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                </p:outputPanel>
            </h:form>
        </p:dialog>
<!--#3400: Task :Sales Reports > Credited Sales Summary > Add export option start -->

        <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
        <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />

        <style>

            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }


            .jqplot-target {

                font-size: 0.75em!important;
            }

            /*            .ui-datatable-data .ui-widget-content
                        {
                            display: none;
                        }*/

        </style>


    </ui:define>

</ui:composition>