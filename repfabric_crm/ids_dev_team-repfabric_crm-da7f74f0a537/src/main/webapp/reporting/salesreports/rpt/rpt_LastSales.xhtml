<?xml version="1.0" encoding="UTF-8"?>
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: rpt_LastSales.xhtml
// Author: poornimas
//*********************************************************
/*
*  //#6502: Logix Sales last 4 years report,
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"                
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">>
    
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title> Last Sales </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>      
           <f:viewParam name="c" value="#{reportFilters.custselect.compId}"/>         
           <f:viewParam name="cn" value="#{reportFilters.custselect.compName}"/>
           <f:viewAction action="#{lastSalesRep.run()}"/>
        </f:metadata>
    </ui:define>
    
    <ui:define  name="body">       
        <f:event listener="#{lastSalesRep.isPageAccessible}" type="preRenderView"/> 
        
         <!--#3084:TaskProduct Details Report - Add gif for loading-->
        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />       
        
        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7" id="lastSalesRpt" >
            <h:form  id="lastSales"> 
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >

                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            Customer: #{lastSalesRep.compname}
                            <br/>
                            Last Sales Date for #{custom.labels.get('IDS_PRINCIS')}
                        </div>
                    </div>
                </div>
                
                <div class="col-md-12">                   
                    <p:dataTable style="margin-bottom: 20px"  value="#{lastSalesRep.lstSaleList}"
                                 var="detail" id="lastSalesRepdt">
                        <p:column style="width: 120px" sortBy="#{detail.principal}"
                                  headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left">
                            <p:outputLabel value="#{detail.principal}"  title="#{detail.principal}" />
                        </p:column>
                        <p:column style="width: 120px" sortBy="#{detail.lastSaleDate}" headerText="Last Sales Date" class="col_left">
                            <p:outputLabel value="#{globalParams.formatDateTime((detail.lastSaleDate),'da')}" />
                                
                        </p:column>
                        <p:column style="width: 120px"
                                  headerText="Products" class="col_left"> 
                            <p:commandLink process="@this" actionListener="#{partDetails.showLastSaleProductDetail(lastSalesRep.custId,detail.princiId,detail.principal)}" 
                                           value="Show" update="formProduct:dlgproduct"
                                           oncomplete="PF('productDlg').show();" style="text-decoration: underline;color: blue" />
                        </p:column>
                    </p:dataTable>
                    
                </div>
                   
            </h:form>
            
             <style>

                .ui-panelgrid .ui-panelgrid-cell {
                    padding-top: 3px !important;
                    padding-bottom: 3px !important;
                }

                [role="gridcell"]{

                    padding: 5px 10px !important;
                }
                .content-header{     display: none; }


                .jqplot-target {

                    font-size: 0.75em!important;
                }
            </style>
            
            <script>
                window.onload = function () {
                    setTimeout(function () {
                        var t = performance.timing;
                        console.log(t.loadEventEnd - t.responseEnd);   
                        document.getElementById("lastSalesRpt").style.pointerEvents = "all";
                        document.getElementById("lastSalesRpt").style.opacity = "1";

                        $('#loading-image').hide();   
                    }, 0);
                };


                function processGif()
                {
                    $('#loading-image').show();
                    document.getElementById("lastSalesRpt").style.pointerEvents = "none";
                    document.getElementById("lastSalesRpt").style.opacity = "0.7";
                }

                function hideGif()
                {

                    $('#loading-image').hide();

                    document.getElementById("lastSalesRpt").style.pointerEvents = "all";
                    document.getElementById("lastSalesRpt").style.opacity = "1";
                }

            </script>
            
            <ui:include src="ProductPartDetailsDlg.xhtml"/>
            
        </div>       
        
    </ui:define>

</ui:composition>
