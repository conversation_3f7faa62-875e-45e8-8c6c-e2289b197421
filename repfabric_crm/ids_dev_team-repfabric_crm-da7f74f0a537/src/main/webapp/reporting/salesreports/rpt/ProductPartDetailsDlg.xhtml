<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!--   //#6502: Logix Sales last 4 years report- poornima -->
<!DOCTYPE html>
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    
    
    <h:form id="formProduct">
        <p:dialog id="dlgproduct" widgetVar="productDlg"  header="#{partDetails.headerTitle}" modal="true" resizable="false" responsive="true" position="center" width="540px" draggable="false">

                <p:dataTable style="width: 500px"  scrollable="true" scrollHeight="250" 
                             id="tblLastSalesProduct" value="#{partDetails.listPartItem}" var="part">
                    <p:column headerText="Part No." width="100" sortBy="#{part.partNo}"> 
                        <p:outputLabel value="#{part.partNo}" />
                    </p:column>
                    <p:column headerText="Last Sale Date" width="50" sortBy="#{part.lastSaleDate}">
                        <p:outputLabel value="#{globalParams.formatDateTime((part.lastSaleDate),'da')}" />
                    </p:column>
                </p:dataTable>

        </p:dialog>          
    </h:form>
</ui:composition>
