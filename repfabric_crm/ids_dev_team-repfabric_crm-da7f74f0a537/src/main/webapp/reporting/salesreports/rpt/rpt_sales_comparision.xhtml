<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: CustomerSummary.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

    <!--#363:page title-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
       
        <title>     Sales Comparison  </title>
    </ui:define>
    <!--                        // Feature #5739 Add export options grouped by customer and detailed by principals-->
    <ui:define name="meta">
        <f:metadata>
            <f:viewParam name="yr" value="#{salesComparisionRep.yr}" />
            <f:viewParam name="mt" value="#{salesComparisionRep.mt}" />
            <f:viewParam name="rG" value="#{salesComparisionRep.repGroup}" />
            <f:viewParam name="rL" value="#{salesComparisionRep.repRepLevel}" />
            <f:viewParam name="rDid" value="#{salesComparisionRep.repDrilDownid}" />
            <f:viewParam name="rDF" value="#{salesComparisionRep.repDrilDownField}" />
            <f:viewParam name="rtDid" value="#{salesComparisionRep.repTopDrilDownid}" />
            <f:viewParam name="rF" value="#{salesComparisionRep.repFor}" />
            <f:viewParam name="sDF" value="#{salesComparisionRep.checkFlag}"/>
            <!--//  #8515  CRM-5877: Aurora: Reports: add Region + prior year total (Sales Comparison)-->
            <f:viewParam name="ri" value="#{salesComparisionRep.compRegns}"/>
            <f:viewParam name="rn" value="#{salesComparisionRep.compRegNames}"/>
            <!--//#6161: CRM-4899: Omit non-active Manufacturers from Sales Comparison Report - poornima -->
            <f:viewParam name="ePrinci" value="#{salesComparisionRep.excldInactivFlg}" />
            <f:viewAction action="#{salesComparisionRep.getComparisonList()}" />            
            <f:viewAction action="#{salesComparisionRep.headerText(salesComparisionRep.repGroup)}" />
            <f:viewAction action="#{salesComparisionRep.checkAccessAndRun()}"/>   
        </f:metadata>
    </ui:define>
    <ui:define name="body">
        <f:event listener="#{salesComparisionRep.isPageAccessible}" type="preRenderView"/>  

        <!--#3153 Sales Reports - Add gif for loading-->

        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />
        <!--#3153 Sales Reports - Add gif for loading style changes-->


        <h:form id="salesCompReport">
            <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4" id="salComprpt" >

                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" style="float:left">


                        <div class="compNamebg" style="float:left" id="datehdr">
                            <h:outputLabel value="#{loginBean.subscriber_name} "   />
                            <br/>
                            Sales Comparison Report
                            <br/>

                        </div>
                        &nbsp;&nbsp;
                        <p:outputLabel value=" As of :"/> <p:outputLabel value="#{salesComparisionRep.yr}"  id="year"/> -  <p:outputLabel value="#{reportFilters.getMonth(salesComparisionRep.mt )}" id="month" />
<!--//  #8515  CRM-5877: Aurora: Reports: add Region + prior year total (Sales Comparison)-->
                      <div style="float:left;width:750px;margin-top:30px;" >
                          <p:outputLabel style="font-weight:normal" rendered="#{salesComparisionRep.compRegNames.length() > 0}" value="*Filtered for selected #{custom.labels.get('IDS_REGION')}"/>  
                    </div>
                    </div>
                    




                    <div class="ui-sm-12 ui-md-7 ui-lg-7"   >
                        <p:panelGrid   columns="2"  layout="grid" style="width: 80%;margin-left:24px"  id="gph"> 
                            <p:chart type="bar" model="#{reports.barChartYearly}"   style="width:285px;height:180px" widgetVar="yrgph" />
                            <p:chart type="bar" model="#{reports.barChartQuarterly}"   style="width:285px;height:180px" widgetVar="mtgph" />
                        </p:panelGrid>
                    </div>
                    <div class="ui-sm-12 ui-md-1 ui-lg-1"  style="width:15%;margin-left: -90px">
                        <h:panelGrid columns="1"  >
                            <p:outputLabel value="Group By" /> 


                            <p:selectOneRadio id="repg"  class="filterbox"  value="#{salesComparisionRep.repGroup}"   
                                              layout="pageDirection"  required="true">
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/> 
                                <!--                           Feature #5011 cleaning up End User terminology in Commission reports-->
                                <f:selectItem itemValue="3" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                                <f:selectItem itemValue="4" itemLabel="#{custom.labels.get('IDS_DISTRI')}"/>
                                <f:selectItem itemValue="5" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}"/> 
                                <!--                        // Feature #5739 Add export options grouped by customer and detailed by principals-->
                                <!--                                <p:ajax event="change"   update="salesCompReport:dtrpt  :salesCompReport:gph "/>-->
                                <!--                                <p:ajax onstart="processGif();" listener="#{salesComparisionRep.setVariables(salesComparisionRep.yr, salesComparisionRep.mt, salesComparisionRep.repGroup, 1, 0, 0, '',salesComparisionRep.repFor ,1)}"
                                                                        update="salesCompReport:dtrpt  :salesCompReport:gph"    oncomplete="hideGif();" />-->
                            </p:selectOneRadio>
                            <p:dialog header="Please wait" draggable="false"  widgetVar="dlgSalaj" modal="true" closable="false" resizable="false" >
                                <h:graphicImage  library="images" name="ajax-loader.gif" />
                                <p:spacer width="5"/>
                                <h:outputLabel value="Loading data.."/>
                            </p:dialog>

                        </h:panelGrid>
                    </div>
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" style="margin-left: -50px">
                        <h:panelGrid columns="2"  >
                            <p:outputLabel value="Month"/>                
                            <p:selectOneMenu widgetVar="mt" value="#{salesComparisionRep.mt}">
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 
                                <p:ajax event="change" listener="#{salesComparisionRep.enableRunRptFlag}" update=":salesCompReport:runReport  :salesCompReport:gph   " />
                            </p:selectOneMenu>

                            <p:outputLabel value="Year"/>                

                            <p:selectOneMenu widgetVar="yr" value="#{salesComparisionRep.yr}"  >
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />
                                <p:ajax event="change" listener="#{salesComparisionRep.enableRunRptFlag}" update="salesCompReport:runReport  :salesCompReport:gph" />
                            </p:selectOneMenu>   
                            <p:spacer />
                            <div></div>
                <!--            <p:commandButton value="Run Report" onclick="PF('dlgSalaj').show()"  id="runReport" action="#{salesComparisionRep.setVariables(salesComparisionRep.yr, salesComparisionRep.mt, salesComparisionRep.repGroup, 1, 0, 0, '')}" 
                                             styleClass="btn btn-primary btn-xs"   update=":salesCompReport:dtrpt :salesCompReport:gph"  disabled="#{!salesComparisionRep.runFlag}"  oncomplete="PF('dlgSalaj').hide()" />-->
                            <!--//     #2932:   Task CRM-1815: Fwd: Sales Comparison ReportsCRM-1815: function updates-->

                            <!--#315 :run report disabled-->
                            <!--//#6161: CRM-4899: Omit non-active Manufacturers from Sales Comparison Report - poornima 
                            //added inactive flag-->
                            <!--//  #8515  CRM-5877: Aurora: Reports: add Region + prior year total (Sales Comparison)-->
                            <p:commandButton value="Run Report" onclick="processGif()"  id="runReport"  actionListener="#{salesComparisionRep.goToNaviPage(salesComparisionRep.yr, salesComparisionRep.mt, salesComparisionRep.repGroup, 1, 0, 0, '',salesComparisionRep.repFor,salesComparisionRep.checkFlag,salesComparisionRep.excldInactivFlg,salesComparisionRep.compRegns,salesComparisionRep.compRegNames)}" 
                                             styleClass="btn btn-primary btn-xs"  oncomplete="hideGif()" update=":salesCompReport:dtrpt :salesCompReport:gph :salesCompReport:year :salesCompReport:month "     />
                            <!--//#3496:Task Sales Reports > Sales Comparison - Export option-->
                            <p:column>
                                <p:commandLink id="xls" ajax="false"  onclick="PrimeFaces.monitorDownload(start, stop);" >  
                                    <p:graphicImage name="/images/excel.png" width="24"/>
                                    <pe:exporter type="xlsx" target="salcompexport"   fileName=" Sales_Comparison_Report" subTable="false" postProcessor="#{salesComparisionRep.postProcessXLS}"  />  

                                </p:commandLink>  

                                <p:commandLink id="pdf" ajax="false">  
                                    <p:graphicImage value="/resources/images/pdf.png"  width="24" onclick="PrimeFaces.monitorDownload(start, stop);"/>  
                                    <!--Bug 656 : CRM-2620: RSTthermal:  Sales Comparison missing a total-->
                                    <pe:exporter type="pdf" target="salcompexport" fileName=" Sales_Comparison_Report" subTable="false"  preProcessor="#{monthlyExpandedRep.preProcessPDF}" />  

                                </p:commandLink>

                            </p:column>


                        </h:panelGrid>
                    </div>


                </div>

                <!--                        // Feature #5739 Add export options grouped by customer and detailed by principals-->
                <h:panelGroup id="tbldata">
                    <p:dataTable id="dtrpt"  value="#{salesComparisionRep.salesComprsnList}" var="salesCompr" paginatorAlwaysVisible="false" paginator="true"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rows="50"  emptyMessage="No data found"   rowStyleClass=" #{salesCompr.repRepLevel eq 1 and salesComparisionRep.checkFlag  ? 'colorr' : null}">
                        <!--//Bug 1164 -# 325040 : By Customer By month Sales Reports-->
                        <p:column  class="col_left" >
                            <f:facet name="header" id="name">
                                <p:outputLabel value="#{salesComparisionRep.header}" />
                            </f:facet>
                            <!--Bug #4009:CRM-4130: summitsales: sales comparison drill down, no progress circle-->
                            <h:commandLink disabled="#{salesComparisionRep.checkFlag}"   onclick="processGif();" >

                                <h:outputText style="#{salesCompr.repRepLevel eq -1 ? 'display:none':'display:inline'}"  value="#{salesCompr.repCompName}  "/>

                             
                                <p:ajax listener="#{salesComparisionRep.setVariablesLevel1(2, salesCompr.repCompId, salesCompr.repCompName)}"  
                                        update="salesCompReport:firstDrill"     oncomplete="PF('drillDown1').show();hideGif();" />
                            </h:commandLink>

                            <f:facet  name="footer">
                                <h:outputLabel   value="Total : "  style="float: right"  class="footerFont"/>
                            </f:facet>

                        </p:column>
                        <p:column rendered="#{salesComparisionRep.checkFlag}"   headerText="#{salesComparisionRep.repGroup == 1 ? custom.labels.get('IDS_DB_CUSTOMER') : custom.labels.get('IDS_PRINCI')}" style="text-align: right" sortBy="#{salesCompr.repMonthCurr}">
                            <h:outputLabel value="#{salesCompr.repCompName2}"></h:outputLabel>
                        </p:column>    

                        <p:column headerText="#{salesComparisionRep.monthCurr}" style="text-align: center" sortBy="#{salesCompr.repMonthCurr}">
                            <h:outputLabel value="#{salesCompr.repMonthCurr}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0 and  not salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.totals[1]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                                <h:outputLabel rendered="#{salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.showDetailsTotals[1]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>

                        <p:column headerText="#{salesComparisionRep.monthPrev}"  style="text-align: center" sortBy="#{salesCompr.repMonthPrev}" >
                            <h:outputLabel  value="#{salesCompr.repMonthPrev}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0 and  not salesComparisionRep.checkFlag }" 
                                               value="#{salesComparisionRep.totals[0]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                                <h:outputLabel rendered="#{salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.showDetailsTotals[0]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>

                        </p:column>


                        <!--//     #2932:   Task CRM-1815: Fwd: Sales Comparison ReportsCRM-1815:column header added-->
                        <p:column headerText="#{salesComparisionRep.yrCurr}"  style="text-align: right" sortBy="#{salesCompr.repYTDCurr}" width="170">
                            <h:outputLabel value="#{salesCompr.repYTDCurr}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0 and  not salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.totals[3]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                                <h:outputLabel rendered="#{salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.showDetailsTotals[3]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>

                        <p:column headerText="#{salesComparisionRep.yrPrev}"  style="text-align: right" sortBy="#{salesCompr.repYTDPrev}" width="170">
                            <h:outputLabel value="#{salesCompr.repYTDPrev}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0 and  not salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.totals[2]}"  style="float: right"  class="footerFont" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                                <h:outputLabel rendered="#{salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.showDetailsTotals[2]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                            </f:facet>
                        </p:column>




                        <p:column headerText="Jan-Dec #{salesComparisionRep.yr - 1}"  style="text-align: right" sortBy="#{salesCompr.repYearPrev}" width="140">
                            <h:outputLabel value="#{salesCompr.repYearPrev}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0 and  not salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.totals[4]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>            
                                </h:outputLabel>
                                <h:outputLabel rendered="#{salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.showDetailsTotals[4]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="YTD Var $"  style="text-align: right" sortBy="#{salesCompr.repYTDVarAmt}">
                            <h:outputLabel value="#{salesCompr.repYTDVarAmt}"  >
                                <f:convertNumber maxFractionDigits=""/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0 and  not salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.totals[5]}"  style="float: right"  class="footerFont" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                                <h:outputLabel rendered="#{salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.showDetailsTotals[5]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>

                        <!--2065 :CRM-2633: RST Thermal: Sales Comparison-->
                        <p:column headerText="Var %"  style="text-align: right" sortBy="#{salesCompr.repYTDVarPct}">
                            <h:outputLabel  value="#{salesCompr.repYTDVarPct}"    style="#{
                                                     salesCompr.repYTDVarPct gt 0 ?  'color:green' 
                                                     : (salesCompr.repYTDVarPct lt 0 ? 'color:red' : 'color:black')
                               }"  >
                                <f:convertNumber maxFractionDigits="2"/>
                            </h:outputLabel>
                            <!--Bug 656 : CRM-2620: RSTthermal:  Sales Comparison missing a total-->
                            <!--1624: CRM-3206: Sales comparison numbers incorrect-->
                            <!--2065 :CRM-2633: RST Thermal: Sales Comparison-->
                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0 and  not salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.calculateYTDVarPct(salesComparisionRep.totals[2], salesComparisionRep.totals[3])}"  style="float: right;#{
                                                        salesComparisionRep.calculateYTDVarPct(salesComparisionRep.totals[2], salesComparisionRep.totals[3]) gt 0 ?  'color:green' 
                                                        : (salesComparisionRep.calculateYTDVarPct(salesComparisionRep.totals[2], salesComparisionRep.totals[3]) lt 0 ? 'color:red' : 'color:black')
                               }" class="footerFont" >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <h:outputLabel rendered="#{salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.calculateYTDVarPct(salesComparisionRep.showDetailsTotals[2], salesComparisionRep.showDetailsTotals[3])}"  style="float: right;
                                               #{salesComparisionRep.calculateYTDVarPct(salesComparisionRep.showDetailsTotals[2], salesComparisionRep.showDetailsTotals[3]) gt 0 ?  'color:green' 
                                                 : (salesComparisionRep.calculateYTDVarPct(salesComparisionRep.showDetailsTotals[2], salesComparisionRep.showDetailsTotals[3]) lt 0 ? 'color:red' : 'color:black')}" class="footerFont">
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="YTD %"  style="text-align: right" sortBy="#{salesCompr.repYTDPCT}">
                            <h:outputLabel value="#{salesCompr.repYTDPCT}"  >
                                <f:convertNumber maxFractionDigits="2"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0 and  not salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.totals[7]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                                <h:outputLabel rendered="#{salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.showDetailsTotals[7]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="LYT %"  style="text-align: right" sortBy="#{salesCompr.repLYTPCT}">
                            <h:outputLabel value="#{salesCompr.repLYTPCT}"  >
                                <f:convertNumber maxFractionDigits="2"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0 and  not salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.totals[8]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                                <h:outputLabel rendered="#{salesComparisionRep.checkFlag}" 
                                               value="#{salesComparisionRep.showDetailsTotals[8]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>
                    </p:dataTable>


                    <!--export-->
                    <!--//#3496:Task Sales Reports > Sales Comparison - Export option-->
                    <!--                        // Feature #5739 Add export options grouped by customer and detailed by principals-->

                    <p:dataTable id="salcompexport"  value="#{salesComparisionRep.salesComprsnList}" var="salesCompr" paginatorAlwaysVisible="false" paginator="true"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rows="50" emptyMessage="No data found" rendered="false" >


                        <p:columnGroup  type="header" class="header"   >
                            <p:row>
                                <p:column rendered="#{salesComparisionRep.checkFlag}" />                                                                                           
                                <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                <p:column />
                                <p:column />
                                <p:column headerText=" Sales Comparison Report" />
                                <p:column/>
                                <p:column/>
                                <p:column/>

                                <p:column headerText="As of : #{salesComparisionRep.yr} - #{reportFilters.getMonth(salesComparisionRep.mt )} " />

                                <p:column />

                                <p:column />



                            </p:row>
                            <p:row></p:row>

                            <p:row>


                                <p:column   headerText="#{salesComparisionRep.header}" >

                                </p:column>

                                <p:column rendered="#{salesComparisionRep.checkFlag}" headerText="#{salesComparisionRep.repGroup == 1 ? custom.labels.get('IDS_DB_CUSTOMER') : custom.labels.get('IDS_PRINCI')}">

                                </p:column>


                                <p:column headerText="#{salesComparisionRep.monthCurr}" >

                                </p:column>

                                <p:column headerText="#{salesComparisionRep.monthPrev}" >

                                </p:column>

                                <p:column headerText="#{salesComparisionRep.yrCurr}" >

                                </p:column>

                                <p:column headerText="#{salesComparisionRep.yrPrev}" >

                                </p:column>


                                <p:column headerText="Jan-Dec #{salesComparisionRep.yr - 1}" >

                                </p:column>


                                <p:column headerText="YTD Var $" >

                                </p:column>


                                <p:column headerText="Var %" >

                                </p:column>


                                <p:column headerText="YTD %" >

                                </p:column>


                                <p:column headerText="LYT %" >

                                </p:column>



                            </p:row>


                        </p:columnGroup>

                        <p:column  class="col_left" >

                            <h:commandLink  style="float: right" >
                                <h:outputText  value="#{salesCompr.repRepLevel eq -1 ? '': salesCompr.repCompName}"  style="float: right;font-weight:bold; " ></h:outputText>
                                <p:ajax listener="#{salesComparisionRep.setVariablesLevel1(2, salesCompr.repCompId, salesCompr.repCompName)}"
                                        update="salesCompReport:firstDrill"     oncomplete="PF('drillDown1').show();" />
                            </h:commandLink>

                            <f:facet  name="footer">
                                <h:outputText   value="Total : "  style="float: right;font-weight:bold;size:11px"  class="footerFont"/>
                            </f:facet>

                        </p:column>

                        <p:column rendered="#{salesComparisionRep.checkFlag}">
                            <h:outputText  value="#{salesCompr.repCompName2}"  style="float: right;font-weight:bold" ></h:outputText>
                            <f:facet  name="footer">
                                <h:outputText   value=" "  />
                            </f:facet>
                        </p:column>



                        <p:column >
                            <h:outputText value="#{salesCompr.repMonthCurr}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer" rendered="#{salesReports.repPrinci eq 0 }">
                                <h:outputText 
                                    value="#{salesComparisionRep.checkFlag ? salesComparisionRep.showDetailsTotals[1] : salesComparisionRep.totals[1]}"  style="float: right;font-weight:bold"   class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>                           
                        </p:column>

                        <p:column  >
                            <h:outputText  value="#{salesCompr.repMonthPrev}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{salesComparisionRep.checkFlag ? salesComparisionRep.showDetailsTotals[0] : salesComparisionRep.totals[0]}"  style="float: right;font-weight:bold"   class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>

                            </f:facet>

                        </p:column>


                        <!--//     #2932:   Task CRM-1815: Fwd: Sales Comparison ReportsCRM-1815:column header added-->
                        <p:column >
                            <h:outputText value="#{salesCompr.repYTDCurr}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0 }" 
                                              value="#{salesComparisionRep.checkFlag ? salesComparisionRep.showDetailsTotals[3] : salesComparisionRep.totals[3]}"  style="float: right;font-weight:bold"   class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>

                            </f:facet>
                        </p:column>

                        <p:column >
                            <h:outputText value="#{salesCompr.repYTDPrev}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{salesComparisionRep.checkFlag ? salesComparisionRep.showDetailsTotals[2] : salesComparisionRep.totals[2]}"  style="float: right;font-weight:bold;"   class="footerFont" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>


                            </f:facet>
                        </p:column>




                        <p:column >
                            <h:outputText value="#{salesCompr.repYearPrev}"  style="float: right">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{salesComparisionRep.checkFlag ? salesComparisionRep.showDetailsTotals[4] : salesComparisionRep.totals[4]}"  >
                                    <f:convertNumber maxFractionDigits="0"/>            
                                </h:outputText>

                            </f:facet>
                        </p:column>


                        <p:column >
                            <h:outputText value="#{salesCompr.repYTDVarAmt}"  style="float: right" >
                                <f:convertNumber maxFractionDigits=""/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0 }" 
                                              value="#{salesComparisionRep.checkFlag ? salesComparisionRep.showDetailsTotals[5] : salesComparisionRep.totals[5]}" style="float: right;font-weight:bold"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>

                            </f:facet>
                        </p:column>


                        <p:column >
                            <h:outputText value="#{salesCompr.repYTDVarPct}" style="float: right" >
                                <f:convertNumber maxFractionDigits="2"/>
                            </h:outputText>

                            <!--#656 :CRM-2620: RSTthermal:  Sales Comparison missing a total-->
                            <!--1624: CRM-3206: Sales comparison numbers incorrect-->
                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0 }" 
                                              value="#{salesComparisionRep.checkFlag ? salesComparisionRep.calculateYTDVarPct(salesComparisionRep.totals[2], salesComparisionRep.totals[3]):salesComparisionRep.calculateYTDVarPct(salesComparisionRep.totals[2], salesComparisionRep.totals[3])}"   >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>

                            </f:facet>
                        </p:column>


                        <p:column >
                            <h:outputText value="#{salesCompr.repYTDPCT}"  style="float: right;" >
                                <f:convertNumber maxFractionDigits="2"/>
                            </h:outputText>

                            <f:facet name="footer">

                                <!--#656 :CRM-2620: RSTthermal:  Sales Comparison missing a total-->

                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{salesComparisionRep.checkFlag ? salesComparisionRep.showDetailsTotals[7] : salesComparisionRep.totals[7]}" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>

                            </f:facet>
                        </p:column>


                        <p:column >
                            <h:outputText value="#{salesCompr.repLYTPCT}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="2"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <!--#656 :CRM-2620: RSTthermal:  Sales Comparison missing a total-->

                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{salesComparisionRep.checkFlag ? salesComparisionRep.showDetailsTotals[8] : salesComparisionRep.totals[8]}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>

                            </f:facet>
                        </p:column>
                    </p:dataTable>


                    <!--export end-->















                </h:panelGroup>

                <h:inputHidden id="rep"/>
                          <!--//            #9671 ESCALATIONS  CRM-6340  Sales Report-->
                          <!--//          #12162  ESCALATIONS CRM-7388   Web Report: Error On Sales Comparison Report-->

                <!--drill down level 1-->
                <p:dialog header="Sales Comparison break up for: #{salesComparisionRep.drillDownHdr}" modal="true" maximizable="true" id='firstDrill' resizable="false" closeOnEscape="true" widgetVar="drillDown1">
                    <p:ajax event="close" onstart="hideGif();" update="salesCompReport:dtrpt" listener="#{salesComparisionRep.goToNaviPage(salesComparisionRep.yr, salesComparisionRep.mt, salesComparisionRep.repGroup, 1, 0, 0, '',salesComparisionRep.repFor,salesComparisionRep.checkFlag,salesComparisionRep.excldInactivFlg,salesComparisionRep.compRegns,salesComparisionRep.compRegNames)}" />
                    <h:panelGroup id="tbldata1">
                        <p:dataTable id="dtrpt1"  value="#{salesComparisionRep.salesDrillDown1List}" var="salesCompr" paginatorAlwaysVisible="false" paginator="true"
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                     rows="15" emptyMessage="No data found" >
                            <!--//Bug 1164 -# 325040 : By Customer By month Sales Reports-->
                            <p:column   class="col_left" >
                                <f:facet name="header" id="name">
                                    <p:outputLabel value="#{salesComparisionRep.header}" />
                                </f:facet>
                                <p:outputLabel value="#{salesCompr.repCompName}" rendered="#{salesComparisionRep.repGroupLevel1 != 2}" />
                                <!--Bug #4009:CRM-4130: summitsales: sales comparison drill down, no progress circle-->
                                <h:commandLink value="#{salesCompr.repCompName}" rendered="#{salesComparisionRep.repGroupLevel1 == 2}"  onclick="processGif();" >
                                    <p:ajax listener="#{salesComparisionRep.setVariablesLevel2(3, salesCompr.repCompId, salesCompr.repCompName)}"
                                            update="salesCompReport:secondDrill" oncomplete="PF('drillDown2').show();hideGif()" />
                                </h:commandLink>

                                <f:facet  name="footer">
                                    <h:outputLabel   value="Total : "  style="float: right"  class="footerFont" />
                                </f:facet>
                            </p:column>

                            <p:column headerText="#{salesComparisionRep.monthCurr}" style="text-align: right" sortBy="#{salesCompr.repMonthCurr}">
                                <h:outputLabel value="#{salesCompr.repMonthCurr}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[1]}"  style="float: right"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column headerText="#{salesComparisionRep.monthPrev}" style="text-align: right" sortBy="#{salesCompr.repMonthPrev}" >
                                <h:outputLabel  value="#{salesCompr.repMonthPrev}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[0]}"  style="text-align: right"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>

                            </p:column>

                            <p:column headerText="#{salesComparisionRep.yrCurr}" style="text-align: right" sortBy="#{salesCompr.repYTDCurr}" width="170">
                                <h:outputLabel value="#{salesCompr.repYTDCurr}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[3]}"  style="float: right"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column headerText="#{salesComparisionRep.yrPrev}" style="text-align: right" sortBy="#{salesCompr.repYTDPrev}" width="170" >
                                <h:outputLabel value="#{salesCompr.repYTDPrev}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[2]}"  style="float: right"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>

                                </f:facet>
                            </p:column>


                            <p:column headerText="Jan-Dec #{salesComparisionRep.yr - 1}" style="text-align: right" sortBy="#{salesCompr.repYearPrev}" width="140">
                                <h:outputLabel value="#{salesCompr.repYearPrev}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[4]}"  style="float: right"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>            
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>


                            <p:column headerText="YTD Var $" style="text-align: right" sortBy="#{salesCompr.repYTDVarAmt}">
                                <h:outputLabel value="#{salesCompr.repYTDVarAmt}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[5]}"  style="float: right"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <!--2065 :CRM-2633: RST Thermal: Sales Comparison-->
                            <p:column headerText="Var %" style="text-align: right" sortBy="#{salesCompr.repYTDVarPct}">
                                <h:outputLabel  value="#{salesCompr.repYTDVarPct}"   style="#{
                                                         salesCompr.repYTDVarPct gt 0 ?  'color:green' 
                                                         : (salesCompr.repYTDVarPct lt 0 ? 'color:red' : 'color:black')
                               }" >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <!--Bug 656 : CRM-2620: RSTthermal:  Sales Comparison missing a total-->
                                <!--1624: CRM-3206: Sales comparison numbers incorrect-->
                                <!--2065 :CRM-2633: RST Thermal: Sales Comparison-->
                                <f:facet name="footer">
                                    <h:outputLabel 
                                        value="#{salesComparisionRep.calculateYTDVarPct(salesComparisionRep.totals[2], salesComparisionRep.totals[3])}"  style="float: right;#{
                                                 salesComparisionRep.calculateYTDVarPct(salesComparisionRep.totals[2], salesComparisionRep.totals[3]) gt 0 ?  'color:green' 
                                                 : (salesComparisionRep.calculateYTDVarPct(salesComparisionRep.totals[2], salesComparisionRep.totals[3]) lt 0 ? 'color:red' : 'color:black')
                               }" class="footerFont" >
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>


                            <p:column headerText="YTD %" style="text-align: right" sortBy="#{salesCompr.repYTDPCT}">
                                <h:outputLabel value="#{salesCompr.repYTDPCT}"  >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[7]}"  style="float: right"  class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>


                            <p:column headerText="LYT %" style="text-align: right" sortBy="#{salesCompr.repLYTPCT}">
                                <h:outputLabel value="#{salesCompr.repLYTPCT}"  >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[8]}"  style="float: right"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>
                        </p:dataTable>

                    </h:panelGroup>

                </p:dialog>

                <!--drill down level 2-->

                <p:dialog header="Sales Comparison break up for: #{salesComparisionRep.drillDownHdr}" modal="true" maximizable="true" resizable="false" closeOnEscape="true" id='secondDrill' widgetVar="drillDown2">
                    <h:panelGroup id="tbldata2">
                        <p:dataTable id="dtrpt2"  value="#{salesComparisionRep.salesDrillDown2List}" var="salesCompr" paginatorAlwaysVisible="false" paginator="true"
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                     rows="15" emptyMessage="No data found" >

                            <p:column   class="col_left" >
                                <f:facet name="header" id="name">
                                    <p:outputLabel value="#{salesComparisionRep.header}" />
                                </f:facet>
                                <p:outputLabel value="#{salesCompr.repCompName}"/>

                                <f:facet  name="footer">
                                    <h:outputLabel   value="Total : "  style="float: right"   class="footerFont"/>
                                </f:facet>
                            </p:column>

                            <p:column headerText="#{salesComparisionRep.monthCurr}" style="text-align: right" sortBy="#{salesCompr.repMonthCurr}">
                                <h:outputLabel value="#{salesCompr.repMonthCurr}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[1]}"  style="float: right"   class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column headerText="#{salesComparisionRep.monthPrev}" style="text-align: right" sortBy="#{salesCompr.repMonthPrev}" >
                                <h:outputLabel  value="#{salesCompr.repMonthPrev}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[0]}"  style="float: right"   class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>

                            </p:column>

                            <p:column headerText="#{salesComparisionRep.yrCurr}" style="text-align: right" sortBy="#{salesCompr.repYTDCurr}" width="170">
                                <h:outputLabel value="#{salesCompr.repYTDCurr}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[3]}"  style="float: right"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column headerText="#{salesComparisionRep.yrPrev}" style="text-align: right" sortBy="#{salesCompr.repYTDPrev}" width="170">
                                <h:outputLabel value="#{salesCompr.repYTDPrev}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[2]}"  style="float: right"  class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>

                                </f:facet>
                            </p:column>


                            <p:column headerText="Jan-Dec #{salesComparisionRep.yr - 1}" style="text-align: right" sortBy="#{salesCompr.repYearPrev}" width="140">
                                <h:outputLabel value="#{salesCompr.repYearPrev}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[4]}"  style="float: right"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>            
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>


                            <p:column headerText="YTD Var $" style="text-align: right" sortBy="#{salesCompr.repYTDVarAmt}">
                                <h:outputLabel value="#{salesCompr.repYTDVarAmt}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[5]}"  style="float: right"  class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <!--2065 :CRM-2633: RST Thermal: Sales Comparison-->
                            <p:column headerText="Var %" style="text-align: right" sortBy="#{salesCompr.repYTDVarPct}">
                                <h:outputLabel  value="#{salesCompr.repYTDVarPct}" style="#{
                                                         salesCompr.repYTDVarPct gt 0 ?  'color:green' 
                                                         : (salesCompr.repYTDVarPct lt 0 ? 'color:red' : 'color:black')
                               }"  >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <!--Bug 656 : CRM-2620: RSTthermal:  Sales Comparison missing a total-->
                                <!--1624: CRM-3206: Sales comparison numbers incorrect-->
                                <!--2065 :CRM-2633: RST Thermal: Sales Comparison-->
                                <f:facet name="footer">
                                    <h:outputLabel 
                                        value="#{salesComparisionRep.calculateYTDVarPct(salesComparisionRep.totals[2], salesComparisionRep.totals[3])}"  style="float: right;#{
                                                 salesComparisionRep.calculateYTDVarPct(salesComparisionRep.totals[2], salesComparisionRep.totals[3]) gt 0 ?  'color:green' 
                                                 : (salesComparisionRep.calculateYTDVarPct(salesComparisionRep.totals[2], salesComparisionRep.totals[3]) lt 0 ? 'color:red' : 'color:black')
                               }" class="footerFont">
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>


                            <p:column headerText="YTD %" style="text-align: right" sortBy="#{salesCompr.repYTDPCT}">
                                <h:outputLabel value="#{salesCompr.repYTDPCT}"  >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[7]}"  style="float: right"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>


                            <p:column headerText="LYT %" style="text-align: right" sortBy="#{salesCompr.repLYTPCT}">
                                <h:outputLabel value="#{salesCompr.repLYTPCT}"  >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                   value="#{salesComparisionRep.totals[8]}"  style="float: right"  class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>
                        </p:dataTable>



                    </h:panelGroup>

                </p:dialog>

            </div>
        </h:form>
        <!--//#3496:Task Sales Reports > Sales Comparison - Export option-->
        <p:dialog widgetVar="inSalCompDlg" closable="false" modal="true" header="Message" resizable="false" >
            <h:form>
                <p:outputPanel >
                    <br />
                    <h:graphicImage  library="images" name="ajax-loader.gif"   />
                    <p:spacer width="5" />
                    <p:outputLabel value="Please wait...Exporting records" />
                    <br /><br />
                    <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                </p:outputPanel>
            </h:form>
        </p:dialog>
        <style>
             /*14453 ESCALATIONS CRM-8464   Reporting Graphs Legend not legible - Multiple Instances*/
            .jqplot-axis {
                 font-size: 0.25em !important;
            }

            .colorr {
                background-color: #A9A9A9 !important;
                background-image: none !important;

            }


            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }


            .jqplot-target {

                font-size: 0.75em!important;
            }
        </style>
        <!--#3153 Sales Reports - Add gif for loading-->
        <script>
            window.onload = function () {
                //                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
                    //      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("salComprpt").style.pointerEvents = "all";
                    document.getElementById("salComprpt").style.opacity = "1";
                    $('#loading-image').hide();
                    //            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };


            function processGif()
            {
                $('#loading-image').show();
                document.getElementById("salComprpt").style.pointerEvents = "none";
                document.getElementById("salComprpt").style.opacity = "0.4";
            }

            function hideGif()
            {
                $('#loading-image').hide();
                document.getElementById("salComprpt").style.pointerEvents = "all";
                document.getElementById("salComprpt").style.opacity = "1";
            }

            function start() {
                PF('inSalCompDlg').show();
            }
            function stop() {

                PF('inSalCompDlg').hide();
            }



        </script>

    </ui:define>




</ui:composition>