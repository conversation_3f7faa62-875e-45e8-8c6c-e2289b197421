<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: CustomerSummary.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>
    <ui:define name="meta">
        <f:metadata> 

            <f:viewParam name="yr" value="#{reports.repYear}" />
            <f:viewParam name="mt" value="#{reports.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="d" value="#{reportFilters.distributor.compId}"/>
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>
            <f:viewParam name="m" value="#{reportFilters.minVal}"/>
            <f:viewParam name="c" value="#{reportFilters.customer.compId}"/>
            <f:viewParam name="g" value="#{reportFilters.grp}"/>

            <!--#3049: Task: Customer Summary report - Summarised by Parent - Add multi select Sales team option.-->
            <!--<f:viewAction action="#{reportFilters.getSelectedCustName(reportFilters.customer.compId)}"/>-->

     <!--<f:viewAction action="#{reportFilters.getSelectedSmanName(reportFilters.customer.compId)}"/>-->

            <!--<f:viewAction action="#{reportFilters.assignSalesTeams()}"/>-->
            <!--<f:viewAction action="#{reportFilters.assignCompanyRegions()}"/>-->

            <f:viewAction action="#{salesSummaryParent.run()}"/>

        </f:metadata>
    </ui:define>

    <ui:define name="body">
        <f:event listener="#{custSummaryRep.isPageAccessible}" type="preRenderView"/> 

        <!--#3116:Task :Sales Reports - Customer Summary - Add gif for loading-->
        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .3 !important;" />


        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7" id="custSumPrtRpt" >


            <h:form id="custSummaryHeader" >
                <!--// #10203 :CRM-6433-->
                <p:remoteCommand autoRun="true"  update=":custSummaryHeader:grid"  />
                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update=":custSummaryHeader:inputCustomer  :custSummaryHeader:viewreport" />
                <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update=":custSummaryHeader:inputDistriName :custSummaryHeader:viewreport "/>
                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update=":custSummaryHeader:inputPrincName   :custSummaryHeader:viewreport" />
                <!--<p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update=":custSummaryHeader:inputAllSalesTeam :custSummaryHeader:viewreport" />-->
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >

                        <div class="compNamebg">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            #{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_CUSTOMER'):'Customer'} Summary Report 

                            <br/>
                            As of : #{reports.repYear} - #{reports.getMonth(reports.repMonth)}
                            <!--465-->
                            <br/>
                        </div>
                        <p:outputLabel value="(Filtered for selected Regions) "   rendered="#{!(reportFilters.compReglst.size()>10) and !(reportFilters.compRegnName.length() eq null) }"/>



                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="margin-left:60px">
                        <p:panelGrid   columns="2"  layout="grid" style="width: 600px;float: right;"> 
                            <p:chart type="bar" model="#{reports.barChartYearly}"   style="width:285px;height:180px"/>
                            <p:chart type="bar" model="#{reports.barChartQuarterly}"   style="width:285px;height:180px"/>
                        </p:panelGrid>
                    </div>  
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:-90px">
                        <p:panelGrid id="grid" columns="2" 
                                     style="float: right;width: 300px;"   columnClasses="ui-grid-col-5,ui-grid-col-7"  layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
        <!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"  />
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:outputLabel for="inputDistriName" value="#{custom.labels.get('IDS_DISTRI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputDistriName" value="#{reportFilters.distributor.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyDistri', 3, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <!--//#2999Task Customer Summary report - Add multi select Sales team option-->
                            <!--//  1741  CRM-3259: Sales team:Thea: prevent users from seeing Sales and Comms -  Dashboard Sales Team Lookup-->

                            <p:outputLabel  value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <!--// #10203 :CRM-6433-->
                            <h:panelGroup class="ui-inputgroup" style="width: 125%">
                                <h:selectOneListbox styleClass="sel-salesteam"  size="2"  style="width: 200%;" id="selSalesTeam" >
                                    <f:selectItems value="#{reportFilters.salesTeams}" var="team"  
                                                   itemValue="#{team.smanId}"
                                                   itemLabel="#{team.smanName}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.crmFlag()}" action="#{lookupService.list('SALES_TEAM')}" update=":frmSalesTeamLookup" oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                &nbsp;&nbsp;&nbsp;
                                <!--// #10203 :CRM-6433-->
                                <p:commandLink styleClass="sel-salesteam" value="Clear" actionListener="#{lookupService.clear('SALES_TEAM')}" update="@(.sel-salesteam)" disabled="#{!(reportFilters.salesTeams!=null and reportFilters.salesTeams.size() != 0)}" style="text-decoration:underline"/>
                            </h:panelGroup> 

                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();" style="width:5px" 
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <!--#465-->
                            <!--                            
                                                         <p:outputLabel value="#{custom.labels.get('IDS_COMP_REGION')}"  />
                                                        <h:panelGroup class="ui-inputgroup"  style="width: 125%" >
                                                            <h:selectOneListbox  styleClass="sel-region"  size="2" style="width: 200%" id="selRegion"     >
                                                                <f:selectItems value="#{lookupService.compregionList}" var="reg"  
                                                                               itemValue="#{reg.recId}"
                                                                               itemLabel="#{reg.compRegion}"  />
                                                            </h:selectOneListbox>
                                                            <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_COMP')}" actionListener="#{lookupService.list('REGION')}" update=":frmCompRegionLookup" oncomplete="PF('dlgRegionLookup').show();"  />
                                                            &nbsp;&nbsp;&nbsp;
                                                            <p:commandLink styleClass="sel-region" value="Clear" actionListener="#{lookupService.clear('REGION')}" update="@(.sel-region)" disabled="#{!(lookupService.compregionList != null and lookupService.compregionList.size() != 0)}" style="text-decoration:underline" />
                                                        </h:panelGroup>-->

                            <p:outputLabel value="Minimum Sales"/>

                                <!--<p:inputText widgetVar="min"  value="#{reportFilters.minVal}" style="width: 80%">-->
                            <p:inputNumber    value="#{reportFilters.minVal}"   style="width: 80%" minValue="0" decimalPlaces="2" >


                            </p:inputNumber>
                            <!--#12119 ESCALATIONS CRM-7368 Customer Summary Report: Summarize by Parent Company disables export-->
                            <!--#3116:Task :Sales Reports - Customer Summary - Add gif for loading-->
                            <!--465:region lookup-->
                            <!--<br/>-->
                            <p:commandButton  value="Run Report "  id="viewreport"   styleClass="btn btn-primary btn-xs"    actionListener="#{custSummaryRep.goToParentreport(reportFilters.compRegnName)}" action="#{reportFilters.setSalesteamlst()}"  onclick="custSumPrtVisibilty();" style="width:30px"/>
                            <!--<div/>-->
                            
                           <!--#12119 ESCALATIONS CRM-7368 Customer Summary Report: Summarize by Parent Company disables export-->
                            <p:column>
                                <p:commandLink id="xls" ajax="false"  >  
                                    <p:graphicImage name="/images/excel.png" width="24" onclick="PrimeFaces.monitorDownload(start, stop);" />
                                    <pe:exporter type="xlsx" target="custSumexport"  fileName="Customer_Summary_Report" subTable="false" postProcessor="#{custSummaryRep.postProcessXLS}"  />  

                                </p:commandLink>  

                                <p:commandLink id="pdf" ajax="false">  
                                    <p:graphicImage value="/resources/images/pdf.png"  width="24"  onclick="PrimeFaces.monitorDownload(start, stop);" />  
                                    <pe:exporter type="pdf" target="custSumexport" fileName="Customer_Summary_Report" subTable="false"  preProcessor="#{salesSummaryParent.preProcessPDF}" />  

                                </p:commandLink>

                            </p:column>

                        </p:panelGrid>

                    </div >

                </div>

                <!--#987  :  CRM-2757: MSGinc customer summary Reports Timing out-->

<!--#12119 ESCALATIONS CRM-7368 Customer Summary Report: Summarize by Parent Company disables export-->
                <p:dataTable  value="#{salesSummaryParent.listCustSummary}" var="custsumprt" 
                              widgetVar="custSumRepTbl"  scrollHeight="390"
                              liveScroll="true"
                              nullSortOrder="-1"
                              virtualScroll="true"
                              scrollable="true"  scrollRows="50"
                              >
                    <!--1842 CRM-3324:  SaleReport private team filters - some clean up - UI-->
                    <p:column  headerText="Parent Company Name" sortBy="#{custsumprt.customer}" filterBy="#{custsumprt.customer}"  style="text-align:left">
                        <!--1909-->
                        <h:link title="View Product Details" value="#{custsumprt.customer}" target="_self" outcome="rpt_ProdDtls_Parent.xhtml?yr=#{reports.repYear}&amp;mt=#{reports.repMonth}&amp;p=#{reportFilters.pricipal.compId}&amp;c=#{custsumprt.id}&amp;d=#{reportFilters.distributor.compId}&amp;s=#{reportFilters.salesTeam.smanId}&amp;g=0&amp;repcode=7"  />
                              <!--<p:outputLabel  value=" #{custsumprt.customer}"  />-->
                        <f:facet name="footer">
                            <h:outputLabel value="Total :"  style="float: right"  class="footerFont"/>
                        </f:facet>
                    </p:column>
                    <p:column headerText="Year-#{(reports.repYear)-1}"  style="text-align:right"  sortBy="#{custsumprt.prevYear}" filterBy="#{custsumprt.prevYear}">
                        <p:outputLabel  value="#{custsumprt.prevYear}"  >

                            <!--#3201: Bug CRM-2001: customer summary report issues--> 
                            <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                        </p:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel value="#{salesSummaryParent.prYrTotal}"  style="float: right"  class="footerFont">

                                <!--#3201: Bug CRM-2001: customer summary report issues-->
                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column >


                    <p:column  headerText="Year-#{(reports.repYear)}" style="text-align:right" sortBy="#{custsumprt.currYear}"  filterBy="#{custsumprt.currYear}" >
                        <!--1909-->
                        <h:link  title="View #{custom.labels.get('IDS_CUSTOMER')} Details"    target="_self" outcome="rpt_CustDtls_Parent.xhtml?yr=#{reports.repYear}&amp;mt=#{reports.repMonth}&amp;p=#{reportFilters.pricipal.compId}&amp;c=#{custsumprt.id}&amp;d=#{reportFilters.distributor.compId}&amp;s=#{reportFilters.salesTeam.smanId}&amp;g=0&amp;cn=#{custsumprt.customer}&amp;repcode=3" > 
                            <p:outputLabel  value="#{custsumprt.currYear}"  >

                                <!--#3201: Bug CRM-2001: customer summary report issues-->
                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                            </p:outputLabel>
                        </h:link>   

                        <f:facet name="footer">
                            <h:outputLabel value="#{salesSummaryParent.cuyrtotal}"  style="float:right"  class="footerFont" >

                                <!--#3201: Bug CRM-2001: customer summary report issues-->
                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>

                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column  headerText="% to LY" style="text-align: right;"  sortBy="#{custsumprt.growth}" filterBy="#{custsumprt.growth}">
                        <p:outputLabel  value=" #{custsumprt.growth}" style="text-align:right" >
                            <h:outputLabel value="%" rendered="#{custsumprt.growth ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1"/>
                        </p:outputLabel>
                    </p:column>
                </p:dataTable>
<!--#12119 ESCALATIONS CRM-7368 Customer Summary Report: Summarize by Parent Company disables export-->
                <p:dataTable id="custSumexport"  value="#{salesSummaryParent.listCustSummary}" var="custrep"   rendered="false">
                    <p:columnGroup  type="header" class="header"   >
                        <p:row>

                            <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                            <p:column />
                            <p:column rendered="#{salesSummaryParent.grp == 2}"/>
                            <p:column headerText=" #{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_CUSTOMER'):'Customer'} Summary"/>

                            <p:column  headerText="Quarter : #{reports.repYear}-#{reports.quarter},  Month : #{reportFilters.getMonth(reports.repMonth )} #{(reportFilters.compRegnName.length() eq null)or (custrep.pfdexport)? '':'(Filtered for selected Regions)'}" />



                        </p:row>

                        <p:row></p:row>

                        <p:row>
                            <p:column   headerText="Parent Company Name"  >

                            </p:column>

                            <p:column headerText="#{salesSummaryParent.grp eq 1 ? 'Previous' :'' } Year #{salesSummaryParent.grp eq 1 ? '' :'-' } #{salesSummaryParent.grp eq 1 ? '' :(reports.repYear)-1 }" >

                            </p:column>

                            <p:column headerText="#{salesSummaryParent.grp eq 1 ? 'Current' :'' } Year #{salesSummaryParent.grp eq 1 ? '(Actual)' :'-' } #{salesSummaryParent.grp eq 1 ? '' :(reports.repYear) }" >

                            </p:column>

                            <p:column headerText="% to LY" >

                            </p:column>

                        </p:row>


                    </p:columnGroup>

                    <p:column   class="col_left" >


                        <h:outputText  value="#{custrep.customer}" style="cursor: pointer;text-decoration:underline" />

                        <f:facet name="footer">
                            <h:outputText value="Total :"  style="float: right"  class="footerFont"/>
                        </f:facet>

                    </p:column>

                    <p:column>
                        <h:outputText value="#{custrep.prevYear}" style="text-align: right;">


                            <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText value="#{salesSummaryParent.prYrTotal}"  style="float: right"  class="footerFont">


                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>
                    </p:column>

                    <p:column>
                        <h:outputText value="#{custrep.currYear}" style="text-align: right;" >


                            <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                        </h:outputText>

                        <f:facet name="footer">
                            <h:outputText value="#{salesSummaryParent.cuyrtotal}"   style="text-align: right;">


                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>

                    </p:column>

                    <p:column >
                        <h:outputText  value="#{custrep.growth}#{custrep.growth ne null ? '%' : ''}" style="text-align: right;"   >
                            <h:outputText value="%" rendered="#{custrep.growth ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1"/>
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText value=""  style="text-align: right;"  class="footerFont" >

                            </h:outputText>
                        </f:facet>
                    </p:column>
                </p:dataTable>





            </h:form>
        </div>
        <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
        <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>
        <!--#456-->
        <ui:include src="/lookup/CompanyRegionLookUp.xhtml" />

        <style>

            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }
             /*14453 ESCALATIONS CRM-8464   Reporting Graphs Legend not legible - Multiple Instances*/
            .jqplot-axis {
                 font-size: 0.25em !important;
            }


            .jqplot-target {

                font-size: 0.75em!important;
            }
        </style>

        <!--#3116:Task :Sales Reports - Customer Summary - Add gif for loading-->
        <script>
            window.onload = function () {
//                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("custSumPrtRpt").style.pointerEvents = "all";
                    document.getElementById("custSumPrtRpt").style.opacity = "1";

                    $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };


            function custSumPrtVisibilty()
            {

                $('#loading-image').show();
                document.getElementById("custSumPrtRpt").style.pointerEvents = "none";
                document.getElementById("custSumPrtRpt").style.opacity = "0.7";

            }

        </script>

    </ui:define>

</ui:composition>