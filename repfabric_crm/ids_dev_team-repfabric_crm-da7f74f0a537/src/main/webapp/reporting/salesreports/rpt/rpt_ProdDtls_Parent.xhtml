<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: productdetails All.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>
    <ui:define name="meta">
        <f:metadata>
            <f:viewParam name="yr" value="#{reports.repYear}" />
            <f:viewParam name="mt" value="#{reports.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="pname" value="#{reportFilters.pricipal.compName}"/>
            <f:viewParam name="cn" value="#{reportFilters.customer.compName}"/>
            <f:viewParam name="c" value="#{reportFilters.customer.compId}"/>
            <f:viewParam name="c1" value="#{reportFilters.customer.compId}"/>
            <f:viewParam name="d" value="#{reportFilters.distributor.compId}"/>
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>
            <f:viewParam name="g" value="#{reportFilters.grp}"/>
<!--#3056: Task Product Details Report: Updates to sales team filter option-->
            <f:viewAction action="#{reportFilters.getSelectedSmanName(reportFilters.customer.compId)}"/>
 
            <!--3499 drill down-->
            <f:viewAction action="#{reportFilters.getSelectedCustName(reportFilters.customer.compId)}"/> 
            <f:viewParam name="repcode" value="#{productDtlsItem.repcode}"/>


            <f:viewAction action="#{productDtlsItem.initparent()}"/>
        </f:metadata>



    </ui:define>


    <ui:define name="body">
 <!--#3153 Sales Reports - Add gif for loading -->
        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />
        <!--<img id="loading-image" src="images/ajax-loader.gif" alt="Loading..." />-->
        <!--</div>-->  

 <!--#3153 Sales Reports - Add gif for loading -->
        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7" id="prodDetPrtrpt" >
         <h:form id="prodDetAllHeader" >

                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >


                        <div class="compNamebg" style="float:left;">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            Product Detail Report  For :#{custDetailRep.getParentCustName(reportFilters.customer.compId)}
                            <br/>
                            As of : #{reports.repYear} - #{reportFilters.getMonth(reports.repMonth )}
                        </div>


                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="margin-left:60px">
                        <!--                                                <p:panelGrid   columns="2"  layout="grid" style="width: 600px;float: right;"> 
                                                                            <p:chart type="bar" model="#{reports.barChartYearly}"   style="width:285px;height:180px"/>
                                                                            <p:chart type="bar" model="#{reports.barChartQuarterly}"   style="width:285px;height:180px"/>
                                                                        </p:panelGrid>-->
                    </div>  
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:100px;width:230px;height:180px;border: 1px solid #9B9999;">
                        <!--style="width: 18.5%;height: 98%; display: inline-block;border: 1px solid #9B9999;"-->

                        <table style="width: 100%;">
                            <thead>

                                <tr>
                                    <th>Filter</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="col_left">#{custom.labels.get('IDS_PRINCI')} : </td>
                                </tr>
                                <tr>
                                    <td class="bold">#{reportFilters.pricipal.compName}</td>
                                </tr>
                                <tr>
                                    <td class="col_left" >#{custom.labels.get('IDS_DISTRI')} : </td>
                                </tr>
                                <tr>
                                    <td class="bold">#{reportFilters.distributor.compName}</td>
                                </tr>
                                <tr>
                                    <td class="col_left">#{custom.labels.get('IDS_SALES_TEAM')}  : </td>
                                </tr>
                                <tr>
                                    <td class="bold">#{reportFilters.salesTeam.smanName}</td>
                                </tr>

                                <!--346 #344579 :Dashboard :Halco :Part number query-->

                                <tr>
                                    <td class="col_left">#{custom.labels.get('IDS_PART_NUM')}  : </td>
                                </tr>
                                <tr>
                                    <td class="bold">#{reportFilters.princiPartNo} </td>
                                </tr>
                            </tbody>
                        </table>





                    </div >


                </div>




                <!--         <p:ajaxStatus  onstart="PF('dlgProductSalesHtyaj').show()" oncomplete="PF('dlgProductSalesHtyaj').hide()" />
                        <p:dialog header="Please wait" draggable="false"  widgetVar="dlgProductSalesHtyaj" closable="false" resizable="false" >
                            <h:graphicImage  library="images" name="ajax-loader.gif" />
                            <p:spacer width="5"/>
                            <h:outputLabel value="Loading data.."/>
                        </p:dialog>
                -->
                <p:ajaxStatus onstart="PF('statusDialog').show()" onsuccess="PF('statusDialog').hide()" />

                <p:dialog widgetVar="statusDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
                    <h:graphicImage  library="images" name="ajax-loader.gif" />
                </p:dialog>



                <p:dataList   type="definition"  value="#{productDtlsItem.listProdDtItems}" var="productDtlsItem1"  >

                    <p:column>

                        <div class="div-selected">

                            <!--#Too many tabs, fixed by replacing _blank to _self -->
                            <a style="color:#323232!important" href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{productDtlsItem1.id}" target="_self" class="header-label">
                                #{productDtlsItem1.name}         
                            </a>
                        </div>
                       <!--//      #11990 CRM-7302   Product Details Report : Does not load-->
                        <p:dataTable style="margin-bottom: 20px"  value="#{productDtlsItem1.details}" var="details"
                                     lazy="true" rows="50" rowsPerPageTemplate="50,100,150" paginatorAlwaysVisible="true" paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}" paginator="true" >

                            <p:column rendered="#{reportFilters.grp ne 2 }" style="width: 120px"
                                      sortBy="#{details.principal}"
                                      headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left">
                                <!--#Too many tabs, fixed by replacing _blank to _self -->
                                <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.princiId}" target="_self" >    
                                    <p:outputLabel value="#{details.principal}"    title="#{details.principal}" style="text-decoration: underline"/>
                                </a>
                                <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                            </p:column>      
                            <p:column rendered="#{reportFilters.grp ne 1 }" style="width: 150px"  
                                      sortBy="#{details.distributor}"
                                      headerText="#{custom.labels.get('IDS_DISTRI')}" class="col_left" >
                                <!--#Too many tabs, fixed by replacing _blank to _self -->
                                <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.distriId}" target="_self" >    

                                    <!--Bug 1019 -Sales Report : Distributor Column -Distributor name not visible properly 21Nov2018-->
                                    <p:outputLabel value="#{details.distributor}"        title="#{details.distributor}" style="text-decoration: underline"  />    

                                </a>
                                <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                            </p:column> 


                              <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:column rendered="#{reportFilters.grp ne 0 }"  style="width: 120px"
                                      sortBy="#{details.customer}"
                                      
                                      headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" class="col_left"  >

                                <!--#Too many tabs, fixed by replacing _blank to _self -->
                                <!--../../RepfabricCRM/opploop/companies/CompanyDetails.xhtml?id=-->

                                <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.custId}" target="_self" title="#{details.customer}">    
                                    <p:outputLabel value="#{details.customer}" title="#{details.customer}" />
                                </a>
                                <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                            </p:column>
                            <!--c3 added for secondary customer 1606 -->
<!--                            <p:column rendered="#{productDtlsItem.repSecCheck ne 0 }" style="width: 80px"  
                                      sortBy="#{details.secCustName}"
                                      headerText="Secondary #{custom.labels.get('IDS_CUSTOMER')}" class="col_left" >

                                <p:outputLabel value="#{details.secCustName}"  title="#{details.secCustName}"  />    

                            </p:column> -->
 <!--3499 drill down-->
                            <p:column sortBy="#{details.partNo}" style="width: 90px"
                                      headerText="#{custom.labels.get('IDS_PART_NUM')}" class="col_left" >
                                <p:commandLink process="@this" actionListener="#{partDetails.showPartDtls(details.partNo, details.prodFamily, details.custId, details.princiId, productDtlsItem1.name,reports.repYear)}" 
                                               value="#{details.partNo}" 
                                               update="prodpartCust:tblPartCust"
                                               oncomplete="PF('dlgPartCust').show()"  title="#{details.partNo}"/>
                                <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                            </p:column>

                            <p:column sortBy="#{details.prodFamily}" 
                                      class="col_left"
                                      headerText="#{custom.labels.get('IDS_PROD_FAMILY')}"  >
                                #{details.prodFamily} 



                                <f:facet name="footer">
                                    <h:outputLabel value="Total :"  style="float: right"  class="footerFont" />
                                </f:facet>
                            </p:column>

                            <p:column sortBy="#{details.prevYr}"  
                                      headerText="Previous Year" 
                                      class="col_right"  style="text-align: right"  >
                                <h:outputLabel value="#{details.prevYr}">
                                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel value="#{productDtlsItem1.totPrevYr}"  style="float: right"  class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column sortBy="#{details.currYr}" headerText="Current Year" class="col_right" style="text-align: right">
                                <h:outputLabel value="#{details.currYr}">
                                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel value="#{productDtlsItem1.totCurrYr}"  style="float: right"   class="footerFont"  >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column sortBy="#{details.yrGrowth}" headerText="Year RR" class="col_right" style="text-align: right">              
                                <h:outputLabel value="#{details.yrGrowth}"
                                               style="#{
                                               details.yrGrowth gt 100 ?  'color:green' 
                                                   : (details.yrGrowth lt 100 ? 'color:red' : 'color:black')
                               }"

                                               ><h:outputLabel value="%" rendered="#{details.yrGrowth ne null}"/>
                                    <f:convertNumber  groupingUsed="false"   maxFractionDigits="1"  />
                                </h:outputLabel>
                            </p:column>

                            <!--Dashboard: Client requirement Added quarterly column Q1,Q2,Q3-->

                            <p:column sortBy="#{details.q3}" headerText="#{salesReports.headervalQr[0]}" style="min-width: 47px;text-align: right" class="col_right" >
                                <h:outputLabel value="#{details.q3}">
                                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                                </h:outputLabel>  

                                <f:facet name="footer">
                                    <h:outputLabel value="#{productDtlsItem1.totQ3}"  style="float: right"   class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column headerText="#{salesReports.headervalQr[1]}" sortBy="#{details.q2}" style="min-width: 47px;text-align: right" class="col_right" >              
                                <h:outputLabel value="#{details.q2}">
                                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                                </h:outputLabel>  

                                <f:facet name="footer">
                                    <h:outputLabel value="#{productDtlsItem1.totQ2}"  style="float: right"   class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column headerText="#{salesReports.headervalQr[2]}" sortBy="#{details.q1}" style="min-width: 47px;text-align: right"  >              
                                <h:outputLabel value="#{details.q1}"  style="text-align:  right">
                                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                                </h:outputLabel>  
                                <f:facet name="footer">
                                    <h:outputLabel value="#{productDtlsItem1.totQ1}"  style="float: right"    class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column headerText="Current Qtr" sortBy="#{details.qrtCurr}"   style="text-align:  right">              
                                <h:outputLabel value="#{details.qrtCurr}">
                                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                                </h:outputLabel> 

                                <f:facet name="footer">
                                    <h:outputLabel value="#{productDtlsItem1.totCurrQtr}"  style="float: right"  class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column headerText="Qtr RR" sortBy="#{details.qrtGrowth}" class="col_right"  >

                                <h:outputLabel value="#{details.qrtGrowth}"                          
                                               style="#{
                                               details.qrtGrowth gt 0 ?  'color:green' 
                                                   : (details.qrtGrowth lt 0 ? 'color:red' : 'color:black')
                               }">
                                    <h:outputLabel value="%" rendered="#{details.qrtGrowth ne null}"/>
                                    <f:convertNumber  groupingUsed="false"   maxFractionDigits="1"  />
                                </h:outputLabel>

                            </p:column>

                        </p:dataTable> 
                    </p:column>   


                </p:dataList>

                <p:dataTable  emptyMessage=" "   >


                    <p:column rendered="#{reportFilters.grp ne 2 }" style="width: 120px"

                              class="col_left">


                    </p:column>      
                    <p:column rendered="#{reportFilters.grp ne 1 }" style="width: 150px"  

                              class="col_left" >

                    </p:column> 
                    <p:column rendered="#{reportFilters.grp ne 0 }"  style="width: 120px"

                              class="col_left"  >


                    </p:column>

                    <p:column  style="width: 90px"
                               class="col_left" >

                    </p:column>

                    <p:column 
                        class="col_left"  
                        >




                        <f:facet name="footer">
                            <h:outputLabel value="Grand Total :"  style="float: right"  class="footerFont"/>
                        </f:facet>
                    </p:column>

                    <p:column  

                        class="col_right" >


                        <f:facet name="footer">
                            <h:outputLabel value="#{productDtlsItem.gtotPrevYr}"  style="float: right"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <p:column  class="col_right"   >


                        <f:facet name="footer">
                            <h:outputLabel value="#{productDtlsItem.gtotCurrYr}"  style="float: right"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <p:column class="col_right"  >              
                        <f:facet name="footer">
                            <h:outputLabel value="#{productDtlsItem.gtotYrGwth}"  style="float: right"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <!--Dashboard: Client requirement Added quarterly column Q1,Q2,Q3-->

                    <p:column   >


                        <f:facet name="footer">
                            <h:outputLabel value="#{productDtlsItem.gtotQ3}"  style="float: right"   class="footerFont">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <p:column  class="col_right"  >              


                        <f:facet name="footer">
                            <h:outputLabel value="#{productDtlsItem.gtotQ2}"  style="float: right"   class="footerFont">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <p:column   class="col_right" >              

                        <f:facet name="footer">
                            <h:outputLabel value="#{productDtlsItem.gtotQ1}"  style="float: right"  class="footerFont">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <p:column   class="col_right"  >              

                        <f:facet name="footer">
                            <h:outputLabel value="#{productDtlsItem.gtotQtrCurr}"  style="float: right"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <p:column  class="col_right"  >

                        <f:facet name="footer">
                            <h:outputLabel value="#{productDtlsItem.gtotQtrGwth}"  style="float: right"  class="footerFont">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>

                    </p:column>

                </p:dataTable>    


            </h:form>

            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
            <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
            <ui:include src="/lookup/PartNumDlg.xhtml"/>
           <!--3499 drill down-->
            <ui:include src="CustPartDetails.xhtml"/>

        </div>
        <style>

            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }
               /*14453 ESCALATIONS CRM-8464   Reporting Graphs Legend not legible - Multiple Instances*/
            .jqplot-axis {
                 font-size: 0.25em !important;
            }

            .jqplot-target {

                font-size: 0.75em!important;
            }
        </style>
        
        <!--#3153 Sales Reports - Add gif for loading --> 
        <script>
            
               window.onload = function () {
//                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("prodDetPrtrpt").style.pointerEvents = "all";
                    document.getElementById("prodDetPrtrpt").style.opacity = "1";
                   
                    $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };
            
            
            </script>
    </ui:define>
</ui:composition>