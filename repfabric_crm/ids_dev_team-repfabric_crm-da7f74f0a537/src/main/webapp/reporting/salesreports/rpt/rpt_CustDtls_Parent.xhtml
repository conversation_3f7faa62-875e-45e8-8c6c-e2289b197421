<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: CustomerSummary.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>
    <ui:define name="meta">
        <f:metadata> 

            <f:viewParam name="yr" value="#{reports.repYear}" />
            <f:viewParam name="mt" value="#{reports.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="d" value="#{reportFilters.distributor.compId}"/>
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>
            <f:viewParam name="m" value="#{reportFilters.minVal}"/>
            <f:viewParam name="g" value="#{reportFilters.grp}"/>
            <f:viewParam name="c" value="#{reportFilters.custselect.compId}"/>
            <f:viewParam name="repcode" value="#{custDetailRep.repcode}"/>
            <f:viewParam name="cn" value="#{reportFilters.custselect.compName}"/>
  <!--Bug #3499:  Thea - drill down for customer details not working-->
            <f:viewAction action="#{reportFilters.getSelCustName(reportFilters.custselect.compId)}" />
            <f:viewAction action="#{custDetailRep.loadData()}"/>

 <!--<f:viewAction action="#{custSummaryRep.getProcCustSummary()}"/>-->
        </f:metadata>

    </ui:define>
    <ui:define name="body">
        <f:event listener="#{custDetailRep.isPageAccessible}" type="preRenderView"/> 

        <!--#3153 Sales Reports - Add gif for loading -->
        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />



        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7" id="custDetPrt" >

            <h:form  id="custSummaryHeader">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >


                        <div class="compNamebg">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            Customer Details   For :   #{reportFilters.custselect.compName}
                            <br/>
                        </div>
                        &nbsp;&nbsp;<p:outputLabel value="Quarter : #{reports.repYear}-#{reports.quarter},  Month : #{reportFilters.getMonth(reports.repMonth )}" /> 
                    </div>
                    <!--                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="margin-left:300px">
                                            <p:panelGrid   columns="2"  layout="grid" style="width: 600px;float: right;"  id="barchart"> 
                                                <p:chart type="bar" model="#{reports.barChartYearly}"   style="width:285px;height:180px"/>
                                                <p:chart type="bar" model="#{reports.barChartQuarterly}"   style="width:285px;height:180px"/>
                                            </p:panelGrid>
                                        </div>  -->


                </div>

                <div  class="div-selected" >

                    <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                        #{reportFilters.custselect.compName}

                    </label> 
                </div>


                <p:dataTable  id="rptTable" value="#{custDetailRep.listCustomerDtls}" var="custdtl" emptyMessage="No data found"  >

                    <p:column sortBy="#{custdtl.princiname}" width="200" headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left" >

                        <!--#Too many tabs, fixed by replacing _blank to _self -->
                        <!--c3 added for secondary customer 1606 -->
                        <!--<h:outputLabel value="#{custdtl.princiname}"/>-->
                        <h:link title="View Product Details" value="#{custdtl.princiname}"  target="_self" outcome="rpt_ProdDtls_All.xhtml?yr=#{reports.repYear}&amp;mt=#{reports.repMonth}&amp;p=#{custdtl.id}&amp;c=#{reportFilters.custselect.compId}&amp;d=#{reportFilters.distributor.compId}&amp;s=#{reportFilters.salesTeam.smanId}&amp;g=0&amp;c3=0&amp;repcode=17&amp;pname=#{custdtl.princiname}" />
                        <f:facet name="footer">
                            <h:outputLabel style="float: right" value="Total"  class="footerFont" />
                        </f:facet>
                    </p:column>

                    <p:column sortBy="#{custdtl.repYearPrev}" headerText="Previous Year" class="col_right" >
                        <h:outputLabel value="#{custdtl.repYearPrev}"  style="float: right">
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right;"  value="#{custDetailRep.totalsCustDtls[0]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <p:column sortBy="#{custdtl.repYearCurr}" headerText="Current Year" class="col_right" >
                        <!--#Too many tabs, fixed by replacing _blank to _self -->
                        <h:link title="View #{custom.labels.get('IDS_CUSTOMER')} Details Product"    target="_self" outcome="rpt_CustDtls_Prod.xhtml?yr=#{reports.repYear}&amp;mt=#{reports.repMonth}&amp;p=#{custdtl.id}&amp;c=#{reportFilters.custselect.compId}&amp;d=#{reportFilters.distributor.compId}&amp;s=#{reportFilters.salesTeam.smanId}&amp;g=0&amp;pname=#{custdtl.princiname}&amp;sn=#{reportFilters.salesTeam.smanName}" >
                            <h:outputLabel style="cursor: pointer;float: right" value="#{custdtl.repYearCurr}"   >          
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </h:link>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailRep.totalsCustDtls[1]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <p:column sortBy="#{custdtl.repYearRr}" headerText="Year RR"  style="text-align: right">
                        <h:outputLabel value="#{custdtl.repYearRr}"
                                       style="#{
                                       custdtl.repYearRr gt 100 ?  'color:green' 
                                           : (custdtl.repYearRr lt 100 ? 'color:red' : 'color:black')
                               }"
                                       >
                            <!--CRM-185 - Incorrect rendering attribute value-->
                            <h:outputLabel value="%" rendered="#{custdtl.repYearRr ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1"/>
                        </h:outputLabel>
                    </p:column>     

                    <!--Dashboard: Client requirement Added quarterly column-->

                    <p:column sortBy="#{custdtl.repQrtr3}"  style="min-width: 50px;text-align:right"  headerText="#{salesReports.headervalQr[0]}" class="col_right">
                        <h:outputLabel value="#{custdtl.repQrtr3}" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailRep.totalsCustDtls[3]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>     

                    <p:column sortBy="#{custdtl.repQrtr2}" headerText="#{salesReports.headervalQr[1]}" style="min-width: 50px;text-align:right" class="col_right">
                        <h:outputLabel value="#{custdtl.repQrtr2}" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailRep.totalsCustDtls[4]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>     

                    <p:column sortBy="#{custdtl.repQrtr1}" headerText="#{salesReports.headervalQr[2]}" style="min-width: 50px;text-align:right"  class="col_right">
                        <h:outputLabel value="#{custdtl.repQrtr1}" style="float: right" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailRep.totalsCustDtls[5]}"  class="footerFont" >
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>  




                    <p:column sortBy="#{custdtl.repQrtrCurr}" headerText="Current Qtr"  class="col_right"  style="text-align:right">
                        <h:outputLabel value="#{custdtl.repQrtrCurr}" style="float: right" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailRep.totalsCustDtls[6]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>     

                    <p:column sortBy="#{custdtl.repQrtrRr}"  headerText="Qtr RR"  class="col_right"  style="text-align:right">
                        <h:outputLabel value="#{custdtl.repQrtrRr}"

                                       style="#{
                                       custdtl.repQrtrRr gt 100 ?  'color:green' 
                                           : (custdtl.repQrtrRr lt 100 ? 'color:red' : 'color:black')
                               }"

                                       ><h:outputLabel value="%" rendered="#{custdtl.repQrtrRr ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1"/>
                        </h:outputLabel>
                    </p:column>     

                </p:dataTable>
            </h:form>
        </div>
        <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />

        <style>

            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }


            .jqplot-target {

                font-size: 0.75em!important;
            }
        </style>
        <!--#3153 Sales Reports - Add gif for loading-->
        <script>
            window.onload = function () {
                //                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
                    //      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("custDetPrt").style.pointerEvents = "all";
                    document.getElementById("custDetPrt").style.opacity = "1";
                    $('#loading-image').hide();
                    //            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };
        </script>

    </ui:define>

    <ui:define name="footer">
        Repfabric Dashboard - #{custom.labels.get('IDS_CUSTOMER')} Details Report 

    </ui:define>
</ui:composition>
