<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                xmlns:pe="http://primefaces.org/ui/extensions"
                template="#{layoutMB.template}">
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>  Sales Analytical Summary</title>
    </ui:define>
    <ui:define name="meta">
        <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
        <f:metadata>
            <f:viewParam name="yr" value="#{reportFilters.repYear}" />
            <f:viewParam name="mt" value="#{reportFilters.repMonth}" />
            <f:viewParam name="c" value="#{reportFilters.customer.compId}" />
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}" />
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}" />
            <f:viewParam name="g" value="#{reportFilters.grp}" />
            <f:viewParam name="prt" value="#{reportFilters.parentComp.compId}" />
            <f:viewParam name="sF" value="#{reportFilters.summaryFlag}"/>
            <f:viewParam name="fdate" value="#{reportFilters.fromDate}"/>
            <f:viewParam name="tdate" value="#{reportFilters.toDate}"/>
            <f:viewParam name="af" value="#{reportFilters.asOf}"/>
            <f:viewParam name="f" value="#{globalParams.dateFormat}"/>
            <f:viewParam name="pro" value="#{reportFilters.processBy}"/>
            <f:viewAction action="#{reportFilters.assignCompanyRegions()}"/>
            <f:viewAction action="#{reportFilters.assignSalesTeams()}"/>
            <f:viewAction action="#{salesComparisonExtRep.run()}"/>
        </f:metadata>
    </ui:define>
    <ui:define name="body">
        <p:growl id="salValidator"  />

        <f:event listener="#{salesComparisonExtRep.isPageAccessible}" type="preRenderView"/> 

        <div class="box box-info box-body" style="vertical-align: top;" id="salesAnalyticalManu"  >

            <h:form id="salesCompProcess" >

                <div id="loading"  class="div-center" style="display:none;">

                    <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                    left: 600px;
                                    top: 300px;
                                    width: 150px;
                                    height: 150px;
                                    z-index: 9999; opacity: .3 !important;" />
                </div>  
                <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                <p:remoteCommand name="viewReport" id="remReport"  actionListener="#{reportFilters.setSalesteamlst()}" action="#{salesComparisonExtRep.run()}"  oncomplete="hideGif()"   update=":salesCompProcess:tbldata2  salValidator"/>

                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="salesCompProcess:inputCustomer "/>

                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="salesCompProcess:inputPrincName"  />


                <!--1704-->
                <!--<p:remoteCommand name="applyParentComp" actionListener="#{reportFilters.applyParentComp(viewCompLookupService.selectedCompany)}"  action="#{salesComparisonExtRep.defaultGrp()}" update="custSummaryForm:inputParentComp :custSummaryForm:viewreport :custSummaryForm:grpBy" />-->
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >

                        <div class="compNamebg" style="float:left;width:670px">
                            <h:outputLabel value="#{loginBean.subscriber_name}" />

                            <br/>

                            <h:outputLabel value="Sales Analytical Summary (by #{custom.labels.get('IDS_PRINCI')})" style="width:600px" />
                        </div>

                        <div  style="float:left;width:600px" >
                            <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                            <p:outputLabel  rendered="#{reportFilters.asOf eq 1}" value="As of: " />  <p:outputLabel  rendered="#{reportFilters.asOf eq 1}" value="#{reportFilters.repYear}" id="year"/> 

                            <p:outputLabel  rendered="#{reportFilters.asOf eq 2}" value="As of: " />   <p:outputLabel rendered="#{reportFilters.asOf eq 2}"  value="#{salesComparisonExtRep.repFrom}" id="fdate"/> 

                        </div>

                        <div  style="float:left;width:600px;margin-top:15px" >
                            <p:outputLabel style="font-weight: normal" rendered="#{reportFilters.compReglst.size() > 0}" value="*Filtered for selected #{custom.labels.get('IDS_REGION')}" />  
                        </div>


                    </div>
                    <div class="ui-sm-12 ui-md-1 ui-lg-3" >
                        <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                        <p:panelGrid rendered="#{reportFilters.asOf eq 1}" id="grid2" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;width:100%;margin-left:320px"       layout="grid" styleClass="box-primary no-border ui-fluid "  >
                            <p:outputLabel  value="Year"   />
                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" >
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />

                            </p:selectOneMenu>   

                            <p:outputLabel  value="Month"   />
                            <p:selectOneMenu height="280"   widgetVar="mt" value="#{reportFilters.repMonth}"  >
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 

                            </p:selectOneMenu>





                        </p:panelGrid>
                        <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                        <p:panelGrid rendered="#{reportFilters.asOf eq 2}"  id="grid3" columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7"
                                     style="min-height: 20px;margin-left:320px;width:90%"       layout="grid" styleClass="box-primary no-border ui-fluid ">
                            <p:outputLabel  value="From"/>
                            <h:panelGroup>

                                <p:calendar value="#{reportFilters.fdate}" showOn="button" pattern="#{globalParams.dateFormat}" id="fmdt" converterMessage="Please enter the valid From date">

                                    <p:ajax  event="keyup"  process="@this" />
                                    <p:ajax event="dateSelect" process="@this"  />
                                </p:calendar>
                            </h:panelGroup>

                            <p:outputLabel  value="To"/>
                            <h:panelGroup >

                                <p:calendar value="#{reportFilters.tdate}" showOn="button" pattern="#{globalParams.dateFormat}" id="todt" widgetVar="todate" converterMessage="Please enter the valid To date">
                                    <p:ajax  event="keyup"  process="@this" />
                                    <p:ajax event="dateSelect" process="@this"  />
                                </p:calendar>
                            </h:panelGroup>
                        </p:panelGrid>
                    </div>
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:270px">
                        <p:panelGrid id="grid" columns="2" 
                                     style="float: right;width:90%;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <p:outputLabel  value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                            <h:panelGroup class="ui-inputgroup" style="width: 130%" >
                                <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width:200%" id="selSalesTeam" >
                                    <f:selectItems value="#{reportFilters.salesTeams}" var="team"  
                                                   itemValue="#{team.smanId}"
                                                   itemLabel="#{team.smanName}"  />
                                </h:selectOneListbox>

                                <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.crmFlag()}"   action="#{lookupService.list('SALES_TEAM')}"   update=":frmSalesTeamLookup"  style="width:70px" oncomplete="PF('dlgSalesTeamsLookup').show();" />

                                <p:commandLink styleClass="sel-salesteam" value="Clear" actionListener="#{lookupService.clear('SALES_TEAM')}" update="@(.sel-salesteam)" disabled="#{!(reportFilters.salesTeams!=null and reportFilters.salesTeams.size() != 0)}" style="text-decoration:underline" />
                            </h:panelGroup>




                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                            <p:commandButton type="button" onclick="viewReport(); processGif()"  value="View Report"   id="viewreport"  styleClass="btn btn-primary btn-xs"   style="width:30px" />
<!--//     #12208 CRM-7384	Reports: Sales Analytical: RKR: Formatting of the Excel version-->
                            <p:menuButton id="btnExport" value="Export"   >
                                <p:menuitem id="btnUnformatted" value="Unformatted" ajax="false"  actionListener="#{salesComparisonExtRep.exportProcessData(true)}"/>
                                <p:menuitem id="btnFormatted" value="Formatted" ajax="false"  actionListener="#{salesComparisonExtRep.exportProcessData(false)}"/>
                            </p:menuButton>


                            <!--                            <p:commandButton value="Export"   style="width:30px"  
                                                                         ajax="false"  actionListener="#{salesComparisonExtRep.exportProcessData()}"  
                                                                         styleClass="btn btn-primary btn-xs" onclick="PrimeFaces.monitorDownload(start, stop);" 
                                                                         />-->


                        </p:panelGrid>


                    </div>

                </div>
                <h:panelGroup  id="tbldata2">
                    <ui:repeat  value="#{salesComparisonExtRep.listSalAnalysisRepProcess}" var="salcompProcess"  id="sid">


                        <p:dataList  type="definition"   id="dtrpt1" value="#{salcompProcess.listSalCompProcessList}"   var="salProc" >

                            <p:column >
                                <div class="div-selected" style="height:25px;width: 100%;" >
                                    <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                                        #{salProc.itemName}   

                                    </label>               
                                </div>
                                <p:dataTable   id="dtrpt"  value="#{salProc.details}" var="salesComp" paginatorAlwaysVisible="false" paginator="true"
                                               paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                               rows="50" emptyMessage="No data found" >

                                    <!--1455 :CRM-3090: mwpt:  can't sort report-->
                                    <p:column   class="col_left"   headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}"   sortBy="#{salesComp.repPrinciName}"  >
                                        <p:outputLabel value="#{salesComp.repCustName}"  />

                                    </p:column>
                                    <p:column   class="col_left"    headerText="#{custom.labels.get('IDS_SALES_TEAM')}"  sortBy="#{salesComp.repSmanName}" >
                                        <p:outputLabel value="#{salesComp.repSmanName}" />
                                        <f:facet  name="footer">
                                            <h:outputLabel   value="Total : "  style="float: right"  class="footerFont"/>
                                        </f:facet>
                                    </p:column>
                                    <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                                    <p:column headerText="#{reportFilters.asOf eq 2 ? salesComparisonExtRep.repFrom : salesComparisonExtRep.monthCurr}" style="text-align: right" sortBy="#{salesComp.repMonthCurr}">

                                        <h:outputLabel value="#{salesComp.repMonthCurr}"     >
                                            <f:convertNumber maxFractionDigits="0"/>
                                        </h:outputLabel>

                                        <f:facet name="footer">
                                            <h:outputLabel 
                                                value="#{salProc.totals[2]}"  style="float: right"  class="footerFont">
                                                <f:convertNumber   maxFractionDigits="0"/>
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>
                                    <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                                    <p:column headerText="#{reportFilters.asOf eq 2 ? salesComparisonExtRep.repDPrev : salesComparisonExtRep.monthPrev}" style="text-align: right" sortBy="#{salesComp.repMonthPrev}">

                                        <h:outputLabel value="#{salesComp.repMonthPrev}"  >
                                            <f:convertNumber maxFractionDigits="0"/>
                                        </h:outputLabel>

                                        <f:facet name="footer">
                                            <h:outputLabel 
                                                value="#{salProc.totals[1]}"  style="float: right"  class="footerFont">
                                                <f:convertNumber maxFractionDigits="0"/>
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>

                                    <p:column headerText="#{salesComparisonExtRep.yrCurr}"  style="text-align: right" sortBy="#{salesComp.repYtdCurr}" >
                                        <h:outputLabel value="#{salesComp.repYtdCurr}"  >
                                            <f:convertNumber maxFractionDigits="0"/>
                                        </h:outputLabel>

                                        <f:facet name="footer">
                                            <h:outputLabel 
                                                value="#{salProc.totals[3]}"  style="float: right"  class="footerFont" >
                                                <f:convertNumber maxFractionDigits="0"/>
                                            </h:outputLabel>

                                        </f:facet>
                                    </p:column>

                                    <p:column headerText="#{salesComparisonExtRep.yrPrev}"  style="text-align: right" sortBy="#{salesComp.repYtdPrev}">
                                        <h:outputLabel value="#{salesComp.repYtdPrev}"  >
                                            <f:convertNumber maxFractionDigits="0"/>
                                        </h:outputLabel>

                                        <f:facet name="footer">
                                            <h:outputLabel 
                                                value="#{salProc.totals[4]}"  style="float: right"  class="footerFont">
                                                <f:convertNumber maxFractionDigits="0"/>
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>



                                    <p:column headerText="Jan-Dec #{salesComparisonExtRep.year - 1}"  style="text-align: right" sortBy="#{salesComp.repYearPrev}">
                                        <h:outputLabel value="#{salesComp.repYearPrev}"  >
                                            <f:convertNumber maxFractionDigits="0"/>
                                        </h:outputLabel>

                                        <f:facet name="footer">
                                            <h:outputLabel 
                                                value="#{salProc.totals[5]}"  style="float: right"  class="footerFont">
                                                <f:convertNumber maxFractionDigits="0"/>            
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>


                                    <p:column headerText="YTD Var $"  style="text-align: right" sortBy="#{salesComp.repYtdVarAmt}">
                                        <h:outputLabel value="#{salesComp.repYtdVarAmt}"  >
                                            <f:convertNumber maxFractionDigits=""/>
                                        </h:outputLabel>

                                        <f:facet name="footer">
                                            <h:outputLabel 
                                                value="#{salProc.totals[6]}"  style="float: right"  class="footerFont" >
                                                <f:convertNumber maxFractionDigits="0"/>
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>


                                    <p:column headerText="Var %"  style="text-align: right" sortBy="#{salesComp.repYtdVarPct}">
                                        <h:outputLabel value="#{salesComp.repYtdVarPct}" style="#{
                                                                salesComp.repYtdVarPct gt 0 ?  'color:green' 
                                                                : (salesComp.repYtdVarPct lt 0 ? 'color:red' : 'color:black')}" >

                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>
                                        <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->
                                        <f:facet name="footer">
                                            <h:outputLabel style="#{
                                                           salesComparisonExtRep.calculateTotalPct(salProc.totals[6], salProc.totals[4]) gt 0 ?  'color:green' 
                                                               : (salesComparisonExtRep.calculateTotalPct(salProc.totals[6], salProc.totals[4]) lt 0 ? 'color:red' : 'color:black')}" 
                                                           value="#{salesComparisonExtRep.calculateTotalPct(salProc.totals[6], salProc.totals[4])}"   class="footerFont">
                                                <f:convertNumber  maxFractionDigits="0"/>
                                            </h:outputLabel>
                                        </f:facet>


                                    </p:column>


                                    <p:column headerText="YTD %"  style="text-align: right" sortBy="#{salesComp.repYtdPct}">
                                        <h:outputLabel value="#{salesComp.repYtdPct}"  >
                                            <f:convertNumber maxFractionDigits="2"/>

                                        </h:outputLabel>
                                        <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->

                                        <!--                                        <f:facet name="footer">
                                                                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                                                                   value="#{salProc.totals[7]}"  style="float: right"  class="footerFont">
                                                                                        <f:convertNumber maxFractionDigits="2"/>
                                                                                    </h:outputLabel>
                                                                                </f:facet>-->
                                    </p:column>


                                    <p:column headerText="LYT %"  style="text-align: right" sortBy="#{salesComp.repLytPct}">
                                        <h:outputLabel value="#{salesComp.repLytPct}"  >
                                            <f:convertNumber  maxFractionDigits="2"/>
                                        </h:outputLabel>
                                        <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->

                                        <!--                                        <f:facet name="footer">
                                                                                    <h:outputLabel 
                                                                                        value="#{salProc.totals[8]}"  style="float: right"  class="footerFont">
                                                                                        <f:convertNumber maxFractionDigits="2"/>
                                                                                    </h:outputLabel>
                                                                                </f:facet>-->
                                    </p:column>        



                                </p:dataTable>


                            </p:column>

                        </p:dataList>


                    </ui:repeat>
                    <p:dataTable  rendered="#{salesComparisonExtRep.listSalAnalysisRepProcess.size() >0}" emptyMessage=" " id="grandtotals" style="margin-top:-20px">

                        <p:column></p:column>


                        <p:column>
                            <f:facet name="footer" >
                                <h:outputLabel value="Grand Total :" style="float: right"   class="footerFont">

                                </h:outputLabel>
                            </f:facet>

                        </p:column>  
                        <p:column  > 

                            <f:facet name="footer">
                                <h:outputLabel value="#{salesComparisonExtRep.grandMonthPrev}"  style="float: right"  class="footerFont" >

                                    <f:convertNumber minFractionDigits="0"/>
                                </h:outputLabel>

                            </f:facet>


                        </p:column> 

                        <p:column> 

                            <f:facet name="footer">
                                <h:outputLabel  value="#{salesComparisonExtRep.grandMonthCurr}"  style="float: right"  class="footerFont" >

                                    <f:convertNumber minFractionDigits="0"/>
                                </h:outputLabel>

                            </f:facet>  


                        </p:column>  
                        <p:column> 


                            <f:facet name="footer">
                                <h:outputLabel value="#{salesComparisonExtRep.grandYtdCurr}"    style="float: right"  class="footerFont" >
                                    <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->
                                    <f:convertNumber minFractionDigits="0"/>
                                </h:outputLabel>

                            </f:facet>   
                        </p:column> 

                        <p:column> 


                            <f:facet name="footer">
                                <h:outputLabel value="#{salesComparisonExtRep.grandYtdPrev}"  style="float: right"  class="footerFont" >
                                    <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->
                                    <f:convertNumber minFractionDigits="0"/>
                                </h:outputLabel>

                            </f:facet>   
                        </p:column> 
                        <p:column> 


                            <f:facet name="footer">
                                <h:outputLabel value="#{salesComparisonExtRep.grandYearPrev}" style="float: right"  class="footerFont" >
                                    <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->
                                    <f:convertNumber minFractionDigits="0"/>
                                </h:outputLabel>

                            </f:facet>   
                        </p:column> 
                        <p:column> 


                            <f:facet name="footer">
                                <h:outputLabel  value="#{salesComparisonExtRep.grandYtdVarAmt}"  style="float: right"  class="footerFont" >
                                    <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->
                                    <f:convertNumber minFractionDigits="0"/>
                                </h:outputLabel>

                            </f:facet>   
                        </p:column> 
                        <p:column style="text-align: right" > 
                            <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->

                            <f:facet name="footer">
                                <h:outputLabel style="#{
                                               salesComparisonExtRep.calculateTotalPct(salesComparisonExtRep.grandYtdVarAmt, salesComparisonExtRep.grandYtdPrev) gt 0 ?  'color:green' 
                                                   : (salesComparisonExtRep.calculateTotalPct(salesComparisonExtRep.grandYtdVarAmt, salesComparisonExtRep.grandYtdPrev) lt 0 ? 'color:red' : 'color:black') }" value="#{salesComparisonExtRep.calculateTotalPct(salesComparisonExtRep.grandYtdVarAmt, salesComparisonExtRep.grandYtdPrev)}"  class="footerFont" >

                                    <f:convertNumber  minFractionDigits="0"/>
                                </h:outputLabel>

                            </f:facet>   
                        </p:column> 
                        <p:column  > 
                            <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->

                            <!--                            <f:facet name="footer">
                                                            <h:outputLabel  value="#{salesComparisonExtRep.grandYtdPct}"   style="float: right"  class="footerFont" >
                            
                                                                <f:convertNumber minFractionDigits="2"/>
                                                            </h:outputLabel>
                            
                                                        </f:facet>   -->
                        </p:column> 
                        <p:column  > 
                            <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->

                            <!--                            <f:facet name="footer">
                                                            <h:outputLabel  value="#{salesComparisonExtRep.grandLytPct}"  style="float: right"  class="footerFont" >
                            
                                                                <f:convertNumber minFractionDigits="2"/>
                                                            </h:outputLabel>
                            
                                                        </f:facet>   -->
                        </p:column> 

                    </p:dataTable>


                </h:panelGroup>
                <!--//     #12208 CRM-7384	Reports: Sales Analytical: RKR: Formatting of the Excel version-->
                <style>
                    .ui-button.ui-widget.ui-state-default.ui-corner-all.ui-button-text-icon-left {
                        height: 25px !important; font-size: 13px; background-color: #3c8dbc ! important;color: #fff !important

                    }
                    #salesCompProcess\:btnExport_button {
                        width:100px !important; height:23px !important; font-size: 12px !important; margin-top: -5px !important;
                    }
                </style>
                <script>

                   
                    function processGif()
                    {
                    $('#loading').show();
                    }
                    function hideGif()
                    {
                    $('#loading').hide();
                    }
                </script>    

            </h:form>
            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />

            <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>
        </div>
    </ui:define>

</ui:composition>
