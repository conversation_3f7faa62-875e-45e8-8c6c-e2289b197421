<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
  <!--Bug #1357: CRM-3045: Customer summary drill down to part number invoices showing min max pricing-->
    <h:form id="prodpartCust" >
    <p:dialog id="dlgCust" position="center"  footer="'Esc' to close"  resizable="false"
              closeOnEscape="true"
              header="#{partDetails.headerTitle}"               
              height="300" width="550" widgetVar="dlgPartCust">
        <div class="div-selected" style="height:20px;width: 100%;background: #9E9E9E" >
            <label style="font-size: 1em;font-weight: bold;color: white;line-height: 20px;padding-left: 5px;">
                Part No : #{partDetails.partNo}
                <h:outputLabel value="( #{partDetails.prodFamily}  )" rendered="#{(partDetails.prodFamily ne '') and (partDetails.prodFamily ne null) }"   />
            </label>
        </div>

        <p:dataTable style="width: 520px" 
                     id="tblPartCust" value="#{partDetails.listPartItem}" var="part"    widgetVar="parDtls">

            <p:column headerText="Date" width="100">
                <h:outputLabel value="#{reports.changeDateFormat(part.date, 1)}" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                </h:outputLabel>
            </p:column>

            <p:column headerText="Quantity"  width="100" class="col_right">
                <h:outputLabel value="#{part.qty}" >
                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                </h:outputLabel>
            </p:column>

            <p:column headerText="Unit Price"  width="100" class="col_right">
                <h:outputLabel value="#{part.unitPrice}" >
                       <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                       <!--<f:convertNumber groupingUsed="true" minFractionDigits="2" maxFractionDigits="6"/>-->
                      <f:convertNumber minFractionDigits="2"/>
                      <f:convertNumber maxFractionDigits="6" />
                      </h:outputLabel>
            </p:column>

            <p:column headerText="Amount"  width="100" class="col_right" >
                <h:outputLabel value="#{part.amt}" >
                      <!--<f:convertNumber minFractionDigits="2"/>-->
                  <f:convertNumber groupingUsed="true" minFractionDigits="2" maxFractionDigits="0"/>
                </h:outputLabel>
            </p:column>
        </p:dataTable>
        <!--Bug #1357: CRM-3045: Customer summary drill down to part number invoices showing min max pricing-->
</p:dialog>

    </h:form>
    </ui:composition>