<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: planvsActual .xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

                         <!--#363:page title-->
<ui:define name="head">
<link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
<title>     Plan vs Actual  </title>
</ui:define>
    <ui:define name="meta">
        <f:metadata>
            <f:viewParam name="yr" value="#{reports.repYear}" />
            <f:viewParam name="mt" value="#{reports.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="t" value="#{pvAPrincipals.reptype}"/>
            <f:viewParam name="rr" value="#{pvAPrincipals.repRR}"/>
            <f:viewParam name="repcode" value="#{pvAPrincipals.repcode}"/>
            <f:viewAction action="#{pvAPrincipals.runReport()}"/>
        </f:metadata>
    </ui:define>

    <ui:define  name="body">
        <f:event listener="#{pvAPrincipals.isPageAccessible}" type="preRenderView"/> 

        <!--//      #3153 Sales Reports - Add gif for loading-->
        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />




        <h:form id="planVsActualHeader" >
            <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update=":planVsActualHeader:inputPrincName " />
            <!--//      #3153 Sales Reports - Add gif for loading-->
            <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7" id="planvsactaulrpt" >
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            Plan vs Actual Report
                            <br/> 

                            As of : #{reports.repYear} - #{reportFilters.getMonth(reports.repMonth )}
                        </div>
                    </div>
                    <div class="ui-sm-12 ui-md-7 ui-lg-7" style="margin-left:60px">  

                    </div>

                    <div class="ui-sm-12 ui-md-2 ui-lg-2" style="margin-left:-60px">
                        <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                        <h:panelGroup class="ui-inputgroup"  >
    <!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                            <!--                                    <p:spacer width="50px"/>-->
                            <p:inputText id="inputPrincName" value="#{pvAPrincipals.repcode eq 7 ?  reports.filters[1] 
                                                                      : (pvAPrincipals.repcode eq 0 ? reportFilters.pricipal.compName : reportFilters.pricipal.compName)}"  placeholder="All" readonly="true"  style="margin-left:-70px"/>
                            <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                              actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                              update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                              styleClass="btn-info btn-xs" />
                        </h:panelGroup>  
                    </div>

                </div>
                <p:commandButton  value="Run Report "  id="viewreport"  style="margin-left:1113px;margin-top: -62px;"  styleClass="btn btn-primary btn-xs"  actionListener="#{pvAPrincipals.goToHeaderPlanVsPage1()}"  onclick="processGif();" />


                <ui:repeat value="#{pvAPrincipals.listPvaPrincis}" var="pvAPrincipals" >

                    <p:dataList type="definition" value="#{pvAPrincipals.listPvaDtls}" var="pvaDetails"    >
                        <!--<p:column>-->
                        <!--            <f:facet name="header">
                        #{pvAPrincipals.name}
                      </f:facet>-->

                        <div class="div-selected" style="height:25px;width: 100%;" >
                            <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                                #{pvAPrincipals.name}       
                            </label>
                        </div>

                        <p:panelGrid style="width: 100% !important"  >

                            <!--Period header row-->
                            <f:facet name="header">
                                <p:row>
                                    <p:column style="width: 150px" class="col_left"><h:outputLabel value="#{pvaDetails.planName}"/></p:column>
                                    <p:column class="pvaWidth"><h:outputLabel value="#{pvaDetails.periods[1]}"/></p:column>
                                    <p:column class="pvaWidth"><h:outputLabel value="#{pvaDetails.periods[2]}"/></p:column>
                                    <p:column class="pvaWidth"><h:outputLabel value="#{pvaDetails.periods[3]}"/></p:column>
                                    <p:column class="pvaWidth"><h:outputLabel value="#{pvaDetails.periods[4]}"/></p:column>
                                    <p:column  ><h:outputLabel value="#{pvaDetails.periods[5]}"/></p:column>
                                    <p:column  ><h:outputLabel value="#{pvaDetails.periods[6]}"/></p:column>
                                    <p:column  ><h:outputLabel value="#{pvaDetails.periods[7]}"/></p:column>
                                    <p:column  ><h:outputLabel value="#{pvaDetails.periods[8]}"/></p:column>
                                    <p:column  ><h:outputLabel value="#{pvaDetails.periods[9]}"/></p:column>
                                    <p:column  ><h:outputLabel value="#{pvaDetails.periods[10]}"/></p:column>
                                    <p:column  ><h:outputLabel value="#{pvaDetails.periods[11]}"/></p:column>
                                    <p:column  ><h:outputLabel value="#{pvaDetails.periods[12]}"/></p:column>
                                    <p:column style="width: 80px" class="col_right"  ><h:outputLabel value="Last Entry"/></p:column>
                                </p:row>
                            </f:facet>

                            <p:row>
                                <p:column class="col_left"><h:outputLabel value="Plan"/></p:column>

                                <p:column class="col_right"  style="text-align: right">
                                    <h:outputLabel value="#{pvaDetails.plans[1]}" >
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel>
                                </p:column>

                                <p:column class="col_right" style="text-align: right"><h:outputLabel value="#{pvaDetails.plans[2]}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column class="col_right" style="text-align: right" ><h:outputLabel value="#{pvaDetails.plans[3]}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column class="col_right" style="text-align: right" ><h:outputLabel value="#{pvaDetails.plans[4]}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column class="col_right" style="text-align: right" ><h:outputLabel value="#{pvaDetails.plans[5]}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column class="col_right" style="text-align: right" ><h:outputLabel value="#{pvaDetails.plans[6]}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column class="col_right" style="text-align: right" ><h:outputLabel value="#{pvaDetails.plans[7]}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column class="col_right" style="text-align: right"><h:outputLabel value="#{pvaDetails.plans[8]}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column class="col_right" style="text-align: right"><h:outputLabel value="#{pvaDetails.plans[9]}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column class="col_right" style="text-align: right"><h:outputLabel value="#{pvaDetails.plans[10]}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column class="col_right" style="text-align: right" ><h:outputLabel value="#{pvaDetails.plans[11]}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column class="col_right" style="text-align: right" ><h:outputLabel value="#{pvaDetails.plans[12]}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column class="col_right" style="width: 100px" ><h:outputLabel value="#{pvaDetails.lastEntry}">
                                        <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                    </h:outputLabel></p:column>
                            </p:row>

                            <!--Actuals Row-->
                            <p:row>
                                <p:column class="col_left"><h:outputLabel value="Actual"/></p:column>
                                <p:column style="background: #{pvaDetails.colors[1]};text-align: right" class="col_right" >
                                    <h:outputLabel value="#{pvaDetails.actuals[1]}" 
                                                   style="border-bottom: #{(pvaDetails.RRcol eq 1) ? '2px solid blue;' : 'none'}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column style="background: #{pvaDetails.colors[2]};text-align: right" class="col_right" >
                                    <h:outputLabel value="#{pvaDetails.actuals[2]}"
                                                   style="border-bottom: #{(pvaDetails.RRcol eq 2) ? '2px solid blue;' : 'none'}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column style="background: #{pvaDetails.colors[3]};text-align: right;" class="col_right" >
                                    <h:outputLabel value="#{pvaDetails.actuals[3]}"
                                                   style="border-bottom: #{(pvaDetails.RRcol eq 3) ? '2px solid blue;' : 'none'}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column style="background: #{pvaDetails.colors[4]};text-align: right" class="col_right" >
                                    <h:outputLabel value="#{pvaDetails.actuals[4]}" 
                                                   style="border-bottom: #{(pvaDetails.RRcol eq 4) ? '2px solid blue;' : 'none'}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column style="background: #{pvaDetails.colors[5]};text-align: right" class="col_right" >
                                    <h:outputLabel value="#{pvaDetails.actuals[5]}" 
                                                   style="border-bottom: #{(pvaDetails.RRcol eq 5) ? '2px solid blue;' : 'none'}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>
                                <p:column style="background: #{pvaDetails.colors[6]};text-align: right" class="col_right" >
                                    <h:outputLabel value="#{pvaDetails.actuals[6]}" 
                                                   style="border-bottom: #{(pvaDetails.RRcol eq 6) ? '2px solid blue;' : 'none'}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column style="background: #{pvaDetails.colors[7]};text-align: right" class="col_right" >
                                    <h:outputLabel value="#{pvaDetails.actuals[7]}" 
                                                   style="border-bottom: #{(pvaDetails.RRcol eq 7) ? '2px solid blue;' : 'none'}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column style="background: #{pvaDetails.colors[8]};text-align: right" class="col_right" >
                                    <h:outputLabel value="#{pvaDetails.actuals[8]}" 
                                                   style="border-bottom: #{(pvaDetails.RRcol eq 8) ? '2px solid blue;' : 'none'}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column style="background: #{pvaDetails.colors[9]};text-align: right" class="col_right" >
                                    <h:outputLabel value="#{pvaDetails.actuals[9]}" 
                                                   style="border-bottom: #{(pvaDetails.RRcol eq 9) ? '2px solid blue;' : 'none'}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column style="background: #{pvaDetails.colors[10]};text-align: right" class="col_right" >
                                    <h:outputLabel value="#{pvaDetails.actuals[10]}" 
                                                   style="border-bottom: #{(pvaDetails.RRcol eq 10) ? '2px solid blue;' : 'none'}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column style="background: #{pvaDetails.colors[11]};text-align: right" class="col_right" >
                                    <h:outputLabel value="#{pvaDetails.actuals[11]}" 
                                                   style="border-bottom: #{(pvaDetails.RRcol eq 11) ? '2px solid blue;' : 'none'}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column style="background: #{pvaDetails.colors[12]};text-align: right"  >
                                    <h:outputLabel value="#{pvaDetails.actuals[12]}" 
                                                   style="border-bottom: #{(pvaDetails.RRcol eq 12) ? '2px solid blue;' : 'none'}">
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                                    </h:outputLabel></p:column>

                                <p:column></p:column>
                            </p:row>

                        </p:panelGrid>

                    </p:dataList>

                </ui:repeat>
            </div>
        </h:form>

        <style>

            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }


            .jqplot-target {

                font-size: 0.75em!important;
            }
        </style>

        <!--//      #3153 Sales Reports - Add gif for loading-->
        <script>
            window.onload = function () {
//                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("planvsactaulrpt").style.pointerEvents = "all";
                    document.getElementById("planvsactaulrpt").style.opacity = "1";

                    $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };

            function processGif()
            {
                  $('#loading-image').show();
                document.getElementById("planvsactaulrpt").style.pointerEvents = "none";
                document.getElementById("planvsactaulrpt").style.opacity = "0.7";
            }


        </script>


        <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />

    </ui:define>



</ui:composition>
