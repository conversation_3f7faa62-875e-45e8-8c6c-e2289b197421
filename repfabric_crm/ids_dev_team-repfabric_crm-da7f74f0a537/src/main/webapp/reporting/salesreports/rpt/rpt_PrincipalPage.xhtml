<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: principalpage .xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <!--#363:page title-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>   #{custom.labels.get('IDS_PRINCI')} Page   </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>
            <f:viewParam name="yr" value="#{reports.repYear}" />
            <f:viewParam name="mt" value="#{reports.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.princiselect.compId}"/>
            <f:viewParam name="pn" value="#{reportFilters.princiselect.compName}"/>
            <f:viewParam name="repcode" value="#{principalPage.repcode}"/>
            <f:viewAction action="#{principalPage.run()}"/>
        </f:metadata>
    </ui:define>

    <ui:define name="body">
        <f:event listener="#{principalPage.isPageAccessible}" type="preRenderView"/> 

        <!--#3153 Sales Reports - Add gif for loading-->

        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />
        <!--#3153 Sales Reports - Add gif for loading style changes-->


        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7" id="princiPagerpt" >

            <h:form   id="princiPalpageForm">
                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrinciSelect(viewCompLookupService.selectedCompany)}" update="princiPalpageForm:inputPrincName  :princiPalpageForm:viewreport"  />


                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >


                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            #{custom.labels.get('IDS_PRINCI')} Page   
                            <br/> 


                        </div>

                        &nbsp;&nbsp;<p:outputLabel value="Quarter : #{reports.repYear}-#{reports.quarter},  Month : #{reportFilters.getMonth(reports.repMonth )}" /> 

                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="margin-left:60px">
                        <p:panelGrid   columns="2"  layout="grid" style="width: 600px;float: right;"> 
                            <p:chart type="bar" model="#{reports.barChartYearly}"   style="width:285px;height:180px"/>
                            <p:chart type="bar" model="#{reports.barChartQuarterly}"   style="width:285px;height:180px"/>
                        </p:panelGrid>
                    </div>  
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:-60px">
                        <p:panelGrid id="grid" columns="2" 
                                     style="float: right;width: 350px;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
        <!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.princiselect.compName}"  placeholder="[Select]" style="margin-left: -95px" />
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listPrincipals(1, 'applyPrinci')}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <div></div>
                            <p:commandButton  value="Run Report "   onclick="processGif();" id="viewreport"  style="width: 30px"  styleClass="btn btn-primary btn-xs"  actionListener="#{principalPage.runReport()}"   />


                        </p:panelGrid>

                    </div >

                </div>


                <div class="div-selected" style="height:25px;width: 100%;" >
                    <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                        #{reportFilters.princiselect.compName}
                    </label>               
                </div> 

                <div class="div-selected" style="height:30px;width: 100%; text-align: center">
                    <label style="font-size: 16px;color: black;line-height: 30px">Plan Vs Actual</label> 
                </div> 
                <p:dataTable  value="#{principalPage.listRptPrinciPage_PvA}" var="PvA"  id="rptTable"   >
                    <p:column  headerText="Plan" width="200" style="text-align: left;"  >
                        <h:outputLabel rendered="#{(PvA[4] eq 1)}" value="Forecast" />
                        <h:outputLabel rendered="#{(PvA[4] eq 2)}" value="#{globalParams.plan1}" />
                        <h:outputLabel rendered="#{(PvA[4] eq 3)}" value="#{globalParams.plan2}" />
                        <h:outputLabel rendered="#{(PvA[4] gt 3)}" value="#{(PvA[6])}" />
                    </p:column>

                    <p:column headerText="Status" width="100" style="text-align: center;height:50px" >

                        <!--# BUG 102 Too many tabs, fixed by added to _self -->


                        <p:commandButton  title="View Plan vs Actual#{PvA[7]}"    type="button" value="" class="btnRR"   style="background: #{salesReports.getRunRateColor(PvA[7])};"
                                          onclick="window.open('rpt_PlanVsActual.xhtml?p=#{PvA[2]}&amp;yr=#{reports.repYear}&amp;t=#{PvA[4]}&amp;repcode=7', '_self')"   />
                    </p:column>
                    <p:column headerText="Target" width="100"  style="text-align: right" >
                        <h:outputLabel value="#{PvA[8]}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                    </p:column>

                    <p:column headerText="Current" width="100"  style="text-align: right" >
                        <h:outputLabel value="#{PvA[9]}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_RUN_RATE')}"  width="100"  style="text-align: right"> 
                        <h:outputLabel value="#{PvA[10]}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                    </p:column> 

                    <p:column headerText="Period"  width="100" style="text-align: center" >
                        <h:outputLabel value="#{PvA[12]}" />
                    </p:column> 
<!--5686 CRM-4691: jobs and line overview: weird column spacing-->
                    <p:column headerText="Last Entry" width="100" style="text-align: center" >       
                        <h:outputLabel value="#{reports.changeDateFormat(PvA[13], 1)}" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </h:outputLabel>
                    </p:column> 

                </p:dataTable>

                <!--Feature #1583: CRM-1437: ROBCO: FR - be able to edit labels in reference to CRM v Fiscal areas-->

                <div class="div-selected" style="height:30px;width: 100%; text-align: center">
                    <label style="font-size: 16px;color: black;line-height: 30px"><p:outputLabel  value="Top 10 " />&nbsp;&nbsp;<p:outputLabel value=" #{custom.labels.get('IDS_CUSTOMERS')}"  rendered="#{!globalParams.allowDistriCommEnabled}" /> </label> 
                </div>           

                <p:dataTable   id="rptTable1" value="#{principalPage.listTop10Cust}" var="cust">
                    <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                    <!-- #6998: CRM-5238 db manufactgurer page: bad page layout - poornima - change wdith size -->
                    <p:column  headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" width="150" style="text-align: left;" 
                               sortBy="#{cust.itemName}"  >  

                        <!--# BUG 102 Too many tabs, fixed by added to _self -->
                        <!--1606 Secondary customer not in drill doen report so c3 is set to 0 in url to product details all 14feb 2019-->
                        <h:link  value="#{cust.itemName}" target="_self" outcome="rpt_ProdDtls_All.xhtml?yr=#{reportFilters.repYear}&amp;mt=#{reportFilters.repMonth}&amp;p=#{reportFilters.princiselect.compId}&amp;c=#{cust.id}&amp;d=#{salesReports.repDistri}&amp;s=#{reportFilters.salesTeam.smanId}&amp;g=0&amp;c3=0&amp;repcode=17"          />
                    </p:column>
                    
                    <!-- #6998: CRM-5238 db manufactgurer page: bad page layout - poornima - change wdith size -->
                    <p:column sortBy="#{cust.prevYr}" headerText="Prev. Year" width="50" style="text-align: right" >
                        <!--Previous year field not in table pls change col value-->
                        <h:outputLabel value="#{cust.prevYr}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                    </p:column>
                    
                    <!-- #6998: CRM-5238 db manufactgurer page: bad page layout - poornima - change wdith size -->
                    <p:column sortBy="#{cust.currYr}"  headerText="Current Year" width="50"  style="text-align: right" >
                        <h:outputLabel value="#{cust.currYr}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                    </p:column>
                    
                    <!-- #6998: CRM-5238 db manufactgurer page: bad page layout - poornima - change wdith size -->
                    <p:column sortBy="#{cust.runRate}" headerText="#{custom.labels.get('IDS_RUN_RATE')}" width="50" style="text-align: right">
                        <h:outputLabel value="#{cust.runRate}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                    </p:column>
                    
                    <!-- #6998: CRM-5238 db manufactgurer page: bad page layout - poornima - change wdith size -->
                    <p:column sortBy="#{cust.growth}" headerText="Growth" width="50" style="text-align: right"  >  
                        <h:outputLabel value="#{cust.growth}"
                                       style="#{
                                       cust.growth gt 0 ?  'color:green' 
                                           : (cust.growth lt 0 ? 'color:red' : 'color:black')
                               }" ><h:outputLabel value="%" rendered="#{cust.growth ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1" />
                        </h:outputLabel>

                    </p:column> 
                </p:dataTable>
                <!--Feature #1583: CRM-1437: ROBCO: FR - be able to edit labels in reference to CRM v Fiscal areas-->


                <!--Feature #2277:CRM-3499: Poklar - Principal Page question-->


                <div class="div-selected" style="height:30px;width: 100%; text-align: center"  >
                    <label style="font-size: 16px;color: black;line-height: 30px"><p:outputLabel value="#{custom.labels.get('IDS_DISTRIS')}" /> </label> 
                </div>     

                <!--Feature #2277:CRM-3499: Poklar - Principal Page question-->
                <p:dataTable   id="rptTable2" value="#{principalPage.listDistri}" var="dist"    >

                    <!--# BUG 102 Too many tabs, fixed by added to _self -->
                    <!--1606 Secondary customer not in drill doen report so c3 is set to 0 in url to product details all 14feb 2019-->
                    <!-- #6998: CRM-5238 db manufactgurer page: bad page layout - poornima - change wdith size -->
                    <p:column sortBy="#{dist.itemName}"  headerText="#{custom.labels.get('IDS_DISTRI')} "  style="text-align: left;"  width="150"  >                   
                        <h:link value="#{dist.itemName}" target="_self" outcome="rpt_ProdDtls_All.xhtml?yr=#{reportFilters.repYear}&amp;mt=#{reportFilters.repMonth}&amp;p=#{reportFilters.princiselect.compId}&amp;c=#{reportFilters.customer.compId}&amp;d=#{dist.id}&amp;s=#{reportFilters.salesTeam.smanId}&amp;g=1&amp;c3=0&amp;repcode=17"  />
                    </p:column>

                    <!-- #6998: CRM-5238 db manufactgurer page: bad page layout - poornima - change wdith size -->
                    <p:column sortBy="#{dist.prevYr}" headerText="Prev. Year" width="50" style="text-align: right" >
                        <!--Previous year field not in table pls change col value-->
                        <h:outputLabel value="#{dist.prevYr}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                    </p:column>
                    
                    <!-- #6998: CRM-5238 db manufactgurer page: bad page layout - poornima - change wdith size -->
                    <p:column sortBy="#{dist.currYr}" headerText="Current Year" width="50" style="text-align: right" >
                        <h:outputLabel value="#{dist.currYr}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                    </p:column>
                    
                    <!-- #6998: CRM-5238 db manufactgurer page: bad page layout - poornima - change wdith size -->
                    <p:column sortBy="#{dist.runRate}" headerText="#{custom.labels.get('IDS_RUN_RATE')}" width="50" style="text-align: right">
                        <h:outputLabel value="#{dist.runRate}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                    </p:column>
                    
                    <!-- #6998: CRM-5238 db manufactgurer page: bad page layout - poornima - change wdith size -->
                    <p:column sortBy="#{dist.growth}" headerText="Growth" width="50" style="text-align: right" >  
                        <h:outputLabel value="#{dist.growth}" 
                                       style="#{
                                       dist.growth gt 0 ?  'color:green' 
                                           : (dist.growth lt 0 ? 'color:red' : 'color:black')
                               }"

                                       ><h:outputLabel value="%" rendered="#{dist.growth ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1" />
                        </h:outputLabel>

                    </p:column> 
                </p:dataTable>
            </h:form>

            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />

        </div>




        <style>

            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }productDtlsItem
             /*14453 ESCALATIONS CRM-8464   Reporting Graphs Legend not legible - Multiple Instances*/
            .jqplot-axis {
                 font-size: 0.25em !important;
            }


            .jqplot-target {

                font-size: 0.75em!important;
            }


            /*#2526 CRM-1413: GRSSTohler: report doesn't print right*/
            @media print {
                a[href]:after {
                    content: none !important;
                }
            }
              /*14453 ESCALATIONS CRM-8464   Reporting Graphs Legend not legible - Multiple Instances*/
            .jqplot-axis {
                 font-size: 0.25em !important;
            }
        </style>

        <script>
            
            
            window.onload = function () {
                //                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
                    //      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("princiPagerpt").style.pointerEvents = "all";
                    document.getElementById("princiPagerpt").style.opacity = "1";

                    $('#loading-image').hide();
                    //            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };

            function processGif()
            {
                $('#loading-image').show();
                document.getElementById("princiPagerpt").style.pointerEvents = "none";
                document.getElementById("princiPagerpt").style.opacity = "0.7";
            }


        </script>


    </ui:define>
</ui:composition>