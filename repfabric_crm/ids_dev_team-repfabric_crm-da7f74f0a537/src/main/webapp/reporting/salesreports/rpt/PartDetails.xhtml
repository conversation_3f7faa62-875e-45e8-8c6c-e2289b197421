<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <h:form id="prodpart" >
    <p:dialog id="dlg" position="center"  footer="'Esc' to close"  resizable="false"
              closeOnEscape="true"
              header="#{partDetails.headerTitle}"               
              height="#{salesReports.repCml eq 0? 300:350}" width="#{reportFilters.repCml eq 0? 550:700}" widgetVar="dlgPart">
        <div class="div-selected" style="height:20px;width: 100%;background: #9E9E9E" >
            <label style="font-size: 1em;font-weight: bold;color: white;line-height: 20px;padding-left: 5px;">
                Part No : #{partDetails.partNo} 
                <h:outputLabel value="( #{partDetails.prodFamily}  )" rendered="#{partDetails.prodFamily ne ''}"   />
            </label>
        </div>

        <p:dataTable style="width: 520px" 
                     id="tblPart" value="#{partDetails.listPartItem}" var="part"  rendered="#{reportFilters.repCml eq 0}"  widgetVar="parDtls">

            <p:column headerText="Date" width="100">
                <h:outputLabel value="#{reports.changeDateFormat(part.date, 1)}" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                </h:outputLabel>
            </p:column>

            <p:column headerText="Quantity"  width="100" class="col_right">
                <h:outputLabel value="#{part.qty}" >
                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                </h:outputLabel>
            </p:column>

            <p:column headerText="Unit Price"  width="100" class="col_right">
                <h:outputLabel value="#{part.unitPrice}" >
                       <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                       <!--<f:convertNumber groupingUsed="true" minFractionDigits="2" maxFractionDigits="6"/>-->
                      <f:convertNumber minFractionDigits="2"/>
                      <f:convertNumber maxFractionDigits="6" />
                      </h:outputLabel>
            </p:column>

            <p:column headerText="Amount"  width="100" class="col_right" >
                <h:outputLabel value="#{part.amt}" >
                      <!--<f:convertNumber minFractionDigits="2"/>-->
                  <f:convertNumber groupingUsed="true" minFractionDigits="2" maxFractionDigits="0"/>
                </h:outputLabel>
            </p:column>
        </p:dataTable>
        <!--Bug #1357: CRM-3045: Customer summary drill down to part number invoices showing min max pricing-->
</p:dialog>
        <!--Bug #1357: CRM-3045: Customer summary drill down to part number invoices showing min max pricing-->
 <p:dialog id="dlgcumu" position="center"  footer="'Esc' to close"  resizable="false"
              closeOnEscape="true"
              header="#{partDetails.headerTitle}"               
              height="#{salesReports.repCml eq 0? 300:350}" width="#{reportFilters.repCml eq 0? 550:700}" widgetVar="dlgPartCumu">
     <div class="div-selected" style="height:20px;width: 100%;background: #9E9E9E" >
            <label style="font-size: 1em;font-weight: bold;color: white;line-height: 20px;padding-left: 5px;">
                Part No : #{partDetails.partNo} 
                <h:outputLabel value="( #{partDetails.prodFamily}  )" rendered="#{partDetails.prodFamily ne ''}"   />
            </label>
        </div>
        <!--Datatable to display Part details for Cumulative reports-->
        <p:dataTable style="width: 670px"  scrollable="true" scrollHeight="250" 
                     id="tblCumuPart" value="#{partDetails.listPartItem}" var="part"  rendered="#{reportFilters.repCml eq 1}">

            <p:column headerText="Customer" width="150">
                <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{part.custid}" target="_blank">
                    <h:outputLabel value="#{part.custName}" />
                </a>
            </p:column>

            <p:column headerText="Max. Unit Price"  width="70" class="col_right">
                <h:outputLabel value="#{part.maxUnitPrice}" >
                    <f:convertNumber  groupingUsed="true"/>
                </h:outputLabel>
            </p:column>

            <p:column headerText="Min. Unit Price"  width="70" class="col_right">
                <h:outputLabel value="#{part.minUnitPrice}" >
                    <f:convertNumber  groupingUsed="true"/>
                </h:outputLabel>
            </p:column>

            <p:column headerText="Total Quantity"  width="80" class="col_right">
                <h:outputLabel value="#{part.qty}" >
                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                </h:outputLabel>
            </p:column>

            <p:column headerText="Last Invoice Date" width="130" class="col_right">
                <h:outputLabel value="#{reports.changeDateFormat(part.invoiceDate, 1)}" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                </h:outputLabel>
            </p:column>

            <!--            <p:column headerText="Quantity"  width="70" class="col_right">
                            <h:outputLabel value="#{part.qty}" >
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </p:column>
            
                        <p:column headerText="Unit Price"  width="70" class="col_right">
                            <h:outputLabel value="#{part.unitPrice}" >
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </p:column>
            
                        <p:column headerText="Phone Number"  width="100" class="col_right" >
                            <h:outputLabel value="#{part.phoneNumber}" />
                        </p:column>-->


        </p:dataTable>

        <!--<h:outputLabel value="Press 'Esc' to close"  />-->
                <!--Bug #1357: CRM-3045: Customer summary drill down to part number invoices showing min max pricing-->
 </p:dialog>
    </h:form>

</ui:composition>