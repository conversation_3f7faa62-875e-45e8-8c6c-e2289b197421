<?xml version='1.0' encoding='UTF-8' ?> 
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: CustomerSummary.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

    <!--#363:page title-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
        <!--Bug #8183 PF 7 related bugs: Reports-->
        <title>  #{custom.labels.get('IDS_DB_CUSTOMER')} Summary </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata> 


            <f:viewParam name="yr" value="#{reports.repYear}" />
            <f:viewParam name="mt" value="#{reports.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="d" value="#{reportFilters.distributor.compId}"/>
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>
            <f:viewParam name="m" value="#{reportFilters.minVal}"/>
            <f:viewParam name="g" value="#{reportFilters.grp}"/>
            <f:viewParam name="c" value="#{reportFilters.customer.compId}"/>
            <!--//#2999Task Customer Summary report - Add multi select Sales team option-->
            <!--// #10203 :CRM-6433-->
            <!--<f:viewAction action="#{reportFilters.getSelectedCustName(reportFilters.customer.compId)}"/>-->

            
<!--<f:viewAction action="#{reportFilters.assignSalesTeams()}"/>-->
<!--<f:viewAction action="#{reportFilters.assignCompanyRegions()}"/>-->

            <f:viewAction action="#{custSummaryRep.run()}" />

        </f:metadata>
    </ui:define>

    <ui:define name="title">
        <ui:param name="title" value="Products"/>
        <div class="row">
            <div class="col-md-6">
                <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                #{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_CUSTOMER'):'Customer'} Summary    

            </div>

        </div>
    </ui:define> 
    <ui:define name="widthcss">
        <style>
            #wrapper {
                margin: 0 auto;
                width: 95%;
            }

            .ui-datatable thead th, .ui-datatable foot td{
                padding: 3px 6px;
            }


        </style>        
    </ui:define>

    <ui:define name="body">
        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .3 !important;" />
        <f:event listener="#{custSummaryRep.isPageAccessible}" type="preRenderView"/> 

        <!--#3116:Task :Sales Reports - Customer Summary - Add gif for loading-->

        <!--<img id="loading-image" src="images/ajax-loader.gif" alt="Loading..." />-->
        <!--</div>-->  


        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4" id="custSumRpt" >


            <!--<div class="box-header with-border">-->
            <!--<div class="row">-->
            <!--<div class="col-md-5">-->
            <h:form id="custSummaryHeader" >
                <!--// #10203 :CRM-6433-->
                <p:remoteCommand autoRun="true"  update=":custSummaryHeader:grid  :custSummaryHeader:graph"  />
                <!--Bug #3237:  CRM-3855: Dashboard Issues - Kevin can't see dashboard numbers-->
                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyOnlyCustomer(viewCompLookupService.selectedCompany)}" update=":custSummaryHeader:inputCustomer  " />
                <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update=":custSummaryHeader:inputDistriName  "/>
                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update=":custSummaryHeader:inputPrincName  " />
                <!--<p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update=":custSummaryHeader:inputAllSalesTeam " />-->
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >


                        <div class="compNamebg">
                            <h:outputLabel value="#{loginBean.subscriber_name}"   />
                            <br/>
                            <!--1833:CRM-2811: MerchantsSales: edit end user label-->
                            <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            #{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_DB_CUSTOMER'):'Customer'} Summary 
                            <br/>

                        </div>
                        &nbsp;&nbsp;&nbsp;<p:outputLabel value="Quarter : #{reports.repYear}-#{reports.quarter},  Month : #{reportFilters.getMonth(reports.repMonth )}" /> 
                                              <!--<p:outputLabel  value="As of : #{reports.repYear} - #{reports.getMonth(reports.repMonth)}" />-->

                        <!--465:region lookup-->
                        <!--#8692  ESCALATIONS CRM-6015  customer summary report by REGION--> 
                        <br/>
                        <br/>

                        &nbsp;&nbsp;&nbsp;<p:outputLabel value="*Filtered for selected #{custom.labels.get('IDS_REGION')}" style="font-weight:normal"   rendered="#{!(reportFilters.compRegnName.length() eq null) }"  />
                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="margin-left:60px" >

                        <p:panelGrid   columns="2"  layout="grid" style="width: 600px;float: right;"  id="graph"> 
                            <!--<p:autoUpdate >-->
                            <p:chart responsive="true"   type="bar" model="#{reports.barChartYearly}"   style="width:285px;height:180px"  widgetVar="yearly" />
                            <!--</p:autoUpdate>-->
                            <p:chart type="bar" model="#{reports.barChartQuarterly}"   style="width:285px;height:180px"  widgetVar="qtrly" />

                        </p:panelGrid>
                    </div>  
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:-60px">
                        <p:panelGrid id="grid" columns="2" 
                                     style="float: right;width: 350px;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
        <!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"  style="margin-left:-70px"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:outputLabel for="inputDistriName" value="#{custom.labels.get('IDS_DISTRI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputDistriName" value="#{reportFilters.distributor.compName}"  placeholder="All" readonly="true" style="margin-left:-70px"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyDistri', 3, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <!--//#2999Task Customer Summary report - Add multi select Sales team option-->
                            <!--//  1741  CRM-3259: Sales team:Thea: prevent users from seeing Sales and Comms -  Dashboard Sales Team Lookup-->

                            <p:outputLabel  value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <!--// #10203 :CRM-6433-->
                            <h:panelGroup class="ui-inputgroup" style="margin-left: -70px;width: 142%">
                                <h:selectOneListbox styleClass="sel-salesteam"  size="2"  style="width: 200%;" id="selSalesTeam" >
                                    <f:selectItems value="#{reportFilters.salesTeams}" var="team"  
                                                   itemValue="#{team.smanId}"
                                                   itemLabel="#{team.smanName}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.crmFlag()}"  action="#{lookupService.list('SALES_TEAM')}"  update=":frmSalesTeamLookup " oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                &nbsp;&nbsp;&nbsp;
                                <!--// #10203 :CRM-6433-->
                                <p:commandLink styleClass="sel-salesteam" value="Clear" actionListener="#{lookupService.clear('SALES_TEAM')}" update="@(.sel-salesteam)" disabled="#{!(reportFilters.salesTeams!=null and reportFilters.salesTeams.size() != 0)}" style="text-decoration:underline"/>
                            </h:panelGroup>



                            <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true" style="margin-left:-70px"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <!--#465-->

<!--                            <p:outputLabel value="#{custom.labels.get('IDS_COMP_REGION')}"  />
                            <h:panelGroup class="ui-inputgroup" style="margin-left: -70px;width: 142%" >
                                <h:selectOneListbox  styleClass="sel-region"  size="2" style="width: 200%" id="selRegion"     >
                                    <f:selectItems value="#{lookupService.compregionList}" var="reg"  
                                                 <p:outputLabel value="Filtered for selected Regions :#{reportFilters.compRegnName}"   rendered="#{!(reportFilters.compReglst.size()>10) and !(reportFilters.compRegnName.length() eq null) }"/>
                   itemValue="#{reg.recId}"
                                                   itemLabel="#{reg.compRegion}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_COMP')}" actionListener="#{lookupService.list('REGION')}" update=":frmCompRegionLookup" oncomplete="PF('dlgRegionLookup').show();"  />
                                &nbsp;&nbsp;&nbsp;
                                <p:commandLink styleClass="sel-region" value="Clear" actionListener="#{lookupService.clear('REGION')}" update="@(.sel-region)" disabled="#{!(lookupService.compregionList != null and lookupService.compregionList.size() != 0)}" style="text-decoration:underline" />
                            </h:panelGroup>-->

                            <p:outputLabel value="Minimum Sales"/>

                                <!--<p:inputText widgetVar="min"  value="#{reportFilters.minVal}" style="width: 80%">-->
                            <p:inputNumber    value="#{reportFilters.minVal}"   style="width: 80%;margin-left:-70px" minValue="0" decimalPlaces="2"  >

                                <!--<p:ajax  event="keyup"  update=":custSummaryHeader:viewreport"  />-->
                            </p:inputNumber>
                            <!--#3116:Task :Sales Reports - Customer Summary - Add gif for loading-->
                            <!--//        #465 Sales Reports > Customer Summary--> 
                            <p:commandButton  value="View Report "  id="viewreport"   styleClass="btn btn-primary btn-xs"  style="width:30px"   action="#{custSummaryRep.goToNavigationpage(2)}"  actionListener="#{reportFilters.setSalesteamlst()}" onclick="custSumVisibilty();" />

                            <!--//3810  Bug CRM-2281 FJM: Customer Summary report- you can not sort by "Previous Year"-->  

                            <p:column>
                                <p:commandLink id="xls" ajax="false"  >  
                                    <p:graphicImage name="/images/excel.png" width="24" onclick="PrimeFaces.monitorDownload(start, stop);" />
                                    <pe:exporter type="xlsx" target="custSumexport"  fileName="Customer_Summary_Report" subTable="false" postProcessor="#{custSummaryRep.postProcessXLS}"  />  

                                </p:commandLink>  

                                <p:commandLink id="pdf" ajax="false">  
                                    <p:graphicImage value="/resources/images/pdf.png"  width="24"  onclick="PrimeFaces.monitorDownload(start, stop);" />  
                                    <pe:exporter type="pdf" target="custSumexport" fileName="Customer_Summary_Report" subTable="false"  preProcessor="#{custSummaryRep.preProcessPDF}" />  

                                </p:commandLink>

                            </p:column>
                        </p:panelGrid>

                    </div >

                </div>
            <!--<p:button value="Run Report" id="viewreport"  style="margin-left:1113px;margin-top: -62px;"   styleClass="btn btn-primary btn-xs"  href="../../salesreports/rpt/#{salesSummaryParent.checkFlag eq true? 'rpt_CustSummary_prt.xhtml':'rpt_custSummary.xhtml'}?yr=#{reports.repYear}&amp;mt=#{reports.repMonth}&amp;p=#{reportFilters.pricipal.compId}&amp;c=#{reportFilters.customer.compId}&amp;d=#{reportFilters.distributor.compId}&amp;s=#{reportFilters.salesTeam.smanId eq null ? 0 : reportFilters.salesTeam.smanId}&amp;m=#{reportFilters.minVal eq null? 0:reportFilters.minVal}&amp;g=#{salesSummaryParent.checkFlag eq true? 1 : custSummaryRep.grp }" />-->

                <!--#987  :  CRM-2757: MSGinc customer summary Reports Timing out-->
                <!--#699: CRM-2638: JOHNSON: Sales Report > Customer Summary - won't run-->
                <!--#9356 CRM-2281  FJM: Customer Summary report- you can not sort by "Previous Year"-->
                <p:dataTable  value="#{custSummaryRep.custRepList}" var="custrep"  
                              widgetVar="custRepTbl" draggableRows="true"   scrollRows="50" id="custSummaryRepdt"
                              virtualScroll="true"   
                              scrollHeight="480"
                              nullSortOrder="-1"  
                              liveScroll="true"
                              scrollable="#{custSummaryRep.custRepList.size() ge 75}"

                              >
                    <!--1842 CRM-3324:  SaleReport private team filters - some clean up - UI-->
                    <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                    <p:column  headerText="#{custom.labels.get('IDS_DB_CUSTOMER')} Name" sortBy="#{custrep.repCustName}"  filterBy="#{custrep.repCustName}"  filterMatchMode="contains" style="text-align: left">

                        <h:link title="View Product Details" value="#{custsum.repCustName}" target="_self" outcome="rpt_ProdDtls_All.xhtml?yr=#{reports.repYear}&amp;mt=#{reports.repMonth}&amp;p=#{reportFilters.pricipal.compId}&amp;c=#{custrep.repCustId}&amp;d=#{reportFilters.distributor.compId}&amp;s=#{reportFilters.salesTeam.smanId}&amp;g=0&amp;cn=#{custrep.repCustName}&amp;c3=0&amp;repcode=17"  >
                            <p:outputLabel  value="#{custrep.repCustName}" style="cursor: pointer;text-decoration:underline" />
                        </h:link>
                        <f:facet name="footer">
                            <h:outputLabel value="Total :"  style="float: right"  class="footerFont"/>
                        </f:facet>
                    </p:column>
                    <!--#3810:   Bug CRM-2281 FJM: Customer Summary report- you can not sort by "Previous Year"-->
                    <p:column sortBy="#{custrep.repYearPrev}" headerText="#{custSummaryRep.grp eq 1 ? 'Previous' :'' } Year #{custSummaryRep.grp eq 1 ? '' :'-' } #{custSummaryRep.grp eq 1 ? '' :(reports.repYear)-1 }" style="text-align: right" filterBy="#{custrep.repYearPrev}" >
                        <h:outputLabel value="#{custrep.repYearPrev}" >

                            <!--#3201: Bug CRM-2001: customer summary report issues-->
                            <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel value="#{custSummaryRep.prevYearTotal}"  style="float: right"  class="footerFont">

                                <!--#3201: Bug CRM-2001: customer summary report issues-->
                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column >
                    <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->

                    <p:column  headerText="#{custSummaryRep.grp eq 1 ? 'Current' :'' } Year #{custSummaryRep.grp eq 1 ? '(Actual)' :'-' } #{custSummaryRep.grp eq 1 ? '' :(reports.repYear) }"   filterBy="#{custrep.repYearCurr}"  sortBy="#{custrep.repYearCurr}"  style="text-align: right;">
                        <h:link  title="View Details"    target="_self" outcome="rpt_CustDtls.xhtml?yr=#{reports.repYear}&amp;mt=#{reports.repMonth}&amp;p=#{reportFilters.pricipal.compId}&amp;c=#{custrep.repCustId}&amp;d=#{reportFilters.distributor.compId}&amp;s=#{reportFilters.salesTeam.smanId}&amp;g=0&amp;repcode=2"   style="text-decoration:underline">
                            <h:outputLabel style="cursor: pointer;text-decoration:underline" value="#{custrep.repYearCurr}" >

                                <!--#3201: Bug CRM-2001: customer summary report issues-->
                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                            </h:outputLabel>
                        </h:link>

                        <f:facet name="footer">
                            <h:outputLabel value="#{custSummaryRep.currYearTotal}"   class="footerFont">

                                <!--#3201: Bug CRM-2001: customer summary report issues-->
                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>

                    </p:column>
                    <p:column  headerText="% to LY" filterBy="#{custrep.repYearGrowth}" sortBy="#{custrep.repYearGrowth}" style="text-align: right;">
                        <p:outputLabel  value=" #{custrep.repYearGrowth}" style="text-align: right;"   >
                            <h:outputLabel value="%" rendered="#{custrep.repYearGrowth ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1"/>
                        </p:outputLabel>
                    </p:column>
                    <p:column  headerText="Direct Sales"  rendered="#{custSummaryRep.grp == 2}" filterBy="#{custrep.repSales1}" sortBy="#{custrep.repSales1}"  style="text-align: right;" >
                        <h:outputLabel value="#{custrep.repSales1}"  >
                            <f:convertNumber  groupingUsed="true"  maxFractionDigits="2"  minFractionDigits="2" />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel value="#{custSummaryRep.directSalesTotal}"  style="text-align: right;"  class="footerFont" >
                                <!--<f:convertNumber maxFractionDigits="2"/>-->
                            </h:outputLabel>
                        </f:facet>

                    </p:column>
                    <p:column  headerText="Credited Sales"  rendered="#{custSummaryRep.grp == 2 }" style="text-align:right" filterBy="#{custrep.repSales2}"  sortBy="#{custrep.repSales2}">

                        <p:commandLink process="@this" 
                                       value="#{custrep.repSales2}" actionListener="#{custSummaryRep.credcustSummary(custrep.repCustName,custrep.repSessionId  ,reports.repYear,reports.repMonth,reportFilters.pricipal.compId,custrep.repCustId,reportFilters.distributor.compId)}"
                                       oncomplete ="PF('dlgDirectSales').show()"  update=":custSummaryHeader:dlgCredit"
                                       >   

                        </p:commandLink>
                        <f:facet name="footer">
                            <h:outputLabel value="#{custSummaryRep.creditedSalesTotal}"  style="float: right"  class="footerFont">
                                <!--                                <f:convertNumber maxFractionDigits="2"/>-->
                            </h:outputLabel>
                        </f:facet>


                    </p:column>
                    <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                    <p:column  headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}  Sales"  style="text-align: right"  width="130" rendered="#{custSummaryRep.grp == 3}">

                    </p:column>   
                    <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                    <p:column  headerText="Primary+#{custom.labels.get('IDS_SECOND_CUSTOMER')}"  style="text-align: right"  width="130" rendered="#{custSummaryRep.grp == 3}">

                    </p:column>   
                </p:dataTable>


                <!--#3810 , 3811 //3810  Bug CRM-2281 FJM: Customer Summary report- you can not sort by "Previous Year"  
   export start-->




                <p:dataTable id="custSumexport"  value="#{custSummaryRep.custRepList}" var="custrep"   rendered="false">


                    <p:columnGroup  type="header" class="header"   >
                        <p:row>

                            <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                            <p:column />
                            <p:column rendered="#{custSummaryRep.grp == 2}"/>


                            <!--1833CRM-2811: MerchantsSales: edit end user label-->
                            <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:column headerText=" #{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_CUSTOMER'):'Customer'} Summary"/>

                            <p:column rendered="#{custSummaryRep.grp == 2}"/>
                            <p:column  headerText="Quarter : #{reports.repYear}-#{reports.quarter},  Month : #{reportFilters.getMonth(reports.repMonth )} #{(reportFilters.compRegnName.length() eq null)or (custrep.pfdexport)? '':'(Filtered for selected Regions)'}" />
                            <p:column rendered="#{custSummaryRep.grp == 3}"/>
                            <p:column rendered="#{custSummaryRep.grp == 3}"/>
                            <!--465 :add region-->
                            <p:column  rendered="#{custrep.pfdexport}"/>
                            <p:column rendered="#{custrep.pfdexport}"/>

                            <p:column  headerText="(Filtered for selected Regions)"   rendered="#{!(reportFilters.compReglst.size()>10) and !(reportFilters.compRegnName.length() eq null) and (custrep.pfdexport) }"/>



                        </p:row>
                        <p:row></p:row>

                        <p:row>
                            <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->

                            <p:column   headerText="#{!globalParams.allowDistriCommEnabled ? custom.labels.get('IDS_CUSTOMER'): 'Customer'} Name"  >

                            </p:column>

                            <p:column headerText="#{custSummaryRep.grp eq 1 ? 'Previous' :'' } Year #{custSummaryRep.grp eq 1 ? '' :'-' } #{custSummaryRep.grp eq 1 ? '' :(reports.repYear)-1 }" >

                            </p:column>

                            <p:column headerText="#{custSummaryRep.grp eq 1 ? 'Current' :'' } Year #{custSummaryRep.grp eq 1 ? '(Actual)' :'-' } #{custSummaryRep.grp eq 1 ? '' :(reports.repYear) }" >

                            </p:column>

                            <p:column headerText="% to LY" >

                            </p:column>

                            <p:column  headerText="Direct Sales"  rendered="#{custSummaryRep.grp == 2 }" >

                            </p:column>


                            <p:column headerText="Credited Sales"  rendered="#{custSummaryRep.grp == 2 }" >

                            </p:column>
                            <p:column  headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}  Sales"  style="text-align: right"  width="130" rendered="#{custSummaryRep.grp == 3}">

                            </p:column>   
                            <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                            <p:column  headerText="Primary+#{custom.labels.get('IDS_SECOND_CUSTOMER')}"  style="text-align: right"  width="130" rendered="#{custSummaryRep.grp == 3}">

                            </p:column> 
                            <!--465 :add region-->
                            <p:column   rendered="#{custrep.pfdexport}"/>
                            <p:column  rendered="#{custrep.pfdexport}"  />
                            <p:column rendered="#{custrep.pfdexport}" />
                        </p:row>


                    </p:columnGroup>

                    <p:column   class="col_left" >


                        <h:outputText  value="#{custrep.repCustName}" style="cursor: pointer;text-decoration:underline" />

                        <f:facet name="footer">
                            <h:outputText value="Total :"  style="float: right"  class="footerFont"/>
                        </f:facet>

                    </p:column>

                    <p:column>
                        <h:outputText value="#{custrep.repYearPrev}" style="text-align: right;">


                            <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText value="#{custSummaryRep.prevYearTotal}"  style="float: right"  class="footerFont">


                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>
                    </p:column>

                    <p:column>
                        <h:outputText value="#{custrep.repYearCurr}" style="text-align: right;" >


                            <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                        </h:outputText>

                        <f:facet name="footer">
                            <h:outputText value="#{custSummaryRep.currYearTotal}"   style="text-align: right;">


                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>

                    </p:column>

                    <p:column >
                        <h:outputText  value="#{custrep.repYearGrowth}#{custrep.repYearGrowth ne null ? '%' : ''}" style="text-align: right;"   >
                            <h:outputText value="%" rendered="#{custrep.repYearGrowth ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1"/>
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText value=""  style="text-align: right;"  class="footerFont" >

                            </h:outputText>
                        </f:facet>
                    </p:column>

                    <p:column rendered="#{custSummaryRep.grp == 2}"  >
                        <h:outputText value="#{custrep.repSales1}" style="text-align: right;" >
                            <f:convertNumber  groupingUsed="true"  maxFractionDigits="2"  minFractionDigits="2" />
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText value="#{custSummaryRep.directSalesTotal}"  style="text-align: right;"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="2"/>
                            </h:outputText>
                        </f:facet>
                    </p:column>




                    <p:column  rendered="#{custSummaryRep.grp == 2}" >
                        <h:outputText value="#{custrep.repSales2}"  style="text-align: right;" >   
                            <f:convertNumber maxFractionDigits="2"/>
                        </h:outputText>

                        <f:facet name="footer"  >
                            <h:outputText value="#{custSummaryRep.creditedSalesTotal}"  style="float: right"  class="footerFont">

                            </h:outputText>
                        </f:facet>

                    </p:column>

                    <p:column    style="text-align: right"  width="130" rendered="#{custSummaryRep.grp == 3}">

                    </p:column>   
                    <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                    <p:column   style="text-align: right"  width="130" rendered="#{custSummaryRep.grp == 3}">

                    </p:column> 
                    <!--465 :add region-->
                    <p:column  rendered="#{custrep.pfdexport}"/>
                    <p:column rendered="#{custrep.pfdexport}"/>
                    <p:column  rendered="#{custrep.pfdexport}"/>

                </p:dataTable>





                <!--Export end-->















                <p:dialog id="dlgCredit" position="center"  footer="'Esc' to close"  resizable="false"
                          closeOnEscape="true"           
                          height="#{reports.repType eq 0? 200:250}" width="#{reports.repType eq 0? 550:600}" widgetVar="dlgDirectSales">
                    <f:facet name="header" >
                        <h:outputText id="dlgHeader" value="Credited Sales Breakup - #{custSummaryRep.repCustName}" />
                    </f:facet>

                    <p:dataTable id="rpCedit" value="#{custSummaryRep.creditedCustomers}" var="custsumdlg" 
                                 emptyMessage="No data found" >


                        <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                        <!--<p:column headerText="Secondary Customer" width="90"  >-->
                        <p:column headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" width="90"  >
                            <h:outputLabel value="#{custsumdlg.custName}" >
                            </h:outputLabel>
                        </p:column>
                        <p:column headerText="Sales Share" width="70"  >
                            <h:outputLabel value="#{custsumdlg.salesShare}" style="float:right">
                            </h:outputLabel>
                        </p:column>

                        <p:column headerText="Credited"  width="70">
                            <h:outputLabel value="#{custsumdlg.custCredit}"  style="float:right">
                                <f:convertNumber  groupingUsed="true"   minFractionDigits="2"  />
                            </h:outputLabel>
                        </p:column>


                    </p:dataTable>

                </p:dialog>

            </h:form>

            <p:dialog widgetVar="inCustSumProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Please wait...Exporting records" />
                        <br /><br />
                        <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                    </p:outputPanel>
                </h:form>
            </p:dialog>



            <!--</div>-->

            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
            <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>
            <!--#456-->
            <ui:include src="/lookup/CompanyRegionLookUp.xhtml" />

        </div>
        <style>

            .ui-datatable-scrollable-theadclone{
                visibility: hidden;
            }
            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }
 /*14453 ESCALATIONS CRM-8464   Reporting Graphs Legend not legible - Multiple Instances*/

            .jqplot-axis {
                 font-size: 0.25em !important;
            }
            
            .jqplot-target {

                font-size: 0.75em!important;
            }
            /*2627 - Related to CRM-1177: Doran associates: Remove link from printed reports - sharvani - 11-12-2019*/
            @media print {
                a[href]:after {
                    content: none !important;
                }
            }
            /*            #bckDetForm\:backogrecordDT ,body .ui-datatable .ui-datatable-scrollable-header{
                                        margin-left: -1000px;
                                                            } */

            /*                                               */
            /* #custSummaryHeader\:custSummaryRepdt.ui-datatable-scrollable-body{
                height: calc(100vh - 420px);
            }*/


        </style>
                                                <!--#3116:Task :Sales Reports - Customer Summary - Add gif for loading-->
        <script>
                                        window.onload = function () {
                                                //                $('#loading-image').show();
                                                setTimeout(function () {
                                                        var t = performance.timing;
                                                        console.log(t.loadEventEnd - t.responseEnd);
                                                        //      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                                                        document.getElementById("custSumRpt").style.pointerEvents = "all";
                                                document.getElementById("custSumRpt").style.opacity = "1";
                                                // <!--#987  :  CRM-2757: MSGinc customer summary Reports Timing out-->
                                                
//                    var s = jQuery('#custSummaryHeader\\:custSummaryRepdt.ui-datatable-scrollable-body').animate({scrollHeight: (screen.height)});
//                    console.log("screen.height::" + s);
                                                        $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };


            function custSumVisibilty()
            {

                $('#loading-image').show();
                document.getElementById("custSumRpt").style.pointerEvents = "none";
                document.getElementById("custSumRpt").style.opacity = "0.4";

            }

            function start() {

                PF('inCustSumProgressDlg').show();
            }

            function stop() {
                PF('inCustSumProgressDlg').hide();
            }



        </script>

    </ui:define>


</ui:composition>