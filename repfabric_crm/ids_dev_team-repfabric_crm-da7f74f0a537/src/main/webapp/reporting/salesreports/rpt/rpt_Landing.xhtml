<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: productdetails All.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

        <!--#363 :page title-->
     
                 <ui:define name="head">
<link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
<title> Line Overview </title>
</ui:define>
    <ui:define name="meta">
        <f:metadata>
      <!--#4775-->
            <f:viewParam name="yr" value="#{reportFilters.repYear}" />
            <f:viewParam name="mt" value="#{reportFilters.repMonth}" />
<!--4775: Sales and Commission Reports: Remember date range for session-->
            <!--<f:viewAction action="#{salesReports.reSetDateMonth()}" />-->
            <f:viewAction action="#{salesReports.setMeterGph(reports.repYear)}"  />

            <f:viewAction action="#{landing.run()}"/>

        </f:metadata>
    </ui:define>
    <ui:define  name="body">
        <!--#3153 Sales Reports - Add gif for loading-->
        <f:event listener="#{landing.isPageAccessible}" type="preRenderView"/> 


        <!--//      #3153 Sales Reports - Add gif for loading-->
        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />

        <!--//      #3153 Sales Reports - Add gif for loading-->
        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7" id="lineOveviewrpt" >
            <h:form   id="lineoverview" >

                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >


                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            Sales Line Overview
                            <br/>
                            <!--428 :sales report->line overview,customer summary->year update issue-->
                            
                             &nbsp;&nbsp; <p:outputLabel value="As of:" /> &nbsp;  <p:outputLabel value="#{reportFilters.repYear}" id="year"/> 
                
                        </div>

                    </div>
                    <div class="ui-sm-12 ui-md-7 ui-lg-7" style="margin-left: 128px;" >
<!--                        <h:panelGrid columns="1"  id="landing" style="float: right;margin-left:-150px">
                            <p:chart type="metergauge" model="#{salesReports.meterGauge}" style="width:400px;height:170px;"  />         
                        </h:panelGrid>-->

                    </div>  
                    <!--                    <div class="ui-sm-12 ui-md-4 ui-lg-4" >
                    
                                        </div>-->
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" style="margin-left: -35px;">
                        <p:panelGrid id="grid2" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;width:100%;"       layout="grid" styleClass="box-primary no-border ui-fluid "  >
                            <p:outputLabel  value="Year"   />
                            <!--#4775: session retain-->
                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" >
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />
                                
                                <!--428 :sales report->line overview,customer summary->year update issue-->
                                 <!--#4775: session retain-->
                               <p:ajax event="change"  listener="#{landing.run()}"   process="@this" onstart="processGif();"  update="lineoverview:rptTable" oncomplete="hideGif()"  />
                          
                            </p:selectOneMenu>   

                            <p:outputLabel  value="Month"   />
                             <!--#4775: session retain-->
                            <p:selectOneMenu height="280"   widgetVar="mt" value="#{reportFilters.repMonth}"  >
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 
                                <!--//      #3153 Sales Reports - Add gif for loading-->
                                 <!--#4775: session retain-->
                                <p:ajax event="change"  listener="#{landing.run()}"   process="@this" onstart="processGif();"  update="lineoverview:rptTable" oncomplete="hideGif()"  />
                          
                            </p:selectOneMenu>





                        </p:panelGrid>


                    </div>
                </div>     




                <p:dataTable   id="rptTable" style="width: 100%" 
                               value="#{landing.listRptLanding}" var="land"
                               emptyMessage="No data found"     >

                    <p:column sortBy="#{land.princiName}"    
                              style="text-align: left;height:28px;width:15%" 
                              headerText="#{custom.labels.get('IDS_PRINCI')}" >
                        <h:outputLabel value="#{land.princiName}" />
                    </p:column>

                    <p:column sortBy="#{land.yearRRcolor}" headerText="Year #{custom.labels.get('IDS_RUN_RATE')}"  class="runRateCol" >
                        <!--# BUG 102 Too many tabs, fixed by added to _self -->
                        <p:commandButton title="View #{custom.labels.get('IDS_PRINCI')} Page"    
                                         type="button" value="" 
                                         class="btnRR"   style="background: #{land.yearRRcolor};"
                                         onclick="window.open('rpt_PrincipalPage.xhtml?p=#{land.princiId}&amp;yr=#{salesReports.repYear}&amp;mt=#{salesReports.repMonth}&amp;repcode=5&amp;pn=#{land.princiName}', '_self')"  />
                    </p:column>

                    <p:column sortBy="#{land.qtrRRcolor}" headerText="Quarter #{custom.labels.get('IDS_RUN_RATE')}" class="runRateCol"  >
                        <!--# BUG 102 Too many tabs, fixed by added to _self -->

                        <p:commandButton title=" View #{custom.labels.get('IDS_PRINCI')} Page"  type="button" 
                                         value="" class="btnRR" style="background: #{land.qtrRRcolor};"
                                         onclick="window.open('rpt_PrincipalPage.xhtml?p=#{land.princiId}&amp;yr=#{salesReports.repYear}&amp;mt=#{salesReports.repMonth}&amp;repcode=5&amp;pn=#{land.princiName}', '_self')" />
                    </p:column>

                    <p:column headerText="#{globalParams.plan1}" class="runRateCol"  >   
                        <!--# BUG 102 Too many tabs, fixed by added to _self -->

                        <p:commandButton title="View #{globalParams.plan1}"  type="button" value="" class="btnRR" 
                                         style="background: #{land.plan1Color};"
                                         onclick="window.open('rpt_PlanVsActual.xhtml?p=#{land.princiId}&amp;yr=#{salesReports.repYear}&amp;t=2&amp;repcode=7', '_self')" />

                    </p:column>  
                    <!--# BUG 102 Too many tabs, fixed by added to _self -->


                    <p:column headerText="#{globalParams.plan2}" class="runRateCol" >                    
                        <p:commandButton title="View #{globalParams.plan2}"  type="button" value="" 
                                         class="btnRR" style="background: #{land.plan2Color};" 
                                         onclick="window.open('rpt_PlanVsActual.xhtml?p=#{land.princiId}&amp;yr=#{salesReports.repYear}&amp;t=3&amp;repcode=7', '_self')" />

                    </p:column>

                    <!--# BUG 102 Too many tabs, fixed by added to _self -->

                    <p:column headerText="Year Forecast" class="runRateCol"  >
                        <p:commandButton title="View Forecast"  type="button" value="" class="btnRR"  
                                         style="background: #{land.yearForcastColor};"
                                         onclick="window.open('rpt_PlanVsActual.xhtml?p=#{land.princiId}&amp;yr=#{salesReports.repYear}&amp;t=1&amp;repcode=7', '_self')"  />

                    </p:column>

                    <p:column headerText="Last Entry" class="runRateCol"  >
                        <h:outputLabel value="#{reports.changeDateFormat(land.lastDate, 1)}">
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </h:outputLabel>

                    </p:column>  
                </p:dataTable> 
            </h:form>

            <style>

                .ui-panelgrid .ui-panelgrid-cell {
                    padding-top: 3px !important;
                    padding-bottom: 3px !important;
                }

                [role="gridcell"]{

                    padding: 5px 10px !important;
                }
                .content-header{     display: none; }


                .jqplot-target {

                    font-size: 0.75em!important;
                }
            </style>
            <!--//      #3153 Sales Reports - Add gif for loading-->
            <script>
                window.onload = function () {
//                $('#loading-image').show();
                    setTimeout(function () {
                        var t = performance.timing;
                        console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                        document.getElementById("lineOveviewrpt").style.pointerEvents = "all";
                        document.getElementById("lineOveviewrpt").style.opacity = "1";

                        $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                    }, 0);
                };


                function processGif()
                {
                    $('#loading-image').show();
                    document.getElementById("lineOveviewrpt").style.pointerEvents = "none";
                    document.getElementById("lineOveviewrpt").style.opacity = "0.7";
                }

                function hideGif()
                {

                    $('#loading-image').hide();

                    document.getElementById("lineOveviewrpt").style.pointerEvents = "all";
                    document.getElementById("lineOveviewrpt").style.opacity = "1";
                }




            </script>
            <!--//      #3153 Sales Reports - Add gif for loading-->

        </div>
    </ui:define>

    <!--    <ui:define  name="footer">
            
                 Repfabric Dashboard - Landing Report 
            
        </ui:define>-->
</ui:composition>