<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: productdetails All.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"

                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

    <!--#363:page title-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>    Sales by Month    </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>

            <f:viewParam name="yr" value="#{reportFilters.repYear}" />
            <f:viewParam name="d" value="#{monthlyExpandedRep.drillFlag}" />
            <f:viewParam name="g" value="#{reportFilters.grp}" />
            <!--//1784:Sales by Month > Add Region Filter commenteds-->
          <!--<f:viewAction action="#{salesReports.reSetDateMonth()}" />-->
            <!--#1900 CRM-1102: AIMR: sales by month only shows one Mfg-->
            <!--<f:viewAction action="#{salesReports.resetFilters()}" />-->
            <!--//1784:Sales by Month > Add Region Filters-->
            <f:viewAction action="#{monthlyExpandedRep.runReport(1)}"/>
            <!--<f:viewAction action="#{reportFilters.assignSalesTeams()}"/>-->

        </f:metadata>
    </ui:define>
    <ui:define  name="body">

        <f:event listener="#{monthlyExpandedRep.isPageAccessible}" type="preRenderView"/>  

        <!--#3153 Sales Reports - Add gif for loading-->
        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />
        <!--<img id="loading-image" src="images/ajax-loader.gif" alt="Loading..." />-->
        <!--</div>-->  

        <!--#3153 Sales Reports - Add gif for loading-->
        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4" id="salbymonthrpt" >
            <h:form id="salesByMonth" >
                <!--<p:remoteCommand name="onload" autoRun="true" process="@this" immediate="true" onstart="PF('statusDialog').show()"   oncomplete="PF('statusDialog').hide()"   />-->


                <div class="ui-g ui-fluid" id="rptheader">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >


                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            Sales by Month  
                            <br/>

                        </div>
                        <!--//#3785: Bug CRM-2265Sales reports-->
                        
                        <p:outputLabel value=" As of : #{reportFilters.repYear} " id="year" />
                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="padding-left:100px"  >
                        <h:panelGrid columns="1"  id="salesbymonth" >
                            <p:chart type="bar" model="#{reports.barChartYearly}"   style="width:550px;height:180px;float: left" widgetVar="mnthgrp"  />
                        </h:panelGrid>
                    </div>  

                    <div class="ui-sm-12 ui-md-2 ui-lg-2"  style="width:20%;padding-left:140px">
                        <h:panelGrid columns="1"  >

                            <table style="width: 100%;">
                                <tr style="background-color: #CAC9C9;">
                                    <th style="width: 200px">Group By</th>
                                </tr>
                                <tr>

                                    <!--                            <p:outputLabel value="Group By" /> -->
                                    <p:selectOneRadio class="filterbox" value="#{reportFilters.grp}" id="repgrp" layout="pageDirection"  required="true" >
                                        <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/> 
                                        <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                        <f:selectItem itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                                        <!--//1784:Sales by Month > Add Region Filters commented--> 
<!--<p:ajax listener="#{monthlyExpandedRep.runReport(1)}" update=":salesByMonth:dtrpt  :salesByMonth:salesbymonth"  onstart="processGif();"  oncomplete="hide();"/>-->

                                    </p:selectOneRadio>
                                </tr>   
                            </table>
                            <br></br>
                            <!--<div></div>-->
                            <!--                        </h:panelGrid>
                                                </div>
                                                <div class="ui-sm-12 ui-md-1 ui-lg-1" >
                                                    <h:panelGrid columns="1" style="padding-left:100px" >-->
                            <table style="width: 100%;">
                                <tr style="background-color: #CAC9C9;">

                                    <th style="width: 100px"> Sales Year</th> </tr>
                                <tr>
                                    <td>
                                        <!--BugCRM-2265:Sales reports-->
                                         <!--// #10203 :CRM-6433-->
                                        <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}"  >
                                             <p:ajax     event="change"   process="@this" />
                                            <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                            <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                            <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                            <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                            <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                            <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                            <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                            <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                            <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                            <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                            <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />
                                            <!--//1784:Sales by Month > Add Region Filters commented-->          
           <!--<p:ajax   event="change" onstart="processGif();"  listener="#{monthlyExpandedRep.runReport(1)}" update=":salesByMonth:dtrpt  :salesByMonth:salesbymonth :salesByMonth:year"   oncomplete="hide();" />-->
                                        </p:selectOneMenu>
                                    </td>
                                </tr>

                            </table>
                        </h:panelGrid>  
                    </div>
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" style="padding-left:20px" >
                        <table style="width: 100%;">
                            <tr style="background-color: #CAC9C9;">

                                <th style="width:100%"> #{custom.labels.get('IDS_SALES_TEAM')}</th> </tr>

                            <tr>
                                <td>
                                    <br/>
                                    <h:panelGrid columns="1" style="width:100%;margin-top: -7px"  >
                                        <!--<p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')}" />-->
                                         <!--// #10203 :CRM-6433-->
                                        <h:panelGroup class="ui-inputgroup"  >
                                            <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width: 100%;" id="selSalesTeam" >
                                                <f:selectItems value="#{reportFilters.salesTeams}" var="team"  
                                                               itemValue="#{team.smanId}"
                                                               itemLabel="#{team.smanName}"  />
                                            </h:selectOneListbox>

                                            <!--//  1741  CRM-3259: Sales team:Thea: prevent users from seeing Sales and Comms -  Dashboard Sales Team Lookup-->

                                            <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.crmFlag()}"  action="#{lookupService.list('SALES_TEAM')}" update=":frmSalesTeamLookup" oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                            &nbsp;&nbsp;&nbsp;
                                             <!--// #10203 :CRM-6433-->
                                            <p:commandLink styleClass="sel-salesteam" value="Clear" actionListener="#{lookupService.clear('SALES_TEAM')}" update="@(.sel-salesteam)" disabled="#{!(reportFilters.salesTeams!=null and reportFilters.salesTeams.size() != 0)}" style="text-decoration:underline"/>
                                        </h:panelGroup>
                                        <p:spacer width="4px" />
                                        <!--1784-->
                                        <p:commandButton  value="Run Report "  id="viewreport"   style="width: 30px;" styleClass="btn btn-primary btn-xs"  actionListener="#{monthlyExpandedRep.goToNavigation()}"  update=":salesByMonth:dtrpt :salesByMonth:salesbymonth" onclick="processGif();"   />
                                        <!--//#2949:Task CRM-1854: caltron: reports: make sales by month report exportable into excel-->

                                        <p:column>
                                            <p:commandLink id="xls" ajax="false" onclick="PrimeFaces.monitorDownload(start, stop);" >  
                                                <p:graphicImage name="/images/excel.png" width="24"/>
                                                <pe:exporter type="xlsx" target="dtrpt1"  fileName="SalesByMonth" subTable="false"  />  

                                            </p:commandLink>  

                                            <p:commandLink id="pdf" ajax="false">  
                                                <p:graphicImage value="/resources/images/pdf.png"  width="24" onclick="PrimeFaces.monitorDownload(start, stop);"/>  
                                                <pe:exporter type="pdf" target="dtrpt1" fileName="SalesByMonth" subTable="false"  preProcessor="#{monthlyExpandedRep.preProcessPDF}" />  

                                            </p:commandLink>

                                        </p:column>

                                    </h:panelGrid> 
                                </td>
                            </tr>   

                        </table>    

                    </div >

                </div>




                <!--                <p:ajaxStatus onstart="PF('statusDialog').show()" onsuccess="PF('statusDialog').hide()" />
                
                                <p:dialog widgetVar="statusDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
                                    <h:graphicImage  library="images" name="ajax-loader.gif" />
                                </p:dialog>-->

                <h:panelGroup id="tbldata">
                   <!--5190: CRM-4537: chart looks off-->
                    <p:dataTable id="dtrpt"  value="#{monthlyExpandedRep.listExpand}" var="exp" emptyMessage="No data found" draggableColumns="true" 
                                 tableStyle="table-layout:auto" resizableColumns="true" >
                        <!--//Bug 1164 -# 325040 : By Customer By month Sales Reports-->
                         <!--// #10203 :CRM-6433-->
                        <p:column headerText="#{reportFilters.grp eq 1 ?  custom.labels.get('IDS_PRINCI') 
                                                : reportFilters.grp eq 2 ? custom.labels.get('IDS_DB_CUSTOMER') : custom.labels.get('IDS_DB_CUSTOMER')}"  class="col_left" id="clmnName" >
                            <h:outputLabel value="#{exp.mexpPrinciName}"   />   

                            <f:facet  name="footer">
                                <h:outputLabel   value="Total : "  style="float: right" class="footerFont"/>
                            </f:facet>
                        </p:column>

                        <!--#390130:sales by month :column sorter provided  -->
                        <p:column headerText="Jan" class="col_right" sortBy="#{exp.mexpMonth1}" >
                            <h:outputLabel  value="#{exp.mexpMonth1}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totals[0]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>

                        </p:column>

                        <p:column headerText="Feb" class="col_right" sortBy="#{exp.mexpMonth2}">
                            <h:outputLabel value="#{exp.mexpMonth2}" style="float: right"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totals[1]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>

                        <p:column headerText="Mar" class="col_right" sortBy="#{exp.mexpMonth3}" >
                            <h:outputLabel value="#{exp.mexpMonth3}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totals[2]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                            </f:facet>
                        </p:column>

                        <p:column headerText="Apr" class="col_right" sortBy="#{exp.mexpMonth4}">
                            <h:outputLabel value="#{exp.mexpMonth4}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totals[3]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="May" class="col_right" sortBy="#{exp.mexpMonth5}">
                            <h:outputLabel value="#{exp.mexpMonth5}"  style="float: right">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totals[4]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>            
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="Jun" class="col_right" sortBy="#{exp.mexpMonth6}">
                            <h:outputLabel value="#{exp.mexpMonth6}"  style="float: right">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totals[5]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="Jul" class="col_right" sortBy="#{exp.mexpMonth7}">
                            <h:outputLabel value="#{exp.mexpMonth7}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totals[6]}"  style="float: right" class="footerFont" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="Aug" class="col_right" sortBy="#{exp.mexpMonth8}">
                            <h:outputLabel value="#{exp.mexpMonth8}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totals[7]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="Sep" class="col_right" sortBy="#{exp.mexpMonth9}" >
                            <h:outputLabel value="#{exp.mexpMonth9}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totals[8]}"  style="float: right;" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>



                        <p:column headerText="Oct" class="col_right" sortBy="#{exp.mexpMonth10}">
                            <h:outputLabel value="#{exp.mexpMonth10}" style="float: right">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totals[9]}"  style="float: right"  class="footerFont" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="Nov" class="col_right" sortBy="#{exp.mexpMonth11}">
                            <h:outputLabel value="#{exp.mexpMonth11}" style="float: right"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totals[10]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="Dec" class="col_right" sortBy="#{exp.mexpMonth12}">
                            <h:outputLabel value="#{exp.mexpMonth12}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0" />
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totals[11]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column  headerText="Total" class="col_right" sortBy="#{exp.mexpTotal}">
                            <h:outputLabel  value="#{exp.mexpTotal}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}"  
                                               value="#{monthlyExpandedRep.totals[12]}"  style="float: right;" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>

                        <p:column headerText="%" sortBy="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}">
                            <h:outputLabel  value="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}"  style="float: right" />                    
                            <f:facet name="footer">
                                <!--                        total issue condition added 20 Nov 2018-->
                                <h:outputLabel rendered="#{salesReports.repPrinci eq 0}"  
                                               value="#{monthlyExpandedRep.totals[13] eq 0 ?  '0' 
                                                        : (monthlyExpandedRep.totals[13] ne 100 ? '100' : '100')}%"  style="float: right;" class="footerFont"/>

<!--<h:outputLabel rendered="#{salesReports.repPrinci eq 0}"-->  
               <!--value="#{monthlyExpandedRep.totals[13]}%"  style="float: right;" />-->
                            </f:facet>

                        </p:column>
                    </p:dataTable>

                    <!--//#2949:Task CRM-1854: caltron: reports: make sales by month report exportable into excel-->

                    <p:dataTable id="dtrpt1"  value="#{monthlyExpandedRep.listExpand}" var="exp" 
                                 rendered="false" >

                        <p:columnGroup  type="header" class="header"   >
                            <p:row>

                                <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                <p:column />
                                <p:column />
                                <p:column headerText="Sales By Month Report" />
                                <p:column/>
                                <p:column/>
                                <p:column/>
                                <!--//#3785: Bug CRM-2265Sales reports-->
                                <p:column headerText="As of : #{reportFilters.repYear} " />

                                <p:column />

                                <p:column />
                                <p:column />
                                <p:column />
                                <p:column />
                                <p:column />
                                <p:column />


                            </p:row>




                            <p:row>





                            </p:row>
                            <p:row  class="header" id="myHeaderExp" >   



<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <!--//Bug 1164 -# 325040 : By Customer By month Sales Reports-->
                                 <!--// #10203 :CRM-6433-->
                                <p:column headerText="#{reportFilters.grp eq 1 ?  custom.labels.get('IDS_PRINCI') 
                                                        : reportFilters.grp eq 2 ?  custom.labels.get('IDS_DB_CUSTOMER') : custom.labels.get('IDS_DB_CUSTOMER')}"  class="col_left" >

                                </p:column>

                                <!--#390130:sales by month :column sorter provided  -->
                                <p:column headerText="Jan" class="col_right" sortBy="#{exp.mexpMonth1}" >

                                </p:column>

                                <p:column headerText="Feb" class="col_right" sortBy="#{exp.mexpMonth2}">

                                </p:column>

                                <p:column headerText="Mar" class="col_right" sortBy="#{exp.mexpMonth3}" >


                                </p:column>

                                <p:column headerText="Apr" class="col_right" sortBy="#{exp.mexpMonth4}">

                                </p:column>


                                <p:column headerText="May" class="col_right" sortBy="#{exp.mexpMonth5}">

                                </p:column>


                                <p:column headerText="Jun" class="col_right" sortBy="#{exp.mexpMonth6}">

                                </p:column>


                                <p:column headerText="Jul" class="col_right" sortBy="#{exp.mexpMonth7}">

                                </p:column>


                                <p:column headerText="Aug" class="col_right" sortBy="#{exp.mexpMonth8}">

                                </p:column>


                                <p:column headerText="Sep" class="col_right" sortBy="#{exp.mexpMonth9}" >

                                </p:column>



                                <p:column headerText="Oct" class="col_right" sortBy="#{exp.mexpMonth10}">

                                </p:column>


                                <p:column headerText="Nov" class="col_right" sortBy="#{exp.mexpMonth11}">

                                </p:column>


                                <p:column headerText="Dec" class="col_right" sortBy="#{exp.mexpMonth12}">

                                </p:column>


                                <p:column  headerText="Total" class="col_right" sortBy="#{exp.mexpTotal}">

                                </p:column>

                                <p:column headerText="%" sortBy="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}">

                                </p:column>
                            </p:row>


                        </p:columnGroup>





                        <!--//Bug 1164 -# 325040 : By Customer By month Sales Reports-->
                        <p:column  class="col_left" >



                            <h:outputText value="#{exp.mexpPrinciName}"   />   

                            <f:facet  name="footer">
                                <h:outputLabel   value="Total : "  style="float: right" class="footerFont"/>
                            </f:facet>
                        </p:column>

                        <!--#390130:sales by month :column sorter provided  -->
                        <p:column  class="col_right" sortBy="#{exp.mexpMonth1}" >
                            <h:outputText  value="#{exp.mexpMonth1}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totals[0]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>

                        </p:column>

                        <p:column  class="col_right" sortBy="#{exp.mexpMonth2}">
                            <h:outputText value="#{exp.mexpMonth2}" style="float: right"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totals[1]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>

                        <p:column class="col_right" sortBy="#{exp.mexpMonth3}" >
                            <h:outputText value="#{exp.mexpMonth3}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totals[2]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>

                            </f:facet>
                        </p:column>

                        <p:column class="col_right" sortBy="#{exp.mexpMonth4}">
                            <h:outputText value="#{exp.mexpMonth4}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totals[3]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth5}">
                            <h:outputText value="#{exp.mexpMonth5}"  style="float: right">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totals[4]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>            
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth6}">
                            <h:outputText value="#{exp.mexpMonth6}"  style="float: right">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totals[5]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth7}">
                            <h:outputText value="#{exp.mexpMonth7}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totals[6]}"  style="float: right" class="footerFont" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth8}">
                            <h:outputText value="#{exp.mexpMonth8}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totals[7]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth9}" >
                            <h:outputText value="#{exp.mexpMonth9}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totals[8]}"  style="float: right;" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>



                        <p:column  class="col_right" sortBy="#{exp.mexpMonth10}">
                            <h:outputText value="#{exp.mexpMonth10}" style="float: right">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totals[9]}"  style="float: right"  class="footerFont" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth11}">
                            <h:outputText value="#{exp.mexpMonth11}" style="float: right"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totals[10]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth12}">
                            <h:outputText value="#{exp.mexpMonth12}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0" />
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totals[11]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column   class="col_right" sortBy="#{exp.mexpTotal}">
                            <h:outputText  value="#{exp.mexpTotal}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}"  
                                              value="#{monthlyExpandedRep.totals[12]}"  style="float: right;" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>

                        <p:column  sortBy="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}">
                            <h:outputLabel  value="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}"  style="float: right" />                    
                            <f:facet name="footer">
                                <!--                        total issue condition added 20 Nov 2018-->
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}"  
                                              value="#{monthlyExpandedRep.totals[13] eq 0 ?  '0' 
                                                       : (monthlyExpandedRep.totals[13] ne 100 ? '100' : '100')}%"  style="float: right;" class="footerFont"/>

<!--<h:outputLabel rendered="#{salesReports.repPrinci eq 0}"-->  
               <!--value="#{monthlyExpandedRep.totals[13]}%"  style="float: right;" />-->
                            </f:facet>

                        </p:column>






                    </p:dataTable>



                </h:panelGroup>

            </h:form>

            <!--#3153 Sales Reports - Add gif for loading START-->
            <p:dialog widgetVar="inSalByMonthProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Please wait...Exporting records" />
                        <br /><br />
                        <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                    </p:outputPanel>
                </h:form>
            </p:dialog>


            <ui:include src="/lookup/CompanyRegionLookUp.xhtml" />

            <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>
        </div>

       
                <style>

                     .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }

             /*14453 ESCALATIONS CRM-8464   Reporting Graphs Legend not legible - Multiple Instances*/
            .jqplot-axis {
                 font-size: 0.25em !important;
            }

            .jqplot-target {

                font-size: 0.75em!important;
            }
        </style>
        <!--#3153 Sales Reports - Add gif for loading START-->
        <script>
            window.onload = function () {
//                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("salbymonthrpt").style.pointerEvents = "all";
                    document.getElementById("salbymonthrpt").style.opacity = "1";

                    $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };

            function processGif()
            {
                $('#loading-image').show();
                document.getElementById("salbymonthrpt").style.pointerEvents = "none";
                document.getElementById("salbymonthrpt").style.opacity = "0.4";
            }

            function hide()
            {

                $('#loading-image').hide();

                document.getElementById("salbymonthrpt").style.pointerEvents = "all";
                document.getElementById("salbymonthrpt").style.opacity = "1";
            }



            function start() {
                PF('inSalByMonthProgressDlg').show();
            }
            function stop() {

                PF('inSalByMonthProgressDlg').hide();
            }
        </script>
        <!--#3153 Sales Reports - Add gif for loading END -->
    </ui:define>

</ui:composition>
