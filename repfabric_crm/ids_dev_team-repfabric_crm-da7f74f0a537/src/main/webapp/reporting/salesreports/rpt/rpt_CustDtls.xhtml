<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: CustomerSummary.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*.
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

    <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
        <title> #{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_DB_CUSTOMER'):'Customer'} Details </title>
    </ui:define>

    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>
    <ui:define name="meta">
        <f:metadata> 

            <f:viewParam name="yr" value="#{reports.repYear}" />
            <f:viewParam name="mt" value="#{reports.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="d" value="#{reportFilters.distributor.compId}"/>
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>
            <f:viewParam name="m" value="#{reportFilters.minVal}"/>
            <f:viewParam name="g" value="#{reportFilters.grp}"/>
            <f:viewParam name="c" value="#{reportFilters.custselect.compId}"/>
            <f:viewParam name="repcode" value="#{custDetailRep.repcode}"/>
            <!--<f:viewParam name="cn" value="#{reportFilters.custselect.compName}"/>-->
            <!--Bug #3499:  Thea - drill down for customer details not working-->
            <!--#10213 CRM-6473  Customer master: quick link to customer summary report-->
            <!--#10829 CRM-6740  Product Details: case navigation screen error-->
            <f:viewAction  action="#{reportFilters.getSetSmanName(reportFilters.salesTeam.smanId)}" />
            <f:viewAction action="#{reportFilters.getSelCustName(reportFilters.custselect.compId)}" />
            <f:viewAction action="#{reportFilters.getPrinciName(reportFilters.pricipal.compId)}"/>
            <f:viewAction action="#{reportFilters.getSelectedDistriName(reportFilters.distributor.compId)}"/>
            <f:viewAction action="#{custDetailRep.loadData()}"/>
            <!--//#3314:Bug Product details/Customer details Report : Run report issue-->

        </f:metadata>

    </ui:define>
    <ui:define name="body">
        <f:event listener="#{custDetailRep.isPageAccessible}" type="preRenderView"/> 
        <!--#3153 Sales Reports - Add gif for loading-->

        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />
        <!--#3153 Sales Reports - Add gif for loading style changes-->

        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4" id="custDetrpt" >
            <h:form  id="custSummaryHeader">
                <p:remoteCommand name="applyCRMCust" actionListener="#{reportFilters.applyCustSelect(viewCompLookupService.selectedCompany)}" update=":custSummaryHeader:inputCustName" />
                <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update=":custSummaryHeader:inputDistriName "/>
                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update=":custSummaryHeader:inputPrincName "   />
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >


                        <div class="compNamebg">
                            <h:outputLabel value="#{loginBean.subscriber_name} "   />
                            <br/>
                            <!--#363 :page title-->

                            <!--1833:CRM-2811: MerchantsSales: edit end user label-->
                            <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            #{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_CUSTOMER'):'Customer'} Details

                            <br/>
                        </div>
                        &nbsp;&nbsp;<p:outputLabel value="Quarter : #{reports.repYear}-#{reports.quarter},  Month : #{reportFilters.getMonth(reports.repMonth )}" /> 
                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="margin-left:60px">
                        <p:panelGrid   columns="2"  layout="grid" style="width: 600px;float: right;"  id="barchart"> 
                            <p:chart type="bar" model="#{reports.barChartYearly}"   style="width:285px;height:180px"/>
                            <p:chart type="bar" model="#{reports.barChartQuarterly}"   style="width:285px;height:180px"/>
                        </p:panelGrid>
                    </div>  
                    
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:-60px">
                        
                        <p:panelGrid id="grid" columns="2" 
                                     style="float: right;"  columnClasses="ui-grid-col-4,ui-grid-col-8"   layout="grid" styleClass="box-primary no-border ui-fluid "  >
                            
                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
        <!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"  />
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:outputLabel for="inputDistriName" value="#{custom.labels.get('IDS_DISTRI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputDistriName" value="#{reportFilters.distributor.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyDistri', 3, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustName" value="#{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_DB_CUSTOMER'):'Customer'}"  />


                            <h:panelGroup class="ui-inputgroup"  >

                                <!--#3473:customer field made read only-->
                                <p:inputText id="inputCustName" value="#{reportFilters.custselect.compName}" widgetVar="cust" placeholder="[Select]"  readonly="true"/>
                                <!--                        #1932 actionListener changed for CRM visibility-->
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCRMCust',2,0,1)}" oncomplete="PF('lookupComp').show()"
                                                  update=":formCompLookup" 
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <p:commandButton  value="Run Report "  id="viewreport"   styleClass="btn btn-primary btn-xs"  style="width:30px"      actionListener="#{custDetailRep.goToCustSummaryDetailsPage1()}" onclick="processGif();"/>


                            <p:column>
                                <p:commandLink id="xls" ajax="false" onclick="PrimeFaces.monitorDownload(start, stop);" >  
                                    <p:graphicImage name="/images/excel.png" width="24"/>
                                    <pe:exporter type="xlsx" target="rptTableExport"  fileName="Customer_details" subTable="false" postProcessor="#{custDetailRep.postProcessXLS}"  />  

                                </p:commandLink>  

                                <p:commandLink id="pdf" ajax="false">  
                                    <p:graphicImage value="/resources/images/pdf.png"  width="24" onclick="PrimeFaces.monitorDownload(start, stop);"/>  
                                    <pe:exporter type="pdf" target="rptTablePdf" fileName="Customer_details" subTable="false"    preProcessor="#{creditedSalesRep.preProcessPDF}" />  

                                </p:commandLink>

                            </p:column> 





                        </p:panelGrid>

                    </div >

                </div>

                <div  class="div-selected" >

                    <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                        #{reportFilters.custselect.compName}
                    </label> 
                </div>


                <p:dataTable  id="rptTable" value="#{custDetailRep.listCustomerDtls}" var="custdtl" emptyMessage="No data found"  >

                    <p:column sortBy="#{custdtl.princiname}" width="200" headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left" >

                        <!--#Too many tabs, fixed by replacing _blank to _self -->
                        <!--c3 added for secondary customer 1606 -->
                        <!--<h:outputLabel value="#{custdtl.princiname}"/>-->
                        <h:link title="View Product Details" value="#{custdtl.princiname}"  target="_self" outcome="rpt_ProdDtls_All.xhtml?yr=#{reports.repYear}&amp;mt=#{reports.repMonth}&amp;p=#{custdtl.id}&amp;c=#{reportFilters.custselect.compId}&amp;d=#{reportFilters.distributor.compId}&amp;s=#{reportFilters.salesTeam.smanId}&amp;g=0&amp;c3=0&amp;repcode=17&amp;pname=#{custdtl.princiname}" />
                        <f:facet name="footer">
                            <h:outputLabel style="float: right" value="Total"  class="footerFont" />
                        </f:facet>
                    </p:column>

                    <p:column sortBy="#{custdtl.repYearPrev}" headerText="Previous Year" class="col_right" >
                        <h:outputLabel value="#{custdtl.repYearPrev}"  style="float: right">
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right;"  value="#{custDetailRep.totalsCustDtls[0]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <p:column sortBy="#{custdtl.repYearCurr}" headerText="Current Year" class="col_right" >
                        <!--#Too many tabs, fixed by replacing _blank to _self -->
                        
                        <!--Bug #3499:  Thea - drill down for customer details not working-->
                         <!--Feature #4134:   CRM-4020: Sales/Comms reports: group by product families and product lines on drill downs-->
<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                        <h:link title="View #{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_DB_CUSTOMER'):'Customer'} Product Details "  rendered="#{!loginBean.productFamilyEnabled()}"  target="_self" outcome="rpt_CustDtls_Prod.xhtml?yr=#{reports.repYear}&amp;mt=#{reports.repMonth}&amp;p=#{custdtl.id}&amp;c=#{reportFilters.custselect.compId}&amp;d=#{reportFilters.distributor.compId}&amp;s=#{reportFilters.salesTeam.smanId}&amp;g=0&amp;pname=#{custdtl.princiname}&amp;sn=#{reportFilters.salesTeam.smanName}" >
                            <h:outputLabel style="cursor: pointer;float: right" value="#{custdtl.repYearCurr}"   >          
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </h:link>
                         
                           <h:link title="View #{custom.labels.get('IDS_PROD_FAMILY')} Product Details "  rendered="#{loginBean.productFamilyEnabled()}"   target="_self" outcome="rpt_CustDtls_Prod.xhtml?yr=#{reports.repYear}&amp;mt=#{reports.repMonth}&amp;p=#{custdtl.id}&amp;c=#{reportFilters.custselect.compId}&amp;d=#{reportFilters.distributor.compId}&amp;s=#{reportFilters.salesTeam.smanId}&amp;g=0&amp;pname=#{custdtl.princiname}&amp;sn=#{reportFilters.salesTeam.smanName}" >
                            <h:outputLabel style="cursor: pointer;float: right" value="#{custdtl.repYearCurr}"   >          
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </h:link>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailRep.totalsCustDtls[1]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <p:column sortBy="#{custdtl.repYearRr}" headerText="Year RR"  style="text-align: right">
                        <h:outputLabel value="#{custdtl.repYearRr}"
                                       style="#{
                                       custdtl.repYearRr gt 100 ?  'color:green' 
                                           : (custdtl.repYearRr lt 100 ? 'color:red' : 'color:black')
                               }"
                                       >
                            <!--CRM-185 - Incorrect rendering attribute value-->
                            <h:outputLabel value="%" rendered="#{custdtl.repYearRr ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1"/>
                        </h:outputLabel>
                    </p:column>     

                    <!--Dashboard: Client requirement Added quarterly column-->

                    <p:column sortBy="#{custdtl.repQrtr3}"  style="min-width: 50px;text-align:right"  headerText="#{salesReports.headervalQr[0]}" class="col_right">
                        <h:outputLabel value="#{custdtl.repQrtr3}" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailRep.totalsCustDtls[3]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>     

                    <p:column sortBy="#{custdtl.repQrtr2}" headerText="#{salesReports.headervalQr[1]}" style="min-width: 50px;text-align:right" class="col_right">
                        <h:outputLabel value="#{custdtl.repQrtr2}" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailRep.totalsCustDtls[4]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>     

                    <p:column sortBy="#{custdtl.repQrtr1}" headerText="#{salesReports.headervalQr[2]}" style="min-width: 50px;text-align:right"  class="col_right">
                        <h:outputLabel value="#{custdtl.repQrtr1}" style="float: right" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailRep.totalsCustDtls[5]}"  class="footerFont" >
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>  




                    <p:column sortBy="#{custdtl.repQrtrCurr}" headerText="Current Qtr"  class="col_right"  style="text-align:right">
                        <h:outputLabel value="#{custdtl.repQrtrCurr}" style="float: right" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailRep.totalsCustDtls[6]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>     

                    <p:column sortBy="#{custdtl.repQrtrRr}"  headerText="Qtr RR"  class="col_right"  style="text-align:right">
                        <h:outputLabel value="#{custdtl.repQrtrRr}"

                                       style="#{
                                       custdtl.repQrtrRr gt 100 ?  'color:green' 
                                           : (custdtl.repQrtrRr lt 100 ? 'color:red' : 'color:black')
                               }"

                                       ><h:outputLabel value="%" rendered="#{custdtl.repQrtrRr ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1"/>
                        </h:outputLabel>
                    </p:column>     

                </p:dataTable>


                <p:dataTable  id="rptTableExport" value="#{custDetailRep.listCustomerDtls}" var="custdtl" rendered="false"  >


                    <p:columnGroup  type="header" class="header"   >
                        <p:row>

                            <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                            <p:column />
                            <p:column />
                            <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:column headerText=" #{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_DB_CUSTOMER'):'Customer'}  Report" />
                            <p:column/>


                            <p:column  colspan="3"  rowspan="2"   headerText="Quarter : #{reports.repYear}-#{reports.quarter},  Month : #{reportFilters.getMonth(reports.repMonth )} " />
                            <p:column/>
                            <p:column/>
                            <p:column />




                        </p:row>




                        <p:row>





                        </p:row>
                        <p:row>





                        </p:row>

                        <p:row>
<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->

                            <p:column colspan="5"   headerText=" #{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_DB_CUSTOMER'):'Customer'} : #{reportFilters.custselect.compName}" />

                        </p:row>
                        <p:row  class="header" id="myHeaderExp" > 




                            <p:column  headerText="#{custom.labels.get('IDS_PRINCI')}"  >


                            </p:column>

                            <p:column  headerText="Previous Year"  >

                            </p:column>

                            <p:column  headerText="Current Year" class="col_right" >

                            </p:column>

                            <p:column  headerText="Year RR"  >

                            </p:column>     

                            <!--Dashboard: Client requirement Added quarterly column-->

                            <p:column   headerText="#{salesReports.headervalQr[0]}" class="col_right">

                            </p:column>     

                            <p:column  headerText="#{salesReports.headervalQr[1]}" >

                            </p:column>     

                            <p:column  headerText="#{salesReports.headervalQr[2]}" >

                            </p:column>  




                            <p:column  headerText="Current Qtr"  >

                            </p:column>     

                            <p:column   headerText="Qtr RR" >

                            </p:column>     
                        </p:row>
                    </p:columnGroup>

                    <p:column sortBy="#{custdtl.princiname}" width="200"  class="col_left" >

                        <!--#Too many tabs, fixed by replacing _blank to _self -->
                        <!--c3 added for secondary customer 1606 -->
                        <!--<h:outputLabel value="#{custdtl.princiname}"/>-->
                        <h:link title="View Product Details" value="#{custdtl.princiname}"  target="_self" outcome="rpt_ProdDtls_All.xhtml?yr=#{reports.repYear}&amp;mt=#{reports.repMonth}&amp;p=#{custdtl.id}&amp;c=#{reportFilters.custselect.compId}&amp;d=#{reportFilters.distributor.compId}&amp;s=#{reportFilters.salesTeam.smanId}&amp;g=0&amp;c3=0&amp;repcode=17&amp;pname=#{custdtl.princiname}" />
                        <f:facet name="footer">
                            <h:outputText style="float: right" value="Total"  class="footerFont" />
                        </f:facet>
                    </p:column>

                    <p:column sortBy="#{custdtl.repYearPrev}" class="col_right" >
                        <h:outputText value="#{custdtl.repYearPrev}"  style="float: right">
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText style="float: right;"  value="#{custDetailRep.totalsCustDtls[0]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputText>
                        </f:facet>
                    </p:column>

                    <p:column sortBy="#{custdtl.repYearCurr}"  class="col_right" >
                        <!--#Too many tabs, fixed by replacing _blank to _self -->

                        <h:outputText style="float: right" value="#{custdtl.repYearCurr}"   >          
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputText>

                        <f:facet name="footer">
                            <h:outputText style="float: right"  value="#{custDetailRep.totalsCustDtls[1]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputText>
                        </f:facet>
                    </p:column>

                    <p:column    style="text-align: right">
                        <h:outputText value="#{monthlyExpandedRep.convertNumber(custdtl.repYearRr)}#{custdtl.repYearRr eq null ?'' :'%'}"
                                      style="#{
                                      custdtl.repYearRr gt 100 ?  'color:green' 
                                          : (custdtl.repYearRr lt 100 ? 'color:red' : 'color:black')
                               };float:right"
                                      >
                            <!--CRM-185 - Incorrect rendering attribute value-->


                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText >

                            </h:outputText>
                        </f:facet>
                    </p:column>     

                    <!--Dashboard: Client requirement Added quarterly column-->

                    <p:column >
                        <h:outputText value="#{custdtl.repQrtr3}" style="float:right" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText style="float: right"  value="#{custDetailRep.totalsCustDtls[3]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputText>
                        </f:facet>
                    </p:column>     

                    <p:column sortBy="#{custdtl.repQrtr2}"  style="min-width: 50px;text-align:right" class="col_right">
                        <h:outputText value="#{custdtl.repQrtr2}" style="float:right" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText style="float: right"  value="#{custDetailRep.totalsCustDtls[4]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputText>
                        </f:facet>
                    </p:column>     

                    <p:column sortBy="#{custdtl.repQrtr1}"  style="min-width: 50px;text-align:right"  class="col_right">
                        <h:outputText value="#{custdtl.repQrtr1}" style="float: right" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText style="float: right"  value="#{custDetailRep.totalsCustDtls[5]}"  class="footerFont" >
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputText>
                        </f:facet>
                    </p:column>  




                    <p:column sortBy="#{custdtl.repQrtrCurr}" class="col_right"  style="text-align:right">
                        <h:outputText value="#{custdtl.repQrtrCurr}" style="float: right" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText style="float: right"  value="#{custDetailRep.totalsCustDtls[6]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputText>
                        </f:facet>
                    </p:column>     

                    <p:column sortBy="#{custdtl.repQrtrRr}"   class="col_right"  style="text-align:right">
                        <h:outputText value="#{custdtl.repQrtrRr}"

                                      style="#{
                                      custdtl.repQrtrRr gt 100 ?  'color:green' 
                                          : (custdtl.repQrtrRr lt 100 ? 'color:red' : 'color:black')
                               }"

                                      ><h:outputText value="%" rendered="#{custdtl.repQrtrRr ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1"/>
                        </h:outputText>
                    </p:column>     



                </p:dataTable>    

                <p:dataTable  id="rptTablePdf" value="#{custDetailRep.listCustomerDtls}" var="custdtl" rendered="false"  >


                    <p:columnGroup  type="header" class="header"   >
                        <p:row>

                            <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                            <p:column />
                            <p:column />
                            <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:column headerText="#{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_DB_CUSTOMER'):'Customer'}  Report" />
                            <p:column/>


                            <p:column     headerText="Quarter : #{reports.repYear}-#{reports.quarter},  Month : #{reportFilters.getMonth(reports.repMonth )} " />
                            <p:column/>
                            <p:column/>
                            <p:column />




                        </p:row>




                        <p:row>





                        </p:row>
                        <p:row>





                        </p:row>
                        <p:row>
                            <p:column/>
                            <p:column/>
                            <p:column/>
                            <p:column/>


<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:column  headerText="#{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_DB_CUSTOMER'):'Customer'} : #{reportFilters.custselect.compName}" />
                            <p:column/>
                            <p:column/>
                            <p:column/>
                            <p:column/>

                        </p:row>
                        <p:row  class="header" id="myHeaderExp" > 




                            <p:column  headerText="#{custom.labels.get('IDS_PRINCI')}"  >


                            </p:column>

                            <p:column  headerText="Previous Year"  >

                            </p:column>

                            <p:column  headerText="Current Year" class="col_right" >

                            </p:column>

                            <p:column  headerText="Year RR"  >

                            </p:column>     

                            <!--Dashboard: Client requirement Added quarterly column-->

                            <p:column   headerText="#{salesReports.headervalQr[0]}" class="col_right">

                            </p:column>     

                            <p:column  headerText="#{salesReports.headervalQr[1]}" >

                            </p:column>     

                            <p:column  headerText="#{salesReports.headervalQr[2]}" >

                            </p:column>  




                            <p:column  headerText="Current Qtr"  >

                            </p:column>     

                            <p:column   headerText="Qtr RR" >

                            </p:column>     
                        </p:row>
                    </p:columnGroup>

                    <p:column sortBy="#{custdtl.princiname}" width="200"  class="col_left" >

                        <!--#Too many tabs, fixed by replacing _blank to _self -->
                        <!--c3 added for secondary customer 1606 -->
                        <!--<h:outputLabel value="#{custdtl.princiname}"/>-->
                        <h:link title="View Product Details" value="#{custdtl.princiname}"  target="_self" outcome="rpt_ProdDtls_All.xhtml?yr=#{reports.repYear}&amp;mt=#{reports.repMonth}&amp;p=#{custdtl.id}&amp;c=#{reportFilters.custselect.compId}&amp;d=#{reportFilters.distributor.compId}&amp;s=#{reportFilters.salesTeam.smanId}&amp;g=0&amp;c3=0&amp;repcode=17&amp;pname=#{custdtl.princiname}" />
                        <f:facet name="footer">
                            <h:outputText style="float: right" value="Total"  class="footerFont" />
                        </f:facet>
                    </p:column>

                    <p:column sortBy="#{custdtl.repYearPrev}" class="col_right" >
                        <h:outputText value="#{custdtl.repYearPrev}"  style="float: right">
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText style="float: right;"  value="#{custDetailRep.totalsCustDtls[0]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputText>
                        </f:facet>
                    </p:column>

                    <p:column sortBy="#{custdtl.repYearCurr}"  class="col_right" >
                        <!--#Too many tabs, fixed by replacing _blank to _self -->

                        <h:outputText style="float: right" value="#{custdtl.repYearCurr}"   >          
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputText>

                        <f:facet name="footer">
                            <h:outputText style="float: right"  value="#{custDetailRep.totalsCustDtls[1]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputText>
                        </f:facet>
                    </p:column>

                    <p:column    style="text-align: right">
                        <h:outputText value="#{monthlyExpandedRep.convertNumber(custdtl.repYearRr)}"
                                      style="#{
                                      custdtl.repYearRr gt 100 ?  'color:green' 
                                          : (custdtl.repYearRr lt 100 ? 'color:red' : 'color:black')
                               };float:right"
                                      >
                            <!--CRM-185 - Incorrect rendering attribute value-->

                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText >

                            </h:outputText>
                        </f:facet>
                    </p:column>     

                    <!--Dashboard: Client requirement Added quarterly column-->

                    <p:column >
                        <h:outputText value="#{custdtl.repQrtr3}" style="float:right" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText style="float: right"  value="#{custDetailRep.totalsCustDtls[3]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputText>
                        </f:facet>
                    </p:column>     

                    <p:column sortBy="#{custdtl.repQrtr2}"  style="min-width: 50px;text-align:right" class="col_right">
                        <h:outputText value="#{custdtl.repQrtr2}" style="float:right" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText style="float: right"  value="#{custDetailRep.totalsCustDtls[4]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputText>
                        </f:facet>
                    </p:column>     

                    <p:column sortBy="#{custdtl.repQrtr1}"  style="min-width: 50px;text-align:right"  class="col_right">
                        <h:outputText value="#{custdtl.repQrtr1}" style="float: right" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText style="float: right"  value="#{custDetailRep.totalsCustDtls[5]}"  class="footerFont" >
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputText>
                        </f:facet>
                    </p:column>  




                    <p:column sortBy="#{custdtl.repQrtrCurr}" class="col_right"  style="text-align:right">
                        <h:outputText value="#{custdtl.repQrtrCurr}" style="float: right" >
                            <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText style="float: right"  value="#{custDetailRep.totalsCustDtls[6]}"  class="footerFont">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputText>
                        </f:facet>
                    </p:column>     

                    <p:column sortBy="#{custdtl.repQrtrRr}"   class="col_right"  style="text-align:right">
                        <h:outputText value="#{custdtl.repQrtrRr}"

                                      style="#{
                                      custdtl.repQrtrRr gt 100 ?  'color:green' 
                                          : (custdtl.repQrtrRr lt 100 ? 'color:red' : 'color:black')
                               }"

                                      ><h:outputText value="%" rendered="#{custdtl.repQrtrRr ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1"/>
                        </h:outputText>
                    </p:column>     



                </p:dataTable>  





            </h:form>

            <p:dialog widgetVar="inCustDetProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Please wait...Exporting records" />
                        <br /><br />
                        <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                    </p:outputPanel>
                </h:form>
            </p:dialog>


        </div>
        <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />

        <style>

            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }
 /*14453 ESCALATIONS CRM-8464   Reporting Graphs Legend not legible - Multiple Instances*/
            .jqplot-axis {
                 font-size: 0.25em !important;
            }

            .jqplot-target {

                font-size: 0.75em!important;
            }
            /*2627 - Related to CRM-1177: Doran associates: Remove link from printed reports - sharvani - 11-12-2019*/
            @media print {
                a[href]:after {
                    content: none !important;
                }
            }
        </style>
        <!--#3153 Sales Reports - Add gif for loading START-->

        <script>
            window.onload = function () {
//                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("custDetrpt").style.pointerEvents = "all";
                    document.getElementById("custDetrpt").style.opacity = "1";

                    $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };

            function visibility()
            {
                document.getElementById("export").style.visibility = "hidden";
                $('#loading-image').show();

            }

            function hide()
            {

                $('#loading-image').hide();
            }

            function processGif()
            {
                $('#loading-image').show();
                document.getElementById("custDetrpt").style.pointerEvents = "none";
                document.getElementById("custDetrpt").style.opacity = "0.4";
            }


            function start() {
                PF('inCustDetProgressDlg').show();
            }
            function stop() {

                PF('inCustDetProgressDlg').hide();
            }


        </script>
        <!--#3153 Sales Reports - Add gif for loading  END-->

    </ui:define>

    <ui:define name="footer">
        <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
        #{fabricClient.clientName} Dashboard - #{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_DB_CUSTOMER'):'Customer'} Details Report 

    </ui:define>
</ui:composition>
