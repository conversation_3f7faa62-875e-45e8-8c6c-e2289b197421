<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: CustomerSummary.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>
    <ui:define name="meta">
        <f:metadata> 

            <f:viewParam name="yr" value="#{reports.repYear}" />
            <f:viewParam name="mt" value="#{reports.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="d" value="#{reportFilters.distributor.compId}"/>
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>

            <f:viewParam name="g" value="#{reportFilters.grp}"/>
             <!--Feature #4134:   CRM-4020: Sales/Comms reports: group by product families and product lines on drill downs-->

            <f:viewParam name="c" value="#{reportFilters.custselect.compId}"/>
            <!--<f:viewParam name="repcode" value="#{custDetailRep.repcode}"/>-->
            <f:viewParam name="pname" value="#{reportFilters.pricipal.compName}"/>
            <f:viewParam name="sn" value="#{reportFilters.salesTeam.smanName}"/>
              <!--Feature #4134:   CRM-4020: Sales/Comms reports: group by product families and product lines on drill downs-->

            <f:viewParam name="pf" value="#{custDetailsProd.prodFlag}"/>
            <f:viewParam name="pfid" value="#{custDetailsProd.repProdFamilyId}"/>
            
            <f:viewAction action="#{custDetailsProd.init()}"/>



        </f:metadata>
    </ui:define>




    <ui:define name="body">


        <!--#3153 Sales Reports - Add gif for loading -->
        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />



        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7" id="custDetProd" >



            <h:form id="custDetprod"> 
    <!--            <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update=":custDetprod:inputCustomer" />
                <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update=":custDetprod:inputDistriName  "/>
                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update=":custDetprod:inputPrincName " />
                <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update=":custDetprod:inputAllSalesTeam" />
                <p:remoteCommand name="onload" autoRun="true" process="@this" immediate="true" onstart="PF('statusDialog').show()"   oncomplete="PF('statusDialog').hide()"   />
                -->
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >


                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            <!--Feature #4134:   CRM-4020: Sales/Comms reports: group by product families and product lines on drill downs-->

                            #{loginBean.productFamilyEnabled() ? custom.labels.get('IDS_PROD_FAMILY') :'Product '}  #{custDetailsProd.prodFlag eq 1 ? '' : 'Summary'}  
                            <br/>


                        </div>

                        &nbsp;&nbsp;<p:outputLabel value="Quarter : #{reports.repYear}-#{reports.quarter},  Month : #{reportFilters.getMonth(reports.repMonth )}" /> 

                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="margin-left:60px">
                        <p:panelGrid   columns="2"  layout="grid" style="width: 600px;float: right;"> 
                            <p:chart type="bar" model="#{reports.barChartYearly}"   style="width:285px;height:180px"/>
                            <p:chart type="bar" model="#{reports.barChartQuarterly}"   style="width:285px;height:180px"/>
                        </p:panelGrid>
                    </div>  
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:100px;width:230px;height:180px;border: 1px solid #9B9999;">
                        <!--style="width: 18.5%;height: 98%; display: inline-block;border: 1px solid #9B9999;"-->

                        <table style="width: 100%;">
                            <thead>

                                <tr>
                                    <th>Filter</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="col_left">#{custom.labels.get('IDS_PRINCI')} : </td>
                                </tr>
                                <tr>
                                    <td class="bold">#{reportFilters.pricipal.compName}</td>
                                </tr>
                                <tr>
                                    <td class="col_left" >#{custom.labels.get('IDS_DISTRI')} : </td>
                                </tr>
                                <tr>
                                    <td class="bold">#{reportFilters.distributor.compName}</td>
                                </tr>
                                <tr>
                                    <td class="col_left">#{custom.labels.get('IDS_SALES_TEAM')}  : </td>
                                </tr>
                                <tr>
                                    <td class="bold">#{reportFilters.salesTeam.smanId == 0 ? "All" : reportFilters.salesTeam.smanName}</td>
                                </tr>

                                <!--346 #344579 :Dashboard :Halco :Part number query-->

                                <tr>
                                    <td class="col_left">#{custom.labels.get('IDS_PART_NUM')}  : </td>
                                </tr>
                                <tr>
                                    <td class="bold">#{reportFilters.princiPartNo} </td>
                                </tr>
                            </tbody>
                        </table>





                    </div >

                </div>


                <div class="div-selected" style="height:25px;width: 100%;" >
                    <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                        #{salesReports.selectedName}
                    </label>               
                </div>


                <p:dataTable value="#{custDetailsProd.listCustProd}" var="cust" stickyHeader="false" id="cusDtlProd" >
                    <p:column sortBy="#{cust.principal}" headerText="#{custom.labels.get('IDS_PRINCI')}"   
                              class="col_left">
                        #{cust.principal}
                    </p:column>
                    <!--<p:column sortBy="#{cust.distributor}" headerText="Distributor"  class="col_left">#{cust.distributor}</p:column>-->

                    <p:column sortBy="#{cust.partNo}" headerText="Part No."  class="col_left"  rendered="#{!loginBean.productFamilyEnabled()}" >

                        <!--Bug #1357: CRM-3045: Customer summary drill down to part number invoices showing min max pricing-->
                        <p:commandLink process="@this" actionListener="#{partDetails.showPartDtls(cust.partNo,cust.prodFamily,cust.custId,cust.princiId,t.customer, reports.repYear)}" 
                                       value="#{cust.partNo}" 
                                       update="prodpartCust:dlgCust"
                                       oncomplete="PF('dlgPartCust').show()" />


                    </p:column>

                    <!--Feature #4134:   CRM-4020: Sales/Comms reports: group by product families and product lines on drill downs-->

                    <p:column sortBy="#{cust.prodFamily}" headerText="Prod. Family"  class="col_left">
                        <h:outputLabel  value="#{cust.prodFamily eq '-' ? '': cust.prodFamily}" rendered="#{!loginBean.productFamilyEnabled()}" />


                        <h:link title="View  Product Family"
                                target="_self" rendered="#{loginBean.productFamilyEnabled()}"
                                outcome="rpt_Prod_Family_Det.xhtml?yr=#{reports.repYear}&amp;mt=#{reports.repMonth}&amp;
                                p=#{cust.princiId}&amp;c=#{cust.custId}&amp;d=#{cust.distriId}&amp;
                                s=#{reportFilters.salesTeam.smanId}&amp;g=0&amp;pname=#{cust.principal}&amp;
                                sn=#{reportFilters.salesTeam.smanName}&amp;pf=1&amp;pfid=#{cust.repProdFamilyId}" >
                           <!--#9357 PRIORITY: CRM-6238 Reports: Product family drill down - blank pf disallows drilldown-->
                            <h:outputLabel  style="cursor: pointer;float:left;" value="#{cust.prodFamily}"   >          
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                             <h:outputLabel rendered="#{!cust.prodFamily eq '-'}" style="cursor: pointer;float: left;text-decoration: underline" value="#{cust.prodFamily}"   >          
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </h:link>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right" value="Total"  class="footerFont"/>
                        </f:facet>
                    </p:column>

                    <p:column  sortBy="#{cust.prevYr}" headerText="Prev. Year"  class="col_right">
                        <h:outputLabel value="#{cust.prevYr}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailsProd.totalsCustDtlsProd[0]}"  class="footerFont">
                                <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <p:column  sortBy="#{cust.currYr}" headerText="Curr. Year"  class="col_right">
                        <h:outputLabel value="#{cust.currYr}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                        <f:facet name="footer">                         
                            <h:outputLabel style="float: right"  value="#{custDetailsProd.totalsCustDtlsProd[1]}"  class="footerFont">
                                <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                            </h:outputLabel>                          
                        </f:facet>
                    </p:column>

                    <p:column  sortBy="#{cust.yrGrowth}" headerText="Year RR"  class="col_right">

                        <h:outputLabel 
                            style="#{
                            cust.yrGrowth gt 100 ?  'color:green' 
                                : (cust.yrGrowth lt 100 ? 'color:red' : 'color:black')
                               }" 
                            value="#{cust.yrGrowth}"><h:outputLabel value="%" rendered="#{cust.yrGrowth ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1"/>
                        </h:outputLabel>


                    </p:column>

                    <!--Bug 288 : #549804 dynamic Quarter header added Q1Q2Q3-->

                    <p:column  sortBy="#{cust.q3}" headerText="#{salesReports.headervalQr[0]}"  class="col_right">
                        <h:outputLabel value="#{cust.q3}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailsProd.totalsCustDtlsProd[2]}"  class="footerFont">
                                <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <!--Bug 288 : #549804 dynamic Quarter header added Q1Q2Q3-->
                    <p:column  sortBy="#{cust.q2}" headerText="#{salesReports.headervalQr[1]}"  class="col_right">
                        <h:outputLabel  value="#{cust.q2}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailsProd.totalsCustDtlsProd[3]}"  class="footerFont">
                                <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <!--Bug 288 : #549804 dynamic Quarter header added Q1Q2Q3-->
                    <p:column  sortBy="#{cust.q1}" headerText="#{salesReports.headervalQr[2]}"  class="col_right">
                        <h:outputLabel value="#{cust.q1}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailsProd.totalsCustDtlsProd[4]}"  class="footerFont" >
                                <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <p:column  sortBy="#{cust.qrtCurr}" headerText="Curr. Qrtr"  class="col_right">
                        <h:outputLabel value="#{cust.qrtCurr}" >
                            <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel style="float: right"  value="#{custDetailsProd.totalsCustDtlsProd[5]}"  class="footerFont">
                                <f:convertNumber groupingUsed="true" maxFractionDigits="0" />
                            </h:outputLabel>
                        </f:facet>
                    </p:column>

                    <p:column  sortBy="#{cust.qrtGrowth}" headerText="Qrtr RR" class="col_right">


                        <h:outputLabel 
                            style="#{
                            cust.qrtGrowth gt 100 ?  'color:green' 
                                : (cust.qrtGrowth lt 100 ? 'color:red' : 'color:black')
                               }" 
                            value="#{cust.qrtGrowth}"><h:outputLabel value="%" rendered="#{cust.qrtGrowth ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1"/>
                        </h:outputLabel>

                    </p:column>

                </p:dataTable>
            </h:form>
            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
            <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
            <ui:include src="/lookup/PartNumDlg.xhtml"/>
            <!--Bug #1357: CRM-3045: Customer summary drill down to part number invoices showing min max pricing-->

            <ui:include src="CustPartDetails.xhtml"/>
        </div>
        <style>

            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }


            .jqplot-target {

                font-size: 0.75em!important;
            }
        </style>
        <!--#3153 Sales Reports - Add gif for loading-->
        <script>
            window.onload = function () {
                //                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
                    //      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("custDetProd").style.pointerEvents = "all";
                    document.getElementById("custDetProd").style.opacity = "1";
                    $('#loading-image').hide();
                    //            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };
        </script>

    </ui:define>

</ui:composition>