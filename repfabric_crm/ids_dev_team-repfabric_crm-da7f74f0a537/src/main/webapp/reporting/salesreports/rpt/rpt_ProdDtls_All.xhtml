<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: productdetails All.xhtml
// Author: Priyadarshini.
//********************************************************** 
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

    <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>   Product Details </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>
            <f:viewParam name="yr" value="#{reports.repYear}" />
            <f:viewParam name="mt" value="#{reports.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="pname" value="#{reportFilters.pricipal.compName}"/>
            <f:viewParam name="cn" value="#{reportFilters.customer.compName}"/>
            <f:viewParam name="c" value="#{reportFilters.customer.compId}"/>
            <f:viewParam name="c1" value="#{reportFilters.customer.compId}"/>
            <f:viewParam name="d" value="#{reportFilters.distributor.compId}"/>
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>
            <f:viewParam name="g" value="#{reportFilters.grp}"/>
            <f:viewParam name="cml" value="#{reportFilters.repCml}"/>
            <!--344579 Dashboard : Halco :Part number query -->
            <f:viewParam name="pn" value="#{reportFilters.princiPartNo}"/>
            <f:viewParam name="c3" value="#{productDtlsItem.repSecCheck}"/>
            <f:viewParam name="repcode" value="#{productDtlsItem.repcode}"/>

            <!--#3056: Task Product Details Report: Updates to sales team filter option-->
            <f:viewAction action="#{reportFilters.getSetSmanName(reportFilters.salesTeam.smanId)}"  />
            <!--<f:viewAction action="#{reportFilters.getSelectedSmanId(reportFilters.customer.compId)}"/>-->

            <!--#3314:    Bug :Product details/Customer details Report : Run report issue-->
            <f:viewAction action="#{reportFilters.getSelectedCustName(reportFilters.customer.compId)}"/> 
<!--            <f:viewAction action="#{reportFilters.getSelectedSmanName(reportFilters.customer.compId)}"/>-->
            <f:viewAction action="#{productDtlsItem.run()}"/>
        </f:metadata>



    </ui:define>


    <ui:define name="body">


        <f:event listener="#{productDtlsItem.isPageAccessible}" type="preRenderView"/> 
        <!--<div id="loading"  class="div-center">-->
        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .3 !important;" />
        <!--<img id="loading-image" src="images/ajax-loader.gif" alt="Loading..." />-->
        <!--</div>-->  


        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7" id="prodrpt" >
            <h:form id="prodDetAllHeader" >
                <!--Bug #3237:  CRM-3855: Dashboard Issues - Kevin can't see dashboard numbers-->
                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyOnlyCustomer(viewCompLookupService.selectedCompany)}" update=":prodDetAllHeader:inputCustomer :prodDetAllHeader:inputAllSalesTeam :prodDetAllHeader:steamsBtn"  action="#{productDtlsItem.resetRepCode()}"/>
                <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update=":prodDetAllHeader:inputDistriName"  action="#{productDtlsItem.resetRepCode()}" />
                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update=":prodDetAllHeader:inputPrincName " />
                <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update=":prodDetAllHeader:inputAllSalesTeam" />
                <p:remoteCommand name="onload" autoRun="true" process="@this" immediate="true" onstart="PF('statusDialog').show()"   oncomplete="PF('statusDialog').hide()"   />
                <p:remoteCommand name="applyProd" actionListener="#{reportFilters.applyProduct(viewProductLookUpService.selectedProduct)}" update="prodDetAllHeader:inpTxtProdName" />

                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >


                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            Product Detail Report 
                            <br/> 

                        </div>
                        &nbsp;&nbsp;<p:outputLabel value="Quarter : #{reports.repYear}-#{reports.quarter},  Month : #{reportFilters.getMonth(reports.repMonth )}" /> 

                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="margin-left:60px">
                        <p:panelGrid   columns="2"  layout="grid" style="width: 600px;float: right;"> 
                            <p:chart type="bar" model="#{reports.barChartYearly}"   style="width:285px;height:180px"/>
                            <p:chart type="bar" model="#{reports.barChartQuarterly}"   style="width:285px;height:180px"/>




                        </p:panelGrid>

                    </div>  
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:-60px">
                        <p:panelGrid id="grid" columns="2" 
                                     style="float: right;width: 350px;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
        <!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"  style="margin-left:-70px"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:outputLabel for="inputDistriName" value="#{custom.labels.get('IDS_DISTRI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputDistriName" value="#{productDtlsItem.repcode eq 17 ? reports.filters[2] 
                                                                           : (productDtlsItem.repcode eq 0 ? reportFilters.distributor.compName : reportFilters.distributor.compName)}"  placeholder="All" readonly="true" style="margin-left:-70px"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyDistri', 3, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All"  style="margin-left:-70px" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_SALES_TEAM')}" immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  id="steamsBtn"
                                                  update=":formSalesTeamLookup " oncomplete="PF('lookupSalesTeam').show()"  style="width:5px"
                                                 
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                             <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{productDtlsItem.repcode eq 17 ? reports.filters[4] 
                                                                         : (productDtlsItem.repcode eq 0 ? reportFilters.customer.compName : reportFilters.customer.compName)}"  placeholder="All" readonly="true" style="margin-left:-70px"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}" 
                                                  update=":formCompLookup :prodDetAllHeader:inputCustomer " oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <p:outputLabel for="inpTxtProdName" value="Part Number" />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inpTxtProdName" value="#{reportFilters.princiPartNo}"   style="margin-left:-70px"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Part Number" immediate="true" 
                                                  actionListener="#{viewProductLookUpService.list('applyProd',0)}" 
                                                  update=":dtPartNumForm" oncomplete="PF('dlgPartNum').show()"  
                                                  styleClass="btn-info btn-xs"  style="width:5px"/>
                            </h:panelGroup>
                            <p:commandButton  value="Run Report "  id="viewreport"  style="width:30px"  styleClass="btn btn-primary btn-xs"  actionListener="#{productDtlsItem.gotTonavigationpage1()}"   onclick="processGif()"/>


                            <!--#2362:   Product Details Report export-->      

                            <p:column>
                                <div id="export" style="visibility:hidden" >
                               <!--//5144 :CRM-4466: Product Detail Report > Export not working-->     
                              <p:commandButton value="Export"   style="width:30px"  
                                             ajax="false"  actionListener="#{productDtlsItem.exportData()}"  
                                             styleClass="btn btn-primary btn-xs" onclick="PrimeFaces.monitorDownload(start, stop);" 
                                             />     
<!--                                    <p:commandLink id="xls" ajax="false" >  
                                        <p:graphicImage name="/images/excel.png" width="24"  onclick="PrimeFaces.monitorDownload(start, stop);"   />
                                        <f:setPropertyActionListener value="false" target="#{exporterController.customExporter}" />  
                                        <pe:exporter type="xlsx" target="#{
                                                     productDtlsItem.repSecCheck ne 0 ?  'prodDtlAllExp' 
                                                         : (productDtlsItem.repSecCheck ne 1 ? 'prodExpwithoutSec' : 'prodExpwithoutSec')
                               }"  fileName="ProductDetails" subTable="true" postProcessor="#{productDtlsItem.postProcessXLS}" />  

                                    </p:commandLink>  -->
                                    <p:spacer width="4px" />
                                    <p:commandLink id="pdf" ajax="false">  
                                        <p:graphicImage value="/resources/images/pdf.png"  width="24"  onclick="PrimeFaces.monitorDownload(start, stop);"/>  
                                        <!--<f:setPropertyActionListener value="false" target="#{productDtlsItem.name}" />-->  
                                        <pe:exporter type="pdf" target="#{
                                                     productDtlsItem.repSecCheck ne 0 ?  'prodDtlAllExp' 
                                                         : (productDtlsItem.repSecCheck ne 1 ? 'prodExpwithoutSec' : 'prodExpwithoutSec')
                               }" fileName="ProductDetails" subTable="true" preProcessor="#{productDtlsItem.preProcessPDF}"/>  

                                    </p:commandLink>
                                </div>
                            </p:column>

                        </p:panelGrid>




                    </div >

                </div>





                <!--         <p:ajaxStatus  onstart="PF('dlgProductSalesHtyaj').show()" oncomplete="PF('dlgProductSalesHtyaj').hide()" />
                        <p:dialog header="Please wait" draggable="false"  widgetVar="dlgProductSalesHtyaj" closable="false" resizable="false" >
                            <h:graphicImage  library="images" name="ajax-loader.gif" />
                            <p:spacer width="5"/>
                            <h:outputLabel value="Loading data.."/>
                        </p:dialog>
                -->
                <!--                <p:ajaxStatus onstart="PF('statusDialog').show()" onsuccess="PF('statusDialog').hide()" />
                
                                <p:dialog widgetVar="statusDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false"   >
                                    <h:graphicImage  library="images" name="ajax-loader.gif" />
                                </p:dialog>-->

                <!--//#2205:CRM-1316: Wes-inc: Reports Timing Out-->
 <!--Bug #2215:CRM-3492: Repfabric / Sales Reports > Product Details > By Distributor-->

     <!--Bug #4994: CRM-4466: Product Detail Report is not working-->
 <p:dataScroller      value="#{productDtlsItem.listProdDtItems}" var="productDtlsItem1"   chunkSize="5"   style="margin-bottom:0">

                    <p:column >

                        <div class="div-selected">

                            <!--#Too many tabs, fixed by replacing _blank to _self -->
                            <a style="color:#323232!important" href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{productDtlsItem1.id}" target="_self" class="header-label">
                                #{productDtlsItem1.name}         
                            </a>
                        </div>
                        <!--//      #11990 CRM-7302   Product Details Report : Does not load-->
                        <!--Bug #2215:CRM-3492: Repfabric / Sales Reports > Product Details > By Distributor-->
                        <p:dataTable style="margin-bottom: 20px"  value="#{productDtlsItem1.details}" var="details"  draggableColumns="true" 
                                     draggableRows="true"    id="custSummaryRepdt"
                                     lazy="true" rowsPerPageTemplate="50,100,150" rows="50" paginatorAlwaysVisible="true" paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}" paginator="true" 

                                     >
                            <p:column rendered="#{reportFilters.grp ne 2 }" style="width: 120px"
                                      sortBy="#{details.principal}"
                                      headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left">
                                <!--#Too many tabs, fixed by replacing _blank to _self -->
                                <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.princiId}" target="_self" title="#{details.principal}">    
                                    <p:outputLabel value="#{details.principal}"  style="text-decoration:underline"  />
                                </a>
                                <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                            </p:column>  
                            <!--Bug #2215:CRM-3492: Repfabric / Sales Reports > Product Details > By Distributor-->
                       
                            <p:column rendered="#{reportFilters.grp ne 1 }" style="width: 120px"  
                                      sortBy="#{details.distributor}"
                                      headerText="#{custom.labels.get('IDS_DISTRI')}" class="col_left" >
                                <!--#Too many tabs, fixed by replacing _blank to _self -->
                                <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.distriId}" target="_self" title=" #{details.distributor}">    

                                    <!--Bug 1019 -Sales Report : Distributor Column -Distributor name not visible properly 21Nov2018-->
                                    <p:outputLabel value="#{details.distributor}"         style="text-decoration: underline"/>    

                                </a>
                                <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                            </p:column> 


<!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:column rendered="#{reportFilters.grp ne 0 }"  style="width: 120px"
                                      sortBy="#{details.customer}"
                                      headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" class="col_left"  >

                                <!--#Too many tabs, fixed by replacing _blank to _self -->
                                <!--../../RepfabricCRM/opploop/companies/CompanyDetails.xhtml?id=-->

                                <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.custId}" target="_self" title="#{details.customer}">    
                                    <p:outputLabel value="#{details.customer}" title="#{details.customer}" />
                                </a>
                                <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                            </p:column>
                            <!--c3 added for secondary customer 1606 -->
                            <p:column rendered="#{productDtlsItem.repSecCheck ne 0 }" style="width: 120px"  
                                      sortBy="#{details.secCustName}"
                                      headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" class="col_left" >

                                <p:outputLabel value="#{details.secCustName}"  title="#{details.secCustName}"  />    

                            </p:column> 
                            <!--//2238--> 
                            <!--Bug #1357: CRM-3045: Customer summary drill down to part number invoices showing min max pricing-->
<!--Bug #2215:CRM-3492: Repfabric / Sales Reports > Product Details > By Distributor-->
                       
                            <p:column sortBy="#{details.partNo}" style="width: 120px"
                                      headerText="#{custom.labels.get('IDS_PART_NUM')}" class="col_left" >
                                <p:commandLink process="@this" actionListener="#{partDetails.showPartDtls(details.partNo, details.prodFamily, details.custId, details.princiId, productDtlsItem1.name ,reports.repYear)}" 
                                               value="#{details.partNo}" 
                                               update="prodpartCust:dlgCust"
                                               oncomplete="PF('dlgPartCust').show()" title="#{details.partNo}"/>
                                <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                            </p:column>
<!--Bug #2215:CRM-3492: Repfabric / Sales Reports > Product Details > By Distributor-->
                       
                            <p:column sortBy="#{details.prodFamily}" 
                                      class="col_left"
                                      headerText="#{custom.labels.get('IDS_PROD_FAMILY')}" style="width: 120px" >
                                <!--5791 CRM-4708: Product Details Report > Show Product Description-->
                                <p:commandButton value="" rendered="#{details.partNo!=''}" title="Click here to View Description" icon="fa fa-eye" id="btnProdPreview"
                              style="border: none;height: 28px;width: 30px; background-color: white"  
                              oncomplete="PF('dlgPartPrinci').show()" action="#{productDtlsItem.gtProdDetails(details.princiId,details.partNo)}" 
                              process="@this" update=":dtPartPrinci:pDscr :dtPartPrinci:partPrinciDlg" immediate="true"
                              />
                                #{details.prodFamily} 
 
                                <f:facet name="footer">
                                    <h:outputLabel value="Total :"  style="float: right"  class="footerFont"/>
                                </f:facet>
                            </p:column>

                            <p:column sortBy="#{details.prevYr}"  
                                      headerText="Previous Year" 
                                      class="col_right"  style="text-align: right"  >
                                <h:outputLabel value="#{details.prevYr}">
                                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel value="#{productDtlsItem1.totPrevYr}"  style="float: right"  class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column sortBy="#{details.currYr}" headerText="Current Year" class="col_right" style="text-align: right">
                                <h:outputLabel value="#{details.currYr}">
                                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel value="#{productDtlsItem1.totCurrYr}"  style="float: right"   class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column sortBy="#{details.yrGrowth}" headerText="Year RR" class="col_right" style="text-align: right">              
                                <h:outputLabel value="#{details.yrGrowth}"
                                               style="#{
                                               details.yrGrowth gt 100 ?  'color:green' 
                                                   : (details.yrGrowth lt 100 ? 'color:red' : 'color:black')
                               }"

                                               ><h:outputLabel value="%" rendered="#{details.yrGrowth ne null}"/>
                                    <f:convertNumber  groupingUsed="false"   maxFractionDigits="1"  />
                                </h:outputLabel>
                            </p:column>

                            <!--Dashboard: Client requirement Added quarterly column Q1,Q2,Q3-->

                            <p:column sortBy="#{details.q3}" headerText="#{salesReports.headervalQr[0]}" style="min-width: 47px;text-align: right" class="col_right" >
                                <h:outputLabel value="#{details.q3}">
                                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                                </h:outputLabel>  

                                <f:facet name="footer">
                                    <h:outputLabel value="#{productDtlsItem1.totQ3}"  style="float: right"  class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column headerText="#{salesReports.headervalQr[1]}" sortBy="#{details.q2}" style="min-width: 47px;text-align: right" class="col_right" >              
                                <h:outputLabel value="#{details.q2}">
                                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                                </h:outputLabel>  

                                <f:facet name="footer">
                                    <h:outputLabel value="#{productDtlsItem1.totQ2}"  style="float: right"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column headerText="#{salesReports.headervalQr[2]}" sortBy="#{details.q1}" style="min-width: 47px;text-align: right"  >              
                                <h:outputLabel value="#{details.q1}"  style="text-align:  right">
                                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                                </h:outputLabel>  
                                <f:facet name="footer">
                                    <h:outputLabel value="#{productDtlsItem1.totQ1}"  style="float: right"   class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column headerText="Current Qtr" sortBy="#{details.qrtCurr}"   style="text-align:  right">              
                                <h:outputLabel value="#{details.qrtCurr}">
                                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                                </h:outputLabel> 

                                <f:facet name="footer">
                                    <h:outputLabel value="#{productDtlsItem1.totCurrQtr}"  style="float: right"   class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <p:column headerText="Qtr RR" sortBy="#{details.qrtGrowth}" class="col_right"  >

                                <h:outputLabel value="#{details.qrtGrowth}"                          
                                               style="#{
                                               details.qrtGrowth gt 100 ?  'color:green' 
                                                   : (details.qrtGrowth lt 100 ? 'color:red' : 'color:black')
                               }">
                                    <h:outputLabel value="%" rendered="#{details.qrtGrowth ne null}"/>
                                    <f:convertNumber  groupingUsed="false"   maxFractionDigits="1"  />
                                </h:outputLabel>

                            </p:column>

                        </p:dataTable> 
                    </p:column>   


                </p:dataScroller>
 <!--</p:dataScroller>-->
                <p:dataTable  id="prodDtlAllExp"  value="#{productDtlsItem.listProdDtItems}" var="productDtlsItem1" scrollable="false" rendered="false">

                    <p:columnGroup  type="header" class="header"  rendered="#{reportFilters.grp ne 2 || reportFilters.grp ne 1 }" >
                        <div> 

                            <p:row>

                                <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                <p:column />
                                <p:column />
                                <p:column headerText="Product Details Report" />
                                <p:column/>
                                <p:column/>
                                <p:column/>

                                <p:column headerText="Year: #{reports.repYear}" />

                                <p:column />
                                <p:column headerText="Month: #{reportFilters.getMonth(reports.repMonth )}" />
                                <p:column />
                                <p:column />
                                <!--5928 Sales Report: Product Details> Export > excel file-Error,  Principal not captured in pdf file-->
                                <p:column rendered="#{productDtlsItem.repSecCheck ne 0 }"/>
                            </p:row>


                            <p:row></p:row>




                            <p:row  class="header" id="myHeaderExp" > 
                                <p:column   rendered="#{reportFilters.grp ne 2 }"
                                            sortBy="#{details.principal}" width="17"
                                            headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left" />
                                <p:column  width="17"
                                           sortBy="#{details.distributor}" 
                                           headerText="#{custom.labels.get('IDS_DISTRI')}" class="col_left" rendered="#{reportFilters.grp ne 1 }"/>
                                <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <p:column   width="17"
                                            sortBy="#{details.customer}"
                                            
                                            headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" class="col_left"  rendered="#{reportFilters.grp ne 0 }"  />
                                <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                <p:column rendered="#{productDtlsItem.repSecCheck ne 0 }" width="17"
                                          sortBy="#{details.secCustName}"
                                          headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" class="col_left"/>
                                <p:column sortBy="#{details.partNo}" 
                                          headerText="#{custom.labels.get('IDS_PART_NUM')}" class="col_left"  width="6" />
                                <p:column sortBy="#{details.prodFamily}" 
                                          class="col_left"
                                          headerText="#{custom.labels.get('IDS_PROD_FAMILY')}"  width="6" />
                                <p:column sortBy="#{details.prevYr}"  
                                          headerText="Previous Year" 
                                          class="col_right"  style="width:10%;"  />
                                <p:column sortBy="#{details.currYr}" headerText="Current Year" class="col_right"  width="6" />
                                <p:column sortBy="#{details.yrGrowth}" headerText="Year RR" class="col_right"  width="6"  />
                                <p:column sortBy="#{details.q3}" headerText="#{salesReports.headervalQr[0]}"  class="col_right"  width="6"  />
                                <p:column headerText="#{salesReports.headervalQr[1]}" sortBy="#{details.q2}" class="col_right"  width="6" />              
                                <p:column headerText="#{salesReports.headervalQr[2]}" sortBy="#{details.q1}" style="min-width: 10%;text-align: right"  width="6"/>              
                                <p:column headerText="Current Qtr" sortBy="#{details.qrtCurr}"   style="text-align:  right;"  width="6"/>              

                                <p:column headerText="Qtr RR" sortBy="#{details.qrtGrowth}" style="text-align:right;"  width="6"/> 



                            </p:row>
                        </div>
                    </p:columnGroup>


                    <p:subTable value="#{productDtlsItem1.details}" var="details" id="prodSub" rendered="true" >

                        <f:facet  name="header"  >
<!--                            <a style="color:#323232!important;text-align: center" href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{productDtlsItem1.id}" target="_self" class="header-label"> 
                            <h:outputText  value=" #{productDtlsItem1.name}" style="color:#323232!important;text-align: center"/>
                            </a>-->
                            <p:commandLink value="#{productDtlsItem1.name}"    onclick="window.open('#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{productDtlsItem1.id}', '_self');"/> 

                        </f:facet>




                        <p:column rendered="#{reportFilters.grp ne 2 }" style="width: 210px"
                                  sortBy="#{details.principal}"
                                  class="col_left">
                            <!--#Too many tabs, fixed by replacing _blank to _self -->
<!--                            <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.princiId}" target="_self" title=" #{details.principal}" >    
                                <p:outputLabel value="#{details.principal}"  style="text-decoration:underline;"  rendered="#{reportFilters.grp ne 2 }" styleClass="text-line-height"/>
                            </a>-->
                            <p:commandLink value="#{details.principal}" style="text-decoration:underline;"   onclick="window.open('#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.princiId}', '_self');"/> 

                            <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                        </p:column>      
                        <p:column rendered="#{reportFilters.grp ne 1 }" style="width: 210px"  
                                  sortBy="#{details.distributor}"
                                  class="col_left" >
                            <!--#Too many tabs, fixed by replacing _blank to _self -->
<!--                            <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.distriId}" target="_self" title=" #{details.distributor}">    

                                Bug 1019 -Sales Report : Distributor Column -Distributor name not visible properly 21Nov2018
                                <p:outputLabel value="#{details.distributor}"         style="text-decoration: underline" rendered="#{reportFilters.grp ne 1 }" />    

                            </a>-->

                            <p:commandLink value="#{details.distributor}" style="text-decoration:underline;"   onclick="window.open('#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.distriId}', '_self');"/> 
                            <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                        </p:column> 



                        <p:column rendered="#{reportFilters.grp ne 0 }"  style="width: 210px"
                                  sortBy="#{details.customer}"
                                  class="col_left"  >

                            <!--#Too many tabs, fixed by replacing _blank to _self -->
                            <!--.../../RepfabricCRM/opploop/companies/CompanyDetails.xhtml?id=-->

<!--                            <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.custId}" target="_self" title="#{details.customer}">    
                                <p:outputLabel value="#{details.customer}" title="#{details.customer}" rendered="#{reportFilters.grp ne 0 }"  />
                            </a>-->
                            <p:commandLink value="#{details.customer}" style="text-decoration:underline;width:20%"   onclick="window.open('#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.custId}', '_self');"/> 



                            <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                        </p:column>
                        <!--c3 added for secondary customer 1606 -->
                        <p:column rendered="#{productDtlsItem.repSecCheck ne 0 }" style="width:20%"  
                                  sortBy="#{details.secCustName}"
                                  class="col_left" >

                            <p:outputLabel value="#{details.secCustName}"  title="#{details.secCustName}" rendered="#{productDtlsItem.repSecCheck ne 0 }" style="width:20%"/>    

                        </p:column> 
                        <!--//2238--> 
                        <p:column sortBy="#{details.partNo}" style="width: 210px"
                                  class="col_left" >

                            <p:commandLink actionListener="#{partDetails.showPartDtls(details.partNo, details.prodFamily, details.custId, details.princiId, productDtlsItem1.name ,reports.repYear)}" 
                                           value="#{details.partNo}" 
                                           update="prodpart:dlg"
                                           oncomplete="PF('dlgPart').show()"  title="#{details.partNo}"/>
                            <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                        </p:column>

                        <p:column sortBy="#{details.prodFamily}" 
                                  class="col_left" style="width: 210px"
                                  >
                            <p:outputLabel  value="#{details.prodFamily}" /> 

                        </p:column>

                        <p:column sortBy="#{details.prevYr}"  style="width: 210px;text-align: right;"

                                  class="col_right"   >
                            <h:outputLabel value="#{details.prevYr}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>


                        </p:column>

                        <p:column sortBy="#{details.currYr}"  class="col_right" style="text-align: right;width:210px">
                            <h:outputLabel value="#{details.currYr}"   class="text-line-height">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>


                        </p:column>

                        <p:column sortBy="#{details.yrGrowth}"  class="col_right" style="text-align: right;width:210px">              
                            <h:outputLabel value="#{details.yrGrowth}"
                                           style="#{
                                           details.yrGrowth gt 100 ?  'color:green' 
                                               : (details.yrGrowth lt 100 ? 'color:red' : 'color:black')
                               }"

                                           ><h:outputLabel value="%" rendered="#{details.yrGrowth ne null}"/>
                                <f:convertNumber  groupingUsed="false"   maxFractionDigits="1"  />
                            </h:outputLabel>
                        </p:column>

                        <!--Dashboard: Client requirement Added quarterly column Q1,Q2,Q3-->

                        <p:column sortBy="#{details.q3}"  style="min-width: 47px;text-align: right;width:210px" class="col_right" >
                            <h:outputLabel value="#{details.q3}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>  

                            <!--                            <f:facet name="footer">
                                                            <h:outputLabel value="#{productDtlsItem1.totQ3}"  style="float: right"  class="footerFont">
                                                                <f:convertNumber maxFractionDigits="0"/>
                                                            </h:outputLabel>
                                                        </f:facet>-->
                        </p:column>

                        <p:column  sortBy="#{details.q2}" style="min-width: 47px;text-align: right;width:210px" class="col_right" >              
                            <h:outputLabel value="#{details.q2}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>  


                        </p:column>

                        <p:column  sortBy="#{details.q1}" style="min-width: 47px;text-align: right;width:210px"  >              
                            <h:outputLabel value="#{details.q1}"  style="text-align:  right">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>  

                        </p:column>

                        <p:column  sortBy="#{details.qrtCurr}"   style="text-align:right;width:210px">              
                            <h:outputLabel value="#{details.qrtCurr}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 

                        </p:column>

                        <p:column  sortBy="#{details.qrtGrowth}" class="col_right"  style="width:210px">

                            <h:outputLabel value="#{details.qrtGrowth}"                          
                                           style="#{
                                           details.qrtGrowth gt 100 ?  'color:green' 
                                               : (details.qrtGrowth lt 100 ? 'color:red' : 'color:black')
                               }">
                                <h:outputLabel value="%" rendered="#{details.qrtGrowth ne null}"/>
                                <f:convertNumber  groupingUsed="false"   maxFractionDigits="1"  />
                            </h:outputLabel>

                        </p:column>

                        <p:columnGroup type="footer">
                            <p:row> 

                                <p:column  />
                                <p:column  />
                                <p:column  />
                                <p:column   />
                                <p:column footerText="Total :"
                                          style="float: right"  class="footerFont"/>
                                <p:column footerText="#{productDtlsItem1.totPrevYr}"  style="text-align: right"/>
                                <p:column footerText="#{productDtlsItem1.totCurrYr}"  style="text-align: right" />
                                <p:column  />
                                <p:column   footerText="#{productDtlsItem1.totQ3}"  style="text-align: right"/>
                                <p:column   footerText="#{productDtlsItem1.totQ2}"  style="text-align: right"/>
                                <p:column   footerText="#{productDtlsItem1.totQ1}"  style="text-align: right"/>
                                <p:column   footerText="#{productDtlsItem1.totCurrQtr}"  style="text-align: right"/>
                                <p:column  />

                            </p:row>
                        </p:columnGroup>


                    </p:subTable>
                    <!--</p:column>-->   


                </p:dataTable>

                <p:dataTable  id="prodExpwithoutSec"  value="#{productDtlsItem.listProdDtItems}" var="productDtlsItem1" scrollable="false"  rendered="false" >

                    <p:columnGroup  type="header" class="header"  rendered="#{reportFilters.grp ne 2 || reportFilters.grp ne 1 }" >
                        <div> 
                            <p:row>

                                <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                <p:column />
                                <p:column />
                                <p:column headerText="Product Details Report" />
                                <p:column/>
                                <p:column/>
                                <p:column/>

                                <p:column headerText="Year: #{reports.repYear}" />

                                <p:column />
                                <p:column headerText="Month: #{reportFilters.getMonth(reports.repMonth )}" />
                                <p:column />
                                <p:column />
                                <!--5928 Sales Report: Product Details> Export > excel file-Error,  Principal not captured in pdf file-->
                                <p:column rendered="#{productDtlsItem.repSecCheck ne 0 }"/>
                            </p:row>


                            <p:row></p:row>




                            <p:row  class="header" id="myHeaderExp" > 
                                <p:column   rendered="#{reportFilters.grp ne 2 }"
                                            sortBy="#{details.principal}" width="17"
                                            headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left" />
                                <p:column  width="17"
                                           sortBy="#{details.distributor}" 
                                           headerText="#{custom.labels.get('IDS_DISTRI')}" class="col_left" rendered="#{reportFilters.grp ne 1 }"/>
                                <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <p:column   width="17"
                                            sortBy="#{details.customer}"
                                            
                                            headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" class="col_left"  rendered="#{reportFilters.grp ne 0 }"  />
                                <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                <p:column rendered="#{productDtlsItem.repSecCheck ne 0 }" width="17"
                                          sortBy="#{details.secCustName}"
                                          headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" class="col_left"/>
                                <p:column sortBy="#{details.partNo}" 
                                          headerText="#{custom.labels.get('IDS_PART_NUM')}" class="col_left"  width="6" />
                                <p:column sortBy="#{details.prodFamily}" 
                                          class="col_left"
                                          headerText="#{custom.labels.get('IDS_PROD_FAMILY')}"  width="6" />
                                <p:column sortBy="#{details.prevYr}"  
                                          headerText="Previous Year" 
                                          class="col_right"  style="width:10%;"  />
                                <p:column sortBy="#{details.currYr}" headerText="Current Year" class="col_right"  width="6" />
                                <p:column sortBy="#{details.yrGrowth}" headerText="Year RR" class="col_right"  width="6"  />
                                <p:column sortBy="#{details.q3}" headerText="#{salesReports.headervalQr[0]}"  class="col_right"  width="6"  />
                                <p:column headerText="#{salesReports.headervalQr[1]}" sortBy="#{details.q2}" class="col_right"  width="6" />              
                                <p:column headerText="#{salesReports.headervalQr[2]}" sortBy="#{details.q1}" style="min-width: 10%;text-align: right"  width="6"/>              
                                <p:column headerText="Current Qtr" sortBy="#{details.qrtCurr}"   style="text-align:  right;"  width="6"/>              

                                <p:column headerText="Qtr RR" sortBy="#{details.qrtGrowth}" style="text-align:right;"  width="6"/> 



                            </p:row>
                        </div>
                    </p:columnGroup>


                    <p:subTable value="#{productDtlsItem1.details}" var="details" id="prodSub" rendered="true" >

                        <f:facet  name="header"  >
<!--                            <a style="color:#323232!important;text-align: center" href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{productDtlsItem1.id}" target="_self" class="header-label"> 
                            <h:outputText  value=" #{productDtlsItem1.name}" style="color:#323232!important;text-align: center"/>
                            </a>-->
                            <p:commandLink value="#{productDtlsItem1.name}"    onclick="window.open('#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{productDtlsItem1.id}', '_self');"/> 

                        </f:facet>




                        <p:column rendered="#{reportFilters.grp ne 2 }" style="width: 210px"
                                  sortBy="#{details.principal}"
                                  class="col_left">
                            <!--#Too many tabs, fixed by replacing _blank to _self -->
<!--                            <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.princiId}" target="_self" title=" #{details.principal}" >    
                                <p:outputLabel value="#{details.principal}"  style="text-decoration:underline;"  rendered="#{reportFilters.grp ne 2 }" styleClass="text-line-height"/>
                            </a>-->
                            <p:commandLink value="#{details.principal}" style="text-decoration:underline;"   onclick="window.open('#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.princiId}', '_self');"/> 

                            <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                        </p:column>      
                        <p:column rendered="#{reportFilters.grp ne 1 }" style="width: 210px"  
                                  sortBy="#{details.distributor}"
                                  class="col_left" >
                            <!--#Too many tabs, fixed by replacing _blank to _self -->
<!--                            <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.distriId}" target="_self" title=" #{details.distributor}">    

                                Bug 1019 -Sales Report : Distributor Column -Distributor name not visible properly 21Nov2018
                                <p:outputLabel value="#{details.distributor}"         style="text-decoration: underline" rendered="#{reportFilters.grp ne 1 }" />    

                            </a>-->

                            <p:commandLink value="#{details.distributor}" style="text-decoration:underline;"   onclick="window.open('#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.distriId}', '_self');"/> 
                            <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                        </p:column> 



                        <p:column rendered="#{reportFilters.grp ne 0 }"  style="width: 210px"
                                  sortBy="#{details.customer}"
                                  class="col_left"  >

                            <!--#Too many tabs, fixed by replacing _blank to _self -->
                            <!--.../../RepfabricCRM/opploop/companies/CompanyDetails.xhtml?id=-->

<!--                            <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.custId}" target="_self" title="#{details.customer}">    
                                <p:outputLabel value="#{details.customer}" title="#{details.customer}" rendered="#{reportFilters.grp ne 0 }"  />
                            </a>-->
                            <p:commandLink value="#{details.customer}" style="text-decoration:underline;width:20%"   onclick="window.open('#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{details.custId}', '_self');"/> 



                            <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                        </p:column>
                        <!--c3 added for secondary customer 1606 -->
                        <p:column rendered="#{productDtlsItem.repSecCheck ne 0 }" style="width:20%"  
                                  sortBy="#{details.secCustName}"
                                  class="col_left" >

                            <p:outputLabel value="#{details.secCustName}"  title="#{details.secCustName}" rendered="#{productDtlsItem.repSecCheck ne 0 }" style="width:20%"/>    

                        </p:column> 
                        <!--//2238--> 
                        <p:column sortBy="#{details.partNo}" style="width: 210px"
                                  class="col_left" >

                            <p:commandLink actionListener="#{partDetails.showPartDtls(details.partNo, details.prodFamily, details.custId, details.princiId, productDtlsItem1.name ,reports.repYear)}" 
                                           value="#{details.partNo}" 
                                           update="prodpart:dlg"
                                           oncomplete="PF('dlgPart').show()"  title="#{details.partNo}"/>
                            <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                        </p:column>

                        <p:column sortBy="#{details.prodFamily}" 
                                  class="col_left" style="width: 210px"
                                  >
                            <p:outputLabel  value="#{details.prodFamily}" /> 

                        </p:column>

                        <p:column sortBy="#{details.prevYr}"  style="width: 210px;text-align: right;"

                                  class="col_right"   >
                            <h:outputLabel value="#{details.prevYr}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>


                        </p:column>

                        <p:column sortBy="#{details.currYr}"  class="col_right" style="text-align: right;width:210px">
                            <h:outputLabel value="#{details.currYr}"   class="text-line-height">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>


                        </p:column>

                        <p:column sortBy="#{details.yrGrowth}"  class="col_right" style="text-align: right;width:210px">              
                            <h:outputLabel value="#{details.yrGrowth}"
                                           style="#{
                                           details.yrGrowth gt 100 ?  'color:green' 
                                               : (details.yrGrowth lt 100 ? 'color:red' : 'color:black')
                               }"

                                           ><h:outputLabel value="%" rendered="#{details.yrGrowth ne null}"/>
                                <f:convertNumber  groupingUsed="false"   maxFractionDigits="1"  />
                            </h:outputLabel>
                        </p:column>

                        <!--Dashboard: Client requirement Added quarterly column Q1,Q2,Q3-->

                        <p:column sortBy="#{details.q3}"  style="min-width: 47px;text-align: right;width:210px" class="col_right" >
                            <h:outputLabel value="#{details.q3}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>  

                            <!--                            <f:facet name="footer">
                                                            <h:outputLabel value="#{productDtlsItem1.totQ3}"  style="float: right"  class="footerFont">
                                                                <f:convertNumber maxFractionDigits="0"/>
                                                            </h:outputLabel>
                                                        </f:facet>-->
                        </p:column>

                        <p:column  sortBy="#{details.q2}" style="min-width: 47px;text-align: right;width:210px" class="col_right" >              
                            <h:outputLabel value="#{details.q2}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>  


                        </p:column>

                        <p:column  sortBy="#{details.q1}" style="min-width: 47px;text-align: right;width:210px"  >              
                            <h:outputLabel value="#{details.q1}"  style="text-align:  right">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>  

                        </p:column>

                        <p:column  sortBy="#{details.qrtCurr}"   style="text-align:right;width:210px">              
                            <h:outputLabel value="#{details.qrtCurr}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 

                        </p:column>

                        <p:column  sortBy="#{details.qrtGrowth}" class="col_right"  style="width:210px">

                            <h:outputLabel value="#{details.qrtGrowth}"                          
                                           style="#{
                                           details.qrtGrowth gt 100 ?  'color:green' 
                                               : (details.qrtGrowth lt 100 ? 'color:red' : 'color:black')
                               }">
                                <h:outputLabel value="%" rendered="#{details.qrtGrowth ne null}"/>
                                <f:convertNumber  groupingUsed="false"   maxFractionDigits="1"  />
                            </h:outputLabel>

                        </p:column>

                        <p:columnGroup type="footer">
                            <p:row> 

                                <p:column  />
                                <p:column  />
                                <p:column  />

                                <p:column footerText="Total :"
                                          style="float: right"  class="footerFont"/>
                                <p:column footerText="#{productDtlsItem1.totPrevYr}"  style="text-align: right"/>
                                <p:column footerText="#{productDtlsItem1.totCurrYr}"  style="text-align: right" />
                                <p:column  />
                                <p:column   footerText="#{productDtlsItem1.totQ3}"  style="text-align: right"/>
                                <p:column   footerText="#{productDtlsItem1.totQ2}"  style="text-align: right"/>
                                <p:column   footerText="#{productDtlsItem1.totQ1}"  style="text-align: right"/>
                                <p:column   footerText="#{productDtlsItem1.totCurrQtr}"  style="text-align: right"/>
                                <p:column  />

                            </p:row>
                        </p:columnGroup>


                    </p:subTable>
                    <!--</p:column>-->   


                </p:dataTable>




            </h:form>


            <p:dialog widgetVar="inProductDetProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Please wait...Exporting records" />
                        <br /><br />
                        <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                    </p:outputPanel>
                </h:form>
            </p:dialog>

         
<!--5791 CRM-4708: Product Details Report > Show Product Description-->
            <h:form id="dtPartPrinci">
            <p:dialog position="center"  footer="'Esc' to close"  resizable="false" id="partPrinciDlg" 
                  closeOnEscape="true"  header="Details for Part No. : #{productDtlsItem.prodMst.prodPrinciPartno}"  
                  height="200" width="350" widgetVar="dlgPartPrinci">
               
<!--                    <div class="div-selected" style="height:20px;width: 100%;background: #9E9E9E" >Part No : 
                        <p:outputLabel value="#{productDtlsItem.prodMst.prodPrinciPartno}" id="pNo"/>
                    </div>-->
                <p:outputLabel value="Product Description: " /> <br />
                <p:outputLabel value="#{productDtlsItem.prodMst.prodDesc}" id="pDscr"/>
               
            </p:dialog>
            
             </h:form>
            
            
            
            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
            <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
            <ui:include src="/lookup/PartNumDlg.xhtml"/>

            <!--Bug #1357: CRM-3045: Customer summary drill down to part number invoices showing min max pricing-->

            <ui:include src="CustPartDetails.xhtml"/>

        </div>




        <style>

            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }

             /*14453 ESCALATIONS CRM-8464   Reporting Graphs Legend not legible - Multiple Instances*/
            .jqplot-axis {
                 font-size: 0.25em !important;
            }

            .jqplot-target {

                font-size: 0.75em!important;
            }
            .ui-datatable-subtable-header
            {
                text-align: center!important;
                height: 25px;
                width: 100%;
                text-align: center;
                background: #0078d7 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
                background: #0078d7 -moz-linear-gradient(top, rgba(255,255,255,0.8), rgba(255,255,255,0)); /*firefox*/
            }


            .ui-datatable table tbody td, body .ui-treetable table tbody td, body .ui-datatable table thead th, body .ui-treetable table thead th {
                padding: 1px 10px!important;

            }

            .ui-widget-header{
                background-color:#dedede;
            }


            .text-line-height
            {
                line-height: 15px!important;
                letter-spacing:200px!important;


            }

            @media print {
                a[href]:after {
                    content: none !important;
                }
            }

            .ui-datatable-scrollable-theadclone{
                visibility: hidden!important;
            }


            .ui-datatable thead tr th.align-left,
            .ui-datatable tbody tr td.align-left {
                text-align: left;
            }




            body {
                margin: 0;
                font-family: Arial, Helvetica, sans-serif;
            }

            .top-container {
                /*  background-color: #f1f1f1;*/
                padding: 30px;
                text-align: center;
            }

            .header {
                padding: 10px 16px;
                background-color:#dedede;
                color: #f1f1f1;
            }

            .content {
                padding: 16px;
            }

            .sticky {
                position: fixed;
                top: 0;
                width: 100%;
            }

            .sticky + .content {
                /*                padding-top: 102px;*/
            }

            .subtable-width{
                width: 200%;
            }

            .loaderz{
                position: fixed;
                left: 0px;
                top: 0px;
                width: 100%;
                height: 100%;
                z-index: 9999;
                background: url("#{request.contextPath}/resources/images/progressicon.gif") 
                    50% 50% no-repeat rgb(249,249,249);
                /*                   background: url("#{request.contextPath}/resources/images/progressicon.gif/10px-Phi_fenomeni.gif") 
                                    50% 50% no-repeat rgb(249,249,249);*/
            }

            /*            #loading{
                            position: fixed;
                            left: 50%;
                            top: 50%;
                            width: 30em;
                            height:18em;
                            margin-left:-15em;
            
                        }
            
            
                        #loading-image {
                             position: fixed;
                            left: 50%;
                            top: 50%;
                            width: 30em;
                            height:18em;
                            margin-left:-15em;
                            
                        }*/
/* #cmds\.ui-state-hover{
               background-color: white!important; 
            }*/
         /*5896 CRM-4794: product details report columns shifted*/
        .ui-datatable-scrollable-header *,
        .ui-datatable-scrollable-theadclone * {
            -moz-box-sizing: content-box;
            -webkit-box-sizing: content-box;
            box-sizing: content-box;
        }

        body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
            width: 15px;
        }
    </style>

        <!--#3084:TaskProduct Details Report - Add gif for loading style added-->
        <script>
            window.onscroll = function () {
                myFunction()
            };

//            var header = document.getElementById("prodDetAllHeader:prodDtlAll_head");
//            var sticky = header.offsetTop;

            function myFunction() {
                if (window.pageYOffset > sticky) {
                    header.classList.add("sticky");
                } else {
                    header.classList.remove("sticky");
                }
            }

            window.onload = function () {
//                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("prodrpt").style.pointerEvents = "all";
                    document.getElementById("prodrpt").style.opacity = "1";
                    document.getElementById("export").style.visibility = "visible";
                    $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };

            function visibility()
            {
                document.getElementById("export").style.visibility = "hidden";
                $('#loading-image').show();
                document.getElementById("prodrpt").style.pointerEvents = "none";
                document.getElementById("prodrpt").style.opacity = "0.7";

            }


            function processGif()
            {
                $('#loading-image').show();
                document.getElementById("prodrpt").style.pointerEvents = "none";
                document.getElementById("prodrpt").style.opacity = "0.4";
            }


            function hide()
            {
                $('#loading-image').hide();
            }

            function start() {

                PF('inProductDetProgressDlg').show();
            }

            function stop() {
                PF('inProductDetProgressDlg').hide();
            }


        </script>

    </ui:define>
</ui:composition>