<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: productdetails All.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

    <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>   Product Sales History </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>

            <f:viewParam name="yr" value="#{reports.repYear}" />
            <f:viewParam name="mt" value="#{reports.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="c" value="#{reportFilters.customer.compId}"/>
            <f:viewParam name="d" value="#{reportFilters.distributor.compId}"/>
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>
            <f:viewParam name="pn" value="#{reportFilters.princiPartNo}"/>
            <f:viewParam name="g" value="#{reportFilters.grp}"/>
            <!--//        2074 :CRM-1836: Caltron: Product Sales History-->
            <f:viewParam name="fy" value="#{reports.repFrmYear}"/>

   <!--<f:viewParam name="c3" value="#{salesSummaryParent.checkFlag}"/>-->
            <!--344579 Dashboard : Halco :Part number query. -->

            <f:viewAction action="#{productSalesHistory.ProdSalesHtry()}"/>

        </f:metadata>


    </ui:define>

    <ui:define name="body">
        <f:event listener="#{productSalesHistory.isPageAccessible}" type="preRenderView"/> 

        <!--#3153 Sales Reports - Add gif for loading-->

        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />
        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4" id="prodsalhisrpt" >
            <h:form id="prodSalesHistory" >
                <!--#3153 Sales Reports - Add gif for loading-->

                <p:dialog header="Please wait" draggable="false"  widgetVar="dlgProductSalesHtyaj" closable="false" resizable="false" modal="true">
                    <h:graphicImage  library="images" name="ajax-loader.gif" />
                    <p:spacer width="5"/>
                    <h:outputLabel value="Loading data.."/>
                </p:dialog>

<!--           <p:dataList   type="definition"  value="#{productSalesHistory.listProductSalesHistorys}" var="prodsal"  >
                -->        
                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update=":prodSalesHistory:inputCustomer" />
                <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update=":prodSalesHistory:inputDistriName  "/>
                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update=":prodSalesHistory:inputPrincName " />
                <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update=":prodSalesHistory:inputAllSalesTeam" />
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >


                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            Product Sales History
                            <br/>

                        </div>
                        <!--//        2074 :CRM-1836: Caltron: Product Sales History-->
                        <p:outputLabel value="January #{((reportFilters.princiPartNo.length() eq 0) ? reports.repYear : reportFilters.repFrmYear)}  - #{reportFilters.getMonth(reports.repMonth )} #{((reportFilters.princiPartNo.length() ne  0) and reports.repYear lt reportFilters.repFrmYear) ? reportFilters.repFrmYear :reports.repYear }" />
                        <div class="ui-sm-12 ui-md-2 ui-lg-2" style="margin-left:1160px">

                        </div>  

                    </div>

                    <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                               <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->
                        <p:panelGrid id="grid" columns="2" 
                                     style="width: 350px;margin-left:400px"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
        <!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"  style="margin-left:-70px"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:outputLabel for="inputDistriName" value="#{custom.labels.get('IDS_DISTRI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputDistriName" value="#{reportFilters.distributor.compName}"  placeholder="All" readonly="true" style="margin-left:-70px"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyDistri', 3, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--#3473:     Bug sales team - Sales team data we can enter manually-->
                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All"  style="margin-left:-70px"  readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_SALES_TEAM')}" immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true" style="margin-left:-70px"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <p:commandButton  value="Run Report "  id="viewreport"  style="width:30px"  styleClass="btn btn-primary btn-xs"  actionListener="#{productSalesHistory.ProdSalesHtry()}" onclick="processGif();" oncomplete="hideGif();" update=":prodSalesHistory:proddsal"/>
                            <!--#3399 :Task Sales Reports > Product Sales History > Add export option-->

                            <p:column>
                                <p:commandLink id="xls" ajax="false" onclick="PrimeFaces.monitorDownload(start, stop);" >  
                                    <p:graphicImage name="/images/excel.png" width="24" />
                                    <pe:exporter type="xlsx" target="prodSalHistoryExport" fileName="Product_Sales_History" subTable="false" postProcessor="#{productSalesHistory.postProcessXLS}" />

                                </p:commandLink>
                                <p:commandLink id="pdf" ajax="false"  onclick="PrimeFaces.monitorDownload(start, stop);" >  
                                    <p:graphicImage value="/resources/images/pdf.png"  width="24" />  
                                    <pe:exporter type="pdf" target="prodSalHistoryPdf" fileName="Product_Sales_History" subTable="false"  preProcessor="#{productSalesHistory.preProcessPDF}"   />  

                                </p:commandLink> 


                            </p:column>
                            <!--#3399 :Task Sales Reports > Product Sales History > Add export option end-->
                        </p:panelGrid>

                    </div >

                </div>


                <div class="div-selected" style="height:25px;width: 100%;" >
                    <label style="font-size: 1.3em;font-weight: bold;color: black;line-height: 25px;padding-left: 5px;">


                        #{productSalesHistory.repPartNo1 eq '' ?  '' 
                          : (productSalesHistory.repPartNo1 eq productSalesHistory.repPartNo1 ? ' Part No  :': '')}
#{ productSalesHistory.repPartNo1 eq '' ?  '
                           ' 
                           : (productSalesHistory.repPartNo1 eq productSalesHistory.repPartNo1 ? productSalesHistory.repPartNo1: '')}
                             #{ productSalesHistory.repPartNo1 eq '' ?  '
                           ' 
                           : (productSalesHistory.repPartNo1 eq productSalesHistory.repPartNo1 ? ' . . .': '')}

                    </label>               
                </div>
                <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->
                <!-- 11-12-2020:TASK#2609-CRM-3451: MOBILE & ONLINE need Part Description visible (not just part#) -->
                <p:dataTable     value="#{productSalesHistory.listProductSalesHistorys}" var="prodsal" emptyMessage="No data found"  draggableColumns="true" 
                                 id="proddsal"     resizableColumns="true"
                                 virtualScroll="true" 
                                 rows="50"
                                 liveScroll="true"
                                 scrollRows="50"
                                 scrollable="#{productSalesHistory.listProductSalesHistorys.size() ge 75}"
                                 >
                      <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->
                    <p:column    sortBy="#{prodsal.repPrincname}" styleClass="might-overflow"  style="width:150px"  headerText="#{custom.labels.get('IDS_PRINCI')}" >
                        #{prodsal.repPrincname}



                    </p:column>
                        <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->
                    <p:column    sortBy="#{prodsal.repDistriName}" styleClass="might-overflow"  style="width:150px"  headerText="#{custom.labels.get('IDS_DISTRI')}"  >
                        #{prodsal.repDistriName}



                    </p:column>
                          <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->
                    <!-- 11-12-2020:TASK#2609-CRM-3451: MOBILE & ONLINE need Part Description visible (not just part#) -->
                    <p:column  sortBy="#{prodsal.repCustName}" styleClass="might-overflow"  style="width:150px"   headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" >
                        #{prodsal.repCustName}



                    </p:column>
                      <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->
                    <!-- 11-12-2020:TASK#2609-CRM-3451: MOBILE & ONLINE need Part Description visible (not just part#) -->
                    <p:column     sortBy="#{prodsal.repSmanName}" styleClass="might-overflow"   style="text-align: left;width:150px"  headerText="#{custom.labels.get('IDS_SALES_TEAM')}" >
                        #{prodsal.repSmanName}



                    </p:column>
                      <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->
                    <!--#446311 , add secondary customer-->
                    <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                    <p:column  class="col_right" sortBy="#{prodsal.repSecCust} " styleClass="might-overflow"   style="width:150px"  headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}"   >

                        #{prodsal.repSecCust}


                    </p:column>      

  <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->
                    <p:column   sortBy="#{prodsal.repPartNo1}" styleClass="might-overflow"   style="width:100px"  headerText="#{custom.labels.get('IDS_PART_NUM')}"   >
                        #{prodsal.repPartNo1}



                    </p:column>
  <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->
                    <p:column  sortBy="#{prodsal.salInvcNumber}" styleClass="might-overflow"   style="width:100px"  headerText="Invoice Number"  >
                        #{prodsal.salInvcNumber}



                    </p:column>
    <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->
                    <p:column   class="col_right"  sortBy="#{prodsal.salDate}" styleClass="might-overflow"    style="width:100px"  headerText="Date"  >
                        #{prodsal.salDate}



                    </p:column>
  <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->
                    <!-- 11-12-2020:TASK#2609-CRM-3451: MOBILE & ONLINE need Part Description visible (not just part#) -->
                    <p:column    class="col_right"  sortBy="#{prodsal.salTotalPrice}" styleClass="might-overflow"   style="width:100px"  headerText="Total Price"    >
                        #{prodsal.salTotalPrice}



                    </p:column>
  <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->

                    <!-- 11-12-2020:TASK#2609-CRM-3451: MOBILE & ONLINE need Part Description visible (not just part#) -->
                    <p:column    class="col_right" sortBy="#{prodsal.salUnitPrice}" styleClass="might-overflow"   style="width:100px"   headerText="Unit Price" >
                        <h:outputLabel value="#{prodsal.salUnitPrice}">
                            <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                            <f:convertNumber minFractionDigits="2"/>
                            <f:convertNumber maxFractionDigits="6" />
                        </h:outputLabel>



                    </p:column>
                      <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->
                    <!-- 11-12-2020:TASK#2609-CRM-3451: MOBILE & ONLINE need Part Description visible (not just part#) -->
                    <p:column  sortBy="#{prodsal.salQuantity}"  style="width:100px" styleClass="might-overflow"    headerText="Quantity" class="col_right">
                        #{prodsal.salQuantity}



                    </p:column>
                      <!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->
                    <!-- 11-12-2020:TASK#2609-CRM-3451: MOBILE & ONLINE need Part Description visible (not just part#) -->
                    <p:column sortBy="#{prodsal.prodDesc}"  style="width:150px" styleClass="might-overflow"   headerText="Product Description"  >

                        <p:outputLabel value="#{productSalesHistory.trimProdDesc(prodsal.prodDesc)}"   title="#{prodsal.prodDesc}" />
                    </p:column>


                </p:dataTable>

                <p:dataTable   value="#{productSalesHistory.listProductSalesHistorys}" var="prodsal" id="prodSalHistoryExport"  rendered="false">

                    <p:columnGroup  type="header" class="header"   >
                        <p:row>

                            <p:column colspan="3" rowspan="2"  headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>


                            <p:column  rowspan="2"   headerText="Product Sales History" />

                            <!--//        2074 :CRM-1836: Caltron: Product Sales History-->

                            <p:column  colspan="4" rowspan="2" headerText="January #{((reportFilters.princiPartNo.length() eq 0) ? reports.repYear : reportFilters.repFrmYear)} - #{reportFilters.getMonth(reports.repMonth )} #{((reportFilters.princiPartNo.length() ne  0) and reports.repYear lt reportFilters.repFrmYear) ? reportFilters.repFrmYear :reports.repYear }" />



                        </p:row>

                        <p:row></p:row>
                        <p:row></p:row>

                        <p:row>

                            <p:column  colspan="8" rowspan="2" headerText="#{productSalesHistory.repPartNo1 eq '' ?  '' 
                                                                             : (productSalesHistory.repPartNo1 eq productSalesHistory.repPartNo1 ? ' Part No  :': '')}
#{ productSalesHistory.repPartNo1 eq '' ? '' 
                                          : (productSalesHistory.repPartNo1 eq productSalesHistory.repPartNo1 ? productSalesHistory.repPartNo1: '')}
                             #{ productSalesHistory.repPartNo1 eq '' ?  '' 
                                          : (productSalesHistory.repPartNo1 eq productSalesHistory.repPartNo1 ? '...':'')}"  />

                        </p:row>
                        <p:row></p:row>

                        <p:row>


                            <p:column   headerText="#{custom.labels.get('IDS_PRINCI')}" style="text-height: max-size"/>
                            <p:column  headerText="#{custom.labels.get('IDS_DISTRI')}"/>
                            <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:column  headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                            <p:column  headerText="#{custom.labels.get('IDS_SALES_TEAM')}"/>
                            <p:column   headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}"   />
                            <p:column     headerText="#{custom.labels.get('IDS_PART_NUM')}"   />
                            <p:column      headerText="Invoice Number"  />
                            <p:column    headerText="Date"  />
                            <p:column    headerText="Total Price"    />
                            <p:column    headerText="Unit Price" />
                            <p:column    headerText="Quantity" />
                            <!--  4-12-2020 :TASK#2609 -CRM-3451: MOBILE & ONLINE need Part Description visible (not just part#) -->
                            <p:column    headerText="Product Description" />
                        </p:row>


                    </p:columnGroup>  
                    <p:column>

                        <h:outputText value="#{prodsal.repPrincname}" />



                    </p:column>
                    <p:column  sortBy="#{prodsal.repDistriName}"    headerText="#{custom.labels.get('IDS_DISTRI')}"  >
                        <h:outputText value="#{prodsal.repDistriName}" />



                    </p:column>
                    <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                    <p:column  sortBy="#{prodsal.repCustName}"    headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" >
                        <h:outputText value="#{prodsal.repCustName}" />



                    </p:column>
                    <p:column  sortBy="#{prodsal.repSmanName}"    headerText="#{custom.labels.get('IDS_SALES_TEAM')}" style="text-align: left" width="20%" >
                        <h:outputText value="#{prodsal.repSmanName}" />



                    </p:column>
                    <!--#446311 , add secondary customer-->
                    <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                    <p:column class="col_right" sortBy="#{prodsal.repSecCust}"    headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}"   >

                        <h:outputText value="#{prodsal.repSecCust}" />


                    </p:column>      


                    <p:column  sortBy="#{prodsal.repPartNo1}"    headerText="#{custom.labels.get('IDS_PART_NUM')}"   >
                        <h:outputText value="#{prodsal.repPartNo1}" />



                    </p:column>

                    <p:column  sortBy="#{prodsal.salInvcNumber}"    headerText="Invoice Number"  >
                        <h:outputText value="#{prodsal.salInvcNumber}" />



                    </p:column>
                    <p:column class="col_right" sortBy="#{prodsal.salDate}"    headerText="Date"  >
                        <h:outputText value="#{prodsal.salDate}" />



                    </p:column>


                    <p:column class="col_right" sortBy="#{prodsal.salTotalPrice}"    headerText="Total Price"    >
                        <h:outputText value="#{prodsal.salTotalPrice}"  style="float:right" />



                    </p:column>



                    <p:column class="col_right" sortBy="#{prodsal.salUnitPrice}"    headerText="Unit Price" >
                        <h:outputText value="#{prodsal.salUnitPrice}"  style="float:right">
                            <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                            <f:convertNumber minFractionDigits="2"/>
                            <f:convertNumber maxFractionDigits="6" />
                        </h:outputText>



                    </p:column>

                    <p:column sortBy="#{prodsal.salQuantity}"    headerText="Quantity" >
                        <h:outputText value="#{prodsal.salQuantity}" style="float:right " />
                    </p:column>

                    <!--  4-12-2020 :TASK#2609 -CRM-3451: MOBILE & ONLINE need Part Description visible (not just part#) -->
                    <p:column sortBy="#{prodsal.prodDesc}"    headerText="Product Description" >
                        <h:outputText value="#{prodsal.prodDesc}" />
                    </p:column>

                </p:dataTable>

                <p:dataTable   value="#{productSalesHistory.listProductSalesHistorys}" var="prodsal" id="prodSalHistoryPdf"   rendered="false" >

                    <p:columnGroup  type="header" class="header"   >
                        <p:row>

                            <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                            <p:column  />
                            <p:column  />
                            <p:column  headerText="Product Sales History"/>
                            <p:column      />
                            <p:column      />
                            <p:column      headerText="January #{reports.repYear} - #{reportFilters.getMonth(reports.repMonth )} #{reports.repYear}"  />
                            <p:column    />
                            <p:column      />
                            <p:column   />
                            <p:column     />         



                        </p:row>


                        <p:row>
                            <p:column    />
                            <p:column      />
                            <p:column   />
                            <p:column     />  
                            <p:column     /> 
                            <p:column  headerText="
                                       #{productSalesHistory.repPartNo1 eq '' ?  '' 
                                         : (productSalesHistory.repPartNo1 eq productSalesHistory.repPartNo1 ? ' Part No  :': '')}
#{ productSalesHistory.repPartNo1 eq '' ? '' 
                                          : (productSalesHistory.repPartNo1 eq productSalesHistory.repPartNo1 ? productSalesHistory.repPartNo1: '')}
                             #{ productSalesHistory.repPartNo1 eq '' ? '' 
                                          : (productSalesHistory.repPartNo1 eq productSalesHistory.repPartNo1 ? '...':'')}"  />
                            <p:column    />
                            <p:column      />
                            <p:column   />
                            <p:column     />     
                            <p:column     />    
                            <!--  4-12-2020 :TASK#2609 -CRM-3451: MOBILE & ONLINE need Part Description visible (not just part#) -->
                            <p:column     />  
                            <p:column     />  
                        </p:row>
                        <p:row></p:row>

                        <p:row>


                            <p:column   headerText="#{custom.labels.get('IDS_PRINCI')}" style="text-height: max-size"/>
                            <p:column  headerText="#{custom.labels.get('IDS_DISTRI')}"/>
                            <!-- Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:column  headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                            <p:column  headerText="#{custom.labels.get('IDS_SALES_TEAM')}"/>
                            <p:column   headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}"   />
                            <p:column     headerText="#{custom.labels.get('IDS_PART_NUM')}"   />
                            <p:column      headerText="Invoice Number"  />
                            <p:column    headerText="Date"  />
                            <p:column    headerText="Total Price"    />
                            <p:column    headerText="Unit Price" />
                            <p:column    headerText="Quantity" />
                            <!--  4-12-2020 :TASK#2609 -CRM-3451: MOBILE & ONLINE need Part Description visible (not just part#) -->
                            <p:column    headerText="Product Description" />
                        </p:row>


                    </p:columnGroup>  
                    <p:column>

                        <h:outputText value="#{prodsal.repPrincname}" />



                    </p:column>
                    <p:column    >
                        <h:outputText value="#{prodsal.repDistriName}" />



                    </p:column>
                    <p:column       >
                        <h:outputText value="#{prodsal.repCustName}" />



                    </p:column>
                    <p:column>
                        <h:outputText value="#{prodsal.repSmanName}" />



                    </p:column>
                    <!--#446311 , add secondary customer-->
                    <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                    <p:column    >

                        <h:outputText value="#{prodsal.repSecCust}" />


                    </p:column>      


                    <p:column  >
                        <h:outputText value="#{prodsal.repPartNo1}" />



                    </p:column>

                    <p:column   >
                        <h:outputText value="#{prodsal.salInvcNumber}" />



                    </p:column>
                    <p:column   >
                        <h:outputText value="#{prodsal.salDate}" />



                    </p:column>


                    <p:column    >
                        <h:outputText value="#{prodsal.salTotalPrice}" />



                    </p:column>



                    <p:column >
                        <h:outputText value="#{prodsal.salUnitPrice}">
                            <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                            <f:convertNumber minFractionDigits="2"/>
                            <f:convertNumber maxFractionDigits="6" />
                        </h:outputText>



                    </p:column>

                    <p:column  >
                        <h:outputText value="#{prodsal.salQuantity}" />
                    </p:column>

                    <!--  4-12-2020 :TASK#2609 -CRM-3451: MOBILE & ONLINE need Part Description visible (not just part#) -->
                    <p:column  >
                        <h:outputText value="#{prodsal.prodDesc}" />
                    </p:column>


                </p:dataTable>



            </h:form>

            <!--#3399 :Task Sales Reports > Product Sales History > Add export option  start-->
            <p:dialog widgetVar="inProdSalProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Please wait...Exporting records" />
                        <br /><br />

                    </p:outputPanel>
                </h:form>
            </p:dialog>

            <!--#3399 :Task Sales Reports > Product Sales History > Add export option end-->

            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
            <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
            <ui:include src="PartDetails.xhtml"/>
        </div>

        <style>
              /*<!--bug #8649  ESCALATIONS: CRM-5999  error message when trying to run Product Sales History Report-->*/
              
                @media only screen and (min-height : 600px) and (max-height : 900px)  {

                    .ui-datatable-scrollable-body{
                        height: 65vh; 
                        outline: 0px;
                    }



                }       

                @media only screen and (min-height : 901px) and (max-height : 1152px)  {

                    .ui-datatable-scrollable-body{
                        height: 70vh; 
                        outline: 0px;
                    }


                }
                /*1920 * 1080*/
                @media only screen and (min-height : 1153px) and (max-height : 1440px)  {

                    .ui-datatable-scrollable-body{
                        /*task :4250*/
                        height: 45vh; 
                        outline: 0px;
                    }


                }

                @media only screen and (min-height : 1500px) and (max-height : 1640px)  {

                    .ui-datatable-scrollable-body{
                        /*task :4250*/
                        height:25vh; 
                        outline: 0px;
                    }


                } 
                .ui-datatable-scrollable-body{
                    outline: 0px;
                }
                
            .ui-datatable-scrollable-header *,
            .ui-datatable-scrollable-theadclone * {
                -moz-box-sizing: content-box;
                -webkit-box-sizing: content-box;
                box-sizing: content-box;
                width: 100%;
            }
            body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
                width: 15px;
                    
            }

            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }
            .might-overflow {
                text-overflow: ellipsis;
                overflow : hidden;
                white-space: nowrap;
            }

            .might-overflow:hover {
                text-overflow: clip;
                white-space: normal;
                word-break: break-all;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }


            .jqplot-target {

                font-size: 0.75em!important;
            }
        </style>  

        <!--#3153 Sales Reports - Add gif for loading-->
        <script>
            window.onload = function () {
//                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("prodsalhisrpt").style.pointerEvents = "all";
                    document.getElementById("prodsalhisrpt").style.opacity = "1";
                    $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };
            function processGif()
            {
                $('#loading-image').show();
                document.getElementById("prodsalhisrpt").style.pointerEvents = "none";
                document.getElementById("prodsalhisrpt").style.opacity = "0.4";
            }

            function hideGif()
            {
                $('#loading-image').hide();
                document.getElementById("prodsalhisrpt").style.pointerEvents = "all";
                document.getElementById("prodsalhisrpt").style.opacity = "1";
            }


            function start() {

                PF('inProdSalProgressDlg').show();
            }

            function stop() {
                PF('inProdSalProgressDlg').hide();
            }



        </script>
    </ui:define>


</ui:composition>