<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: salesAnalysissummary.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                xmlns:pe="http://primefaces.org/ui/extensions"
                template="#{layoutMB.template}">
    <!--#363 :page title-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>  Sales Analytical Summary</title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>
            <!--//Task #5796 devincahn: enhancements to the  sales analytical summary report-->
            <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
            <f:viewParam name="yr" value="#{reportFilters.repYear}" />
            <f:viewParam name="mt" value="#{reportFilters.repMonth}" />
            <f:viewParam name="c" value="#{reportFilters.customer.compId}" />
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}" />
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}" />
            <f:viewParam name="g" value="#{reportFilters.grp}" />
            <f:viewParam name="prt" value="#{reportFilters.parentComp.compId}" />
            <f:viewParam name="sF" value="#{reportFilters.summaryFlag}"/>
            <f:viewParam name="fdate" value="#{salesComparisonExtRep.fromDate}"/>
            <f:viewParam name="tdate" value="#{salesComparisonExtRep.toDate}"/>
            <f:viewParam name="af" value="#{reportFilters.asOf}"/>
            <f:viewParam name="f" value="#{globalParams.dateFormat}"/>
            <f:viewParam name="pro" value="#{reportFilters.processBy}"/>


            <!--<f:viewAction action="#{reportFilters.assignSalesTeams()}"/>-->
            <!--//#8500 CRM-5877: Aurora: Reports: add Region + prior year total-->
       <!--<f:viewAction action="#{reportFilters.assignCompanyRegions()}"/>-->


            <f:viewAction action="#{salesComparisonExtRep.run()}"/>

        </f:metadata>
    </ui:define>
    <ui:define name="body">

        <f:event listener="#{salesComparisonExtRep.isPageAccessible}" type="preRenderView"/>  

        <p:dialog header="Please wait" draggable="false"  widgetVar="dlgSalComCompaj" modal="true" closable="false" resizable="false" >
            <h:graphicImage  library="images" name="ajax-loader.gif" />
            <p:spacer width="5"/>
            <h:outputLabel value="Loading data.."/>
        </p:dialog>
        <!--#3153 Sales Reports - Add gif for loading-->
        .
        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />
        <!--#3153 Sales Reports - Add gif for loading style changes-->
        <p:growl id="salValidator"  />

        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7" id="salComExtendedrpt" >

            <h:form id="salescommcomp" >

                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="salescommcomp:inputCustomer  "/>

                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="salescommcomp:inputPrincName "  />



                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >

                        <!--1717 :CRM-3174: Sales Analytical Report -  Label update-->

                        <div class="compNamebg" style="float:left;width:670px">
                            <h:outputLabel value="  #{loginBean.subscriber_name} " />
                            <!--                            <br/>
                                                        Sales Analytical Summary-->
                            <br/>
                            <!--1704-->
                            <!--1717 :CRM-3174: Sales Analytical Report -  Label update-->
                            <h:outputLabel value=" #{reportFilters.parentComp.compId >0 ?'Sales Analytical Summary (by Parent Company)':'Sales Analytical Summary (by Sales Team)'}" style="width:450px" />
                        </div>
                        <!--                        // BUg #6447 CRM-5027: Sales Analytical Summary Date Range Issue-->
                        <!--//#8500 CRM-5877: Aurora: Reports: add Region + prior year total-->
                        <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                        <div  style="float:left;width:600px" >
                            &nbsp;&nbsp; <p:outputLabel rendered="#{reportFilters.asOf eq 1}" value="As of:" /> &nbsp;  <p:outputLabel rendered="#{reportFilters.asOf eq 1}" value="#{reportFilters.repYear}" id="year"/> 
                            <p:outputLabel rendered="#{reportFilters.asOf eq 2}" value="As of:" /> &nbsp;  <p:outputLabel rendered="#{reportFilters.asOf eq 2}" value="#{salesComparisonExtRep.repFrom}" id="fdate"/> &nbsp; 

                        </div>

                        <!--//#8500 CRM-5877: Aurora: Reports: add Region + prior year total-->
                        <div  style="float:left;width:600px;margin-top:15px" >
                            <p:outputLabel style="font-weight: normal" rendered="#{reportFilters.compReglst.size() > 0}" value="*Filtered for selected #{custom.labels.get('IDS_REGION')}" />  
                        </div>

                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                        <p:panelGrid id="grid1" columns="1" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;width:100%;margin-left: 150px"       layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <!--1704-->
                            <p:outputLabel value="Group by"/>

                            <p:selectOneRadio value="#{reportFilters.grp}"  widgetVar="grp17" layout="pageDirection"  required="true" disabled="#{reportFilters.parentComp.compId >0}">
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/>  
                                <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                <f:selectItem   itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/> 
                                <!--<p:ajax  onstart="PF('dlgSalComCompaj').show();"   listener="#{salesComparisonExtRep.run()}" update=":salescommcomp:tbldata1" oncomplete="PF('dlgSalComCompaj').hide()" />-->
                            </p:selectOneRadio>
                        </p:panelGrid>
                    </div>  
                    <div class="ui-sm-12 ui-md-1 ui-lg-2" >
                        <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                        <!--//#8500 CRM-5877: Aurora: Reports: add Region + prior year total-->
                        <p:panelGrid rendered="#{reportFilters.asOf eq 1}" id="grid2" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;width:100%;margin-left: 50px"       layout="grid" styleClass="box-primary no-border ui-fluid "  >
                            <p:outputLabel  value="Year"   />
                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" >
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />
                                <!--<p:ajax listener="#{salesComparisonExtRep.run()}"  onstart="PF('dlgSalComCompaj').show();"   update=":salescommcomp:tbldata1 :salescommcomp:year"   oncomplete="PF('dlgSalComCompaj').hide()"/>-->
                            </p:selectOneMenu>   

                            <p:outputLabel  value="Month"   />
                            <p:selectOneMenu height="280"   widgetVar="mt" value="#{reportFilters.repMonth}"  >
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 
                                <!--<p:ajax listener="#{salesComparisonExtRep.run()}" onstart="PF('dlgSalComCompaj').show();"  update=":salescommcomp:tbldata1"  oncomplete="PF('dlgSalComCompaj').hide()"/>-->
                            </p:selectOneMenu>





                        </p:panelGrid>
                        <!--//Task #5796 devincahn: enhancements to the  sales analytical summary report-->
                        <p:panelGrid rendered="#{reportFilters.asOf eq 2}"  id="grid3" columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7"
                                     style="min-height: 20px;margin-left:10px;width:120%"       layout="grid" styleClass="box-primary no-border ui-fluid ">
                            <p:outputLabel  value="From"/>
                            <h:panelGroup>

                                <p:calendar value="#{reportFilters.fdate}" showOn="button" pattern="#{globalParams.dateFormat}" id="fmdt" converterMessage="Please enter the valid From date">

                                    <p:ajax  event="keyup"  process="@this" />
                                    <p:ajax event="dateSelect" process="@this"  />
                                </p:calendar>
                            </h:panelGroup>

                            <p:outputLabel  value="To"/>
                            <h:panelGroup >

                                <p:calendar value="#{reportFilters.tdate}" showOn="button" pattern="#{globalParams.dateFormat}" id="todt" widgetVar="todate" converterMessage="Please enter the valid To date">
                                    <p:ajax  event="keyup"  process="@this" />
                                    <p:ajax event="dateSelect" process="@this"  />
                                </p:calendar>
                            </h:panelGroup>
                        </p:panelGrid>
                    </div>
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:10px">
                        <p:panelGrid id="grid" columns="2" 
                                     style="float: right;width: 350px;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <!--//Task #5796 devincahn: enhancements to the  sales analytical summary report-->
                            <p:outputLabel  value="#{custom.labels.get('IDS_SALES_TEAM')}"  />

                            <h:panelGroup class="ui-inputgroup" style="width: 130%" >
                                <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width:200%" id="selSalesTeam" >
                                    <f:selectItems value="#{reportFilters.salesTeams}" var="team"  
                                                   itemValue="#{team.smanId}"
                                                   itemLabel="#{team.smanName}"  />
                                </h:selectOneListbox>
                                <!--//  1741  CRM-3259: Sales team:Thea: prevent users from seeing Sales and Comms -  Dashboard Sales Team Lookup-->

                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.crmFlag()}"   action="#{lookupService.list('SALES_TEAM')}"   update=":frmSalesTeamLookup"  style="width:70px" oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                &nbsp;&nbsp;&nbsp;
                                <p:commandLink styleClass="sel-salesteam" value="Clear" actionListener="#{lookupService.clear('SALES_TEAM')}" update="@(.sel-salesteam)" disabled="#{!(reportFilters.salesTeams!=null and reportFilters.salesTeams.size() != 0)}" style="text-decoration:underline" />
                            </h:panelGroup>



                            <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <!--#160 : Yearr filter updates-->
                            <p:commandButton  value="View Report "  id="viewreport" onclick="processGif();"  styleClass="btn btn-primary btn-xs" actionListener="#{reportFilters.setSalesteamlst()}" action="#{salesComparisonExtRep.run( )}"  style="width:30px" update=":salescommcomp   :salescommcomp:tbldata1  :salescommcomp:year  salValidator"  oncomplete="hideGif();" />
                            <!--3391 export-->
<!--//     #12208 CRM-7384	Reports: Sales Analytical: RKR: Formatting of the Excel version-->
                            <p:menuButton  id="btnExport" value="Export"    >
                                <p:menuitem id="btnUnformatted" value="Unformatted" ajax="false" actionListener="#{salesComparisonExtRep.exportData(true)}"/>
                                <p:menuitem id="btnFormatted"  value="Formatted" ajax="false" actionListener="#{salesComparisonExtRep.exportData(false)}"/>
                            </p:menuButton>
<!--                            <p:commandButton value="Export"   style="width:30px"  
                                             ajax="false"    
                                             styleClass="btn btn-primary btn-xs" onclick="PrimeFaces.monitorDownload(start, stop);" 
                                             />-->


                        </p:panelGrid>


                    </div>
                </div>     





                <!--//Task #5796 devincahn: enhancements to the  sales analytical summary report-->
                <h:panelGroup id="tbldata1">

                    <ui:repeat  value="#{salesComparisonExtRep.listSalAnalysisRep}" var="salcomlist"  id="sid">

                        <div   style="height:28px;width: 100%;background-color:#000;vertical-align:central" >
                            <label style="font-size: 1.3em;font-weight: bold;color:white;line-height: 25px;padding-left: 5px;">
                                <p:outputLabel  value="For: "/>&nbsp;&nbsp;<p:outputLabel  value="#{salcomlist.repSmanName}"/>
                            </label>
                        </div>
                        <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                        <p:dataList  rendered="#{not reportFilters.summaryFlag}" type="definition" id="dtrpt1" value="#{salcomlist.listSalAnalysisSalTmList}" var="salComComp" >

                            <p:column >
                                <div class="div-selected" style="height:25px;width: 100%;" >
                                    <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                                        #{salComComp.itemName}   

                                    </label>               
                                </div>
                                <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                                <p:dataTable  rendered="#{not reportFilters.summaryFlag}" id="dtrpt"  value="#{salComComp.details}" var="salesCompr" paginatorAlwaysVisible="false" paginator="true"
                                              paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                              rows="50" emptyMessage="No data found" >
                                    <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                                    <!--1455 :CRM-3090: mwpt:  can't sort report-->
                                    <p:column   class="col_left"   headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}"  rendered="#{reportFilters.grp == 1}" sortBy="#{salesCompr.repCustName}"  >
                                        <p:outputLabel value="#{salesCompr.repCustName}"  />
                                        <f:facet  name="footer">
                                            <h:outputLabel   value="Total : "  style="float: right"  class="footerFont"/>
                                        </f:facet>
                                    </p:column>
                                    <p:column   class="col_left"    headerText="#{custom.labels.get('IDS_PRINCI')}" rendered="#{reportFilters.grp == 2}" sortBy="#{salesCompr.repPrinciName}" >
                                        <p:outputLabel value="#{salesCompr.repPrinciName}" />
                                        <f:facet  name="footer">
                                            <h:outputLabel   value="Total : "  style="float: right"  class="footerFont"/>
                                        </f:facet>
                                    </p:column>
                                    <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                                    <p:column headerText="#{reportFilters.asOf eq 2 ? salesComparisonExtRep.repFrom : salesComparisonExtRep.monthCurr}" style="text-align: right" sortBy="#{salesCompr.repMonthCurr}">

                                        <h:outputLabel value="#{salesCompr.repMonthCurr}"     >
                                            <f:convertNumber maxFractionDigits="0"/>
                                        </h:outputLabel>

                                        <f:facet name="footer">
                                            <h:outputLabel 
                                                value="#{salComComp.totals[2]}"  style="float: right"  class="footerFont">
                                                <f:convertNumber   maxFractionDigits="0"/>
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>

                                    <!--1455 :CRM-3090: mwpt:  can't sort report-->
                                    <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                                    <p:column headerText="#{reportFilters.asOf eq 2 ? salesComparisonExtRep.repDPrev : salesComparisonExtRep.monthPrev}" style="text-align: right" sortBy="#{salesCompr.repMonthPrev}">

                                        <h:outputLabel value="#{salesCompr.repMonthPrev}"  >
                                            <f:convertNumber maxFractionDigits="0"/>
                                        </h:outputLabel>

                                        <f:facet name="footer">
                                            <h:outputLabel 
                                                value="#{salComComp.totals[1]}"  style="float: right"  class="footerFont">
                                                <f:convertNumber maxFractionDigits="0"/>
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>

                                    <p:column headerText="#{salesComparisonExtRep.yrCurr}"  style="text-align: right" sortBy="#{salesCompr.repYtdCurr}" >
                                        <h:outputLabel value="#{salesCompr.repYtdCurr}"  >
                                            <f:convertNumber maxFractionDigits="0"/>
                                        </h:outputLabel>

                                        <f:facet name="footer">
                                            <h:outputLabel 
                                                value="#{salComComp.totals[3]}"  style="float: right"  class="footerFont" >
                                                <f:convertNumber maxFractionDigits="0"/>
                                            </h:outputLabel>

                                        </f:facet>
                                    </p:column>

                                    <p:column headerText="#{salesComparisonExtRep.yrPrev}"  style="text-align: right" sortBy="#{salesCompr.repYtdPrev}">
                                        <h:outputLabel value="#{salesCompr.repYtdPrev}"  >
                                            <f:convertNumber maxFractionDigits="0"/>
                                        </h:outputLabel>

                                        <f:facet name="footer">
                                            <h:outputLabel 
                                                value="#{salComComp.totals[4]}"  style="float: right"  class="footerFont">
                                                <f:convertNumber maxFractionDigits="0"/>
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>






                                    <p:column headerText="Jan-Dec #{salesComparisonExtRep.year - 1}"  style="text-align: right" sortBy="#{salesCompr.repYearPrev}">
                                        <h:outputLabel value="#{salesCompr.repYearPrev}"  >
                                            <f:convertNumber maxFractionDigits="0"/>
                                        </h:outputLabel>

                                        <f:facet name="footer">
                                            <h:outputLabel 
                                                value="#{salComComp.totals[5]}"  style="float: right"  class="footerFont">
                                                <f:convertNumber maxFractionDigits="0"/>            
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>


                                    <p:column headerText="YTD Var $"  style="text-align: right" sortBy="#{salesCompr.repYtdVarAmt}">
                                        <h:outputLabel value="#{salesCompr.repYtdVarAmt}"  >
                                            <f:convertNumber maxFractionDigits=""/>
                                        </h:outputLabel>

                                        <f:facet name="footer">
                                            <h:outputLabel 
                                                value="#{salComComp.totals[6]}"  style="float: right"  class="footerFont" >
                                                <f:convertNumber maxFractionDigits="0"/>
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>

                                    <!--#9156 CRM-6134  Dellon: Sales Analytical report: color code variance and pdf export-->
                                    <p:column headerText="Var %"  style="text-align: right" sortBy="#{salesCompr.repYtdVarPct}">
                                        <h:outputLabel value="#{salesCompr.repYtdVarPct}" style="#{
                                                                salesCompr.repYtdVarPct gt 0 ?  'color:green' 
                                                                : (salesCompr.repYtdVarPct lt 0 ? 'color:red' : 'color:black')
                               }" >
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>
                                        <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->
                                        <f:facet name="footer">
                                            <h:outputLabel  style="#{salesComparisonExtRep.calculateTotalPct(salComComp.totals[6], salComComp.totals[4]) gt 0 ?  'color:green' 
                                                                     : (salesComparisonExtRep.calculateTotalPct(salComComp.totals[6], salComComp.totals[4]) lt 0 ? 'color:red' : 'color:black')}"

                                                            value="#{salesComparisonExtRep.calculateTotalPct(salComComp.totals[6], salComComp.totals[4])}"   class="footerFont">
                                                <f:convertNumber maxFractionDigits="0"/>
                                            </h:outputLabel>
                                        </f:facet>



                                    </p:column>


                                    <p:column headerText="YTD %"  style="text-align: right" sortBy="#{salesCompr.repYtdPct}">
                                        <h:outputLabel value="#{salesCompr.repYtdPct}"  >
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>
                                        <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->
                                        <!--                                        <f:facet name="footer">
                                                                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                                                                   value="#{salComComp.totals[7]}"  style="float: right"  class="footerFont">
                                                                                        <f:convertNumber maxFractionDigits="2"/>
                                                                                    </h:outputLabel>
                                                                                </f:facet>-->
                                    </p:column>


                                    <p:column headerText="LYT %"  style="text-align: right" sortBy="#{salesCompr.repLytPct}">
                                        <h:outputLabel value="#{salesCompr.repLytPct}"  >
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>
                                        <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->
                                        <!--                                        <f:facet name="footer">
                                                                                    <h:outputLabel 
                                                                                        value="#{salComComp.totals[8]}"  style="float: right"  class="footerFont">
                                                                                        <f:convertNumber maxFractionDigits="2"/>
                                                                                    </h:outputLabel>
                                                                                </f:facet>-->
                                    </p:column>        



                                </p:dataTable>


                            </p:column>

                        </p:dataList>
                        <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                        <!--//Task #5796 devincahn: enhancements to the  sales analytical summary report-->
                        <h:panelGroup  rendered="#{not reportFilters.summaryFlag}" >
                            <div    style="height:28px;width: 100%;background-color: #666666;vertical-align:central" >
                                <label  style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                                    <p:outputLabel  value="Recap For:"/>&nbsp;&nbsp;<p:outputLabel  value="#{salcomlist.repSmanName}"/>
                                </label>
                            </div>
                        </h:panelGroup>

                        <p:dataTable value="#{salcomlist.salAnalysisDet}" var="salComSmry">
                            <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                            <!--                            Bug #8010 CRM-5700   sales analytical summary: aroura: sorting seems random-->
                            <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                            <p:column   class="col_left"   headerText="#{(reportFilters.grp == 1  and reportFilters.summaryFlag) ? custom.labels.get('IDS_PRINCI') : (reportFilters.grp == 2  and reportFilters.summaryFlag) or (!reportFilters.summaryFlag and reportFilters.grp == 1 ) ? custom.labels.get('IDS_DB_CUSTOMER') : custom.labels.get('IDS_PRINCI') }"  sortBy="#{salComSmry.itemName}" >
                                <h:outputLabel value="#{salComSmry.itemName}"  />    
                                <f:facet  name="footer">
                                    <h:outputLabel   value="Total : "  style="float: right"  class="footerFont"/>
                                </f:facet>
                            </p:column>



                            <!--1455 :CRM-3090: mwpt:  can't sort report-->
                            <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                            <p:column headerText="#{reportFilters.asOf eq  2 ? salesComparisonExtRep.repFrom : salesComparisonExtRep.monthCurr}" style="text-align: right" sortBy="#{salComSmry.repMonthCurr}">

                                <h:outputLabel value="#{salComSmry.repMonthCurr}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel 
                                        value="#{salcomlist.totMonthCurr}"  style="float: right"  class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column> 


                            <!--1455 :CRM-3090: mwpt:  can't sort report-->
                            <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                            <p:column headerText="#{reportFilters.asOf eq 2 ? salesComparisonExtRep.repDPrev : salesComparisonExtRep.monthPrev}" style="text-align: right" sortBy="#{salComSmry.repMonthPrev}">

                                <h:outputLabel value="#{salComSmry.repMonthPrev}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel 
                                        value="#{salcomlist.totMonthPrev}"  style="float: right"  class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>


                            <!--1455 :CRM-3090: mwpt:  can't sort report-->

                            <p:column headerText="#{salesComparisonExtRep.yrCurr}"  style="text-align: right" sortBy="#{salComSmry.repYtdCurr}" >
                                <h:outputLabel value="#{salComSmry.repYtdCurr}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel 
                                        value="#{salcomlist.totYtdCurr}"  style="float: right"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>

                                </f:facet>
                            </p:column>



                            <!--1455 :CRM-3090: mwpt:  can't sort report-->


                            <p:column headerText="#{salesComparisonExtRep.yrPrev}"  style="text-align: right" sortBy="#{salComSmry.repYtdPrev}">
                                <h:outputLabel value="#{salComSmry.repYtdPrev}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel 
                                        value="#{salcomlist.totYtdPrev}"  style="float: right"  class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>




                            <!--1455 :CRM-3090: mwpt:  can't sort report-->


                            <p:column headerText="Jan-Dec #{salesComparisonExtRep.year - 1}"  style="text-align: right" sortBy="#{salComSmry.repYearPrev}">
                                <h:outputLabel value="#{salComSmry.repYearPrev}"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel 
                                        value="#{salcomlist.totYearPrev}"  style="float: right"  class="footerFont">
                                        <f:convertNumber maxFractionDigits="0"/>            
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <!--1455 :CRM-3090: mwpt:  can't sort report-->
                            <p:column headerText="YTD Var $"  style="text-align: right" sortBy="#{salComSmry.repYtdVarAmt}">
                                <h:outputLabel value="#{salComSmry.repYtdVarAmt}"  >
                                    <f:convertNumber maxFractionDigits=""/>
                                </h:outputLabel>

                                <f:facet name="footer">
                                    <h:outputLabel
                                        value="#{salcomlist.totYtdVarAmt}"  style="float: right"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>
                            <!--#9156 CRM-6134  Dellon: Sales Analytical report: color code variance and pdf export-->
                            <!--1455 :CRM-3090: mwpt:  can't sort report-->
                            <p:column headerText="Var %"  style="text-align: right" sortBy="#{salComSmry.repYtdVarPct}">
                                <h:outputLabel value="#{salComSmry.repYtdVarPct}" style="#{
                                                        salComSmry.repYtdVarPct gt 0 ?  'color:green' 
                                                        : (salComSmry.repYtdVarPct lt 0 ? 'color:red' : 'color:black')
                               }" > 
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>

                                <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->
                                <f:facet name="footer">
                                    <h:outputLabel style="#{salesComparisonExtRep.calculateTotalPct(salcomlist.totYtdVarAmt, salcomlist.totYtdPrev) gt 0 ?  'color:green' 
                                                            : (salesComparisonExtRep.calculateTotalPct(salcomlist.totYtdVarAmt, salcomlist.totYtdPrev) lt 0 ? 'color:red' : 'color:black')}"
                                                   value="#{salesComparisonExtRep.calculateTotalPct(salcomlist.totYtdVarAmt, salcomlist.totYtdPrev)}"  >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>

                            <!--1455 :CRM-3090: mwpt:  can't sort report-->
                            <p:column headerText="YTD %"  style="text-align: right" sortBy="#{salComSmry.repYtdPct}">
                                <h:outputLabel value="#{salComSmry.repYtdPct}"  >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->
                                <!--                                <f:facet name="footer">
                                                                    <h:outputLabel rendered="#{salesReports.repPrinci eq 0}" 
                                                                                   value="#{salesComparisonExtRep.calculateTotalPct(salcomlist.repYtdCurr, val2)}"  style="float: right"  class="footerFont">
                                                                        <f:convertNumber maxFractionDigits="2"/>
                                                                    </h:outputLabel>
                                                                </f:facet>-->
                            </p:column>

                            <!--1455 :CRM-3090: mwpt:  can't sort report-->
                            <p:column headerText="LYT %"  style="text-align: right" sortBy="#{salComSmry.repLytPct}">
                                <h:outputLabel value="#{salComSmry.repLytPct}"  >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <!--#12157  CRM-7383   sales analytical: rkr report has bad summary % calcs-->
                                <!--                                <f:facet name="footer">
                                                                    <h:outputLabel 
                                                                        value="#{salcomlist.totLytPct}"  style="float: right"  class="footerFont">
                                                                        <f:convertNumber maxFractionDigits="2"/>
                                                                    </h:outputLabel>
                                                                </f:facet>-->
                            </p:column>        



                        </p:dataTable>

                    </ui:repeat>

                </h:panelGroup>
<!--//     #12208 CRM-7384	Reports: Sales Analytical: RKR: Formatting of the Excel version-->
                <style>
               .ui-button.ui-widget.ui-state-default.ui-corner-all.ui-button-text-icon-left {
                    height: 25px !important; font-size: 13px; background-color: #3c8dbc ! important;color: #fff !important

                }
                     #salescommcomp\:btnExport_button {
                    width:100px !important; height:23px !important; font-size: 12px !important; margin-top: -5px !important;
                }

                    .ui-panelgrid .ui-panelgrid-cell {
                        padding-top: 1px !important;
                        padding-bottom: 1px !important;
                    }

                    [role="gridcell"]{

                        padding: 0px 10px !important;
                    }
                    .content-header{     display: none;
                    }


                    .jqplot-target {

                        font-size: 0.75em!important;
                    }
                </style>

                <!--#3153 Sales Reports - Add gif for loading style changes-->     
                <script>
                    window.onload = function () {
                        //                $('#loading-image').show();
                        setTimeout(function () {
                            var t = performance.timing;
                            console.log(t.loadEventEnd - t.responseEnd);
                            //      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                            document.getElementById("salComExtendedrpt").style.pointerEvents = "all";
                            document.getElementById("salComExtendedrpt").style.opacity = "1";

                            $('#loading-image').hide();
                            //            $('#prodrpt').css({pointer - events:"all"});
                        }, 0);
                    };

                    function processGif()
                    {
                        $('#loading-image').show();
                        document.getElementById("salComExtendedrpt").style.pointerEvents = "none";
                        document.getElementById("salComExtendedrpt").style.opacity = "0.7";
                    }

                    function hideGif()
                    {
                        $('#loading-image').hide();
                        document.getElementById("salComExtendedrpt").style.pointerEvents = "all";
                        document.getElementById("salComExtendedrpt").style.opacity = "1";
                    }

                </script>


            </h:form>
            <!--//Task #5796 devincahn: enhancements to the  sales analytical summary report-->
            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />

            <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>

        </div>
    </ui:define>
</ui:composition>


