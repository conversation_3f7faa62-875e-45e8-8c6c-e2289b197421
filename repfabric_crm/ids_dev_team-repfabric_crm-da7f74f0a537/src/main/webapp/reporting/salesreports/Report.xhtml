<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: Leftreports.xhtml
// Author: Priyadarshini
// Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved.
//********************************************************************* -->
<!--Task #6: Settings: Subtables -->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">
    <!--#363:page title.-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>  #{leftReports.selectedSubMenu.label}  </title>
    </ui:define>

    <ui:define name="metadata">
        <f:metadata>
            <f:viewParam name="r" value="#{leftReports.id}"/>
           <!--// #10203 :CRM-6433-->  
            <!--<f:viewAction action="#{salesReports.resetFilters()}"/>-->
            <f:viewAction action="#{leftReports.init(leftReports.id)}"/>
            <!--#2521:.   Related - CRM-1521: Tutorial button landing urls - Sales Reports, Funnel Report-->

            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('SALES_RPT'))}" />
            <!--29-09-2023  12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('SALES_RPT')}" />

          
            <!--#pms:160 Product details year and month issue-->
                                         <!--4775: Sales and Commission Reports: Remember date range for session-->
            <!--<f:viewAction action="#{salesReports.reSetDateMonth()}"/>-->

        </f:metadata>
        <!--         <script>
          (function() {
          var script = document.createElement('script');
          script.type = 'text/javascript';
          script.src = 'https://code.jquery.com/jquery-3.1.1.slim.min.js';  
          script.setAttribute('integrity','sha256-/SIrNqv8h6QGKDuNoLGA4iret+kyesCkHGzVUUV0shc=');
          script.setAttribute('crossorigin','anonymous');
          document.getElementsByTagName('head')[0].appendChild(script);
          })();
        </script>   -->
    </ui:define>
    <ui:define name="title">Sales-Reports
        <ui:include src="/help/Help.xhtml"/>
    </ui:define>
    <!--        <ui:define name="menu">
        
            </ui:define>-->


    <!--    <ui:define name="title" >
            <ui:param name="title" value="Customer Summary"/>
            <f:event listener="#{leftReports.isPageAccessible}" type="preRenderView"/>
    
        </ui:define>-->
    <ui:define name="body">

        <div class="box box-info box-body">        
            <div class="left-column">
                <h:form>
                    <p:dataTable value="#{leftReports.salesRepMenuList}"  var="sm" selectionMode="single" id="dtSalesMenuLst" selection="#{leftReports.selectedSubMenu}" class="hide-column-names"
                                 rowKey="#{sm.id}" >

<!--listener="#{reports.resetFilters()}"-->
                        <p:ajax event="rowSelect" update=":salesReport" listener="#{leftReports.redirectPage()}"  />
                        <p:column >
                            <h:outputText value="#{sm.label}"/>
                        </p:column>

                        <p:colorPicker id="colorPick" value="" widgetVar="color"/>  
                        <p:editor id="editor" widgetVar="editorWidget"  width="600" />
                        <p:fileUpload  mode="advanced" dragDropSupport="false"
                                       sizeLimit="100000" fileLimit="3" allowTypes="/(\.|\/)(gif|jpe?g|png)$/" />
                    </p:dataTable>
                </h:form>
            </div>
            <div class="right-column">

                <p:outputPanel id="salesReport">
                    <ui:include src="#{leftReports.selectedSubMenu.pageUrl}"  />

                </p:outputPanel>



            </div>



        </div>
    </ui:define>
</ui:composition>