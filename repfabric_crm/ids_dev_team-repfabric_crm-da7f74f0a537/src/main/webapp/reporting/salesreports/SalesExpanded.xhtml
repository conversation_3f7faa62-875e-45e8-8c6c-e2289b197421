<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: SalesExpanded.xhtml
// Author: priyadarshini
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 


    <h:form id="salesExpandedForm">
        <!--// #10203 :CRM-6433-->
        <p:remoteCommand autoRun="true"   update=":salesExpandedForm:grid"/>

        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;" id="cusSumFilter">
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
            <h4 style="margin-top:0px;margin-left: 18px;">  Sales by Month : Report Filter</h4>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->
            <p:outputPanel rendered="#{leftReports.countMsgSalesReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>  
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border"  style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">
                        <div id="loading"  class="div-center" style="display:none;">
                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />
                        </div>  

                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7"
                                     style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel  value="Sales Year"   />
                            <!--// #10203 :CRM-6433-->
                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" style="width: 100%;"  >
                                 <p:ajax     event="change"   process="@this" />
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />

                            </p:selectOneMenu>

                            <p:outputLabel  value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                               <!--// #10203 :CRM-6433-->
                            <h:panelGroup class="ui-inputgroup" style="width:110%" >
                                <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width: 100%;" id="selSalesTeam" >
                                    <f:selectItems value="#{reportFilters.salesTeams}" var="team"  
                                                   itemValue="#{team.smanId}"
                                                   itemLabel="#{team.smanName}"  />
                                </h:selectOneListbox>
                                <!--//  1741  CRM-3259: Sales team:Thea: prevent users from seeing Sales and Comms -  Dashboard Sales Team Lookup-->
                                    <!--// #10203 :CRM-6433-->
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.crmFlag()}"   action="#{lookupService.list('SALES_TEAM')}"   update=":frmSalesTeamLookup"  style="width:70px" oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                &nbsp;&nbsp;&nbsp;
                                <p:commandLink styleClass="sel-salesteam" value="Clear" actionListener="#{lookupService.clear('SALES_TEAM')}" update="@(.sel-salesteam)" disabled="#{!(reportFilters.salesTeams!=null and reportFilters.salesTeams.size() != 0)}" style="text-decoration:underline"/>
                            </h:panelGroup>



                            <p:outputLabel value="#{custom.labels.get('IDS_COMP_REGION')}"  />
                             <!--// #10203 :CRM-6433-->
                            <h:panelGroup class="ui-inputgroup" style="width:110%" >
                                <h:selectOneListbox  styleClass="sel-region"  size="2" style="width: 100%" id="selRegion"     >
                                    <f:selectItems value="#{reportFilters.compReglst}" var="reg"  
                                                   itemValue="#{reg.recId}"
                                                   itemLabel="#{reg.compRegion}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_COMP')}" actionListener="#{lookupService.list('REGION')}" update=":frmCompRegionLookup" oncomplete="PF('dlgRegionLookup').show();" style="width:70px" />
                                &nbsp;&nbsp;&nbsp;
                                 <!--// #10203 :CRM-6433-->
                                <p:commandLink styleClass="sel-region" value="Clear" actionListener="#{lookupService.clear('REGION')}" update="@(.sel-region)" disabled="#{!(reportFilters.compReglst != null and reportFilters.compReglst.size() != 0)}" style="text-decoration:underline" />
                            </h:panelGroup>


                            <p:outputLabel value="Group By"  />
                                <!--// #10203 :CRM-6433-->
                            <p:selectOneRadio class="filterbox" value="#{reportFilters.grp}" id="repgrp" layout="pageDirection"  required="true" >
                                <f:selectItem itemValue="1"  itemLabel="#{custom.labels.get('IDS_PRINCI')}"/> 
                                <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <f:selectItem itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                              
                            </p:selectOneRadio>

                        </p:panelGrid>
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <p:commandButton  value="View Report "  id="viewreport"   styleClass="btn btn-primary btn-xs"  action="#{monthlyExpandedRep.goToNavigation()}"   onclick="custSumGif();" style="margin-left: 10px;"/>

                    </div>
                </div>
            </div>
        </div>
        <script>
            function custSumGif()
            {
                $('#loading').show(); //  reportFilters.setComRegions(compRegns);
         //   reportFilters.setComRegions(this.compRegns);

          //  reportFilters.setCompReglst(lookupService.getCompregionList());
                document.getElementById("cusSumFilter").style.pointerEvents = "none";
                document.getElementById("cusSumFilter").style.opacity = "0.7";
            }

        </script>
    </h:form>
    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
    <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>


    <ui:include src="/lookup/CompanyRegionLookUp.xhtml" />

</ui:composition>