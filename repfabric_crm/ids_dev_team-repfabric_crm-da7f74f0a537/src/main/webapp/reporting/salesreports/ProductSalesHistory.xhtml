<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: CompanyClass.xhtml
// Author: priyadarshini
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 
    <h:form id="prodSalesHistory">
        <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="prodSalesHistory:inputCustomer " />
        <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update="prodSalesHistory:inputDistriName " />
        <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="prodSalesHistory:inputPrincName  " />
        <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="prodSalesHistory:inputAllSalesTeam :prodSalesHistory:viewreport" />

        <p:remoteCommand name="applyProd" actionListener="#{reportFilters.applyProduct(viewProductLookUpService.selectedProduct)}" update="prodSalesHistory:inpTxtProdName :prodSalesHistory:fromYr " />
       <!--#2720:  CRM-1704: Fwd: Product Sales History screen-->
                               
        <p:remoteCommand  autoRun="true"  update="prodSalesHistory:grid" />
        <!--#3153 Sales Reports - Add gif for loading-->
        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;" id="prodSalHisFilterPg">
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
            <h4 style="margin-top: 0px;margin-left: 18px;">  Products Sales History : Report Filter</h4>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->
            <p:outputPanel rendered="#{leftReports.countMsgSalesReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>  
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border"  style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">

                        <div id="loading"  class="div-center" style="display:none;">

                            <!--#3153 Sales Reports - Add gif for loading-->
                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />
                        </div>   


                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >
                            <p:outputLabel  value="As of"   />

                           <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" style="width: 100%;"  >
                                 <p:ajax     event="change"   process="@this" />
                               <f:selectItem itemLabel="#{reports.repYear+1}" itemValue="#{reports.repYear+1}" />
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <!--#330952 - THORSON: 2014 doesn't show - Added 10 years behind-->
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />

                            </p:selectOneMenu>
                            <!--  <p:outputLabel  value="Month"   />-->
                            <div></div>
                            <p:selectOneMenu height="340"   widgetVar="mt" value="#{reportFilters.repMonth}" style="width: 100%;" >
                        <p:ajax     event="change"   process="@this" />
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 
                                <!--<p:ajax event="select" update=":frmDash"/>--> 
                            </p:selectOneMenu>

                            <!--                        </p:panelGrid>
                            
                                                    <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                                                 style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >
                            -->
                            <p:outputLabel for="inpTxtProdName" value="Part Number"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inpTxtProdName" value="#{reportFilters.princiPartNo}"  >
                                    <p:ajax  event="keyup"  update=":prodSalesHistory:fromYr" />
                                </p:inputText>
                                <p:commandButton  icon="fa fa-search" title="Choose Part Number" immediate="true" 
                                                  actionListener="#{viewProductLookUpService.list('applyProd',0)}" 
                                                  update=":dtPartNumForm " oncomplete="PF('dlgPartNum').show()"  
                                                  styleClass="btn-info btn-xs"  style="width:67px" />
                            </h:panelGroup>



                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <p:outputLabel for="inputDistriName" value="#{custom.labels.get('IDS_DISTRI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputDistriName" value="#{reportFilters.distributor.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyDistri', 3, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

<!--                                <p:outputLabel for="inputSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                                <h:panelGroup class="ui-inputgroup"  >
                                    <p:inputText id="inputSalesTeam" value="#{example.salesTeam.smanName}"  />
                                    <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_SALES_TEAM')}" immediate="true" 
                                                      actionListener="#{salesTeamLookupService.list('applySalesTeam',0)}" 
                                                      update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                                      styleClass="btn-info btn-xs" />
                                </h:panelGroup>-->

                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--#3473:     Bug sales team - Sales team data we can enter manually-->
                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  style="width:67px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:outputLabel  value="From Year"   />

                            <p:selectOneMenu widgetVar="fmyr" value="#{reportFilters.repFrmYear}" style="width: 100%;"  id="fromYr"  disabled="#{reportFilters.princiPartNo == ''}">
                                    
                                <!--#2720:  CRM-1704: Fwd: Product Sales History screen-->
                                <f:selectItem itemLabel="#{reports.repFrmYear}" itemValue="#{reports.repFrmYear}" />

                                <f:selectItem itemLabel="#{reports.repFrmYear-1}" itemValue="#{reports.repFrmYear-1}" />
                                <f:selectItem itemLabel="#{reports.repFrmYear-2}" itemValue="#{reports.repFrmYear-2}" />
                                <f:selectItem itemLabel="#{reports.repFrmYear-3}" itemValue="#{reports.repFrmYear-3}" />
                                <!--#330952 - THORSON: 2014 doesn't show - Added 10 years behind-->
                                <f:selectItem itemLabel="#{reports.repFrmYear-4}" itemValue="#{reports.repFrmYear-4}" />
                                <f:selectItem itemLabel="#{reports.repFrmYear-5}" itemValue="#{reports.repFrmYear-5}" />
                                <f:selectItem itemLabel="#{reports.repFrmYear-6}" itemValue="#{reports.repFrmYear-6}" />
                                <f:selectItem itemLabel="#{reports.repFrmYear-7}" itemValue="#{reports.repFrmYear-7}" />
                                <f:selectItem itemLabel="#{reports.repFrmYear-8}" itemValue="#{reports.repFrmYear-8}" />
                                <f:selectItem itemLabel="#{reports.repFrmYear-9}" itemValue="#{reports.repFrmYear-9}" />
                                <f:selectItem itemLabel="#{reports.repFrmYear-10}" itemValue="#{reports.repFrmYear-10}" />

                            </p:selectOneMenu>

                        </p:panelGrid>
                        <!--#3153 Sales Reports - Add gif for loading-->
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <p:commandButton  value="View Report " onclick="processGif();" id="viewreport"   styleClass="btn btn-primary btn-xs"  actionListener="#{productSalesHistory.gotToNavigationPage()}" style="margin-left: 10px;"/>
                    </div>
                </div>
            </div>
        </div>

        <!--#3153 Sales Reports - Add gif for loading-->
        <script>
            function processGif()
            {
                $('#loading').show();
                document.getElementById("prodSalHisFilterPg").style.pointerEvents = "none";
                document.getElementById("prodSalHisFilterPg").style.opacity = "0.4";
            }

        </script>


    </h:form>
    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
    <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
    <ui:include src="/lookup/PartNumDlg.xhtml"/>
</ui:composition>