<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: LastSales.xhtml
// Author: poornimas
//*********************************************************
/*
*  //#6502: Logix Sales last 4 years report,
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    
    <h:form id="lastsalesForm">
        <p:remoteCommand name="applyCRMCust" actionListener="#{reportFilters.applyCustSelect(viewCompLookupService.selectedCompany)}" update="lastsalesForm:inputCustomer"/>
        
        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;" id="lastSalesFilter">
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
            <h4 style="margin-top: 0px;margin-left: 18px;">Last Sales : Report Filter</h4>            
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->
            <p:outputPanel rendered="#{leftReports.countMsgSalesReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>  
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border"  style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">
                        <div id="loading"  class="div-center" style="display:none;">
                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />
                        </div> 
                        
                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7"
                            style="min-height: 50px;margin-top:20px;width: 150%" layout="grid" styleClass="box-primary no-border ui-fluid " >
                            
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  styleClass="required"/>
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.custselect.compName}"  placeholder="[Select]" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCRMCust',2,0,1)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                                         
                        </p:panelGrid>
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <p:commandButton  value="View Report "  id="viewreport"   styleClass="btn btn-primary btn-xs" actionListener="#{lastSalesRep.goToNavigation()}"  onclick="processGif();" style="margin-left: 10px;"/>
                    </div>
                </div>
            </div> 
        </div>
        
        <script>
            function processGif()
            {
                $('#loading').show();
                document.getElementById("lastSalesFilter").style.pointerEvents = "none";
                document.getElementById("lastSalesFilter").style.opacity = "0.4";
            }
            
            function hideGif()
            {
                document.getElementById("lastSalesFilter").style.pointerEvents = "all";
                document.getElementById("lastSalesFilter").style.opacity = "1";
                $('#loading').hide();
            }
        </script>  
        
    </h:form>
    
    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />                     
    
</ui:composition>
    

