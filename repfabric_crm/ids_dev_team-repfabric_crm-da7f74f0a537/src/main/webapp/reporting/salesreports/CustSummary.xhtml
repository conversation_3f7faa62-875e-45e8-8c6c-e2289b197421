<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: CustSummary.xhtml
// Author: priyadarshini
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 
    <!--248 :sales report->line overview,customer summary->year update issue-->

    <!--        <ui:define name="meta">
                <f:metadata> 
                    <f:viewAction action="#{custSummaryRep.setFilterValues()}"/>
                </f:metadata>
            </ui:define>
    -->



    <h:form id="custSummaryForm">
        <!--428 :sales report->line overview,customer summary->year update issue-->
        <p:remoteCommand autoRun="true"   update=":custSummaryForm:grid"/>
        <!--Bug #3237:  CRM-3855: Dashboard Issues - Kevin can't see dashboard numbers-->
        <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyOnlyCustomer(viewCompLookupService.selectedCompany)}" update="custSummaryForm:inputCustomer  :custSummaryForm:viewreport"/>
        <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update="custSummaryForm:inputDistriName  :custSummaryForm:viewreport" />
        <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="custSummaryForm:inputPrincName  :custSummaryForm:viewreport"  />
        <!--3811 CRM-2282 FJM: Add "Export to Excel" button in Customer Summary & Customer Detail reports-->


        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;" id="cusSumFilter">
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
            <h4 style="margin-top: 0px;margin-left: 18px;">  
                <!--1833:CRM-2811: MerchantsSales: edit end user label-->
                <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->

                #{custom.labels.get('IDS_DB_CUSTOMER')} Summary : Report Filter 
            </h4>

            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->
            <p:outputPanel rendered="#{leftReports.countMsgSalesReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>  

            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border"  style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">
                        <!--#3116:Task :Sales Reports - Customer Summary - Add gif for loading-->
                        <div id="loading"  class="div-center" style="display:none;">
                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />
                            <!--<img id="loading-image" src="images/ajax-loader.gif" alt="Loading..." />-->
                        </div>  

                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7"
                                     style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel  value="As of"   />
                            <!--428 :sales report->line overview,customer summary->year update issue-->
                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" style="width: 100%;"  >
                                <p:ajax     event="change"  process="@this"   update=":custSummaryForm:viewreport" />
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <!--#330952 - THORSON: 2014 doesn't show - Added 10 years behind-->
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />

                            </p:selectOneMenu>
                            <!--  <p:outputLabel  value="Month"   />-->
                            <div></div>
                            <!--428 :sales report->line overview,customer summary->year update issue-->
                            <p:selectOneMenu height="340"   widgetVar="mt" value="#{reportFilters.repMonth}" style="width: 100%;" >
                                <p:ajax     event="change"   process="@this"  update=":custSummaryForm:viewreport"/>
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 
                                <!--<p:ajax event="select" update=":frmDash"/>--> 
                            </p:selectOneMenu>

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <p:outputLabel for="inputDistriName" value="#{custom.labels.get('IDS_DISTRI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputDistriName" value="#{reportFilters.distributor.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyDistri', 3, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

<!--                                <p:outputLabel for="inputSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                                <h:panelGroup class="ui-inputgroup"  >
                                    <p:inputText id="inputSalesTeam" value="#{example.salesTeam.smanName}"  />
                                    <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_SALES_TEAM')}" immediate="true" 
                                                      actionListener="#{salesTeamLookupService.list('applySalesTeam',0)}" 
                                                      update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                                      styleClass="btn-info btn-xs" />
                                </h:panelGroup>-->

                            <p:outputLabel  value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <!--                            <h:panelGroup class="ui-inputgroup"  >
                                                            <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" />
                                                            <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                                              actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                                              update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  style="width:67px"
                                                                              styleClass="btn-info btn-xs" />
                                                        </h:panelGroup>-->
                            <!--#8692  ESCALATIONS CRM-6015  customer summary report by REGION--> 
                            <!--//#2999Task Customer Summary report - Add multi select Sales team option-->
                            <!--// #10203 :CRM-6433-->
                                                        
                           <!--#12119 ESCALATIONS CRM-7368 Customer Summary Report: Summarize by Parent Company disables export-->
                            <h:panelGroup class="ui-inputgroup" style="width:112%" >
                                <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width: 84%;" id="selSalesTeam" >
                                    <f:selectItems value="#{reportFilters.salesTeams}" var="team"  
                                                   itemValue="#{team.smanId}"
                                                   itemLabel="#{team.smanName}"  />
                                </h:selectOneListbox>
                                <!--                                //  1741  CRM-3259: Sales team:Thea: prevent users from seeing Sales and Comms -  Dashboard Sales Team Lookup-->

                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.crmFlag()}"   action="#{lookupService.list('SALES_TEAM')}"   update=":frmSalesTeamLookup"   oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                &nbsp;&nbsp;&nbsp;
                                <!--// #10203 :CRM-6433-->
                                <p:commandLink styleClass="sel-salesteam" value="Clear" actionListener="#{lookupService.clear('SALES_TEAM')}" update="@(.sel-salesteam)" disabled="#{!(reportFilters.salesTeams!=null and reportFilters.salesTeams.size() != 0)}" style="text-decoration:underline"/>
                            </h:panelGroup>
                            <!--                       //    Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            
                           <!--#12119  ESCALATIONS CRM-7368 Customer Summary Report: Summarize by Parent Company disables export-->
                            <!--#8692  ESCALATIONS CRM-6015  customer summary report by REGION--> 
                            <!--// #10203 :CRM-6433-->
                            <p:outputLabel value="#{custom.labels.get('IDS_REGION')}"  />
                            <h:panelGroup class="ui-inputgroup" style="width:112%" >
                                <h:selectOneListbox  styleClass="sel-region"  size="2" style="width: 84%" id="selRegion"     >
                                    <f:selectItems value="#{reportFilters.compReglst}" var="reg"  
                                                   itemValue="#{reg.recId}"
                                                   itemLabel="#{reg.compRegion}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select  #{custom.labels.get('IDS_REGION')}" actionListener="#{lookupService.list('REGION')}" update=":frmCompRegionLookup" oncomplete="PF('dlgRegionLookup').show();"  />
                                &nbsp;&nbsp;&nbsp;
                                <!--#8692  ESCALATIONS CRM-6015  customer summary report by REGION--> 
                                <p:commandLink styleClass="sel-region" value="Clear" actionListener="#{lookupService.clear('REGION')}" update="@(.sel-region)" disabled="#{!(reportFilters.compReglst != null and reportFilters.compReglst.size() != 0)}" style="text-decoration:underline" />
                            </h:panelGroup>

                            <p:outputLabel value="Minimum Sales"/>

                                <!--<p:inputText widgetVar="min"  value="#{reportFilters.minVal}" style="width: 80%">-->
                            <p:inputNumber    value="#{reportFilters.minVal}"   style="width: 80%" minValue="0" decimalPlaces="2" >

                                <p:ajax  event="keyup"   />
                            </p:inputNumber>
                            <!--</p:inputText>-->
                            <!--// #10203 :CRM-6433-->
                            <p:outputLabel value="Summarize by Parent Company"  />
                            <!--                <p:outputLabel  styleClass="m" value=":"/>-->
                            <p:selectBooleanCheckbox  itemLabel="#{reportFilters.summarizeByParentCompFlag ? '':''}"  value="#{reportFilters.summarizeByParentCompFlag}"     id="chkGoogleSync" widgetVar="chk">
                                <p:ajax update=":custSummaryForm:chkGoogleSync  :custSummaryForm:rpt1  :custSummaryForm:viewreport"   />               


                            </p:selectBooleanCheckbox>

                            <p:outputLabel value="Report Type"/>
                            <!--// #10203 :CRM-6433-->
                            <!-- <p:selectOneRadio value="1" id="rpt1"   layout="pageDirection" widgetVar="grp" >-->
                            <p:selectOneRadio id="rpt1"  value="#{reportFilters.grp}" layout="pageDirection" widgetVar="grp"  style="width: 160%" disabled="#{reportFilters.summarizeByParentCompFlag}" >
                                <p:ajax event="blur" process="rpt1"   />

                                <f:selectItem   itemValue="1" itemLabel="Standard Summary"  />                                   
                                <f:selectItem itemValue="2" itemLabel="Direct and Credited Sales Amounts"/>
                                <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                <!--<f:selectItem itemValue="3" itemLabel="Secondary Related Company"/>-->


                                <!--Bug #5424:   CRM-4606: "Secondary Customer Sales" & "Primary + Secondary Customer" not working in Sales-customer-->
                                            <!--<f:selectItem itemValue="3" itemLabel="#{custom.labels.get('IDS_SECOND_CUSTOMER')} Related Company"/>-->
                            </p:selectOneRadio>
                        </p:panelGrid>
                        <!--#3116:Task :Sales Reports - Customer Summary - Add gif for loading-->
                        <!--428 :sales report->line overview,customer summary->year update issue-->
                        <!--//        #465 Sales Reports > Customer Summary--> 
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <p:commandButton  value="View Report"  id="viewreport"   styleClass="btn btn-primary btn-xs"  action="#{custSummaryRep.goToNavigationpage(1)}"  actionListener="#{reportFilters.setSalesteamlst()}" onclick="custSumGif();" style="margin-left: 10px;"/>

                    </div>
                </div>
            </div>             
        </div>
        <!--#3116:Task :Sales Reports - Customer Summary - Add gif for loading-->
        <script>
            function custSumGif()
            {
                $('#loading').show();
                document.getElementById("cusSumFilter").style.pointerEvents = "none";
                document.getElementById("cusSumFilter").style.opacity = "0.7";
            }

        </script>
    </h:form>
    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
    <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>

    <!--465:region lookup-->
    <ui:include src="/lookup/CompanyRegionLookUp.xhtml" />

</ui:composition>