<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: AccessDenied.xhtml
// Author: Priyadarshini
// Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved.
//********************************************************************* -->
<!--Task #6: Settings: Subtables -->
<!--<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <div style="height: 400px">

        <div style="text-align: center;line-height: 400px;font-weight: bold">
            You are not authorized to access this page 
        </div> 

    </div>
</ui:composition>-->


<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui">
    <div style="padding-top: 150px;text-align: center">


        <h3>
             You are not authorized to access this page.<br/><br/><h:link onclick="assignWinName('/index.xhtml', 'login', event);" outcome="/index" value="Home"/>
        </h3>

    </div>
</ui:composition>
