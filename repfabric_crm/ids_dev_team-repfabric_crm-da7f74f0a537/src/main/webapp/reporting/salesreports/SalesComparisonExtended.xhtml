<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: SalesAnalyticalSummary.xhtml
// Author: priyadarshini
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 

    <ui:define name="meta">
        <f:metadata> 
            <!--<f:viewAction action="#{salesReports.resetFilters()}"/>-->
        </f:metadata>
    </ui:define>




    <h:form id="custSummaryForm">

        <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="custSummaryForm:inputCustomer  :custSummaryForm:viewreport"/>

        <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="custSummaryForm:inputPrincName  :custSummaryForm:viewreport"  />


        <!--1704-->
        <p:remoteCommand name="applyParentComp" actionListener="#{reportFilters.applyParentComp(viewCompLookupService.selectedCompany)}"  action="#{salesComparisonExtRep.defaultGrp()}" update="custSummaryForm:inputParentComp :custSummaryForm:viewreport :custSummaryForm:grpBy :custSummaryForm:procBy"/>


        <!--#160 date issue-->
        <p:remoteCommand autoRun="true" update=":custSummaryForm:grid" />

        <!--#3153 Sales Reports - Add gif for loading-->
        <div id="loading"  class="div-center" style="display:none;">

            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                            left: 600px;
                            top: 300px;
                            width: 150px;
                            height: 150px;
                            z-index: 9999; opacity: .3 !important;" />
        </div>  
        <p:growl id="calValidator"  />
        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;" id="salcomExtended">
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
            <h4 style="margin-top:0px;margin-left: 18px;">  Sales Analytical Summary : Report Filter</h4>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->
            <p:outputPanel rendered="#{leftReports.countMsgSalesReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>  
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border"  style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">
                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7"
                                     style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >
                            <!--//Task #5796 devincahn: enhancements to the  sales analytical summary report-->
                            <p:outputLabel value="As of "/>
                             <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                            <p:selectOneRadio value="#{reportFilters.asOf}"  widgetVar="assOf" layout="pageDirection" id="asOf" required="true"    >            
                                <f:selectItem itemValue="1" itemLabel="Month"/> 
                                <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <f:selectItem itemValue="2" itemLabel="Selected Date Range"/>
                                <p:ajax event="change"   process="asOf" update="custSummaryForm" />
                            </p:selectOneRadio>
                             <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                            <p:panelGrid rendered="#{reportFilters.asOf eq 2}"  id="grid1" columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7"
                                         style="min-height: 50px;margin-top:20px;width: 250%"       layout="grid" styleClass="box-primary no-border ui-fluid ">
                                <p:outputLabel  value="From"/>
                                <h:panelGroup>

                                    <p:calendar  value="#{reportFilters.fdate}" showOn="button" pattern="#{globalParams.dateFormat}" id="fmdt" converterMessage="Please enter the valid From date">

                                        <p:ajax  event="keyup"  process="@this" />
                                        <p:ajax event="dateSelect" process="@this"  />
                                    </p:calendar>
                                </h:panelGroup>

                                <p:outputLabel  value="To"/>
                                <h:panelGroup >

                                    <p:calendar value="#{reportFilters.tdate}" showOn="button" pattern="#{globalParams.dateFormat}" id="todt" widgetVar="todate" converterMessage="Please enter the valid To date">
                                        <p:ajax  event="keyup"  process="@this" />
                                        <p:ajax event="dateSelect" process="@this"  />
                                    </p:calendar>
                                </h:panelGroup>
                            </p:panelGrid>

                            <p:panelGrid rendered="#{reportFilters.asOf eq 1}"  id="grid3" columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7"
                                         style="min-height: 50px;margin-top:20px;width: 250%;margin-left: -10px"       layout="grid" styleClass="box-primary no-border ui-fluid ">
                                <p:outputLabel  value="Year"/>
                                <!--#160 date issue-->
                                <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added margin left-->
                                <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" style="width: 100%;margin-left: 10px;"  >
                                    <!--#160 date issue-->
                                    <p:ajax     event="change"   process="@this" />
                                    <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                    <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                    <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                    <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                    <!--#330952 - THORSON: 2014 doesn't show - Added 10 years behind-->
                                    <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                    <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                    <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                    <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                    <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                    <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                    <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />

                                </p:selectOneMenu>
                                <!--  <p:outputLabel  value="Month"   />-->
                                <p:outputLabel  value="Month"/>
                                <!--#160 date issue-->
                                <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added margin left-->
                                <p:selectOneMenu  height="340"   widgetVar="mt" value="#{reportFilters.repMonth}" style="width: 100%;margin-left: 10px;" >
                                    <p:ajax     event="change"   process="@this" />
                                    <f:selectItem itemLabel="January" itemValue="1" />
                                    <f:selectItem itemLabel="February" itemValue="2" />
                                    <f:selectItem itemLabel="March" itemValue="3" />
                                    <f:selectItem itemLabel="April" itemValue="4" /> 
                                    <f:selectItem itemLabel="May" itemValue="5" /> 
                                    <f:selectItem itemLabel="June" itemValue="6" /> 
                                    <f:selectItem itemLabel="July" itemValue="7" /> 
                                    <f:selectItem itemLabel="August" itemValue="8" /> 
                                    <f:selectItem itemLabel="September" itemValue="9" /> 
                                    <f:selectItem itemLabel="October" itemValue="10" /> 
                                    <f:selectItem itemLabel="November" itemValue="11" /> 
                                    <f:selectItem itemLabel="December" itemValue="12" /> 
                                    <!--<p:ajax event="select" update=":frmDash"/>--> 
                                </p:selectOneMenu>

                            </p:panelGrid>


                            <br></br>
                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>



<!--                                <p:outputLabel for="inputSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                                <h:panelGroup class="ui-inputgroup"  >
                                    <p:inputText id="inputSalesTeam" value="#{example.salesTeam.smanName}"  />
                                    <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_SALES_TEAM')}" immediate="true" 
                                                      actionListener="#{salesTeamLookupService.list('applySalesTeam',0)}" 
                                                      update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                                      styleClass="btn-info btn-xs" />
                                </h:panelGroup>-->


                            <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <!--//Task #5796 devincahn: enhancements to the  sales analytical summary report-->
                             <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                            <p:outputLabel  value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup" style="width: 112%">
                                <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width: 350%" id="selSalesTeam" >
                                    <f:selectItems value="#{reportFilters.salesTeams}" var="team"  
                                                   itemValue="#{team.smanId}"
                                                   itemLabel="#{team.smanName}"  />
                                </h:selectOneListbox>
                                <!--//  1741  CRM-3259: Sales team:Thea: prevent users from seeing Sales and Comms -  Dashboard Sales Team Lookup-->

                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.crmFlag()}"   action="#{lookupService.list('SALES_TEAM')}"   update=":frmSalesTeamLookup"  style="width:70px" oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                &nbsp;&nbsp;&nbsp;
                                <p:commandLink process="@this" immediate="true" styleClass="sel-salesteam" value="Clear" actionListener="#{lookupService.clear('SALES_TEAM')}" update="@(.sel-salesteam)" disabled="#{!(reportFilters.salesTeams!=null and reportFilters.salesTeams.size() != 0)}" style="text-decoration:underline"/>
                            </h:panelGroup>
                            <!--//#8500 CRM-5877: Aurora: Reports: add Region + prior year total-->
                             <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                            <p:outputLabel value="#{custom.labels.get('IDS_REGION')}"  />
                            <h:panelGroup class="ui-inputgroup" style="width:112%" >
                                <h:selectOneListbox  styleClass="sel-region"  size="2" style="width: 350%" id="selRegion"     >
                                    <f:selectItems value="#{reportFilters.compReglst}" var="reg"  
                                                   itemValue="#{reg.recId}"
                                                   itemLabel="#{reg.compRegion}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_REGION')}" actionListener="#{lookupService.list('REGION')}" update=":frmCompRegionLookup" oncomplete="PF('dlgRegionLookup').show();" style="width:70px" />
                                &nbsp;&nbsp;&nbsp;
                                <p:commandLink styleClass="sel-region" value="Clear" actionListener="#{lookupService.clear('REGION')}" update="@(.sel-region)" disabled="#{!(reportFilters.compReglst != null and reportFilters.compReglst.size() != 0)}" style="text-decoration:underline" />
                            </h:panelGroup>
                            
                            

<!--                            <h:panelGroup class="ui-inputgroup" rendered="#{salesComparisonExtRep.asOf eq 1}" >
                                #3473:     Bug sales team - Sales team data we can enter manually
                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  style="width:67px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>-->


                            <!--1704 :-->
                            <p:outputLabel for="inputParentComp" value="Parent Company"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputParentComp" value="#{reportFilters.parentComp.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true"  actionListener="#{viewCompLookupService.showPrtValues()}"
                                                  action="#{viewCompLookupService.listActive('applyParentComp', 2, 0, 1)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" >
                                  
                                </p:commandButton>
                            </h:panelGroup>
                             <!--//           #9921 PRIORITY CRM-6230 Report: Aurora: Sales Analytical enhancement-->
                             <p:outputLabel value="Process by"/>
                              <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                             <p:selectOneRadio  value="#{reportFilters.processBy}" disabled="#{reportFilters.parentComp.compName.length() >0 and reportFilters.parentComp.compName != 'All'}"  widgetVar="grp17" layout="pageDirection" id="procBy" required="true"  >            
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}"/>
                                <f:selectItem itemValue="2" itemLabel="#{custom.labels.get('IDS_PRINCI')}" /> 
                                <p:ajax  process="@this"  listener="#{salesComparisonExtRep.unCheckSummaryFlag()}"  event="change" update="custSummaryForm:grpBy  custSummaryForm:chkShowSummary"/>
                            </p:selectOneRadio>
                            
                            <p:outputLabel value="Group by"/>
                             <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                               <!--//           #9921 PRIORITY CRM-6230 Report: Aurora: Sales Analytical enhancement-->
                               <p:selectOneRadio value="#{reportFilters.grp}"   widgetVar="grp16" layout="pageDirection" id="grpBy" required="true"  disabled="#{reportFilters.parentComp.compId >0 || reportFilters.processBy eq 2}"  >            
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/> 
                                <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <f:selectItem itemValue="2"  itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}" />

                            </p:selectOneRadio>
                           

                              <!--//           #9921 PRIORITY CRM-6230 Report: Aurora: Sales Analytical enhancement-->
                            <!--//Task #5796 devincahn: enhancements to the  sales analytical summary report-->
                             <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                            <p:outputLabel value="Summary Only"   />
                            <!--                                            <p:outputLabel  styleClass="m" value=":"/>-->
                            <p:selectBooleanCheckbox  styleClass="sel-salesteam" disabled="#{reportFilters.processBy eq 2}"  itemLabel="#{reportFilters.summaryFlag ? '':''}"  value="#{reportFilters.summaryFlag}"     id="chkShowSummary" widgetVar="Summary">
                                <p:ajax update="custSummaryForm:chkShowSummary"    />               


                            </p:selectBooleanCheckbox>


                        </p:panelGrid>
                         <!--// #10261 02/01/2023: CRM-6433: Saved Report Filters: Sales Reports > Sales Analytical Summary-->
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <p:commandButton  value="View Report "  onclick="processGif()" id="viewreport"   styleClass="btn btn-primary btn-xs"   action="#{salesComparisonExtRep.goToNavigationpage()}" update=":custSummaryForm  calValidator" oncomplete="PF('dtlSalesTeamsLookup').unselectAllRows();" style="margin-left: 10px;"/>

                    </div>
                </div>
            </div>
        </div>
        <!--//Task #5796 devincahn: enhancements to the  sales analytical summary report-->
        <!--#3153 Sales Reports - Add gif for loading-->
        <style type="text/css">
            input[type=checkbox]:checked:before {

                color: black !important;
            }
        </style>

        <script>
            function processGif()
            {
                $('#loading').show();
                document.getElementById("salcomExtended").style.pointerEvents = "none";
                document.getElementById("salcomExtended").style.opacity = "0.4";
            }
            function hideGif()
            {
                $('#loading').hide();
                document.getElementById("salComExtendedrpt").style.pointerEvents = "all";
                document.getElementById("salComExtendedrpt").style.opacity = "1";
            }
        </script>                   

    </h:form>
    <!--//#8500 CRM-5877: Aurora: Reports: add Region + prior year total-->
    <!--//Task #5796 devincahn: enhancements to the  sales analytical summary report-->
    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
    <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
    <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>
     <ui:include src="/lookup/CompanyRegionLookUp.xhtml" />
</ui:composition>