<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 
    <h:form id="prodDetForm">
        <!--Bug #3237:  CRM-3855: Dashboard Issues - <PERSON> can't see dashboard numbers-->
        <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyOnlyCustomer(viewCompLookupService.selectedCompany)}" update="prodDetForm:inputCustomer :prodDetForm:inputAllSalesTeam :prodDetForm:viewreport"/>
        <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update="prodDetForm:inputDistriName  :prodDetForm:viewreport" />
        <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="prodDetForm:inputPrincName  :prodDetForm:viewreport"  />
        <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="prodDetForm:inputAllSalesTeam :prodDetForm:viewreport" />
        <p:remoteCommand name="applyProd" actionListener="#{reportFilters.applyProduct(viewProductLookUpService.selectedProduct)}" update="prodDetForm:inpTxtProdName" />
        <!--#pms:160 Product details year and month issue-->
        <p:remoteCommand autoRun="true"  update=":prodDetForm:grid" />
        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;" id="prodFilterPg">
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
            <h4 style="margin-top:0px;margin-left: 18px;">     Product Details : Report Filter</h4>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->
            <p:outputPanel rendered="#{leftReports.countMsgSalesReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>  
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border"  style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">
                        <!--#3084:TaskProduct Details Report - Add gif for loading style added-->
                        <div id="loading"  class="div-center" style="display:none;">
                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />
                            <!--<img id="loading-image" src="images/ajax-loader.gif" alt="Loading..." />-->
                        </div>  

                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7"
                                     style="min-height: 50px;margin-top:20px;width: 130%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel  value="As of"   />
 <!--#pms:160 Product details year and month issue-->
                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" style="width: 100%;"  >
                               <p:ajax     event="change"   process="@this" />
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <!--#330952 - THORSON: 2014 doesn't show - Added 10 years behind-->
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />

                            </p:selectOneMenu>
                            <!--  <p:outputLabel  value="Month"   />-->
                            <div></div>
                             <!--#pms:160 Product details year and month issue-->
                            <p:selectOneMenu height="340"   widgetVar="mt" value="#{reportFilters.repMonth}" style="width: 100%;" >
                                <p:ajax     event="change"   process="@this" />
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 
                                <!--<p:ajax event="select" update=":frmDash"/>--> 
                            </p:selectOneMenu>
                            <!--                        </p:panelGrid>
                            
                                                    <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                                                 style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >-->

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <p:outputLabel for="inputDistriName" value="#{custom.labels.get('IDS_DISTRI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputDistriName" value="#{reportFilters.distributor.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyDistri', 3, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>



                            <!--#11990  CRM-7302   Product Details Report : Does not load-->
                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                                  styleClass="btn-info btn-xs"  />
                            </h:panelGroup>



<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <!--#11990 CRM-7302   Product Details Report : Does not load-->
                            <p:outputLabel for="inpTxtProdName" value="Part Number"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inpTxtProdName" value="#{reportFilters.princiPartNo}"  />
                                <p:commandButton  icon="fa fa-search" title="Choose Part Number" immediate="true" 
                                                  actionListener="#{viewProductLookUpService.list('applyProd',0)}" 
                                                  update=":dtPartNumForm" oncomplete="PF('dlgPartNum').show()"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                            <!--<p:outputLabel value="Show Secondary"  />-->
                            <p:outputLabel value="Show #{custom.labels.get('IDS_SECOND_CUSTOMER')}"  />
                              <!--//                      #10385  CRM-6433: Saved Report Filters: Sales Reports > Product Details Report-->
                            <p:selectBooleanCheckbox value="#{reportFilters.showSecondaryFlag}"     id="chkSec" widgetVar="chk">
                                <!--<p:ajax update="chkSec" />-->               
                                <!--5928 Sales Report: Product Details> Export > excel file-Error,  Principal not captured in pdf file-->
                               <!--  <p:ajax event="change"  listener="#{productDtlsItem.chnageFlag(reportFilters.showSecondaryFlag)}"  
                                        update=":prodDetForm:chkSec"/>--> 
                            </p:selectBooleanCheckbox> 

                            <p:outputLabel value="Group by"/>

                            <p:selectOneRadio value="#{reportFilters.grp}"  widgetVar="grp" layout="pageDirection"  required="true">
                                <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <f:selectItem   itemValue="0" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>                                   
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_DISTRI')}"/>
                                <f:selectItem itemValue="2" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/>
                            </p:selectOneRadio>
                            <!--                Report type Radio Button-->
                            <p:outputLabel value="Report Type"/>

                            <p:selectOneRadio value="#{reportFilters.repCml}"  widgetVar="rptType" layout="pageDirection"  required="true">
                                <f:selectItem itemValue="0" itemLabel="Quarterly Periods with Sales History"/>
                                <f:selectItem   itemValue="1" itemLabel="Cumulative with Pricing Comparisons"/> 
                            </p:selectOneRadio>         


                        </p:panelGrid>
                        <!--#3084:TaskProduct Details Report - Add gif for loading style added-->
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <p:commandButton  onclick="processGif();"  value="View Report "  id="viewreport"   styleClass="btn btn-primary btn-xs"  actionListener="#{productDtlsItem.gotTonavigationpage()}"  style="margin-left: 10px;" />
                        <p:dialog header="Please wait" draggable="false"  widgetVar="dlgProdaj" modal="true" closable="false" resizable="false" width="150" >
                            <h:graphicImage  library="images" name="ajax-loader.gif" />
                            <p:spacer width="5"/>
                            <h:outputLabel value="Loading data.."/>
                        </p:dialog>
                    </div>
                </div>
            </div>
        </div>

        <!--#3084:TaskProduct Details Report - Add gif for loading style added-->
        <script>
            function processGif()
            {
                $('#loading').show();
                document.getElementById("prodFilterPg").style.pointerEvents = "none";
                document.getElementById("prodFilterPg").style.opacity = "0.7";
            }

        </script>
    </h:form>

    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
    <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
    <ui:include src="/lookup/PartNumDlg.xhtml"/>
</ui:composition>