<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: SalesComparison.xhtml
// Author: priyadarshini
 //     #2932:   Task CRM-1815: Fwd: Sales Comparison ReportsCRM-1815:
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

    <!--Bug 656 : CRM-2620: RSTthermal:  Sales Comparison missing a total-->

    <!--    <ui:define name="meta">
            <f:metadata> 
                <f:viewAction action="#{reports.resetFilters()}"/>
            </f:metadata>
        </ui:define>-->

    <h:form id="salCompForm">
        <!--Bug 656 : CRM-2620: RSTthermal:  Sales Comparison missing a total-->

        <p:remoteCommand autoRun="true" update=":salCompForm:grid :salCompForm:grid1 :salCompForm:grid3" />
        <!--#3153 Sales Reports - Add gif for loading-->
        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;" id="salCompFilterPg">
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
            <h4 style="margin-top:0px;margin-left: 18px;">  Sales Comparison : Report Filter</h4>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->
            <p:outputPanel rendered="#{leftReports.countMsgSalesReport > 0}" style="border: 0px;color: red;margin-top: -11px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>  
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border"  style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">
                        <div id="loading"  class="div-center" style="display:none;">

                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />
                        </div>
                        <!--Bug 656 : CRM-2620: RSTthermal:  Sales Comparison missing a total-->

                        <p:panelGrid columns="3"   layout="grid"  id="grid3"  style="width: 150%;"  styleClass="box-primary no-border ui-fluid ">
                       
                            <p:outputLabel  value="As of"   />
                           
                                <!--//  #8515  CRM-5877: Aurora: Reports: add Region + prior year total (Sales Comparison)-->
                            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                            <h:panelGroup>
                            <!--<p:outputLabel value="Month"/>-->    
                            <!--#908    CRM-2727: FW: sales comparison always defaulting to january 2020-->
                            <!--// 908 : lastest month-->
                            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                            <p:selectOneMenu widgetVar="mt" value="#{reportFilters.repMonth}" style="margin-left: 5px;">
                                <p:ajax     event="change"   process="@this" />
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 
                                <!--<p:ajax event="change" listener="#{salesComparisionRep.enableRunRptFlag}" update=":salesCompReport:runReport  :salesCompReport:gph   " />-->
                            </p:selectOneMenu>
                            </h:panelGroup>
                               <!--//  #8515  CRM-5877: Aurora: Reports: add Region + prior year total (Sales Comparison)-->
                            <!--<p:outputLabel value="Year"/>-->                
                            <!--#908    CRM-2727: FW: sales comparison always defaulting to january 2020-->
                            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                            <h:panelGroup>
                                <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                                <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" style="margin-left: 5px;" >
                                 <p:ajax     event="change"   process="@this" />
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />
                                <!--<p:ajax event="change" listener="#{salesComparisionRep.enableRunRptFlag}" update="salesCompReport:runReport  :salesCompReport:gph" />-->
                            </p:selectOneMenu> 
                           </h:panelGroup>
                            
                           
     
                        </p:panelGrid> 
                       

                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;margin-top:25px;width: 150%;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >
                              <!--//  #8515  CRM-5877: Aurora: Reports: add Region + prior year total (Sales Comparison)-->
                            <p:outputLabel value="#{custom.labels.get('IDS_REGION')}"  />
                            <h:panelGroup class="ui-inputgroup" layout="pageDirection" style="margin-left:-95px">
                                <h:selectOneListbox  styleClass="sel-region"  size="2" style="width: 350%" id="selRegion"     >
                                    <f:selectItems value="#{reportFilters.compReglst}" var="reg"  
                                                   itemValue="#{reg.recId}"
                                                   itemLabel="#{reg.compRegion}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_REGION')}" actionListener="#{lookupService.list('REGION')}" update=":frmCompRegionLookup" oncomplete="PF('dlgRegionLookup').show();" style="width:70px" />
                                &nbsp;&nbsp;&nbsp;
                                <p:commandLink styleClass="sel-region" value="Clear" actionListener="#{lookupService.clear('REGION')}" update="@(.sel-region)" disabled="#{!(reportFilters.compReglst != null and reportFilters.compReglst.size() != 0)}" style="text-decoration:underline" />
                            </h:panelGroup>

                            <p:outputLabel value="Group By" /> 
                            
                                <!--#10325  JOHN CRM-6433: Saved Report Filters: Sales Reports > Sales Comparison-->
                            <p:selectOneRadio id="repg"  class="filterbox"  value="#{reportFilters.grp}"   
                                              layout="pageDirection"   style="margin-left:-95px"  required="true">
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/>  
                                <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <f:selectItem itemValue="3" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                                <f:selectItem itemValue="4" itemLabel="#{custom.labels.get('IDS_DISTRI')}"/>
                                <f:selectItem itemValue="5" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}"/> 
                                <!--//#6161: CRM-4899: Omit non-active Manufacturers from Sales Comparison Report - poornima - to update the Inactive Principal checkbox -->
                                <p:ajax  event="change" update="otPanel" />
                            </p:selectOneRadio>


                        </p:panelGrid>

                        <p:panelGrid id="grid1" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;margin-top:20px;width: 150%;"       layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel value="Report for " /> 
       <!--#10325  JOHN CRM-6433: Saved Report Filters: Sales Reports > Sales Comparison-->

                            <p:selectOneRadio id="forgp"  class="filterbox"  value="#{reportFilters.reportFor}"   
                                              layout="pageDirection"   style="margin-left:-95px"  required="true">
                                <f:selectItem itemValue="1" itemLabel="Selected Year"/>                                                
                                <f:selectItem itemValue="2" itemLabel="Previous 12 months"/>                                
                            </p:selectOneRadio>
                        </p:panelGrid>
                        
<!--                        // Feature #5739 Add export options grouped by customer and detailed by principals-->
                        <!--#10325  JOHN CRM-6433: Saved Report Filters: Sales Reports > Sales Comparison-->
                        <p:panelGrid id="grid2" columns="2" style="width: 150%;" layout="grid"   >
                            <p:outputLabel value="Show Details"   />
                            <!--                                            <p:outputLabel  styleClass="m" value=":"/>-->
                            <p:selectBooleanCheckbox  itemLabel="#{reportFilters.showDetails ? '':''}" style="margin-left:-95px"   value="#{reportFilters.showDetails}"     id="chkShowDetails" widgetVar="chek">
                                <p:ajax update=":salCompForm:chkShowDetails   :salCompForm:viewreport" />               


                            </p:selectBooleanCheckbox>
                        </p:panelGrid>


                        <!--//#6161: CRM-4899: Omit non-active Manufacturers from Sales Comparison Report - poornima -->
                         <!--#10325  JOHN CRM-6433: Saved Report Filters: Sales Reports > Sales Comparison-->
                        <p:outputPanel  id="otPanel">  
                            <p:autoUpdate/>
                            <p:panelGrid id="grid4" columns="2" style="width: 150%;" layout="grid" rendered="#{reportFilters.grp == 1}"  >
                                <p:outputLabel value="Exclude Inactive #{custom.labels.get('IDS_PRINCIS')}" />                               
                                <p:selectBooleanCheckbox value="#{reportFilters.excludeInactPrinciFlag}" style="margin-left:-95px" id="chkExcldInactivePrinc" widgetVar="excldInactiveFlag">
                                    <p:ajax update=":salCompForm:chkExcldInactivePrinc   :salCompForm:viewreport" /> 
                                
                                </p:selectBooleanCheckbox>
                            </p:panelGrid>
                        </p:outputPanel>
                        <!--#3153 Sales Reports - Add gif for loading processGif(); -->
                        <p:commandButton  value="View Report "  onclick="processGif();" id="viewreport"   styleClass="btn btn-primary btn-xs"  actionListener="#{salesComparisionRep.goToNavigationPage()}"  style="margin-left: 10px;"/>

                    </div>
                </div>
            </div>
        </div>

        <!--#3153 Sales Reports - Add gif for loading-->
        <script>
            function processGif()
            {
                $('#loading').show();
                document.getElementById("salCompFilterPg").style.pointerEvents = "none";
                document.getElementById("salCompFilterPg").style.opacity = "0.7";
            }
            

        </script>


    </h:form>
    <!--//  #8515  CRM-5877: Aurora: Reports: add Region + prior year total (Sales Comparison)-->
    <ui:include src="/lookup/CompanyRegionLookUp.xhtml" />
</ui:composition>