<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: CompanyClass.xhtml
// Author: priyadarshini
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 


    <h:form id="custDetailsForm">
        <!--<p:growl widgetVar="grl"/>-->
        <p:remoteCommand name="applyCRMCust" actionListener="#{reportFilters.applyCustSelect(viewCompLookupService.selectedCompany)}" update=":custDetailsForm:inputCustName  :custDetailsForm:viewreport" />

        <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update=":custDetailsForm:inputDistriName :custDetailsForm:viewreport "/>
        <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update=":custDetailsForm:inputPrincName   :custDetailsForm:viewreport" />
<!--            <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update=":custDetailsForm:inputAllSalesTeam :custDetailsForm:viewreport" />-->
        <!---->
        <!--#3153 Sales Reports - Add gif for loading-->
        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;" id="custDetFilterPg">
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
            <h4 style="margin-top: 0px;margin-left: 18px;"> 
   <!--1833:CRM-2811: MerchantsSales: edit end user label-->
<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            #{custom.labels.get('IDS_DB_CUSTOMER')} Details: Report Filter</h4>
                            
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->
            <p:outputPanel rendered="#{leftReports.countMsgSalesReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>  
                            
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->               
            <div class="box-header with-border"  style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">
                        <!--#3153 Sales Reports - Add gif for loading-->

                        <div id="loading"  class="div-center" style="display:none;">

                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />
                        </div>  
                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                     style="min-height: 50px;margin-top:20px;width: 130%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >


                            <p:outputLabel  value="As of"   />
                             <!--4775: Sales and Commission Reports: Remember date range for session-->
                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" style="width: 100%;"  >
                                <p:ajax     event="change"   process="@this"    update=":custDetailsForm:viewreport" />
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <!--#330952 - THORSON: 2014 doesn't show - Added 10 years behind-->
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />

                            </p:selectOneMenu>
                            <div></div>
                             <!--4775: Sales and Commission Reports: Remember date range for session-->
                            <p:selectOneMenu height="340"   widgetVar="mt" value="#{reportFilters.repMonth}" style="width: 100%;" >
                                <p:ajax     event="change"    process="@this"  update=":custDetailsForm:viewreport" />
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 
                                <!--<p:ajax event="select" update=":frmDash"/>--> 
                            </p:selectOneMenu>
                            <!--                        </p:panelGrid>
                            
                                                    <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                                                 style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >-->
<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustName" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  styleClass="required"/>
                            
                            <h:panelGroup class="ui-inputgroup"  >

                                <!--#3473:     Bug sales team - Sales team data we can enter manually-->
                                <p:inputText id="inputCustName" value="#{reportFilters.custselect.compName}" widgetVar="cust" placeholder="[Select]"  readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCRMCust',2,0,1)}" oncomplete="PF('lookupComp').show()"
                                                  update=":formCompLookup" 
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <!--                            </p:panelGrid>:salesReport
                                                        <h4 style="margin-left:10px">Report Filter</h4>
                                                        <p:panelGrid id="grid1" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                                                     style="min-height: 50px;margin-top:20px;"       layout="grid" styleClass="box-primary no-border ui-fluid "  >-->

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                          
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <p:outputLabel for="inputDistriName" value="#{custom.labels.get('IDS_DISTRI')}" />
                           
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputDistriName" value="#{reportFilters.distributor.compName}" placeholder="All"   readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyDistri', 3, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>



                        </p:panelGrid>

                        <!--#3153 Sales Reports - Add gif for loading-->
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <!--#10213 CRM-6473  Customer master: quick link to customer summary report-->
                        <p:commandButton  value="View Report "  onclick="processGif();"  id="viewreport"   styleClass="btn btn-primary btn-xs" actionListener="#{custDetailRep.setRepcode(0)}"  action="#{custDetailRep.getproc()}" style="margin-left: 10px;"/>
                         

                    </div>
                </div>
            </div>
        </div>

        <!--#3153 Sales Reports - Add gif for loading-->
        <script>
            function processGif()
            {
                $('#loading').show();
                document.getElementById("custDetFilterPg").style.pointerEvents = "none";
                document.getElementById("custDetFilterPg").style.opacity = "0.4";
            }


            function hideGif()
            {
                document.getElementById("custDetFilterPg").style.pointerEvents = "all";
                document.getElementById("custDetFilterPg").style.opacity = "1";
                $('#loading').hide();
            }


        </script>
    </h:form>
    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />

    <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
    
   



</ui:composition>

