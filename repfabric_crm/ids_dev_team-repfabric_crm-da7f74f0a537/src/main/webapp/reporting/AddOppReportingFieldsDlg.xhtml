<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: AddOppReportingFieldsDlg.xhtml
// Author: Sarayu
//*********************************************************
* Copyright (c) 2017 Indea Design Systems Private Limited, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

   <h:form id="addOppReportingFieldsForm">

    <p:dialog  id="addOppReportingFieldsDlg" header="Add Field" resizable="false" styleClass="disable-scroll" width="520" height="510"   modal="true" widgetVar="dlgAddOppReportingFields" >  
        
            <p:dataTable value="#{reportingService.fieldList}" var="fieldType" 
                         scrollable="true" scrollHeight="500" style="width: 480px" id="label">
                <p:subTable var="fields" value="#{fieldType.fieldsList}" >
                    <f:facet name="header">
                        <h:outputText value="#{fieldType.fieldDispName}"/>
                    </f:facet>
                    <p:column style="height:3px">
                        
                        <p:commandLink value="#{fields.fieldDispName}" actionListener="#{oppRptMap.addOppReportField(fields,viewCompLookupService.selectedCompany.compId)}" oncomplete="PF('dlgAddOppReportingFields').hide();" update=":mappingForm:oppFieldsDT :mappingForm:rptBtn"/>
                    </p:column>
                </p:subTable>
            </p:dataTable>
       
    </p:dialog>
    </h:form>
 <!--Bug 1446 - #79630:Earleaz Partnumber not separated on line items-->
 <!--Bug CRM-45-->
    <p:dialog  id="OppReportingHelpDlg" header="Help" resizable="false" styleClass="disable-scroll" width="620" height="120"   modal="true" widgetVar="dlgOppReportingHelp" >  
        <h:form id="addOppReportingHelpForm">


            <table>
                <tr >
                    
                        <td style="text-align: left;font-weight: bold">
                            One #{custom.labels.get('IDS_OPP')} per row</td>
                </tr>
                <tr>
                   
                    <td>
                    - Select this option if you do not want break ups by #{custom.labels.get('IDS_LINE_ITEM')}. Only a list of comma separated #{custom.labels.get('IDS_LINE_ITEMS')} can be exported by adding "Combined #{custom.labels.get('IDS_LINE_ITEMS')}" to the report. 
                    </td> 
                   
                    </tr>
                <tr >
                  
                        <td style="text-align: left;font-weight: bold">
                            One Row per Line Item</td>
                </tr>
                 <td>
                    - Select this option to export one row per #{custom.labels.get('IDS_LINE_ITEM')} with option to select all the #{custom.labels.get('IDS_LINE_ITEM')} fields. In this case, the #{custom.labels.get('IDS_OPP')} will repeat for each #{custom.labels.get('IDS_LINE_ITEM')}.
                    </td> 

            </table>

        </h:form>
    </p:dialog>

</ui:composition>
