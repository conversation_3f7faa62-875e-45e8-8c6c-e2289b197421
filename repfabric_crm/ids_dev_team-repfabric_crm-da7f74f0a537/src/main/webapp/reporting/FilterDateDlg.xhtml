<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui">
    <h:form id = "filterForm">
        <p:dialog widgetVar="filterDateDlg" header="Filter #{commsMain.filterStr}" modal="true"   resizable="false" responsive="true">
            <p:ajax event="close" listener="#{commsMain.cancelFilter()}" />
              <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                         style="min-height: 50px"       layout="grid" styleClass="box-primary no-border ui-fluid ">

                <p:outputLabel value="From" />
              
                <p:calendar id="startDate" pattern="#{globalParams.dateFormat}"  value="#{commsMain.fromDate}"    showOn="button" style="width:220px"/>

                <p:outputLabel value="To" />
            
                <p:calendar id="endDate" pattern="#{globalParams.dateFormat}"  value="#{commsMain.toDate}"  showOn="button" />
              </p:panelGrid>
            <div class="div-center">
            <!--<p:commandButton value="Filter"  action="#{commsMain.populateCommRecords()}" oncomplete="if (args &amp;&amp; !args.validationFailed) PF('filterDateDlg').hide()" update=":commOverviewForm:fromDt   :commOverviewForm:toDt   :commOverviewForm:recordDT  :filterSummary " styleClass="btn btn-primary btn-xs"/>-->
             <p:commandButton value="Filter"  action="#{commsMain.populateCommRecords()}" oncomplete="if (args &amp;&amp; !args.validationFailed) PF('filterDateDlg').hide()" update=":commOverviewForm  :commOverviewForm:pnlView" styleClass="btn btn-primary btn-xs"/>
            <p:spacer width="4px"  />
            <p:commandButton value="Cancel"  actionListener="#{commsMain.cancelFilter()}" update=":commOverviewForm" oncomplete="PF('filterDateDlg').hide()" styleClass="btn btn-warning btn-xs"/>
            </div>
        </p:dialog>
    </h:form>
</ui:composition>