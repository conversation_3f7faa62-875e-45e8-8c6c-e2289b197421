<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: MappingPage.xhtml
// Author: Nisha
//*********************************************************
/*
*
* Copyright (c) 2015 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <h:form id="mappingForm">
        <!--Growl message-->
        <p:growl id="mapGrowl"/>
        <div class="button_bar">
            <div style="float: left;">
                <p:commandButton id="rptBtn" ajax="false" value="Generate Report" /><p:spacer width="3"/>
            </div>

            <!--            <div style="float: right;">
                            <p:outputPanel id="pnlButtons">
                                <p:commandButton class="button_top"  value="Add" actionListener="#{reportingService.populateFieldList()}"  /><p:spacer width="3"/>
                                <p:commandButton class="button_top" value="Edit" actionListener="#{reportingService.populateFieldList()}"  disabled="#{oppRptMap.selectedField == null}"/><p:spacer width="3"/>
                                <p:commandButton class="button_top" value="Delete" actionListener="#{oppRptMap.deleteField()}" >
                                    <p:confirm header="Confirmation" message="Are you sure to delete?" icon="ui-icon-alert"/>
                                </p:commandButton><p:spacer width="3"/>
                                <p:commandButton class="button_top" value="Move Up"  disabled="#{oppRptMap.selectedField == null}"/><p:spacer width="3"/>
                                <p:commandButton class="button_top" value="Move Down"   disabled="#{oppRptMap.selectedField == null}"/><p:spacer width="3"/>
                            </p:outputPanel>
                        </div>-->
        </div>
        <!--        <p:panelGrid   >
                    <p:row>
                        <p:column><p:outputLabel value="#{custom.labels.get('IDS_PRINCI')}" /></p:column>
                        <p:column><p:outputLabel value=":" class="m" /></p:column>
                        <p:column colspan="4">
        
                            <p:outputLabel value="#{oppRptMap.principal.compName}" style="font-weight: bold;font-size: 17px;color:blue;"/>
                        </p:column>
        
        
                    </p:row>
        
                    <p:row  >
        
                        <p:column style="width: 650px"><p:outputLabel value="Report Type  " /></p:column>
                        <p:column><p:outputLabel class="m"  value=":" /></p:column>
                        <p:column style="width: 1750px" >
                            <p:selectOneRadio id="reportType" value="#{reportingService.reportType}" disabled="#{oppRptMap.principal ==null}" style="font-size-adjust: " >
                                <p:ajax process="@this" listener="#{reportingService.onChangeReportType()}" update=":addOppReportingFieldsForm :updateOppReportingFieldsForm"/>
                                <f:selectItem itemLabel="One #{custom.labels.get('IDS_OPP')} per row" itemValue="1" itemEscaped="true"    >
        
                                </f:selectItem>
                                <f:selectItem itemLabel="One Row per #{custom.labels.get('IDS_LINE_ITEM')}"   itemValue="2" id="oneRowPerLineItem"  > 
        
                                </f:selectItem>
        
                            </p:selectOneRadio> 
                        </p:column>
                        <p:column>
        
                            <p:commandButton icon="ui-icon-help" oncomplete="PF('dlgOppReportingHelp').show();" style="width:17px;height:17px;vertical-align: top"/>   
                        </p:column>
        
                        <p:column  >
                            <p:outputLabel value="File Type  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :" style="float: right;width: 140px;" />
                        </p:column>
                        <p:column><p:outputLabel   class="m"  value=":" style="text-align: right"/></p:column>
                        <p:column >
                            <p:selectOneRadio id="expType" value="#{reportingService.fileType}" required="true"  requiredMessage="Select any file type">
                                <f:selectItem itemLabel="Excel" itemValue="3"/>
                                <f:selectItem itemLabel="CSV" itemValue="2"/>
                            </p:selectOneRadio>  
                        </p:column>
        
                    </p:row>
        
                    CRM -106   
                    <p:row>
        
                        <p:column>
                            <p:outputLabel value="Filter By "/></p:column>
                        <p:column><p:outputLabel styleClass="m" value=":"/></p:column>
        
                        <p:column> 
        
                            <p:selectOneMenu value="#{reportingService.filterby}" id="fb"   widgetVar="filterBy"  >
                                <f:selectItem   itemValue="1" itemLabel="Open and Reporting turned ON"  />                                   
                                <f:selectItem itemValue="2" itemLabel="Open/Closed and Reporting turned ON"/>
                                   // CRM-106 All(closed reasons and dates)
                                <f:selectItem itemValue="3" itemLabel="All Closed"/>
                                <f:selectItem itemValue="4" itemLabel="All"/>
                            </p:selectOneMenu>
        
                        </p:column>
                    </p:row>
                  CRM -106   
                    <p:row>
        
                        <p:column style="width: 1050px;" ><p:outputLabel value="Last Modified Start Date  "  style="width: 750px"/></p:column>
                        <p:column><p:outputLabel class="m" value=":" /></p:column>
                        <p:column > <p:calendar pattern="#{globalParams.dateFormat}"  value="#{reportingService.startDate}" style="float: left" >
                            </p:calendar></p:column>
                        <p:column style="width: 1050px;"><p:outputLabel value="Last Modified End Date" /></p:column>
                        <p:column><p:outputLabel class="m" value=":" /></p:column>
                        <p:column> <p:calendar pattern="#{globalParams.dateFormat}"  value="#{reportingService.endDate}" >
                            </p:calendar></p:column>
        
                    </p:row>
        
                </p:panelGrid>
        
                <p:dataTable value="#{oppRptMap.oppFieldsList}" var="oppField" id="oppFieldsDT" scrollable="true" scrollHeight="300px"
                             selectionMode="single" selection="#{oppRptMap.selectedField}" rowKey="#{oppField.recId}">
                    <p:ajax event="rowSelect" update=":mappingForm:pnlButtons"/>
                    <p:column headerText="Seq.#" style="width: 30px">
                        <h:outputText value="#{oppField.rptSeqNum}"/>
                    </p:column>
                    <p:column headerText="Field">
                        <h:outputText value="#{oppField.columnName}"/>
                    </p:column>
                    <p:column headerText="Type">
                        <h:outputText value="#{custom.labels.get('IDS_OPP')} (#{custom.labels.get('IDS_CUSTOM_FIELDS')})" rendered="#{oppField.rptColType == 'OC'}"/>
                        <h:outputText value="#{custom.labels.get('IDS_OPP')} (Standard)" rendered="#{oppField.rptColType == 'OR'}"/>
                        <h:outputText value="#{custom.labels.get('IDS_LINE_ITEM')} (#{custom.labels.get('IDS_CUSTOM_FIELDS')})" rendered="#{oppField.rptColType == 'LC'}"/>
                        <h:outputText value="#{custom.labels.get('IDS_LINE_ITEM')} (Standard)" rendered="#{oppField.rptColType == 'LR'}"/>
                    </p:column>
                </p:dataTable>
        
                <p:confirmDialog global="true" showEffect="fade" hideEffect="fade">
                    <p:commandButton value="Yes" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                    <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                </p:confirmDialog>
        
            </h:form>
            <p:dialog widgetVar="inProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Please wait...Exporting records" />
                        <br /><br />
                        <p:poll stop="#{oppReportingStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />
                    </p:outputPanel>
                </h:form>
            </p:dialog>
            <style>
                .ui-panelgrid tr, .ui-panelgrid td{
                    background-color: transparent!important;
                    border:0!important;
                }
                .ui-selectonemenu-label{
                    font-size:14px;
                }
        
            </style>
            <script type="text/javascript">
                function start() {
                    PF('inProgressDlg').show();
                }
                function stop() {
                    PF('inProgressDlg').hide();
                }
            </script>
        
            <p:confirmDialog severity="alert" id="confirmReportTypeDlg" showEffect="fade" hideEffect="fade" header="Confirmation" styleClass="disable-scroll"  widgetVar="dlgConfirmReportType" closable="false" >  
                <f:facet name="message">
                    <p:outputLabel value="#{custom.labels.get('IDS_LINE_ITEM')} fields will be deleted. Are you sure to proceed?" />
                </f:facet>
                <h:form id="confirmReportTypeForm">
                    <p:commandButton value="Yes" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" actionListener="#{reportingService.updateReportType()}" update=":mappingForm:oppFieldsDT :mappingForm:rptBtn" oncomplete="PF('dlgConfirmReportType').hide();"/>
                    <p:commandButton value="No" styleClass="ui-confirmdialog-no" icon="ui-icon-close" actionListener="#{reportingService.resetReportType()}" update=":mappingForm:reportType" oncomplete="PF('dlgConfirmReportType').hide();"/>
                </h:form>
            </p:confirmDialog>-->

</ui:composition>

