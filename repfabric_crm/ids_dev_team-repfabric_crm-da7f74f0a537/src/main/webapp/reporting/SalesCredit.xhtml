<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                 template="#{layoutMB.template}">

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
       <!--#13168 CRM-7652  Sales Credit Report needs to show ALL orders, not just those with Direct Commissions - add-->
        <title>
            Orders Credit Report
        </title>
    </ui:define>
    <ui:define name="meta">

        <f:metadata>
            <f:viewAction action="#{leftReports.setReportsFilterData('0')}"/>
        </f:metadata>
    </ui:define>
     <!--#13168 CRM-7652  Sales Credit Report needs to show ALL orders, not just those with Direct Commissions - add-->
    <ui:define name="title"> Orders Credit: Report Filter

    </ui:define>
    <ui:define name="body">
        <h:form id="directSalesForm">

            <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="directSalesForm:inputPrincName"   />

            <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="directSalesForm:inputCustomer" />       


            <div class="box box-info box-body" style="vertical-align: top;height:100vh;box-shadow: none;" id="crmDirectSales">
                <h3 style="margin-top:0px;margin-left: 18px;"></h3>
                <div class="box-header with-border"  style="border: 0px;">
                    <div class="row">
                        <div class="col-md-5">
                            <p:panelGrid id="grid" columns="3" columnClasses="ui-grid-col-10,ui-grid-col-10"
                                         style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >

                                <p:outputLabel  value="From Date"/>


                                <h:panelGroup>

                                    <p:calendar   value="#{reportFilters.fdate}" showOn="button" pattern="#{globalParams.dateFormat}" id="fmdt" converterMessage="Please enter the valid From date">

                                        <p:ajax  event="keyup"  process="@this" />
                                        <p:ajax event="dateSelect" process="@this"  />
                                    </p:calendar>
                                </h:panelGroup>

                                <p:outputLabel/>

                                <p:outputLabel  value="To Date"/>

                                <h:panelGroup >

                                    <p:calendar value="#{reportFilters.tdate}" showOn="button" pattern="#{globalParams.dateFormat}" id="todt" widgetVar="todate" converterMessage="Please enter the valid To date">
                                        <p:ajax  event="keyup"  process="@this" />
                                        <p:ajax event="dateSelect" process="@this"  />
                                    </p:calendar>
                                </h:panelGroup>
                                <p:outputLabel/>

                                <p:outputLabel value="Sales Rep"  />
                                <!--#13092   ESCALATIONS CRM-7761   Sales Credit Report: Gave user who is a "Manager" report visibility to all us-->
                                <!--#12840 ESCALATIONS CRM-7652   Sales Credit Report needs to show ALL orders, not just those with Direct Comm-->
                                <h:panelGroup class="ui-inputgroup" style="width:100%" >
                                    <h:selectOneListbox   styleClass="sel-users"  size="2" style="width:-webkit-fill-available" id="selUsers">
                                        <f:selectItems value="#{reportFilters.users}" var="users"  
                                                       itemValue="#{users.userId}"
                                                       itemLabel="#{users.userName}"  />
                                    </h:selectOneListbox>
                                    <!--#13092   ESCALATIONS CRM-7761   Sales Credit Report: Gave user who is a "Manager" report visibility to all us-->
                                    <!--#12840 ESCALATIONS CRM-7652   Sales Credit Report needs to show ALL orders, not just those with Direct Comm-->
                                    <p:commandButton icon="fa fa-search" process="@this"  styleClass="btn-info btn-xs"  title="Choose Sales Rep"  action="#{lookupService.listSalesRep()}" update=":frmUsersLookup"  oncomplete="PF('dlgUsersLookup').show();" />


                                </h:panelGroup>
                                <!--#13092   ESCALATIONS CRM-7761   Sales Credit Report: Gave user who is a "Manager" report visibility to all us-->
                                <!--#12840 ESCALATIONS CRM-7652   Sales Credit Report needs to show ALL orders, not just those with Direct Comm-->
                                <p:commandLink styleClass="sel-users"  value="Clear" actionListener="#{lookupService.clear('USER')}" update="@(.sel-users)" disabled="#{!(reportFilters.users!=null and reportFilters.users.size() != 0)}" style="text-decoration:underline;margin-left:-10px "/>

                                <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                                <h:panelGroup class="ui-inputgroup"  >

                                    <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                      update=":formCompLookup" actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                      oncomplete="PF('lookupComp').show();"  
                                                      styleClass="btn-info btn-xs" />
                                </h:panelGroup>
                                <p:outputLabel/>

                                <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_CUSTOMER')}"  />
                                <h:panelGroup class="ui-inputgroup"  >

                                    <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                      update=":formCompLookup"  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                      oncomplete="PF('lookupComp').show();"  
                                                      styleClass="btn-info btn-xs" />
                                </h:panelGroup>
                                <p:outputLabel/>





                            </p:panelGrid>

                            <p:panelGrid id="grid1" columns="3" columnClasses="ui-grid-col-5,ui-grid-col-7"
                                         style="min-height:30px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >


                                <p:outputLabel value="Group by Sales Rep"/>
                                <!--          #10300  CRM-6433:-->
                                <p:selectBooleanCheckbox id="grpby" value="#{mainSalesCreditRep.groupBy}"  widgetVar="grp">
                                    <p:ajax event="change" update="directSalesForm:grpby" />
                                </p:selectBooleanCheckbox>

                            </p:panelGrid>

                            <p:commandButton process="@this"   value="View Report "   id="viewreport" action="#{mainSalesCreditRep.gotTonavigationpage()}"    styleClass="btn btn-primary btn-xs"  style="margin-left: 10px;"/>
                        </div>
                    </div>
                </div>

            </div>

        </h:form>
        <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
        <ui:include src="/lookup/UsersLookupDlg.xhtml"/>
    </ui:define>  
</ui:composition>
