<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: Commission Summary.xhtml
// Author: Sharvani
//*********************************************************
/*
*..
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 
    <h:form id="frm1" >
        <!--//#3410:Bug Comm.Reports > loading Issue when date entered manually-->

        <p:growl id="calValidator"  />
        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;"   id="salesRepFilterPg">
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
            <h4 style="margin-top:0px;margin-left: 18px;">     Sales Rep Details : Report Filter</h4>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->           
            <p:outputPanel rendered="#{leftCommission.countMsgCommReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border" style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">
                        <div id="loading"  class="div-center" style="display:none;">
                            <!--#3249:#3249:  .   Task Reporting > Commission Reports > Add loading gif-->


                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />
                            <!--<img id="loading-image" src="images/ajax-loader.gif" alt="Loading..." />-->
                        </div>  
                        <p:remoteCommand autoRun="true" update=":frm1:salRepDet" />    
                        <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}"  update="frm1:inputPrincName :frm1:viewreport" />
                        <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}"  update="frm1:inputAllSalesTeam :frm1:viewreport"/>
                        <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="frm1:inputCustomer :frm1:viewreport :frm1:inputAllSalesTeam"/>       
                        <p:remoteCommand name="applyUser" actionListener="#{reportFilters.applySalesRep(userLookupService.user)}" update="frm1:inputUserName :frm1:viewreport"/>  



                        <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid " id="salRepDet">
                            <!-- //Bug #6787 commission reports ->date blank issue-->
                            <p:outputLabel value="From"  class="required" />
                            <h:panelGroup>
                                <!--#3409:    Bug Comm.Reports > Date format Issue-->
                                <!--//#3410:Bug Comm.Reports > loading Issue when date entered manually-->
                                <!--#4775:CRM-4398: Sales and Commission Reports: Remember date range for session-->
                                <p:calendar value="#{reportFilters.fdate}" showOn="button" pattern="#{globalParams.dateFormat}" id="fmdt" converterMessage="Please enter the valid From date">
                             
                             <p:ajax  event="keyup"  process="@this" />
                              <p:ajax event="dateSelect" process="@this"  />
                                </p:calendar>
                            </h:panelGroup>
                             <!-- //Bug #6787 commission reports ->date blank issue-->
                            <p:outputLabel value="To"  class="required" />
                            <h:panelGroup>
                                <!--#3409:    Bug Comm.Reports > Date format Issue-->
                                <!--//#3410:Bug Comm.Reports > loading Issue when date entered manually-->
           <!--#4775:CRM-4398: Sales and Commission Reports: Remember date range for session-->
                                <p:calendar value="#{reportFilters.tdate}" showOn="button" pattern="#{globalParams.dateFormat}" id="todt" widgetVar="todate" converterMessage="Please enter the valid To date">
                                <p:ajax  event="keyup"  process="@this" />
                                 <p:ajax event="dateSelect" process="@this"  />
                                </p:calendar>
                            </h:panelGroup>
                           
                            <p:outputLabel for="inputUserName" value="Sales Rep"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputUserName" value="#{reportFilters.salesRep.userName}"  readonly="true"/>
                                <!--/Bug 1948: CRM-1131: reports: salesrep comm" cwrreps - salesman can see other salesman commissions!-->
                                <!--// Dashbaord commission reports-->

                                <p:commandButton  icon="fa fa-search" title="Choose Sales Rep" immediate="true" 
                                                  actionListener="#{userLookupService.listSalesRep('applyUser', 'Sales Rep')}"
                                                  update=":formUserLookup" oncomplete="PF('lookupUser').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup :frm1" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                 <!--#3473:     sales team - Sales team data we can enter manually-->

                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true" />
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                  update=":formSalesTeamLookup"  oncomplete="PF('lookupSalesTeam').show()"  
                                                  styleClass="btn-info btn-xs"  style="width:67px" />
                            </h:panelGroup>
                            <p:outputLabel value="Group by"/>
                             <!--          #10300   CRM-6433:-->
                            <p:selectOneRadio value="#{reportFilters.grp}"  widgetVar="grp" layout="pageDirection"  required="true">
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/> 
                                <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <f:selectItem   itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                                <f:selectItem itemValue="3" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}"/>


                            </p:selectOneRadio>
                            <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->
                           <!--          #10300  CRM-6433:-->
                            <p:outputLabel value="Show Credited Sales"  />

                            <p:selectBooleanCheckbox  itemLabel="#{reportFilters.showCreditSaleFlag ? '':''}"  value="#{reportFilters.showCreditSaleFlag}"     id="chkGoogleSync" widgetVar="chk">


                            </p:selectBooleanCheckbox>
                            
                            
                        </p:panelGrid>

                        <!--#3249:#3249:     Task Reporting > Commission Reports > Add loading gif-->

                        <!--//#3410:Bug Comm.Reports > loading Issue when date entered manually-->
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <p:commandButton  value="View Report "  id="viewreport"  actionListener="#{repDetailItem.gotTonavigationpage()}" styleClass="btn btn-primary btn-xs"  update="calValidator"  style="margin-left: 10px;"/>




                    </div>
                </div>
            </div>
        </div>

        <!--#3249:#3249:     Task Reporting > Commission Reports > Add loading gif-->

        <script>
//#3410:Bug Comm.Reports > loading Issue when date entered manually

            function filterProcessGif()
            {
                $('#loading').show();
                document.getElementById("salesRepFilterPg").style.pointerEvents = "none";
                document.getElementById("salesRepFilterPg").style.opacity = "0.7";
//            $('#ui-growl-title').hide()
//            {
//                
//            }

            }

            function hideGif()
            {

                document.getElementById("salesRepFilterPg").style.pointerEvents = "all";
                document.getElementById("salesRepFilterPg").style.opacity = "1";
                $('#loading').hide();
            }




            window.onload = function () {
//             
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
                    document.getElementById("salesRepFilterPg").style.pointerEvents = "all";
                    document.getElementById("salesRepFilterPg").style.opacity = "1";
                    $('#loading').hide();
                }, 0);
            };

        </script>



    </h:form>
    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
    <ui:include src="/lookup/UserLookupDlg.xhtml"  />
    <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
    <ui:include src="/lookup/PartNumDlg.xhtml"/>
</ui:composition>