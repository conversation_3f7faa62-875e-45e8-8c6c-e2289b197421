<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: Commission Summary.xhtml
// Author: Sharvani.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 
    <h:form id="commSummForm">
        <!--//#3410:Bug Comm.Reports > loading Issue when date entered manually-->

        <p:growl id="commsg" />
        <p:remoteCommand autoRun="true"  update=":commSummForm:grid" />
        <!--//    #1148: Commission Reports > Do not default to Customer Sales team-->
        <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyOnlyCustomer(viewCompLookupService.selectedCompany)}" update=":commSummForm:inputCustomer  :commSummForm:viewreport"/>
        <!--<p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update="prodDetForm:inputDistriName  :prodDetForm:viewreport" />-->
        <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update=":commSummForm:inputPrincName  :commSummForm:viewreport"  />
        <!--<p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update=":commSummForm:inputAllSalesTeam :commSummForm:viewreport" />-->
        <!--<p:remoteCommand name="applyProd" actionListener="#{reportFilters.applyProduct(viewProductLookUpService.selectedProduct)}" update="prodDetForm:inpTxtProdName" />-->

        <!--#3249: Task Reporting > Commission Reports > Add loading gif-->
        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;" id="commDetfilterPg" >
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
    <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
            <h4 style="margin-top:0px;margin-left: 18px;">     #{custom.labels.get('IDS_COMMISSION')} Details : Report Filter</h4>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->           
            <p:outputPanel rendered="#{leftCommission.countMsgCommReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border"  style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">


                        <div id="loading"  class="div-center" style="display:none;">
                            <!--#3249: Task Reporting > Commission Reports > Add loading gif-->


                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />
                            <!--<img id="loading-image" src="images/ajax-loader.gif" alt="Loading..." />-->
                        </div>   





                        <!--                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-6"
                                                             style="width: 150%"   layout="grid">-->
                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-3,ui-grid-col-6"
                                     style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >
                            <!-- //Bug #6787 commission reports ->date blank issue-->
                            <p:outputLabel  value="From"   class="required"  />


                            <p:calendar size="10" showOn="button" 
                                        value="#{reportFilters.fdate}" 
                                        pattern="#{globalParams.dateFormat}"  id="fmDt"  converterMessage="Please enter valid From date">
                           <p:ajax  event="keyup"  process="@this" />
                                      <p:ajax event="dateSelect" process="@this"  />
                            </p:calendar>
                            <!-- //Bug #6787 commission reports ->date blank issue-->
                            <p:outputLabel  value="To"   class="required"  />

                            <p:calendar size="10" showOn="button"  
                                        value="#{reportFilters.tdate}" 
                                        pattern="#{globalParams.dateFormat}"   id="toDt" converterMessage="Please enter valid To date" >
                                <p:ajax  event="keyup"  process="@this" />
                                      <p:ajax event="dateSelect" process="@this"  />
                            </p:calendar>





                            <!--                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                                                 style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >                            -->
                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />

                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <p:outputLabel  value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
<!--                            <h:panelGroup class="ui-inputgroup"  >
                                #3473:     sales team - Sales team data we can enter manually

                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                                  styleClass="btn-info btn-xs"  style="width:67px" />
                            </h:panelGroup>-->
  <h:panelGroup class="ui-inputgroup" style="width:110%" >
                                <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width: 100%;" id="selSalesTeam" >
                                    <f:selectItems value="#{lookupService.salesTeams}" var="team"  
                                                   itemValue="#{team.smanId}"
                                                   itemLabel="#{team.smanName}"  />
                                </h:selectOneListbox>
                                <!--//  1741  CRM-3259: Sales team:Thea: prevent users from seeing Sales and Comms -  Dashboard Sales Team Lookup-->

                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.crmFlag()}"   action="#{lookupService.list('SALES_TEAM')}"   update=":frmSalesTeamLookup"  style="width:70px" oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                &nbsp;&nbsp;&nbsp;
                                <p:commandLink styleClass="sel-salesteam" value="Clear" actionListener="#{lookupService.clear('SALES_TEAM')}" update="@(.sel-salesteam)" disabled="#{!(lookupService.salesTeams!=null and lookupService.salesTeams.size() != 0)}" style="text-decoration:underline"/>
                            </h:panelGroup>
                            <p:outputLabel value="Group by"/>

                            <p:selectOneRadio value="#{commitems.grp}"  widgetVar="grp" layout="pageDirection"  required="true">

                                <p:ajax  update=":commSummForm:subGrpFlg" />
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/> 
                                <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <f:selectItem itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                                <f:selectItem itemValue="3" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}"/> 

                            </p:selectOneRadio>
                            <!--                Report type Radio Button-->
                            <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                            <p:outputLabel value="Show Credited Sales"  />

                            <p:selectBooleanCheckbox  itemLabel="#{commitems.checkFlag ? '':''}"  value="#{commitems.checkFlag}"     id="chkGoogleSync" widgetVar="chk">

 <!--Feature #5262CRM-4555: Somewhat urgent report needed for JD -->
                            </p:selectBooleanCheckbox>

                            <p:outputLabel value="Sub Group by Region"  />

                            <p:selectBooleanCheckbox  itemLabel="#{commitems.subGrpFlag ? '':''}"  value="#{commitems.subGrpFlag}"     id="subGrpFlg" widgetVar="flagChk" disabled="#{commitems.grp != 1}">

                              
                            </p:selectBooleanCheckbox>


                        </p:panelGrid>
                        <!--//#3410:Bug Comm.Reports > loading Issue when date entered manually-->
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <p:commandButton  value="View Report"  id="viewreport"   styleClass="btn btn-primary btn-xs"  actionListener="#{commitems.goToNavigationpage()}"    update="commsg" style="margin-left: 10px"/>

                    </div>
                </div>
            </div>
        </div>


        <!--#3249:   Task Reporting > Commission Reports > Add loading gif-->

        <script>
            function filterProcessGif()
            {
                $('#loading').show();
                document.getElementById("commDetfilterPg").style.pointerEvents = "none";
                document.getElementById("commDetfilterPg").style.opacity = "0.4";
//                var frmdate = document.getElementById("fmDt").value;
//                var todate = document.getElementById("toDt").value;
            }
//#3410:Bug Comm.Reports > loading Issue when date entered manually

            function hideGif()
            {
                document.getElementById("commDetfilterPg").style.pointerEvents = "all";
                document.getElementById("commDetfilterPg").style.opacity = "1";
                $('#loading').hide();
            }


            $(".ui-growl").on("click", function () {
                hideGif();
//                navigateToMessage([{name: 'param', value: messageId}]);
            });


        </script>  

        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->


    </h:form>

    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
    <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
   <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>
</ui:composition>