<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: SalesCommissionComp.xhtml
// Author: priyadarshini
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 
    <h:form id="comCompForm">
        <!--Task #456: commission reports->sales/comm comparision year update isuue-->
        <p:remoteCommand  autoRun="true"  update=":comCompForm:grid" />
        <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="comCompForm:inputCustomer "/>

        <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="comCompForm:inputPrincName "  />
        <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="comCompForm:inputAllSalesTeam " />

        <!--#3249: Task Reporting > Commission Reports > Add loading gif-->
        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;"   id="comCompFilterPg" >
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
        <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
            <h4 style="margin-top:0px;margin-left: 18px;"> #{custom.labels.get('IDS_COMMISSION')} Comparison : Report Filter</h4>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->           
            <p:outputPanel rendered="#{leftCommission.countMsgCommReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel> 
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border"  style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">

                        <div id="loading"  class="div-center" style="display:none;">
                            <!--#3249: Task Reporting > Commission Reports > Add loading gif-->


                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />

                        </div>   




                      <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added margin left-->
                        <p:panelGrid id="grid" columns="3" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;margin-top:20px;width: 220%;margin-left: 10px"       layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <!--#4775:CRM-4398: Sales and Commission Reports: Remember date range for session-->

                            <!--Task #456: commission reports->sales/comm comparision year update isuue-->
                            <p:outputLabel  value="As of"   />

                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" style="width: 100%;"  >
                                <p:ajax     event="change" process="@this" />
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <!--#330952 - THORSON: 2014 doesn't show - Added 10 years behind-->
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />

                            </p:selectOneMenu>
                              <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                            <!--  <p:outputLabel  value="Month"   />-->
                            <!--                            <div></div>-->
                            <p:outputLabel  />

                            <!--#4775:CRM-4398: Sales and Commission Reports: Remember date range for session-->

                            <!--Task #456: commission reports->sales/comm comparision year update isuue-->
                              <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                            <p:outputLabel  />
                            <p:selectOneMenu height="340"   widgetVar="mt" value="#{reportFilters.repMonth}" style="width: 100%;" >
                                <p:ajax     event="change" process="@this" />

                                <!--Bug #5895:  CRM-4795: Comparison report - Where is January?-->
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 
                                <!--<p:ajax event="select" update=":frmDash"/>--> 
                            </p:selectOneMenu>
                              <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                            <p:outputLabel  />
                            <!--                        </p:panelGrid>
                            
                            
                            
                                                    <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                                                 style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >-->


  <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                              <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                            <p:outputLabel  />

                              
                            <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                              <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                            <p:outputLabel  />

                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--#3473:     sales team - Sales team data we can enter manually-->
                                <!--                                 Bug #6044 sales/comm comparision report->sales team look up displaying blank data-->
                                <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  id="saleTeamLookUpbtn"
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()" 
                                                  styleClass="btn-info btn-xs"  />
                            </h:panelGroup>
                              <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                            <p:outputLabel  />

                            <p:outputLabel value="#{custom.labels.get('IDS_REGION')}"  />
                            <h:panelGroup class="ui-inputgroup" >
                                <h:selectOneListbox  styleClass="sel-region"  size="2" style="width: 650%" id="selRegion"     >
                                    <f:selectItems value="#{lookupService.compregionList}" var="reg"  
                                                   itemValue="#{reg.recId}"
                                                   itemLabel="#{reg.compRegion}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_REGION')}" actionListener="#{lookupService.list('REGION')}" update=":frmCompRegionLookup" oncomplete="PF('dlgRegionLookup').show();" style="width:70px" />

                            </h:panelGroup>
                              <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                            <p:commandLink styleClass="sel-region" value="Clear" actionListener="#{lookupService.clear('REGION')}" update="@(.sel-region)" disabled="#{!(lookupService.compregionList != null and lookupService.compregionList.size() != 0)}" style="text-decoration:underline" />


                            <p:outputLabel value="Group by"/>

                            <p:selectOneRadio value="#{salCommComparisonRep.grp}"  widgetVar="grp17" layout="pageDirection"  required="true">
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/> 
                                <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <f:selectItem   itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/> 

                            </p:selectOneRadio>
                              <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                            <p:outputLabel  />

                            <p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')} break up"  />

                            <p:selectBooleanCheckbox  itemLabel="#{salCommComparisonRep.checkFlag ? '':''}"  value="#{salCommComparisonRep.checkFlag}"     id="chkGoogleSync" widgetVar="chk">


                            </p:selectBooleanCheckbox>
                              <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                            <p:outputLabel  />

                            <!--//    CRM-4285: JOHN: Removing INACTIVE Manufacturer from reporting-->
                            <p:outputLabel value="Exclude Inactive #{custom.labels.get('IDS_PRINCI')}"  />
                            <p:selectBooleanCheckbox  itemLabel="#{salCommComparisonRep.excludeInactiveFlg ? '':''}"  
                                                      value="#{salCommComparisonRep.excludeInactiveFlg}"     id="excInactivPrinc" 
                                                      widgetVar="excludeInactveFlg">


                            </p:selectBooleanCheckbox>
                              <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                             <p:outputLabel  />

                            
                        </p:panelGrid>  
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <p:commandButton  value="View Report "  id="viewreport"   styleClass="btn btn-primary btn-xs"  actionListener="#{salCommComparisonRep.goTonavigatePage()}"  onclick="processGif();"   style="width:30px;margin-left: 10px" />

                    </div>
                </div>
            </div>
             <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
            <style>
                .ui-panelgrid .ui-panelgrid-cell{
                    padding: 4px 2px !important;
                }
            </style>

            <script>
                function processGif()
                {
                    $('#loading').show();
                    document.getElementById("comCompFilterPg").style.pointerEvents = "none";
                    document.getElementById("comCompFilterPg").style.opacity = "0.4";
                }






            </script>  




        </div>
    </h:form>
    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
    <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
 <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
    <ui:include src="/lookup/CompanyRegionLookUp.xhtml" />

</ui:composition>