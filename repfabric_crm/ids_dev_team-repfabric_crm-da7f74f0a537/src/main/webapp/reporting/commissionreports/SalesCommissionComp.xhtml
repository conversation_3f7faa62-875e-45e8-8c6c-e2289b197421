<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: SalesCommissionComp.xhtml
// Author: priyadarshini
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 
    <h:form id="salcommForm">
        <!--//  #2421: Updates to Sales/Comm By Month Report-->
       
<!--Task #456: commission reports->sales/comm comparision year update isuue-->
        <p:remoteCommand autoRun="true" update=":salcommForm:grid" />

        <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="salcommForm:inputCustomer  salcommForm:saleTeamLookUpbtn "/>

        <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="salcommForm:inputPrincName "  />
        <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="salcommForm:inputAllSalesTeam " />

        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;"  id="salCommissionCompFilterPg">
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
          <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23--> 
            <h4 style="margin-top:0px;margin-left: 18px;"> Sales/#{custom.labels.get('IDS_COMMISSION')} by Month : Report Filter</h4>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->           
            <p:outputPanel rendered="#{leftCommission.countMsgCommReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border"  style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">

                        <div id="loading"  class="div-center" style="display:none;">
                            <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->


                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />
                            <!--<img id="loading-image" src="images/ajax-loader.gif" alt="Loading..." />-->
                        </div>   




<!--Bug #5738: Sales Comm by month: Option to print by Region by Manfacturer-->
                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7"
                                     style="min-height: 50px;margin-top:20px;width: 100%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >



                            <p:outputLabel  value="As of"   />
<!--Task #456: commission reports->sales/comm comparision year update isuue-->
<p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" style="width: 100%;"  >
                                <p:ajax     event="change"   />

                                <!--#2492:  CRM-1549: Fwd: Sales/Commissions by Month-->
                                <f:selectItem itemLabel="#{reports.repYear+1}" itemValue="#{reports.repYear+1}" />
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <!--#330952 - THORSON: 2014 doesn't show - Added 10 years behind-->
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />

                            </p:selectOneMenu>

                            <!--
                                                        <p:selectOneMenu height="340"   widgetVar="mt" value="#{reports.repMonth}" style="width: 90%;margin-left: -15px" >
                                                            <p:ajax     event="change"  />
                                                            <f:selectItem itemLabel="January" itemValue="1" />
                                                            <f:selectItem itemLabel="February" itemValue="2" />
                                                            <f:selectItem itemLabel="March" itemValue="3" />
                                                            <f:selectItem itemLabel="April" itemValue="4" /> 
                                                            <f:selectItem itemLabel="May" itemValue="5" /> 
                                                            <f:selectItem itemLabel="June" itemValue="6" /> 
                                                            <f:selectItem itemLabel="July" itemValue="7" /> 
                                                            <f:selectItem itemLabel="August" itemValue="8" /> 
                                                            <f:selectItem itemLabel="September" itemValue="9" /> 
                                                            <f:selectItem itemLabel="October" itemValue="10" /> 
                                                            <f:selectItem itemLabel="November" itemValue="11" /> 
                                                            <f:selectItem itemLabel="December" itemValue="12" /> 
                                                            <p:ajax event="select" update=":frmDash"/> 
                                                        </p:selectOneMenu>-->

                            <!--                        </p:panelGrid>
                            
                                                    <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                                                 style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >-->



                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>



<!--                                <p:outputLabel for="inputSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                                <h:panelGroup class="ui-inputgroup"  >
                                    <p:inputText id="inputSalesTeam" value="#{example.salesTeam.smanName}"  />
                                    <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_SALES_TEAM')}" immediate="true" 
                                                      actionListener="#{salesTeamLookupService.list('applySalesTeam',0)}" 
                                                      update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                                      styleClass="btn-info btn-xs" />
                                </h:panelGroup>-->

                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                 <!--#3473:     sales team - Sales team data we can enter manually-->

                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true" />
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  id="saleTeamLookUpbtn"
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  style="width:67px"
                                                  styleClass="btn-info btn-xs"  disabled="#{reportFilters.customer.compId >0}"/>
                            </h:panelGroup>
<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->

                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <p:outputLabel value="Summary by"/>

                            <p:selectOneRadio value="#{monthlyExpandedRep.repgrp}"  widgetVar="grp16" layout="pageDirection"  required="true">
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/>                                                
                                <f:selectItem itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                               <!--//Bug #5738: Sales Comm by month: Option to print by Region by Manfacturer-->
                                <p:ajax  listener="#{monthlyExpandedRep.setRegFlag()}"  event="change"  update=":salcommForm:regFirstFlg"   />
                            </p:selectOneRadio>
                            
                            <!--//5330 :Sales and Commission by Month: Include region breakup-->
                              <p:outputLabel value="Sub Group by Region"  />

                              <p:selectBooleanCheckbox  itemLabel="#{monthlyExpandedRep.subGrpFlag ? '':''}"  value="#{monthlyExpandedRep.subGrpFlag}"     id="subGrpFlg" widgetVar="salComFlagChk" >
                                  <p:ajax    listener="#{monthlyExpandedRep.setRegFlag()}"  update="salcommForm:regFirstFlg"  />

                            </p:selectBooleanCheckbox>
                              <!--//Bug #5738: Sales Comm by month: Option to print by Region by Manfacturer-->
                               <p:outputLabel value="Region First"  />

                               <p:selectBooleanCheckbox  itemLabel="#{monthlyExpandedRep.regFirstFlag ? '':''}"  value="#{monthlyExpandedRep.regFirstFlag}"     id="regFirstFlg" widgetVar="regFirstFlagChk"    disabled="#{!monthlyExpandedRep.subGrpFlag ||  monthlyExpandedRep.repgrp==2}">
                                  <!--<p:ajax   update=":salcommForm:grid"  />-->

                            </p:selectBooleanCheckbox>
                              
                              

                        </p:panelGrid>
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <p:commandButton  value="View Report "  id="viewreport"   styleClass="btn btn-primary btn-xs"  actionListener="#{monthlyExpandedRep.init()}" onclick="processGif();" style="margin-left: 10px"/>
                    </div>
                </div>
            </div> 
        </div>


        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

        <script>
            function processGif()
            {
                $('#loading').show();
                document.getElementById("salCommissionCompFilterPg").style.pointerEvents = "none";
                document.getElementById("salCommissionCompFilterPg").style.opacity = "0.4";
            }
            
                   function hideGif()
            {
                document.getElementById("salCommissionCompFilterPg").style.pointerEvents = "all";
                document.getElementById("salCommissionCompFilterPg").style.opacity = "1";
                $('#loading').hide();
            }

            
            

        </script>  



    </h:form>
    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
    <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />

</ui:composition>