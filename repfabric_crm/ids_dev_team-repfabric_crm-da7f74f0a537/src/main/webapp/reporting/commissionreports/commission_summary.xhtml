<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: Commission Summary.xhtml
// Author: Sharvani
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 
    <h:form id="commSummForm">
        <!--//#3410:Bug Comm.Reports > loading Issue when date entered manually-->

        <p:growl  id="commsummsg"   />
        <p:remoteCommand autoRun="true" update=":commSummForm:grid" />
        <!--//    #1148: Commission Reports > Do not default to Customer Sales team-->
        <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyOnlyCustomer(viewCompLookupService.selectedCompany)}" update=":commSummForm:inputCustomer  :commSummForm:viewreport"/>
        <!--<p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update="prodDetForm:inputDistriName  :prodDetForm:viewreport" />-->
        <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update=":commSummForm:inputPrincName  :commSummForm:viewreport"  />
        <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update=":commSummForm:inputAllSalesTeam :commSummForm:viewreport" />
        <!--<p:remoteCommand name="applyProd" actionListener="#{reportFilters.applyProduct(viewProductLookUpService.selectedProduct)}" update="prodDetForm:inpTxtProdName" />-->
        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;" id="commSumFilterPg">

            <!--Task #2419Commission Summary > Summarize by Parent - UI Changes-->
            <br/>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
        <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
            <h4 style="margin-top:-18px;margin-left: 18px">     #{custom.labels.get('IDS_COMMISSION')} Summary : Report Filter</h4>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->           
            <p:outputPanel rendered="#{leftCommission.countMsgCommReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border"  style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">


                        <div id="loading"  class="div-center" style="display:none;">
                            <!--#3249: Task Reporting > Commission Reports > Add loading gif-->


                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />
                            <!--<img id="loading-image" src="images/ajax-loader.gif" alt="Loading..." />-->
                        </div>   



                        <!--<p:panelGrid columns="3"   layout="grid"   style="width:120%" >-->
                        <!--Task #2419Commission Summary > Summarize by Parent - UI Changes-->
                           <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added margin left-->
                        <p:panelGrid id="grid" columns="3" columnClasses="ui-grid-col-5,ui-grid-col-7"
                                     style="min-height: 50px;margin-top:20px;width: 215%;margin-left: 10px"       layout="grid" styleClass="box-primary no-border ui-fluid "  >   
                            <p:outputLabel  value="From"   class="required"   />

                            <!--//#3410:Bug Comm.Reports > loading Issue when date entered manually-->
                            <!--                            //Bug #6787 commission reports ->date blank issue-->
                            <p:calendar size="10" showOn="button" 
                                        value="#{reportFilters.fdate}" 
                                        pattern="#{globalParams.dateFormat}"  id="frmDt" converterMessage="Please enter valid From Date">
                                <p:ajax event="keyup"   process="@this"/>

                                <p:ajax event="dateSelect" process="@this"  />
                            </p:calendar>
                               <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
                            <p:outputLabel/>
                            <!-- //Bug #6787 commission reports ->date blank issue-->
                            <p:outputLabel  value="To"   class="required"   />

                            <p:calendar size="10" showOn="button"  
                                        value="#{reportFilters.tdate}" 
                                        pattern="#{globalParams.dateFormat}"  id="toDt"  converterMessage="Please enter valid  To Date"  >

                                <p:ajax event="keyup"   process="@this"/>

                                <p:ajax event="dateSelect" process="@this"  />
                            </p:calendar>
                               <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
                              <p:outputLabel/>



                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />

                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                               <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
                            <p:outputLabel/>

                            <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->


                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                               <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
                            <p:outputLabel/>


                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--#3473:     sales team - Sales team data we can enter manually-->
         <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                                  styleClass="btn-info btn-xs"  />
                            </h:panelGroup>
                               <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
                            <p:outputLabel/>

         <!--#8516   CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
                            <p:outputLabel value="#{custom.labels.get('IDS_REGION')}"  />
                            <h:panelGroup class="ui-inputgroup" >
                                <h:selectOneListbox  styleClass="sel-region"  size="2" style="width: 570%" id="selRegion"     >
                                    <f:selectItems value="#{lookupService.compregionList}" var="reg"  
                                                   itemValue="#{reg.recId}"
                                                   itemLabel="#{reg.compRegion}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_REGION')}" actionListener="#{lookupService.list('REGION')}" update=":frmCompRegionLookup" oncomplete="PF('dlgRegionLookup').show();" style="width:70px" />
                               
                            </h:panelGroup>
                               <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->

                            <p:commandLink styleClass="sel-region" value="Clear" actionListener="#{lookupService.clear('REGION')}" update="@(.sel-region)" disabled="#{!(lookupService.compregionList != null and lookupService.compregionList.size() != 0)}" style="text-decoration:underline" />



                            <p:outputLabel value="Group by"/>

                            <p:selectOneRadio value="#{commSummary.grp}"  widgetVar="grp" layout="pageDirection"  required="true">
<!--                                <f:selectItem   itemValue="0" itemLabel="#{custom.labels.get('IDS_CUSTOMER')}"/>                                   
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_DISTRI')}"/>
                                <f:selectItem itemValue="2" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/>-->

                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/> 
                                <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <f:selectItem itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                                <f:selectItem itemValue="3" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}"/> 
                                <!---Feature #2295:Commission Summary > Summarize by Parent-->
                                <p:ajax event="change"   listener="#{commSummary.setChkBoxVal()}" update=":commSummForm:prtchk"/>
                            </p:selectOneRadio>
                               <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
                            <p:outputLabel/>
                            <!--                Report type Radio Button-->
                            <!--Feature #2295:Commission Summary > Summarize by Parent-->
                            <p:outputLabel value="Summarize by Parent Company"  />
                            <p:selectBooleanCheckbox  itemLabel="#{commSummary.checkFlag ? '':''}"  value="#{commSummary.checkFlag}"   id="prtchk" widgetVar="ptChk" disabled="#{commSummary.grp eq 3}" >


                            </p:selectBooleanCheckbox>
                               <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
                            <p:outputLabel/>
                            <!--Task #2419Commission Summary > Summarize by Parent - UI Changes-->


                        </p:panelGrid>
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <p:commandButton  value="View Report"  id="viewreport"   styleClass="btn btn-primary btn-xs"  actionListener="#{commSummary.goToNavigationpage()}"   update="commsummsg" style="width: 30px;margin-left: 10px;"/>
                        <!--//#3410:Bug Comm.Reports > loading Issue when date entered manually-->


                    </div>

                </div>

            </div>
        </div>

        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->



        <script>
            function filterProcessGif()
            {
                $('#loading').show();
                document.getElementById("commSumFilterPg").style.pointerEvents = "none";
                document.getElementById("commSumFilterPg").style.opacity = "0.4";

//                var fromdate = document.getElementById("frmDt").value;
//                var todate = document.getElementById("toDt").value;


            }
//#3410:Bug Comm.Reports > loading Issue when date entered manually

            function hideGif()
            {
                document.getElementById("commSumFilterPg").style.pointerEvents = "all";
                document.getElementById("commSumFilterPg").style.opacity = "1";
                $('#loading').hide();
            }


            $(".ui-growl").on("click", function () {
                hideGif();
//                navigateToMessage([{name: 'param', value: messageId}]);
            });



        </script>  
          <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
        <style>
            .ui-panelgrid .ui-panelgrid-cell{
                padding: 4px 2px !important;
            }
        </style>
           




    </h:form>
         <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
    <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
    <ui:include src="/lookup/CompanyRegionLookUp.xhtml" />

</ui:composition>