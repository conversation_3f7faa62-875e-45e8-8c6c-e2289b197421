<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: Leftreports.xhtml
// Author: Priyadarshini
// Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved.
//********************************************************************* -->
<!--Task #6: Settings: Subtables -->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
             >
    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>

    <ui:define name="metadata">
        <f:metadata> 
            <f:viewParam name="r" value="#{leftCommission.id}"/>

            <f:viewAction action="#{leftCommission.redirectPage()}"/>
        </f:metadata>
    </ui:define>
    
</ui:composition>