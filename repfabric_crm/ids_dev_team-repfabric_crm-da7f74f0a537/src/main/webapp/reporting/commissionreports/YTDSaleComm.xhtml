<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: SalesCommissionComp.xhtml
// Author: priyadarshini
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 
    <h:form id="ytdForm">
        <!--//#3410:Bug Comm.Reports > loading Issue when date entered manually-->

        <p:growl id="msgs"  showDetail="false" showSummary="true"  />
        <p:remoteCommand autoRun="true" update=":ytdForm:grid :ytdForm:asofdate"  />
        <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="ytdForm:inputCustomer "/>
        <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update="ytdForm:inputDistriName" />
        <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="ytdForm:inputPrincName "  />
        <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="ytdForm:inputAllSalesTeam " />

        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;"  id="ytdSalCom" >
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
            <h4 style="margin-top:0px;margin-left: 18px;"> YTD Sales Comparison : Report Filter</h4>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->           
            <p:outputPanel rendered="#{leftCommission.countMsgCommReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border"  style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">

                        <div id="loading"  class="div-center" style="display:none;">
                            <!--#3249: Task Reporting > Commission Reports > Add loading gif-->


                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />
                            <!--<img id="loading-image" src="images/ajax-loader.gif" alt="Loading..." />-->
                        </div>   





                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;margin-top:20px;width: 130%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >

<!--                            //Bug #6787 commission reports ->date blank issue-->
                            <p:outputLabel  value="As of"   class="required"  />
                            <!--#3410:Bug Comm.Reports > loading Issue when date entered manually-->
                            <!--#4775:CRM-4398: Sales and Commission Reports: Remember date range for session-->
                            <p:calendar size="10" showOn="button"  
                                        value="#{reportFilters.tdate}" 
                                        pattern="#{globalParams.dateFormat}"   id="asofdate" converterMessage="Please enter valid date"  >
                                <p:ajax event="keyup"   process="@this"/>
                                
                                 <p:ajax event="dateSelect" process="@this"  />
                            </p:calendar>



                            <!--                        </p:panelGrid>
                            
                            
                            
                                                    <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                                                 style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >-->



                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>




                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                 <!--#3473:     sales team - Sales team data we can enter manually-->

                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  id="saleTeamLookUpbtn"
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  style="width:67px"
                                                  styleClass="btn-info btn-xs"  disabled="#{reportFilters.customer.compId >0}"/>
                            </h:panelGroup>

                            <p:outputLabel for="inputDistriName" value="#{custom.labels.get('IDS_DISTRI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputDistriName" value="#{reportFilters.distributor.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyDistri', 3, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->

                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:outputLabel value="Group by"/>

                            <p:selectOneRadio value="#{yTDComparison.repgrp}"  widgetVar="grp17" layout="pageDirection"  required="true">
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/>    
                                <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <f:selectItem   itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/> 
                                <f:selectItem itemValue="3" itemLabel="#{custom.labels.get('IDS_DISTRI')}"/>
                            </p:selectOneRadio>

<!--                             <p:selectOneRadio value="#{monthlyExpandedRep.repgrp}"  widgetVar="grp16" layout="pageDirection"  required="true">
    <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/>                                                
    <f:selectItem itemValue="2" itemLabel="#{custom.labels.get('IDS_CUSTOMER')}"/>
</p:selectOneRadio>-->
                        </p:panelGrid>
                     <!--#12947   ESCALATIONS CRM-7694   YTD Sales Comparison not running-->
                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <p:commandButton  value="View Report "  id="viewreport" onstart="filterProcessGif();"  styleClass="btn btn-primary btn-xs"  actionListener="#{yTDComparison.init(yTDComparison.repgrp)}"  oncomplete="hideGif()"  update="msgs" style="margin-left: 10px" />
                    </div>
                </div>
            </div>
            <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->
            <!--//#3410:Bug Comm.Reports > loading Issue when date entered manually-->            
        </div>


        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->



        <script>
            function filterProcessGif()
            {


                $('#loading').show();
                document.getElementById("ytdSalCom").style.pointerEvents = "none";
                document.getElementById("ytdSalCom").style.opacity = "0.4";


            }

//#3410:Bug Comm.Reports > loading Issue when date entered manually

            function hideGif()
            {
                document.getElementById("ytdSalCom").style.pointerEvents = "all";
                document.getElementById("ytdSalCom").style.opacity = "1";
                $('#loading').hide();
            }



        </script>  




    </h:form>
    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
    <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />

</ui:composition>