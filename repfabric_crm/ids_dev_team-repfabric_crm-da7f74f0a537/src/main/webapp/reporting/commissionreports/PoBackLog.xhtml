<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: poBackLog.xhtml
// Author: Priyadarshini
//*********************************************************
/*
*
* Copyright (c) 2020 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 
    <h:form id="poBackLogForm">

        <p:growl id="commsg" />
        <!--<p:remoteCommand autoRun="true"  update=":poBackLogForm:grid" actionListener="#{poBacklogService.backLogDefaultvalues()}"/>-->

        <p:remoteCommand name="applyCustomer" actionListener="#{poBacklogService.applyCustSelect(viewCompLookupService.selectedCompany)}" update=":poBackLogForm:inputCustomer "/>

        <p:remoteCommand name="applyPrinci" actionListener="#{poBacklogService.applyPrincipal(viewCompLookupService.selectedCompany)}" update=":poBackLogForm:inputPrincName "  />
        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima- added box shadow-->
        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;box-shadow: none;" id="commDetfilterPg" >
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
            <h4 style="margin-top:0px;margin-left: 18px">    PO Backlog Report : Report Filter</h4>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima-->           
            <p:outputPanel rendered="#{leftCommission.countMsgCommReport > 0}" style="border: 0px;color: red;margin-top: -8px;margin-left: 18px;font-size: 16px;">
                <b>Note: </b>These numbers may not be accurate. You still have transactions in the Aliaser.    
            </p:outputPanel>
            <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima - added style-->
            <div class="box-header with-border" style="border: 0px;">
                <div class="row">
                    <div class="col-md-5">


                        <div id="loading"  class="div-center" style="display:none;">


                            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                            left: 600px;
                                            top: 300px;
                                            width: 150px;
                                            height: 150px;
                                            z-index: 9999; opacity: .3 !important;" />

                        </div>   






                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-6"
                                     style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <!--#4775:CRM-4398: Sales and Commission Reports: Remember date range for session-->
                            <!-- //Bug #6787 commission reports ->date blank issue-->
                            <p:outputLabel  value="Planned Date From"   class="required"   />

                            <p:calendar  value="#{reportFilters.fdate}"   showOn="button"   pattern="#{globalParams.dateFormat}"  autocomplete="off" 
                                         id="fmDt"  converterMessage="Please enter valid From date">

                                <p:ajax  event="keyup"  process="@this" />
                                <p:ajax event="dateSelect" process="@this"  />
                            </p:calendar>

                            <!--#4775:CRM-4398: Sales and Commission Reports: Remember date range for session-->
                             <!-- //Bug #6787 commission reports ->date blank issue-->
                            <p:outputLabel  value="Planned Date To"  class="required" />

                            <p:calendar  value="#{reportFilters.tdate}"  showOn="button"   autocomplete="off"  pattern="#{globalParams.dateFormat}"   id="toDt" converterMessage="Please enter valid To date"  >

                                <p:ajax  event="keyup"  process="@this" />
                                <p:ajax event="dateSelect" process="@this"  />
                            </p:calendar>


                            <p:outputLabel value="Group by"/>

                            <p:selectOneMenu value="#{poBacklogService.grpBy}" style="width: 100%;" id="source">
                                <!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->

                                <f:selectItem itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')} Sort by #{custom.labels.get('IDS_PRINCI')}" itemValue="2" />
                                <f:selectItem itemLabel="#{custom.labels.get('IDS_PRINCI')} Sort by #{custom.labels.get('IDS_DB_CUSTOMER')}" itemValue="1" />

                            </p:selectOneMenu> 
                            <p:outputLabel  value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width: 120%" id="selSalesTeam">
                                    <f:selectItems value="#{lookupService.salesTeams}" var="team"  
                                                   itemValue="#{team.smanId}"
                                                   itemLabel="#{team.smanName}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.list('SALES_TEAM')}" update=":frmSalesTeamLookup" oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                <p:commandLink styleClass="sel-salesteam" value="Clear" actionListener="#{lookupService.clear('SALES_TEAM')}" update="@(.sel-salesteam)" disabled="#{!(lookupService.salesTeams!=null and lookupService.salesTeams.size() != 0)}" />
                            </h:panelGroup>




                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />

                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputPrincName" value="#{poBacklogService.princiName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

<!--                           Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{poBacklogService.custName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <p:outputLabel value="Include all late shipments"  />

                            <p:selectBooleanCheckbox  itemLabel="#{poBacklogService.shipmentFlag ? '':''}"  value="#{poBacklogService.shipmentFlag}"     id="shipFlag" widgetVar="shipmentFlag">


                            </p:selectBooleanCheckbox>


                        </p:panelGrid>

                        <!--#8386: ESCALATION: CRM-5875 Add message to reporting pages - poornima--> 
                        <p:commandButton id="btnViewRpt" value="View Report" action="#{poBacklogService.goToNavigationPage(2)}" actionListener="#{reportFilters.setSalesteamlst()}"   styleClass="btn btn-primary btn-xs" style="margin-left: 10px"/> 

                    </div>
                </div>
            </div>
        </div>


        <script>
            function filterProcessGif()
            {
                $('#loading').show();
                document.getElementById("commDetfilterPg").style.pointerEvents = "none";
                document.getElementById("commDetfilterPg").style.opacity = "0.4";

            }


            function hideGif()
            {
                document.getElementById("commDetfilterPg").style.pointerEvents = "all";
                document.getElementById("commDetfilterPg").style.opacity = "1";
                $('#loading').hide();
            }


            $(".ui-growl").on("click", function () {
                hideGif();

            });


        </script>  

    </h:form>


    <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>
    <ui:include src="/lookup/CompanyLookupDlg.xhtml"/>
</ui:composition>