<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: Leftreports.xhtml
// Author: Priyadarshini
// Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved.
//********************************************************************* -->
<!--Task #6: Settings: Subtables -->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">
      <!--#363:page title-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>  #{leftCommission.selectedSubMenu.label}  </title>
    </ui:define>
    <ui:define name="metadata">
    <f:metadata>
            <f:viewParam name="r" value="#{leftCommission.id}"/>
           
            <f:viewAction action="#{leftCommission.init(leftCommission.id)}"/>
<!--          #10300  CRM-6433:-->
             <!--<f:viewAction action="#{salesReports.resetFilters()}"/>-->
             <!--Task #456: commission reports->sales/comm comparision year update isuue-->
             <!--<f:viewAction action="#{salesReports.reSetDateMonth()}"/>-->
             
             
             <!--#2472 - CRM-1521: Tutorial button landing urls-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('COMM_RPT'))}" />
            <!--29-09-2023 12191  CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('COMM_RPT')}" />
        </f:metadata>
    </ui:define>
      <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
    <ui:define name="title">#{custom.labels.get('IDS_COMMISSION')} Reports
    <ui:include src="/help/Help.xhtml"/>
    </ui:define>
    <ui:define name="menu">

    </ui:define>
    <ui:define name="body">
        <div class="box box-info box-body">        
            <div class="left-column">
                <h:form>
                    <p:dataTable value="#{leftCommission.commsRepMenuList}"  var="cm" selectionMode="single" id="dtCommissionMenuLst" selection="#{leftCommission.selectedSubMenu}" class="hide-column-names"
                                 rowKey="#{cm.id}" >

<!--listener="#{reports.resetFilters()}"-->
                        <p:ajax event="rowSelect" update=":commReport" listener="#{leftCommission.redirectPage()}"  />
                        <p:column >
                            <h:outputText value="#{cm.label}"/>
                        </p:column>


                        <p:editor id="editor" widgetVar="editorWidget"  width="600" />
                        <p:fileUpload  mode="advanced" dragDropSupport="false"
                                       sizeLimit="100000" fileLimit="3" allowTypes="/(\.|\/)(gif|jpe?g|png)$/" />
                    </p:dataTable>
                </h:form>
            </div>
            <div class="right-column">

                <p:outputPanel id="commReport">
                    <ui:include src="#{leftCommission.selectedSubMenu.pageUrl}"  />

                </p:outputPanel>



            </div>



        </div>
    </ui:define>
</ui:composition>
