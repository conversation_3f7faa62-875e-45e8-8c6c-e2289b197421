<?xml version='1.0' encoding='UTF-8' ?> 
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: PrincipalSummary.xhtml
// Author: Priyadarshini.
20-Oct-2020
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

    <!--#363:page title-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title> #{custom.labels.get('IDS_PRINCI')} Summary  </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata> 


            <f:viewParam name="yr" value="#{reports.repYear}" />
            <f:viewParam name="mt" value="#{reports.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="d" value="#{reportFilters.distributor.compId}"/>
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>

            <f:viewParam name="c" value="#{reportFilters.customer.compId}"/>
            <!--//#2999Task Customer Summary report - Add multi select Sales team option-->
            <f:viewAction action="#{reportFilters.getSelectedCustName(reportFilters.customer.compId)}"/>

            <f:viewParam name="prt" value="#{salesSummaryParent.checkFlag}"/>
            <f:viewAction action="#{reportFilters.assignSalesTeams()}"/>
            <!--<f:viewAction action="#{reportFilters.assignCompanyRegions()}"/>-->

            <f:viewAction action="#{custSummaryRep.runPrinciReport()}"/>

  <!--<f:viewAction action="#{principalSummary.checkAccessAndRun()}"/>-->


        </f:metadata>
    </ui:define>

    <ui:define name="title">
        <ui:param name="title" value="Products"/>
        <div class="row">
            <div class="col-md-6">
                #{custom.labels.get('IDS_PRINCI')} Summary    

            </div>

        </div>
    </ui:define> 
    <ui:define name="widthcss">
        <style>
            #wrapper {
                margin: 0 auto;
                width: 95%;
            }

            .ui-datatable thead th, .ui-datatable foot td{
                padding: 3px 6px;
            }


        </style>        
    </ui:define>

    <ui:define name="body">
        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .3 !important;" />
        <f:event listener="#{custSummaryRep.isPageAccessible}" type="preRenderView"/> 

        <!--#3116:Task :Sales Reports - Customer Summary - Add gif for loading-->

        <!--<img id="loading-image" src="images/ajax-loader.gif" alt="Loading..." />-->
        <!--</div>-->  


        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4" id="custSumRpt" >


            <!--<div class="box-header with-border">-->
            <!--<div class="row">-->
            <!--<div class="col-md-5">-->
            <h:form id="custSummaryHeader" >
                <p:remoteCommand autoRun="true"  update=":custSummaryHeader:grid"  />

                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update=":custSummaryHeader:inputCustomer  " />
                <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update=":custSummaryHeader:inputDistriName  "/>
                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update=":custSummaryHeader:inputPrincName  " />
                <!--<p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update=":custSummaryHeader:inputAllSalesTeam " />-->
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >


                        <div class="compNamebg">
                            <h:outputLabel value="#{loginBean.subscriber_name}"   />
                            <br/>


                            #{!globalParams.allowDistriCommEnabled ?custom.labels.get('IDS_PRINCI'):'Manufacturer'} Summary 

                            <br/>

                        </div>
                        &nbsp;&nbsp;&nbsp;<p:outputLabel value="Quarter : #{reports.repYear}-#{reports.quarter},  Month : #{reportFilters.getMonth(reports.repMonth )}" /> 
                                              <!--<p:outputLabel  value="As of : #{reports.repYear} - #{reports.getMonth(reports.repMonth)}" />-->

                        <!--465:region lookup-->
                        <br/>

                        &nbsp;&nbsp;&nbsp;<p:outputLabel value="(Filtered for selected Regions)"   rendered="#{!(reportFilters.compRegnName.length() eq null) }"  />
                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="margin-left:60px" >

                        <p:panelGrid   columns="2"  layout="grid" style="width: 600px;float: right;"  id="graph"> 
                        
                          

                        </p:panelGrid>
                    </div>  
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:-60px">
                        <p:panelGrid id="grid" columns="2" 
                                     style="float: right;width: 350px;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
        <!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"  style="margin-left:-70px"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:outputLabel for="inputDistriName" value="#{custom.labels.get('IDS_DISTRI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputDistriName" value="#{reportFilters.distributor.compName}"  placeholder="All" readonly="true" style="margin-left:-70px"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyDistri', 3, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <!--//#2999Task Customer Summary report - Add multi select Sales team option-->
                            <!--//  1741  CRM-3259: Sales team:Thea: prevent users from seeing Sales and Comms -  Dashboard Sales Team Lookup-->

                            <p:outputLabel  value="#{custom.labels.get('IDS_SALES_TEAM')}"  />

                            <h:panelGroup class="ui-inputgroup" style="margin-left: -70px;width: 142%">
                                <h:selectOneListbox styleClass="sel-salesteam"  size="2"  style="width: 200%;" id="selSalesTeam" >
                                    <f:selectItems value="#{lookupService.salesTeams}" var="team"  
                                                   itemValue="#{team.smanId}"
                                                   itemLabel="#{team.smanName}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.crmFlag()}"  action="#{lookupService.list('SALES_TEAM')}"  update=":frmSalesTeamLookup " oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                &nbsp;&nbsp;&nbsp;
                                <p:commandLink styleClass="sel-salesteam" value="Clear" actionListener="#{lookupService.clear('SALES_TEAM')}" update="@(.sel-salesteam)" disabled="#{!(lookupService.salesTeams!=null and lookupService.salesTeams.size() != 0)}" style="text-decoration:underline"/>
                            </h:panelGroup>


                           <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true" style="margin-left:-70px"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  style="width:5px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <p:commandButton  value="View Report "  id="viewreport"   styleClass="btn btn-primary btn-xs"  style="width:30px"   action="#{custSummaryRep.goToPrincipalSummary(2)}"  actionListener="#{reportFilters.setSalesteamlst()}" onclick="custSumVisibilty();" />

                            <!--                            <p:column>
                                                            <p:commandLink id="xls" ajax="false"  >  
                                                                <p:graphicImage name="/images/excel.png" width="24" onclick="PrimeFaces.monitorDownload(start, stop);" />
                                                                <pe:exporter type="xlsx" target="custSumexport"  fileName="Customer_Summary_Report" subTable="false" postProcessor="#{custSummaryRep.postProcessXLS}"  />  
                            
                                                            </p:commandLink>  
                            
                                                            <p:commandLink id="pdf" ajax="false">  
                                                                <p:graphicImage value="/resources/images/pdf.png"  width="24"  onclick="PrimeFaces.monitorDownload(start, stop);" />  
                                                                <pe:exporter type="pdf" target="custSumexport" fileName="Customer_Summary_Report" subTable="false"  preProcessor="#{custSummaryRep.preProcessPDF}" />  
                            
                                                            </p:commandLink>-->

                            <!--</p:column>-->
                        </p:panelGrid>

                    </div >

                </div>


                <p:dataTable  value="#{custSummaryRep.princiSumryList}" var="princirep"  
                              id="custSummaryRepdt"
                              >
                    <p:column  headerText="#{!salesSummaryParent.checkFlag ? custom.labels.get('IDS_PRINCI'): 'Parent Company' } "  sortBy="#{princirep.repCustName}"  filterBy="#{princirep.repCustName}"  filterMatchMode="contains" style="text-align: left">

                        <p:outputLabel  value="#{princirep.repCustName}"  />

                        <f:facet name="footer">
                            <h:outputLabel value="Total :"  style="float: right"  class="footerFont"/>
                        </f:facet>
                    </p:column>
                    <p:column sortBy="#{princirep.repYearPrev}" headerText="Previous Year " style="text-align: right" filterBy="#{princirep.repYearPrev}" >
                        <h:outputLabel value="#{princirep.repYearPrev}" >

                            <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                        </h:outputLabel>
                        <f:facet name="footer">
                            <h:outputLabel value="#{custSummaryRep.prevYearTotal}"  style="float: right"  class="footerFont">

                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column >

                    <p:column  headerText="Current Year"   filterBy="#{princirep.repYearCurr}"  sortBy="#{princirep.repYearCurr}"  style="text-align: right;">
                        <h:outputLabel  value="#{princirep.repYearCurr}" >
                            <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                        </h:outputLabel>


                        <f:facet name="footer">
                            <h:outputLabel value="#{custSummaryRep.currYearTotal}"   class="footerFont">

                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>

                    </p:column>
                    <p:column  headerText="% to LY" filterBy="#{princirep.repYearGrowth}" sortBy="#{princirep.repYearGrowth}" style="text-align: right;">
                        <p:outputLabel  value=" #{princirep.repYearGrowth}" style="text-align: right;"   >
                            <h:outputLabel value="%" rendered="#{princirep.repYearGrowth ne null}"/>
                            <f:convertNumber groupingUsed="false" maxFractionDigits="1"/>
                        </p:outputLabel>
                    </p:column>

                </p:dataTable>











            </h:form>

            <p:dialog widgetVar="inCustSumProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Please wait...Exporting records" />
                        <br /><br />
                        <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                    </p:outputPanel>
                </h:form>
            </p:dialog>



            <!--</div>-->

            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
            <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>
            <!--#456-->
            <ui:include src="/lookup/CompanyRegionLookUp.xhtml" />

        </div>
        <style>

            .ui-datatable-scrollable-theadclone{
                visibility: hidden;
            }
            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }


            .jqplot-target {

                font-size: 0.75em!important;
            }
            /*2627 - Related to CRM-1177: Doran associates: Remove link from printed reports - sharvani - 11-12-2019*/
            @media print {
                a[href]:after {
                    content: none !important;
                }
            }
            /*            #bckDetForm\:backogrecordDT ,body .ui-datatable .ui-datatable-scrollable-header{
                                        margin-left: -1000px;
                                                            } */

            /*                                               */
            /* #custSummaryHeader\:custSummaryRepdt.ui-datatable-scrollable-body{
                height: calc(100vh - 420px);
                                        }*/


        </style>
                                        <!--#3116:Task :Sales Reports - Customer Summary - Add gif for loading-->
                                        <script>                                 window.onload = function () {
                                        //                $('#loading-image').show();
                                        setTimeout(function () {
                                                var t = performance.timing;
                                                console.log(t.loadEventEnd - t.responseEnd);
                                                //      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                                                document.getElementById("custSumRpt").style.pointerEvents = "all";
                                        document.getElementById("custSumRpt").style.opacity = "1";
                                        // <!--#987  :  CRM-2757: MSGinc customer summary Reports Timing out-->

//                    var s = jQuery('#custSummaryHeader\\:custSummaryRepdt.ui-datatable-scrollable-body').animate({scrollHeight: (screen.height)});
//                    console.log("screen.height::" + s);
                                                        $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };


            function custSumVisibilty()
            {

                $('#loading-image').show();
                document.getElementById("custSumRpt").style.pointerEvents = "none";
                document.getElementById("custSumRpt").style.opacity = "0.4";

            }

            function start() {

                PF('inCustSumProgressDlg').show();
            }

            function stop() {
                PF('inCustSumProgressDlg').hide();
            }



        </script>

    </ui:define>


</ui:composition>