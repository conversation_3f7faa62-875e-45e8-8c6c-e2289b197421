<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: sals/com princi.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
       <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
        <title>   Sales/#{custom.labels.get('IDS_COMMI')} Comparison  </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>

            <f:viewParam name="yr" value="#{reportFilters.repYear}" />
            <f:viewParam name="mt" value="#{reportFilters.repMonth}" />
            <f:viewParam name="c" value="#{reportFilters.customer.compId}" />
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}" />
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}" />
            <f:viewParam name="g" value="#{salCommComparisonRep.grp}" />
            <f:viewParam name="flg" value="#{salCommComparisonRep.salesTeamFlag}" />
            <f:viewParam name="ePrinci" value="#{salCommComparisonRep.excldInactivFlg}" />
             <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
            <f:viewParam name="ri" value="#{salCommComparisonRep.compRegns}"/>
            <f:viewParam name="rn" value="#{salCommComparisonRep.compRegNames}"/>
                         
            <f:viewAction action="#{salCommComparisonRep.init(salCommComparisonRep.grp)}"/>

        </f:metadata>
    </ui:define>
    <ui:define name="body">
        <f:event listener="#{salCommComparisonRep.isPageAccessible}" type="preRenderView"/>  

        <p:dialog header="Please wait" draggable="false"  widgetVar="dlgSalComCompaj" modal="true" closable="false" resizable="false" >
            <h:graphicImage  library="images" name="ajax-loader.gif" />
            <p:spacer width="5"/>
            <h:outputLabel value="Loading data.."/>
        </p:dialog>

        <!--#3249:   Task Reporting > Commission Reports > Add loading gif  END   -->


        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4"   id="compCompSalesteam" >

            <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                            left: 600px;
                            top: 300px;
                            width: 150px;
                            height: 150px;
                            z-index: 9999; opacity: .5 !important; " />   




            <h:form id="salescommcomp" >

                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="salescommcomp:inputCustomer  salescommcomp:saleTeamLookUpbtn "/>

                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="salescommcomp:inputPrincName "  />
                <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="salescommcomp:inputAllSalesTeam " />





                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >


                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                    <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
         #{custom.labels.get('IDS_COMMISSION')} to Sales Comparison 
                            <br/>

                        </div>
                        &nbsp;&nbsp; <p:outputLabel value="As of:" /> &nbsp;  <p:outputLabel value="#{reportFilters.repYear}" id="year"/>
                         <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                           <div  style="float:left;width:750px;margin-top: 20px" >
                             <p:outputLabel style="font-weight:normal" rendered="#{salCommComparisonRep.compRegNames.length() > 0}"  value="*Filtered for selected #{custom.labels.get('IDS_REGION')}" />  
                        </div>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                         <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                        <p:panelGrid id="grid1" columns="1" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;width:170%;margin-left: 200px"       layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel value="Group by"/>

                            <p:selectOneRadio value="#{salCommComparisonRep.grp}"  widgetVar="grp17" layout="pageDirection"  required="true">
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/>                                                
                                <f:selectItem   itemValue="2" itemLabel="#{custom.labels.get('IDS_CUSTOMER')}"/> 
                                <!--#3249:   Task Reporting > Commission Reports > Add loading gif  END   -->


                                <p:ajax  onstart="processGif();"  listener="#{salCommComparisonRep.init(salCommComparisonRep.grp)}" update=":salescommcomp:tbldata1" oncomplete="hide();" />
                            </p:selectOneRadio>
                        </p:panelGrid>
                    </div>  
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >
                        <p:panelGrid id="grid2" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;width:100%;margin-left: 100px"       layout="grid" styleClass="box-primary no-border ui-fluid "  >
                            <p:outputLabel  value="Year"   />
                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" >
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />

                                <!--#3249:   Task Reporting > Commission Reports > Add loading gif  END   -->


                                <p:ajax listener="#{salCommComparisonRep.init(salCommComparisonRep.grp)}"  onstart="processGif();"   update=":salescommcomp:tbldata1 :salescommcomp:year"   oncomplete="hide();"/>
                            </p:selectOneMenu>   

                            <p:outputLabel  value="Month"   />
                            <p:selectOneMenu height="280"   widgetVar="mt" value="#{reportFilters.repMonth}"  >
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 

                                <!--#3249:   Task Reporting > Commission Reports > Add loading gif  END   -->


                                <p:ajax listener="#{salCommComparisonRep.init(salCommComparisonRep.grp)}" onstart="processGif();"   update=":salescommcomp:tbldata1"  oncomplete="hide();"/>
                            </p:selectOneMenu>





                        </p:panelGrid>
                    </div>
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:100px">
                        <p:panelGrid id="grid" columns="2" 
                                     style="float: right;width: 350px;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--#3473:     sales team - Sales team data we can enter manually-->

                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  id="saleTeamLookUpbtn"
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  style="width:67px"
                                                  styleClass="btn-info btn-xs"  />
                            </h:panelGroup>


                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:commandButton  value="View Report "  id="viewreport" onclick="processGif();"  styleClass="btn btn-primary btn-xs"  actionListener="#{salCommComparisonRep.init(salCommComparisonRep.grp)}"  style="width:30px" update=":salescommcomp:tbldata1"  oncomplete="hide();" />
                            <p:commandButton value="Export" style="width:30px" action="#{salCommComparisonRep.exportData()}"
                                             onclick="PrimeFaces.monitorDownload(start3, stop3);
                                                     PF('pollStatExp').start3();" styleClass="btn btn-primary  btn-xs"/>


                        </p:panelGrid>


                    </div>
                </div> 
                <!-- //// bug #5912 Comm Reports > Sales/Comm Comparison: missing headers in Excel export-->

                <h:panelGroup id="tbldata1">

                    <ui:repeat  value="#{salCommComparisonRep.listSalComCompRep}" var="salcomlist"  id="sid">
                        <!--class="div-header"-->
                        <div  style="height:28px;width: 100%;background-color:#000;vertical-align:central" >
                            <label style="font-size: 1.3em;font-weight: bold;color:white;line-height: 25px;padding-left: 5px;">
                                <p:outputLabel  value="For: "/>&nbsp;&nbsp;<p:outputLabel  value="#{salcomlist.steams}"/>
                            </label>
                        </div>

                        <p:dataScroller chunkSize="5"   id="dtrpt1" value="#{salcomlist.listSalCommSalTmList}" var="salComComp" >

                            <p:column >
                                <div class="div-selected" style="height:25px;width: 100%;" >
                                    <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                                        #{salComComp.itemName}   

                                    </label>               
                                </div>

                                <p:dataTable  value="#{salComComp.details}"  var="salComComp1" emptyMessage="No data found" >
                                    <p:columnGroup type="header" id="tableHeader">
                                        <p:row>

                                            <p:column rowspan="2" style="width: 250px;height:20px;" 
                                                      headerText="#{custom.labels.get('IDS_PRINCI')}#{salComComp.custname}" class="col_center" sortBy="#{salComComp1.princiName}" rendered="#{salCommComparisonRep.grp eq 2}" > 

                                                <f:facet name="footer" >
                                                    <h:outputLabel value="Total :" style="float: right" rendered="#{salCommComparisonRep.grp eq 2}" class="footerFont">

                                                    </h:outputLabel>
                                                </f:facet>

                                            </p:column>
                                            <p:column rowspan="2"  style="width: 140px;height:20px;" 
                                                      headerText="#{custom.labels.get('IDS_CUSTOMER')}#{salComComp1.custname}" class="col_center" sortBy="#{salComComp1.custname}"  rendered="#{salCommComparisonRep.grp eq 1}" /> 
                                            <p:column colspan="3" style="width: 140px;height:20px;" 
                                                      headerText="Sales" class="col_center" /> 
                                        <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->  
                                            <p:column  colspan="3"  style="width: 140px;height:20px;" 
                                                       headerText="#{custom.labels.get('IDS_COMMISSION')}" class="col_center"/> 

                                    <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                            <p:column rowspan="2"
                                                      headerText="Avg. #{custom.labels.get('IDS_COMM')} Rate
                                                      " style="width: 80px;height:20px;"   />

                                        </p:row>

                                        <p:row>
                                            <p:column headerText="#{salCommComparisonRep.monthName} #{reportFilters.repYear}"  style="width: 140px" />
                                            <p:column headerText="Jan-#{salCommComparisonRep.monthName} #{reportFilters.repYear}"   style="width: 140px"/>
                                            <p:column headerText="Jan-#{reports.repMonth eq 1? '' 
                                                                        : (reports.repMonth ne 1 ? salCommComparisonRep.monthName :salCommComparisonRep.monthName)}  #{reportFilters.repYear -1}"  style="width: 140px"/>

                                            <p:column headerText="#{salCommComparisonRep.monthName} #{reportFilters.repYear}" style="width: 140px;text-align:right" />
                                            <p:column headerText="Jan-#{salCommComparisonRep.monthName} #{reportFilters.repYear}" style="width: 140px" />
                                            <p:column headerText="Jan-#{reports.repMonth eq 1 ? '' 
                                                                        : (reports.repMonth ne 1 ? salCommComparisonRep.monthName : salCommComparisonRep.monthName)}  #{reportFilters.repYear -1}" style="width: 140px" />


                                        </p:row>
                                    </p:columnGroup> 

                                    <p:column  rendered="#{salCommComparisonRep.grp eq 2}" style="width: 180px" >
                                        <h:outputLabel value="#{salComComp1.princiName}"  />    

                                        <br/>

                                        <f:facet name="footer" >
                                            <h:outputLabel value="Total :"  style="float: right" class="footerFont" />

                                        </f:facet>

                                    </p:column>

                                    <p:column class="col_left"  rendered="#{salCommComparisonRep.grp eq 1}" style="width: 180px" >

                                        <h:outputLabel value="#{salComComp1.custname}"  />    




                                        <f:facet name="footer" >
                                            <h:outputLabel value="Total :"  style="float: right" class="footerFont"/>

                                        </f:facet>

                                    </p:column>
                                    <p:column style="text-align: right" >

                                        <h:outputLabel value="#{salComComp1.salMonth eq null ? '0' 
                                                                : (salComComp1.salMonth ne null ? salComComp1.salMonth : salComComp1.salMonth)}"  >   
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>
                                        <f:facet name="footer" >
                                            <h:outputLabel value="#{salComComp.totals[1]}"  style="float: right" class="footerFont">
                                                <f:convertNumber maxFractionDigits="2"/>
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>
                                    <p:column style="text-align: right " >

                                        <h:outputLabel value="#{salComComp1.salYtdCurr eq null ? '0' 
                                                                : (salComComp1.salYtdCurr ne null ? salComComp1.salYtdCurr : salComComp1.salYtdCurr)}" >
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>
                                        <f:facet name="footer" >
                                            <h:outputLabel value="#{salComComp.totals[2]}"  >
                                                <f:convertNumber maxFractionDigits="2"/>
                                            </h:outputLabel>

                                        </f:facet>
                                    </p:column>

                                    <p:column style="text-align: right" >

                                        <h:outputLabel value="#{salComComp1.salYtdprev eq null ? '0' 
                                                                : (salComComp1.salYtdprev ne null ? salComComp1.salYtdprev : salComComp1.salYtdprev)}" >
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>
                                        <f:facet name="footer" >
                                            <h:outputLabel value="#{salComComp.totals[3]}" >
                                                <f:convertNumber maxFractionDigits="2"/>
                                            </h:outputLabel>

                                        </f:facet>
                                    </p:column>
                                    <p:column style="text-align: right" >

                                        <h:outputLabel value="#{salComComp1.commMonth eq null ? '0' 
                                                                : (salComComp1.commMonth ne null ? salComComp1.commMonth : salComComp1.commMonth)}" >
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>
                                        <f:facet name="footer" >
                                            <h:outputLabel value="#{salComComp.totals[4]}">
                                                <f:convertNumber maxFractionDigits="2"/>
                                            </h:outputLabel>

                                        </f:facet>
                                    </p:column>
                                    <p:column style="text-align: right" >

                                        <h:outputLabel value="#{salComComp1.commYtdCurr eq null ? '0' 
                                                                : (salComComp1.commYtdCurr ne null ? salComComp1.commYtdCurr : salComComp1.commYtdCurr)}" >
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>

                                        <f:facet name="footer" >
                                            <h:outputLabel value="#{salComComp.totals[5]}"   >
                                                <f:convertNumber maxFractionDigits="2"/>
                                            </h:outputLabel>

                                        </f:facet>

                                    </p:column>
                                    <p:column style="text-align: right" >

                                        <h:outputLabel value="#{salComComp1.commYtdPrev eq null ? '0' 
                                                                : (salComComp1.commYtdPrev ne null ? salComComp1.commYtdPrev : salComComp1.commYtdPrev)}" >
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>

                                        <f:facet name="footer"  >
                                            <h:outputLabel value="#{salComComp.totals[6]}" >
                                                <f:convertNumber maxFractionDigits="2"/>
                                            </h:outputLabel>

                                        </f:facet>
                                    </p:column>

                                    <p:column style="text-align: right" >
                                        <h:outputLabel value="#{salComComp1.commAvg}"  >
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>
                                        <!--                                <f:facet name="footer" >
                                                                            <h:outputLabel value="#{salComComp.totals[7]}%"  style="float: right" >
                                                                                  <f:convertNumber maxFractionDigits="2"/>
                                                                            </h:outputLabel>
                                        
                                                                        </f:facet>-->

                                    </p:column>



                                </p:dataTable>
                                <!--</ui:repeat>-->




                            </p:column>
                        </p:dataScroller>


                        <div  style="height:28px;width: 100%;background-color: #666666;vertical-align:central" >
                            <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                                <p:outputLabel  value="Recap For:"/>&nbsp;&nbsp;<p:outputLabel  value="#{salcomlist.steams}"/>
                            </label>
                        </div>

                        <p:dataTable  value="#{salcomlist.salCommSummDet}" var="salComSmry">
                            <p:columnGroup type="header" id="tableHeader" >
                                <p:row>
                                    <!--  #6526: Ref: 5912 : Comm Reports > Sales/Comm Comparison:Column Alignment Issues - poornima //added width to 250px -->
                                    <p:column rowspan="2" style="width: 250px;padding-bottom: 0px;" 
                                              headerText="#{custom.labels.get('IDS_PRINCI')}#{salComSmry.custname}" class="col_center" sortBy="#{salComComp1.princiName}" rendered="#{salCommComparisonRep.grp eq 2}" > 

                                        <f:facet name="footer" >
                                            <h:outputLabel value="Total :" style="float: right" rendered="#{salCommComparisonRep.grp eq 2}" class="footerFont">

                                            </h:outputLabel>
                                        </f:facet>

                                    </p:column>
                                    <p:column rowspan="2"  style="width: 140px" 
                                              headerText="#{custom.labels.get('IDS_CUSTOMER')}#{salComComp1.custname}" class="col_center" sortBy="#{salComSmry.custname}"  rendered="#{salCommComparisonRep.grp eq 1}" /> 
                                    <p:column colspan="3" style="width: 140px" 
                                              headerText="Sales" class="col_center"/> 
                                  <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->  
                                    <p:column  colspan="3"  style="width: 140px;" 
                                               headerText="#{custom.labels.get('IDS_COMMISSION')}" class="col_center"/> 
 <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                    <p:column rowspan="2"
                                              headerText="Avg. #{custom.labels.get('IDS_COMM')} Rate
                                              " style="width: 80px"   />

                                </p:row>

                                <p:row>
                                    <p:column headerText="#{salCommComparisonRep.monthName} #{reportFilters.repYear}"  style="width: 140px" />
                                    <p:column headerText="Jan-#{salCommComparisonRep.monthName} #{reportFilters.repYear}"   style="width: 140px"/>
                                    <p:column headerText="Jan-#{reports.repMonth eq 1? '' 
                                                                : (reports.repMonth ne 1 ? salCommComparisonRep.monthName :salCommComparisonRep.monthName)}  #{reportFilters.repYear -1}"  style="width: 140px"/>

                                    <p:column headerText="#{salCommComparisonRep.monthName} #{reportFilters.repYear}" style="width: 140px;text-align:right" />
                                    <p:column headerText="Jan-#{salCommComparisonRep.monthName} #{reportFilters.repYear}" style="width: 140px" />
                                    <p:column headerText="Jan-#{reports.repMonth eq 1 ? '' 
                                                                : (reports.repMonth ne 1 ? salCommComparisonRep.monthName : salCommComparisonRep.monthName)}  #{reportFilters.repYear -1}" style="width: 140px" />


                                </p:row>
                            </p:columnGroup> 

                            <p:column  rendered="#{salCommComparisonRep.grp eq 2}" style="width: 180px" >
                                <h:outputLabel value="#{salComSmry.itemName}"  />    

                                <br/>

                                <f:facet name="footer" >
                                    <h:outputLabel value="Total :"  style="float: right" class="footerFont" />

                                </f:facet>

                            </p:column>

                            <p:column class="col_left"  rendered="#{salCommComparisonRep.grp eq 1}" style="width: 180px" >

                                <h:outputLabel value="#{salComSmry.itemName}"  />    




                                <f:facet name="footer" >
                                    <h:outputLabel value="Total :"  style="float: right" class="footerFont"/>

                                </f:facet>

                            </p:column>
                            <p:column style="text-align: right" >

                                <h:outputLabel value="#{salComSmry.salMonth eq null ? '0' 
                                                        : (salComSmry.salMonth ne null ? salComSmry.salMonth : salComSmry.salMonth)}"  >   
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <f:facet name="footer" >
                                    <h:outputLabel value="#{salcomlist.totSalmonth eq null ? '0' 
                                                            : (salcomlist.totSalmonth ne null ? salcomlist.totSalmonth : salcomlist.totSalmonth)}"  style="float: right" class="footerFont">
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>
                            <p:column style="text-align: right" >

                                <h:outputLabel value="#{salComSmry.salYtdCurr eq null ? '0' 
                                                        : (salComSmry.salYtdCurr ne null ? salComSmry.salYtdCurr : salComSmry.salYtdCurr)}" >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <f:facet name="footer" >
                                    <h:outputLabel value="#{salcomlist.totSalYTDCurr eq null ? '0' 
                                                            : (salcomlist.totSalYTDCurr ne null ? salcomlist.totSalYTDCurr : salcomlist.totSalYTDCurr)}"  style="float: right" class="footerFont" >
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>

                                </f:facet>
                            </p:column>

                            <p:column style="text-align: right" >

                                <h:outputLabel value="#{salComSmry.salYtdprev eq null ? '0' 
                                                        : (salComSmry.salYtdprev ne null ? salComSmry.salYtdprev : salComSmry.salYtdprev)}" >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <f:facet name="footer" >
                                    <h:outputLabel value="#{salcomlist.totSalYTDPrev eq null ? '0' 
                                                            : (salcomlist.totSalYTDPrev ne null ? salcomlist.totSalYTDPrev : salcomlist.totSalYTDPrev)}"  style="float: right" class="footerFont">
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>

                                </f:facet>
                            </p:column>
                            <p:column style="text-align: right" >

                                <h:outputLabel value="#{salComSmry.commMonth eq null ? '0' 
                                                        : (salComSmry.commMonth ne null ? salComSmry.commMonth : salComSmry.commMonth)}" >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <f:facet name="footer" >
                                    <h:outputLabel value="#{salcomlist.totComMonth eq null ? '0' 
                                                            : (salcomlist.totComMonth ne null ? salcomlist.totComMonth : salcomlist.totComMonth)}"  style="float: right" class="footerFont" >
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>

                                </f:facet>
                            </p:column>
                            <p:column style="text-align: right" >

                                <h:outputLabel value="#{salComSmry.commYtdCurr eq null ? '0' 
                                                        : (salComSmry.commYtdCurr ne null ? salComSmry.commYtdCurr : salComSmry.commYtdCurr)}" >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>

                                <f:facet name="footer" >
                                    <h:outputLabel value="#{salcomlist.totComYTDCurr eq null ? '0' 
                                                            : (salcomlist.totComYTDCurr ne null ? salcomlist.totComYTDCurr : salcomlist.totComYTDCurr)}"  style="float: right" class="footerFont" >
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>

                                </f:facet>

                            </p:column>
                            <p:column style="text-align: right" >

                                <h:outputLabel value="#{salComSmry.commYtdPrev eq null ? '0' 
                                                        : (salComSmry.commYtdPrev ne null ? salComSmry.commYtdPrev : salComSmry.commYtdPrev)}" >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>

                                <f:facet name="footer" >
                                    <h:outputLabel value="#{salcomlist.totComYTDPrev eq null ? '0' 
                                                            : (salcomlist.totComYTDPrev ne null ? salcomlist.totComYTDPrev : salcomlist.totComYTDPrev)}"  style="float: right" class="footerFont">
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>

                                </f:facet>
                            </p:column>

                            <p:column style="text-align: right" >
                                <h:outputLabel value="#{salComSmry.commAvg}"  >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <!--                                <f:facet name="footer" >
                                                                    <h:outputLabel value="#{salComComp.totals[7]}%"  style="float: right" >
                                                                          <f:convertNumber maxFractionDigits="2"/>
                                                                    </h:outputLabel>
                                
                                                                </f:facet>-->

                            </p:column>



                        </p:dataTable>
                        <div  style="height:30px;width: 100%;" >

                        </div>

                    </ui:repeat>




                </h:panelGroup> 
                
                <style>
                    .ui-datatable-scrollable-header *,
                    .ui-datatable-scrollable-theadclone * {
                        -moz-box-sizing: content-box;
                        -webkit-box-sizing: content-box;
                        box-sizing: content-box;
                    }

                    body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
                        width: 15px;
                    }

                    .ui-panelgrid .ui-panelgrid-cell {
                        padding-top: 1px !important;
                        padding-bottom: 1px !important;
                    }

                    [role="gridcell"]{

                        padding: 0px 10px !important;
                    }
                    .content-header{     display: none; }


                    .jqplot-target {

                        font-size: 0.75em!important;
                    }

                    /*            .ui-datatable-data .ui-widget-content
                                {
                                    display: none;
                                }*/


                    body .ui-datatable th.ui-state-default[role="columnheader"], body .ui-treetable th.ui-state-default[role="columnheader"] {
                        padding-top: 0px!important;

                        padding-bottom: 0px!important;
                        /*    #d2dadc*/
                    }
                </style>

                <!--#3249: Task Reporting > Commission Reports > Add loading gif START -->
                <script>

                    window.onload = function () {
                        //                $('#loading-image').show();
                        setTimeout(function () {
                            var t = performance.timing;
                            console.log(t.loadEventEnd - t.responseEnd);
                            //      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                            document.getElementById("compCompSalesteam").style.pointerEvents = "all";
                            document.getElementById("compCompSalesteam").style.opacity = "1";

                            $('#loading-image').hide();
                            //            $('#prodrpt').css({pointer - events:"all"});
                        }, 0);
                    };


                    function hide()
                    {

                        $('#loading-image').hide();
                        document.getElementById("compCompSalesteam").style.pointerEvents = "all";
                        document.getElementById("compCompSalesteam").style.opacity = "1";
                    }

                    function processGif()
                    {

                        $('#loading-image').show();
                        document.getElementById("compCompSalesteam").style.pointerEvents = "none";
                        document.getElementById("compCompSalesteam").style.opacity = "0.5";
                    }
                </script>

                <!--#3249:   Task Reporting > Commission Reports > Add loading gif  END   -->


            </h:form>

            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
            <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />

        </div>
    </ui:define>
</ui:composition>