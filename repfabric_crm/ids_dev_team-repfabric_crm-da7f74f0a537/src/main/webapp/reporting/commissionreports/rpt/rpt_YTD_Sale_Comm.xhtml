<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: ytdcomparison.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

      <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>    YTD Sales Comparison    </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>

            <f:viewParam name="d2" value="#{yTDComparison.toDate}" />
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}" />
            <f:viewParam name="c" value="#{reportFilters.customer.compId}" />
            <f:viewParam name="d" value="#{reportFilters.distributor.compId}" />
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}" />
            <f:viewParam name="g" value="#{yTDComparison.repgrp}" />
            <f:viewAction action="#{yTDComparison.run(yTDComparison.repgrp)}"/>

        </f:metadata>
    </ui:define>
    <ui:define  name="body">

        <f:event listener="#{yTDComparison.isPageAccessible}" type="preRenderView"/>


        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->


        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4"  id="ytdSalCommrpt">

            <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                            left: 600px;
                            top: 300px;
                            width: 150px;
                            height: 150px;
                            z-index: 9999; opacity: .5 !important; " />   




            <h:form id="ytdsalescomm" >

                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="ytdsalescomm:inputCustomer  ytdsalescomm:saleTeamLookUpbtn "/>
                <p:remoteCommand name="applyDistri" actionListener="#{reportFilters.applyDist(viewCompLookupService.selectedCompany)}" update="ytdsalescomm:inputDistriName" />
                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="ytdsalescomm:inputPrincName "  />
                <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="ytdsalescomm:inputAllSalesTeam " />





                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >


                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            YTD Sales Comparison 
                            <br/>
                            As of : #{yTDComparison.toDate} 
                        </div>

                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                        <p:panelGrid id="grid1" columns="1" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;width:50%;margin-left:450px"       layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel value="Group by"/>      

                            <p:selectOneRadio value="#{yTDComparison.repgrp}"  widgetVar="grp17" layout="pageDirection"  required="true">
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/> 
                                <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                <f:selectItem   itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/> 
                                <f:selectItem itemValue="3" itemLabel="#{custom.labels.get('IDS_DISTRI')}"/>
                            </p:selectOneRadio>
                        </p:panelGrid>
                    </div> 
                    <p:spacer width="4"/>
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" >
                        <p:panelGrid id="grid" columns="2" 
                                     style="float: right;width: 350px;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>



<!--                                <p:outputLabel for="inputSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                                <h:panelGroup class="ui-inputgroup"  >
                                    <p:inputText id="inputSalesTeam" value="#{example.salesTeam.smanName}"  />
                                    <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_SALES_TEAM')}" immediate="true" 
                                                      actionListener="#{salesTeamLookupService.list('applySalesTeam',0)}" 
                                                      update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                                      styleClass="btn-info btn-xs" />
                                </h:panelGroup>-->

                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--#3473:     sales team - Sales team data we can enter manually-->

                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  id="saleTeamLookUpbtn"
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  style="width:67px"
                                                  styleClass="btn-info btn-xs"  />
                            </h:panelGroup>
                            <p:outputLabel for="inputDistriName" value="#{custom.labels.get('IDS_DISTRI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputDistriName" value="#{reportFilters.distributor.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyDistri', 3, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:commandButton  value="View Report "    id="viewreport"  style="width:30px"  styleClass="btn btn-primary btn-xs"  actionListener="#{yTDComparison.initReport(yTDComparison.repgrp)}"  onclick="processGif()" />

                            <!--#3342:Task Reporting > YTD Sales Comparison - export option  Start--> 
                            <p:column>
                                <p:commandLink id="xls" ajax="false"  onclick="PrimeFaces.monitorDownload(start, stop);" >  
                                    <p:graphicImage name="/images/excel.png" width="24"/>
                                    <pe:exporter type="xlsx" target="ytdExport"  fileName="YTD Sales Comparison" subTable="true" postProcessor="#{yTDComparison.postProcessXLS}"  />  

                                </p:commandLink> 

                                <p:commandLink id="pdf" ajax="false"   onclick="PrimeFaces.monitorDownload(start, stop);"    >  
                                    <p:graphicImage value="/resources/images/pdf.png"  width="24" />  
                                    <!--<f:setPropertyActionListener value="false" target="#{repDetailItem.name}" />-->  
                                    <pe:exporter type="pdf" target="ytdExport" fileName="SalesRepDetails" subTable="true"  preProcessor="#{yTDComparison.preProcessPDF}"    />  

                                </p:commandLink>


                            </p:column>

                            <!--#3342:Task Reporting > YTD Sales Comparison - export option  End-->
                        </p:panelGrid>


                    </div>
                </div>     


                <h:panelGroup id="tbldata">
                    <!--Display all rows-->
                    <p:dataList  type="definition" id="dtrpt1" value="#{yTDComparison.listYearlyComp}" var="commitems1"  paginator="false"  paginatorAlwaysVisible="false">
                        <p:column >
                            <div class="div-selected" style="height:25px;width: 100%;" >
                                <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                                    #{commitems1.itemname}          
                                </label>               
                            </div>

                            <p:dataTable id="rptTable" value="#{commitems1.details}" var="ytd" 
                                         emptyMessage="No data found" stickyHeader="false"  >

                                <p:column sortBy="#{ytd.customer}"    headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" rendered="#{yTDComparison.repgrp ne 2}" style="text-align: left;width: 150px"  >

                                    <h:outputLabel value=" #{ytd.customer}" />
                                </p:column>

                                <p:column sortBy="#{ytd.principal}"    headerText="#{custom.labels.get('IDS_PRINCI')}" rendered="#{yTDComparison.repgrp ne 1}" style="text-align: left;width: 150px"  >

                                    <h:outputLabel value="#{ytd.principal}" /> 

                                    <f:facet name="footer" >
                                        <h:outputLabel value="Totals :" style="float: right" rendered="#{ yTDComparison.repgrp ne 2}" class="footerFont"  >

                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>



                                <p:column sortBy="#{ytd.distirbutor}"    headerText="#{custom.labels.get('IDS_DISTRI')}" rendered="#{yTDComparison.repgrp ne 3}" style="text-align: left;width: 150px"  >

                                    <h:outputLabel value="#{ytd.distirbutor}" />
                                    <f:facet name="footer">
                                        <h:outputLabel value="Totals :" style="float: right" class="footerFont" >

                                        </h:outputLabel>
                                    </f:facet>

                                </p:column>



                                <p:column sortBy="#{ytd.ytdPrev}" headerText="YTD(Previous Year)" style="text-align: right;width: 150px"   >
                                    <h:outputLabel value="#{ytd.ytdPrev}"  >

                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0"  minFractionDigits="2"/>
                                    </h:outputLabel>
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{commitems1.totals[1]}" style="float: right" class="footerFont" >
                                            <!--<h:outputLabel value="#{yearlyComparison.t}" style="float: right"  >-->
                                            <f:convertNumber  groupingUsed="true" maxFractionDigits="0"  minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </f:facet>


                                </p:column>

                                <p:column sortBy="#{ytd.ytdCurr}" headerText="YTD(Current Year)" style="text-align: right"  width="180" >
                                    <h:outputLabel value="#{ytd.ytdCurr}" >
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0"  minFractionDigits="2"/>
                                    </h:outputLabel>
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{commitems1.totals[2]}" style="float: right" class="footerFont" >
                                            <f:convertNumber groupingUsed="true" maxFractionDigits="0"  />
                                        </h:outputLabel>
                                    </f:facet>

                                </p:column>

                                <p:column sortBy="#{ytd.prevYear}" headerText="LYT(Previous Year)"  style="text-align: right"  width="130">
                                    <h:outputLabel value="#{ytd.prevYear}" >
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0"  minFractionDigits="2"/>
                                    </h:outputLabel>
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{commitems1.totals[3]}" style="float: right" class="footerFont" >
                                            <f:convertNumber groupingUsed="true" maxFractionDigits="0"  minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>   
<!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                <p:column sortBy="#{ytd.ytdComm}" headerText="YTD(Firm #{custom.labels.get('IDS_COMMISSION')})"  style="text-align: right"  width="130">
                                    <h:outputLabel value="#{ytd.ytdComm}" >
                                        <f:convertNumber groupingUsed="true" maxFractionDigits="0"  minFractionDigits="2"/>
                                    </h:outputLabel>
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{commitems1.totals[4]}" style="float: right"  class="footerFont" >
                                            <f:convertNumber  groupingUsed="true" maxFractionDigits="0"    minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>   

                            </p:dataTable>

                        </p:column>
                    </p:dataList>

                    <!--Grand totals footer 28Nov2018-->
                    <p:dataTable emptyMessage=" " >

                        <p:column    rendered="#{yTDComparison.repgrp ne 2}" style="text-align: left;width: 150px"  >


                        </p:column>

                        <p:column     rendered="#{yTDComparison.repgrp ne 1}" style="text-align: left;width: 150px"  >



                            <f:facet name="footer" >
                                <h:outputLabel value="Grand Total :" style="float: right" rendered="#{yTDComparison.repgrp ne 2}" class="footerFont">

                                </h:outputLabel>
                            </f:facet>
                        </p:column>



                        <p:column      rendered="#{yTDComparison.repgrp ne 3}" style="text-align: left;width: 150px"  >

                            <f:facet name="footer">
                                <h:outputLabel value="Grand Total :" style="float: right" class="footerFont" >

                                </h:outputLabel>
                            </f:facet>

                        </p:column>



                        <p:column   style="text-align: right;width: 150px"   >




                            <f:facet name="footer"  >
                                <h:outputLabel value="#{yTDComparison.totprevYear}"  class="footerFont" >

                                    <f:convertNumber  groupingUsed="true" maxFractionDigits="0"    minFractionDigits="2"/>
                                </h:outputLabel>
                            </f:facet>


                        </p:column>

                        <p:column   style="text-align: right"  width="180" >


                            <f:facet name="footer">
                                <h:outputLabel value="#{yTDComparison.totytdCurr}" style="float: right" class="footerFont" >
                                    <f:convertNumber  groupingUsed="true" maxFractionDigits="0"   minFractionDigits="2"/>
                                </h:outputLabel>
                            </f:facet>

                        </p:column>

                        <p:column    style="text-align: right"  width="130">


                            <f:facet name="footer">
                                <h:outputLabel value="#{yTDComparison.totytdPrev}" style="float: right" class="footerFont" >
                                    <f:convertNumber   groupingUsed="true" maxFractionDigits="0"   minFractionDigits="2"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>   

                        <p:column    style="text-align: right"  width="130">


                            <f:facet name="footer">
                                <h:outputLabel value="#{yTDComparison.totytdComm}" style="float: right"  class="footerFont" >
                                    <f:convertNumber  groupingUsed="true" maxFractionDigits="0"   minFractionDigits="2"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>   

                    </p:dataTable>




                    <!--#3342:Task Reporting > YTD Sales Comparison - export option  Start-->

                    <p:dataTable value="#{yTDComparison.listYearlyComp}" var="commitems1" id="ytdExport"  rendered="false">



                        <p:columnGroup  type="header" class="header"  rendered="#{reportFilters.grp ne 2 || reportFilters.grp ne 1 }" >
                            <p:row>

                                <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                <p:column />

                                <p:column headerText="YTD Sales Comparison " />
                                <p:column/>

                                <p:column/>
                                <p:column headerText="As of: #{yTDComparison.toDate}" />


                            </p:row>


                            <div>

                                <p:row></p:row>

                                <p:row  class="header" id="myHeaderExp" >
                                    <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                    <p:column headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" rendered="#{yTDComparison.repgrp ne 2}" style="text-align: left;width: 150px" />
                                    <p:column  headerText="#{custom.labels.get('IDS_PRINCI')}" rendered="#{yTDComparison.repgrp ne 1}" style="text-align: left;width: 150px"  />
                                    <p:column  headerText="#{custom.labels.get('IDS_DISTRI')}" rendered="#{yTDComparison.repgrp ne 3}" style="text-align: left;width: 150px" />

                                    <p:column   headerText="YTD(Previous Year)" style="text-align: right;width: 150px"  />
                                    <p:column    headerText="YTD(Current Year)" style="text-align: right"  width="180" />
                                    <p:column   headerText="LYT(Previous Year)"  style="text-align: right"  width="130" />

                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->  
                                    <p:column   headerText="YTD(Firm #{custom.labels.get('IDS_COMMISSION')})"  style="text-align: right"  width="130" />

                                </p:row>
                            </div></p:columnGroup>

                        <p:subTable   id="YTD"   value="#{commitems1.details}" var="ytd"   >

                            <f:facet  name="header"  >
                                <p:outputLabel  value="#{yTDComparison.repgrp eq 1 ?custom.labels.get('IDS_PRINCI')
                                                         : (yTDComparison.repgrp eq 2 ? custom.labels.get('IDS_CUSTOMER') : custom.labels.get('IDS_DISTRI')) } "  />
                                <h:outputText value=": #{commitems1.itemname}" style="text-align:center" /> 


                            </f:facet>



                            <p:column sortBy="#{ytd.customer}"   rendered="#{yTDComparison.repgrp ne 2}"  >

                                <h:outputText value=" #{ytd.customer}" />
                            </p:column>

                            <p:column sortBy="#{ytd.principal}"   rendered="#{yTDComparison.repgrp ne 1}"   >

                                <h:outputText value="#{ytd.principal}" /> 


                            </p:column>   
                            <p:column sortBy="#{ytd.distirbutor}"   rendered="#{yTDComparison.repgrp ne 3}"    >

                                <h:outputText value="#{ytd.distirbutor}" />


                            </p:column>



                            <p:column sortBy="#{ytd.ytdPrev}"   >
                                <h:outputText value="#{ytd.ytdPrev}"  >

                                    <f:convertNumber groupingUsed="true" maxFractionDigits="0"  minFractionDigits="2"/>
                                </h:outputText>


                            </p:column>

                            <p:column sortBy="#{ytd.ytdCurr}" >
                                <h:outputText value="#{ytd.ytdCurr}" >
                                    <f:convertNumber groupingUsed="true" maxFractionDigits="0"  minFractionDigits="2"/>
                                </h:outputText>

                            </p:column>

                            <p:column sortBy="#{ytd.prevYear}">
                                <h:outputText value="#{ytd.prevYear}" >
                                    <f:convertNumber groupingUsed="true" maxFractionDigits="0"  minFractionDigits="2"/>
                                </h:outputText>

                            </p:column>   

                            <p:column sortBy="#{ytd.ytdComm}">
                                <h:outputText value="#{ytd.ytdComm}" >
                                    <f:convertNumber groupingUsed="true" maxFractionDigits="0"  minFractionDigits="2"/>
                                </h:outputText>

                            </p:column>   

                            <p:columnGroup type="footer">
                                <p:row> 

                                    <p:column  />
                                    <p:column  footerText="Total :" rendered="#{yTDComparison.repgrp ne 1}" style="text-align: left;width: 150px"  />

                                    <p:column   footerText="#{commitems1.totals[1]}" style="text-align: right;font-weight: bold"  />
                                    <p:column    footerText="#{commitems1.totals[2]}" style="text-align: right;font-weight: bold"  width="180" />
                                    <p:column   footerText="#{commitems1.totals[3]}"  style="text-align: right;font-weight: bold"  width="130" />

                                    <p:column   footerText="#{commitems1.totals[4]}"  style="text-align: right;font-weight: bold"  width="130" />


                                </p:row>
                            </p:columnGroup> 
                        </p:subTable>
                        <!--#2560: CRM-1567: Sales rep details report-->
                        <p:columnGroup type="footer">
                            <p:row> 

                                <p:column  />
                                <p:column  footerText="Grand Total"  />

                                <p:column   footerText="#{yTDComparison.totprevYear}" style="text-align: right;width: 150px"  />
                                <p:column    footerText="#{yTDComparison.totytdCurr}" style="text-align: right"  width="180" />
                                <p:column   footerText="#{yTDComparison.totytdPrev}"  style="text-align: right"  width="130" />

                                <p:column   footerText="#{yTDComparison.totytdComm}"  style="text-align: right"  width="130" />



                            </p:row>
                        </p:columnGroup>  


                    </p:dataTable>


                    <!--#3342:Task Reporting > YTD Sales Comparison - export option  Start-->


















                </h:panelGroup>
                <style>

                    .ui-panelgrid .ui-panelgrid-cell {
                        padding-top: 1px !important;
                        padding-bottom: 1px !important;
                    }

                    [role="gridcell"]{

                        padding: 0px 10px !important;
                    }
                    .content-header{     display: none; }


                    .jqplot-target {

                        font-size: 0.75em!important;
                    }

                    /*            .ui-datatable-data .ui-widget-content
                                {
                                    display: none;
                                }*/

                </style>



                <!--#3249: Task Reporting > Commission Reports > Add loading gif START -->
                <script>

                    window.onload = function () {
                        //                $('#loading-image').show();
                        setTimeout(function () {
                            var t = performance.timing;
                            console.log(t.loadEventEnd - t.responseEnd);
                            //      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                            document.getElementById("ytdSalCommrpt").style.pointerEvents = "all";
                            document.getElementById("ytdSalCommrpt").style.opacity = "1";

                            $('#loading-image').hide();
                            //            $('#prodrpt').css({pointer - events:"all"});
                        }, 0);
                    };


                    function hide()
                    {

                        $('#loading-image').hide();
                        document.getElementById("ytdSalCommrpt").style.pointerEvents = "all";
                        document.getElementById("ytdSalCommrpt").style.opacity = "1";
                    }

                    function processGif()
                    {
                        $('#loading-image').show();
                        document.getElementById("ytdSalCommrpt").style.pointerEvents = "none";
                        document.getElementById("ytdSalCommrpt").style.opacity = "0.5";
                    }

                    function start() {

                        PF('inYtdSalCompProgressDlg').show();
                    }

                    function stop() {
                        PF('inYtdSalCompProgressDlg').hide();
                    }



                </script>

                <!--#3249:   Task Reporting > Commission Reports > Add loading gif  END   -->



            </h:form>

            <!--#3249:   Task Reporting > Commission Reports > Add loading gif  END   -->
            <p:dialog widgetVar="inYtdSalCompProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Please wait...Exporting records" />
                        <br /><br />

                    </p:outputPanel>
                </h:form>
            </p:dialog>
            <!--#3249:   Task Reporting > Commission Reports > Add loading gif  END   -->


            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
            <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />


        </div>
    </ui:define>
</ui:composition>