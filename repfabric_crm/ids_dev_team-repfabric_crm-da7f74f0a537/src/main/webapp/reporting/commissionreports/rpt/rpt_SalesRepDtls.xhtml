<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: productdetails CUMU.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

    <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>   Sales Rep Details   </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>
            <!--          #10300  CRM-6433:-->
            <f:viewParam name="fdate" value="#{repDetailItem.fromDate}" />
            <f:viewParam name="tdate" value="#{repDetailItem.toDate}"/> 
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>
            <f:viewParam name="c" value="#{reportFilters.customer.compId}"/>
            <f:viewParam name="m" value="#{reportFilters.salesRep.userId}"/>
            <f:viewParam name="g" value="#{reportFilters.grp}"/> 

            <f:viewParam name="f" value="#{repDetailItem.salesTotalFlag}" />

            <f:viewAction action="#{repDetailItem.run()}"/>
            <!--          #10300  CRM-6433:-->
            <!--# PMS-204 sales rep details not working in rendellsales-->
            <f:viewAction action="#{reportFilters.getSetSmanName(reportFilters.salesTeam.smanId)}"  />
            <f:viewAction action="#{reportFilters.getUserName(reportFilters.salesRep.userId)}"  />
            <!--<f:viewAction action="#{reportFilters.getSelectedCustName(reportFilters.customer.compId)}" />-->
            <f:viewAction action="#{reportFilters.getPrinciName(reportFilters.pricipal.compId)}" />
            <f:viewParam name="r" value="#{repDetailItem.reducedList}" />
        </f:metadata>
    </ui:define>
    <ui:define name="body">
        <!--#2630   CRM-1628 Sa;es rep details report is a mess width:auto ;width:120% -->
        <div style="width:100%;white-space:nowrap;overflow:auto" id="repDiv" >
            <f:event listener="#{repDetailItem.isPageAccessible}" type="preRenderView"/>
            <!--#3249:#3249:     Task Reporting > Commission Reports > Add loading gif-->

            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                            left: 600px;
                            top: 300px;
                            width: 150px;
                            height: 150px;
                            z-index: 9999; opacity: .5 !important; " />

            <!--#3249:#3249:     Task Reporting > Commission Reports > Add loading gif-->

            <div class="box box-info box-body" style="vertical-align: top;width:100%;pointer-events:none;opacity:0.7"  id="salRepDetrpt">



                <h:form id="frm1">
                    <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}"  update="frm1:inputPrincName :frm1:viewreport" />
                    <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}"  update="frm1:inputAllSalesTeam :frm1:viewreport"/>
                    <!--# PMS-204 sales rep details not working in rendellsales-->
                    <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="frm1:inputCustomer :frm1:viewreport  :frm1:inputAllSalesTeam"/>       
                    <p:remoteCommand name="applyUser" actionListener="#{reportFilters.applySalesRep(userLookupService.user)}" update="frm1:inputUserName :frm1:viewreport"/>  
                    <div class="ui-g ui-fluid"  >
                        <div class="compNamebg">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            Sales Rep Details  
                            <br/>
                            <p:outputLabel  value="From: "   />
                            <p:spacer width="4px"/>
                            #{commReports.dtFrm}
                            <br/>
                            <p:outputLabel  value="To:"   />
                            <p:spacer width="22px"/>
                            <p:spacer width="4px"/>
                            #{commReports.dtTo}

                            <br/>
                            <br/>
                            <div>


                                <p:selectBooleanCheckbox  itemLabel="#{repDetailItem.reducedList ? '':''}"  
                                                          value="#{repDetailItem.reducedList}"    
                                                          id="reduceFlag"  >   
                                    <!--//      5517:  CRM-4632 FW: Commission report formatting missing headers in Excel export-->
                                    <p:ajax   event="change" process="@this"  update=":frm1"
                                              listener="#{repDetailItem.goToNavigation(repDetailItem.fromDate, repDetailItem.toDate)}"  />
                                </p:selectBooleanCheckbox> 


                                <h:outputLabel value="Show Expanded List" style="margin-left: -7px"/>
                            </div>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4" >
                            <h:panelGrid columns="3" style="margin-left: 150px">
                                <p:outputLabel value="Group by"/>
                                <p:spacer width="4px"/>
                                <!--          #10300  CRM-6433:-->
                                <p:selectOneRadio value="#{reportFilters.grp}"  widgetVar="grp" layout="pageDirection"  required="true">
                                    <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/> 
                                    <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                    <f:selectItem   itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                                    <f:selectItem itemValue="3" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}"/>


                                </p:selectOneRadio>
                            </h:panelGrid>
                        </div>


                        <p:panelGrid columns="2" style="float: right;width: 350px;margin-left: 100px"  columnClasses="ui-grid-col-5,ui-grid-col-9"   layout="grid" styleClass="box-primary no-border ui-fluid ">



                            <p:outputLabel for="inputUserName" value="Sales Rep"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputUserName" value="#{reportFilters.salesRep.userName}"  readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Sales Rep" immediate="true" 
                                                  actionListener="#{userLookupService.listSalesRep('applyUser', 'Sales Rep')}"
                                                  update=":formUserLookup :frm1" oncomplete="PF('lookupUser').show();"  
                                                  styleClass="btn-info btn-xs" style="min-width: 5.2em" />
                            </h:panelGroup>
                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
        <!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup :frm1" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" style="min-width: 5.2em"/>
                            </h:panelGroup>
                            <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" style="min-width: 5.2em"/>
                            </h:panelGroup>
                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--#3473:     sales team - Sales team data we can enter manually-->

                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true" />
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                  update=":formSalesTeamLookup"  oncomplete="PF('lookupSalesTeam').show()"  
                                                  styleClass="btn-info btn-xs"  style="width:67px;min-width: 5.2em" />


                            </h:panelGroup>
                            <h:panelGroup >

                                <!--#3249:#3249:     Task Reporting > Commission Reports > Add loading gif-->
                                <!--1580-->
                                <!--          #10300  CRM-6433:-->
                                <p:remoteCommand name="viewReport" update=":frm1:tbldata" action="#{repDetailItem.goToNavigation(repDetailItem.fromDate, repDetailItem.toDate)}"    />
                                <p:commandButton  value="View Report" type="button"   onclick="viewReport()"   style="width: 40px"  id="viewreport"  styleClass="btn btn-primary btn-xs"    />
                                &nbsp;&nbsp;&nbsp;


                                <!--//export #2468.:  Bug CRM-1523: Waterlines - printable Sales Rep details report-->

                                <p:column>

                                    <p:commandButton value="Export"   style="width:30px"  
                                                     ajax="false"  actionListener="#{repDetailItem.exportData()}"  
                                                     styleClass="btn btn-primary btn-xs" onclick="PrimeFaces.monitorDownload(start, stop);" 
                                                     />  
                                    <!--//      5517:  CRM-4632 FW: Commission report formatting missing headers in Excel export-->
      <!--                                    <p:commandLink id="xls"  ajax="false" onclick="PrimeFaces.monitorDownload(start, stop);" actionListener="#{repDetailItem.export()}" update=":frm1"  >  
                                              <p:graphicImage name="/images/excel.png" width="24"/>
                                              <f:setPropertyActionListener value="false" target="#{exporterController.customExporter}" />  
                                              <pe:exporter type="xlsx" target="#{
                                    globalParams.showTotalCommRepDtl ? (repDetailItem.reducedList ?  'salRepExpWithCommRate':'salRepReducedExpWithCommRate') 
                                        : ((!globalParams.showTotalCommRepDtl and !repDetailItem.reducedList )? 'salRepExpWithRedLt' : 'salRepExp')}"  fileName="SalesRepDetails" subTable="true" postProcessor="#{repDetailItem.postProcessXLS}"   />  

                   </p:commandLink>  -->

                                    <p:commandLink id="pdf"    ajax="false"    onclick="PrimeFaces.monitorDownload(start, stop);"  actionListener="#{repDetailItem.export()}"  >  
                                        <p:graphicImage value="/resources/images/pdf.png"  width="24" />  
                                        <!--<f:setPropertyActionListener value="false" target="#{repDetailItem.name}" />-->  
                                        <pe:exporter type="pdf" target="#{
                                                     globalParams.showTotalCommRepDtl ? (repDetailItem.reducedList ?  'salRepExpWithCommRate':'salRepReducedExpWithCommRate') 
                                                         : ((!globalParams.showTotalCommRepDtl and !repDetailItem.reducedList )? 'salRepExpWithRedLt' : 'salRepExp')}" fileName="SalesRepDetails" subTable="true"  preProcessor="#{repDetailItem.preProcessPDF}"    />  

                                    </p:commandLink>

                                </p:column>
                            </h:panelGroup>
                        </p:panelGrid>


                    </div>
                    <!--            </h:form>
                    
                    
                    
                                <h:form id="details">-->
                    <!--#2630   CRM-1628 Sa;es rep details report is a mess width:auto ;width:120% -->
                    <div class="box box-info box-body" style="vertical-align: top;width:100%">
                        <!--                         <div class="ui-g ui-fluid" id="dt" style="overflow-x:scroll;transform:rotateX(180deg);">
                        
                                            <div class="ui-g ui-fluid" id="dt1" style="transform:rotateX(180deg);">-->


                        <h:panelGroup id="tbldata"  >


                            <p:dataList type="definition" id="dtSalRepDet" value="#{repDetailItem.grpItems}" var="repDetailItem1" style="width:auto"   >
                                <div class="div-selected" style="height:25px;width: 100%;" >
                                    <label style="font-size: 1.3em;font-weight: bold;color: #384c55;line-height: 25px;padding-left: 5px;width:120% ">
                                        #{repDetailItem1.itemName} 
                                    </label>               
                                </div>
                                <!--          #10300  CRM-6433:-->
                                <p:dataTable  id="salRepDetRpt" value="#{repDetailItem1.details}" var="repDetails" draggableColumns="true"  sortBy="#{(reportFilters.grp eq 1? repDetails.customer :repDetails.principal)}" emptyMessage="No data found" style="width:auto" >
                                    <!--//1478-->
                                    <p:column  sortBy="#{repDetails.principal}"  
                                               headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left" style="width:150px"  rendered="#{reportFilters.grp ne 1}">
                                        <h:outputLabel value="#{repDetails.principal}"  />       
                                    </p:column>
                                    <!--//1478-->
                                    <!--          #10300  CRM-6433:-->
                                    <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                    <p:column  sortBy="#{repDetails.customer}"
                                               headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" class="col_left" style="width:150px" rendered="#{reportFilters.grp ne 2}" >
                                        <h:outputLabel value="#{repDetails.customer}"  />       
                                    </p:column>

                                    <!--//1478-->
                                    <!--          #10300  CRM-6433:-->
                                    <p:column  sortBy="#{repDetails.teamName}" 
                                               headerText="#{custom.labels.get('IDS_SALES_TEAM')}" class="col_left" style="width:150px" rendered="#{reportFilters.grp ne 3 }" >
                                        <h:outputLabel value="#{repDetails.teamName}"  />          
                                    </p:column>


                                    <!--//1478-->
                                    <!--          
                                    786 225548 : Everests: FF en SMK.#2 , added distributor to the query //17-9-2018 .-->
                                    <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                    <p:column  sortBy="#{repDetails.distName}" 
                                               headerText="#{custom.labels.get('IDS_DISTRI')}"   class="col_left"  style="width:150px" rendered="#{repDetailItem.reducedList}" >
                                        <h:outputLabel value="#{repDetails.distName}"  />          
                                    </p:column>
                                    <!--//1478-->
                                    <!-- 29/01/2018 - Feature Request -  Add column for invoice amount in sales rep commission details report + secondary customer-->
                                    <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                    <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                    <p:column sortBy="#{repDetails.secondaryCustomer}"
                                              headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}"   rendered="#{repDetailItem.reducedList}" style="width:150px" class="col_left"  >
                                        <h:outputLabel value="#{repDetails.secondaryCustomer}"  />       
                                    </p:column>
                                    <!--//EkMicro issue , part no added in Sales rep report-->
                                    <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                    <p:column   headerText="#{custom.labels.get('IDS_PART_NUM')}"   rendered="#{repDetailItem.reducedList}"  class="col_left" sortBy="#{repDetails.partno}" width="130"  >  
                                        <h:outputLabel value="#{repDetails.partno}"  />       
                                    </p:column>
                                    <!--//1478-->
                                    <!--//    #12428 ESCALATIONS CRM-7445   Nolan: ability from sales rep details to view upstream documents, automat-->
                                    <p:column   headerText="Invoice" class="col_left" sortBy="#{repDetails.invNo}" style="width:100px"   >
                                        <p:commandLink  style="font-weight: normal" action="#{repDetailItem.loadHeader(repDetails.invNo)}" actionListener="#{repDetailItem.populateLinkedDocuments(repDetails.commRcommId,repDetails.princiId,repDetails.custId,repDetails.invNo,repDetails.commPoOrder)}"  update=":linkedDocForm:lnkReportList  dlgHeader"  oncomplete="PF('linkedDlg').show()" > 
                                            #{repDetails.invNo}
                                        </p:commandLink>

                                    </p:column>

                                    <!--//1478-->                  
                                    <!--5318: date format issue-->
                                    <p:column   headerText="Inv Date" class="col_center" sortBy="#{repDetails.date}" style="width:100px"
                                                >
                                        <h:outputLabel value="#{repDetails.date}"  >
                                            <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                        </h:outputLabel>

                                    </p:column>
                                    <!--//1478-->
                                    <!--5318: date format issue-->
                                    <p:column   headerText="Chk Date" class="col_center" sortBy="#{repDetails.chkDate}" style="width:100px"
                                                >
                                        <h:outputLabel value="#{repDetails.chkDate}"  >
                                            <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                        </h:outputLabel>
                                        <f:facet name="footer">
                                            <h:outputLabel value="Total" style="float: right;font-weight: bold"  />
                                        </f:facet>
                                    </p:column>
                                    <!-- 29/01/2018 - Feature Request -  Add column for invoice amount in sales rep commission details report + secondary customer-->
                                    <!--  Bug 288 #549804 Dashboard db :need sales rep details to the 2 decimal -->

                                    <!--#2725: .CRM-1627: Fwd: Shared Commission Splits column shifted -->
                                    <!--//1478-->
                                    <p:column headerText="Invoice Amt" class="col_right" sortBy="#{repDetails.invAmt}"
                                              width="130"     >
                                        <h:outputLabel value="#{repDetails.invAmt}"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  
                                        <!--//#421825 salesrepdetails:needs invoice sub/grandtotals-->
                                        <f:facet name="footer">
                                            <h:outputLabel value="#{repDetailItem1.totals[2]}" style="float: right;font-weight: bold"  >
                                                <f:convertNumber minFractionDigits="2"/>
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>
                                    <!--//1580-->
                                    <!--//    Feature #3091:-->  
                                    <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23--> 
                                    <p:column headerText="#{custom.labels.get('IDS_COMM')} Rate %" class="col_right" sortBy="#{repDetails.commPct}"
                                              width="130"  rendered="#{globalParams.showTotalCommRepDtl}"   >
                                        <h:outputLabel value="#{repDetails.commPct}"  >
                                            <!--3987:   CRM-4124: Sales Rep Detail Report - column widths too narrow in PDF export-->

                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  
                                        <!--//#421825 salesrepdetails:needs invoice sub/grandtotals-->
                                        <f:facet name="footer">
<!--                                            <h:outputLabel value="#{repDetailItem1.totals[2]}" style="float: right;font-weight: bold"  >
                                                <f:convertNumber minFractionDigits="2"/>
                                            </h:outputLabel>-->
                                        </f:facet>
                                    </p:column>
                                    <!--1580-->
                                    <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                    <p:column headerText="#{custom.labels.get('IDS_COMM')} Amt" class="col_right" sortBy="#{repDetails.commissionAmt}"
                                              width="130"  rendered="#{globalParams.showTotalCommRepDtl}"   >
                                        <h:outputLabel value="#{repDetails.commissionAmt}"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  
                                        <!--//#421825 salesrepdetails:needs invoice sub/grandtotals-->
                                        <f:facet name="footer">
                                            <h:outputLabel value="#{repDetailItem1.totals[6]}" style="float: right;font-weight: bold"  >
                                                <f:convertNumber minFractionDigits="2"/>
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>


                                    <!--//1478-->
                                    <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                    <p:column   headerText="Split (%)" class="col_right" sortBy="#{repDetails.pct}"
                                                style="width:100px"  rendered="#{globalParams.showSplitOnSalesAmt}"  >
                                        <h:outputLabel value="#{repDetails.pct}"  >
                                            <!--<f:convertNumber maxFractionDigits="0"/>-->
                                        </h:outputLabel>

                                    </p:column>
                                    <!--//1478-->
                                    <!--#2725: CRM-1627: Fwd: Shared Commission Splits new  column added -->
                                    <p:column headerText="Split Inv.Amt" class="col_right" sortBy="#{repDetails.splitInvAmt}"
                                              width="140"  rendered="#{globalParams.showSplitOnSalesAmt}" >
                                        <h:outputLabel value="#{repDetails.splitInvAmt}"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  
                                        <!--//#421825 salesrepdetails:needs invoice sub/grandtotals-->
                                        <f:facet name="footer">
                                            <h:outputLabel value="#{repDetailItem1.totals[3]}" style="float: right;font-weight: bold"  >
                                                <f:convertNumber minFractionDigits="2"/>
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>
                                    <!--//1478-->
                                    <!--1580 : subotals-->
                                    <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                    <p:column headerText="Rep #{custom.labels.get('IDS_COMM')} Amt" class="col_right" sortBy="#{repDetails.commAmt}"
                                              width="110"       >
                                        <h:outputLabel value="#{repDetails.commAmt}"  >
                                            <!--<f:convertNumber minFractionDigits="2"/>-->
                                        </h:outputLabel>    
                                        <f:facet name="footer">
                                            <h:outputLabel value="#{repDetailItem1.totals[1]}" style="float: right;font-weight: bold"  >
                                                <f:convertNumber minFractionDigits="2"/>
                                            </h:outputLabel>
                                        </f:facet>
                                    </p:column>
                                    <!--//1478-->
                                    <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                    <!--1580 : subotals-->
                                    <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                    <p:column headerText="PO #" class="col_right" sortBy="#{repDetails.commPoOrder}"   rendered="#{repDetailItem.reducedList}"
                                              width="110"     >
                                        <h:outputLabel value="#{repDetails.commPoOrder}"  >

                                        </h:outputLabel>    
                                    </p:column>
                                    <!--//1478-->
                                    <!--1580 : subotals-->
                                    <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                    <p:column headerText="SO #" class="col_right" sortBy="#{repDetails.commSalOrder}"  rendered="#{repDetailItem.reducedList}"
                                              width="110"   >
                                        <h:outputLabel value="#{repDetails.commSalOrder}"  >

                                        </h:outputLabel>    
                                    </p:column>
                                    <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                                    <p:column headerText="Total Sales" class="col_right" sortBy="#{repDetails.commSalesTotal}"  rendered="#{repDetailItem.salesTotalFlag eq 1}" style="width:100px" >

                                        <h:outputLabel value="#{repDetails.commSalesTotal}"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>    
                                        <f:facet name="footer">
                                            <h:outputLabel value="#{repDetailItem1.totals[4]}" style="float: right;font-weight: bold"  >
                                                <f:convertNumber minFractionDigits="2"/>
                                            </h:outputLabel>
                                        </f:facet>

                                    </p:column>

                                    <!--//1478-->
                                    <!--1580 : subotals-->
                                    <p:column headerText="Credited Sales" class="col_right" sortBy="#{repDetails.commSalesCredited}"  rendered="#{repDetailItem.salesTotalFlag eq 1}" style="width:100px" >

                                        <h:outputLabel value="#{repDetails.commSalesCredited}"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel> 
                                        <f:facet name="footer">
                                            <h:outputLabel value="#{repDetailItem1.totals[5]}" style="float: right;font-weight: bold"  >
                                                <f:convertNumber minFractionDigits="2"/>
                                            </h:outputLabel>
                                        </f:facet>

                                    </p:column>
                                    <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                    <!--1580 : subotals-->
                                    <p:summaryRow   >
                                        <p:column  />
                                        <p:column   />
                                        <p:column   />
                                        <p:column    />
                                        <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                        <p:column   rendered="#{repDetailItem.reducedList}"/>
                                        <p:column    rendered="#{repDetailItem.reducedList}"/>
                                        <p:column   rendered="#{repDetailItem.reducedList}"/>


                                        <p:column>
                                            <h:outputText value="Sub Total" style="float: right;font-weight: bold" />

                                        </p:column>
                                        <!--          #10300  CRM-6433:-->
                                        <p:column>
                                            <h:outputText value="#{repDetailItem1.subGrpTotals.get((reportFilters.grp eq 1? repDetails.custId :repDetails.princiId))[1]}" style="float: right;font-weight: bold"  >
                                                <f:convertNumber minFractionDigits="2"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column   rendered="#{globalParams.showTotalCommRepDtl}">

                                        </p:column  >
                                        <!--          #10300  CRM-6433:-->
                                        <p:column  rendered="#{globalParams.showTotalCommRepDtl}">
                                            <h:outputText value="#{repDetailItem1.subGrpTotals.get((reportFilters.grp eq 1? repDetails.custId :repDetails.princiId))[5]}" style="float: right;font-weight: bold"  >
                                                <f:convertNumber minFractionDigits="2"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column   rendered="#{globalParams.showSplitOnSalesAmt}" >

                                        </p:column>
                                        <p:column  rendered="#{globalParams.showSplitOnSalesAmt}">
                                            <h:outputText value="#{repDetailItem1.subGrpTotals.get((reportFilters.grp eq 1? repDetails.custId :repDetails.princiId))[2]}" style="float: right;font-weight: bold"  >
                                                <f:convertNumber minFractionDigits="2"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column>
                                            <!--          #10300  CRM-6433:-->
                                            <h:outputText value="#{repDetailItem1.subGrpTotals.get((reportFilters.grp eq 1? repDetails.custId :repDetails.princiId))[0]}" style="float: right;font-weight: bold"  >
                                                <f:convertNumber minFractionDigits="2"/>
                                            </h:outputText>
                                        </p:column>
                                        <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                        <p:column rendered="#{repDetailItem.reducedList}" />
                                        <p:column rendered="#{repDetailItem.reducedList}">


 <!--<h:outputText value="#{repDetails.compTotals}" />-->
                                        </p:column>
                                        <!--          #10300  CRM-6433:-->
                                        <p:column  rendered="#{repDetailItem.salesTotalFlag eq 1}" >
                                            <h:outputText value="#{repDetailItem1.subGrpTotals.get((reportFilters.grp eq 1? repDetails.custId :repDetails.princiId))[3]}" style="float: right;font-weight: bold"  >
                                                <f:convertNumber minFractionDigits="2"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column rendered="#{repDetailItem.salesTotalFlag eq 1}" >
                                            <h:outputText value="#{repDetailItem1.subGrpTotals.get((reportFilters.grp eq 1? repDetails.custId :repDetails.princiId))[4]}" style="float: right;font-weight: bold"  >
                                                <f:convertNumber minFractionDigits="2"/>
                                            </h:outputText>
                                        </p:column>
                                    </p:summaryRow>

                                </p:dataTable>

                            </p:dataList>

                            <!--          #10300  CRM-6433:-->
                            <!--#2560: CRM-1567: Sales rep details report-->
                            <p:dataTable emptyMessage=" " style="width:100%;">

                                <p:column  rendered="#{reportFilters.grp ne 1}"  
                                           class="col_left" style="width:150px" >

                                </p:column>

                                <p:column rendered="#{reportFilters.grp ne 2}" 
                                          class="col_left" style="width:150px" >

                                </p:column>


                                <p:column rendered="#{reportFilters.grp ne 3}" 
                                          class="col_left" style="width:150px">

                                </p:column>
                                <!--                     786 225548 : Everests: FF en SMK.#2 , added distributor to the query //17-9-2018 .-->
<!--                                <p:column  style="#{globalParams.showSplitOnSalesAmt ?'width:50px':'width:180px'}"
                                           class="col_left"  >

                                </p:column>-->

                                <!-- 29/01/2018 - Feature Request -  Add column for invoice amount in sales rep commission details report + secondary customer-->
                                <!--1580 : subotals-->
                                <p:column  rendered="#{repDetailItem.reducedList}"
                                           class="col_left"  style="#{globalParams.showSplitOnSalesAmt ?'width:130px':'width:180px'}"  >

                                </p:column>
                                <!--//EkMicro issue , part no added in Sales rep report-->
                                <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                <p:column  rendered="#{repDetailItem.reducedList}"  class="col_left" style="#{globalParams.showSplitOnSalesAmt ?'width:130px':'width:180px'}"  >

                                </p:column>
                                <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->
                                <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                <p:column  rendered="#{repDetailItem.reducedList}"   class="col_left"  style="#{globalParams.showSplitOnSalesAmt ?'width:130px':'width:180px'}">

                                </p:column>
                                <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                <p:column    class="col_center" 
                                             style="#{globalParams.showSplitOnSalesAmt ?'width:130px':'width:130px'}" >

                                </p:column>

                                <p:column   class="col_center" 
                                            style="width:110px"   >
                                    <f:facet name="footer"  >
                                        <h:outputLabel value="Grand Total :"  class="footerFont" >


                                        </h:outputLabel>
                                    </f:facet>
                                    <p:outputLabel value="" />

                                </p:column>

                                <!--                                <p:column    class="col_right" 
                                                                             >
                                
                                
                                
                                                                </p:column>-->



                                <p:column  style="text-align:right;width:150px">
                                    <f:facet name="footer"  >
                                        <h:outputLabel value="#{repDetailItem.invTotals}"  class="footerFont" >

                                            <f:convertNumber  groupingUsed="true" maxFractionDigits="0"    minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>

                                <!--1580 : subotals-->

                                <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                <p:column    class="col_right" rendered="#{globalParams.showTotalCommRepDtl}" style="width:130px">
                                </p:column>
                                <!--//               #13769 ESCALATIONS CRM-8090   Commissions Report -> Sales Rep Detail -> No Comm Grand Total-->
                                <p:column  class="col_right" rendered="#{globalParams.showTotalCommRepDtl}" style="width:130px">
                                    <f:facet name="footer"  >
                                        <h:outputLabel value="#{repDetailItem.commAmtTotals}"  class="footerFont" >

                                            <f:convertNumber  groupingUsed="true" maxFractionDigits="0"    minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </f:facet>

                                </p:column>
                                <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                <p:column    class="col_right" rendered="#{globalParams.showSplitOnSalesAmt}" style="width:100px">
                                </p:column>
                                <!--// 5577 : Sales rep details grand totals missing-->
                                <p:column    class="col_right"   rendered="#{globalParams.showSplitOnSalesAmt}" style="width:110px">
                                    <f:facet name="footer"  >
                                        <h:outputLabel value="#{repDetailItem.splitInvAmtTotal}"  class="footerFont" >

                                            <f:convertNumber  groupingUsed="true" maxFractionDigits="0"    minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>
                                <p:column  style="text-align: right;width:110px">
                                    <f:facet name="footer"  >
                                        <!--//               #13769 ESCALATIONS CRM-8090   Commissions Report -> Sales Rep Detail -> No Comm Grand Total-->
                                        <h:outputLabel value="#{repDetailItem.repcomAmtTotals}"  class="footerFont" >

                                            <f:convertNumber  groupingUsed="true" maxFractionDigits="0"    minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>
                                <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                <p:column   rendered="#{repDetailItem.reducedList}" class="col_right"  style="width:110px"  >
                                </p:column>
                                <p:column   rendered="#{repDetailItem.reducedList}" class="col_right" style="width:110px" >
                                </p:column>
                                <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->
                                <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->
                                <!--// 5577 : Sales rep details grand totals missing-->
                                <p:column      rendered="#{repDetailItem.salesTotalFlag eq 1}" style="width:110px" >
                                    <f:facet name="footer"  >
                                        <h:outputLabel value="#{repDetailItem.salTotal}"  class="footerFont" >

                                            <f:convertNumber  groupingUsed="true" maxFractionDigits="0"    minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>
                                <p:column    rendered="#{repDetailItem.salesTotalFlag eq 1}" style="width:110px">
                                    <f:facet name="footer"  >
                                        <h:outputLabel value="#{repDetailItem.creidtedSalTotal}"  class="footerFont" >

                                            <f:convertNumber  groupingUsed="true" maxFractionDigits="0"    minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>
                            </p:dataTable>







                            <!--//export #2468:  Bug CRM-1523: Waterlines - printable Sales Rep details report-->

                            <p:dataTable value="#{repDetailItem.grpItems}" var="repDetailItem1" id="salRepExp"  rendered="false">


                                <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                <!--          #10300  CRM-6433:-->
                                <p:columnGroup  type="header" class="header"  rendered="#{reportFilters.grp ne 2 || reportFilters.grp ne 1 }" >
                                    <p:row>

                                        <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                        <p:column />
                                        <p:column />
                                        <p:column headerText="Sales Rep Details Report" />
                                        <p:column/>
                                        <p:column/>
                                        <p:column/>

                                        <p:column headerText="From: #{commReports.dtFrm}" />

                                        <p:column />
                                        <p:column headerText="To: #{commReports.dtTo}" />
                                        <p:column />
                                        <p:column />
                                        <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                        <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                        <p:column  rendered="#{globalParams.showTotalCommRepDtl }" /> 
                                        <p:column  rendered="#{globalParams.showTotalCommRepDtl}"/> 

                                        <p:column rendered="#{globalParams.showSplitOnSalesAmt}"  /> 
                                        <p:column  rendered="#{globalParams.showSplitOnSalesAmt}" /> 
                                        <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                                        <p:column  rendered="#{repDetailItem.salesTotalFlag eq 1}" /> 
                                        <p:column  rendered="#{repDetailItem.salesTotalFlag eq 1}" /> 
                                    </p:row>


                                    <div>

                                        <p:row></p:row>
                                        <!--          #10300  CRM-6433:-->
                                        <p:row  class="header" id="myHeaderExp" >
                                            <p:column rendered="#{reportFilters.grp ne 1}" sortBy="#{repDetails.principal}"  
                                                      headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left" width="320" />
                                            <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                            <p:column rendered="#{reportFilters.grp ne 2}" sortBy="#{repDetails.customer}"

                                                      headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" class="col_left" width="320" />
                                            <p:column rendered="#{reportFilters.grp ne 3}" sortBy="#{repDetails.teamName}" 
                                                      headerText="#{custom.labels.get('IDS_SALES_TEAM')}" class="col_left" width="240" />
                                            <p:column  sortBy="#{repDetails.distName}" rendered="#{repDetailItem.reducedList}"
                                                       headerText="#{custom.labels.get('IDS_DISTRI')}" class="col_left" width="240" />
                                            <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                            <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                            <p:column sortBy="#{repDetails.secondaryCustomer}" rendered="#{repDetailItem.reducedList}"
                                                      headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" class="col_left" width="220" />
                                            <p:column  rendered="#{repDetailItem.reducedList}"  headerText="#{custom.labels.get('IDS_PART_NUM')}" class="col_left" sortBy="#{repDetails.partno}" width="220" />
                                            <p:column   headerText="Invoice" class="col_left" sortBy="#{repDetails.invNo}" width="170" />
                                            <p:column   headerText="Inv Date" class="col_center" sortBy="#{repDetails.date}"
                                                        width="220" />
                                            <p:column   headerText="Chk Date" class="col_center" sortBy="#{repDetails.chkDate}"
                                                        width="100" />
                                            <p:column headerText="Invoice Amt" class="col_right" sortBy="#{repDetails.invAmt}"
                                                      width="100" />
                                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                            <p:column headerText="#{custom.labels.get('IDS_COMM')} Rate (%)" class="col_right" sortBy="#{repDetails.commPct}"
                                                      width="100"  rendered="#{globalParams.showTotalCommRepDtl}"/>
                                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                            <p:column headerText="#{custom.labels.get('IDS_COMM')} Amt" class="col_right" sortBy="#{repDetails.commissionAmt}"
                                                      width="100"  rendered="#{globalParams.showTotalCommRepDtl}" />
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->


                                            <p:column   headerText="Split (%)" class="col_right" sortBy="#{repDetails.pct}"
                                                        width="100" rendered="#{globalParams.showSplitOnSalesAmt}"/>

                                            <p:column  headerText="Split Inv. Amt" class="col_right" sortBy="#{repDetails.splitInvAmt}"
                                                       width="100" rendered="#{globalParams.showSplitOnSalesAmt}"/>

                                            <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                            <!--//1580 - sub groups-->
                                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                            <p:column headerText="Rep #{custom.labels.get('IDS_COMM')} Amt" class="col_right" sortBy="#{repDetails.commAmt}"
                                                      width="100" />   
                                            <p:column   headerText="PO #" class="col_center" sortBy="#{repDetails.commPoOrder}" rendered="#{repDetailItem.reducedList}"
                                                        width="220" />
                                            <p:column   headerText="SO #" class="col_center" sortBy="#{repDetails.commSalOrder}" rendered="#{repDetailItem.reducedList}"
                                                        width="220" />
                                            <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                                            <p:column headerText="Total Sales"  rendered="#{repDetailItem.salesTotalFlag eq 1}"/>
                                            <p:column headerText="Credited Sales" rendered="#{repDetailItem.salesTotalFlag eq 1}"/>
                                            <p:column  /> 
                                        </p:row>
                                    </div></p:columnGroup>

                                <p:subTable  value="#{repDetailItem1.details}" var="repDetails"  >

                                    <!--          #10300  CRM-6433:-->
                                    <f:facet  name="header"  >
                                        <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                        <!--<div class="div-selected" style="height:25px;width: 100%;" >-->
                                        <!--                                    <label style="font-size: 1.3em;font-weight: bold;color: #384c55;line-height: 25px;padding-left: 5px;">-->
                                        <p:outputLabel  value="#{reportFilters.grp eq 1 ?custom.labels.get('IDS_PRINCI')
                                                                 : reportFilters.grp eq 2 ? custom.labels.get('IDS_DB_CUSTOMER') : custom.labels.get('IDS_SALES_TEAM') } "  />:   <p:outputLabel  value=" #{repDetailItem1.itemName}"       />   
                                        <!--</label>-->               
                                        <!--</div>-->

                                    </f:facet>
                                    <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                    <p:column rendered="#{reportFilters.grp ne 1}"  class="col_left" width="320" >
                                        <h:outputLabel value="#{repDetails.principal}"  />       
                                    </p:column>

                                    <p:column rendered="#{reportFilters.grp ne 2}" class="col_left" width="320" >
                                        <h:outputLabel value="#{repDetails.customer}"  />       
                                    </p:column>


                                    <p:column rendered="#{reportFilters.grp ne 3}"  class="col_left" width="240" >
                                        <h:outputLabel value="#{repDetails.teamName}"  />          
                                    </p:column>
                                    <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                    <!--                     786 225548 : Everests: FF en SMK.#2 , added distributor to the query //17-9-2018 .-->
                                    <p:column  class="col_left" width="240" rendered="#{repDetailItem.reducedList}"   >
                                        <h:outputLabel value="#{repDetails.distName}"  />          
                                    </p:column>

                                    <!-- 29/01/2018 - Feature Request -  Add column for invoice amount in sales rep commission details report + secondary customer-->
                                    <p:column  class="col_left" width="220"  rendered="#{repDetailItem.reducedList}"   >
                                        <h:outputLabel value="#{repDetails.secondaryCustomer}"  />       
                                    </p:column>
                                    <!--//EkMicro issue , part no added in Sales rep report-->
                                    <p:column    class="col_left" width="220"  rendered="#{repDetailItem.reducedList}" >
                                        <h:outputLabel value="#{repDetails.partno}"  />       
                                    </p:column>

                                    <p:column   class="col_left"  width="170" >
                                        <h:outputLabel value="#{repDetails.invNo}"  />       
                                    </p:column>

                                    <p:column  class="col_center" 
                                               width="220" >
                                        <h:outputLabel value="#{repDetails.date}"  >
                                            <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                        </h:outputLabel>

                                    </p:column>

                                    <p:column   class="col_center" 
                                                width="100" >
                                        <h:outputLabel value="#{repDetails.chkDate}"  >
                                            <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                        </h:outputLabel>

                                    </p:column>

                                    <p:column 
                                        width="100" >
                                        <h:outputLabel value="#{repDetails.invAmt}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  
                                        <!--//#421825 salesrepdetails:needs invoice sub/grandtotals-->
                                        <!--1580 : subotals-->
                                    </p:column>
                                    <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                    <p:column  rendered="#{globalParams.showTotalCommRepDtl}" >
                                        <h:outputLabel value="#{repDetails.commPct}"  style="text-align:right"  >
                                            <!--<f:convertNumber minFractionDigits="0"/>-->
                                        </h:outputLabel> 


                                    </p:column>
                                    <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->
                                    <!--Bug #3987:    CRM-4124: Sales Rep Detail Report - column widths too narrow in PDF export-->

                                    <p:column class="col_right"  rendered="#{globalParams.showTotalCommRepDtl}" >
                                        <h:outputLabel value="#{repDetails.commissionAmt}"  style="text-align:right"  >

                                            <!--Bug #3987:    CRM-4124: Sales Rep Detail Report - column widths too narrow in PDF export-->
                                            <!--<f:convertNumber minFractionDigits="2"/>-->
                                        </h:outputLabel> 



                                    </p:column>
                                    <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->
                                    <!--//    Feature #3091:-->  
                                    <p:column   class="col_right" 
                                                width="100" rendered="#{globalParams.showSplitOnSalesAmt}" >
                                        <h:outputText value="#{repDetails.pct}" style="text-align:right" >
                                            <!--Bug #3987:    CRM-4124: Sales Rep Detail Report - column widths too narrow in PDF export-->

                                            <!--<f:convertNumber maxFractionDigits="2"/>-->
                                        </h:outputText>

                                    </p:column>

                                    <!-- 29/01/2018 - Feature Request -  Add column for invoice amount in sales rep commission details report + secondary customer-->
                                    <!--  Bug 288 #549804 Dashboard db :need sales rep details to the 2 decimal -->

                                    <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                    <p:column  class="col_right" sortBy="#{repDetails.splitInvAmt}"
                                               rendered="#{globalParams.showSplitOnSalesAmt}"  >
                                        <h:outputLabel value="#{repDetails.splitInvAmt}"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>

                                    <p:column  class="col_right" 
                                               width="100"  >
                                        <h:outputLabel value="#{repDetails.commAmt}"  style="text-align:right"  >
                                            <!--<f:convertNumber minFractionDigits="2"/>-->
                                        </h:outputLabel>    

                                    </p:column>
                                    <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                    <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                    <p:column 
                                        width="100" rendered="#{repDetailItem.reducedList}" >
                                        <h:outputLabel value="#{repDetails.commPoOrder}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>
                                    <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                    <p:column 
                                        width="100" rendered="#{repDetailItem.reducedList}" >
                                        <h:outputLabel value="#{repDetails.commSalOrder}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>
                                    <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                                    <p:column 
                                        width="100" rendered="#{repDetailItem.salesTotalFlag eq 1}"  >
                                        <h:outputLabel value="#{repDetails.commSalesTotal}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>
                                    <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->
                                    <!--1580 : subotals-->
                                    <p:column 
                                        width="100" rendered="#{repDetailItem.salesTotalFlag eq 1}" >
                                        <h:outputLabel value="#{repDetails.commSalesCredited}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>



                                    <p:columnGroup type="footer" >

                                        <p:row   rendered="true"> 

                                            <p:column  />
                                            <p:column  />
                                            <p:column  />
                                            <p:column  /> 
                                            <p:column  /> 
                                            <p:column  /> 
                                            <p:column  /> 

                                            <p:column footerText="Total :"
                                                      style="float: right"  class="footerFont"/>

                                            <p:column footerText="#{repDetailItem1.totals[2]}"  style="text-align: right"/>
                                            <!--//#421825 salesrepdetails:needs invoice sub/grandtotals-->
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                            <p:column  footerText="#{globalParams.showSplitOnSalesAmt?'':repDetailItem1.totals[1]}"/>
                                            <p:column footerText="#{globalParams.showSplitOnSalesAmt?repDetailItem.convertNumber(repDetailItem1.totals[3]):''}" rendered="#{globalParams.showSplitOnSalesAmt}"  style="text-align: right"/>
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                            <p:column footerText="#{globalParams.showSplitOnSalesAmt?repDetailItem1.totals[1]:''}" rendered="#{globalParams.showSplitOnSalesAmt}"  style="text-align: right" />
                                            <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                            <p:column  footerText="#{((repDetailItem.salesTotalFlag eq 1) and !(globalParams.showSplitOnSalesAmt)) ?  repDetailItem1.totals[4] :''}" rendered="false"/> 
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                            <p:column  footerText="#{((repDetailItem.salesTotalFlag eq 1) and !(globalParams.showSplitOnSalesAmt)) ? repDetailItem1.totals[5] : ''}"  rendered="false"/> 

                                            <!--footer issue-->
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->
                                            <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                            <p:column  footerText="#{((repDetailItem.salesTotalFlag eq 1) and (globalParams.showSplitOnSalesAmt)) ? repDetailItem1.totals[4] :''}" />
                                            <p:column  footerText="#{((repDetailItem.salesTotalFlag eq 1) and (globalParams.showSplitOnSalesAmt)) ? repDetailItem1.totals[5] :''}" />

                                            <p:column  footerText="#{repDetailItem.salesTotalFlag eq 1 ? '' :''}" />
                                            <p:column  footerText="#{repDetailItem.salesTotalFlag eq 1 ? '' :''}" />

                                        </p:row>
                                    </p:columnGroup> 


                                </p:subTable>
                                <!--#2560: CRM-1567: Sales rep details report-->
                                <p:columnGroup type="footer">
                                    <p:row> 

                                        <p:column  />
                                        <p:column  />
                                        <p:column  />
                                        <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                                        <p:column  rendered="#{repDetailItem.reducedList}" exportable="#{repDetailItem.reducedList}"  /> 
                                        <p:column rendered="#{repDetailItem.reducedList}" exportable="#{repDetailItem.reducedList}"  /> 
                                        <p:column rendered="#{repDetailItem.reducedList}"  exportable="#{repDetailItem.reducedList}" /> 
                                        <p:column  /> 
                                        <!--1580 : subotals-->
                                        <p:column footerText="Grand Total :"
                                                  style="float: right"  class="footerFont"/>
                                        <!--                                        <p:column  />-->
                                        <p:column footerText="#{repDetailItem.invTotals}"  style="text-align: right"/>
                                        <p:column    class="col_right" rendered="#{globalParams.showTotalCommRepDtl}" style="width:130px">
                                        </p:column>
                                        <!--//               #13769 ESCALATIONS CRM-8090   Commissions Report -> Sales Rep Detail -> No Comm Grand Total-->
                                        <p:column   footerText="#{repDetailItem.commAmtTotals}"  class="col_right" rendered="#{globalParams.showTotalCommRepDtl}" style="width:130px">
                                        </p:column>
                                        <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                        <p:column    class="col_right" rendered="#{globalParams.showSplitOnSalesAmt}" style="width:130px">
                                        </p:column>
                                        <p:column   footerText="#{repDetailItem.convertNumber(repDetailItem.splitInvAmtTotal)}"  class="col_right" rendered="#{globalParams.showSplitOnSalesAmt}" style="width:130px">
                                        </p:column>
                                        <!--//               #13769 ESCALATIONS CRM-8090   Commissions Report -> Sales Rep Detail -> No Comm Grand Total-->
                                        <p:column footerText="#{repDetailItem.repcomAmtTotals}"  style="text-align: right;width:130px" />
                                        <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                        <p:column rendered="#{repDetailItem.reducedList}" exportable="#{repDetailItem.reducedList}"    /> 
                                        <p:column rendered="#{repDetailItem.reducedList}" exportable="#{repDetailItem.reducedList}"   /> 
                                        <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->
                                        <!--// 5577 : Sales rep details grand totals missing-->

                                        <p:column  footerText="#{repDetailItem.salTotal}"     rendered="#{repDetailItem.salesTotalFlag eq 1}" /> 
                                        <p:column  footerText="#{repDetailItem.creidtedSalTotal}"   rendered="#{repDetailItem.salesTotalFlag eq 1}" /> 
                                    </p:row>
                                </p:columnGroup>  


                            </p:dataTable>

                            <!--//export #2468:  Bug CRM-1523: Waterlines - printable Sales Rep details report-->
                            <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->                                                                                            
                            <p:dataTable value="#{repDetailItem.grpItems}" var="repDetailItem1" id="salRepExpWithCommRate" rendered="false" >




                                <p:columnGroup  type="header" class="header"  rendered="#{reportFilters.grp ne 2 || reportFilters.grp ne 1 }" >
                                    <p:row>

                                        <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                        <p:column />
                                        <p:column />
                                        <p:column headerText="Sales Rep Details Report" />
                                        <p:column/>
                                        <p:column/>
                                        <p:column/>

                                        <p:column headerText="From: #{commReports.dtFrm}" />

                                        <p:column />
                                        <p:column headerText="To: #{commReports.dtTo}" />
                                        <p:column />
                                        <p:column />
                                        <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                        <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                        <p:column  rendered="#{globalParams.showTotalCommRepDtl}" /> 
                                        <p:column  rendered="#{globalParams.showTotalCommRepDtl}"/> 
                                        <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                        <p:column  rendered="#{globalParams.showSplitOnSalesAmt}" /> 
                                        <p:column  rendered="#{globalParams.showSplitOnSalesAmt}"/> 
                                        <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                                        <p:column  rendered="#{repDetailItem.salesTotalFlag eq 1}" /> 
                                        <p:column  rendered="#{repDetailItem.salesTotalFlag eq 1}" /> 
                                    </p:row>


                                    <div>

                                        <p:row></p:row>
                                        <!--          #10300  CRM-6433:-->
                                        <p:row  class="header" id="myHeaderExp" >
                                            <p:column rendered="#{reportFilters.grp ne 1}" sortBy="#{repDetails.principal}"  
                                                      headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left" width="320" />
                                            <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                            <p:column rendered="#{reportFilters.grp ne 2}" sortBy="#{repDetails.customer}"
                                                      headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" class="col_left" width="320" />
                                            <p:column rendered="#{reportFilters.grp ne 3}" sortBy="#{repDetails.teamName}" 
                                                      headerText="#{custom.labels.get('IDS_SALES_TEAM')}" class="col_left" width="240" />
                                            <p:column  sortBy="#{repDetails.distName}" 
                                                       headerText="#{custom.labels.get('IDS_DISTRI')}" class="col_left" width="240" rendered="#{repDetailItem.reducedList}" />
                                            <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                            <p:column sortBy="#{repDetails.secondaryCustomer}"
                                                      headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" class="col_left" width="220"  rendered="#{repDetailItem.reducedList}"  />
                                            <p:column   headerText="#{custom.labels.get('IDS_PART_NUM')}" class="col_left" sortBy="#{repDetails.partno}" width="220" rendered="#{repDetailItem.reducedList}" />
                                            <p:column   headerText="Invoice" class="col_left" sortBy="#{repDetails.invNo}" width="170" />
                                            <p:column   headerText="Inv Date" class="col_center" sortBy="#{repDetails.date}"
                                                        width="220" />
                                            <p:column   headerText="Chk Date" class="col_center" sortBy="#{repDetails.chkDate}"
                                                        width="100" />
                                            <p:column headerText="Invoice Amt" class="col_right" sortBy="#{repDetails.invAmt}"
                                                      width="100" />
                                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                            <p:column headerText="#{custom.labels.get('IDS_COMM')} Rate (%)" class="col_right" sortBy="#{repDetails.commPct}"
                                                      width="100"  rendered="#{globalParams.showTotalCommRepDtl}"/>
                                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->  
                                            <p:column headerText="#{custom.labels.get('IDS_COMM')} Amt" class="col_right" sortBy="#{repDetails.commissionAmt}"
                                                      width="100"  rendered="#{globalParams.showTotalCommRepDtl}" />
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                            <p:column   headerText="Split (%)" class="col_right" sortBy="#{repDetails.pct}"
                                                        width="100" rendered="#{globalParams.showSplitOnSalesAmt}"/>

                                            <p:column  headerText="Split Inv. Amt" class="col_right" sortBy="#{repDetails.splitInvAmt}"
                                                       width="100" rendered="#{globalParams.showSplitOnSalesAmt}" />

                                            <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                            <!--//1580 - sub groups-->
                                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->         
                                            <p:column headerText="Rep #{custom.labels.get('IDS_COMM')} Amt" class="col_right" sortBy="#{repDetails.commAmt}"
                                                      width="100" />   
                                            <p:column   headerText="PO #" class="col_center" sortBy="#{repDetails.commPoOrder}"
                                                        width="220"  rendered="#{repDetailItem.reducedList}"  />
                                            <p:column   headerText="SO #" class="col_center" sortBy="#{repDetails.commSalOrder}"
                                                        width="220"  rendered="#{repDetailItem.reducedList}" />
                                            <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                                            <p:column headerText="Total Sales"  rendered="#{repDetailItem.salesTotalFlag eq 1}"/>
                                            <p:column headerText="Credited Sales" rendered="#{repDetailItem.salesTotalFlag eq 1}"/>
                                            <p:column  /> 
                                        </p:row>
                                    </div></p:columnGroup>

                                <p:subTable  value="#{repDetailItem1.details}" var="repDetails"  >


                                    <f:facet  name="header"  >
                                        <!--<div class="div-selected" style="height:25px;width: 100%;" >-->
                                        <!--                                    <label style="font-size: 1.3em;font-weight: bold;color: #384c55;line-height: 25px;padding-left: 5px;">-->
                                        <p:outputLabel  value="#{reportFilters.grp eq 1 ?custom.labels.get('IDS_PRINCI')
                                                                 : (reportFilters.grp eq 2 ? custom.labels.get('IDS_CUSTOMER') : custom.labels.get('IDS_SALES_TEAM')) } "  />:   <p:outputLabel  value=" #{repDetailItem1.itemName}"       />   
                                        <!--</label>-->               
                                        <!--</div>-->

                                    </f:facet>
                                    <!--          #10300  CRM-6433:-->
                                    <p:column rendered="#{reportFilters.grp ne 1}"  class="col_left" width="320" >
                                        <h:outputLabel value="#{repDetails.principal}"  />       


                                    </p:column>
                                    <!--          #10300  CRM-6433:-->
                                    <p:column rendered="#{reportFilters.grp ne 2}" class="col_left" width="320" >
                                        <h:outputLabel value="#{repDetails.customer}"  />       
                                    </p:column>
                                    <!--          #10300  CRM-6433:-->

                                    <p:column rendered="#{reportFilters.grp ne 3}"  class="col_left" width="240" >
                                        <h:outputLabel value="#{repDetails.teamName}"  />          
                                    </p:column>
                                    <!--                     786 225548 : Everests: FF en SMK.#2 , added distributor to the query //17-9-2018 .-->
                                    <p:column  class="col_left" width="240" rendered="#{repDetailItem.reducedList}" >
                                        <h:outputLabel value="#{repDetails.distName}"   />          
                                    </p:column>

                                    <!-- 29/01/2018 - Feature Request -  Add column for invoice amount in sales rep commission details report + secondary customer-->
                                    <p:column  class="col_left" width="220" rendered="#{repDetailItem.reducedList}" >
                                        <h:outputLabel value="#{repDetails.secondaryCustomer}"  />       
                                    </p:column>
                                    <!--//EkMicro issue , part no added in Sales rep report-->
                                    <p:column    class="col_left" width="220" rendered="#{repDetailItem.reducedList}" >
                                        <h:outputLabel value="#{repDetails.partno}"  />       
                                    </p:column>

                                    <p:column   class="col_left"  width="170" >
                                        <h:outputLabel value="#{repDetails.invNo}"  />       
                                    </p:column>

                                    <p:column  class="col_center" 
                                               width="220" >
                                        <h:outputLabel value="#{repDetails.date}"  >
                                            <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                        </h:outputLabel>

                                    </p:column>

                                    <p:column   class="col_center" 
                                                width="100" >
                                        <h:outputLabel value="#{repDetails.chkDate}"  >
                                            <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                        </h:outputLabel>

                                    </p:column>

                                    <p:column 
                                        width="100" >
                                        <h:outputLabel value="#{repDetails.invAmt}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  
                                        <!--//#421825 salesrepdetails:needs invoice sub/grandtotals-->

                                    </p:column>
                                    <!--//    Feature #3091:-->  
                                    <p:column  rendered="#{globalParams.showTotalCommRepDtl}" >
                                        <h:outputLabel value="#{repDetails.commPct}"  style="text-align:right"  >
                                            <!--3987:   CRM-4124: Sales Rep Detail Report - column widths too narrow in PDF export-->
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel> 


                                    </p:column>

                                    <p:column rendered="#{globalParams.showTotalCommRepDtl}" >
                                        <h:outputLabel value="#{repDetails.commissionAmt}"  style="text-align:right"  >
                                            <!--3987:   CRM-4124: Sales Rep Detail Report - column widths too narrow in PDF export-->

                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel> 



                                    </p:column>

                                    <p:column   class="col_right" 
                                                width="100" rendered="#{globalParams.showSplitOnSalesAmt}" >
                                        <h:outputText value="#{repDetails.pct}"  style="text-align:right" >
                                            <!--Bug #3987:    CRM-4124: Sales Rep Detail Report - column widths too narrow in PDF export-->

                                            <!--<f:convertNumber maxFractionDigits="2"/>-->
                                        </h:outputText>

                                    </p:column>

                                    <!-- 29/01/2018 - Feature Request -  Add column for invoice amount in sales rep commission details report + secondary customer-->
                                    <!--  Bug 288 #549804 Dashboard db :need sales rep details to the 2 decimal -->
                                    <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->


                                    <p:column  class="col_right" sortBy="#{repDetails.splitInvAmt}"
                                               rendered="#{globalParams.showSplitOnSalesAmt}"   >
                                        <h:outputLabel value="#{repDetails.splitInvAmt}"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>

                                    <p:column  class="col_right" 
                                               width="100"  >
                                        <h:outputLabel value="#{repDetails.commAmt}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>    

                                    </p:column>
                                    <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                    <p:column 
                                        width="100"  rendered="#{repDetailItem.reducedList}"  >
                                        <h:outputLabel value="#{repDetails.commPoOrder}"  style="text-align:right"   >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>
                                    <p:column 
                                        width="100"  rendered="#{repDetailItem.reducedList}"   >
                                        <h:outputLabel value="#{repDetails.commSalOrder}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>
                                    <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                                    <p:column 
                                        width="100" rendered="#{repDetailItem.salesTotalFlag eq 1}"  >
                                        <h:outputLabel value="#{repDetails.commSalesTotal}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>
                                    <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                                    <p:column 
                                        width="100" rendered="#{repDetailItem.salesTotalFlag eq 1}" >
                                        <h:outputLabel value="#{repDetails.commSalesCredited}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>



                                    <p:columnGroup type="footer" >
                                        <p:row   rendered="true"> 

                                            <p:column  />
                                            <p:column  />
                                            <p:column  />
                                            <p:column  /> 
                                            <p:column  /> 
                                            <p:column  /> 
                                            <p:column  /> 

                                            <p:column footerText="Total :"
                                                      style="float: right"  class="footerFont"/>

                                            <p:column footerText="#{repDetailItem1.totals[2]}"  style="text-align: right"/>
                                            <!--//#421825 salesrepdetails:needs invoice sub/grandtotals-->
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                            <p:column rendered="#{globalParams.showTotalCommRepDtl}" />
                                            <p:column footerText="#{repDetailItem1.totals[6]}" rendered="#{globalParams.showTotalCommRepDtl}" />
                                            <p:column footerText="#{globalParams.showSplitOnSalesAmt?'':repDetailItem1.totals[1]}" rendered="#{globalParams.showSplitOnSalesAmt}" />
                                            <p:column footerText="#{globalParams.showSplitOnSalesAmt?repDetailItem.convertNumber(repDetailItem1.totals[3]):''}" rendered="#{globalParams.showSplitOnSalesAmt}"  style="text-align: right"/>

                                            <p:column footerText="#{globalParams.showSplitOnSalesAmt?repDetailItem1.totals[1]:''}"  rendered="#{globalParams.showTotalCommRepDtl}" style="text-align: right" />
                                            <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                            <!--                                            <p:column />
                                                                                        <p:column />-->
                                            <p:column footerText="#{(repDetailItem.salesTotalFlag eq 1 and globalParams.showSplitOnSalesAmt and globalParams.showTotalCommRepDtl)?  '' :((repDetailItem.salesTotalFlag ne 1 and globalParams.showTotalCommRepDtl)?'':repDetailItem1.totals[4]) }"  /> 
                                            <p:column footerText="#{(repDetailItem.salesTotalFlag eq 1 and globalParams.showSplitOnSalesAmt and globalParams.showTotalCommRepDtl)?  '' :((repDetailItem.salesTotalFlag ne 1 and globalParams.showTotalCommRepDtl)?'':repDetailItem1.totals[5]) }" /> 

                                            <p:column  footerText="#{(globalParams.showSplitOnSalesAmt and (repDetailItem.salesTotalFlag eq 1))?repDetailItem1.totals[4]:''}"  /> 
                                            <p:column footerText="#{(globalParams.showSplitOnSalesAmt and (repDetailItem.salesTotalFlag eq 1))?repDetailItem1.totals[5]:''}" /> 
                                        </p:row>
                                    </p:columnGroup> 

                                </p:subTable>
                                <!--#2560: CRM-1567: Sales rep details report-->
                                <p:columnGroup type="footer">
                                    <p:row> 

                                        <p:column  />
                                        <p:column  />
                                        <p:column  />
                                        <p:column  /> 
                                        <p:column  /> 
                                        <p:column  /> 
                                        <p:column  /> 

                                        <p:column footerText="Grand Total :"
                                                  style="float: right"  class="footerFont"/>
                                        <!--<p:column  />-->
                                        <p:column footerText="#{repDetailItem.invTotals}"  style="text-align: right"/>
                                        <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                        <p:column    class="col_right" rendered="#{globalParams.showTotalCommRepDtl}" style="width:130px">
                                        </p:column>
                                        <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->
                                       <!--//               #13769 ESCALATIONS CRM-8090   Commissions Report -> Sales Rep Detail -> No Comm Grand Total-->
                                        <p:column  footerText="#{repDetailItem.commAmtTotals}" rendered="#{globalParams.showTotalCommRepDtl}"  class="col_right"  style="width:130px">
                                        </p:column>
                                        <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->
                                        <!--// 5577 : Sales rep details grand totals missing-->
                                        <p:column    class="col_right" rendered="#{globalParams.showSplitOnSalesAmt}" style="width:130px">
                                        </p:column>
                                        <p:column    footerText="#{repDetailItem.convertNumber(repDetailItem.splitInvAmtTotal)}"   class="col_right" rendered="#{globalParams.showSplitOnSalesAmt}" style="width:130px">
                                        </p:column>
                                        <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->
                                                 <!--//               #13769 ESCALATIONS CRM-8090   Commissions Report -> Sales Rep Detail -> No Comm Grand Total-->
                                        <p:column footerText="#{repDetailItem.repcomAmtTotals}"  style="text-align: right;width:130px" />
                                        <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                        <p:column  /> 
                                        <p:column  /> 
                                        <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->
                                        <!--// 5577 : Sales rep details grand totals missing-->
                                        <p:column footerText="#{repDetailItem.salTotal}"    rendered="#{repDetailItem.salesTotalFlag eq 1}" /> 
                                        <p:column  footerText="#{repDetailItem.creidtedSalTotal}"    rendered="#{repDetailItem.salesTotalFlag eq 1}" /> 
                                    </p:row>
                                </p:columnGroup>  


                            </p:dataTable>
                            <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->
                            <p:dataTable value="#{repDetailItem.grpItems}" var="repDetailItem1" id="salRepReducedExpWithCommRate" rendered="false" >




                                <p:columnGroup  type="header" class="header"  rendered="#{reportFilters.grp ne 2 || reportFilters.grp ne 1 }" >
                                    <p:row>

                                        <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                        <p:column />
                                        <p:column />
                                        <p:column headerText="Sales Rep Details Report" />
                                        <p:column/>


                                        <p:column headerText="From: #{commReports.dtFrm}" />
                                        <!--5040 : removed extra column for pdf-->
                                        <p:column />
                                        <p:column headerText="To: #{commReports.dtTo}" />
                                        <p:column />
                                        <p:column  rendered="#{repDetailItem.reducedList}" />

                                        <p:column  rendered="#{globalParams.showTotalCommRepDtl and repDetailItem.reducedList}" /> 
                                        <p:column  rendered="#{globalParams.showTotalCommRepDtl and repDetailItem.reducedList}"/> 

                                        <!--5040 :-->
                                        <p:column  rendered="#{globalParams.showSplitOnSalesAmt and globalParams.showTotalCommRepDtl}" /> 
                                        <p:column  rendered="#{globalParams.showSplitOnSalesAmt and globalParams.showTotalCommRepDtl}"/> 


                                        <p:column  rendered="#{repDetailItem.salesTotalFlag eq 1}" /> 
                                        <p:column  rendered="#{repDetailItem.salesTotalFlag eq 1}" /> 
                                    </p:row>


                                    <div>

                                        <p:row></p:row>
                                        <!--          #10300  CRM-6433:-->
                                        <p:row  class="header" id="myHeaderExp" >
                                            <p:column rendered="#{reportFilters.grp ne 1}" sortBy="#{repDetails.principal}"  
                                                      headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left" width="320" />
                                            <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                            <p:column rendered="#{reportFilters.grp ne 2}" sortBy="#{repDetails.customer}"
                                                      headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" class="col_left" width="320" />
                                            <p:column rendered="#{reportFilters.grp ne 3}" sortBy="#{repDetails.teamName}" 
                                                      headerText="#{custom.labels.get('IDS_SALES_TEAM')}" class="col_left" width="240" />
                                            <p:column  sortBy="#{repDetails.distName}" 
                                                       headerText="#{custom.labels.get('IDS_DISTRI')}" class="col_left" width="240" rendered="#{repDetailItem.reducedList}" />
                                            <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                            <p:column sortBy="#{repDetails.secondaryCustomer}"
                                                      headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" class="col_left" width="220"  rendered="#{repDetailItem.reducedList}"  />
                                            <p:column   headerText="#{custom.labels.get('IDS_PART_NUM')}" class="col_left" sortBy="#{repDetails.partno}" width="220" rendered="#{repDetailItem.reducedList}" />
                                            <p:column   headerText="Invoice" class="col_left" sortBy="#{repDetails.invNo}" width="170" />
                                            <p:column   headerText="Inv Date" class="col_center" sortBy="#{repDetails.date}"
                                                        width="220" />
                                            <p:column   headerText="Chk Date" class="col_center" sortBy="#{repDetails.chkDate}"
                                                        width="100" />
                                            <p:column headerText="Invoice Amt" class="col_right" sortBy="#{repDetails.invAmt}"
                                                      width="100" />
                                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23--> 
                                            <p:column headerText="#{custom.labels.get('IDS_COMM')} Rate (%)" class="col_right" sortBy="#{repDetails.commPct}"
                                                      width="100"  rendered="#{globalParams.showTotalCommRepDtl}"/>
                                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                            <p:column headerText="#{custom.labels.get('IDS_COMM')} Amt" class="col_right" sortBy="#{repDetails.commissionAmt}"
                                                      width="100"  rendered="#{globalParams.showTotalCommRepDtl}" />
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                            <p:column   headerText="Split (%)" class="col_right" sortBy="#{repDetails.pct}"
                                                        width="100" rendered="#{globalParams.showSplitOnSalesAmt}"/>

                                            <p:column  headerText="Split Inv. Amt" class="col_right" sortBy="#{repDetails.splitInvAmt}"
                                                       width="100" rendered="#{globalParams.showSplitOnSalesAmt}" />

                                            <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                            <!--//1580 - sub groups-->
                                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                            <p:column headerText="Rep #{custom.labels.get('IDS_COMM')} Amt" class="col_right" sortBy="#{repDetails.commAmt}"
                                                      width="100" />   
                                            <p:column   headerText="PO #" class="col_center" sortBy="#{repDetails.commPoOrder}"
                                                        width="220"  rendered="#{repDetailItem.reducedList}"  />
                                            <p:column   headerText="SO #" class="col_center" sortBy="#{repDetails.commSalOrder}"
                                                        width="220"  rendered="#{repDetailItem.reducedList}" />
                                            <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                                            <p:column headerText="Total Sales"  rendered="#{repDetailItem.salesTotalFlag eq 1}"/>
                                            <p:column headerText="Credited Sales" rendered="#{repDetailItem.salesTotalFlag eq 1}"/>
                                            <p:column  /> 
                                        </p:row>
                                    </div></p:columnGroup>

                                <p:subTable  value="#{repDetailItem1.details}" var="repDetails"  >


                                    <f:facet  name="header"  >
                                        <!--          #10300  CRM-6433:-->
                                        <!--<div class="div-selected" style="height:25px;width: 100%;" >-->
                                        <!--                                    <label style="font-size: 1.3em;font-weight: bold;color: #384c55;line-height: 25px;padding-left: 5px;">-->
                                        <p:outputLabel  value="#{reportFilters.grp eq 1 ?custom.labels.get('IDS_PRINCI')
                                                                 : (reportFilters.grp eq 2 ? custom.labels.get('IDS_CUSTOMER') : custom.labels.get('IDS_SALES_TEAM')) } "  />:   <p:outputLabel  value=" #{repDetailItem1.itemName}"       />   
                                        <!--</label>-->               
                                        <!--</div>-->

                                    </f:facet>
                                    <p:column rendered="#{reportFilters.grp ne 1}"  class="col_left" width="320" >
                                        <h:outputLabel value="#{repDetails.principal}"  />       


                                    </p:column>

                                    <p:column rendered="#{reportFilters.grp ne 2}" class="col_left" width="320" >
                                        <h:outputLabel value="#{repDetails.customer}"  />       
                                    </p:column>


                                    <p:column rendered="#{reportFilters.grp ne 3}"  class="col_left" width="240" >
                                        <h:outputLabel value="#{repDetails.teamName}"  />          
                                    </p:column>
                                    <!--                     786 225548 : Everests: FF en SMK.#2 , added distributor to the query //17-9-2018 .-->
                                    <p:column  class="col_left" width="240" rendered="#{repDetailItem.reducedList}" >
                                        <h:outputLabel value="#{repDetails.distName}"   />          
                                    </p:column>

                                    <!-- 29/01/2018 - Feature Request -  Add column for invoice amount in sales rep commission details report + secondary customer-->
                                    <p:column  class="col_left" width="220" rendered="#{repDetailItem.reducedList}" >
                                        <h:outputLabel value="#{repDetails.secondaryCustomer}"  />       
                                    </p:column>
                                    <!--//EkMicro issue , part no added in Sales rep report-->
                                    <p:column    class="col_left" width="220" rendered="#{repDetailItem.reducedList}" >
                                        <h:outputLabel value="#{repDetails.partno}"  />       
                                    </p:column>

                                    <p:column   class="col_left"  width="170" >
                                        <h:outputLabel value="#{repDetails.invNo}"  />       
                                    </p:column>
                                    <!--#5040-->
                                    <p:column  class="col_center" 
                                               width="220" >
                                        <h:outputLabel value="#{repDetails.date}"  >
                                            <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                        </h:outputLabel>

                                    </p:column>
                                    <!--#5040-->
                                    <p:column   class="col_center" 
                                                width="100" >
                                        <h:outputLabel value="#{repDetails.chkDate}"  >
                                            <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                        </h:outputLabel>

                                    </p:column>

                                    <p:column 
                                        width="100" >
                                        <h:outputLabel value="#{repDetails.invAmt}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  
                                        <!--//#421825 salesrepdetails:needs invoice sub/grandtotals-->

                                    </p:column>
                                    <!--//    Feature #3091:-->  
                                    <p:column  rendered="#{globalParams.showTotalCommRepDtl}" >
                                        <h:outputLabel value="#{repDetails.commPct}"  style="text-align:right"  >
                                            <!--3987:   CRM-4124: Sales Rep Detail Report - column widths too narrow in PDF export-->
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel> 


                                    </p:column>

                                    <p:column rendered="#{globalParams.showTotalCommRepDtl}" >
                                        <h:outputLabel value="#{repDetails.commissionAmt}"  style="text-align:right"  >
                                            <!--3987:   CRM-4124: Sales Rep Detail Report - column widths too narrow in PDF export-->

                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel> 



                                    </p:column>

                                    <p:column   class="col_right" 
                                                width="100" rendered="#{globalParams.showSplitOnSalesAmt}" >
                                        <h:outputText value="#{repDetails.pct}"  style="text-align:right" >
                                            <!--Bug #3987:    CRM-4124: Sales Rep Detail Report - column widths too narrow in PDF export-->

                                            <!--<f:convertNumber maxFractionDigits="2"/>-->
                                        </h:outputText>

                                    </p:column>

                                    <!-- 29/01/2018 - Feature Request -  Add column for invoice amount in sales rep commission details report + secondary customer-->
                                    <!--  Bug 288 #549804 Dashboard db :need sales rep details to the 2 decimal -->
                                    <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->


                                    <p:column  class="col_right" sortBy="#{repDetails.splitInvAmt}"
                                               rendered="#{globalParams.showSplitOnSalesAmt}"   >
                                        <h:outputLabel value="#{repDetails.splitInvAmt}"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>

                                    <p:column  class="col_right" 
                                               width="100"  >
                                        <h:outputLabel value="#{repDetails.commAmt}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>    

                                    </p:column>
                                    <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                    <p:column 
                                        width="100"  rendered="#{repDetailItem.reducedList}"  >
                                        <h:outputLabel value="#{repDetails.commPoOrder}"  style="text-align:right"   >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>
                                    <p:column 
                                        width="100"  rendered="#{repDetailItem.reducedList}"   >
                                        <h:outputLabel value="#{repDetails.commSalOrder}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>
                                    <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                                    <p:column 
                                        width="100" rendered="#{repDetailItem.salesTotalFlag eq 1}"  >
                                        <h:outputLabel value="#{repDetails.commSalesTotal}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>
                                    <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                                    <p:column 
                                        width="100" rendered="#{repDetailItem.salesTotalFlag eq 1}" >
                                        <h:outputLabel value="#{repDetails.commSalesCredited}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>




                                    <p:columnGroup type="footer" >
                                        <p:row   rendered="true"> 

                                            <p:column  />

                                            <p:column  /> 
                                            <p:column  /> 
                                            <p:column  /> 

                                            <p:column footerText="Total :"
                                                      style="float: right"  class="footerFont"/>

                                            <p:column footerText="#{repDetailItem1.totals[2]}"  style="text-align: right"/>
                                            <!--//#421825 salesrepdetails:needs invoice sub/grandtotals-->
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                            <p:column rendered="#{globalParams.showTotalCommRepDtl}" />
                                            <p:column footerText="#{repDetailItem1.totals[6]}" rendered="#{globalParams.showTotalCommRepDtl}" />
                                            <p:column footerText="#{globalParams.showSplitOnSalesAmt?'':repDetailItem1.totals[1]}" rendered="#{globalParams.showSplitOnSalesAmt}" />
                                            <!--5517 : commission report details formating-->

                                            <p:column footerText="#{globalParams.showSplitOnSalesAmt?repDetailItem.convertNumber(repDetailItem1.totals[3]):(repDetailItem.salesTotalFlag eq 1) ?repDetailItem1.totals[4]:''}" rendered="#{globalParams.showSplitOnSalesAmt}"  style="text-align: right"/>

                                            <p:column footerText="#{globalParams.showSplitOnSalesAmt?repDetailItem1.totals[1]:(repDetailItem.salesTotalFlag eq 1)?repDetailItem1.totals[5]:''}"  rendered="#{globalParams.showTotalCommRepDtl}" style="text-align: right" />

                                            <p:column  footerText="#{(globalParams.showSplitOnSalesAmt and (repDetailItem.salesTotalFlag eq 1))?repDetailItem1.totals[4]:''}"  /> 
                                            <p:column footerText="#{(globalParams.showSplitOnSalesAmt and (repDetailItem.salesTotalFlag eq 1))?repDetailItem1.totals[5]:''}" /> 
                                        </p:row>
                                    </p:columnGroup> 


                                </p:subTable>
                                <!--#2560: CRM-1567: Sales rep details report-->
                                <p:columnGroup type="footer">

                                    <p:row   rendered="true"> 

                                        <p:column  />

                                        <p:column  /> 
                                        <p:column  /> 
                                        <p:column  /> 

                                        <p:column footerText="Grand Total :"
                                                  style="float: right"  class="footerFont"/>

                                        <p:column footerText="#{repDetailItem.invTotals}"  style="text-align: right"/>
                                        <!--//#421825 salesrepdetails:needs invoice sub/grandtotals-->
                                        <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                        <p:column rendered="#{globalParams.showTotalCommRepDtl}" />
                                        <!--//               #13769 ESCALATIONS CRM-8090   Commissions Report -> Sales Rep Detail -> No Comm Grand Total-->
                                        <p:column footerText="#{repDetailItem.commAmtTotals}"  rendered="#{globalParams.showTotalCommRepDtl}" />
                                        <p:column  rendered="#{globalParams.showSplitOnSalesAmt}" />
                                        <!--// 5577 : Sales rep details grand totals missing-->
                                        <p:column  footerText="#{repDetailItem.convertNumber(repDetailItem.splitInvAmtTotal)}" rendered="#{globalParams.showSplitOnSalesAmt}"  style="text-align: right"/>
                                         <!--//               #13769 ESCALATIONS CRM-8090   Commissions Report -> Sales Rep Detail -> No Comm Grand Total-->
                                        <p:column footerText="#{globalParams.showSplitOnSalesAmt?repDetailItem.repcomAmtTotals:repDetailItem.repcomAmtTotals}"  rendered="#{globalParams.showTotalCommRepDtl}" style="text-align: right" />
                                        <!--// 5577 : Sales rep details grand totals missing-->
                                        <p:column  footerText="#{repDetailItem.salTotal}"  rendered="#{(repDetailItem.salesTotalFlag eq 1)}" /> 
                                        <p:column  footerText="#{repDetailItem.creidtedSalTotal}" rendered="#{(repDetailItem.salesTotalFlag eq 1)}"/> 
                                    </p:row>

                                </p:columnGroup>  


                            </p:dataTable>

                            <!--5040   CRM-4400: merchantsales: sort of companies list is wrong, fixed box sizes make important info go off-->

                            <p:dataTable value="#{repDetailItem.grpItems}" var="repDetailItem1" id="salRepExpWithRedLt"  rendered="false">




                                <p:columnGroup  type="header" class="header"   >
                                    <p:row>

                                        <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                        <p:column />
                                        <p:column />
                                        <p:column headerText="Sales Rep Details Report" />

                                        <p:column  rendered="#{(repDetailItem.salesTotalFlag eq 1) or globalParams.showSplitOnSalesAmt }" />
                                        <p:column rendered="#{(repDetailItem.salesTotalFlag eq 1) or globalParams.showSplitOnSalesAmt }" />
                                        <p:column headerText="From: #{commReports.dtFrm}" />

                                        <p:column />
                                        <p:column headerText="To: #{commReports.dtTo}" />

                                        <p:column rendered="#{repDetailItem.salesTotalFlag eq 1 and globalParams.showSplitOnSalesAmt }"  /> 
                                        <p:column rendered="#{repDetailItem.salesTotalFlag eq 1 and  globalParams.showSplitOnSalesAmt}"  /> 
                                        <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                                    </p:row>


                                    <div>

                                        <p:row></p:row>
                                        <!--          #10300  CRM-6433:-->
                                        <p:row  class="header" id="myHeaderExp" >
                                            <p:column rendered="#{reportFilters.grp ne 1}" sortBy="#{repDetails.principal}"  
                                                      headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left" width="320" />
                                            <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                            <p:column rendered="#{reportFilters.grp ne 2}" sortBy="#{repDetails.customer}"

                                                      headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" class="col_left" width="320" />
                                            <p:column rendered="#{reportFilters.grp ne 3}" sortBy="#{repDetails.teamName}" 
                                                      headerText="#{custom.labels.get('IDS_SALES_TEAM')}" class="col_left" width="240" />
                                            <p:column  sortBy="#{repDetails.distName}" rendered="#{repDetailItem.reducedList}"
                                                       headerText="#{custom.labels.get('IDS_DISTRI')}" class="col_left" width="240" />
                                            <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                            <p:column sortBy="#{repDetails.secondaryCustomer}" rendered="#{repDetailItem.reducedList}"
                                                      headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" class="col_left" width="220" />
                                            <p:column  rendered="#{repDetailItem.reducedList}"  headerText="#{custom.labels.get('IDS_PART_NUM')}" class="col_left" sortBy="#{repDetails.partno}" width="220" />
                                            <p:column   headerText="Invoice" class="col_left" sortBy="#{repDetails.invNo}" width="170" />
                                            <p:column   headerText="Inv Date" class="col_center" sortBy="#{repDetails.date}"
                                                        width="220" />
                                            <p:column   headerText="Chk Date" class="col_center" sortBy="#{repDetails.chkDate}"
                                                        width="100" />
                                            <p:column headerText="Invoice Amt" class="col_right" sortBy="#{repDetails.invAmt}"
                                                      width="100" />
                                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->     
                                            <p:column headerText="#{custom.labels.get('IDS_COMM')} Rate (%)" class="col_right" sortBy="#{repDetails.commPct}"
                                                      width="100"  rendered="#{globalParams.showTotalCommRepDtl}"/>
                                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->  
                                            <p:column headerText="#{custom.labels.get('IDS_COMM')} Amt" class="col_right" sortBy="#{repDetails.commissionAmt}"
                                                      width="100"  rendered="#{globalParams.showTotalCommRepDtl}" />
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->


                                            <p:column   headerText="Split (%)" class="col_right" sortBy="#{repDetails.pct}"
                                                        width="100" rendered="#{globalParams.showSplitOnSalesAmt}"/>

                                            <p:column  headerText="Split Inv. Amt" class="col_right" sortBy="#{repDetails.splitInvAmt}"
                                                       width="100" rendered="#{globalParams.showSplitOnSalesAmt}"/>

                                            <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                            <!--//1580 - sub groups-->
                                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                            <p:column headerText="Rep #{custom.labels.get('IDS_COMM')} Amt" class="col_right" sortBy="#{repDetails.commAmt}"
                                                      width="100" />   
                                            <p:column   headerText="PO #" class="col_center" sortBy="#{repDetails.commPoOrder}" rendered="#{repDetailItem.reducedList}"
                                                        width="220" />
                                            <p:column   headerText="SO #" class="col_center" sortBy="#{repDetails.commSalOrder}" rendered="#{repDetailItem.reducedList}"
                                                        width="220" />
                                            <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->

                                            <p:column headerText="Total Sales"  rendered="#{repDetailItem.salesTotalFlag eq 1}"/>
                                            <p:column headerText="Credited Sales" rendered="#{repDetailItem.salesTotalFlag eq 1}"/>
                                            <p:column  /> 
                                        </p:row>
                                    </div></p:columnGroup>

                                <p:subTable  value="#{repDetailItem1.details}" var="repDetails"  >


                                    <f:facet  name="header"  >
                                        <!--          #10300  CRM-6433:-->
                                        <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                        <!--<div class="div-selected" style="height:25px;width: 100%;" >-->
                                        <!--                                    <label style="font-size: 1.3em;font-weight: bold;color: #384c55;line-height: 25px;padding-left: 5px;">-->
                                        <p:outputLabel  value="#{reportFilters.grp eq 1 ?custom.labels.get('IDS_PRINCI')
                                                                 : reportFilters.grp eq 2 ? custom.labels.get('IDS_DB_CUSTOMER') : custom.labels.get('IDS_SALES_TEAM') } "  />:   <p:outputLabel  value=" #{repDetailItem1.itemName}"       />   
                                        <!--</label>-->               
                                        <!--</div>-->

                                    </f:facet>
                                    <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->
                                    <!--          #10300  CRM-6433:-->
                                    <p:column rendered="#{reportFilters.grp ne 1}"  class="col_left" width="320" >
                                        <h:outputLabel value="#{repDetails.principal}"  />       
                                    </p:column>

                                    <p:column rendered="#{reportFilters.grp ne 2}" class="col_left" width="320" >
                                        <h:outputLabel value="#{repDetails.customer}"  />       
                                    </p:column>


                                    <p:column rendered="#{reportFilters.grp ne 3}"  class="col_left" width="240" >
                                        <h:outputLabel value="#{repDetails.teamName}"  />          
                                    </p:column>
                                    <!--                     786 225548 : Everests: FF en SMK.#2 , added distributor to the query //17-9-2018 .-->
                                    <p:column  class="col_left" width="240" rendered="#{repDetailItem.reducedList}"   >
                                        <h:outputLabel value="#{repDetails.distName}"  />          
                                    </p:column>

                                    <!-- 29/01/2018 - Feature Request -  Add column for invoice amount in sales rep commission details report + secondary customer-->
                                    <p:column  class="col_left" width="220"  rendered="#{repDetailItem.reducedList}"   >
                                        <h:outputLabel value="#{repDetails.secondaryCustomer}"  />       
                                    </p:column>
                                    <!--//EkMicro issue , part no added in Sales rep report-->
                                    <p:column    class="col_left" width="220"  rendered="#{repDetailItem.reducedList}" >
                                        <h:outputLabel value="#{repDetails.partno}"  />       
                                    </p:column>

                                    <p:column   class="col_left"  width="170" >
                                        <h:outputLabel value="#{repDetails.invNo}"  />       
                                    </p:column>
                                    <!--#5040-->
                                    <p:column  class="col_center" 
                                               width="220" >
                                        <h:outputLabel value="#{repDetails.date}"  >
                                            <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                        </h:outputLabel>

                                    </p:column>
                                    <!--#5040-->     
                                    <p:column   class="col_center" 
                                                width="100" >
                                        <h:outputLabel value="#{repDetails.chkDate}"  >
                                            <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                        </h:outputLabel>

                                    </p:column>

                                    <p:column 
                                        width="100" >
                                        <h:outputLabel value="#{repDetails.invAmt}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  
                                        <!--//#421825 salesrepdetails:needs invoice sub/grandtotals-->
                                        <!--1580 : subotals-->
                                    </p:column>
                                    <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                    <p:column  rendered="#{globalParams.showTotalCommRepDtl}" >
                                        <h:outputLabel value="#{repDetails.commPct}"  style="text-align:right"  >
                                            <!--<f:convertNumber minFractionDigits="0"/>-->
                                        </h:outputLabel> 


                                    </p:column>
                                    <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->
                                    <!--Bug #3987:    CRM-4124: Sales Rep Detail Report - column widths too narrow in PDF export-->

                                    <p:column class="col_right"  rendered="#{globalParams.showTotalCommRepDtl}" >
                                        <h:outputLabel value="#{repDetails.commissionAmt}"  style="text-align:right"  >

                                            <!--Bug #3987:    CRM-4124: Sales Rep Detail Report - column widths too narrow in PDF export-->
                                            <!--<f:convertNumber minFractionDigits="2"/>-->
                                        </h:outputLabel> 



                                    </p:column>
                                    <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->
                                    <!--//    Feature #3091:-->  
                                    <p:column   class="col_right" 
                                                width="100" rendered="#{globalParams.showSplitOnSalesAmt}" >
                                        <h:outputText value="#{repDetails.pct}" style="text-align:right" >
                                            <!--Bug #3987:    CRM-4124: Sales Rep Detail Report - column widths too narrow in PDF export-->

                                            <!--<f:convertNumber maxFractionDigits="2"/>-->
                                        </h:outputText>

                                    </p:column>

                                    <!-- 29/01/2018 - Feature Request -  Add column for invoice amount in sales rep commission details report + secondary customer-->
                                    <!--  Bug 288 #549804 Dashboard db :need sales rep details to the 2 decimal -->

                                    <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                    <p:column  class="col_right" sortBy="#{repDetails.splitInvAmt}"
                                               rendered="#{globalParams.showSplitOnSalesAmt}"  >
                                        <h:outputLabel value="#{repDetails.splitInvAmt}"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>

                                    <p:column  class="col_right" 
                                               width="100"  >
                                        <h:outputLabel value="#{repDetails.commAmt}"  style="text-align:right"  >
                                            <!--<f:convertNumber minFractionDigits="2"/>-->
                                        </h:outputLabel>    

                                    </p:column>
                                    <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                    <p:column 
                                        width="100" rendered="#{repDetailItem.reducedList}" >
                                        <h:outputLabel value="#{repDetails.commPoOrder}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>
                                    <p:column 
                                        width="100" rendered="#{repDetailItem.reducedList}" >
                                        <h:outputLabel value="#{repDetails.commSalOrder}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>

                                    <p:column 
                                        width="100" rendered="#{repDetailItem.salesTotalFlag eq 1}"  >
                                        <h:outputLabel value="#{repDetails.commSalesTotal}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>

                                    <p:column 
                                        width="100" rendered="#{repDetailItem.salesTotalFlag eq 1}" >
                                        <h:outputLabel value="#{repDetails.commSalesCredited}"  style="text-align:right"  >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>  

                                    </p:column>



                                    <p:columnGroup type="footer" >

                                        <p:row   rendered="true"> 

                                            <p:column  />
                                            <p:column  />

                                            <p:column  /> 
                                            <p:column  /> 

                                            <p:column footerText="Total:"
                                                      style="float: right"  class="footerFont"/>

                                            <p:column footerText="#{repDetailItem1.totals[2]}"  style="text-align: right"/>
                                            <!--//#421825 salesrepdetails:needs invoice sub/grandtotals-->
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                            <p:column  footerText="#{globalParams.showSplitOnSalesAmt?'':repDetailItem1.totals[1]}"/>
                                            <p:column footerText="#{globalParams.showSplitOnSalesAmt?repDetailItem.convertNumber(repDetailItem1.totals[3]):(repDetailItem.salesTotalFlag eq 1)?repDetailItem1.totals[4]:''}" rendered="#{globalParams.showSplitOnSalesAmt}"  style="text-align: right"/>
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                            <p:column footerText="#{globalParams.showSplitOnSalesAmt?repDetailItem1.totals[1]:(repDetailItem.salesTotalFlag eq 1)?repDetailItem1.totals[5]:''}" rendered="#{globalParams.showSplitOnSalesAmt}"  style="text-align: right" />
                                            <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                            <p:column  footerText="#{((repDetailItem.salesTotalFlag eq 1) and !(globalParams.showSplitOnSalesAmt)) ?  '' :(repDetailItem.salesTotalFlag eq 1)?repDetailItem1.totals[4]:''}" rendered="false"/> 
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                            <p:column  footerText="#{((repDetailItem.salesTotalFlag eq 1) and !(globalParams.showSplitOnSalesAmt)) ? '' :(repDetailItem.salesTotalFlag eq 1)? repDetailItem1.totals[5]:''}"  rendered="false"/> 

                                            <!--footer issue-->
                                            <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

<!--                                           <p:column  footerText="#{((repDetailItem.salesTotalFlag eq 1) and (globalParams.showSplitOnSalesAmt)) ? '' :''}" />
                                            <p:column  footerText="#{((repDetailItem.salesTotalFlag eq 1) and (globalParams.showSplitOnSalesAmt)) ? '' :''}" />-->

                                        <!--     <p:column  footerText="#{repDetailItem.salesTotalFlag eq 1 ? '' :''}" />
                                            <p:column  footerText="#{repDetailItem.salesTotalFlag eq 1 ? '' :''}" />-->

                                        </p:row>
                                    </p:columnGroup> 


                                </p:subTable>
                                <!--#2560: CRM-1567: Sales rep details report-->
                                <p:columnGroup type="footer">
                                    <p:row> 

                                        <p:column  />
                                        <p:column  />
                                        <p:column  />
                                        <p:column  rendered="#{repDetailItem.reducedList}" exportable="#{repDetailItem.reducedList}"  /> 
                                        <p:column rendered="#{repDetailItem.reducedList}" exportable="#{repDetailItem.reducedList}"  /> 
                                        <p:column rendered="#{repDetailItem.reducedList}"  exportable="#{repDetailItem.reducedList}" /> 
                                        <p:column  /> 
                                        <!--1580 : subotals-->
                                        <p:column footerText="Grand Total :"
                                                  style="float: right"  class="footerFont"/>
                                        <!--                                        <p:column  />-->
                                        <p:column footerText="#{repDetailItem.invTotals}"  style="text-align: right"/>
                                        <p:column    class="col_right" rendered="#{globalParams.showTotalCommRepDtl}" style="width:130px">
                                        </p:column>
                                        <!--//               #13769 ESCALATIONS CRM-8090   Commissions Report -> Sales Rep Detail -> No Comm Grand Total-->
                                        <p:column  footerText="#{repDetailItem.commAmtTotals}"  class="col_right" rendered="#{globalParams.showTotalCommRepDtl}" style="width:130px">
                                        </p:column>
                                        <!--2423  CRM-3535:Commission reports :- request option to include / not include columns-->

                                        <p:column    class="col_right" rendered="#{globalParams.showSplitOnSalesAmt}" style="width:130px">
                                        </p:column>
                                        <!--// 5577 : Sales rep details grand totals missing-->
                                        <p:column     class="col_right"  footerText="#{repDetailItem.convertNumber(repDetailItem.splitInvAmtTotal)}"   rendered="#{globalParams.showSplitOnSalesAmt}" style="width:130px">
                                        </p:column>
                                        <!--//               #13769 ESCALATIONS CRM-8090   Commissions Report -> Sales Rep Detail -> No Comm Grand Total-->
                                        <p:column footerText="#{repDetailItem.repcomAmtTotals}"  style="text-align: right;width:130px" />
                                        <!--//   #995:  CCRM-2749: FW: Adding PO# to Reports-->
                                        <p:column rendered="#{repDetailItem.reducedList}" exportable="#{repDetailItem.reducedList}"    /> 
                                        <p:column rendered="#{repDetailItem.reducedList}" exportable="#{repDetailItem.reducedList}"   /> 
                                        <!--1478 :CRM-3053: Epic / Credited Sales for in-territory End Customers and CEMs (Commission Reports)-->
                                        <!--// 5577 : Sales rep details grand totals missing-->
                                        <p:column  footerText="#{repDetailItem.salTotal}"   rendered="#{repDetailItem.salesTotalFlag eq 1}" /> 
                                        <p:column footerText="#{repDetailItem.creidtedSalTotal}"    rendered="#{repDetailItem.salesTotalFlag eq 1}" /> 
                                    </p:row>
                                </p:columnGroup>  


                            </p:dataTable>

                        </h:panelGroup>
                    </div>          

                </h:form>
                <!--//    #12428 ESCALATIONS CRM-7445   Nolan: ability from sales rep details to view upstream documents, automat-->
                <p:dialog widgetVar="linkedDlg" closeOnEscape="true"  modal="true" resizable="false"  >
                    <f:facet  name="header">
                        <h:outputText id="dlgHeader" style="width:90%;" value="#{repDetailItem.header}" />
                    </f:facet>

                    <h:form id="linkedDocForm">
                        <p:dataTable id="lnkReportList" value="#{repDetailItem.repDetailsLinkedDocList}" 
                                     var="lnkdDoc"
                                     scrollHeight="300"                                     
                                     tableStyle="table-layout:auto;"
                                     widgetVar="lnReportTable" 

                                     draggableColumns="true"
                                     emptyMessage="No linked documents found."  
                                     scrollable="true"
                                     class="tblmain"
                                     rowKey="#{lnkdDoc.recId}" 
                                     >

                            <p:column headerText="Doc. Type" filterBy="#{lnkdDoc.docType}" filterMatchMode="contains" sortBy="#{lnkdDoc.docType}">
                                <p:commandLink rendered="#{lnkdDoc.docType eq custom.labels.get('IDS_PURCHASE_ORDERS')}"  value="#{lnkdDoc.docType}"
                                               onclick="window.open('/RepfabricCRM/opploop/po/PoDetails.xhtml?id=#{lnkdDoc.docId}', '_blank');"
                                               >
                                </p:commandLink>
    <!--                             <p:commandLink rendered="#{lnkdDoc.docType eq 'Opportunity'}"  value="#{custom.labels.get('IDS_OPP')}"
                                                   onclick="window.open('../../opploop/opportunity/OpportunityView.xhtml?opid=#{lnkdDoc.docId}', '_blank');" 
                                                   >
                                  </p:commandLink>-->
                            </p:column>


                            <p:column headerText="Doc. Date" filterBy="#{oppLinkedDocumentsService.getFormattedDate(lnkdDoc.docDate, 'da')}" filterMatchMode="contains" sortBy="#{lnkdDoc.docDate}">
                                <h:outputText value="#{oppLinkedDocumentsService.getFormattedDate(lnkdDoc.docDate, 'da')}" />
                            </p:column>
                            <p:column headerText="Doc. Number" filterBy="#{lnkdDoc.docNo}" filterMatchMode="contains" sortBy="#{lnkdDoc.docNo}">
                                <h:outputText value="#{lnkdDoc.docNo}" />
                            </p:column>
                            <p:column headerText="Topic" filterBy="#{lnkdDoc.docTopic}" filterMatchMode="contains" sortBy="#{lnkdDoc.docTopic}">
                                <h:outputText value="#{lnkdDoc.docTopic}" />
                            </p:column>

                            <p:column headerText="Value" filterBy="#{lnkdDoc.docValue}" filterMatchMode="contains" sortBy="#{lnkdDoc.docValue}">
                                <h:outputText value="#{lnkdDoc.docValue}" style="float: right" />
                            </p:column>

                        </p:dataTable>

                    </h:form>

                </p:dialog>



                <p:dialog widgetVar="inSalRepDetProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                    <h:form>
                        <p:outputPanel >
                            <br />
                            <h:graphicImage  library="images" name="ajax-loader.gif"   />
                            <p:spacer width="5" />
                            <p:outputLabel value="Please wait...Exporting records" />
                            <br /><br />
                            <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                        </p:outputPanel>
                    </h:form>
                </p:dialog>

                <style>

                    #frm1\:dtrpt1_data
                    {
                        display: none;
                    }

                    @media only screen and (min-height : 901px) and (max-height : 1152px)  {

                        #tbldata{
                            height: 800px; 

                        }

                        #dt1{
                            height: 50vh;
                        }

                    }
                    .main-footer{
                        display: none;
                    }

                    @media only screen and (min-height : 600px) and (max-height : 900px)  {

                        #tbldata{
                            height: 540px; 

                        }

                        #dt1{
                            height: 50vh;
                        }

                    } 

                </style>


                <!--#2630   CRM-1628 Sa;es rep details report is a mess-->
                <script>

                    window.onload = function () {

                        setTimeout(function () {
                            var t = performance.timing;
                            console.log(t.loadEventEnd - t.responseEnd);
                            //#3249:#3249:     Task Reporting > Commission Reports > Add loading gif

                            document.getElementById("salRepDetrpt").style.pointerEvents = "all";
                            document.getElementById("salRepDetrpt").style.opacity = "1";


                            $('#loading-image').hide();
                        }, 0);
                        myFunction();
                    }



                    function myFunction() {
                        var x = "Total Height: " + screen.height + "px";
                        console.log("height :::" + (parseInt(screen.height)));
                        //                        document.getElementById("demo").innerHTML = x;
                        document.getElementById("repDiv").style.height = "" + (parseInt(screen.height) - 175) + "px";
                    }

                    //#3249:#3249:     Task Reporting > Commission Reports > Add loading gif

                    function hide()
                    {

                        $('#loading-image').hide();
                        document.getElementById("salRepDetrpt").style.pointerEvents = "all";
                        document.getElementById("salRepDetrpt").style.opacity = "1";
                    }

                    function processGif()
                    {
                        $('#loading-image').show();
                        document.getElementById("salRepDetrpt").style.pointerEvents = "none";
                        document.getElementById("salRepDetrpt").style.opacity = "0.7";
                    }


                    function start() {

                        PF('inSalRepDetProgressDlg').show();
                    }

                    function stop() {
                        PF('inSalRepDetProgressDlg').hide();
                    }


                </script>
                <style>

                    .ui-panelgrid .ui-panelgrid-cell {
                        padding-top: 1px !important;
                        padding-bottom: 1px !important;
                    }

                    [role="gridcell"]{

                        padding: 0px 10px !important;
                    }
                    .content-header{     display: none; }


                    .jqplot-target {

                        font-size: 0.75em!important;
                    }
                    .fltleft{
                        float: right;
                    }
                    .main-footer{
                        display: none
                    }
                </style>

                <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
                <ui:include src="/lookup/UserLookupDlg.xhtml"  />
                <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
                <ui:include src="/lookup/PartNumDlg.xhtml"/>


            </div>

        </div>
    </ui:define>

</ui:composition>