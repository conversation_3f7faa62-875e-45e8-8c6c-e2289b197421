<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: productdetails All.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

    <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
       <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
        <title>   #{custom.labels.get('IDS_COMMISSION')} Expanded  </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>

            <f:viewParam name="yr" value="#{reportFilters.repYear}" />
            <f:viewParam name="s" value="#{reportFilters.steams}" />
            <f:viewParam name="p"  value="#{reportFilters.pricipal.compId}"/>
            <!--//    #3408:    Bug Comm. Expanded View: 2019 displayed twice instead of 2020-->
<!--// 4775 - session retain -->
            <!--<f:viewAction action="#{salesReports.reSetDateMonth()}"/>-->
            <f:viewAction action="#{monthlyExpandedRep.runReport(2)}"/>

        </f:metadata>
    </ui:define>

    <ui:define name="body">

        <f:event listener="#{monthlyExpandedRep.isCommPageAccessible}" type="preRenderView"/>  
        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />


        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7"  id="comExpandedrpt">
            <h:form id="commsByMonth" >
                <!--<p:remoteCommand name="onload" autoRun="true" process="@this" immediate="true" onstart="PF('statusDialog').show()"   oncomplete="PF('statusDialog').hide()"   />-->


                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >


                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="#{loginBean.subscriber_name} "   />
                            <br/>
                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            #{custom.labels.get('IDS_COMMISSION')} Expanded View
                            <br/>
                            <!--//    #3408:    Bug Comm. Expanded View: 2019 displayed twice instead of 2020-->

                            <p:outputLabel value="  As of :" /> <p:outputLabel value="#{reportFilters.repYear} "  id="year"/>

                        </div>


                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5"  >

                    </div>  

                    <div class="ui-sm-12 ui-md-2 ui-lg-2"  style="width:15%;margin-left: 200px">

                    </div>
                    <div class="ui-sm-12 ui-md-1 ui-lg-1" >
                        <h:panelGrid columns="1" >


                            <p:outputLabel value="Year " /> 
                            <!--//    #3408:    Bug Comm. Expanded View: 2019 displayed twice instead of 2020-->

                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" >
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />

                                <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->
                                <!--#3770:Commission expanded/Line Overview > Export pdf/excel file : 'As of' displays as '2020'-->

                                <p:ajax   event="change"  onstart="processGif();"  listener="#{monthlyExpandedRep.runReport(2)}" update=":commsByMonth:dtrpt  :commsByMonth:year  :commsByMonth:dtrptexport" oncomplete="hideGif();" />

                            </p:selectOneMenu>

                            <!--#3344: Task Reporting > Commission Expanded - Export option Start-->
                            <p:column  >
                                <p:commandLink id="xls" ajax="false" onclick="PrimeFaces.monitorDownload(start, stop);" >  
                                    <p:graphicImage name="/images/excel.png" width="24"/>
                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->     
                                    <pe:exporter type="xlsx" target="dtrptexport"  fileName="#{custom.labels.get('IDS_COMMISSION')}_Expanded_View" subTable="false"   />  

                                </p:commandLink>  

                                <p:commandLink id="pdf" ajax="false"   onclick="PrimeFaces.monitorDownload(start, stop);"   >  
                                    <p:graphicImage value="/resources/images/pdf.png"  width="24" />  
                          <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->  
                                    <pe:exporter type="pdf" target="dtrptexport" fileName="#{custom.labels.get('IDS_COMMISSION')}_Expanded_View" subTable="false"    preProcessor="#{monthlyExpandedRep.preProcessPDF}" />  

                                </p:commandLink>
                            </p:column>
                            <!--#3344: Task Reporting > Commission Expanded - Export option End-->

                        </h:panelGrid> 

                        <h:panelGroup >




                        </h:panelGroup>



                    </div>
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >


                    </div >

                </div>

                <h:panelGroup id="tbldata">
                    <p:dataTable id="dtrpt"  value="#{monthlyExpandedRep.listExpand}" var="exp" emptyMessage="No data found" >

                        <p:column headerText="#{custom.labels.get('IDS_PRINCI')}"  class="col_left" >
                            <h:outputLabel value="#{exp.mexpPrinciName}"   />   

                            <f:facet  name="footer">
                                <h:outputLabel   value="Total : "  style="float: right"  class="footerFont"/>
                            </f:facet>
                        </p:column>


                        <p:column headerText="Jan" style="text-align:right ">
                            <h:outputLabel  value="#{exp.mexpMonth1}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totalc[0]}"  style="float: right" class="footerFont" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>

                        </p:column>

                        <p:column headerText="Feb" style="text-align:right ">
                            <h:outputLabel value="#{exp.mexpMonth2}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totalc[1]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>

                        <p:column headerText="Mar" style="text-align:right ">
                            <h:outputLabel value="#{exp.mexpMonth3}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totalc[2]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>

                            </f:facet>
                        </p:column>

                        <p:column headerText="Apr" style="text-align:right ">
                            <h:outputLabel value="#{exp.mexpMonth4}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totalc[3]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="May" style="text-align:right ">
                            <h:outputLabel value="#{exp.mexpMonth5}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totalc[4]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>            
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="Jun" style="text-align:right ">
                            <h:outputLabel value="#{exp.mexpMonth6}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totalc[5]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="Jul" style="text-align:right ">
                            <h:outputLabel value="#{exp.mexpMonth7}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totalc[6]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="Aug" style="text-align:right ">
                            <h:outputLabel value="#{exp.mexpMonth8}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totalc[7]}"  style="float: right" class="footerFont" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="Sep" style="text-align:right ">
                            <h:outputLabel value="#{exp.mexpMonth9}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totalc[8]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>



                        <p:column headerText="Oct" style="text-align:right ">
                            <h:outputLabel value="#{exp.mexpMonth10}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totalc[9]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="Nov" style="text-align:right ">
                            <h:outputLabel value="#{exp.mexpMonth11}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totalc[10]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column headerText="Dec" style="text-align:right ">
                            <h:outputLabel value="#{exp.mexpMonth12}"  >
                                <f:convertNumber maxFractionDigits="0" />
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}" 
                                               value="#{monthlyExpandedRep.totalc[11]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <p:column  headerText="Total" style="text-align:right ">
                            <h:outputLabel  value="#{exp.mexpTotal}"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>

                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}"  
                                               value="#{monthlyExpandedRep.totalc[12]}"  style="float: right;" class="footerFont" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>

                        <p:column headerText="%" sortBy="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}" style="text-align:right ">
                            <h:outputLabel  value="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}"  style="float: right" />                    
                            <f:facet name="footer">
                                <h:outputLabel rendered="#{commReports.repPrinci eq 0}"  
                                               value="#{monthlyExpandedRep.totalc[13] eq 0 ?  '0' 
                                                        : (monthlyExpandedRep.totalc[13] ne 100 ? '100' : '100')}%"  style="float: right;" class="footerFont"/>
                            </f:facet>

                        </p:column>



                    </p:dataTable>

                    <!--#3344: Task Reporting > Commission Expanded - Export option Start-->

                    <p:dataTable id="dtrptexport"  value="#{monthlyExpandedRep.listExpand}" var="exp" rendered="false" >

                        <p:columnGroup  type="header" class="header"   >
                            <p:row>

                                <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                <p:column />
                                <p:column />
                                <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                <p:column headerText="#{custom.labels.get('IDS_COMMISSION')} Expanded View" />
                                <p:column/>
                                <p:column/>
                                <p:column/>

                                <!--#3770:Commission expanded/Line Overview > Export pdf/excel file : 'As of' displays as '2020'-->
                                <p:column headerText="As of : #{reportFilters.repYear} " />

                                <p:column />

                                <p:column />
                                <p:column />
                                <p:column />
                                <p:column />
                                <p:column />
                                <p:column />


                            </p:row>




                            <p:row>





                            </p:row>
                            <p:row  class="header" id="myHeaderExp" >   




                                <!--//Bug 1164 -# 325040 : By Customer By month Sales Reports-->
                                <p:column headerText="#{monthlyExpandedRep.repgrp eq 1 ?  custom.labels.get('IDS_PRINCI') 
                                                        : (monthlyExpandedRep.repgrp eq 2 ? custom.labels.get('IDS_CUSTOMER') : custom.labels.get('IDS_CUSTOMER'))}"  class="col_left" >

                                </p:column>

                                <!--#390130:sales by month :column sorter provided  -->
                                <p:column headerText="Jan" class="col_right" sortBy="#{exp.mexpMonth1}" >

                                </p:column>

                                <p:column headerText="Feb" class="col_right" sortBy="#{exp.mexpMonth2}">

                                </p:column>

                                <p:column headerText="Mar" class="col_right" sortBy="#{exp.mexpMonth3}" >


                                </p:column>

                                <p:column headerText="Apr" class="col_right" sortBy="#{exp.mexpMonth4}">

                                </p:column>


                                <p:column headerText="May" class="col_right" sortBy="#{exp.mexpMonth5}">

                                </p:column>


                                <p:column headerText="Jun" class="col_right" sortBy="#{exp.mexpMonth6}">

                                </p:column>


                                <p:column headerText="Jul" class="col_right" sortBy="#{exp.mexpMonth7}">

                                </p:column>


                                <p:column headerText="Aug" class="col_right" sortBy="#{exp.mexpMonth8}">

                                </p:column>


                                <p:column headerText="Sep" class="col_right" sortBy="#{exp.mexpMonth9}" >

                                </p:column>



                                <p:column headerText="Oct" class="col_right" sortBy="#{exp.mexpMonth10}">

                                </p:column>


                                <p:column headerText="Nov" class="col_right" sortBy="#{exp.mexpMonth11}">

                                </p:column>


                                <p:column headerText="Dec" class="col_right" sortBy="#{exp.mexpMonth12}">

                                </p:column>


                                <p:column  headerText="Total" class="col_right" sortBy="#{exp.mexpTotal}">

                                </p:column>

                                <p:column headerText="%" sortBy="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}">

                                </p:column>
                            </p:row>


                        </p:columnGroup>





                        <!--//Bug 1164 -# 325040 : By Customer By month Sales Reports-->
                        <p:column  class="col_left" >



                            <h:outputText value="#{exp.mexpPrinciName}"   />   

                            <f:facet  name="footer">
                                <h:outputText   value="Total : "  style="float: right" class="footerFont"/>
                            </f:facet>
                        </p:column>

                        <!--#390130:sales by month :column sorter provided  -->
                        <p:column  class="col_right" sortBy="#{exp.mexpMonth1}" >
                            <h:outputText  value="#{exp.mexpMonth1}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totalc[0]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>

                        </p:column>

                        <p:column  class="col_right" sortBy="#{exp.mexpMonth2}">
                            <h:outputText value="#{exp.mexpMonth2}" style="float: right"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totalc[1]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>

                        <p:column class="col_right" sortBy="#{exp.mexpMonth3}" >
                            <h:outputText value="#{exp.mexpMonth3}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totalc[2]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>

                            </f:facet>
                        </p:column>

                        <p:column class="col_right" sortBy="#{exp.mexpMonth4}">
                            <h:outputText value="#{exp.mexpMonth4}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totalc[3]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth5}">
                            <h:outputText value="#{exp.mexpMonth5}"  style="float: right">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totalc[4]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>            
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth6}">
                            <h:outputText value="#{exp.mexpMonth6}"  style="float: right">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totalc[5]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth7}">
                            <h:outputText value="#{exp.mexpMonth7}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totalc[6]}"  style="float: right" class="footerFont" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth8}">
                            <h:outputText value="#{exp.mexpMonth8}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totalc[7]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth9}" >
                            <h:outputText value="#{exp.mexpMonth9}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totalc[8]}"  style="float: right;" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>



                        <p:column  class="col_right" sortBy="#{exp.mexpMonth10}">
                            <h:outputText value="#{exp.mexpMonth10}" style="float: right">
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totalc[9]}"  style="float: right"  class="footerFont" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth11}">
                            <h:outputText value="#{exp.mexpMonth11}" style="float: right"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totalc[10]}"  style="float: right"  class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column  class="col_right" sortBy="#{exp.mexpMonth12}">
                            <h:outputText value="#{exp.mexpMonth12}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0" />
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}" 
                                              value="#{monthlyExpandedRep.totalc[11]}"  style="float: right" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>


                        <p:column   class="col_right" sortBy="#{exp.mexpTotal}">
                            <h:outputText  value="#{exp.mexpTotal}" style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>

                            <f:facet name="footer">
                                <h:outputText rendered="#{salesReports.repPrinci eq 0}"  
                                              value="#{monthlyExpandedRep.totalc[12]}"  style="float: right;" class="footerFont">
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>

                        <p:column  sortBy="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}">
                            <h:outputText  value="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}"  style="float: right" />                    
                            <f:facet name="footer">
                                <h:outputText rendered="#{commReports.repPrinci eq 0}"  
                                              value="#{monthlyExpandedRep.totalc[13] eq 0 ?  '0' 
                                                       : (monthlyExpandedRep.totalc[13] ne 100 ? '100' : '100')}%"  style="float: right;" class="footerFont"/>
                            </f:facet>


                        </p:column>


                    </p:dataTable>
                    <!--#3344: Task Reporting > Commission Expanded - Export option End-->
                </h:panelGroup>
            </h:form>
            <!--#3344: Task Reporting > Commission Expanded - Export option Start-->
            <p:dialog widgetVar="inCommExpandedProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Please wait...Exporting records" />
                        <br /><br />
                        <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                    </p:outputPanel>
                </h:form>
            </p:dialog>
            <!--#3344: Task Reporting > Commission Expanded - Export option End-->

        </div>

        <style>

            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }


            .jqplot-target {

                font-size: 0.75em!important;
            }
        </style>

        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

        <script>
            window.onload = function () {
//                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("comExpandedrpt").style.pointerEvents = "all";
                    document.getElementById("comExpandedrpt").style.opacity = "1";

                    $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };


            function processGif()
            {

                $('#loading-image').show();
                document.getElementById("comExpandedrpt").style.pointerEvents = "none";
                document.getElementById("comExpandedrpt").style.opacity = "0.7";
            }

            function hideGif()
            {

                $('#loading-image').hide();

                document.getElementById("comExpandedrpt").style.pointerEvents = "all";
                document.getElementById("comExpandedrpt").style.opacity = "1";
            }

            function start() {

                PF('inCommExpandedProgressDlg').show();
            }

            function stop() {
                PF('inCommExpandedProgressDlg').hide();
            }


        </script>


        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->




    </ui:define>



</ui:composition>