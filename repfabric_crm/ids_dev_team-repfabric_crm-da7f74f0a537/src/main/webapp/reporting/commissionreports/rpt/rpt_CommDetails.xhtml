<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: rptCommissionSummary.xhtml
// Author: Sharvani
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">


    <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
     <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
        <title>    #{custom.labels.get('IDS_COMMISSION')} Details  </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata> 
            <f:viewParam name="d1" value="#{commitems.repFrom}" />
            <f:viewParam name="d2" value="#{commitems.repTo}" />
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="c" value="#{reportFilters.customer.compId}"/>        
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>         
            <f:viewParam name="g" value="#{commitems.grp}"/>
            <!--1478-->
            <f:viewParam name="f" value="#{commitems.salesTotalFlag}"/>
                <!--# Bug 5295: commission reports not working -->
            <f:viewParam name="chk" value="#{commitems.checkFlag}"/>
            <f:viewParam name="gf" value="#{commitems.subGrpFlag}"/>
            <!--//Bug #5575:   Customer Details Report: Pdf Issues(Region included in SubTotal)-->
            <f:viewParam name="sb" value="#{commitems.oldcheckFlag}"/>
            <f:viewAction action="#{reportFilters.assignSalesTeams()}" />  

            <f:viewAction action="#{commitems.run()}"/>

        </f:metadata>
    </ui:define>

    <ui:define name="title">
        <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
        <ui:param name="title" value="#{custom.labels.get('IDS_COMMISSION')} details"/>
        <div class="row">
            <div class="col-md-6">
            </div>

        </div>
    </ui:define>

    <ui:define name="body">
        <!--Bug #15235 ESCALATIONS : CRM-8898   Commission Details not authorized to view this page-->   
        <f:event listener="#{commitems.isPageAccessible()}" type="preRenderView"/>
        <!--#3249:    Task Reporting > Commission Reports > Add loading gif-->

        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4"  id="commdetrpt"  >

            <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                            left: 600px;
                            top: 300px;
                            width: 150px;
                            height: 150px;
                            z-index: 9999; opacity: .5 !important; " />      
            <h:form id="commDetailsHeader" >
                <!--//    #1148: Commission Reports > Do not default to Customer Sales team-->
                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyOnlyCustomer(viewCompLookupService.selectedCompany)}" update="commDetailsHeader:inputCustomer "/>

                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="commDetailsHeader:inputPrincName"  />
                <!--<p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="commDetailsHeader:inputAllSalesTeam" />-->
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                        <div class="compNamebg">
                            <h:outputLabel value="#{loginBean.subscriber_name} "   />
                            <br/>
                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            #{custom.labels.get('IDS_COMMISSION')} Details Report   
                            <br/>
                            <p:outputLabel  value="From:"   />
                            #{commReports.dtFrm}
                            <br/>
                            <p:outputLabel  value="To:"   />
                            <p:spacer width="22px"/>
                            #{commReports.dtTo}
                        </div>

                    </div>

                    <!--                       <div class="ui-sm-12 ui-md-1 ui-lg-1">
                                            
                                        </div>-->

                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="margin-left:100px" >

                        <p:panelGrid   columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" layout="grid" style="width: 250px;float: right;"  id="graph"> 
                            <p:outputLabel value="Group by"/>

                            <p:selectOneRadio value="#{commitems.grp}"  widgetVar="grp" layout="pageDirection"  required="true">

                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/>  
                                <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                <f:selectItem itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                                <f:selectItem itemValue="3" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}"/> 
                                <!--Feature #5262CRM-4555: Somewhat urgent report needed for JD Martin-->
                                <p:ajax event="change"  listener="#{commitems.setlevel()}" />
                            </p:selectOneRadio>

                        </p:panelGrid>
                    </div> 


                    <!--Feature #5262CRM-4555: Somewhat urgent report needed for JD Martin-->
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:-140px">
                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                     style="float: right;width: 350px;"      layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />

                            <h:panelGroup class="ui-inputgroup"  >
    <!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
<!--                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                #3473:     sales team - Sales team data we can enter manually

                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true" />
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                                  styleClass="btn-info btn-xs"  style="width:67px" />
                            </h:panelGroup>-->



                            <p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')}" />
                            <h:panelGroup class="ui-inputgroup"  style="width: 112%">
                                <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width: 200%;" id="selSalesTeam">
                                    <f:selectItems value="#{lookupService.salesTeams}" var="team"  
                                                   itemValue="#{team.smanId}"
                                                   itemLabel="#{team.smanName}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.list('SALES_TEAM')}"  update=":frmSalesTeamLookup"   oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                <p:commandLink styleClass="sel-salesteam" value="Clear" action="#{lookupService.clear('SALES_TEAM')}"   actionListener="#{poBacklogService.clearSalesTeam()}"      update="@(.sel-salesteam)" disabled="#{!(lookupService.salesTeams!=null and lookupService.salesTeams.size() != 0)}" />
                            </h:panelGroup>

<!--//          Bug #5575:Customer Details Report: Pdf Issues(Region included in SubTotal)-->

                            <p:commandButton  value="View Report"  id="viewreport"  styleClass="btn btn-primary btn-xs"   actionListener="#{commitems.goToNavigationpage()}" update=" :commDetailsHeader:dtrpt1 :commDetailsHeader:grandTotals :commDetailsHeader:commDetailsExp1 :commDetailsHeader:commDetailsExpwithSubGrp"  onclick="processGif()"  />

                            <p:column>
                                <p:commandButton value="Export"   style="width:30px"  
                                                 ajax="false"  actionListener="#{commitems.exportData()}"  
                                                 styleClass="btn btn-primary btn-xs" onclick="PrimeFaces.monitorDownload(start, stop);" 
                                                 />  
                                <!--                                <p:commandLink id="xls" ajax="false" onclick="PrimeFaces.monitorDownload(start, stop);"  >  
                                                                    <p:graphicImage name="/images/excel.png" width="24"/>
                                                                    <f:setPropertyActionListener value="false" onclick="PrimeFaces.monitorDownload(start, stop);" target="#{exporterController.customExporter}" />  
                                                                    <pe:exporter type="xlsx" target="commDetailsExp1"  fileName="Commission _Details" subTable="true"  postProcessor="#{commitems.postProcessXLS}"  />  
                                
                                                                </p:commandLink>  -->
                            <!--#10894  ESCALATIONS CRM-6752   Commission Detail Report: Page Error-->
                                <p:commandLink id="pdf" ajax="false" onclick="PrimeFaces.monitorDownload(start, stop);"    rendered="false"    >  
                                    <p:graphicImage value="/resources/images/pdf.png"  width="24" />  

                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->  
                                    <pe:exporter   type="pdf" target="#{(commitems.subGrpFlag and commitems.grp ==1) ?'commDetailsExpwithSubGrp':'commDetailsExp1' }" fileName="#{custom.labels.get('IDS_COMMISSION')}_Details" subTable="true"    preProcessor="#{commitems.preProcessPDF}"  />  

                                </p:commandLink>
                                <p:commandLink id="subGrppdf" ajax="false" onclick="PrimeFaces.monitorDownload(start, stop);" actionListener="#{commitems.export()}"     rendered="false" >  
                                    <p:graphicImage value="/resources/images/pdf.png"  width="24" />  

                     <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                    <pe:exporter    type="pdf"  target="#{(commitems.subGrpFlag and commitems.grp ==1) ?'commDetailsExpwithSubGrp':'commDetailsExp1' }" fileName="#{custom.labels.get('IDS_COMMISSION')}_Details" subTable="true"    preProcessor="#{commitems.preProcessPDF}"  />  

                                </p:commandLink>
                            </p:column>

                        </p:panelGrid>     
                    </div >

                </div>
                <p:spacer width="4px" />
          
                <h:panelGroup id="tbldata">
          <!--<ui:repeat id="dtrpt1" value="#{commitems.grpItems}" var="commitems1" >-->
                      <!--//          Bug #5575:Customer Details Report: Pdf Issues(Region included in SubTotal)-->
                      <p:dataScroller     id="dtrpt1" value="#{commitems.grpItems}" var="commitems1"  chunkSize="7" >
                        <p:column >
                            <div class="div-selected" style="height:25px;width: 100%;" >
                                <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                                    #{commitems1.itemName}          
                                </label>               
                            </div>
                             <!--// ESCALATIONS CRM-6752   Commission Detail Report: Page Error-->
                            <p:dataTable lazy="true" rows="50" paginatorAlwaysVisible="false" paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}" paginator="true" value="#{commitems1.singleCommDetails}" var="commdetails" emptyMessage="No data found"   sortBy="#{commdetails.regId}" >
                                <!--//1478-->
                               
                                <p:column  rendered="#{commitems.grp ne 1}"  headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left"  width="200"  sortBy="#{commdetails.principal}" >
                                    <h:outputLabel value="#{commdetails.principal}"  />       
                                </p:column>
                                <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                <!--//1478-->
                                <p:column sortBy="#{commdetails.customer}" rendered="#{commitems.grp ne 2}"   headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" class="col_left"   width="200" >
                                    <h:outputLabel value="#{commdetails.customer}"  />       
                                </p:column>

                                <p:column sortBy="#{commdetails.regName}"   rendered="#{commitems.grp eq 1 and commitems.subGrpFlag}" headerText="#{custom.labels.get('IDS_REGION')}" class="col_left"   width="200" >
                                    <h:outputLabel value="#{commdetails.regName}"  />       
                                </p:column>

                                <p:column sortBy="#{commdetails.teamName}" rendered="#{commitems.grp ne 3}"  headerText="#{custom.labels.get('IDS_SALES_TEAM')}" class="col_left" width="200" >
                                    <h:outputLabel value="#{commdetails.teamName}"  />               
                                </p:column>


                                <!--// 786 225548 : Everests: FF en SMK.#2-->
                                <p:column sortBy="#{commdetails.distributor}"  headerText="#{custom.labels.get('IDS_DISTRI')}" class="col_left" width="150" >
                                    <h:outputLabel value="#{commdetails.distributor}"  />               
                                </p:column>

                                <p:column sortBy="#{commdetails.invNo}"   headerText="Invoice" class="col_left" width="100" >
                                    <h:outputLabel value="#{commdetails.invNo}"  />   
                                    <!--<h:outputLabel value="#{commitems.commDtlItems.indexOf(commitems)}"  />--> 
                                </p:column>
                                <!--Feature #5262: CRM-4555: Somewhat urgent report needed for JD Martin-->
                                <p:column sortBy="#{commdetails.date}"   headerText="Inv Date" class="col_center" width="100" >
                                    <h:outputLabel value="#{commdetails.date}"  >
                                        <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                    </h:outputLabel>
                                </p:column>


                                <!--Feature #5262: CRM-4555: Somewhat urgent report needed for JD Martin-->
                                <p:column sortBy="#{commdetails.chkDate}"   headerText="Chk Date" class="col_center" width="100" >
                                    <h:outputLabel value="#{commdetails.chkDate}"  >
                                        <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                    </h:outputLabel>
                                    <f:facet name="footer">
                                        <h:outputLabel value="Total" style="float: right;font-weight: bold"  />
                                    </f:facet>
                                </p:column>


                                <p:column sortBy="#{commdetails.totalAmt}"    headerText="Sales Amt" class="col_right" width="100" >
                                    <h:outputLabel value="#{commdetails.totalAmt}" style="float: right"  >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{commitems1.totals[1]}"  style="float: right;font-weight: bold" >
                                            <f:convertNumber maxFractionDigits="0"/>
                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>
                                <!--Bug 288 #549804 Dashboard: db : need sales rep detail to the 2 decimals-->   

                       <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                <p:column sortBy="#{commdetails.commAmt}" headerText="#{custom.labels.get('IDS_COMM')} Amt" class="col_right" width="100" >
                                    <h:outputLabel value="#{commdetails.commAmt}" style="float: right"  >
                                        <f:convertNumber minFractionDigits="2"/>
                                    </h:outputLabel>    
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{commitems1.totals[2]}" style="float: right;font-weight: bold" >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>

                                <!--Bug 288 #549804 Dashboard: db : need sales rep detail to the 2 decimals-->   

                                <p:column sortBy="#{commdetails.compCommAmt}"  headerText="Company Split" class="col_right" width="100" >
                                    <h:outputLabel value="#{commdetails.compCommAmt}" style="float: right"   >
                                        <f:convertNumber minFractionDigits="2"/>
                                    </h:outputLabel> 
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{commitems1.totals[3]}"  style="float: right;font-weight: bold" >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>
                                                 
                                <!--//    Feature #5262CRM-4555: Somewhat urgent report needed for JD Martin-->
                                <!--02-09-2024 : CRM:8429 : Task:14403 : Renamed the Colum name of "Paid" to "Paid to Rep" -->
                                <p:column sortBy="#{commdetails.paid}" headerText="Paid To Rep" class="col_left" width="100" >
                                    <h:outputLabel value="#{commdetails.paidVal}"
                                                   style="color: #{commdetails.paid eq 1  ? 'green' : 'red'}" 
                                                   />   



                                </p:column>

                                <!--//1478-->
                                <p:column sortBy="#{commdetails.commSalTotal}"  headerText="Total Sales" class="col_right" width="110"   rendered="#{commitems.salesTotalFlag eq 1}" >
                                    <h:outputLabel value="#{commdetails.commSalTotal}" style="float: right"   >
                                        <f:convertNumber minFractionDigits="2"/>
                                    </h:outputLabel> 
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{commitems1.totals[4]}"  style="float: right;font-weight: bold" >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>

                                <p:column sortBy="#{commdetails.creditedSalTotal}"  headerText="Credited Sales" class="col_right" width="110" rendered="#{commitems.salesTotalFlag eq 1}" >
                                    <h:outputLabel value="#{commdetails.creditedSalTotal}" style="float: right"   >
                                        <f:convertNumber minFractionDigits="2"/>
                                    </h:outputLabel> 
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{commitems1.totals[5]}"  style="float: right;font-weight: bold" >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </f:facet>
                                </p:column>

                                <!--Feature #5262: CRM-4555: Somewhat urgent report needed for JD Martin subtotals-->
                                <p:summaryRow  rendered="#{commitems.subGrpFlag and commitems.grp ==1 }" >

                                    <p:column>


                                    </p:column>
                                    <p:column   />
                                    <p:column    />
                                    <p:column  />
                                    <p:column   />
                                    <p:column   />
                                    <p:column>
                                        <h:outputText value="Sub Total" style="float: right;font-weight: bold" />

                                    </p:column>
                                    <p:column    >
                                        <h:outputLabel value="#{commitems1.subGrpTotals.get(commdetails.regId)[0]}" style="float: right;font-weight: bold" >

                                            <f:convertNumber maxFractionDigits="0"/>
                                        </h:outputLabel>
                                    </p:column>
                                    <p:column >
                                        <h:outputLabel value="#{commitems1.subGrpTotals.get(commdetails.regId)[1]}" style="float: right;font-weight: bold" >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </p:column>
                                    <p:column >
                                        <h:outputLabel value="#{commitems1.subGrpTotals.get(commdetails.regId)[2]}" style="float: right;font-weight: bold" >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </p:column>
                                    <p:column></p:column>
                                    <p:column  rendered="#{commitems.salesTotalFlag eq 1}"  >
                                        <h:outputLabel value="#{commitems1.subGrpTotals.get(commdetails.regId)[3]}" style="float: right;font-weight: bold" >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </p:column>
                                    <p:column  rendered="#{commitems.salesTotalFlag eq 1}"  >
                                        <h:outputLabel value="#{commitems1.subGrpTotals.get(commdetails.regId)[4]}" style="float: right;font-weight: bold" >
                                            <f:convertNumber minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </p:column>
                                </p:summaryRow>

                            </p:dataTable>
                            <!--</ui:repeat>-->

                        </p:column>

                    </p:dataScroller>

                    <p:dataTable emptyMessage="" style="width:100%;"  id="grandTotals">

                        <p:column   width="200" >


                        </p:column>
                        <p:column  width="200" />
                        <p:column  rendered="#{commitems.subGrpFlag}"  width="200" />
                        <p:column  width="150" />
                        <p:column  width="100" />
                        <p:column    width="100"   />
                        <p:column  headerText="Grand Total" width="100" >
                            <h:outputLabel value="Grand Total" style="float: right;font-weight: bold" />

                        </p:column>
                        <p:column  headerText="#{commitems.totalAmtGt}" width="100"  >
                            <h:outputLabel value="#{commitems.totalAmtGt}" style="float: right;font-weight: bold" >

                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </p:column>
                        <p:column  headerText="#{commitems.commAmtGt}"  width="100"  >
                            <h:outputLabel value="#{commitems.commAmtGt}" style="float: right;font-weight: bold" >
                                <f:convertNumber minFractionDigits="2"/>
                            </h:outputLabel>
                        </p:column>
                        <p:column  headerText="#{commitems.compCommAmtGt}"   width="100"  >
                            <h:outputLabel value="#{commitems.compCommAmtGt}" style="float: right;font-weight: bold" >
                                <f:convertNumber minFractionDigits="2"/>
                            </h:outputLabel>
                        </p:column>
                        <p:column   width="100"  ></p:column>
                        <p:column  headerText="#{commitems.commSalTotalGt}"  rendered="#{commitems.salesTotalFlag eq 1}"  width="100"  >
                            <h:outputLabel value="#{commitems.commSalTotalGt}" style="float: right;font-weight: bold" >
                                <f:convertNumber minFractionDigits="2"/>
                            </h:outputLabel>
                        </p:column>
                        <p:column  headerText="#{commitems.creditedSalTotalGt}"   rendered="#{commitems.salesTotalFlag eq 1}" width="100"  >
                            <h:outputLabel value="#{commitems.creditedSalTotalGt}" style="float: right;font-weight: bold" >
                                <f:convertNumber minFractionDigits="2"/>
                            </h:outputLabel>
                        </p:column>

                    </p:dataTable>


                    <!--export-->
                    <p:dataTable value="#{commitems.grpItems}" var="commitems2" id="commDetailsExp1"  rendered="false">
                        <p:columnGroup   type="header"  >
                            <p:row>

                                <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                <p:column />
                                <p:column />
<!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                <p:column headerText="#{custom.labels.get('IDS_COMMISSION')} Details Report" />
                                <p:column/>
                                <p:column/>
                                <p:column/>

                                <p:column  headerText="From : #{commReports.dtFrm}" />

                                <p:column  headerText="To : #{commReports.dtTo}"/>
                                <p:column  />
                                <!--1478-->
                                <p:column    rendered="#{commitems.grp eq 1 and commitems.subGrpFlag}"/>
                                <p:column rendered="#{commitems.salesTotalFlag eq 1}" />
                                <p:column rendered="#{commitems.salesTotalFlag eq 1}" />

                            </p:row>
                            <p:row></p:row>

                            <p:row  class="header" id="myHeaderExp" >
                                <p:column rendered="#{commitems.grp ne 1}" 
                                          headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left" width="320" />


                                <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                <p:column rendered="#{commitems.grp ne 2}" 
                                          headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" class="col_left" width="320" />
                                <p:column rendered="#{commitems.grp eq 1 and commitems.subGrpFlag}" 
                                          headerText="#{custom.labels.get('IDS_REGION')}" class="col_left" width="320" />

                                <p:column rendered="#{commitems.grp ne 3}" 
                                          headerText="#{custom.labels.get('IDS_SALES_TEAM')}" class="col_left" width="240" />
                                <p:column  
                                    headerText="#{custom.labels.get('IDS_DISTRI')}" class="col_left" width="240" />

                                <p:column   headerText="Invoice" class="col_left"  width="170" />
                                <p:column   headerText="Inv Date" class="col_center" 
                                            width="220" />
                                <p:column   headerText="Chk Date" class="col_center" 
                                            width="100" />
                                <p:column headerText="Sales Amt" class="col_right" 
                                          width="100" />
                        <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->  
                                <p:column   headerText="#{custom.labels.get('IDS_COMM')} Amt" class="col_right" 
                                            width="100" />

                                <p:column  headerText="Company Split" class="col_right" 
                                           width="100" />


                                <p:column headerText="Paid" class="col_right" 
                                          width="100" />  

                                <!--1478 CRM-3053: Epic-->
                                <p:column headerText="Total Sales" class="col_right" 
                                          width="100" rendered="#{commitems.salesTotalFlag eq 1}"   />  
                                <p:column headerText="Credited Sales" class="col_right" 
                                          width="100"   rendered="#{commitems.salesTotalFlag eq 1}" />  
                            </p:row> 


                        </p:columnGroup>
                        <p:subTable value="#{commitems2.singleCommDetails}" var="commdetails"  rendered="#{(commitems.grp eq 1 and commitems.subGrpFlag)}" >

                            <f:facet  name="header"  >
                                <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                <p:outputLabel  value="#{commitems.grp eq 1 ?custom.labels.get('IDS_PRINCI')
                                                         : commitems.grp  eq 2 ?  custom.labels.get('IDS_DB_CUSTOMER') : custom.labels.get('IDS_SALES_TEAM') } "  />   <h:outputText value=":#{commitems2.itemName}"/>
                            </f:facet>
                            <p:column  rendered="#{commitems.grp ne 1}"  >
                                <h:outputText value="#{commdetails.principal}"  />       
                            </p:column>
                            <p:column  rendered="#{commitems.grp ne 2}"   >
                                <h:outputText value="#{commdetails.customer}"  />       
                            </p:column>
                            <p:column  rendered="#{commitems.grp eq 1 and commitems.subGrpFlag}"   >
                                <h:outputText value="#{commdetails.regName}"  />       
                            </p:column>

                            <p:column  rendered="#{commitems.grp ne 3}" >
                                <h:outputText value="#{commdetails.teamName}"  />               
                            </p:column>

                            <p:column  >
                                <h:outputText value="#{commdetails.distributor}"  />               
                            </p:column>
                            <p:column >
                                <h:outputText value="#{commdetails.invNo}"  />   
                                <!--<h:outputLabel value="#{commitems.commDtlItems.indexOf(commitems)}"  />--> 
                            </p:column>
                            <!--Feature #5262: CRM-4555: Somewhat urgent report needed for JD Martin-->
                            <p:column  >
                                <h:outputText value="#{commdetails.date}"  >
                                    <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                </h:outputText>
                            </p:column>
                            <!--Feature #5262: CRM-4555: Somewhat urgent report needed for JD Martin-->
                            <p:column  >
                                <h:outputText value="#{commdetails.chkDate}"  >
                                    <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                </h:outputText>

                            </p:column>
                            <p:column >
                                <h:outputText value="#{commdetails.totalAmt}" style="float: right"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>

                            </p:column>

                            <p:column  >
                                <h:outputText value="#{commdetails.commAmt}" style="float: right"  >
                                    <f:convertNumber minFractionDigits="2"/>
                                </h:outputText>    

                            </p:column>
                            <p:column >
                                <h:outputText value="#{commdetails.compCommAmt}" style="float: right"   >
                                    <f:convertNumber minFractionDigits="2"/>
                                </h:outputText> 

                            </p:column>
                            <p:column >
                                <!--//    Feature #5262 CRM-4555: Somewhat urgent report needed for JD Martin-->
                                <h:outputText value="#{commdetails.paidVal}"
                                              style="color: #{commdetails.paid eq 1  ? 'green' : 'red'}" 
                                              />   



                            </p:column>


                            <!--1478-->

                            <p:column  rendered="#{commitems.salesTotalFlag eq 1}">
                                <h:outputText value="#{commdetails.commSalTotal}" style="float: right"  rendered="#{commitems.salesTotalFlag eq 1}" >
                                    <f:convertNumber minFractionDigits="2"/>
                                </h:outputText> 

                            </p:column>
                            <p:column rendered="#{commitems.salesTotalFlag eq 1}">
                                <h:outputText value="#{commdetails.creditedSalTotal}" style="float: right"  rendered="#{commitems.salesTotalFlag eq 1}" >
                                    <f:convertNumber minFractionDigits="2"/>
                                </h:outputText> 

                            </p:column>

                            <p:columnGroup type="footer">
                                <p:row  rendered="false"> 

                                    <p:column  />
                                    <p:column  />
                                    <p:column  />
                                    <p:column  /> 
                                    <p:column  /> 


                                    <p:column footerText="Total"
                                              style="float: right"  class="footerFont"/>


                                    <p:column footerText="#{commitems2.totals[1]}"  style="float: right">

                                    </p:column>

                                    <p:column footerText="#{commitems2.totals[2]}"  style="text-align: right"/>

                                    <p:column footerText="#{commitems2.totals[3]}"  style="text-align: right" />

                                    <p:column />
                                    <p:column footerText="#{commitems.salesTotalFlag eq 1 ? commitems2.totals[4]  :''}"  style="text-align: right" />

                                    <p:column footerText="#{commitems.salesTotalFlag eq 1 ? commitems2.totals[5] :''}"  style="text-align: right" />


                                </p:row>
                            </p:columnGroup> 


                        </p:subTable>

                        <p:columnGroup type="footer">
                            <p:row> 

                                <p:column  />
                                <p:column  />
                                <p:column  />
                                <p:column  /> 
                                <p:column  /> 

                                <p:column footerText="Grand Total "
                                          style="float: right"  class="footerFont"/>


                                <p:column footerText=" #{commitems.totalAmtGt}  "  style="float: right">

                                </p:column>

                                <p:column footerText="#{commitems.commAmtGt}"  style="text-align: right"/>

                                <p:column footerText="#{commitems.compCommAmtGt}"  style="text-align: right" />

                                <p:column  /> 
                                <p:column footerText=" #{!(commitems.salesTotalFlag eq 1) ?'' :commitems.commSalTotalGt}"  style="text-align: right" rendered="#{(commitems.salesTotalFlag eq 1)}"/>

                                <p:column footerText="#{!(commitems.salesTotalFlag eq 1) ?'':commitems.creditedSalTotalGt}"  style="text-align: right" rendered="#{(commitems.salesTotalFlag eq 1)}"/>


                            </p:row>
                        </p:columnGroup> 

                    </p:dataTable>

                    <p:dataTable value="#{commitems.grpItems}" var="commitems2" id="commDetailsExpwithSubGrp"  rendered="false">
                        <p:columnGroup  type="header"  >
                            <p:row>

                                <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                <p:column />
                                <p:column />
<!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                <p:column headerText="#{custom.labels.get('IDS_COMMISSION')} Details Report" />
                                <p:column/>
                                <p:column/>
                                <p:column/>

                                <p:column  headerText="From : #{commReports.dtFrm}" />

                                <p:column  headerText="To : #{commReports.dtTo}"/>
                                <p:column  />
                                <!--1478-->
                                <p:column    rendered="#{commitems.grp eq 1 and commitems.subGrpFlag}"/>
                                <p:column rendered="#{commitems.salesTotalFlag eq 1}" />
                                <p:column rendered="#{commitems.salesTotalFlag eq 1}" />

                            </p:row>
                            <p:row></p:row>

                            <p:row  class="header" id="myHeaderExp" >
                                <p:column rendered="#{commitems.grp ne 1}" 
                                          headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left" width="320" />


                                <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                <p:column rendered="#{commitems.grp ne 2}" 
                                          headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}" class="col_left" width="320" />
                                <p:column rendered="#{commitems.grp eq 1 and commitems.subGrpFlag}" 
                                          headerText="#{custom.labels.get('IDS_REGION')}" class="col_left" width="320" />

                                <p:column rendered="#{commitems.grp ne 3}" 
                                          headerText="#{custom.labels.get('IDS_SALES_TEAM')}" class="col_left" width="240" />
                                <p:column  
                                    headerText="#{custom.labels.get('IDS_DISTRI')}" class="col_left" width="240" />

                                <p:column   headerText="Invoice" class="col_left"  width="170" />
                                <p:column   headerText="Inv Date" class="col_center" 
                                            width="220" />
                                <p:column   headerText="Chk Date" class="col_center" 
                                            width="100" />
                                <p:column headerText="Sales Amt" class="col_right" 
                                          width="100" />
   <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->                       
                                <p:column   headerText="#{custom.labels.get('IDS_COMM')} Amt" class="col_right" 
                                            width="100" />

                                <p:column  headerText="Company Split" class="col_right" 
                                           width="100" />


                                <p:column headerText="Paid" class="col_right" 
                                          width="100" />  

                                <!--1478 CRM-3053: Epic-->
                                <p:column headerText="Total Sales" class="col_right" 
                                          width="100" rendered="#{commitems.salesTotalFlag eq 1}"   />  
                                <p:column headerText="Credited Sales" class="col_right" 
                                          width="100"   rendered="#{commitems.salesTotalFlag eq 1}" />  
                            </p:row> 


                        </p:columnGroup>
                        <p:subTable value="#{commitems2.singleCommDetails}" var="commdetails"  rendered="#{(commitems.grp eq 1 and commitems.subGrpFlag)}" >

                            <f:facet  name="header"  >
                                <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                <p:outputLabel  value="#{commitems.grp eq 1 ?custom.labels.get('IDS_PRINCI')
                                                         : commitems.grp  eq 2 ?  custom.labels.get('IDS_DB_CUSTOMER') : custom.labels.get('IDS_SALES_TEAM') } "  />   <h:outputText value=":#{commitems2.itemName}"/>
                            </f:facet>
                            <p:column  rendered="#{commitems.grp ne 1}"  >
                                <h:outputText value="#{commdetails.principal}"  />       
                            </p:column>
                            <p:column  rendered="#{commitems.grp ne 2}"   >
                                <h:outputText value="#{commdetails.customer}"  />       
                            </p:column>
                            <p:column  rendered="#{commitems.grp eq 1 and commitems.subGrpFlag}"   >
                                <h:outputText value="#{commdetails.regName}"  />       
                            </p:column>

                            <p:column  rendered="#{commitems.grp ne 3}" >
                                <h:outputText value="#{commdetails.teamName}"  />               
                            </p:column>

                            <p:column  >
                                <h:outputText value="#{commdetails.distributor}"  />               
                            </p:column>
                            <p:column >
                                <h:outputText value="#{commdetails.invNo}"  />   
                                <!--<h:outputLabel value="#{commitems.commDtlItems.indexOf(commitems)}"  />--> 
                            </p:column>
                            <!--Feature #5262: CRM-4555: Somewhat urgent report needed for JD Martin-->
                            <p:column  >
                                <h:outputText value="#{commdetails.date}"  >
                                    <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                </h:outputText>
                            </p:column>
                            <!--Feature #5262: CRM-4555: Somewhat urgent report needed for JD Martin-->
                            <p:column  >
                                <h:outputText value="#{commdetails.chkDate}"  >
                                    <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                </h:outputText>

                            </p:column>
                            <p:column >
                                <h:outputText value="#{commdetails.totalAmt}" style="float: right"  >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputText>

                            </p:column>

                            <p:column  >
                                <h:outputText value="#{commdetails.commAmt}" style="float: right"  >
                                    <f:convertNumber minFractionDigits="2"/>
                                </h:outputText>    

                            </p:column>
                            <p:column >
                                <h:outputText value="#{commdetails.compCommAmt}" style="float: right"   >
                                    <f:convertNumber minFractionDigits="2"/>
                                </h:outputText> 

                            </p:column>
                            <p:column >
                                <!--//    Feature #5262CRM-4555: Somewhat urgent report needed for JD Martin-->
                                <h:outputText value="#{commdetails.paidVal}"
                                              style="color: #{commdetails.paid eq 1  ? 'green' : 'red'}" 
                                              />   



                            </p:column>


                            <!--1478-->

                            <p:column  rendered="#{commitems.salesTotalFlag eq 1}">
                                <h:outputText value="#{commdetails.commSalTotal}" style="float: right"  rendered="#{commitems.salesTotalFlag eq 1}" >
                                    <f:convertNumber minFractionDigits="2"/>
                                </h:outputText> 

                            </p:column>
                            <p:column rendered="#{commitems.salesTotalFlag eq 1}">
                                <h:outputText value="#{commdetails.creditedSalTotal}" style="float: right"  rendered="#{commitems.salesTotalFlag eq 1}" >
                                    <f:convertNumber minFractionDigits="2"/>
                                </h:outputText> 

                            </p:column>

                            <p:columnGroup type="footer">
                                <p:row  rendered="false"> 

                                    <p:column  />
                                    <p:column  />
                                    <p:column  />
                                    <p:column  /> 
                                    <p:column  /> 
                                    <p:column  /> 
                                    <p:column footerText="Total "
                                              style="float: right"  class="footerFont"/>


                                    <p:column footerText="#{commitems2.totals[1]}"  style="float: right">

                                    </p:column>

                                    <p:column footerText="#{commitems2.totals[2]}"  style="text-align: right"/>

                                    <p:column footerText="#{commitems2.totals[3]}"  style="text-align: right" />

                                    <p:column  />
                                    <!--Feature #5262: CRM-4555: Somewhat urgent report needed for JD Martin-->
                                    <p:column footerText="#{commitems.salesTotalFlag eq 1 ?commitems2.totals[4] :''}"  style="text-align: right" />

                                    <p:column footerText="#{commitems.salesTotalFlag eq 1 ? commitems2.totals[5] :''}"  style="text-align: right" />


                                </p:row>
                            </p:columnGroup> 


                        </p:subTable>

                        <p:columnGroup type="footer">
                            <p:row> 

                                <p:column  />
                                <p:column  />
                                <p:column  />
                                <p:column  /> 
                                <p:column  /> 
                                <p:column   /> 
                                <p:column    rendered="#{!(commitems.grp eq 1 and commitems.subGrpFlag)}"/>
                                <p:column footerText="Grand Total "
                                          style="float: right"  class="footerFont"/>


                                <p:column footerText=" #{commitems.totalAmtGt}  "  style="float: right">

                                </p:column>

                                <p:column footerText="#{commitems.commAmtGt}"  style="text-align: right"/>

                                <p:column footerText="#{commitems.compCommAmtGt}"  style="text-align: right" />

                                <p:column   /> 
                                <!--Feature #5262: CRM-4555: Somewhat urgent report needed for JD Martin-->
                                
                                <p:column footerText=" #{!(commitems.salesTotalFlag eq 1) ? '' :commitems.commSalTotalGt}"  style="text-align: right" rendered="#{(commitems.salesTotalFlag eq 1)}"/>

                                <p:column footerText="#{!(commitems.salesTotalFlag eq 1) ?'' : commitems.creditedSalTotalGt}"  style="text-align: right" rendered="#{(commitems.salesTotalFlag eq 1)}"/>


                            </p:row>
                        </p:columnGroup> 

                    </p:dataTable>





                </h:panelGroup>

                <!--                <div id="footer">
                                    <div style="font-size: 12px;color: #4f4f4f;display: inline-block;float: right;line-height: 20px">
                                                                <ui:insert name="footer">
                                        
                                                                </ui:insert>   
                                        Repfabric Dashboard - Commission Details Report 
                                                                <h:outputLabel value="#{params.getCurrentTimeZoneDate()}">
                                        <h:outputLabel value="#{rFUtilities.getCurrentTimeZoneDate()}">
                                            <f:convertDateTime   pattern="#{globalParams.dateTimeFormat}" />
                                        </h:outputLabel>
                                    </div>
                                </div>-->

            </h:form>

            <p:dialog widgetVar="inCommDetProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Please wait...Exporting records" />
                        <br /><br />

                    </p:outputPanel>
                </h:form>
            </p:dialog>

        </div>


        <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
        <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>


        <style>

            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }


            .jqplot-target {

                font-size: 0.75em!important;
            }
        </style>


        <!--#3249: Task Reporting > Commission Reports > Add loading gif START -->
        <script>

            window.onload = function () {
//                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("commdetrpt").style.pointerEvents = "all";
                    document.getElementById("commdetrpt").style.opacity = "1";

                    $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };


            function hide()
            {

                $('#loading-image').hide();
                document.getElementById("commdetrpt").style.pointerEvents = "all";
                document.getElementById("commdetrpt").style.opacity = "1";
            }

            function processGif()
            {
                $('#loading-image').show();
                document.getElementById("commdetrpt").style.pointerEvents = "none";
                document.getElementById("commdetrpt").style.opacity = "0.5";
            }

            function start() {

                PF('inCommDetProgressDlg').show();
            }

            function stop() {
                PF('inCommDetProgressDlg').hide();
            }



        </script>

        <!--#3249:   Task Reporting > Commission Reports > Add loading gif  END   -->

    </ui:define>



</ui:composition>