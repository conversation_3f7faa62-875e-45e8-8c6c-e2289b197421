<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: productdetails All.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

    <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>   Sales Rep Summary   </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>


            <f:viewParam name="fdate" value="#{repSummary.fromDate}" />
            <f:viewParam name="tdate" value="#{repSummary.toDate}"/> 
            <!--26-12-2020:TASK#2822-CRM-3205:Feature Request for change to Sales Rep Summary Report-->
            <f:viewParam name="f" value="#{repSummary.salesTotalFlag}"/>
            <!--#5797 : CRM-4739: salesrepsummary: option to select all salespeople : Gunder - poornima-->
            <f:viewParam name="srf" value="#{commReports.salesRepMultiFlag}"/>
            <f:viewAction action="#{reportFilters.assignUsers()}" />
            <!--End-poornima-->

            <f:viewAction action="#{repSummary.run()}"/>
            <!--            //    Feature #6260 CRM-4951: Add check box to hide column in Sales Rep Summary Report-->
            <f:viewAction action="#{repSummary.hideSalesSummaryColumn()}"/>
            <f:viewParam name="sf" value="#{repSummary.splitAmountFlag}" /> 


            <!--#1948:CRM-1131: reports: salesrep comm" cwrreps - salesman can see other salesman commissions!-->
            <f:viewParam name="g" value="#{repSummary.grp}" /> 
            <!--                                        <f:metadata> 
            
                                            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}" />
                                            <f:viewParam name="c" value="#{rreportFilters.customer.compId}" />
                                            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}" />
                                            <f:viewParam name="fdate" value="#{repDetailItem.fromDate}" />
                                            <f:viewParam name="tdate" value="#{repDetailItem.toDate}" />
                                            <f:viewParam name="m" value="#{reportFilters.salesRep.userName}" />
                                            <f:viewParam name="g" value="#{repDetailItem.grp}" />
                                        </f:metadata>-->
        </f:metadata>
    </ui:define>

    <ui:define name="body">
        <f:event listener="#{repSummary.isPageAccessible}" type="preRenderView"/>
        <!--#3249: Task Reporting > Commission Reports > Add loading gif-->
        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7"  id="salRepSumrpt">

            <!--#3249:#3249:     Task Reporting > Commission Reports > Add loading gif-->

            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                            left: 600px;
                            top: 300px;
                            width: 150px;
                            height: 150px;
                            z-index: 9999; opacity: .5 !important; " />  



            <h:form id="frm1">  
                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}"  update="frm1:inputPrincName :frm1:viewreport" />
                <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}"  update="frm1:inputAllSalesTeam :frm1:viewreport"/>
                <!--//#2999Task Customer Summary report - Add multi select Sales team option-->
                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="frm1:inputCustomer :frm1:inputAllSalesTeam :frm1:salTeambtn :frm1:viewreport"/>       
                <p:remoteCommand name="applyUser" actionListener="#{reportFilters.applySalesRep(userLookupService.user)}" update="frm1:inputUserName :frm1:viewreport"/>  


                <div class="ui-g ui-fluid">
                    <div class="compNamebg" style="float:left">
                        <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                        <br/>
                        Sales Rep Summary
                        <br/>
                        <p:outputLabel value="  From:" /> 
                        <h:panelGroup>
                            #{commReports.dtFrm}
                        </h:panelGroup><br/>
                        <p:outputLabel value="  To:" />
                        <h:panelGroup>
                            <p:spacer width="22px"/>

                            #{commReports.dtTo}
                        </h:panelGroup>



                    </div>


                    <div class="ui-sm-12 ui-md-4 ui-lg-4" >
                        <h:panelGrid columns="3" style="margin-left: 140px">
                            <p:outputLabel value="Group by"/>
                            <p:spacer width="4px"/>
                            <p:selectOneRadio value="#{repSummary.grp}"  widgetVar="grp" layout="pageDirection"  required="true">
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/> 
                                <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                                <f:selectItem   itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                                <f:selectItem itemValue="3" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}"/>


                            </p:selectOneRadio></h:panelGrid>
                    </div> 


                    <div>
                        <p:panelGrid columns="2" style="float: right;width: 350px; margin-left: 160px"     layout="grid" styleClass="box-primary no-border ui-fluid ">
                            <!--#5797 : CRM-4739: salesrepsummary: option to select all salespeople : Gunder - poornima
                            added rendered for outputlabel & panel group b'coz for non owner-->
                            <p:outputLabel for="inputUserName" value="Sales Rep" rendered="#{loginBean.loggedInUser.userCategory != 1}" />
                            <h:panelGroup class="ui-inputgroup" rendered="#{loginBean.loggedInUser.userCategory != 1}">                               
                                <p:inputText id="inputUserName" value="#{reportFilters.salesRep.userName}"  readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Sales Rep" immediate="true" 
                                                  actionListener="#{userLookupService.listSalesRep('applyUser', 'Sales Rep')}"
                                                  update=":formUserLookup :frm1" oncomplete="PF('lookupUser').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <!--#5797 : CRM-4739: salesrepsummary: option to select all salespeople : Gunder - poornima-->
                            <p:outputLabel value="Sales Rep" rendered="#{loginBean.loggedInUser.userCategory == 1}" />
                            <h:panelGroup class="ui-inputgroup" style="width:120%" rendered="#{loginBean.loggedInUser.userCategory == 1}"  >
                                <h:selectOneListbox  styleClass="sel-users"  size="2" style="width: 200%" id="selUsers"     >
                                    <f:selectItems value="#{reportFilters.users}" var="users"  
                                                   itemValue="#{users.userId}"
                                                   itemLabel="#{users.userName}"  />
                                </h:selectOneListbox>

                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Choose Sales Rep" action="#{lookupService.list('USER')}"  update=":frmUsersLookup" oncomplete="PF('dlgUsersLookup').show();" />
                                <p:commandLink styleClass="sel-users" value="Clear" actionListener="#{lookupService.clear('USER')}" update="@(.sel-users)" disabled="#{!(reportFilters.users!=null and reportFilters.users.size() != 0)}" style="text-decoration:underline"/>
                            </h:panelGroup>
                            <!--End-poornima-->
                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                <!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup :frm1" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--#3473:     sales team - Sales team data we can enter manually-->


                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                  update=":formSalesTeamLookup"  oncomplete="PF('lookupSalesTeam').show()" 
                                                  disabled="#{reportFilters.customer.compId ne 0}"  id="salTeambtn"
                                                  styleClass="btn-info btn-xs"  style="width:67px" />
                            </h:panelGroup> 

                            <h:panelGroup>
                                 <!--          #10300  CRM-6433:-->
                                <p:commandButton  value="View Report "  id="viewreport"  onclick="processGif()" actionListener="#{repSummary.hideSalesSummaryColumn()}"   action="#{repSummary.run()}"   styleClass="btn btn-primary btn-xs sel-users"  update=":frm1"  style="width: 50px;"  oncomplete="hide();" disabled="#{loginBean.loggedInUser.userCategory == 1 ? !(reportFilters.users!=null and reportFilters.users.size() != 0) : !(reportFilters.salesRep.userName.length() >0)}"/>
                                <!--#3341: Task Reporting > Sales Rep Summary export START  -->
                                <p:spacer width="10px" />

                                <!--#5797 : CRM-4739: salesrepsummary: option to select all salespeople : Gunder
                                for owner-->
                                <p:commandLink id="xls" ajax="false" onclick="PrimeFaces.monitorDownload(start, stop);" rendered="#{loginBean.loggedInUser.userCategory == 1}">  
                                    <p:graphicImage name="/images/excel.png" width="24"    />
                                    <pe:exporter type="xlsx" target="#{loginBean.loggedInUser.userCategory == 1?'salerepSumryMultiSelectExport':'salrepSummaryExport'}"  fileName="SalesRepSummary" subTable="#{loginBean.loggedInUser.userCategory == 1}" postProcessor="#{repSummary.postProcessXLS}"/>                                    

                                </p:commandLink>
                                 <!--            //    Feature #6260 CRM-4951: Add check box to hide column in Sales Rep Summary Report-->
                                <!--#5797 : CRM-4739: salesrepsummary: option to select all salespeople : Gunder
                                for sales rep-->
                              <p:commandLink id="xls1" ajax="false" rendered="#{loginBean.loggedInUser.userCategory != 1}"   onclick="PrimeFaces.monitorDownload(start, stop);">
                                    <p:graphicImage name="/images/excel.png" width="24"    />
                                    <pe:exporter type="xlsx" target="#{loginBean.loggedInUser.userCategory == 1?'salerepSumryMultiSelectExport':'salrepSummaryExport'}"  fileName="SalesRepSummary" postProcessor="#{repSummary.postProcessXLS}" />  
                                </p:commandLink>

                                <p:commandLink id="pdf" ajax="false"  onclick="PrimeFaces.monitorDownload(start, stop);"  >  
                                    <p:graphicImage value="/resources/images/pdf.png"  width="24" />  
                                    <pe:exporter type="pdf" target="#{loginBean.loggedInUser.userCategory == 1?'salerepSumryMultiSelectExport':'salrepSummaryExport'}" fileName="SalesRepSummary" subTable="#{loginBean.loggedInUser.userCategory == 1}"   preProcessor="#{repSummary.preProcessPDF}" />  

                                </p:commandLink>
                                <!--#3341: Task Reporting > Sales Rep Summary export END  -->
                            </h:panelGroup>

                        </p:panelGrid>

                    </div>

                </div>

                <!--            </h:form>
                            <h:form id="detalis">-->

                <div class="box box-info box-body" style="vertical-align: top">                   
                    <!--#5797 : CRM-4739: salesrepsummary: option to select all salespeople : Gunder - poornima
                    //added datascroller for level 1 and level 2 i.e for drill down to display sales rep name in blue color bar-->
                    <p:dataScroller value="#{repSummary.listSalesRep}" chunkSize="10" var="listsr" rendered="#{loginBean.loggedInUser.userCategory == 1}" style="margin-bottom:0">                            
                        <div class="div-selected" style="height:25px;width: 100%;" >
                            <label style="font-size: 1.3em;font-weight: bold;color: #384c55;line-height: 25px;padding-left: 5px;width:120% ">
                                #{listsr.repName} 
                            </label>               
                        </div>
                        <!--Here it will display details for sales rep-->
                        <p:dataTable value="#{listsr.details}" var="sum" emptyMessage="No data found"   id="listsalerepSummary" rendered="#{loginBean.loggedInUser.userCategory == 1}">
                            <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:column sortBy="#{sum.itemName}"  headerText="#{commReports.repGrp eq 1 ? custom.labels.get('IDS_PRINCI') :
                                                commReports.repGrp eq 2 ?  custom.labels.get('IDS_DB_CUSTOMER'):custom.labels.get('IDS_SALES_TEAM')}" 

                                      class="col_left" >
                                <h:outputLabel value="#{sum.itemName}"  />   
                                <f:facet name="footer">
                                    <h:outputLabel value="Total"  style="float: right; font-weight: bold" />
                                </f:facet>
                            </p:column>
<!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                            <p:column sortBy="#{sum.totalAmt}" headerText="Sales Amt" class="col_right" width="150" >
                                <h:outputLabel value="#{sum.totalAmt}"  >
                                    <f:convertNumber maxFractionDigits="2" groupingUsed="true" minFractionDigits="2" />  <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                                </h:outputLabel>    
                                <f:facet name="footer">
                                    <h:outputLabel value="#{listsr.totalsdtl[1]}" style="float: right;font-weight: bold"  >
                                        <f:convertNumber maxFractionDigits="2"  groupingUsed="true" minFractionDigits="2" /> <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>
                            <!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                            <!--//    Feature #3091:-->  
                            <!--            //    Feature #6260 CRM-4951: Add check box to hide column in Sales Rep Summary Report-->
                            <p:column rendered="#{repSummary.splitAmountFlag}" sortBy="#{sum.splitInvAmount}"  headerText="Split Inv. Amount" class="col_right" width="150" >
                                <h:outputLabel value="#{sum.splitInvAmount}" style="float: right"   >
                                    <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                </h:outputLabel> 
                                <f:facet name="footer">
                                    <h:outputLabel value="#{listsr.totalsdtl[5]}"  style="float: right;font-weight: bold" >
                                        <f:convertNumber   maxFractionDigits="2"  minFractionDigits="2"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>
<!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
    <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->                
<p:column sortBy="#{sum.commAmt}" headerText="#{custom.labels.get('IDS_COMM')} Amt" class="col_right" width="150" >
                                <h:outputLabel value="#{sum.commAmt}"  > 
                                    <f:convertNumber maxFractionDigits="2" groupingUsed="true" minFractionDigits="2" /> <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                                </h:outputLabel>    
                                <f:facet name="footer">
                                    <h:outputLabel value="#{listsr.totalsdtl[2]}" style="float: right;font-weight: bold"  >
                                        <f:convertNumber maxFractionDigits="2"  groupingUsed="true" minFractionDigits="2" /> <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>
                            <!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                            <!--26-12-2020:TASK#2822-CRM-3205:Feature Request for change to Sales Rep Summary Report-->
                            <p:column sortBy="#{sum.creditedSalTotal}"  headerText="Credited Sales" class="col_right"  rendered="#{repSummary.salesTotalFlag eq 1}" width="150">
                                <h:outputLabel value="#{sum.creditedSalTotal}" style="float: right"   >
                                    <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                </h:outputLabel> 
                                <f:facet name="footer">
                                    <h:outputLabel value="#{listsr.totalsdtl[4]}"  style="float: right;font-weight: bold" >
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>                        


                        </p:dataTable>                         
                    </p:dataScroller>
                    <!--End-poornima-->

                    <!--#5797 : CRM-4739: salesrepsummary: option to select all salespeople : Gunder - poornima
                    added rendered conddition for non owner-->
                    <p:dataTable  value="#{repSummary.listRepSummary}" var="sum" emptyMessage="No data found"   id="salrepSummary" rendered="#{loginBean.loggedInUser.userCategory != 1}">
                        <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                        <p:column sortBy="#{sum.itemName}"  headerText="#{commReports.repGrp eq 1 ? custom.labels.get('IDS_PRINCI') :
                                            commReports.repGrp eq 2 ?  custom.labels.get('IDS_DB_CUSTOMER'):custom.labels.get('IDS_SALES_TEAM')}" 

                                  class="col_left" >
                            <h:outputLabel value="#{sum.itemName}"  />   
                            <f:facet name="footer">
                                <h:outputLabel value="Total"  style="float: right; font-weight: bold" />
                            </f:facet>
                        </p:column>
<!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                        <p:column sortBy="#{sum.totalAmt}" headerText="Sales Amt" class="col_right" width="150" >
                            <h:outputLabel value="#{sum.totalAmt}"  >
                                <f:convertNumber  maxFractionDigits="2" groupingUsed="true" minFractionDigits="2" />  <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                            </h:outputLabel>    
                            <f:facet name="footer">
                                <h:outputLabel value="#{repSummary.totals[1]}" style="float: right;font-weight: bold"  >
                                    <f:convertNumber  maxFractionDigits="2" groupingUsed="true" minFractionDigits="2" /> <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                                </h:outputLabel>
                            </f:facet>
                        </p:column>
                        <!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                        <!--//    Feature #3091:-->  
                        <!--            //    Feature #6260 CRM-4951: Add check box to hide column in Sales Rep Summary Report-->
                        <p:column rendered="#{repSummary.splitAmountFlag}" sortBy="#{sum.splitInvAmount}"  headerText="Split Inv. Amount" class="col_right" width="150" >
                            <h:outputLabel value="#{sum.splitInvAmount}" style="float: right"   >
                                <f:convertNumber  maxFractionDigits="2" minFractionDigits="2"/>
                            </h:outputLabel> 
                            <f:facet name="footer">
                                <h:outputLabel value="#{repSummary.totals[5]}"  style="float: right;font-weight: bold" >
                                    <f:convertNumber  maxFractionDigits="2"  minFractionDigits="2"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>
<!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
               <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->      
<p:column sortBy="#{sum.commAmt}" headerText="#{custom.labels.get('IDS_COMM')} Amt" class="col_right" width="150" >
                            <h:outputLabel value="#{sum.commAmt}"  > 
                                <f:convertNumber  maxFractionDigits="2" groupingUsed="true" minFractionDigits="2" /> <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                            </h:outputLabel>    
                            <f:facet name="footer">
                                <h:outputLabel value="#{repSummary.totals[2]}" style="float: right;font-weight: bold"  >
                                    <f:convertNumber  maxFractionDigits="2" groupingUsed="true" minFractionDigits="2" /> <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                                </h:outputLabel>
                            </f:facet>
                        </p:column>
                        <!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                        <!--26-12-2020:TASK#2822-CRM-3205:Feature Request for change to Sales Rep Summary Report-->
                        <p:column sortBy="#{sum.creditedSalTotal}"  headerText="Credited Sales" class="col_right"  rendered="#{repSummary.salesTotalFlag eq 1}" width="150">
                            <h:outputLabel value="#{sum.creditedSalTotal}" style="float: right"   >
                                <f:convertNumber  maxFractionDigits="2"  minFractionDigits="2"/>
                            </h:outputLabel> 
                            <f:facet name="footer">
                                <h:outputLabel value="#{repSummary.totals[4]}"  style="float: right;font-weight: bold" >
                                    <f:convertNumber  maxFractionDigits="2"  minFractionDigits="2"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>



                    </p:dataTable>

                    <!--#3341: Task Reporting > Sales Rep Summary export START  -->
                          <!--            //    Feature #6260 CRM-4951: Add check box to hide column in Sales Rep Summary Report-->
                    <!--#5797 : CRM-4739: salesrepsummary: option to select all salespeople : Gunder - poornima
                        added rendered for non owner-->
                    <p:dataTable  value="#{repSummary.listRepSummary}" var="sum" emptyMessage="No data found"   id="salrepSummaryExport" rendered="false"  >

                        <p:columnGroup  type="header" class="header"   >
                            <p:row>
                                <!--26-12-2020:TASK#2822-CRM-3205:Feature Request for change to Sales Rep Summary Report-->
                                <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                <!--26-12-2020:TASK#2822-CRM-3205:Feature Request for change to Sales Rep Summary Report-->

                                <p:column    headerText="Sales Rep Summary" />

                                <!--26-12-2020:TASK#2822-CRM-3205:Feature Request for change to Sales Rep Summary Report-->

                                <p:column    headerText="From: #{commReports.dtFrm}  To: #{commReports.dtTo}" />
                                 <!--            //    Feature #6260 CRM-4951: Add check box to hide column in Sales Rep Summary Report-->
                                <!--26-12-2020:TASK#2822-CRM-3205:Feature Request for change to Sales Rep Summary Report-->
                                <p:column rendered="#{repSummary.salesTotalFlag eq 1}" width="200px" />
                                <!--//    Feature #3091:-->  
                                 <p:column rendered="#{repSummary.splitAmountFlag}" /> 
                            </p:row>


                            <p:row>
                                  <!--            //    Feature #6260 CRM-4951: Add check box to hide column in Sales Rep Summary Report-->
                                <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                <p:column   headerText="#{commReports.repGrp eq 1 ? custom.labels.get('IDS_PRINCI') :
                                                          commReports.repGrp eq 2 ? custom.labels.get('IDS_DB_CUSTOMER'):custom.labels.get('IDS_SALES_TEAM') }" style="text-height: max-size"/>
                                <p:column  headerText="Sales Amt"/>
                                <!--            //    Feature #6260 CRM-4951: Add check box to hide column in Sales Rep Summary Report-->                                       <!--//    Feature #3091:-->  
                                <p:column rendered="#{repSummary.splitAmountFlag}"  headerText="Split Inv. Amount" class="col_right" width="110"/> 

                                

                      <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                <p:column headerText="#{custom.labels.get('IDS_COMM')} Amt"/>
                                <!--26-12-2020:TASK#2822-CRM-3205:Feature Request for change to Sales Rep Summary Report-->
                                <p:column headerText="Credited Sales " rendered="#{repSummary.salesTotalFlag eq 1}"></p:column>



                            </p:row>


                        </p:columnGroup>  




                        <p:column sortBy="#{sum.itemName}"   

                                  class="col_left" >
                            <h:outputText value="#{sum.itemName}"  />   
                            <f:facet name="footer">
                                <h:outputText value="Total"  style="float: right; font-weight: bold" />
                            </f:facet>
                        </p:column>
<!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                        <p:column sortBy="#{sum.totalAmt}"  class="col_right" width="150" >
                            <h:outputText value="#{sum.totalAmt}" style="float:right" >
                                <f:convertNumber  maxFractionDigits="2" groupingUsed="true" minFractionDigits="2" />  <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                            </h:outputText>  
                            <!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                            <f:facet name="footer">
                                <h:outputText value="#{repSummary.totals[1]}" style="float:right;font-weight: bold"  >
                                    <f:convertNumber  maxFractionDigits="2" groupingUsed="true" minFractionDigits="2" /> <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                                </h:outputText>
                            </f:facet>
                        </p:column>
                        <!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                        <!--            //    Feature #6260 CRM-4951: Add check box to hide column in Sales Rep Summary Report-->
                        <!--//    Feature #3091:-->  
                        <p:column rendered="#{repSummary.splitAmountFlag}" sortBy="#{sum.splitInvAmount}"   class="col_right" width="110" >
                            <h:outputText value="#{sum.splitInvAmount}" style="float: right"   >
                                <f:convertNumber  maxFractionDigits="2"  minFractionDigits="2"/>
                            </h:outputText> 
                            <!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                            <f:facet name="footer">
                                <h:outputText value="#{repSummary.totals[5]}"  style="float: right;font-weight: bold" >
                                    <f:convertNumber  maxFractionDigits="2"  minFractionDigits="2"/>
                                </h:outputText>
                            </f:facet>
                        </p:column>
                        <!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->

                        <p:column sortBy="#{sum.commAmt}"  class="col_right" width="150" >
                            <h:outputText value="#{sum.commAmt}" style="float:right" >
                                <f:convertNumber  maxFractionDigits="2"  groupingUsed="true" minFractionDigits="2" /> <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                            </h:outputText>    
                            <!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                            <f:facet name="footer">
                                <h:outputText value="#{repSummary.totals[2]}" style="float:right;font-weight: bold;text-align: right"  >
                                    <f:convertNumber  maxFractionDigits="2" groupingUsed="true" minFractionDigits="2" /> <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                                </h:outputText>
                            </f:facet>
                        </p:column>
                         <!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                        <!--26-12-2020:TASK#2822-CRM-3205:Feature Request for change to Sales Rep Summary Report-->
                        <p:column sortBy="#{sum.creditedSalTotal}"  class="col_right" width="150" rendered="#{repSummary.salesTotalFlag eq 1}" >
                            <h:outputText value="#{sum.creditedSalTotal}" style="float:right" >
                                <f:convertNumber  maxFractionDigits="2" groupingUsed="true" minFractionDigits="2" /> 
                            </h:outputText>    
                            <f:facet name="footer">
                                <h:outputText value="#{repSummary.totals[4]}" style="float:right;font-weight: bold;text-align: right"  >
                                    <f:convertNumber  maxFractionDigits="2" groupingUsed="true" minFractionDigits="2" /> 
                                </h:outputText>
                            </f:facet>
                        </p:column>

                    </p:dataTable>
                    <!--#3341: Task Reporting > Sales Rep Summary export END  -->
                    <!--            //    Feature #6260 CRM-4951: Add check box to hide column in Sales Rep Summary Report-->
                    <!--#5797 : CRM-4739: salesrepsummary: option to select all salespeople : Gunder - poornima
                       Export for multiselect option for sales rep i.e for owner-->
                        <p:dataTable  value="#{repSummary.listSalesRep}" var="slrp" id="salerepSumryMultiSelectExport" rendered="false"  >
                        <p:columnGroup  type="header" class="header">
                            <p:row>
                                <p:column headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                               
                                <p:column headerText="Sales Rep Summary" /> 

                               <p:column colspan="1" headerText="From: #{commReports.dtFrm}  To: #{commReports.dtTo}" />

                                <p:column rendered="#{repSummary.salesTotalFlag eq 1  }" width="200px" /> 
                                <p:column rendered="#{repSummary.splitAmountFlag}" /> 
                                   

                            </p:row> 
                            
                               
                            
                                           
                            <p:row >
                                 <!--            //    Feature #6260 CRM-4951: Add check box to hide column in Sales Rep Summary Report-->
                                <p:column headerText="#{commReports.repGrp eq 1 ? custom.labels.get('IDS_PRINCI') :
                                                        commReports.repGrp eq 2 ? custom.labels.get('IDS_DB_CUSTOMER'):custom.labels.get('IDS_SALES_TEAM') }" style="text-height: max-size"/>
                                <p:column headerText="Sales Amt"/>
                                <p:column rendered="#{globalParams.showSplitOnSalesSummaryAmt}"  headerText="Split Inv. Amount" class="col_right" width="110" />
                       <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->     
                                <p:column headerText="#{custom.labels.get('IDS_COMM')} Amt"/>                                   
                                <p:column headerText="Credited Sales " rendered="#{repSummary.salesTotalFlag eq 1}"/>
                                 
                            </p:row>                                    

                        </p:columnGroup>

                        <p:subTable id="SaleRep" value="#{slrp.details}" var="sum" >
                            <f:facet  name="header"  >
                                <p:outputLabel  value="Sales Rep"/>
                                <h:outputText value=":#{slrp.repName}" style="text-align:center" />
                            </f:facet>

                            <p:column sortBy="#{sum.itemName}" class="col_left" >
                                <h:outputText value="#{sum.itemName}" /> 

                            </p:column>
<!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                            <p:column sortBy="#{sum.totalAmt}" >
                                <h:outputText value="#{sum.totalAmt}" >
                                    <f:convertNumber groupingUsed="true" maxFractionDigits="2" minFractionDigits="2" /> 
                                    <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                                </h:outputText> 

                            </p:column>
                             <!--            //    Feature #6260 CRM-4951: Add check box to hide column in Sales Rep Summary Report-->
                            <!--//    Feature #3091:-->  
                            <!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                             <p:column rendered="#{globalParams.showSplitOnSalesSummaryAmt}" sortBy="#{sum.splitInvAmount}" >
                                <h:outputText value="#{sum.splitInvAmount}">
                                    <f:convertNumber groupingUsed="true" maxFractionDigits="2" minFractionDigits="2"/>
                                </h:outputText> 

                            </p:column>
                           <!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                            <p:column sortBy="#{sum.commAmt}" >
                                <h:outputText value="#{sum.commAmt}"  > 
                                    <f:convertNumber groupingUsed="true" maxFractionDigits="2" minFractionDigits="2" /> <!--ticket#306748 comm salesrep details and summ:2decimal places -->
                                </h:outputText>

                            </p:column>
                           <!--#9358 CRM-6237  sales rep summary: credited sales showing 3 decimals for money-->
                            <!--26-12-2020:TASK#2822-CRM-3205:Feature Request for change to Sales Rep Summary Report-->
                            <p:column sortBy="#{sum.creditedSalTotal}"  rendered="#{repSummary.salesTotalFlag eq 1}">
                                <h:outputText value="#{sum.creditedSalTotal}" >
                                    <f:convertNumber groupingUsed="true" maxFractionDigits="2" minFractionDigits="2"/>
                                </h:outputText>

                            </p:column>
                            
                            
                            <p:columnGroup type="footer">
                                <p:row>
                                       <!--            //    Feature #6260 CRM-4951: Add check box to hide column in Sales Rep Summary Report-->
                                    <p:column footerText="Total :" style="text-align: left;width: 150px"  />

                                    <p:column footerText="#{slrp.totalsdtl[1]}" />
                                    <p:column footerText="#{globalParams.showSplitOnSalesSummaryAmt ? (globalParams.showSplitOnSalesSummaryAmt ? monthlyExpandedRep.convertValue(slrp.totalsdtl[5]):'') : slrp.totalsdtl[2]}" />
                                    <p:column footerText="#{globalParams.showSplitOnSalesSummaryAmt ? slrp.totalsdtl[2] : (repSummary.salesTotalFlag eq 1 ? slrp.totalsdtl[4]: '')}" />
                                    <p:column footerText="#{repSummary.salesTotalFlag eq 1 ? (globalParams.showSplitOnSalesSummaryAmt ? slrp.totalsdtl[4] : '') :' '}" />

                                </p:row>
                            </p:columnGroup> 
                        </p:subTable>
                    </p:dataTable>
                    <!--End-poornima-->
                  
                </div>

                <style>

                    .ui-panelgrid .ui-panelgrid-cell {
                        padding-top: 1px !important;
                        padding-bottom: 1px !important;
                    }

                    [role="gridcell"]{

                        padding: 0px 10px !important;
                    }
                    .content-header{     display: none; }


                    .jqplot-target {

                        font-size: 0.75em!important;
                    }

                </style>


                <!--#3249: Task Reporting > Commission Reports > Add loading gif START -->
                <script>

                    window.onload = function () {
//                $('#loading-image').show();
                        setTimeout(function () {
                            var t = performance.timing;
                            console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                            document.getElementById("salRepSumrpt").style.pointerEvents = "all";
                            document.getElementById("salRepSumrpt").style.opacity = "1";

                            $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                        }, 0);
                    };


                    function hide()
                    {

                        $('#loading-image').hide();
                        document.getElementById("salRepSumrpt").style.pointerEvents = "all";
                        document.getElementById("salRepSumrpt").style.opacity = "1";
                    }

                    function processGif()
                    {
                        $('#loading-image').show();
                        document.getElementById("salRepSumrpt").style.pointerEvents = "none";
                        document.getElementById("salRepSumrpt").style.opacity = "0.7";
                    }

                    function start() {

                        PF('inSalRepSumProgressDlg').show();
                    }

                    function stop() {
                        PF('inSalRepSumProgressDlg').hide();
                    }





                </script>

                <!--#3249: Task Reporting > Commission Reports > Add loading gif  END -->
            </h:form>

            <p:dialog widgetVar="inSalRepSumProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Please wait...Exporting records" />
                        <br /><br />
                        <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                    </p:outputPanel>
                </h:form>
            </p:dialog>

            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
            <ui:include src="/lookup/UserLookupDlg.xhtml"  />
            <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
            <ui:include src="/lookup/PartNumDlg.xhtml"/>
            <ui:include src="/lookup/UsersLookupDlg.xhtml"/>
        </div>
    </ui:define>


</ui:composition>