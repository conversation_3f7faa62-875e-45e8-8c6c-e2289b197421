<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: ytdcomparison.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

         <!--#363:page title-->
<ui:define name="head">


<link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>

<title> Line Overview </title>
</ui:define>
     
    <ui:define name="meta">
        <f:metadata>
            <f:viewParam name="yr" value="#{reportFilters.repYear}" />
            <!--#3343: Task Reporting > Line Overview - Export-->
            <f:viewParam name="mt" value="#{reportFilters.repMonth}" />
            <f:viewAction  action="#{commLanding.run()}"/>
            <!--<f:viewAction action="#{salesReports.reSetDateMonth()}" />-->
        </f:metadata>
    </ui:define>
    <ui:define name="body">
        <f:event listener="#{commLanding.isPageAccessible}" type="preRenderView"/>

        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

        <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                        left: 600px;
                        top: 300px;
                        width: 150px;
                        height: 150px;
                        z-index: 9999; opacity: .5 !important; " />

        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.7"  id="comOverviewrpt">

            <h:form   id="commlineoverview" >
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >


                        <div class="compNamebg" style="float:left" >
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            #{custom.labels.get('IDS_COMMISSION')} Line Overview 
                            <br/>
                            <p:outputLabel value="As of : #{reportFilters.repYear} " id="asofyr" />    
                        </div>

                    </div>
                    <div class="ui-sm-12 ui-md-7 ui-lg-7" >


                        <p:panelGrid   columns="2"  layout="grid" style="width: 600px;float: right;"  id="graph"> 
                            <!--<p:autoUpdate >-->
                            <p:chart responsive="true"   type="bar" model="#{reports.barChartYearly}"   style="width:285px;height:180px"  widgetVar="yearly" />
                            <!--</p:autoUpdate>-->
                            <p:chart type="bar" model="#{reports.barChartQuarterly}"   style="width:285px;height:180px"  widgetVar="qtrly" />

                        </p:panelGrid>
                    </div>  


                    <div class="ui-sm-12 ui-md-2 ui-lg-2" style="margin-left:100px">
                        <p:panelGrid id="grid2" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;width:100%;"       layout="grid" styleClass="box-primary no-border ui-fluid "  >
                            <p:outputLabel  value="Year"   />
                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" >
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />
                                <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

                                <p:ajax listener="#{commLanding.run()}"   onstart="processGif();"   update=":commlineoverview:commlineDtl  :commlineoverview:graph :commlineoverview:asofyr :commlineoverview:lineOverviewExport  :commlineoverview:lineOverviewpdf" oncomplete="hideGif();" />
                            </p:selectOneMenu>   

                            <p:outputLabel  value="Month"   />
                            <p:selectOneMenu height="280"   widgetVar="mt" value="#{reportFilters.repMonth}"  >
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 

                                <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

                                <p:ajax listener="#{commLanding.run()}"  onstart="processGif();"  update=":commlineoverview:commlineDtl :commlineoverview:graph :commlineoverview:asofyr :commlineoverview:lineOverviewExport  :commlineoverview:lineOverviewpdf" oncomplete="hideGif();"/>
                            </p:selectOneMenu>
                            <!--#3343: Task Reporting > Line Overview - Export   Start-->
                            <p:column>
                                <p:commandLink id="xls" ajax="false"  onclick="PrimeFaces.monitorDownload(start, stop);">  
                                    <p:graphicImage name="/images/excel.png" width="24"/>
                                  <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23--> 
                                    <pe:exporter type="xlsx" target="lineOverviewExport"  fileName="#{custom.labels.get('IDS_COMMISSION')}LineOverview" subTable="false"   />  

                                </p:commandLink>  

                                <p:commandLink id="pdf" ajax="false"  onclick="PrimeFaces.monitorDownload(start, stop);"    >  
                                    <p:graphicImage value="/resources/images/pdf.png"  width="24" />  
                  <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->           
                                    <pe:exporter type="pdf" target="lineOverviewpdf" fileName="#{custom.labels.get('IDS_COMMISSION')}LineOverview" subTable="false"  preProcessor="#{monthlyExpandedRep.preProcessPDF}"  />  

                                </p:commandLink>
                            </p:column>

                            <!--#3343: Task Reporting > Line Overview - Export  End-->

                        </p:panelGrid>


                    </div>
                </div>     
                <p:dataTable  id="commlineDtl"  value="#{commLanding.listLanding}" var="land" emptyMessage="No data found"  >

                    <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left" sortBy="#{land.princiName}" style="text-align: left;height:28px;width:25%" >
                        #{land.princiName}
                        <f:facet name="footer">
                            <h:outputText value="Total" />
                        </f:facet>

                    </p:column>

                    <p:column headerText="#{commReports.quarter}-#{reportFilters.repYear}" 
                              class="col_right"
                              sortBy="#{land.curQtrAmt}">

                        <h:outputLabel value="#{land.curQtrAmt}"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>

                        <f:facet name="footer">
                            <h:outputLabel value="#{commLanding.totals[1]}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>


                    </p:column>

                    <p:column headerText="#{commReports.quarter}-#{reportFilters.repYear-1}" 
                              class="col_right"
                              sortBy="#{land.prevQtrAmt}">
                        <h:outputLabel value="#{land.prevQtrAmt}"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>

                        <f:facet name="footer">
                            <h:outputLabel value="#{commLanding.totals[2]}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>

                    </p:column>

                    <p:column headerText="#{reportFilters.repYear}" class="col_right" sortBy="#{land.curYrAmt}" >

                        <h:link title="View #{land.princiName}'s Expanded Report "
                                outcome="rpt_CommExpanded.xhtml?yr=#{commReports.repYear}&amp;p=#{land.princiId}" style="text-decoration:underline" >
                            <h:outputLabel value="#{land.curYrAmt}" style="cursor: pointer;text-decoration: underline" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </h:link>

                        <f:facet name="footer">
                            <h:outputLabel value="#{commLanding.totals[3]}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>

                    </p:column>

                    <p:column headerText="#{reportFilters.repYear-1}" class="col_right" sortBy="#{land.prevYrAmt}" >

                        <h:outputLabel value="#{land.prevYrAmt}"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>

                        <f:facet name="footer">
                            <h:outputLabel value="#{commLanding.totals[4]}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>

                    </p:column>

                    <p:column headerText="Year Pct" class="col_right" sortBy="#{land.yrPct}" >

                        <h:outputLabel value="#{land.yrPct}" 

                                       style="#{
                                       land.yrPct gt 0 ?  'color:green' 
                                           : (land.yrPct lt 0 ? 'color:red' : 'color:black')
                               }"
                                       >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>

                        <h:outputLabel value="%" rendered="#{land.yrPct ne null}" 
                                       style="#{
                                       land.yrPct gt 0 ?  'color:green' 
                                           : (land.yrPct lt 0 ? 'color:red' : 'color:black')
                               }" />


                    </p:column>

                    <p:column headerText="Invoice Date" class="col_center"  sortBy="#{land.invDate}" >
                        #{reports.changeDateFormat(land.invDate, 1)}
                    </p:column>

                    <p:column headerText="Last Check" class="col_center" >
                        #{reports.changeDateFormat(land.checkDate, 1)}
                        <!--#{land.checkDate}-->
                    </p:column>
        <!--            <p:column headerText="Total #{commReports.repYear-1}" class="col_center" >
                    </p:column>-->

                </p:dataTable>
                <!--#3343: Task Reporting > Line Overview - Export   Start-->


                <p:dataTable   id="lineOverviewExport"  value="#{commLanding.listLanding}" var="land"  rendered="false">


                    <p:columnGroup  type="header" class="header"   >
                        <p:row>

                            <p:column  colspan="2"  rowspan="2"  headerText="#{loginBean.subscriber_name}" />

                            <p:column />
                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            <p:column colspan="2"  rowspan="2"   headerText="#{custom.labels.get('IDS_COMMISSION')} Line Overview" />

                            <p:column />
  <!--//    #3408:    Bug Comm. Expanded View: 2019 displayed twice instead of 2020-->

                            <p:column  rowspan="2" headerText="As of :  #{reportFilters.repYear}" />

                            <p:column />
                            <p:column /> <p:column /> <p:column /> <p:column /> <p:column /> <p:column /> <p:column />



                        </p:row>

                        <p:row></p:row>
                        <p:row></p:row>
                        <p:row></p:row>

                        <p:row>
                            <p:column  headerText="#{custom.labels.get('IDS_PRINCI')}" />
                            <p:column  headerText="#{commReports.quarter}-#{reportFilters.repYear}"  />
                            <p:column  headerText="#{commReports.quarter}-#{reportFilters.repYear-1}" />
                            <p:column headerText="#{reportFilters.repYear}" />
                            <p:column  headerText="#{reportFilters.repYear-1}"/>
                            <p:column headerText="Year Pct" />
                            <p:column headerText="Invoice Date"/>

                            <p:column headerText="Last Check"/>




                        </p:row>
                    </p:columnGroup>

                    <p:column  class="col_left" sortBy="#{land.princiName}" style="text-align: left;height:28px;width:25%" >
                        <h:outputText  value="#{land.princiName}" />
                        <f:facet name="footer">
                            <h:outputText value="Total" />
                        </f:facet>

                    </p:column>

                    <p:column  
                        class="col_right"
                        sortBy="#{land.curQtrAmt}">

                        <h:outputText value="#{land.curQtrAmt}"   style="float: right">
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>

                        <f:facet name="footer">
                            <h:outputText value="#{commLanding.totals[1]}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>


                    </p:column>

                    <p:column 
                        class="col_right"
                        sortBy="#{land.prevQtrAmt}">
                        <h:outputText value="#{land.prevQtrAmt}"   style="float: right">
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>

                        <f:facet name="footer">
                            <h:outputText value="#{commLanding.totals[2]}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>

                    </p:column>

                    <p:column  class="col_right" sortBy="#{land.curYrAmt}" >

                        <h:outputText value="#{land.curYrAmt}"  style="float: right" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                        <f:facet name="footer">
                            <h:outputText value="#{commLanding.totals[3]}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>

                    </p:column>

                    <p:column  class="col_right" sortBy="#{land.prevYrAmt}" >

                        <h:outputText value="#{land.prevYrAmt}"  style="float: right" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>

                        <f:facet name="footer">
                            <h:outputText value="#{commLanding.totals[4]}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>

                    </p:column>

                    <p:column  class="col_right" sortBy="#{land.yrPct}" >

                        <h:outputText value="#{land.yrPct}" 
                                      style="float: right"
                                      >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>

                        <h:outputText value="%" rendered="#{land.yrPct ne null}" 
                                      style="#{
                                      land.yrPct gt 0 ?  'color:green' 
                                          : (land.yrPct lt 0 ? 'color:red' : 'color:black')
                               }" />


                    </p:column>

                    <p:column  class="col_center"  sortBy="#{land.invDate}" >
                        <h:outputText value=" #{reports.changeDateFormat(land.invDate, 1)}" />
                    </p:column>

                    <p:column  class="col_center"   >
                        <h:outputText  value=" #{reports.changeDateFormat(land.checkDate, 1)}"  />

                    </p:column>



                </p:dataTable>
                <p:dataTable   id="lineOverviewpdf"  value="#{commLanding.listLanding}" var="land"  rendered="false">


                    <p:columnGroup  type="header" class="header"   >
                        <p:row>

                            <p:column    rowspan="2"  headerText="#{loginBean.subscriber_name}" />

                            <p:column />
                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            <p:column   rowspan="2"   headerText="#{custom.labels.get('IDS_COMMISSION')} Line Overview" />

                            <p:column />
  <!--//    #3408:    Bug Comm. Expanded View: 2019 displayed twice instead of 2020-->

                            <p:column  rowspan="2" headerText="As of :  #{reportFilters.repYear}" />

                            <p:column />
                            <p:column /> <p:column /> <p:column /> <p:column /> <p:column /> <p:column /> <p:column />



                        </p:row>

                        <p:row></p:row>
                        <p:row></p:row>
                        <p:row></p:row>

                        <p:row>
                            <p:column  headerText="#{custom.labels.get('IDS_PRINCI')}" />
                            <p:column  headerText="#{commReports.quarter}-#{reportFilters.repYear}"  />
                            <p:column  headerText="#{commReports.quarter}-#{reportFilters.repYear-1}" />
                            <p:column headerText="#{reportFilters.repYear}" />
                            <p:column  headerText="#{reportFilters.repYear-1}"/>
                            <p:column headerText="Year Pct" />
                            <p:column headerText="Invoice Date"/>

                            <p:column headerText="Last Check"/>




                        </p:row>
                    </p:columnGroup>

                    <p:column  class="col_left" sortBy="#{land.princiName}" style="text-align: left;height:28px;width:25%" >
                        <h:outputText  value="#{land.princiName}" />
                        <f:facet name="footer">
                            <h:outputText value="Total" />
                        </f:facet>

                    </p:column>

                    <p:column  
                        class="col_right"
                        sortBy="#{land.curQtrAmt}">

                        <h:outputText value="#{land.curQtrAmt}"   style="float: right">
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>

                        <f:facet name="footer">
                            <h:outputText value="#{commLanding.totals[1]}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>


                    </p:column>

                    <p:column 
                        class="col_right"
                        sortBy="#{land.prevQtrAmt}">
                        <h:outputText value="#{land.prevQtrAmt}"   style="float: right">
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>

                        <f:facet name="footer">
                            <h:outputText value="#{commLanding.totals[2]}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>

                    </p:column>

                    <p:column  class="col_right" sortBy="#{land.curYrAmt}" >

                        <h:outputText value="#{land.curYrAmt}"  style="float: right" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                        <f:facet name="footer">
                            <h:outputText value="#{commLanding.totals[3]}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>

                    </p:column>

                    <p:column  class="col_right" sortBy="#{land.prevYrAmt}" >

                        <h:outputText value="#{land.prevYrAmt}"  style="float: right" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>

                        <f:facet name="footer">
                            <h:outputText value="#{commLanding.totals[4]}"  style="float: right" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>

                    </p:column>

                    <p:column  class="col_right" sortBy="#{land.yrPct}" >

                        <h:outputText value="#{land.yrPct}" 
                                      style="float: right"
                                      >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>

                        <h:outputText value="%" rendered="#{land.yrPct ne null}" 
                                      style="#{
                                      land.yrPct gt 0 ?  'color:green' 
                                          : (land.yrPct lt 0 ? 'color:red' : 'color:black')
                               }" />


                    </p:column>

                    <p:column  class="col_center"  sortBy="#{land.invDate}" >
                        <h:outputText value=" #{reports.changeDateFormat(land.invDate, 1)}" />
                    </p:column>

                    <p:column  class="col_center"   >
                        <h:outputText  value=" #{reports.changeDateFormat(land.checkDate, 1)}"  />
                        <!--#{land.checkDate}-->
                    </p:column>
        <!--            <p:column headerText="Total #{commReports.repYear-1}" class="col_center" >
                    </p:column>-->





                </p:dataTable>


            </h:form>

            <!--#3343: Task Reporting > Line Overview - Export   Start-->
            <p:dialog widgetVar="inCommLineOverviewProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Please wait...Exporting records" />
                        <br /><br />
                        <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                    </p:outputPanel>
                </h:form>
            </p:dialog>
            <!--#3343: Task Reporting > Line Overview - Export   End-->


        </div>

        <style>
             /*14453 ESCALATIONS CRM-8464   Reporting Graphs Legend not legible - Multiple Instances*/
            .jqplot-axis {
                 font-size: 0.25em !important;
            }
            
            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 3px !important;
                padding-bottom: 3px !important;
            }

            [role="gridcell"]{

                padding: 5px 10px !important;
            }
            .content-header{     display: none; }


            .jqplot-target {

                font-size: 0.75em!important;
            }

            /*2021- CRM-1177 Doran associates: Remove link from printed reports - sharvani - 09-12-2019*/
            @media print {
                a[href]:after {
                    content: none !important;
                }
            }
        </style>


        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

        <script>
            window.onload = function () {
//                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("comOverviewrpt").style.pointerEvents = "all";
                    document.getElementById("comOverviewrpt").style.opacity = "1";

                    $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };


            function processGif()
            {

                $('#loading-image').show();
                document.getElementById("comOverviewrpt").style.pointerEvents = "none";
                document.getElementById("comOverviewrpt").style.opacity = "0.7";
            }

            function hideGif()
            {

                $('#loading-image').hide();

                document.getElementById("comOverviewrpt").style.pointerEvents = "all";
                document.getElementById("comOverviewrpt").style.opacity = "1";
            }

            function start() {

                PF('inCommLineOverviewProgressDlg').show();
            }

            function stop() {
                PF('inCommLineOverviewProgressDlg').hide();
            }



        </script>


        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->


    </ui:define>

</ui:composition>