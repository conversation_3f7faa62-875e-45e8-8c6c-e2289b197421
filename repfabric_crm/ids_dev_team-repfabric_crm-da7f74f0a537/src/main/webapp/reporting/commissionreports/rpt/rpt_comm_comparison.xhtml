<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: ytdcomparison.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
      <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
        <title>   Sales/#{custom.labels.get('IDS_COMM')} Comparison  </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>

            <f:viewParam name="yr" value="#{reportFilters.repYear}" />
            <f:viewParam name="mt" value="#{reportFilters.repMonth}" />
            <f:viewParam name="c" value="#{reportFilters.customer.compId}" />
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}" />
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}" />
            <f:viewParam name="g" value="#{salCommComparisonRep.grp}" />
            <f:viewParam name="flg" value="#{salCommComparisonRep.salesTeamFlag}" />
             <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
            <f:viewParam name="ri" value="#{salCommComparisonRep.compRegns}"/>
            <f:viewParam name="rn" value="#{salCommComparisonRep.compRegNames}"/>
           
            <!--//Feature #4738:CRM-4285: JOHN: Removing INACTIVE Manufacturer from reporting-->
            <f:viewParam name="ePrinci" value="#{salCommComparisonRep.excldInactivFlg}" />
            <f:viewAction action="#{salCommComparisonRep.init(salCommComparisonRep.grp)}"/>

        </f:metadata>
    </ui:define>
    <ui:define  name="body">
        <f:event listener="#{salCommComparisonRep.isPageAccessible}" type="preRenderView"/>  

        <p:dialog header="Please wait" draggable="false"  widgetVar="dlgSalComCompaj" modal="true" closable="false" resizable="false" >
            <h:graphicImage  library="images" name="ajax-loader.gif" />
            <p:spacer width="5"/>
            <h:outputLabel value="Loading data.."/>
        </p:dialog>

        <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->
        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4"  id="commCompRpt">

            <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                            left: 600px;
                            top: 300px;
                            width: 150px;
                            height: 150px;
                            z-index: 9999; opacity: .5 !important; " />   



            <h:form id="salescommcomp" >

                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update="salescommcomp:inputCustomer  salescommcomp:saleTeamLookUpbtn "/>

                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="salescommcomp:inputPrincName "  />
                <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="salescommcomp:inputAllSalesTeam " />





                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >


                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            #{custom.labels.get('IDS_COMMISSION')} to Sales Comparison 
                            <br/>

                        </div>
                        &nbsp;&nbsp; <p:outputLabel value="As of:" /> &nbsp;  <p:outputLabel value="#{reportFilters.repYear}" id="year"/>
                         <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                         <div  style="float:left;width:750px;margin-top: 20px" >
                             <p:outputLabel style="font-weight:normal" rendered="#{salCommComparisonRep.compRegNames.length() > 0}"  value="*Filtered for selected #{custom.labels.get('IDS_REGION')}" />  
                        </div>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                         <!--#8517  CRM-5877: Aurora: Reports: add Region + prior year total (Sales/Commission Comparison)-->
                        <p:panelGrid id="grid1" columns="1" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;width:170%;margin-left: 200px"       layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel value="Group by"/>

                            <p:selectOneRadio value="#{salCommComparisonRep.grp}"  widgetVar="grp17" layout="pageDirection"  required="true">
                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/> 
                                <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                <f:selectItem   itemValue="2" itemLabel="#{!globalParams.allowDistriCommEnabled ? custom.labels.get('IDS_DB_CUSTOMER'):'Customer'}"/> 
                                <p:ajax  onstart="processGif();"   listener="#{salCommComparisonRep.init(salCommComparisonRep.grp)}" update="salescommcomp:dtrpt1 :salescommcomp:grdtot" oncomplete="hide();" />
                            </p:selectOneRadio>
                        </p:panelGrid>
                    </div>  
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >
                        <p:panelGrid id="grid2" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     style="min-height: 50px;width:100%;margin-left: 100px"       layout="grid" styleClass="box-primary no-border ui-fluid "  >
                            <p:outputLabel  value="Year"   />
                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" >
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />
                                <p:ajax listener="#{salCommComparisonRep.init(salCommComparisonRep.grp)}"   onstart="processGif();"    update="salescommcomp:dtrpt1 :salescommcomp:grdtot  :salescommcomp:year"  oncomplete="hide();"/>
                            </p:selectOneMenu>   

                            <p:outputLabel  value="Month"   />
                            <p:selectOneMenu height="280"   widgetVar="mt" value="#{reportFilters.repMonth}"  >
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 
                                <p:ajax listener="#{salCommComparisonRep.init(salCommComparisonRep.grp)}"  onstart="processGif();"  update="salescommcomp:dtrpt1 :salescommcomp:grdtot" oncomplete="hide();"/>
                            </p:selectOneMenu>





                        </p:panelGrid>
                    </div>
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:100px">
                        <p:panelGrid id="grid" columns="2" 
                                     style="float: right;width: 350px;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

<!--                                  Bug #6044 sales/comm comparision report->sales team look up displaying blank data-->
                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--#3473:     sales team - Sales team data we can enter manually-->

                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  id="saleTeamLookUpbtn"
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  style="width:67px"
                                                  styleClass="btn-info btn-xs"  />
                            </h:panelGroup>

                            <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{!globalParams.allowDistriCommEnabled ? custom.labels.get('IDS_DB_CUSTOMER'):'Customer'}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:commandButton  value="View Report "  id="viewreport" onclick="processGif();"  styleClass="btn btn-primary btn-xs"  actionListener="#{salCommComparisonRep.init(salCommComparisonRep.grp)}"  style="width:30px"  update=":salescommcomp:dtrpt1 :salescommcomp:dtrpt1 :salescommcomp:grdtot"  oncomplete="hide();" />
<!--                            <p:commandButton  value="Export "  id="exportt"   styleClass="btn btn-primary btn-xs"  actionListener="#{salCommComparisonRep.exportData()}"  style="width:30px"  update=":salescommcomp:dtrpt1 :salescommcomp:dtrpt1 :salescommcomp:grdtot"  onclick="PrimeFaces.monitorDownload(start3, stop3);
                                                 PF('pollStatExp').start3();" styleClass="btn btn-primary  btn-xs" />-->

                            <p:commandButton id="btnExport" value="Export" style="width:30px" action="#{salCommComparisonRep.exportData()}"
                                             onclick="PrimeFaces.monitorDownload(start3, stop3);
                                                     PF('pollStatExp').start3();" styleClass="btn btn-primary  btn-xs"/>


                        </p:panelGrid>


                    </div>
                </div>     

                <!-- //// bug #5912 Comm Reports > Sales/Comm Comparison: missing headers in Excel export-->
                <!--                <h:panelGroup id="tbldata1">-->

                <p:dataList   id="dtrpt1"  value="#{salCommComparisonRep.listSalComCompRep}" var="salComComp" >
                <!--<p:dataScroller  id="dtrpt1" value="#{salCommComparisonRep.listSalComCompRep}" var="salComComp" chunkSize="10">-->    
                    <p:column >
                        <div class="div-selected" style="height:25px;width: 100%;" >
                            <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                                #{salComComp.itemName}        
                            </label>               
                        </div>
                      
                <!--15177  ESCALATIONS : CRM-8843   Access Partners Reporting Errors-->
                        <p:dataTable   scrollable="#{salComComp.details.size()>15 ? 'true' :'false'}"   scrollHeight="#{salComComp.details.size()>15 ? '45vh' : '200'}"    value="#{salComComp.details}" var="salComComp1" emptyMessage="No data found" >

                            <p:columnGroup type="header" id="tableHeader">
                                <p:row>
<!--                                    #6774: Sales/Comm Comparison report: Column alignment Issue - poornima - removed width-->
                                    <p:column rowspan="2"   
                                              headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_center" sortBy="#{salComComp1.princiName}" rendered="#{salCommComparisonRep.grp eq 2}" > 

                                        <f:facet name="footer"   >
                                            <h:outputLabel value="Total :" style="width: 180px"  rendered="#{salCommComparisonRep.grp eq 2}" class="footerFont">

                                            </h:outputLabel>
                                        </f:facet>

                                    </p:column>
                                    <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                    <!--  #6526: Ref: 5912 : Comm Reports > Sales/Comm Comparison:Column Alignment Issues - poornima //added width to 180px -->
                                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - removed width-->
                                    <p:column rowspan="2"  
                                              headerText="#{!globalParams.allowDistriCommEnabled ? custom.labels.get('IDS_DB_CUSTOMER'):'Customer'}#{salComComp1.custname}" class="col_center" sortBy="#{salComComp1.custname}"  rendered="#{salCommComparisonRep.grp eq 1}" /> 
                                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - removed width-->
                                    <p:column colspan="3" 
                                              headerText="Sales" class="col_center"/> 
                                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - removed width-->
                                  <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                    <p:column  colspan="3"   
                                               headerText="#{custom.labels.get('IDS_COMMISSION')}" class="col_center"/> 
                                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - removed width-->
                               <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                    <p:column rowspan="2"
                                              headerText="Avg. #{custom.labels.get('IDS_COMM')} Rate"  />

                                </p:row>

                                <p:row>
                                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - removed width-->
                                    <p:column headerText="#{salCommComparisonRep.monthName} #{reportFilters.repYear}"  />
                                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - removed width-->
                                    <p:column headerText="Jan-#{salCommComparisonRep.monthName} #{reportFilters.repYear}"  />
                                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - removed width-->
                                    <p:column headerText="Jan-#{reports.repMonth eq 1? '' 
                                                                : (reports.repMonth ne 1 ? salCommComparisonRep.monthName :salCommComparisonRep.monthName)}  #{reportFilters.repYear -1}" />
                                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - removed width-->
                                    <p:column headerText="#{salCommComparisonRep.monthName} #{reportFilters.repYear}" style="text-align:right" />
                                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - removed width-->
                                    <p:column headerText="Jan-#{salCommComparisonRep.monthName} #{reportFilters.repYear}" />
                                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - removed width-->
                                    <p:column headerText="Jan-#{reports.repMonth eq 1 ? '' 
                                                                : (reports.repMonth ne 1 ? salCommComparisonRep.monthName : salCommComparisonRep.monthName)}  #{reportFilters.repYear -1}"  />


                                </p:row>
                            </p:columnGroup> 

                            <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - removed width-->
                            <p:column rendered="#{salCommComparisonRep.grp eq 2}" >
                                <h:outputLabel value="#{salComComp1.princiName}"  />    

                                <br/>

                                <f:facet name="footer" >
                                    <h:outputLabel value="Total :"  style="float: right;" class="footerFont" />

                                </f:facet>

                            </p:column>

                            <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - removed width-->
                            <p:column rendered="#{salCommComparisonRep.grp eq 1}"  >

                                <h:outputLabel value="#{salComComp1.custname}"  />    

                                <br/>

                                <f:facet name="footer" >                                    
                                    <h:outputLabel value="Total :"  style="float: right;" class="footerFont"/>
                                </f:facet>

                            </p:column>
                            <p:column style="text-align: right" >

                                <h:outputLabel value="#{salComComp1.salMonth}"  >   
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <f:facet name="footer" >
                                    <h:outputLabel value="#{salComComp.totals[1]}"  style="float: right;" class="footerFont">
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>
                                </f:facet>
                            </p:column>
                            <p:column style="text-align: right" >

                                <h:outputLabel value="#{salComComp1.salYtdCurr}" >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <f:facet name="footer" >
                                    <h:outputLabel value="#{salComComp.totals[2]}"  style="float: right" class="footerFont" >
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>

                                </f:facet>
                            </p:column>

                            <p:column style="text-align: right" >

                                <h:outputLabel value="#{salComComp1.salYtdprev}" >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <f:facet name="footer" >
                                    <h:outputLabel value="#{salComComp.totals[3]}"  style="float: right" class="footerFont">
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>

                                </f:facet>
                            </p:column>
                            <p:column style="text-align: right" >

                                <h:outputLabel value="#{salComComp1.commMonth}" >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>
                                <f:facet name="footer" >
                                    <h:outputLabel value="#{salComComp.totals[4]}"  style="float: right" class="footerFont" >
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>

                                </f:facet>
                            </p:column>
                            <p:column style="text-align: right" >

                                <h:outputLabel value="#{salComComp1.commYtdCurr}" >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>

                                <f:facet name="footer" >
                                    <h:outputLabel value="#{salComComp.totals[5]}"  style="float: right" class="footerFont" >
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>

                                </f:facet>

                            </p:column>
                            <p:column style="text-align: right" >

                                <h:outputLabel value="#{salComComp1.commYtdPrev}" >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>

                                <f:facet name="footer" >
                                    <h:outputLabel value="#{salComComp.totals[6]}"  style="float: right" class="footerFont">
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>

                                </f:facet>
                            </p:column>

                            <p:column style="text-align: right" >
                                <h:outputLabel value="#{salComComp1.commAvg}#{salComComp1.commAvg eq null ? '' 
                                                        : (salComComp1.commAvg ne null ? '%' : '%')}"  >
                                    <f:convertNumber maxFractionDigits="2"/>
                                </h:outputLabel>

                                <f:facet name="footer" >
                                    <h:outputLabel   style="float: right" class="footerFont">
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </h:outputLabel>

                                </f:facet>
                                <!--                                <f:facet name="footer" >
                                                                    <h:outputLabel value="#{salComComp.totals[7]}%"  style="float: right" >
                                                                          <f:convertNumber maxFractionDigits="2"/>
                                                                    </h:outputLabel>
                                
                                                                </f:facet>-->

                            </p:column>



                        </p:dataTable>
                        <!--</ui:repeat>-->




                    </p:column>
                </p:dataList>
                <!--</p:dataScroller>-->
               


                <!-- // bug #5912 Comm Reports > Sales/Comm Comparison: missing headers in Excel export-->
                <p:dataTable  emptyMessage=" " id="grdtot">


                    <p:column  rendered="#{commReports.repGrp eq 2}" style="width: 450px" >

                        <f:facet name="footer" >
                            <h:outputLabel value="Grand Total:" style="float: right" rendered="#{ commReports.repGrp eq 2}" class="footerFont" >

                            </h:outputLabel>
                        </f:facet>  


                    </p:column>

                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - added width-->
                    <p:column class="col_left"  rendered="#{commReports.repGrp eq 1}" style="width: 138px"   >
                  <!--<p:column  rendered="#{commReports.repGrp eq 1}" style="width: 450px" >-->
                        <f:facet name="footer" >
                            <h:outputLabel value="Grand Total :" style="float: right" rendered="#{ commReports.repGrp eq 1}" class="footerFont">

                            </h:outputLabel>
                        </f:facet>

                    </p:column>
                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - added width-->
                    <p:column class="col_right"  style="width: 135px" >

                        <f:facet name="footer" >
                            <h:outputLabel value="#{salCommComparisonRep.totSalmonth}"  style="float: right" class="footerFont" >
                                <f:convertNumber maxFractionDigits="2"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - added width-->
                    <p:column class="col_right"  style="width: 135px" >

                        <f:facet name="footer" >
                            <h:outputLabel value="#{salCommComparisonRep.totSalYTDCurr}"  style="float: right"  class="footerFont">
                                <f:convertNumber maxFractionDigits="2"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - added width-->
                    <p:column class="col_right"  style="width: 135px" >
                        <f:facet name="footer"  >
                            <h:outputLabel value="#{salCommComparisonRep.totSalYTDPrev}"  style="float: right" class="footerFont">
                                <f:convertNumber maxFractionDigits="2"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - added width-->
                    <p:column class="col_right"  style="width: 135px" >

                        <f:facet name="footer" >
                            <h:outputLabel value="#{salCommComparisonRep.totComMonth}"  style="float: right" class="footerFont">
                                <f:convertNumber maxFractionDigits="2"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - added width-->
                    <p:column class="col_right"  style="width: 135px" >

                        <f:facet name="footer"   >
                            <h:outputLabel value="#{salCommComparisonRep.totComYTDCurr}"  style="float: right" class="footerFont">
                                <f:convertNumber maxFractionDigits="2"/>
                            </h:outputLabel>
                        </f:facet>

                    </p:column>
                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - added width-->
                    <p:column class="col_right" style="width: 135px"  >

                        <f:facet name="footer" >
                            <h:outputLabel value="#{salCommComparisonRep.totComYTDPrev}"  style="float: right" class="footerFont">
                                <f:convertNumber maxFractionDigits="2"/>
                            </h:outputLabel>
                        </f:facet>

                    </p:column>
                    <!--#6774: Sales/Comm Comparison report: Column alignment Issue - poornima - added width-->
                    <p:column class="col_right" style="width: 135px"  >

                        <!--                                    <f:facet name="footer" >
                                                                <h:outputLabel value="#{salCommComparisonRep.totCommAvg}#{salCommComparisonRep.totCommAvg eq null ? '' 
                                                : (salCommComparisonRep.totCommAvg ne null ? '%' : '%')}"  style="float: right" >
                                               <f:convertNumber maxFractionDigits="2"/>
                                         </h:outputLabel>
                                     </f:facet>-->


                    </p:column>



                </p:dataTable> 



                <!--                </h:panelGroup>    -->
                <style>

                    .ui-datatable-scrollable-header *,
                    .ui-datatable-scrollable-theadclone * {
                        -moz-box-sizing: content-box;
                        -webkit-box-sizing: content-box;
                        box-sizing: content-box;
                    }

                    body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
                        width: 15px;
                    }

                    .ui-panelgrid .ui-panelgrid-cell {
                        padding-top: 1px !important;
                        padding-bottom: 1px !important;
                    }

                    [role="gridcell"]{

                        padding: 0px 10px !important;
                    }
                    .content-header{     display: none; }


                    .jqplot-target {

                        font-size: 0.75em!important;
                    }

                    /*            .ui-datatable-data .ui-widget-content
                                {
                                    display: none;
                                }*/

                    body .ui-datatable th.ui-state-default[role="columnheader"], body .ui-treetable th.ui-state-default[role="columnheader"] {
                        padding-top: 0px!important;

                        padding-bottom: 0px!important;
                        /*    #d2dadc*/
                    }
                   
  /*<!--15177  ESCALATIONS : CRM-8843   Access Partners Reporting Errors-->*/
                     @media only screen and (min-height : 600px) and (max-height : 900px)  {

                .ui-datatable-scrollable-body{
                    height: 45vh; 

                }



            }       

            @media only screen and (min-height : 901px) and (max-height : 1152px)  {

                .ui-datatable-scrollable-body{
                    height: 52vh; 

                }


            }
            /*1920 * 1080*/
            @media only screen and (min-height : 1153px) and (max-height : 1440px)  {

                .ui-datatable-scrollable-body{
                    /*task :4250*/
                     /*12325: 17/10/2023: CRM-7444   Commisson Data overview: not using full screen*/
                    height: 66vh; 

                }


            }

            @media only screen and (min-height : 1500px) and (max-height : 1640px)  {

                .ui-datatable-scrollable-body{
                    /*task :4250*/
                     /*12325: 17/10/2023: CRM-7444   Commisson Data overview: not using full screen*/
                    height:66vh; 

                }


            } 
             /*12325: 17/10/2023: CRM-7444   Commisson Data overview: not using full screen*/
              @media only screen and (min-height : 1641px) and (max-height : 2560px)  {
                .ui-datatable-scrollable-body{
                    /*task :4250*/
                    height:80vh; 
                }
            } 



                </style>

                <!--#3249: Task Reporting > Commission Reports > Add loading gif START -->
                <script>

                    window.onload = function () {
                        //                $('#loading-image').show();
                        setTimeout(function () {
                            var t = performance.timing;
                            console.log(t.loadEventEnd - t.responseEnd);
                            //      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                            document.getElementById("commCompRpt").style.pointerEvents = "all";
                            document.getElementById("commCompRpt").style.opacity = "1";

                            $('#loading-image').hide();
                            //            $('#prodrpt').css({pointer - events:"all"});
                        }, 0);
                    };


                    function hide()
                    {

                        $('#loading-image').hide();
                        document.getElementById("commCompRpt").style.pointerEvents = "all";
                        document.getElementById("commCompRpt").style.opacity = "1";
                    }

                    function processGif()
                    {
                        $('#loading-image').show();
                        document.getElementById("commCompRpt").style.pointerEvents = "none";
                        document.getElementById("commCompRpt").style.opacity = "0.5";
                    }
                </script>

                <!--#3249:   Task Reporting > Commission Reports > Add loading gif  END   -->



            </h:form>

            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
            <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />

        </div>
    </ui:define>

</ui:composition>