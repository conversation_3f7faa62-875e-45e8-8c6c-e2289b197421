<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: productdetails All.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">
    <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
        <title>  Sales/#{custom.labels.get('IDS_COMM')} by Month </title>
    </ui:define>


    <ui:define name="meta">
        <f:metadata> 

            <f:viewParam name="yr" value="#{reports.repYear}" />
            <f:viewParam name="mt" value="#{reports.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="g" value="#{monthlyExpandedRep.repgrp}"/>

            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>
            <f:viewParam name="c" value="#{reportFilters.customer.compId}"/>
            <f:viewParam name="r" value="#{monthlyExpandedRep.subGrpFlag}" />
          <!--<f:viewAction action="#{monthlyExpandedRep.run()}"/>-->
            <f:viewAction action="#{monthlyExpandedRep.runProc()}"/>
        </f:metadata>
    </ui:define>


    <ui:define name="body">



        <h:form id="salcommCust">
            <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyCustomer(viewCompLookupService.selectedCompany)}" update=":salcommCust:inputCustomer  :salcommCust:saleTeamLookUpbtn  :salcommCust:inputAllSalesTeam"/>

            <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="salcommCust:inputPrincName "  />
            <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="salcommCust:inputAllSalesTeam " />


            <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4"  id="salComComprpt">

                <!--#3249:#3249:     Task Reporting > Commission Reports > Add loading gif-->

                <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                                left: 600px;
                                top: 300px;
                                width: 150px;
                                height: 150px;
                                z-index: 9999; opacity: .5 !important; " />  

                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >


                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                                 <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            Sales and #{custom.labels.get('IDS_COMMISSION')} by month
                            <br/>
                            As of : #{reports.repYear} 
                        </div>

                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="margin-left:60px" >


                    </div>  
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:-60px">
                        <p:panelGrid id="grid" columns="2" 
                                     style="float: right;width: 350px;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>



<!--                                <p:outputLabel for="inputSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                                <h:panelGroup class="ui-inputgroup"  >
                                    <p:inputText id="inputSalesTeam" value="#{example.salesTeam.smanName}"  />
                                    <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_SALES_TEAM')}" immediate="true" 
                                                      actionListener="#{salesTeamLookupService.list('applySalesTeam',0)}" 
                                                      update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                                      styleClass="btn-info btn-xs" />
                                </h:panelGroup>-->

                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--#3473:     sales team - Sales team data we can enter manually-->

                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  id="saleTeamLookUpbtn"
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  style="width:67px"
                                                  styleClass="btn-info btn-xs"  disabled="#{reportFilters.customer.compId >0}"/>
                            </h:panelGroup>

                            <!--Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <!--#3249: Task Reporting > Commission Reports > Add loading gif   -->   


                            <p:commandButton  value="View Report "  id="viewreport"   styleClass="btn btn-primary btn-xs"  actionListener="#{monthlyExpandedRep.runCust()}" style="width: 30px"   onclick="processGif()" />
                            <p:column>
                                  <!--//Bug #5330:Sales and Commission by Month: Include region breakup-->
                                <p:commandButton value="Export"   style="width:30px"  
                                                 ajax="false"  actionListener="#{monthlyExpandedRep.exportData()}"  
                                             styleClass="btn btn-primary btn-xs" onclick="PrimeFaces.monitorDownload(start, stop);" 
                                             />  
<!--                                <p:spacer width="4" />
                                <p:commandLink id="xls" ajax="false"  onclick="PrimeFaces.monitorDownload(start, stop);" >  
                                    <p:graphicImage name="/images/excel.png" width="24"/>
                                    <pe:exporter type="xlsx" target="dtsalcomcomprpt"  fileName="SalescommsByMonth" subTable="false"  preProcessor="#{monthlyExpandedRep.postProcessXLS}" />

                                </p:commandLink>  

                                <p:commandLink id="pdf" ajax="false"   onclick="PrimeFaces.monitorDownload(start, stop);"   >  
                                    <p:graphicImage value="/resources/images/pdf.png"  width="24" />  
                                    <pe:exporter type="pdf" target="dtsalcomcomppdf" fileName="SalescommsByMonth" subTable="false"  preProcessor="#{monthlyExpandedRep.preProcessPDF}" />  

                                </p:commandLink>-->

                            </p:column> 

                        </p:panelGrid>


                    </div>
                </div>
                <!--Feature #3052: CRM-3695: Rouzer: Lock headers on Sales/comms by month report-->

                <p:dataTable id="dtrpt"  value="#{monthlyExpandedRep.listExpand}" var="exp" emptyMessage="No data found"  sortBy="#{exp.regName}"

                             scrollable="#{monthlyExpandedRep.listExpand.size() ge 20 }"    styleClass="tab">



                    <p:columnGroup  type="header" class="header"   >


                        <p:row  class="header" id="myHeaderExp" >   

                            <!--Feature #5011 cleaning up End User terminology in Commission reports-->
 <p:column headerText="#{custom.labels.get('IDS_REGION')}"  rendered="#{monthlyExpandedRep.subGrpFlag}" >

                            </p:column>
                            <p:column headerText="#{monthlyExpandedRep.repgrp eq 1 ?  custom.labels.get('IDS_PRINCI') 
                                                    : monthlyExpandedRep.repgrp eq 2 ? custom.labels.get('IDS_DB_CUSTOMER') : custom.labels.get('IDS_DB_CUSTOMER')}"  width="150" class="col_left" >

                            </p:column>
                           
                            <p:column headerText="Sal/Com" >

                            </p:column>
                            <p:column headerText="Jan" class="col_right"  >

                            </p:column>

                            <p:column headerText="Feb" class="col_right" >

                            </p:column>

                            <p:column headerText="Mar" class="col_right"  >


                            </p:column>

                            <p:column headerText="Apr" class="col_right" >

                            </p:column>


                            <p:column headerText="May" class="col_right" >

                            </p:column>


                            <p:column headerText="Jun" class="col_right" >

                            </p:column>


                            <p:column headerText="Jul" class="col_right" >

                            </p:column>


                            <p:column headerText="Aug" class="col_right" >

                            </p:column>


                            <p:column headerText="Sep" class="col_right"  >

                            </p:column>



                            <p:column headerText="Oct" class="col_right" >

                            </p:column>


                            <p:column headerText="Nov" class="col_right" >

                            </p:column>


                            <p:column headerText="Dec" class="col_right" >

                            </p:column>


                            <p:column  headerText="Total" class="col_right" >

                            </p:column>

                            <p:column headerText="%"  width="80">

                            </p:column>
                        </p:row>


                    </p:columnGroup>     



                    <!--Feature #5011 cleaning up End User terminology in Commission reports-->
                    <p:column headerText="#{custom.labels.get('IDS_REGION')}"   rendered="#{monthlyExpandedRep.subGrpFlag}"     class="col_left" width="150"   >


                        <h:outputLabel value="#{exp.mexpDataType eq 0 ? exp.regName 
                                               : (exp.mexpDataType eq 1 ? '' : '')}"  /> 



                    </p:column>
                    <p:column headerText="#{custom.labels.get('IDS_DB_CUSTOMER')}"    class="col_left" width="150"   >


                        <h:outputLabel value="#{exp.mexpDataType eq 0 ? exp.mexpPrinciName 
                                                : (exp.mexpDataType eq 1 ? '' : '')}"  style="#{
                                                exp.mexpDataType eq 0 ?  'color:#084E8B
                                                ' 
                                                : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }"  /> 

                        <!--                        <f:facet  name="footer">
                                                    <h:outputLabel   value="Total : "  style="float: right"  class="footerFont"/>
                        
                                                </f:facet>-->

                    </p:column>
                   
     <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                    <p:column headerText="Sales/#{custom.labels.get('IDS_COMM')}"  class="col_left" >
                        <h:outputLabel value="#{exp.mexpDataType eq 0 ?  'Sales' 
                                                : (exp.mexpDataType eq 1 ? custom.labels.get('IDS_COMM') : custom.labels.get('IDS_COMM'))}"     
                                       style="#{
                                       exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                           : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }"/>

                        <f:facet  name="footer">
                            <h:outputLabel value="Sales" style="color:#084E8B "  class="footerFont"

                                           />

                            <br/>
                             <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            <h:outputLabel value="#{custom.labels.get('IDS_COMM')}" class="footerFont"
                                           />
                        </f:facet>

                    </p:column> 


                    <p:column headerText="Jan" style="text-align:right">
                        <h:outputLabel  value="#{exp.mexpMonth1}" style="float:right;#{
                                                 exp.mexpDataType eq 0 ? 'color:#084E8B;display:block;'
                                                 : (exp.mexpDataType eq 1 ? 'color:black;display:block;' : 'color:black;display:block;')
                               }" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>







                    </p:column>

                    <p:column headerText="Feb" style="text-align:right" >
                        <h:outputLabel value="#{exp.mexpMonth2}" style="#{
                                                exp.mexpDataType eq 0 ?  'color:#084E8B;display:block;' 
                                                : (exp.mexpDataType eq 1 ? 'color:black;display:block;' : 'color:black;display:block;')
                               }" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>



                    </p:column>

                    <p:column headerText="Mar" style="text-align:right">
                        <h:outputLabel value="#{exp.mexpMonth3}" style="#{
                                                exp.mexpDataType eq 0 ? 'color:#084E8B;display:block;' 
                                                : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>



                    </p:column>

                    <p:column headerText="Apr" style="text-align:right">
                        <h:outputLabel value="#{exp.mexpMonth4}" style="#{
                                                exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                                : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>



                    </p:column>


                    <p:column headerText="May" style="text-align:right">
                        <h:outputLabel value="#{exp.mexpMonth5}" style="#{
                                                exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                                : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>


                    </p:column>


                    <p:column headerText="Jun" style="text-align:right">
                        <h:outputLabel value="#{exp.mexpMonth6}" style="#{
                                                exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                                : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>



                    </p:column>


                    <p:column headerText="Jul" style="text-align:right">
                        <h:outputLabel value="#{exp.mexpMonth7}" style="#{
                                                exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                                : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>



                    </p:column>

                    <p:column headerText="Aug" style="text-align:right" >
                        <h:outputLabel value="#{exp.mexpMonth8}" style="#{
                                                exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                                : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>



                    </p:column>
                    <p:column headerText="Sep" style="text-align:right">
                        <h:outputLabel value="#{exp.mexpMonth9}" style="#{
                                                exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                                : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }"   >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>


                    </p:column>



                    <p:column headerText="Oct" style="text-align:right">
                        <h:outputLabel value="#{exp.mexpMonth10}" style="#{
                                                exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                                : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>


                    </p:column>


                    <p:column headerText="Nov" style="text-align:right">
                        <h:outputLabel value="#{exp.mexpMonth11}"  style="#{
                                                exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                                : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }"    >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>


                    </p:column>


                    <p:column headerText="Dec" style="text-align:right">
                        <h:outputLabel value="#{exp.mexpMonth12}"  style="#{
                                                exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                                : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }"    >
                            <f:convertNumber maxFractionDigits="0" />
                        </h:outputLabel>


                    </p:column>


                    <p:column  headerText="Total" style="text-align:right">
                        <h:outputLabel  value="#{exp.mexpTotal}" style="#{
                                                 exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                                 : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputLabel>

                    </p:column>

                    <p:column headerText="%" sortBy="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}"  style="text-align:right" >
                        <h:outputLabel  value="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}"  style="float:right ; #{
                                                 exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                                 : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }" />                    

                    </p:column>

<!--//5330 :Sales and Commission by Month: Include region breakup-->
                    <p:summaryRow  rendered="#{monthlyExpandedRep.subGrpFlag}" >
                        <p:column  rendered="#{monthlyExpandedRep.subGrpFlag}" >
                            
                        </p:column>
                        <p:column  style="text-align:right" width="200">
                            <h:outputText  value="Sub Total :"  style="float: left;font-weight: bold"  >

                            </h:outputText>
                           

                        </p:column>
                         <p:column  style="text-align:right">
                            <h:outputText  value="Sales"  style="float: left;font-weight: bold;color:#084E8B;"   >

                            </h:outputText>
                             <br/>
                              <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            <h:outputText  value="#{custom.labels.get('IDS_COMM')}"  style="float: left;font-weight: bold;"  >

                            </h:outputText>

                        </p:column>
                        
                          <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.regId)[0])}" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(exp.regId)[0])}" style="float: right;font-weight: bold"  />


                        </p:column>
                         <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.regId)[1])}" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(exp.regId)[1])}" style="float: right;font-weight: bold"  />


                        </p:column>
                        <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.regId)[2])}" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(exp.regId)[2])}" style="float: right;font-weight: bold"  />


                        </p:column>
                        <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.regId)[3])}" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(exp.regId)[3])}" style="float: right;font-weight: bold"  />


                        </p:column>
                        <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.regId)[4])}" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(exp.regId)[4])}" style="float: right;font-weight: bold"  />


                        </p:column>
                             <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.regId)[5])}" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(exp.regId)[5])}" style="float: right;font-weight: bold"  />


                        </p:column>
                       
                             <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.regId)[6])}" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(exp.regId)[6])}" style="float: right;font-weight: bold"  />


                        </p:column>
                        
                     
                             <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.mexpPrinciId)[7])}" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(exp.mexpPrinciId)[7])}" style="float: right;font-weight: bold"  />


                        </p:column>
                         <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.regId)[8])}" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(eexp.regId)[8])}" style="float: right;font-weight: bold"  />


                        </p:column>
                         <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.regId)[9])}" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(exp.regId)[9])}" style="float: right;font-weight: bold"  />


                        </p:column>
                        
                         <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.regId)[10])}" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(exp.regId)[10])}" style="float: right;font-weight: bold"  />


                        </p:column>
                         <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.regId)[11])}" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(exp.regId)[11])}" style="float: right;font-weight: bold"  />


                        </p:column>
                        
                        <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.regId)[12])}" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(exp.regId)[12])}" style="float: right;font-weight: bold"  />


                        </p:column>
                        
                        <p:column  style="text-align:right">
                            <h:outputText  value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotals.get(exp.regId)[13])}%" style="float: right;font-weight: bold;color:#084E8B" />
                            
                            <br/>
                            <h:outputText value="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.subGrpRegTotalc.get(exp.regId)[13])}%" style="float: right;font-weight: bold"  />


                        </p:column>

                    </p:summaryRow>


                    <p:columnGroup  type="footer" class="header"   >

                        <p:row>
                              <p:column     class="col_left" width="150" rendered="#{monthlyExpandedRep.subGrpFlag}"  >

                            </p:column>


                            <p:column footerText="Total:"   class="col_left" width="150" id="totalfooter">

                            </p:column>




                            <p:column footerText="Sales" class="col_left" sortBy="#{exp.mexpMonth2}"  style="color:#084E8B;width:5%;" >

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[0])}"   style="color:#084E8B;text-align: right; " >


                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[1])}"  style="color:#084E8B;text-align: right; "  >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[2])}"   style="color:#084E8B;text-align: right; "  >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[3])}"   style="color:#084E8B;text-align: right; "  >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[4])}"   style="color:#084E8B;text-align: right; " >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[5])}"   style="color:#084E8B;text-align: right; "  >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[6])}"   style="color:#084E8B;text-align: right; " >

                            </p:column>



                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[7])}"   style="color:#084E8B;text-align: right; "  >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[8])}"   style="color:#084E8B;text-align: right; " >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[9])}"   style="color:#084E8B;text-align: right; " >

                            </p:column>
                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[10])}"   style="color:#084E8B;text-align: right; " >

                            </p:column>
                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[11])}"   style="color:#084E8B;text-align: right; " >

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[12])}"   style="color:#084E8B;text-align: right; "  >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.totals[13]}#{monthlyExpandedRep.totals[13] ne null ? '%' : ''}"   style="color:#084E8B;text-align: right; " >

                            </p:column>





                        </p:row>
                        <p:row>
                            <p:column     class="col_left" width="150" rendered="#{monthlyExpandedRep.subGrpFlag}"  >

                            </p:column>
                            <p:column  class="col_right"  style="width:8%">

                            </p:column>
<!--                             Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            <p:column   footerText="#{custom.labels.get('IDS_COMM')}"  class="col_left"  sortBy="#{exp.mexpMonth1}"  >
                            </p:column>
                            <!--#390130:sales by month :column sorter provided  -->
                            <p:column   footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[0])}"  style="text-align: right;">

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[1])}" style="text-align: right;" >

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[2])}" style="text-align: right;"  >


                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[3])}" style="text-align: right;" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[4])}" style="text-align: right;" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[5])}" style="text-align: right;" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[6])}" style="text-align: right;" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[7])}" style="text-align: right;" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[8])}" style="text-align: right;"  >

                            </p:column>



                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[9])}" style="text-align: right;" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[10])}" style="text-align: right;" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[11])}" style="text-align: right;" >

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[12])}" style="text-align: right;" >

                            </p:column>
                            <!--#3771: Task Total :% not showing-->
                            <p:column footerText="#{monthlyExpandedRep.totalc[13]}#{monthlyExpandedRep.totalc[13] ne null ? '%' : ''}" style="text-align: right;" >

                            </p:column>





                        </p:row>



                    </p:columnGroup>









                </p:dataTable>



                <!--#3204:Task Related to CRM-2014: Commission Reports: Export option in Sales/Comm by month  START -->


                <p:dataTable id="dtsalcomcomprpt"  value="#{monthlyExpandedRep.listExpand}" var="exp"  
                             rendered="false"         >



                    <p:columnGroup  type="header" class="header"   >
                        <p:row>

                            <p:column  colspan="6" rowspan="2" headerText="#{loginBean.subscriber_name}" style="text-height: max-size;"  />

                            <p:column   colspan="6"  rowspan="2" headerText="Sales By Month Report" />


                            <p:column  colspan="3" rowspan="2" headerText="As of : #{reports.repYear} " />



                        </p:row>




                        <p:row>





                        </p:row>

                        <p:row></p:row> 

                        <p:row  class="header" id="myHeaderExp" >   


                            <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:column headerText="#{monthlyExpandedRep.repgrp eq 1 ?  custom.labels.get('IDS_PRINCI') 
                                                    : monthlyExpandedRep.repgrp eq 2 ? custom.labels.get('IDS_DB_CUSTOMER') : custom.labels.get('IDS_DB_CUSTOMER')}"  class="col_left" >

                            </p:column>
                            <p:column headerText="Sal/Com" >

                            </p:column>
                            <p:column headerText="Jan" class="col_right" sortBy="#{exp.mexpMonth1}" >

                            </p:column>

                            <p:column headerText="Feb" class="col_right" sortBy="#{exp.mexpMonth2}">

                            </p:column>

                            <p:column headerText="Mar" class="col_right" sortBy="#{exp.mexpMonth3}" >


                            </p:column>

                            <p:column headerText="Apr" class="col_right" sortBy="#{exp.mexpMonth4}">

                            </p:column>


                            <p:column headerText="May" class="col_right" sortBy="#{exp.mexpMonth5}">

                            </p:column>


                            <p:column headerText="Jun" class="col_right" sortBy="#{exp.mexpMonth6}">

                            </p:column>


                            <p:column headerText="Jul" class="col_right" sortBy="#{exp.mexpMonth7}">

                            </p:column>


                            <p:column headerText="Aug" class="col_right" sortBy="#{exp.mexpMonth8}">

                            </p:column>


                            <p:column headerText="Sep" class="col_right" sortBy="#{exp.mexpMonth9}" >

                            </p:column>



                            <p:column headerText="Oct" class="col_right" sortBy="#{exp.mexpMonth10}">

                            </p:column>


                            <p:column headerText="Nov" class="col_right" sortBy="#{exp.mexpMonth11}">

                            </p:column>


                            <p:column headerText="Dec" class="col_right" sortBy="#{exp.mexpMonth12}">

                            </p:column>


                            <p:column  headerText="Total" class="col_right" sortBy="#{exp.mexpTotal}">

                            </p:column>

                            <p:column headerText="%" sortBy="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}">

                            </p:column>
                        </p:row>


                    </p:columnGroup>





                    <!--//Bug 1164 -# 325040 : By Customer By month Sales Reports-->
                    <p:column  class="col_left" >



                        <h:outputText value="#{exp.mexpDataType eq 0 ? exp.mexpPrinciName 
                                               : (exp.mexpDataType eq 1 ? '' : '')}"    />   



                    </p:column>
                         <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                    <p:column headerText="Sales/#{custom.labels.get('IDS_COMM')}"   >
                        <h:outputLabel value="#{exp.mexpDataType eq 0 ?  'Sales' 
                                                : (exp.mexpDataType eq 1 ? custom.labels.get('IDS_COMM') : custom.labels.get('IDS_COMM'))}"     
                                       style="#{
                                       exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                           : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }"/>



                    </p:column> 

                    <p:column    >
                        <h:outputText  value="#{exp.mexpMonth1}"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>

                    <p:column   sortBy="#{exp.mexpMonth2}">
                        <h:outputText value="#{exp.mexpMonth2}"   >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>

                    <p:column  sortBy="#{exp.mexpMonth3}" >
                        <h:outputText value="#{exp.mexpMonth3}"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>

                    <p:column  sortBy="#{exp.mexpMonth4}">
                        <h:outputText value="#{exp.mexpMonth4}"   >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column   sortBy="#{exp.mexpMonth5}">
                        <h:outputText value="#{exp.mexpMonth5}" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column   sortBy="#{exp.mexpMonth6}">
                        <h:outputText value="#{exp.mexpMonth6}"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column   sortBy="#{exp.mexpMonth7}">
                        <h:outputText value="#{exp.mexpMonth7}"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column   sortBy="#{exp.mexpMonth8}">
                        <h:outputText value="#{exp.mexpMonth8}" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column   sortBy="#{exp.mexpMonth9}" >
                        <h:outputText value="#{exp.mexpMonth9}" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>



                    <p:column   sortBy="#{exp.mexpMonth10}">
                        <h:outputText value="#{exp.mexpMonth10}">
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column   sortBy="#{exp.mexpMonth11}">
                        <h:outputText value="#{exp.mexpMonth11}"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column   sortBy="#{exp.mexpMonth12}">
                        <h:outputText value="#{exp.mexpMonth12}" >
                            <f:convertNumber maxFractionDigits="0" />
                        </h:outputText>


                    </p:column>


                    <p:column    sortBy="#{exp.mexpTotal}">
                        <h:outputText  value="#{exp.mexpTotal}"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>

                    <p:column  sortBy="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}">
                        <h:outputText  value="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}"  style="float: right" />                    


                    </p:column>


                    <p:columnGroup  type="footer" class="header"   >

                        <p:row>

                            <p:column footerText="Total :"  style="text-align:left" >

                            </p:column>

                            <!--#390130:sales by month :column sorter provided  -->


                            <p:column footerText="Sales" >

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[0])}"  >


                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[1])}" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[2])}" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[3])}" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[4])}" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[5])}" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[6])}"  >

                            </p:column>



                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[7])}" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[8])}" >

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[9])}" >

                            </p:column>
                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[10])}" >

                            </p:column>
                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[11])}" >

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[12])}">

                            </p:column>
                            <!--#3771: Task Total :% not showing-->
                            <p:column footerText="#{monthlyExpandedRep.totals[13]}#{monthlyExpandedRep.totals[13] ne null ? '%' : ''}" >

                            </p:column>





                        </p:row>



                        <p:row>

                            <p:column   style="float:left">

                            </p:column>
                             <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            <p:column   footerText="#{custom.labels.get('IDS_COMM')}"   style="text-align:left;float: left" >
                            </p:column>
                            <!--#390130:sales by month :column sorter provided  -->
                            <p:column   footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[0])}" sortBy="#{exp.mexpMonth1}" >

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[1])}" style="float:right" sortBy="#{exp.mexpMonth2}">

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[2])}" style="float:right" sortBy="#{exp.mexpMonth3}" >


                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[3])}" class="col_right" sortBy="#{exp.mexpMonth4}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[4])}" class="col_right" sortBy="#{exp.mexpMonth5}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[5])}" class="col_right" sortBy="#{exp.mexpMonth6}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[6])}" class="col_right" sortBy="#{exp.mexpMonth7}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[7])}" class="col_right" sortBy="#{exp.mexpMonth8}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[8])}" class="col_right" sortBy="#{exp.mexpMonth9}" >

                            </p:column>



                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[9])}" class="col_right" sortBy="#{exp.mexpMonth10}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[10])}" class="col_right" sortBy="#{exp.mexpMonth11}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[11])}" class="col_right" sortBy="#{exp.mexpMonth12}">

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[12])}" class="col_right" sortBy="#{exp.mexpMonth12}">

                            </p:column>
                            <!--#3771: Task Total :% not showing-->
                            <p:column footerText="#{monthlyExpandedRep.totalc[13]}#{monthlyExpandedRep.totalc[13] ne null ? '%' : ''}" class="col_right" sortBy="#{exp.mexpMonth12}">

                            </p:column>





                        </p:row>


                    </p:columnGroup>


                </p:dataTable>


                <p:dataTable id="dtsalcomcomppdf"  value="#{monthlyExpandedRep.listExpand}" var="exp"  
                             rendered="false"               >
                    <!--
                                      <p:columnGroup  type="header" class="header"   >
                                                <p:row>
                    
                                                    <p:column   headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                                                    <p:column />
                                                    <p:column />
                                                    <p:column headerText="Sales By Month Report" />
                                                    <p:column/>
                                                    <p:column/>
                                                    <p:column/>
                    
                                                    <p:column headerText="As of : #{reports.repYear} " />
                    
                                                    <p:column />
                    
                                                    <p:column />
                                                    <p:column />
                                                    <p:column />
                                                    <p:column />
                                                    <p:column />
                                                    <p:column />
                    
                    
                                                </p:row>
                                      </p:columnGroup>
                                        
                                        <p:column>
                                            
                                           
                                            
                                        <f:facet name="header">
                                            
                                            <h:outputText  value="#{monthlyExpandedRep.repgrp eq 1 ?  custom.labels.get('IDS_PRINCI') 
                                            : (monthlyExpandedRep.repgrp eq 2 ? custom.labels.get('IDS_CUSTOMER') : custom.labels.get('IDS_CUSTOMER'))}">
                    
                </h:outputText> 
            </f:facet>
                
                   <h:outputText value="#{exp.mexpPrinciName}"   /> 
                
                   <f:facet name="footer" >
                         <h:outputText value="Total"  />
                   </f:facet>
            </p:column>-->


                    <p:columnGroup  type="header" class="header"   >
                        <p:row>

                            <p:column colspan="5"  headerText="#{loginBean.subscriber_name}" style="text-height: max-size"/>
                            <p:column />
                            <p:column />
                            <p:column  colspan="3"  headerText="Sales By Month Report" />
                            <p:column/>
                            <p:column/>


                            <p:column  colspan="3" headerText="As of : #{reports.repYear} " />




                            <p:column />
                        </p:row>




                        <p:row>





                        </p:row>
                        <p:row>





                        </p:row>
                        <p:row  class="header" id="myHeaderExp" >   




                            <!--//Bug 1164 -# 325040 : By Customer By month Sales Reports-->
                            <!--Feature #5011  Feature #5011 cleaning up End User terminology in Commission reports-->
                            <p:column headerText="#{monthlyExpandedRep.repgrp eq 1 ?  custom.labels.get('IDS_PRINCI') 
                                                    : monthlyExpandedRep.repgrp eq 2 ? custom.labels.get('IDS_DB_CUSTOMER') :  custom.labels.get('IDS_DB_CUSTOMER')}"  class="col_left" >

                            </p:column>
                            <p:column headerText="Sal/Com" >

                            </p:column>
                            <!--#390130:sales by month :column sorter provided  -->
                            <p:column headerText="Jan" class="col_right" sortBy="#{exp.mexpMonth1}" >

                            </p:column>

                            <p:column headerText="Feb" class="col_right" sortBy="#{exp.mexpMonth2}">

                            </p:column>

                            <p:column headerText="Mar" class="col_right" sortBy="#{exp.mexpMonth3}" >


                            </p:column>

                            <p:column headerText="Apr" class="col_right" sortBy="#{exp.mexpMonth4}">

                            </p:column>


                            <p:column headerText="May" class="col_right" sortBy="#{exp.mexpMonth5}">

                            </p:column>


                            <p:column headerText="Jun" class="col_right" sortBy="#{exp.mexpMonth6}">

                            </p:column>


                            <p:column headerText="Jul" class="col_right" sortBy="#{exp.mexpMonth7}">

                            </p:column>


                            <p:column headerText="Aug" class="col_right" sortBy="#{exp.mexpMonth8}">

                            </p:column>


                            <p:column headerText="Sep" class="col_right" sortBy="#{exp.mexpMonth9}" >

                            </p:column>



                            <p:column headerText="Oct" class="col_right" sortBy="#{exp.mexpMonth10}">

                            </p:column>


                            <p:column headerText="Nov" class="col_right" sortBy="#{exp.mexpMonth11}">

                            </p:column>


                            <p:column headerText="Dec" class="col_right" sortBy="#{exp.mexpMonth12}">

                            </p:column>


                            <p:column  headerText="Total" class="col_right" sortBy="#{exp.mexpTotal}">

                            </p:column>

                            <p:column headerText="%" sortBy="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}"  width="50" >

                            </p:column>
                        </p:row>


                    </p:columnGroup>





                    <!--//Bug 1164 -# 325040 : By Customer By month Sales Reports-->
                    <p:column  class="col_left" >



                        <h:outputText value="#{exp.mexpDataType eq 0 ? exp.mexpPrinciName 
                                               : (exp.mexpDataType eq 1 ? '' : '')}"  style="#{
                                               exp.mexpDataType eq 0 ?  'color:#084E8B
                                               ' 
                                               : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }"   />   



                    </p:column>
                    <p:column   class="col_left" >
                        <h:outputLabel value="#{exp.mexpDataType eq 0 ?  'Sales' 
                                                : (exp.mexpDataType eq 1 ? 'Comm' : 'Comm')}"     
                                       style="#{
                                       exp.mexpDataType eq 0 ?  'color:#084E8B' 
                                           : (exp.mexpDataType eq 1 ? 'color:black' : 'color:black')
                               }"/>



                    </p:column> 

                    <p:column  class="col_right" sortBy="#{exp.mexpMonth1}" >
                        <h:outputText  value="#{exp.mexpMonth1}" style="float:right" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>

                    <p:column  class="col_right" sortBy="#{exp.mexpMonth2}">
                        <h:outputText value="#{exp.mexpMonth2}" style="float: right"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>

                    <p:column class="col_right" sortBy="#{exp.mexpMonth3}" >
                        <h:outputText value="#{exp.mexpMonth3}" style="float: right" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>

                    <p:column class="col_right" sortBy="#{exp.mexpMonth4}">
                        <h:outputText value="#{exp.mexpMonth4}"  style="float: right" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column  class="col_right" sortBy="#{exp.mexpMonth5}">
                        <h:outputText value="#{exp.mexpMonth5}"  style="float: right">
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column  class="col_right" sortBy="#{exp.mexpMonth6}">
                        <h:outputText value="#{exp.mexpMonth6}"  style="float: right">
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column  class="col_right" sortBy="#{exp.mexpMonth7}">
                        <h:outputText value="#{exp.mexpMonth7}" style="float: right" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column  class="col_right" sortBy="#{exp.mexpMonth8}">
                        <h:outputText value="#{exp.mexpMonth8}" style="float: right" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column  class="col_right" sortBy="#{exp.mexpMonth9}" >
                        <h:outputText value="#{exp.mexpMonth9}" style="float: right" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>



                    <p:column  class="col_right" sortBy="#{exp.mexpMonth10}">
                        <h:outputText value="#{exp.mexpMonth10}" style="float: right">
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column  class="col_right" sortBy="#{exp.mexpMonth11}">
                        <h:outputText value="#{exp.mexpMonth11}" style="float: right"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>


                    <p:column  class="col_right" sortBy="#{exp.mexpMonth12}">
                        <h:outputText value="#{exp.mexpMonth12}" style="float: right" >
                            <f:convertNumber maxFractionDigits="0" />
                        </h:outputText>


                    </p:column>


                    <p:column   class="col_right" sortBy="#{exp.mexpTotal}">
                        <h:outputText  value="#{exp.mexpTotal}" style="float: right" >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>


                    </p:column>

                    <p:column  sortBy="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}">
                        <h:outputText  value="#{exp.mexpPct}#{exp.mexpPct ne null ? '%' : ''}"  style="float: right" />                    


                    </p:column>


                    <p:columnGroup  type="footer" class="header"   >

                        <p:row>

                            <p:column footerText="Total :"  style="text-align:left" >

                            </p:column>

                            <!--#390130:sales by month :column sorter provided  -->


                            <p:column footerText="Sales" class="col_right" sortBy="#{exp.mexpMonth2}">

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[0])}" class="col_right" sortBy="#{exp.mexpMonth3}" >


                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[1])}" class="col_right" sortBy="#{exp.mexpMonth4}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[2])}" class="col_right" sortBy="#{exp.mexpMonth5}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[3])}" class="col_right" sortBy="#{exp.mexpMonth6}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[4])}" class="col_right" sortBy="#{exp.mexpMonth7}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.totals[5]}" class="col_right" sortBy="#{exp.mexpMonth8}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[6])}" class="col_right" sortBy="#{exp.mexpMonth9}" >

                            </p:column>



                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[7])}" class="col_right" sortBy="#{exp.mexpMonth10}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[8])}" class="col_right" sortBy="#{exp.mexpMonth11}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[9])}" class="col_right" sortBy="#{exp.mexpMonth12}">

                            </p:column>
                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[10])}" class="col_right" sortBy="#{exp.mexpMonth12}">

                            </p:column>
                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[11])}" class="col_right" sortBy="#{exp.mexpMonth12}">

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totals[12])}" class="col_right" sortBy="#{exp.mexpMonth12}">

                            </p:column>
                            <!--#3771: Task Total :% not showing-->
                            <p:column footerText="#{monthlyExpandedRep.totals[13]}#{monthlyExpandedRep.totals[13] ne null ? '%' : ''}" class="col_right" sortBy="#{exp.mexpMonth12}"  width="50">

                            </p:column>





                        </p:row>
                        <p:row>

                            <p:column   style="float:left">

                            </p:column>
                             <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            <p:column   footerText="#{custom.labels.get('IDS_COMM')}" sortBy="#{exp.mexpMonth1}" >
                            </p:column>
                            <!--#390130:sales by month :column sorter provided  -->
                            <p:column   footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[0])}" sortBy="#{exp.mexpMonth1}" >

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[1])}" style="float:right" sortBy="#{exp.mexpMonth2}">

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[2])}" style="float:right" sortBy="#{exp.mexpMonth3}" >


                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[3])}" class="col_right" sortBy="#{exp.mexpMonth4}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[4])}" class="col_right" sortBy="#{exp.mexpMonth5}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[5])}" class="col_right" sortBy="#{exp.mexpMonth6}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[6])}" class="col_right" sortBy="#{exp.mexpMonth7}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[7])}" class="col_right" sortBy="#{exp.mexpMonth8}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[8])}" class="col_right" sortBy="#{exp.mexpMonth9}" >

                            </p:column>



                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[9])}" class="col_right" sortBy="#{exp.mexpMonth10}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[10])}" class="col_right" sortBy="#{exp.mexpMonth11}">

                            </p:column>


                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[11])}" class="col_right" sortBy="#{exp.mexpMonth12}">

                            </p:column>

                            <p:column footerText="#{monthlyExpandedRep.convertNumber(monthlyExpandedRep.totalc[12])}" class="col_right" sortBy="#{exp.mexpMonth12}">

                            </p:column>
                            <!--#3771: Task Total :% not showing-->
                            <p:column footerText="#{monthlyExpandedRep.totalc[13]}#{monthlyExpandedRep.totalc[13] ne null ? '%' : ''}" class="col_right"  width="50"  sortBy="#{exp.mexpMonth12}">

                            </p:column>





                        </p:row>

                    </p:columnGroup>

                    <!--#3204:Task Related to CRM-2014: Commission Reports: Export option in Sales/Comm by month  START -->
                </p:dataTable>





                <style>

                    .ui-panelgrid .ui-panelgrid-cell {
                        padding-top: 1px !important;
                        padding-bottom: 1px !important;
                    }

                    [role="gridcell"]{

                        padding: 0px 10px !important;
                    }
                    .content-header{     display: none; }


                    .jqplot-target {

                        font-size: 0.75em!important;
                    }

                    .main-footer{
                        display: none;
                    }
                    /*<!--Feature #3052: CRM-3695: Rouzer: Lock headers on Sales/comms by month report-->*/

                    /*Bug #3490:CRM-3936: Sales/Comm by month display change*/

                    @media only screen and (min-height : 600px) and (max-height : 900px)  {

                        .ui-datatable-scrollable-body{
                            height: 57vh; 

                        }



                    }       
                    /*1920 * 1080*/
                    @media only screen and (min-height : 901px) and (max-height : 1152px)  {

                        .ui-datatable-scrollable-body{
                            height: 70vh; 

                        }


                    }

                    @media only screen and (min-height : 1153px) and (max-height : 1440px)  {
                        /*task :4250*/
                        .ui-datatable-scrollable-body{
                            height: 78vh; 

                        }


                    }

                    /*1920 * 1080*/
                    /*            @media only screen and (min-width : 1425px) and (max-width : 2598px)  {
                    
                                    .ui-datatable-scrollable-body{
                                        height:70vh; 
                    
                                    }
                    
                    
                                }*/



                    /*            .ui-datatable-scrollable-body
                                {
                                    height:70vh;
                                }*/


                    html,body{
                        min-height:120%;

                        overflow-y: auto;
                    }
                    /*//3490:CRM-3936: Sales/Comm by month display change*/
                    .ui-datatable-scrollable-body::-webkit-scrollbar-thumb {
                        border-radius: 8px;
                        border: 1px solid white;
                        background-color: #8c8f96fa !important;
                    }



                </style>


                <!--#3249: Task Reporting > Commission Reports > Add loading gif START -->
                <script>

                    window.onload = function () {
//                $('#loading-image').show();
                        setTimeout(function () {
                            var t = performance.timing;
                            console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                            document.getElementById("salComComprpt").style.pointerEvents = "all";
                            document.getElementById("salComComprpt").style.opacity = "1";
                            $('#salcommCust\\:loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                        }, 0);
                    };
                    //     5330:  Sales and Commission by Month: Include region breakup
                    function hide()
                    {

                         $('#salcommCust\\:loading-image').hide();
                        document.getElementById("salComComprpt").style.pointerEvents = "all";
                        document.getElementById("salComComprpt").style.opacity = "1";
                    }

//     5330:  Sales and Commission by Month: Include region breakup
                    function processGif()
                    {
                        $('#salcommCust\\:loading-image').show();
                        document.getElementById("salComComprpt").style.pointerEvents = "none";
                        document.getElementById("salComComprpt").style.opacity = "0.5";
                    }

                    function start() {
                        PF('insalComPrinProgressDlg').show();
                    }
                    function stop() {
                        PF('insalComPrinProgressDlg').hide();
                    }


                </script>

                <!--#3249: Task Reporting > Commission Reports > Add loading gif  END -->   


            </div>
            <!--Feature #3052: CRM-3695: Rouzer: Lock headers on Sales/comms by month report-->
            <footer class="main-footer">

            </footer>



        </h:form>
        <!--#3249: Task Reporting > Commission Reports > Add loading gif  END -->   


        <p:dialog widgetVar="insalComPrinProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
            <h:form>
                <p:outputPanel >
                    <br />
                    <h:graphicImage  library="images" name="ajax-loader.gif"   />
                    <p:spacer width="5" />
                    <p:outputLabel value="Please wait...Exporting records" />
                    <br /><br />
                    <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                </p:outputPanel>
            </h:form>
        </p:dialog>


        <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
        <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />



    </ui:define>













</ui:composition>