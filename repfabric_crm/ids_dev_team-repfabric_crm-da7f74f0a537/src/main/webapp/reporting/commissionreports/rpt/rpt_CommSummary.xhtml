<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: rptCommissionSummary.xhtml
// Author: Sharvani
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">

    <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
       <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
        <title>  #{custom.labels.get('IDS_COMMISSION')} Summary  </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata> 

<!--            <f:viewParam name="yr" value="#{reportFilters.repYear}" />
            <f:viewParam name="mt" value="#{reportFilters.repMonth}"/>
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="d" value="#{reportFilters.distributor.compId}"/>
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>
            <f:viewParam name="m" value="#{reportFilters.minVal}"/>
            <f:viewParam name="g" value="#{reportFilters.grp}"/>
            <f:viewParam name="c" value="#{reportFilters.customer.compId}"/>-->


            <f:viewParam name="d1" value="#{commSummary.repFrom}" />
            <f:viewParam name="d2" value="#{commSummary.repTo}" />
            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="c" value="#{reportFilters.customer.compId}"/>        
            <f:viewParam name="s" value="#{reportFilters.salesTeam.smanId}"/>         
            <f:viewParam name="g" value="#{commSummary.grp}"/>
             <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
            <f:viewParam name="ri" value="#{commSummary.compRegns}"/>
            <f:viewParam name="rn" value="#{commSummary.compRegNames}"/>
            <!--//Feature #2295:Commission Summary > Summarize by Parent-->
            <f:viewParam name="prt" value="#{commSummary.checkFlag}"/>
            <f:viewAction action="#{commSummary.run()}"/>

        </f:metadata>
    </ui:define>

    <ui:define name="title">
        <ui:param name="title" value="Products"/>
        <div class="row">
            <div class="col-md-6">
                <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                #{custom.labels.get('IDS_COMMISSION')} Summary
            </div>

        </div>
    </ui:define>

    <ui:define name="body">


        <f:event listener="#{commSummary.isPageAccessible}" type="preRenderView"/>
        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4"   id="commSummaryRpt" >


            <!--#3249:  Task Reporting > Commission Reports > Add loading gif-->

            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                            left: 600px;
                            top: 300px;
                            width: 150px;
                            height: 150px;
                            z-index: 9999; opacity: .5 !important; " />      


            <h:form id="commSummaryHeader" >
                <!--//    #1148: Commission Reports > Do not default to Customer Sales team-->
                <p:remoteCommand name="applyCustomer" actionListener="#{reportFilters.applyOnlyCustomer(viewCompLookupService.selectedCompany)}" update="commSummaryHeader:inputCustomer "/>

                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="commSummaryHeader:inputPrincName"  />
                <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="commSummaryHeader:inputAllSalesTeam" />
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                        <div class="compNamebg" style="width: 1000px" >
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            <!--Task #2419Commission Summary > Summarize by Parent - UI Changes-->
                       <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->  
                            <p:outputLabel value="#{custom.labels.get('IDS_COMMISSION')} Summary Report  #{commSummary.checkFlag ?'(by Parent Company)':''}" id="comSummLabel"  style="width: 2000px"/>
                            <br/>
                            <p:outputLabel  value="From:"   />
                            #{commReports.dtFrm}
                            <br/>
                            <p:outputLabel  value="To:"   />
                            <p:spacer width="22px"/>
                            #{commReports.dtTo}
                        </div>
                         <!--#8516   CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
                        <div   style="float:left;width:600px" >
                            <p:outputLabel style="font-weight: normal" rendered="#{commSummary.compRegNames.length() > 0}" value="*Filtered for selected #{custom.labels.get('IDS_REGION')}" />  
                        </div>
                    </div>

                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="margin-left:100px" >
                          <!--#8516  CRM-5877: Aurora: Reports: add Region + prior year total (Commission Summary)-->
                        <p:panelGrid   columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" layout="grid" style="width: 250px;float:left;"  id="graph"> 
                            <p:outputLabel value="Group by"/>

                            <p:selectOneRadio value="#{commSummary.grp}"  widgetVar="grp" layout="pageDirection"  required="true">

                                <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/>  
                                <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                                <f:selectItem itemValue="2" itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')}"/>
                                <f:selectItem itemValue="3" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}"  /> 
                                <!---Feature #2295:Commission Summary > Summarize by Parent-->

                                <p:ajax event="change"   listener="#{commSummary.setChkBoxVal()}"  />

<!--<p:ajax   event="change"   listener="#{commSummary.run()}" update=":commSummaryHeader:dtrpt"/>-->
                            </p:selectOneRadio>

                        </p:panelGrid>
                    </div> 

                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:-100px">
                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                     style="float: right;width: 350px;"      layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />

                            <h:panelGroup class="ui-inputgroup"  >
    <!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustomer" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="inputCustomer" value="#{reportFilters.customer.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--#3473:     sales team - Sales team data we can enter manually-->

                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                                  styleClass="btn-info btn-xs"  style="width:67px" />
                            </h:panelGroup>
                            <!--#3249:   Task Reporting > Commission Reports > Add loading gif  view report button included in panel grid   -->

                            <p:spacer width="4px" />
                            <h:panelGroup>

                                <!--Task #2419Commission Summary > Summarize by Parent - UI Changes-->
                                <p:commandButton  value="View Report"  id="viewreport"  styleClass="btn btn-primary btn-xs"  style="width:30px;" actionListener="#{commSummary.run()}" update=":commSummaryHeader:dtrpt :commSummaryHeader:comSummaryPdf :commSummaryHeader:comSummaryExport :commSummaryHeader:comSummLabel"  onclick="processGif()"  oncomplete="hide();"/>
                                <p:spacer width="4px" />

                                <p:commandLink id="xls" ajax="false" onclick="PrimeFaces.monitorDownload(start, stop);">  
                                    <p:graphicImage name="/images/excel.png" width="24"    />

                              <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->     
                                    <pe:exporter type="xlsx" target="comSummaryExport"  fileName="#{custom.labels.get('IDS_COMMISSION')}_Summary" subTable="false" />  
                                </p:commandLink>

                                <p:commandLink id="pdf" ajax="false"  onclick="PrimeFaces.monitorDownload(start, stop);"  >  
                                    <p:graphicImage value="/resources/images/pdf.png"  width="24"  />  
                             <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23--> 
                                    <pe:exporter type="pdf" target="comSummaryPdf" fileName="#{custom.labels.get('IDS_COMMISSION')}_Summary" subTable="false" preProcessor="#{productDtlsItem.preProcessPDF}"/>  

                                </p:commandLink>
                            </h:panelGroup>

                            <div></div>
                        </p:panelGrid>
                        <!--<p:ajax event="rowSelect" listener="#{commSummary.run()}" update=":commSummaryHeader:dtrpt" />-->


                    </div >
                </div>






                <h:panelGroup id="tbldata">
                    <p:dataTable id="dtrpt"  value="#{commSummary.listComSummary}" var="sum" emptyMessage="No data found" >

<!--                <p:column sortBy="#{sum.itemName}"  headerText="#{commReports.repGrp eq 1 ? custom.labels.get('IDS_PRINCI') :
                                                                (commReports.repGrp eq 2 ?  (custom.labels.get('IDS_DB_CUSTOMER')) : custom.labels.get('IDS_SALES_TEAM')) }" 
                            
                                                      class="col_left" >-->
                        <!--//Feature #2295:Commission Summary > Summarize by Parent-->
                        <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                        <p:column sortBy="#{sum.itemName}"  headerText="#{commSummary.grp eq 1 ? (commSummary.checkFlag ?'Parent Company':  custom.labels.get('IDS_PRINCI')) :
                                            (commSummary.grp eq 2 ? (commSummary.checkFlag ?'Parent Company': (custom.labels.get('IDS_DB_CUSTOMER'))) : custom.labels.get('IDS_SALES_TEAM')) }" 

                                  class="col_left" >  

                            <h:outputLabel value="#{sum.itemName}"  />   
                            <f:facet name="footer">
                                <h:outputLabel value="Total"  style="float: right;font-weight: bold" />
                            </f:facet>

                        </p:column>

                        <p:column sortBy="#{sum.totalAmt}"   headerText="Sales Amt" class="col_right" width="150" >
                            <h:outputLabel value="#{sum.totalAmt}" style="float: right"  >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputLabel>
                            <f:facet name="footer">
                                <h:outputLabel value="#{commSummary.totals[1]}"  style="float: right;font-weight: bold" >
                                    <f:convertNumber maxFractionDigits="0"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                        <!--Bug 288 #549804 Dashboard: db : need sales rep detail to the 2 decimals-->    
                  <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                        <p:column sortBy="#{sum.commAmt}" headerText="#{custom.labels.get('IDS_COMM')} Amt" class="col_right" width="150" >
                            <h:outputLabel value="#{sum.commAmt}" style="float: right"  >
                                <f:convertNumber minFractionDigits="2"/>
                            </h:outputLabel>    
                            <f:facet name="footer">
                                <h:outputLabel value="#{commSummary.totals[2]}" style="float: right;font-weight: bold"  >
                                    <f:convertNumber minFractionDigits="2"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>

                        <!--Bug 288 #549804 Dashboard: db : need sales rep detail to the 2 decimals-->    
                        <p:column sortBy="#{sum.compCommAmt}"  headerText="Company Split" class="col_right" width="150" >
                            <h:outputLabel value="#{sum.compCommAmt}" style="float: right"  >
                                <f:convertNumber minFractionDigits="2"/>
                            </h:outputLabel> 
                            <f:facet name="footer">
                                <h:outputLabel value="#{commSummary.totals[3]}" style="float: right;font-weight: bold" >
                                    <f:convertNumber minFractionDigits="2"/>
                                </h:outputLabel>
                            </f:facet>
                        </p:column>


                    </p:dataTable>
                </h:panelGroup>


                <!--                <div id="footer">
                                    <div style="font-size: 12px;color: #4f4f4f;display: inline-block;float: right;line-height: 20px">
                                                                <ui:insert name="footer">
                                        
                                                                </ui:insert>   
                                        Repfabric Dashboard - Commission Details Report 
                                        <h:outputLabel value="#{rFUtilities.getCurrentTimeZoneDate()}">
                                            <f:convertDateTime   pattern="#{globalParams.dateTimeFormat}" />
                                        </h:outputLabel>
                                    </div>
                                </div>-->

                <p:dataTable  value="#{commSummary.listComSummary}"  id="comSummaryPdf"  var="sum"  rendered="false">

                    <p:columnGroup  type="header" class="header"   >
                        <p:row>

                            <p:column rowspan="2"  headerText="#{loginBean.subscriber_name}" />


                    <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            <p:column  rowspan="2"  colspan="1"  headerText="#{custom.labels.get('IDS_COMMISSION')} Summary" />


                            <p:column  rowspan="2" headerText="From: #{commReports.dtFrm}  To: #{commReports.dtTo}" />


                            <p:column  rowspan="2" />





                        </p:row>

                        <p:row></p:row>
                        <p:row></p:row>
                        <p:row></p:row>

                        <p:row>

                            <!--//Feature #2295:Commission Summary > Summarize by Parent-->
                            <!--Feature #5011  cleaning up End User terminology in Commission reports-->

                            <p:column   headerText="#{commSummary.grp eq 1 ? (commSummary.checkFlag ?'Parent Company':  custom.labels.get('IDS_PRINCI')) :
                                                      (commSummary.grp eq 2 ? (commSummary.checkFlag ?'Parent Company':  custom.labels.get('IDS_DB_CUSTOMER')) : custom.labels.get('IDS_SALES_TEAM')) }" style="text-height: max-size"/>
                            <p:column  headerText="Sales Amt"/>
                       <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            <p:column headerText="#{custom.labels.get('IDS_COMM')} Amt"/>

                            <p:column headerText="Company Split" />

                        </p:row>


                    </p:columnGroup>  

                    <p:column sortBy="#{sum.itemName}" 

                              class="col_left" >  

                        <h:outputText value="#{sum.itemName}"  />   
                        <f:facet name="footer">
                            <h:outputText value="Total"  style="float: right;font-weight: bold" />
                        </f:facet>

                    </p:column>

                    <p:column sortBy="#{sum.totalAmt}"   class="col_right" width="150" >
                        <h:outputText value="#{sum.totalAmt}" style="float: right"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText value="#{commSummary.totals[1]}"  style="float: right;font-weight: bold" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>
                    </p:column>


                    <!--Bug 288 #549804 Dashboard: db : need sales rep detail to the 2 decimals-->    
                    <p:column sortBy="#{sum.commAmt}" class="col_right" width="150" >
                        <h:outputText value="#{sum.commAmt}" style="float: right"  >
                            <f:convertNumber minFractionDigits="2"/>
                        </h:outputText>    
                        <f:facet name="footer">
                            <h:outputText value="#{commSummary.totals[2]}" style="float: right;font-weight: bold"  >
                                <f:convertNumber minFractionDigits="2"/>
                            </h:outputText>
                        </f:facet>
                    </p:column>

                    <!--Bug 288 #549804 Dashboard: db : need sales rep detail to the 2 decimals-->    
                    <p:column sortBy="#{sum.compCommAmt}"  class="col_right" width="150" >
                        <h:outputText value="#{sum.compCommAmt}" style="float: right"  >
                            <f:convertNumber minFractionDigits="2"/>
                        </h:outputText> 
                        <f:facet name="footer">
                            <h:outputText value="#{commSummary.totals[3]}" style="float: right;font-weight: bold" >
                                <f:convertNumber minFractionDigits="2"/>
                            </h:outputText>
                        </f:facet>
                    </p:column>


                </p:dataTable> 



                <p:dataTable   id="comSummaryExport"  value="#{commSummary.listComSummary}" var="sum"  rendered="false">


                    <p:columnGroup  type="header" class="header"   >
                        <p:row>

                            <p:column colspan="3"   rowspan="2"  headerText="#{loginBean.subscriber_name}" />


                           <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            <p:column  colspan="2" rowspan="2"   headerText="#{custom.labels.get('IDS_COMMISSION')} Summary" />



                            <p:column  colspan="5" rowspan="2" headerText="From: #{commReports.dtFrm}  To: #{commReports.dtTo}" />






                        </p:row>

                        <p:row></p:row>
                        
                        <p:row>
                         
                        </p:row>
                        <p:row></p:row>

                        <p:row>

                            <!--//Feature #2295:Commission Summary > Summarize by Parent-->
                            <!--Feature #5011  cleaning up End User terminology in Commission reports-->
                            <p:column   headerText="#{commSummary.grp eq 1 ? (commSummary.checkFlag ?'Parent Company':  custom.labels.get('IDS_PRINCI')) :
                                                      (commSummary.grp eq 2 ? (commSummary.checkFlag ?'Parent Company':   custom.labels.get('IDS_DB_CUSTOMER')) : custom.labels.get('IDS_SALES_TEAM')) }" style="text-height: max-size"/>
                            <p:column  headerText="Sales Amt"/>
<!--                     Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23  -->
                            <p:column headerText="#{custom.labels.get('IDS_COMM')} Amt"/>

                            <p:column headerText="Company Split" />

                        </p:row>


                    </p:columnGroup>  

                    <p:column sortBy="#{sum.itemName}" 

                              class="col_left" >  

                        <h:outputText value="#{sum.itemName}"  />   
                        <f:facet name="footer">
                            <h:outputText value="Total"  style="float: right;font-weight: bold" />
                        </f:facet>

                    </p:column>

                    <p:column sortBy="#{sum.totalAmt}"   class="col_right" width="150" >
                        <h:outputText value="#{sum.totalAmt}" style="float: right"  >
                            <f:convertNumber maxFractionDigits="0"/>
                        </h:outputText>
                        <f:facet name="footer">
                            <h:outputText value="#{commSummary.totals[1]}"  style="float: right;font-weight: bold" >
                                <f:convertNumber maxFractionDigits="0"/>
                            </h:outputText>
                        </f:facet>
                    </p:column>


                    <!--Bug 288 #549804 Dashboard: db : need sales rep detail to the 2 decimals-->    
                    <p:column sortBy="#{sum.commAmt}" class="col_right" width="150" >
                        <h:outputText value="#{sum.commAmt}" style="float: right"  >
                            <f:convertNumber minFractionDigits="2"/>
                        </h:outputText>    
                        <f:facet name="footer">
                            <h:outputText value="#{commSummary.totals[2]}" style="float: right;font-weight: bold"  >
                                <f:convertNumber minFractionDigits="2"/>
                            </h:outputText>
                        </f:facet>
                    </p:column>

                    <!--Bug 288 #549804 Dashboard: db : need sales rep detail to the 2 decimals-->    
                    <p:column sortBy="#{sum.compCommAmt}"  class="col_right" width="150" >
                        <h:outputText value="#{sum.compCommAmt}" style="float: right"  >
                            <f:convertNumber minFractionDigits="2"/>
                        </h:outputText> 
                        <f:facet name="footer">
                            <h:outputText value="#{commSummary.totals[3]}" style="float: right;font-weight: bold" >
                                <f:convertNumber minFractionDigits="2"/>
                            </h:outputText>
                        </f:facet>
                    </p:column>




                </p:dataTable>



            </h:form>

            <p:dialog widgetVar="inCommSumProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Please wait...Exporting records" />
                        <br /><br />
                        <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                    </p:outputPanel>
                </h:form>
            </p:dialog>    



        </div>

        <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
        <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />


        <style>

            .ui-panelgrid .ui-panelgrid-cell {
                padding-top: 1px !important;
                padding-bottom: 1px !important;
            }

            [role="gridcell"]{

                padding: 0px 10px !important;
            }
            .content-header{     display: none; }


            .jqplot-target {

                font-size: 0.75em!important;
            }

            /*            .ui-datatable-data .ui-widget-content
                        {
                            display: none;
                        }*/

        </style>


        <!--#3249: Task Reporting > Commission Reports > Add loading gif START -->
        <script>

            window.onload = function () {
                //                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
                    //      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("commSummaryRpt").style.pointerEvents = "all";
                    document.getElementById("commSummaryRpt").style.opacity = "1";

                    $('#loading-image').hide();
                    //            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };


            function hide()
            {

                $('#loading-image').hide();
                document.getElementById("commSummaryRpt").style.pointerEvents = "all";
                document.getElementById("commSummaryRpt").style.opacity = "1";
            }

            function processGif()
            {
                $('#loading-image').show();
                document.getElementById("commSummaryRpt").style.pointerEvents = "none";
                document.getElementById("commSummaryRpt").style.opacity = "0.5";
            }

            function start() {

                PF('inCommSumProgressDlg').show();
            }

            function stop() {
                PF('inCommSumProgressDlg').hide();
            }




        </script>

        <!--#3249:   Task Reporting > Commission Reports > Add loading gif  END   -->





    </ui:define>





    <!--     <ui:define name="widthcss">
            <style>
                #wrapper {
                    margin: 0 auto;
                    width: 95%;
                }
    
                .ui-datatable thead th, .ui-datatable foot td{
                    padding: 3px 6px;
                }
    
    
            </style>        
        </ui:define>-->




</ui:composition>