<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: cashflowForecast.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"

                template="#{layoutMB.template}"

                xmlns:pe="http://primefaces.org/ui/extensions">
    <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>  Cash Flow Forecast Report  </title>
    </ui:define>
 
    <ui:define name="meta">
        <f:metadata> 


            <f:viewParam name="p" value="#{reportFilters.pricipal.compId}"/>
            <f:viewParam name="g" value="#{cashflowRep.grp}"/>
            <f:viewAction action="#{reportFilters.assignSalesTeams()}"/>
            <f:viewAction action="#{cashflowRep.run()}"/>
        </f:metadata>
    </ui:define>


    <ui:define name="body">
        <!--<f:event listener="#{monthlyExpandedRep.isSalCommPageAccessible}" type="preRenderView"/>-->  


        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4"  id="cashFlowRep"  >

            <!--#3249:#3249:     Task Reporting > Commission Reports > Add loading gif-->

            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style=" position: fixed;
                            left: 600px;
                            top: 300px;
                            width: 150px;
                            height: 150px;
                            z-index: 9999; opacity: .5 !important; " />      



            <h:form id="cashflowFm" >

                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="cashflowFm:inputPrincName" />





                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >


                        <div class="compNamebg" style="float:left">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            Cash Flow Forecast Report   
                            <br/>

                        </div>

                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5" style="padding-left:-100px"  >
                        <h:panelGrid columns="1"  id="salesbymonth" >
                            <p:chart type="bar" model="#{cashflowRep.barChartcashFlow}"   style="width:600px;height:180px;float: left" widgetVar="mnthgrp"  />
                        </h:panelGrid>
                    </div>   
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" >
                        <p:panelGrid id="grid" columns="2" 
                                     style="float: right;width: 300px;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')}" />
                            <h:panelGroup class="ui-inputgroup"  style="width: 112%">
                                <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width: 200%;" id="selSalesTeam">
                                    <f:selectItems value="#{lookupService.salesTeams}" var="team"  
                                                   itemValue="#{team.smanId}"
                                                   itemLabel="#{team.smanName}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.list('SALES_TEAM')}"  update=":frmSalesTeamLookup"   oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                <p:commandLink styleClass="sel-salesteam" value="Clear" action="#{lookupService.clear('SALES_TEAM')}"   actionListener="#{poBacklogService.clearSalesTeam()}"      update="@(.sel-salesteam)" disabled="#{!(lookupService.salesTeams!=null and lookupService.salesTeams.size() != 0)}" />
                            </h:panelGroup>


                            <p:commandButton  value="View Report "  id="viewreport"   styleClass="btn btn-primary btn-xs" actionListener="#{reportFilters.setSalesteamlst()}"  action="#{cashflowRep.goToCashFlow()}"  style="width:30px" onclick="processGif();"/>

                            <p:column>
                                <p:commandButton value="Export"   style="width:30px"  
                                                 ajax="false"  actionListener="#{cashflowRep.exportData()}"  
                                                 styleClass="btn btn-primary btn-xs" onclick="PrimeFaces.monitorDownload(start, stop);" 
                                                 /> 

                               
                            </p:column>     

                        </p:panelGrid>


                    </div>
                </div>  


                <h:panelGroup id="tbldata">

                    <p:dataList   id="dtrpt1" value="#{cashflowRep.listExpand}" var="cashFlow"  >


                        <p:column >
                            <div class="div-selected" style="height:25px;width: 100%;" >
                                <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                                    #{cashFlow.grpName}          
                                </label>               
                            </div>
                            <p:dataTable  value="#{cashFlow.details}" var="cashFlowdetails" emptyMessage="No data found"    >

                                <p:column  rendered="#{cashflowRep.grp eq 2}"  sortBy="#{cashFlowdetails.repPrinciName}" headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left"  width="200" >
                                    <h:outputLabel value="#{cashFlowdetails.repPrinciName}"  />       
                                </p:column>

                                <p:column sortBy="#{cashFlowdetails.repSmanName}" rendered="#{cashflowRep.grp eq 1}"   headerText="#{custom.labels.get('IDS_SALES_TEAM')}" class="col_left"   width="200" >
                                    <h:outputLabel value="#{cashFlowdetails.repSmanName}"  />       
                                </p:column>


                                <p:column sortBy="#{cashFlowdetails.repDataType}"   headerText="Type" class="col_left" width="50" >
                                    <h:outputLabel value="#{cashFlowdetails.repDataType}"  />
                                    <f:facet name="footer">
                                        <h:outputLabel value="Total" style="float: right;font-weight: bold"  />
                                    </f:facet>
                                </p:column>



                                <p:column sortBy="#{cashFlowdetails.repPast}"  headerText="Till Today"  class="col_right"   width="130" >
                                    <h:outputLabel value="#{cashFlowdetails.repPast}"  class="col_right"/>   

                                    <f:facet name="footer">
                                        <h:outputLabel value="#{cashFlow.totals[1]}" style="float: right;font-weight: bold"  />
                                    </f:facet>
                                </p:column>

                                <p:column sortBy="#{cashFlowdetails.repMonth1}"   headerText="1 Month" class="col_right" width="120" >
                                    <h:outputLabel value="#{cashFlowdetails.repMonth1}" class="col_right" />  
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{cashFlow.totals[2]}" style="float: right;font-weight: bold"  />
                                    </f:facet>

                                </p:column>

                                <p:column sortBy="#{cashFlowdetails.repMonth2}"   headerText="2 Months" class="col_right" width="120" >
                                    <h:outputLabel value="#{cashFlowdetails.repMonth2}" class="col_right" >

                                    </h:outputLabel>
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{cashFlow.totals[3]}" style="float: right;font-weight: bold"  />
                                    </f:facet>
                                </p:column>


                                <p:column sortBy="#{cashFlowdetails.repMonth3}"   headerText="3 Months" class="col_right" width="120" >
                                    <h:outputLabel value="#{cashFlowdetails.repMonth3}" class="col_right" >

                                    </h:outputLabel>
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{cashFlow.totals[4]}" style="float: right;font-weight: bold"  />
                                    </f:facet>
                                </p:column>


                                <p:column sortBy="#{cashFlowdetails.repMonth4}"    headerText="4 Months" class="col_right" width="120" >
                                    <h:outputLabel value="#{cashFlowdetails.repMonth4}" style="float: right"  class="col_right"  >
                                        <f:convertNumber maxFractionDigits="0"/>
                                    </h:outputLabel>
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{cashFlow.totals[5]}" style="float: right;font-weight: bold"  />
                                    </f:facet>
                                </p:column>

                                <p:column sortBy="#{cashFlowdetails.repMonth5}" headerText="5 Months" class="col_right" width="120" >
                                    <h:outputLabel value="#{cashFlowdetails.repMonth5}" style="float: right"  >
                                        <f:convertNumber minFractionDigits="2"/>
                                    </h:outputLabel>    
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{cashFlow.totals[6]}" style="float: right;font-weight: bold"  />
                                    </f:facet>
                                </p:column>

                                <p:column sortBy="#{cashFlowdetails.repMonth6}" headerText="6 Months" class="col_right" width="120" >
                                    <h:outputLabel value="#{cashFlowdetails.repMonth6}" style="float: right"  >
                                        <f:convertNumber minFractionDigits="2"/>
                                    </h:outputLabel>    
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{cashFlow.totals[7]}" style="float: right;font-weight: bold"  />
                                    </f:facet>
                                </p:column>

                                <p:column sortBy="#{cashFlowdetails.repLater}"  headerText="> 6 Months" class="col_right" width="120" >
                                    <h:outputLabel value="#{cashFlowdetails.repLater}" style="float: right"   >
                                        <f:convertNumber minFractionDigits="2"/>
                                    </h:outputLabel> 
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{cashFlow.totals[8]}" style="float: right;font-weight: bold"  />
                                    </f:facet>
                                </p:column>

                                <p:column sortBy="#{cashFlowdetails.repTotal}" headerText="Total " class="col_right" width="120" >
                                    <h:outputLabel value="#{cashFlowdetails.repTotal }" class="col_right" />   
                                    <f:facet name="footer">
                                        <h:outputLabel value="#{cashFlow.totals[9]}" style="float: right;font-weight: bold"  />
                                    </f:facet>
                                </p:column>


<!--                                <p:column sortBy="#{cashFlowdetails.repPct}"  headerText="Pct (%)" class="col_right" width="120"    >
     <h:outputLabel value="#{cashFlowdetails.repPct}" style="float: right"   >
         
     </h:outputLabel> 
     
 </p:column>-->
                                <!--

                                -->


                            </p:dataTable>
                           

                        </p:column>
                           
                    </p:dataList>
                    <p:dataTable emptyMessage="" >
                                <p:column  width="200" />
                                <p:column  headerText="Grand Total"  width="65"/>
                               
                                <p:column headerText="#{cashflowRep.repPastG}" />
                                <p:column headerText="#{cashflowRep.repMonth1G}" />
                                <p:column headerText="#{cashflowRep.repMonth2G}" />
                                <p:column headerText="#{cashflowRep.repMonth3G}" />
                                <p:column headerText="#{cashflowRep.repMonth4G}" />
                                <p:column headerText="#{cashflowRep.repMonth5G}" />
                                <p:column headerText="#{cashflowRep.repMonth6G}" />
                                <p:column  headerText="#{cashflowRep.repLaterG}"/>
                                <p:column  headerText="#{cashflowRep.repTotalG}"/>

                            </p:dataTable>
                </h:panelGroup>
            </h:form>
        </div>
        <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
        <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>
        <style> 
            .content-header{     display: none; }
           /*14453 ESCALATIONS CRM-8464   Reporting Graphs Legend not legible - Multiple Instances*/
            .jqplot-axis {
                 font-size: 0.25em !important;
            }


            .jqplot-target {

                font-size: 0.75em!important;
            }
        </style>
        <script>
            window.onload = function () {
//                $('#loading-image').show();
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
//      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                    document.getElementById("cashFlowRep").style.pointerEvents = "all";
                    document.getElementById("cashFlowRep").style.opacity = "1";

                    $('#loading-image').hide();
//            $('#prodrpt').css({pointer - events:"all"});
                }, 0);
            };

            function processGif()
            {
                $('#loading-image').show();
                document.getElementById("cashFlowRep").style.pointerEvents = "none";
                document.getElementById("cashFlowRep").style.opacity = "0.4";
            }

            function hide()
            {

                $('#loading-image').hide();

                document.getElementById("cashFlowRep").style.pointerEvents = "all";
                document.getElementById("cashFlowRep").style.opacity = "1";
            }



            function start() {
                PF('inSalByMonthProgressDlg').show();
            }
            function stop() {

                PF('inSalByMonthProgressDlg').hide();
            }
        </script>

    </ui:define>
</ui:composition>