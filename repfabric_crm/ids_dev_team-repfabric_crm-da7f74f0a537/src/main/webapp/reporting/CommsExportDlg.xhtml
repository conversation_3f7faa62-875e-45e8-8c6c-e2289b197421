<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
<!--     Task#3842-CRM-4032:  smgrep: Post-split not relabelable in import mapping (Comm. Data Over Export)     15-03-2021  by harshithad-->
  <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
<p:dialog header="Export #{custom.labels.get('IDS_COMMISSION')}" class="dialogCSS" widgetVar="dlgCommsExport" modal="true" width="570"  resizable="false" responsive="true"  id="exportDlg"  >
        <h:form id="commsExportDlgForm">
            <!--<p:remoteCommand actionListener="#{viewPrincipalMst.loadPrinciListForCommsExport()}"  name="loadPrinci" autoRun="true" update=":frmPrinciLookup princiList @(.clrStatus)"  />-->
            <!--<p:remoteCommand actionListener="#{viewCustomerMst.loadCustomersForCommsExport()}" name = "loadCust" autoRun="true" update=":frmCustsLookup custList @(.clrStatus)"/>-->
<!--            <p:remoteCommand actionListener="#{viewSmanMst.loadSalesTeamForCommsExport()}" name="loadDistri" autoRun="true" update=":frmSmanLookup smanList @(.clrStatus)" />
            <p:remoteCommand autoRun="true" actionListener="#{stateLookup.clearSelectedStates()}" name="clrState" update="stateList :frmStateLookup @(.clrStatus)" />-->
            <!--<p:remoteCommand name="applyComps1" actionListener="#{commsMain.applyPrincipals(lookupService.principals)}" update=":commsExportDlgForm:selPrinci @(.sel-state)" />-->
            <p:remoteCommand name="applyComps1" actionListener="#{commsMain.applyPrincipals(lookupService.principals)}"  update="commsExportDlgForm:selPrinci  :commsExportDlgForm:princiClr" />
            <p:remoteCommand name="applyComps2" actionListener="#{commsMain.applyCustomers(lookupService.customers)}" update=":commsExportDlgForm:selCust  :commsExportDlgForm:custClr" />     
            <p:panelGrid id="grid" columns="4" 
                         style="min-height: 50px"       layout="grid" styleClass="box-primary no-border ui-fluid ">
                <!--Invoice Date-->

                <p:outputLabel value="Invoice Date From " />


                <p:calendar  pattern="#{globalParams.dateFormat}"  value="#{commsMain.invDateFrom}"  converterMessage="Invalid Date format"   autocomplete="off"  style="width:40%" >  
                    <p:ajax event="dateSelect" process="@this"  />
                </p:calendar>

                <p:outputLabel value="To " />
                <p:calendar  id="invoiceToDate" pattern="#{globalParams.dateFormat}"  value="#{commsMain.invDateTo}"  converterMessage="Invalid Date format"   autocomplete="off" style="margin-left:-100px" >  
                    <p:ajax event="change" async="true" process="@this"   />
                </p:calendar>

                <!--Check Date 1711 ajax added-->
                <p:outputLabel value="Check Date From " />
                <p:calendar  pattern="#{globalParams.dateFormat}"  value="#{commsMain.checkDateFrom}"  converterMessage="Invalid Date format"  autocomplete="off" >  
                    <p:ajax event="dateSelect"  process="@this"  />
                    <p:ajax event="keyup"  process="@this"   />
                </p:calendar>
                <p:outputLabel value="To " />
                <p:calendar  pattern="#{globalParams.dateFormat}"  value="#{commsMain.checkDateTo}"  converterMessage="Invalid Date format"  autocomplete="off" style="margin-left:-100px" >  
                       <p:ajax event="dateSelect" process="@this"  update=":commsExportDlgForm:postSplitbtn" />
                    <p:ajax event="keyup"  process="@this"  update=":commsExportDlgForm:postSplitbtn" />
                </p:calendar>
            </p:panelGrid>
            <p:panelGrid id="grid1" columns="2" 
                         style="min-height: 50px;"       layout="grid" styleClass="box-primary no-border ui-fluid ">

                <p:outputLabel value="#{custom.labels.get('IDS_PRINCI')}" />
                <h:panelGroup class="ui-inputgroup"  style="margin-left:-55%">
                    <h:selectOneListbox  size="2" style="width: 50%;" styleClass="sel-comp" id="selPrinci"   >
                        <f:selectItems value="#{commsMain.selectedPrincipals}" var="comp"  
                                       itemValue="#{comp.compId}"
                                       itemLabel="#{comp.compName}"  />

                    </h:selectOneListbox>
<!--                    3130 Comm. Data Overview: Related to CRM-1917: EARLEAZ: export alias clear filter added-->
                    <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  
                                     actionListener="#{viewCompLookupService.listCRM(1, 'applyComps1')}" oncomplete="PF('lookupComps').show();PF('dlgCompLookup').clearFilters();"
                                     update=":formCompsLookup" />
                    <p:commandLink styleClass="sel-comp" id="princiClr" value="Clear" actionListener="#{commsMain.clearPrincipals()}" update=":commsExportDlgForm:selPrinci  :commsExportDlgForm:princiClr" disabled="#{!(commsMain.selectedPrincipals!=null and commsMain.selectedPrincipals.size() != 0)}" />

                </h:panelGroup>
                <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}" />
                <h:panelGroup class="ui-inputgroup" style="margin-left:-55%">
                    <h:selectOneListbox  size="2" style="width: 50%;" styleClass="sel-comp" id="selCust"   >
                        <f:selectItems value="#{commsMain.selectedCustomers}" var="comp"  
                                       itemValue="#{comp.compId}"
                                       itemLabel="#{comp.compName}"  />

                    </h:selectOneListbox>
<!--3130 Comm. Data Overview: Related to CRM-1917: EARLEAZ: export alias clear filter added-->
   <!--<p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"    actionListener="#{viewCompLookupService.listCRM(2, 'applyComps')}" oncomplete="PF('lookupComps').show()"  update=":formCompsLookup"/>-->
                    <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  
                                     actionListener="#{viewCompLookupService.listCRM(2, 'applyComps2')}" oncomplete="PF('lookupComps').show();PF('dlgCompLookup').clearFilters();"
                                     update=":formCompsLookup" />
                    <p:commandLink styleClass="sel-comp" id="custClr"  value="Clear" actionListener="#{commsMain.clearCustomers()}" update=":commsExportDlgForm:selCust"  disabled="#{!(commsMain.selectedCustomers!=null and commsMain.selectedCustomers.size() != 0)}" />
                </h:panelGroup>


                <p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')}" />
                <h:panelGroup class="ui-inputgroup" style="margin-left:-55%" >
                    <h:selectOneListbox styleClass="sel-salesteam"   style="width: 50%;"   size="2"  id="selSalesTeam">
                        <f:selectItems value="#{lookupService.salesTeams}" var="team"  
                                       itemValue="#{team.smanId}"
                                       itemLabel="#{team.smanName}"  />
                    </h:selectOneListbox>
                    <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.list('SALES_TEAM')}" update=":frmSalesTeamLookup" oncomplete="PF('dlgSalesTeamsLookup').show();" />
                    <p:commandLink styleClass="sel-salesteam" value="Clear" actionListener="#{lookupService.clear('SALES_TEAM')}" update="@(.sel-salesteam)" disabled="#{!(lookupService.salesTeams!=null and lookupService.salesTeams.size() != 0)}" />
                </h:panelGroup>


                <p:outputLabel value="States" />
                <h:panelGroup class="ui-inputgroup" style="margin-left:-55%" >
                    <h:selectOneListbox  styleClass="sel-state"  size="2" style="width: 50%" id="selState"     >
                        <f:selectItems value="#{lookupService.states}" var="state"  
                                       itemValue="#{state.stateId}"
                                       itemLabel="#{state.stateName}"  />
                    </h:selectOneListbox>
                    <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select State" actionListener="#{lookupService.list('STATE')}" update=":frmStateLookup" oncomplete="PF('dlgStateLookup').show();"  />
                    <p:commandLink styleClass="sel-state" value="Clear" actionListener="#{lookupService.clear('STATE')}" update="@(.sel-state)" disabled="#{!(lookupService.states!=null and lookupService.states.size() != 0)}" />
                </h:panelGroup>


            
                <p:column>
                    <p:outputLabel value="File Type" />
                </p:column>

                <p:selectOneRadio id="expType" value="#{commsMain.fileType}" required="true"  requiredMessage="Select any file type" style="font-size:14px;width:200px;margin-left:-55%">
                    <f:selectItem itemLabel="Excel" itemValue="1"/>
                    <f:selectItem itemLabel="CSV" itemValue="2"/>
                </p:selectOneRadio>  

            </p:panelGrid>


            <!--Export-->
            <div class="div-center" >
                <p:commandButton value="Export" 
                                 ajax="false" onclick="PrimeFaces.monitorDownload(start, stop);" styleClass="btn btn-primary btn-xs" actionListener="#{commsMain.exportComms()}"   oncomplete="PF('inProgressDlg').hide();"/>
        
                <p:spacer width="4px" />
            <!--//#2196:Commission Data Overview - Export Post Split -->
<!--          Task#3842-CRM-4032:  smgrep: Post-split not relabelable in import mapping (Comm. Data Over Export)     15-03-2021  by harshithad-->
            <p:commandButton id="postSplitbtn"    value="Export #{custom.labels.get('IDS_POST_SPLIT_AMT')}*"  actionListener="#{commsMain.exportCommsSplit()}" onclick="PrimeFaces.monitorDownload(start, stop);" ajax="#{commsMain.checkDateFrom == null or commsMain.checkDateTo == null}"   styleClass="btn btn-primary btn-xs"   oncomplete="  PF('inProgressDlg').hide();" rendered="#{commsMain.flag == 1}" title="Filtered only for Check Dates" />
              <br/>
            <p:outputLabel  value=" *Filtered only for Check Dates" style="float: right ; font-size:12px" rendered="#{commsMain.flag == 1}"   ></p:outputLabel>

            </div>

           
            <!--Bug 1711-->
          



        </h:form>
    </p:dialog>
       <p:dialog widgetVar="inProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
        <h:form>
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif"   />
                <p:spacer width="5" />
                <p:outputLabel value="Please wait...Exporting records" />
                <br /><br />
                <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
            </p:outputPanel>
        </h:form>
    </p:dialog>
    <ui:include src="../lookup/CompaniesLookup.xhtml" />
    <ui:include src="../lookup/SalesTeamsLookup.xhtml" />
    <ui:include src="../lookup/StatesLookup.xhtml" />

    <!--        <ui:include src="lookup/CustomersLookUpDlg.xhtml" />
            <ui:include src="lookup/SalesTeamsLookUpDlg.xhtml" />
            <ui:include src="lookup/StateLookUpDlg.xhtml" />-->
       <script type="text/javascript">
        function start() {
          
            PF('inProgressDlg').show();
        }
        function stop() {
        
            PF('inProgressDlg').hide();
        }
    </script>
</ui:composition>