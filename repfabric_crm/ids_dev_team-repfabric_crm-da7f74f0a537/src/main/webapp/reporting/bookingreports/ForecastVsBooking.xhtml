<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: CustSummary.xhtml
// Author: priyadarshini
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved .
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 

    <ui:define name="meta">
        <f:metadata> 
            <f:viewAction action="#{salesReports.resetFilters()}"/>
        </f:metadata>
    </ui:define>

    <h:form id="custSummaryForm">
        <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="custSummaryForm:inputPrincName  :custSummaryForm:viewreport"  />
        <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="custSummaryForm:inputAllSalesTeam :custSummaryForm:viewreport" />


        <div class="box box-info box-body" style="vertical-align: top;min-height: 529px;" >
            <h4 style="margin-top:-10px">  Forecast Vs Booking : Report Filter</h4>
            <div class="box-header with-border">
                <div class="row">
                    <div class="col-md-5">

                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                     style="min-height: 50px;margin-top:20px;width: 110%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >
                            <p:outputLabel  value="As of "   />

                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}"  >
                               <f:selectItem itemLabel="#{reports.repYear+1}" itemValue="#{reports.repYear+1}" />
                                <f:selectItem itemLabel="#{reports.repYear+2}" itemValue="#{reports.repYear+2}" />
                                <f:selectItem itemLabel="#{reports.repYear+3}" itemValue="#{reports.repYear+3}" />
                          
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <!--#330952 - THORSON: 2014 doesn't show - Added 10 years behind-->
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />

                            </p:selectOneMenu>
                            <!--<p:outputLabel  value="As of Month"   />-->
                            <div></div>
                            <p:selectOneMenu height="340"   widgetVar="mt" value="#{reportFilters.repMonth}"  >
                                <p:ajax     event="change"  update=":custSummaryForm:viewreport"/>
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 
                                <!--<p:ajax event="select" update=":frmDash"/>--> 
                            </p:selectOneMenu>
                            <!-- <p:outputLabel  value="Year"   />
                            
                                        <p:selectOneMenu widgetVar="toyr" value="#{reports.repYear}" style="width: 20%;margin-left: -15px"  >
                                            <p:ajax     event="change"  />
                                            <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                            <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                            <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                            <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                            #330952 - THORSON: 2014 doesn't show - Added 10 years behind
                                            <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                            <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                            <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                            <f:selectItem itemLabel="#{reports.repYear-7}" itemValue="#{reports.repYear-7}" />
                                            <f:selectItem itemLabel="#{reports.repYear-8}" itemValue="#{reports.repYear-8}" />
                                            <f:selectItem itemLabel="#{reports.repYear-9}" itemValue="#{reports.repYear-9}" />
                                            <f:selectItem itemLabel="#{reports.repYear-10}" itemValue="#{reports.repYear-10}" />
                            
                                        </p:selectOneMenu>
                              <p:outputLabel  value="Month"   />
                                        
                                        <p:selectOneMenu height="340"   widgetVar="tomt" value="#{reports.repMonth}" style="width: 90%;margin-left: -15px" >
                                            <p:ajax     event="change"  />
                                                    <f:selectItem itemLabel="January" itemValue="1" />
                                                    <f:selectItem itemLabel="February" itemValue="2" />
                                                    <f:selectItem itemLabel="March" itemValue="3" />
                                                    <f:selectItem itemLabel="April" itemValue="4" /> 
                                                    <f:selectItem itemLabel="May" itemValue="5" /> 
                                                    <f:selectItem itemLabel="June" itemValue="6" /> 
                                                    <f:selectItem itemLabel="July" itemValue="7" /> 
                                                    <f:selectItem itemLabel="August" itemValue="8" /> 
                                                    <f:selectItem itemLabel="September" itemValue="9" /> 
                                                    <f:selectItem itemLabel="October" itemValue="10" /> 
                                                    <f:selectItem itemLabel="November" itemValue="11" /> 
                                                    <f:selectItem itemLabel="December" itemValue="12" /> 
                                                    <p:ajax event="select" update=":frmDash"/> 
                                                </p:selectOneMenu>-->
                            <!--                        </p:panelGrid>
                            
                                                    <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                                                 style="min-height: 50px;margin-top:20px;width: 150%"       layout="grid" styleClass="box-primary no-border ui-fluid "  >-->

                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                    <!--#3473:     Bug sales team - Sales team data we can enter manually-->
                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  style="width:67px"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <p:outputLabel value="Group by"/>

                            <p:selectOneRadio value="#{bookingReports.repGrp}"  widgetVar="grp" layout="pageDirection"  required="true">
                                <f:selectItem itemValue="0" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}"/>
                                <f:selectItem   itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/>                                   
                            </p:selectOneRadio>

                            <p:outputLabel value="#{custom.labels.get('IDS_OPP')} Value"/>

                            <p:selectOneRadio value="#{bookingReports.repProjGrp}"  widgetVar="grProj" layout="pageDirection"  required="true">
                                <f:selectItem itemValue="0" itemLabel="Actual $ Value"/>
                                <f:selectItem   itemValue="1" itemLabel="Weighted ($ Value * Confidence %)"/>                                   
                            </p:selectOneRadio>

                            <p:outputLabel value="Report Type"/>

                            <p:selectOneRadio  widgetVar="reportType" layout="pageDirection"  value="#{bookingReports.reportFlag}" required="true">
                                <f:selectItem itemValue="0" itemLabel="Monthly"/>    
                                <f:selectItem itemValue="1" itemLabel="Quarterly"/>                     
                            </p:selectOneRadio>



                        </p:panelGrid>



                        <p:commandButton  value="View Report "  id="viewreport"   styleClass="btn btn-primary btn-xs"  actionListener="#{bookingReports.gotTonavigationpage()}" />

                    </div>
                </div>


            </div>
        </div>
    </h:form>
    <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
    <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
</ui:composition>