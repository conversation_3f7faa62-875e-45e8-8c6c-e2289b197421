<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">

    <p:panel>

        <h:panelGrid columns="5" style="padding-left: 10px;">
            <!--Select year-->
            <p:outputLabel value="Forecast Year"/>
            <p:outputLabel styleClass="m" value=":" />
            <p:selectOneMenu value="#{forecast.selectedForecastYear}" style="width: 250px" id="yearSelect" >   
                <f:selectItem itemLabel="#{forecast.currentYear}" itemValue="#{forecast.currentYear}" />
                <f:selectItem itemLabel="#{forecast.currentYear+1}" itemValue="#{forecast.currentYear+1}" />
                <f:selectItem itemLabel="#{forecast.currentYear+2}" itemValue="#{forecast.currentYear+2}" />
                <f:selectItem itemLabel="#{forecast.currentYear+3}" itemValue="#{forecast.currentYear+3}" />
                <p:ajax event="change" process="@this" listener="#{forecast.onSelectSalesTam()}" update=":importForm"  />
            </p:selectOneMenu>  
            <h:panelGroup></h:panelGroup>
            <h:panelGroup></h:panelGroup>

            <!--Select sales team-->
            <p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')}"/>
            <p:outputLabel styleClass="m" value=":" />
            <p:selectOneMenu filter="true" widgetVar="sman5"  value="#{forecast.forecastSmanId}" style="width: 250px">   
                <f:selectItem itemLabel="None" itemValue="0"/>
                <f:selectItems var="sman" value="#{salesmenMst.getLinkedSalesmenList(0)}"  itemLabel="#{sman.smanName}" itemValue="#{sman.smanId}" />
                <p:ajax event="change" listener="#{forecast.onSelectSalesTam()}" update=":importForm" />
            </p:selectOneMenu>  
            <h:panelGroup></h:panelGroup>
            <h:panelGroup></h:panelGroup>

            <!--Fetch Principals-->
            <h:panelGroup></h:panelGroup>
            <h:panelGroup></h:panelGroup>
            <p:commandButton id="fetchBtn" value="Fetch #{custom.labels.get('IDS_PRINCIS')}" actionListener="#{forecast.initFilter()}" update=":filterPrinForm" oncomplete="PF('dlgFilterPrinc').show()" disabled="#{forecast.forecastSmanId ==0}"/>
            <p:ajaxStatus style="display:inline">
                <f:facet name="start">
                    <h:graphicImage  library="images" name="ajax-loader.gif" />                
                </f:facet>
                <f:facet name="success">
                </f:facet>
            </p:ajaxStatus>
            <h:panelGroup style="float:right" >
                <h:outputLabel value="Filter #{custom.labels.get('IDS_CUSTOMER')}: " /><p:spacer/>
                <p:inputText  value="#{forecast.custSearchStr}" id="srch_str" disabled="#{forecast.forecastSmanId == 0}">
                </p:inputText>
                <p:spacer/>
                <p:commandButton class="btnlukup" icon="ui-icon-search" action="#{forecast.populateForecasts()}" update=":importForm" disabled="#{forecast.forecastSmanId == 0}"/>
                <p:commandButton class="btnlukup" id="cancel" icon="ui-icon-close" action="#{forecast.resetFilter()}" update=":importForm" disabled="#{forecast.forecastSmanId == 0}" />
            </h:panelGroup>
        </h:panelGrid>


        <style>
            .input-width{
                width: 95px;
            }

            .cust-bc .ui-widget-header{
                color:red!important;
                background:#fff !important;
            }

            .cust-bc {
                width:100%;
                color: #190382!important;
                font-weight:normal!important;
                text-shadow: none!important;
                text-align: center;
            }

            td.ui-datatable-subtable-header{
                background:#e9f4f3!important;
            }

        </style>

        <!--Used for Add new Forecast-->
        <p:remoteCommand name="prepare" actionListener="#{forecast.prepareDlg}" autoRun="false"   />

        <p:dataTable id="forecastDatalist" value="#{forecast.custForecastList}" var="cust" rowKey="#{cust.custId}"  paginatorAlwaysVisible="false" paginator="true" rows="50" rowsPerPageTemplate="30,50,100, 150, 200"  >
            <p:columnGroup type="header">
                <p:row>
                    <p:column headerText=" #{custom.labels.get('IDS_PRINCI')}" style="width: 40%">
                    </p:column>      
                    <p:column headerText="Q1" style="width: 10%">
                    </p:column>
                    <p:column headerText="Q2" style="width: 10%">
                    </p:column>
                    <p:column headerText="Q3" style="width: 10%">
                    </p:column>
                    <p:column headerText="Q4" style="width: 10%">
                    </p:column>
                    <p:column headerText="Yearly Total" style="width: 10%">
                    </p:column>
                    <p:column headerText="" style="width: 8%">
                    </p:column>
                </p:row>
            </p:columnGroup>

            <p:subTable  value="#{cust.forecastList}" var="fcRecord" class="test">
                <f:facet name="header">
                    <p:commandButton 
                        icon="ui-icon-plus"
                        class="btnlukup"
                        onclick="prepare([{name: 'custid', value: '#{cust.custId}'}]);"
                        style="height: 20px!important;width:20px;float: right;"
                        title="Add Forecast"
                        >
                    </p:commandButton>
                    <center><p:outputLabel value="#{cust.custName}" class="cust-bc"/></center>
                </f:facet>
                <p:column style="width: 40%" >
                    <p:outputLabel value="All #{custom.labels.get('IDS_PRINCIS')}" rendered="#{fcRecord.forecastPrinciId == 0}"/>
                    <p:outputLabel value="#{fcRecord.getPrinciName(fcRecord.forecastPrinciId)}" rendered="#{fcRecord.forecastPrinciId != 0}"/>

<!--<p:outputLabel value="#{fcRecord.princiName}"/>-->
                </p:column>      
                <p:column style="width: 10%">
                    <p:outputLabel value="#{fcRecord.forecast1}" >
                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0" groupingUsed="false" />
                    </p:outputLabel>
                </p:column>
                <p:column style="width: 10%">
                    <p:outputLabel value="#{fcRecord.forecast2}"  >
                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0" groupingUsed="false" />
                    </p:outputLabel>
                </p:column>
                <p:column style="width: 10%">
                    <p:outputLabel value="#{fcRecord.forecast3}"  >
                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0" groupingUsed="false" />
                    </p:outputLabel>
                </p:column>
                <p:column style="width: 10%">
                    <p:outputLabel value="#{fcRecord.forecast4}"  >
                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0" groupingUsed="false" />
                    </p:outputLabel>
                </p:column>
                <p:column style="width: 10%">
                    <p:outputLabel value="#{fcRecord.forecastTotal}"  >
                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0" groupingUsed="false" />
                    </p:outputLabel>
                </p:column>
                <p:column>
                    <p:commandButton style="height: 20px!important;width:20px;" 
                                     icon="ui-icon-pencil" 
                                     actionListener="#{forecast.edit(fcRecord, cust)}" 
                                     update=":fcForm" oncomplete="PF('dlgFc').show()"
                                     title="Edit Forecast"/>
                    <p:spacer width="5" />
                    <p:commandButton icon="ui-icon-trash" action="#{forecast.prepareConfirmDelete(fcRecord.recId)}" 
                                     style="height: 20px!important;width:20px;float:right"
                                     title="Delete Forecast">
                    </p:commandButton>
                </p:column>
            </p:subTable>
        </p:dataTable>
    </p:panel>
</ui:composition>
