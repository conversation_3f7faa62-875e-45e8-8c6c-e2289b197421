<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui">
    <h:form id="fcForm">
        <p:dialog header="#{forecast.custName}" 
                  widgetVar="dlgFc"
                  resizable="false"
                  modal="true">
            <p:remoteCommand name="tc" actionListener="#{forecast.calculateForecast()}" process="total" update="forecast1 forecast2 forecast3 forecast4"  autoRun="false" />
            <h:panelGrid id="fcUpdate" columns="3" cellpadding="5" cellspacing="5">
                <f:facet name="header">
                    <h:outputText value="Please enter forecast entries below"/>
                </f:facet>

                <h:outputText value="#{custom.labels.get('IDS_PRINCI')}"  />
                <p:outputLabel styleClass="m" value=":" />
                <p:selectOneMenu filter="true"  height="350" widgetVar="prin4"  value="#{forecast.forecastPrinciId}" style="width:250px" >      
<!--                    <f:selectItem itemLabel="All" itemValue="0"/>-->
                    <f:selectItems var="prin" value="#{viewPrincipalMst.queryPrincipals('')}"  itemLabel="#{prin.princiName}" itemValue="#{prin.princiId}" />
                    <p:ajax event="change" listener="#{forecast.onPrincipalChange()}" update="lastInvcDate1 totalSales"  />
                </p:selectOneMenu> 

                <h:outputText value="Last Invoice for #{forecast.currentYear-1}"  />
                <p:outputLabel styleClass="m" value=":" />
                <p:outputLabel id="lastInvcDate1" value="#{forecast.convertDate(forecast.lastInvcDate)}" style="width:250px"   >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}"  />
                </p:outputLabel>

                <h:outputText value="Total Sales for #{forecast.currentYear-1}"   />
                <p:outputLabel styleClass="m" value=":" />
                <p:outputLabel id="totalSales" value="#{forecast.totalLastSales}" style="width:250px"   >
                    <f:convertNumber maxFractionDigits="0" minFractionDigits="0" groupingUsed="false" />
                </p:outputLabel>

                <h:outputText value="Q1"  />
                <p:outputLabel styleClass="m" value=":" />
                <p:inputText widgetVar="q1" id="forecast1" value="#{forecast.forecast1}" style="width:250px"   >
                    <f:convertNumber maxFractionDigits="0" minFractionDigits="0" groupingUsed="false" />
                    <p:ajax event="blur" process="forecast1" update="total" listener="#{forecast.calculateTotal()}" />
                </p:inputText>

                <h:outputText value="Q2"  />
                <p:outputLabel styleClass="m" value=":" />
                <p:inputText widgetVar="q2" id="forecast2" value="#{forecast.forecast2}" style="width:250px" >
                    <f:convertNumber maxFractionDigits="0" minFractionDigits="0" groupingUsed="false" />
                    <p:ajax event="blur" process="forecast2" update="total" listener="#{forecast.calculateTotal()}" />
                </p:inputText>

                <h:outputText value="Q3"  />
                <p:outputLabel styleClass="m" value=":" />
                <p:inputText widgetVar="q3" id="forecast3" value="#{forecast.forecast3}" style="width:250px"   >
                    <f:convertNumber maxFractionDigits="0" minFractionDigits="0" groupingUsed="false" />
                    <p:ajax event="blur" process="forecast3" update="total" listener="#{forecast.calculateTotal()}" />
                </p:inputText>

                <h:outputText value="Q4"  />
                <p:outputLabel styleClass="m" value=":" />
                <p:inputText widgetVar="q4" id="forecast4" value="#{forecast.forecast4}" style="width:250px" >
                    <f:convertNumber maxFractionDigits="0" minFractionDigits="0" groupingUsed="false" />
                    <p:ajax event="blur" process="forecast4" update="total" listener="#{forecast.calculateTotal()}" />
                </p:inputText>

                <h:outputText value="Yearly Total"  />
                <p:outputLabel styleClass="m" value=":" />
                <p:inputText widgetVar="fTotal" id="total" value="#{forecast.forecastTotal}" style="width:250px" onfocus="setIntialValue()" onblur="processFinalValue()" >
                    <f:convertNumber maxFractionDigits="0" minFractionDigits="0" groupingUsed="false" />
                    <!--#114165 - Customer Forecast - tabbing breaks calculation: Changing logic to use javascript functions.-->
                    <!--Update quarter fields based on Total only if Total is changed from initial value-->
                    <!--<p:ajax event="change" process="total" update="forecast1 forecast2 forecast3 forecast4" listener="#{forecast.calculateForecast()}" />-->
                </p:inputText>
            </h:panelGrid>

            <h:panelGroup id = "updateFC" style="display: block;text-align: center">
                <p:commandButton value="Save" action="#{forecast.saveToDB()}" oncomplete="PF('dlgFc').hide()" update=":importForm"/>
                <p:spacer width="5" />
                <p:commandButton value="Cancel" onclick="PF('dlgFc').hide()"/>
            </h:panelGroup>

        </p:dialog>
    </h:form>
    <h:form>
        <p:confirmDialog widgetVar="deleteSplitPrinciDlg" header="Delete Plan Confirmation" >
            <f:facet name="message">
                <p:outputLabel value="There are forecast entries for mulitple #{custom.labels.get('IDS_PRINCIS')}. Are you sure to delete them and the new entry ?" />
            </f:facet>
            <p:commandButton value="Yes" action="#{forecast.deletePrinciForecast(1)}" oncomplete="PF('deleteSplitPrinciDlg').hide();" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" update=":importForm" />
            <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" onclick="PF('deleteSplitPrinciDlg').hide();" />

        </p:confirmDialog>
    </h:form>

    <h:form>
        <p:confirmDialog widgetVar="deleteAllPrinciDlg" header="Delete Plan Confirmation" >
            <f:facet name="message">
                <p:outputLabel value="There are forecast entry for All #{custom.labels.get('IDS_PRINCIS')}. Are you sure to delete that and the new entry ?" />
            </f:facet>
            <p:commandButton value="Yes" action="#{forecast.deletePrinciForecast(2)}" oncomplete="PF('deleteAllPrinciDlg').hide();" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" update=":importForm" />
            <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" onclick="PF('deleteAllPrinciDlg').hide();" />
        </p:confirmDialog>
    </h:form>

    <h:form id="conConfirmDialogForm">
        <p:confirmDialog id="cdConDelete" showEffect="fade" widgetVar="delForecastDlg" header="Forecast Deletion Confirmation">
            <f:facet name="message">
                <p:outputLabel value="Are you sure to delete this forecast?" />
            </f:facet>
            <p:commandButton value="Yes" action="#{forecast.delete()}" update=":importForm" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" oncomplete="PF('delForecastDlg').hide()" />
            <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" onclick="PF('delForecastDlg').hide()" />
        </p:confirmDialog>
    </h:form>
    <!--#114165 - Customer Forecast - tabbing breaks calculation-->
    <script type="text/javascript" >
        var newValue, oldValue;
        function setIntialValue() {
            oldValue = PF('fTotal').jq.val();
        }
        function processFinalValue() {
            newValue = PF('fTotal').jq.val();

            if (oldValue !== newValue) {
                var qValue = newValue / 4;
                PF('q1').jq.val(qValue);
                PF('q2').jq.val(qValue);
                PF('q3').jq.val(qValue);
                PF('q4').jq.val(qValue);
            }
        }
    </script>

</ui:composition>