<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui">
    <h:form id="filterPrinForm">
        <p:dialog header="Fetch #{custom.labels.get('IDS_PRINCIS')} Based On " 
                  widgetVar="dlgFilterPrinc"
                  resizable="false"
                  modal="true">
            <h:panelGrid id="filterPrinGrid" columns="3" cellpadding="5" cellspacing="5">
                <h:outputText value="Sales for Year"  />
                <p:outputLabel styleClass="m" value=":" />
                <p:selectOneMenu value="#{forecast.salesYear}" style="width: 250px" id="yearSelect" >   
                    <f:selectItem itemLabel="#{forecast.salesYear}" itemValue="#{forecast.salesYear}" />
                    <f:selectItem itemLabel="#{forecast.salesYear-1}" itemValue="#{forecast.salesYear-1}" />
                    <f:selectItem itemLabel="#{forecast.salesYear-2}" itemValue="#{forecast.salesYear-2}" />
                    <f:selectItem itemLabel="#{forecast.salesYear-3}" itemValue="#{forecast.salesYear-3}" />
                </p:selectOneMenu>  

                <h:outputText value="Minimum Total Sales"  />
                <p:outputLabel styleClass="m" value=":" />
                <p:inputText id="forecast1" value="#{forecast.minSalAmt}" style="width:250px"   >
                    <f:convertNumber maxFractionDigits="0" minFractionDigits="0" groupingUsed="false" />
                </p:inputText>
            </h:panelGrid>

            <h:panelGroup id = "filterPrin" style="display: block;text-align: center">
                <p:commandButton value="Fetch" action="#{forecast.populatePrinci()}" oncomplete="PF('dlgFilterPrinc').hide()" update=":importForm"/>
                <p:spacer width="5" />
                <p:commandButton value="Cancel" onclick="PF('dlgFilterPrinc').hide()"/>
            </h:panelGroup>
        </p:dialog>
    </h:form>
</ui:composition>

