<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: CustomerSummary.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <!--#363 :page title.-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>   Forecast Vs Booking  </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata> 
            <f:viewParam name="yr" value="#{bookingReports.repYear}" />
            <f:viewParam name="mt" value="#{bookingReports.repMonth}"/>
            <f:viewParam name="p" value="#{bookingReports.repPrinci}"/>
            <f:viewParam name="c" value="#{bookingReports.repCust}"/>
            <f:viewParam name="s" value="#{bookingReports.repSman}"/>
            <f:viewParam name="g" value="#{bookingReports.repGrp}"/>
            <f:viewParam name="proj" value="#{bookingReports.repProjGrp}"/>
            <f:viewParam name="flag" value="#{bookingReports.reportFlag}"/>
<!--            <f:viewAction action="#{bookingReports.initFilters()}"/>-->
            <f:viewAction action="#{bookingReports.displayReport()}"/>
        </f:metadata>
    </ui:define>
    #{bookingReports.reportFlag} 
    <ui:define name="body">

        <f:event listener="#{leftBooking.isPageAccessible}" type="preRenderView"/>  


        <div class="box box-info box-body" style="vertical-align: top">  
            <h:form id="bookreport">
                <p:remoteCommand name="applyPrinci" actionListener="#{reportFilters.applyPrincipal(viewCompLookupService.selectedCompany)}" update="bookreport:inputPrincName"  />
                <p:remoteCommand name="applyAllSalesTeam" actionListener="#{reportFilters.applySalesTeam(salesTeamLookupService.salesTeam)}" update="bookreport:inputAllSalesTeam" />
                <!--<p:remoteCommand name="onload" autoRun="true" process="@this" immediate="true" onstart="PF('statusDialog').show()"   oncomplete="PF('statusDialog').hide()"   />-->

                <!--                <p:dialog widgetVar="statusDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
                                    <h:graphicImage  library="images" name="progressicon.gif" height="100px" width="100px"/>
                                </p:dialog>-->
                <p:dialog header="Please wait" draggable="false"  widgetVar="dlgBookj" modal="true" closable="false" resizable="false" >
                    <h:graphicImage  library="images" name="progressicon.gif" height="50px" width="100px"/>
                    <p:spacer width="5"/>
                    <!--<h:outputLabel value="Loading data.."/>-->
                </p:dialog>




                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >


                        <div class="compNamebg">
                            <h:outputLabel value="  #{loginBean.subscriber_name} "   />
                            <br/>
                            Forecast Vs Booking  
                            <br/>

                        </div>
                        <b>  As of : </b><p:outputLabel value="#{reportFilters.repYear}" id="year"/><p:outputLabel value="-" /> <p:outputLabel value="#{reportFilters.getMonth(reportFilters.repMonth )}" id="month"/>
                        <h:panelGrid columns="1" style="width: 100%">
                            <p:selectOneRadio  widgetVar="grp" layout="pageDirection"  value="#{bookingReports.reportFlag}" required="true">
                                <f:selectItem itemValue="0" itemLabel="Monthly"/>
                                <f:selectItem  itemValue="1" itemLabel="Quarterly"/>                                   
                                <ui:include src="filter.xhtml"/>
                                <!--<p:ajaxStatus onstart="PF('statusDialog').show()" onsuccess="PF('statusDialog').hide()" />-->
                                <p:ajax onstart="PF('dlgBookj').show();"     oncomplete="PF('dlgBookj').hide()" />
                            </p:selectOneRadio>
                        </h:panelGrid>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >

                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >

                        <h:panelGrid columns="1" style="width: 100%">
                            <table style="width: 100%;">
                                <tr style="background-color: #CAC9C9;">
                                    <th>Group By</th>
                                </tr>
                            </table>
                            <p:selectOneRadio  widgetVar="grp" layout="pageDirection" value="#{bookingReports.repGrp}"  required="true">
                                <f:selectItem itemValue="0" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}"/>
                                <f:selectItem   itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/>                                   
                                <ui:include src="filter.xhtml"/>
                                <!--Bug #2735: CRM-1726: Fwd: RE: Forecasts-->
                                <p:ajax onstart="PF('dlgBookj').show();"     oncomplete="PF('dlgBookj').hide()" />
                            </p:selectOneRadio>
                        </h:panelGrid>


                        <table style="width: 100%;">
                            <tr style="background-color: #CAC9C9;">
                                <th>#{custom.labels.get('IDS_OPP')} Value</th>
                            </tr>
                        </table>
                        <h:panelGrid columns="1" style="width: 100%">
                            <p:selectOneRadio layout="pageDirection" widgetVar="grProj" value="#{bookingReports.repProjGrp}"  required="true">
                                <f:selectItem itemValue="0" itemLabel="Actual $ Value"/>
                                <f:selectItem   itemValue="1" itemLabel="Weighted ($ Value * Confidence %)"/>                                   
                                <ui:include src="filter.xhtml"/>
                                <!--Bug #2735: CRM-1726: Fwd: RE: Forecasts-->
                                <p:ajax onstart="PF('dlgBookj').show();"     oncomplete="PF('dlgBookj').hide()" />
                            </p:selectOneRadio>
                        </h:panelGrid>

                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                        <h:panelGrid columns="2" style="width: 100%" >
                            <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
<!--                                    <p:inputText  value="#{reportFilters.pricipal.compId}" type="hidden" id="inputPrincId" readonly="true" style="width: 20px"/>-->
                                <!--                                    <p:spacer width="50px"/>-->
                                <p:inputText id="inputPrincName" value="#{reportFilters.pricipal.compName}"  placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                            <p:outputLabel for="inputAllSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--#3473:     Bug sales team - Sales team data we can enter manually-->
                                <p:inputText id="inputAllSalesTeam" value="#{reportFilters.salesTeam.smanName}" placeholder="All" readonly="true"/>
                                <p:commandButton  icon="fa fa-search"  immediate="true" 
                                                  actionListener="#{salesTeamLookupService.list('applyAllSalesTeam',1)}"  
                                                  update=":formSalesTeamLookup" oncomplete="PF('lookupSalesTeam').show()"  
                                                  styleClass="btn-info btn-xs"  style="width:67px" />
                            </h:panelGroup>

                            <p:outputLabel value="Year"/>   
                            <p:outputLabel value="Month"/>  
                            <!--            Year-->
                            <p:selectOneMenu widgetVar="yr" value="#{reportFilters.repYear}" style="width: 100%">
                                <f:selectItem itemLabel="#{reports.repYear-6}" itemValue="#{reports.repYear-6}" />
                                <f:selectItem itemLabel="#{reports.repYear-5}" itemValue="#{reports.repYear-5}" />
                                <f:selectItem itemLabel="#{reports.repYear-4}" itemValue="#{reports.repYear-4}" />
                                <f:selectItem itemLabel="#{reports.repYear-3}" itemValue="#{reports.repYear-3}" />
                                <f:selectItem itemLabel="#{reports.repYear-2}" itemValue="#{reports.repYear-2}" />
                                <f:selectItem itemLabel="#{reports.repYear-1}" itemValue="#{reports.repYear-1}" />
                                <f:selectItem itemLabel="#{reports.repYear}" itemValue="#{reports.repYear}" />
                                <f:selectItem itemLabel="#{reports.repYear+1}" itemValue="#{reports.repYear+1}" />
                                <f:selectItem itemLabel="#{reports.repYear+2}" itemValue="#{reports.repYear+2}" />
                                <f:selectItem itemLabel="#{reports.repYear+3}" itemValue="#{reports.repYear+3}" />
                                <!--Bug #2735: CRM-1726: Fwd: RE: Forecasts-->

                                <!--<ui:include src="filter.xhtml"/>-->
                                <!--<p:ajax onstart="PF('dlgBookj').show();"  update=":bookreport:year :bookreport:month"   oncomplete="PF('dlgBookj').hide()" />-->
                            </p:selectOneMenu>
                            <!--            Month-->
                            <p:selectOneMenu height="340"   widgetVar="mt" value="#{reportFilters.repMonth}" style="width: 100%">
                                <f:selectItem itemLabel="January" itemValue="1" />
                                <f:selectItem itemLabel="February" itemValue="2" />
                                <f:selectItem itemLabel="March" itemValue="3" />
                                <f:selectItem itemLabel="April" itemValue="4" /> 
                                <f:selectItem itemLabel="May" itemValue="5" /> 
                                <f:selectItem itemLabel="June" itemValue="6" /> 
                                <f:selectItem itemLabel="July" itemValue="7" /> 
                                <f:selectItem itemLabel="August" itemValue="8" /> 
                                <f:selectItem itemLabel="September" itemValue="9" /> 
                                <f:selectItem itemLabel="October" itemValue="10" /> 
                                <f:selectItem itemLabel="November" itemValue="11" /> 
                                <f:selectItem itemLabel="December" itemValue="12" /> 
                                <!--Bug #2735: CRM-1726: Fwd: RE: Forecasts-->
                                <!--<ui:include src="filter.xhtml"/>-->
                                <!--<p:ajax onstart="PF('dlgBookj').show();"     oncomplete="PF('dlgBookj').hide()" />-->
                            </p:selectOneMenu>

                            <p:commandButton  value="Run Report "  id="viewreport"  style="width:30px"  onclick="PF('dlgBookj').show();"  styleClass="btn btn-primary btn-xs"      actionListener="#{bookingReports.refreshReport()}" update=":bookreport"  oncomplete="PF('dlgBookj').hide()"/>
                        </h:panelGrid>    


                    </div>
                </div>

                <ui:include src="BookingSplitDlg.xhtml"/>
                <ui:include src="MonthlyBookingSplitDlg.xhtml"/>
                <ui:include src="MonthlyReport.xhtml"/>
                <ui:include src="QuarterlyReport.xhtml"/>
            </h:form>
            <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
            <ui:include src="/lookup/SalesTeamLookupDlg.xhtml"  />
            <style>

                .ui-panelgrid .ui-panelgrid-cell {
                    padding-top: 1px !important;
                    padding-bottom: 1px !important;
                }

                [role="gridcell"]{

                    padding: 0px 10px !important;
                }
                .content-header{     display: none; }


                .jqplot-target {

                    font-size: 0.75em!important;
                }

                .header-label{
                    font-size: 1.3em;
                    text-decoration: underline!important;
                    font-weight: bold;
                    line-height: 25px;
                    padding-left: 5px;
                }

                /*2627 - Related to CRM-1177: Doran associates: Remove link from printed reports - sharvani - 11-12-2019*/
                @media print {
                    a[href]:after {
                        content: none !important;
                    }
                }


            </style> 
        </div>
    </ui:define>
</ui:composition>