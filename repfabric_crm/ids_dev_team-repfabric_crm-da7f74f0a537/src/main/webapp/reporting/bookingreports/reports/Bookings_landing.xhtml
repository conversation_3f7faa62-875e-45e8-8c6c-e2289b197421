<!--//**********************************************************
 Program: RepFabric Sys
 Filename: Bookings_report.xhtml
 Author: <PERSON><PERSON> R

* Copyright (c) 2015 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition template="/WEB-INF/templates/LayoutHome.xhtml" 
                xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

    <ui:define name="meta">
        <f:metadata>
<!--            <f:viewAction action="#{salesReports.prepareList()}"/>-->
        </f:metadata>
    </ui:define>

    <ui:define name="title">
        Booking Reports    
    </ui:define>

    <ui:define name="top_menu">
        <ui:include src="/Dash_Menu.xhtml" />
    </ui:define>

    <ui:define name="PageHeader">
        <h1>Booking Reports</h1>                
    </ui:define>

    <ui:define name="leftpanel">               
        <ui:include src="LeftBookingReports.xhtml" />                
    </ui:define>

    <ui:define name="slogan">

    </ui:define>

    <ui:define name="content"> 
        <ui:include src="/dashboard/SalesManLookupDlg.xhtml"/>
        <ui:include src="/dashboard/PrincipalLookupDlg.xhtml"/>

        <h:form id="frmDash">   
            <ui:include src="#{reports.page}"/>  
        </h:form>
    </ui:define>


</ui:composition>
