<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">


    <!--        <h:commandLink value="Export to Excel">
                <pe:exporter type="xls" target="frmRep:tableBookingGroup" fileName="Booking_Report" subTable="true"/>
                <p:dataExporter type="xls" target="frmRep:tableBookingGroup" fileName="Booking_Report"/>
            </h:commandLink>-->

    <!--<p:sticky target=":frmRep:tableBookingGroup:tableHeader"/>-->

    <p:dataTable styleClass="#{bookingReports.listEmptyFlag?'':'hide-table-header'}"  value="#{bookingReports.bookingsReportList}" var="booking" id="tableBookingGroup" rendered="#{bookingReports.reportFlag==1}">
        <p:columnGroup type="header" id="tableHeader">
            <p:row>
                <p:column rowspan="2" style="width: 140px"
                          headerText="#{custom.labels.get('IDS_CUSTOMER')}" class="col_left" />
                <p:column rowspan="2" style="width: 140px" rendered="#{bookingReports.repGrp eq 0}"
                          headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left"/> 
                <p:column rowspan="2" rendered="#{bookingReports.repGrp eq 1}"  style="width: 140px"
                          headerText="#{custom.labels.get('IDS_SALES_TEAM')}" class="col_left"/> 
                <p:column colspan="3" headerText="#{bookingReports.quarters[1]}"/>
                <p:column colspan="3" headerText="#{bookingReports.quarters[2]}"/>              
                <p:column colspan="3" headerText="#{bookingReports.quarters[3]}"/>    
                <p:column colspan="3" headerText="#{bookingReports.quarters[4]}"/>    
                <p:column colspan="3" headerText="Year #{bookingReports.yearLabel}"  style="min-width: 47px" class="col_right" />              
            </p:row>

            <p:row>
                <p:column headerText="FC"/>
                <p:column headerText="Bk+Inv"/>
                <p:column headerText="%"/>
                <p:column headerText="FC"/>
                <p:column headerText="Bk+Inv"/>
                <p:column headerText="%"/>
                <p:column headerText="FC"/>
                <p:column headerText="Bk+Inv"/>
                <p:column headerText="%"/>
                <p:column headerText="FC"/>
                <p:column headerText="Bk+Inv"/>
                <p:column headerText="%"/>
                <p:column headerText="FC"/>
                <p:column headerText="Bk+Inv"/>
                <p:column headerText="%"/>
            </p:row>
        </p:columnGroup>

        <p:subTable value="#{booking.planBookingsReportList}" var="plan">

            <f:facet name="header">
                <div class="div-selected">
                    <p:outputLabel value=" #{booking.smanName}" rendered="#{bookingReports.repGrp eq 0}"/>
                    <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{booking.repPrinci}" target="_self">                          
                        <p:outputLabel style="color: #333;text-decoration: underline" value=" #{booking.princiName}" rendered="#{bookingReports.repGrp eq 1}"/>        
                    </a>
                </div>
            </f:facet>
            <p:column class="col_left" >
                
              <!--#Too many tabs, fixed by replacing _blank to _self -->
                <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{plan.planCustId}" target="_self">                          
                    #{plan.custName}
                </a>
            </p:column>
            <p:column class="col_left" rendered="#{bookingReports.repGrp eq 0}" style="border-right-color: gray!important">
                
                <!--#Too many tabs, fixed by replacing _blank to _self -->
                <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{plan.planPrinciId}" target="_self">                          
                    #{plan.repPrinciName}
                </a>
                <f:facet name="footer">
                    <h:outputLabel value="Total :"  style="float: right" />
                </f:facet>
            </p:column>  
            <p:column class="col_left" rendered="#{bookingReports.repGrp eq 1}" style="border-right-color: gray!important">
                <h:outputLabel id="sname" value=" #{plan.smanName}"/>
                <p:tooltip for="sname" value="#{plan.smanName}"/> 
                <f:facet name="footer">
                    <h:outputLabel value="Total :"  style="float: right" />
                </f:facet>
            </p:column>



            <!--Quarter 1-->
            <p:column class="col_right">
                <h:outputLabel value="#{plan.repPlan1}">
                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                </h:outputLabel> 
            </p:column>
            <p:column class="col_right">
                <p:commandLink actionListener="#{planBookingReport.retrieveBreakup(bookingReports.quarters[1], 2,1,plan)}" update=":bookreport:bookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                    <h:outputText value="#{plan.repBookInv1}">
                        <f:convertNumber  maxFractionDigits="0"  />
                    </h:outputText>
                </p:commandLink>
            </p:column>
            <p:column class="col_right" style="border-right-color: gray!important">
                <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                <h:outputLabel value="#{plan.repPcent1}" style="#{plan.repPcent1 ge 100 ?  'color:green' : (plan.repPcent1 lt 100 ? 'color:red' : 'color:black')}">
                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2" />
                </h:outputLabel> 
            </p:column>




            <!--Quarter 2-->
            <p:column class="col_right">
                <h:outputLabel value="#{plan.repPlan2}">
                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                </h:outputLabel> 
            </p:column>
            <p:column class="col_right">
                <p:commandLink actionListener="#{planBookingReport.retrieveBreakup(bookingReports.quarters[2], 2,2,plan)}" update=":bookreport:bookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                    <h:outputText value="#{plan.repBookInv2}">
                        <f:convertNumber  maxFractionDigits="0"  />
                    </h:outputText>
                </p:commandLink>
            </p:column>
            <p:column class="col_right" style="border-right-color: gray!important">
                <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                <h:outputLabel value="#{plan.repPcent2}" style="#{plan.repPcent2 ge 100 ?  'color:green' : (plan.repPcent2 lt 100 ? 'color:red' : 'color:black')}">
                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2"/>
                </h:outputLabel> 
            </p:column>





            <!--Quarter 3-->
            <p:column class="col_right">
                <h:outputLabel value="#{plan.repPlan3}">
                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                </h:outputLabel> 
            </p:column>
            <p:column class="col_right">
                <p:commandLink actionListener="#{planBookingReport.retrieveBreakup(bookingReports.quarters[3], 2,3,plan)}"  update=":bookreport:bookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                    <h:outputText value="#{plan.repBookInv3}">
                        <f:convertNumber  maxFractionDigits="0"  />
                    </h:outputText>
                </p:commandLink>
            </p:column>
            <p:column class="col_right" style="border-right-color: gray!important">
                <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                <h:outputLabel value="#{plan.repPcent3}" style="#{plan.repPcent3 ge 100 ?  'color:green' : (plan.repPcent3 lt 100 ? 'color:red' : 'color:black')}">
                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2"/>
                </h:outputLabel> 
            </p:column>





            <!--Quarter 4-->
            <p:column class="col_right">
                <h:outputLabel value="#{plan.repPlan4}">
                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                </h:outputLabel>
            </p:column>
            <p:column class="col_right">
                <p:commandLink actionListener="#{planBookingReport.retrieveBreakup(bookingReports.quarters[4], 2,4,plan)}" update=":bookreport:bookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                    <h:outputText value="#{plan.repBookInv4}">
                        <f:convertNumber  maxFractionDigits="0"  />
                    </h:outputText>
                </p:commandLink>
            </p:column>
            <p:column class="col_right" style="border-right-color: gray!important">
                <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                <h:outputLabel value="#{plan.repPcent4}" style="#{plan.repPcent4 ge 100 ?  'color:green' : (plan.repPcent4 lt 100 ? 'color:red' : 'color:black')}">
                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="2" minFractionDigits="2" />
                </h:outputLabel> 
            </p:column>






            <!--Year-->
            <p:column class="col_right">
                <h:outputLabel value="#{plan.repPlanTotal}">
                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                </h:outputLabel>
            </p:column>
            <p:column class="col_right">
                <p:commandLink actionListener="#{planBookingReport.retrieveBreakup(bookingReports.yearLabel, 3,0,plan)}"  update=":bookreport:bookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                    <h:outputText value="#{plan.repBookInvTotal}">
                        <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                    </h:outputText> 
                </p:commandLink>
            </p:column>
            <p:column class="col_right">
                <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                <h:outputLabel value="#{plan.repPcentTotal}" style="#{plan.repPcentTotal ge 100 ?  'color:green' : (plan.repPcentTotal lt 100 ? 'color:red' : 'color:black')}">
                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2"/>
                </h:outputLabel> 
            </p:column>


            <!--subtotal footer-->
            <p:columnGroup type="footer">
                <p:row>
                    <p:column colspan="2" footerText="Total:"  style="border-right-color: gray!important;text-align: right"/>
                    <p:column  class="col_right">
                        <f:facet name="footer">
                            <h:outputLabel value="#{booking.planTotal1}"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <h:outputLabel value="#{booking.bookTotal1}"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column style="border-right-color: gray!important" class="col_right" >
                        <f:facet name="footer">
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                            <h:outputLabel value="#{booking.pcentTotal1}"  class="footerFont" style="#{booking.pcentTotal1 ge 100 ?  'color:green' : (booking.pcentTotal1 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column>
                        <f:facet name="footer" class="col_right">
                            <h:outputLabel value="#{booking.planTotal2}"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column>
                        <f:facet name="footer" class="col_right">
                            <h:outputLabel value="#{booking.bookTotal2}"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column style="border-right-color: gray!important" class="col_right">
                        <f:facet name="footer"  >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                            <h:outputLabel value="#{booking.pcentTotal2}"  class="footerFont" style="#{booking.pcentTotal2 ge 100 ?  'color:green' : (booking.pcentTotal2 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <h:outputLabel value="#{booking.planTotal3}"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <h:outputLabel value="#{booking.bookTotal3}"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column style="border-right-color: gray!important" class="col_right" >
                        <f:facet name="footer">
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                            <h:outputLabel value="#{booking.pcentTotal3}"  class="footerFont" style="#{booking.pcentTotal3 ge 100 ?  'color:green' : (booking.pcentTotal3 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column class="col_right" >
                        <f:facet name="footer">
                            <h:outputLabel value="#{booking.planTotal4}"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column  class="col_right">
                        <f:facet name="footer">
                            <h:outputLabel value="#{booking.bookTotal4}"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column style="border-right-color: gray!important" class="col_right" >
                        <f:facet name="footer">
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                            <h:outputLabel value="#{booking.pcentTotal4}"  class="footerFont" style="#{booking.pcentTotal4 ge 100 ?  'color:green' : (booking.pcentTotal4 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <h:outputLabel value="#{booking.planTotal}"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column  class="col_right" >
                        <f:facet name="footer">
                            <h:outputLabel value="#{booking.bookTotal}"  class="footerFont" >
                                <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                    <p:column class="col_right"  >
                        <f:facet name="footer">
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                            <h:outputLabel value="#{booking.pcentTotal}"  class="footerFont" style="#{booking.pcentTotal ge 100 ?  'color:green' : (booking.pcentTotal lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                            </h:outputLabel>
                        </f:facet>
                    </p:column>
                </p:row>
            </p:columnGroup>
        </p:subTable> 



        <!--Grand total footer-->
        <p:columnGroup type="footer" >
            <p:row>
               
                <p:column footerText="Grand Total:"  class="footerFont" style="text-align:right;" colspan="2" /> 
                <p:column>
                    <f:facet name="footer">
                        <h:outputLabel value="#{bookingReports.planTotal1}"  class="footerFont" >
                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column >
                    <f:facet name="footer">
                        <h:outputLabel value="#{bookingReports.bookTotal1}"  class="footerFont" >
                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column>
                    <f:facet name="footer">
                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                        <h:outputLabel value="#{bookingReports.pcentTotal1}"  class="footerFont" style="#{bookingReports.pcentTotal1 ge 100 ?  'color:green' : (bookingReports.pcentTotal1 lt 100 ? 'color:red' : 'color:black')}">
                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column>
                    <f:facet name="footer">
                        <h:outputLabel value="#{bookingReports.planTotal2}"  class="footerFont" >
                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column>
                    <f:facet name="footer">
                        <h:outputLabel value="#{bookingReports.bookTotal2}"  class="footerFont" >
                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column>
                    <f:facet name="footer">
                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                        <h:outputLabel value="#{bookingReports.pcentTotal2}"  class="footerFont" style="#{bookingReports.pcentTotal2 ge 100 ?  'color:green' : (bookingReports.pcentTotal2 lt 100 ? 'color:red' : 'color:black')}">
                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column >
                    <f:facet name="footer">
                        <h:outputLabel value="#{bookingReports.planTotal3}"  class="footerFont" >
                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column>
                    <f:facet name="footer">
                        <h:outputLabel value="#{bookingReports.bookTotal3}"  class="footerFont" >
                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column>
                    <f:facet name="footer">
                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                        <h:outputLabel value="#{bookingReports.pcentTotal3}"  class="footerFont" style="#{bookingReports.pcentTotal3 ge 100 ?  'color:green' : (bookingReports.pcentTotal3 lt 100 ? 'color:red' : 'color:black')}">
                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column>
                    <f:facet name="footer">
                        <h:outputLabel value="#{bookingReports.planTotal4}"  class="footerFont" >
                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column>
                    <f:facet name="footer">
                        <h:outputLabel value="#{bookingReports.bookTotal4}"  class="footerFont" >
                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column>
                    <f:facet name="footer">
                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                        <h:outputLabel value="#{bookingReports.pcentTotal4}"  class="footerFont" style="#{bookingReports.pcentTotal4 ge 100 ?  'color:green' : (bookingReports.pcentTotal4 lt 100 ? 'color:red' : 'color:black')}">
                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column>
                    <f:facet name="footer">
                        <h:outputLabel value="#{bookingReports.planTotal}"  class="footerFont" >
                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column>
                    <f:facet name="footer">
                        <h:outputLabel value="#{bookingReports.bookTotal}"  class="footerFont" >
                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
                <p:column>
                    <f:facet name="footer">
                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                        <h:outputLabel value="#{bookingReports.pcentTotal}"  class="footerFont" style="#{bookingReports.pcentTotal ge 100 ?  'color:green' : (bookingReports.pcentTotal lt 100 ? 'color:red' : 'color:black')}">
                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                        </h:outputLabel>
                    </f:facet>
                </p:column>
            </p:row>
        </p:columnGroup>
    </p:dataTable>
           <style>

                .ui-panelgrid .ui-panelgrid-cell {
                    padding-top: 1px !important;
                    padding-bottom: 1px !important;
                }

                [role="gridcell"]{

                    padding: 0px 10px !important;
                }
                .content-header{     display: none; }


                .jqplot-target {

                    font-size: 0.75em!important;
                }
            </style> 



</ui:composition>
