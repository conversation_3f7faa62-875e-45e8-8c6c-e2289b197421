<!--//**********************************************************
 Program: RepFabric Sys
 Filename: rpt_ProdDtls_All.xhtml
 Author: <PERSON>

* Copyright (c) 2015 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition  template="/dashboard/bookings/reports/LayoutRpt.xhtml" 
                 xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                 xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <ui:define name="meta">
        <f:metadata>
            <f:viewParam name="yr" value="#{bookingReports.repYear}" />
            <f:viewParam name="mt" value="#{bookingReports.repMonth}"/>
            <f:viewParam name="p" value="#{bookingReports.repPrinci}"/>
            <f:viewParam name="c" value="#{bookingReports.repCust}"/>
            <f:viewParam name="s" value="#{bookingReports.repSman}"/>
            <f:viewParam name="g" value="#{bookingReports.repGrp}"/>
            <f:viewAction action="#{bookingsGraph.init()}"/>
        </f:metadata>
    </ui:define>
    <ui:define name="title">
        Bookings - Forecast Vs Booking
    </ui:define>

    <ui:define name="widthcss">
        <style>
            #wrapper {
                margin: 0 auto;
                width: 100%;
            }

            .ui-datatable thead th, .ui-datatable tbody td, .ui-datatable tfoot td{
                padding: 3px 6px;
            }


        </style>        
    </ui:define>

    <ui:define name="header">
        <ui:include src="Header1.xhtml" />         
    </ui:define>

    <ui:define name="reportTitle">
        Forecast Vs Booking
    </ui:define>     

    <ui:define name="content">        
        <script>
            function ext() {
                this.cfg.axes = {
                    xaxis: {
                        tickInterval: 80,
                        tickOptions: {
                            formatString: ''
                        }
                    }
                };

                this.cfg.grid = {
                    drawBorder: false
                };
//                this.cfg.seriesDefaults = {
//                    useSeriesColor:true,
//                    min:0,
//                    max:200,
//                    tickInterval:20
//                };
            }
        </script>

        <p:panel id="BookingLineChart" style="width:45%;height: 100%;display: inline-block" >
            <!--<p:lineChart extender="ext" yaxisLabel="Numbeer" xaxisLabel="Year" value="#{bookingsGraph.lineModel}" animate="true" legendPosition="se"/>-->
        </p:panel>
        <p:panel id="BookingLineChart" style="width:45%;height: 100%;display: inline-block;float: right" >
            <!--<p:lineChart yaxisLabel="Numbeer" xaxisLabel="Year" value="#{bookingsGraph.bookingLineModel}" animate="true" legendPosition="se"/>-->
        </p:panel>
    </ui:define>

    <ui:define name="footer">
        Repfabric Dashboard - Forecast Vs Booking 
    </ui:define>


</ui:composition>