<!--//**********************************************************
 Program: RepFabric Sys
 Filename: ForecastVsBooking.xhtml
 Author: <PERSON><PERSON> R

* Copyright (c) 2015 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition  template="/dashboard/sales_reports/Layout.xhtml" 
                 xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                 xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">


    <ui:define name="caption">
        Graph Test
    </ui:define>


    <ui:define name="options">
        <p:growl widgetVar="grl"/>
        <p:panel class="pnldlgs" style="width: 100%;height: 100%">
            <table style="width: 40%">
                <tr>
                    <td>#{custom.labels.get('IDS_SALES_TEAM')}</td>
                    <td>: </td>
                    <td>                                      
<!--                        <p:selectOneMenu filter="true"  height="350" widgetVar="sman1"  value="#{salesReports.repSman}" style="width: 90%">   
                            <f:selectItem itemLabel="All" itemValue="0"/>
                            <f:selectItems var="sman" value="#{salesReports.listSalesmen}"  itemLabel="#{sman[1]}" itemValue="#{sman[0]}" />
                        </p:selectOneMenu>                              -->
                    </td>
                </tr>
                <tr>
                    <td style="width:100px">#{custom.labels.get('IDS_PRINCI')}</td>
                    <td style="width: 35px">: </td>
                    <td>
<!--                        <p:selectOneMenu filter="true"  height="350" widgetVar="prin1"  value="#{salesReports.repPrinci}" style="width: 90%">      
                            <f:selectItem itemLabel="All" itemValue="0"/>
                            <f:selectItems var="prin" value="#{salesReports.listPrincipals}"  itemLabel="#{prin[2]}" itemValue="#{prin[1]}" />
                        </p:selectOneMenu>                                                -->
                    </td>
                </tr>    
                <tr>
                    <td>Group by</td>
                    <td>: </td>
                    <td> 
                        <p:selectOneRadio value="0"  widgetVar="grp" layout="pageDirection"  required="true">
                            <f:selectItem itemValue="0" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}"/>
                            <f:selectItem   itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}"/>                                   
                        </p:selectOneRadio>
                    </td>
                </tr>
                <tr>
                </tr>
            </table>                                                       
            <p:commandButton icon="ui-icon-script" class="button_top btn_tabs" ajax="true"
                             onclick="showBookingReport(2)"  
                             value="View Report "  />   
        </p:panel>

    </ui:define>
    <ui:define name="summary">
    </ui:define>

</ui:composition>