<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">

    <!--Dialog to display Booking Invoiced split-->
    <p:dialog modal="true" header="#{planBookingMonthRep.smanName}" id="monthlyBookingDetailDlg" width="400" height="200" widgetVar="bookingSplit" resizable="false">
        <table style="width: 100%" cellpadding="5">
            <tr>
                <td style="width: 150px">#{custom.labels.get('IDS_CUSTOMER')}</td>
                <td>: </td>
                <td>#{planBookingMonthRep.custName}</td>
            </tr>
            <tr>
                <td>#{custom.labels.get('IDS_PRINCI')}</td>
                <td>: </td>
                <td>#{planBookingMonthRep.repPrinciName}</td>
            </tr>    
            <tr>
                <td>Month</td>
                <td>: </td>
                <td>#{planBookingMonthRep.repPeriod}</td>
            </tr>
            <tr>
                <td style="font-weight: bold">Booked</td>
                <td>: </td>
                <td> 
                    <h:outputLabel  id="lblMonthlyBooked" value="#{planBookingMonthRep.booked}" style="font-weight: bold">
                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                    </h:outputLabel>
                </td>
            </tr>
            <tr>
                <td style="font-weight: bold">Invoiced</td>
                <td>: </td>
                <td> 
                    <h:outputLabel  id="lblMonthlyInvoiced" value="#{planBookingMonthRep.invoiced}" style="font-weight: bold">
                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                    </h:outputLabel>
                </td>
            </tr>
            <tr>
                <td style="font-weight: bold">#{custom.labels.get('IDS_OPP')} Value</td>
                <td>: </td>
                <td> 
                    <h:outputLabel value="#{planBookingMonthRep.projected}" style="font-weight: bold">
                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                    </h:outputLabel>
                </td>
            </tr>
        </table>               
    </p:dialog>
</ui:composition>
