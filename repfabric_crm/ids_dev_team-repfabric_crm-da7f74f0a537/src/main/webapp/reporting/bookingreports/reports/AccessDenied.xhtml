<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">
    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>

    <ui:define name="metadata">         
        <f:metadata> 

        </f:metadata>
    </ui:define>
    <ui:define name="title"></ui:define>
    <ui:define name="menu">

    </ui:define>

    <ui:define name="body">
        Access Denied
<!--        <ui:include src="/config/tabs.xhtml"/>-->
<!--        <center>
            <h3>
                You are not authorized to access this page.<br/><br/><h:link onclick="assignWinName('/Index.xhtml', 'login', event);" outcome="/Index" value="Home"/>
            </h3>
        </center>-->
    </ui:define>


</ui:composition>
