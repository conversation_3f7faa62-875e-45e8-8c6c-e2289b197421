<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 
    <ui:define name="meta">
        <f:viewParam name="g" value="#{bookingReports.repGrp}"/>
        <f:viewParam name="proj" value="#{bookingReports.repProjGrp}"/>
        <f:viewParam name="flag" value="#{bookingReports.reportFlag}"/>
    </ui:define>

    <style>

        .custom-table td{
            border: none!important;
        }

        .custom-table tr{
            background-color: transparent!important;
        }

        .ui-widget-content tr:nth-child(odd){
            background-color: #F2F5F9;
        }
/*        .ui-state-default {
            border: 0px solid #FFF;
            font-weight: normal;
            color: #555;
        }  */
    </style>


    <p:dataTable styleClass="#{bookingReports.listEmptyFlag?'':'hide-table-header'}"  value="#{bookingReports.bookingsReportList}" var="booking" id="tableMonthlyBookingGroup" rendered="#{bookingReports.reportFlag==0}"  
                 >

        <p:columnGroup type="header" id="tableHeader">
            <p:row>
                <p:column rowspan="1" 
                          headerText="#{custom.labels.get('IDS_CUSTOMER')}" class="col_left"  style="width: 140px" />
                <p:column rowspan="1"  rendered="#{bookingReports.repGrp eq 0}"
                          headerText="#{custom.labels.get('IDS_PRINCI')}" class="col_left"  style="width: 140px"/> 
                <p:column rowspan="1" rendered="#{bookingReports.repGrp eq 1}"
                          headerText="#{custom.labels.get('IDS_SALES_TEAM')}" class="col_left"  style="width: 140px"/> 
                <p:column colspan="1" rowspan="3" headerText="Values" />
                <p:column colspan="1" headerText="#{bookingReports.quarters[0]}"/>
                <p:column colspan="1" headerText="#{bookingReports.quarters[1]}"/>
                <p:column colspan="1" headerText="#{bookingReports.quarters[2]}"/>              
                <p:column colspan="1" headerText="#{bookingReports.quarters[3]}"/>    
                <p:column colspan="1" headerText="#{bookingReports.quarters[4]}"/>  
                <p:column colspan="1" headerText="#{bookingReports.quarters[5]}"/> 
                <p:column colspan="1" headerText="#{bookingReports.quarters[6]}"/> 
                <p:column colspan="1" headerText="#{bookingReports.quarters[7]}"/> 
                <p:column colspan="1" headerText="#{bookingReports.quarters[8]}"/> 
                <p:column colspan="1" headerText="#{bookingReports.quarters[9]}"/> 
                <p:column colspan="1" headerText="#{bookingReports.quarters[10]}"/> 
                <p:column colspan="1" headerText="#{bookingReports.quarters[11]}"/> 
                <p:column colspan="1" headerText="Year #{bookingReports.yearLabel}"   class="col_right" />              
            </p:row>


        </p:columnGroup>

        <p:subTable value="#{booking.monthlybookingsReportList}" var="plan">

            <f:facet name="header">
                <div class="div-selected">
                    <p:outputLabel value=" #{booking.smanName}" rendered="#{bookingReports.repGrp eq 0}"/>
                    <!--#Too many tabs, fixed by replacing _blank to _self -->
                    <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{booking.repPrinci}" target="_self">                          
                        <p:outputLabel style="color: #333;text-decoration: underline" value=" #{booking.princiName}" rendered="#{bookingReports.repGrp eq 1}" />        
                    </a>
                </div>
            </f:facet>
            <p:column class="col_left"  >
                <!--#Too many tabs, fixed by replacing _blank to _self -->
                <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{plan.planCustId}" target="_self">                          
                    #{plan.custName}
                </a>
            </p:column>
            <p:column class="col_left" rendered="#{bookingReports.repGrp eq 0}" style="border-right-color: gray!important">

                <!--#Too many tabs, fixed by replacing _blank to _self -->
                <a href="#{request.contextPath}/opploop/companies/CompanyDetails.xhtml?id=#{plan.planPrinciId}" target="_self">                          
                    #{plan.repPrinciName}
                </a>
                <f:facet name="footer">
                    <h:outputLabel value="Total :"  style="float: right"  class="footerFont" />
                </f:facet>
            </p:column>  
            <p:column class="col_left" rendered="#{bookingReports.repGrp eq 1}" style="border-right-color: gray!important">
                <h:outputLabel id="sname" value=" #{plan.smanName}"/>
                <p:tooltip for="sname" value="#{plan.smanName}"/> 
                <f:facet name="footer">
                    <h:outputLabel value="Total :"  style="float: right" />
                </f:facet>
            </p:column>

            <p:column style="width:30%">
                <table class="custom-table">
                    <tr><td style="font-weight: bold" >FC</td></tr>
                    <tr><td style="font-weight: bold" >Bk+Inv</td></tr>
                    <tr><td style="font-weight: bold" >%</td></tr>
                </table>
            </p:column>

            <!--Month 1-->
            <p:column class="col_right">
                <table class="custom-table">
                    <tr><td>
                            <h:outputLabel value="#{plan.repPlan1}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 
                        </td></tr>
                    <tr><td >
                            <p:commandLink actionListener="#{planBookingMonthRep.retrieveBreakup(bookingReports.quarters[0], 2,1,plan)}"  update=":bookreport:monthlyBookingDetailDlg"   oncomplete="PF('bookingSplit').show();">
                                <h:outputText value="#{plan.repBookInv1}">
                                    <f:convertNumber  maxFractionDigits="0"  />
                                </h:outputText>
                            </p:commandLink>
                        </td></tr>
                    <tr><td >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - color change based on value-->
                            <h:outputLabel value="#{plan.repPcent1}" style="#{plan.repPcent1 ge 100 ?  'color:green' : (plan.repPcent1 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber  groupingUsed="true"  maxFractionDigits="2"  minFractionDigits="2" />
                            </h:outputLabel>
                        </td></tr>
                </table>
            </p:column>

            <!--Month 2-->
            <p:column class="col_right">
                <table class="custom-table">
                    <tr><td >
                            <h:outputLabel value="#{plan.repPlan2}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 
                        </td></tr>
                    <tr><td >
                            <p:commandLink actionListener="#{planBookingMonthRep.retrieveBreakup(bookingReports.quarters[1], 2,2,plan)}" update=":bookreport:monthlyBookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                                <h:outputText value="#{plan.repBookInv2}">
                                    <f:convertNumber  maxFractionDigits="0"  />
                                </h:outputText>
                            </p:commandLink>
                        </td></tr>
                    <tr><td >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - color change based on value-->
                            <h:outputLabel value="#{plan.repPcent2}" style="#{plan.repPcent2 ge 100 ?  'color:green' : (plan.repPcent2 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2" />
                            </h:outputLabel>
                        </td></tr>
                </table>
            </p:column>

            <!--Month 3-->
            <p:column class="col_right">
                <table class="custom-table">
                    <tr><td >
                            <h:outputLabel value="#{plan.repPlan3}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 
                        </td></tr>
                    <tr><td >
                            <p:commandLink actionListener="#{planBookingMonthRep.retrieveBreakup(bookingReports.quarters[2], 2,3,plan)}" update=":bookreport:monthlyBookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                                <h:outputText value="#{plan.repBookInv3}">
                                    <f:convertNumber  maxFractionDigits="0"  />
                                </h:outputText>
                            </p:commandLink>
                        </td></tr>
                    <tr><td >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - color change based on value-->
                            <h:outputLabel value="#{plan.repPcent3}" style="#{plan.repPcent3 ge 100 ?  'color:green' : (plan.repPcent3 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2" />
                            </h:outputLabel>
                        </td></tr>
                </table>
            </p:column>

            <!--Month 4-->
            <p:column class="col_right">
                <table class="custom-table">
                    <tr><td >
                            <h:outputLabel value="#{plan.repPlan4}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 
                        </td></tr>
                    <tr><td >
                            <p:commandLink actionListener="#{planBookingMonthRep.retrieveBreakup(bookingReports.quarters[3], 2,4,plan)}" update=":bookreport:monthlyBookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                                <h:outputText value="#{plan.repBookInv4}">
                                    <f:convertNumber  maxFractionDigits="0"  />
                                </h:outputText>
                            </p:commandLink>
                        </td></tr>
                    <tr><td >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - color change based on value-->
                            <h:outputLabel value="#{plan.repPcent4}" style="#{plan.repPcent4 ge 100 ?  'color:green' : (plan.repPcent4 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2" />
                            </h:outputLabel>
                        </td></tr>
                </table>
            </p:column>

            <!--Month 5-->
            <p:column class="col_right">
                <table class="custom-table">
                    <tr><td >
                            <h:outputLabel value="#{plan.repPlan5}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 
                        </td></tr>
                    <tr><td >
                            <p:commandLink actionListener="#{planBookingMonthRep.retrieveBreakup(bookingReports.quarters[4], 2,5,plan)}" update=":bookreport:monthlyBookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                                <h:outputText value="#{plan.repBookInv5}">
                                    <f:convertNumber  maxFractionDigits="0"  />
                                </h:outputText>
                            </p:commandLink>
                        </td></tr>
                    <tr><td >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - color change based on value-->
                            <h:outputLabel value="#{plan.repPcent5}" style="#{plan.repPcent5 ge 100 ?  'color:green' : (plan.repPcent5 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2" />
                            </h:outputLabel>
                        </td></tr>
                </table>
            </p:column>

            <!--Month 6-->
            <p:column class="col_right">
                <table class="custom-table">
                    <tr><td >
                            <h:outputLabel value="#{plan.repPlan6}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 
                        </td></tr>
                    <tr><td >
                            <p:commandLink actionListener="#{planBookingMonthRep.retrieveBreakup(bookingReports.quarters[5], 2,6,plan)}" update=":bookreport:monthlyBookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                                <h:outputText value="#{plan.repBookInv6}">
                                    <f:convertNumber  maxFractionDigits="0"  />
                                </h:outputText>
                            </p:commandLink>
                        </td></tr>
                    <tr><td >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - color change based on value-->
                            <h:outputLabel value="#{plan.repPcent6}" style="#{plan.repPcent6 ge 100 ?  'color:green' : (plan.repPcent6 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2" />
                            </h:outputLabel>
                        </td></tr>
                </table>
            </p:column>

            <!--Month 7-->
            <p:column class="col_right">
                <table class="custom-table">
                    <tr><td >
                            <h:outputLabel value="#{plan.repPlan7}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 
                        </td></tr>
                    <tr><td >
                            <p:commandLink actionListener="#{planBookingMonthRep.retrieveBreakup(bookingReports.quarters[6], 2,7,plan)}" update=":bookreport:monthlyBookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                                <h:outputText value="#{plan.repBookInv7}">
                                    <f:convertNumber  maxFractionDigits="0"  />
                                </h:outputText>
                            </p:commandLink>
                        </td></tr>
                    <tr><td >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - color change based on value-->
                            <h:outputLabel value="#{plan.repPcent7}" style="#{plan.repPcent7 ge 100 ?  'color:green' : (plan.repPcent7 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2" />
                            </h:outputLabel>
                        </td></tr>
                </table>
            </p:column>

            <!--Month 8-->
            <p:column class="col_right">
                <table class="custom-table">
                    <tr><td >
                            <h:outputLabel value="#{plan.repPlan8}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 
                        </td></tr>
                    <tr><td >
                            <p:commandLink actionListener="#{planBookingMonthRep.retrieveBreakup(bookingReports.quarters[7], 2,8,plan)}" update=":bookreport:monthlyBookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                                <h:outputText value="#{plan.repBookInv8}">
                                    <f:convertNumber  maxFractionDigits="0"  />
                                </h:outputText>
                            </p:commandLink>
                        </td></tr>
                    <tr><td >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - color change based on value-->
                            <h:outputLabel value="#{plan.repPcent8}" style="#{plan.repPcent8 ge 100 ?  'color:green' : (plan.repPcent8 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2" />
                            </h:outputLabel>
                        </td></tr>
                </table>
            </p:column>

            <!--Month 9-->
            <p:column class="col_right">
                <table class="custom-table">
                    <tr><td >
                            <h:outputLabel value="#{plan.repPlan9}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 
                        </td></tr>
                    <tr><td >
                            <p:commandLink actionListener="#{planBookingMonthRep.retrieveBreakup(bookingReports.quarters[8], 2,9,plan)}"  update=":bookreport:monthlyBookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                                <h:outputText value="#{plan.repBookInv9}">
                                    <f:convertNumber  maxFractionDigits="0"  />
                                </h:outputText>
                            </p:commandLink>
                        </td></tr>
                    <tr><td >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - color change based on value-->
                            <h:outputLabel value="#{plan.repPcent9}" style="#{plan.repPcent9 ge 100 ?  'color:green' : (plan.repPcent9 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2" />
                            </h:outputLabel>
                        </td></tr>
                </table>
            </p:column>

            <!--Month 10-->
            <p:column class="col_right">
                <table class="custom-table">
                    <tr><td >
                            <h:outputLabel value="#{plan.repPlan10}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 
                        </td></tr>
                    <tr><td >
                            <p:commandLink actionListener="#{planBookingMonthRep.retrieveBreakup(bookingReports.quarters[9], 2,10,plan)}" update=":bookreport:monthlyBookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                                <h:outputText value="#{plan.repBookInv10}">
                                    <f:convertNumber  maxFractionDigits="0"  />
                                </h:outputText>
                            </p:commandLink>
                        </td></tr>
                    <tr><td >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - color change based on value-->
                            <h:outputLabel value="#{plan.repPcent10}" style="#{plan.repPcent10 ge 100 ?  'color:green' : (plan.repPcent10 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2" />
                            </h:outputLabel>
                        </td></tr>
                </table>
            </p:column>

            <!--Month 11-->
            <p:column class="col_right">
                <table class="custom-table">
                    <tr><td >
                            <h:outputLabel value="#{plan.repPlan11}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 
                        </td></tr>
                    <tr><td >
                            <p:commandLink actionListener="#{planBookingMonthRep.retrieveBreakup(bookingReports.quarters[10], 2,11,plan)}" update=":bookreport:monthlyBookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                                <h:outputText value="#{plan.repBookInv11}">
                                    <f:convertNumber  maxFractionDigits="0"  />
                                </h:outputText>
                            </p:commandLink>
                        </td></tr>
                    <tr><td >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - color change based on value-->
                            <h:outputLabel value="#{plan.repPcent11}" style="#{plan.repPcent11 ge 100 ?  'color:green' : (plan.repPcent11 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2" />
                            </h:outputLabel>
                        </td></tr>
                </table>
            </p:column>

            <!--Month 12-->
            <p:column class="col_right">
                <table class="custom-table">
                    <tr><td >
                            <h:outputLabel value="#{plan.repPlan12}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel> 
                        </td></tr>
                    <tr><td >
                            <p:commandLink actionListener="#{planBookingMonthRep.retrieveBreakup(bookingReports.quarters[11], 2,12,plan)}" update=":bookreport:monthlyBookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                                <h:outputText value="#{plan.repBookInv12}">
                                    <f:convertNumber  maxFractionDigits="0"  />
                                </h:outputText>
                            </p:commandLink>
                        </td></tr>
                    <tr><td >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - color change based on value-->
                            <h:outputLabel value="#{plan.repPcent12}" style="#{plan.repPcent12 ge 100 ?  'color:green' : (plan.repPcent12 lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2" />
                            </h:outputLabel>
                        </td></tr>
                </table>
            </p:column>


            <!--Year-->
            <p:column class="col_right">
                <table class="custom-table">
                    <tr><td >
                            <h:outputLabel value="#{plan.repPlanTotal}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                            </h:outputLabel>
                        </td></tr>
                    <tr><td >
                            <p:commandLink actionListener="#{planBookingMonthRep.retrieveBreakup(bookingReports.yearLabel, 3,0,plan)}" update=":bookreport:monthlyBookingDetailDlg" oncomplete="PF('bookingSplit').show();">
                                <h:outputText value="#{plan.repBookInvTotal}">
                                    <f:convertNumber  groupingUsed="true"   maxFractionDigits="0"  />
                                </h:outputText> 
                            </p:commandLink>
                        </td></tr>
                    <tr><td >
                            <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - color change based on value-->
                            <h:outputLabel value="#{plan.repPcentTotal}" style="#{plan.repPcentTotal ge 100 ?  'color:green' : (plan.repPcentTotal lt 100 ? 'color:red' : 'color:black')}">
                                <f:convertNumber  groupingUsed="true"   maxFractionDigits="2"  minFractionDigits="2"/>
                            </h:outputLabel> 
                        </td></tr>
                </table>
            </p:column>



            <!--subtotal footer-->
            <p:columnGroup type="footer">
                <p:row>
                    <p:column colspan="2" footerText="Total:" class="col_right" style="border-right-color: gray!important"/>
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr><td style="font-weight: bold" >FC</td></tr>
                                <tr><td style="font-weight: bold" >Bk+Inv</td></tr>
                                <tr><td style="font-weight: bold" >%</td></tr>
                            </table>
                        </f:facet>
                    </p:column>

                    <!--                Jan-->
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.planTotal1}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.bookTotal1}" class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                        <h:outputLabel value="#{booking.pcentTotal1}"  class="footerFont" style="#{booking.pcentTotal1 ge 100 ?  'color:green' : (booking.pcentTotal1 lt 100 ? 'color:red' : 'color:black')}">
                                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                            </table>
                        </f:facet>
                    </p:column>

                    <!--                Feb-->
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.planTotal2}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>

                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.bookTotal2}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                        <h:outputLabel value="#{booking.pcentTotal2}"  class="footerFont" style="#{booking.pcentTotal2 ge 100 ?  'color:green' : (booking.pcentTotal2 lt 100 ? 'color:red' : 'color:black')}">
                                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                            </table>
                        </f:facet>  
                    </p:column>

                    <!--                March-->
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.planTotal3}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.bookTotal3}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                        <h:outputLabel value="#{booking.pcentTotal3}"  class="footerFont" style="#{booking.pcentTotal3 ge 100 ?  'color:green' : (booking.pcentTotal3 lt 100 ? 'color:red' : 'color:black')}">
                                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                            </table>
                        </f:facet>  
                    </p:column>

                    <!--                April-->
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.planTotal4}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.bookTotal4}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                        <h:outputLabel value="#{booking.pcentTotal4}"  class="footerFont" style="#{booking.pcentTotal4 ge 100 ?  'color:green' : (booking.pcentTotal4 lt 100 ? 'color:red' : 'color:black')}">
                                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                            </table>
                        </f:facet>  
                    </p:column>

                    <!--                May-->
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.planTotal5}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>

                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.bookTotal5}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                        <h:outputLabel value="#{booking.pcentTotal5}"  class="footerFont" style="#{booking.pcentTotal5 ge 100 ?  'color:green' : (booking.pcentTotal5 lt 100 ? 'color:red' : 'color:black')}">
                                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                            </table>
                        </f:facet>  
                    </p:column>

                    <!--                June-->
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.planTotal6}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>

                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.bookTotal6}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                        <h:outputLabel value="#{booking.pcentTotal6}"  class="footerFont" style="#{booking.pcentTotal6 ge 100 ?  'color:green' : (booking.pcentTotal6 lt 100 ? 'color:red' : 'color:black')}">
                                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                            </table>
                        </f:facet>  
                    </p:column>

                    <!--                July-->
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.planTotal7}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.bookTotal7}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>

                                <tr>
                                    <td >
                                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                        <h:outputLabel value="#{booking.pcentTotal7}"  class="footerFont" style="#{booking.pcentTotal7 ge 100 ?  'color:green' : (booking.pcentTotal7 lt 100 ? 'color:red' : 'color:black')}">
                                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                            </table>
                        </f:facet> 
                    </p:column>

                    <!--                August-->
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.planTotal8}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.bookTotal8}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                        <h:outputLabel value="#{booking.pcentTotal8}"  class="footerFont" style="#{booking.pcentTotal8 ge 100 ?  'color:green' : (booking.pcentTotal8 lt 100 ? 'color:red' : 'color:black')}">
                                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                            </table>
                        </f:facet> 
                    </p:column>
                    <!--
                                    Sep-->
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.planTotal9}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.bookTotal9}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                        <h:outputLabel value="#{booking.pcentTotal9}"  class="footerFont" style="#{booking.pcentTotal9 ge 100 ?  'color:green' : (booking.pcentTotal9 lt 100 ? 'color:red' : 'color:black')}">
                                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                            </table>
                        </f:facet> 
                    </p:column>

                    <!--                October-->
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.planTotal10}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.bookTotal10}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                        <h:outputLabel value="#{booking.pcentTotal10}"  class="footerFont" style="#{booking.pcentTotal10 ge 100 ?  'color:green' : (booking.pcentTotal10 lt 100 ? 'color:red' : 'color:black')}">
                                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                            </table>
                        </f:facet> 
                    </p:column>

                    <!--                November-->
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.planTotal11}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.bookTotal11}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                        <h:outputLabel value="#{booking.pcentTotal11}"  class="footerFont" style="#{booking.pcentTotal11 ge 100 ?  'color:green' : (booking.pcentTotal11 lt 100 ? 'color:red' : 'color:black')}">
                                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                            </table>
                        </f:facet> 
                    </p:column>

                    <!--December-->
                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.planTotal12}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.bookTotal12}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                        <h:outputLabel value="#{booking.pcentTotal12}"  class="footerFont" style="#{booking.pcentTotal12 ge 100 ?  'color:green' : (booking.pcentTotal12 lt 100 ? 'color:red' : 'color:black')}">
                                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                            </table>
                        </f:facet> 

                    </p:column>

                    <p:column class="col_right">
                        <f:facet name="footer">
                            <table class="custom-table">
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.planTotal}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <h:outputLabel value="#{booking.bookTotal}"  class="footerFont" >
                                            <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td >
                                        <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                        <h:outputLabel value="#{booking.pcentTotal}"  class="footerFont" style="#{booking.pcentTotal ge 100 ?  'color:green' : (booking.pcentTotal lt 100 ? 'color:red' : 'color:black')}">
                                            <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                        </h:outputLabel>
                                    </td>
                                </tr>
                            </table>
                        </f:facet>
                    </p:column>
                </p:row>

            </p:columnGroup>


        </p:subTable> 



        <!--Grand total footer-->
        <p:columnGroup type="footer"  >
            <p:row  style=" border: 0px solid #FFF;">
                <p:column  style=" border: 0px solid #FFF;
            font-weight: normal;
            color: #555;">
                </p:column>
                <p:column footerText="Grand Total:"  style="float:right;width:140px;padding-top: 55px; border: 0px solid #FFF;
            font-weight: normal;
            color: #555;"   class="footerFont" colspan="2" /> 
                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr><td style="font-weight: bold">FC</td></tr>
                            <tr><td style="font-weight: bold">Bk+Inv</td></tr>
                            <tr><td style="font-weight: bold">%</td></tr>
                        </table>
                    </f:facet>
                </p:column>

                <!--                Jan-->
                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.planTotal1}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.bookTotal1}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                    <h:outputLabel value="#{bookingReports.pcentTotal1}"  class="footerFont" style="#{bookingReports.pcentTotal1 ge 100 ?  'color:green' : (bookingReports.pcentTotal1 lt 100 ? 'color:red' : 'color:black')}">
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                        </table>
                    </f:facet>
                </p:column>

                <!--               Feb-->
                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.planTotal2}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.bookTotal2}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                    <h:outputLabel value="#{bookingReports.pcentTotal2}"  class="footerFont" style="#{bookingReports.pcentTotal2 ge 100 ?  'color:green' : (bookingReports.pcentTotal2 lt 100 ? 'color:red' : 'color:black')}">
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                        </table>
                    </f:facet>
                </p:column>

                <!--               march-->
                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.planTotal3}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.bookTotal3}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                    <h:outputLabel value="#{bookingReports.pcentTotal3}"  class="footerFont" style="#{bookingReports.pcentTotal3 ge 100 ?  'color:green' : (bookingReports.pcentTotal3 lt 100 ? 'color:red' : 'color:black')}">
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                        </table>
                    </f:facet>
                </p:column>

                <!--               april-->
                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.planTotal4}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.bookTotal4}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                    <h:outputLabel value="#{bookingReports.pcentTotal4}"  class="footerFont" style="#{bookingReports.pcentTotal4 ge 100 ?  'color:green' : (bookingReports.pcentTotal4 lt 100 ? 'color:red' : 'color:black')}">
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                        </table>
                    </f:facet>
                </p:column>

                <!--               May-->
                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.planTotal5}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.bookTotal5}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                    <h:outputLabel value="#{bookingReports.pcentTotal5}"  class="footerFont" style="#{bookingReports.pcentTotal5 ge 100 ?  'color:green' : (bookingReports.pcentTotal5 lt 100 ? 'color:red' : 'color:black')}">
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                        </table>
                    </f:facet>
                </p:column>

                <!--               June-->
                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.planTotal6}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.bookTotal6}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                    <h:outputLabel value="#{bookingReports.pcentTotal6}"  class="footerFont" style="#{bookingReports.pcentTotal6 ge 100 ?  'color:green' : (bookingReports.pcentTotal6 lt 100 ? 'color:red' : 'color:black')}">
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                        </table>
                    </f:facet>
                </p:column>

                <!--               July-->
                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.planTotal7}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.bookTotal7}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                    <h:outputLabel value="#{bookingReports.pcentTotal7}"  class="footerFont" style="#{bookingReports.pcentTotal7 ge 100 ?  'color:green' : (bookingReports.pcentTotal7 lt 100 ? 'color:red' : 'color:black')}">
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                        </table>
                    </f:facet>
                </p:column>

                <!--               august-->
                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.planTotal8}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.bookTotal8}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                    <h:outputLabel value="#{bookingReports.pcentTotal8}"  class="footerFont" style="#{bookingReports.pcentTotal8 ge 100 ?  'color:green' : (bookingReports.pcentTotal8 lt 100 ? 'color:red' : 'color:black')}">
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                        </table>
                    </f:facet>
                </p:column>

                <!--               Septembere-->
                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.planTotal9}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.bookTotal9}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                    <h:outputLabel value="#{bookingReports.pcentTotal9}"  class="footerFont" style="#{bookingReports.pcentTotal9 ge 100 ?  'color:green' : (bookingReports.pcentTotal9 lt 100 ? 'color:red' : 'color:black')}">
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                        </table>
                    </f:facet>
                </p:column>

                <!--               November-->
                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.planTotal10}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.bookTotal10}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                    <h:outputLabel value="#{bookingReports.pcentTotal10}"  class="footerFont" style="#{bookingReports.pcentTotal10 ge 100 ?  'color:green' : (bookingReports.pcentTotal10 lt 100 ? 'color:red' : 'color:black')}">
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                        </table>
                    </f:facet>
                </p:column>

                <!--               December-->
                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.planTotal11}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.bookTotal11}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                    <h:outputLabel value="#{bookingReports.pcentTotal11}"  class="footerFont" style="#{bookingReports.pcentTotal11 ge 100 ?  'color:green' : (bookingReports.pcentTotal11 lt 100 ? 'color:red' : 'color:black')}">
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                        </table>
                    </f:facet>
                </p:column>

                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.planTotal12}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.bookTotal12}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                    <h:outputLabel value="#{bookingReports.pcentTotal12}"  class="footerFont" style="#{bookingReports.pcentTotal12 ge 100 ?  'color:green' : (bookingReports.pcentTotal12 lt 100 ? 'color:red' : 'color:black')}">
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                        </table>
                    </f:facet>
                </p:column>


                <p:column class="col_right">
                    <f:facet name="footer">
                        <table class="custom-table">
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.planTotal}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <h:outputLabel value="#{bookingReports.bookTotal}"  class="footerFont" >
                                        <f:convertNumber maxFractionDigits="0" minFractionDigits="0"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                    <!--#7885: CRM-5651 forecast vs booking: percentages rise and fall not calculating - poornima - added style-->
                                    <h:outputLabel value="#{bookingReports.pcentTotal}"  class="footerFont" style="#{bookingReports.pcentTotal ge 100 ?  'color:green' : (bookingReports.pcentTotal lt 100 ? 'color:red' : 'color:black')}">
                                        <f:convertNumber maxFractionDigits="2" minFractionDigits="2"/>
                                    </h:outputLabel>
                                </td>
                            </tr>
                        </table>
                    </f:facet>
                </p:column>
            </p:row>
        </p:columnGroup>
    </p:dataTable>

    <!--    <style>
        .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
        border: 0px solid #FFF;
        font-weight: normal;
        color: #555;
    }
    </style>-->


</ui:composition>
