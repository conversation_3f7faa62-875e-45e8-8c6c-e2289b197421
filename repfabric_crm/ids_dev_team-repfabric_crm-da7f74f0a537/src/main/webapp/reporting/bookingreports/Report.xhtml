<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: Leftreports.xhtml
// Author: Priyadarshini
// Copyright (c) 2019 Indea Design Systems Pvt. Limited, All Rights Reserved.
//********************************************************************* -->
<!--Task #6: Settings: Subtables -->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">
    <!--#363:page title-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>  #{leftBooking.selectedSubMenu.label}  </title>
    </ui:define>

    <ui:define name="metadata">
        <f:metadata> 
            <f:viewParam name="r" value="#{leftBooking.id}"/>

            <f:viewAction action="#{leftBooking.init(leftBooking.id)}"/>
            <f:viewAction action="#{salesReports.resetFilters()}"/>
            <!--#2521:   Related - CRM-1521: Tutorial button landing urls - Sales Reports, Funnel Report-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('BOOK_RPT'))}" />
            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('BOOK_RPT')}" />
        </f:metadata>
    </ui:define>

    <ui:define name="title">Booking Reports
    <ui:include src="/help/Help.xhtml"/>
    </ui:define>
    <ui:define name="body">
        <f:event listener="#{leftBooking.isPageAccessible}" type="preRenderView"/>  
      
        <div class="box box-info box-body">   
            
            <div class="left-column">
                <h:form>
                    <p:dataTable value="#{leftBooking.bookingRepMenuList}"  var="cm" selectionMode="single" id="dtCommissionMenuLst" selection="#{leftCommission.selectedSubMenu}" class="hide-column-names"
                                 rowKey="#{cm.id}" >

<!--listener="#{reports.resetFilters()}"-->
                        <p:ajax event="rowSelect" update=":commReport" listener="#{leftBooking.redirectPage()}"  />
                        <p:column >
                            <h:outputText value="#{cm.label}"/>
                        </p:column>


                        <p:editor id="editor" widgetVar="editorWidget"  width="600" />
                        <p:fileUpload  mode="advanced" dragDropSupport="false"
                                       sizeLimit="100000" fileLimit="3" allowTypes="/(\.|\/)(gif|jpe?g|png)$/" />
                    </p:dataTable>
                </h:form>
            </div>
            <div class="right-column">

                <p:outputPanel id="commReport">
                    <ui:include src="#{leftBooking.selectedSubMenu.pageUrl}"  />

                </p:outputPanel>



            </div>



        </div>
    </ui:define>

</ui:composition>
