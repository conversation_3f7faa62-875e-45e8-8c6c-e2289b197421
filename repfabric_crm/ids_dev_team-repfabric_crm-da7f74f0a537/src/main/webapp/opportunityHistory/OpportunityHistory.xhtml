<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">
    <!--#363 :page title-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>   #{custom.labels.get('IDS_OPP')} History </title>
    </ui:define>

    <ui:define name="metadata">
        <f:metadata> 
            <f:viewAction action="#{userActivity.init()}"/>

            <!--#2521:   Related - CRM-1521: Tutorial button landing urls - Sales Reports, Funnel Report-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('OPP_HIST'))}" />
            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('OPP_HIST')}" />
        </f:metadata>
    </ui:define>

<!--    <ui:define name="title">
        <h:form>
        </h:form>
        <f:event listener="#{roleService.isPageAccessible}" type="preRenderView"/> 
        #3354 Fix Sonarqube issues
        <title>Opportunity History</title> 
        <ui:include src="/help/Help.xhtml"/>
    </ui:define>-->
    <!--1435 - sync  opp history-->
      <ui:define name="title">
        <ui:param name="title" value="Sync History"/>
        <div class="row">
            <div class="col-md-6">
               #{custom.labels.get('IDS_OPP')} History
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define> 

    <!--    <ui:define name="PageHeader">
            <h1>Company Management</h1>
        </ui:define>-->
    
     <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title> Opportunity History </title>
    </ui:define>

    <ui:define name="menu">
        <h:form>

        </h:form>
        <ul class="sidebar-menu tree" data-widget="tree">
            <h:form >
            </h:form>  
        </ul>
    </ui:define>

    <!--    <ui:define name="slogan">
        </ui:define>-->

    <ui:define name="body">

        <f:event listener="#{opportunityHistory.isPageAccessible}" type="preRenderView"/>


        <div class="box box-info box-body" style="vertical-align: top">


            <div class="left-column">

                <h:form id="formLeft">
                    <p:dataTable value="#{users.getDependentUserList()}"  var="u" id="userHistoryDT" selection="#{opportunityHistory.selectedUserList}"  
                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                                 emptyMessage=" " paginator="true" rows="30" paginatorAlwaysVisible="false" 
                                 rowKey="#{u.userId}"  rowSelectMode="add" >
                        <p:column selectionMode="multiple" style="width:40px;text-align: center"/>
                        <p:column filterMatchMode="contains" filterBy="#{u.userName}" id="historyUsr" >
                            #{u.userName}
                        </p:column>                
                        <p:ajax  event="rowSelectCheckbox" listener="#{opportunityHistory.onUserSelect}"  update=":form1"/>
                        <p:ajax  event="rowUnselectCheckbox" listener="#{opportunityHistory.onUserSelect}"   update=":form1"/>
                        <p:ajax  event="rowSelect" listener="#{opportunityHistory.onUserSelect}"    update=":form1"/>
                        <p:ajax  event="rowUnselect" listener="#{opportunityHistory.onUserSelect}"  update=":form1"/>
                        <p:ajax  event="toggleSelect" listener="#{opportunityHistory.onToggleSelect}"   update=":form1"/>
                    </p:dataTable>
                </h:form>
            </div>

            <div class="right-column">

                <h:form id="form1">


                    <h:panelGrid columns="15">
                        <p:outputLabel value="From :" />
                        <p:spacer width="4px"/>
                        <p:calendar id="frmDte"  pattern="#{globalParams.dateFormat}" showOn="button"
                                    value="#{opportunityHistory.fromDate}" required="true" requiredMessage="Please select From Date" converterMessage="Invalid Date Format" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                            <p:ajax event="dateSelect" process="@this" listener="#{opportunityHistory.onUserSelect}"  />
                        </p:calendar>
                        <p:spacer width="4px"/>
                        <p:outputLabel value="To :" />
                        <p:spacer width="4px"/>
                        <p:calendar id="toDte"  pattern="#{globalParams.dateFormat}" showOn="button"
                                    value="#{opportunityHistory.toDate}" required="true" requiredMessage="Please select To Date" converterMessage="Invalid Date Format" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                            <p:ajax event="dateSelect" process="@this" listener="#{opportunityHistory.onUserSelect}"  />
                        </p:calendar>
                        <p:spacer width="4px"/>
                        <p:selectOneMenu value="#{opportunityHistory.field}" style="width: 200px"  >
                            <f:selectItem itemValue="" itemLabel="Select Field" />
                            <f:selectItem itemValue="ACTIVITY" itemLabel="#{custom.labels.get('IDS_ACTIVITY')}" />
                            <f:selectItem itemValue="STAGE" itemLabel="#{custom.labels.get('IDS_ACTIVITY')} Stage" />
                           <!--#13178 ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
                            <f:selectItem itemValue="STATUS" itemLabel="#{custom.labels.get('IDS_ACT_STATUS')}" />
                            <!--<f:selectItem itemValue="FOLLOW_UP" itemLabel="#{custom.labels.get('IDS_FOLLOW_UP')}" />-->
                            <f:selectItem itemValue="PRIORITY" itemLabel="#{custom.labels.get('IDS_PRIORITY')}" />
                            <f:selectItem itemValue="POTENTIAL" itemLabel="#{custom.labels.get('IDS_POTENTIAL')}" />
                            <f:selectItem itemValue="VALUE" itemLabel="#{custom.labels.get('IDS_VALUE')}" />
                <!--            <f:selectItem itemValue="PROD_DATE" itemLabel="#{custom.labels.get('IDS_PRODUCTION_DATE')}" />
                            <f:selectItem itemValue="PROTO_DATE" itemLabel="#{custom.labels.get('IDS_PROTO_DATE')}" />-->
                            <p:ajax event="change" process="@this" listener="#{opportunityHistory.onChangeField}"   />
                        </p:selectOneMenu>
                        <p:spacer width="4px"/>
                        <p:commandButton  id="btnView" value="Run Report" action="#{opportunityHistory.viewOpp()}" styleClass="btn btn-primary  btn-xs"  onclick="PF('dlgMsg').show()"  oncomplete="PF('dlgMsg').hide()"  update=":formData"/>
                        <!--Bug 857 Opp history Export feature -->
                        <p:spacer width="4px"/>
<!--                        // #Task 4757 Add Ids for Buttons in Reporting screens-->
                        <p:commandButton id="btnExpOppHis" value="Export" action="#{opportunityHistory.exportOppHistory(2)}"
                                         onclick="PrimeFaces.monitorDownload(start3, stop3);
                                                 PF('pollStatExp').start3();" styleClass="btn btn-primary  btn-xs"/>
                       
<!--Task #1435: Opp History >  Sync History-->
                        
                        <p:spacer width="4px"/>
<!--                        // #Task 4757 Add Ids for Buttons in Reporting screens-->
                        <p:commandButton id="btnSyncHist" value="Sync History" action="#{syncOppHistory.redirectToSyncHistory()}"
                                          styleClass="btn btn-primary  btn-xs"/>



                    </h:panelGrid>
                </h:form>
                <h:form id="formData">

                    <p:dataTable var="opp1" value="#{opportunityHistory.summaryFields}" id="oppSummary" >
                        <p:columnGroup type="header">
                            <p:row>
                                <!--Value-->
                                <p:column rowspan="2" headerText="#{opportunityHistory.historyLabel}"  />

                                <!--As of-->
                                <p:column rowspan="2" >
                                    <f:facet name="header" >
                                        <h:outputLabel value="Upto " style="font-weight: bold"/>
                                        <p:spacer width="2px"/>
                                        <p:outputLabel value="#{opportunityHistory.fromDate}" style="font-weight: bold!important;">
                                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                        </p:outputLabel>
                                    </f:facet>
                                </p:column>

                                <!--During-->
                                <p:column colspan="2" headerText="During"  />

                                <!--End Date-->
                                <p:column rowspan="2" 
                                          >
                                    <f:facet name="header" >
                                        <h:outputLabel value="Ending (As of " style="font-weight: bold!important;"/>
                                        <p:outputLabel value="#{opportunityHistory.toDate}" style="font-weight: bold!important;">
                                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                        </p:outputLabel>
                                        <h:outputLabel value=")" />
                                    </f:facet>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column headerText="Added" class="col_left" />
                                <p:column headerText="Progressed" class="col_left" />
                            </p:row>
                        </p:columnGroup>
                        <p:column > 
                            <h:outputText value="#{opp1.fieldValue}" />
                        </p:column>
                        <p:column  > 
                            <p:commandLink rendered="#{opp1.fieldInitialCount > 0}" actionListener="#{opportunityHistory.loadOpps(opp1.fieldName,opp1.fieldValue, 1)}" onclick="PF('dlgLoadOppMsg').show();" update=":dlgOppForm"  oncomplete="PF('dtValues').clearFilters();PF('dlgLoadOppMsg').hide();PF('dlgOpp').show();">  
                                <h:outputText value="#{opp1.fieldInitialCount}" />
                            </p:commandLink>
                            <h:outputText value="#{opp1.fieldInitialCount}" rendered="#{opp1.fieldInitialCount == 0}"/>

                        </p:column>
                        <p:column > 
                            <p:commandLink rendered="#{opp1.fieldDuringAddCount > 0}" actionListener="#{opportunityHistory.loadOpps(opp1.fieldName,opp1.fieldValue, 2)}" onclick="PF('dlgLoadOppMsg').show();" update=" :dlgOppForm"  oncomplete="PF('dtValues').clearFilters();PF('dlgLoadOppMsg').hide();PF('dlgOpp').show();">  
                                <h:outputText value="#{opp1.fieldDuringAddCount}" />
                            </p:commandLink>
                            <h:outputText value="#{opp1.fieldDuringAddCount}" rendered="#{opp1.fieldDuringAddCount == 0}"/>
                        </p:column>
                        <p:column > 
                <!--            <p:commandLink rendered="#{opp1.fieldDuringRemoveCount > 0}" actionListener="#{opportunityHistory.loadOpps(opp1.fieldName,opp1.fieldValue, 3)}" update=":dlgOppForm" oncomplete="PF('dlgOpp').show();">  
                                <h:outputText value="#{opp1.fieldDuringRemoveCount}" />
                            </p:commandLink>
                            <h:outputText value="#{opp1.fieldDuringRemoveCount}" rendered="#{opp1.fieldDuringAddCount == 0}" />-->
                            <h:outputText value="#{opp1.fieldDuringRemoveCount}" />
                        </p:column>
                        <p:column > 
                            <p:commandLink rendered="#{opp1.fieldEndCount > 0}" actionListener="#{opportunityHistory.loadOpps(opp1.fieldName,opp1.fieldValue, 4)}" onclick="PF('dlgLoadOppMsg').show();" update=":formData :dlgOppForm" oncomplete="PF('dtValues').clearFilters();PF('dlgLoadOppMsg').hide();PF('dlgOpp').show();" >  
                                <h:outputText value="#{opp1.fieldEndCount}" />
                            </p:commandLink>
                            <h:outputText value="#{opp1.fieldEndCount}" rendered="#{opp1.fieldEndCount == 0}"/>
                        </p:column>
                    </p:dataTable>



                </h:form>

            </div>

            <p:dialog widgetVar="dlgOpp"  id="dlgOppPnl" header="#{custom.labels.get('IDS_OPPS')}"  closeOnEscape="true"
                      modal="true"   height="500" width="1200px">
                <h:form id="dlgOppForm"> 
                    <p:dataTable var="opp" widgetVar="dtValues" value="#{opportunityHistory.oppList}"   filteredValue="#{opportunityHistory.filteredList}" paginator="true"  rows="20" paginatorPosition="top" rowKey="#{opp.oppId}" >


                        <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}"  filterMatchMode="contains" sortBy="#{opp.custName}" filterBy="#{opp.custName}"> 
                            <h:link  style="text-decoration: underline"  outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"
                                     value="#{opp.custName}" target="_self" disabled="#{opp.oppDelFlag}" />

                            <!--Bug 1303 disabled added  -->
                        </p:column>
                        <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{opp.princiName}" filterMatchMode="contains" sortBy="#{opp.princiName}" >
                            <h:outputText value="#{opp.princiName}" />

                        </p:column>
                        <p:column headerText="#{custom.labels.get('IDS_DISTRI')}" filterBy="#{opp.distriName}" filterMatchMode="contains" sortBy="#{opp.distriName}" >

                            <h:outputText value="#{opp.distriName}" />
                        </p:column>

                        <p:column headerText="#{custom.labels.get('IDS_PROGRAM')}" filterBy="#{opp.curCustProgram}" filterMatchMode="contains" sortBy="#{opp.curCustProgram}"> 
                            <h:outputText value="#{opp.curCustProgram}" />
                        </p:column>

                        <p:column headerText="#{custom.labels.get('IDS_ACTIVITY')}" filterBy="#{opp.curActivity}" filterMatchMode="contains" sortBy="#{opp.curActivity}"> 
                            <h:outputText value="#{opp.curActivity}" />
                        </p:column>
                        <p:column headerText="Stage" filterBy="#{opp.curActStageName}" filterMatchMode="contains" sortBy="#{opp.curActStageName}"> 
                            <h:outputText value="#{opp.curActStageName}" />
                        </p:column>
                        <p:column headerText="#{custom.labels.get('IDS_FOLLOW_UP')}" sortBy="#{opp.curFollowUp}"> 
                            <h:outputText value="#{opp.curFollowUp}" style="width:10%" >
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                            </h:outputText>
                        </p:column>
                        <p:column style="width:6%!important"  headerText="Value"  sortBy="#{opp.curValue}"> 
                            <h:outputText value="#{opp.curValue}" style="width:6%" />
                        </p:column>
                        <p:column style="width:6%!important"  headerText="#{custom.labels.get('IDS_POTENTIAL')}" rendered="#{opportunityHistory.fieldName eq 'POTENTIAL'}" sortBy="#{opp.curPotential}"> 
                            <h:outputText value="#{opp.curPotential}" />
                        </p:column>
                        <p:column style="width:6%!important"  headerText="#{custom.labels.get('IDS_PRIORITY')}" rendered="#{opportunityHistory.fieldName eq 'PRIORITY'}"  sortBy="#{opp.curPriority}"> 
                            <h:outputText value="#{opp.curPriority}" style="width:6%" />
                        </p:column>
                        <p:column headerText="Last Modified Date"  sortBy="#{opp.updDate}"> 
                            <h:outputText value="#{opp.updDate}" style="width:10%" >
                                <f:convertDateTime pattern="#{globalParams.dateTimeFormat}" />
                            </h:outputText>
                        </p:column>
                        <p:column headerText="Last Modified User" sortBy="#{opp.userName}"> 
                            <h:outputText value="#{opp.userName}" style="width:10%" />
                        </p:column>

                    </p:dataTable>

                </h:form>   </p:dialog>









            <style>
                .no-header thead{
                    display: none;
                }
                .vos { 
                    display:block !important;
                }
                body .ui-panel.ui-widget {
                    height: 550px !important;
                    border: 0px !important;
                }
                #user\:userDT tbody div.ui-chkbox span.ui-icon-check {
                    position: relative;
                    right: 4px;
                    bottom: -4px;

                }
                .ctr{
                    text-align:center;
                    padding: 4px;
                }
                #formLeft\:userHistoryDT tbody div.ui-chkbox span.ui-icon-check {
                    position: relative;
                    right: 4px;
                    bottom: -4px;
                }
            </style>

        </div>
        <script type="text/javascript">
            function start() {

                PF('dlgMsg1').show();
            }
            function stop() {
                PF('dlgMsg1').hide();
            }
        </script>
        <p:dialog widgetVar="dlgMsg" closable="false" modal="true" header="Message" resizable="false" width="250" >
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif"   />
                <p:spacer width="5" />
                <p:outputLabel value="Processing... Please wait..." />
                <br /><br />
            </p:outputPanel>
        </p:dialog>
        <p:dialog widgetVar="dlgLoadOppMsg" closable="false" modal="true" header="Message" resizable="false" width="300" >
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif"   />
                <p:spacer width="5" />
                <p:outputLabel value="Loading #{custom.labels.get('IDS_OPPS')}... Please wait..." />
                <br /><br />
            </p:outputPanel>
        </p:dialog>


    </ui:define> 
</ui:composition>
