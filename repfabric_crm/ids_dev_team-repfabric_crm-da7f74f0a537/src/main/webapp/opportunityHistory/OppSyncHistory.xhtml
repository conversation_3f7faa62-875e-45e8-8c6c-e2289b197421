<?xml version='1.0' encoding='UTF-8' ?> 
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename:OppSyncHistory.xhtml
// Author: Priyadarshini
#1435- - Sync opp history
//*********************************************************
/*
*
*/-->


<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">




    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>   #{custom.labels.get('IDS_OPP')} Sync History </title>
    </ui:define>

    <ui:define name="meta">
        <f:metadata> 
            <f:viewAction action="#{syncOppHistory.defaultDates()}"/>
            <!--<f:viewAction action="#{viewSalesMain.viewSalLst()}"/>-->
            <!--//16-05-2022 : #7966  : CRM-5690: Sync History Report: link the Tutorial menu to the slug-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('OPP_SYNC_HIST'))}" />
            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('OPP_SYNC_HIST')}" />
        </f:metadata>
    </ui:define>

    <ui:define name="title">
        <ui:param name="title" value="Sync History"/>
        <div class="row">
            <div class="col-md-6">
               Sync History
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define> 
    <ui:define name="body">
        <!--Task #1435: Opp History >  Sync History-->
                <!--<f:event listener="#{productsMst.isPageAccessible}" type="preRenderView"/>--> 
        <div style="width:100%;" id="syncRepDiv" >
            <h:form id="syncDetForm">
                <p:remoteCommand name="applyPrinci" actionListener="#{syncOppHistory.applyPrincipal(viewCompLookupService.selectedCompany)}" update="syncDetForm:inputPrincName"  />

                <div class="box box-info box-body" style="vertical-align: top;">
                    <div class="box-header with-border">
                        <div class="row">
                            <div class="col-md-12">
                                <p:panel id="filterSummary" style="padding:0;border:0px;width:100%; " >

                                    <p:row>
                                        <p:column>

                                            <p:outputLabel value=" #{custom.labels.get('IDS_PRINCI')}" />
                                        </p:column>
                                        <p:spacer width="4" />
                                        <p:column>

                                            <p:inputText id="inputPrincName" value="#{syncOppHistory.princiName}"  placeholder="[Select]" readonly="true"/>

                                        </p:column>



                                        <p:column>
                                            <!--// 24-05-2022 :  #8023 : CRM-5689  OppSync History Report - showing inactive manufacturers-->
                                            <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                              actionListener="#{viewCompLookupService.listPrincipals(0,'applyPrinci')}"
                                                              update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                              styleClass="btn-info btn-xs" />

                                        </p:column>
                                        <p:spacer width="20" />
                                        <p:column>
                                            <p:outputLabel value="From Date:" class="fmt-b"/>
                                        </p:column>

                                        <p:column style="width:10%">
                                            <!--<p:outputLabel styleClass="m" value=":" />-->
                                            &nbsp;
                                            <p:calendar id="startDate" pattern="#{globalParams.dateFormat}"  value="#{syncOppHistory.fromDate}"    showOn="button" style="width:50px"  rendered="#{commsMain.filterOption != 4}">

                                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                            </p:calendar>
                                        </p:column>

                                        <p:spacer width="10" />
                                        <p:column>
                                            <p:outputLabel value="To Date: " class="fmt-b"/>
                                        </p:column>
                                        <p:column style="width:120px">
                                            <!--<p:outputLabel styleClass="m" value=":" />-->
                                            &nbsp;
                                            <p:calendar id="endDate" pattern="#{globalParams.dateFormat}"  value="#{syncOppHistory.toDate}"  showOn="button"  rendered="#{commsMain.filterOption != 4}"  >
                                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                            </p:calendar>
                                        </p:column>
                                        &nbsp;&nbsp;&nbsp;
                                        <p:column>
                                            <!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                                            <!--                                            // #Task 4757 Add Ids for Buttons in Reporting screens-->
                                            <p:commandButton id="btnFetchRecords" value="Fetch Records" onstart="PF('inProgressDlg1').show();"  
                                                             oncomplete="PF('inProgressDlg1').hide();" action="#{syncOppHistory.load()}"  styleClass="btn btn-primary btn-xs" update=":syncDetForm:table" />

                                        </p:column>
                                        &nbsp;&nbsp;&nbsp; 
                                        <p:column>
                                        </p:column>
                                    </p:row>
                                </p:panel>

                            </div>
                        </div>
                    </div>
                    
                    <!-- bug #6683 CRM-5118: thorson: sync history filtered view of an opp very broken-->
                    <p:dataTable value="#{syncOppHistory.syncOppHistoryLst}" var="sync"
                                 filteredValue="#{syncOppHistory.filteredSyncOppHistoryList}"
                                 widgetVar="syncTbl"


                                 resizableColumns="true"
                                 draggableColumns="true"
                                 emptyMessage="No records found"

                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rowsPerPageTemplate="50,100" 
                                 paginatorAlwaysVisible="true" 
                                 id="table"
                                 paginator="true"
                                 rows="50"
                                 rowKey="#{sync.oppRecId}"
                                 style="height:75vh" 
                                 tableStyle="table-layout:auto" 
                                 resizeMode="expand"

                                 >
                        <p:ajax event="page" listener="#{syncOppHistory.onPageChange}" update=":syncDetForm:table" />

                        <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{sync.custName}" filterMatchMode="contains" sortBy="#{sync.custName}"   style="width:100px" >

<!--                            <p:outputLabel value="#{sync.custName}" />-->
                            <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                            <p:commandLink  value="#{sync.custName}"  actionListener="#{syncOppHistory.showOppHistoryDet(sync.flag,sync.oppRecId ,sync.oppId,sync.oppHistType,sync.description)}"   update=":dlgOppHistory"  style="text-decoration:underline"/>


                        </p:column>
                        <p:column headerText="#{custom.labels.get('IDS_DISTRI')}" filterBy="#{sync.distriname}" filterMatchMode="contains" sortBy="#{sync.custName}"  style="width:100px" >

                            <p:outputLabel value="#{sync.distriname}" />


                        </p:column>

                        <p:column headerText="#{custom.labels.get('IDS_PROGRAM')}" filterBy="#{sync.topic}" filterMatchMode="contains" sortBy="#{sync.topic}"  style="width:100px" >

                            <p:outputLabel value="#{sync.topic}" />


                        </p:column>
                        <p:column headerText="#{custom.labels.get('IDS_ACTIVITY')}" filterBy="#{sync.activity}" filterMatchMode="contains" sortBy="#{sync.activity}"  style="width:100px" >

                            <p:outputLabel value="#{sync.activity}" />


                        </p:column><!--
                        
                        --> 
                        <!--#13178 ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
                        <p:column headerText="#{custom.labels.get('IDS_ACT_STATUS')}" filterBy="#{sync.status}" filterMatchMode="contains" sortBy="#{sync.status}"  style="width:100px" >

                            <p:outputLabel value="#{sync.status}" />


                        </p:column>

                        <p:column headerText="#{custom.labels.get('IDS_FOLLOW_UP')}" filterBy="#{sync.followUp}" filterMatchMode="contains" sortBy="#{sync.followUp}"  style="width:100px" >

                            <p:outputLabel value="#{sync.followUp}" >
                                <f:convertDateTime pattern="#{globalParams.dateFormat}"/>

                            </p:outputLabel>


                        </p:column>
                        <!--                        18/05/2021 Feature  #4889 Repfabric / AVX CRMSync / Relabeling Fields / "Next Step"-->
                        <!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
                        <p:column headerText="#{custom.labels.get('IDS_NEXT_STEP')}" filterBy="#{sync.nextStep}" filterMatchMode="contains" 
                                  sortBy="#{sync.nextStep}"  style="width:100px" styleClass="might-overflow">

                            <p:outputLabel value="#{sync.nextStep}" />


                        </p:column>





                        <p:column headerText=" Last Modified Date " filterBy="#{sync.lastModifiedDate}" filterMatchMode="contains" sortBy="#{sync.lastModifiedDate}"  style="width:100px" >

                            <p:outputLabel value="#{rFUtilities.convertFromUTC(sync.lastModifiedDate)}" >
                                <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>

                            </p:outputLabel>
                        </p:column>
                        <p:column headerText="Last Modified User " filterBy="#{sync.lastModifiedUser}" filterMatchMode="contains" sortBy="#{sync.lastModifiedUser}"  style="width:100px" >

                            <p:outputLabel value="#{sync.lastModifiedUser}" />


                        </p:column>

                        <p:column headerText="CRM Sync Link " filterBy="#{sync.crmSyncLink}" filterMatchMode="contains" sortBy="#{sync.crmSyncLink}"  style="width:100px" >

                            <!--feature #3913:   CRM-4099: CRMSync Opp history report: why arent crm links hot?-->
                            <p:commandLink  
                                value="#{sync.crmSyncLink}"  
                                id="crmSyncLnk"
                                style="text-decoration: underline;color: #73b2d9"
                                onclick="window.open('#{syncOppHistory.appendHttp(sync.crmSyncLink)}', '_blank');"  
                                title="#{sync.crmSyncLink}"  ></p:commandLink>


                        </p:column>

                        <p:column headerText="History Comment " filterBy="#{sync.description}" filterMatchMode="contains" sortBy="#{sync.description}"  style="width:100px" >

                            <p:outputLabel value="#{sync.description}" />


                        </p:column>
                        <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                        <p:column id='done' headerText="Done" width="100"> 
                          
                            <p:selectBooleanCheckbox    id="booleanBtn"  value="#{sync.flag}" > 
                                <p:ajax event="change"  listener="#{syncOppHistory.changeFlagStatus(sync.flag , sync.oppRecId ,sync.oppId,sync.oppHistType,sync.description)}"  /> 
                            </p:selectBooleanCheckbox>   
<!--                             <p:selectBooleanCheckbox  rendered="#{sync.oppFlag == 0}" id="booleanBtn1"  value="#{syncOppHistory.flag1}" > 
                                <p:ajax event="change"  listener="#{syncOppHistory.changeFlagStatus(sync.flag , sync.oppRecId ,sync.oppId)}"  /> 
                            </p:selectBooleanCheckbox>    -->
                        </p:column>
                        
                        

                    </p:dataTable>
                </div>
            </h:form>
        </div>
        <!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
        <p:dialog  widgetVar="inProgressDlg1" closable="false" modal="true" header="Message" resizable="false"  >

            <h:form id ="frmPoll">
                <h:outputText value="Fetching Records..." id="txtStatus" />
                <p:outputPanel >
                    <br />
                    <h:graphicImage  library="images" name="ajax-loader.gif" id="loadingImg1"   />
                    <p:spacer width="5" />

                    <br /><br />
                </p:outputPanel>
            </h:form>
        </p:dialog>
        <ui:include src="/lookup/CompanyLookupDlg.xhtml"  />
        <ui:include src="ViewOppHistoryDlg.xhtml" />

        <script>

            window.onload = function () {
//                   document.getElementById("syncDetForm:startDate_input").style.width=100px;
                setTimeout(function () {
                    var t = performance.timing;
                    console.log(t.loadEventEnd - t.responseEnd);
                }, 0);
                myFunction();
            }

            function myFunction() {
                var x = "Total Height: " + screen.height + "px";
                console.log("height :::" + (parseInt(screen.height)));
                //                        document.getElementById("demo").innerHTML = x;
//                document.getElementById("repDiv").style.height = "" + (parseInt(screen.height) - 215) + "px";


            }



        </script>
        <style>

            .ui-datatable td[role="gridcell"], body .ui-treetable td[role="gridcell"] {
                position: relative;
                padding: 4px 10px !important;
            }

            /*            .ui-paginator {
                            margin-left: -1367px;
                        }
                        .q{
                            width: 100%!important;
                        }
                        .p{
                            width: 80%!important;
                        }*/

            .ui-datatable-scrollable-theadclone {
                visibility: collapse !important;
            }
            .sss{
                overflow-x: scroll;
                overflow-y: scroll;
                overflow: auto;
                position: fixed;
                height: 400px;
            }

            .ui-datatable-tablewrapper {
                overflow: initial !important; 
            }
            .ui-paginator {
                background-color:#ffffff1f!important;
            }
            .q{
                width: 100%!important;
            }
            .p{
                width: 80%!important;
            }


            #syncDetForm\:startDate_input{
                width: 8%;
            }

            #syncDetForm\:endDate_input{
                width: 8%;  
            }

        </style>


    </ui:define>

</ui:composition>
