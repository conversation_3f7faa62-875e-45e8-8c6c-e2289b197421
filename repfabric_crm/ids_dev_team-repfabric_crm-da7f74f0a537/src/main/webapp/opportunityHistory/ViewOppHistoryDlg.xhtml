<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <!--#1435: Opp Sync History-->
    <!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->

    <p:dialog id="dlgOppHistory" header="#{custom.labels.get('IDS_OPP')} Change History" widgetVar="oppHistoryDlg" width="1000" height="#{syncOppHistory.fieldCount > 10 ? 500:300}"   
              style="text-overflow: scroll;" closable="false" draggable="false" resizable="true"  onHide="refreshTable();"
              modal="true">
<!--        <p:ajax event="close"  oncomplete="PF('oppHistoryDlg').hide();"   listener="#{syncOppHistory.resetlist()}" />-->
        <h:form  id="oppDet"> 
            <p:remoteCommand name="refreshTable" immediate="true"  onstart="PF('syncTbl').getPaginator().setPage(#{syncOppHistory.pageValue});" />
            <p:panelGrid columns="3" columnClasses="ui-grid-col-3,ui-grid-col-9"
                         rendered="#{syncOppHistory.oppHistType != 50 and syncOppHistory.oppHistType != 51 and syncOppHistory.oppHistType != 52}"
                         styleClass="box-primary no-border ui-fluid" layout="grid"  id="oppHisDet">
                <!--Principal-->
                <!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtPrinci" value="#{custom.labels.get('IDS_PRINCI')}:"  rendered="#{syncOppHistory.oppPrincipal!= null }" />
                <!--text box or label--> 
                <!--copy to clipboard button-->
                <p:inputText  id="inpTxtPrinci"  value="#{syncOppHistory.oppPrincipal}" readonly="true" />
                <h:panelGroup>

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(1)"  rendered="#{syncOppHistory.oppPrincipal!= null }"/>

                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   id="princiFlag"  value="#{syncOppHistory.oppPrincipalFlag}"  >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppPrincipalFlag)}"   />

                    </p:selectBooleanCheckbox>

                </h:panelGroup>
                <!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <!--Customer1-->
                <p:outputLabel for="inpTxtCust" value="#{custom.labels.get('IDS_CUSTOMER')}:"  />
                <p:inputText  id="inpTxtCust"  value="#{syncOppHistory.oppCustomer}" readonly="true" />
                <h:panelGroup>

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(2)" />
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox    value="#{syncOppHistory.oppCustomerFlag}"   id="custFlag" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppCustomerFlag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>
                <!--Distributor2-->
                <!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtDistri" value="#{custom.labels.get('IDS_DISTRI')}:" rendered="#{syncOppHistory.oppDistri.length() ne 0 }" />

                <p:inputText  id="inpTxtDistri" value="#{syncOppHistory.oppDistri}" readonly="true" rendered="#{syncOppHistory.oppDistri.length() ne 0}" />
                <h:panelGroup rendered="#{syncOppHistory.oppDistri.length() ne 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(3)" rendered="#{syncOppHistory.oppDistri.length() ne 0 }"/>
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppDistriFlag}"   id="oppDistri" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppDistriFlag)}"   />

                    </p:selectBooleanCheckbox>

                </h:panelGroup>
                <!--Principal Contact3-->
                <!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtPrinciCont" value="#{custom.labels.get('IDS_PRINCI')} Contact:" rendered="#{syncOppHistory.oppPrincipalCont.length() ne 0}" />
                <p:inputText  id="inpTxtPrinciCont" value="#{syncOppHistory.oppPrincipalCont}" readonly="true" rendered="#{syncOppHistory.oppPrincipalCont.length() ne 0 }" />
                <h:panelGroup  rendered="#{syncOppHistory.oppPrincipalCont.length() ne 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(4)" rendered="#{syncOppHistory.oppPrincipalCont.length() ne 0 }"/>
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox       value="#{syncOppHistory.oppPrincipalContFlag}" id="princiCont" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppPrincipalContFlag)}"/>

                    </p:selectBooleanCheckbox>

                </h:panelGroup>

                <!--Customer Contact4-->
                <!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtCustCont" value="#{custom.labels.get('IDS_CUSTOMER')} Contact:"  rendered="#{syncOppHistory.oppCustomerCont.length() ne 0 }"   />
                <p:inputText  id="inpTxtCustCont" value="#{syncOppHistory.oppCustomerCont}" readonly="true" rendered="#{syncOppHistory.oppCustomerCont.length() ne 0 }"  />
                <h:panelGroup  rendered="#{syncOppHistory.oppCustomerCont.length() ne 0}"  >

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(5)"  rendered="#{syncOppHistory.oppCustomerCont.length() ne 0 }" />

                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox    value="#{syncOppHistory.oppCustomerContFlag}"  id="custContact" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppCustomerContFlag)}"   />

                    </p:selectBooleanCheckbox>
                </h:panelGroup>

                <!--Distributor Contact5-->
                <!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->


                <p:outputLabel for="inpTxtDistriCont" value="#{custom.labels.get('IDS_DISTRI')} Contact:"  rendered="#{syncOppHistory.oppDistriCont.length() ne 0}" />

                <p:inputText  id="inpTxtDistriCont" value="#{syncOppHistory.oppDistriCont}" readonly="true"  rendered="#{syncOppHistory.oppDistriCont.length() ne 0 }" />
                <h:panelGroup  rendered="#{syncOppHistory.oppDistriCont.length() ne 0}" >

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(6)" rendered="#{syncOppHistory.oppDistriCont.length() ne 0 }" />
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppDistriContFlag}"     id="distriCont"  >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppDistriContFlag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>
                <!--Topic6-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtTopic" value="#{custom.labels.get('IDS_PROGRAM')}:"  rendered="#{syncOppHistory.oppCustProgram.length() ne 0}"/>
                <p:inputText  id="inpTxtTopic"  value="#{syncOppHistory.oppCustProgram}" readonly="true" rendered="#{syncOppHistory.oppCustProgram.length() ne 0 }" />
                <h:panelGroup rendered="#{syncOppHistory.oppCustProgram.length()  ne 0 }">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(7)" rendered="#{syncOppHistory.oppCustProgram.length() ne 0 }"/>
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox     value="#{syncOppHistory.oppCustProgramFlag}"   id="topic" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppCustProgram)}" />


                    </p:selectBooleanCheckbox>

                </h:panelGroup>
                <!--Sales Team7--><!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtSalesTeam" value="#{custom.labels.get('IDS_SALES_TEAM')}:" rendered="#{syncOppHistory.oppSmanName.length() ne 0}" />
                <p:inputText  id="inpTxtSalesTeam" value="#{syncOppHistory.oppSmanName}" readonly="true"  rendered="#{syncOppHistory.oppSmanName.length() ne 0}"/>
                <h:panelGroup rendered="#{syncOppHistory.oppSmanName.length() ne 0}" >

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(8)" rendered="#{syncOppHistory.oppSmanName.length() ne 0}"/>
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox  value="#{syncOppHistory.oppSmanNameFlag}"   id="smanName" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppSmanNameFlag)}"   />

                    </p:selectBooleanCheckbox>

                </h:panelGroup>
                <!--Follow Up8-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtFollowUp" value="#{custom.labels.get('IDS_FOLLOW_UP')} :" rendered="#{syncOppHistory.oppFollowUp !=null}" />
                <p:inputText  id="inpTxtFollowUp"  value="#{syncOppHistory.oppFollowUp}" readonly="true" rendered="#{syncOppHistory.oppFollowUp !=null}"  >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}"/>

                </p:inputText>

                <h:panelGroup  rendered="#{syncOppHistory.oppFollowUp !=null}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(9)" rendered="#{syncOppHistory.oppFollowUp !=null}"/>
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppFollowUpFlag}"    id="followUpDt">
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppFollowUpFlag)}"   />

                    </p:selectBooleanCheckbox>

                </h:panelGroup>

                <!--Next Step9-->
                <!--Feature #5025 RE: Repfabric / AVX CRMSync / Relabeling Fields / "Next Step" (Pending)-->
                <p:outputLabel for="inpTxtNextStep" value="#{custom.labels.get('IDS_NEXT_STEP')} :" rendered="#{syncOppHistory.oppNextStep.length() ne 0}" />
                <p:inputText  id="inpTxtNextStep" value="#{syncOppHistory.oppNextStep}" readonly="true" rendered="#{syncOppHistory.oppNextStep.length() ne 0}" />
                <h:panelGroup  rendered="#{syncOppHistory.oppNextStep.length() ne 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(10)" rendered="#{syncOppHistory.oppNextStep.length() ne 0}" />

                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox    value="#{syncOppHistory.oppNextStepFlag}"   id="nextStep" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppNextStepFlag)}"   />

                    </p:selectBooleanCheckbox>

                </h:panelGroup>
                <!--Activity10-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtActivity" value="#{custom.labels.get('IDS_OPP_STAGE')} :"  rendered="#{syncOppHistory.oppActStageName.length() ne  0}"/>
                <p:inputText  id="inpTxtActivity" value="#{syncOppHistory.oppActStageName}" readonly="true" rendered="#{syncOppHistory.oppActStageName.length() ne  0}"/>
                <h:panelGroup rendered="#{syncOppHistory.oppActStageName.length() ne  0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(11)" rendered="#{syncOppHistory.oppActStageName.length() ne  0}"/>
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppActStageNameFlag}"  id="actStageName" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppActStageNameFlag)}"  />

                    </p:selectBooleanCheckbox>

                </h:panelGroup>
                <!--Status11-->
                <!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                 <!--#13178 ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
                <p:outputLabel for="inpTxtActStatus" value="#{custom.labels.get('IDS_ACT_STATUS')} :"   rendered="#{syncOppHistory.oppStatus.length() ne 0}"/>
                <p:inputText  id="inpTxtActStatus" value="#{syncOppHistory.oppStatus}" readonly="true"  rendered="#{syncOppHistory.oppStatus.length() ne 0}"/>
                <h:panelGroup  rendered="#{syncOppHistory.oppStatus.length() ne 0}" >

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(12)"  rendered="#{syncOppHistory.oppStatus.length() ne 0}"/>
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppStatusFlag}"    id="stat" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppStatusFlag)}"  />

                    </p:selectBooleanCheckbox>

                </h:panelGroup>
                <!--Priority12-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtPriority" value="#{custom.labels.get('IDS_PRIORITY')} :"  rendered="#{syncOppHistory.oppPriority ne 0}"/>
                <p:inputText  id="inpTxtPriority" value="#{syncOppHistory.oppPriority}" readonly="true" rendered="#{syncOppHistory.oppPriority ne 0}" />
                <h:panelGroup   rendered="#{syncOppHistory.oppPriority ne 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(13)"  rendered="#{syncOppHistory.oppPriority ne 0}"/>
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppPriorityFlag}"     id="priority" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppPriorityFlag)}"   />

                    </p:selectBooleanCheckbox>

                </h:panelGroup>
                <!--Potential13-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtPotential" value="#{custom.labels.get('IDS_POTENTIAL')} :"  rendered="#{syncOppHistory.oppPotential ne 0}" />
                <p:inputText  id="inpTxtPotential" value="#{syncOppHistory.oppPotential}" readonly="true"  rendered="#{syncOppHistory.oppPotential ne 0}"/>
                <h:panelGroup rendered="#{syncOppHistory.oppPotential ne 0}" >

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(14)" rendered="#{syncOppHistory.oppPotential ne 0}" />

                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppPotentialFlag}"    id="potenl" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppPotentialFlag)}"  />

                    </p:selectBooleanCheckbox>

                </h:panelGroup>
                <!--EAU14-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtEAU" value="#{custom.labels.get('IDS_EAU')} :"  rendered="#{syncOppHistory.oppEau ne 0}"/>
                <p:inputText  id="inpTxtEAU" value="#{syncOppHistory.oppEau}" readonly="true" rendered="#{syncOppHistory.oppEau ne 0}" />
                <h:panelGroup  rendered="#{syncOppHistory.oppEau ne 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(15)" rendered="#{syncOppHistory.oppEau ne 0}" />
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox  value="#{syncOppHistory.oppEauFlag}"   id="eau" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppEauFlag)}"   />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>

                <!--Value15-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtValue" value="#{custom.labels.get('IDS_VALUE')} :" rendered="#{syncOppHistory.oppValue ne 0}" />
                <p:inputText  id="inpTxtValue" value="#{syncOppHistory.oppValue}" readonly="true"  rendered="#{syncOppHistory.oppValue ne 0}"/>
                <h:panelGroup  rendered="#{syncOppHistory.oppValue ne 0}" >

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(16)" rendered="#{syncOppHistory.oppValue ne 0}" />
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox  value="#{syncOppHistory.oppValueFlag}"     id="val" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppValueFlag)}"  />

                    </p:selectBooleanCheckbox>
                </h:panelGroup>

                <!--Prototype Date16-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtProtoDate" value="#{custom.labels.get('IDS_PROTO_DATE')} :"  rendered="#{syncOppHistory.oppProtoDate !=null}"/>
                <p:inputText  id="inpTxtProtoDate" value="#{syncOppHistory.oppProtoDate}" readonly="true" rendered="#{syncOppHistory.oppProtoDate !=null}"  />
                <h:panelGroup  rendered="#{syncOppHistory.oppProtoDate !=null}"  >

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(17)" rendered="#{syncOppHistory.oppProtoDate !=null}" />
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppProtoDateFlag}"     id="protodt" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppProtoDateFlag)}"   />

                    </p:selectBooleanCheckbox>

                </h:panelGroup>
                <!--Competitor 1 17-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtComp1" value="#{custom.labels.get('IDS_COMPETITOR1')} :" rendered="#{syncOppHistory.oppCompetitor1.length() ne 0}" />
                <p:inputText  id="inpTxtComp1" value="#{syncOppHistory.oppCompetitor1}"  readonly="true" rendered="#{syncOppHistory.oppCompetitor1.length() ne 0}"/>
                <h:panelGroup rendered="#{syncOppHistory.oppCompetitor1.length() ne 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(18)" rendered="#{syncOppHistory.oppCompetitor1.length() ne 0}" />
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox  value="#{syncOppHistory.oppCompetitor1Flag}"  id="competr1">
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppCompetitor1Flag)}"  />

                    </p:selectBooleanCheckbox>

                </h:panelGroup>

                <!--Competitor 2 18-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtComp2" value="#{custom.labels.get('IDS_COMPETITOR2')} :" rendered="#{syncOppHistory.oppCompetitor2.length() ne 0}" />
                <p:inputText  id="inpTxtComp2" value="#{syncOppHistory.oppCompetitor2}" readonly="true" rendered="#{syncOppHistory.oppCompetitor2.length() ne 0}"/>
                <h:panelGroup  rendered="#{syncOppHistory.oppCompetitor2.length() ne 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(19)" rendered="#{syncOppHistory.oppCompetitor2.length() ne 0}" />


                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox  value="#{syncOppHistory.oppCompetitor2Flag}"      id="competr2">
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppCompetitor2Flag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>
                <!--Description19-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtDescr" value="#{custom.labels.get('IDS_OPP_DESC')} :" rendered="#{syncOppHistory.oppDescr.length() ne 0}" />
                <p:inputTextarea  id="inpTxtDescr"  rows="4"  autoResize="false" value="#{syncOppHistory.oppDescr}" readonly="true"  rendered="#{syncOppHistory.oppDescr.length() ne 0}" />
                <h:panelGroup rendered="#{syncOppHistory.oppDescr.length() ne 0}"  >

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(20)" rendered="#{syncOppHistory.oppDescr.length() ne 0}"/>
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppDescrFlag}" id="desc"   >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppDescrFlag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>

                <!--Reporting Comments20-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtRptCmts" value="#{custom.labels.get('IDS_RPT_COMMENTS')} :" rendered="#{syncOppHistory.oppRptComments.length() ne 0}" />
                <p:inputTextarea rows="4"  autoResize="false" id="inpTxtRptCmts" value="#{syncOppHistory.oppRptComments}" readonly="true" rendered="#{syncOppHistory.oppRptComments.length() ne 0}" />
                <h:panelGroup rendered="#{syncOppHistory.oppRptComments.length() ne 0}" >

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(21)" rendered="#{syncOppHistory.oppRptComments.length() ne 0}" />
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppRptCommentsFlag}" id="rptComts" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppRptCommentsFlag)}"    />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>

                <!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <!--Status21-->
                <p:outputLabel for="inpTxtStatus" value="Close Status:"  />
                <p:inputText  id="inpTxtStatus" value="#{syncOppHistory.oppCloseStatusVal}"  readonly="true" />
                <h:panelGroup>

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(22)" />
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppCloseStatusFlag}"     id="oppCloseStat" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppCloseStatusFlag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>
                <!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <!--Close Date 22-->
                <p:outputLabel for="inpTxtCloseDate" value="Close Date:"  rendered="#{syncOppHistory.oppCloseDate != null}"/>
                <p:inputText  id="inpTxtCloseDate" value="#{syncOppHistory.oppCloseDate}" readonly="true"     rendered="#{syncOppHistory.oppCloseDate != null}" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}"/>

                </p:inputText>
                <h:panelGroup  rendered="#{syncOppHistory.oppCloseDate != null}"  >

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(23)"  rendered="#{syncOppHistory.oppCloseDate != null}" />

                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppCloseDateFlag}"     id="closeDate" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppCloseDateFlag)}"  />

                    </p:selectBooleanCheckbox>

                </h:panelGroup>
                <!-- //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <!--Close Reason 23-->
                <p:outputLabel for="inpTxtCloseReason" value="Close Reason:" rendered="#{syncOppHistory.oppCloseReason.length() ne 0}" />
                <p:inputText  id="inpTxtCloseReason" value="#{syncOppHistory.oppCloseReason}" readonly="true" rendered="#{syncOppHistory.oppCloseReason.length() ne 0}"  />
                <h:panelGroup  rendered="#{syncOppHistory.oppCloseReason.length() ne 0}" >

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(24)" rendered="#{syncOppHistory.oppCloseReason.length() ne 0}" />
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppCloseReasonFlag}"     id="closeRsn" >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppCloseReasonFlag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>

                <!--Comments 24-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <p:outputLabel for="inpTxtComments" value="#{custom.labels.get('IDS_COMMENTS')} :" rendered="#{syncOppHistory.oppComment.length() ne 0}" />
                <p:inputText  id="inpTxtComments"  value="#{syncOppHistory.oppComment}"   readonly="true" rendered="#{syncOppHistory.oppComment.length() ne 0}"  />
                <h:panelGroup rendered="#{syncOppHistory.oppComment.length() ne 0}"  >

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" title="Click here to copy" onclick="copyToClipboard(25)" rendered="#{syncOppHistory.oppComment.length() ne 0}" />
                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox   value="#{syncOppHistory.oppCommentFlag}" id="commnt"  >
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.oppCommentFlag)}"   />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>
            </p:panelGrid>
            <!--// 10-05-2022 :  #7901 : CRM-2639:  line item level tracking for crmsync no connection-->
            <br/>
            <p:outputPanel rendered="#{syncOppHistory.oppHistType == 50 or syncOppHistory.oppHistType == 51 or syncOppHistory.oppHistType == 52}">
                <div class="ui-g ui-fluid header-bar">
                    <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                        <p:outputLabel styleClass="acct-name-hdr" value="#{syncOppHistory.description}" class="sub_title" style="width:100%"/>
                    </div>
                </div>  
            </p:outputPanel>

            <!--// 10-05-2022 :  #7901 : CRM-2639:  line item level tracking for crmsync no connection-->
            <p:panelGrid columns="3" columnClasses="ui-grid-col-3,ui-grid-col-9"
                         rendered="#{syncOppHistory.oppHistType == 50 or syncOppHistory.oppHistType == 51 or syncOppHistory.oppHistType == 52}"
                         styleClass="box-primary no-border ui-fluid" layout="grid"  id="oppHisDet1">
                <p:outputLabel for="inpTxtMfgPartNum" value="#{custom.labels.get('IDS_MFG_PART_NUM')} :" 
                               rendered="#{oppLineItemHistory.newItemPartManf.length() > 0}" />
                <p:inputText  id="inpTxtMfgPartNum" value="#{oppLineItemHistory.newItemPartManf}" readonly="true" 
                              rendered="#{oppLineItemHistory.newItemPartManf.length() > 0}"/>
                <h:panelGroup  rendered="#{oppLineItemHistory.newItemPartManf.length() > 0}">
                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" 
                                     title="Click here to copy" onclick="copyToClipboard(26)" 
                                     rendered="#{oppLineItemHistory.newItemPartManf.length() > 0}" />


                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox  value="#{syncOppHistory.newItemPartManfFlag}"      id="selectMfgPart">
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.newItemPartManfFlag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>

                <p:outputLabel for="inpTxtCustPart" value="#{custom.labels.get('IDS_CUST_PART')} :" 
                               rendered="#{oppLineItemHistory.newItemPartCust.length() > 0}" />
                <p:inputText  id="inpTxtCustPart" value="#{oppLineItemHistory.newItemPartCust}" readonly="true" 
                              rendered="#{oppLineItemHistory.newItemPartCust.length() > 0}"/>
                <h:panelGroup  rendered="#{oppLineItemHistory.newItemPartCust.length() > 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" 
                                     title="Click here to copy" onclick="copyToClipboard(27)" 
                                     rendered="#{oppLineItemHistory.newItemPartCust.length() > 0}" />


                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox  value="#{syncOppHistory.newItemPartCustFlag}"      id="selectPartNum">
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.newItemPartCustFlag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>

                <p:outputLabel for="inpTxtLineItemDesc" value="#{custom.labels.get('IDS_LINE_ITEM_DESC')} :" 
                               rendered="#{oppLineItemHistory.newItemPartDesc.length() > 0}" />
                <p:inputTextarea  id="inpTxtLineItemDesc" rows="3" autoResize="false" value="#{oppLineItemHistory.newItemPartDesc}" 
                                  readonly="true" maxlength="200" styleClass="partNumBox" 
                              rendered="#{oppLineItemHistory.newItemPartDesc.length() > 0}"/>
                <h:panelGroup  rendered="#{oppLineItemHistory.newItemPartDesc.length() > 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" 
                                     title="Click here to copy" onclick="copyToClipboard(28)" 
                                     rendered="#{oppLineItemHistory.newItemPartDesc.length() > 0}" />


                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox  value="#{syncOppHistory.newItemPartDescFlag}"      id="selectLineItemDesc">
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.newItemPartDescFlag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>

                <p:outputLabel for="inpTxtLineItemQnty" value="#{custom.labels.get('IDS_LINE_ITEM_QTY')} :" 
                               rendered="#{oppLineItemHistory.newItemQnty > 0}" />
                <p:inputText  id="inpTxtLineItemQnty" value="#{oppLineItemHistory.newItemQnty}" readonly="true" 
                              rendered="#{oppLineItemHistory.newItemQnty > 0}"/>
                <h:panelGroup  rendered="#{oppLineItemHistory.newItemQnty > 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" 
                                     title="Click here to copy" onclick="copyToClipboard(29)" 
                                     rendered="#{oppLineItemHistory.newItemQnty > 0}" />


                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox  value="#{syncOppHistory.newItemQntyFlag}"      id="selectLineItemQnty">
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.newItemQntyFlag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>

                <p:outputLabel for="inpTxtLineItemCost" value="#{custom.labels.get('IDS_COST')} :" 
                               rendered="#{oppLineItemHistory.newItemCost > 0}" />
                <p:inputText  id="inpTxtLineItemCost" value="#{oppLineItemHistory.newItemCost}" readonly="true" 
                              rendered="#{oppLineItemHistory.newItemCost > 0}"/>
                <h:panelGroup  rendered="#{oppLineItemHistory.newItemCost > 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" 
                                     title="Click here to copy" onclick="copyToClipboard(30)" 
                                     rendered="#{oppLineItemHistory.newItemCost > 0}" />


                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox  value="#{syncOppHistory.newItemCostFlag}"      id="selectLineItemCost">
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.newItemCostFlag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>

                <p:outputLabel for="inpTxtLineItemResale" value="#{custom.labels.get('IDS_RESALE')} :" 
                               rendered="#{oppLineItemHistory.newItemResale > 0}" />
                <p:inputText  id="inpTxtLineItemResale" value="#{oppLineItemHistory.newItemResale}" readonly="true" 
                              rendered="#{oppLineItemHistory.newItemResale > 0}"/>
                <h:panelGroup  rendered="#{oppLineItemHistory.newItemResale > 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" 
                                     title="Click here to copy" onclick="copyToClipboard(31)" 
                                     rendered="#{oppLineItemHistory.newItemResale > 0}" />


                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox  value="#{syncOppHistory.newItemResaleFlag}"      id="selectLineItemResale">
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.newItemResaleFlag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>

                <p:outputLabel for="inpTxtLineItemStatus" value="Status :" 
                               rendered="#{oppLineItemHistory.newLineItemStatus >= 0}" />
                <p:inputText  id="inpTxtLineItemStatus" value="#{oppLineItemHistory.newLineItemStatus==0?'Open':oppLineItemHistory.newLineItemStatus==1?'Won':'Lost'}" readonly="true" 
                              rendered="#{oppLineItemHistory.newLineItemStatus >= 0}"/>
                <h:panelGroup  rendered="#{oppLineItemHistory.newLineItemStatus >= 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" 
                                     title="Click here to copy" onclick="copyToClipboard(32)" 
                                     rendered="#{oppLineItemHistory.newLineItemStatus >= 0}" />


                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox  value="#{syncOppHistory.newLineItemStatusFlag}"      id="selectLineItemStatus">
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.newLineItemStatusFlag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>
  <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                <p:outputLabel for="inpTxtLineItemCommRate" value="#{custom.labels.get('IDS_COMMISSION')} Rate :" 
                               rendered="#{oppLineItemHistory.newCommRate > 0}" />
                <p:inputText  id="inpTxtLineItemCommRate" value="#{oppLineItemHistory.newCommRate}" readonly="true" 
                              rendered="#{oppLineItemHistory.newCommRate > 0}"/>
                <h:panelGroup  rendered="#{oppLineItemHistory.newCommRate > 0}">

                    <p:commandButton class="btn-xs btn-primary" style="width:25px;height: 25px;" icon="fa fa-clipboard" 
                                     title="Click here to copy" onclick="copyToClipboard(33)" 
                                     rendered="#{oppLineItemHistory.newCommRate > 0}" />


                    <p:spacer width="24" />
                    <p:selectBooleanCheckbox  value="#{syncOppHistory.newCommRateFlag}"      id="selectLineItemCommRate">
                        <p:ajax event="change"  listener="#{syncOppHistory.setFieldVal(syncOppHistory.newCommRateFlag)}"  />

                    </p:selectBooleanCheckbox>


                </h:panelGroup>
            </p:panelGrid>
            <p:panelGrid columns="3" columnClasses="ui-grid-col-3,ui-grid-col-9"
                         styleClass="box-primary no-border ui-fluid" layout="grid"  id="oppHisDet2">
                <!--Mark as Done-->
                <!--                <p:outputLabel for="inpTxtDone" value="Mark as Done"  />
                                <p:outputLabel  id="inpTxtDone" />-->
                <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
                <h:panelGroup></h:panelGroup>
                <h:panelGroup></h:panelGroup>
                <p:selectBooleanCheckbox id="markDone" itemLabel="Mark as Done"  value="#{syncOppHistory.flag}"  style="margin-left:-120px">
                    <p:ajax event="change"  listener="#{syncOppHistory.changeStatus(syncOppHistory.flag ,syncOppHistory.oppRecId ,syncOppHistory.oppId)}"   oncomplete="refreshTable();"  />

                </p:selectBooleanCheckbox>

            </p:panelGrid>
            <!--OK button-->
            <!--                  //Bug #7177   CRM-5327 synchistory: completely buggy and not functional-->
            <h:panelGroup style="display: block;text-align: center">
                <p:commandButton id="ok" value="OK"  styleClass="btn btn-success  btn-xs"    oncomplete="PF('oppHistoryDlg').hide();"   >

                </p:commandButton>

            </h:panelGroup>
        </h:form>
    </p:dialog>


    <script>

        function copyToClipboard(i) {
//            alert("clipboard");

            switch (i)
            {
                case 1:
                    document.getElementById("oppDet:inpTxtPrinci").select();
                    break;
                case 2:
                    document.getElementById("oppDet:inpTxtCust").select();
                    break;
                case 3:
                    document.getElementById("oppDet:inpTxtDistri").select();
                    break;
                case 4:
                    document.getElementById("oppDet:inpTxtPrinciCont").select();
                    break;
                case 5:
                    document.getElementById("oppDet:inpTxtCustCont").select();
                    break;
                case 6:
                    document.getElementById("oppDet:inpTxtDistriCont").select();
                    break;
                case 7:
                    document.getElementById("oppDet:inpTxtTopic").select();
                    break;

                case 8:
                    document.getElementById("oppDet:inpTxtSalesTeam").select();
                    break;
                case 9:
                    document.getElementById("oppDet:inpTxtFollowUp").select();
                    break;
                case 10:
                    document.getElementById("oppDet:inpTxtNextStep").select();
                    break;
                case 11:
                    document.getElementById("oppDet:inpTxtActivity").select();
                    break;
                case 12:
                    document.getElementById("oppDet:inpTxtActStatus").select();
                    break;
                case 13:
                    document.getElementById("oppDet:inpTxtPriority").select();
                    break;
                case 14:
                    document.getElementById("oppDet:inpTxtPotential").select();
                    break;

                case 15:
                    document.getElementById("oppDet:inpTxtEAU").select();
                    break;
//                    
//                    
//                    
//                    
//                    
                case 16:
                    document.getElementById("oppDet:inpTxtValue").select();
                    break;
                case 17:
                    document.getElementById("oppDet:inpTxtProtoDate").select();
                    break;
                case 18:
                    document.getElementById("oppDet:inpTxtComp1").select();
                    break;
                case 19:
                    document.getElementById("oppDet:inpTxtComp2").select();
                    break;
                case 20:
                    document.getElementById("oppDet:inpTxtDescr").select();
                    break;
                case 21:
                    document.getElementById("oppDet:inpTxtRptCmts").select();
                    break;
                case 22:
                    document.getElementById("oppDet:inpTxtStatus").select();
                    break;
                case 23:
                    document.getElementById("oppDet:inpTxtCloseDate").select();
                    break;
                case 24:
                    document.getElementById("oppDet:inpTxtCloseReason").select();
                    break;
                case 25:
                    document.getElementById("oppDet:inpTxtComments").select();
                    break;
                case 26:
                    document.getElementById("oppDet:inpTxtMfgPartNum").select();
                    break;
                case 27:
                    document.getElementById("oppDet:inpTxtCustPart").select();
                    break;
                case 28:
                    document.getElementById("oppDet:inpTxtLineItemDesc").select();
                    break;
                case 29:
                    document.getElementById("oppDet:inpTxtLineItemQnty").select();
                    break;
                case 30:
                    document.getElementById("oppDet:inpTxtLineItemCost").select();
                    break;
                case 31:
                    document.getElementById("oppDet:inpTxtLineItemResale").select();
                    break;
                case 32:
                    document.getElementById("oppDet:inpTxtLineItemStatus").select();
                    break;
                case 33:
                    document.getElementById("oppDet:inpTxtLineItemCommRate").select();
                    break;
//                        

//                case 23 :
//                    document.getElementById("oppDet:inpTxtTopic").select();

            }
            document.execCommand('copy');
        }
    </script>
</ui:composition>