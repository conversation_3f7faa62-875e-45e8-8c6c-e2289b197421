<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: Roles.xhtml
// Author: Sarayu
//*********************************************************
/*
*
* Copyright (c) 2015 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">
    <!--#363 :page title-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>  Salesperson Activity </title>
    </ui:define>

    <ui:define name="metadata">
        <f:metadata> 
            <f:viewAction action="#{userActivity.init()}"/>
            <!-- #6461: CRM-5035: Tutorial button not working on Salesperson Activity report page for Sol-Tech Group - poornima -->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('USER_ACT'))}" />
            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('USER_ACT')}" />
        </f:metadata>
    </ui:define>

    <ui:define name="title">
        <h:form>
        </h:form>
        <!--       Task:1379: MIGUAT-179: reports: relabel "User Activities" to "Salesperson Activity"-->
        Salesperson Activity
        <ui:include src="/help/Help.xhtml"/>
    </ui:define>


    <ui:define name="menu">
        <h:form>

        </h:form>
        <ul class="sidebar-menu tree" data-widget="tree">
            <h:form >
            </h:form>  
        </ul>
    </ui:define>


    <ui:define name="body">

        <f:event listener="#{userActivity.isPageAccessible}" type="preRenderView"/>


        <div class="box box-info box-body" style="vertical-align: top">


            <div class="left-column">
                <h:form id="user">
                    <p:dataTable value="#{users.getDependentUserList()}"  var="u" id="userDT" selection="#{userActivity.selectedUserList}"  
                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                                 emptyMessage=" " paginator="true" rows="30" paginatorAlwaysVisible="false" 
                                 rowKey="#{u.userId}"  rowSelectMode="add" >


                        <p:ajax  event="rowSelectCheckbox" listener="#{userActivity.onUserSelect}"  update=" :frm2" />
                        <p:ajax  event="rowUnselectCheckbox" listener="#{userActivity.onUserUnselect}"  update=":frm2" />
                        <p:ajax  event="rowSelect" listener="#{userActivity.onUserSelect}"  update=" :frm2" />
                        <p:ajax event="rowUnselect" listener="#{userActivity.onUserUnselect}" update=" :frm2" />
                        <p:ajax  event="toggleSelect" listener="#{userActivity.onToggleSelect}" update=" :frm2"  />

                        <p:column selectionMode="multiple"   />
                        <p:column  filterMatchMode="contains" headerText="Users"   id="usr" style="width: 200px;" filterBy="#{u.userName}">
                            #{u.userName}
                        </p:column> 
                    </p:dataTable> 
                </h:form>
            </div>

            <div class="right-column">
                <h:form id="frm2">
                    <h:panelGrid columns="16">
                        From:
                        <p:spacer width="4px"/>
                        <p:calendar value="#{userActivity.fromDate}" pattern="#{globalParams.dateFormat}"  requiredMessage="Please select Date" showOn="button">
                            <p:ajax event="dateSelect" process="@this" listener="#{userActivity.onUserSelect}" update =":frm2 :frm3 :enquiryForm :activityForm" />
                        </p:calendar>
                        <p:spacer width="4px"/>
                        To:
                        <p:spacer width="4px"/>
                        <p:calendar value="#{userActivity.toDate}" pattern="#{globalParams.dateFormat}" requiredMessage="Please select Date" showOn="button">
                            <p:ajax event="dateSelect" process="@this" listener="#{userActivity.onUserSelect}" update =":frm2 :frm3 :enquiryForm :activityForm" />
                        </p:calendar>
                        <p:spacer width="4px"/>
                        <p:selectOneMenu value="#{userActivity.compType}" style="width: 200px"  >
                            <f:selectItem itemValue="2" itemLabel="#{custom.labels.get('IDS_CUSTOMER')}" />
                            <f:selectItem itemValue="1" itemLabel="#{custom.labels.get('IDS_PRINCI')}" />
                            <!--Feature #3650:   Salesperson usage report-->
                            <f:selectItem itemValue="3" itemLabel="User" />
                            <p:ajax event="change" listener="#{userActivity.onChangeFunction}" />
                        </p:selectOneMenu>
                        <p:spacer width="4px"/>
                        <!--                        // #Task 4757 Add Ids for Buttons in Reporting screens-->
                        <!--Feature #3650:   Salesperson usage report-->
                        <p:commandButton id="btnUserActView" value="View" action="#{userActivity.viewUserActivity()}" onclick=" PrimeFaces.monitorDownload(start, stop);
                                PF('pollStat').start();" update=":frm3:dtUserAct :frm2" styleClass="btn btn-primary btn-xs"/>

                        <p:spacer width="4px"/>

                        <p:spacer width="4px"/>
                        <p:commandButton id="activityJournal" style="width:140px;position:relative;left:-4px;"  value="Export Journals" styleClass=" btn-primary btn-xs"
                                         disabled="#{userActivity.selectedUserList.size()==0}"  oncomplete="PF('enquiryDialog').show();" update=":enquiryForm"/>


                        <p:commandButton id="activities" style="width:145px;"  value="Export Activities" styleClass="btn btn-primary btn-xs" 
                                         disabled="#{userActivity.selectedUserList.size()==0}"  oncomplete="PF('activityDialog').show();" update=":activityForm"/>





                    </h:panelGrid>
                </h:form>
                <h:form id="frm3">
                    <p:dataTable id="dtUserAct"   value="#{userActivity.userActivityList}" var="act" rowKey="#{act.recId}" >
                        <!--Feature #3650:   Salesperson usage report-->
                        <p:column headerText="#{userActivity.getLabel(userActivity.compType)}" filterBy="#{act.repCompName}" sortBy="#{act.repCompName}">
                            <h:outputText value="#{act.repCompName}" >
                            </h:outputText>
                            <f:facet name="footer" >
                                Total
                            </f:facet>
                        </p:column>

                        <p:column headerText="New #{custom.labels.get('IDS_OPPS')}" style="text-align: center" sortBy="#{act.repNewOppCount}">
                            <p:commandLink actionListener="#{userActivity.getnewOppList(act.repCompName, act.repCompId,'INS_USER','INS_USER_NAME')}"  onclick=" PrimeFaces.monitorDownload(start, stop);"   oncomplete="PF('dlgOppInq').show();PF('dlgMsg1').hide();" update=":frm2 :frm3:dtUserAct :newOppPnlForm:dtNewOpp :dlgOppInqPnl">

                                <h:outputText value="#{act.repNewOppCount}" />
                            </p:commandLink>
                            <f:facet name="footer" >
                                #{userActivity.newOppTotal}
                            </f:facet>
                        </p:column>

                        <!--Opportunities Owned-->
                        <p:column headerText="Owned #{custom.labels.get('IDS_OPPS')}" style="text-align: center" sortBy="#{act.repOwnedOppCount}">
                            <p:commandLink actionListener="#{userActivity.getnewOppList(act.repCompName, act.repCompId, 'OPP_OWNER' , 'OWNER_NAME')}" oncomplete="PF('dlgOppInq').show()" update=":frm2 :frm3:dtUserAct :newOppPnlForm:dtNewOpp :dlgOppInqPnl">

                                <h:outputText value="#{act.repOwnedOppCount}" />
                            </p:commandLink>
                            <f:facet name="footer" >
                                #{userActivity.oppOwnedTotal}
                            </f:facet>
                        </p:column>

                        <p:column headerText="#{custom.labels.get('IDS_ACT_JOURNAL')}" style="text-align: center" sortBy="#{act.repJournalCount}">
                            <p:commandLink  actionListener="#{userActivity.getActJournalList(act.repCompName, act.repCompId)}" oncomplete="PF('dlgActJournal').show()" update=":frm2 :frm3 :newOppPnlForm :actJournalForm:dtActJournal :actJournalPnl">

                                <h:outputText value="#{act.repJournalCount}" />
                            </p:commandLink>
                            <f:facet name="footer" >
                                #{userActivity.journalTotal}
                            </f:facet>
                        </p:column>

                        <p:column headerText="Emails Sent" style="text-align: center" sortBy="#{act.repEmailOutCount}">
                            <h:outputText value="#{act.repEmailOutCount}" />
                            <f:facet name="footer" >
                                #{userActivity.emailSentTotal}
                            </f:facet>
                        </p:column>

                        <p:column headerText="Emails Received" style="text-align: center" sortBy="#{act.repEmailInCount}">
                            <h:outputText value="#{act.repEmailInCount}" />
                            <f:facet name="footer" >
                                #{userActivity.emailRecvdTotal}
                            </f:facet>
                        </p:column>


                    </p:dataTable>
                </h:form>
                <p:dialog responsive="true" widgetVar="enquiryDialog" width="400" closable="false" modal="true" header="Confirm Export" resizable="false" >
                    <h:form id="enquiryForm">
                        <h:panelGrid columns="1">

                            <h:column>
                                <p:outputLabel value="You are about to export activity journals of selected users dated from #{userActivity.getFormattedDate(userActivity.fromDate, 'da')} to #{userActivity.getFormattedDate(userActivity.toDate, 'da')}. Are you sure to proceed?"/>
                            </h:column>


                            <h:column class="lastChild">
                                <div class="ctr">
                                    <p:commandButton id="btnExpJrnlProceed"  value="Proceed" onclick="PF('enquiryDialog').hide();
                                            PrimeFaces.monitorDownload(start, stop);
                                            PF('pollStat').start();
                                            stopNavigation();" styleClass="btn btn-success  btn-xs" action="#{userActivity.exportEnquiryRows()}" ajax="false">
                                    </p:commandButton>
                                    <p:spacer width="7px"/>   
                                    <p:commandButton value="Cancel" styleClass="btn btn-warning  btn-xs" oncomplete="PF('enquiryDialog').hide();"/>
                                </div>
                            </h:column>



                        </h:panelGrid>
                    </h:form>
                </p:dialog>



                <p:dialog widgetVar="activityDialog" id="actdailog" width="400" closable="false" modal="true" header="Confirm Export" resizable="false" responsive="true">
                    <h:form id="activityForm">
                        <h:panelGrid columns="1">

                            <h:column>
                                <p:outputLabel value="You are about to export the user activities of selected users dated from #{userActivity.getFormattedDate(userActivity.fromDate, 'da')} to #{userActivity.getFormattedDate(userActivity.toDate, 'da')}. Are you sure to proceed?"/>
                            </h:column>


                            <h:column class="lastChild">
                                <div class="ctr">
                                    <p:commandButton id="btnExpActProceed" value="Proceed" styleClass="btn btn-success  btn-xs" immediate="true" onclick="PF('activityDialog').hide();
                                            PrimeFaces.monitorDownload(start, stop);
                                            PF('pollStat').start();" action="#{userActivity.exportUserActivty()}" ajax="false" />
                                    <p:spacer width="7px"/>   
                                    <p:commandButton value="Cancel" styleClass="btn btn-warning  btn-xs" oncomplete="PF('activityDialog').hide();"/>
                                </div>
                            </h:column>



                        </h:panelGrid>
                    </h:form>
                </p:dialog>


            </div>
<!-- 25/06/2021 Feature #4956    user salesperson activity report - there is no company name mentioned-->
            <p:dialog widgetVar="dlgOppInq"  id="dlgOppInqPnl" 
                      modal="true" dynamic="true" maximizable="true" width="1200px"  style=" text-overflow: scroll" responsive="true">
                <f:facet name="header">
                    <h:outputText id="dlgHeader" value="New #{custom.labels.get('IDS_OPPS')} For #{userActivity.dlgHeader}" />
                </f:facet>
                <h:form id="newOppPnlForm" >
                    <!--13119 CRM-7769   Salesperson Activity CRM Report Showing Users Not without mutual Sale Teams-->
                    <p:outputLabel value="*Sales team visibility applied"/>
                    <p:dataTable id="dtNewOpp" widgetVar="custDT" value="#{userActivity.newOpportunityList}" var="opp"
                                 paginator="true"   rows="6" paginatorPosition="top" rowKey="#{opp.oppId}" selectionMode="single"
                                 >
<!--                         25/06/2021 Feature #4956    user salesperson activity report - there is no company name mentioned-->
                        <!-- add this for each link below if u need to open in same tab --> 
                        <p:column  headerText="#{custom.labels.get('IDS_CUSTOMER')}">

                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"  id="lnkOppCustName" target="_blank">

                                <h:outputLabel value="#{opp.oppCustomerName}" class="pointer"></h:outputLabel>
                            </h:link>    

                        </p:column>
                        
                        <p:column  headerText="#{custom.labels.get('IDS_PRINCI')}">

                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"  id="lnkOppPrinciName" target="_blank">

                                <h:outputLabel value="#{opp.oppPrincipalName}" class="pointer"></h:outputLabel>
                            </h:link>    

                        </p:column>
                        
                        <p:column  headerText="User" rendered="#{userActivity.compType==3}">

                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"  id="lnkOpOwner" target="_blank">

                                <h:outputLabel value="#{opp.oppOwnerName}" class="pointer"></h:outputLabel>
                            </h:link>    

                        </p:column>

                        <p:column headerText="#{custom.labels.get('IDS_PROGRAM')}" filterMatchMode="contains" 
                                  style="max-width: 200px;"
                                  id="filter_program"
                                  sortBy="#{opp.oppCustProgram}">

                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"  id="lnkOppCustPrgm" target="_blank">

                                <h:outputLabel value="#{opp.oppCustProgram}" class="pointer" ></h:outputLabel>
                            </h:link> 

                        </p:column>

                        <p:column  headerText="#{custom.labels.get('IDS_ACTIVITY')}"  filterMatchMode="contains" 
                                   sortBy="#{opp.oppActivity.toUpperCase()}" 
                                   id="filter_activity">

                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"  id="lnkOppActivityt" target="_blank">

                                <h:outputLabel value="#{opp.oppActivity}" class="pointer"></h:outputLabel>
                            </h:link> 

                        </p:column>

                        <p:column rendered="#{!opportunities.closeCols and oppFilter.topFilterDispCol.filterValue==0}" headerText="Status" filterMatchMode="contains" 
                                  sortBy="#{opp.oppStatus}"
                                  id="filter_status">
                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"  id="lnkOppStatus" target="_blank">

                                <h:outputLabel value="#{opp.oppStatus}" class="pointer"></h:outputLabel>
                            </h:link> 


                        </p:column>

                        <p:column rendered="#{oppFilter.topFilterDispCol.filterValue==1}" headerText="#{custom.labels.get('IDS_LINE_ITEMS')}" filterMatchMode="contains" 
                                  sortBy="#{opp.oppPartNums}"
                                  id="filter_line_items"
                                  >
                        </p:column>

                        <p:column  rendered="#{!opportunities.closeCols}" headerText="Value" filterMatchMode="contains" 
                                   style="
                                   padding: 5px 2px 1px 2px !important;
                                   text-align: right;width: 10%!important" 
                                   sortBy="#{opp.oppValue}"
                                   id="filter_value"
                                   >
                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"  id="lnkOppValue" target="_blank">

                                <p:outputLabel value="#{opp.oppValue}" class="pointer">
                                    <f:convertNumber maxFractionDigits="0" groupingUsed="true" maxIntegerDigits="15"/>
                                </p:outputLabel>
                            </h:link> 


                        </p:column>

                        <!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
                        <p:column rendered="#{!opportunities.closeCols}" headerText="#{custom.labels.get('IDS_NEXT_STEP')}" filterMatchMode="contains" 
                                  style="max-width: 200px;" styleClass="might-overflow"
                                  sortBy="#{opp.oppNextStep}" id = "filter_next_step">

                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"  id="lnkOppNextStep" target="_blank">

                                <h:outputLabel value="#{opp.oppNextStep}" class="pointer" ></h:outputLabel>
                            </h:link> 

                        </p:column>                

                        <p:column headerText="#{custom.labels.get('IDS_FOLLOW_UP')}" 
                                  filterMatchMode="contains"
                                  sortBy="#{opp.oppFollowUp}" id="filter_follow_up" >

                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"  id="lnkOppFollowUp" target="_blank">
                                <h:outputLabel value="#{opp.oppFollowUp}" class="pointer">
                                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                </h:outputLabel>

                            </h:link> 

                        </p:column>
                    </p:dataTable>
                </h:form>
            </p:dialog>

<!--                         25/06/2021 Feature #4956    user salesperson activity report - there is no company name mentioned-->
            <p:dialog widgetVar="dlgActJournal"  
                      modal="true" dynamic="true" id="actJournalPnl"  width="1200px" responsive="true" >
                <f:facet name="header">
                    <h:outputText id="dlgHeader1" value="#{custom.labels.get('IDS_ACT_JOURNAL')} For #{userActivity.dlgHeader}" />
                </f:facet>
                <h:form id ="actJournalForm" >
                    <!--13119 CRM-7769   Salesperson Activity CRM Report Showing Users Not without mutual Sale Teams-->
                    <p:outputLabel value="*Sales team visibility applied"/>
                    <p:dataTable id="dtActJournal" 
                                 value="#{userActivity.actJournalList}" 
                                 var="act"
                                 paginator="true"  
                                 rows="6" 
                                 paginatorPosition="top"
                                 rowKey="#{act.acjHdrId}"
                                 selectionMode="single"
                                


                               >                              



<!--//   25/06/2021 Feature #4956    user salesperson activity report - there is no company name mentioned-->
                        <p:column  headerText="Company"  >
                            <h:link outcome="/Journal/JournalEntry.xhtml?id=#{act.acjHdrId}"  id="lnkacjCompName"  target="_blank">
                                <h:outputLabel value=" #{act.acjCompName}" class="pointer" ></h:outputLabel>
                            </h:link>
                           
                        </p:column>
                        <p:column  headerText="User"  >
                            <h:link outcome="/Journal/JournalEntry.xhtml?id=#{act.acjHdrId}"  id="lnkacjUsername" target="_blank">
                                <h:outputLabel value="#{act.acjUserName}" class="pointer"></h:outputLabel>
                            </h:link>


                        </p:column>

                        <p:column headerText="Title" >

                            <h:link outcome="/Journal/JournalEntry.xhtml?id=#{act.acjHdrId}"  id="lnkacjTitle" target="_blank">
                                <h:outputLabel value="#{act.acjhTitle}" class="pointer" ></h:outputLabel>
                            </h:link>

                        </p:column>

                        <!--Bug 1303 -Call type in Activity journal export--> 
                        <p:column headerText="Call Type" >
                            <h:link outcome="/Journal/JournalEntry.xhtml?id=#{act.acjHdrId}"  id="lnkacjCallType" target="_blank">
                                <h:outputLabel value="#{act.acjhCallType}" class="pointer"></h:outputLabel>
                            </h:link>

                        </p:column>

                        <p:column headerText="#{custom.labels.get('IDS_SALES_TEAM')}" >
                            <h:link outcome="/Journal/JournalEntry.xhtml?id=#{act.acjHdrId}"  id="lnkacjSalesName" target="_blank">
                                <h:outputLabel value="#{act.acjSamnName}" class="pointer" ></h:outputLabel>
                            </h:link>

                        </p:column>

                        <p:column headerText="Attendees" >
                            <h:link outcome="/Journal/JournalEntry.xhtml?id=#{act.acjHdrId}"  id="lnkacjContName" target="_blank">
                                <h:outputLabel value="#{act.acjContName}" class="pointer"></h:outputLabel>
                            </h:link>

                        </p:column>
                        <!--CRM -555 : south :future activity journals showing up , but nothing in exports-->
                        <p:column headerText="Date" >
                            <h:link outcome="/Journal/JournalEntry.xhtml?id=#{act.acjHdrId}"  id="lnkacjDate" target="_blank">
                                <h:outputLabel   value="#{act.acjhDate}" class="pointer">
                                    <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                </h:outputLabel>
                            </h:link>

                        </p:column>

                    </p:dataTable>
                </h:form>
            </p:dialog>




            <p:dialog widgetVar="dlgMsg1" closable="false" modal="true" header="Message" resizable="false" width="250" responsive="true">
                <h:form>
                    <p:outputPanel >


                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Processing... Please wait..." />
                        <br /><br />



                        <p:poll  stop="#{viewStatus.isViewComplete()}"  interval="5" widgetVar="pollStat" autoStart="false"  />

                    </p:outputPanel>

                </h:form>

            </p:dialog>



            <style>
                .pointer{
                    cursor: pointer !important;
                }
                
                .no-header thead{
                    display: none;
                }
                .vos { 
                    display:block !important;
                }
                body .ui-panel.ui-widget {
                    height: 550px !important;
                    border: 0px !important;
                }
                /*Feature #3650:Salesperson usage report*/
                /*                #user\:userDT tbody div.ui-chkbox span.ui-icon-check {
                                    position: relative;
                                    right: 4px;
                                    bottom: -4px;
                
                                }*/


                .ctr{
                    text-align:center;
                    padding: 4px;
                }
            </style>

        </div>
        <script type="text/javascript">
            function start() {

                PF('dlgMsg1').show();
            }
            function stop() {
                PF('dlgMsg1').hide();
            }
        </script>
    </ui:define> 
</ui:composition>
