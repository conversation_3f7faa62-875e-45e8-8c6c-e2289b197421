<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
<!--    3502 Planner : UI Issue-->
    <p:dialog resizable="false" widgetVar="PlnrContactDialog"  onShow="PF('PlnrContactDialog').initPosition();" header="Contacts" showEffect="clip" hideEffect="clip">                    
        <p:ajax event="close" listener="#{plannerService.addClear()}" update=":frmPlanner" />

        <h:form id="frmContDlg">
            <p:commandButton value="Add" action="#{plannerService.onPlannerContactAdd()}" oncomplete="PF('contactTable').filter();" update=":frmPlanner" styleClass="btn-primary btn-xs" />
            <p:dataTable id="contactTable" value="#{plannerService.smanContact}" var="cont" rows="8"  selection="#{plannerService.selectedPlannerContact}" rowKey="#{cont.recId}"
                         widgetVar="contactTable" filterEvent="keyup" filteredValue="#{viewContList.filteredContacts}" paginator="true" paginatorAlwaysVisible="false"
                          paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                         paginatorPosition="top" style="width: 700px" rowSelectMode="add" >
                <p:column  selectionMode="multiple"  style="width: 30px;" />
                <p:ajax event="rowSelect" listener="#{plannerService.inviteRendr1}"  /> 
                <p:ajax event="rowUnselect" listener="#{plannerService.inviteRendr1}"   /> 
                <p:ajax event="rowSelectCheckbox" listener="#{plannerService.inviteRendr1}"  /> 
                <p:ajax event="rowUnselectCheckbox" listener="#{plannerService.inviteRendr1}"   />
                <p:ajax event="toggleSelect"  listener="#{plannerService.inviteRendr1}" />
                <p:column style="width: 30%" filterBy="#{cont.contFullName}" filterMatchMode="contains" sortBy="#{cont.contFullName}"  headerText="Name"  >
                            #{cont.contFullName}
                </p:column>
<!--                3707  Planner : Data issue-->
                <p:column style="width: 30%" filterValue="#{plannerService.compFilter}" filterMatchMode="contains" sortBy="#{cont.compName}" filterBy="#{cont.compName}"  headerText="Company" >
                            #{cont.compName}
                </p:column>
                <p:column style="width: 30%" filterBy="#{cont.contJobTitle}" filterMatchMode="contains" sortBy="#{cont.contJobTitle}" headerText="Job Title">
                            #{cont.contJobTitle}
                </p:column>
<!--3707  Planner : Data issue-->
                <p:column style="width: 30%" filterBy="#{cont.contEmailBusiness}" filterMatchMode="contains" sortBy="#{cont.contEmailBusiness}" headerText="Business Email">
                            #{cont.contEmailBusiness}
                </p:column>
            </p:dataTable><br/>



        </h:form>
    </p:dialog> 
    
</ui:composition>

