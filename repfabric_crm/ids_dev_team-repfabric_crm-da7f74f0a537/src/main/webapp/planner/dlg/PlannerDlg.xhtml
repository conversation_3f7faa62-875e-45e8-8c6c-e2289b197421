<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: PlannercDlg.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    <p:dialog id="newPlannerDlg" header="#{plannerService.planner.recId==0?'Add':'Edit'} Planner Detail" width="850" 
              modal="true" responsive="true" 
              onShow="PF('tabView').select(0)" widgetVar="dlgPalnner" resizable="false">
        <p:ajax event="close" listener="#{plannerService.populatePlanrList()}" oncomplete="PF('dlgPalnner').hide();"/>
        <h:form id="frmPlanner">
            <p:growl escape="false" life="6000" showDetail="false" showSummary="true" widgetVar="grlmsgr" id="growlmsg"/>
<!--        3466    Planner > Handle invalid date-->
            <p:remoteCommand name="applyActivePrinci" autoRun="false" immediate="true" update=":frmPlanner:tvPlanner:inpTxtCustName :frmPlanner"
                             actionListener="#{plannerService.onCustomerSelection(viewCompLookupService.selectedCompany)}"
                             />
            <p:tabView id="tvPlanner"  widgetVar="tabView" >
                <p:ajax event="tabChange" listener="#{plannerService.onTabChange()}" update=":frmPlanner" />
                <p:tab title="Basic">
                    <ui:include src="../tabs/PlannerBasicDlg.xhtml"/>
                </p:tab>
                <p:tab title="Contacts" rendered="#{plannerService.planner.planId>0}" id="tabContact">
                    <ui:include src="../tabs/PlannerContactTab.xhtml"/>

                </p:tab>
            </p:tabView>
        </h:form>

    </p:dialog>
    <!--    3443 #4 CRM-2114: Planner feedback in TW dev - Invite button-->
    <p:dialog widgetVar="dlgMaillSend" closable="false" modal="true" header="Message" resizable="false" >
        <p:outputPanel >
            <br />
            <h:graphicImage  library="images" name="ajax-loader.gif" id="loadingImg"   />
            <p:spacer width="5" />
            <p:outputLabel value="Please wait... Sending emails" />
            <br /><br />
        </p:outputPanel>
    </p:dialog>
    <ui:include src="../dlg/PlannerContDlg.xhtml"/>
    <ui:include src="../../lookup/CompanyLookupDlg.xhtml"/>
    <ui:include src="../dlg/PlannerDraftEmailDlg.xhtml"/>
    <ui:include src="../dlg/PlannerMailContentDlg.xhtml"/>
</ui:composition>