<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: InsertContentDlg.xhtml
// Author: Nisha
//*********************************************************
* Copyright (c) 2017 Indea Design Systems Private Limited, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">


    <p:dialog  id="plnrInsertContentDlg" resizable="false" header="Insert Data Tags" style="width:350px;"  modal="true" widgetVar="plnrInsertContentDlg" responsive="true">  
        <h:form id="insertContentForm">
            <p:panelGrid id="grid" columns="1" 
                         style="min-height: 50px"       layout="grid" styleClass="box-primary no-border ui-fluid ">
                
                <p:outputLabel value="Select data item to be substituted for the tag" style="width:100%" /> 

                <p:selectOneMenu id="console" widgetVar="DDSubTag"  style="width:125px">
        
                    <f:selectItems value="#{plannerService.dataTags}" var="data_tag" />

                </p:selectOneMenu>
            </p:panelGrid>

            <p:spacer width="5px"/>
            <div class="div-center"> 
                <p:commandButton  value="Insert into Content"   onclick="insertOnContent();" oncomplete="PF('plnrInsertContentDlg').hide();"
                                  styleClass="btn btn btn-primary btn-xs"  />
                <p:commandButton  value="Insert into subject"  onclick="insertOnSubject();"  oncomplete="PF('plnrInsertContentDlg').hide();"
                                   styleClass="btn btn btn-primary btn-xs"  style="margin-left:5px"/>
                <p:commandButton  value="Close" type="button" onclick="PF('plnrInsertContentDlg').hide()" styleClass="btn btn btn-danger btn-xs" style="margin-left:5px"/>
            </div>

        </h:form>
        <script>

            function insertOnContent() {
                  $('#edtrSub').summernote('editor.restoreRange');
                const rng = $('#edtrSub').summernote('editor.getLastRange');   
                var text = PF('DDSubTag').getSelectedValue();

                $('#edtrSub').summernote({focus: true});
                document.execCommand('insertText', false, text);
            }
                   
                function insertOnSubject() {
//                       
                        var element1 = $('#draftEmailFrm\\:inputSubject');
  
                        var text = PF('DDSubTag').getSelectedValue();
                        console.log("insert1"+element1);
                        
                        console.log("insert1"+text);
                        var caretPos = element1[0].selectionStart;
                                currentValue = element1.val();
        
                        element1.val(currentValue.substring(0, caretPos) + text + currentValue.substring(caretPos));
//        

                            }

            
        </script>


        <!--        <script>
        
                    function insertOnContent() {
                        PF('editorWidget').editor.focus();
                        PF('editorWidget').editor.execCommand('inserthtml', PF('DDSubTag').getSelectedValue(), false);
                    }
        
                    function insertOnSubject() {
                        var element1 = $('#staticEmailActionForm\\:inputSubject');
                        var element2 = $('#emailPitchActionForm\\:inputSubject');
                        var element3 = $('#draftEmailActionForm\\:inputSubject');
        
        
                        var text = PF('DDSubTag').getSelectedValue();
                        var caretPos = element1[0].selectionStart,
                                currentValue = element1.val();
        
                        element1.val(currentValue.substring(0, caretPos) + text + currentValue.substring(caretPos));
        
                        var caretPos = element2[0].selectionStart,
                                currentValue = element2.val();
        
                        element2.val(currentValue.substring(0, caretPos) + text + currentValue.substring(caretPos));
        
                        var caretPos = element3[0].selectionStart,
                                currentValue = element3.val();
        
                        element3.val(currentValue.substring(0, caretPos) + text + currentValue.substring(caretPos));
                    }
        
                </script>-->
    </p:dialog>

</ui:composition>

