<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: DraftEmailDlg.xhtml
// Author: Sarayu
//*********************************************************
* Copyright (c) 2017 Indea Design Systems, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <!--03-09-2024 14388 CRITICAL RS-22294: RE: Repfabric - Recht's Instance / System Audit Request-->
    <script>
        var isSummerNoteLoaded = false;
        function loadScript(src, callback) {
            var script = document.createElement('script');
            script.src = src;
            script.onload = callback;
            document.head.appendChild(script);
        }

        function loadStylesheet(href, callback) {
            var link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.onload = callback;
            document.head.appendChild(link);
        }

        function loadSummernote() {
            if (isSummerNoteLoaded)
                return;
            loadStylesheet('https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css', function () {
                loadStylesheet('https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote.css', function () {
                    loadScript('https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote.min.js', function () {
                        $('#edtrSub').summernote({
                            airMode: false,
                            height: 250,
                            minHeight: 200,
                            maxHeight: 250,
                            disableResizeEditor: true,
                            callbacks: {
                                onInit: function () {
                                    console.log('Summernote is ready!');
                                    loadVal();
                                    loadEditorContent1();
                                }
                            }
                        });
                        isSummerNoteLoaded = true;
                    });
                });
            });
        }

    </script>
    <p:dialog  id="plannerDraftEmailActionDlg"  header="Send Mail" width="1050px" height="550px" resizable="true" responsive="true" onShow="loadSummernote()" onHide="clearSummerNote()"   modal="true" widgetVar="plannerDialogDraftEmailAction" >  
        <h:form id="draftEmailFrm">
<!--            <p:remoteCommand autoRun="true" onstart="
                             loadVal();loadEditorContent1()" />-->
            <p:remoteCommand id="ajUpdate" name="ajUpdate" update=":draftEmailFrm:dtAttListPlan" actionListener="#{plannerService.addPitchAttach()}"/>
            <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-2,ui-grid-col-4"
                         style="min-height: 50px"       layout="grid" styleClass="box-primary no-border ui-fluid ">
                <p:outputLabel  value="Subject"/>

                <p:inputText id="inputSubject"  value="#{plannerService.plannerMailSubject}" maxlength="100"  style="width:550px;"/>

            </p:panelGrid>

            <p:spacer width="4" />
            <p:fileUpload id="fleUpldAttch" fileUploadListener="#{plannerService.uploadEmailAttachment}" 
                          label="Add Attachment" mode="advanced"
                          auto="true"
                          style="float: right;margin-left: 2px;font-size: 0px; height: 3px;"
                          update="draftEmailFrm:dtAttListPlan"
                          />
            <p:commandButton value="Insert Data Tags" 
                             onclick="PF('plnrInsertContentDlg').show();" onblur="saverng();"
                             style="float: right;margin-right: 10px; width: 100px;margin-left: 5px"   styleClass="btn btn btn-primary btn-xs"/>
            <!--PMs task 6849: Pitch Selector: Planner > Invite-->
            <p:commandButton id="btnPitch" value="Insert Pitch"  onblur="saverng();" styleClass="btn btn-primary btn-xs" oncomplete="PF('insertPitchDlg').show();" style="float:right; margin-bottom: 10px !important;margin-left: 2px;" action="#{pitchSelectorService.setAjCompName(plannerService.planner.planCompName,plannerService.planner.planCompId)}" actionListener="#{pitchSelectorService.resetDefalutPitch()}" update=":pitchSelForm:hdnContCompName1 :pitchSelForm:itCompName1 :pitchSelForm:pitchName :pitchSelForm"/>

            <p:spacer width="4" />
            <p:commandButton id="btnSetAsDef" value="Set as default" action="#{plannerService.setAsDefaultTemplate()}" style="margin-right: 10px; width: 100px;"
                             oncomplete="loadVal();" styleClass="btn btn btn-primary btn-xs" onclick="saveContent()"/>                
            <div style="height:10px;">

            </div>
            <!--03-09-2024 14388 CRITICAL RS-22294: RE: Repfabric - Recht's Instance / System Audit Request-->
            <textArea id="edtrSub" class="summernote summernote-btns" style="height:300px; margin-top: 10px;" ></textArea>
            
            <h:inputHidden id="hiddenText"  value="#{plannerService.plannerMailContent}" />
            <p:dataTable id="dtAttListPlan" value="#{plannerService.addAttaments}" style="width: 100%;margin-left: 7px;" var="att" emptyMessage="No Attachments Found" >
                <p:column headerText="Attachments"   style="width:90%;"  >
                    <h:outputText value="#{plannerService.fileName(att)}" />
                </p:column>

                <p:column style="width:15%;" >
                    <p:commandButton action="#{plannerService.removeOppAttachment(att)}" icon="fa fa-trash" title="Delete" styleClass="btn-danger btn-xs btn-icon"
                                     style="height:20px;width:20px;"   update=":draftEmailFrm:dtAttListPlan" >
                        <p:confirm header="Confirmation" message="Are you sure to delete?" icon="ui-icon-alert" />
                    </p:commandButton>
                </p:column>

            </p:dataTable>

            <div class="div-center" >
                <p:commandButton id="btnSendMail" value="Send" onclick="onSendingMail();" style="margin-right: 10px;margin-top:10px; width: 100px;"
                    styleClass="btn btn btn-primary btn-xs" />
            </div>
        </h:form>
    </p:dialog>
    <p:dialog widgetVar="cnrfmDlg" header="Confirmation" modal="true" >
        <h:form>
            <div class="div-center" >
        <p:outputLabel value="Do you want to save the email template?"/><br/><br/>
        <p:remoteCommand autoRun="false" action="#{plannerService.sendMail()}" oncomplete="PF('plannerDialogDraftEmailAction').hide();" name="rmSend" update=":frmPlanner" />
        <p:commandButton value="Yes" action="#{plannerService.saveToDefaltAndSend()}" onclick="PF('cnrfmDlg').hide();PF('plannerDialogDraftEmailAction').hide();" update=":frmPlanner" styleClass="btn btn-success btn-xs"  />
        <p:spacer width="4" />
        <p:commandButton value="No" action="#{plannerService.sendMail()}" onclick="PF('cnrfmDlg').hide();PF('plannerDialogDraftEmailAction').hide();" update=":frmPlanner" styleClass="btn btn-danger btn-xs" />
            </div>
        </h:form>
    </p:dialog>
    <script>

        var oldValue = "";
        var newValue = "";
        function loadVal() {

            oldValue = document.getElementById('draftEmailFrm:inputSubject').value;
            oldValue += document.getElementById('draftEmailFrm:hiddenText').value;
        }
        ;
        function onSendingMail() {
            var m = $('#edtrSub').summernote('code');
            var sum = document.getElementById('draftEmailFrm:hiddenText').value = m;
            newValue = document.getElementById('draftEmailFrm:inputSubject').value;
            newValue += document.getElementById('draftEmailFrm:hiddenText').value;
            if (newValue != oldValue) {
                PF('cnrfmDlg').show();
            } else {
                rmSend();
            }
        }
        function saveContent() {
            var m = $('#edtrSub').summernote('code');

            var sum = document.getElementById('draftEmailFrm:hiddenText').value = m;

        }
        function loadEditorContent1() {
            var mailcont = document.getElementById('draftEmailFrm:hiddenText').value;

            $("#draftEmailFrm\\:edtrSub").html(mailcont);
            $("#edtrSub").summernote('code', mailcont);
            fx(document.getElementsByTagName('textarea')[0]);
//                                        $("#edtrSub").closest(".note-editing-area").find('[contenteditable]').placeCursorAtEnd();


        }

        function saverng()
        {

            $('#edtrSub').summernote('editor.saveRange');
        }

        function  clearSummerNote() {

            $('#edtrSub').summernote('reset');
        }
//            PMs task 6849: Pitch Selector: Planner > Invite
//            03-09-2024 14388 CRITICAL RS-22294: RE: Repfabric - Recht's Instance / System Audit Request
//        function loadSummerNote() {
//            $('#edtrSub').summernote({
//                airMode: false, height: 250, minHeight: 200, maxHeight: 250, disableResizeEditor: true
//            });
//            var m = $('#edtrSub').summernote('code');
//            $('div.note-editable').height(250);
//        }
//                                      
        function insertCode() {
            // alert('&lt;br /&gt;'+ content);

            $('#edtrSub').summernote('editor.restoreRange');
            const rng = $('#edtrSub').summernote('editor.getLastRange');
            $('#edtrSub').summernote('editor.focus');

            $('#edtrSub').summernote('pasteHTML', content);
//                                        $("p").each(function(){
//                                            if (!$(this).text().trim().length) {
//                                                $(this).remove();
//                                            }
//                                        });


        }
        function fx(element)
        {
            //alert("hii");
            element.focus();
            element.setSelectionRange(element.value.length, element.value.length);
        }
    </script>
    <style>
        /*        ui-editor ui-widget-content{
                    height: 700px !important;
                }*/
        #draftEmailFrm\:fleUpldAttch>.ui-fileupload-buttonbar.ui-widget-header.ui-corner-top>
        .ui-button.ui-widget.ui-state-default.ui-corner-all.ui-button-text-icon-left.ui-fileupload-choose>
        .ui-button-icon-left.ui-icon.ui-c.ui-icon-plusthick
        {
            color: #fff;
            text-indent: 0;

            display: none;
            visibility: hidden;
        }

        /*        .ui-fileupload-buttonbar .ui-fileupload-choose .ui-button-text{
                    padding-left:1em !important;
                    font-size:12px;
                        padding-top: 6px !important;
                }*/

        #draftEmailFrm\:fleUpldAttch>.ui-fileupload-content{
            display:none;
        }
        /*<!--4867 Contacts > Send Bulk Email > Add Attachment : Fix button size issue-->*/
        #draftEmailFrm\:fleUpldAttch_label{ 
            padding-left:1em !important;
            font-size:12px;
            padding-top: 2px !important;
        }
        /*7648: CRM-5509  Per Sarah Cozzens: Please add third option to Online Instance Help Button*/
        .ui-button.ui-widget.ui-state-default.ui-corner-all.ui-button-text-icon-left {
            height: 26px !important;
            padding-top: inherit !important;
            color: #fff;
            background-color: #337ab7; 
            border-color: #2e6da4;
        }
    </style>

</ui:composition>

