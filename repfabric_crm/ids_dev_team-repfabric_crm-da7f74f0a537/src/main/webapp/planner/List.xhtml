<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: List.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->


<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <!--PMS 357:Page Title - Companies, Contacts, AJ, Tasks, Messages, Calendar, Planner-->
    <ui:define name="head">
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
        <title>Planner</title>
    </ui:define>
    <ui:define name="meta">
    </ui:define>

    <ui:define name="title">
        <ui:param name="title" value="Planner"/>
        <!--<f:event listener="#{commTransactionService.isCommTransProcessPageAccessible}" type="preRenderView"/>-->
        <div class="row">
            <div class="col-md-6">
                <!--29-09-2023  12191 CRM-6510 LMS Learning links-->
                <f:metadata>
                    <f:viewAction action="#{helpService.setPageTag('PLANNER')}" />
                </f:metadata>
                Planner
            </div>
            <!--7648: CRM-5509  Per Sarah Cozzens: Please add third option to Online Instance Help Button-->
              <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>

    <ui:define name="menu">

    </ui:define>

    <ui:define name="body">
        <div class="box box-info box-body" style="vertical-align: top">
<!--            <h:form>
                <p:remoteCommand name="applyFilter" autoRun="false" 
                                 immediate="true" 
                                 actionListener="#{plannerFilter.retainFilter()}"/>
            </h:form>-->
            <h:form id="frmListPlanner">
                <!--                //3442-CRM-2114 :Planner feedback in TWDEV
                                //Seema- 21/02/2020-->
                <p:defaultCommand target="btnSearch"/> 
                <div class="box-header with-border">
                    <div class="row">
                        <div class="col-md-1">
                            <p:commandButton value="New"  styleClass="btn btn-primary btn-xs" 
                                             actionListener="#{plannerService.initialValues()}"
                                             update=":newPlannerDlg :frmPlanner "
                                             oncomplete="PF('dlgPalnner').show();"/> 
                        </div> 
                        <div style="text-align: right;font-weight:normal ">
                            <p:outputLabel  value="Show Schedule for next" id="lblSearch"/>
                            <p:spacer width="4px"/>
                            <p:spinner min="0" max="9999"   maxlength="4" 
                                       id="spinnerDays"
                                       value="#{plannerService.plnDays}">
                            </p:spinner>
                            <p:spacer width="4px"/>
                            <p:outputLabel  value="days" id="lbldays"/>
                            <p:spacer width="4px"/>
                            <p:commandButton title="Search Planner" id="btnSearch"
                                             class="button_top btn-primary btn-xs" 
                                             icon="fa fa-search"
                                             actionListener="#{plannerService.searchSchedules(plannerService.plnDays)}"
                                             />
                            <p:spacer width="15px"/>
                        </div>
                    </div> 
                </div> 

                <p:dataTable id="dataTblPlannerList"  widgetVar="plannerListTable" 
                             value="#{plannerService.plannerList}" 
                             filteredValue="#{plannerService.fileteredPlnerLst}"
                             var="vplanner" 
                             rows="50" 
                             filterEvent="keyup paste"
                             draggableColumns="true"
                             emptyMessage="No Transactions found."  
                             paginator="true" 
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rowsPerPageTemplate="50,100" 
                             paginatorAlwaysVisible="false" 
                             rowKey="#{vplanner.recId}"
                             multiViewState="true"
                             >
                    <p:column  headerText="Company"  filterBy="#{vplanner.planCompName}" 
                               filterMatchMode="contains" sortBy="#{vplanner.planCompName}" 
                               id="filter_plnr_comp_name"
                               >
                        <p:outputLabel style="font-size: 12px" value="#{vplanner.planCompName}" />
                    </p:column>
                    <p:column  headerText="Planner Date"  filterBy="#{plannerService.getFormattedDate(vplanner.planDate,'da')}" 
                               filterMatchMode="contains" id="filter_plnr__date"
                               sortBy="#{plannerService.getFormattedDate(vplanner.planDate,'da')}" >
                        <p:outputLabel style="font-size: 12px" value="#{vplanner.planDate}">
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </p:outputLabel>
                    </p:column>
                    <p:column  headerText="Description" filterBy="#{vplanner.planDescr}" filterMatchMode="contains" 
                               sortBy="#{vplanner.planDescr}" 
                               id="filter_plnr_desc"  >
                        <p:outputLabel style="font-size: 12px" value="#{vplanner.planDescr}" />
                    </p:column>
                    <p:column  headerText="Status"  filterMatchMode="contains" 
                               sortBy="#{vplanner.planStatus}" 
                               style="width:65px">
                        <p:selectBooleanCheckbox value="#{vplanner.planStatusFlag}" title="Planner Status">
                            <p:ajax  event="change" listener="#{plannerService.updatePlannerStatus(vplanner)}" process="@this"/>
                        </p:selectBooleanCheckbox>
                    </p:column>
                    <p:column style="width: 80px">       
                        <p:commandButton resetValues="true"  
                                         icon="fa fa-pencil"
                                         styleClass="btn-primary  btn-xs btn-icon"
                                         title="Edit Planner"   
                                         actionListener="#{plannerService.populatePlanner(vplanner)}"
                                         update=":newPlannerDlg :frmPlanner"
                                         oncomplete="PF('dlgPalnner').show();">

                        </p:commandButton>        
                        <p:spacer width="4px"/>
                        <p:commandButton title="Delete Planner"  
                                         icon="fa fa-trash"
                                         process="@this"
                                         action="#{plannerService.delete(vplanner.planId)}"
                                         styleClass="btn-danger btn-xs btn-icon" 
                                         oncomplete="PF('dltPlnrConfirmation').show();"
                                         />
                    </p:column>
                </p:dataTable>
            </h:form>
        </div>
        <ui:include src="dlg/PlannerDlg.xhtml"/>
        <ui:include src="/lookup/PitchLookup.xhtml"/>
        <p:confirmDialog header="Confirmation" message="Are you sure to delete?" 
                         global="false"  widgetVar="dltPlnrConfirmation" >
            <h:form id="fromDltPlner">
                <div align="center">
                    <p:commandButton value="Yes" 
                                     actionListener="#{plannerService.deletePlanner()}"
                                     oncomplete="PF('dltPlnrConfirmation').hide();PF('plannerListTable').filter();"
                                     process="@this"
                                     styleClass="btn btn-success  btn-xs"    />
                    <p:spacer width="4px"/>
                    <p:commandButton value="No" styleClass="btn btn-danger  btn-xs"
                                     onclick="PF('dltPlnrConfirmation').hide()"
                                     />
                </div>
            </h:form>
        </p:confirmDialog>
    </ui:define>  
</ui:composition>