<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: PlannerBasicDlg.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    <div>
<!--        3466    Planner > Handle invalid date-->
         <p:growl id="calValidator"  />
        <h:panelGroup id="pnlPlanner">
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Company" id="lblCustomer" styleClass="required" />
                </div>
                <div class="ui-sm-12 ui-md-10 ui-lg-10">
                    <h:panelGroup class="ui-inputgroup" rendered="#{!plannerService.planner.fromComp}" >
<!--                        3466    Planner > Handle invalid date-->
                        <p:inputText id="inpTxtCustName" readonly="true" value="#{plannerService.planner.planCompName}"
                                       >
                            
                        </p:inputText>
<!--                        <h:inputHidden id="hdninputcName" value="#{plannerService.planner.planCompName}"  
                                         />-->
                        <p:commandButton  icon="fa fa-search"  immediate="true" id="btnCustomer"
                                          styleClass="btn-info btn-xs"
                                          action="#{viewCompLookupService.listAll('applyActivePrinci')}" 
                                          oncomplete="PF('lookupComp').show()"
                                          update=":formCompLookup" />
                        <p:commandButton   icon="fa fa-close" immediate="true"  id="btnClear"
                                           process="@this" actionListener="#{plannerService.clearCustomer()}"
                                           class="button_top btn-danger btn-xs"  />
                    </h:panelGroup>
                    <p:outputLabel value="#{plannerService.planner.planCompName}" 
                                   id="lblCustomerName" 
                                   rendered="#{plannerService.planner.fromComp}" />
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Plan Date" id="lblPlanDate" styleClass="required"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
<!--                    3466    Planner > Handle invalid date-->
                    <p:calendar  showOn="button" pattern="#{globalParams.dateFormat}" id="calPlnDate"  
                                value="#{plannerService.planner.planDate}"  converterMessage="Invalid Date"
                              >  
<!--                        <p:ajax event="dateSelect" process="@this"/>-->
                    <p:ajax event="blur" update="frmPlanner:tvPlanner:calValidator " />
                    </p:calendar> 
                </div>
                <div class="ui-sm-12 ui-md-1 ui-lg-1">
                    <p:outputLabel value="Status" id="lblStatus" rendered="#{!plannerService.planner.fromComp}"/>
                </div>
                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                    <p:selectOneMenu id="ddstatus" value="#{plannerService.planner.planStatus}" 
                                     rendered="#{!plannerService.planner.fromComp}">
                        <f:selectItem itemLabel="Planned" itemValue="0" />
                        <f:selectItem itemLabel="Done" itemValue="1"  />
                        <p:ajax process="@this"/>
                    </p:selectOneMenu>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Description" id="lblDescr"/>
                </div>
                <div class="ui-sm-12 ui-md-10 ui-lg-10">
                    <p:inputTextarea  value="#{plannerService.planner.planDescr}" 
                                      id="inpTxtAreaDesc" autoResize="false" 
                                      maxlength="250" rows="3">
                        <p:ajax process="@this"/>
                    </p:inputTextarea>
                </div>
            </div>
            <div style="text-align: center">
                
                <p:commandButton id="btnSave" value="Save" 
                                 styleClass="btn btn-success  btn-xs" widgetVar="saveBtn"
                                 onclick="PF('saveBtn').disable()" 
                                 update=" :frmPlanner:tvPlanner:inpTxtCustName :frmPlanner"
                                 oncomplete="PF('saveBtn').enable();"
                                 action="#{plannerService.save()}"/>                 
                <p:spacer width="4px"/>
                <p:commandButton  value="Cancel" styleClass="btn btn-warning  btn-xs"  id="btnCancel"
                                  title="Go to Planner List"
                                  onclick="PF('dlgPalnner').hide();"
                                  />
            </div>
        </h:panelGroup>
    </div>
</ui:composition>