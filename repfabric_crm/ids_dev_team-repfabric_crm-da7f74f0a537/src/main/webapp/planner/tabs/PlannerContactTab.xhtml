<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    <div style="margin-left: 130px;">  
        <p:outputPanel id="pnlMsg" styleClass="error-dlg" rendered="#{plannerService.refrshCont}" >
        <p:outputLabel styleClass="err-msg" value="Please click on the Refresh button to see the status updates after sending email" />
    </p:outputPanel>
    </div>   
    <p:outputPanel>
    <p:commandButton id="addCont" action="#{plannerService.initPlancont()}"  value="Add" update=":frmContDlg" styleClass="btn-primary btn-xs"  oncomplete="PF('PlnrContactDialog').show();PF('contactTable').filter();"  />
<!--3460 Planner >  Disable Invite button-->

<p:commandButton value="Invite"  disabled="#{plannerService.planner.planStatus==1 or plannerService.pastEvent(plannerService.planner.planDate) or plannerService.invPlanContt==null or plannerService.invPlanContt.size()==0}" 
                 action="#{plannerService.clearMail()}"
                 oncomplete="PF('plannerDialogDraftEmailAction').show();" update=":frmPlanner :draftEmailFrm" style="float: right;" styleClass="btn-primary btn-xs"/>
     <p:spacer width="4px" style="float: right"/>
     <p:commandButton  action="#{plannerService.refreshContDtl()}" icon="fa fa-refresh"  
                      title="Refresh" update="frmPlanner" style="float: right;max-height: 21px;" rendered="#{plannerService.refrshCont}"
                               styleClass="btn-primary btn-xs"
                               />
</p:outputPanel>
   
<!--    <p:spacer height="1px"/>-->
    <p:dataTable  rowSelectMode="add"  style="height:100%;width:100%; margin-top: 5px;" var="cont1" value="#{plannerService.planContList}" selection="#{plannerService.invPlanContt}"
                  id="tblplanCont" widgetVar="tblplanCont" scrollable="true" scrollHeight="250"  rowKey="#{cont1.planContId}" resizableColumns="false">
            <p:column selectionMode="multiple" style="width:10px;text-align: center" />
            <p:ajax event="rowSelectCheckbox"  listener="#{plannerService.inviteRendr1}" update="frmPlanner" />
            <p:ajax event="rowUnselectCheckbox"   listener="#{plannerService.inviteRendr1}" update="frmPlanner" />
            <p:ajax event="rowUnselect"   listener="#{plannerService.inviteRendr1}" update="frmPlanner" />
            <p:ajax event="rowSelect"    listener="#{plannerService.inviteRendr1}" update="frmPlanner" />
            <p:ajax event="toggleSelect"   listener="#{plannerService.inviteRendr1}" update="frmPlanner" /> 
            <p:column width="100" headerText="Name">
                #{cont1.contName} 
            </p:column>            
            <p:column width="100" headerText="Company">
                #{cont1.contComp}
            </p:column>
            <p:column width="80" headerText="Job Title"> 
                #{cont1.contJob}
            </p:column>
            <p:column width="140" headerText="Email"> 
                #{cont1.contEmail}
            </p:column>
            <p:column width="80" headerText="Status"> 
                <p:selectOneMenu id="ddstatus" value="#{cont1.planContStatus}" >
                    <f:selectItem itemLabel="Planned" itemValue="0" />
                    <f:selectItem itemLabel="Invited" itemValue="1"  />
                    <f:selectItem itemLabel="Confirmed" itemValue="2" />
                    <f:selectItem itemLabel="Denied" itemValue="3"  />
                    <p:ajax event="change" listener="#{plannerService.changeContactStatus(cont1.planId, cont1.planContId, cont1.planContStatus)}" update=":frmPlanner" process="@this"/>
                </p:selectOneMenu>
            </p:column>
            <p:column width="15"> 
                <p:commandButton  type="button"  styleClass="btn-danger btn-xs btn-icon" icon="fa fa-trash"   title="Delete" update=":frmPlanner" >
                <p:ajax event="click" listener="#{plannerService.deleteInvirtee(cont1.planContId)}"  update=":frmPlanner"/>
                <p:confirm message="Are you sure to delete this invitee?" header="Confirmation" />
            </p:commandButton>
            </p:column>
            <p:column width="15">
                <p:commandButton id="btnCustEmal"   action="#{plannerService.sendCustomizedEmail(cont1.planId,cont1.planContId)}" update=":frmPlanner"   styleClass="btn-primary  btn-xs btn-icon"  icon="fa fa fa-envelope-o" >
                  
                </p:commandButton>
            </p:column>


        </p:dataTable>



    <p:confirmDialog global="true" showEffect="fade">
        <div class="cntr"><p:commandButton value="Yes"  type="button" update=":frmPlanner :frmContDlg" styleClass="ui-confirmdialog-yes btn btn-success btn-xs"  />
            <p:spacer width="4px"/>
            <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no btn btn-danger btn-xs"   /></div>
    </p:confirmDialog>   
    <style>
                    .cntr{
                text-align:center;
                padding: 9px;
            }
            .err-msg{
                color:green;
                font-weight: bold !important;
            }
            .error-dlg{
                
                text-align:left;
               height: auto !important;
               width: 500px;
            }
            .cntr{
                text-align:center;
                padding: 9px;
            }
    </style>


</ui:composition>

