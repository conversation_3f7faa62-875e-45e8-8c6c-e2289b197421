<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: HistoryDlg.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    <p:dialog widgetVar="contHistoryDlg" id="dlgContactHistory" width="1000" height="400" draggable="false"
              header="Contact History" resizable="true">
        <h:form id="frmContactHistory">
            <!--            //PMS:2886
                        //seema - 01/01/2021-->
            <p:dataTable value="#{recordHistoryService.contHistorylst}" id="dtContactHistry" var="histry"
                         class="hide-column-names" rendered="#{recordHistoryService.contHistorylst.size()>0}"
                         rowStyleClass="tbl-row" >
                <p:column style="height: auto">
                    <div style="margin-bottom: -10px;">
                        <!--                                //PMS:2565
                                                                              //Seema - 01/12/2020-->
                        <h:outputLabel class="header-label" id="oLblHistryUpdatedDate"
                                       value="#{histry.updDate eq null?'':recordHistoryService.getFormattedDate(histry.updDate,'dt')} ">
                            <p:spacer width="4"/>
                            <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                        </h:outputLabel>
                        <h:outputLabel value="#{histry.histryStatus}" styleClass="header-label" style="color:black!important;" id="oLblContHistryDesc"/>
                        <p:spacer width="4"/>
                        <h:outputLabel value=" by #{histry.userName}" styleClass="header-label" id="oLblContHisryBy"/>
                    </div>
                    <br/>

                    <table>
                        <ui:repeat value="#{histry.keys}" var="key">
                            
<!--                            <h:panelGroup rendered="#{histry.historyData.get(key) != ''}" >
                                <h:outputLabel  value="#{key}:"  styleClass="opp-label" />
                                <p:spacer width="4" />
                                <h:outputLabel value="#{histry.historyData.get(key)}" styleClass="opp-value" />
                                <p:spacer width="5" />
                            </h:panelGroup>-->
<!--                                //PMS:2886
                                //Seema - 04/01/2021-->
                                <h:outputLabel  value="#{key}:"  styleClass="opp-label" />
                                <p:spacer width="4" />
                                <h:outputLabel value="#{histry.historyData.get(key)}" styleClass="opp-value" />
                                <p:spacer width="5" />
                           
                        </ui:repeat>
                    </table>
                </p:column>
            </p:dataTable>
            <!--            //PMS:2886
                      //seema - 01/01/2021-->
            <p:outputLabel id="oLblNoHistryMsg" rendered="#{recordHistoryService.contHistorylst.size() eq 0}" 
                           styleClass="header-label" style="color:black!important;"
                           value="Nothing is logged for this contact"/>
            <div style="text-align: center">
                <p:commandButton value="OK" style="margin-top: 10px" id="btnContHistryOk"
                                 onclick="PF('contHistoryDlg').hide()"
                                 styleClass="btn-info btn-xs"
                                 />
            </div>

        </h:form>
        <style>
            .opp-value{
                color:black!important;
                line-height: 1.5em;
            }
            .opp-label{
                color:rgba(0, 0, 0, 0.51)!important;
            }
            .header-label{
                font: initial;
                font-size: small;
            }
            .history-label{
                color:black!important;
            }
            .tbl-row{
                background: rgba(199, 205, 213, 0.21);
            }
        </style>
    </p:dialog>



</ui:composition>