<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: LineItemCustomFields.xhtml
// Author: Sarayu
//*********************************************************
* Copyright (c) 2017 Indea Design Systems Private Limited, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <style>
        .firstColumn{
            min-width: 200px !important;
        }

        .secondColumn{
            min-width: 360px !important;
        }
        .grpBox{
            padding:0 20px!important;
            margin:0px!important;
            border: solid 1px #d1d4de;


        }

    </style>

    <p:outputLabel value="No #{custom.labels.get('IDS_CUSTOM_FIELDS')}" rendered="#{lineItemCustomFields.lineCustomFieldsList eq null or lineItemCustomFields.lineCustomFieldsList.size() eq 0}"/>

    <ui:repeat value="#{lineItemCustomFields.lineCustomFieldsList}" var="cf">

        <p:outputPanel rendered="#{cf.custFieldType eq 'in'}" style="padding-bottom: 3px; padding-top: 3px;">
            <h:panelGrid columns="3" cellpadding="2" columnClasses="firstColumn, secondColumn">
                <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                <!--                <p:outputLabel styleClass="m" value=" "/>-->
                <p:outputPanel>
                    <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                    <p:spinner value="#{cf.customFieldValue}" onkeypress="return event.keyCode != 13;" min="#{cf.custFieldMin}" max="#{cf.custFieldMax}" size="8"/>
                    <p:spacer height="0" width="10"/>
                    <p:outputLabel value="(min. #{cf.custFieldMin}, max. #{cf.custFieldMax})"/>
                </p:outputPanel>
            </h:panelGrid>
        </p:outputPanel>
        <!--<p:spacer height="1"/>-->
        <p:outputPanel rendered="#{cf.custFieldType eq 'tx'}" style="padding-bottom: 3px; padding-top: 3px;">
            <h:panelGrid columns="3" cellpadding="3" columnClasses="firstColumn, secondColumn">
                <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                <!--                <p:outputLabel styleClass="m" value=" "/>-->
                <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                <p:inputText value="#{cf.customFieldValue}" onkeypress="return event.keyCode != 13;" style="width: 97%" maxlength="#{customFieldsMstService.fieldMaxLength}"/>
            </h:panelGrid>
        </p:outputPanel>
        <!--<p:spacer height="1"/>-->
        <p:outputPanel rendered="#{cf.custFieldType eq 'da'}" style="padding-bottom: 3px; padding-top: 3px;">
            <h:panelGrid columns="3" cellpadding="3" columnClasses="firstColumn, secondColumn">
                <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                <!--<p:outputLabel styleClass="m" value=" "/>-->
                <p:calendar value="#{cf.customFieldValue}" pattern="#{globalParams.dateFormat}" style="width: 100%" converterMessage="#{cf.custFieldLabel} is invalid">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:calendar>
            </h:panelGrid>
        </p:outputPanel>
        <!--<p:spacer height="1"/>-->
        <p:outputPanel rendered="#{cf.custFieldType eq 'dt'}" style="padding-bottom: 3px; padding-top: 3px;">
            <h:panelGrid columns="3" cellpadding="3" columnClasses="firstColumn, secondColumn">
                <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                <!--<p:outputLabel styleClass="m" value=" "/>-->
                <p:calendar value="#{cf.customFieldValue}" pattern="#{globalParams.dateTimeFormat}" style="width: 100%" converterMessage="#{cf.custFieldLabel} is invalid">
                    <f:convertDateTime pattern="#{globalParams.dateTimeFormat}" />
                </p:calendar>
            </h:panelGrid>
        </p:outputPanel>
        <!--<p:spacer height="1"/>-->
        <p:outputPanel rendered="#{cf.custFieldType eq 'dd'}" style="padding-bottom: 3px; padding-top: 3px;">
            <h:panelGrid columns="3" cellpadding="3" columnClasses="firstColumn, secondColumn">
                <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                <!--<p:outputLabel styleClass="m" value=" " />-->
                <p:selectOneMenu value="#{cf.customFieldValue}" style="width: 100%">
                    <!--#631003: Custom Fields: dropdown defaults to 1st in list-->
                    <f:selectItem itemLabel="[--Select--]" itemValue="" />
                    <f:selectItems var="cfOption" value="#{cf.custSelectOptions}" itemLabel="#{cfOption}" itemValue="#{cfOption}" />
                </p:selectOneMenu>                   
            </h:panelGrid>
        </p:outputPanel>
        <!--<p:spacer height="1"/>-->
        <!--CRM-265 :MultiSelect Checkbox--> 
        <!--        #2940 CRM-1847: Golden: custom field with multi select is awkward-->
        <p:outputPanel rendered="#{cf.custFieldType eq 'md'}" style="padding-bottom: 3px; padding-top: 3px;">
            <h:panelGrid id="multiLineMenuSelect" columns="3" cellpadding="3" style="width:60%" columnClasses="firstColumn, secondColumn">
                <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                <p:selectCheckboxMenu   value="#{cf.multiSelectedOptions}" multiple="true" label="#{cf.custFieldLabel}" filter="true" filterMatchMode="startsWith" panelStyle="width:250px"  >
                    <f:selectItems value="#{cf.allMultiSelect}" var="cust" itemLabel="#{cust}" itemValue="#{cust}"/>
                    <!--                    <p:ajax  event="change" update="displayOption" />
                                        <p:ajax event="toggleSelect" update="displayOption"  />-->

                </p:selectCheckboxMenu>
                <!--<p:outputLabel styleClass="m" value=" " />-->
                <!--                <p:outputPanel id="displayOption" class="ui-selectonemenu-label ui-inputfield ui-corner-all potential-css "  style=" border: solid 1px #d1d4de; min-height: 28px;" >
                                    <p:dataList value="#{cf.multiSelectedOptions}" var="cust" emptyMessage="No options selected" class="grpBox">
                #{cust}
            </p:dataList>
        </p:outputPanel>-->

            </h:panelGrid>
        </p:outputPanel>
        <!--<p:spacer height="1"/>-->

        <p:outputPanel rendered="#{cf.custFieldType eq 'cb'}" style="padding-bottom: 3px; padding-top: 3px;">
            <h:panelGrid columns="3" cellpadding="3" columnClasses="firstColumn, secondColumn">
                <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                <!--<p:outputLabel styleClass="m" value=" " />-->
                <p:selectBooleanCheckbox value="#{cf.cbValue}"/>                  
            </h:panelGrid>
        </p:outputPanel>
        <!--<p:spacer height="1"/>-->
        <p:outputPanel rendered="#{cf.custFieldType eq 'de'}" style="padding-bottom: 3px; padding-top: 3px;">
            <h:panelGrid columns="3" cellpadding="3" columnClasses="firstColumn, secondColumn">
                <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                <!--<p:outputLabel styleClass="m" value=" " />-->
                <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                <p:inputText value="#{cf.customFieldValue}" onkeypress="return event.keyCode != 13;" maxlength="12" converterMessage="#{cf.custFieldLabel} is not a number">
                    <f:convertNumber groupingUsed="false" maxIntegerDigits="15"  maxFractionDigits="4" minFractionDigits="2" />
                </p:inputText>
            </h:panelGrid>
        </p:outputPanel>
        <!--<p:spacer height="1"/>-->
        <!--CRM 158-->
        <p:outputPanel rendered="#{cf.custFieldType eq 'ta'}" style="padding-bottom: 3px; padding-top: 3px;">
            <h:panelGrid columns="3" cellpadding="3" columnClasses="firstColumn, secondColumn">
                <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                <!--<p:outputLabel styleClass="m" value=" " />-->
                <p:inputTextarea rows="5" value="#{cf.customFieldValue}" style="overflow-y: scroll; height: 50px; width: 97%;" maxlength="#{customFieldsMstService.getLargeTextMaxLength()}" />
            </h:panelGrid>
        </p:outputPanel>
        <!--<p:spacer height="1"/>-->
        <!--CRM 101-->
        <p:outputPanel rendered="#{cf.custFieldType eq 'lk'}" style="padding-bottom: 3px; padding-top: 3px;">
            <h:panelGrid columns="3" cellpadding="3" columnClasses="firstColumn, secondColumn">
                <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                <!--<p:outputLabel styleClass="m" value=""/>-->
                <h:panelGroup>
                    <!--Sharvani-03/06/2019:User story:CRM-589:Custom Fields-->
                    <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                    <p:inputText value="#{cf.customFieldValue}" onkeypress="return event.keyCode != 13;" style="width: 65%" validatorMessage="Invalid URL" requiredMessage="#{cf.custFieldLabel} is mandatory" maxlength="#{customFieldsMstService.hyprTxtFieldMaxLength}">
                        <!--<f:validateRegex pattern="^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$" />-->
                    </p:inputText> 
                    <p:spacer width="10" />
                    <!--2873 Code Optimization - Company Details screen    -->   
                    <!--15-06-2023 11410  CRM-7057   Microsoft OneNote Section link URL being redirected from rf url custom field popout: Bulk-->
                    <p:commandLink id="opnLinkLI" value="Open link" style="color: blue;" disabled="#{cf.customFieldValue == ''}" onclick="window.open('#{companyService.getValidURL(cf.customFieldValue)}', '_blank');" />
                    <p:tooltip id="toolTopOpnLinkOppLI"  for="opnLinkLI" value="#{cf.customFieldValue}" showEvent="mouseover" position="top" />
                    <style type="text/css">
                        .ui-tooltip {
                            word-wrap: break-word;
                            max-width: 800px;
                            white-space:pre-wrap;
                            text-overflow: ellipsis
                        }
                    </style>
                </h:panelGroup>
            </h:panelGrid>
        </p:outputPanel>

    </ui:repeat>
    <style>
        .ui-selectcheckboxmenu-multiple-container{
            min-width: 100%;
            width:100%; 
            height: auto !important;

        }
    </style>
</ui:composition>
