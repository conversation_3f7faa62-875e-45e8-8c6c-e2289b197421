<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: OppCustomFields.xhtmlfetchCustomFields
// Author: Sarayu
//*********************************************************
* Copyright (c) 2017 Indea Design Systems Private Limited, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <p:remoteCommand id="rcOppCustomFields" name="rcOppCustomFields" update="pnlOppCustFields" actionListener="#{oppCustomFields.populateCustomFields()}" autoRun="true"/>
    <p:blockUI block="pnlOppCustFields" trigger="rcOppCustomFields"/>

    <h:panelGroup  layout="grid"  id="pnlOppCustFields">
        <style>
            .firstColumn{
                min-width: 200px !important;
            }

            .secondColumn{
                min-width: 360px !important;
            }


            .grpBox{
                padding:0 20px!important;
                margin:0px!important;
                border: solid 1px #d1d4de;


            }
        </style>

        <p:outputLabel value="No #{custom.labels.get('IDS_CUSTOM_FIELDS')}" rendered="#{oppCustomFields.oppCustomFieldsList eq null or oppCustomFields.oppCustomFieldsList.size() eq 0}"/>

        <ui:repeat value="#{oppCustomFields.oppCustomFieldsList}" var="cf">

            <p:outputPanel rendered="#{cf.custFieldType eq 'in'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputPanel>
                            <p:spinner value="#{cf.customFieldValue}" min="#{cf.custFieldMin}" max="#{cf.custFieldMax}" size="8"/>
                            <p:spacer height="0" width="10"/>
                            <p:outputLabel value="(min. #{cf.custFieldMin}, max. #{cf.custFieldMax})" />
                        </p:outputPanel>
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>

            <p:outputPanel rendered="#{cf.custFieldType eq 'tx'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:inputText value="#{cf.customFieldValue}" maxlength="#{customFieldsMstService.fieldMaxLength}"/>

                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>

            <p:outputPanel rendered="#{cf.custFieldType eq 'da'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 

                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:calendar value="#{cf.customFieldValue}" pattern="#{globalParams.dateFormat}" style="width:80%" converterMessage="#{cf.custFieldLabel} is invalid">
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </p:calendar>
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>

            <p:outputPanel rendered="#{cf.custFieldType eq 'dt'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">

                        <p:calendar value="#{cf.customFieldValue}" pattern="#{globalParams.dateTimeFormat}" style="width: 100%" converterMessage="#{cf.custFieldLabel} is invalid">
                            <f:convertDateTime pattern="#{globalParams.dateTimeFormat}" />
                        </p:calendar>
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6">   </div> 

                </div>
            </p:outputPanel>

            <p:outputPanel rendered="#{cf.custFieldType eq 'dd'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:selectOneMenu value="#{cf.customFieldValue}"  >
                            <f:selectItem itemLabel="[--Select--]" itemValue="" />
                            <f:selectItems var="cfOption" value="#{cf.custSelectOptions}" itemLabel="#{cfOption}" itemValue="#{cfOption}" />
                        </p:selectOneMenu> 
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>
            <!--CRM-265 :MultiSelect Checkbox--> 
            <p:outputPanel rendered="#{cf.custFieldType eq 'md'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 

<!--                        <p:selectCheckboxMenu  value="#{cf.multiSelectedOptions}" label="#{cf.custFieldLabel}" filter="true" filterMatchMode="startsWith"   >
                            <f:selectItems value="#{cf.allMultiSelect}" />
                            <p:ajax  event="change" update="displayOption" />
                            <p:ajax event="toggleSelect" update="displayOption"  />

                        </p:selectCheckboxMenu>-->
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
<!--                        #2940 CRM-1847: Golden: custom field with multi select is awkward-->
                        <p:selectCheckboxMenu  value="#{cf.multiSelectedOptions}" label="#{cf.custFieldLabel}" multiple="true" filter="true" filterMatchMode="startsWith"   >
                            <f:selectItems value="#{cf.allMultiSelect}" var="mulVal" itemLabel="#{mulVal}" itemValue="#{mulVal}" />
<!--                            <p:ajax  event="change" update="displayOption" />
                            <p:ajax event="toggleSelect" update="displayOption"  />-->

                        </p:selectCheckboxMenu>
<!--                        <p:outputPanel id="displayOption" class="ui-selectonemenu-label ui-inputfield ui-corner-all potential-css " style=" border: solid 1px #d1d4de; min-height: 28px;" >
                            <p:dataList value="#{cf.multiSelectedOptions}" var="cust" emptyMessage="No options selected">
                                #{cust.toString()}
                            </p:dataList> 
                        </p:outputPanel>-->
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>



            <p:outputPanel rendered="#{cf.custFieldType eq 'cb'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:selectBooleanCheckbox value="#{cf.cbValue}" style="width:80%"/>             
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>

            <p:outputPanel rendered="#{cf.custFieldType eq 'de'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:inputText value="#{cf.customFieldValue}" maxlength="12" converterMessage="#{cf.custFieldLabel} is not a number">
                            <f:convertNumber groupingUsed="false" maxIntegerDigits="15"  maxFractionDigits="4" minFractionDigits="2" />
                        </p:inputText>
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>
            <!--CRM 158-->
            <p:outputPanel rendered="#{cf.custFieldType eq 'ta'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:inputTextarea   rows="5" value="#{cf.customFieldValue}" style="overflow-y: scroll; height: 50px;" maxlength="#{customFieldsMstService.getLargeTextMaxLength()}">
                        </p:inputTextarea>
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>
            <!--CRM 101-->
            <p:outputPanel rendered="#{cf.custFieldType eq 'lk'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <h:panelGroup>
                            <!--Sharvani-03/06/2019:User story:CRM-589:Custom Fields-->
                            <p:inputText   value="#{cf.customFieldValue}"   validatorMessage="Invalid URL" maxlength="#{customFieldsMstService.hyprTxtFieldMaxLength}">
                                <!--<f:validateRegex pattern="^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$" />-->
                            </p:inputText> 
                            <p:spacer width="10" />
<!--2873 Code Optimization - Company Details screen    -->                            
                            <p:commandLink id="opnLinkOpp" value="Open link" style="color: blue;" title="#{cf.customFieldValue}" disabled="#{cf.customFieldValue == ''}" onclick="window.open('#{companyService.getValidURL(cf.customFieldValue)}', '_blank');" />
                        </h:panelGroup>
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>

        </ui:repeat>

    </h:panelGroup>
    <style>
        .ui-selectcheckboxmenu-multiple-container{
            width:100%; 
            height: auto !important;

        }
    </style>
</ui:composition>
