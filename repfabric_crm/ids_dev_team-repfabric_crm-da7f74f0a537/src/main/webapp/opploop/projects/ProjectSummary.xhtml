<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: Projects
// Author: Sharvani
//*********************************************************
/*
*
*/-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces">



    <!--    <h:head>
            <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
        </h:head>
    
        <ui:define name="title">
            <ui:param name="title" value="Commissionable Transactions"/>
            <div class="row">
                <div class="col-md-6">
                    Projects
                </div>
            </div>
        </ui:define>
        <ui:define name="menu">
    
        </ui:define>
    
        <ui:define name="body">
            <div class="box box-info box-body" style="vertical-align: top">
            </div>
        </ui:define>-->

    <div class="ui-g ui-fluid header-bar">
        <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
        </div>
        <div class="ui-sm-12 ui-md-6 ui-lg-6 " >
            <p:outputLabel styleClass="acct-name-hdr" class="sub_title">#{custom.labels.get('IDS_OPP_PROJ')} Summary</p:outputLabel>
        </div>
        <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
        </div>
    </div>





<!--     <h1 class="hedding">#{custom.labels.get('IDS_OPP_PROJ')} Summary</h1>
    <br/>-->
    <p:outputPanel id="projSummary" style="text-align: center;" >

        <table cellpadding="2px" style="width: 100%;background: white;table-layout: fixed;padding: 0px 6px;">
            <tr>
                <td style="text-align: left">
                    <h:outputLabel value="Start Date: " style="font-weight: bold"    />
                    <p:spacer width="4px"/>
                    <h:outputLabel class="lbl_val"  value="#{projOpps.selectedProj.projStartDate}" >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                    </h:outputLabel>
                </td>
            </tr>

            <tr>
                <td style="text-align: left">
                    <h:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}: " style="font-weight: bold"/>
                    <p:spacer width="4px"/>
                    <h:link   outcome="/opploop/companies/CompanyDetails.xhtml?id=#{projOpps.selectedProj.projCustId}"  
                              value="#{projOpps.selectedProj.projCustName}" />
                </td>
            </tr> 

            <tr>
                <td style="text-align: left">
                    <h:outputLabel value="#{custom.labels.get('IDS_PROGRAM')}: " style="font-weight: bold"    />
                    <p:spacer width="4px"/>
                    <h:outputLabel class="lbl_val"  value="#{projOpps.selectedProj.projProgram}" />
                </td>
            </tr>

            <tr>
                <td style="text-align: left">
                    <h:outputLabel value="Value: " style="font-weight: bold"    />
                    <p:spacer width="4px"/>
                    <h:outputLabel class="lbl_val"  value="#{projOpps.selectedProj.projValue}" />
                </td>
            </tr>

        </table>
    </p:outputPanel>

</ui:composition>
