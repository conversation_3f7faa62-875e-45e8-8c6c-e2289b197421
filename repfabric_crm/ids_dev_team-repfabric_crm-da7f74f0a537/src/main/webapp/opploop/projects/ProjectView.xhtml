<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: Projects
// Author: Sharvani
//*********************************************************
/*
*
*/-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <!--
    
        <h:head>
            <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
        </h:head>
    
        <ui:define name="title">
            <ui:param name="title" value="Projects"/>
            <div class="row">
                <div class="col-md-6">
                    Projects
                </div>
            </div>
        </ui:define>
        <ui:define name="menu">
    
        </ui:define>-->
    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>


    <!--#358 Page Title sharvanim - 03-05-2020-->

    <ui:define name="head">


        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>

        <title>#{custom.labels.get('IDS_OPP_PROJ')} Details</title>
    </ui:define>


    <style>
        .ui-button-icon-right {
            right: 36.5em !important;
        }
        .ui-select-btn{
            border-radius: 0px;
            margin-top: 0px;
            background: #fff; 
            height:27px;
        }

        .ui-datatable .ui-datatable-header, .ui-datatable .ui-datatable-footer {
            text-align: right !important;

        }

        .ui-chkbox .ui-chkbox-box {
            display: initial !important;
            margin-left: -8px !important;
        }
    </style>


    <ui:define name="title">
        <ui:param name="title" value="Projects"/>
        <div class="row">
            <div class="col-md-6">
                #{custom.labels.get('IDS_OPP_PROJ')} View  
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>

    <ui:define name="content">
        <f:metadata>
            <f:viewParam id="id" name="id" value="#{projOpps.projId}"/>      
            <f:viewAction action="#{projOpps.loadProjDetails()}"/>
            <!--2520 - Related - CRM-1521: Tutorial button landing urls - Quotes, Samples, Opportunities, Jobs, Projects-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('PROJ_DTL'))}" />
            <!--29-09-2023  12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('PROJ_DTL')}" />        
        </f:metadata>
    </ui:define>

    <ui:define name="body">
        <!--#1293: Add Page Access Check - Samples, Customer Exceptions, Projects-->
        <f:event listener="#{projects.isPageAccessible}" type="preRenderView"/>  

        <div class="box box-info box-body" style="vertical-align: top">
            <div class="left-column">

                <ui:include src="ProjectSummary.xhtml" />
            </div>


            <div class="right-column">

                <h:form id="projOppListForm">
                    <p:growl id="projGrowl"/>

                    <div class="box-header with-border">


                        <h:link   outcome="/opploop/opportunity/OpportunityView.xhtml?projid=#{projOpps.projId}"  >
                            <p:commandButton type="button" ajax="false" process="none"
                                             title="Create new  #{custom.labels.get('IDS_OPP')}" class="btn btn-primary btn-xs" 
                                             value="Create #{custom.labels.get('IDS_OPP')}" />
                        </h:link>
                        <p:spacer width="4px"/>
                        <p:commandButton action="#{projOpps.fetchOpps()}"
                                         title="Add  #{custom.labels.get('IDS_OPP')}" class="btn btn-primary btn-xs" 
                                         value="Add Existing #{custom.labels.get('IDS_OPP')}"  oncomplete="PF('reltdOpportunityDlg').show()" update=":reltdOpportunityForm"/>
                        <p:spacer width="4px"/>
                        <p:commandButton action="#{projOpps.clear()}"
                                         title="Update  Selected #{custom.labels.get('IDS_OPP')}" disabled="#{projOpps.selectedProjOpps.size() eq 0}" class="btn btn-success btn-xs" 
                                         value="Update Selected"  oncomplete="PF('projOppsUpdateDlg').show()" update=":projOppsUpdateForm"/>
                        <p:spacer width="4px"/>
                        <!--#3142 CRM-1967: Opportunities: Victory/Golden: when clone inside project auto adds to project-->
                        <p:commandButton  value="Clone" class="btn btn-primary btn-xs"   disabled="#{projOpps.selectedProjOpps.size() eq 0}"
                                          action="#{projOpps.cloneClear()}" update=":cloneOppForm"/>
                        <!--oncomplete="PF('cloneOppDlg').show();"-->

                    </div>


                </h:form>


                <h:form id="frmProjOpps">
                    <p:dataTable  id="DTProj" widgetVar="tblProj" paginator="true"                    
                                  value="#{projOpps.projOppsList}"                         
                                  filterEvent="keyup"                          
                                  filteredValue="#{projOpps.filteredProjOpps}" 
                                  selection="#{projOpps.selectedProjOpps}"
                                  var="opp"                                       
                                  rowKey="#{opp.oppId}"                           
                                  paginatorAlwaysVisible="false"
                                  paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                  rows="50"   class="tblmain"
                                  emptyMessage="No #{custom.labels.get('IDS_OPP_PROJ')} #{custom.labels.get('IDS_OPPS')} found"
                                  draggableColumns="true"
                                  tableStyle="table-layout:auto" resizableColumns="true">
                        <f:facet name="header">
                            <p:inputText style="display: none"   id="globalFilter" onkeyup="PF('tblProj').filter()" />
                        </f:facet>


                        <p:column  selectionMode="multiple" style="width:16px;text-align:center"/>
                        <p:ajax event="toggleSelect" update=":projOppListForm"/>
                        <p:ajax event="rowSelectCheckbox" update=":projOppListForm"/>
                        <p:ajax event="rowUnselectCheckbox" update=":projOppListForm"/>

                        <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterMatchMode="contains" 
                                  filterBy="#{opp.principalName}"  
                                  sortBy="#{opp.principalName}">
                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"
                                    value="#{opp.principalName}"/>
                        </p:column>

                        <p:column headerText="#{custom.labels.get('IDS_PROGRAM')}" filterMatchMode="contains" 
                                  filterBy="#{opp.oppCustProgram}"
                                  sortBy="#{opp.oppCustProgram}">
                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"
                                    value="#{opp.oppCustProgram}"/>
                        </p:column>

                        <p:column  headerText="#{custom.labels.get('IDS_ACTIVITY')}"  filterMatchMode="contains" 
                                   filterBy="#{opp.oppActivity}" 
                                   sortBy="#{opp.oppActivity}" >
                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"
                                    value="#{opp.oppActivity}"/>
                        </p:column>   
                        <!--Feature #5025 RE: Repfabric / AVX CRMSync / Relabeling Fields / "Next Step" (Pending)-->
                        <!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
                        <p:column  headerText="#{custom.labels.get('IDS_NEXT_STEP')}"  filterMatchMode="contains" 
                                   filterBy="#{opp.oppNextStep}" styleClass="might-overflow">
                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"
                                    value="#{opp.oppNextStep}" />
                        </p:column> 

                        <!--Fixed value to display whole numbers-->
                        <p:column  headerText="#{custom.labels.get('IDS_VALUE')}"  filterMatchMode="contains"
                                   filterBy="#{opp.oppValue}" 
                                   sortBy="#{opp.oppValue}" >
                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                                <p:outputLabel value="#{opp.oppValue}">
                                    <f:convertNumber maxFractionDigits="0" groupingUsed="true" maxIntegerDigits="15"/>
                                </p:outputLabel>
                            </h:link>
                        </p:column>   

                        <p:column  headerText="#{custom.labels.get('IDS_FOLLOW_UP')}"  filterMatchMode="contains" 
                                   filterBy="#{opp.oppFollowUp}" 
                                   sortBy="#{opp.oppFollowUp}" >
                            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}"
                                    value="">
                                <h:outputLabel value="#{opp.oppFollowUp}">
                                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                </h:outputLabel>
                            </h:link>
                        </p:column>

                        <p:column  headerText="#{custom.labels.get('IDS_LINE_ITEMS')}" style="text-align: center">
                            <p:commandLink value="#{opp.oppLines}" style="color: blue;" action="#{projOpps.fetchOppLineItems(opp.oppId)}" oncomplete="PF('OppLineItemsDlg').show()" update=":frmProjOppLineItem"/>
                        </p:column> 

                        <!--                        <p:column>
                                                    <p:commandButton action="#{projOpps.deleteLinkedOpp(opp.oppId)}" icon="ui-icon-trash"  update=":projOppListForm:projGrowl :frmProjOpps :projSummary" style="height:25px;width:25px;">
                                                        <p:confirm header="Confirmation" message="Are you sure to remove #{custom.labels.get('IDS_OPP')} from #{custom.labels.get('IDS_OPP_PROJ')}?" icon="ui-icon-alert" />
                                                    </p:commandButton>
                                                </p:column> -->

                        <p:column style="width:39px "  toggleable="false">
                            <p:commandButton action="#{projOpps.deleteLinkedOpp(opp.oppId)}" icon="fa fa-trash"  update=":frmProjOpps:DTProj :projOppListForm:projGrowl :frmProjOpps :projSummary" style="height:20px;width:20px;" styleClass="btn-danger btn-xs btn-icon">
                                <!--<p:confirm header="Confirmation" message="Are you sure to delete the #{custom.labels.get('IDS_OPP_PROJ')}?" icon="ui-icon-alert" />-->
                                <p:confirm header="Confirmation" message="Are you sure to remove #{custom.labels.get('IDS_OPP')} from #{custom.labels.get('IDS_OPP_PROJ')} ?" icon="ui-icon-alert"/>
                            </p:commandButton>
                        </p:column>

                    </p:dataTable>
                </h:form>


                <!--                <h:form>
                                    <p:confirmDialog global="true" showEffect="fade">
                                        <p:commandButton value="Yes" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
                                        <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
                                    </p:confirmDialog>
                                </h:form>-->

                <h:form id="delProjects">
                    <p:confirmDialog global="true" showEffect="fade" responsive="true" >
                        <div class="button_bar" align="center">
                            <p:commandButton value="Yes" type="button" styleClass="ui-confirmdialog-yes btn btn-success btn-xs"  />
                            <p:spacer width="4px" />
                            <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no btn btn-danger btn-xs"  />
                        </div>
                    </p:confirmDialog>
                </h:form>

                <ui:include src="LinkOppsDlg.xhtml"/>
                <ui:include src="EditItem.xhtml"/>
                <ui:include src="ProjOppsUpdateDlg.xhtml"/>
                <ui:include src="OppLineItemsDlg.xhtml"/>
                <ui:include src="CloneOppDlg.xhtml"/>
                <ui:include src="/lookup/CompanyLookupDlg.xhtml"/>
                <ui:include src="/lookup/PartNumDlg.xhtml" />

            </div>
        </div>
    </ui:define>
</ui:composition>
