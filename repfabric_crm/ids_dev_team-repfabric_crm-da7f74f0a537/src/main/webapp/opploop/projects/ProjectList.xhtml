<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: Projects
// Author: Sharvani
//*********************************************************
/*
*
*/-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">



    <h:head>
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
    </h:head>
     <!--#358 Page Title sharvanim - 03-05-2020-->

    <ui:define name="head">


        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>

        <title>#{custom.labels.get('IDS_OPP_PROJ')} List</title>
    </ui:define>

    <!--    <ui:define name="title">
            <f:event listener="#{projects.isPageAccessible}" type="preRenderView"/>
            <f:metadata>
                <f:viewAction action="#{projects.fetchProjectList()}"/>
            </f:metadata>
    #{custom.labels.get('IDS_OPP_PROJ')} List   
</ui:define>-->

    <ui:define name="title">
        <ui:param name="title" value="Projects List"/>
        <div class="row">
            <div class="col-md-6">
                <f:metadata>
                    <f:viewAction action="#{projects.fetchProjectList()}"/>
                    <!--2520 - Related - CRM-1521: Tutorial button landing urls - Quotes, Samples, Opportunities, Jobs, Projects-->
                    <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('PROJ_LIST'))}" />
                    <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
                    <f:viewAction action="#{helpService.setPageTag('PROJ_LIST')}" />                
                </f:metadata>
                #{custom.labels.get('IDS_OPP_PROJ')} List   
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>
    <ui:define name="menu">

    </ui:define>

    <ui:define name="body">
        <!--#1293: Add Page Access Check - Samples, Customer Exceptions, Projects-->
        <f:event listener="#{projects.isPageAccessible}" type="preRenderView"/>  
        <div class="box box-info box-body" style="vertical-align: top">
            <h:form id="projListForm">
                <p:growl id="projGrowl"/>

                <div class="box-header with-border">
                    <div class="row">
                        <div class="col-md-5">
                            <p:commandButton action="#{projects.onCreateProject()}" update=":addReltdCompForm"
                                             title="Create new #{custom.labels.get('IDS_OPP')}" 
                                             value="New" styleClass="btn btn-primary btn-xs" oncomplete="PF('createProjDlg').show()" />


                            <!--                <div style="float: left;margin-top: 9px;
                                                 margin-left: 5px;">
                                                <ui:include src="/opploop/opportunity/ajaxStatus.xhtml"  />
                                            </div>-->
                        </div>  
                    </div>
                </div>

                <p:dataTable  id="DTProj" widgetVar="tblProj" paginator="true"                    
                              value="#{projects.projectList}"                         
                              filterEvent="keyup"                          
                              filteredValue="#{projects.filteredProjList}" 
                              var="proj"                                       
                              rowKey="#{proj.recId}"                                    
                              paginatorAlwaysVisible="false"
                              paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                              rows="50"   class="tblmain"
                              emptyMessage="No #{custom.labels.get('IDS_OPP_PROJ')}s available"
                              draggableColumns="true">
                    <f:facet name="header">
                        <p:inputText style="display: none"   id="globalFilter" onkeyup="PF('tblProj').filter()" />
                    </f:facet>

                    <p:column  headerText="#{custom.labels.get('IDS_CUSTOMER')}"  filterMatchMode="contains" 
                               filterBy="#{proj.projCustName}"
                               sortBy="#{proj.projCustName.toUpperCase()}">
                        <h:link style="text-decoration: underline"  outcome="ProjectView.xhtml?id=#{proj.projId}"  
                                value="#{proj.projCustName}"  />
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_PROGRAM')}" filterMatchMode="contains" 
                              filterBy="#{proj.projProgram}"
                              sortBy="#{proj.projProgram.toUpperCase()}">
                        <h:link outcome="ProjectView.xhtml?id=#{proj.projId}"    
                                value="#{proj.projProgram}"  />
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_VALUE')}" filterMatchMode="contains" 
                              filterBy="#{proj.projValue}" 
                              style="max-width: 100px;
                              padding: 5px 2px 1px 2px !important;"  
                              sortBy="#{proj.projValue}">
                        <h:link  outcome="ProjectView.xhtml?id=#{proj.projId}" >

                            <p:outputLabel value="#{proj.projValue}" style="float: right">
                                <f:convertNumber maxFractionDigits="0" groupingUsed="true" maxIntegerDigits="15"/>
                            </p:outputLabel>

                        </h:link> 

                    </p:column>

                    <!--#383366 : Project: Need filter by sales team, add sales team.-->
                    <p:column headerText="#{custom.labels.get('IDS_SALES_TEAM')}" filterMatchMode="contains" 
                              filterBy="#{proj.smanName.toUpperCase()}"
                              sortBy="#{proj.smanName}">
                        <h:link outcome="ProjectView.xhtml?id=#{proj.projId}"    
                                value="#{proj.smanName}"  />
                    </p:column>

                    <p:column style="width:07%;">
                        <!--Edit project/s option required-->
                        <p:commandButton title="Edit" icon="fa fa-pencil"  action="#{projects.onUpdateProject(proj)}" update=":updateReltdCompForm" oncomplete="PF('updateProjDlg').show()"  styleClass="btn-primary  btn-xs btn-icon"/>
                        <p:spacer width="2"/>
                        <p:commandButton action="#{projects.deleteProject(proj.projId)}" icon="fa fa-trash"  update=":projListForm:projGrowl :projListForm" style="height:20px;width:20px;" styleClass="btn-danger btn-xs btn-icon">
                            <!--<p:confirm header="Confirmation" message="Are you sure to delete the #{custom.labels.get('IDS_OPP_PROJ')}?" icon="ui-icon-alert" />-->
                            <p:confirm header="Confirmation" message="Do you want to delete the #{custom.labels.get('IDS_OPP_PROJ')}?" icon="ui-icon-alert"/>
                        </p:commandButton>
                    </p:column>
                </p:dataTable>

            </h:form>

            <h:form id="delProjects">
                <p:confirmDialog global="true" showEffect="fade" responsive="true" >
                    <div class="button_bar" align="center">
                        <p:commandButton value="Yes" type="button" styleClass="ui-confirmdialog-yes btn btn-success btn-xs"  />
                        <p:spacer width="4px" />
                        <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no btn btn-danger btn-xs"  />
                    </div>
                </p:confirmDialog>
            </h:form>

            <ui:include src="CreateProjDlg.xhtml"/>
            <ui:include src="/lookup/CompanyLookupDlg.xhtml"/>
            <ui:include src="UpdateProjDlg.xhtml" />

            <script>
                function openTab(id, winName, event) {
                    var url1;
                    event.preventDefault();
                    if (id !== '') {
                        url1 = 'OpportunityView.xhtml?opid=' + id;
                    } else {
                        url1 = 'OpportunityView.xhtml';
                    }
                    this.target = '_blank';
                    var win = window.open(url1, winName, '', false);
                    win.focus();
                }
            </script>

        </div>
    </ui:define>
</ui:composition>