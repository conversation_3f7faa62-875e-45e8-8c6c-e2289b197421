<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <p:dialog id="dialog1" styleClass="disable-scroll" widgetVar="reltdOpportunityDlg" header=" #{custom.labels.get('IDS_OPPS')}"  modal="true" resizable="false"> 
        <h:form id = "reltdOpportunityForm">
            <!--SONARQUBE ISSUES - sharvani - 23-01-2020-->
            <!--<center>-->
            <div align="center">
                <!--2998 - Project: Value is not updated when an opportunity is created or existing opportunity is linked to the project- sharvani - 28-01-2020-->
                <p:commandButton value="Save" styleClass="btn btn-success  btn-xs"  
                                 actionListener="#{projOpps.saveProjOpps()}" update=":projOppListForm:projGrowl :frmProjOpps :reltdOpportunityForm :frmProjOpps:DTProj :projSummary"/>
                <p:spacer width="4px" />
                <p:commandButton value="Cancel" 
                                 styleClass="btn btn-warning btn-xs" onclick="PF('reltdOpportunityDlg').hide();"/>
            </div>
                <!--</center>-->
            <!--#908075: Project: add existing needs search filter-->
            <p:dataTable paginator="true"                    
                         value="#{projOpps.opplist}"                       
                         filterEvent="keyup"                          
                         filteredValue="#{projOpps.filteredOppList}"
                         selection="#{projOpps.selectedOppList}" 
                         var="opp"                                       
                         rowKey="#{opp.oppId}"                           
                         paginatorAlwaysVisible="false"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rows="50"   class="tblmain" scrollable="true"
                         scrollHeight="300"
                         emptyMessage="No #{custom.labels.get('IDS_OPPS')} found"
                         draggableColumns="true" style="width: 750px;margin-top: 10px;">
                <p:column  selectionMode="multiple" style="width:16px;text-align: center"/>                             
                <p:column  headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterMatchMode="contains" 
                           sortBy="#{opp.custName}" filterBy="#{opp.custName}" >
                    <h:outputText  value="#{opp.custName}" />
                </p:column>
                <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterMatchMode="contains" 
                          sortBy="#{opp.principalName}" filterBy="#{opp.principalName}">
                    <h:outputText  value="#{opp.principalName}" />
                </p:column>
                <p:column headerText="#{custom.labels.get('IDS_PROGRAM')}" filterMatchMode="contains" 
                          sortBy="#{opp.oppCustProgram}" filterBy="#{opp.oppCustProgram}">
                    <h:outputText  value="#{opp.oppCustProgram}" />
                </p:column>
                <p:column  headerText="#{custom.labels.get('IDS_ACTIVITY')}" filterMatchMode="contains" 
                           sortBy="#{opp.oppActivity}" filterBy="#{opp.oppActivity}">
                    <h:outputText  value="#{opp.oppActivity}" />
                </p:column>                   
                <p:column  headerText="#{custom.labels.get('IDS_FOLLOW_UP')}" filterMatchMode="contains" 
                           sortBy="#{opp.oppFollowUp}" filterBy="#{opp.oppFollowUp}">
                    <h:outputText  value="#{opp.oppFollowUp}" />
                </p:column>
            </p:dataTable>
        </h:form>
    </p:dialog>

</ui:composition>