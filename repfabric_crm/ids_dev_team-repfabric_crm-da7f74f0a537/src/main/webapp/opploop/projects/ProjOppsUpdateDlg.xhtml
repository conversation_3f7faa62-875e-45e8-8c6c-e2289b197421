<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <!--3091- CRM-1939: Unity: bulk update project with reporting comments - sharvani - 29-01-2020-->
    <p:dialog widgetVar="projOppsUpdateDlg" header="Update Selected" modal="true" resizable="false" width="450" height="450" style=" text-overflow: scroll" closeOnEscape="true" responsive="true" > 
        <h:form id = "projOppsUpdateForm">
            <p:panelGrid styleClass="box-primary no-border ui-fluid" layout="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" style="min-height: 100px"   >
                <p:outputLabel value="#{custom.labels.get('IDS_PROGRAM')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->      
                <p:inputText value="#{projOpps.opp.oppCustProgram}" 
                             style="width: 100%"
                             id="opcustprgm"
                             maxlength="120" />

                <p:outputLabel value="#{custom.labels.get('IDS_ACTIVITY')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->               
                <p:selectOneMenu style="width: 100%" widgetVar="wopselact" id="opselact" value="#{projOpps.opp.selActivity}"   >
                    <f:selectItem itemValue="" itemLabel="Select #{custom.labels.get('IDS_ACTIVITY')}"/>
                    <f:selectItems value="#{oppActivitiesMst.listOppActivities}"  
                                   var="act"   
                                   itemValue="#{act.recId}"
                                   itemLabel="#{act.actName}"/>
                    <p:ajax process="@this" listener="#{projOpps.activityChanged}"  update="opstat1" />
                </p:selectOneMenu>  

                <p:outputLabel value="#{custom.labels.get('IDS_ACT_STATUS')}" />
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:selectOneMenu widgetVar="wopstat" id="opstat1" style="width: 100%" value="#{projOpps.opp.oppStatus}" >
                    <f:selectItem itemValue="" itemLabel="Select #{custom.labels.get('IDS_ACT_STATUS')}"/>
                    <f:selectItems value="#{projOpps.statusList}"  
                                   var="status"   
                                   itemValue="#{status}"
                                   itemLabel="#{status}"/>
                </p:selectOneMenu>

                <p:outputLabel value="#{custom.labels.get('IDS_FOLLOW_UP')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:calendar  id="calopfollow"  pattern="#{globalParams.dateFormat}"
                             size="10"  widgetVar="wopfollow"
                             converterMessage="#{custom.labels.get('IDS_FOLLOW_UP')} date not valid" 
                             value="#{projOpps.opp.oppFollowUp}"  >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:calendar>

                <p:outputLabel value="#{custom.labels.get('IDS_PRIORITY')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:spinner min="0" max="10"    maxlength="2"  
                           size="8"  
                           id="oppriority"
                           value="#{projOpps.opp.oppPriority}">
                </p:spinner>

                <p:outputLabel value="#{custom.labels.get('IDS_POTENTIAL')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:spinner min="0" max="100"   maxlength="3" 
                           size="8"
                           id="oppotential"
                           value="#{projOpps.opp.oppPotential}">

                </p:spinner>

                <p:outputLabel value="#{custom.labels.get('IDS_EAU')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <!--                <p:inputText     
                                    value="#{projOpps.opp.oppEau}" 
                                    maxlength="10"
                                    size="10" 
                                    id="opeau">
                                    <f:convertNumber  type="number" integerOnly="true"/>
                
                                </p:inputText>-->
                <p:inputNumber value="#{projOpps.opp.oppEau}" 
                                    maxlength="10"
                                    size="10" 
                                    id="opeau">
                    
                </p:inputNumber>
<!--Feature #5025 RE: Repfabric / AVX CRMSync / Relabeling Fields / "Next Step" (Pending)-->
                <p:outputLabel value="#{custom.labels.get('IDS_NEXT_STEP')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
                <p:inputText value="#{projOpps.opp.oppNextStep}"
                             id="opnext" maxlength="250" style="width: 100%"/>


                <p:outputLabel value="#{custom.labels.get('IDS_OPP')} Comments"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:inputTextarea rows="3" cols="30" value="#{projOpps.oppCommText}" id="cmnt"  style="width:100% !important;resize: none" autoResize="false"/>


                <p:outputLabel value="#{custom.labels.get('IDS_VALUE')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
<!--                <p:inputText   value="#{projOpps.opp.oppValue}" id="opval"
                               size="10"  converterMessage="not a number"   

                               maxlength="14">

                    <f:convertNumber   groupingUsed="true"                                                                                      
                                       maxIntegerDigits="15"  
                                       minFractionDigits="2" maxFractionDigits="2" />
                </p:inputText>-->
                
                <p:inputNumber value="#{projOpps.opp.oppValue}" id="opval"
                               size="10"  
                               maxlength="14" >
                </p:inputNumber>


                <p:outputLabel value="#{custom.labels.get('IDS_PROTO_DATE')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:calendar  id="opproto" value="#{projOpps.opp.oppProtoDate}"
                             size="10" widgetVar="wopproto"
                             converterMessage="#{custom.labels.get('IDS_PROTO_DATE')} date not valid" 
                             pattern="#{globalParams.dateFormat}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>


                <p:outputLabel value="#{custom.labels.get('IDS_PRODUCTION_DATE')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:calendar  id="opprod" value="#{projOpps.opp.oppProdDate}"  
                             size="10" widgetVar="wopprod"
                             converterMessage="#{custom.labels.get('IDS_PRODUCTION_DATE')} not valid" 
                             pattern="#{globalParams.dateFormat}"  >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>
                <!--3091- CRM-1939: Unity: bulk update project with reporting comments - sharvani - 29-01-2020-->
                <p:outputLabel>#{custom.labels.get('IDS_RPT_COMMENTS')}</p:outputLabel>
                <p:inputTextarea rows="3" cols="30" value="#{projOpps.opp.oppRptComments}" id="inpTxtAreaRptCmnt"  style="width:100% !important;resize: none" autoResize="false"/>
            </p:panelGrid>
            <p:spacer width="4 px"/>
            <!--SONARQUBE ISSUES - sharvani - 23-01-2020-->
            <!--<center>-->
            <div align="center">
                <p:commandButton value="Update" class="btn btn-success btn-xs"  action="#{projOpps.updateProjOpps()}" update=":projOppListForm :frmProjOpps :projOppListForm:projGrowl :projSummary :frmProjOpps:DTProj" oncomplete="if(!args.validationFailed &amp;&amp; args) PF('projOppsUpdateDlg').hide()"/>
                <p:spacer width="4 px"/>
                <p:commandButton type="button" value="Cancel" styleClass="btn btn-warning btn-xs" onclick="PF('projOppsUpdateDlg').hide()"/>
            </div>
                <!--</center>-->
            <br/>
            <p:outputLabel value="Note: " style="font-weight: bold"/>
            <p:outputLabel value="Only the updated fields will be saved. #{custom.labels.get('IDS_ACTIVITY')} updates will be applied for #{custom.labels.get('IDS_OPPS')} with default #{custom.labels.get('IDS_ACTIVITY')}."/>
        </h:form>
    </p:dialog>

</ui:composition>