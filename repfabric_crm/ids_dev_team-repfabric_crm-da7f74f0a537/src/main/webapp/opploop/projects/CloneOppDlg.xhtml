<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <p:dialog widgetVar="cloneOppDlg" header="Clone #{custom.labels.get('IDS_OPP')}" modal="true" resizable="false" width="400" closeOnEscape="true"  styleClass="dialogCSS" > 
        <h:form id = "cloneOppForm">
       
            <p:remoteCommand immediate="true" name="applyPrinci" actionListener="#{projOpps.applyCompany(viewCompLookupService.selectedCompany)}" update=":cloneOppForm:inputActivePrincName :cloneOppForm:btnPart" />

<!--            <p:remoteCommand name="applyProd" 
                             actionListener="#{oppLineItems.applyProd(viewProductLookUpService.selectedProduct)}"
                             update=":cloneOppForm:partNO" />-->
             <p:remoteCommand name="applyProd" 
                              actionListener="#{projOpps.applyProd(viewProductLookUpService.selectedProduct)}"
                             update=":cloneOppForm:partNO :cloneOppForm:lqty :cloneOppForm:lres" />

            <p:panelGrid  styleClass="box-primary no-border ui-fluid" layout="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" style="min-height: 100px"   >
<!--#3142 CRM-1967: Opportunities: Victory/Golden: when clone inside project auto adds to project-->
                <p:outputLabel value="#{custom.labels.get('IDS_PRINCI')}" class="required"/>

<!--                <p:selectOneMenu id="cust" filter="true"  filterMatchMode="contains" required="true" value="#{projOpps.clonePrinciId}" style="width: 100%" requiredMessage="Please select #{custom.labels.get('IDS_PRINCI')}">   
<f:selectItem itemLabel="Select" itemValue=""/>
<f:selectItems var="princi" value="#{projOpps.listPrincipals}"  itemLabel="#{princi[1]}" itemValue="#{princi[0]}" />
</p:selectOneMenu>        -->
                <h:panelGroup class="ui-inputgroup" >
                    <p:inputText value="#{projOpps.princiName}"
                                 id="inputActivePrincName"                          
                                 placeholder="[Not selected]"
                                 readonly="true"

                                 />
                    <!--<p:spacer width="3" />-->

                    <p:commandButton id="cmdBtnPrinci" 
                                     icon="fa fa-search"                                     
                                     title="#{custom.labels.get('IDS_PRINCI')} Look Up"
                                     styleClass="btn-info btn-xs" 
                                     actionListener="#{viewCompLookupService.listPrincipals(1, 'applyPrinci')}" 
                                     oncomplete="PF('lookupComp').show()"
                                     update=":formCompLookup" 
                                     immediate="true"
                                     process="@this"
                                     />
                </h:panelGroup>
                
                <!--#3142 CRM-1967: Opportunities: Victory/Golden: when clone inside project auto adds to project-->
                <p:outputLabel value="#{custom.labels.get('IDS_MFG_PART_NUM')}" class="required"/>
                
                <h:panelGroup class="ui-inputgroup" >


                <p:inputText value="#{projOpps.lineItem.oppItemPartManf}" 
                             id="partNO"
                             maxlength="40" style="width: 96%"/>

                <p:commandButton id="btnPart"   icon="fa fa-search" title="Choose Part Number" immediate="true" 
                                 actionListener="#{viewProductLookUpService.list('applyProd',projOpps.clonePrinciId)}" 
                                 update=":dtPartNumForm" oncomplete="PF('dlgPartNum').show()"  
                                 styleClass="btn-info btn-xs" 
                                 disabled="#{projOpps.clonePrinciId==0}"/>
                </h:panelGroup>

                <p:outputLabel value="#{custom.labels.get('IDS_LINE_ITEM_QTY')}" />

                <p:inputText value="#{projOpps.lineItem.oppItemQnty}" id="lqty" maxlength="12"   
                             converterMessage="#{custom.labels.get('IDS_LINE_ITEM_QTY')} not valid"  >
                    <f:convertNumber groupingUsed="false"   minFractionDigits="2" />
                </p:inputText>

                <p:outputLabel value="#{custom.labels.get('IDS_RESALE')}"/>

                <p:inputText value="#{projOpps.lineItem.oppItemResale}" id="lres" maxlength="12"
                             converterMessage="#{custom.labels.get('IDS_RESALE')} not valid" >
                    <f:convertNumber groupingUsed="false" maxIntegerDigits="15"  minFractionDigits="2" maxFractionDigits="4" />
                </p:inputText>

            </p:panelGrid>

            <p:spacer width="4px" />
            <center>
                <p:commandButton value="Clone" class="btn btn-primary btn-xs" action="#{projOpps.cloneOpp()}" update=":projOppListForm :projOppListForm:projGrowl :frmProjOpps :projSummary" />
                <p:spacer width="4px" />
                <p:commandButton type="button" value="Cancel" styleClass="btn btn-warning btn-xs" onclick="PF('cloneOppDlg').hide()"/>
            </center><br/>
        </h:form>
    </p:dialog>

</ui:composition>