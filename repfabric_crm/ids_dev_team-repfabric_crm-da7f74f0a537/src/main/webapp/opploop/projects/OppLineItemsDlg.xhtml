<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    
   <p:dialog styleClass="disable-scroll" widgetVar="OppLineItemsDlg" header=" #{custom.labels.get('IDS_LINE_ITEMS')}" width="600px" height="320px" modal="true" resizable="false"> 
        <h:form id = "frmProjOppLineItem">
            <p:dataTable value="#{projOpps.oppLineItemsList}"  rowKey="#{line.recId}"
                         selectionMode="single" selection="#{projOpps.lineItem}"
                         emptyMessage="No #{custom.labels.get('IDS_LINE_ITEM')} found"
                         var="line" >
                <p:ajax event="rowSelect" oncomplete="PF('editLineItemDlg').show()" update=":frmEditLineItem"/>
                <p:column headerText="#{custom.labels.get('IDS_LINE_ITEM_DESC')}">
                    #{line.oppItemPartDesc}
                </p:column>

                <p:column headerText="#{custom.labels.get('IDS_MFG_PART_NUM')}">
                    #{line.oppItemPartManf}
                </p:column>

                <p:column headerText="#{custom.labels.get('IDS_CUST_PART')}">
                    #{line.oppItemPartCust}
                </p:column>

                <p:column headerText="Qty Per Unit" style="text-align: right">          
                    <p:outputLabel value="#{line.oppItemQnty}">
                        <f:convertNumber minFractionDigits="2" groupingUsed="false" maxIntegerDigits="15"/>
                    </p:outputLabel>
                </p:column>

                <p:column headerText="#{custom.labels.get('IDS_COST')}" style="text-align: right">
                    <p:outputLabel value="#{line.oppItemCost}">
                        <f:convertNumber minFractionDigits="2" maxFractionDigits="4" groupingUsed="false" maxIntegerDigits="15"/>
                    </p:outputLabel>
                </p:column>

                <p:column  headerText="#{custom.labels.get('IDS_RESALE')}" style="text-align: right">           
                    <p:outputLabel value="#{line.oppItemResale}">
                        <f:convertNumber minFractionDigits="2" maxFractionDigits="4" groupingUsed="false" maxIntegerDigits="15"/>
                    </p:outputLabel>
                </p:column>

            </p:dataTable> 
        </h:form>
    </p:dialog>
    
</ui:composition>