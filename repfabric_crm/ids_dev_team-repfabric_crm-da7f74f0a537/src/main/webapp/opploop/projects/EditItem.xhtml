<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <p:dialog widgetVar="editLineItemDlg"   modal="true" resizable="false"   width="500px"
              header="Edit #{custom.labels.get('IDS_LINE_ITEM')}" >
        <h:form id = "frmEditLineItem">
            <p:panelGrid styleClass="box-primary no-border ui-fluid" layout="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" style="min-height: 100px"   >
                <p:outputLabel value="#{custom.labels.get('IDS_MFG_PART_NUM')}"/>
                <!--                <p:outputLabel styleClass="m" value=":" />               -->
                <p:inputText value="#{projOpps.lineItem.oppItemPartManf}"  maxlength="40" />

                <p:outputLabel value="#{custom.labels.get('IDS_CUST_PART')}"/>
                <!--                <p:outputLabel styleClass="m" value=":" />               -->
                <p:inputText value="#{projOpps.lineItem.oppItemPartCust}"  maxlength="40" />

                <p:outputLabel value="Qty Per Unit"/>
                <!--                <p:outputLabel styleClass="m" value=":" />               -->
                <p:inputText value="#{projOpps.lineItem.oppItemQnty}" id="lqty" maxlength="12"   
                             converterMessage="Qty not valid"  >
                    <f:convertNumber groupingUsed="false"   minFractionDigits="2" />
                </p:inputText>

                <p:outputLabel value="#{custom.labels.get('IDS_COST')}"/>
                <!--                <p:outputLabel styleClass="m" value=":" />               -->
                <p:inputText value="#{projOpps.lineItem.oppItemCost}" id="lcost" maxlength="12"
                             converterMessage="Item cost not valid" >
                    <f:convertNumber groupingUsed="false" maxIntegerDigits="15"  minFractionDigits="2" maxFractionDigits="4"/>
                </p:inputText>

                <p:outputLabel value="#{custom.labels.get('IDS_RESALE')}"/>
                <!--                <p:outputLabel styleClass="m" value=":" />               -->
                <p:inputText value="#{projOpps.lineItem.oppItemResale}" id="lres" maxlength="12"
                             converterMessage="#{custom.labels.get('IDS_RESALE')} not valid" >
                    <f:convertNumber groupingUsed="false" maxIntegerDigits="15"  minFractionDigits="2"  maxFractionDigits="4"/>
                </p:inputText>

                <p:outputLabel value="#{custom.labels.get('IDS_LINE_ITEM_DESC')}"/>
                <!--                <p:outputLabel styleClass="m" value=":" />               -->
                <p:inputTextarea id="ldes" value="#{projOpps.lineItem.oppItemPartDesc}" 
                                 rows="2" maxlength="60" style="width: 100%" />
            </p:panelGrid>
            <p:spacer width="4px" />
            <center>
                <p:commandButton    value="Update"  
                                    id="lbtupd" 
                                    oncomplete="if (args &amp;&amp; !args.validationFailed) PF('editLineItemDlg').hide()" 
                                    class="btn btn-success btn-xs" 
                                    actionListener="#{projOpps.updateLineItem()}"
                                    update=":frmProjOppLineItem :frmProjOpps :projOppListForm:projGrowl :projSummary"/>
                <p:spacer width="4px" />
                <p:commandButton type="button" 
                                 class="btn btn-warning btn-xs"
                                 onclick="PF('editLineItemDlg').hide()" value="Cancel"/>
            </center><br/>
        </h:form>
    </p:dialog>

</ui:composition>