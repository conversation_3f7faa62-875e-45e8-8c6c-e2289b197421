<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
        
    <p:dialog widgetVar="createProjDlg" header="Create #{custom.labels.get('IDS_OPP_PROJ')}" modal="true" resizable="false" width="400" styleClass="dialogCSS"> 
        <h:form id = "addReltdCompForm">
            <!--<p:remoteCommand name="applyCRMCust" actionListener="#{example.applyCompany(viewCompLookupService.selectedCompany)}" update=":sampleForm:inputCustName" />-->
            <p:remoteCommand name="applyCRMCust" immediate="true" actionListener="#{projects.applyCompany(viewCompLookupService.selectedCompany)}" update=":addReltdCompForm:inputCustName" />
            
            <p:panelGrid styleClass="box-primary no-border ui-fluid" layout="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" style="min-height: 100px"   >
<!--                <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}" />
                            
                <p:selectOneMenu id="cust" filter="true" filterMatchMode="contains"  height="350" widgetVar="cust3" required="true" value="#" style="width: 100%" requiredMessage="Please select #{custom.labels.get('IDS_CUSTOMER')}">   
                    <f:selectItem itemLabel="Select" itemValue=""/>
                    <f:selectItems var="cust" value="#{projects.listCustomers}"  itemLabel="#{cust[1]}" itemValue="#{cust[0]}" />
                </p:selectOneMenu>        -->

                <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}" styleClass="required" />
                <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText value="#{projects.projCustName}"
                                   id="inputCustName"                          
                                   
                                   required="true"
                                   requiredMessage="Please select #{custom.labels.get('IDS_CUSTOMER')}"
                                   readonly="true"
                                 />  
                  
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{viewCompLookupService.listCRM(2, 'applyCRMCust')}" oncomplete="PF('lookupComp').show()"
                                      update=":formCompLookup" 
                                      styleClass="btn-info btn-xs" />
                </h:panelGroup>
                
                <p:outputLabel value="#{custom.labels.get('IDS_PROGRAM')}" styleClass="required" />
                
                <p:inputText value="#{projects.projProgram}" 
                             id="opcustprgm" 
                             maxlength="120" style="width: 96%"/>

                <p:outputLabel value="#{custom.labels.get('IDS_VALUE')}" />
                
                <p:inputText   value="#{projects.projValue}" id="opval"
                               converterMessage="not a number"   
                               maxlength="14" style="width: 96%">

                    <f:convertNumber   groupingUsed="true"                                                                                      
                                       maxIntegerDigits="15"  
                                       minFractionDigits="2" maxFractionDigits="2" />
                </p:inputText>

                <p:outputLabel value="Start Date"/>
               
                <p:calendar  pattern="#{globalParams.dateFormat}"
                             size="10"  
                             converterMessage="Start date not valid" 
                             required="true" 
                             requiredMessage="Please select start date"
                             value="#{projects.projStartDate}"  >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:calendar>


            </p:panelGrid>
            <p:spacer width="4px" />
            <center>
                <!--Bug 12 - Projects: Value should not exceed 99 billion - handle dialog close from code-->
                <p:commandButton value="Save"  action="#{projects.createProject()}" update=":projListForm :projListForm:projGrowl" styleClass="btn btn-success  btn-xs"/>
                <p:spacer width="4px" />
                <p:commandButton type="button" value="Cancel" onclick="PF('createProjDlg').hide()" styleClass="btn btn-warning  btn-xs"/>
            </center><br/>
        </h:form>
    </p:dialog>
</ui:composition>
