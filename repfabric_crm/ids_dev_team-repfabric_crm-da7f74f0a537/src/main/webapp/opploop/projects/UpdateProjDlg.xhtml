<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
        
    <p:dialog widgetVar="updateProjDlg" header="Update #{custom.labels.get('IDS_OPP_PROJ')}" modal="true" resizable="false" width="400"> 
        <h:form id = "updateReltdCompForm">
            <p:remoteCommand name="applyCRMCust" immediate="true" actionListener="#{projects.applyCompany(viewCompLookupService.selectedCompany)}" update=":updateReltdCompForm:inputCustName" />
            <p:panelGrid styleClass="box-primary no-border ui-fluid" layout="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" style="min-height: 100px"  >
                <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}" />
<!--                <p:outputLabel styleClass="m" value=":" />               -->
<!--                <p:selectOneMenu id="cust" filter="true" filterMatchMode="contains"  height="350" widgetVar="cust3" required="true" value="" style="width: 100%" requiredMessage="Please select #{custom.labels.get('IDS_CUSTOMER')}" disabled="true">   
                    <f:selectItem itemLabel="" itemValue=""/>
                    
                </p:selectOneMenu>        -->
 <h:panelGroup class="ui-inputgroup"  >
                    <p:inputText value="#{projects.projCustName}"
                                   id="inputCustName"                          
                                   
                                   required="true"
                                   requiredMessage="Please select #{custom.labels.get('IDS_CUSTOMER')}"
                                   readonly="true"
                                   disabled="true"
                                 />  
                  
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      actionListener="#{viewCompLookupService.listCRM(2, 'applyCRMCust')}" oncomplete="PF('lookupComp').show()"
                                      update=":formCompLookup" 
                                      styleClass="btn-info btn-xs"
                                      disabled="true"/>
                </h:panelGroup>

                <p:outputLabel value="#{custom.labels.get('IDS_PROGRAM')}" />
<!--                <p:outputLabel styleClass="m" value=":" />-->
<p:inputText value="#{projects.projProgram}" 
                             id="opcustprgm" required="true" requiredMessage="Please enter #{custom.labels.get('IDS_PROGRAM')}"
                             maxlength="120" style="width: 96%"/>

                <p:outputLabel value="#{custom.labels.get('IDS_VALUE')}" />
<!--                <p:outputLabel styleClass="m" value=":" />-->
<p:inputText   value="#{projects.projValue}" id="opval"
                               converterMessage="not a number"   
                               maxlength="14" style="width: 96%" disabled="true">

                    <f:convertNumber   groupingUsed="true"                                                                                      
                                       maxIntegerDigits="15"  
                                       minFractionDigits="2" maxFractionDigits="2" />
                </p:inputText>

                <p:outputLabel value="Start Date"/>
<!--                <p:outputLabel styleClass="m" value=":" />-->
                <p:calendar  pattern="#{globalParams.dateFormat}"
                             size="10"  
                             converterMessage="Start date not valid" 
                             required="true" 
                             requiredMessage="Please select start date"
                             value="#{projects.projStartDate}" disabled="true" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:calendar>


            </p:panelGrid>
            <p:spacer width="4px" />
            <center>
                <!--Bug 12 - Projects: Value should not exceed 99 billion - handle dialog close from code-->
                <p:commandButton value="Save" action="#{projects.updateProject()}" update=":projListForm :projListForm:projGrowl" styleClass="btn btn-success  btn-xs" />
                <p:spacer width="4px" />
                <p:commandButton type="button" value="Cancel" onclick="PF('updateProjDlg').hide()" styleClass="btn btn-warning  btn-xs"/>
            </center><br/>
        </h:form>
    </p:dialog>
</ui:composition>
