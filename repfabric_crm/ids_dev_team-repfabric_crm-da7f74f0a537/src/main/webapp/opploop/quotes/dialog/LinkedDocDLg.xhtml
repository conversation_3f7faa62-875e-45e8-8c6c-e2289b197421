<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"> 
    <h:form id="linkedDocumentForm">
        <p:dialog id="linkedDocumentDialogue"  widgetVar="linkedDocumentDlgVar" width="950" class="dialogCSS" resizable="false" 
                  showEffect="clip" hideEffect="clip" modal="true" closable="true">
            <!--<p:ajax event="close" oncomplete="PF('linkedDocumentVar').hide()"/>-->
            <f:facet name="header">
                <h:outputText style="width:90%;" value="#{linkedDocService.header}" />
            </f:facet>
            <!--//27-09-2023 : #12180 : CRM-7324 Include cloned/parent quotes in the quote linked docs tab-->
            <p:dataTable id="linkedDocumentsDataTable" value="#{linkedDocService.oppLinkedDocLst}" 
                         var="linkedDocVar"
                         rows="10" selection="#{linkedDocService.selectdQuotLnkdDoc}" 
                         selectionMode="single" 
                         filterEvent="keyup" 
                         draggableColumns="true"
                         emptyMessage="No linked documents found."  
                         paginator="true" 
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         paginatorAlwaysVisible="false" 
                         class="tblmain" rowIndexVar="linkedDocRowIndex"
                         rowKey="#{linkedDocVar.recId}" resizableColumns="true"
                         >
                <p:column headerText="Type" filterBy="#{linkedDocVar.docType}" filterMatchMode="contains" sortBy="#{linkedDocVar.docType}">
                    <p:commandLink rendered="#{linkedDocVar.docType eq 'Opportunity'}"  value="#{linkedDocVar.docType}" id="cmndLnkOpp"
                                   onclick="window.open('../../opploop/opportunity/OpportunityView.xhtml?opid=#{linkedDocVar.docId}', '_blank');"
                                   ></p:commandLink>
                    <p:commandLink rendered="#{linkedDocVar.docType eq 'Quote'}"  value="#{linkedDocVar.docType}" id="cmndLnkQuotes"
                                   onclick="window.open('../../opploop/quotes/NewQuote.xhtml?recId=#{linkedDocVar.docId}', '_blank');"

                                   >
                    </p:commandLink>

                    <p:commandLink rendered="#{linkedDocVar.docType eq 'Job'}"  value="#{linkedDocVar.docType}"
                                   onclick="window.open('../../opploop/jobs/JobsView.xhtml?id=#{linkedDocVar.docId}', '_blank');"
                                   >
                    </p:commandLink>
                    <p:commandLink rendered="#{linkedDocVar.docType eq 'Project'}"  value="#{linkedDocVar.docType}"
                                   onclick="window.open('../../opploop/projects/ProjectView.xhtml?id=#{linkedDocVar.docId}', '_blank');"
                                   >
                    </p:commandLink>
                    <p:commandLink rendered="#{linkedDocVar.docType eq custom.labels.get('IDS_PURCHASE_ORDERS')}"  value="#{linkedDocVar.docType}"
                                   onclick="window.open('../../opploop/po/PoDetails.xhtml?id=#{linkedDocVar.docId}', '_blank');"
                                   >
                    </p:commandLink>
                    <!--//20-11-2023 : #12558 : CRM-7205  Linked documents: inconsistencies all over the place-->
                    <p:commandLink rendered="#{linkedDocVar.docType eq 'Invoice'}"  value="#{linkedDocVar.docType}"
                                   onclick="window.open('../../datamanagement/commtransactions/CommtransDetails.xhtml?id=#{linkedDocVar.docId}', '_blank');"

                                   >
                    </p:commandLink>
                    <p:commandLink rendered="#{linkedDocVar.docType eq 'Sample'}"  value="#{linkedDocVar.docType}"
                                   onclick="window.open('../../opploop/samples/Sample.xhtml?recId=#{linkedDocVar.docId}', '_blank');"

                                   >
                    </p:commandLink>

                </p:column>
                <p:column headerText="Doc. No." filterBy="#{linkedDocVar.docNo}" filterMatchMode="contains" sortBy="#{linkedDocVar.docNo}">
                    <h:outputText value="#{linkedDocVar.docNo}" />
                </p:column>
                <p:column headerText="Doc. Date" filterBy="#{oppLinkedDocumentsService.getFormattedDate(linkedDocVar.docDate, 'da')}" filterMatchMode="contains" sortBy="#{linkedDocVar.docDate}">
                    <h:outputText value="#{oppLinkedDocumentsService.getFormattedDate(linkedDocVar.docDate, 'da')}" />
                </p:column>
                <p:column headerText="Topic" filterBy="#{linkedDocVar.docTopic}" filterMatchMode="contains" sortBy="#{linkedDocVar.docTopic}">
                    <h:outputText value="#{linkedDocVar.docTopic}" />
                </p:column>
                <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{linkedDocVar.docCustomer}" filterMatchMode="contains" sortBy="#{linkedDocVar.docCustomer}">
                    <h:outputText value="#{linkedDocVar.docCustomer}" />
                </p:column>
                <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{linkedDocVar.docPrinci}" filterMatchMode="contains" sortBy="#{linkedDocVar.docPrinci}">
                    <h:outputText value="#{linkedDocVar.docPrinci}" />
                </p:column>
                <p:column headerText="Value" filterBy="#{linkedDocVar.docValue}" filterMatchMode="contains" sortBy="#{linkedDocVar.docValue}">
                    <h:outputText value="#{linkedDocVar.docValue}" style="float: right" />
                </p:column>
            </p:dataTable>
        </p:dialog>
    </h:form>
    
</ui:composition>
