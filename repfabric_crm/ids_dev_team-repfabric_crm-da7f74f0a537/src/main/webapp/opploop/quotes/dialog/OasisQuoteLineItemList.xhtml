<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">


    <p:dialog  styleClass="dialogCSS" id="isQuotLnItmListDtlsx1" widgetVar="isQuotLnItmListDtls1"  draggable="false" 
               resizable="false" style="text-overflow: scroll;"  height="80%" width="80%" modal="true" >
        <f:facet name="header">
            Resolve Company names
            <!--<h:outputText id="isQuotLnItmListTxt1" value="Resolve Company names" />-->
        </f:facet>   
        <p:ajax event="close" update=":quotefrom:tabQuot" listener="#{quotesHdr.clearFields()}"/>
        <h:form id="isQfrm22">
            <p:remoteCommand autoRun="true" update=":isQfrm22:isql1"/> 
            
            <p:remoteCommand name="applyActivePrinciResolve1" autoRun="false" immediate="true" update=":isQfrm22:isql1"
                             actionListener="#{quotesHdr.updateFieldsForOasisResolve(viewCompLookupService.selectedCompany)}"/>
             <!--13375  CRM-7815   OASIS: Assign the quotes to the correct user when ingesting from OASIS-->
            <p:remoteCommand name="applyOwner" actionListener="#{quotesHdr.applyUser(userLookupService.user)}"  update=":isQfrm22:isql1"/>
            <!--4872 CRM Sync: Settings for Oasis-->
            <p:commandButton  value="Apply" title="Apply" class="btn btn-primary btn-xs" process="@this" disabled="#{quotesHdr.compResolveList.size()==0}"
                              immediate="true" action="#{quotesHdr.saveResolvedOasisData()}" widgetVar="applyBtn"
                              update=":quotefrom:tabQuot:tblQuotDtls" onclick="PF('applyBtn').disable();" />
            <p:dataTable value="#{quotesHdr.compResolveList}" 
                         var="errRec" id="isql1" sortBy="#{errRec.recId}" sortOrder="ascending"
                         emptyMessage="No Companies available" 
                         scrollable="true" 
                         scrollHeight="350"   
                         widgetVar="qlWVar">
                 
                 <p:column  width="10%" rendered="false" >
                    <f:facet name="header">Company ID</f:facet>
                    <h:outputText value="#{errRec.recId}" />
                </p:column>      
               
                
                <p:column  width="10%" rendered="false" >
                    <f:facet name="header">Company ID</f:facet>
                    <h:outputText value="#{errRec.companyTypeId}" />
                </p:column>
                <p:column  width="10%">
                    <f:facet name="header">Company Type</f:facet>
                    <h:outputText value="#{errRec.companyTypeName}"/>
                    <!--#{errRec.companyTypeId}-->
                </p:column>
                <!--//                                    12768   ESCALATIONS CRM-7634  Synergy Electrical Sales: Specifier Field-->
                <p:column  width="10%">
                     <f:facet name="header">Company Sub Type</f:facet>
                    <h:outputText value="#{errRec.companySubTypeName}"/>
                </p:column>
                <p:column width="10%">
                    <f:facet name="header">Oasis Company</f:facet>
                    <h:outputText value="#{errRec.quotISCustomerName}" rendered="#{errRec.companyTypeId==1}"/>
                    <h:outputText value="#{errRec.quotISPrincipalName}" rendered="#{errRec.companyTypeId==2}"/>
                    <h:outputText value="#{errRec.quotISDistrbutorName}" rendered="#{errRec.companyTypeId==3}"/>
                    <!--//                                   12332  ESCALATION CRM-7457 OASIS: Add specifiers on the job header/basics tab--> 
                     <h:outputText value="#{errRec.quotISDistrbutorName}" rendered="#{errRec.companyTypeId==12}"/>
                    <!--#{errRec.quotISCustomerName} #{errRec.quotISPrincipalName} #{errRec.quotISDistrbutorName}--> 
                    <!--13375  CRM-7815   OASIS: Assign the quotes to the correct user when ingesting from OASIS-->
                    <h:outputText value="#{errRec.oasisQuoter}" rendered="#{errRec.companyTypeId==16}"/>
                </p:column>
                <p:column  width="10%"  rendered="false">
                    <f:facet name="header">City</f:facet>
                    <h:outputText value="#{errRec.quotISCity}"  rendered="true"/></p:column>
                <p:column  width="10%"   rendered="false" >
                    <f:facet name="header">State</f:facet>
                    <h:outputText value="#{errRec.quotISState}"  rendered="true"/>                      
                </p:column>
                <p:column  width="10%"  rendered="false">
                    <f:facet name="header">Zip</f:facet>
                    <h:outputText value="#{errRec.quotISZip}"  rendered="true"/> 
                </p:column>



                <p:column width="10%">
                    <f:facet name="header">RF Company</f:facet>
                    <h:outputText value="#{errRec.quotCustomerName}" rendered="#{errRec.companyTypeId==1}"/>
                    <h:outputText value="#{errRec.quotPrincipalName}" rendered="#{errRec.companyTypeId==2}"/>
                    <h:outputText value="#{errRec.quotDistrbutorName}" rendered="#{errRec.companyTypeId==3}"/>
                    <!--//                                   12332  ESCALATION CRM-7457 OASIS: Add specifiers on the job header/basics tab--> 
                    <h:outputText value="#{errRec.quotDistrbutorName}" rendered="#{errRec.companyTypeId==12}"/>
                     <!--13375  CRM-7815   OASIS: Assign the quotes to the correct user when ingesting from OASIS-->
                    <h:outputText value="#{errRec.quotOasisQuoter}" rendered="#{errRec.companyTypeId==16}"/>
                </p:column>
<!--                <p:column width="10%" rendered="false"  >
                    <f:facet name="header">Hidden Ids</f:facet>aaa-->
<!--                    <h:outputText value="#{errRec.quotPrincipalId}"/>-
                    <h:outputText value="#{errRec.quotCustomerId}"/>-
                    <h:outputText value="#{errRec.quotDistrbutorId}"/>
                    
                    bbb<h:outputText value="#{errRec.quotISPrincipalId}"/>-
                    <h:outputText value="#{errRec.quotISCustomerId}"/>-
                    <h:outputText value="#{errRec.quotISDistrbutorId}"/>
                    
                    ccc<h:outputText value="#{errRec.quotISPrincipalIdStr}"/>-
                    <h:outputText value="#{errRec.quotISCustomerIdStr}"/>-
                    <h:outputText value="#{errRec.quotISDistrbutorIdStr}"/>-->
<!--                    <h:outputText rendered="#{errRec.companyTypeId==16}" value="#{errRec.quotOasisQuoterId}"/> 
                </p:column>-->
                <p:column width="10%">
                    <f:facet name="header"></f:facet>
                    <!--13107  CRM-7735   OASIS: Pre-fill the new company screen with information from OASIS during aliasing-->
                    <p:commandButton id="btnCompCustSearch1" icon="fa fa-search"  title="Choose Company"  rendered="#{errRec.companyTypeId==1}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForOasisResolve('applyActivePrinciResolve1', 5,errRec.recId,errRec.quotISPrincipalName)}"
                                     update=":formCompLookup :frmLookupHead" immediate="true" process="@this"  oncomplete="PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />
                    <p:commandButton id="btnCompPrinciSearch1" icon="fa fa-search" title="Choose Company" rendered="#{errRec.companyTypeId==2}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForOasisResolve('applyActivePrinciResolve1', 1,errRec.recId,errRec.quotISPrincipalName)}"
                                     update=":formCompLookup :frmLookupHead" immediate="true" process="@this"  oncomplete="PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />
<!--                    <p:commandButton id="btnCompDistriSearch1" icon="fa fa-search" title="Choose Company" rendered="#{errRec.companyTypeId==3}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForResolve('applyActivePrinciResolve1', 3,errRec.recId)}"
                                     update=":formCompLookup" immediate="true" process="@this" 
                                     styleClass="btn-info btn-xs" />-->
                    <p:commandButton id="btnCompDistriSearch1" icon="fa fa-search" title="Choose Company" rendered="#{errRec.companyTypeId==3}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForOasisResolve('applyActivePrinciResolve1', 9,errRec.recId,errRec.quotISDistrbutorName)}"
                                     update=":formCompLookup :frmLookupHead" immediate="true" process="@this"  oncomplete="PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />
                    <!--//                                   12332  ESCALATION CRM-7457 OASIS: Add specifiers on the job header/basics tab--> 
                    <!--//   11768 CRM-7634-->
                    <p:commandButton id="btnCompSpecifierSearch1" icon="fa fa-search" title="Choose Company" rendered="#{errRec.companyTypeId==12}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForOasisResolve('applyActivePrinciResolve1', 12,errRec.recId,errRec.quotISDistrbutorName)}"
                                     update=":formCompLookup :frmLookupHead" immediate="true" process="@this" oncomplete="PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />
                     <!--13375  CRM-7815   OASIS: Assign the quotes to the correct user when ingesting from OASIS-->
                    <p:commandButton id="btnOwnerSearch" icon="fa fa-search" title="Choose User" immediate="true"  rendered="#{errRec.companyTypeId==16}"
                                     actionListener="#{userLookupService.listOasisOwner2('applyOwner',errRec.recId)}" 
                                     update=":formUserLookup" oncomplete="PF('lookupUser').show();"   
                                     styleClass="btn-info btn-xs" />
                    
                    <!--13107  CRM-7735   OASIS: Pre-fill the new company screen with information from OASIS during aliasing-->
                </p:column> 
            </p:dataTable>
        </h:form>
    </p:dialog>
</ui:composition>



