<?xml version="1.0" encoding="UTF-8"?>
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">
<!--28/02/2022 7316 MoreCRM>Quotes > Detailed tab bugs-->
    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier--> 
    <p:dialog  styleClass="dialogCSS" id="dlgQuoteDtls" widgetVar="dlgQuoteDtls"  responsive="true" 
               modal="true"  style="text-overflow: scroll"  >
        <!--//          #12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
        <p:growl widgetVar="addDialogGrowl1" />
         <!--7930 09/05/2022 Quotes: UOM and UOM units doesn't change/Quote Selection issues-->
        <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
         <p:remoteCommand name="applyProd" actionListener="#{quotesDtl.applyProd(viewProductLookUpService.selectedProduct)}"
                        update="quotLstForm:oppItemPartCust quotLstForm:oppItemPartManf quotLstForm:oppItemCost 
                         quotLstForm:oppItemQnty quotLstForm:unit quotLstForm:extPrice quotLstForm:weight 
                         quotLstForm:quotMultiplier quotLstForm:prdLne  quotLstForm:ledTime quotLstForm:oppEau  quotLstForm:quotUom quotLstForm:quotUnit quotLstForm:quotTargetCost    quotLstForm:oppItemPartDesc
                         quotLstForm:quotStdRate quotLstForm:specialPrice quotLstForm:imageURLInput quotLstForm:specURLInput quotLstForm:opnLink quotLstForm:opnLink1"/>
        <f:facet name="header">
            <h:outputText id="hdrText" value="#{custom.labels.get('IDS_QUOTES')} Details #{quotesDtl.cloned ? '(Cloned)' : ''}" />
        </f:facet>
        <p:panelGrid id="gridQuoteDtls" styleClass="panlGrid">

            <div class="ui-g ui-fluid" id="btns1">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--#3551 CRM-2135: #5: Quotes > Line Items > Delete All-->
                    <!--oncomplete="PF('processDlg').hide()"onstart="PF('processDlg').show()"-->    
                    <!--#302 Quotes > Move Line Items to a new tab-->
                    <!--3269 Quotes > Linked Docs-->
                                      <!--12/03/2022 : 7316 : MoreCRM>Quotes > Detailed tab bugs                    Task#3918-CRM-4017: quote2opp conversion - cherry pick which lines to generate an opp for from a quote  22-03-2021 by harshithad-->
                    <p:outputPanel id="gridQuoteDtl1">
                        <p:commandButton value="#{quotesDtl.recId > 0 ? 'Update' : 'Save'}" actionListener="#{quotesDtl.saveOrUpdateDetails(quotesDtl)}"                              
                                     update=":quotLstForm:tblQdtl"  
                                     class="btn btn-xs btn-success" ></p:commandButton>
                    </p:outputPanel>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:commandButton value="Cancel"    onclick="PF('dlgQuoteDtls').hide()" 
                                     type="button" class="btn btn-xs btn-warning"  />
                    <!--                    <p:spacer width="10"/>
                                        <p:commandButton value="Delete" onclick="PF('confirmation1').show()"   
                                                         update="" rendered="#{quotesDtl.recId > 0}" class="btn btn-xs btn-danger" >
                                            <p:confirm header="Confirmation" message="Are you sure to delete?" icon="ui-icon-alert" />
                                        </p:commandButton>-->
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier--> 
                    <p:outputPanel id="gridQuoteDtl2">
                    <p:commandButton value="Delete" onclick="PF('confirmationQt').show()"      
                                     update="quotLstForm:tblQdtl" rendered="#{quotesDtl.recId > 0}" class="btn btn-xs btn-danger" >
                        <!--<p:confirm header="Confirmation1" message="Are you sure to delete?" icon="ui-icon-alert" />-->
                    </p:commandButton>
                        </p:outputPanel>

                </div>
                                        <!--12/03/2022 : 7316 : MoreCRM>Quotes > Detailed tab bugs                    Task#3918-CRM-4017: quote2opp conversion - cherry pick which lines to generate an opp for from a quote  22-03-2021 by harshithad-->
                                        <!--7931 :  10/05/2022 : #7316 MoreCRM>Quotes > Detailed tab bugs-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier--> 
                    <p:outputPanel id="gridQuoteDtl">
                        <p:commandButton class="btn btn-xs btn-primary" process="@this" immediate="true"  
                                         value="Clone" actionListener="#{quotesDtl.cloneQuoteDtl(quotesDtl)}" rendered="#{quotesDtl.recId > 0}"
                                         update=":quotLstForm:gridQuoteDtl1 :quotLstForm:gridQuoteDtl2 :quotLstForm:gridQuoteDtl" >

                            </p:commandButton>
                    </p:outputPanel>
                </div>
                <!--                <p:column>
                                    <h:outputText value="created by "
                                </p:column>-->
            </div>
            <!--            Feature #4996:Customer Pricing  > Quote Get Best Price 26/06/21 by harshithad-->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-12 ui-lg-12" >
                    <p:outputPanel id="specialPrice">
                        <p:outputLabel id="otptLblMessge" rendered="#{quotesDtl.quotCustPriceFlag==1}" value="** #{custom.labels.get('IDS_CUSTOMER')} Special Price applied"/>
                    </p:outputPanel>
                </div>
            </div>
            <!--   _______________start opportunities______________________  -->
            <!--#336 Quotes > SuperQuote updates-->
            <div class="ui-g ui-fluid" >
                <!--06.05.2022 : 7316: MoreCRM>Quotes > Detailed tab bugs-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2" style="#{quotesDtl.quotPrincipal!=0 ? '' : 'display:none'}">
                    <p:outputLabel value="#{custom.labels.get('IDS_PRINCI')}" for="oppLnItmPrincipal"  styleClass="required"/>
                </div>
                <!--06.05.2022 : 7316: MoreCRM>Quotes > Detailed tab bugs-->
                <div class="ui-sm-12 ui-md-4 ui-lg-4" style="#{quotesDtl.quotPrincipal!=0 ? '' : 'display:none'}">
                    <h:panelGroup class="ui-inputgroup"  >
                        <p:inputText id="oppLnItmPrincipal" value="#{quotesDtl.quotPrincipalName}" readonly="true"  required="true" requiredMessage="#{custom.labels.get('IDS_PRINCI')} is required"/>
                        <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true"  disabled="true"
                                          actionListener="#{quotesHdr.listLookUpvalues('applyActivePrinciForSuperQuote', 1)}"
                                          update=":formCompLookup :quotLstForm:btnPart" tabindex="2"  id="btnSrchCompLkp1"
                                          styleClass="btn-info btn-xs" />
                    </h:panelGroup>
                </div>
                <!--                <p:column>
                                    <p:outputLabel id="princiLnItmContactLbl" value="#{custom.labels.get('IDS_PRINCI')} Contact" for="oppLnItmPrincipalContact" />
                                </p:column><p:column>
                                    <h:panelGroup class="ui-inputgroup" rendered="#{quotesHdr.isSuperQuote==false}"  id="princiLnItmContactInpt" >
                                        <p:inputText id="oppLnItmPrincipalContact" readonly="true"  value="#{quotesHdr.oppLnItmPrincipalContName}"  />
                                        <p:commandButton id="btnOppLnItmPrinciContSearch" icon="fa fa-search" title="Choose Contact" immediate="true"  
                                                         actionListener="#{viewContLookupService.list('applyCont5',1,quotesHdr.oppLnItmPrincipal,1)}" 
                                                         update=":formContLookup" oncomplete="PF('lookupCont').show()"  
                                                         styleClass="btn-info btn-xs" />
                                    </h:panelGroup>
                                </p:column>-->
               
            </div>

            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Manuf. Part No"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <h:panelGroup class="ui-inputgroup"  >  
                        <p:inputText id="oppItemPartManf" value="#{quotesDtl.oppItemPartManf}" styleClass="partNumBox" maxlength="60"  style="width: 90%" >
    <!--                        <p:ajax event="change" listener="#{lookupSelection.fetchPartNumDescription(quotesDtl.oppItemPartManf, 'QUOTE')}"
                                    update="@(.partNumBox)"/>-->
                            <!--                          Feature #4996:Customer Pricing  > Quote Get Best Price 26/06/21 by harshithad-->
                            <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                            <!--//29-07-2024 : #14134 : CRM-8215   Quotes: Need "add to product master" option on detailed view-->
                            <p:ajax event="change" listener="#{quotesDtl.changePartNumberUpdate(quotesDtl)}" 
                                    update="quotLstForm:oppItemPartCust quotLstForm:oppItemPartManf quotLstForm:oppItemCost 
                                        quotLstForm:oppItemQnty quotLstForm:unit quotLstForm:extPrice quotLstForm:weight 
                                        quotLstForm:quotMultiplier quotLstForm:prdLne  quotLstForm:ledTime quotLstForm:oppEau  quotLstForm:quotUom quotLstForm:quotUnit quotLstForm:quotTargetCost    quotLstForm:oppItemPartDesc
                                        quotLstForm:quotStdRate quotLstForm:specialPrice"/>
                        </p:inputText>
                        <!--                    <p:commandButton id="btnPart"  
                                                             class="btnlukup"
                                                             style="height: 27px;width:32px;"
                                                             icon="ui-icon-search" 
                                                             title="#{custom.labels.get('IDS_PART_NUM')} Look Up"
                                                             actionListener="#{lookupSelection.loadMenuOption('QUOTE')}"
                                                             action="#{productsMst.loadProducts()}" 
                                                             update=" " 
                                                             oncomplete="PF('dlgPartNum').show();"
                                                             />-->
                        <!--01/03/2022 7298 More CRM>open Quote>Lineitems>double click in Partnum lookup>Cancel>Add>old partnum shows.-->
<!--7931 :  10/05/2022 : #7316 MoreCRM>Quotes > Detailed tab bugs-->
<!--actionListener="#{viewProductLookUpService.list('applyProd',quotesHdr.recId!=0?quotesDtl.quotPrincipal:quotesHdr.oppPrincipal)}"-->
                        <p:commandButton disabled="#{quotesHdr.isSuperQuote==true and quotesDtl.quotPrincipal==0}" id="btnPart"   
                                         icon="fa fa-search" title="Choose Part Number" immediate="true" widgetVar="btnPart" onclick="PF('btnPart').disable()"
                                        actionListener="#{quotesDtl.partLookup(quotesDtl.quotPrincipal)}"
                                         update=":dtPartNumForm" oncomplete="PF('dlgPartNum').show();PF('btnPart').enable()"  
                                         styleClass="btn-info btn-xs" />
                    </h:panelGroup>
                </div>

                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')} Part No"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:inputText value="#{quotesDtl.oppItemPartCust}" id="oppItemPartCust" maxlength="60"  style="width: 100%"  />
                </div>
            </div>
            <!--//29-07-2024 : #14134 : CRM-8215   Quotes: Need "add to product master" option on detailed view-->
            <p:outputPanel id="isExistPartNumberOutputPanel" >
                <p:outputPanel >
                    <div class="ui-g ui-fluid">
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Add to Products Master"/>
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3" style="margin-left:-60px;">
                            <p:selectBooleanCheckbox value="#{quotesDtl.addProductMstBol}" disabled="#{quotesDtl.isExistPart}">
                            </p:selectBooleanCheckbox>      

                        </div>
                    </div>
                </p:outputPanel>
            </p:outputPanel>
            <!--#3156 Quotes > Line Item > UOM-->


            <!--#3568:Task :CRM-2135: #4: Quotes > Line Items > UOM  start-->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="UOM"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:selectOneMenu styleClass="tds1"  onfocus="this.select();" value="#{quotesDtl.quotUom}" style="border-radius: 5px; background: white;" id="quotUom">
                        <f:selectItem itemLabel="[Select]" itemValue=""/>
                        <f:selectItems value="#{unitOfMeasurementsService.prodUoms()}"    var="prod" itemValue="#{prod.uomName}" itemLabel="#{prod.uomName}"/>    
                        <!--//#3534 Quotes > Line Items > Updates to Multiplier and Unit Price-->
                        <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                        <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                        <p:ajax   event="itemSelect"  process="@this" listener="#{quotesDtl.onChangeUOM(quotesDtl.quotStdRate, quotesDtl.quotUom, quotesDtl.oppItemQnty,quotesDtl.quotMultiplier)}"   update=":quotLstForm:extPrice :quotLstForm:quotUnit :quotLstForm:unit :quotLstForm:quotStdRate  :quotLstForm:specialPrice :quotLstForm:weight"  />

                    </p:selectOneMenu>
                        <!--<p:inputText value="#{quotesDtl.quotUom}" id="quotUom" style="width: 97%" maxlength="11" />-->
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="UOM Units"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:inputText value="#{quotesDtl.quotUomUnits}" id="quotUnit" style="width: 100%" maxlength="11" readonly="true"/>
                </div>

            </div>
            <!--#3568:Task :CRM-2135: #4: Quotes > Line Items > UOM End-->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="EAU"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:inputNumber maxValue="999999999"  decimalPlaces="0"  value="#{quotesDtl.oppEau}" id="oppEau" style="width: 100%" maxlength="2" converterMessage="Must be a number consisting of one or more digits."/>
                </div>
                <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2" style="display:flex">
                    <p:outputLabel value="Qty in"/>
                    <p:spacer width="10"/>
                    <p:selectOneMenu id="quotQntyUom" value="#{quotesDtl.quotQntyUom}" style="width:60px;min-width:70px" >
                        <f:selectItem itemLabel="Units" itemValue="0" />
                        <f:selectItem itemLabel="UOM" itemValue="1" />
                        <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                        <p:ajax event="itemSelect"   process="@this"  listener="#{quotesDtl.onChnageQuotQntyUom(quotesDtl.quotQntyUom, quotesDtl.oppItemQnty, quotesDtl.quotResale,quotesDtl.quotUomUnits,1)}" update=":quotLstForm:extPrice  :quotLstForm:weight "/>
                    </p:selectOneMenu>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    
                    <!--#601237: thorsonrm - increase qty decimals: changed quantity length to 12-->
                    <p:inputNumber decimalPlaces="3"  maxValue="999999999.999"   value="#{quotesDtl.oppItemQnty}" id="oppItemQnty" style="width: 100%" maxlength="12" converterMessage="Must be a signed decimal number." >
                        <!--#3547 Quotes Line item : Multiple time alert message displays. (Page Reloading issue)-->
                        <!--#3640 CRM-2135: #4: Quotes > Updates to Extended Price calculation involving UOM Units-->
                        <!--                    Feature #4996:Customer Pricing  > Quote Get Best Price 26/06/21 by harshithad-->
                        <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                        <p:ajax event="change" listener="#{quotesDtl.onQtyChange(quotesDtl)}" update=" :quotLstForm:quotUnit
                                :quotLstForm:quotStdRate :quotLstForm:quotMultiplier :quotLstForm:quotMarkupPct :quotLstForm:weight 
                                :quotLstForm:unit :quotLstForm:extPrice :quotLstForm:oppItemQnty" oncomplete="updateWidth();"/> 
                        <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
<!--                        <p:ajax event="blur" listener="#{quotesDtl.calculateExtPrice(quotesDtl.oppItemQnty, quotesDtl.quotResale, quotesDtl.quotUomUnits,quotesDtl.quotQntyUom)}" 
                                update="quotLstForm:extPrice" />-->
                    </p:inputNumber>
                </div>

            </div>

            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Unit Cost"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:inputNumber decimalPlaces="3"   maxValue="999999999.999"  value="#{quotesDtl.oppItemCost}" id="oppItemCost" style="width: 100%" maxlength="12" styleClass="partNumBox" converterMessage="Must be a signed decimal number." >
                        <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                        <!--<f:convertNumber minFractionDigits="3" maxFractionDigits="6"/>-->
                    </p:inputNumber>
                </div>


                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Target Cost"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:inputNumber decimalPlaces="3"   maxValue="999999999.999"  value="#{quotesDtl.quotTargetCost}" id="quotTargetCost" style="width: 100%" maxlength="12" converterMessage="Must be a signed decimal number." >
                        <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                        <!--<f:convertNumber minFractionDigits="3" maxFractionDigits="6"/>-->
                    </p:inputNumber>
                </div>
            </div>

            <!--Shreejith Bug 462 2018-06-21-->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--<p:outputLabel value="Standard Price:"/>-->
                    <!--                    Seema - 22/04/2019-->
                    <!--                    CRM 315:Gentsch :change quote labels-->
                    <p:outputLabel value="#{custom.labels.get('IDS_STD_PRICE')}"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--//          #12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
                    <!--                    Feature #4996:Customer Pricing  > Quote Get Best Price 26/06/21 by harshithad-->
                    <!--//05-10-2023 : #12270 : Incidental Finding: CRM-7392: Quotes: Standard price field has to be totally erased before you enter-->
                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                    <!--//30-11-2023 : #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                    <p:inputText  onkeyup="window.myFunction3(1)"  onkeypress="return window.myFunction4(event,1)"   
                                  value="#{quotesDtl.quotStdRate}"   converter="javax.faces.BigDecimal"  id="quotStdRate" style="width: 100%"  maxlength="15" styleClass="partNumBox" converterMessage="Must be a signed decimal number." onfocus="this.select();">
                        <!--<f:convertNumber maxFractionDigits="3"/>-->
                        <!--#3547 Quotes Line item : Multiple time alert message displays. (Page Reloading issue)-->
                        <!--#3640 CRM-2135: #4: Quotes > Updates to Extended Price calculation involving UOM Units--> 
                        <f:convertNumber   maxFractionDigits="6" minFractionDigits="2"/>

                        <!--//25-09-2024 : #14246 : 2 ESCALATION CRM-8351 : Quote: Unable to update standard price field for robroy items-->
                        <p:keyFilter mask="num" for="quotStdRate" preventPaste="false"/>
                        <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                        <p:ajax event="change" 
                                listener="#{quotesDtl.calculateUnitPrice(quotesDtl.quotStdRate, quotesDtl.quotMultiplier, quotesDtl.oppItemQnty, quotesDtl.quotUomUnits,1)}" 
                                update="quotLstForm:unit quotLstForm:extPrice quotLstForm:specialPrice" />
                        <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                        <!--<f:convertNumber minFractionDigits="3" maxFractionDigits="6"/>-->
                    </p:inputText>
                </div>

                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Multiplier:"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:-7px">
                    <div class="ui-g ui-fluid" >
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <!--//30-11-2023 : #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                            <p:inputNumber decimalPlaces="6"  maxValue="9999.999999" value="#{quotesDtl.quotMultiplier}" 
                                           id="quotMultiplier" maxlength="20" converterMessage="Must be a signed decimal number." 
                                           >    
                                <!--#3547 Quotes Line item : Multiple time alert message displays. (Page Reloading issue)-->
                                <!--#3640 CRM-2135: #4: Quotes > Updates to Extended Price calculation involving UOM Units-->
                                <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                                <p:ajax event="change" 
                                    listener="#{quotesDtl.calculateUnitPrice(quotesDtl.quotStdRate, quotesDtl.quotMultiplier, quotesDtl.oppItemQnty, quotesDtl.quotUomUnits,1)}" 
                                    update="quotLstForm:unit quotLstForm:extPrice quotLstForm:specialPrice" />
                            </p:inputNumber>
                        </div>
                        <div class="ui-sm-12 ui-md-7 ui-lg-7">
                            <p:outputLabel value="Customer Markup:" style="margin-left:10px;"/>
                        </div>
                        <div class="ui-sm-12 ui-md-2 ui-lg-2" style="margin-left:-25px;">
                            <!--//30-11-2023 : #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                            <p:inputNumber decimalPlaces="2" onkeypress="return event.keyCode != 13;"  maxValue="99999.99" 
                                   value="#{quotesDtl.quotCustMarkupPct}" id="quotMarkupPct" style="width: 100%" maxlength="8" 
                                   converterMessage="Must be a signed decimal number.">
                                <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                                <p:ajax event="change" 
                                    listener="#{quotesDtl.calculateMarkupValue(quotesDtl.oppItemQnty,quotesDtl.quotUomUnits,quotesDtl.quotStdRate, quotesDtl.quotMultiplier,quotesDtl.quotCustMarkupPct,quotesHdr.recId)}"
                                    update="quotLstForm:unit quotLstForm:extPrice quotLstForm:specialPrice" />
                        
                            </p:inputNumber>
                        </div>
                    </div>
                </div>
                
            </div>
            <script>
                
                //02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                            $(document).ready(function () {
                                var element = document.getElementById("quotLstForm:quotMultiplier_input");
                                element.style.width = "130%";
                                var elementMarkup = document.getElementById("quotLstForm:quotMarkupPct_input");
                                elementMarkup.style.width = "240%";
                            });
                            //02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier
                            function updateWidth(){
                                var element = document.getElementById("quotLstForm:quotMultiplier_input");
                                element.style.width = "130%";
                                var elementMarkup = document.getElementById("quotLstForm:quotMarkupPct_input");
                                elementMarkup.style.width = "240%";
                            }
            </script>

            <div class="ui-g ui-fluid" >
                <div class="ui-sm-12 ui-md-2 ui-lg-2" >
                    <!--#586840-->
                    <!--                    <p:outputLabel value="Unit Price:"/>-->
                    <!--                    Seema - 22/04/2019-->
                    <!--                    CRM 315:Gentsch :change quote labels-->
                    <p:outputLabel value="#{custom.labels.get('IDS_UNIT_PRICE')}"/>
                    <!--                   Feature #4996:Customer Pricing  > Quote Get Best Price 26/06/21 by harshithad-->
                    <p:outputLabel id ="otptLblAstersk" value="**" rendered="#{quotesDtl.quotCustPriceFlag==1}"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--//          #12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
<!--                    <p:inputText value="#{quotesDtl.quotResale}"  style="width: 97%" id='unit' styleClass="partNumBox"  maxlength="12" converterMessage="Must be a signed decimal number." disabled="true">
                        Bug 1045 : #982069 :unit price decimals messing up report
                        <f:convertNumber minFractionDigits="3" maxFractionDigits="6"/>
                    </p:inputText>-->
                    <!--#3534 Quotes > Line Items > Updates to Multiplier and Unit Price-->
                    <!--                  Feature #4996:Customer Pricing  > Quote Get Best Price 26/06/21 by harshithad-->
                    <p:inputText  onkeyup="window.myFunction3(2)"  onkeypress="return window.myFunction4(event,2)"    converter="javax.faces.BigDecimal"   value="#{quotesDtl.quotResale}" id='unit' styleClass="partNumBox" style="width: 100%" converterMessage="Must be a signed decimal number." disabled="#{quotesDtl.unitPriceFlag}">
                         <f:convertNumber   maxFractionDigits="6" minFractionDigits="2"/>

                        <!--//25-09-2024 : #14246 : 2 ESCALATION CRM-8351 : Quote: Unable to update standard price field for robroy items-->
                        <p:keyFilter mask="num" for="unit" preventPaste="false"/>
                        <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                        <p:ajax event="change" listener="#{quotesDtl.calculateExtPrice(quotesDtl.oppItemQnty, quotesDtl.quotResale, quotesDtl.quotUomUnits,quotesDtl.quotQntyUom,0)}" 
                                update=":quotLstForm:oppItemPartCust :quotLstForm:quotUnit
                                :quotLstForm:oppEau :quotLstForm:oppItemCost :quotLstForm:quotTargetCost :quotLstForm:quotStdRate :quotLstForm:quotMultiplier
                                :quotLstForm:unit :quotLstForm:extPrice :quotLstForm:prdLne" oncomplete="updateWidth();"/>
                    </p:inputText>
                </div>

                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Extended Price"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--#3534 Quotes > Line Items > Updates to Multiplier and Unit Price-->
                    <p:inputNumber value="#{quotesDtl.quotExtPrice}" id="extPrice" styleClass="partNumBox" style="width: 100%" disabled="true" converterMessage="Must be a signed decimal number.">
                    </p:inputNumber>

                </div>
            </div>

            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Lead Time"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                     <!--7930 09/05/2022 Quotes: UOM and UOM units doesn't change/Quote Selection issues-->
                    <p:inputText value="#{quotesDtl.quotLeadTime}" id="ledTime" style="width: 100%" maxlength="20" converterMessage="Must be a signed decimal number." styleClass="partNumBox"/>
                </div>

                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Prod Line"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                     <!--7930 09/05/2022 Quotes: UOM and UOM units doesn't change/Quote Selection issues-->
                    <p:inputText value="#{quotesDtl.quotProdLine}" style="width: 100%"  maxlength="30" styleClass="partNumBox" id="prdLne"/>
                </div>
            </div>
            <!--5858 IS Quotes: Quote Line Item > Location-->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                      <!--task:14847 :05-11-2024 added the custom label for "Location" field-->
                    <p:outputLabel value="#{custom.labels.get('IDS_LOCATION')}"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:inputText value="#{quotesDtl.quotLocation}" style="width: 100%" 
                                 maxlength="60" styleClass="partNumBox" />
                </div>
                <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Weight (lbs)"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--//30-06-2022 :  #8348 : Quote Line Item: Change Part Number lookup to Input-text Autocomplete feature-->
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                    <p:inputText id="weight" onkeypress="return event.keyCode != 13;"  value="#{quotesDtl.quotWeight}" style="width: 100%"  
                                 maxlength="30" styleClass="partNumBox" />
                </div>
            </div>

            
            
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
           <p:outputLabel value="Image URL" />
           </div>
                
                 <div class="ui-sm-12 ui-md-4 ui-lg-9">
                <p:inputText onkeypress="return event.keyCode != 13;"  styleClass="partNumBox"   onfocus="this.select();"  value="#{quotesDtl.prodImgUrl}"  id="imageURLInput" maxlength="500"  >
                    <p:ajax event="change" update="quotLstForm:opnLink"/>
                </p:inputText> 
                      </div>
             <div class="ui-sm-12 ui-md-4 ui-lg-1">       
                <p:link id="opnLink" value="View link" 
                                                style="color: blue;" disabled="#{quotesDtl.prodImgUrl == ''}"  
                                                href="#{quotesDtl.getValidURL(quotesDtl.prodImgUrl)}" target="_blank" /> 
                           </div>
                           </div>
            <!--//14-08-2024 : #14272 :  CRM-8214 : Quote Line Item: New Feature of Specific URL -->
                 <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
           <p:outputLabel value="Specification URL" />
           </div>
 <div class="ui-sm-12 ui-md-4 ui-lg-9">
                <p:inputText  onkeypress="return event.keyCode != 13;" styleClass="partNumBox"  onfocus="this.select();"  value="#{quotesDtl.prodSpecUrl}"  id="specURLInput" maxlength="500"  >
                     <p:ajax event="change" update="quotLstForm:opnLink1"/>
                </p:inputText> 
                       </div>
                    <div class="ui-sm-12 ui-md-7 ui-lg-1"> 
                <p:link id="opnLink1" value="View link"  
                                                style="color: blue;" disabled="#{quotesDtl.prodSpecUrl == ''}"  
                                                href="#{quotesDtl.getValidURL(quotesDtl.prodSpecUrl)}" target="_blank"/>
                </div>
                     </div>
            
            
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Part Description"/>
                </div>
                <div class="ui-sm-12 ui-md-10 ui-lg-10" >
                    <!--//    Bug 1266 -#193088 :EPIC/GOLDEN INSTANCE:part number UI-->
                    <!--                    Task#3939-CRM-3987: Quote description field too limited  23/03/21 by harshithad-->
                    <p:inputTextarea rows="2" id="oppItemPartDesc" value="#{quotesDtl.oppItemPartDesc}" style="width: 99%;text-overflow: scroll" styleClass="partNumBox" autoResize="false" >
                        <!--#3547 Quotes Line item : Multiple time alert message displays. (Page Reloading issue)-->
<!--                        <p:ajax event="blur" listener="#{transDtl.checkvalidate(quotesDtl.oppItemPartDesc ,1)}"  update=" " />-->
                    </p:inputTextarea>
                </div>
            </div>

            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Notes"/>
                </div>
                <div class="ui-sm-12 ui-md-10 ui-lg-10">
                    <p:inputTextarea rows="2" value="#{quotesDtl.quotNotes}" style="width: 99%;text-overflow: scroll" autoResize="false" />
                </div>
            </div>            
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-12 ui-lg-12">
                    <p:selectBooleanCheckbox rendered="#{quotesDtl.oppId!=0}" id="updNrmlLnItmReq" 
                                             itemLabel="Update linked #{custom.labels.get('IDS_OPP')}" 
                                             value="#{quotesDtl.updateOppLnItm}"  />

                </div>
            </div>
            <!--//15-12-2023 : #12725 : CRM-7458	quotes: customer markup next to multiplier (Tool tip updates)-->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-12 ui-lg-12">
                    <p:outputLabel value="Note: Unit Price is calculated as (Std. Price * Multiplier) / Customer Markup. Customer Markup is not considered if entered 0 or blank."/>
                </div>
            </div>



        </p:panelGrid>
        <!--28/02/2022 7316 MoreCRM>Quotes > Detailed tab bugs-->
<style>
            .ui-panelgrid .ui-panelgrid-cell {    
                border-style: none !important;  
                border: none !important;
            }
            .ui-panelgrid {
                border-collapse: unset !important;
            }
        </style>
           <p:confirmDialog header="Confirm delete"  width="400" global="true"
                         message="Are you sure to delete?"
                         widgetVar="confirmation1" >
            <div class="div-center">
                <!--                //2848 CRM-1790 :Hawkins :Fw Quote total-->
                <!--Seema - 06/01/2020 - updated :quotLstForm:opVl-->
                <!--#302 Quotes > Move Line Items to a new tab-->
                <p:commandButton value="Delete" action="#{quotesDtl.DeleteDtls()}"
                                 class="btn btn-danger btn-xs" process="@this"   onclick="PF('confirmation1').hide();
                                         PF('dlgQuoteDtls').hide();"  update=":quotLstForm:tblQdtl"  />
                <p:spacer width="4"/>
                <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"    onclick="PF('confirmation1').hide()" 
                                 type="button"  />
            </div>
        </p:confirmDialog>
        <!--         <p:dialog header="Message" widgetVar="processDlg" closable="false" resizable="false" modal="true">
                <p:outputPanel style="text-align: center" >
                    <br />
                    <h:graphicImage  library="images" name="ajax-loader.gif" id="loadingImg"   />
                    <p:spacer width="8" />
                    <h:outputLabel value="Processing..." />
                    <br /><br />
                </p:outputPanel>
                <br />
            </p:dialog>-->
    </p:dialog>


      
</ui:composition>