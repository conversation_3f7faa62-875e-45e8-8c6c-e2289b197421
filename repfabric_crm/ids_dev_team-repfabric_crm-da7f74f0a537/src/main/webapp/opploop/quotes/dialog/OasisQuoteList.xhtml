<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
 <!--//        4872 CRM Sync: Settings for Oasis-->
    <p:dialog  styleClass="dialogCSS" id="isQuotListDtlsx1" widgetVar="isQuotListDtls1"  responsive="true" draggable="false" resizable="false"
               style="text-overflow: scroll;"  height="80%" width="80%" modal="true">
        <f:facet name="header">
            <h:outputText id="isQuotListTxt1" value="Oasis Quotes List" />
        </f:facet>
        <h:form id="isQfrm1">
            <p:outputLabel rendered="#{quotesHdr.filterISQuotesStatus!=2}" id="dtIsQtLbl1" value="Quotes for : " style="font-weight: bold;" /><p:spacer/>
            <p:outputLabel rendered="#{quotesHdr.filterISQuotesStatus!=2}" id="dtIsQt1" value="#{quotesHdr.paramISQuotDate}" style="font-weight: bolder;" >
                <f:convertDateTime pattern="#{globalParams.dateFormat}" />  
            </p:outputLabel>
            <p:spacer /> 
            <p:dataTable value="#{iSQuoteCrmClient.quotLookUp}" rows="10"
                         var="isqHd" id="isq1" 
                         emptyMessage="No Quotes available" 
                         scrollable="true"  paginator="true" selectionMode="single"
                         selection="#{quotesHdr.selectedHdr}" 
                         rowKey="#{isqHd.quotNumber}" 
                         >
                <!--7024 Oasis: Auto-Download Process for Jobs and linked quotes (cron job)-->
                <!--7291 CRM-5389  Oasis thea: not able to pull down because no companies are surfaced-->
                <p:ajax event="rowSelect" listener="#{quotesHdr.selectOasisQuote}" process="@this" 
                        immediate="true" update=":quotefrom:tabQuot :isQuotLnItmListDtlsx :isQfrm22:isql1 :isQfrm22" 
                        oncomplete="PF('isQuotListDtls1').hide();" />

                <!--                <p:column  width="2%"> 
                                    <f:facet name="header"></f:facet>
                
                                </p:column> 
                                <p:column width="8%" >
                                    <f:facet name="header">Quote ID</f:facet>
                #{isqHd.quotName}  
        </p:column>-->
<!--                <p:column width="8%" >
                    <f:facet name="header">job Name </f:facet>
                        #{isqHd.jobName}  
                </p:column>-->
                <p:column width="8%" >
                    <f:facet name="header">Quote Number</f:facet>
                        #{isqHd.quotNumber}  
                </p:column>
                <p:column width="8%" >
                    <f:facet name="header">Job Name</f:facet>
                        #{isqHd.jobName}  
                </p:column>
<!--9703 CRM-6129   oasis: add quote value column to selector/ ERI-->
                <p:column width="8%" >
                    <f:facet name="header" >Quote Value</f:facet>
                    <div style="text-align: right" > #{isqHd.quotValue}</div>
                </p:column> 
                <p:column width="8%" >
                    <f:facet name="header">City</f:facet>
                        #{isqHd.cityName}  
                </p:column>

<!--                <p:column width="8%" >
                    <f:facet name="header">Quote Type</f:facet>
                        #{isqHd.quoteType}  
                </p:column>-->
                <p:column width="8%" >
                    <f:facet name="header">Quote Date</f:facet>
                        #{isqHd.quotDate}  
                </p:column> 
            </p:dataTable>

        </h:form>
    </p:dialog>


</ui:composition>   



