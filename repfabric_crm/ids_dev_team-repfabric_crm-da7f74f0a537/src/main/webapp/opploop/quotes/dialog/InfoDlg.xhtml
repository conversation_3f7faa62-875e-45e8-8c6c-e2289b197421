<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <!--#3085 Quotes - Line Items - Import-->
    <!--#3708 Quotes> Line Items -Import - Updates in template-->
    <p:dialog widgetVar="helpDlg" header="Info" height="530" width="950" modal="true"  >

        Import feature allows you to import Quote details data to the database from .csv / .xls /.xlsx file formats.

        <br/><br/>
        <!--#3354 Fix Sonarqube issues-->
        <strong>Guidelines</strong>
        <font size="2" style="padding-left: 5px;">
            <ul>
                <li>
                    Provide a header column with column titles in the import file.
                </li>
                <li>
                    Make sure all the fields are present and in the same order as given below.
                </li>
                <li>
                    Provide blank value in case no data is available for a data cell.
                </li>
<!--                Task#3543-CRM-3924: quotes: need ability to import multiple manufacturer quote   :24-02-2021 by harshithad-->
                 <h:panelGroup rendered="#{quotesHdr.isSuperQuote==false}">
                <li>
                    Mandatory Fields: Qty, Part #, Unit Price, Extended Price
                </li> 
                 </h:panelGroup>
                <h:panelGroup rendered="#{quotesHdr.isSuperQuote==true}">
                <li>
                    Mandatory Fields: Qty, Part #, Unit Price, Extended Price, #{custom.labels.get('IDS_PRINCI')}
                </li> 
                 </h:panelGroup>
                     
            </ul>
        </font>
        <fieldset>							

            <legend style="font-weight: bold">Column Order</legend>
            Quotes Line Items Data<br/>
             <!--#11212 CRM-6918: Product: Set pricing per hundred in template-->
<!--            Task#3543-CRM-3924: quotes: need ability to import multiple manufacturer quote   :24-02-2021 by harshithad-->
              <h:panelGroup rendered="#{quotesHdr.isSuperQuote==false}">
            <div  style="font-size: 2; display: flex">
                Qty (in units)   &#160;<div style="color: red; white-space: normal;" >|</div>     &#160;
                Part #   &#160;<div style="color: red" >|</div>     &#160;
                Description   &#160;<div style="color: red" >|</div>     &#160;
                Unit Price   &#160;<div style="color: red" >|</div>     &#160;
                Pricing UOM   &#160; <div style="color: red" >|</div>     &#160;
                Extended Price    
            </div>
              </h:panelGroup>
 <!--#11212 CRM-6918: Product: Set pricing per hundred in template-->
            <h:panelGroup rendered="#{quotesHdr.isSuperQuote==true}">
                  <div  style="font-size: 2; display: flex">
                Qty (in units)   &#160;<div style="color: red; white-space: normal;" >|</div>     &#160;
                Part #   &#160;<div style="color: red" >|</div>     &#160;
                Description   &#160;<div style="color: red" >|</div>     &#160;
                Unit Price   &#160;<div style="color: red" >|</div>     &#160;
                Pricing UOM   &#160; <div style="color: red" >|</div>     &#160;
                Extended Price     &#160; <div style="color: red"  >|</div>     &#160;
                #{custom.labels.get('IDS_PRINCI')}
            </div>
            </h:panelGroup>






        </fieldset>
        <br/>
        <h:form>
            <div style="text-align: center">
                <p:commandButton value="OK" onclick="PF('helpDlg').hide();" class="btn btn-xs btn-primary" />
            </div>
        </h:form>
    </p:dialog>
    <style>
        body .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content {
            padding: 30px!important;
        }
    </style>
</ui:composition>