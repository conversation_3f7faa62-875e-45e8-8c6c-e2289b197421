<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui" 
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--08-02-2023 10506 CRMSYNC-112/141: AutoQuotes: Quotes: Sync Status-->
    <h:form id="frmQuoteCompsAliasLookup" >

        <!--03-05-2024 13633 CRMSYNC-662: AutoQuotes: Alias Contact on Quote creation-->
        <p:remoteCommand 
            id="applyQuoteRfCompanyAlies" name="applyQuoteRfCompanyAlies" autoRun="false"
            action="#{autoQuotesService.applyRfComp(aurinkoCompService.aurinkoComp.tsCompId,viewCompLookupService.selectedCompany.compId,viewCompLookupService.selectedCompany.compName)}"
            update=":frmQuoteCompsAliasLookup:opQuoteCompAliasLookup"/>

        <!--03-05-2024 13633 CRMSYNC-662: AutoQuotes: Alias Contact on Quote creation-->
        <p:remoteCommand 
            id="applyQuoteRfContactAlies" name="applyQuoteRfContactAlies" autoRun="false"
            action="#{autoQuotesService.applyRfComp(aurinkoCompService.aurinkoComp.tsCompId,viewContLookupService.selectedContact.contId,viewContLookupService.selectedContact.contFullName)}"
            update=":frmQuoteCompsAliasLookup:opQuoteCompAliasLookup"/>

        <!--27-02-2023 10710 ESCALATIONS: CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
        <!--08-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
        <p:dialog header="AutoQuote company alias " modal="true" width="1000" 
                  responsive="true" id="iddlgQuoteCompsAlias" widgetVar="dlgQuoteCompsAlias" style="z-index: 905 !important" >
            <!--08-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
            <f:facet name="header">
                <!--08-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
                <p:outputPanel id="opQuoteCompHdr" style="font-weight: bold; font-size: 18px">
                    #{autoQuotesService.quoteCompDlgHeader}
                </p:outputPanel>
            </f:facet>

            <p:outputPanel id="opQuoteCompAliasLookup">

                <!--16-02-2023 10695 CRMSYNC-139  Add Autoquotes option to 'Fetch from' drop-down box-->
                <!--17-03-2023 10909 CRM-6199: Need to check if AutoQuotes breaks-->
                <!--04-08-2023 11801 CRMSYNC-359   Sync Logic. Quote. RF UI-->
                <p:commandButton  value="Apply" rendered="#{autoQuotesService.createQutoe==2}"
                                  class="btn-primary btn-xs" disabled="#{empty autoQuotesService.seletedQuote.targetCustId}"
                                  onclick="PF('dlgProcessing').show();"
                                  actionListener="#{autoQuotesService.resolveQuoteAndRedirect(autoQuotesService.seletedQuote,loginBean.userId,autoQuotesService.seletedQuote.princiId)}"/>

                <!--17-03-2023 10909 CRM-6199: Need to check if AutoQuotes breaks-->
                <!--05-07-2024 13633 CRMSYNC-662/688CRM-7992: AutoQuotes: Alias Contact on Quote creation-->
                <p:poll id="pollQuoteSyncStatus" widgetVar="wvPollQuoteSyncView" interval="3" autoStart="false"
                        listener="#{autoQuotesService.onPollQuoteStatus()}" 
                        stop="#{autoQuotesService.stopPoll}" update="@this" process="@this" />

                <!--17-03-2023 10909 CRM-6199: Need to check if AutoQuotes breaks-->
                <!--05-07-2024 13633 CRMSYNC-662/688CRM-7992: AutoQuotes: Alias Contact on Quote creation-->
                <!--11-09-2024 13977 CRMSYNC-727 : Line Item Query Parameters & Optimizations-->
                <p:remoteCommand id="rcOnQuoteCreateDoneRedirect" name="rcOnQuoteCreateDoneRedirect" autoRun="false"
                                 actionListener="#{autoQuotesService.redirectRfQuote()}" 
                                 oncomplete="PF('dlgQuoteCompsAlias').hide();PF('wvDlgUnResAutoquote').hide();PF('dlgProcessing').hide();"/>

                <!--17-03-2023 10909 CRM-6199: Need to check if AutoQuotes breaks-->
                <!--05-07-2024 13633 CRMSYNC-662/688CRM-7992: AutoQuotes: Alias Contact on Quote creation-->
<!--                <p:remoteCommand id="rcOnQuoteCreateDone" name="rcOnQuoteCreateDone" autoRun="false"
                                 actionListener="#{autoQuotesService.quoteCreated(loginBean.userId)}"
                                 oncomplete="PF('dlgQuoteCompsAlias').hide();PF('dlgProcessing').hide();"/>-->

                <!--27-02-2023 10710 ESCALATIONS: CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                <p:dataTable id="dtQuoteCompsAlias" widgetVar="wvDtQuoteCompsAlias" paginator="false"
                             scrollHeight="320"
                             scrollable="true"
                             value="#{autoQuotesService.seletedQuote.linkedCompanies}"  
                             filteredValue="#{autoQuotesService.linkedCompaniesFlt}"
                             var="autoQuoteComp" multiViewState="true"
                             emptyMessage="No records found"
                             rowKey="#{autoQuoteComp.tsCompId}" 
                             rowIndexVar="autoQuoteCompRowIdx" 
                             filterEvent="keyup paste">

                    <p:column width="25" headerText="ID" 
                              filterBy="#{autoQuoteComp.tsCompId}" filterMatchMode="contains" 
                              sortBy="#{autoQuoteComp.tsCompId}" 
                              styleClass="might-overflow" >
                        <p:outputLabel value="#{autoQuoteComp.tsCompId}" />
                    </p:column>

                    <p:column width="25" visible="false" headerText="Type" filterBy="#{autoQuoteComp.aurType}" styleClass="might-overflow"
                              filterMatchMode="contains" sortBy="#{autoQuoteComp.aurType}" >
                        #{autoQuoteComp.aurType}
                    </p:column>

                    <p:column width="25" headerText="Name" styleClass="might-overflow"
                              filterBy="#{autoQuoteComp.tsCompName}" filterMatchMode="contains" sortBy="#{autoQuoteComp.tsCompName}"  >
                        #{autoQuoteComp.tsCompName}
                    </p:column>

                    <p:column width="25" headerText="Type" styleClass="might-overflow"
                              filterBy="#{autoQuoteComp.compTypeName}" filterMatchMode="contains" 
                              sortBy="#{autoQuoteComp.compTypeName}"  >
                        #{autoQuoteComp.compTypeName}
                    </p:column>

                    <p:column width="25" headerText="City" styleClass="might-overflow"
                              filterBy="#{autoQuoteComp.city}" filterMatchMode="contains" 
                              sortBy="#{autoQuoteComp.city}"  >
                        #{autoQuoteComp.city}
                    </p:column>

                    <p:column width="25" headerText="#{custom.labels.get('IDS_ZIP')}" styleClass="might-overflow"
                              filterBy="#{autoQuoteComp.postalCode}" filterMatchMode="contains" sortBy="#{autoQuoteComp.postalCode}"  >
                        #{autoQuoteComp.postalCode}
                    </p:column>

                    <p:column width="40"  styleClass="might-overflow">
                        <p:inputText value="#{autoQuoteComp.rfCompName}" readonly="true" style="width: 70%"/>
                        <p:spacer width="4" />
                        <!--27-02-2023 10710 ESCALATIONS: CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                        <!--08-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
                        <!--03-05-2024 13633 CRMSYNC-662: AutoQuotes: Alias Contact on Quote creation-->
                        <!--19-09-2024 13633 CRMSYNC-662/688/CRM-7992: AutoQuotes: Alias Contact on Quote creation-->
                        <p:commandButton 
                            icon="fa fa-search" class="btn-info btn-xs" 
                            style="width:50px" process="@this" immediate="true"
                            rendered="#{not autoQuoteComp.contactType}"
                            actionListener="#{aurinkoCompService.rfCompAlieas(autoQuoteComp)}"
                            action="#{viewCompLookupService.listLookUpvalues('applyQuoteRfCompanyAlies',autoQuoteComp.compType)}"
                            update=":frmLookupHead :formCompLookup"
                            oncomplete="PF('lookupComp').show();PF('compLookup').filter();"/>

                        <!--03-05-2024 13633 CRMSYNC-662: AutoQuotes: Alias Contact on Quote creation-->
                        <p:commandButton 
                            icon="fa fa-search" title="Choose Contact" styleClass="btn-info btn-xs" 
                            style="width:50px" process="@this" immediate="true"
                            rendered="#{autoQuoteComp.contactType}"
                            actionListener="#{aurinkoCompService.rfContAlieas(autoQuoteComp)}"
                            action="#{viewContLookupService.listVisibleContacts('applyQuoteRfContactAlies',autoQuoteComp.compType,null)}" 
                            update=":formNew :formContLookup :formContLookup:contactLookUp"
                            oncomplete="PF('lookupCont').show();PF('contLookup').filter();" />
                    </p:column>

                </p:dataTable>

                <br/>
                <div style=" text-align: center">
                    <!--16-02-2023 10695 CRMSYNC-139  Add Autoquotes option to 'Fetch from' drop-down box-->
                    <!--17-03-2023 10909 CRM-6199: Need to check if AutoQuotes breaks-->
                    <p:commandButton  value="Create quote" rendered="#{autoQuotesService.createQutoe==0}"
                                      class="btn-primary btn-xs" disabled="#{empty autoQuotesService.seletedQuote.targetCustId}"
                                      onclick="PF('dlgProcessing').show();"
                                      actionListener="#{autoQuotesService.resolveQuote(autoQuotesService.seletedQuote,loginBean.userId,autoQuotesService.seletedQuote.princiId)}"/>

                    <!--16-02-2023 10695 CRMSYNC-139  Add Autoquotes option to 'Fetch from' drop-down box-->
                    <!--16-03-2023 10909 CRM-6199: Need to check if AutoQuotes breaks-->
                    <p:commandButton  value="Save alias" rendered="#{autoQuotesService.createQutoe==1}"
                                      class="btn-primary btn-xs"
                                      onclick="PF('dlgProcessing').show();"
                                      actionListener="#{autoQuotesService.saveAliasing(autoQuotesService.seletedQuote.linkedCompanies, autoQuotesService.seletedQuote.princiId,loginBean.userId)}"
                                      oncomplete="PF('dlgProcessing').hide();"/>
                    <p:spacer width="4" />
                    <p:commandButton value="Cancel" class="btn-warning btn-xs" 
                                     rendered="#{autoQuotesService.createQutoe!=2}"
                                     oncomplete="PF('dlgQuoteCompsAlias').hide();" />
                </div>
            </p:outputPanel>
        </p:dialog>
    </h:form>
    <!--08-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
    <style>
        div.ui-tooltip{
            z-index: 1129 !important;
        }
    </style>
</ui:composition>