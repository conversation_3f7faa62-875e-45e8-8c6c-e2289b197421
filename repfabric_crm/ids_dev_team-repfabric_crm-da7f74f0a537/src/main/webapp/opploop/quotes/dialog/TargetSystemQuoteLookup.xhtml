<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--11-08-2023 11857 CRMSYNC-359 Sync Logic. Quote. RF UI-->
    <h:form id="frmTargetSystemQuoteLookup">


        <p:dialog  class="dialogCSS" id="dlgTargetSystemQuote" widgetVar="wvDlgTargetSystemQuote"  
                   responsive="true" draggable="false" resizable="false" header="Target System Lookup"
                   style="text-overflow: scroll;"  height="80%" width="80%" modal="true">

            <p:dataTable value="#{quoteSyncService.targetSystemQuoteList}" filteredValue="#{quoteSyncService.targetSystemQuoteFilteredList}"
                         rows="10"
                         var="tsq" id="dtTargetSystemQuotes" widgetVar="wvDtTargetSystemQuotes"
                         emptyMessage="No Quotes available" 
                         scrollable="true"  paginator="true" selectionMode="single"
                         selection="#{quoteSyncService.targetSystemQuote}" 
                         rowKey="#{tsq.aurinkoId}" >

                <p:ajax event="rowSelect" listener="#{quoteSyncService.onTargetSystemQuoteSelected}" process="@this" />

                <p:column width="8%" styleClass="might-overflow" sortBy="#{tsq.extId}"
                          filterBy="#{tsq.extId}" filterMatchMode="contains">
                    <f:facet name="header">Ext. Quote ID</f:facet>
                        #{tsq.extId}  
                </p:column>

                <p:column width="8%" styleClass="might-overflow" sortBy="#{tsq.quoteDate}"
                          filterBy="#{tsq.quoteDate}" filterMatchMode="contains">
                    <f:facet name="header">Quote Date</f:facet>
                        #{tsq.quoteDate}  
                </p:column> 

                <p:column width="8%" styleClass="might-overflow" sortBy="#{tsq.topic}"
                          filterBy="#{tsq.topic}" filterMatchMode="contains">
                    <f:facet name="header">Name</f:facet>
                        #{tsq.topic}  
                </p:column> 

                <p:column width="8%" styleClass="might-overflow" sortBy="#{tsq.custName}"
                          filterBy="#{tsq.topic}" filterMatchMode="contains">
                    <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet>
                        #{tsq.custName}  
                </p:column> 

                <p:column width="8%" styleClass="might-overflow" sortBy="#{tsq.compCity}"
                          filterBy="#{tsq.compCity}" filterMatchMode="contains">
                    <f:facet name="header">City</f:facet>
                        #{tsq.compCity}  
                </p:column>

                <p:column width="8%" styleClass="might-overflow" sortBy="#{tsq.value}" 
                          filterBy="#{tsq.value}" filterMatchMode="contains">
                    <f:facet name="header" >Quote Value</f:facet>
                    <h:outputLabel  value="#{tsq.value}" style="width:100%;text-align: right">
                        <f:convertNumber groupingUsed="true"  maxIntegerDigits="15"  
                                         minFractionDigits="2" maxFractionDigits="2"/>
                    </h:outputLabel>
                </p:column> 
            </p:dataTable>

        </p:dialog>
    </h:form>

</ui:composition>   



