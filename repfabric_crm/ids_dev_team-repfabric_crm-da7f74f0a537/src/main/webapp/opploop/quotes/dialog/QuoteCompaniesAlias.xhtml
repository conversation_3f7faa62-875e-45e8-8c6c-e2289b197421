<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui" 
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--11-08-2023 11857 CRMSYNC-359 Sync Logic. Quote. RF UI-->
    <h:form id="frmQuoteCompsnaniesAliasLookup" >
        <p:dialog header="Target System Company Alias " modal="true" width="900" 
                  responsive="true" id="iddlgQuoteCompaniesAlias" widgetVar="dlgQuoteCompaniesAlias" style="z-index: 905 !important" >

            <p:remoteCommand id="rcApplyTsComp" name="rcApplyTsComp" autoRun="false"
                             actionListener="#{quoteSyncService.applyTsComp(aurinkoCompService.aurinkoComp.rfCompId, aurinkoCompService.selectedAurinkoComp.tsCompId, aurinkoCompService.selectedAurinkoComp.tsCompName)}" 
                             update=":frmQuoteCompsnaniesAliasLookup:opQuoteCompaniesAliasLookup"/>

            <p:outputPanel id="opQuoteCompaniesAliasLookup">

                <p:dataTable id="dtQuoteCompaniesAlias" widgetVar="wvDtQuoteCompaniesAlias" paginator="false"
                             scrollHeight="320"
                             scrollable="true"
                             value="#{quoteSyncService.quoteCompanyList}"  
                             filteredValue="#{quoteSyncService.quoteCompanyFilteredList}"
                             var="quoteComp" multiViewState="true"
                             emptyMessage="No records found"
                             rowKey="#{quoteComp.aurinkoId}" 
                             rowIndexVar="quoteCompRowIdx" 
                             filterEvent="keyup paste">

                    <p:column width="25" headerText="ID" 
                              filterBy="#{quoteComp.rfCompId}" filterMatchMode="contains" 
                              sortBy="#{quoteComp.rfCompId}" 
                              styleClass="might-overflow" >
                        <p:outputLabel value="#{quoteComp.rfCompId}" />
                    </p:column>

                    <p:column width="25" visible="false" headerText="Type" filterBy="#{quoteComp.aurType}" styleClass="might-overflow"
                              filterMatchMode="contains" sortBy="#{quoteComp.aurType}" >
                        #{quoteComp.aurType}
                    </p:column>

                    <p:column width="25" headerText="Name" styleClass="might-overflow"
                              filterBy="#{quoteComp.rfCompName}" filterMatchMode="contains" sortBy="#{quoteComp.companyName}"  >
                        #{quoteComp.rfCompName}
                    </p:column>

                    <p:column width="25" headerText="Type" styleClass="might-overflow"
                              filterBy="#{quoteComp.compTypeName}" filterMatchMode="contains" 
                              sortBy="#{quoteComp.compTypeName}"  >
                        #{quoteComp.compTypeName}
                    </p:column>

                    <p:column width="25" headerText="City" styleClass="might-overflow"
                              filterBy="#{quoteComp.city}" filterMatchMode="contains" 
                              sortBy="#{quoteComp.city}"  >
                        #{quoteComp.city}
                    </p:column>

                    <p:column width="25" headerText="#{custom.labels.get('IDS_ZIP')}" styleClass="might-overflow"
                              filterBy="#{quoteComp.postalCode}" filterMatchMode="contains" sortBy="#{quoteComp.postalCode}"  >
                        #{quoteComp.postalCode}
                    </p:column>

                    <p:column width="40"  styleClass="might-overflow">
                        <p:inputText value="#{quoteComp.tsCompId}" readonly="true" style="width: 70%"/>
                        <p:spacer width="4" />
                        <p:commandButton 
                            icon="fa fa-search" class="btn-primary btn-xs" style="width:50px" process="@this" immediate="true"
                            actionListener="#{aurinkoCompService.listAurinkoComps(quoteComp,quoteSyncService.principalId, 'rcApplyTsComp')}"
                            update=":frmAurComps:dtAurinkoComp :frmAurComps:pgAurCompFlt"
                            oncomplete="PF('dlgAurComp').show();PF('wvDtAurinkoComp').filter();"/>
                    </p:column>

                </p:dataTable>

                <br/>
                <div style=" text-align: center">
                    <p:commandButton  value="Create quote" class="btn-primary btn-xs" disabled="#{empty quoteSyncService.targetSystemQuote.targetCustId}"
                                      onclick="PF('dlgProcessingOnTask').show();"
                                      actionListener="#{quoteSyncService.onclickCreateQuote(quoteSyncService.targetSystemQuote.linkedCompanies, quoteSyncService.targetSystemQuote.extId, quoteSyncService.targetSystemQuote.targetCustId,quoteSyncService.principalId)}"
                                      update=":quotefrom:tabQuot:pnlCRMSYNC" oncomplete="PF('dlgProcessingOnTask').hide();"/>

                    <p:spacer width="4" />
                    <p:commandButton value="Cancel" class="btn-warning btn-xs" 
                                     oncomplete="PF('dlgQuoteCompaniesAlias').hide();" />
                </div>
            </p:outputPanel>
        </p:dialog>
    </h:form>
    <style>
        div.ui-tooltip{
            z-index: 1129 !important;
        }
    </style>
</ui:composition>