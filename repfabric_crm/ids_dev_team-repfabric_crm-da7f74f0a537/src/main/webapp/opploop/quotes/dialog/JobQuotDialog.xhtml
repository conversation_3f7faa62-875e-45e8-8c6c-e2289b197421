<?xml version="1.0" encoding="UTF-8"?>
<!--
#3242 Quotes > View Job
-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui" 
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">
    <div>
        <p:dialog widgetVar="dlgQuotJobList" header="Link Quote to #{custom.labels.get('IDS_JOB')}" modal="true">
            <h:form id="frmJobList" >
               
                <!--23-01-2024 12761 CRM-7599   OASIS: Surface Project Rank for Quotes-->
                <p:remoteCommand id="refreshQuoteRank" name="refreshQuoteRank" 
                                 actionListener="#{quotesHdr.refreshRank()}"
                                 update=":quotefrom:tabQuot:quotRank" />
                
               <p:commandButton class="btn btn-primary btn-xs" id="add1" value="New" onstart="PF('dlgQuotJobList').hide()" actionListener="#{jobs.resetfields()}" action="#{jobs.populateOppFields()}" 
                                oncomplete="PF('addJobDlg').show(); $('#jobAddNewForm\\:jobName').val($('#quotefrom\\:tabQuot\\:qTopic').val()); "
                             update=":jobAddNewForm"    ></p:commandButton>
                
                <p:dataTable  id="dtJobList" widgetVar="dtJobList" paginator="true"                    
                              value="#{jobs.jobList}"                         
                              filterEvent="keyup"                          
                              filteredValue="#{jobs.filteredJobList}" 
                              selectionMode="single" 
                              var="viewjobs"                                       
                              rowKey="#{jobs.jobId}"                                    
                              paginatorAlwaysVisible="false"
                              paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                              rows="10"   class="tblmain"
                              emptyMessage="No jobs available"
                              draggableColumns="true">


                    <f:facet name="header">
                        <p:inputText style="display: none"   id="globalFilter" onkeyup="PF('dtJobList').filter()" />
                    </f:facet>
<!-- //       #11089 Quotes Code Optimisation : Quotes Slowness issues for a Linked Documents tab-->
                    <p:column id="col1" headerText="#{custom.labels.get('IDS_JOB')} Name"  filterMatchMode="contains" filterBy="#{viewjobs.jobDescr}" sortBy="#{viewjobs.jobDescr}">
                        <!--23-01-2024 12761 CRM-7599   OASIS: Surface Project Rank for Quotes-->
                        <p:commandLink value="#{viewjobs.jobDescr}" actionListener="#{jobs.onQuotJobRowSelect(quotesHdr.recId,viewjobs.jobId)}" action="#{quotesHdr.setQuotJob(viewjobs.jobId)}" oncomplete="refreshQuoteRank();PF('dlgQuotJobList').hide();linkedDocsCmd();"  update=":quotefrom">
                        </p:commandLink>
                    </p:column>
                    <p:column  id="col2"  headerText="Job Owner" filterMatchMode="contains" filterBy="#{viewjobs.jobOwnerName}" sortBy="#{viewjobs.jobOwnerName}">
                        <!--23-01-2024 12761 CRM-7599   OASIS: Surface Project Rank for Quotes-->
                        <p:commandLink value="#{viewjobs.jobOwnerName}" actionListener="#{jobs.onQuotJobRowSelect(quotesHdr.recId,viewjobs.jobId)}" action="#{quotesHdr.setQuotJob(viewjobs.jobId)}" oncomplete="refreshQuoteRank();PF('dlgQuotJobList').hide();linkedDocsCmd();"  update=":quotefrom">
                        </p:commandLink>
                    </p:column>
                    <p:column  id="col3"  headerText="Address" filterMatchMode="contains" filterBy="#{viewjobs.jobFormattedAddr}" sortBy="#{viewjobs.jobFormattedAddr}">
                        <!--23-01-2024 12761 CRM-7599   OASIS: Surface Project Rank for Quotes-->
                        <p:commandLink value="#{viewjobs.jobFormattedAddr}" actionListener="#{jobs.onQuotJobRowSelect(quotesHdr.recId,viewjobs.jobId)}" action="#{quotesHdr.setQuotJob(viewjobs.jobId)}" oncomplete="refreshQuoteRank();PF('dlgQuotJobList').hide();linkedDocsCmd();"  update=":quotefrom">
                        </p:commandLink>
                    </p:column>
                    <p:column  id="col4"  headerText="#{custom.labels.get('IDS_JOB_STAGE')}" filterMatchMode="contains" filterBy="#{viewjobs.jobActivityName}" sortBy="#{viewjobs.jobActivityName}">
                        <!--23-01-2024 12761 CRM-7599   OASIS: Surface Project Rank for Quotes-->
                        <p:commandLink value="#{viewjobs.jobActivityName}" actionListener="#{jobs.onQuotJobRowSelect(quotesHdr.recId,viewjobs.jobId)}" action="#{quotesHdr.setQuotJob(viewjobs.jobId)}" oncomplete="refreshQuoteRank();PF('dlgQuotJobList').hide();linkedDocsCmd();"  update=":quotefrom">
                        </p:commandLink>
                    </p:column>
                    <p:column  id="col5"  headerText="Awarded To" filterMatchMode="contains" filterBy="#{viewjobs.jobAwardedToName}" sortBy="#{viewjobs.jobAwardedToName}">
                        <!--23-01-2024 12761 CRM-7599   OASIS: Surface Project Rank for Quotes-->
                        <p:commandLink value="#{viewjobs.jobAwardedToName}" actionListener="#{jobs.onQuotJobRowSelect(quotesHdr.recId,viewjobs.jobId)}" action="#{quotesHdr.setQuotJob(viewjobs.jobId)}" oncomplete="refreshQuoteRank();PF('dlgQuotJobList').hide();linkedDocsCmd();"  update=":quotefrom">
                        </p:commandLink>
                    </p:column>
                    <p:column  id="col6"  headerText="Awarded Date" filterMatchMode="contains" filterBy="#{viewjobs.jobAwardedDate}" sortBy="#{viewjobs.jobAwardedDate}">
<!--                        <p:commandLink value="#{viewjobs.jobAwardedDate}" actionListener="#{jobs.onQuotJobRowSelect(quotesHdr.recId,viewjobs.jobId)}" action="#{quotesHdr.setQuotJob(viewjobs.jobId)}" oncomplete="PF('dlgQuotJobList').hide()"  update=":quotefrom">
                        </p:commandLink>-->
                        <p:outputLabel  value="#{viewjobs.jobAwardedDate}"  >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                        </p:outputLabel> 
                    </p:column>
                    <p:column  id="col7"  headerText="Last Modified date" filterMatchMode="contains" filterBy="#{viewjobs.updDate}" sortBy="#{viewjobs.updDate}">
<!--                        <p:commandLink value="#{viewjobs.updDate}" actionListener="#{jobs.onQuotJobRowSelect(quotesHdr.recId,viewjobs.jobId)}" action="#{quotesHdr.setQuotJob(viewjobs.jobId)}" oncomplete="PF('dlgQuotJobList').hide()"  update=":quotefrom">
                            <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                        </p:commandLink>-->

                        <p:outputLabel  value="#{rFUtilities.convertFromUTC(viewjobs.updDate)}"  >
                            <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                        </p:outputLabel>   
                    </p:column>
                    <p:column  id="col8"  headerText="Created date" filterMatchMode="contains" filterBy="#{viewjobs.insDate}" sortBy="#{viewjobs.insDate}">
<!--                        <p:commandLink value="#{viewjobs.insDate}" actionListener="#{jobs.onQuotJobRowSelect(quotesHdr.recId,viewjobs.jobId)}" action="#{quotesHdr.setQuotJob(viewjobs.jobId)}" oncomplete="PF('dlgQuotJobList').hide()"  update=":quotefrom">
                            <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                        </p:commandLink> -->
                        <p:outputLabel  value="#{rFUtilities.convertFromUTC(viewjobs.insDate)}"  >
                            <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                        </p:outputLabel> 
                    </p:column>
                    <!--                    Last Modified / Created date
                                        <p:column  id="col2"  headerText="#{custom.labels.get('IDS_JOB')} Type" filterMatchMode="contains" filterBy="#{viewjobs.jobTypeName}" sortBy="#{viewjobs.jobTypeName}">
                                            <p:outputLabel value="#{quotesHdr.recId}"/>         #{viewjobs.jobId}
                    
                                        </p:column>
                                        <p:column  id="col2"  headerText="#{custom.labels.get('IDS_JOB')} Type" filterMatchMode="contains" filterBy="#{viewjobs.jobTypeName}" sortBy="#{viewjobs.jobTypeName}">
                                            <p:outputLabel value="#{quotesHdr.recId}"/>         #{viewjobs.jobId}
                    
                                        </p:column>-->
                    <!--                    <p:column  id="col2"  headerText="#{custom.labels.get('IDS_JOB')} Type" filterMatchMode="contains" filterBy="#{viewjobs.jobTypeName}" sortBy="#{viewjobs.jobTypeName}">
                                            <p:outputLabel value="#{quotesHdr.recId}"/>         #{viewjobs.jobId}
                                           
                                        </p:column>
                    
                                        <p:column id="col3"  headerText="Job Specifier" filterMatchMode="contains"  filterBy="#{viewjobs.jobSpecifierName}" sortBy="#{viewjobs.jobSpecifierName}"
                                                  style="max-width: 100px;
                                                  padding: 5px 2px 1px 2px !important">
                                            <p:outputLabel value="#{viewjobs.jobSpecifierName}"/>
                                        </p:column>
                    
                                        <p:column id="col4"  headerText="Activity Stage" filterMatchMode="contains" filterBy="#{viewjobs.jobActivityName}" sortBy="#{viewjobs.jobActivityName}">
                                            <p:outputLabel value="#{viewjobs.jobActivityName}"/>
                                        </p:column>
                    
                                        <p:column id="col5"  headerText="Value" filterMatchMode="contains" filterBy="#{viewjobs.jobvalue}" sortBy="#{viewjobs.jobvalue}">
                                            <p:outputLabel value="#{viewjobs.jobvalue}"/>
                                        </p:column>
                    
                                        <p:column id="col6"  headerText="Status" filterMatchMode="contains" filterBy="#{viewjobs.jobStatus}" sortBy="#{viewjobs.jobStatus}">
                                            <p:outputLabel value="#{viewjobs.jobStatus}"/>
                                        </p:column>-->
                </p:dataTable>
            </h:form>
        </p:dialog>
    </div>
</ui:composition>