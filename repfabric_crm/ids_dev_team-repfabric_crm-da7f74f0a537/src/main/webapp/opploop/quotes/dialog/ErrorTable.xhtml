<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <!--<p:panelGrid rendered="#{quotesDtl.statusList.size()>0}">-->
    <!--#3085 Quotes - Line Items - Import-->
    <!--#3708 Quotes> Line Items -Import - Updates in template-->
    <p:dataTable value="#{quotesDtl.statusList}" rendered="#{quotesDtl.statusList.size()>0}"
                 var="errRec" id="er" rowIndexVar="erIndex"
                 emptyMessage="No errors" 
                 scrollable="true"
                 scrollHeight="350" filteredValue="#{quotesDtl.statusList}"
                 widgetVar="errRec">
        <p:column  width="2%"> 
            <f:facet name="header"></f:facet>
                #{erIndex+1}
        </p:column>
        <p:column  width="2%"> 
            <f:facet name="header"></f:facet>
            <p:graphicImage name="/images/greenFlag.png" width="12" rendered="#{errRec[6]=='Success'}" title="#{errRec[7]}"/>
            <p:graphicImage name="/images/redFlag.png" width="12" rendered="#{errRec[6]=='Failed'}" title="#{errRec[7]}"/>
        </p:column>
        <p:column width="15%">
             <!--#11212 CRM-6918: Product: Set pricing per hundred in template-->
            <f:facet name="header">Qty (in units)</f:facet>
            <h:outputText value="#{errRec[0]}"/>
        </p:column>
          <!--//#11212 CRM-6918: Product: Set pricing per hundred in template-->
        <p:column width="15%">
            <f:facet name="header">Part #</f:facet>
            <h:outputText value="#{errRec[1]}"/>
        </p:column>
           <!--//#11212 CRM-6918: Product: Set pricing per hundred in template-->
        <p:column width="15%">
            <f:facet name="header">Description</f:facet>
            <h:outputText value="#{errRec[2]}"/>
        </p:column>
            <!--//#11212 CRM-6918: Product: Set pricing per hundred in template-->
        <p:column width="15%">
            <f:facet name="header">Unit Price</f:facet>
            <h:outputText value="#{errRec[3]}"/>
        </p:column>
             <!--//#11212 CRM-6918: Product: Set pricing per hundred in template-->
        <p:column width="10%">
             <!--#11212 CRM-6918: Product: Set pricing per hundred in template-->
            <f:facet name="header">Pricing UOM</f:facet>
            <h:outputText value="#{errRec[4]}"/>
        </p:column>
              <!--//#11212 CRM-6918: Product: Set pricing per hundred in template-->
        <p:column width="15%">
            <f:facet name="header">Extended Price</f:facet>
            <h:outputText value="#{errRec[5]}"/>
        </p:column> 
<!--        Task#3543-CRM-3924: quotes: need ability to import multiple manufacturer quote   :24-02-2021 by harshithad-->
        <p:column width="20%" rendered="#{quotesHdr.isSuperQuote==true}">
            <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet>
            <h:outputText value="#{errRec[8]}"/>
        </p:column> 
        <p:column width="30%">
            <f:facet name="header">Details</f:facet>
            <h:outputText value="#{errRec[7]}"/>
        </p:column>
    </p:dataTable>

    <!--</p:panelGrid>-->
</ui:composition>

