<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"> 
    <!--08-02-2023 10506 CRMSYNC-112/141: AutoQuotes: Quotes: Sync Status-->
    <h:form id="frmAurComps">
        <p:dialog header="Target System Account Lookup" modal="true" width="1200" id="iddlgAurComp" widgetVar="dlgAurComp" 
                  closeOnEscape="true" responsive="true" onShow="PF('dlgAurComp').initPosition();" height="550">
            <f:facet name ="header">
                <p:outputPanel id="opLookupHead">
                    Target System Account Lookup 
                    <p:spacer width="750" height="0"/>
                    <!--05-09-2023 11857 ESCALATION CRMSYNC-359 Sync Logic. Quote. RF UI > CRMSync tab - Options to add/link/sync/view-->
                    <p:commandButton id="btnNewAurComp" value="New" title="Add new company in target system" 
                                     styleClass="btn btn-primary btn-xs" immediate="true"
                                     actionListener="#{aurinkoCompService.onclickNewAurCompany()}"
                                     update=":frmNewAurCompLokup" oncomplete="PF('dlgNewAurComp').show();"/> 

                </p:outputPanel>
            </f:facet>
            <p:outputPanel id="opLookupData" >

                <p:remoteCommand id="rcAurCompSetPage" name="rcAurCompSetPage"  autoRun="false"
                                 oncomplete="PF('wvDtAurinkoComp').paginator.setPage(PF('wvDtAurinkoComp').paginator.cfg.pageCount-1);" />

                <h:panelGroup id="pgAurCompFlt">
                    <div class="ui-g ui-fluid">

                        <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                            <h:panelGroup style="width: 100%">
                                <p:outputLabel value="Name: " />
                                <p:spacer width="8"/>
                                <p:inputText value="#{aurinkoCompService.nameFilter}"
                                             style="width: 70%"  />
                            </h:panelGroup>
                        </div>

                        <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                            <h:panelGroup style="width: 100%">

                                <p:outputLabel value="City: " />
                                <p:spacer width="8"/>
                                <p:inputText value="#{aurinkoCompService.cityFilter}"
                                             style="width: 70%" />  
                            </h:panelGroup>
                        </div>

                        <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                            <h:panelGroup style="width: 100%">
                                <p:outputLabel value="Zip: " />
                                <p:spacer width="8"/>
                                <p:inputText value="#{aurinkoCompService.zipFilter}"
                                             style="width: 70%" />  
                            </h:panelGroup>
                        </div>

                        <div class="ui-sm-12 ui-md-1 ui-lg-1">

                            <p:commandButton value="Filter" 
                                             class="btn btn-primary btn-xs"   
                                             title="Fetch records"
                                             onclick="PF('dlgFetchRec').show();"
                                             actionListener="#{aurinkoCompService.ppltAurinkoComps(aurinkoCompService.compPrinciId, null)}"
                                             update=":frmAurComps:dtAurinkoComp :frmAurComps:pgAurCompFlt"
                                             oncomplete="PF('dlgFetchRec').hide();PF('wvDtAurinkoComp').clearFilters()" />
                        </div>
                        <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                            <p:commandButton value="Clear" 
                                             class="btn btn-primary btn-xs" rendered="#{aurinkoCompService.fetchedAurCompanies}"
                                             title="Clear everything"
                                             actionListener="#{aurinkoCompService.clearAurinkoCompLookup()}"
                                             update=":frmAurComps:dtAurinkoComp :frmAurComps:pgAurCompFlt"
                                             oncomplete="PF('wvDtAurinkoComp').clearFilters()" />
                        </div>
                        <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                            <p:commandButton value="Load More" onclick="PF('dlgFetchRec').show()"
                                             class="btn btn-success btn-xs" rendered="#{not empty aurinkoCompService.nextPage}"
                                             action="#{aurinkoCompService.fetchAurinkoComps(aurinkoCompService.compPrinciId,aurinkoCompService.nextPage)}"
                                             oncomplete="PF('wvDtAurinkoComp').filter();PF('dlgFetchRec').hide();rcAurCompSetPage();" 
                                             update=":frmAurComps:pgAurCompFlt :frmAurComps:dtAurinkoComp"/>
                        </div>
                    </div>
                </h:panelGroup>
                <p:dataTable id="dtAurinkoComp" widgetVar="wvDtAurinkoComp"
                             scrollHeight="320"
                             scrollable="true"
                             value="#{aurinkoCompService.aurCompanies}"                         
                             filterEvent="keyup"
                             filteredValue="#{aurinkoCompService.aurCompaniesFlt}"
                             var="comp"
                             emptyMessage="No Account found"
                             multiViewState="true"
                             selection="#{aurinkoCompService.selectedAurinkoComp}"
                             selectionMode="single"
                             rowKey="#{comp.tsCompId}"
                             paginator="true" paginatorAlwaysVisible="true"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rowsPerPageTemplate="100">
                    <!--21-08-2023 11857 CRMSYNC-359 Sync Logic. Quote. RF UI > CRMSync tab - Options to add/link/sync/view-->
                    <p:ajax event="rowSelect" onstart="#{aurinkoCompService.remoteCommand}();"
                            oncomplete="PF('dlgAurComp').hide();"/>

                    <p:column visible="false" rendered="false" headerText="ID">
                        #{comp.tsCompId}
                    </p:column>
                    <p:column width="25" headerText="Company" filterBy="#{comp.tsCompName}" filterMatchMode="contains" sortBy="#{comp.tsCompName}" >
                        #{comp.tsCompName}
                    </p:column>
                    <p:column width="12" headerText="Type" filterBy="#{comp.aurType}" filterMatchMode="contains" sortBy="#{comp.aurType}"  >
                        #{comp.aurType}
                    </p:column>
                    <p:column width="12" headerText="Phone" filterBy="#{comp.phone}" filterMatchMode="contains" sortBy="#{comp.phone}"  >
                        #{comp.phone}
                    </p:column>
                    <p:column width="12" headerText="City" filterBy="#{comp.city}" filterMatchMode="contains" sortBy="#{comp.city}"  >
                        #{comp.city}
                    </p:column>
                    <p:column width="12" headerText="State" filterBy="#{comp.state}" filterMatchMode="contains" sortBy="#{comp.state}"  >
                        #{comp.state}
                    </p:column>
                    <p:column width="12" headerText="Zip Code" filterBy="#{comp.postalCode}" filterMatchMode="contains" sortBy="#{comp.postalCode}"  >
                        #{comp.postalCode}
                    </p:column>
                    <p:column width="12" headerText="Website" filterBy="#{comp.website}" filterMatchMode="contains" sortBy="#{comp.website}"  >
                        #{comp.website}
                    </p:column>
                    <p:column width="25" headerText="Last Modified Time" filterBy="#{comp.updatedAt}" filterMatchMode="contains" sortBy="#{comp.updatedAt}"  >
                        #{comp.updatedAt}
                    </p:column>
                </p:dataTable>
            </p:outputPanel>
        </p:dialog>
    </h:form>
 
    <ui:include src="NewAurComp.xhtml"/>
    <style>
        .ui-paginator {
            background-color:#ffffff !important;
        }
    </style>
</ui:composition> 
