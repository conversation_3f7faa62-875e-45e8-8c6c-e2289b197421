<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <!--Feature #5564:CRM-3319: Quote to PO comm trans downflow 2/09/21 by harshithad-->
    <!--Feature #11571: CRM-7094: Quotes: copy into purchase orders by harshithad on 03/07/23-->
    <p:dialog id="poGenDlg" header="PO Generation" 
              widgetVar="dlgGenpo" responsive="true" height="400" styleClass="disable-scroll"
              width="1000"  
              >
        <h:form id="poGenerateFrm">
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--Feature #11571: CRM-7094: Quotes: copy into purchase orders by harshithad on 03/07/23-->
                    <p:outputLabel id="otptLblPrnci" styleClass="required" value="#{custom.labels.get('IDS_PRINCI')}" rendered="#{quotesHdr.isSuperQuote}"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:selectOneMenu  id="oneMenuPrinci" value="#{quotesHdr.qtDtlPrinciId}"  style="margin-top:-1px;width:100%" rendered="#{quotesHdr.isSuperQuote}" filter="true" filterMatchMode="contains">
                        <f:selectItem itemLabel="Select #{custom.labels.get('IDS_PRINCI')}" itemValue="0" />
                        <f:selectItems var="vDtl" value="#{quotesHdr.principalsMap}"  />

                        <p:ajax listener="#{quotesHdr.onSelectPrinci()}" update="poGenerateFrm:dtQuoteLI poGenerateFrm:inptTxtComRte" />
                    </p:selectOneMenu>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6"></div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel id="otptLblQtNo" value="Quote No"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:inputText id="inptTxtqtNo" value="#{quotesHdr.quotNumber}" readonly="true"/>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel id="otptLblQtDt" value="Quote Date"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:calendar id="calQtDte" value="#{quotesHdr.quotDate}"   pattern="#{globalParams.dateFormat}"  readonly="#{quotesHdr.editable}" >

                        <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                    </p:calendar>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel id="otptLblPoNo" value="PO No" styleClass="required"  />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:inputText id="inptTxtPoNo" value="#{quotesHdr.poNum}"   />
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel id="otptLblPodte" value="PO Date" styleClass="required"  />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:calendar id="calPOdte" value="#{quotesHdr.poDate}"  pattern="#{globalParams.dateFormat}" converterMessage="Please enter valid date" >
                        <p:ajax event="dateSelect" process="@this" />
                    </p:calendar>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel id="otptLblSoNo" value="SO No"  />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:inputText id="inptTxtSoNum" value="#{quotesHdr.soNum}" />
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel id="otptLblSoDte" value="#{custom.labels.get('IDS_SO_DATE')}"  />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:calendar id="calSoDte" value="#{quotesHdr.soDate}" pattern="#{globalParams.dateFormat}" converterMessage="Please enter valid date">

                        <p:ajax event="dateSelect" process="@this" />
                    </p:calendar>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                    <p:outputLabel id="otptLblCommRte" value="#{custom.labels.get('IDS_COMM')} Rate"  />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:inputText id="inptTxtComRte" value="#{quotesHdr.poCommRate}" placeholder="0.00"/>
                </div>
            </div>

            <p:spacer height="4px"/>
            <!--Feature #11571: CRM-7094: Quotes: copy into purchase orders by harshithad on 03/07/23-->
            <p:outputPanel id="otptPnlInputs" style="position: relative;height:200px;overflow: auto">
                <!--Feature #11571: CRM-7094: Quotes: copy into purchase orders by harshithad on 03/07/23-->
           <!--Jira Task #14114: CRM-8165 : PO from Quote: allow user to select line items (not ALL)-->
                <p:dataTable id="dtQuoteLI" widgetVar="quotDt" selection="#{quotesHdr.selectedQuotLineItem}"     rowSelectMode="add"  var="quotLitem" rowKey="#{quotLitem.recId}"  value="#{quotesHdr.quotLineItems}"   paginator="true"  rows="10"  tableStyle="table-layout:auto" resizableColumns="true" emptyMessage="No #{custom.labels.get('IDS_LINE_ITEMS')} found" >
                    <p:ajax event="rowSelectCheckbox"  update="poGenerateFrm:cmdBtnGnrte" />
                    <p:ajax event="rowUnselectCheckbox"   update="poGenerateFrm:cmdBtnGnrte" />
                    <p:ajax event="toggleSelect"   update="poGenerateFrm:cmdBtnGnrte" />
                    <p:ajax event="rowSelect" update="poGenerateFrm:cmdBtnGnrte"/>
                    <p:ajax event="rowUnselect"  update="poGenerateFrm:cmdBtnGnrte"/>
                    <p:column  selectionMode="multiple" style="text-align: center">
                    </p:column>

                    <p:column id="colPrtNo" headerText="#{custom.labels.get('IDS_PART_NUM')}" filterBy="#{quotLitem.oppItemPartManf}" sortBy="#{quotLitem.oppItemPartManf}"  filterMatchMode="contains">
                        #{quotLitem.oppItemPartManf}
                    </p:column>
                    <p:column id="colQty" headerText="Qty" sortBy="#{quotLitem.oppItemQnty}" filterBy="#{quotLitem.oppItemQnty}" filterMatchMode="contains">
                        #{quotLitem.oppItemQnty}
                    </p:column>
                    <p:column id="colStdPrc" headerText="#{custom.labels.get('IDS_STD_PRICE')}" filterBy="#{quotLitem.quotStdRate}" sortBy="#{quotLitem.quotStdRate}"   filterMatchMode="contains">
                        #{quotLitem.quotStdRate}
                    </p:column>
                    <p:column id="colMltplr" headerText="Multiplier" filterBy="#{quotLitem.quotMultiplier}" sortBy="#{quotLitem.quotMultiplier}"  filterMatchMode="contains">
                        #{quotLitem.quotMultiplier}
                    </p:column> <p:column id="colUnitPrc" headerText="#{custom.labels.get('IDS_UNIT_PRICE')}" filterBy="#{quotLitem.quotResale}" sortBy="#{quotLitem.quotResale}"  filterMatchMode="contains">
                        #{quotLitem.quotResale}
                    </p:column> <p:column id="colExtPrc" headerText="Extended Price" sortBy="#{quotLitem.quotExtPrice}" filterBy="#{quotLitem.quotExtPrice}" filterMatchMode="contains">
                        #{quotLitem.quotExtPrice}
                    </p:column> 
                </p:dataTable>
                <!--Feature #11571: CRM-7094: Quotes: copy into purchase orders by harshithad on 03/07/23-->
            </p:outputPanel>
            <p:spacer height="4px"/>
            <div style="text-align: center">

                <!--Feature #5747:CRM-4725: Want to UNLINK a quote from an oppty          by harshithad  11/09/2021-->
                <!--Feature #11571: CRM-7094: Quotes: copy into purchase orders by harshithad on 03/07/23-->
<!--              Jira Task #14114: CRM-8165 : PO from Quote: allow user to select line items (not ALL) -->
<p:commandButton id ="cmdBtnGnrte" actionListener="#{quotesHdr.generatePo(0)}" disabled="#{quotesHdr.saveButtonFlag or ((quotesHdr.quotLineItems.size()==0 and quotesHdr.isSuperQuote ) or (quotesHdr.quotLineItems.size()>0 ) and quotesHdr.selectedQuotLineItem.size()==0)}" update="poGenerateFrm:cmdBtnGnrte  quotefrom:tabQuot:lnQtOppMstList poGenerateFrm"
                                 oncomplete="PF('lnQtOppTable').clearFilters();" value="Generate" styleClass="btn btn-primary btn-xs"  />
                <p:spacer width="4px"/>
                <!--Feature #11571: CRM-7094: Quotes: copy into purchase orders by harshithad on 03/07/23-->
                <p:commandButton id="cmdBtnCancl" value="Cancel" styleClass="btn btn-warning btn-xs" oncomplete="PF('dlgGenpo').hide();"/>
            </div>
        </h:form>
    </p:dialog>
    <!--Feature #11571: CRM-7094: Quotes: copy into purchase orders by harshithad on 03/07/23-->
    <style>

        .disable-scroll .ui-dialog-content {

            overflow: hidden !important;
        }

    </style>
</ui:composition>


