<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--11-08-2023 11857 CRMSYNC-359 Sync Logic. Quote. RF UI-->
    <h:form id="frmQuotMaster" >
        <p:dialog id="dlgIdQuoteMaster" widgetVar="dlgQuoteMaster" width="600" closable="false" 
                  modal="true" header="Choose master data as" resizable="false" >
            <h:panelGroup id="pgQuotMaster">

                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                        <p:outputLabel value="#{custom.labels.get('IDS_QUOTES')} Date:" />
                    </div>

                    <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                        <p:outputLabel value="Repfabric #{custom.labels.get('IDS_QUOTES')}"/>
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8 ">
                        <p:outputLabel value="#{quotesHdr.quotDate}"/>
                    </div>

                    <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                        <p:outputLabel value="RepfabricMfg #{custom.labels.get('IDS_QUOTES')}"/>
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8 ">
                        <p:outputLabel value=" #{quoteSyncService.targetSystemQuote.quoteDate}"/>
                    </div>

                    <div class="ui-sm-12 ui-md-12 ui-lg-12 "/>
                    <div class="ui-sm-12 ui-md-12 ui-lg-12 "/>

                    <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                        <p:outputLabel value="Choose master data as:"/>
                    </div>

                    <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                        <p:selectOneRadio id="radioIntMasterSel" value="#{quoteSyncService.targetSystemQuote.master}" 
                                          style="width:100%;" layout="lineDirection" >
                            <f:selectItem itemLabel="Repfabric #{custom.labels.get('IDS_QUOTES')}" itemValue="0"/>
                            <f:selectItem itemLabel="Target System #{custom.labels.get('IDS_QUOTES')}" itemValue="1"/>
                            <f:ajax execute="@this" />
                        </p:selectOneRadio>
                    </div>
                </div> 

                <br/>
                <div style="text-align: center">
                    <p:commandButton value="Ok" class="btn-primary btn-xs"
                                     onclick="PF('wvDlgTargetSystemQuote').hide();PF('dlgProcessingOnTask').show();" 
                                     actionListener="#{quoteSyncService.linkQuoteFromQuoteDetails(quotesHdr.recId,quoteSyncService.targetSystemQuote.extId,quoteSyncService.targetSystemQuote.aurinkoId,quoteSyncService.targetSystemQuote.master)}"
                                     update=":quotefrom:tabQuot:pnlCRMSYNC"
                                     oncomplete="PF('dlgProcessingOnTask').hide();PF('dlgQuoteMaster').hide();"  />
                </div>
            </h:panelGroup>
        </p:dialog> 
    </h:form>

</ui:composition>
