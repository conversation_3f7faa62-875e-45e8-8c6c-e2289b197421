
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui">
    <!--        //08-03-2024 : #13280 : CRM-7791   Quotes: Link multiple PO-->
    <p:dialog id="dlgQtLinkSuperPo" widgetVar="quotLinkSuperPoDlg"  modal="true" header="#{custom.labels.get('IDS_PURCHASE_ORDER')} List"
              maximizable="true" closeOnEscape="true" height="600" width="95%">
        <h:form id="frmQuotLinkSuperPo">
            <p:growl id="msg22" showDetail="false"/>
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="#{custom.labels.get('IDS_PRINCI')}" />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                   <!--#13603 Thea threatening to cancel due to application's slow performance-->
                    <p:selectOneMenu style="width: 100%"  id="sprQtPrinciPo" value="#{quotesHdr.selectPrinciPo}"  >
                        <f:selectItem itemLabel="Select #{custom.labels.get('IDS_PRINCI')}" itemValue="0" />
                        <f:selectItems value="#{quotesHdr.quotLineItemLinkPoList}"  
                                       var="quot"   
                                       itemValue="#{quot.quotPrincipal}"
                                       itemLabel="#{quot.princiName}"/>
                        <p:ajax process="@this" listener="#{quotesHdr.loadSuperPoList(quotesHdr.oppCustomer,quotesHdr.recId)}" update=":frmQuotLinkSuperPo:tblQtLinkPoPanel :frmQuotLinkSuperPo:btnQuotLinkPo" oncomplete="PF('quotLinkPoListTable').clearFilters()"/>
                       </p:selectOneMenu>
                </div>
                <div class="ui-sm-12 ui-md-3 ui-lg-3"> 
                    <p:commandButton disabled="#{quotesHdr.quotLinkPoList.size()==0 or quotesHdr.selectPrinciPo == 0}" id="btnQuotLinkPo"  
                                     class="btn btn-primary btn-xs" action="#{quotesHdr.selectQuotLinkToPo(quotesHdr.recId)}" 
                                     value="Link to #{custom.labels.get('IDS_PURCHASE_ORDER')}"  
                                     title="Link to #{custom.labels.get('IDS_PURCHASE_ORDER')}"/>
                    <br/> 
                </div>
                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                     
                </div>
            </div>
            <br/> 
            <p:outputPanel id="tblQtLinkPoPanel">
            <p:dataTable id="dtQtLinkPoList" rendered="#{quotesHdr.selectPrinciPo != null and quotesHdr.selectPrinciPo != 0}" widgetVar="quotLinkPoListTable" 
                         value="#{quotesHdr.quotLinkPoList}" var = "po" multiViewState="true" rowKey="#{po.recId}" selection="#{quotesHdr.quotLinkPoFilter}" 
                         emptyMessage="No open #{custom.labels.get('IDS_PURCHASE_ORDER')} found." paginator="true" rows="10" style="width:100%;"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="5,10,15" paginatorAlwaysVisible="false" class="tblmain" scrollable="true" scrollHeight="300">
                            
                          
                <p:column selectionMode="multiple" style="width: 40px;text-align: center; " >                             
                            </p:column>  
                <p:column filterBy="#{po.poCustName}" id="custName" filterMatchMode="contains" sortBy="#{po.poCustName}" >
                    <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet> 
                        #{po.poCustName}
                </p:column>
                <p:column filterBy="#{po.poPrinciName}" id="principalName" filterMatchMode="contains" sortBy="#{po.poPrinciName}">
                    <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet> 
                        #{po.poPrinciName}
                </p:column> 
                <p:column filterBy="#{po.poDistriName}" id="distriName" filterMatchMode="contains" sortBy="#{po.poDistriName}" >
                    <f:facet name="header">#{custom.labels.get('IDS_DISTRI')}</f:facet> 
                        #{po.poDistriName}
                </p:column>

                <p:column filterBy="#{po.poProgram}" filterMatchMode="contains" sortBy="#{po.poProgram}">
                    <f:facet name="header">#{custom.labels.get('IDS_PROGRAM')}</f:facet> 
                        #{po.poProgram}
                </p:column> 

                <p:column filterBy="#{po.poNumber}" filterMatchMode="contains" sortBy="#{po.poNumber}">
                    <f:facet name="header">PO Number</f:facet>  
                        #{po.poNumber}
                </p:column>

                <p:column filterBy="#{po.poStatus}" filterMatchMode="contains" sortBy="#{po.poStatus}">
                    <f:facet name="header">Status</f:facet>  
                        #{po.poStatus}
                </p:column>

                <p:column filterBy="#{oppLinkedDocumentsService.getFormattedDate(po.poFollowUpDate,'da')}" filterMatchMode="contains" headerText="Follow Up Date" sortBy="#{po.poFollowUpDate}">
                    <p:outputLabel value="#{oppLinkedDocumentsService.getFormattedDate(po.poFollowUpDate,'da')}" >
                    </p:outputLabel>
                </p:column>
            </p:dataTable>
                </p:outputPanel>
        </h:form>
    </p:dialog>



</ui:composition>


