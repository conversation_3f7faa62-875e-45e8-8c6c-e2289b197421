<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <!--- 8486 : Task:14467 added new feature of Quote History in Quotes Detail--> 
    <h:form id="formQuotHistory"  >
        <p:dialog id="quotHistoryDlg" header="Quote History" widgetVar="quotHistoryDlg" width="1200" height="500">
            <p:outputPanel id="opQuotHistory">

                <p:dataTable  value="#{quotHistoryService.listQuoteHistory}" 
                              var="history" id="dtHistory" class="hide-column-names"
                              rowStyleClass="tbl-row" 

                              >
                    <p:column style="height: auto"> 
                        <div style="margin-bottom: -10px;">
                            <h:outputLabel  class="header-label" value="#{quotHistoryService.getFormattedDate(history.updDate,'dt')} " styleClass="header-label" style="color:black!important;">
                                <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                            </h:outputLabel>
                            <p:spacer width="10"/>
                            <h:outputLabel value="Quote Updated by #{history.updUserName}" styleClass="header-label" />
                        </div>
                        <br/>

                        <table>
                            <tr>    
                                
                                <h:outputLabel   value="#{custom.labels.get('IDS_PRINCI')} : " rendered="#{not empty history.oppPrincipalName}" styleClass="opp-label" />
                                <p:spacer width="6px" />
                                <h:outputLabel   value=" #{history.oppPrincipalName} " rendered="#{not empty history.oppPrincipalName}" styleClass="opp-value" />
                            </tr>
                            <tr>
                                <p:spacer width="10px"  rendered="#{not empty history.oppPrincipalName}" />
                                <h:outputLabel value="Follow Up : " rendered="#{not empty history.quotFollowUp}" styleClass="opp-label" />
                                <p:spacer width="6px" rendered="#{not empty history.quotFollowUp}" />
                                <h:outputLabel value="#{history.quotFollowUp}" rendered="#{not empty history.quotFollowUp}" styleClass="opp-value" />

                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotFollowUp}" styleClass="opp-value"/>
                                <h:outputLabel   value="#{custom.labels.get('IDS_CUSTOMER')} Ref.No : " rendered="#{not empty history.quotCustRefNum}" styleClass="opp-label"/> 
                                <p:spacer width="6px" rendered="#{not empty history.quotCustRefNum}"/>
                                <h:outputLabel   value="#{history.quotCustRefNum} " rendered="#{not empty history.quotCustRefNum}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotCustRefNum}"/>              
                                <h:outputLabel   value="Recipient : " rendered="#{not empty history.quotReceipient}"  styleClass="opp-label"/>  
                                <p:spacer width="6px" rendered="#{not empty history.quotReceipient}"/>
                                <h:outputLabel   value="#{history.quotReceipient} " rendered="#{not empty history.quotReceipient}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotReceipient}"/>              
                                <h:outputLabel   value="Quote form comments : " rendered="#{not empty history.quotComments}" styleClass="opp-label"/> 
                                <p:spacer width="6px" rendered="#{not empty history.quotComments}"/>
                                <h:outputLabel   value="#{history.quotComments} " rendered="#{not empty history.quotComments}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotComments}"/>              
                                <h:outputLabel   value="Bid/Buy : " rendered="#{not empty history.quotBidBuy}" styleClass="opp-label"/>  
                                <p:spacer width="6px" rendered="#{not empty history.quotBidBuy}" />
                                <h:outputLabel   value="#{history.quotBidBuy} " rendered="#{not empty history.quotBidBuy}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotBidBuy}"/>              
                                <h:outputLabel   value="#{custom.labels.get('IDS_PROGRAM')} : " rendered="#{not empty history.oppCustProgram}" styleClass="opp-label"/>  
                                <p:spacer width="6px" rendered="#{not empty history.oppCustProgram}"/>
                                <h:outputLabel   value="#{history.oppCustProgram} " rendered="#{not empty history.oppCustProgram}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.oppCustProgram}"/> 

                                <h:outputLabel   value="#{custom.labels.get('IDS_CUSTOMER')} Contact : "  rendered="#{not empty history.quotCustomerCont}" styleClass="opp-label"/>  
                                <p:spacer width="6px" rendered="#{not empty history.quotCustomerCont}"/>
                                <h:outputLabel   value="#{history.quotCustomerCont} " rendered="#{not empty history.quotCustomerCont}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotCustomerCont}"/>              
                                <h:outputLabel   value="Application : " rendered="#{not empty history.quotApplication}" styleClass="opp-label" />  
                                <p:spacer width="6px" rendered="#{not empty history.quotApplication}"/>
                                <h:outputLabel   value="#{history.quotApplication} " rendered="#{not empty history.quotApplication}" styleClass="opp-value" />  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotApplication}"/>   
                                <h:outputLabel   value="#{custom.labels.get('IDS_DISTRI')} : "  rendered="#{not empty history.crmTSQuotDistriId}" styleClass="opp-label"/>                    
                                <p:spacer width="6px" rendered="#{not empty history.crmTSQuotDistriId}"/>
                                <h:outputLabel   value="#{history.crmTSQuotDistriId} " rendered="#{not empty history.crmTSQuotDistriId}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.crmTSQuotDistriId}"/>              

                                <h:outputLabel   value=" Status : " rendered="#{not empty history.quotOpenStatus}" styleClass="opp-label"/> 
                                <p:spacer width="6px" rendered="#{not empty history.quotOpenStatus}"/>    
                                <h:outputLabel   value="#{history.quotOpenStatus} " rendered="#{not empty history.quotOpenStatus}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10" rendered="#{not empty history.quotOpenStatus}"/>              

                                <h:outputLabel   value="Quote Number : " rendered="#{not empty history.quotNumber}" styleClass="opp-label"/>  
                                <p:spacer width="6px" rendered="#{not empty history.quotNumber}"/> 
                                <h:outputLabel   value="#{history.quotNumber} " rendered="#{not empty history.quotNumber}" styleClass="opp-value"/>
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotNumber}"/>              

                                <h:outputLabel   value="#{custom.labels.get('IDS_SECOND_CUSTOMER')} : "  rendered="#{not empty history.quotSecCustomer}"  styleClass="opp-label"/> 
                                <p:spacer width="6px" rendered="#{not empty history.quotSecCustomer}" />   
                                <h:outputLabel   value="#{history.quotSecCustomer} " rendered="#{not empty history.quotSecCustomer}" styleClass="opp-value"/> 
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotSecCustomer}"/>              

                                <h:outputLabel   value="Owner :  " rendered="#{not empty history.quotOwner}" styleClass="opp-label"/>  
                                <p:spacer width="6px" rendered="#{not empty history.quotOwner}"/> 
                                <h:outputLabel   value="#{history.quotOwner} " rendered="#{not empty history.quotOwner}" styleClass="opp-value"/>
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotOwner}"/>   
                                <h:outputLabel   value="#{custom.labels.get('IDS_SECOND_CUSTOMER')} Contact : " rendered="#{not empty history.oppSecCustomerCont}" styleClass="opp-label"/>                     
                                <p:spacer width="6px" rendered="#{not empty history.oppSecCustomerCont}"/>
                                <h:outputLabel   value="#{history.oppSecCustomerCont} " rendered="#{not empty history.oppSecCustomerCont}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.oppSecCustomerCont}"/>  
                                <h:outputLabel    value="#{custom.labels.get('IDS_DISTRI')} Contact : "  rendered="#{not empty history.oppDistriCont}" styleClass="opp-label"/>  
                                <p:spacer width="6px" rendered="#{not empty history.oppDistriCont}"/>
                                <h:outputLabel   value="#{history.oppDistriCont} " rendered="#{not empty history.oppDistriCont}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.oppDistriCont}"/>              
                                <h:outputLabel   value="Quote Status : " rendered="#{not empty history.quotDelivStatus}" styleClass="opp-label"/>  
                                <p:spacer width="6px" rendered="#{not empty history.quotDelivStatus}"/>
                                <h:outputLabel   value="#{history.quotDelivStatus} " rendered="#{not empty history.quotDelivStatus}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotDelivStatus}"/>  
                                <h:outputLabel    value="#{custom.labels.get('IDS_PRINCI')} Contact : " rendered="#{not empty history.quotePrincipalCont}" styleClass="opp-label" />       
                                <p:spacer width="6px" rendered="#{not empty history.quotePrincipalCont}"/>
                                <h:outputLabel   value="#{history.quotePrincipalCont} " rendered="#{not empty history.quotePrincipalCont}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotePrincipalCont}"/>              
                                <h:outputLabel   value="Quote Date : " rendered="#{not empty history.quoteDate}" styleClass="opp-label"/>
                                <p:spacer width="6px" rendered="#{not empty history.quoteDate}"/>
                                <h:outputLabel   value="#{history.quoteDate} " rendered="#{not empty history.quoteDate}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quoteDate}"/>              
                                <h:outputLabel  value="#{custom.labels.get('IDS_RFQ_NUM')} : " rendered="#{not empty history.quotRfqNum}" styleClass="opp-label"/> 
                                <p:spacer width="6px" rendered="#{not empty history.quotRfqNum}"/>
                                <h:outputLabel   value="#{history.quotRfqNum} " rendered="#{not empty history.quotRfqNum}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotRfqNum}"/>   
                                <h:outputLabel   value=" #{custom.labels.get('IDS_CUSTOMER')} : " rendered="#{not empty history.quoteCustomer}" styleClass="opp-label"/> 
                                <p:spacer width="6px" rendered="#{not empty history.quoteCustomer}" /> 
                                <h:outputLabel   value="#{history.quoteCustomer} " rendered="#{not empty history.quoteCustomer}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quoteCustomer}"/>   
                                <h:outputLabel   value="Quote Value : " rendered="#{not empty history.quotValue}" styleClass="opp-label"/> 
                                <p:spacer width="6px" rendered="#{not empty history.quotValue}" />
                                <h:outputLabel   value="#{history.quotValue} " rendered="#{not empty history.quotValue}" styleClass="opp-value"/>  
                            </tr>

                            <tr>
                                <p:spacer width="10px" rendered="#{not empty history.quotValue}"/>   
                                <h:outputLabel   value="Internal Comments : " rendered="#{not empty history.quoteInternalComments}" styleClass="opp-label"/> 
                                <p:spacer width="6px" rendered="#{not empty history.quoteInternalComments}"/>
                                <h:outputLabel   value="#{history.quoteInternalComments} " rendered="#{not empty history.quoteInternalComments}" styleClass="opp-value"/>  
                            </tr>
                            
                             <tr>
                                <p:spacer width="10px"  rendered="#{not empty history.quoteInternalComments}" />
                                <h:outputLabel value="Expiration Date : " rendered="#{not empty history.quotExpiry}" styleClass="opp-label" />
                                <p:spacer width="6px" rendered="#{not empty history.quotExpiry}" />
                                <h:outputLabel value="#{history.quotExpiry}" rendered="#{not empty history.quotExpiry}" styleClass="opp-value" />

                            </tr>

                        </table>
                    </p:column>
                </p:dataTable>  
            </p:outputPanel>
        </p:dialog>
    </h:form>

    <style>
        .opp-value{
            color:black!important;
            line-height: 1.5em;
        }
        .opp-label{
            color:rgba(0, 0, 0, 0.51)!important;
        }
        .header-label{
            font: initial;
            font-size: small;
        }
        .history-label{
            color:black!important;
        }
        .tbl-row{
            background: rgba(199, 205, 213, 0.21);
        }
    </style>
</ui:composition>

