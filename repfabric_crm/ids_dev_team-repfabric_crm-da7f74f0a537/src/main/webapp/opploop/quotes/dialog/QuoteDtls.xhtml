<?xml version="1.0" encoding="UTF-8"?>
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:pe="http://primefaces.org/ui/extensions">

    <!--06-07-2022 8348 Quote Line Item: Change Part Number lookup to Input-text Autocomplete feature-->
    <p:dialog  styleClass="dialogCSS" id="dlgQuoteDtls" widgetVar="dlgQuoteDtls"  responsive="true"
               modal="true" style="text-overflow: scroll"  >
        <!--//15-12-2023 : #12503 : CRM-7304 Quote: Line Items: part number typing issue-->
        <p:ajax event="close" onstart="hideautoCompleteList();timeoutMouse()"/>
        <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier--> 
        <!--//          #12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
        <p:growl widgetVar="addDialogGrowl" />
        <!--//       #13654 ESCALATION CRM-8032 Quotes: Product Notes of Product in Product Master is not carrying over to Quote-->
        <!--06-07-2022 8348 Quote Line Item: Change Part Number lookup to Input-text Autocomplete feature-->
        <!--<p:remoteCommand name="populateParties" autoRun="false" actionListener="#{quotesDtl.populatePartList(quotesHdr.isSuperQuote==true and quotesHdr.recId!=0?quotesDtl.quotPrincipal:quotesHdr.oppPrincipal)}"/>-->
        <!--//19-08-2022 : #8767 : CRM-6002   Lestersales/Kim Heck/- Quotes Feature - when -Adding Weight-->
        <!--//10-10-2023 : #12059 : CRM-7304   Repfabric: Quote line item add button doesn't add new part number to products master-->
        <!--//10-10-2023 : #12059 : CRM-7304   Repfabric: Quote line item add button doesn't add new part number to products master-->
        <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
        <p:remoteCommand name="applyProd" actionListener="#{quotesDtl.applyProd(viewProductLookUpService.selectedProduct)}"
                         update="quotefrom:oppItemPartCust quotefrom:oppItemPartManf quotefrom:leadTime quotefrom:prodline
                         quotefrom:oppItemCost  quotefrom:oppItemQnty quotefrom:unit quotefrom:extPrice  quotefrom:weight 
                         quotefrom:quotMultiplier  quotefrom:oppEau  quotefrom:quotUom  quotefrom:quotTargetCost    quotefrom:oppItemPartDesc  quotefrom:quotNotes
                         quotefrom:quotStdRate quotefrom:isExistPartNumberOutputPanel quotefrom:specialPrice quotefrom:quotUnit quotefrom:quotMarkupPct quotefrom:imageURLInput quotefrom:specURLInput quotefrom:opnLink quotefrom:opnLink1"
                         oncomplete="updateWidth();"/>
        <!--//10-10-2023 : #12059 : CRM-7304   Repfabric: Quote line item add button doesn't add new part number to products master-->
        <p:remoteCommand name="PartNumberExists" autoRun="false" actionListener="#{quotesDtl.parNumExistProductMst(quotesHdr.isSuperQuote==true ? quotesDtl.quotPrincipal : quotesHdr.oppPrincipal)}" 
                         update="quotefrom:isExistPartNumberOutputPanel"/>
        <!--        <p:remoteCommand name="quotPartNumberNotExist" autoRun="false" update="quotefrom:specialPrice quotefrom:oppItemPartCust 
                                 quotefrom:oppItemCost  quotefrom:oppItemQnty quotefrom:unit quotefrom:extPrice  quotefrom:weight
                                 quotefrom:quotMultiplier  quotefrom:oppEau  quotefrom:quotUom  quotefrom:quotTargetCost    quotefrom:oppItemPartDesc
                                 quotefrom:quotStdRate quotefrom:unitSprice"/>-->
        <f:facet name="header">
            <h:outputText id="hdrText" value="#{custom.labels.get('IDS_QUOTES')} Details #{quotesDtl.cloned ? '(Cloned)' : ''}" />
        </f:facet>
        <p:panelGrid id="gridQuoteDtls" styleClass="panlGrid">
            <!--//10-10-2023 : #12059 : CRM-7304   Repfabric: Quote line item add button doesn't add new part number to products master-->
            <div class="ui-g ui-fluid" id="btns1">
                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                    <!--#12168 CRM-7323 Quotes: refresh pricing on quotes-->
                    <!--#3551 CRM-2135: #5: Quotes > Line Items > Delete All-->
                    <!--oncomplete="PF('processDlg').hide()"onstart="PF('processDlg').show()"-->    
                    <!--#302 Quotes > Move Line Items to a new tab-->
                    <!--3269 Quotes > Linked Docs-->
                    <!--                    Task#3918-CRM-4017: quote2opp conversion - cherry pick which lines to generate an opp for from a quote  22-03-2021 by harshithad-->
                    <!--//05-10-2023 : #12270 : Incidental Finding: CRM-7392: Quotes: Standard price field has to be totally erased before you enter-->
                    <p:outputPanel id="gridQuoteDtl1">
                        <!--//#13723 CRM-8065   Quotes: Deleting line items DOES NOT update the time stamp on the top right-->
                        <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                        <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                        <!--13653: 03/05/2024: ESCALATION CRM-8031 Quotes: When clicking the edit button, it stops you from editing a Line Item-->
                        <p:commandButton value="#{quotesDtl.recId > 0 ? 'Update' : 'Save'}" actionListener="#{quotesDtl.saveOrUpdateDetails(quotesDtl)}" id="dlgSaveBtn"                             
                                         update=":quotefrom:tabQuot:recalculatePricing  :quotefrom:tabQuot:opVl :quotefrom:tabQuot:delDtls  :quotefrom:tabQuot:btns :quotefrom:tabQuot:quotWeight :quotefrom:tabQuot:tblQuotDtls :quotefrom quotefrom:imageURLInput quotefrom:specURLInput quotefrom:opnLink quotefrom:opnLink1 "  
                                         class="btn btn-xs btn-success" >
                        </p:commandButton>
                    </p:outputPanel>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:commandButton value="Cancel"    onclick="PF('dlgQuoteDtls').hide()" 
                                     type="button" class="btn btn-xs btn-warning"  />
                </div>
                <!--                    <p:spacer width="10"/>
                                    <p:commandButton value="Delete" onclick="PF('confirmation1').show()"   
                                                     update="" rendered="#{quotesDtl.recId > 0}" class="btn btn-xs btn-danger" >
                                        <p:confirm header="Confirmation" message="Are you sure to delete?" icon="ui-icon-alert" />
                                    </p:commandButton>-->

                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--//31-10-2023 : #12438 : Quote line item Clone: Not working-->
                    <p:outputPanel id="gridQuoteDtl">
                        <p:commandButton class="btn btn-xs btn-primary"  value="Clone" actionListener="#{quotesDtl.cloneQuoteDetails(quotesDtl)}" rendered="#{quotesDtl.recId > 0}"
                                         update=":quotefrom:gridQuoteDtl :quotefrom:gridQuoteDtl1">

                        </p:commandButton>
                    </p:outputPanel>
                </div>
                <!--                <p:column>
                                    <h:outputText value="created by "
                                </p:column>-->
            </div>
            <!--            Feature #4996:Customer Pricing  > Quote Get Best Price 26/06/21 by harshithad-->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-12 ui-lg-12" >
                    <p:outputPanel id="specialPrice">
                        <p:outputLabel id="otptLblMessge" rendered="#{quotesDtl.quotCustPriceFlag==1}" value="** #{custom.labels.get('IDS_CUSTOMER')} Special Price applied"/>
                    </p:outputPanel>
                </div>
            </div>
            <!--   _______________start opportunities______________________  -->
            <!--#336 Quotes > SuperQuote updates-->
            <div class="ui-g ui-fluid" >

                <div class="ui-sm-12 ui-md-2 ui-lg-2" style="#{quotesHdr.isSuperQuote==true and quotesHdr.recId!=0 ? '' : 'display:none'}">
                    <p:outputLabel value="#{custom.labels.get('IDS_PRINCI')}" for="oppLnItmPrincipal"  styleClass="required"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4" style="#{quotesHdr.isSuperQuote==true and quotesHdr.recId!=0 ? '' : 'display:none'}">
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <h:panelGroup class="ui-inputgroup" >
                        <p:inputText id="oppLnItmPrincipal" onkeypress="return event.keyCode != 13;" value="#{quotesDtl.quotPrincipalName}" readonly="true"  required="true" requiredMessage="#{custom.labels.get('IDS_PRINCI')} is required"/>
                        <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                          actionListener="#{quotesHdr.listLookUpvalues('applyActivePrinciForSuperQuote', 1)}"
                                          update=":formCompLookup :quotefrom:btnPart" tabindex="2"  id="btnSrchCompLkp1"
                                          styleClass="btn-info btn-xs" />
                    </h:panelGroup>
                </div>
                <!--</div>-->
                <!--                <p:column>
                                    <p:outputLabel id="princiLnItmContactLbl" value="#{custom.labels.get('IDS_PRINCI')} Contact" for="oppLnItmPrincipalContact" />
                                </p:column><p:column>
                                    <h:panelGroup class="ui-inputgroup" rendered="#{quotesHdr.isSuperQuote==false}"  id="princiLnItmContactInpt" >
                                        <p:inputText id="oppLnItmPrincipalContact" readonly="true"  value="#{quotesHdr.oppLnItmPrincipalContName}"  />
                                        <p:commandButton id="btnOppLnItmPrinciContSearch" icon="fa fa-search" title="Choose Contact" immediate="true"  
                                                         actionListener="#{viewContLookupService.list('applyCont5',1,quotesHdr.oppLnItmPrincipal,1)}" 
                                                         update=":formContLookup" oncomplete="PF('lookupCont').show()"  
                                                         styleClass="btn-info btn-xs" />
                                    </h:panelGroup>
                                </p:column>-->
                <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2" >
                    <p:outputPanel rendered="#{quotesDtl.recId==0}">
                        <p:outputLabel value="Add at Position"/> 
                    </p:outputPanel>
                </div>
                <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                <div class="ui-sm-12 ui-md-4 ui-lg-4" >
                    <p:outputPanel rendered="#{quotesDtl.recId==0}">
                        <p:selectOneMenu styleClass="tds1"  onfocus="this.select();" value="#{quotesDtl.quotSeqNum}" 
                                         style="border-radius: 5px; background: white;" id="quotPos">
                            <!--6163 Quotes  - line items adding for single principal -if more than 1 row adding then -> position error-->
                            <p:ajax event="change" update="@this" /> 
                            <!--<p:ajax event="itemSelect" process="@this"  update="@this" />-->
    <!--                         <f:selectItems value="#{activityJournalHdr.listOppAct}"  
                                           var="act"   
                                           itemValue="#{act.actName}"
                                           itemLabel="#{act.actName}"/>
                                    <p:ajax listener="#{activityJournalHdr.activityChanged(act.actName)}"/>-->
                            <f:selectItem itemLabel="At the end" itemValue="0"/>
                            <f:selectItems value="#{quotesDtl.sequenceNo()}"/>

                        </p:selectOneMenu>
                    </p:outputPanel>
                </div>
                <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2" style="#{quotesHdr.isSuperQuote==true and quotesHdr.recId!=0 ? 'display:none' : ''}"></div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4" style="#{quotesHdr.isSuperQuote==true and quotesHdr.recId!=0 ? 'display:none' : ''}"></div>
<!--                <div class="ui-sm-12 ui-md-3 ui-lg-3" rendered="#{quotesHdr.isSuperQuote==false and quotesDtl.recId!=0}">
                </div>
                <div class="ui-sm-12 ui-md-3 ui-lg-3" rendered="#{quotesHdr.isSuperQuote==false and quotesDtl.recId!=0}">
                </div>-->
            </div>


            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Manuf. Part No"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--//16-09-2022 : #9024 : Quote Refactoring: Tooltip suggestion for part number-->
                    <p:tooltip id="tooltip" for="quotefrom:panelAutoComplete" widgetVar="tollForAutoComplete" 
                               value="Type a few characters to get the assisted lookup" showEvent="mouseover" 
                               hideEvent="mouseout" />
                    <!--//   #14850  CRM-8639 : part number authoring: optionally autocapitalize alphanumerics, especially quote-->
                    <h:inputHidden id="capitalize" value="#{quotesDtl.autoCapsPartNo}"></h:inputHidden>
                    <!--                    <p:tooltip for="quotefrom:panelAutoComplete" value="Type a few characters to get the assisted lookup" showEvent="mouseover" 
                                                   hideEvent="mouseout" />-->
                    <h:panelGroup class="ui-inputgroup" id="panelAutoComplete"  >  
                        <!--//30-06-2022 :  #8348 : Quote Line Item: Change Part Number lookup to Input-text Autocomplete feature-->
                        <!--//16-09-2022 : #9024 : Quote Refactoring: Tooltip suggestion for part number-->
                        <!--//16-11-2022 : #9294 : ESCALATIONS CRM-6212  pirsales/Quotes Line Item - part description box does not collapse after savin-->
                        <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                        <!--11357:05/06/2023:  ESCALATIONS CRM-7033   Quotes: Search function needs to be refined-->
                        <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                        <!--//08-11-2023 : #12503 : CRM-7304 Quote: Line Items: part number typing issue-->
                        <!--//05-12-2023 : #12629 : CRM-7513 Quote: adding part number with "-" kills query: Bugs-->
                        <p:autoComplete id="oppItemPartManf" value="#{quotesDtl.oppItemPartManf}" onkeyup="myFunction()" 
                                        onmouseover="timeoutMouse()" completeMethod="#{quotesDtl.completePartManNewLineV2Dlg}"  style="width: 90%;"
                                        placeholder="Type a few characters to get the assisted lookup"
                                        minQueryLength="3" onclick="PF('tollForAutoComplete').hide();"
                                        widgetVar="dlgOppItemartNum"  scrollHeight="250" queryDelay="500">

                            <f:attribute name="selectViewProd" value="#{quotesDtl.viewProdMst}"/>`
                            <f:facet name="itemtip">
                                <p:outputPanel id="itemTip">
                                    <h:panelGrid columns="2" cellpadding="5" >
                                        <f:facet name="header">
                                            <h:outputText value="Product Information:"/>
                                        </f:facet>
                                        <h:outputText value="#{custom.labels.get('IDS_PRINCI')}:"/>
                                        <h:outputText id="dlgOtnewquoteprinciname"/>
                                        <!--//04-10-2022 : #9218 : CRM-6190: quotes: crosses does not do anything (at least in superquotes)-->
                                        <h:outputText value="#{custom.labels.get('IDS_PART_NUM')}:"/>
                                        <h:outputText id="dlgOtnewquotepartno" />
                                        <h:outputText value="#{custom.labels.get('IDS_PROD_FAMILY')}:"/>
                                        <h:outputText id="dlOtnewquotefamily"/>
                                        <h:outputText value="#{custom.labels.get('IDS_PROD_LINE')}:"/>
                                        <h:outputText id="dlgOtnewquoteline"/>
                                        <h:outputText value="Description:"/>
                                        <h:outputText id="dlgOtnewquotedesc"/>
                                        <h:outputText value="Standard Price:"/>
                                        <h:outputText id="dlgOtnewquoteprodstdprice" >
                                            <f:convertNumber groupingUsed="true" minFractionDigits="0" maxFractionDigits="3"/>
                                        </h:outputText>
                                        <h:outputText value="Comm. Rate:"/>
                                        <h:outputText id="dlgOtnewquoteprodcommrate"/>
                                    </h:panelGrid>
                                    <!--//04-10-2022 : #9218 : CRM-6190: quotes: crosses does not do anything (at least in superquotes)-->
                                    <h:panelGrid columns="2" cellpadding="5" id="dlgPgCrossProd" >
                                        <f:facet name="header">
                                            <h:outputText value="Crosses Information:"/>
                                        </f:facet>
                                        <h:outputText value="#{custom.labels.get('IDS_PRINCI')}:"/>
                                        <!--<p:spacer width="4"/>-->
                                        <h:outputText id="dlgOtnewquotecrossprinciname"/>
                                        <h:outputText value="#{custom.labels.get('IDS_PART_NUM')}:"/>
                                        <!--<p:spacer width="4"/>-->
                                        <h:outputText id="dlgOtnewquotecrosspartno"/>
                                        <h:outputText value="Description:"/>
                                        <!--<p:spacer width="4"/>-->
                                        <h:outputText id="dlgOtnewquotecrossproddesc"/>
                                    </h:panelGrid>
                                </p:outputPanel>
                            </f:facet>
<!--                        <p:ajax event="change" listener="#{lookupSelection.fetchPartNumDescription(quotesDtl.oppItemPartManf, 'QUOTE')}"
                        update="@(.partNumBox)"/>-->
                            <!--                          Feature #4996:Customer Pricing  > Quote Get Best Price 26/06/21 by harshithad-->
<!--                            <p:ajax event="keypress" listener="#{quotesDtl.selectedProdMst()}" update="quotefrom:gridQuoteDtls quotefrom:oppItemPartCust
                                    quotefrom:oppItemCost  quotefrom:oppItemQnty quotefrom:unit quotefrom:extPrice 
                                    quotefrom:quotMultiplier  quotefrom:oppEau  quotefrom:quotUom  quotefrom:quotTargetCost    quotefrom:oppItemPartDesc
                                    quotefrom:quotStdRate "/>-->
                            <!--//19-08-2022 : #8767 : CRM-6002   Lestersales/Kim Heck/- Quotes Feature - when -Adding Weight-->
                            <!--//18-08-2022 : #8749 : Quotes Refractoring: Show Crosses Part Number on typeahead-->
                            <!--//16-09-2022 : #9024 : Quote Refactoring: Tooltip suggestion for part number-->
                            <!--//22-09-2022 : #9113 :Quote > Line Item: Type Part #, UOM Units doesn't update-->
                            <!--//25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value-->
                            <!--//05-12-2023 : #12629 : CRM-7513 Quote: adding part number with "-" kills query: Bugs-->
                            <!--<p:ajax event="keyup" onstart="principalName();"/>-->
                            <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                            <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                            <!--//       #13654 ESCALATION CRM-8032 Quotes: Product Notes of Product in Product Master is not carrying over to Quote-->
                            <!--// #14272 :  CRM-8214 : Quote Line Item: New Feature of ImageURL -->  
                            <p:ajax event="itemSelect" onstart="PF('dlgProcessingOnQuot').show();" oncomplete=";PF('dlgProcessingOnQuot').hide();timeoutDelay();updateWidth();" listener="#{quotesDtl.onselectProdInLineItem}" update="quotefrom:oppItemPartCust
                                    quotefrom:oppItemCost  quotefrom:oppItemQnty quotefrom:unit quotefrom:extPrice quotefrom:weight quotefrom:quotMarkupPct
                                    quotefrom:quotMultiplier  quotefrom:oppEau  quotefrom:quotUom  quotefrom:quotTargetCost    quotefrom:oppItemPartDesc  quotefrom:quotNotes
                                    quotefrom:quotStdRate quotefrom:prodline quotefrom:leadTime quotefrom:quotUnit quotefrom:specialPrice quotefrom:unitSprice quotefrom:isExistPartNumberOutputPanel quotefrom:imageURLInput quotefrom:opnLink quotefrom:specURLInput quotefrom:opnLink1" />
                            <!--//15-12-2023 : #12503 : CRM-7304 Quote: Line Items: part number typing issue-->
                            <!--//08-01-2024 : #12842 : CRM-7304   Repfabric: Quote line item add button doesn't add new part number to products master-->
                            <p:ajax event="blur" onstart="if (PF('dlgOppItemartNum').panel.is(':visible')) return false;PF('dlgOppItemartNum').close();timeoutMouse();" listener="#{quotesDtl.parNumExistProductMst(quotesHdr.isSuperQuote==true ? quotesDtl.quotPrincipal : quotesHdr.oppPrincipal)}"/>
                            <p:ajax event="change" onstart="if (PF('dlgOppItemartNum').panel.is(':visible')) return false;PF('dlgOppItemartNum').close();" />
                            <!--<p:ajax event="blur" delay="600" listener="#{quotesDtl.quotDlgBlur(quotesDtl)}"/>-->
                                       <!--<p:ajax event="change" listener="#{quotesDtl.onPartmanf(quotesDtl)}" update="quotefrom:gridQuoteDtls"/>-->
                        </p:autoComplete>
                        <!--//18-08-2022 : #8749 : Quotes Refractoring: Show Crosses Part Number on typeahead-->
                        <!--//25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value-->
                        <p:remoteCommand name="fetchingPrincipalName" autoRun="false" actionListener="#{quotesDtl.applyCompNameDlg(quotesHdr.isSuperQuote==true ? quotesDtl.quotPrincipal : quotesHdr.oppPrincipal)}"
                                         />
                        <script>
                            function cutCross() {
                                var comp = document.getElementById('quotefrom:oppItemPartManf_input');
                                var complete = $("#quotefrom\\:oppItemPartManf");
                                var val = complete;
//                                console.log('complete object' + Object.keys(val));
////                               console.dir('complete Json='+JSON.stringify(val));
//                                console.dir('complete dir=' + val);
//                                console.log('complete log=' + val);
//                                console.log('complete item=' + val.item);
                                //                                Object.keys('complete object='+val);
                                var value = comp.value;
//                                console.log('componenet ' + comp)
//                                console.log('value ' + value);
                                value = value.replace('CROSS', '');
                                value = value.replace('_', '');
//                                console.log('value ' + value);
                                comp.value = value;
                            }
                            //25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value
                            function principalName() {
                                var compNameDlg = document.getElementById('quotefrom:oppItemPartManf_input').value;
                                fetchingPrincipalName([{name: 'compNameDlg', value: compNameDlg}]);
                            }

//                            document.getElementById("quotefrom:oppItemPartManf_input").addEventListener("blur", function () {
//                                var y = $("#quotefrom\\:oppItemPartManf_input");
//                                y = y.val()
//                                console.log('name=', y)
//                                PartNumberExists([{name: 'dlgPartNumber', value: y}]);
//                            });

                            function fetchPartNumber() {
                                var y = $("#quotefrom\\:oppItemPartManf_input");
                                y = y.val()
//                                console.log('name=', y)
                                PartNumberExists([{name: 'dlgPartNumber', value: y}]);
                            }
                            //02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                            $(document).ready(function () {
                                var element = document.getElementById("quotefrom:quotMultiplier_input");
                                element.style.width = "130%";
                                var elementMarkup = document.getElementById("quotefrom:quotMarkupPct_input");
                                elementMarkup.style.width = "240%";
                            });
                            //02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier
                            function updateWidth() {
                                var element = document.getElementById("quotefrom:quotMultiplier_input");
                                element.style.width = "130%";
                                var elementMarkup = document.getElementById("quotefrom:quotMarkupPct_input");
                                elementMarkup.style.width = "240%";
                            }

                            //05-12-2023 : #12629 : CRM-7513 Quote: adding part number with "-" kills query: Bugs
                            function partNoDetilDlg(jsonProdpart) {
                                setTimeout(function () {
                                    if ($("#quotefrom\\:oppItemPartManf_panel").length !== 0) {
                                        //27-03-2023 : #10343 : CRM-6498  Product Info pop up boxes sticking again
                                        setTimeout(function () {
                                            var d = document.getElementById('quotefrom:oppItemPartManf_itemtip')
                                            d.style.display = 'none';
                                        }, 600)
                                        loadQuoteItemtipDataDlg(jsonProdpart[0]);
                                        $("#quotefrom\\:oppItemPartManf_panel ul").each(function () {
                                            $(this).find('li').hover(
                                                    function () {
                                                        var des = document.getElementById('quotefrom:oppItemPartManf_itemtip');
                                                        if (des.length !== 2) {
                                                            des.style.display = 'none';
                                                        }
                                                        document.getElementById('quotefrom:oppItemPartManf_itemtip').style.display = 'block';
                                                        var ii = $(this).index() / 2;
                                                        loadQuoteItemtipDataDlg(jsonProdpart[ii]);
                                                        //                                            setTimeout(function () {
                                                        ////                                                const element = document.getElementById('quotefrom:tabQuot:tblQuotDtls:oppItemPartManf_panel');
                                                        ////                                                let pos = element.offsetTop;
                                                        ////                                                document.getElementById("quotefrom:tabQuot:tblQuotDtls:oppItemPartManf_itemtip").style.marginTop = "200px";
                                                        //                                                
                                                        //
                                                        //                                            }, 500)
                                                    },
                                                    function () {
                                                    }
                                            );
                                            //                                $(this).find('li').focus(
                                            //                                        function () {
                                            //                                            console.log('testing the focus')
                                            //                                        }
                                            //                                );
                                        });
                                    }
                                }, 0);
                            }
                            //05-12-2023 : #12629 : CRM-7513 Quote: adding part number with "-" kills query: Bugs
                            function loadQuoteItemtipDataDlg(jsonObject) {
                                $('#quotefrom\\:dlgOtnewquoteprinciname').html(jsonObject["princiName"]);
                                $('#quotefrom\\:dlgOtnewquotepartno').html(jsonObject["partno"]);
                                $('#quotefrom\\:dlgOtnewquotefamily').html(jsonObject["family"]);
                                $('#quotefrom\\:dlgOtnewquoteline').html(jsonObject["line"]);
                                $('#quotefrom\\:dlgOtnewquotedesc').html(jsonObject["desc"]);
                                $('#quotefrom\\:dlgOtnewquoteprodstdprice').html(jsonObject["price"]);
                                $('#quotefrom\\:dlgOtnewquoteprodcommrate').html(jsonObject["commrate"]);
                                $('#quotefrom\\:dlgOtnewquotecrossprinciname').html(jsonObject["crossprinciname"]);
                                $('#quotefrom\\:dlgOtnewquotecrosspartno').html(jsonObject["crosspartno"]);
                                $('#quotefrom\\:dlgOtnewquotecrossproddesc').html(jsonObject["crosspartdesc"]);
                                //                    $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:quotPrincipalTextValue').val = principal.val();
                                //                    var date =document.getElementById('quotefrom:tabQuot:tblQuotDtls:otnewquoteprinciname').value;
                                //                    var update = document.getElementById('quotefrom:tabQuot:tblQuotDtls:quotPrincipalTextValue');
                                //                    update.value = date;
                                //                    console.log('updated value=',update.value)
                                if (jsonObject["crossrecid"] === null || jsonObject["crossrecid"] === 0) {
                                    //                        $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:pgCrossProd').hide();
                                    $("#quotefrom\\:dlgPgCrossProd").css("display", "none");
                                    var s = document.getElementById("quotefrom:dlgPgCrossProd");
                                    s.style.display = 'none';
                                } else {
                                    //                        $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:pgCrossProd').show();
                                    $("#quotefrom\\:dlgPgCrossProd").css("display", "block");
                                    var s = document.getElementById("quotefrom:dlgPgCrossProd");
                                    s.style.display = 'block';
                                }
                                //08-01-2024 : #12842 : CRM-7304   Repfabric: Quote line item add button doesn't add new part number to products master
                                var y = $("#quotefrom\\:oppItemPartManf_input");
                                y = y.val()
                                PartNumberExists([{name: 'dlgPartNumber', value: y}]);
                            }
                            //15-12-2023 : #12503 : CRM-7304 Quote: Line Items: part number typing issue
                            function hideautoCompleteList() {
                                // Find the autocomplete dropdown list by class
                                var autocompleteDropdown = document.querySelector('.ui-autocomplete-items');

                                if (autocompleteDropdown) {
                                    PF('dlgOppItemartNum').close();
                                }
                            }
                            //15-12-2023 : #12503 : CRM-7304 Quote: Line Items: part number typing issue
                            function timeoutMouse() {
                                setTimeout(hideautoCompleteList, 1000);
                            }

                        </script>
                        <!--                    <p:commandButton id="btnPart"  
                                                             class="btnlukup"
                                                             style="height: 27px;width:32px;"
                                                             icon="ui-icon-search" 
                                                             title="#{custom.labels.get('IDS_PART_NUM')} Look Up"
                                                             actionListener="#{lookupSelection.loadMenuOption('QUOTE')}"
                                                             action="#{productsMst.loadProducts()}" 
                                                             update=" " 
                                                             oncomplete="PF('dlgPartNum').show();"
                                                             />-->
                        <!--01/03/2022 7298 More CRM>open Quote>Lineitems>double click in Partnum lookup>Cancel>Add>old partnum shows.-->

                        <!--//21-10-2022 : #9422 : CRM-6259   Quotes: POBCO Quote Needs-->
                        <p:commandButton  id="btnPart"   
                                          icon="fa fa-search" title="Choose Part Number" immediate="true" widgetVar="btnPart" onclick="PF('btnPart').disable()"
                                          actionListener="#{viewProductLookUpService.list('applyProd',quotesHdr.isSuperQuote==true and quotesHdr.recId!=0?quotesDtl.quotPrincipal:quotesHdr.oppPrincipal)}" 
                                          update=":dtPartNumForm" oncomplete="PF('dlgPartNum').show();PF('btnPart').enable()"  
                                          styleClass="btn-info btn-xs" />
                    </h:panelGroup>
                </div>

                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')} Part No"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                    <p:inputText value="#{quotesDtl.oppItemPartCust}" onkeypress="return event.keyCode != 13;" id="oppItemPartCust" maxlength="60"  style="width: 100%"  />
                </div>
            </div>
            <!--//06-10-2023 : #12059 : CRM-7304   Repfabric: Quote line item add button doesn't add new part number to products master-->
            <p:outputPanel id="isExistPartNumberOutputPanel" >
                <p:outputPanel >
                    <div class="ui-g ui-fluid">
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Add to Products Master"/>
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3" style="margin-left:-60px;">
                            <p:selectBooleanCheckbox value="#{quotesDtl.addProductMstBol}" disabled="#{quotesDtl.isExistPart}">
                            </p:selectBooleanCheckbox>      

                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3" style="margin-left:60px;">
                            <p:outputLabel value="Add to #{custom.labels.get('IDS_CUSTOMER')}'s  Pricing"/>
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3" style="margin-left:-35px">
                            <p:selectBooleanCheckbox value="#{quotesDtl.addcustomerPriceBol}" disabled="#{quotesDtl.isExistPart}">
                            </p:selectBooleanCheckbox>
                        </div>
                    </div>
                </p:outputPanel>
            </p:outputPanel>
            <!--            <p:row id="isExistPartNumberPanelGrid">
                            <p:column id="productLabel" >
                                <p:outputLabel value="Add to Products Master"/>
                            </p:column>
                            <p:column id="productInput" >
                                <p:selectBooleanCheckbox value="#{quotesHdr.defCloneQuotNumBol}" disabled="#{quotesDtl.isExistPart}">
                                </p:selectBooleanCheckbox>
                            </p:column>
                            <p:column id="customerLabel">
                                <p:outputLabel value="Add to #{custom.labels.get('IDS_CUSTOMER')}'s  Pricing"/>
                            </p:column>
                            <p:column id="customerInput">
                                <p:selectBooleanCheckbox value="#{quotesHdr.defCloneQuotNumBol}" disabled="#{quotesDtl.isExistPart}">
                                </p:selectBooleanCheckbox>
                            </p:column> 
                        </p:row>-->
            <!--#3156 Quotes > Line Item > UOM-->


            <!--#3568:Task :CRM-2135: #4: Quotes > Line Items > UOM  start-->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--#11212 CRM-6918: Product: Set pricing per hundred in template-->
                    <!--Feature #11444: CRM-6918: Product: Set pricing per hundred in template QUOTE PDF  by harshithad on 14/06/23-->

                    <p:outputLabel id="un" value="Pricing UOM"/>
                </div>

                <div class="ui-sm-12 ui-md-4 ui-lg-4" id="columnQuotUom">
                    <p:selectOneMenu styleClass="tds1"  onfocus="this.select();" value="#{quotesDtl.quotUom}" style="border-radius: 5px; background: white;" id="quotUom">
                        <f:selectItem itemLabel="[Select]" itemValue=""/>
                        <f:selectItems value="#{unitOfMeasurementsService.prodUoms()}"    var="prod" itemValue="#{prod.uomName}" itemLabel="#{prod.uomName}"/>    
                        <!--//#3534 Quotes > Line Items > Updates to Multiplier and Unit Price-->
                        <!--<p:ajax   event="itemSelect"  process="@this" listener="#{quotesDtl.calculateExtPrice(quotesDtl.oppItemQnty, quotesDtl.quotResale, quotesDtl.quotUom)}" 
                        />-->
                        <!--                        <p:ajax event="itemSelect" process="@this" update=":quotefrom:quotUnit" />-->
                        <!--//#11212 CRM-6918: Product: Set pricing per hundred in template-->
                        <!--#13702 CRM-7892   UOM issues in Quote- Urgent-->
                        <p:ajax event="itemSelect"   process="@this"  listener="#{quotesDtl.onChangeUOM(quotesDtl.quotStdRate, quotesDtl.quotUom, quotesDtl.oppItemQnty,quotesDtl.quotMultiplier)}" update=":quotefrom:quotUnit :quotefrom:extPrice :quotefrom:quotStdRate :quotefrom:unit :quotefrom:weight quotefrom:specialPrice :quotefrom:quotMultiplier"/>
                    </p:selectOneMenu>
                        <!--<p:inputText value="#{quotesDtl.quotUom}" id="quotUom" style="width: 97%" maxlength="11" />-->
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="UOM Units"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                    <p:inputText value="#{quotesDtl.quotUomUnits}" onkeypress="return event.keyCode != 13;" id="quotUnit" style="width: 100%" maxlength="11" readonly="true"/>
                </div>

            </div>
            <!--#3568:Task :CRM-2135: #4: Quotes > Line Items > UOM End-->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="EAU"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <p:inputNumber maxValue="999999999"  decimalPlaces="0" onkeypress="return event.keyCode != 13;"  value="#{quotesDtl.oppEau}" id="oppEau" style="width: 97%" maxlength="2" converterMessage="Must be a number consisting of one or more digits."/>
                </div>
                <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2" style="display: flex">
                    <p:outputLabel value="Qty in"/>
                    <p:spacer width="10"/>
                    <p:selectOneMenu id="quotQntyUom" value="#{quotesDtl.quotQntyUom}" style="width:60px;min-width:70px" >
                        <f:selectItem itemLabel="Units" itemValue="0" />
                        <f:selectItem itemLabel="UOM" itemValue="1" />
                        <p:ajax event="itemSelect"   process="@this"  listener="#{quotesDtl.onChnageQuotQntyUom(quotesDtl.quotQntyUom, quotesDtl.oppItemQnty, quotesDtl.quotResale,quotesDtl.quotUomUnits,1)}" update=":quotefrom:extPrice  :quotefrom:weight"/>
                    </p:selectOneMenu>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--#601237: thorsonrm - increase qty decimals: changed quantity length to 12-->
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <p:inputNumber decimalPlaces="3" onkeypress="return event.keyCode != 13;"  maxValue="999999999.999"   value="#{quotesDtl.oppItemQnty}" id="oppItemQnty" style="width: 97%" maxlength="12" converterMessage="Must be a signed decimal number." >
                        <!--#3547 Quotes Line item : Multiple time alert message displays. (Page Reloading issue)-->
                        <!--#3640 CRM-2135: #4: Quotes > Updates to Extended Price calculation involving UOM Units-->
                        <!--                    Feature #4996:Customer Pricing  > Quote Get Best Price 26/06/21 by harshithad-->
                        <!--//30-06-2022 :  #8348 : Quote Line Item: Change Part Number lookup to Input-text Autocomplete feature-->
                        <!--//05-08-2022 : #8623 : CRM-5986   Quote: changing qty wipes our line item details-->
                        <!--//22-09-2022 : #9109  : CRITICAL CRM-6159: Quotes: Weight is not calculating for total weight on the quote and on the quote-->
                        <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                        <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                        <!--//              #13743  Quote line item functionality testing-->
                        <p:ajax event="change" listener="#{quotesDtl.onQtyChange(quotesDtl)}" update=" :quotefrom:quotUnit
                                :quotefrom:quotStdRate :quotefrom:quotMultiplier quotefrom:quotMarkupPct :quotefrom:oppItemQnty
                                :quotefrom:unit :quotefrom:extPrice :quotefrom:weight quotefrom:specialPrice" oncomplete="updateWidth();"/>

<!--                        <p:ajax event="blur" listener="#{quotesDtl.calculateExtPrice(quotesDtl.oppItemQnty, quotesDtl.quotResale, quotesDtl.quotUomUnits,quotesDtl.quotQntyUom,0)}" 
                                update="quotefrom:extPrice " />-->
                    </p:inputNumber>
                </div>

            </div>

            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Unit Cost"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <p:inputNumber decimalPlaces="3" onkeypress="return event.keyCode != 13;"   maxValue="999999999.999"  value="#{quotesDtl.oppItemCost}" id="oppItemCost" style="width: 97%" maxlength="12" styleClass="partNumBox" converterMessage="Must be a signed decimal number." >
                        <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                        <!--<f:convertNumber minFractionDigits="3" maxFractionDigits="6"/>-->
                    </p:inputNumber>
                </div>


                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Target Cost"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <p:inputNumber decimalPlaces="3" onkeypress="return event.keyCode != 13;"   maxValue="999999999.999"  value="#{quotesDtl.quotTargetCost}" id="quotTargetCost" style="width: 97%" maxlength="12" converterMessage="Must be a signed decimal number." >
                        <!--Bug 1045 : #982069 :unit price decimals messing up report-->
                        <!--<f:convertNumber minFractionDigits="3" maxFractionDigits="6"/>-->
                    </p:inputNumber>
                </div>
            </div>

            <!--Shreejith Bug 462 2018-06-21-->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--<p:outputLabel value="Standard Price:"/>-->
                    <!--                    Seema - 22/04/2019-->
                    <!--                    CRM 315:Gentsch :change quote labels-->
                    <p:outputLabel value="#{custom.labels.get('IDS_STD_PRICE')}"/>


                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">


                    <!--#12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
                    <!--                    Feature #4996:Customer Pricing  > Quote Get Best Price 26/06/21 by harshithad-->
                    <!--9923 PRIORITY CRM-6404  quote decimal places for unit price not consistent with opps, downflow fails-->
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <!--//27-09-2023 :  #12176 : ESCALATION CRM-7392 Quotes: Standard price field has to be totally erased before you enter a number-->
                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                    <!--//30-11-2023 : #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                    <p:inputText    onkeyup="window.myFunction1(1)"   value="#{quotesDtl.quotStdRate}"  converterMessage="Must be a signed decimal number."  converter="javax.faces.BigDecimal"  id="quotStdRate" maxlength="15"   onfocus="this.select();"
                                    styleClass="partNumBox" onkeypress="return window.myFunction2(event, 1)"  style="width: 100%"   >
                        <f:convertNumber   maxFractionDigits="6" minFractionDigits="2"/>
                        <!--//07-11-2023 :  #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                        <!--//25-09-2024 : #14246 : 2 ESCALATION CRM-8351 : Quote: Unable to update standard price field for robroy items-->
                        <p:keyFilter mask="num" for="quotStdRate" preventPaste="false"/>
                        <!--<p:ajax  event="keypress" listener="#{quotesDtl.onStandardPriceChange(quotesDtl.quotStdRate,false)}" update=":quotefrom:gridQuoteDtls" />-->
                        <!--//13-12-2023 : #12689 : ESCALATIONS CRM-7458   quotes: customer markup next to multiplier-->
                        <p:ajax event="change" 
                                listener="#{quotesDtl.calculateUnitPrice(quotesDtl.quotStdRate, quotesDtl.quotMultiplier, quotesDtl.oppItemQnty, quotesDtl.quotUomUnits,1)}" 
                                update="quotefrom:unit quotefrom:extPrice :quotefrom:quotStdRate quotefrom:specialPrice quotefrom:unitSprice" />
                    </p:inputText>


<!--                   <p:inputNumber   onkeypress="return event.keyCode != 13;" value="#{quotesDtl.quotStdRate}"  
                id="quotStdRate" style="width: 97%"  maxlength="15" styleClass="partNumBox" converterMessage="Must be a signed decimal number." 
                disabled="#{quotesDtl.standardPriceFlag}" onfocus="this.select();" >
     <f:convertNumber maxFractionDigits="3"/>
     #3547 Quotes Line item : Multiple time alert message displays. (Page Reloading issue)
     #3640 CRM-2135: #4: Quotes > Updates to Extended Price calculation involving UOM Units 
     //#11212 CRM-6918: Product: Set pricing per hundred in template
   

     Bug 1045 : #982069 :unit price decimals messing up report
     <f:convertNumber minFractionDigits="2" maxFractionDigits="6"/>
 </p:inputNumber>-->
                </div>
                <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Multiplier:"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:-7px">
                    <div class="ui-g ui-fluid" >
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <!--#12199 CRM-7350   Uploaded multiplier at .3735 it changed to .374 - increase number of decimal places to 5-->
                            <!--#3534 Quotes > Line Items > Updates to Multiplier and Unit Price-->
                            <!--                   Feature #4996:Customer Pricing  > Quote Get Best Price 26/06/21 by harshithad-->
                            <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                            <!--//30-11-2023 : #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                            <p:inputNumber decimalPlaces="5" onkeypress="return event.keyCode != 13;"  maxValue="9999.99999" 
                                           value="#{quotesDtl.quotMultiplier}" id="quotMultiplier" style="width: 97%" maxlength="20" 
                                           converterMessage="Must be a signed decimal number." >    
                                <!--#3547 Quotes Line item : Multiple time alert message displays. (Page Reloading issue)-->
                                <!--#3640 CRM-2135: #4: Quotes > Updates to Extended Price calculation involving UOM Units-->
                                <!--//13-12-2023 : #12689 : ESCALATIONS CRM-7458   quotes: customer markup next to multiplier-->
                                <p:ajax event="change" 
                                        listener="#{quotesDtl.calculateUnitPrice(quotesDtl.quotStdRate, quotesDtl.quotMultiplier, quotesDtl.oppItemQnty, quotesDtl.quotUomUnits,1)}" 
                                        update="quotefrom:unit quotefrom:extPrice quotefrom:specialPrice quotefrom:unitSprice" />
                            </p:inputNumber>
                        </div>
                        <div class="ui-sm-12 ui-md-7 ui-lg-7">
                            <p:outputLabel value="Customer Markup:" style="margin-left:10px;"/>
                        </div>
                        <div class="ui-sm-12 ui-md-2 ui-lg-2" style="margin-left:-25px;">
                            <!--//30-11-2023 : #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                            <p:inputNumber decimalPlaces="2" onkeypress="return event.keyCode != 13;"  maxValue="99999.99" 
                                           value="#{quotesDtl.quotCustMarkupPct}" id="quotMarkupPct" style="width: 100%" maxlength="8" 
                                           converterMessage="Must be a signed decimal number." >
                                <!--//13-12-2023 : #12689 : ESCALATIONS CRM-7458   quotes: customer markup next to multiplier-->
                                <p:ajax event="change" 
                                        listener="#{quotesDtl.calculateMarkupValue(quotesDtl.oppItemQnty,quotesDtl.quotUomUnits,quotesDtl.quotStdRate, quotesDtl.quotMultiplier,quotesDtl.quotCustMarkupPct,quotesHdr.recId)}"
                                        update="quotefrom:unit quotefrom:tabQuot:quotReceipient quotefrom:extPrice quotefrom:specialPrice quotefrom:unitSprice" />

                            </p:inputNumber>
                        </div>
                    </div>

                </div>
                <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                <!--                <div class="ui-sm-12 ui-md-2 ui-lg-2" style="margin-left:-10px">
                                    
                                </div>-->
                <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                <!--                <div class="ui-sm-12 ui-md-1 ui-lg-1" style="margin-left:-15px">
                                    
                                </div>-->
            </div>

            <div class="ui-g ui-fluid" >
                <div class="ui-sm-12 ui-md-2 ui-lg-2" >
                    <!--#586840-->
                    <!--                    <p:outputLabel value="Unit Price:"/>-->
                    <!--                    Seema - 22/04/2019-->
                    <!--                    CRM 315:Gentsch :change quote labels-->
                    <p:outputPanel id="unitSprice">
                        <p:outputLabel value="#{custom.labels.get('IDS_UNIT_PRICE')}"/>
                        <!--                   Feature #4996:Customer Pricing  > Quote Get Best Price 26/06/21 by harshithad-->
                        <p:outputLabel id ="otptLblAstersk" value="**" rendered="#{quotesDtl.quotCustPriceFlag==1}"/>
                    </p:outputPanel>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
<!--                    <p:inputText value="#{quotesDtl.quotResale}"  style="width: 97%" id='unit' styleClass="partNumBox"  maxlength="12" converterMessage="Must be a signed decimal number." disabled="true">
                        Bug 1045 : #982069 :unit price decimals messing up report
                        <f:convertNumber minFractionDigits="3" maxFractionDigits="6"/>
                    </p:inputText>-->
                    <!--#12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
                    <!--#3534 Quotes > Line Items > Updates to Multiplier and Unit Price-->
                    <!--                  Feature #4996:Customer Pricing  > Quote Get Best Price 26/06/21 by harshithad-->
                    <!--9923 PRIORITY CRM-6404  quote decimal places for unit price not consistent with opps, downflow fails-->
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                    <p:inputText   onkeyup="window.myFunction1(2)"  converter="javax.faces.BigDecimal"   onkeypress="return window.myFunction2(event, 2)" value="#{quotesDtl.quotResale}" id='unit' styleClass="partNumBox" style="width: 100%" converterMessage="Must be a signed decimal number." disabled="#{quotesDtl.unitPriceFlag}">
                        <!--//30-06-2022 :  #8348 : Quote Line Item: Change Part Number lookup to Input-text Autocomplete feature-->
                        <f:convertNumber   maxFractionDigits="6" minFractionDigits="2"/>

                        <!--//07-11-2023 :  #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                        <!--//25-09-2024 : #14246 : 2 ESCALATION CRM-8351 : Quote: Unable to update standard price field for robroy items-->
                        <p:keyFilter mask="num" for="unit" preventPaste="false"/>
                        <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                        <p:ajax event="change" listener="#{quotesDtl.calculateExtPrice(quotesDtl.oppItemQnty, quotesDtl.quotResale, quotesDtl.quotUomUnits,quotesDtl.quotQntyUom,0)}" update=":quotefrom:oppItemPartCust :quotefrom:quotUnit
                                :quotefrom:oppEau :quotefrom:oppItemCost :quotefrom:quotTargetCost :quotefrom:quotStdRate :quotefrom:quotMultiplier
                                :quotefrom:unit :quotefrom:extPrice :quotefrom:prodline" oncomplete="updateWidth();"/>
                    </p:inputText>
                </div>

                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Extended Price"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--#3534 Quotes > Line Items > Updates to Multiplier and Unit Price-->
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                    <p:inputNumber value="#{quotesDtl.quotExtPrice}" onkeypress="return event.keyCode != 13;" id='extPrice' styleClass="partNumBox" style="width: 100%" disabled="true" converterMessage="Must be a signed decimal number.">
                    </p:inputNumber>

                </div>
            </div>

            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Lead Time"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <!--Feature #11228: CRM-6924   Quotes: lead time does not print  by harshithad on 05/05/23-->
                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                    <p:inputText id="leadTime" onkeypress="return event.keyCode != 13;" value="#{quotesDtl.quotLeadTime}" style="width: 100%" maxlength="40" converterMessage="Must be a signed decimal number." styleClass="partNumBox"/>
                </div>

                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Prod Line"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--//30-06-2022 :  #8348 : Quote Line Item: Change Part Number lookup to Input-text Autocomplete feature-->
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                    <p:inputText id="prodline" onkeypress="return event.keyCode != 13;" value="#{quotesDtl.quotProdLine}" style="width: 100%"  maxlength="30" styleClass="partNumBox" />
                </div>
            </div>
            <!--5858 IS Quotes: Quote Line Item > Location-->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--task:14847 : 05-11-2024 added the custom label for the field of "Location"-->
                    <p:outputLabel value="#{custom.labels.get('IDS_LOCATION')}"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                    <p:inputText onkeypress="return event.keyCode != 13;" value="#{quotesDtl.quotLocation}" style="width: 100%" 
                                 maxlength="60" styleClass="partNumBox" />
                </div>
                <!--//19-08-2022 : #8767 : CRM-6002   Lestersales/Kim Heck/- Quotes Feature - when -Adding Weight-->
                <!--#9086 CRM-6151  product master - update weight label to show "(lbs)"-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Weight (lbs)"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--//30-06-2022 :  #8348 : Quote Line Item: Change Part Number lookup to Input-text Autocomplete feature-->
                    <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                    <p:inputText id="weight" onkeypress="return event.keyCode != 13;"  value="#{quotesDtl.quotWeight}" style="width: 100%"  
                                 maxlength="30" styleClass="partNumBox" />
                </div>
            </div>
            <!--//14-08-2024 : #14272 :  CRM-8214 : Quote Line Item: New Feature of ImageURL -->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Image URL" />
                </div>

                <div class="ui-sm-12 ui-md-4 ui-lg-9">
                    <p:inputText onkeypress="return event.keyCode != 13;"  styleClass="partNumBox"   onfocus="this.select();"  value="#{quotesDtl.prodImgUrl}"  id="imageURLInput" maxlength="500"  >
                        <p:ajax event="change" update="quotefrom:opnLink"/>
                    </p:inputText> 
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-1">       
                    <p:link id="opnLink" value="View link" 
                            style="color: blue;" disabled="#{quotesDtl.prodImgUrl == ''}"  
                            href="#{quotesDtl.getValidURL(quotesDtl.prodImgUrl)}" target="_blank" /> 
                </div>
            </div>
            <!--//14-08-2024 : #14272 :  CRM-8214 : Quote Line Item: New Feature of Specific URL -->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Specification URL" />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-9">
                    <p:inputText  onkeypress="return event.keyCode != 13;" styleClass="partNumBox"  onfocus="this.select();"  value="#{quotesDtl.prodSpecUrl}"  id="specURLInput" maxlength="500"  >
                        <p:ajax event="change" update="quotefrom:opnLink1"/>
                    </p:inputText> 
                </div>
                <div class="ui-sm-12 ui-md-7 ui-lg-1"> 
                    <p:link id="opnLink1" value="View link"  
                            style="color: blue;" disabled="#{quotesDtl.prodSpecUrl == ''}"  
                            href="#{quotesDtl.getValidURL(quotesDtl.prodSpecUrl)}" target="_blank"/>
                </div>
            </div>

            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Part Description"/>
                </div>
                <div class="ui-sm-12 ui-md-10 ui-lg-10" >
                    <!--//    Bug 1266 -#193088 :EPIC/GOLDEN INSTANCE:part number UI-->
                    <!--                    Task#3939-CRM-3987: Quote description field too limited  23/03/21 by harshithad-->
                    <p:inputTextarea rows="2" id="oppItemPartDesc"  value="#{quotesDtl.oppItemPartDesc}" style="width: 99%;text-overflow: scroll" styleClass="partNumBox" autoResize="false" >
                        <!--#3547 Quotes Line item : Multiple time alert message displays. (Page Reloading issue)-->
<!--                        <p:ajax event="blur" listener="#{transDtl.checkvalidate(quotesDtl.oppItemPartDesc ,1)}"  update=" " />-->

                    </p:inputTextarea>
                </div>
            </div>

            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Notes"/>
                </div>
                <div class="ui-sm-12 ui-md-10 ui-lg-10">
                    <p:inputTextarea id="quotNotes" rows="2" value="#{quotesDtl.quotNotes}"  onkeypress="return event.keyCode != 9;" style="width: 99%;text-overflow: scroll" autoResize="false" />
                </div>
            </div>            
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-12 ui-lg-12">
                    <p:selectBooleanCheckbox rendered="#{quotesDtl.oppId!=0}" id="updNrmlLnItmReq" 
                                             itemLabel="Update linked #{custom.labels.get('IDS_OPP')}"                                
                                             value="#{quotesDtl.updateOppLnItm}"  />

                </div>
            </div>
            <!--//15-12-2023 : #12725 : CRM-7458	quotes: customer markup next to multiplier (Tool tip updates)-->
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-12 ui-lg-12">
                    <p:outputLabel value="Note: Unit Price is calculated as (Std. Price * Multiplier) / Customer Markup. Customer Markup is not considered if entered 0 or blank."/>
                </div>
            </div>



        </p:panelGrid>

        <p:confirmDialog header="Confirm delete"  width="400" global="true"
                         message="Are you sure to delete?"
                         widgetVar="confirmation1" >
            <div class="div-center">
                <!--                //2848 CRM-1790 :Hawkins :Fw Quote total-->
                <!--Seema - 06/01/2020 - updated :quotefrom:opVl-->
                <!--#302 Quotes > Move Line Items to a new tab-->
                <p:commandButton value="Delete" action="#{quotesDtl.DeleteDtls()}"
                                 class="btn btn-danger btn-xs" process="@this"   onclick="PF('confirmation1').hide();
                                         PF('dlgQuoteDtls').hide();"  update=":quotefrom:tabQuot:tblQuotDtls :quotefrom:tabQuot:opVl"  />
                <p:spacer width="4"/>
                <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"    onclick="PF('confirmation1').hide()" 
                                 type="button"  />
            </div>
        </p:confirmDialog>
        <!--//08-02-2023 : #10636 : ESCALATIONS CRM-6661   Adding a line item pop up disappears when hitting the enter button-->
        <p:dialog id="dlgIdProcessingOnQuot" widgetVar="dlgProcessingOnQuot" closable="false" modal="true" header="Message" onShow="PF('dlgProcessingOnQuot').initPosition();" resizable="false" >
            <h:form>
                <p:outputPanel >
                    <br />
                    <h:graphicImage  library="images" name="ajax-loader.gif"   />
                    <p:spacer width="5" />
                    <p:outputLabel value="Processing please wait..." />
                    <br /><br />
                </p:outputPanel>
            </h:form>
        </p:dialog>
        <!--         <p:dialog header="Message" widgetVar="processDlg" closable="false" resizable="false" modal="true">
                <p:outputPanel style="text-align: center" >
                    <br />
                    <h:graphicImage  library="images" name="ajax-loader.gif" id="loadingImg"   />
                    <p:spacer width="8" />
                    <h:outputLabel value="Processing..." />
                    <br /><br />
                </p:outputPanel>
                <br />
            </p:dialog>-->
    </p:dialog>
    <style>
        /*        .ui-panelgrid-cell {
                    vertical-align: top !important;
                }
                                        .top-align tbody tr td{
                                            vertical-align: top !important;
                                        }*/

        .ui-autocomplete-input::placeholder {
            text-transform: none;
        }
        .tool-height {
            width:300px;
        }
        .hide-facet{
            display: none;
        }
        .ui-grid-col-3 {
            vertical-align: top !important;
        }
    </style>
    <!--//   #14850  CRM-8639 : part number authoring: optionally autocapitalize alphanumerics, especially quote-->
    <!--//16-09-2022 : #9024 : Quote Refactoring: Tooltip suggestion for part number-->
    <script type="text/javascript">
        function myFunction() {
            var autocapitalize = document.getElementById("quotefrom:capitalize").value;
            var y = document.getElementById("quotefrom:oppItemPartManf_input");
           
            if (autocapitalize=='true') {
                y.style.textTransform = 'uppercase';
            } else {
                y.style.textTransform = 'none';
            }

            if (y.val().length > 0) {
                console.log('length is greater')
                PF('tollForAutoComplete').hide();
            }
        }
        function mouseOver() {
            var y = $("#quotefrom\\:oppItemPartManf_input");
            console.log('y=' + y.val())
            if (y.val().length > 0) {
                console.log('length is greater')
                PF('tollForAutoComplete').hide();
            }
        }
        function timeoutMouse() {
            setTimeout(mouseOver, 300);
        }
        function hideTool() {
            PF('tollForAutoComplete').hide();
        }
        function timeoutDelay() {
            setTimeout(hideTool, 1000);
        }
    </script>
</ui:composition>