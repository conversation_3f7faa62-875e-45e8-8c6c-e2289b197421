<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
<!--March 23 2020-->

    <p:dialog  styleClass="dialogCSS" id="isQuotSyncDtlsx" widgetVar="isQuotSyncDtls"  responsive="true" 
               style="text-overflow: scroll;"  height="550" width="900" modal="true">
        <f:facet name="header">
            <h:outputText id="isQuotSyncx" value="Target Quotes List" />
        </f:facet>
        <!--#{oAuthLogin.quotLookUp.get(0)}-->
        <!--rendered="#{oAuthLogin.quotLookUp.size()>0}"-->
       <!--1461 CRM-3100: FW: Questions/Comments from SOUTHWEST-->
        <p:dataTable value="#{iSQuoteCrmClient.quotLookUp}" 
                     var="qHd" id="esr" rowIndexVar="errRecc" 
                     emptyMessage="No errors" 
                     scrollable="true"
                     scrollHeight="350"  
                     widgetVar="errRecc"
                      selection="#{quotesHdr.selectedTSQhdr}"
                     selectionMode="single"
                     rowKey="#{qHd.recId}" paginator="true" 
                     >
            <!--#302 Quotes > Move Line Items to a new tab-->
            <p:ajax event="rowSelect" listener="#{quotesHdr.createQuotLink}" update=":quotefrom:tabQuot:quotSync" oncomplete="PF('isQuotSyncDtls').hide();" />
            
            <p:column  width="2%"> 
                <f:facet name="header"></f:facet>
                   #{errRecc+1}
            </p:column> 
            <p:column width="10%">
                <f:facet name="header">Quote ID</f:facet>
               <!--#{qHd.quotNotes}--> 
               #{qHd.recId}
            </p:column>
            <p:column width="10%">
                <f:facet name="header">Quote Number</f:facet>
               #{qHd.quotNumber} 
            </p:column>
            <p:column width="10%">
                <f:facet name="header">Job Name</f:facet>
               #{qHd.quotJob} 
            </p:column>
            <p:column width="10%">
                <f:facet name="header">Salesperson ID</f:facet>
               #{qHd.oppSManName} 
            </p:column>
            <p:column width="10%">
                <f:facet name="header">Company</f:facet>
               #{qHd.oppPrincipalName} 
            </p:column>
            <p:column width="10%">
                <f:facet name="header">Name</f:facet>
               #{qHd.oppPrincipalContName} 
            </p:column>
            <p:column width="10%">
                <f:facet name="header">Customer ID</f:facet>
               #{qHd.oppCustomerName} 
            </p:column>
            <p:column width="10%">
                <f:facet name="header">Comments</f:facet>
               #{qHd.quotComments} 
            </p:column>
        </p:dataTable>
        
        
        
          
<!-- <p:dataTable id="esr" widgetVar="errRecc" value="#{oAuthLogin.quotLookUp}" var = "errRecc" 
                     selectionMode="single"  selection="#{oAuthLogin.quotLookUp}" 
                         emptyMessage="No open #{custom.labels.get('IDS_OPPS')} found." paginator="true" rows="10"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="5,10,15" paginatorAlwaysVisible="false" class="tblmain">
                 
                <p:ajax event="rowSelect" listener="#{viewOppList.onselectQtOpps}" oncomplete="PF('oppQtMstDlg').hide()"  update="quotefrom quotefrom:vwlnk quotefrom:lnk" />
                
                <p:column   id="custName" filterMatchMode="contains"  >
                    <f:facet name="header">  </f:facet> 
                        
                </p:column>
                
 
            </p:dataTable>-->
    </p:dialog>
</ui:composition>



