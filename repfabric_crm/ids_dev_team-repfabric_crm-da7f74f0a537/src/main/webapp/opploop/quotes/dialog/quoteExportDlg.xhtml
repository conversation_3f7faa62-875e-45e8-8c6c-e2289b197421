<?xml version="1.0" encoding="UTF-8"?>
<!--
#1027:CRM-2813: HALCO:  Quote module-WHEN can we export?
#  quotes export..
-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui" 
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">

    <p:dialog widgetVar="dlgQuotExp" header="Quotes Export" modal="true"  width="300px"  id="quotesExpDlg" class="dialogCSS">
        <p:growl id="msgs"  showDetail="false" showSummary="true"  />
        <h:form id="frmQuoteExp" >
            <p:panelGrid id="headerExportDlg" >    
                <p:row>
                    <p:column>

                        <p:outputLabel value="From"/>

                    </p:column>
                    <p:column>
                        <p:calendar pattern="#{globalParams.dateFormat}" value="#{quotesService.quoteFromDate}" converterMessage="Please enter valid date" autocomplete="off">
                       
                         </p:calendar>
                    </p:column>
                </p:row>

                <br/>
                <p:row>
                    <p:column>

                        <p:outputLabel value="To"/>

                    </p:column>
                    <p:column>
                        <p:calendar pattern="#{globalParams.dateFormat}"  value="#{quotesService.quoteToDate}"  converterMessage="Please enter valid date"  autocomplete="off"   >

                        </p:calendar>
                    </p:column>
                    
                </p:row>
                <p:row>
                    <div   class="div-center">


                        <p:column colspan="2"  style="text-align: center">
                            <p:commandButton value="Export" actionListener="#{quotesService.populateQuotesRecords()}"  ajax="true" onclick="PrimeFaces.monitorDownload(startdlg, stopdlg);
                                             " styleClass="btn btn-primary btn-xs"  />
                            <p:spacer width="5" />
                            <p:commandButton value="Cancel" 
                                             process="@this"  oncomplete="PF('dlgQuotExp').hide();"  styleClass="btn btn-warning btn-xs"/>
                        </p:column>
                    </div> 


                </p:row>

            </p:panelGrid >
        </h:form>

    </p:dialog>


    <!-- <p:dialog widgetVar="inQuotesProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
            <h:form>
                <p:outputPanel >
                    <br />
                    <h:graphicImage  library="images" name="ajax-loader.gif"   />
                    <p:spacer width="5" />
                    <p:outputLabel value="Please wait...Exporting records" />
                    <br /><br />
                    <p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />
                </p:outputPanel>
            </h:form>
        </p:dialog>-->
    <script type="text/javascript">
        function startdlg() {

            PF('inProgressDlg').show();
        }
        function stopdlg() {

            PF('inProgressDlg').hide();
        }
    </script>



</ui:composition>