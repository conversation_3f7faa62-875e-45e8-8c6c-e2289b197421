<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
        
    <h:panelGroup id="panelAttach" class="pnls">
<!--       #11095  Quotes Code Optimisation : Quotes Slowness issues for Attachments tab-->
       <p:remoteCommand name="attachTabCmd"   autoRun="false"  actionListener="#{quoteAttachments.getListAttachments}" oncomplete="PF('quoteAttachTable').clearFilters();" update="quotefrom:tabQuot:dtQuoteAtt"/> 
        <p:fileUpload 
            fileUploadListener="#{quoteAttachments.uploadAttchments}" 
            mode="advanced" dragDropSupport="true"  
            auto="true" id="fileUploadQuoteAttach"
            label="Choose File" update="quotefrom:tabQuot:dtQuoteAtt @this" />
        
        <p:dataTable  value="#{quoteAttachments.listQuoteAttachments}" rowKey="#{quote.quotAttId}" selectionMode="single" 
                      filteredValue="#{quoteAttachments.filteredQuoteAttachments}"
                      style="width: 80%"
                      var="quote" id="dtQuoteAtt" widgetVar="quoteAttachTable">            
            <p:column width="6%" >
                
                <p:commandButton 
                    actionListener="#{quoteAttachments.delAttach(quote)}"
                    oncomplete="PF('confirmations').show()"  
                    id="btnQuoteAttachDelete"
                    icon="fa fa-trash" 
                    title="Remove Attachment" 
                    class="btn-xs btn-danger">
                    
                </p:commandButton>
            </p:column>
            <p:column headerText="File Name" style="text-align: left" filterBy="#{quote.quotAttName}" filterMatchMode="contains" sortBy="#{quote.quotAttName}">

                #{quote.quotAttName}

            </p:column>
            <p:column headerText="Uploaded On" style="text-align: left" filterBy="#{globalParams.formatDateTime(quote.insDate,'dt')}" filterMatchMode="contains" sortBy="#{globalParams.formatDateTime(quote.insDate,'dt')}"  >
                <p:outputLabel id="oTxtQuoteAtchDateTime" value="#{globalParams.formatDateTime(quote.insDate,'dt')}"  >
                    <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>

                </p:outputLabel>
            </p:column>
            <p:column>
                <p:commandButton   immediate="true" process="@none" title="Download Attachment"  class="btn-primary btn-xs"  icon="fa fa-download" id="btnQuoteAttchDownload"
                                   onclick="primeFaces.monitorDownload(start, stop)">
                    <p:fileDownload    value="#{quote.file}"/>
                </p:commandButton>
            </p:column>
        </p:dataTable>
        
        <p:confirmDialog header="Confirm delete"  width="400" global="false"
                         message="Are you sure to delete attachment?" 
                         widgetVar="confirmations" >
            <div class="div-center">
                <p:commandButton value="Delete" actionListener="#{quoteAttachments.delete()}" update="quotefrom:tabQuot:dtQuoteAtt"
                    class="btn btn-danger btn-xs"   oncomplete="PF('confirmations').hide()" />
                <p:spacer width="4"/>
                <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"  onclick="PF('confirmations').hide()" 
                    type="button"  />
            </div>
        </p:confirmDialog>
        
        <style>
            div.ui-fileupload .ui-fileupload-buttonbar span, div.ui-fileupload .ui-fileupload-buttonbar button {
                padding-bottom:27px !important;
            }
            
            .ui-fileupload-content:before{
                content: "Please Drop Files Here";
                margin-left: 50vh;
                text-justify: auto;
                font-size: 20px;
                color:#b4c3d0;
                align-content: center;

            } 
        </style>
        
        
    </h:panelGroup>
</ui:composition>
