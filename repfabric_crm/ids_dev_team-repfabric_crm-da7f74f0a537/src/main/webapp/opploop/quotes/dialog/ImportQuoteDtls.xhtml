<?xml version="1.0" encoding="UTF-8"?>
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">
    <!--#3085 Quotes - Line Items - Import-->
    <!--//17-02-2023 : #10734 : ESCALATIONS CRM-6704  Quotes: Unable to import excel file-->
    <h:form id="frmImportQuotDtl">
        <p:dialog  styleClass="dialogCSS" id="dlgImpQuoteDtls" widgetVar="dlgImpQuoteDtls"  responsive="true" 
                   style="text-overflow: scroll;"  height="550" width="900" modal="true">
            <f:facet name="header">
                <h:outputText id="hdrImpText" value="Import #{custom.labels.get('IDS_QUOTES')} #{quotesDtl.cloned ? '(Cloned)' : ''}" />
            </f:facet>
            <p:panelGrid id="gridImpQuoteDtls" styleClass="panlGrid">
            </p:panelGrid>
            <!--oncomplete="PF('tblQuotDtls1').clearFilters();"-->
            <!--#3551 CRM-2135: #5: Quotes > Line Items > Delete All-->
            <!--#302 Quotes > Move Line Items to a new tab-->
            <p:ajax event="close" update="quotefrom:tabQuot:tblQuotDtls quotefrom:tabQuot:opVl quotefrom:tabQuot:delDtls" listener="#{quotesHdr.initializeQuotes()}"   process="@this"/>
            <!--<p:ajax event="close" update="@all" oncomplete="zxz()"/>-->

            <div class="ui-g ui-fluid"> 
                <div class="ui-sm-12 ui-md-9 ui-lg-9">
                    <p:outputLabel  value="Select a csv/xls/xlsx file to import"/>

                </div>
                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                    <p:commandButton icon="fa fa-info-circle" class="btn-xs btn-info" title="Help" onclick="PF('helpDlg').show();" style="height: 27px;width:27px; float: right"/>
                </div>

                <div class="ui-sm-12 ui-md-10 ui-lg-10">
                    <p:fileUpload  fileUploadListener="#{quotesDtl.startImport}" disabled="#{quotesDtl.statusList.size()>0}"
                                   label="Select File" id="qtUpld" multiple="false" fileLimit="1"
                                   uploadLabel="Load File" mode="advanced" 
                                   update="@this :frmImportQuotDtl:op :frmImportQuotDtl:er :frmImportQuotDtl:oq :frmImportQuotDtl:qtUpld" />



                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputPanel id = "oq" >
                        <p:commandButton value="Import"  rendered="#{quotesDtl.statusList.size()>0}" id="btnImportQuotes" onclick="PF('impDlg').show();" class="btn btn-xs btn-success" />
                    </p:outputPanel>   
                </div>

                <div class="ui-sm-12 ui-md-12 ui-lg-12" style="text-align: center">
                    <h:outputText value="#{users.impStatusStr}"  id="txtStatus" style="color:red;font-weight: bold;font-size: 15px;"/> 
                </div>
                <div class="ui-sm-12 ui-md-12 ui-lg-12">
                    <p:outputPanel id = "op">    

                        <!--                            <p:outputPanel  >
                                                        <br/> 
                                                        <table class="entryTbl" frame="box" cellspacing="5" >
                                                            <thead>
                                                                <tr>
                                                                    <th colspan="6" style="font-size:15px;">
                                                                        <p:outputLabel value="Load Process Status"/>
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                            <tr>
                                                                <td style="width:30%"><p:outputLabel value="Total processed" /></td>
                                                                <td style="width:5%"><p:outputLabel value=":"/></td>
                                                                <td style="width:10%"><p:outputLabel  value="#{users.recordCounter}"/></td>
                                                                <td style="width:30%;text-align: center;"><p:outputLabel value="Total loaded"/></td>
                                                                <td style="width:5%"><p:outputLabel value=":" /></td>
                                                                <td><p:outputLabel value="#{users.userErrCnt}" /></td>
                                                            </tr>
                                                        </table>
                                                    </p:outputPanel>-->

                        <ui:include src="ErrorTable.xhtml" />

                    </p:outputPanel>
                </div>
            </div>







        </p:dialog>
        <p:confirmDialog widgetVar="impDlg" header="Confirmation"  >
            <f:facet name="message">
                <p:outputLabel id="msg" value="Only data ready to be imported will be saved. Do you want to Continue?" />
            </f:facet>
            <!--                2294 - Users: Button alignments should be to the centre -  09-11-2019- sharvani-->
            <div align="center">
                <!--3269-->
                <!--//19-08-2022 : #8767 : CRM-6002   Lestersales/Kim Heck/- Quotes Feature - when -Adding Weight-->
                 <!--11257... code fix-->
                <p:commandButton value="Proceed" actionListener="#{quotesDtl.savePopulatedRecords()}"
                                 action="#{quotesHdr.loadOppListForSuperQuote()}"     update="quotefrom:tabQuot:lnkSprQt :quotefrom:tabQuot:quotWeight :quotefrom"     onclick="PF('impDlg').hide();"  class="btn btn-xs btn-primary" >

                </p:commandButton>
                <p:spacer width="4px"/>
                <p:commandButton value="Cancel" class="btn btn-xs btn-danger" onclick="PF('impDlg').hide();"/>
            </div>
        </p:confirmDialog>  
        <ui:include src="InfoDlg.xhtml" />

    </h:form>


</ui:composition>