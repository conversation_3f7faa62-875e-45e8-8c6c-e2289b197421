<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--        //08-03-2024 : #13280 : CRM-7791   Quotes: Link multiple PO-->
    <p:dialog  styleClass="dialogCSS" id="dlgQtLinkPo" widgetVar="quotLinkPoDlg"  responsive="true" 
               maximizable="true" closeOnEscape="true" height="600" width="95%" modal="true">
        <f:facet name="header">
            <h:outputText value="#{custom.labels.get('IDS_PURCHASE_ORDER')} List" />
        </f:facet>
        <h:form id="frmQuotLinkPo">
            <p:growl id="msg2" showDetail="false"/>
            <p:commandButton disabled="#{quotesHdr.quotLinkPoList.size()==0}" value="Link to #{custom.labels.get('IDS_PURCHASE_ORDER')}" actionListener="#{quotesHdr.selectQuotLinkToPo(quotesHdr.recId)}" oncomplete="PF('quotLinkPoDlg').hide();linkedDocsCmd();" class="btn btn-primary btn-xs"/>
            <br/>
            <p:dataTable id="dtQtLinkPoList" widgetVar="quotLinkPoListTable" value="#{quotesHdr.quotLinkPoList}" var = "po" multiViewState="true" rowKey="#{po.recId}" selection="#{quotesHdr.quotLinkPoFilter}" 
                         emptyMessage="No open #{custom.labels.get('IDS_PURCHASE_ORDER')} found." paginator="true" rows="10" style="width:100%;"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="5,10,15" paginatorAlwaysVisible="false" class="tblmain" scrollable="true" scrollHeight="300">
                
                            
                <p:column selectionMode="multiple" style="width: 40px;text-align: center; " >                             
                            </p:column>  
                <p:column filterBy="#{po.poCustName}" id="custName" filterMatchMode="contains" sortBy="#{po.poCustName}" >
                    <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet> 
                        #{po.poCustName}
                </p:column>
                <p:column filterBy="#{po.poPrinciName}" id="principalName" filterMatchMode="contains" sortBy="#{po.poPrinciName}">
                    <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet> 
                        #{po.poPrinciName}
                </p:column> 
                <p:column filterBy="#{po.poDistriName}" id="distriName" filterMatchMode="contains" sortBy="#{po.poDistriName}" >
                    <f:facet name="header">#{custom.labels.get('IDS_DISTRI')}</f:facet> 
                        #{po.poDistriName}
                </p:column>

                <p:column filterBy="#{po.poProgram}" filterMatchMode="contains" sortBy="#{po.poProgram}">
                    <f:facet name="header">#{custom.labels.get('IDS_PROGRAM')}</f:facet> 
                        #{po.poProgram}
                </p:column> 

                <p:column filterBy="#{po.poNumber}" filterMatchMode="contains" sortBy="#{po.poNumber}">
                    <f:facet name="header">PO Number</f:facet>  
                        #{po.poNumber}
                </p:column>

                <p:column filterBy="#{po.poStatus}" filterMatchMode="contains" sortBy="#{po.poStatus}">
                    <f:facet name="header">Status</f:facet>  
                        #{po.poStatus}
                </p:column>

                <p:column filterBy="#{oppLinkedDocumentsService.getFormattedDate(po.poFollowUpDate,'da')}" headerText="Follow Up Date" filterMatchMode="contains" sortBy="#{po.poFollowUpDate}">
                    <p:outputLabel value="#{oppLinkedDocumentsService.getFormattedDate(po.poFollowUpDate,'da')}" >
                    </p:outputLabel>
                </p:column>
            </p:dataTable>
        </h:form>
        
    </p:dialog>
</ui:composition>




<!--<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui">
            //07-03-2024 : #13280 : CRM-7791   Quotes: Link multiple PO
    <p:dialog id="dlgQtLinkPo" widgetVar=quotLinkPoDlg"  modal="true" header="#{custom.labels.get('IDS_PURCHASE_ORDER')} List"
              appendTo="@(body)" draggable="false" resizable="false" maximizable="true" closeOnEscape="true" width="100%" position="center center" onShow="PF('quotLinkPoDlg').initPosition();">
        <h:form id="frmQuotLinkPo">
            <p:growl id="msg2" showDetail="false"/>
            <p:commandButton class="btn btn-primary btn-xs" action="#{quotesHdr.createOpp()}" value="Convert to #{custom.labels.get('IDS_OPP')}" update="" oncomplete="" />
            <br/>
            <p:dataTable id="dtQtLinkPoList" widgetVar="quotLinkPoListTable" value="#{quotesHdr.quotLinkPoList}" var = "po" multiViewState="true" rowKey="#{po.recId}" selection="#{quotesHdr.quotLinkPoFilter}" 
                         emptyMessage="No open #{custom.labels.get('IDS_PURCHASE_ORDER')} found." paginator="true" rows="10"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="5,10,15" paginatorAlwaysVisible="false" class="tblmain" scrollable="true" scrollHeight="300">
                <p:ajax event="rowSelect" listener="#{viewOppList.onselectQtOpps}" oncomplete="PF('quotLinkPoDlg').hide();linkedDocsCmd()"  update="quotefrom quotefrom:tabQuot:btns quotefrom:tabQuot:lnQtOppMstList quotefrom:btns" />  
                
                <p:column filterBy="#{po.poCustName}" id="custName" filterMatchMode="contains" sortBy="#{po.poCustName}" >
                    <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet> 
                        #{po.poCustName}
                </p:column>
                <p:column filterBy="#{po.poPrinciName}" id="principalName" filterMatchMode="contains" sortBy="#{po.poPrinciName}">
                    <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet> 
                        #{po.poPrinciName}
                </p:column> 
                <p:column filterBy="#{po.poDistriName}" id="distriName" filterMatchMode="contains" sortBy="#{po.poDistriName}" >
                    <f:facet name="header">#{custom.labels.get('IDS_DISTRI')}</f:facet> 
                        #{po.poDistriName}
                </p:column>

                <p:column filterBy="#{po.poProgram}" filterMatchMode="contains" sortBy="#{po.poProgram}">
                    <f:facet name="header">#{custom.labels.get('IDS_PROGRAM')}</f:facet> 
                        #{po.poProgram}
                </p:column> 

                <p:column filterBy="#{po.poNumber}" filterMatchMode="contains" sortBy="#{po.poNumber}">
                    <f:facet name="header">PO Number</f:facet>  
                        #{po.poNumber}
                </p:column>

                <p:column filterBy="#{po.poStatus}" filterMatchMode="contains" sortBy="#{po.poStatus}">
                    <f:facet name="header">Status</f:facet>  
                        #{po.poStatus}
                </p:column>

                <p:column filterBy="#{po.poSoDate}" filterMatchMode="contains" sortBy="#{po.poSoDate}">
                    <f:facet name="header">#{custom.labels.get('IDS_FOLLOW_UP')}</f:facet> 
                    <p:outputLabel value="#{po.poSoDate}" >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                    </p:outputLabel>
                </p:column>
            </p:dataTable>
        </h:form>
    </p:dialog>



</ui:composition>-->


