<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <h:form id="frmOasisQuotesList"> 
        <p:dialog id="oasisQuotesList" widgetVar="wvOasisQuotesList"
                  responsive="true" draggable="false" resizable="false"
                  style="text-overflow: scroll;"  height="80%" width="80%" modal="true">
            <f:facet name="header">
                <h:outputText value="Oasis Quotes List" />
            </f:facet>
            <p:outputPanel id="opOasisQuotesList">
                <p:outputLabel rendered="#{quotesHdr.filterISQuotesStatus!=2}" value="Quotes for : " style="font-weight: bold;" /><p:spacer/>
                <p:outputLabel rendered="#{quotesHdr.filterISQuotesStatus!=2}" value="#{quotesHdr.paramISQuotDate}" style="font-weight: bolder;" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />  
                </p:outputLabel>
                <p:spacer /> 
                <p:dataTable value="#{oasisCrmService.quotesHdrList}" rows="10"
                             var="oasisQuote" id="dtOasisQuotesList" 
                             emptyMessage="No Quotes available" 
                             scrollable="true" paginator="true" selectionMode="single"
                             selection="#{quotesHdr.selectedHdr}" 
                             rowKey="#{oasisQuote.quotNumber}" >

                    <p:ajax event="rowSelect" process="@this" immediate="true" 
                            onstart="PF('dlgProcessing').show();" listener="#{oasisCrmService.onOasisQuoteSelect}"
                            update=":quotefrom:tabQuot" 
                            oncomplete="PF('dlgProcessing').hide();" />

                    <p:column width="8%" >
                        <f:facet name="header">Quote Number</f:facet>
                            #{oasisQuote.quotNumber}  
                    </p:column>
                    <p:column width="8%" >
                        <f:facet name="header">Job Name</f:facet>
                            #{oasisQuote.jobName}  
                    </p:column>
                    <p:column width="8%" >
                        <f:facet name="header" >Quote Value</f:facet>
                        <div style="text-align: right" > #{oasisQuote.quotValue}</div>
                    </p:column> 
                    <p:column width="8%" >
                        <f:facet name="header">City</f:facet>
                            #{oasisQuote.cityName}  
                    </p:column>

                    <p:column width="8%" >
                        <f:facet name="header">Quote Type</f:facet>
                            #{oasisQuote.quoteType}  
                    </p:column>
                    <p:column width="8%" >
                        <f:facet name="header">Quote Date</f:facet>
                        <p:outputLabel value="#{oasisQuote.quotDate}" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                        </p:outputLabel>

                    </p:column> 
                </p:dataTable>
            </p:outputPanel>
        </p:dialog>
    </h:form>
</ui:composition>   
