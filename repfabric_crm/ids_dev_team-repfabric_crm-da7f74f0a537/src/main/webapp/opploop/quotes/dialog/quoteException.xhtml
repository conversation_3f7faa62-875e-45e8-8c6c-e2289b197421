<?xml version="1.0" encoding="UTF-8"?> 
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui" 
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">
    <!--2420 Quotes List > Sync Exceptions-->
    <p:dialog widgetVar="dlgQuotException"   modal="true"  width="1100" height="475"   id="quotesExceptionDlg" class="dialogCSS">
        <!--<p:dialog  id="oppExportDlg" resizable="false"  width="1100" height="475"  class="dialogCSS" modal="true" widgetVar="dlgOppExport" focus="exportOppForm:expType"-->
        <!--2521 Quotes > Updates to Sync Exceptions and auto download-->

        <f:facet name="header" >
            <div style="display: flex;">
                <!--//    Bug #7914: CRM-5643 Oasis: Thea: most quotes and jobs have no values-->
                <div style="width: 30%">
                    Sync Exceptions
                </div>
                <p:spacer width="550" height="0"/>
                <h:form id="frmQuoteExceptionHdr" style="width: 50%; text-align: end;" >
                    <!--2521 Quotes > Updates to Sync Exceptions and auto download-->
                    <!--<p:commandButton  immediate="true" class="btn-primary  btn-xs" title="Reprocess" id="btnReprocess" style="height:30px; " action= />-->
                    <!--3437 Job Auto Download (cron job)-->
                    <!--//    Bug #7914: CRM-5643 Oasis: Thea: most quotes and jobs have no values-->
                    <!--//23-02-2024 : #13083 : CRM-7745   thea crashing (Quotes)-->
                    <!--                    //04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                    <p:commandButton  immediate="true"  value="Reprocess ISQuote Exceptions" title="Reprocess ISQuote Exceptions" id="reprocessFetchBtn"                     
                                      widgetVar="reprocessButton" class="btn btn-primary btn-xs" process="@this"
                                      actionListener="#{iSQuoteCrmClient.reprocessISQuotesDownload()}" action="#{quotesHdr.exceptionListingForQuotes()}"  update=":frmQuoteException:tblQtException :quotLstForm:tblQt :quotLstForm"
                                      onclick="PF('reprocessButton').disable();" onstart="PF('imprtProgressDlg').show();" rendered="#{iSQuoteCrmClient.quotesCounter}" 
                                      oncomplete="PF('tblVarQtException').filter(); PF('imprtProgressDlg').hide(); PF('reprocessButton').enable();if (#{quotesHdr.summaryDetals == 0}) { applyTopScrollbarQuot(); }else { applyTopScrollbarQuotDetails(); }"/>
                    <p:spacer width="4" height="0"/>
                    <!--#7024 CRM-5185: Oasis: Auto-Download Process for Jobs and linked quotes (cron job)-->
                    <!--//23-02-2024 : #13083 : CRM-7745   thea crashing (Quotes)-->
                    <!--                    <p:commandButton  immediate="true"  value="Reprocess Oasis Exceptions" title="Reprocess Oasis Exceptions" id="reprocessFetchBtno"                     
                                                          widgetVar="reprocessButton" class="btn btn-primary btn-xs" process="@this" rendered="#{iSQuoteCrmClient.oasisCounter}"
                                                          actionListener="#{oasisService.reprocessOasisDownload()}" action="#{quotesHdr.exceptionListingForQuotes()}"  update=":frmQuoteException:tblQtException :quotLstForm:tblQt :quotLstForm"
                                                          onclick="PF('reprocessButton').disable();" onstart="PF('imprtProgressDlg').show();" 
                                                          oncomplete="PF('tblVarQtException').filter(); PF('imprtProgressDlg').hide(); PF('reprocessButton').enable();"/>-->

                    <!--30-05-2024 13523 Code Optimisation for Oasis Integration Quotes and Jobs : Auto-download, Sync exceptio-->
                    <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                    <!--27-11-2024 14449 CRM-8399 3 OASIS: Clear entries from Sync Exception if the quote no longer exists in Oasis-->
                    <p:commandButton  immediate="true"  value="Reprocess Oasis Exceptions" title="Reprocess Oasis Exceptions"                   
                                      widgetVar="reprocessButton" class="btn btn-primary btn-xs" process="@this" rendered="#{iSQuoteCrmClient.oasisCounter}"
                                      onclick="PF('reprocessButton').disable();" onstart="PF('imprtProgressDlg').show();" 
                                      actionListener="#{oasisCrmService.reprocessQuoteSync(loginBean.userId)}" 
                                      update=":frmQuoteException:tblQtException"
                                      oncomplete="PF('tblVarQtException').filter();PF('tblQt').filter();PF('imprtProgressDlg').hide(); PF('reprocessButton').enable();if (#{quotesHdr.summaryDetals == 0}) { applyTopScrollbarQuot(); }else { applyTopScrollbarQuotDetails(); }"/>

                </h:form>   
            </div>
        </f:facet>
        <!--        <p:ajax event="close" update="@all" oncomplete="zxz()"/> -->
        <!--//23-02-2024 : #13083 : CRM-7745   thea crashing (Quotes)-->
        <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
        <p:ajax event="close" update="quotLstForm" listener="#{quotesHdr.populateHdrList}" oncomplete="if (#{quotesHdr.summaryDetals == 0}) { applyTopScrollbarQuot(); }else { applyTopScrollbarQuotDetails(); }"/> 
        <h:form id="frmQuoteException" >
            <p:remoteCommand autoRun="true" actionListener="#{quotesHdr.exceptionListingForQuotes()}" update="tblQtException" />
            <!--14303: 19/08/2024: ESCALATION CRM-8388: ISQuote: Quote Sync Exceptions Not Showing Option to Alias Companies & Contacts-->
            <p:remoteCommand name="applyActiveCompResolve" autoRun="false" immediate="true" update="tblQtException"
                             actionListener="#{quotesHdr.updateFieldsOnSelectionResolveListPage(viewCompLookupService.selectedCompany)}" oncomplete="PF('tblVarQtException').filter();"/>
            <!--            #7024 CRM-5185: Oasis: Auto-Download Process for Jobs and linked quotes (cron job)
                        <p:remoteCommand name="applyActiveGenericCompResolve" autoRun="false" immediate="true" update="tblQtException"
                                         actionListener="#{quotesHdr.updateFieldsOnSelectionResolveListPage(9,viewCompLookupService.selectedCompany)}" oncomplete="PF('tblVarQtException').filter();"/>
                        13375  CRM-7815   OASIS: Assign the quotes to the correct user when ingesting from OASIS
                        <p:remoteCommand name="applyOasisOwner" actionListener="#{quotesHdr.onOwnerSelectionResolveListPage(9,userLookupService.user)}"  update="tblQtException"/>-->

            <!--14303: 19/08/2024: ESCALATION CRM-8388: ISQuote: Quote Sync Exceptions Not Showing Option to Alias Companies & Contacts-->
            <p:remoteCommand name="applyActivePrinciResolve1" autoRun="false" immediate="true" update="tblQtException"
                             actionListener="#{quotesHdr.updateFieldsForOasisResolve(viewCompLookupService.selectedCompany)}"/>
            <!--11556: 29/01/2023:  ESCALATION CRM-7125 IS Quote: Specifier Contacts DO NOT populate when injesting a job from IS Quote.-->
            <!--14303: 19/08/2024: ESCALATION CRM-8388: ISQuote: Quote Sync Exceptions Not Showing Option to Alias Companies & Contacts-->
            <p:remoteCommand name="applyContForJob" actionListener="#{quotesHdr.updateJobContactOnSelectionResolveListPage(viewContLookupService.selectedContact.contId, viewContLookupService.selectedContact.contFullName)}"  update="tblQtException" />
            <!--             //08-02-2024 : #13083 : CRM-7745   thea crashing (Quotes)-->

            <!--04-06-2024 13523 Code Optimisation for Oasis Integration Quotes and Jobs : Auto-download, Sync exceptio-->
            <p:remoteCommand name="remoteCmdAliasOasisCompany" autoRun="false" immediate="true" 
                             actionListener="#{oasisCrmService.onAliasSelectedForCrmsyncLog(9,viewCompLookupService.selectedCompany)}"
                             oncomplete="PF('tblVarQtException').filter();" />

            <p:remoteCommand name="remoteCmdAliasOasisQuoteOwner" autoRun="false" immediate="true" 
                             actionListener="#{oasisCrmService.onAliasSelectedForCrmsyncLogUser(9,userLookupService.user)}"
                             oncomplete="PF('tblVarQtException').filter();"/>


            <p:dataTable value="#{crmsyncLog}"   paginator="true"  
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         id="tblQtException" widgetVar="tblVarQtException"
                         emptyMessage="No exceptions" var="quotEx"  rows="50"  lazy="true" sortMode="single" 
                         scrollHeight="350" filteredValue="#{quotesHdr.listFilterQuotException}" >
                <p:column rendered="false" headerText="Rec ID" field="REC_ID" >
                    #{quotEx.recId}
                </p:column>
                <p:column rendered="false" headerText="Quote ID" field="CRMSYNC_DOC_ID" >
                    #{quotEx.crmsyncDocId}
                </p:column>
                <p:column headerText="Quote Number" field="CRMSYNC_DOC_NUM" filterBy="#{quotEx.crmsyncDocNum}"  sortBy="#{quotEx.crmsyncDocNum}" id="colCrmsyncDocNum" filterMatchMode="contains">
                    #{quotEx.crmsyncDocNum}
                </p:column>
                <!--6293 CRM-4966: aspinall: sync exception errors-->
                <p:column headerText="Entity Name" field="CRMSYNC_DOC_COMPANY" filterBy="#{quotEx.crmsyncDocCompany}"  sortBy="#{quotEx.crmsyncDocCompany}" id="colCrmsyncDocCompany" filterMatchMode="contains">
                    #{quotEx.crmsyncDocCompany}
                </p:column>
                <p:column headerText="Entity Type" field="CRMSYNC_DOC_COMP_TYPE"  filterBy="#{quotEx.crmsyncDocCompTypeStr}" id="colCrmsyncDocCompType" filterMatchMode="contains">
                    #{quotEx.crmsyncDocCompTypeStr}
                </p:column>
                <!--//   11768 CRM-7634-->
                <p:column headerText="Entity Sub Type" field="CRMSYNC_DOC_TYPE"  filterBy="#{quotEx.crmsyncDocCompTypeStr}"    sortBy="#{quotEx.crmsyncDocCompTypeStr}" id="colCrmsyncDocCompSubType" filterMatchMode="contains">
                    #{quotEx.crmsyncDocCompSubType}
                </p:column>
                <p:column headerText="City" filterBy="#{quotEx.crmsyncDocCompCity}" field="CRMSYNC_DOC_COMP_CITY"  sortBy="#{quotEx.crmsyncDocCompCity}" id="colCrmsyncDocCompCity" filterMatchMode="contains">
                    #{quotEx.crmsyncDocCompCity}
                </p:column>            
                <p:column headerText="Exception" filterBy="#{quotEx.crmsyncFailedReason}" field="CRMSYNC_FAILED_REASON"  sortBy="#{quotEx.crmsyncFailedReason}" id="colCrmsyncFailedReason" filterMatchMode="contains">
                    #{quotEx.crmsyncFailedReason}
                </p:column>
                <p:column headerText="Processed on" filterBy="#{quotEx.crmsyncProcessedDate}" field="CRMSYNC_PROCESSED_DATE"  sortBy="#{quotEx.crmsyncProcessedDate}" id="colCrmsyncProcessedDate" filterMatchMode="contains">
                    <h:outputText value="#{quotEx.crmsyncProcessedDate}" >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                    </h:outputText>
                </p:column>
                <!--2521 Quotes > Updates to Sync Exceptions and auto download-->
                <p:column headerText="Status" sortBy="#{quotEx.crmsyncStatus}" field="CRMSYNC_STATUS" id="colCrmsyncStatus" style="align-content: center">
                    <h:graphicImage library="images" name="greenFlag.png" rendered="#{quotEx.crmsyncStatus==1}" width="12" height="12">
                    </h:graphicImage>
                </p:column>

                <p:column> 
                    <!--2563 Quotes > Sync Exceptions - Alias button-->
                    <!--#7024 CRM-5185: Oasis: Auto-Download Process for Jobs and linked quotes (cron job)-->
                    <!--14303: 19/08/2024: ESCALATION CRM-8388: ISQuote: Quote Sync Exceptions Not Showing Option to Alias Companies & Contacts-->
                    <p:commandButton id="btnCompCustSearch" value="Alias" title="Alias"  rendered="#{quotEx.crmsyncDocCompType==2 and quotEx.crmsyncSysId==6}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForResolveListPage('applyActiveCompResolve', 5,quotEx)}"
                                     update=":formCompLookup" immediate="true" process="@this"  oncomplete="$('#formCompLookup\\:companyLookUp\\:name\\:filter').val('#{quotEx.crmsyncDocCompany}'); PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />
                    <!--14303: 19/08/2024: ESCALATION CRM-8388: ISQuote: Quote Sync Exceptions Not Showing Option to Alias Companies & Contacts-->
                    <p:commandButton id="btnCompPrinciSearch" value="Alias"  title="Alias" rendered="#{quotEx.crmsyncDocCompType==1 and quotEx.crmsyncSysId==6}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForResolveListPage('applyActiveCompResolve', 1,quotEx)}"
                                     update=":formCompLookup" immediate="true" process="@this"  oncomplete="$('#formCompLookup\\:companyLookUp\\:name\\:filter').val('#{quotEx.crmsyncDocCompany}'); PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" /> 
                    <!--14303: 19/08/2024: ESCALATION CRM-8388: ISQuote: Quote Sync Exceptions Not Showing Option to Alias Companies & Contacts-->
                    <p:commandButton id="btnCompDistriSearch" value="Alias" title="Alias" rendered="#{quotEx.crmsyncDocCompType==3 and quotEx.crmsyncSysId==6}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForResolveListPage('applyActiveCompResolve', 3,quotEx)}"
                                     update=":formCompLookup" immediate="true" process="@this" oncomplete="$('#formCompLookup\\:companyLookUp\\:name\\:filter').val('#{quotEx.crmsyncDocCompany}'); PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />
                    <!--11556: 29/01/2023:  ESCALATION CRM-7125 IS Quote: Specifier Contacts DO NOT populate when injesting a job from IS Quote.-->
                    <!--13074: 12/02/2024 ESCALATIONS CRM-7750   Quote Contact Sync Issues-->
                    <!--14303: 19/08/2024: ESCALATION CRM-8388: ISQuote: Quote Sync Exceptions Not Showing Option to Alias Companies & Contacts-->
                    <p:commandButton  value="Alias"  title="Alias" immediate="true"  rendered="#{quotEx.crmsyncDocCompType==9}"
                                      actionListener="#{quotesHdr.updateSyncLog('applyContForJob',quotEx,2,1)}" 
                                      update=":formContLookup :formNew" oncomplete="PF('lookupCont').show();"  
                                      styleClass="btn-info btn-xs" />
                    <!--#7024 CRM-5185: Oasis: Auto-Download Process for Jobs and linked quotes (cron job)-->    
                    <!--13391-->
<!--                    <p:commandButton id="btnCompPrinciSearcho" value="Alias"  title="Alias" rendered="#{quotEx.crmsyncDocCompType==1 and quotEx.crmsyncSysId==9}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForOasisResolveListPage('applyActiveGenericCompResolve', 1,quotEx)}"
                                     update=":formCompLookup  :frmLookupHead" immediate="true" process="@this"  oncomplete="$('#formCompLookup\\:companyLookUp\\:name\\:filter').val('#{quotEx.crmsyncDocCompany}'); PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" /> -->
                    <!--//    13202 ESCALATIONS CRM-7813   OASIS: opening quote 130675 causes an internal server error-->
<!--                    <p:commandButton id="btnCompDistriSearcho" value="Alias" title="Alias" rendered="#{quotEx.crmsyncDocCompType==3 and quotEx.crmsyncSysId==9}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForOasisResolveListPage('applyActiveGenericCompResolve', 9,quotEx)}"
                                     update=":formCompLookup  :frmLookupHead" immediate="true" process="@this" oncomplete="$('#formCompLookup\\:companyLookUp\\:name\\:filter').val('#{quotEx.crmsyncDocCompany}'); PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />-->

<!--                     <p:commandButton id="btnCompPrinciSearcho" value="Alias"  title="Alias" rendered="#{quotEx.crmsyncDocCompType==1 and quotEx.crmsyncSysId==9}"
                 actionListener="#{quotesHdr.listLookUpvaluesForOasisResolve('applyActivePrinciResolve1', 1,quotEx.recId)}"
                 update=":formCompLookup" immediate="true" process="@this"  oncomplete="$('#formCompLookup\\:companyLookUp\\:name\\:filter').val('#{quotEx.crmsyncDocCompany}'); PF('compLookup').filter();"
                 styleClass="btn-info btn-xs" /> 
<p:commandButton id="btnCompDistriSearcho" value="Alias" title="Alias" rendered="#{quotEx.crmsyncDocCompType==3 and quotEx.crmsyncSysId==9}"
                 actionListener="#{quotesHdr.listLookUpvaluesForOasisResolve('applyActivePrinciResolve1', 9,quotEx.recId)}"
                 update=":formCompLookup" immediate="true" process="@this" oncomplete="$('#formCompLookup\\:companyLookUp\\:name\\:filter').val('#{quotEx.crmsyncDocCompany}'); PF('compLookup').filter();"
                 styleClass="btn-info btn-xs" />-->
                    <!--//   11768 CRM-7634-->
<!--                    <p:commandButton id="btnCompSpecifierSearcho" value="Alias" title="Alias" rendered="#{quotEx.crmsyncDocCompType==12 and quotEx.crmsyncSysId==9}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForOasisResolveListPage('applyActiveGenericCompResolve', 12,quotEx)}"
                                     update=":formCompLookup  :frmLookupHead" immediate="true" process="@this" oncomplete="$('#formCompLookup\\:companyLookUp\\:name\\:filter').val('#{quotEx.crmsyncDocCompany}'); PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />-->
                    <!--13375  CRM-7815   OASIS: Assign the quotes to the correct user when ingesting from OASIS-->            
<!--                    <p:commandButton id="btnOasisOwnerSearcho" value="Alias" title="Alias" rendered="#{quotEx.crmsyncDocCompType==16 and quotEx.crmsyncSysId==9}"
                                     actionListener="#{userLookupService.listOasisOwner('applyOasisOwner',quotEx)}" 
                                     update=":formUserLookup" immediate="true" process="@this"
                                     oncomplete="PF('lookupUser').show(); $('#formCompLookup\\:companyLookUp\\:name\\:filter').val('#{quotEx.crmsyncDocCompany}'); PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />-->

                    <!--13391-->

                    <!--04-06-2024 13523 Code Optimisation for Oasis Integration Quotes and Jobs : Auto-download, Sync exceptio-->
                    <p:spacer width="4"/>
                    <p:commandButton id="btnAliasOasisCompany" value="Alias" title="Alias" 
                                     styleClass="btn-info btn-xs" immediate="true" process="@this" 
                                     rendered="#{(quotEx.crmsyncDocCompType==1 or quotEx.crmsyncDocCompType==3 or quotEx.crmsyncDocCompType==12)and quotEx.crmsyncSysId==9}"
                                     actionListener="#{oasisCrmService.onSelectedCrmsyncLog(quotEx)}"
                                     action="#{oasisCrmService.listCompanies('remoteCmdAliasOasisCompany', quotEx)}"
                                     update=":formCompLookup :frmLookupHead" 
                                     oncomplete="PF('compLookup').filter();" />

                    <p:commandButton id="btnAliasOasisQuoteOwner" value="Alias" title="Alias" 
                                     styleClass="btn-info btn-xs" immediate="true" process="@this" 
                                     rendered="#{(quotEx.crmsyncDocCompType==16) and quotEx.crmsyncSysId==9}"
                                     actionListener="#{oasisCrmService.onSelectedCrmsyncLog(quotEx)}"
                                     action="#{userLookupService.list('remoteCmdAliasOasisQuoteOwner')}"
                                     update=":formUserLookup" oncomplete="PF('lookupUser').show();"   />

                </p:column>   
                <p:column> 
                    <!--3748 CRM-4006: Quote import: BDA-IN - can't refresh a pending alias on quote/job import-->
                    <!--#7024 CRM-5185: Oasis: Auto-Download Process for Jobs and linked quotes (cron job)-->
                    <!--//23-02-2024 : #13083 : CRM-7745   thea crashing (Quotes)-->
                    <!--14303: 19/08/2024: ESCALATION CRM-8388: ISQuote: Quote Sync Exceptions Not Showing Option to Alias Companies & Contacts-->
                    <p:commandButton id="btnReAlias" value="Re-Sync" title="Re-Sync" rendered="#{quotEx.crmsyncDocCompType!=0 and quotEx.crmsyncSysId==6}"
                                     actionListener="#{iSQuoteCrmClient.resyncISQuotesDownload(quotEx)}"
                                     action="#{quotesHdr.exceptionListingForQuotes()}"  update=":frmQuoteException:tblQtException :quotLstForm:tblQt :quotLstForm"
                                     onclick="PF('reprocessButton').disable();" onstart="PF('imprtProgressDlg').show();" 
                                     oncomplete="PF('tblVarQtException').filter(); PF('imprtProgressDlg').hide(); PF('reprocessButton').enable();"
                                     immediate="true" process="@this"  
                                     styleClass="btn-info btn-xs" />
<!--                    <p:commandButton id="btnReAliaso" value="Re-Sync" title="Re-Sync" rendered="#{quotEx.crmsyncDocCompType!=0 and quotEx.crmsyncSysId==9}"
                                     actionListener="#{oasisService.resyncOasisQuotesDownload(quotEx)}"
                                     action="#{quotesHdr.exceptionListingForQuotes()}"  update=":frmQuoteException:tblQtException :quotLstForm:tblQt :quotLstForm"
                                     onclick="PF('reprocessButton').disable();" onstart="PF('imprtProgressDlg').show();" 
                                     oncomplete="PF('tblVarQtException').filter(); PF('imprtProgressDlg').hide(); PF('reprocessButton').enable();"
                                     immediate="true" process="@this"  
                                     styleClass="btn-info btn-xs" />-->

                    <!--04-06-2024 13523 Code Optimisation for Oasis Integration Quotes and Jobs : Auto-download, Sync exceptio-->
                    <!--05-12-2024 14845 4 ESCALATIONS CRM-8672   OASIS: Bring in the Quoter of Phase in Oasis as the Job Owner/Salesperson-->
                    <p:commandButton id="btnReSyncOasisJob" value="Re-Sync" title="Re-Sync" immediate="true" process="@this" styleClass="btn-info btn-xs"
                                     rendered="#{quotEx.crmsyncDocCompType!=0 and quotEx.crmsyncSysId==9}"
                                     onclick="PF('reprocessButton').disable();" 
                                     onstart="PF('imprtProgressDlg').show();" 
                                     actionListener="#{oasisCrmService.resyncQuotes(loginBean.userId, quotEx.crmsyncDocId)}"
                                     update=":quotLstForm"
                                     oncomplete="PF('tblVarQtException').filter(); PF('imprtProgressDlg').hide(); PF('reprocessButton').enable();"
                                     />


                </p:column> 
            </p:dataTable>
        </h:form>

    </p:dialog>
    <!--2521 Quotes > Updates to Sync Exceptions and auto download-->
    <p:dialog widgetVar="imprtProgressDlg" closable="false" modal="true" header="Message" resizable="false" width="30%" >
        <p:outputPanel style="text-align: center" >
            <br />
            <h:graphicImage  library="images" name="ajax-loader.gif" id="loadingImgImprt"   />
            <p:spacer width="8" />
            <p:outputLabel value="Please wait... Import in progress.." />
            <br /><br />
        </p:outputPanel>

    </p:dialog>

</ui:composition>