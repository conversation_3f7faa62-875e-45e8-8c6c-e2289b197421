<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--March 23 2020-->

    <p:dialog  styleClass="dialogCSS" id="isQuotListDtlsx" widgetVar="isQuotListDtls"  responsive="true" draggable="false" resizable="false"
               style="text-overflow: scroll;"  height="80%" width="80%" modal="true">
        <f:facet name="header">
            <h:outputText id="isQuotListTxt" value="ISQuote List" />
        </f:facet>
        <h:form id="isQfrm">
            <p:outputLabel rendered="#{quotesHdr.filterISQuotesStatus!=2}" id="dtIsQtLbl" value="Quotes for : " style="font-weight: bold;" /><p:spacer/>
            <p:outputLabel rendered="#{quotesHdr.filterISQuotesStatus!=2}" id="dtIsQt" value="#{quotesHdr.paramISQuotDate}" style="font-weight: bolder;" >
                <f:convertDateTime pattern="#{globalParams.dateFormat}" />  
            </p:outputLabel>
            <p:spacer/>
            <!--#{oAuthLogin.quotLookUp.get(0)}-->
            <!--rendered="#{oAuthLogin.quotLookUp.size()>0}"-->
            <!--1461 CRM-3100: FW: Questions/Comments from SOUTHWEST-->
            <p:dataTable value="#{iSQuoteCrmClient.quotLookUp}" rows="10"
                         var="isqHd" id="isq" rowIndexVar="isqRecc" 
                         emptyMessage="No Quotes available" 
                         scrollable="true"
                         scrollHeight="360"  
                         widgetVar="isqRecc"
                         selection="#{quotesHdr.selectedTSQhdr}"
                         selectionMode="single" 
                         rowKey="#{isqHd.recId}" paginator="true" 
                         >
                <!--#302 Quotes > Move Line Items to a new tab-->
                <!--<p:ajax event="rowSelect" listener="#{quotesHdr.selectSFQuote}" process="@this" immediate="true" update=":quotefrom:tabQuot :isQuotLnItmListDtlsx" oncomplete="PF('isQuotListDtls').hide(); PF('isQuotLnItmListDtls').show(); PF('qlWVar').clearFilters();" />-->
                <!--2161 ISQuotes Distribution List-->
                <!--14469: 08/10/2024: CRM-8480   ISQuote: job caught in aliaser hangs system when manually fetched-->
                <p:ajax event="rowSelect" onstart="PF('progressDlg123').show()" listener="#{quotesHdr.selectSFQuote1}" process="@this" immediate="true" update=":quotefrom:tabQuot :isQuotLnItmListDtlsx" oncomplete="PF('isQuotListDtls').hide(); PF('progressDlg123').hide()" />

                <!--  PF('qlWVar').clearFilters();  :quotefrom:isql-->
                <p:column  width="2%"> 
                    <f:facet name="header"></f:facet>
                        #{isqRecc+1}
                </p:column> 
                <p:column width="8%" >
                    <f:facet name="header">Quote Number</f:facet>
                        #{isqHd.quotNumber} 
                    <!--#{isqHd.recId}-->
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Quote Name</f:facet>
                        #{isqHd.quotName==null?'':isqHd.quotName} 
                </p:column>
                <p:column width="10%">
                    <f:facet name="header">Job Name</f:facet>
                        #{isqHd.jobName==null?'':isqHd.jobName} 
                </p:column>
                <p:column width="8%">
                    <f:facet name="header">Salesperson ID</f:facet>
                        #{isqHd.oppSManName=='null'?'':isqHd.oppSManName} 
                </p:column>
                <p:column width="10%">
                    <f:facet name="header">Customer Name</f:facet>
                        #{isqHd.oppCustomerName=='null'?'':isqHd.oppCustomerName} 
                </p:column>            
                <!--857 Quotes > Fetch IS Quote > Update to IS Quote Lookup - Include City-->
                <p:column width="10%">
                    <f:facet name="header">Customer City</f:facet>
                        #{isqHd.cityName=='null'?'':isqHd.cityName} 
                </p:column>
                <p:column width="10%" rendered="false">
                    <f:facet name="header">Contact Name</f:facet>
                        #{isqHd.oppPrincipalContName=='null'?'':isqHd.oppPrincipalContName} 
                </p:column>
                <p:column width="10%">
                    <!--4408   CRM-3916: HSArep: ISQuote: surface quote type in job-->
                    <f:facet name="header">Quote Type</f:facet> 
                    #{isqHd.quoteType==null?'':isqHd.quoteType} 
                </p:column>
                <p:column width="10%"> 
                    <!--2020 CRM-3380: ISQuote: fetching quote should have value listing when cherrypicking-->
                    <f:facet name="header">Quote Value</f:facet> 
                    #{isqHd.oppValue==null?'':isqHd.oppValue} 
                </p:column>
                <p:column width="10%"> 
                    <f:facet name="header">Quote Date</f:facet>
                    <!--#{isqHd.quotDate=='null'?'':isqHd.quotDate}--> 
                    <h:outputLabel   value="#{rFUtilities.convertFromUTC(isqHd.quotDate)}" >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" for="isqHd.quotDate"/>
                    </h:outputLabel>
                </p:column>
            </p:dataTable>

        </h:form>
    </p:dialog>
    
   
    <!--2161 ISQuotes Distribution List-->
<h:form>
   <p:confirmDialog widgetVar="quotDistDlg" header="Create Individual Quotes Confirmation" >
       <f:facet name="message">
           <p:outputLabel value=" There are quote distributions list. Do you want to create individual quotes for these distributors?" />
       </f:facet>
       <div class="div-center">
            <p:commandButton value="Yes" action="#{quotesHdr.selectSFQuote(1)}" oncomplete="PF('quotDistDlg').hide();" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" update=":isQuotLnItmListDtlsx" class="btn btn-success btn-xs"/>
            <!--<p:commandButton value="No" action="#{quotesHdr.selectSFQuote(0)}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" onclick="PF('quotDistDlg').hide();" />-->
                <p:spacer width="4"/>
            <p:commandButton value="No" action="#{quotesHdr.selectSFQuote(0)}" oncomplete="PF('quotDistDlg').hide();" styleClass="ui-confirmdialog-no" icon="ui-icon-check" update=":isQuotLnItmListDtlsx" class="btn btn-danger btn-xs" />
        </div>
   </p:confirmDialog>
</h:form>
</ui:composition>   



