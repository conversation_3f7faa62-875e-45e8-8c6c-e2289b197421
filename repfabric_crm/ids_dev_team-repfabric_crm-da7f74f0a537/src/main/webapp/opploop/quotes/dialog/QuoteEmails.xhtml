<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    
    <p:remoteCommand autoRun="false" name="loadQuoteEmail" actionListener="#{quotesEmailService.loadQuoteEmailContents(quotesHdr.recId)}" update=":quotefrom:tabQuot:quoteEmailDtl"  oncomplete="PF('emailQuotVar').filter();"  /> 
    <p:growl widgetVar="grl" showDetail="false"  />
    <p:outputPanel id="pnlEMAIL"  >
        <p:dataTable id="quoteEmailDtl" value="#{quotesEmailService.listEmails}" var="email"   filterEvent="keyup"   filteredValue="#{quotesEmailService.emailFilter}"
                     emptyMessage="No Emails found" widgetVar="emailQuotVar"  rowKey="#{email.recId}"  scrollable="true" scrollHeight="500" >

            <p:column style="width:30px" >
                <f:facet name="header">
                    <h:graphicImage  library="images" name="unlink.png" width="15" height="15" />
                </f:facet> 
<!--//           #9713 Quote: Line Item > Edit , Update > Line Item Details are not updating.-->
                <p:commandLink  oncomplete="PF('unlnkEmlDlg').show()"   
                                title="Unlink this email"
                                style="margin-left:3px;"  actionListener="#{emailHeader.setIDToUnlinkQuote(quotesHdr.recId,email.recId)}"

                                >
                    <h:graphicImage name="unlink.png" library="images" height="15" width="15"/> 

                </p:commandLink>
            </p:column>
            <p:column style="width:30px">

                <p:commandButton value="" title="Preview" 
                                 class="fa fa-eye" style="border: none;margin-left:-12px; background: white"
                                 actionListener="#{emailHeader.storeEmailContent(email)}"   oncomplete="PF('overlay').show()" 
                                 update=":quotefrom:tabQuot:opnl"
                                 />

            </p:column>
            <p:column headerText="Subject" style="min-width: 45%;max-width: 45%" filterBy="#{email.emailSubject}" filterMatchMode="contains" sortBy="#{email.emailSubject}" >
               
                 <p:commandLink  process="@this"  rendered="#{(email.emailUserId ne loginBean.userId)}" 
                               actionListener="#{emailHeader.setRandomUid(email.emailUserId,0)}" 
                               target="_blank"    title="View Email"   oncomplete="emailActions(#{email.emailUserId},#{email.emailId},'read',false)" update=":quotefrom:tabQuot:lbldeleg  :quotefrom:tabQuot:lblreq" 
                               >
                    #{email.emailSubject}
                   
                </p:commandLink>
               
                <h:commandLink  title="View Email"  rendered="#{(email.emailUserId eq loginBean.userId)}"  
                                onclick="emailActions(#{email.emailUserId},#{email.emailId}, 'read', true)">
                    #{email.emailSubject}
                </h:commandLink>

            </p:column>
            <p:column style="width:30px;padding: 0px 4px 0px 5px;">
                <f:facet name="header">
                    <h:graphicImage  class="fa fa-paperclip"/>
                </f:facet>
<!--//           #9713 Quote: Line Item > Edit , Update > Line Item Details are not updating.-->
                <p:commandButton  value=""  title="Attachments" 
                                  class="fa fa-paperclip" style="border: none;margin-left:-12px; background: white"
                                  action="#{emailAttachment.loadEmailAttchmentListForQuotes(email.emailId)}"
                                  oncomplete="PF('attchmentVar').show()" rendered="#{email.emailAttachFlag==1}"
                                  update=":quotefrom:tabQuot:atpnl"

                                  />
            </p:column>
            <p:column headerText="From"   style="width:250px"   filterBy="#{email.emailFrom}"  filterMatchMode="contains"  sortBy="#{email.emailFrom}" >
                #{email.emailFrom}
            </p:column>

            <!--//21-05-2024 : #13748 : CRM-7902   BCC field: Sending Quote doen't show as sent in Email tab-->
            <p:column headerText="Recipients"   style="width:250px"  filterBy="#{email.emailTo}" filterMatchMode="contains" sortBy="#{email.emailTo}">
                #{email.emailTo}
            </p:column>
            <p:column headerText="Date"  style="width:150px;" >
                <h:outputLabel  style="font-size: 0.95em" value="#{rFUtilities.getCurrentTimeZoneDate(email.emailDate, email.emailTime)}" >
                    <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                </h:outputLabel>
            </p:column>
            <p:column headerText="Action"  style="width:115px;" >

                <!--reply-->

                <p:commandButton rendered="#{(email.emailUserId ne loginBean.userId)}" icon="fa fa-mail-reply" 
                                 immediate="true"    class="btn-primary  btn-icon"
                                 actionListener="#{opportunities.replyEmail(email.emailId, 1)}" 
                                 title="Reply"  
                                 />

                <p:spacer width="4"/>
                <!--this will render if user is owner, or email userid is same as loggedin user-->
                <p:commandButton  class="btn-primary  btn-icon" title="Reply" icon="fa fa-mail-reply" 
                                  immediate="true"  action="#{opportunities.replyEmail(email.emailId, 1)}" 
                                  rendered="#{(email.emailUserId eq loginBean.userId)}" />

                <p:spacer width="4"/>
                <!--replyall-->
                <p:commandButton rendered="#{(email.emailUserId ne loginBean.userId)}"  icon="fa fa-mail-reply-all" 
                                 immediate="true"    class="btn-primary  btn-icon"
                                 actionListener="#{opportunities.replyEmail(email.emailId, 1)}" 
                                 update="" title="Reply All"  
                                 />

                <p:spacer width="4"/>
                <!--this will render if user is owner, or email userid is same as loggedin user-->
                <p:commandButton  class="btn-primary btn-icon" title="Reply All"   icon="fa fa-mail-reply-all" 
                                  immediate="true"    actionListener="#{opportunities.replyEmail(email.emailId,2)}" 
                                  rendered="#{(email.emailUserId eq loginBean.userId)}" />

                <p:spacer width="4"/>
                <!--forward-->

                <p:commandButton rendered="#{(email.emailUserId ne loginBean.userId)}"   icon="fa fa-mail-forward" 
                                 immediate="true"    process="@this"   class="btn-primary btn-icon"
                                 actionListener="#{opportunities.replyEmail(email.emailId, 3)}" 
                                 update="" title="Forward" 
                                 />

                <p:spacer width="4"/>               
                <!--this will render if user is owner, or email userid is same as loggedin user-->
                <p:commandButton  class="btn-primary btn-icon" title="Forward"    icon="fa fa-mail-forward" 
                                  immediate="true"  actionListener="#{opportunities.replyEmail(email.emailId,3)}" 
                                  rendered="#{(email.emailUserId eq loginBean.userId)}" />

            </p:column>

        </p:dataTable>    
        <h:inputHidden id="lblreq" value="#{emailHeader.rnd}"/>
        <h:inputHidden id="lbldeleg" value="#{emailHeader.chkDeleg}"/>
    </p:outputPanel>
    <p:confirmDialog header="Confirm Unlinking"  global="false"
                     message="Are you sure to Unlink?" 
                     widgetVar="unlnkEmlDlg" 
                     >

        <div style="text-align: center">

            <p:commandButton value="Unlink" 
                             styleClass="btn btn-danger btn-xs"
                             action="#{emailHeader.unlinkQuotes()}"
                             oncomplete="PF('unlnkEmlDlg').hide();loadQuoteEmail();" 
                             update=":quotefrom:tabQuot:quoteEmailDtl"

                             />

            <p:spacer width="4px" />
            <p:commandButton value="Cancel" 
                             styleClass="btn btn-warning btn-xs"
                             onclick="PF('unlnkEmlDlg').hide();"
                             type="button"  />
        </div>
    </p:confirmDialog>

    <p:dialog resizable="false" onShow="PF('overlay').initPosition();"  dynamic="true"  header="Email Preview" height="450" width="580"    id="opnl"    widgetVar="overlay" closeOnEscape="true" closable="true" >
        <table>              
            <tr style="height: 25px">
                <td style="width: 70px">
                    <h:outputLabel value="To" style="font-weight: bold" /></td>
                <td style="width: 15px">:</td>
                <td >
                    <h:outputText id="txtEmlTo"   value="#{emailHeader.emailTo}" /></td>
            </tr>
            <tr style="height: 25px">
                <td>
                    <h:outputLabel value="From" style="font-weight: bold" /></td>
                <td style="width: 15px">:</td>
                <td >
                    <h:outputText id="txtEmlFrm"  value="#{emailHeader.emailFrom}"  /></td>
            </tr>
<!--            //      #11078  ESCALATIONS CRM-6883   Companies: Email tab under company not working as expected-->
             <tr style="height: 25px">
                <td>
                    <h:outputLabel value="CC" style="font-weight: bold" /></td>
                <td style="width: 15px">:</td>
                <td >
                    <h:outputText id="txtEmlCC"  value="#{emailHeader.emailCc}"  /></td>
            </tr>
            <tr style="height: 25px">
                <td >
                    <h:outputLabel value="Subject"  style="font-weight: bold" /></td> 
                <td style="width: 15px">:</td>
                <td>
                    <h:outputText id="txtEmlSubj"  value="#{emailHeader.emailSubject}" /></td>
                <td>
                    <h:graphicImage id="imgEmlAtch" value="#{eml.emailAttachFlag}"  library="images" name="attach_icon.png" /></td>
            </tr>
        </table>
        <hr></hr>
        <div style="width: 548px;overflow-wrap: break-word;">
            <h:outputText id="txtEmlBody" value="#{emailHeader.emailContent}" escape="false"  style="width: 548px"    />
        </div>

    </p:dialog>


    <p:dialog resizable="true" onShow="PF('attchmentVar').initPosition();"  position="center"  header="Attachments" height="450" width="580"    id="atpnl"  class="dialogCSS"  widgetVar="attchmentVar" closeOnEscape="true" closable="true">

        <p:dataTable  value="#{emailAttachment.listEmailAttachments}"
                      style="width: 100%"
                      id="emAt"   var="emAt"  >
            <!--                #{emAt.recId}
            #{emAt.emailAttaEmailId}
            <p:column headerText="File Name" style="text-align: left">
            #{emAt.emailAttaLocation}
        </p:column>
          <p:column headerText="File Name" style="text-align: left">
            #{emAt.recId}
        </p:column>-->
            <p:column headerText="File Name" style="text-align: left" width="90">
                #{emAt.emailAttaName}
            </p:column>
            <p:column width="10" style="text-align: center">
                <p:commandButton immediate="true" process="@none" title="Download Attachment"  class="btn-primary btn-xs"  icon="fa fa-download" 
                                 onclick="primeFaces.monitorDownload(start, stop)">
                    <p:fileDownload    value="#{emAt.file}"/>
                </p:commandButton>
            </p:column>
        </p:dataTable>
    </p:dialog>
<!--//           #9713 Quote: Line Item > Edit , Update > Line Item Details are not updating.-->
<!--    <p:dialog widgetVar="loadingDlg" closable="false" modal="true" header="Message" resizable="false"  >
    
        <h:form id ="frmPoll">
            <h:outputText value="Loading Emails..." id="txtStatus" />
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif" id="loadingImg1"   />
                <p:spacer width="5" />
                <br /><br />
            </p:outputPanel>
        </h:form>
    </p:dialog>-->
    

    <p:dialog id="deml" header="Send Email" widgetVar="wgdlgDeleg" height="60" width="380" closeOnEscape="true" >
        <h:panelGrid columns="3">
            <h:outputLabel id="emulabel" value="Send as"/>
            <p:commandButton onclick="window.open(urlUser);
                    PF('wgdlgDeleg').hide();" value="Current User"  type="button"  disabled="#{opportunities.oppCloseStatus!=0}"  title="Reply"  class="button_top btn_tabs" style="background: none;"  />
            <p:commandButton onclick="window.open(urlDeleg);
                    PF('wgdlgDeleg').hide();" value ="Delegate" type="button"  disabled="#{opportunities.oppCloseStatus!=0}"  title="Reply"  class="button_top btn_tabs" style="background: none;"  />
        </h:panelGrid>
    </p:dialog>

    <script>
        function emailActions(eusr, mailid, act, acces) {
//           alert('role' + #{loginBean.loggedInUser.userRole} + '; user : ' + #{loginBean.userId} + '; mail user : ' + eusr + '; access : ' + acces );
//             alert('action');
            // set url for email action
            if (act === 'read') {
//                urlUser = '#{loginBean.emailURL}/action.php?action=view_content&amp;reqid=#{loginBean.randomUid}&amp;mail=' + mailid + '&amp;viewemail=1&amp;src=1';
                urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=maillist&amp;email_id=' + mailid;

            } else {
                //alert('else');
                if (act === 'forward') {
//                    urlUser = '#{loginBean.emailURL}/action.php?action=' + act + '&amp;mail=' + mailid + '&amp;reqid=#{loginBean.randomUid}&amp;composeemail=1&amp;forward=1&amp;src=1';
                    urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=forward&amp;email_id=' + mailid;

                } else {
//                    urlUser = '#{loginBean.emailURL}/action.php?action=' + act + '&amp;mail=' + mailid + '&amp;reqid=#{loginBean.randomUid}&amp;composeemail=1&amp;src=1';
                    if (act === 'reply') {
                        urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=reply&amp;email_id=' + mailid;
                    } else {
                        urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=replyall&amp;email_id=' + mailid;
                    }
                }
            }


            //current user have accss, so openemail tab
            if (acces === true) {
                openTab(urlUser);

                return;
            }

            // if not owner, not same user,chek value of lbldeleg
            // 1=in salesteam,2=in delegates, 3=both
            chk = document.getElementById('quotefrom:tabQuot:lbldeleg').value;

            // random id generated for email user id
            rq = document.getElementById('quotefrom:tabQuot:lblreq').value;


            //alert('chk : ' + chk + '; act : ' + act);
            //user in sales team
//            #14495 ESCALATIONS CRM-8484   Attaching Emails to Quotes
            if (chk == 1) {
               
                openTab(urlUser);
                return;
            }


            //user in deleg or in both sales tem and deleg
            if (chk == 2 || chk == 3) {


                if (act === 'read') {

                    // urlDeleg = '#{loginBean.emailURL}/action.php?action=view_content&amp;reqid=' + rq + '&amp;mail=' + mailid + '&amp;viewemail=1&amp;src=1';

                    urlDeleg = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=maillist&amp;email_id=' + mailid;


                } else {
                    if (act === 'forward') {
                        //    urlDeleg = '#{loginBean.emailURL}/action.php?action=' + act + '&amp;mail=' + mailid + '&amp;reqid=' + rq + '&amp;composeemail=1&amp;forward=1&amp;src=1';
                        urlDeleg = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=forward&amp;email_id=' + mailid;
                    } else {
//                        urlDeleg = '#{loginBean.emailURL}/action.php?action=' + act + '&amp;mail=' + mailid + '&amp;reqid=' + rq + '&amp;composeemail=1&amp;src=1';
                        if (act === 'reply') {
                            urlDeleg = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=reply&amp;email_id=' + mailid;
                        } else {
                            urlDeleg = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=replyall&amp;email_id=' + mailid;
                        }

                    }
                }

                document.getElementById('frmOpp:tabOpp:emulabel').innerHTML = getLabel(act);
                PF('wgdlgDeleg').show();

            } else {
                // email user not in deleg,sales team
                PF('grl').renderMessage({"summary": " No access to email ", "severity": "warn"});
            }

        }

        function openTab(url) {
            var win = window.open(url, '_blank');
            win.focus();
        }

        function getLabel(act) {
            var lblAction;
            switch (act) {
                case 'read':
                    lblAction = "View email as : ";
                    break;
                case 'reply':
                    lblAction = "Reply as : ";
                    break;
                case 'replyall':
                    lblAction = "s All as : ";
                    break;
                case 'forward':
                    lblAction = "Forward as : ";
                    break;
            }
            return lblAction;
        }


        function openEmailWin(url, winName, event) {
            this.target = '_blank';
            var win = window.open(url, winName);
            win.focus();
            var browser = navigator.userAgent.toLowerCase();
            if (browser.indexOf('firefox') > -1) {
                win.close();
                var win1 = window.open(url, winName);
                win1.focus();
            }
            event.preventDefault();

        }

    </script>
</ui:composition>