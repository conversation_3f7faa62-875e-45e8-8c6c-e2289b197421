<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--16-02-2023 10695 CRMSYNC-139  Add Autoquotes option to 'Fetch from' drop-down box-->
    <h:form id="frmUnResAutoQquoteLookup"> 

        <p:dialog  class="dialogCSS" id="dlgUnResAutoquote" widgetVar="wvDlgUnResAutoquote"  
                   responsive="true" draggable="false" resizable="false" header="AutoQuotes Lookup"
                   style="text-overflow: scroll;"  height="80%" width="80%" modal="true">

            <p:dataTable value="#{autoQuotesService.autoQutoesList}" filteredValue="#{autoQuotesService.autoQutoesListFlt}"
                         rows="10"
                         var="unResAutoQuote" id="dtAutoQuotes" widgetVar="wvDtAutoQuotes"
                         emptyMessage="No Quotes available" 
                         scrollable="true"  paginator="true" selectionMode="single"
                         selection="#{autoQuotesService.seletedQuote}" 
                         rowKey="#{unResAutoQuote.extId}" >

                <p:ajax event="rowSelect" listener="#{autoQuotesService.autoquoteSelectedToApply}" process="@this"
                        update=":frmQuoteCompsAliasLookup:opQuoteCompAliasLookup :frmQuoteCompsAliasLookup:opQuoteCompHdr"
                        oncomplete="PF('dlgQuoteCompsAlias').show();PF('wvDtQuoteCompsAlias').clearFilters();" />

                <p:column width="8%" styleClass="might-overflow" sortBy="#{unResAutoQuote.extId}"
                          filterBy="#{unResAutoQuote.extId}" filterMatchMode="contains">
                    <f:facet name="header">Ext. Quote ID</f:facet>
                        #{unResAutoQuote.extId}  
                </p:column>

                <p:column width="8%" styleClass="might-overflow" sortBy="#{unResAutoQuote.quoteDate}"
                          filterBy="#{unResAutoQuote.quoteDate}" filterMatchMode="contains">
                    <f:facet name="header">Quote Date</f:facet>
                        #{unResAutoQuote.quoteDate}  
                </p:column> 

                <p:column width="8%" styleClass="might-overflow" sortBy="#{unResAutoQuote.topic}"
                          filterBy="#{unResAutoQuote.topic}" filterMatchMode="contains">
                    <f:facet name="header">Name</f:facet>
                        #{unResAutoQuote.topic}  
                </p:column> 

                <p:column width="8%" styleClass="might-overflow" sortBy="#{unResAutoQuote.custName}"
                          filterBy="#{unResAutoQuote.topic}" filterMatchMode="contains">
                    <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet>
                        #{unResAutoQuote.custName}  
                </p:column> 

                <p:column width="8%" styleClass="might-overflow" sortBy="#{unResAutoQuote.compCity}"
                          filterBy="#{unResAutoQuote.compCity}" filterMatchMode="contains">
                    <f:facet name="header">City</f:facet>
                        #{unResAutoQuote.compCity}  
                </p:column>

                <p:column width="8%" styleClass="might-overflow" sortBy="#{unResAutoQuote.value}" 
                          filterBy="#{unResAutoQuote.value}" filterMatchMode="contains">
                    <f:facet name="header" >Quote Value</f:facet>
                    <h:outputLabel  value="#{unResAutoQuote.value}" style="width:100%;text-align: right">
                        <f:convertNumber groupingUsed="true"  maxIntegerDigits="15"  
                                         minFractionDigits="2" maxFractionDigits="2"/>
                    </h:outputLabel>
                </p:column> 
            </p:dataTable>

        </p:dialog>
    </h:form>

</ui:composition>   



