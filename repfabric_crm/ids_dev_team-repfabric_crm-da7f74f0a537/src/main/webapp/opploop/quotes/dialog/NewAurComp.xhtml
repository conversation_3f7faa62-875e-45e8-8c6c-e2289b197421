<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"> 
    <!--08-02-2023 10506 CRMSYNC-112/141: AutoQuotes: Quotes: Sync Status-->
    <p:dialog header="Target System New Account" modal="true" width="600" height="450" id="idDlgNewAurComp" widgetVar="dlgNewAurComp" 
              closeOnEscape="true" responsive="true" onShow="PF('dlgNewAurComp').initPosition();" >
        <h:form id="frmNewAurCompLokup" >

            <p:outputPanel id="opnlNewAurComp" >
                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-4" id="pnlgNewAurCOmp" 
                             styleClass="ui-noborder">

                    <p:outputLabel  value="Name:"/>
                    <p:outputLabel value="#{aurinkoCompService.aurinkoComp.tsCompName}" />

                    <p:outputLabel value="#{custom.labels.get('IDS_COMP_PHONE1')}:"/>
                    <p:outputLabel value="#{aurinkoCompService.aurinkoComp.phone}"  />

<!--                    <p:outputLabel value="#{custom.labels.get('IDS_FAX')}:"/>
                    <p:outputLabel value="#{oAuthBasedSyncService.aurNewComp.compFax}" />-->

                    <p:outputLabel value="Street:"/>
                    <p:outputLabel value="#{aurinkoCompService.aurinkoComp.street}"  />

                    <p:outputLabel value="City:"/>
                    <p:outputLabel value="#{aurinkoCompService.aurinkoComp.city}" />

                    <p:outputLabel value="#{custom.labels.get('IDS_STATE')}:"/>
                    <p:outputLabel value="#{aurinkoCompService.aurinkoComp.state}" />

                    <p:outputLabel value="#{custom.labels.get('IDS_ZIP')}:"/>
                    <p:outputLabel value="#{aurinkoCompService.aurinkoComp.postalCode}" />

                    <p:outputLabel value="Website:"/>
                    <p:outputLabel value="#{aurinkoCompService.aurinkoComp.website}"/>

                    <!--                    <p:outputLabel value="Comments:"/>
                                        <p:inputTextarea style="width: 100%" readonly="true" autoResize="false" rows="3"
                                                         value="#{oAuthBasedSyncService.aurNewComp.compComments}" />-->

                </p:panelGrid>
            </p:outputPanel>

            <br/>
            <br/>

            <div class="button_bar" style=" text-align: center" >
                <p:commandButton id="btnSaveNewAurComp" value="Create" title="Add new company in target system" immediate="true" 
                                 styleClass="btn btn-success btn-xs" onclick="PF('dlgNewAurComp').hide();"
                                 actionListener="#{aurinkoCompService.createNewAurCompany(aurinkoCompService.compPrinciId, aurinkoCompService.aurinkoComp)}"/> 
                <p:spacer width="4"/>
                <p:commandButton id="btnFrmNewAurCompCancel" value="Cancel" immediate="true"
                                 styleClass="btn btn-warning btn-xs" update=":frmNewAurCompLokup"
                                 actionListener="#{aurinkoCompService.clearAurNewComp()}"
                                 oncomplete="PF('dlgAurNewComp').hide();" /> 
            </div>

        </h:form>
    </p:dialog>
</ui:composition>