<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">

    <!--responsive="true"--> 
    <p:dialog  styleClass="dialogCSS" id="isQuotLnItmListDtlsx" widgetVar="isQuotLnItmListDtls"  draggable="false" resizable="false"
               style="text-overflow: scroll;"  height="80%" width="80%" modal="true" >
        <f:facet name="header">
            <h:outputText id="isQuotLnItmListTxt" value="Resolve Company names" />
        </f:facet> 
         <!--3167 Quotes > IS Quotes:  Resolve Companies window> Click on Close button. Topic, Quote # fetched.-->
        <p:ajax event="close" update=":quotefrom:tabQuot" listener="#{quotesHdr.clearFields()}"/>
        <h:form id="isQfrm2">
            <p:remoteCommand autoRun="true" update=":isQfrm2:isql"/>
            
            <p:remoteCommand name="applyActivePrinciResolve" autoRun="false" immediate="true" update=":isQfrm2:isql"
                             actionListener="#{quotesHdr.updateFieldsOnSelectionResolve(viewCompLookupService.selectedCompany)}"/>
      
           <!--11556: 29/01/2023:  ESCALATION CRM-7125 IS Quote: Specifier Contacts DO NOT populate when injesting a job from IS Quote.--> 
            <p:remoteCommand name="applyContForJob" actionListener="#{quotesHdr.updateJobContactOnSelectionResolve(viewContLookupService.selectedContact)}" update=":isQfrm2:isql" />

            <p:commandButton  value="Apply" title="Apply" class="btn btn-primary btn-xs" process="@this"  immediate="true" action="#{quotesHdr.saveResolved()}"
                              update=":quotefrom:tabQuot:tblQuotDtls" />
                            <!--   #{quotesHdr.quotLineItemLookUp.size()} ==
            #{quotesHdr.compResolveList.size()} ==
            #{iSQuoteCrmClient.multiplePrincipalNames.toString()}
    
            <p:inputNumber id="isql" value="999"/>-->

<!--        <p:dataTable value="#{quotesHdr.quotLineItemLookUp}" binding="#{isql}"
                     var="qlVar" id="isql"
                     emptyMessage="No Line Items availaable" 
                     scrollable="true"
                     scrollHeight="350" filteredValue="#{quotesHdr.quotLineItemLookUp}" 
                     rowKey="#{qlVar.quotTsLinkId}" 
                     widgetVar="qlWVar">
            <p:column width="10%">
                <f:facet name="header">Manf. Name</f:facet>
            #{qlVar.quotISQuotPrincipalName}
    </p:column>
    <p:column width="10%">
        <f:facet name="header">Part Number</f:facet>
            #{qlVar.oppItemPartManf}
    </p:column>
    <p:column width="10%">
        <f:facet name="header">Quote Link ID</f:facet>
            #{qlVar.quotTsLinkId}
    </p:column>
    <p:column width="10%">
        <f:facet name="header">Part Description</f:facet>
            #{qlVar.oppItemPartDesc}
    </p:column>
    <p:column width="10%">
        <f:facet name="header">Quantity</f:facet>
            #{qlVar.oppItemQnty}
    </p:column>
    <p:column width="10%">
        <f:facet name="header">Notes</f:facet>
            #{qlVar.quotNotes}
    </p:column>
    <p:column width="10%">
        <f:facet name="header">Unit Price</f:facet>
            #{qlVar.quotResale}
    </p:column>
    <p:column width="10%">
        <f:facet name="header">Extended Price</f:facet>
            #{qlVar.quotExtPrice}
    </p:column>
    </p:dataTable>      
            -->



            <p:dataTable value="#{quotesHdr.compResolveList}" 
                         var="errRec" id="isql" sortBy="#{errRec.recId}" sortOrder="ascending"
                         emptyMessage="No Companies available" 
                         scrollable="true" rowKey="#{errRec.recId}"
                         scrollHeight="350" filteredValue="#{quotesHdr.compResolveList}" 
                         widgetVar="qlWVar">
                <p:column  width="10%" rendered="false">
                    <f:facet name="header">Company ID</f:facet>
                    <h:outputText value="#{errRec.companyTypeId}" />
                </p:column>
                <p:column  width="10%">
                    <f:facet name="header">Company Type</f:facet>
                    <h:outputText value="#{errRec.companyTypeName}"/>
                </p:column>
                <p:column width="10%">
                    <f:facet name="header">ISQuote Company</f:facet>
                    <h:outputText value="#{errRec.quotISCustomerName}" rendered="#{errRec.companyTypeId==1}"/>
                    <h:outputText value="#{errRec.quotISPrincipalName}" rendered="#{errRec.companyTypeId==2}"/>
                    <h:outputText value="#{errRec.quotISDistrbutorName}" rendered="#{errRec.companyTypeId==3}"/>
<!--11556: 29/01/2023:  ESCALATION CRM-7125 IS Quote: Specifier Contacts DO NOT populate when injesting a job from IS Quote.-->
                    <h:outputText value="#{errRec.quotISJobContactName}" rendered="#{errRec.companyTypeId==9}"/>
                </p:column>
                <!--857 Quotes > Fetch IS Quote > Update to IS Quote Lookup - Include City-->
<!--                <p:column  width="10%">
                    <f:facet name="header">City</f:facet>
                    <h:outputText value="#{errRec.quotISCustomerCityName}"  rendered="#{errRec.companyTypeId==1}"/>
                </p:column>-->
                
                <!--4016 CRM-3917: JOHN:  hsarep: no way to tell which company it is-->
                <p:column  width="10%" >
                    <f:facet name="header">City</f:facet>
                    <h:outputText value="#{errRec.quotISCity}"  rendered="true"/></p:column>
                <p:column  width="10%"  >
                    <f:facet name="header">State</f:facet>
                    <h:outputText value="#{errRec.quotISState}"  rendered="true"/>                      
                </p:column>
                <p:column  width="10%" >
                    <f:facet name="header">Zip</f:facet>
                    <h:outputText value="#{errRec.quotISZip}"  rendered="true"/> 
                </p:column>
                
                
                
                <p:column width="10%">
                    <f:facet name="header">RF Company</f:facet>
                    <h:outputText value="#{errRec.quotCustomerName}" rendered="#{errRec.companyTypeId==1}"/>
                    <h:outputText value="#{errRec.quotPrincipalName}" rendered="#{errRec.companyTypeId==2}"/>
                    <h:outputText value="#{errRec.quotDistrbutorName}" rendered="#{errRec.companyTypeId==3}"/>
<!--11556: 29/01/2023:  ESCALATION CRM-7125 IS Quote: Specifier Contacts DO NOT populate when injesting a job from IS Quote.-->
                    <h:outputText value="#{errRec.quotJobContactName}" rendered="#{errRec.companyTypeId==9}"/>
                </p:column>
                <p:column width="10%" rendered="false">
                    <f:facet name="header">Ids</f:facet>
                    <h:outputText value="#{errRec.quotISPrincipalIdStr}"/>-
                    <h:outputText value="#{errRec.quotISCustomerId}"/>-
                    <h:outputText value="#{errRec.quotISPrincipalId}"/>
                </p:column>
                <p:column width="10%">
                    <f:facet name="header"></f:facet>
                    <!--9437: 09/11/2022: Feature CRM-6203  ISQuote: Aliasing - when creating a new company, use the ISQuote company details-->
                    <p:commandButton id="btnCompCustSearch" icon="fa fa-search"  title="Choose Company"  rendered="#{errRec.companyTypeId==1}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForResolve('applyActivePrinciResolve', 5,errRec.recId, errRec.quotISCustomerId)}"
                                     update=":formCompLookup :frmLookupHead" immediate="true" process="@this" oncomplete="$('#formCompLookup\\:companyLookUp\\:name\\:filter').val('#{errRec.quotISCustomerName}');PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />
                    <!--9437: 09/11/2022: Feature CRM-6203  ISQuote: Aliasing - when creating a new company, use the ISQuote company details-->
                    <p:commandButton id="btnCompPrinciSearch" icon="fa fa-search" title="Choose Company" rendered="#{errRec.companyTypeId==2}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForResolve1('applyActivePrinciResolve', 1,errRec.recId, errRec.quotISPrincipalIdStr)}"
                                     update=":formCompLookup :quotefrom:btnPart :frmLookupHead" immediate="true" process="@this" oncomplete="$('#formCompLookup\\:companyLookUp\\:name\\:filter').val('#{errRec.quotISPrincipalName}');PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />
                    <!--2161 ISQuotes Distribution List-->
                    <!--9437: 09/11/2022: Feature CRM-6203  ISQuote: Aliasing - when creating a new company, use the ISQuote company details-->
                    <p:commandButton id="btnCompDistriSearch" icon="fa fa-search" title="Choose Company" rendered="#{errRec.companyTypeId==3}"
                                     actionListener="#{quotesHdr.listLookUpvaluesForResolve('applyActivePrinciResolve', 3,errRec.recId, errRec.quotISDistrbutorId)}"
                                     update=":formCompLookup :quotefrom:btnPart :frmLookupHead" immediate="true" process="@this" oncomplete="$('#formCompLookup\\:companyLookUp\\:name\\:filter').val('#{errRec.quotISDistrbutorName}');PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />

<!--11556: 29/01/2023:   ESCALATION CRM-7125 IS Quote: Specifier Contacts DO NOT populate when injesting a job from IS Quote.-->
                    <p:commandButton  icon="fa fa-search" title="Choose Contact" immediate="true"  rendered="#{errRec.companyTypeId==9}"
                                          actionListener="#{viewContLookupService.listiSQCnt('applyContForJob',2,errRec.recId,0,1)}" 
                                          update=":formContLookup :formNew" oncomplete="PF('lookupCont').show();"  
                                          styleClass="btn-info btn-xs" />
                
                </p:column> 
            </p:dataTable>

<!--                          <p:dataList value="#{oppService.opp.otherPartyList}" var="opot" id="tblOthrList" varStatus="status">    
<div class="ui-g ui-fluid">
<div class="ui-sm-12 ui-md-4 ui-lg-4" style="padding-left: 0px">

  <p:outputLabel value="#{opot.compTypeName}" id="lbl#{status.index}"></p:outputLabel>
</div>
<div class="ui-sm-12 ui-md-8 ui-lg-8" style="padding-left: 4px"> 
  <p:inputText readonly="true" value="#{opot.compName}" id="txt#{status.index}"  style="width: 250px;"/><p:spacer width="6"/>
  <p:commandLink actionListener="#{oppService.removeOtherComp}" update="frmOpp:tabOpp:tblOthrList" type="button" style="font-size: larger; color: red;" title="Remove Other Parties">
      <f:param  name="indexPtr" value="#{status.index}" />
      <f:param  name="compTypeName" value="#{opot.compTypeName}" />
      <f:param  name="compName" value="#{opot.compName}" />
      <h:outputText value="X"/>
  </p:commandLink>

</div> 
</div>
</p:dataList>-->
        </h:form>
    </p:dialog>
</ui:composition>



