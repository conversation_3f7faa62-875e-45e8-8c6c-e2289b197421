<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--13-03-2024 13234 Code Optimisation for Oasis Integration Quotes and Jobs : Manual fetch-->
    <h:form id="frmOasisCompanyLink">
        <p:dialog  id="dlgOasisCompanyLink" widgetVar="wvDlgOasisCompanyLink"  draggable="false" resizable="false"
                   style="text-overflow: scroll;"  height="80%" width="80%" modal="true" >
            <f:facet name="header">
                <h:outputText value="Resolve Entities" />
            </f:facet> 
            <p:outputPanel id="opOasisCompanyLinkList">
                <p:remoteCommand name="applyOasisCompanyLink" autoRun="false" immediate="true" 
                                 actionListener="#{oasisCrmService.updateCompanyLinks(viewCompLookupService.selectedCompany)}"
                                 update=":frmOasisCompanyLink:opOasisCompanyLinkList" />

                <!--15-05-2024 13523 Code Optimisation for Oasis Integration Quotes and Jobs : Auto-download, Sync exceptio-->
                <p:remoteCommand name="applyOasisQuoteOwnerLink" autoRun="false" immediate="true" 
                                 actionListener="#{oasisCrmService.applyAlias(oasisCrmService.selectedCrmsyncAliases.aliasRefId,oasisCrmService.selectedCrmsyncAliases.aliasType,userLookupService.user.userId,userLookupService.user.userName)}"
                                 update=":frmOasisCompanyLink:opOasisCompanyLinkList" />

                <p:commandButton  value="Apply" title="Apply" class="btn btn-primary btn-xs" process="@this" immediate="true" 
                                  onclick="PF('dlgProcessing').show();"
                                  actionListener="#{oasisCrmService.onApplyOasisQuote()}"
                                  update=":quotefrom:tabQuot:tblQuotDtls"  
                                  oncomplete="PF('dlgProcessing').hide();"/> 


                <p:dataTable value="#{oasisCrmService.crmsyncAliasesList}" 
                             var="companyLink" id="dtOasisCompanyResolve" sortBy="#{companyLink.recId}" sortOrder="ascending"
                             emptyMessage="No Companies available" 
                             scrollable="true" 
                             scrollHeight="350"   
                             widgetVar="wvDtOasisCompanyResolve">
                    <p:column  width="10%" rendered="false">
                        <f:facet name="header">Company ID</f:facet>
                        <h:outputText value="#{companyLink.aliasRefId}" />
                    </p:column>
                    <p:column  width="10%">
                        <f:facet name="header">Company Type</f:facet>
                        <h:outputText value="#{companyLink.aliasTypeName}"/>
                    </p:column>
                    <p:column  width="10%">
                        <f:facet name="header">Company Sub Type</f:facet>
                        <h:outputText value="#{companyLink.aliasSubType}"/>
                    </p:column>

                    <p:column width="10%">
                        <f:facet name="header">Oasis Company</f:facet>
                        <h:outputText value="#{companyLink.aliasName}" />
                    </p:column>

                    <p:column  width="10%"  rendered="false">
                        <f:facet name="header">REC id</f:facet>
                        <h:outputText value="#{companyLink.recId}" />
                    </p:column>

                    <p:column width="10%">
                        <f:facet name="header">RF Company</f:facet>
                        <h:outputText value="#{companyLink.aliasRfName}"/>
                    </p:column>
                    <p:column width="10%">
                        <f:facet name="header"></f:facet>
                        <!--15-05-2024 13523 Code Optimisation for Oasis Integration Quotes and Jobs : Auto-download, Sync exceptio-->
                        <p:commandButton id="btnOasisCompanyLink" icon="fa fa-search" title="Choose Company" 
                                         styleClass="btn-info btn-xs" immediate="true" process="@this" rendered="#{companyLink.aliasType!=16}"
                                         actionListener="#{oasisCrmService.listRfCompanies('applyOasisCompanyLink', companyLink)}"
                                         update=":formCompLookup :frmLookupHead" 
                                         oncomplete="PF('compLookup').filter();" />

                        <!--15-05-2024 13523 Code Optimisation for Oasis Integration Quotes and Jobs : Auto-download, Sync exceptio-->
                        <p:commandButton id="btnOasisQuoteOwnerLink" icon="fa fa-search" title="Choose User" 
                                         styleClass="btn-info btn-xs" immediate="true" process="@this" rendered="#{companyLink.aliasType==16}"
                                         actionListener="#{oasisCrmService.onAliasSelected(companyLink)}"
                                         action="#{userLookupService.list('applyOasisQuoteOwnerLink')}"
                                         update=":formUserLookup" oncomplete="PF('lookupUser').show();"   />
                    </p:column> 
                </p:dataTable>
            </p:outputPanel>
        </p:dialog>
    </h:form>

</ui:composition>



