<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<!--24-01-2023 10506 CRMSYNC-112/141: AutoQuotes: Quotes: Sync Status-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <ui:define name="head">
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
        <title>Sync Status</title>
    </ui:define>

    <ui:define name="metadeta">
        <f:metadata> 
            <!--17-02-2023 10710 CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
            <f:viewParam name="option" value="#{autoQuotesService.syncStatusOptions}"/>
            <f:viewAction action="#{autoQuotesService.initScreen(loginBean.userId)}" />
        </f:metadata>
    </ui:define>

    <ui:define name="title">
        <ui:param name="title" value="Sync Status"/>
        <div class="row">
            <div class="col-md-6">
                <!--17-02-2023 10710 CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                <h:form id="frmSyncConTitle">
                    #{autoQuotesService.pageTitle}
                </h:form>  
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>

    <ui:define name="body">

        <div class="box box-info box-body" style="vertical-align: top">
            <h:form id="frmAurQuotesSyncStatus" >  

                <p:remoteCommand id="applyQuoteCompAlias" name="applyQuoteCompAlias" autoRun="false"
                                 actionListener="#{autoQuotesService.applyTargetComp(aurinkoCompService.aurinkoComp.rfCompId, aurinkoCompService.aurinkoComp.tsCompId, aurinkoCompService.aurinkoComp.tsCompName)}" 
                                 update=":frmQuoteCompsAliasLookup:opQuoteCompAliasLookup :frmAurQuotesSyncStatus:grlAurQuotesSyncStatus"/>

                <p:remoteCommand id="applyQuoteNewRfCompAlies" name="applyQuoteNewRfCompAlies" autoRun="false"
                                 action="#{autoQuotesService.applyRfComp(aurinkoCompService.aurinkoComp.tsCompId,viewCompLookupService.selectedCompany.compId,viewCompLookupService.selectedCompany.compName)}"
                                 update=":frmQuoteCompsAliasLookup:opQuoteCompAliasLookup :frmAurQuotesSyncStatus:grlAurQuotesSyncStatus"/>

                <!--13-05-2024 13633 CRMSYNC-662: AutoQuotes: Alias Contact on Quote creation-->
                <p:remoteCommand id="applyQuoteNewRfContAlies" name="applyQuoteNewRfContAlies" autoRun="false"
                                 action="#{autoQuotesService.applyRfComp(aurinkoCompService.aurinkoComp.tsCompId,viewContLookupService.selectedContact.contId,viewContLookupService.selectedContact.contFullName)}"
                                 update=":frmQuoteCompsAliasLookup:opQuoteCompAliasLookup :frmAurQuotesSyncStatus:grlAurQuotesSyncStatus"/>

                <p:remoteCommand id="rcUpdateDtAurQuotesSyncSttus" name="rcUpdateDtAurQuotesSyncSttus" autoRun="true" 
                                 update=":frmAurQuotesSyncStatus:opAurQuotesSyncStatus :frmAurQuotesSyncStatus:dtAurQuotesSyncStatus"
                                 oncomplete="PF('wvDTAurQuotesSyncStatus').clearFilters();PF('wvDTAurQuotesSyncStatus').paginator.setPage(0)" immediate="true"/>

                <!--07-10-2024 13633 CRMSYNC-662/688CRM-7992: AutoQuotes: Alias Contact on Quote creation-->
                <p:remoteCommand id="rcOnQuoteCreateDone" name="rcOnQuoteCreateDone" autoRun="false"
                                 actionListener="#{autoQuotesService.quoteCreated(loginBean.userId)}"
                                 update=":frmAurQuotesSyncStatus:grlAurQuotesSyncStatus"
                                 oncomplete="rcUpdateDtAurQuotesSyncSttus();PF('dlgQuoteCompsAlias').hide();PF('dlgProcessing').hide();"/>

                <p:growl id="grlAurQuotesSyncStatus" escape="false" globalOnly="false" 
                         life="6000" showDetail="false" showSummary="true"/>

                <div class="box-header with-border">

                    <div class="row">
                        <div class="col-md-12" >  
                            <!--17-02-2023 10710 CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                            <p:row>
                                <p:outputPanel id="opAutoquoteSyncOptions" style="margin-bottom: 10px;  display: flex;">
                                    <p:outputLabel value="Show" /> 
                                    <p:spacer width="4"/>
                                    <p:selectOneMenu id="opOption" value="#{autoQuotesService.syncStatusOptions}" style=" width: 250px;">
                                        <f:selectItem itemValue="0" itemLabel="Unresolved Target System #{custom.labels.get('IDS_QUOTES')}"/>
                                        <f:selectItem itemValue="1" itemLabel="Sync Errors"/>
                                        <!--03-08-2023 11801 CRMSYNC-359   Sync Logic. Quote. RF UI-->
                                        <p:ajax event="change" onstart="showFetchDlg()"
                                                listener="#{autoQuotesService.onOptionChanged()}" 
                                                update=":frmSyncConTitle :frmAurQuotesSyncStatus:grlAurQuotesSyncStatus" 
                                                process="@this" oncomplete="rcUpdateDtAurQuotesSyncSttus();PF('dlgFetchRec').hide();"/>
                                    </p:selectOneMenu> 
                                </p:outputPanel>
                            </p:row>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-md-12" >
                            <p:outputPanel  id="opAurQuotesSyncStatus" style=" height: 90vh">

                                <p:dataTable id="dtAurQuotesSyncStatus"  widgetVar="wvDTAurQuotesSyncStatus" 
                                             value="#{autoQuotesService.autoQutoesList}"
                                             filteredValue="#{autoQuotesService.autoQutoesListFlt}"
                                             scrollHeight="100%" scrollable="true"
                                             var="quotesStatus"
                                             rowIndexVar="rowQuotesSyncStatusIdx"
                                             filterEvent="keyup paste"
                                             emptyMessage="No records found"  
                                             rowKey="#{quotesStatus.extId}"
                                             paginator="true"
                                             rowsPerPageTemplate="50,100" 
                                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                             paginatorAlwaysVisible="true" >  

                                    <f:facet name="header">
                                        <div style="display: inline-flex; text-align:right;width: 100%">
                                            <h:panelGroup style=" width: 100%">
                                                <!--27-03-2023 10961 CRM-6815   AutoQuotes: Jobs UI - Fetch Jobs-->
                                                <!--04-08-2023 11801 CRMSYNC-359   Sync Logic. Quote. RF UI-->
                                                <p:commandButton id="btnLoadMoreQuoteSyncStatus"  class="btn-primary btn-xs" 
                                                                 value="Load more" style="float: right"  
                                                                 onclick=" PF('dlgFetchRec').show();" 
                                                                 action="#{autoQuotesService.loadMoreQuotes(loginBean.userId)}"
                                                                 update=":frmAurQuotesSyncStatus:dtAurQuotesSyncStatus :frmAurQuotesSyncStatus:grlAurQuotesSyncStatus"
                                                                 oncomplete="PF('wvDTAurQuotesSyncStatus').filter();PF('dlgFetchRec').hide();"
                                                                 rendered="#{autoQuotesService.moreData}"/>
                                            </h:panelGroup>
                                        </div>
                                    </f:facet>

                                    <!--17-02-2023 10710 CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                                    <p:column style="width:50px!important" visible="#{autoQuotesService.syncStatusOptions==1}" 
                                              rendered="#{autoQuotesService.syncStatusOptions==1}">
                                        <p:rowToggler rendered="#{(autoQuotesService.syncStatusOptions==1) and (quotesStatus.lineItems.size()>0)}"/>    
                                    </p:column>

                                    <!--17-02-2023 10710 CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                                    <p:column id="clmnDtBtnsAurQuoteSyncStatus" visible="#{autoQuotesService.syncStatusOptions==0}"
                                              style="width:120px!important" >

                                        <p:commandButton icon="fa fa-search" rendered="false" styleClass="btn-info btn-xs" title="Yet to be implemented" 
                                                         onclick="PF('dlgFetchRec').show();" 
                                                         oncomplete="PF('dlgFetchRec').hide();" 
                                                         update=":frmAurQuotesSyncStatus:dtAurQuotesSyncStatus :frmAurQuotesSyncStatus:grlAurQuotesSyncStatus" />  

                                        <p:spacer width="4"/>
                                        <p:commandButton icon="fa fa-plus" styleClass="btn-info btn-xs" title="Add this  Quote to Repfabric"
                                                         actionListener="#{autoQuotesService.quoteSelected(true,quotesStatus)}"
                                                         update=":frmQuoteCompsAliasLookup:opQuoteCompAliasLookup :frmQuoteCompsAliasLookup:opQuoteCompHdr"
                                                         oncomplete="PF('wvDtQuoteCompsAlias').clearFilters();PF('dlgQuoteCompsAlias').show();" />
                                        <p:spacer width="4"/>
                                        <p:commandButton icon="fa fa-eye" title="View other companies" 
                                                         styleClass="btn-info btn-xs"
                                                         actionListener="#{autoQuotesService.quoteSelected(false,quotesStatus)}"
                                                         update=":frmQuoteCompsAliasLookup:opQuoteCompAliasLookup :frmQuoteCompsAliasLookup:opQuoteCompHdr"
                                                         oncomplete="PF('wvDtQuoteCompsAlias').clearFilters();PF('dlgQuoteCompsAlias').show();" 
                                                         />
                                    </p:column>

                                    <p:column id="clmnDtAurQuotesSyncStatusCust" headerText="#{custom.labels.get('IDS_CUSTOMER')}"
                                              filterBy="#{quotesStatus.custName}" filterMatchMode="contains" sortBy="#{quotesStatus.custName}" >

                                        <h:outputText value="#{quotesStatus.custName}" />

                                    </p:column>

                                    <!--17-02-2023 10710 CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                                    <p:column  id="clmnDtAurQuotesSyncStatusTopic"
                                               headerText=" #{autoQuotesService.syncStatusOptions==0 ? 'Topic':'Name'}" 
                                               filterBy="#{quotesStatus.topic}" filterMatchMode="contains" 
                                               sortBy="#{quotesStatus.topic}" styleClass="might-overflow">

                                        <h:outputText value=" #{quotesStatus.topic}" />

                                    </p:column>
                                    <!--11315: 24/05/2023: CRM-7012   AutoQuotes Sync Status UI: Sorting by Quote Date does not sort quotes in chronological or-->
                                    <p:column  id="clmnDtAurQuoteSyncStatusDate" headerText="Quote Date" 
                                               style="width:120px!important"  
                                               filterBy="#{quotesStatus.quoteDate}" filterMatchMode="contains" 
                                               sortBy="#{quotesStatus.quoteDate1}" styleClass="might-overflow">

                                        <p:outputLabel id="clmnOlDtAurQuoteSyncStatusDate" value="#{quotesStatus.quoteDate}" />

                                    </p:column>

                                    <p:column  id="clmnDtAurQuoteSyncStatusValue" headerText="Value" style="width:120px!important;"
                                               filterBy="#{quotesStatus.value}" filterMatchMode="contains" 
                                               sortBy="#{quotesStatus.value}" styleClass="might-overflow">

                                        <h:outputLabel value="#{quotesStatus.value}" style="width:100%;text-align: right">
                                            <f:convertNumber groupingUsed="true"  maxIntegerDigits="15"  
                                                             minFractionDigits="2" maxFractionDigits="2"/>
                                        </h:outputLabel>

                                    </p:column>

                                    <!--17-02-2023 10710 CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                                    <p:column id="clmnDtAurQuotesSyncExcStatus" visible="#{autoQuotesService.syncStatusOptions==0}"
                                              headerText="Status" style="width:100px!important" 
                                              filterBy="#{quotesStatus.status}" filterMatchMode="contains" 
                                              sortBy="#{quotesStatus.status}" styleClass="might-overflow">

                                        <h:outputText value=" #{quotesStatus.status}" />

                                    </p:column> 

                                    <!--17-02-2023 10710 CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                                    <p:column  id="clmnDtAurQuotesSyncStatusExtId" visible="#{autoQuotesService.syncStatusOptions==0}"
                                               headerText="Ext. Id"
                                               filterBy="#{quotesStatus.extId}" filterMatchMode="contains" 
                                               sortBy="#{quotesStatus.extId}" styleClass="might-overflow">

                                        <p:outputLabel id="clmnlblDtAurQuoteSyncStatusExtId" value="#{quotesStatus.extId}" />

                                    </p:column>

                                    <!--17-02-2023 10710 CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                                    <p:column  id="clmnDtAurQuotesSyncStatusError" visible="#{autoQuotesService.syncStatusOptions==1}"
                                               headerText="Error"
                                               filterBy="#{quotesStatus.error}" filterMatchMode="contains" 
                                               sortBy="#{quotesStatus.error}" styleClass="might-overflow">

                                        <p:outputLabel value="#{quotesStatus.error}" />

                                    </p:column>

                                    <!--17-02-2023 10710 CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                                    <p:rowExpansion id="reSyncStatusAutoquoteItems">                                             
                                        <p:dataTable scrollable="false"
                                                     rows="10" 
                                                     widgetVar="wvDtAutoqutoeItems"
                                                     value="#{quotesStatus.lineItems}" 
                                                     var="AutoQuoteItem" id="dtAutoqutoeItems" 
                                                     rowKey="#{AutoQuoteItem.extItemId}"
                                                     paginator="true" 
                                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                                     rowsPerPageTemplate="10,20" 
                                                     paginatorAlwaysVisible="false" 
                                                     draggableColumns="true"
                                                     resizableColumns="true" resizeMode="expand"
                                                     rowStyleClass="aur_sync_exc_li"
                                                     style="margin-right: 5px !important;margin-left: -10px!important"
                                                     class="hide-column-names aur_sync_exc-tbl">

                                            <p:column headerText="Name" styleClass="might-overflow"> 
                                                <p:outputLabel value="#{AutoQuoteItem.topic}"/>
                                            </p:column>

                                            <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" styleClass="might-overflow">
                                                #{AutoQuoteItem.princiName}
                                            </p:column>

                                            <p:column headerText="Qty" styleClass="might-overflow">
                                                <p:outputLabel id="lblSyncExcPtNo" value="#{AutoQuoteItem.qty}"/>
                                            </p:column>

                                            <p:column headerText="Standard Price"  styleClass="might-overflow">
                                                <h:outputLabel value="#{AutoQuoteItem.stdPrice}" style="width:100%; text-align: right">
                                                    <f:convertNumber groupingUsed="true"  maxIntegerDigits="15"  
                                                                     minFractionDigits="2" maxFractionDigits="2"/>
                                                </h:outputLabel>
                                            </p:column>
                                            <p:column headerText="Error" styleClass="might-overflow">
                                                <p:outputLabel value="#{AutoQuoteItem.error}" />
                                            </p:column>
                                        </p:dataTable>
                                    </p:rowExpansion>
                                </p:dataTable>
                            </p:outputPanel>
                        </div>
                    </div>
                </div>

            </h:form>

            <ui:include src="../../resources/dialogs/DlgFetchAndProc.xhtml" />
            <ui:include src="dialog/QuoteSyncCompanies.xhtml"/>
            <!--<ui:include src="dialog/QuoteAurCompLookup.xhtml"/>-->
            <!--<ui:include src="dialog/NewAurComp.xhtml"/>-->
            <ui:include src="../../lookup/CompanyLookupDlg.xhtml" />
            <!--03-05-2024 13633 CRMSYNC-662: AutoQuotes: Alias Contact on Quote creation-->
            <ui:include src="../../lookup/ContactLookupDlg.xhtml" />

            <style>

                .ui-datatable-scrollable-body{
                    outline: 0px;
                }

                .ui-datatable-scrollable-header *,
                .ui-datatable-scrollable-theadclone * {
                    -moz-box-sizing: content-box;
                    -webkit-box-sizing: content-box;
                    box-sizing: content-box;
                    width: 100%;
                }
                body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
                    width: 15px;
                }

                .ui-datatable-scrollable-theadclone {
                    visibility: collapse !important;
                }
                .sss{
                    overflow-x: scroll;
                    overflow-y: scroll;
                    overflow: auto;
                }

                .ui-datatable-tablewrapper {
                    overflow: initial !important; 
                }
                .ui-paginator {
                    background-color:#dedede!important;
                }
                .q{
                    width: 100%!important;
                }
                .p{
                    width: 80%!important;
                }
                .might-overflow {
                    text-overflow: ellipsis;
                    overflow : hidden;
                    white-space: nowrap;
                }

                .might-overflow:hover {
                    text-overflow: clip;
                    white-space: normal;
                    word-break: break-all;
                }

                .aur_sync_exc-odd{
                    background: rgba(199, 205, 213, 0.05);
                }

                .aur_sync_exc tbody,
                .aur_sync_exc tbody tr,
                .aur_sync_exc tbody td{
                    border: 1px solid #C4C4C4 !important;

                }

                .ui-datatable td[role="gridcell"], body .ui-treetable td[role="gridcell"] {
                    position: relative;
                    padding: 4px 10px !important;
                }
                .aur_sync_exc_li{
                    background-color:#D9E0E6!important;
                    /*                    margin-left: 10px;
                                        margin-right: 10px;*/
                }

            </style>

            <script>
                function showFetchDlg() {
                    PF('dlgFetchRec').show();
                }
                function hideFetchDlg() {
                    PF('wvDTAurSyncExc').clearFilters();
                    PF('dlgFetchRec').hide();
                }

                window.onload = function () {
                    PF('dlgProcessing').hide();
                };
            </script>

            <h:outputScript>
                PF('dlgProcessing').show();
            </h:outputScript>
        </div>
    </ui:define>
</ui:composition>
