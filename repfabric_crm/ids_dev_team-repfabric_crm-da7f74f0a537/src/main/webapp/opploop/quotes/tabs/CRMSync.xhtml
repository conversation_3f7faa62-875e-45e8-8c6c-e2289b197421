<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--11-08-2023 11857 CRMSYNC-359 Sync Logic. Quote. RF UI-->
    <style>
        .viewOpp {
            border:1px solid #a8a8a8;
            border-radius:1px;
            background: #c4c4c4 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
            padding: .4em 1em;
            text-decoration: none;
            font-size: 14px;
            padding-bottom:.5em;
            height:20px;
            border: 1px solid #a8a8a8;
            background: #c4c4c4 url(/RepfabricCRM/javax.faces.resource/images/ui-bg_highlight-hard_80_c4c4c4_1x100.png?ln=primefaces-aristo) 50% 50% repeat-x;
            background: #c4c4c4 linear-gradient(top, rgba(255,255,255,0.8), rgba(255,255,255,0));
            background: #c4c4c4 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
            background: #c4c4c4 -moz-linear-gradient(top, rgba(255,255,255,0.8), rgba(255,255,255,0));
            font-weight: bold;
            color: #4f4f4f;
            text-shadow: 0px 1px 0px rgba(255,255,255,0.7);


        }

    </style>
    <p:panel id="pnlCRMSYNC" class="pnls">

        <p:outputPanel rendered="#{quotesHdr.quoteIdISquote.length()!=0}" id="isquote">
            <p:commandButton  value="Update from ISQuotes" title="Update from ISquote" id="isquoteUpdateBtn"  onclick="PF('fetchUpdButton').disable();" 
                              rendered="#{quotesHdr.quoteIdISquote.length()>0 and quotesHdr.crmSystemId==6}"
                              widgetVar="fetchUpdButton" class="btn btn-primary btn-xs"  
                              update=":quotefrom :quotefrom:tabQuot:delDtls :quotefrom:tabQuot:tblQuotDtls :quotefrom:tabQuot:opVl :quotefrom:tabQuot:delDtls  :quotefrom:tabQuot:lnkSprQt  :quotefrom:tabQuot:lnQtOppMstList :quotefrom:tabQuot :isQuotLnItmListDtlsx" 
                              action="#{quotesHdr.updateISquotes(quotesHdr.quoteIdISquote)}"  oncomplete="PF('fetchUpdButton').enable();"  />

        </p:outputPanel> 

        <p:outputPanel id="opCrmsyncAPI" rendered="#{quotesHdr.OAuthBasedConfigured}">
            <!--<h4  class="sub_title">CRMSync API Settings</h4>-->

            <h:panelGrid columns="3" cellpadding="5">
                <p:outputLabel  value="External #{custom.labels.get('IDS_QUOTES')} ID" />
                <p:outputLabel class="m" value=":" />

                <h:panelGroup>
                    <p:spacer width="10" height="0"/>
                    <p:outputLabel value="[Target #{custom.labels.get('IDS_QUOTES')} ID]" rendered="#{empty quoteSyncService.quoteCrmId}"  />
                    <p:outputLabel style="padding:3px" id="olTsQuoteId" value="#{quoteSyncService.quoteCrmId}" rendered="#{not empty quoteSyncService.quoteCrmId}"/>

                    <p:spacer width="4"/>

                    <p:remoteCommand id="rcOnQuoteCreateDone" name="rcOnQuoteCreateDone" autoRun="false"
                                     onstart="PF('dlgProcessingOnTask').show();" actionListener="#{autoQuotesService.quoteCreated(loginBean.userId)}"
                                     oncomplete="PF('dlgProcessingOnTask').hide();" update=":quotefrom:tabQuot:pnlCRMSYNC"/>

                    <p:commandButton value="New" class="btn btn-primary btn-xs" onclick="PF('dlgProcessingOnTask').show();"
                                     rendered="#{empty quoteSyncService.quoteCrmId}"
                                     actionListener="#{quoteSyncService.onclickNewTargetSystemQuote(quotesHdr.oppPrincipal, quotesHdr.recId,quotesHdr.oppCustomer)}" oncomplete="PF('dlgProcessingOnTask').hide();" >
                    </p:commandButton>

                    <p:spacer width="4"/>
                    <p:remoteCommand id="remotCommandShowQuoteMasterDlg" name="remotCommandShowQuoteMasterDlg" actionListener="#{quoteSyncService.applyRfMaster()}"
                                     update=":frmQuotMaster:pgQuotMaster" oncomplete="PF('dlgQuoteMaster').show();"/>

                    <p:commandButton  onclick="PF('dlgFetchRec').show();"
                                      actionListener="#{quoteSyncService.loadQuotes()}"
                                      oncomplete="PF('wvDtTargetSystemQuotes').clearFilters();PF('wvDlgTargetSystemQuote').show();PF('dlgFetchRec').hide()"
                                      icon="fa fa-search" class="btn-primary btn-xs"  
                                      update=":frmTargetSystemQuoteLookup:dtTargetSystemQuotes" immediate="true" 
                                      disabled="#{not empty quoteSyncService.quoteCrmId}"/>

                    <p:spacer width="4"/>
                    <p:poll id="pollQuotSyncStatus" widgetVar="pollQuotSyncStatus" interval="3" autoStart="false"
                            stop="#{not quoteSyncService.startPoll}" 
                            listener="#{quoteSyncService.checkSyncStatus()}" 
                            update="@this :quotefrom:tabQuot:pnlCRMSYNC" />
                    <p:commandButton actionListener="#{quoteSyncService.onClickSync()}"
                                     id="btnQuotSync"
                                     immediate="true"
                                     value="Sync"
                                     style="height:26px !important;"
                                     title="Sync to CRM System"
                                     styleClass="viewOpp btn-primary btn-sm" update=":quotefrom:tabQuot:pollQuotSyncStatus"
                                     rendered="#{not empty quoteSyncService.quoteCrmId}"/>


                    <p:spacer width="4"/>
                    <p:commandButton actionListener="#{quoteSyncService.redirectToTargetSystemQuote()}" 
                                     value="View"
                                     style="height:26px !important;" onclick="target = '_blank'"
                                     styleClass="viewOpp btn-primary btn-sm" id="btnViewTargetSystemQuote" 
                                     rendered="#{false and (not empty quoteSyncService.quoteCrmId)}"/>

                    <p:commandLink style="float:right;padding:4px;margin-top:3px"  value="Clear" 
                                   immediate="true" rendered="#{not empty quoteSyncService.quoteCrmId}" 
                                   onclick="PF('dlgConfirmCleareQuotLink').show()"/>

                </h:panelGroup>

            </h:panelGrid>
        </p:outputPanel>

    </p:panel>

    <p:dialog  widgetVar="dlgConfirmCleareQuotLink" header="Confirmation" modal="true" showEffect="fade" hideEffect="fade">
        <div class="div-center">
            <p:outputLabel value="Are you sure to unlink?" />
            <br/>
            <br/>
            <p:commandButton value="Yes" onclick="PF('dlgProcessingOnTask').show(); PF('dlgConfirmCleareQuotLink').hide();"
                             actionListener="#{quoteSyncService.onclickClearQuotLink(quotesHdr.recId)}" 
                             update=":quotefrom:tabQuot:pnlCRMSYNC" 
                             styleClass="ui-confirmdialog-yes btn btn-success btn-xs"
                             oncomplete="PF('dlgProcessingOnTask').hide();"/>
            <p:spacer width="4"/>
            <p:commandButton value="No" onclick="PF('dlgConfirmCleareQuotLink').hide()"
                             styleClass="ui-confirmdialog-no btn btn-danger btn-xs"  />
        </div>
    </p:dialog>

</ui:composition>



