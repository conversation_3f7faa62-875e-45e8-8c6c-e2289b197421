<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
        <!--//21-08-2024 : #14330 : CRM-8376 : Quote form updates for internal comments-->
    <p:remoteCommand name="quotCommentRemote" autoRun="false" actionListener="#{quoteCommentService.loadQuoteComments(quotesHdr.recId)}"
                                         update=":quotefrom:tabQuot:quotDtCmnt"/>
    <p:outputPanel  id="quotCommentsGrid" >
        
        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel style="font-weight: bold !important;">Comments</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            </div>
        </div> 
        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                <p:inputTextarea id="quotCommText" rows="10"  value="#{quoteCommentService.quotComment}"  style=" text-overflow: scroll" 
                                 autoResize="false" placeholder="Type in your comments here." ></p:inputTextarea>
            </div>
        </div>
        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-11 ui-lg-11"></div>
            <div class="ui-sm-12 ui-md-1 ui-lg-1">
                <p:commandButton value="Save" styleClass="btn btn-primary btn-xs" widgetVar="btnSendQuotComment" 
                                 actionListener="#{quoteCommentService.saveComment(quotesHdr.recId,loginBean.userId)}" update=""/>    
            </div>
        </div>
        
        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                <p:outputPanel  id="quotCommentsDtGrid" >
                <p:dataTable  value="#{quoteCommentService.quotCommentList}" emptyMessage="No Comments Added" 
                              var="quotCmnt" id="quotDtCmnt">
                    <p:column style="padding: 0px;">  
                        <div class="comment_div">  
                            <div style="margin-top:5px;margin-left:10px;margin-right:10px;">
                                <h:outputLabel value="#{opportunities.getFormattedDate(quotCmnt.quotCommDate,'dt')}" style="float: right;"/>
                                #{history.getFormattedDate(quotCmnt.quotCommDate,'dt')}
                                <p:spacer width="4" style="float: right;"/>
                                <h:graphicImage library="images" name="dummy.jpg" width="20" height="20"/>                 
                                <h:outputLabel value="#{users.getUserName(quotCmnt.quotCommUser)}" style="font-weight: bold !important;margin-left:10px;"/>

                                <p:commandButton rendered="#{quotCmnt.quotCommUser eq loginBean.userId}"
                                                 title="Remove comment" update="quotefrom:tabQuot:quotDtCmnt"
                                                 process="@this" actionListener="#{quoteCommentService.deleteQuoteComments(quotesHdr.recId,quotCmnt.recId)}"  
                                                 style="  background: none;float: right;width:20px;height:20px;margin-right: 10px !important;"
                                                 class="btn-danger btn-xs"
                                                 icon="fa fa-trash" />
                            </div>
                            <!--//04-11-2024 : #14822 : ESCALATIONS CRM-8647   Quote: The saved comments in the comments tab does not wrap a-->
                            <div class="comment_cmnt" style="white-space: pre-wrap; word-wrap: break-word; overflow-wrap: break-word; margin-left:38px;">
                                <h:outputLabel value="#{quotCmnt.quotCommText}" escape="false"/>  
                            </div>

                        </div>

                    </p:column>

                </p:dataTable> 
                    </p:outputPanel>
            </div>
        </div>

    </p:outputPanel>
</ui:composition>
