<ui:composition  xmlns="http://www.w3.org/1999/xhtml" 
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:f="http://java.sun.com/jsf/core" 
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:p="http://primefaces.org/ui" 
                 xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                 template="#{layoutMB.template}">

    <h:head>
    </h:head>
    <!--#359 Page tirle quotes-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>
            Quotes List 
        </title>
    </ui:define>
    <ui:define name="metadata">  
        <f:metadata>   
            <!--10/05/2022 task fixing lazy;loading issue-->
            <!--            <f:viewAction action="#{quotesHdr.getUnlinkedPoData()}">
                            <p:ajax update=":quotLstForm"></p:ajax>
                        </f:viewAction>-->

            <!--3051 CRM-3731, 3767, 3797: Quotes: add standard price and make columns selectable-->
            <f:viewAction action="#{quotesHdr.loadQuotHdrColumnsList()}"/>
             <!--#13603 Thea threatening to cancel due to application's slow performance-->
            <!--<f:viewAction action="#{quotesHdr.loadQuotDtlColumnsList()}"/>-->

            <!--03-03-2023 10837 CRM-6651  Quote: UI CRMSync Autoquotes- create company aliasing : Hide Sync Exceptions / Sync Status-->
            <f:viewAction action="#{quotesHdr.checkIsQuoteOasis()}"/>
            <f:viewAction action="#{quotesHdr.checkAutoQuoteConfigured()}" />
            <!--2520 - Related - CRM-1521: Tutorial button landing urls - Quotes, Samples, Opportunities, Jobs, Projects-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('QUOT_LIST'))}" />
            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('QUOT_LIST')}" />

        </f:metadata>

        <style>

        </style>
        <script>



            $(document).ready(function () {
                $('#quotLstForm\\:globalFilter2').val("");
                // alert("hii");

              

            });

            //document.getElementById('form:receivedMessage').value = 'myValue';


//pms task : 6916  Quotes : 500 error
            $(document).keypress(
                    function (event) {
                        if (event.which == '13') {
                            event.preventDefault();
                        }
                    });


//23-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-
            function resetHdrFilter() {
                PF('tblQt').clearFilters();
                PF('tblQt').filter();
            }
            function resetDtlFilter() {
                PF('tbldtl1').clearFilters();
                PF('tbldtl1').filter();
            }
            function ovBodyAuto() {
                $('html, body').css('overflow', 'auto');
            }

            /* <![CDATA[*/
            function ovBodyHidden() {
                //$(window).scrollTop(0);
                var scroll = $(window).scrollTop();
                var scrl = parseInt(scroll);
                if (scrl <= 100) {
                    // alert(scroll);
                    $('html, body').animate({scrollTop: 0}, 'fast');

                }
            }
            /*  ]]>*/
        </script>

    </ui:define>
    <!--#359 Page tirle quotes-->
    <ui:define name="title">
        <f:event listener="#{quotesHdr.isPageAccessible}" type="preRenderView"/>
        <ui:param name="title" value="Quotes List"/>
        <div class="row">
            <div class="col-md-6">
                Quotes List 
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>

    <ui:define name="menu">
    </ui:define>

    <ui:define name="body">
        <style>

        </style>

        <div class="box box-info box-body">
            <h:form id="quotLstForm">

                <!--                <h:panelGroup>
                                    <p:commandButton   value="New" ajax="false" process="none" title="New"  action="/opploop/quotes/NewQuote.xhtml?faces-redirect=true"  styleClass="btn btn-primary btn-xs" /> 
                                </h:panelGroup>-->


                <h:inputHidden value="#{quotesHdr.dlgName}" id="dlgname"/>
                <h:panelGroup id="add">
                    <!--3051 CRM-3731, 3767, 3797: Quotes: add standard price and make columns selectable-->

                    <!--5614 Quotes List >  Add drop with options: My Quotes, Show All-->
                    <!--//20-02-2024 : #13198 : Quote list view ui break issue for my quotes-->
                    <div class="button_bar" style="margin-top: 4px;display:-webkit-inline-box;width:100%;">
                        <p:selectOneRadio id="sumdtl" widgetVar="selOption" value="#{quotesHdr.summaryDetals}"  style="float: left;">
                            <f:selectItem itemLabel="Summary" itemValue="0" />
                            <f:selectItem itemLabel="Detailed" itemValue="1" />
                            <!--//          #11158  ESCALATIONS CRM-6926   Quotes: Detailed view does not show quotes without line items-->
                            <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                            <p:ajax event="change" listener="#{quotesHdr.viewDtl(quotesHdr.summaryDetals,quotesHdr.quotListPageFilter,quotesHdr.quotOpenStatus)}" 
                                    update=":quotLstForm :quotLstForm:add" oncomplete="applyTopScrollbarQuot();applyTopScrollbarQuotDetails();" />

                            <!--<p:resetInput target=":quotLstForm:add"/>-->
                        </p:selectOneRadio>
                        <!--27-02-2023 10710 ESCALATIONS: CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                        <!--<p:spacer width="4"/>-->
                        <p:commandButton  id="btnNwQuot" value="New" ajax="false" process="none" title="New"  action="/opploop/quotes/NewQuote.xhtml?faces-redirect=true"  styleClass="btn btn-primary btn-xs" style="margin-top: -18px;"/> 
                        <!--//    #10990 CRM-6748   Quote Report: Tradetech equivalent for Thea-->
                        <p:spacer width="3" />
                        <!--Pms Task 6452 : CRM-5018: can we have an "Update selected" button in "Quotes" module?-->
                        <!--//23-06-2022 : #8306 : Quotes> Update selected button gets enabled after clearing filter. Related to #8003-->
                        <p:commandButton value="Update Selected" immediate="true"  id="btnQutBlkUpdate"  
                                         styleClass="btn btn-primary btn-xs"  
                                         rendered="#{quotesHdr.summaryDetals == 0}"
                                         update="blkUpdateForm:bulkQutUpdate"
                                         actionListener="#{dataSelfService.resetQutDefaultValues()}"
                                         action="#{dataSelfService.showDialog(quotesHdr.selectedLzyQuotHdr, 'QUT','NORMAL')}" 
                                         disabled="#{quotesHdr.selectedLzyQuotHdr.size() == null or quotesHdr.selectedLzyQuotHdr.size() == 0}" style="margin-top: -18px;"
                                         />

                        <!--#3542 CRM-2135: #1 : Quotes > Filter by Status-->
                        <!--//    #10990 CRM-6748   Quote Report: Tradetech equivalent for Thea-->
                        <p:spacer width="3"/>
                        <!--5614 Quotes List >  Add drop with options: My Quotes, Show All-->
                        <h:panelGroup>
                            <p:selectOneMenu id="jobFilter1" value="#{quotesHdr.quotListPageFilter}" rendered="#{quotesHdr.summaryDetals == 0}"
                                             widgetVar="jobFilter1w" class="ui-select-btn"  style=" display: inline-block;"  >
                                <!--//   #14351: ESCALATION CRM-8408 : Quotes : Default shows all quotes instead of my quotes-->
                                <!--7929 08/06/2022 #7677,More CRM>Quote>Show all>page refresh>observe dropdown not changed to My Quotes(bug in mozilla)-->
                                 <f:selectItem itemLabel="My Quotes"  itemValue="1"/> 
                                <f:selectItem itemLabel="Show all" itemValue="0"/>
                                 <!--//       #14896 CRM-8696   set default to show "All" instead of "My Quotes"-->
                                <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                                <p:ajax event="change"  process="@this" listener="#{quotesHdr.onChangeQuoteVisibility}" update=":quotLstForm" oncomplete="#{quotesHdr.summaryDetals != 0?resetDtlFilter:resetHdrFilter};if (#{quotesHdr.summaryDetals == 0}) { applyTopScrollbarQuot(); }"/>

                            </p:selectOneMenu>
                            <!--//-6-07-2022 : #7929 :#7677,More CRM>Quote>Show all>page refresh>observe dropdown not changed to My Quotes(bug in--> 
                            <p:selectOneMenu id="jobFilter2" value="#{quotesDtl.quotListPageFilter}" rendered="#{quotesHdr.summaryDetals != 0}"
                                             widgetVar="jobFilter2w" class="ui-select-btn"  style=" display: inline-block"  >
                               <!--//   #14351: ESCALATION CRM-8408 : Quotes : Default shows all quotes instead of my quotes-->
                                <!--7929 08/06/2022 #7677,More CRM>Quote>Show all>page refresh>observe dropdown not changed to My Quotes(bug in mozilla)-->
                                <f:selectItem itemLabel="My Quotes" itemValue="1"/> 
                                <f:selectItem itemLabel="Show all" itemValue="0"/>
                                    <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                                <p:ajax event="change"  process="@this"   update=":quotLstForm" oncomplete="#{quotesHdr.summaryDetals != 0?resetDtlFilter:resetHdrFilter};applyTopScrollbarQuotDetails()" />
                            </p:selectOneMenu>
                        </h:panelGroup>
                        <!--//    #10990 CRM-6748   Quote Report: Tradetech equivalent for Thea-->
                        <p:spacer width="3"/>
                        <!--//7677 20/04/2022 : CRM-5531  Kevin Lowe/Jim Bryce & Associates - Quotes link issue-->
                        <p:selectOneMenu id="opFilter" value="#{quotesHdr.quotOpenStatus}" rendered="#{quotesHdr.summaryDetals == 0}" class="ui-select-btn" style=" display: inline-block;"  >
                            <f:selectItem itemLabel="Open" itemValue="0"/> 
                            <f:selectItem itemLabel="Closed" itemValue="1"/>
                            <f:selectItem itemLabel="All" itemValue="2"/>
                            <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                            <p:ajax event="change" listener="#{quotesHdr.showFollowUp()}"  
                                    oncomplete="#{quotesHdr.summaryDetals != 0?resetDtlFilter:resetHdrFilter};if (#{quotesHdr.summaryDetals == 0}) { applyTopScrollbarQuot(); }"/>
                        </p:selectOneMenu>
                        <!--//7677 20/04/2022 : CRM-5531  Kevin Lowe/Jim Bryce & Associates - Quotes link issue-->
                        <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                        <p:selectOneMenu id="opFilter2" value="#{quotesDtl.quotOpenStatus}" rendered="#{quotesHdr.summaryDetals != 0}" class="ui-select-btn" style=" display: inline-block;"  >
                            <f:selectItem itemLabel="Open" itemValue="0"/> 
                            <f:selectItem itemLabel="Closed" itemValue="1"/>
                            <f:selectItem itemLabel="All" itemValue="2"/>
                            <p:ajax event="change" listener="#{quotesHdr.showFollowUp()}"  oncomplete="#{quotesHdr.summaryDetals != 0?resetDtlFilter:resetHdrFilter};applyTopScrollbarQuotDetails()"/>
                        </p:selectOneMenu>
                        <!--//    #10990 CRM-6748   Quote Report: Tradetech equivalent for Thea-->
                        <p:spacer width="3"/>
                        <!--#3542 CRM-2135: #1 : Quotes > Filter by Status-->
                        <!--
                                                <p:commandButton title="Clear Summary Filters" icon="fa fa-times-circle" id="btnclearSfilter" action="#{quotesHdr.clearSummaryFilter()}" onclick="PF('tblQt1').clearFilters()"  style="margin-top: -18px;"
                        
                                                                 class="btn-danger btn-xs" oncomplete="PF('tblQt1').getPaginator().setPage(0);PF('tblQt1').filter()"  rendered="#{quotesHdr.summaryDetals == 0}" update=":quotLstForm :quotLstForm:tblQt" />
                        
                        
                                                <p:commandButton title="Clear Detailed Filters" icon="fa fa-times-circle" id="btnclearDfilter"  onclick="PF('tbldtl1').clearFilters()"  action="#{quotesHdr.clearDtlFilter()}" style="margin-top: -18px;"
                                                                 class="btn-danger btn-xs" oncomplete="PF('tbldtl1').getPaginator().setPage(0);PF('tbldtl1').filter()"  rendered="#{quotesHdr.summaryDetals != 0}" update=":quotLstForm :quotLstForm:tblQdtl"/>-->


<!--                        <p:selectOneMenu id="opFilter1" value="#{quotesHdr.quotOpenStatus}"  class="ui-select-btn" style="width:150px; display: inline-block;" rendered="#{quotesHdr.summaryDetals != 0}">
                           <f:selectItem itemLabel="Open" itemValue="0"/> 
                           <f:selectItem itemLabel="Closed" itemValue="1"/>
                           <f:selectItem itemLabel="All" itemValue="2"/>
                           <p:ajax event="change" listener="#{quotesHdr.showDtlFollowUp()}"  oncomplete="PF('tbldtl1').clearFilters();PF('tbldtl1').filter();"/>
                       </p:selectOneMenu>-->
                        <!--update=":quotLstForm" PF('tbldtl1').filter(); PF('tbldtl1').clearFilters(); PF('tblQt1').filter(); PF('tblQt1').clearFilters();-->

                        <!--27-02-2023 10710 ESCALATIONS: CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                        <!--<p:spacer width="4"/>-->
                        <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                        <p:commandButton  value="Export"   oncomplete="PF('dlgQuotExp').show();if (#{quotesHdr.summaryDetals == 0}) { applyTopScrollbarQuot(); }else { applyTopScrollbarQuotDetails(); }"   actionListener="#{quotesService.resetValues()}"   style="margin-top: -18px;"  styleClass="btn btn-primary btn-xs" update="quotLstForm"  />
                        <!--//    #10990 CRM-6748   Quote Report: Tradetech equivalent for Thea-->
                        <p:spacer width="3"/>
                        <!--2420 Quotes List > Sync Exceptions-->
                        <!--3437 Job Auto Download (cron job)-->
                        <!--//    Bug #7914: CRM-5643 Oasis: Thea: most quotes and jobs have no values-->
                        <!--03-03-2023 10837 CRM-6651  Quote: UI CRMSync Autoquotes- create company aliasing : Hide Sync Exceptions / Sync Status-->
                        <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                        <p:commandButton  value="Sync Exceptions" styleClass="btn btn-primary btn-xs" style="margin-top: -18px;" 
                                          rendered="#{quotesHdr.configuredForIsQuoteOrOasis}"
                                          actionListener="#{crmsyncLog.exceptionListingForQuotes()}" 
                                          action="#{iSQuoteCrmClient.reprocessFlag()}"    
                                          update=":quotLstForm :frmQuoteException" 
                                          oncomplete="PF('dlgQuotException').show(); PF('tblVarQtException').clearFilters();if (#{quotesHdr.summaryDetals == 0}) { applyTopScrollbarQuot(); }else { applyTopScrollbarQuotDetails(); }"  
                                          />
                        <!--//    #10990 CRM-6748   Quote Report: Tradetech equivalent for Thea-->
                        <!--24-01-2023 10506 CRMSYNC-112/141: AutoQuotes: Quotes: Sync Status<p:spacer width="4"/>-->
                        <p:spacer width="3"/>
                        <!--03-03-2023 10837 CRM-6651  Quote: UI CRMSync Autoquotes- create company aliasing : Hide Sync Exceptions / Sync Status-->
                        <p:commandButton id="btnSyncStatusQuoteList" value="Sync Status" style="margin-top: -18px;"
                                         rendered="#{quotesHdr.configuredAutoQuote}"
                                         styleClass="btn btn-primary btn-xs"
                                         onclick="window.open('QuotesSyncStatus.xhtml', '_self');"/>

                        <!--27-02-2023 10710 ESCALATIONS: CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                        <!--<p:spacer width="4"/>-->

                        <!--27-02-2023 10710 ESCALATIONS: CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                        <!--<div style="display: inline-flex; text-align: left;float: left">-->
                        <!--<h:panelGroup>-->
                            <!--<p:commandButton id="opNewBtn"  value="New" ajax="false" process="none" title="Create new #{custom.labels.get('IDS_OPP')}"  action="/opploop/opportunity/OpportunityView.xhtml?faces-redirect=true"  styleClass="btn btn-primary btn-xs" />--> 
                        <!--27-02-2023 10710 ESCALATIONS: CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                        <!--</h:panelGroup>-->
                        <!--</div>-->
                        <div style="display: inline-flex; text-align: left; ">
                            <div style="float:right; ">
                                <!--                                        <h:commandLink title="Excel">
                                                                            <p:graphicImage name="/images/excel.png" width="24"/>
                                                                            <p:dataExporter type="xls" target="tblQt" fileName="QuotesHdr_List" />
                                                                        </h:commandLink>
                                                                                                                <p:spacer width="4"/>
                                                                        <h:commandLink title="PDF">
                                                                            <p:graphicImage name="/images/pdf.png" width="24"/>
                                                                            <p:dataExporter type="pdf" target="tblOpp" fileName="tblOpp1"/>
                                                                                                                </h:commandLink>
                                                                        <p:spacer width="4"/>
                                                                        <h:commandLink title="CSV">
                                                                            <p:graphicImage name="/images/csv.png" width="24"/>
                                                                            <p:dataExporter type="csv" target="tblQt" fileName="QuotesHdr_List" />
                                                                        </h:commandLink>-->
                            </div>
                            <!--27-02-2023 10710 ESCALATIONS: CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                            <!--<p:spacer width="4"/>-->
                            <!--<h:panelGroup>-->
<!--                                        <p:selectOneMenu id="opFilter" value="#{oppFilter.topFilterExt.filterValue}"  class="ui-select-btn" style="width:150px">
                                    <f:selectItem itemLabel="None" itemValue="0"/>
                                    <f:selectItem itemLabel="#{custom.labels.get('IDS_FOLLOW_UP')} today or older" itemValue="1"/> 
                                    <f:selectItem itemLabel="Future #{custom.labels.get('IDS_FOLLOW_UP')}" itemValue="2"/>  
                                    <f:selectItem itemLabel="My #{custom.labels.get('IDS_SALES_TEAM')}" itemValue="3"/>
                                    <f:selectItem itemLabel="My #{custom.labels.get('IDS_OPPS')}" itemValue="4"/>
                                    <p:ajax event="change" listener="#{oppService.showFollowUp()}" update="tblQt1"  oncomplete="PF('tblQt1').filter(); PF('tblQt1').clearFilters();"/>
                                </p:selectOneMenu> -->
                            <!--27-02-2023 10710 ESCALATIONS: CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                            <!--</h:panelGroup>-->
                            <!--<p:spacer width="4"/>-->

                            <!--PMS task 6632 : quotes  search fields not working 29/01/2022--> 
                            <h:panelGroup>
                                <!--27-02-2023 10710 ESCALATIONS: CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                                <p:inputText id="globalFilter2" value="#{quotesHdr.filterValueSummary}" rendered="#{quotesHdr.summaryDetals == 0}" onkeyup="PF('tblQt').filter()" style="width:100px" placeholder="Search fields">
                                    <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                                    <p:ajax event="keyup" update=":quotLstForm:tblQt" process="@this" listener="#{quotesHdr.onGlobalFilter(quotesHdr.filterValueSummary)}" oncomplete="PF('tblQt').filter();if (#{quotesHdr.summaryDetals == 0}) { applyTopScrollbarQuot(); }"/>
                                </p:inputText>
                                <!--//7677 20/04/2022 : CRM-5531  Kevin Lowe/Jim Bryce & Associates - Quotes link issue-->
                                <!--27-02-2023 10710 ESCALATIONS: CRMSYNC-141 Add Sync Exception Function for quotes and jobs coming from AQ-->
                                <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                                <p:inputText id="globalFilter1"  value="#{quotesDtl.filterValueDetails}" rendered="#{quotesHdr.summaryDetals != 0}" onkeyup="PF('tbldtl1').filter()" style="width:100px" placeholder="Search fields">
                                    <p:ajax event="keyup" update=":quotLstForm:tblQdtl" process="@this" oncomplete="PF('tbldtl1').filter();applyTopScrollbarQuotDetails()" listener="#{quotesHdr.onGlobalFilter2(quotesHdr.filterValueDetails)}"/>

                                </p:inputText>
                            </h:panelGroup>
                            <!--//    #10990 CRM-6748   Quote Report: Tradetech equivalent for Thea-->
                            <p:spacer width="3"/>
                            <p:column>
                                <p:commandButton id="toggler2" rendered="#{quotesHdr.summaryDetals == 0}" type="button" value="Columns" style="float: right" icon="fa fa-align-justify"/>
                                <p:columnToggler datasource="tblQt" trigger="toggler2" rendered="#{quotesHdr.summaryDetals == 0}">
                                    <p:ajax event="toggle" listener="#{quotesHdr.onHdrToggle}"/>
                                </p:columnToggler>

                                <p:commandButton id="toggler1" type="button" value="Columns" style="float: right" icon="fa fa-align-justify"  rendered="#{quotesHdr.summaryDetals != 0}"/>
                                <p:columnToggler datasource="tblQdtl" trigger="toggler1"  rendered="#{quotesHdr.summaryDetals != 0}">
                                    <p:ajax event="toggle" listener="#{quotesHdr.onDtlToggle}" />
                                </p:columnToggler>
                            </p:column>
                            <!--//    #10990 CRM-6748   Quote Report: Tradetech equivalent for Thea-->
                            <p:spacer width="3"/>
                            <p:column>
                                <!--3841 Quotes: Select last column(s) and apply filter > Blank Column data-->
                                <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                                <p:commandButton rendered="#{quotesHdr.summaryDetals == 0}" title="Save settings" 
                                                 icon="fa fa-save"
                                                 id="btnclonesave1" oncomplete="if (#{quotesHdr.summaryDetals == 0}) { applyTopScrollbarQuot(); }"  
                                                 action="#{quotesHdr.saveHdrTableSettings()}" 
                                                 class="btn-success btn-xs" update=":quotLstForm :quotLstForm:tblQt" />
<!--                                //04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                                <p:commandButton title="Save settings" rendered="#{quotesHdr.summaryDetals != 0}" 
                                                 icon="fa fa-save"
                                                 id="btnclonesave2" 
                                                 oncomplete="applyTopScrollbarQuotDetails();applyTopScrollbarQuot();"
                                                 action="#{quotesHdr.saveDtlTableSettings()}" 
                                                 class="btn-success btn-xs" update=":quotLstForm :quotLstForm:tblQdtl" /> 
                            </p:column>
                            <!--//    #10990 CRM-6748   Quote Report: Tradetech equivalent for Thea-->
                            <p:spacer width="3"/>
                            <p:column>
                                <!--                                        3726    Opportunities > Retain pagination-update removed-->
                                <!--                                            3791 opportunities clear filter not working-->
                                <!--#3051: Removed actionListener as it is not required, QA reported issue - Clicking on Clear Filters twice gives a blank list-->
                                <!--PMS task 6632 : quotes  search fields not working 29/01/2022--> 
                                <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                                <p:commandButton  rendered="#{quotesHdr.summaryDetals == 0}" title="Clear Filters" 
                                                  icon="fa fa-times-circle"
                                                  id="btnfilterclear2"
                                                  update=":quotLstForm :quotLstForm:tblQt"
                                                  action="#{quotesHdr.clearSummaryFilter()}"
                                                  onclick="PF('tblQt').clearFilters();"
                                                  class="btn-danger btn-xs" oncomplete="PF('tblQt').getPaginator().setPage(0);PF('tblQt').filter();if (#{quotesHdr.summaryDetals == 0}) { applyTopScrollbarQuot(); }" />
                                <!--onclick="clearFilterValues()"-->
                                <!--#3051: Removed actionListener as it is not required, QA reported issue - Clicking on Clear Filters twice gives a blank list-->                                  
                                <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                                <p:commandButton title="Clear Filters" rendered="#{quotesHdr.summaryDetals != 0}" 
                                                 icon="fa fa-times-circle"
                                                 id="btnfilterclear1"
                                                 update=":quotLstForm :quotLstForm:tblQdtl"
                                                 action="#{quotesDtl.clearDtlFilter()}"
                                                 onclick="PF('tbldtl1').clearFilters();"
                                                 class="btn-danger btn-xs" oncomplete="PF('tbldtl1').getPaginator().setPage(0);PF('tbldtl1').filter();applyTopScrollbarQuotDetails();" />
                                <!--//    #10990 CRM-6748   Quote Report: Tradetech equivalent for Thea-->
                                <!--8078: 02/06/2022 : CRM-5548  PO Flow: Quotes that have no linked POs show in status and subtotal billboard card-->
                                <p:spacer width="3"/>
                                <p:commandButton value="PO Link Status" immediate="true"  id="btnSubTotal" actionListener="#{quotesHdr.getUnlinkedPoData()}"
                                                 styleClass="btn btn-primary btn-xs" update="ovrLaySubTtl"
                                                 oncomplete="PF('ovrLaySubTtl').show();"  />
                                <!--//    #10990 CRM-6748   Quote Report: Tradetech equivalent for Thea-->
                                <p:spacer width="3"/>
                                <p:commandButton value="Quote Report"   id="btnQuoteReport" actionListener="#{quotesHdr.reDirectToQuoteReport()}"
                                                 styleClass="btn btn-primary btn-xs" 
                                                 />
                                <p:overlayPanel  showCloseIcon="true" widgetVar="ovrLaySubTtl" id="ovrLaySubTtl" style="width: 550px;height: auto; margin-left: -100px">

                                    <table >

                                        <tr style="border:0px !important;">
                                            <td style="border:0px solid #C4C4C4 !important;text-align: left;font-weight: bold; width: 100px;">Quotes unlinked to PO<h:outputText value="*" style="color:red;"/></td>
                                            <td style="border:0px solid #C4C4C4 !important;text-align: left">
                                                <h:outputText value="Count: #{quotesHdr.unLinkedCnt}">

                                                </h:outputText>
                                            </td>
                                            <td style=" width: 30px;"></td>
                                            <td style="border:0px solid #C4C4C4 !important;text-align: right">  
                                                <h:outputText value="Value: "/>
                                                <h:outputText value="#{quotesHdr.unLinkedVal}">
                                                    <f:convertNumber groupingUsed="true"  maxIntegerDigits="15"  
                                                                     minFractionDigits="2" maxFractionDigits="2"/>
                                                </h:outputText>

                                            </td>
                                        </tr>
                                        <tr style="border:solid 0px !important">
                                            <td style="border:0px solid #C4C4C4 !important;width: 20%;text-align: left;width: 280px; font-size:12px;"><h:outputText value="*" style="color:red;"/>Quotes generated for the last 12 months</td>

                                        </tr>
                                    </table>
                                </p:overlayPanel>
                            </p:column>

                        </div>  

                    </div>

                                       
                                                 
                                           
                </h:panelGroup>



                <p:spacer/>

                <!--//7677 20/04/2022 : CRM-5531  Kevin Lowe/Jim Bryce & Associates - Quotes link issue-->
                <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                <p:dataTable id="tblQdtl" widgetVar="tbldtl1" paginator="true"  rendered="#{quotesHdr.summaryDetals != 0}"
                             value="#{quotesDtl}"
                             var="enq" rowIndexVar="rowIndex"
                             rowKey="#{enq.recId}" multiViewState="true"
                             selection="#{quotesDtl.selectedLzyquotDtl}" 
                             style="margin-top: -16px!important;"
                             rowsPerPageTemplate="50" 
                             selectionMode="single"  
                             filteredValue="#{quotesDtl.listFilterLzyQuotDtl}"                             
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rows="50"   scrollable="true" scrollHeight="2000"                    
                             draggableColumns="true" lazy="true">  
                    <!--paginatorAlwaysVisible="false"-->
                    <p:ajax event="rowSelect"                            
                            listener="#{quotesDtl.onRowDtlSelct}" 
                            /> 
                    <!--                    <h:panelGroup style="display:none !important">  
                                                             
                                                              <f:facet name="header">
                                                <p:inputText id="globalFilter" onkeyup="PF('tbldtl1').filter()" />
                                                              </f:facet>
                                                                  
                                        </h:panelGroup>-->

                    <p:ajax event="page" listener="#{quotesDtlFilter.onPageChange}" />  
                    <p:ajax event="colReorder" listener="#{quotesHdr.onColumnReorderQuotDtl}"/>
                    <!--7930 09/05/2022 Quotes: UOM and UOM units doesn't change/Quote Selection issues-->
                    <p:ajax event="page" listener="#{quotesDtl.onPagination}" update=""/>
                   
                    <!--<p:column exportable="false" toggleable="false" selectionMode="multiple" style="width: 10px;text-align: center;"/>-->





                    <!--<p:column exportable="false" toggleable="false" selectionMode="multiple" style="width: 10px;text-align: center;"/>-->




<!--                    <p:column headerText="Quote Number" filterBy="#{enq.quotNumber}"  sortBy="#{enq.quotNumber}"   >
                        <p:outputLabel value="#{enq.quotNumber}" style="text-align: right"/>
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_MFG_PART_NUM')}" filterBy="#{enq.oppItemPartManf}" sortBy="#{enq.oppItemPartManf}" >
                    #{enq.oppItemPartManf}
                </p:column>


                <p:column headerText="Qty" filterBy="#{enq.oppItemQnty}"  sortBy="#{enq.oppItemQnty}" style="text-align:right" id="qtyId">
                    #{enq.oppItemQnty}
                </p:column>

                <p:column headerText="EAU" filterBy="#{enq.oppEau}"  sortBy="#{enq.oppEau}" style="text-align: right" id="eauId" >
                    #{enq.oppEau}                                  
                </p:column>

                <p:column headerText="Multiplier" filterBy="#{enq.quotMultiplier}" sortBy="#{enq.quotMultiplier}" style="text-align: right" id="mulId">
                    #{enq.quotMultiplier}
                </p:column>

                <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{enq.oppCustomerName}" sortBy="#{enq.oppCustomerName}" id="custD">
                    #{enq.oppCustomerName}                               
                </p:column>

                <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{enq.oppPrincipalName}"  sortBy="#{enq.oppPrincipalName}" id="prinD" >
                    #{enq.oppPrincipalName}                               
                </p:column>
                <p:column headerText="#{custom.labels.get('IDS_SALES_TEAMS')}" filterBy="#{enq.oppSManName}"  sortBy="#{enq.oppSManName}" id="smanD" >
                    #{enq.oppSManName}                               
                </p:column>

                <p:column headerText="#{custom.labels.get('IDS_DISTRI')}" filterBy="#{enq.oppDistriName}"  sortBy="#{enq.oppDistriName}" id="distriD">
                    #{enq.oppDistriName}
                </p:column>
                2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020
                <p:column headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" filterBy="#{enq.oppSecCustomerName}"  sortBy="#{enq.oppSecCustomerName}"  id="secCustD">
                    #{enq.oppSecCustomerName}
                </p:column>
                10-12-2020:TASK#2641-Quote > Quote Date- Filter Fails 
                <p:column headerText="Quote Date" filterBy=" #{rFUtilities.formatDateTime(quotesHdr.convertDate(enq.quotDate), 'da')}" filterMatchMode="contains"  sortBy="#{enq.quotDate}" id="qdateD" >

                    #{rFUtilities.formatDateTime(quotesHdr.convertDate(enq.quotDate), 'da')}
                </p:column>               
                <p:column headerText="Quote Value" filterBy="#{enq.oppValue}"  sortBy="#{enq.oppValue}"  style="text-align: right" id="valId">
                    #{enq.oppValue}
                </p:column>  id="#{column.columnName}"     -->
                    <!--Feature #6908 Quotes: Sort the quotes by Quote Date by default-->
                    <c:forEach var="column" items="#{quotesHdr.listQuotDtlColumns}">
                        <!--26/05/2022:  8057 : CRM-5090: can we add a comma to the "value" field in Quotes-->
<!--                        //16-09-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                        <p:column headerText="#{column.columnHeader}" 
                                  style="width:200px"
                                  visible="#{column.columnVisibleFlag==1}"  
                                  filterMatchMode="#{column.columnFilterMatchMode}"
                                  filterBy="#{column.columnName=='quotDate' ? globalParams.formatDateTime(enq[column.columnName], 'da') : enq[column.columnName]}"  id="#{column.columnName}"
                                  sortBy="#{enq[column.columnName]}" 
                                  field="#{column.columnName}"
                                  >

                            <c:choose>  
                                <!--14379: 28/08/2024: CRM-8418   Quote: jump from detail view part number rows to quote detail, not line item detail-->
                                 <c:when test="#{column.columnName=='quotNumber'}"> 
                                   
                                     <h:link  outcome="NewQuote.xhtml?recId=#{enq.quotHdrId}"   value="#{enq.quotNumber} " title="Open Quote" /> 
                                </c:when>  
                                
                                <c:when test="#{column.columnName=='quotDate'}"> 
                                    #{poServices.getFormattedDate(enq[column.columnName], 'da')}

<!--<h:link outcome="NewQuote.xhtml?recId=#{enq.recId}"  value="#{poServices.getFormattedDate(enq[column.columnName], 'da')}" />--> 
                                </c:when>  
                                <c:otherwise>
                                    #{enq[column.columnName]}
                                    <!--<h:link outcome="NewQuote.xhtml?recId=#{enq.recId}"  value="#{enq[column.columnName]}" />--> 
                                </c:otherwise>
                                <!--26/05/2022:  8057 : CRM-5090: can we add a comma to the "value" field in Quotes-->
                                <c:when test="#{column.columnName=='quotValue'}">
                                    <!--//07-07-2023 :  #11602 : CRM-7138   quotes: detailed view - no way to open quote from detailed view-->
                                    #{enq.quotValue}
<!--                                    <h:outputLabel class="hvre" styleClass="" value="#{enq.quotValue}">


                                        <f:convertNumber groupingUsed="true"  maxIntegerDigits="15"  
                                                         minFractionDigits="2" maxFractionDigits="2"/>



                                    </h:outputLabel>-->
                                </c:when>
                                <!--#12199 CRM-7350   Uploaded multiplier at .3735 it changed to .374 - increase number of decimal places to 5-->
                                <c:when test="#{column.columnName == 'quotMultiplier'}">
                                    <p:outputLabel value="#{enq.quotMultiplier}">


                                        <f:convertNumber groupingUsed="true"   
                                                         minFractionDigits="5" maxFractionDigits="5"/>



                                    </p:outputLabel>

                                </c:when>
                                <!--#12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
                                <c:when test="#{column.columnName == 'quotStdRate'}">
                                    <p:outputLabel value="#{enq.quotStdRate}">
                                        <f:convertNumber groupingUsed="true"   
                                                         minFractionDigits="2" maxFractionDigits="6"/>

                                    </p:outputLabel>

                                </c:when>

                            </c:choose>

                        </p:column>
                    </c:forEach> 




<!--                    <c:forEach var="column" items="#{quotesHdr.listQuotDtlColumns}">
    <p:column headerText="#{column.columnHeader}" width="width: 10%; white-space: nowrap;"
              visible="#{column.columnVisibleFlag==1}"  
              filterMatchMode="#{column.columnFilterMatchMode}"
              filterBy="#{column.columnName=='quotDate' ? globalParams.formatDateTime(enq[column.columnName], 'da') : enq[column.columnName]}"  id="#{column.columnName}"
              sortBy="#{enq[column.columnName]}" 
              >
        Feature #6908 Quotes: Sort the quotes by Quote Date by default
        <c:choose>  
            <c:when test="#{column.columnName=='quotDate'}"> 
                    #{poServices.getFormattedDate(enq[column.columnName], 'da')}
                    
                    <h:link outcome="NewQuote.xhtml?recId=#{enq.recId}"  value="#{poServices.getFormattedDate(enq[column.columnName], 'da')}" /> 
                </c:when>  
                <c:otherwise>
                    #{enq[column.columnName]}
                    <h:link outcome="NewQuote.xhtml?recId=#{enq.recId}"  value="#{enq[column.columnName]}" /> 
                </c:otherwise>

            </c:choose>

        </p:column>
    </c:forEach> -->

                </p:dataTable>
                <!--Pms Task 6452 : CRM-5018: can we have an "Update selected" button in "Quotes" module?-->

                <!--//7677 20/04/2022 : CRM-5531  Kevin Lowe/Jim Bryce & Associates - Quotes link issue-->
                <!--//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                <p:dataTable id="tblQt" widgetVar="tblQt" paginator="true" rendered="#{quotesHdr.summaryDetals == 0}"
                             value="#{quotesHdr}"
                             var="quot" 
                             style="margin-top: -16px!important;"
                             rowsPerPageTemplate="50" 
                             rowKey="#{quot.recId}" multiViewState="true"  
                             selection="#{quotesHdr.selectedLzyQuotHdr}" 
                             rowSelectMode="add" scrollable="true" scrollHeight="2000"
                             filteredValue="#{quotesHdr.listFilterLzyQuotHdr}" 
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rows="50"   class="tblmain"                         
                             draggableColumns="true" lazy="true">  
                    <!--paginatorAlwaysVisible="false"-->
                    <!--                <p:ajax event="rowSelect"                            
                                            listener="#{quotesHdr.onQtHdrSelect}"
                                            update=" "
                                            oncomplete="showDlg()"
                                            />-->
                    <!--Pms Task 6452 : CRM-5018: can we have an "Update selected" button in "Quotes" module?
                    //7677 20/04/2022 : CRM-5531  Kevin Lowe/Jim Bryce & Associates - Quotes link issue-->
                    <p:ajax event="rowSelectCheckbox"  update=":quotLstForm:btnQutBlkUpdate" listener="#{quotesHdr.onRowSelectCheckbox}"/>
                    <p:ajax event="rowUnselectCheckbox"  update=":quotLstForm:btnQutBlkUpdate" listener="#{quotesHdr.onRowUnselectCheckbox}"/>
                    <p:ajax event="toggleSelect"  update=":quotLstForm:btnQutBlkUpdate" listener="#{quotesHdr.onToggleSelectOpp}"/>

                    <p:ajax event="page" listener="#{quotesHdrFilter.onPageChange}" />  
                    <p:ajax event="colReorder" listener="#{quotesHdr.onColumnReorderQuotHdr}"/>
                    <!--7930 09/05/2022 Quotes: UOM and UOM units doesn't change/Quote Selection issues-->
                    <p:ajax event="page" listener="#{quotesHdr.onPagination}" update=""/>
                    <f:facet name="header">
                    </f:facet>
                    <!--Pms Task 6452 : CRM-5018: can we have an "Update selected" button in "Quotes" module?-->  
                    <!--//26-08-2024 : #14137 : CRM-8267   quote list: file attachment won't open on edge browser-->
                    <p:column exportable="false" toggleable="false" selectionMode="multiple" style="width: 10px;text-align: center;cursor:auto"/>

                    <!--                                                <f:facet name="header">
                                                                        <p:inputText id="globalFilter1" class="filter2" onkeyup="PF('tblQt').filter()" />
                                                            </f:facet>-->
                    <!--#2905: Quote List - Change the Search filters to contains , filter matchmode added to all string columns-->
<!--                    <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{quot.oppCustomerName}"  sortBy="#{quot.oppCustomerName}" id="custH" filterMatchMode="contains">

                        <h:link outcome="NewQuote.xhtml?recId=#{quot.recId}"  value="#{quot.oppCustomerName}"  />
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{quot.oppPrincipalName}" sortBy="#{quot.oppPrincipalName}" id="prinH"  filterMatchMode="contains" >
                        <h:link outcome="NewQuote.xhtml?recId=#{quot.recId}"  value="#{quot.oppPrincipalName}"  />                                  
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_SALES_TEAMS')}" filterBy="#{quot.oppSManName}" sortBy="#{quot.oppSManName}"  filterMatchMode="contains"
                              id="smanHId">
                        <h:link outcome="NewQuote.xhtml?recId=#{quot.recId}"  value="#{quot.oppSManName}"  />    
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_DISTRI')}" filterBy="#{quot.oppDistriName}"   sortBy="#{quot.oppDistriName}"  filterMatchMode="contains"
                              id="distriHId">
                        <h:link outcome="NewQuote.xhtml?recId=#{quot.recId}"  value="#{quot.oppDistriName}"  />    
                    </p:column>
                    2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020
                    <p:column headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" filterBy="#{quot.oppSecCustomerName}"  sortBy="#{quot.oppSecCustomerName}"  filterMatchMode="contains"
                              id="secCustHId">
                        <h:link outcome="NewQuote.xhtml?recId=#{quot.recId}"  value="#{quot.oppSecCustomerName}"  />           
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_PROGRAM')}" filterBy="#{quot.oppCustProgram}"  sortBy="#{quot.oppCustProgram}"  id="prog"  filterMatchMode="contains"  >
                        <h:link outcome="NewQuote.xhtml?recId=#{quot.recId}"  value="#{quot.oppCustProgram}"  />                                   
                    </p:column>

                    <p:column headerText="Quote Value" filterBy="#{quot.oppValue}"  sortBy="#{quot.oppValue}" filterMatchMode="contains"
                              style="text-align: right" id="valHId">
                        <h:link outcome="NewQuote.xhtml?recId=#{quot.recId}"  value="#{quot.oppValue}"  />
                    </p:column>

                    <p:column headerText="Quote Number" filterBy="#{quot.quotNumber}"  sortBy="#{quot.quotNumber}"  filterMatchMode="contains" >
                        <h:link outcome="NewQuote.xhtml?recId=#{quot.recId}"  value="#{quot.quotNumber}"  />                       
                    </p:column> 

                    <p:column headerText="Quote Status" filterBy="#{quot.delivStatusName}"  sortBy="#{quot.delivStatusName}" id="statH"  filterMatchMode="contains" >
                        <h:link outcome="NewQuote.xhtml?recId=#{quot.recId}"  value="#{quot.delivStatusName}"  />                                   

                    </p:column>
                    10-12-2020:TASK#2641-Quote > Quote Date- Filter Fails 
                    <p:column headerText="Quote Date" filterBy="#{rFUtilities.formatDateTime(quotesHdr.convertDate(quot.quotDate), 'da')}"  sortBy="#{quot.quotDate}"   filterMatchMode="contains" >

                        Task #2124: 2124:quotes->date format issue

                        <h:link outcome="NewQuote.xhtml?recId=#{quot.recId}"   >                         
                            <h:outputText value="#{quot.quotDate}" >
                                <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                            </h:outputText>
                        </h:link>
                    </p:column>    id="#{column.columnName}"  -->
                    <!--                    Task #7129 CRM-5063 : add paper clip icon on Quote List-view page so you can see if it has an attachment-->
<!--                    //26-08-2024 : #14137 : CRM-8267   quote list: file attachment won't open on edge browser-->
                    <p:column  style="width:30px;min-width:30px; max-width: 30px;align-content:center;" toggleable="false" exportable="false">
                        <p:commandLink styleClass="attachment-icon" ajax="true"  rendered="#{quot.quotAttCount>0}" title="Click to preview"
                                       actionListener="#{quotesHdr.findAttachments(quot.recId,quot.quotNumber)}">
                            <i   class="fa fa-paperclip attachment-icon"  style="font-size: 1.5rem;cursor: auto; " />   
                        </p:commandLink>
                    </p:column> 
                    <!--                            Feature #6908 Quotes: Sort the quotes by Quote Date by default-->
                    <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
                    <!--26/05/2022:  8057 : CRM-5090: can we add a comma to the "value" field in Quotes-->
                    <c:forEach var="column" items="#{quotesHdr.listQuotHdrColumns}">
                        <p:column headerText="#{column.columnHeader}" style="width:200px"
                                  visible="#{column.columnVisibleFlag==1}"  
                                  filterMatchMode="#{column.columnFilterMatchMode}"                                          
                                  filterBy="#{column.columnName=='insDate' || column.columnName=='quotExpiry' || column.columnName=='quotFollowUp'  || column.columnName=='quotDate' ? globalParams.formatDateTime(quot[column.columnName], 'da') : quot[column.columnName]}"                                
                                  id="#{column.columnName}"
                                  sortBy="#{quot[column.columnName]}" 
                                  field="#{column.columnName}" 
                                  >

                            <c:choose>  
                                <c:when test="#{column.columnName=='insDate' || column.columnName=='quotExpiry' || column.columnName=='quotFollowUp'  || column.columnName=='quotDate'}"> 
                                    <h:link outcome="NewQuote.xhtml?recId=#{quot.recId}"  value="#{globalParams.formatDateTime(quot[column.columnName], 'da')}" /> 
                                </c:when>  
                                <c:otherwise>
                                    <h:link  outcome="NewQuote.xhtml?recId=#{quot.recId}"   value="#{quot[column.columnName]}" /> 
                                </c:otherwise>
                                <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
                                <!--26/05/2022:  8057 : CRM-5090: can we add a comma to the "value" field in Quotes-->
                                <c:when test="#{column.columnName=='quotValue'}">

                                    <h:outputLabel  class="hvr"   value="#{quot.quotValue}">


                                        <f:convertNumber groupingUsed="true"  maxIntegerDigits="15"  
                                                         minFractionDigits="2" maxFractionDigits="2"/>

                                        <p:ajax event="click" listener="#{quotesHdr.redirectPage(quot.recId)}"/>

                                    </h:outputLabel>
                                </c:when>
                            </c:choose>
                        </p:column>
                    </c:forEach> 









                </p:dataTable>
                <ui:include src="dialog/QuoteDtls_1.xhtml" />

                <!--#1027:CRM-2813: HALCO:  Quote module-WHEN can we export?-->

                <!--//7677 20/04/2022 : CRM-5531  Kevin Lowe/Jim Bryce & Associates - Quotes link issue-->


            </h:form>
            <h:form>
                <p:confirmDialog header="Confirm delete"  width="400" global="true"
                                 message="Are you sure to delete?"
                                 widgetVar="confirmationQt" >
                    <div class="div-center">
                        <p:commandButton value="Delete" actionListener="#{quotesDtl.DeleteDtls1()}" update="quotLstForm:tblQdtl"
                                         class="btn btn-danger btn-xs" process="@this"   oncomplete="PF('confirmationQt').hide();PF('dlgQuoteDtls').hide()"     />
                        <p:spacer width="4"/>
                        <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"  oncomplete="PF('confirmationQt').hide();"     />

                    </div>
                </p:confirmDialog>
            </h:form>
            <p:dialog styleClass="disable-scroll" id="dlgQuotePrevw" widgetVar="dlgQuoteAttchmentPreview"  
                header="Quote Number: #{quotesHdr.quotPreviewNumber} - Attachments" width="500px"  height="400px"> 
                
                <h:form id="frmQuotePreview">
                    <p:dataTable id="dtQuoteAttchmentsDlg" widgetVar="dtQuotAtchmt" emptyMessage="No Attachments" value="#{quotesHdr.quotAttachmentList}"   
                         var="quoteAtch" scrollable="true"  scrollHeight="400" style="width: 100%">        
                        <p:column headerText="File Name"  filterBy="#{quoteAtch.quotAttName}" filterMatchMode="contains"  sortBy="#{quoteAtch.quotAttName}" >
                            <p:commandLink value="#{quoteAtch.quotAttName}" ajax="true"  actionListener="#{quotesHdr.showPreviewDlg(quoteAtch.quotAttName)}"/>
                        </p:column>
                    </p:dataTable>
                </h:form>
            </p:dialog>
            
            <p:dialog modal="true" widgetVar="wvDlgPreviewIframeQuot" header="Preview: #{quotesHdr.fileName}" id="dlgPreviewQuotFile"  
              onShow="PF('wvDlgPreviewIframeQuot').initPosition();"
              width="800" position="center" height="600" appendTo="@(body)"
              maximizable="true" resizable="false" draggable="true" closeOnEscape="true">
        <p:ajax event="maximize" onstart="handleDialogResizeQuot(true)" process="@none"/>
        <p:ajax event="restoreMaximize" onstart="handleDialogResizeQuot(false)" process="@none"/> 
        <f:facet name="header">
            <p:outputPanel id="headerOfficePreviewQuot">
                Preview: #{quotesHdr.fileName}
            </p:outputPanel>
        </f:facet>
        <h:form id="frmPreviewQuotFile" style="height:95%">
            <p:outputPanel  styleClass="previewContainer">
                <br/>
                <br/>
                <p:outputPanel rendered="#{empty quotesHdr.previewFile}"   styleClass="fileNoteFound">
                    <h1>File not found</h1>
                </p:outputPanel>
                <p:outputPanel rendered="#{not empty quotesHdr.previewFile}"  styleClass="iFrameContainer">
                    <iframe id="officeFrameQuot" src="#{quotesHdr.previewFile}" width="1000px" height="600px" frameborder="0" class="styleIframe" ></iframe>
                </p:outputPanel>


            </p:outputPanel>
        </h:form>
    </p:dialog>
            <style>
                /*//28-08-2024 : #14137 : CRM-8267   quote list: file attachment won't open on edge browser*/
        .previewContainer{
            width:100%;
            height: 95%;
        }

        .iFrameContainer{
            width:100%;
            height: 100%;
        }

        .styleIframe{
            width:100%;
            height: 100%;
        }   

        .fileNoteFound{
            text-align: center;
            display:flex;
            justify-content: center;
            top:10px;
        }
        /*//26-08-2024 : #14137 : CRM-8267   quote list: file attachment won't open on edge browser*/
        .attachment-icon:hover{
                cursor: pointer!important;
            }
             /*//28-08-2024 : #14137 : CRM-8267   quote list: file attachment won't open on edge browser*/
            .disable-scroll .ui-dialog-content {
 
    overflow: hidden !important;
}
    </style>

    <script>
        var isMaximize = false;
        function resetPreviewDialogSizeQuote() {
            if (isMaximize) {
                var dialog = PrimeFaces.widgets.wvDlgPreviewIframeQuot;
                dialog.toggleMaximize();
                isMaximize = false;
            }
        }

        function handleDialogResizeQuot(updateMaximize) {
            isMaximize = updateMaximize;
            if (isMaximize) {
                document.getElementById('officeFrameQuot').style.height = '100%';
            } else {
                document.getElementById('officeFrameQuot').style.height = '100%';
            }
        }
    </script>
            <ui:include src="dialog/quoteExportDlg.xhtml" /> 

        </div>

        <!--2521 Quotes > Updates to Sync Exceptions and auto download-->
        <ui:include src="../../lookup/CompanyLookupDlg.xhtml"/>
        <!--//28/02/2022 7316 MoreCRM>Quotes > Detailed tab bugs-->
        <ui:include src="../../lookup/PartNumDlg.xhtml"/>

        <ui:include src="/bulkupdate/DataSelfServiceDlg.xhtml"/>
        <!--2420 Quotes List > Sync Exceptions-->
        <ui:include src="dialog/quoteException.xhtml" />  
        <!--13375  CRM-7815   OASIS: Assign the quotes to the correct user when ingesting from OASIS-->
        <ui:include src="../../lookup/UserLookupDlg.xhtml"/>
     <!--11556: 29/01/2023:  ESCALATION CRM-7125 IS Quote: Specifier Contacts DO NOT populate when injesting a job from IS Quote.-->   
        <ui:include src="../../lookup/ContactLookupDlg.xhtml"/>
        <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
        <!--26/05/2022:  8057 : CRM-5090: can we add a comma to the "value" field in Quotes-->
        <style>
            .hvr:hover{
                text-decoration:  underline;
                cursor: pointer;
            }
            /*//07-07-2023 :  #11602 : CRM-7138   quotes: detailed view - no way to open quote from detailed view*/
            .hvre:hover{
                cursor: pointer;
            }
            
            /*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
    @media only screen and (min-height: 600px) and (max-height: 900px) {
    .ui-datatable-scrollable-body {
        height: 65vh;
        outline: 0px;
    }
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
@media only screen and (min-height: 901px) and (max-height: 1152px) {
    .ui-datatable-scrollable-body {
        height: 70vh;
        outline: 0px;
    }
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
@media only screen and (min-height: 1153px) and (max-height: 1440px) {
    .ui-datatable-scrollable-body {
        /*//21-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
        height: 70vh;
        outline: 0px;
    }
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
@media only screen and (min-height: 1500px) and (max-height: 1640px) {
    .ui-datatable-scrollable-body {
        /*//21-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
        height: 70vh;
        outline: 0px;
    }
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
.ui-datatable-scrollable-body {
    outline: 0px;
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
.ui-datatable-scrollable-header *,
.ui-datatable-scrollable-theadclone * {
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    /*width: 100%;*/
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
    width: 15px;
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
.ui-datatable-scrollable table {
    /* table-layout: fixed !important; */
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
.ui-datatable-scrollable-theadclone {
    visibility: collapse !important;
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
.ui-datatable-tablewrapper {
    overflow: initial !important;
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
.ui-paginator {
    background-color: #dedede !important;
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
.q {
    width: 100% !important;
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
.p {
    width: 80% !important;
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
.ui-datatable-scrollable-header-box,
.ui-datatable-scrollable-footer-box {
/*    overflow-x: auto !important;
    overflow-y: hidden !important;*/
    position: relative;
    /*margin-left: 0px !important;*/
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
.ui-datatable-scrollable-view {
    position: relative;
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
.top-scrollbar {
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    height: 16px; /* Adjust height as needed */
    overflow-x: auto;
    overflow-y: hidden;
    z-index: 10; /* Ensure it appears above the DataTable */
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
@-moz-document url-prefix() {
    .top-scrollbar {
        height: 12px; /* Height for Firefox */
    }
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-*/
.top-scrollbar div {
    width: 100%; /* Match the width of the DataTable */
    height: 20px; /* Match the height of the scrollbar container */
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-header {
    z-index: 20; /* Ensure it appears above the DataTable */
}

        </style>

<!--//          #12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
<script>
    
    //04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top-->
                function applyTopScrollbarQuot() {
                  var tableExists = document.getElementById('quotLstForm:tblQt') === null;
                  if(tableExists){
                      return;
                  }
    var dataTable = PF('tblQt').jq;
    var scrollableHeaderBox = dataTable.find('.ui-datatable-scrollable-header-box');
    var scrollableBody = dataTable.find('.ui-datatable-scrollable-body');
    var scrollableView = dataTable.find('.ui-datatable-scrollable-view');

    // Check if the scrollbar already exists
    if ($('.top-scrollbar').length === 0) {
        // Create the custom top scrollbar
        var topScrollbar = document.createElement('div');
        topScrollbar.className = 'top-scrollbar';
        var innerDiv = document.createElement('div');
        var width = scrollableBody.find('table').width();
        if(width > 1338){
            if(1358 > width){
                width = 1360;
            }
        }
        console.log('width=',width)
        innerDiv.style.width = width + 'px';
        topScrollbar.appendChild(innerDiv);

        // Insert the top scrollbar above the scrollable header box
        scrollableHeaderBox.before(topScrollbar);

        // Synchronize scroll positions
        topScrollbar.addEventListener('scroll', function () {
            scrollableBody.scrollLeft(topScrollbar.scrollLeft);
            scrollableHeaderBox.scrollLeft(topScrollbar.scrollLeft);
        });

        scrollableBody.on('scroll', function () {
            topScrollbar.scrollLeft = scrollableBody.scrollLeft();
            scrollableHeaderBox.scrollLeft(scrollableBody.scrollLeft());
        });

        scrollableHeaderBox.on('scroll', function () {
            topScrollbar.scrollLeft = scrollableHeaderBox.scrollLeft();
            scrollableBody.scrollLeft(scrollableHeaderBox.scrollLeft());
        });
    }
}

//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top
function applyTopScrollbarQuotDetails() {
                  var tableExists = document.getElementById('quotLstForm:tblQdtl') === null;
                  if(tableExists){
                      return;
                  }
    var dataTable = PF('tbldtl1').jq;
    var scrollableHeaderBox = dataTable.find('.ui-datatable-scrollable-header-box');
    var scrollableBody = dataTable.find('.ui-datatable-scrollable-body');
    var scrollableView = dataTable.find('.ui-datatable-scrollable-view');

    // Check if the scrollbar already exists
    if ($('.top-scrollbar').length === 0) {
        // Create the custom top scrollbar
        var topScrollbar = document.createElement('div');
        topScrollbar.className = 'top-scrollbar';
        var innerDiv = document.createElement('div');
        var width = scrollableBody.find('table').width();
        if(width > 1338){
            if(1358 > width){
                width = 1360;
            }
        }
        console.log('width=',width)
        innerDiv.style.width = width + 'px';
        topScrollbar.appendChild(innerDiv);

        // Insert the top scrollbar above the scrollable header box
        scrollableHeaderBox.before(topScrollbar);

        // Synchronize scroll positions
        topScrollbar.addEventListener('scroll', function () {
            scrollableBody.scrollLeft(topScrollbar.scrollLeft);
            scrollableHeaderBox.scrollLeft(topScrollbar.scrollLeft);
        });

        scrollableBody.on('scroll', function () {
            topScrollbar.scrollLeft = scrollableBody.scrollLeft();
            scrollableHeaderBox.scrollLeft(scrollableBody.scrollLeft());
        });

        scrollableHeaderBox.on('scroll', function () {
            topScrollbar.scrollLeft = scrollableHeaderBox.scrollLeft();
            scrollableBody.scrollLeft(scrollableHeaderBox.scrollLeft());
        });
    }
}

//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top
document.addEventListener('DOMContentLoaded', function () {
    applyTopScrollbarQuot();
    applyTopScrollbarQuotDetails();
});

////04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top
//PF('tblQt').on('update', function() {
//    applyTopScrollbarQuot();
//});
    
    $(document).ready(function () {
     /*<![CDATA[*/
                function maxValueValidate(para) {



                    if (para === 1) {
                        var value = document.getElementById("quotLstForm:quotStdRate").value;

                    } else {
                        var value = document.getElementById("quotLstForm:unit").value;

                    }
                    var text = value.toString().split('.');

                    if (text[0].length > 9) {
                        PF('addDialogGrowl1').show([{summary: "Max value exceeded", severity: "error"}]);
                    }

                }

                function maxDecimalValidate(event, para) {

                    if (para === 1) {
                        var value = document.getElementById("quotLstForm:quotStdRate").value;
                    } else {
                        var value = document.getElementById("quotLstForm:unit").value;
                    }

                    var text = value.toString().split('.');
//                    25-09-2024 : #14246 : 2 ESCALATION CRM-8351 : Quote: Unable to update standard price field for robroy items
                    if ((text[1] && text[1].length >= 6) || (event.keyCode >= 65 && event.keyCode <= 90) || (event.keyCode >= 97 && event.keyCode <= 122)) {
                        return false;
                    } else {
                        return true;
                    }

                }
                /*  ]]>*/

                window.myFunction3 = maxValueValidate;
                window.myFunction4 = maxDecimalValidate;

            });
            
            function test(){
                var checked=$(document).find(":checked")["0"].checked;
//                console.log("------checked-----"+checked);
                if(checked===true){
                    PF('tblQt').selectAllRows();
                } else{
                   PF('tblQt').unselectAllRows();
                }
            }
    
</script>


    </ui:define>

</ui:composition>
