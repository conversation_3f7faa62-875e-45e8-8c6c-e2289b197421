<ui:composition  xmlns="http://www.w3.org/1999/xhtml" 
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:f="http://java.sun.com/jsf/core" 
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:p="http://primefaces.org/ui" 
                 xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                 template="#{layoutMB.template}">
    <!--#302 Quotes > Move Line Items to a new tab-->
    <h:head>
    </h:head>
    <!--#359 Page tirle quotes-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>
            Quote Details 
        </title>
    </ui:define>
    <ui:define name="metadata">  

        <f:metadata>

            <!--            Task#3843-CRM-3841: Quote Watchers  16-03-2021  by harshithad-->
            <f:viewAction action="#{quotesHdr.listQuoteWatchers(quotesHdr.recId)}" />
            <f:viewParam id="recId" name="recId" value="#{quotesHdr.recId}"   />
            <f:viewParam id="oppId" name="oppId" value="#{quotesHdr.oppId}"   />
            <!--//Feature #3569:CRM-3923: quotes: no way create a new quote from a job-->
            <f:viewParam id="jobId" name="jobId" value="#{jobQuotes.jobId}"   />
            <!--834 Quotes: issue in Clone-->
            <f:viewParam id="clnSts" name="clnSts" value="#{quotesHdr.clnSts}"   />

            <f:viewParam id="tempRecId" name="tempRecId" value="#{quotesHdr.tempRecId}"   />

            <!--Feature #3456: CRM-3920: Quotes: cloning a quote linked to a job or project should keep the link-->
            <f:viewAction action="#{quotesHdr.getQuoteCloneJobId(quotesHdr.clnSts,quotesHdr.tempRecId)}" />   

            <!--             //#3167:Comapnies- quote tab
                         //seema - 05/02/2020-->
            <!--#3190 Quotes> Unable to create new quote-->

            <f:viewParam id="compId" name="compId" value="#{quotesHdr.oppCustomerTemp}"   />
            <!--//03-03-2023 : #10834 : CRM-6736   Quote: when jumping from the addin into the quote form, land on the line item tab-->
            <f:viewParam name="tabid" value="#{quotesHdr.tabIndex}"/>
            <f:viewAction action="#{quotesHdr.initializeQuotes()}" /> 
            <!--//10923: 20/03/2023: ESCALATIONS CRM-6796   quotes: creating a new one brings already used quote number-->
            <!--10740 : 21/02/2023: CRM-6199: Priority : Quote Number Customization for Quote Creation-->
            <f:viewAction  action="#{quotesHdr.getQuotSeqnumFunction()}"/>
            <!--10740 : 21/02/2023: CRM-6199: Priority : Quote Number Customization for Quote Creation-->
            <f:viewAction  action="#{quotesHdr.generateQuoteNo()}"/>
            <!--Feature #5747:CRM-4725: Want to UNLINK a quote from an oppty by harshithad  21/09/2021-->
           <!--<f:viewAction  action="#{quotesHdr.displayQuotes()}"/>--> 
            <!--2520 - Related - CRM-1521: Tutorial button landing urls - Quotes, Samples, Opportunities, Jobs, Projects-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('QUOT_DTL'))}" />
            <!-- #4644: handling mandatory field from settings -->
            <!--            //     #11098  Quotes Code Optimisation : Quotes Slowness issues for Basic tab-->
            <!--// reverted the code changes for #11098-->
            <f:viewAction action="#{fieldsMstService.load('CONT_DTL')}"/>
            <!--//04-08-2023 : #11805 : CRM-7169: Quotes: Other Parties Features-->
            <f:viewAction action="#{quotesHdr.listOthr(quotesHdr.recId)}" />
    <!--<f:viewAction action="#{jobQuotes.setQuoteTopicVal(jobQuotes.jobId)}" />-->  

            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('QUOT_DTL')}" />
            <!--//15-01-2024 :  #12912 : Investigate Quote details screen - slowness in page load-->
            <f:viewAction action="#{quotesHdr.isPageAccessible(quotesHdr.recId)}"/>


        </f:metadata>

    </ui:define>
    <ui:define name="title">
        <f:event type="preRenderView" listener="#{facesContext.externalContext.setResponseHeader('Cache-Control','no-cache, no-store')}" />
        <!--<f:event listener="#{oppService.isPageAccessible}" type="preRenderView"/>-->
        <!--<ui:param name="title" value=" #{custom.labels.get('IDS_OPP')} List  "/>-->
        <div class="row">
            <div class="col-md-6"> 
                <p:outputLabel  value="#{quotesHdr.recId == 0 ? 'New' : ''}" style="font-weight: normal;" />
                Quote 
                <p:outputLabel  value="#{quotesHdr.recId > 0 ? 'Details' : ''}" style="font-weight: normal;" />
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>
    <!--    <ui:define name="title">
            <div class="row">
                <div class="col-md-6">
                    <f:event listener="#{quotesHdr.isPageAccessible}" type="preRenderView"/>
                    #3354 Fix Sonarqube issues
                    <title>
                        <p:outputLabel  value="#{quotesHdr.recId == 0 ? 'New' : ''}"   />
                        Quote 
                        <p:outputLabel  value="#{quotesHdr.recId > 0 ? 'Details' : ''}"   />
                    </title>
                </div>
                <div class="col-md-6" align="right">
    
                </div>
                <ui:include src="/help/Help.xhtml"/>
            </div>
        </ui:define>-->

    <ui:define name="menu">
    </ui:define>

    <ui:define name="body">
        <style>
            /*//04-08-2023 : #11805 : CRM-7169: Quotes: Other Parties Features*/
            .ui-g li {
                list-style: none;
            }
            .ui-widget-content {
                border: white;
            }
        </style>
        <div class="box box-info box-body">
            <h:form id="quotefrom" >
                <!--//27-12-2023 : #12778 : CRM-7619	Oasis Jobs: Surfacing Quote Number-->
                <!--<f:event listener="#{quotesHdr.isPageAccessible(quotesHdr.recId)}" type="preRenderView"/>-->
                <!--20-02-2023 10695 CRMSYNC-139  Add Autoquotes option to 'Fetch from' drop-down box-->
                <p:remoteCommand id="applyQuoteRfCompAlies" name="applyQuoteRfCompAlies" autoRun="false"
                                 action="#{autoQuotesService.applyRfComp(aurinkoCompService.aurinkoComp.tsCompId,viewCompLookupService.selectedCompany.compId,viewCompLookupService.selectedCompany.compName)}"
                                 update=":frmQuoteCompsAliasLookup:opQuoteCompAliasLookup"/>

                <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->

<!--<p:remoteCommand  autoRun="true" immediate="true" actionListener="#{quotesHdr.initializeQuotes}" update="quotefrom:tabQuot:quotNumb"/>-->
                <p:remoteCommand name="applyActivePrinci" autoRun="false"  process="@this" immediate="true" actionListener="#{quotesHdr.updateFieldsOnSelection(viewCompLookupService.selectedCompany,1)}"/>
                <!--#336 Quotes > SuperQuote updates-->
    <!--                <p:remoteCommand name="applyCRMCust" actionListener="#{quotesHdr.applyCompany(viewCompLookupService.selectedCompany)}"action="#{quotesHdr.selectCompany(2,viewCompLookupService.selectedCompany.compId,viewCompLookupService.selectedCompany.compNamviewCompLookupService.selectedCompany.compSmanId,viewCompLookupService.selectedCompany.compType)}" />-->
                <!--//06-07-2022 8348 Quote Line Item: Change Part Number lookup to Input-text Autocomplete feature-->
                <p:remoteCommand name="applyActivePrinciForSuperQuote" autoRun="false" immediate="true" actionListener="#{quotesHdr.updateFieldsOnSelectionOfSuperQuote(viewCompLookupService.selectedCompany)}"/>
                <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                <!--//19-08-2022 : #8768 :Direct Request : Quick Add Line Item - auto place the cursor on the newly added part--> 
<!--                <p:remoteCommand name="selectPrincipal" autoRun="false"  immediate="true" actionListener="#{quotesDtl.superQuotPrinci(viewCompLookupService.selectedCompany)}" update=":quotefrom:tabQuot:tblQuotDtls"
                                 oncomplete="select();"/>-->
                <!--9806 Quote Refactoring:  Quotes Line item grid add Row - taking time-->
                <p:remoteCommand name="selectPrincipal" autoRun="false"  immediate="true" actionListener="#{quotesHdr.superQuotPrinci(viewCompLookupService.selectedCompany)}" update=":quotefrom:tabQuot:tblQuotDtls"
                                 oncomplete="select();"/>
                <!--//04-08-2023 : #11805 : CRM-7169: Quotes: Other Parties Features-->
                <p:remoteCommand name="applyOtherComp"                     
                                 action="#{quotesHdr.selectCompany(viewCompLookupService.selectedCompany.compId,
                                           viewCompLookupService.selectedCompany.compName,
                                           viewCompLookupService.selectedCompany.compTypeName)}" 
                                 update="quotefrom:tabQuot:tblOthrList" />
                <p:remoteCommand name="applyContacts" 
                                 actionListener="#{quotesHdr.applyContacts(viewContLookupService.selectedContact)}"
                                 update="quotefrom:tabQuot:tblOthrList" /> 
                <!--//09-12-2022 : #10035 : CRM-6132: Quote Refactoring: Changes to company/contact type ahead-->
                <p:remoteCommand name="applyCont1"  autoRun="false" immediate="true" process="@this" actionListener="#{quotesHdr.applyContact1(viewContLookupService.selectedContact,1)}" />
                <p:remoteCommand name="applyCont2"  autoRun="false" immediate="true" process="@this" actionListener="#{quotesHdr.applyContact2(viewContLookupService.selectedContact)}"/> 
                <p:remoteCommand name="applyCont3"  autoRun="false" immediate="true" process="@this" actionListener="#{quotesHdr.applyContact3(viewContLookupService.selectedContact,1)}"/> 
                <p:remoteCommand name="applyCont4"  autoRun="false" immediate="true" process="@this" actionListener="#{quotesHdr.applyContact4(viewContLookupService.selectedContact)}"/>
                <!--//16-03-2023 : #10892 : CRM-6737 Quote Clone: reprice according to the new customer selected-->
                <p:remoteCommand  name="quotCloneRemote" actionListener="#{quotesHdr.updateQuoteCloneCustomer(viewCompLookupService.selectedCompany)}" update=":formQuotClone:quotCloneCustomer"/>
                <!--//18-11-2022 : #9777 : PRIORITY: CRM-6132  Quotes: typeahead for customer/princi/distributor secondary contacts-->
                <!--<p:remoteCommand name="applyForAllComp"  autoRun="false" immediate="true" actionListener="#{quotesHdr.applyCompanyRemote}"/>-->
                <!--//06-02-2023 : #10619 : CRM-6498 : Logic change for adding new line item to quotes-->
                <!--<p:remoteCommand name="applyContForAllComp"  autoRun="false" immediate="true" actionListener="#{quotesHdr.applyCompanyContactRemote}"/>-->
                <script>

                    //09-12-2022 : #10035 : CRM-6132: Quote Refactoring: Changes to company/contact type ahead-->
                    function applyCompanyType(compType) {
//                        var compName = '';
                        switch (compType) {
                            case 1:
                            {
                                var y = $("#quotefrom\\:tabQuot\\:oppPrincipal_input");
                               // console.log('y=' + y.val())
                                if (y.val().length > 2) {
                               //     console.log('length is greater')
                                } else {
                                    var s = document.getElementById("quotefrom:tabQuot:oppPrincipal_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                            case 2:
                            {
//                    compName = document.getElementById("quotefrom:tabQuot:oppCustomer_input").value;
                                var y = $("#quotefrom\\:tabQuot\\:oppCustomer_input");
                               // console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                  //  console.log('length is greater')
                                } else {
                                    var s = document.getElementById("quotefrom:tabQuot:oppCustomer_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                            case 3:
                            {
//                    compName = document.getElementById("quotefrom:tabQuot:oppDistributor_input").value;
                                var y = $("#quotefrom\\:tabQuot\\:oppDistributor_input");
                           //     console.log('y=' + y.val())
                                if (y.val().length > 2) {
                               //     console.log('length is greater')
                                } else {
                                    var s = document.getElementById("quotefrom:tabQuot:oppDistributor_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                            case 4:
                            {
//                    compName = document.getElementById("quotefrom:tabQuot:oppSecCustomer_input").value;
                                var y = $("#quotefrom\\:tabQuot\\:oppSecCustomer_input");
                             //   console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                 //   console.log('length is greater')
                                } else {
                                    var s = document.getElementById("quotefrom:tabQuot:oppSecCustomer_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                        }
//                        applyForAllComp([{name: 'compName', value: compName}, {name: 'compType', value: compType}]);
//                        removeFocusWithTime();
                    }
                    function applyCompanyContontact(compContType) {
//                        var compContName = '';
                        switch (compContType) {
                            case 1:
                            {
//                                compContName = document.getElementById("quotefrom:tabQuot:oppPrincipalContact_input").value;
                                var y = $("#quotefrom\\:tabQuot\\:oppPrincipalContact_input");
                            //    console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                //    console.log('length is greater')
                                } else {
                                    var s = document.getElementById("quotefrom:tabQuot:oppPrincipalContact_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                            case 2:
                            {
//                                compContName = document.getElementById("quotefrom:tabQuot:oppCustomerContact_input").value;
                                var y = $("#quotefrom\\:tabQuot\\:oppCustomerContact_input");
                               //  console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                  //  console.log('length is greater')
                                } else {
                                    var s = document.getElementById("quotefrom:tabQuot:oppCustomerContact_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                            case 3:
                            {
//                                compContName = document.getElementById("quotefrom:tabQuot:oppDistributorContact_input").value;
                                var y = $("#quotefrom\\:tabQuot\\:oppDistributorContact_input");
                               // console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                //    console.log('length is greater')
                                } else {
                                    var s = document.getElementById("quotefrom:tabQuot:oppDistributorContact_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                            case 4:
                            {
//                                compContName = document.getElementById("quotefrom:tabQuot:oppSecCustomerContact_input").value;
                                var y = $("#quotefrom\\:tabQuot\\:oppSecCustomerContact_input");
                               // console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                //    console.log('length is greater')
                                } else {
                                    var s = document.getElementById("quotefrom:tabQuot:oppSecCustomerContact_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                        }
//                        applyContForAllComp([{name: 'compContName', value: compContName}, {name: 'compContType', value: compContType}]);
//                        removeFocusWithTime();
                    }
                    function delayForkeyup() {
                        setTimeout(function () {

                        }, 500);
                    }

                    function removeingFocus() {
                        document.activeElement.blur();
                    }

                </script>
            <!--<p:remoteCommand name="applyCont5"  autoRun="false" immediate="true" actionListener="#{quotesHdr.applyContact5(viewContLookupService.selectedContact)}"/>-->
            <!--<p:remoteCommand  autoRun="#{jobQuotes.jobId != null}" actionListener="#{jobQuotes.setQuoteTopicVal(jobQuotes.jobId)}" />-->
                <div class="box-header with-border">
                    <div class="row">
                        <div class="col-md-9" >
                            <p:outputPanel id="btns" style="display: inline-flex; height: 24px;"  >

                                <!--                 #789900: GES Ticket Assigned - Quotations-shreejith added update::dtForm:OppInq:tblQuotDtls-->
                                <!--#302 Quotes > Move Line Items to a new tab-->
                                <!--1999 CRM-3393: Avoid double click - CRM-->
                                <!--//19-07-2022 :  #8487  : Direct Request: Quotes: Make Company/Contact typeahead-->
                                <!--//23-02-2023 :  #10619 : CRM-6498 : Logic change for adding new line item to quotes-->
                                <p:commandButton style="display:none"/>
                                <p:commandButton value="#{quotesHdr.recId > 0 and quotesHdr.editable? 'Edit' : 'Save'}" action="#{quotesHdr.saveOrUpdate(quotesHdr.recId, 'QUOTE')}"  onstart=""                              
                                                 id="btnSaveEditQuot" widgetVar="qotSaveVar" onclick="PF('qotSaveVar').disable();"  oncomplete="PF('qotSaveVar').enable();"  
                                                 class="btn btn-success btn-xs"/>

                                <p:spacer width="10"/>
                                <p:commandButton value="Cancel" id="btnCancelQuot"   action="#{quotesHdr.cancelEdit()}"  
                                                 class="btn btn-warning btn-xs"  />
                                <p:spacer width="10"/>
                                   <!--#13603 Thea threatening to cancel due to application's slow performance-->
                                 <!-- #14608 :  CRM-8513   Jobs : Quotes: need a user custom feature "Do not allow user delete quotes"-->     
                                   <p:commandButton value="Delete" id="btnDeleteQuot"  title=" #{quotesHdr.disableQuoteDeletion()? 'You are not authorized to delete a quote':  'Delete Quote'}"                                                                         
                                                 oncomplete="PF('confirmation').show()"   update=" " rendered="#{quotesHdr.recId > 0}" disabled="#{quotesHdr.disableQuoteDeletion()}"  class="btn btn-danger btn-xs"  >
                                    <!--<p:confirm header="Confirmation" message="Are you sure to delete?" icon="ui-icon-alert" />-->
                                </p:commandButton> 
                                <p:spacer width="10"/>
                                <!--//27-10-2022 : #9470 : PRIORITY : Quote PDF settings-->
                                <p:commandButton  class="btn btn-primary btn-xs"  id="btnGeneratePdf" value="Generate PDF"  actionListener="#{quotesHdr.pdfQuote(quotesHdr)}" update=":formQuotPdf" rendered="#{quotesHdr.recId > 0}" disabled="#{!quotesHdr.editable}" oncomplete="PF('quotPdf').show()"/>

                                <p:spacer width="10"/>
                                <!--#302 Quotes > Move Line Items to a new tab-->
                                <!--//16-03-2023 : #10892 : CRM-6737 Quote Clone: reprice according to the new customer selected-->
                                <p:commandButton  class="btn btn-primary btn-xs"  id="btnCloneQuot" value="Clone"  oncomplete="PF('quotCloneDlg').show()" disabled="#{!quotesHdr.editable}" rendered="#{quotesHdr.recId > 0}"
                                                  update=":formQuotClone" actionListener="#{quotesHdr.defaultQuotCloneCustom()}">
                                </p:commandButton>
                                <!--5859 IS Quotes: Quotes > ISQ Link to PDF-->
                                <p:spacer width="10"/>

                                <p:commandButton  class="btn btn-primary btn-xs"  id="btnHistoryQuot" value="Show History"  oncomplete="PF('quotHistoryDlg').show()" disabled="#{!quotesHdr.editable}" rendered="#{quotesHdr.recId > 0}"
                                                  update=":formQuotHistory:opQuotHistory" actionListener="#{quotHistoryService.fetchQuoteHistory(quotesHdr.recId)}">
                                </p:commandButton>
                                <!--5859 IS Quotes: Quotes > ISQ Link to PDF-->
                                <p:spacer width="10"/>

                                <p:commandButton  class="btn btn-primary btn-xs"  id="btnIsQuotPdf" value="Link to ISQ PDF" 
                                                  actionListener="#{quotesHdr.onPageLoadForNewTab(quotesHdr.crmTSQuotDistriId)}" oncomplete="" 
                                                  disabled="#{!quotesHdr.editable}" rendered="#{quotesHdr.recId > 0 and quotesHdr.crmTSQuotDistriId!=0}"  >
                                </p:commandButton>

                                <p:spacer width="10"/> 
                                <!--#3242 Quotes > View Job-->
                                <!--#1094: Quotes > Link to Job  > No jobs are listed in lookup - changed action method to list jobs -->
                                <!--#2543: CRM-3371: Fwd: RE: Repfabric- Sales Outsource Solutions-->
                                <!--3144 Quotes > Issues in showing links for Job in normal quote-->
    <!--                                <p:commandButton  rendered="#{quotesHdr.disableOpp and quotesHdr.oppId != 0 and quotesHdr.quotJob == 0}"   
                                                  value="Link to #{custom.labels.get('IDS_JOB')}" title="Link to #{custom.labels.get('IDS_JOB')}"
                                                  class="btn btn-primary btn-xs"   action="#{jobs.fetchJobsListForOpps()}"  process="@this"
                                                  update=":frmJobList:dtJobList"   oncomplete="PF('dlgQuotJobList').show(); $('#frmJobList\\:dtJobList\\:col1\\:filter').val($('#quotefrom\\:tabQuot\\:qTopic').val()); PF('dtJobList').filter();"/>
    
                                <p:commandButton  rendered="#{quotesHdr.disableOpp and quotesHdr.oppId != 0 and quotesHdr.quotJob != 0}" 
                                                  class="btn btn-primary btn-xs" title="View #{custom.labels.get('IDS_JOB')}"
                                                  value="View #{custom.labels.get('IDS_JOB')}"  onclick="window.open('../jobs/JobsView.xhtml?id=#{quotesHdr.quotJob}', '_self');"
                                                  /> 
    
                                <p:commandButton  rendered="#{!quotesHdr.disableOpp and quotesHdr.recId > 0 and quotesHdr.quotJob eq 0}"   
                                                  value="Link to #{custom.labels.get('IDS_JOB')}" title="Link to #{custom.labels.get('IDS_JOB')}"
                                                  class="btn btn-primary btn-xs"   action="#{jobs.fetchJobsListForOpps()}"  process="@this"
                                                  update=":frmJobList:dtJobList"   oncomplete="PF('dlgQuotJobList').show(); $('#frmJobList\\:dtJobList\\:col1\\:filter').val($('#quotefrom\\:tabQuot\\:qTopic').val()); PF('dtJobList').filter();"/>
    
                                <p:commandButton  rendered="#{!quotesHdr.disableOpp and quotesHdr.recId > 0 and quotesHdr.quotJob > 0}" 
                                                  class="btn btn-primary btn-xs" title="View #{custom.labels.get('IDS_JOB')}"
                                                  value="View #{custom.labels.get('IDS_JOB')}"  onclick="window.open('../jobs/JobsView.xhtml?id=#{quotesHdr.quotJob}', '_self');"
                                                  />
    
                                <p:spacer width="10"/>-->
                                <!--#3242 Quotes > View Job-->   
    <!--                                <p:commandButton class="btn-info btn-xs" rendered="#{quotesHdr.disableOpp and quotesHdr.recId > 0 and quotesHdr.isSuperQuote==false}" icon="fa fa-external-link" id="vwlnk" disabled="#{!quotesHdr.editable}" onclick="window.open('../opportunity/OpportunityView.xhtml?opid=#{quotesHdr.oppId}', '_self');"
                                                 value="View #{custom.labels.get('IDS_OPP')}"   title="View #{custom.labels.get('IDS_OPP')}" style="float: right; width: 30px; "/>-->
                                <!--button_add_opp-->
                                <!--#3545 CRM-2135: #2: Quotes > Link to Opp > Prefiltered list-->
    <!--                                <p:commandButton   rendered="#{!quotesHdr.disableOpp and quotesHdr.recId > 0 and quotesHdr.isSuperQuote==false}" class="btn-info btn-xs" icon="fa fa-link" id="lnk"
                                                   disabled="#{!quotesHdr.editable}"   oncomplete="PF('oppQtMstDlg').show(); $('#frmQtOppMst\\:dtQtOppMstList\\:custName\\:filter').val($('#quotefrom\\:tabQuot\\:oppCustomer').val()); $('#frmQtOppMst\\:dtQtOppMstList\\:distriName\\:filter').val($('#quotefrom\\:tabQuot\\:oppDistributor').val()); " process="@this"
                                                   actionListener="#{quotesHdr.loadOppList('QUOTE', quotesHdr.oppPrincipal)}" 
                                                   value=""  title="Link to #{custom.labels.get('IDS_OPP')}"  style="float: right; width: 30px; "  update=":quotefrom:btns :dlgQtoppMst :frmQtOppMst :frmQtOppMst:dtQtOppMstList :frmQtOppMst:updLnItmReq"/>
                                <p:commandButton class="btn-success btn-xs" rendered="#{quotesHdr.isSuperQuote==true and quotesHdr.clonedtls==false and  quotesHdr.clnSts==0}" icon="fa fa-link" id="spQtlnk" disabled="#{!quotesHdr.editable || quotesHdr.listQuotDtls.size()==0 || quotesHdr.isOppLinked==true}" 
                                                 oncomplete="PF('confirBulkQuot').show()" value="Bulk generate #{custom.labels.get('IDS_OPP')}"   title="Bulk generate #{custom.labels.get('IDS_OPP')}" style="float: right; width: 30px; "/>-->

                                <!--                                                    update=":frmQtOppMst" />-->
                                <!--<p:spacer width="30"/> from ISQuote-->
                            </p:outputPanel>
                            <!--4872 CRM Sync: Settings for Oasis-->
                            <!--08-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
                            <!--09-03-2023 10862 Quotes : Fetch From > UI Issue-->
                            <p:outputPanel id="btnos" style="display: inline-flex; height: 24px;"  rendered="#{(quotesHdr.recId==0) and (not quotesHdr.clonedtls) and ((not quotesHdr.disableFetchOasisBtn) or (not quotesHdr.disableFetchISQuoteBtn) or quotesHdr.configuredAutoQuote)}">
                                <!--//        4872 CRM Sync: Settings for Oasis-->
                                <!--09-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
                                <p:outputLabel value="Fetch from"  />
                                <p:spacer width="4"/>
                                <h:panelGroup>
                                    <!--08-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
                                    <p:selectOneMenu id="quotCRMTypeFilter" 
                                                     value="#{quotesHdr.filterCRMSyncType}" 
                                                     widgetVar="quotCRMTypeFilterw" 
                                                     class="ui-select-btn">
                                        <f:selectItems value="#{quotesHdr.crmSystemMap}"/>
                                        <p:ajax event="change"  process="@this" listener="#{quotesHdr.onChangeCRMTypeStatus(quotesHdr.filterCRMSyncType)}"  
                                                update=":quotefrom:isquotedateparam :quotefrom:isquoteparam :quotefrom :quotefrom:btnos" />
                                    </p:selectOneMenu>
                                </h:panelGroup>
                                <p:spacer width="4"/>
                                <!--4872 CRM Sync: Settings for Oasis-->
                                <h:panelGroup rendered="#{quotesHdr.filterCRMSyncType==0}">
                                    <!--09-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
                                    <p:selectOneMenu id="quotFilter" value="#{quotesHdr.filterISQuotesStatus}"  widgetVar="quotFilterw" class="ui-select-btn">
                                        <f:selectItem itemLabel="By Date Entered" itemValue="0"/>
                                        <f:selectItem itemLabel="By Date Changed" itemValue="1"/>
                                        <f:selectItem itemLabel="By Quote No." itemValue="2"/>
                                        <p:ajax event="change"  process="@this" listener="#{quotesHdr.onChangeISQuotesStatus(quotesHdr.filterISQuotesStatus)}"  
                                                update=":quotefrom:isquotedateparam :quotefrom:isquoteparam :quotefrom " />
                                    </p:selectOneMenu>
                                </h:panelGroup>
                                <!--4872 CRM Sync: Settings for Oasis-->
                                <!--09-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
                                <h:panelGroup rendered="#{quotesHdr.filterCRMSyncType==1}">
                                    <p:selectOneMenu id="quotFilterO" value="#{quotesHdr.filterISQuotesStatus}"  widgetVar="quotFiltero" class="ui-select-btn">
                                        <f:selectItem itemLabel="By Date Entered" itemValue="0"/>
                                        <!--11462 CRM-7051   OASIS: Add option to fetch OASIS Jobs and Quotes by Job or Quote Number-->
                                        <f:selectItem itemLabel="By Quote No." itemValue="2"/>
                                        <p:ajax event="change"  process="@this" listener="#{quotesHdr.onChangeISQuotesStatus(quotesHdr.filterISQuotesStatus)}"  
                                                update=":quotefrom:isquotedateparam :quotefrom:isquoteparam :quotefrom " />
                                    </p:selectOneMenu>
                                </h:panelGroup>

                                <!--09-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
                                <p:inputText id="isquoteparam" style="width: 76px!important;"  
                                             rendered="#{quotesHdr.filterISQuotesStatus==2}" 
                                             value="#{quotesHdr.filterISQuotes}"  />

                                <!--840 Quotes > Show Date and Fetch from IS Quote button only when iS Quote is configured-->
                                <!--4872 CRM Sync: Settings for Oasis-->
                                <!--09-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
                                <p:calendar rendered="#{(quotesHdr.filterISQuotesStatus==0) or (quotesHdr.filterISQuotesStatus==1)}" 
                                            value="#{quotesHdr.paramISQuotDate}" 
                                            id="isquotedateparam" 
                                            converterMessage="Invalid Date Format" 
                                            style="width: 76px!important; margin: 0; padding: 1px;" pattern="#{globalParams.dateFormat}"  >
                                    <p:ajax event="dateSelect" process="@this" listener="#{quotesHdr.onDateChange(quotesHdr.paramISQuotDate)}" update=":quotefrom:isquoteFetchBtn" />
                                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                                </p:calendar>
                                <p:spacer width="4"/>
                                <!--840 Quotes > Show Date and Fetch from IS Quote button only when iS Quote is configured-->
                                <!--780 Create Quotes from IS Quotes with Header and Line Items-->
                                <!--1461 CRM-3100: FW: Questions/Comments from SOUTHWEST-->
                                <!--09-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
                                <!--14469: 08/10/2024: CRM-8480   ISQuote: job caught in aliaser hangs system when manually fetched-->
                                <p:commandButton  value="Fetch" title="Fetch from ISQuote" onstart="PF('progressDlg123').show()" id="isquoteFetchBtn" onclick="PF('fetchButton').disable();" rendered="#{(quotesHdr.filterCRMSyncType==0)}"
                                                  widgetVar="fetchButton" class="btn btn-primary btn-xs"  actionListener="#{iSQuoteCrmClient.populateAllISQuotesList(quotesHdr.syncUserForIsQuote,quotesHdr.paramISQuotDate,quotesHdr.filterISQuotes,quotesHdr.filterISQuotesStatus)}"
                                                  update=":quotefrom:isQuotSyncDtlsx :isQfrm:isq :isQfrm:dtIsQt :isQfrm:dtIsQtLbl"   oncomplete="PF('isQuotListDtls').show();PF('fetchButton').enable(); PF('progressDlg123').hide()"/>

                                <!--//        4872 CRM Sync: Settings for Oasis-->
                                <!--09-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
                                <!--                                <p:commandButton  value="Fetch" title="Fetch from Oasis" id="isquoteFetchBtn1"  class="btn btn-primary btn-xs"
                                                                                  onclick="PF('fetchButtonO').disable();" rendered="#{(quotesHdr.filterCRMSyncType==1)}"
                                                                                  widgetVar="fetchButtonO"   oncomplete="PF('isQuotListDtls1').show();PF('fetchButtonO').enable();"
                                                                                  actionListener="#{oasisService.fetchOasisData(quotesHdr.syncUserForIsQuote,quotesHdr.paramISQuotDate,quotesHdr.filterISQuotes,quotesHdr.filterISQuotesStatus)}"
                                                                                  update=":isQfrm1"  />-->

                                <!--19-03-2024 13234 Code Optimisation for Oasis Integration Quotes and Jobs : Manual fetch-->
                                <!--14-08-2024 14278 CRM-8374   Oasis: Medgar: manual fetching quotes shows error thats not right-->
                                <!--<p:spacer width="4"/>-->
                                <p:commandButton  value="Fetch" title="Fetch from Oasis" id="btnFetchOasis" widgetVar="wvBtnFetchOasis"
                                                  class="btn btn-primary btn-xs" rendered="#{(quotesHdr.filterCRMSyncType==1)}"
                                                  onclick="PF('wvBtnFetchOasis').disable();" 
                                                  oncomplete="PF('wvBtnFetchOasis').enable();"
                                                  actionListener="#{oasisCrmService.manualOasisQuotesList(loginBean.userId,quotesHdr.paramISQuotDate,quotesHdr.filterISQuotes,quotesHdr.filterISQuotesStatus)}"
                                                  update=":frmOasisQuotesList:opOasisQuotesList"  />

                                <!--16-02-2023 10695 CRMSYNC-139  Add Autoquotes option to 'Fetch from' drop-down box-->
                                <!--09-03-2023 10855 CRMSYNC-263: AutoQuotes: Existing issues (DEP-1447)-->
                                <p:commandButton  value="Fetch" title="Fetch from AutoQuotes" id="btnFetchAutoquote"  class="btn btn-primary btn-xs"
                                                  onclick="PF('wvBtnFetchAutoquote').disable();" rendered="#{quotesHdr.filterCRMSyncType==2}"
                                                  widgetVar="wvBtnFetchAutoquote" oncomplete="PF('wvDtAutoQuotes').clearFilters();PF('wvDlgUnResAutoquote').show();PF('wvBtnFetchAutoquote').enable();"
                                                  actionListener="#{autoQuotesService.fetchQutoesByQuoteId(quotesHdr.filterISQuotes)}"
                                                  update=":isQfrm1 :frmUnResAutoQquoteLookup:dtAutoQuotes"  />

                            </p:outputPanel>
                        </div>
                        <div class="col-md-3" style="text-align: end !important;">
                            <h:outputLabel value="Last update  "  style="font-size: 0.795em;
                                           color: #6A6F73;font-weight: bold"  rendered="#{quotesHdr.recId > 0}"/>   
                            <p:spacer width="4"/>
                            <h:outputLabel   value="#{rFUtilities.convertFromUTC(quotesHdr.updDate)}" style="font-size: 0.795em;
                                             color: #6A6F73;" rendered="#{quotesHdr.recId > 0}">
                                <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                            </h:outputLabel>
                            <p:spacer width="4"/>
                            <h:outputLabel   value="   by #{quotesHdr.getUserById(quotesHdr.updUser)}" style="font-size: 0.795em;
                                             color: #6A6F73;" rendered="#{quotesHdr.recId > 0}"/>

                        </div>
                    </div>
                </div>

                <p:confirmDialog header="Confirm delete"  width="400" global="false"
                                 message="Are you sure to delete #{custom.labels.get('IDS_QUOTES')}?"
                                 widgetVar="confirmation" >
                    <div class="div-center">
                        <!--#13603 Thea threatening to cancel due to application's slow performance-->
                        <p:commandButton value="Delete" actionListener="#{quotesHdr.deleteOperation()}"
                                         class="btn btn-danger btn-xs" process="@this"      />
                        <p:spacer width="4"/>

                        <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"    onclick="PF('confirmation').hide()" 
                                         type="button"  />
                    </div>
                </p:confirmDialog>
                <!--// 27-01-2023 : #10541 : ESCALATION CRM-6610: Multiple instances of the same quote number-->
                <p:confirmDialog header="Confirmation"  width="400" global="false"
                                 message="Quote Number is already being used by another quote. Do you want the quote number to be auto-generated on save?"
                                 widgetVar="quotNumberDlg" >
                    <div class="div-center">
                        <p:commandButton value="Yes" oncomplete="PF('quotNumberDlg').hide()"
                                         class="btn btn-danger btn-xs" process="@this" actionListener="#{quotesHdr.updateQuotNumber()}" 
                                         action="#{quotesHdr.saveOrUpdate(quotesHdr.recId, 'QUOTE')}"      />
                        <p:spacer width="4"/>
                        <p:commandButton value="No"  class="btn btn-warning btn-xs"    onclick="PF('quotNumberDlg').hide()" 
                                         type="button"  />
                    </div>
                </p:confirmDialog>
                <!--//11079 03/05/2023: ESCALATIONS   CRM-6884   Quotes: Updating customer changes the quote number (Refresh)-->

                <p:confirmDialog header="Confirmation"  width="400" global="false"
                                 message="Quote Number #{quotesHdr.quotNumber} is already being used by another quote. Do you want to increment the sequence number?"
                                 widgetVar="quotNumberRfshDlg" >
                    <div class="div-center">
                        <p:commandButton value="Yes" oncomplete="PF('quotNumberRfshDlg').hide()"
                                         class="btn btn-danger btn-xs" process="@this" actionListener="#{quotesHdr.refreshQuotNumber()}" update="quotefrom:tabQuot:quotNumb"/>
                        <p:spacer width="4"/>
                        <p:commandButton value="No"  class="btn btn-warning btn-xs"    onclick="PF('quotNumberRfshDlg').hide()" 
                                         type="button"  />
                    </div>
                </p:confirmDialog>


                <!--#3551 CRM-2135: #5: Quotes > Line Items > Delete All-->
                <!--#336 Quotes > SuperQuote updates-->
                <!--                <p:confirmDialog header="Confirm delete"  width="400" global="false"
                                                 message="Are you sure to delete selected line items?"
                                                 widgetVar="delAllConfirmation" >
                                    <div class="div-center"> 
                                        <p:commandButton value="Delete" actionListener="#{quotesHdr.deleteQuotDtls()}"
                                                         class="btn btn-danger btn-xs"   update=":quotefrom:tabQuot:tblQuotDtls :quotefrom:tabQuot:opVl :quotefrom:tabQuot:delDtls :quotefrom:tabQuot:spQtlnk :quotefrom:tabQuot:lnkSprQt"  oncomplete="PF('delAllConfirmation').hide();"     />
                                        <p:spacer width="4"/>
                                        <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"    onclick="PF('delAllConfirmation').hide()" 
                                                         type="button"  />
                                    </div>
                                </p:confirmDialog>-->



                <p:confirmDialog header="Confirm delete"  width="400" global="false"  widgetVar="delAllConfirmation" > 
                    <!--        <h:form id="confirmationDlg"> -->

                    <f:facet name="message">
                        <p:outputLabel value="Are you sure to delete selected line items?"  />
                        <br/><br/>
                        <p:selectBooleanCheckbox value="#{quotesHdr.isDeleteFrmOppLneItm}"  itemLabel="Delete Linked #{custom.labels.get('IDS_OPP')} line items?" id="chkDeleteStatus">
                            <p:ajax event="change" listener="#{quotesHdr.chnageDelFlag(quotesHdr.isDeleteFrmOppLneItm)}"/>
                        </p:selectBooleanCheckbox>   


                    </f:facet>
                    <!--<div align="center">-->
                    <div class="div-center">
                        <!--#3551 CRM-2135: #5: Quotes > Line Items > Delete All-->
                        <!--                        Task#3918-CRM-4017: quote2opp conversion - cherry pick which lines to generate an opp for from a quote  22-03-2021 by harshithad-->
                        <!-- 6162 validation error message in while adding Line items in Quotes-->
                        <!--//11-04-2023 :#11027 : ESCALATION CRM-6860   Line Item Importing: not accepting 4 digit unit price-->
                        <p:commandButton value="Delete" actionListener="#{quotesHdr.deleteQuotDtls()}" widgetVar="btnDeleteQuotDtl"
                                         onclick="PF('btnDeleteQuotDtl').disable();"
                                         class="btn btn-danger btn-xs"   update=":quotefrom:tabQuot :quotefrom:dlgQuoteDtls"  oncomplete="PF('delAllConfirmation').hide();PF('btnDeleteQuotDtl').enable();"     />
                        <!--:quotefrom:tabQuot:opVl :quotefrom:tabQuot:delDtls  :quotefrom:tabQuot:lnkSprQt  :quotefrom:tabQuot:lnQtOppMstList-->
                        <p:spacer width="4"/>
                        <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"    onclick="PF('delAllConfirmation').hide()" 
                                         type="button"  />
                    </div>
                    <!--</div>--> 
                    <!--</h:form>--> 
                </p:confirmDialog>

                <!--<h4 class="sub_title">-->
                <!--                    <p:outputLabel value="Line Item Details" style="font-size: 20px; font-weight: bold" />
                                    <p:commandButton value="Add" oncomplete="PF('dlgQuoteDtls').show()"  actionListener="#{quotesDtl.clearQuotDtls()}" style="float: right"
                                                     update=" " disabled="#{quotesHdr.recId == 0 || !quotesHdr.editable}" id="addDtls" />-->
                <!--                </h4>-->
                <!-- #789900: GES Ticket Assigned - Quotations-Added selectionMode in p:columns-->
                <!--#3702 CRM-2217: #4: Quotes > Line Items >  Retain pagination-->
                <!--            //     #11098  Quotes Code Optimisation : Quotes Slowness issues for Basic tab-->
                <p:tabView  id="tabQuot"  widgetVar="tabQ"      cache="true" activeIndex="#{quotesHdr.tabIndex}">
                                     <!--<p:ajax event="tabChange" listener="#{tabController.onOppTabChange}"/>-->
                    <p:ajax  event="tabChange" listener="#{quotesHdr.onTabChange}"    />
                    <p:tab title="Basic" id="basicTab" >
                        <p:panelGrid  id="gridQuote" styleClass="panlGrid">
                            <!--   _______________start opportunities______________________  -->
                            <div class="ui-g ui-fluid header-bar">
                                <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                                    <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Primary Information</p:outputLabel>
                                </div>
                                <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                                </div>
                            </div>  

                            <div class="ui-g ui-fluid">

                                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                    <p:outputLabel value="#{custom.labels.get('IDS_PRINCI')}" for="oppPrincipal"  styleClass="required"/>
                                </div> 
                                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                                    <h:panelGroup class="ui-inputgroup"  >
                                        <!--834 Quotes: issue in Clone-->
                                        <!--747-->
                                        <!--1260 Quotes : Inocrrect ToolTip Message for Manufacturer-->
                                        <!--//19-07-2022 :  #8487  : Direct Request: Quotes: Make Company/Contact typeahead-->

                                        <!--//09-12-2022 : #10035 : CRM-6132: Quote Refactoring: Changes to company/contact type ahead-->
                                        <!--//09-12-2022 : #10035 : CRM-6132: Quote Refactoring: Changes to company/contact type ahead-->
                                        <p:autoComplete panelStyleClass="acCompCont" id="oppPrincipal" value="#{quotesHdr.oppPrincipalName}" 
                                                        completeMethod="#{quotesHdr.completeBasicComp}"
                                                        widgetVar="oppPrincipalWidget"
                                                        disabled="#{quotesHdr.disableOpp || quotesHdr.editable || (quotesHdr.isSuperQuote==true and quotesHdr.recId!=0) || (quotesHdr.isSuperQuote==true and quotesHdr.clonedtls==true)}">
                                            <!--//08-12-2022 : #10023 : WEBINAR: Quote Line Items: line items do not enter after selection-->
                                            <!--<f:attribute name="selecetViewCompLookup" value="#{viewComp}"/>`-->
                                            <!--                                            <f:facet name="itemtip">
                                                                                            <h:panelGrid columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Company Information:"/>
                                                                                                </f:facet>
                                            
                                                                                                <h:outputText value="Name:"/>
                                                                                                <h:outputText value="#{viewComp.compName}"/>
                                            
                                                                                                <h:outputText value="Type:"/>
                                                                                                <h:outputText value="#{viewComp.compTypeName}"/>
                                            
                                                                                                <h:outputText value="Sales Team:" rendered="#{viewComp.smanName != null and viewComp.smanName != ''}"/>
                                                                                                <h:outputText value="#{viewComp.smanName}" rendered="#{viewComp.smanName != null and viewComp.smanName != ''}"/>
                                            
                                                                                                <h:outputText value="City:"/>
                                                                                                <h:outputText value="#{viewComp.compCity}"/>
                                            
                                                                                                <h:outputText value="State:"/>
                                                                                                <h:outputText value="#{viewComp.compState}"/>
                                            
                                                                                                <h:outputText value="Zip Code:"/>
                                                                                                <h:outputText value="#{viewComp.compZipCode}" />
                                                                                            </h:panelGrid>
                                                                                        </f:facet>-->
                                            <!--//21-11-2022 : #9744 : Quote Refactoring: Quotes> company type ahead> company info sticks to the page-->
                                            <!--<p:ajax event="keyup" onstart="applyCompanyType(1);"/>-->
                                            <!--<p:ajax event="blur" oncomplete="PF('oppPrincipalWidget').close();"/>-->
                                            <p:ajax event="itemSelect" onstart="PF('dlgProcessingOnTask').show();" listener="#{quotesHdr.selectedPricipal}" oncomplete="PF('dlgProcessingOnTask').hide();" />
                                            <!--<p:ajax event="change" onstart="oppMouseValue(false);if(PF('oppPrincipalWidget').panel.is(':visible')) return false;applyCompanyType(1);"/>-->
                                            <!--<p:ajax event="change" onstart="PF('dlgProcessingOnTask').show();" oncomplete="PF('dlgProcessingOnTask').hide();" listener="#{quotesDtl.basicTabPrincipalUpdate()}" delay="800"/>-->
                                        </p:autoComplete>
                                        <!--//24-04-2023 : #11135 : CRM-6737 : Quotes:Add line item& clone>Manf part number not copied to clonned line item-->
                                        <p:commandButton  icon="fa fa-search" process="@this" immediate="true" disabled="#{quotesHdr.disableOpp || quotesHdr.editable || (quotesHdr.isSuperQuote==true and quotesHdr.recId!=0) || (quotesHdr.isSuperQuote==true and quotesHdr.clonedtls==true)}"
                                                          actionListener="#{quotesHdr.listLookUpvaluesForSuperQuote('applyActivePrinci', 1)}" action="#{paramsService.isCreatMultiple()}"
                                                          update=":formCompLookup :formNew" oncomplete="PF('lookupComp').show()" tabindex="2"  id="btnSrchCompLkp" style="width: 68px;"
                                                          styleClass="btn-info btn-xs" title="#{quotesHdr.recId!=0 and quotesHdr.isSuperQuote==false and quotesHdr.isOppLinked==true and quotesHdr.clonedtls==false and quotesHdr.oppsLnList.size()>0? quotesHdr.fetchToolTipMsg():quotesHdr.isSuperQuote==true and quotesHdr.recId!=0 ? quotesHdr.fetchToolTipMsgForSuperQuote() : quotesHdr.isSuperQuote==true and quotesHdr.clonedtls==true?quotesHdr.fetchToolTipMsgForSuperQuote(): 'Choose Company'}" 
                                                          />
                                        <!--"#{quotesHdr.disableOpp || quotesHdr.editable || (quotesHdr.isSuperQuote==true and quotesHdr.recId!=0 and quotesHdr.isOppLinked==true) ? (quotesHdr.disableOpp and quotesHdr.recId > 0 and quotesHdr.isSuperQuote==false)?quotesHdr.fetchToolTipMsgForSuperQuote():'Choose Company':quotesHdr.fetchToolTipMsg()}"-->
                                        <!--actionListener="#{quotesHdr.listLookUpvalues('applyActivePrinci', 1)}"-->
                                    </h:panelGroup>


                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2 ">
                                    <p:outputPanel id="panelClearPrincipalAndLbl">
                                        <!--//24-11-2022 : #9855 : Quote Clone issue-->
                                        <h:panelGroup id="pnlclrprincisprqt"  rendered="#{quotesHdr.isSuperQuote==true and quotesHdr.recId==0 and quotesHdr.clnSts == 0}"  style="margin-left: -3px;">
                                            <!--2988 CRM-3741: Quote Problem- remove asterisk from "Customer*" in a Quote-->
                                            <p:commandLink value="Clear" disabled="#{quotesHdr.editable}" 
                                                           title="Clear #{custom.labels.get('IDS_PRINCI')} selection" class="btnlukup " 
                                                           style="border: none;background: none"  
                                                           actionListener="#{quotesHdr.clearSelection(5)}"  
                                                           process="@this" 
                                                           update=":quotefrom:tabQuot:oppPrincipal :quotefrom:tabQuot:oppPrincipalContact :quotefrom:tabQuot:pnlclrprincisprqt :quotefrom:tabQuot:oppCustomerLbl" />
                                        </h:panelGroup>
                                        <p:outputLabel id="princiContactLbl" rendered="#{quotesHdr.isSuperQuote==false}" value="#{custom.labels.get('IDS_PRINCI')} Contact" for="oppPrincipalContact" />
                                    </p:outputPanel>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 " style="display: inline-flex;">
                                    <p:outputPanel id="panelOppPrincipalCust">
                                        <h:panelGroup class="ui-inputgroup" rendered="#{quotesHdr.isSuperQuote==false}"  id="princiContactInpt" style="min-width: 365px;">
                                            <!--//19-07-2022 :  #8487  : Direct Request: Quotes: Make Company/Contact typeahead-->
                                            <!--//09-12-2022 : #10035 : CRM-6132: Quote Refactoring: Changes to company/contact type ahead-->
                                            <p:autoComplete id="oppPrincipalContact"  panelStyleClass="acCompCont" 
                                                            value="#{quotesHdr.oppPrincipalContName}"  
                                                            disabled="#{quotesHdr.disableOpp || quotesHdr.editable}"
                                                            completeMethod="#{quotesHdr.completeCompContacts}" minQueryLength="3">
                                                <!--//08-12-2022 : #10023 : WEBINAR: Quote Line Items: line items do not enter after selection-->
                                                <!--                                                <f:facet name="itemtip">
                                                                                                    <h:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8">
                                                                                                        <f:facet name="header">
                                                                                                            <h:outputText value="Contact Information:"/>
                                                                                                        </f:facet>
                                                
                                                                                                        <h:outputText value="Name:"/>
                                                                                                        <h:outputText value="#{viewCompContact.contFullName}"/>
                                                
                                                                                                        <h:outputText value="Company:"/>
                                                                                                        <h:outputText value="#{viewCompContact.compName}"/>
                                                
                                                                                                        <h:outputText value="Job Title:"/>
                                                                                                        <h:outputText value="#{viewCompContact.contJobTitle}"/>
                                                
                                                                                                        <h:outputText value="Email:"/>
                                                                                                        <h:outputText value="#{viewCompContact.contEmailBusiness}"/>
                                                
                                                                                                        <h:outputText value="Phone:"/>
                                                                                                        <h:outputText value="#{viewCompContact.contPhones}"/>
                                                                                                    </h:panelGrid>
                                                                                                </f:facet>-->
                                                <!--//21-11-2022 : #9744 : Quote Refactoring: Quotes> company type ahead> company info sticks to the page-->
                                                <p:ajax event="keyup" onstart="applyCompanyContontact(1);delayForkeyup();" oncomplete="applyCompanyContontact(1);"/>
                                                <p:ajax event="itemSelect" listener="#{quotesHdr.selectedCompContacts}" />
                                                <!--<p:ajax event="change" onstart="if(PF('oppPrincipalContactWidget').panel.is(':visible')) return false;PF('oppPrincipalContactWidget').close()"/>-->
                                                <!--<p:ajax event="blur" oncomplete="PF('oppPrincipalContactWidget').close();"/>-->
                                            </p:autoComplete>
                                            <!--                                        PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                                            <p:commandButton id="btnOppPrinciContSearch" icon="fa fa-search" title="#{quotesHdr.isSuperQuote==false and quotesHdr.isOppLinked==true and quotesHdr.clonedtls==false and quotesHdr.oppsLnList.size()>0? quotesHdr.fetchToolTipLinkedMsg(): 'Choose Contact'}" immediate="true" disabled="#{quotesHdr.disableOpp || quotesHdr.editable}"
                                                             actionListener="#{viewContLookupService.list('applyCont2',1,quotesHdr.oppPrincipal,1)}" 
                                                             action="#{viewContLookupService.disableContNewBtn(quotesHdr.oppPrincipal)}"
                                                             update=":formContLookup :formNew" oncomplete="PF('lookupCont').show()"  
                                                             style="width: 68px;" styleClass="btn-info btn-xs" />
            <!--                                <p:commandButton  disabled="#{opportunities.oppCloseStatus!=0}"id="princiContactLbl"
                                                 icon="fa fa-search" title="Choose Contact" immediate="true" 
                                                 actionListener="#{viewContLookupService.list('applyCont2',1,0,1)}" 
                                                 update=":frmOpp:tabOpp:tblPC :formContLookup" oncomplete="PF('lookupCont').show()"  
                                                 styleClass="btn-info btn-xs" />-->
                                        </h:panelGroup>
                                    </p:outputPanel>
                                    <!--                                </div>
                                                                    <div class="ui-sm-12 ui-md-1 ui-lg-1 ">-->
                                    <p:outputPanel id="panelOppPrincipalClear">
                                        <h:panelGroup id="pnlclrprinci"  rendered="#{quotesHdr.isSuperQuote==false}"  style="padding-left: 3px;">
                                            <p:commandLink rendered="#{quotesHdr.oppPrincipal>0 and quotesHdr.oppsLnList.size()==0}" value="Clear" disabled="#{quotesHdr.editable}" 
                                                           title="Clear #{custom.labels.get('IDS_PRINCI')} selection" class="btnlukup " 
                                                           style="border: none;background: none"  
                                                           actionListener="#{quotesHdr.clearSelection(1)}"  
                                                           process="@this" 
                                                           update=":quotefrom:tabQuot:oppPrincipal :quotefrom:tabQuot:oppPrincipalContact :quotefrom:tabQuot:pnlclrprinci :quotefrom:tabQuot:oppCustomerLbl" />
                                        </h:panelGroup>
                                    </p:outputPanel>
                                </div>

                                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                    <!--2988 CRM-3741: Quote Problem- remove asterisk from "Customer*" in a Quote-->
                                    <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}" for="oppCustomer" id="oppCustomerLbl"  styleClass="#{quotesHdr.oppCustomer eq 0 and quotesHdr.oppDistri eq 0 ? 'required' : quotesHdr.oppCustomer eq quotesHdr.oppDistri ? '' : 'required'}"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                                    <h:panelGroup class="ui-inputgroup"  > 
                                        <!--<h:inputHidden id="hdninputcName" value="#{commTransactionService.quotesService.trnhCustName}"  required="true" requiredMessage="#{custom.labels.get('IDS_CUSTOMER')} is required" />-->
                                        <!--2899 Quotes >  Show Direct Sales when Customer  is same as Distributor-->
<!--                                        <p:inputText rendered="#{globalParams.allowDistriCommEnabled==false}" id="oppCustomer" value="#{quotesHdr.oppCustomerName}" required="true" requiredMessage="#{custom.labels.get('IDS_CUSTOMER')} is required" />
                                        2988 CRM-3741: Quote Problem- remove asterisk from "Customer*" in a Quote--> 
<!--                                       <p:inputText rendered="#{globalParams.allowDistriCommEnabled==true}" id="oppCustomer2" 
                                                     value="#{quotesHdr.oppCustomer eq 0 and quotesHdr.oppDistri eq 0 ? '' : quotesHdr.oppCustomer eq quotesHdr.oppDistri ? '&lt;' : ''} #{quotesHdr.oppCustomer eq 0 and quotesHdr.oppDistri eq 0 ? quotesHdr.oppCustomerName : quotesHdr.oppCustomer eq quotesHdr.oppDistri ? custom.labels.get('IDS_DIRECT_SALES') : quotesHdr.oppCustomerName} #{quotesHdr.oppCustomer eq 0 and quotesHdr.oppDistri eq 0 ? '' : quotesHdr.oppCustomer eq quotesHdr.oppDistri ? '&gt;' : ''}" 
                                                     required="#{quotesHdr.oppCustomer eq 0 and quotesHdr.oppDistri eq 0 ? 'true' : quotesHdr.oppCustomer eq quotesHdr.oppDistri ? 'false' : 'true'}" requiredMessage="#{custom.labels.get('IDS_CUSTOMER')} is required" />
                                        -->
                                        <!--//19-07-2022 :  #8487  : Direct Request: Quotes: Make Company/Contact typeahead-->
                                        <!--//09-12-2022 : #10035 : CRM-6132: Quote Refactoring: Changes to company/contact type ahead-->
                                        <p:autoComplete id="oppCustomer" value="#{quotesHdr.oppCustomerName}"
                                                        disabled="#{quotesHdr.editable}" panelStyleClass="acCompCont"
                                                        completeMethod="#{quotesHdr.completeCustomer}" minQueryLength="3"
                                                        onkeyup="applyCompanyType(2);">
                                            <!--//08-12-2022 : #10023 : WEBINAR: Quote Line Items: line items do not enter after selection-->
                                            <!--<f:attribute name="selecetViewCompLookup" value="#{viewComp}"/>`-->
                                            <!--                                            <f:facet name="itemtip">
                                                                                            <h:panelGrid columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Company Information:"/>
                                                                                                </f:facet>
                                            
                                                                                                <h:outputText value="Name:"/>
                                                                                                <h:outputText value="#{viewComp.compName}"/>
                                            
                                                                                                <h:outputText value="Type:"/>
                                                                                                <h:outputText value="#{viewComp.compTypeName}"/>
                                            
                                                                                                <h:outputText value="Sales Team:" rendered="#{viewComp.smanName != null and viewComp.smanName != ''}"/>
                                                                                                <h:outputText value="#{viewComp.smanName}" rendered="#{viewComp.smanName != null and viewComp.smanName != ''}"/>
                                            
                                                                                                <h:outputText value="City:"/>
                                                                                                <h:outputText value="#{viewComp.compCity}"/>
                                            
                                                                                                <h:outputText value="State:"/>
                                                                                                <h:outputText value="#{viewComp.compState}"/>
                                            
                                                                                                <h:outputText value="Zip Code:"/>
                                                                                                <h:outputText value="#{viewComp.compZipCode}" />
                                                                                            </h:panelGrid>
                                                                                        </f:facet>-->
                                            <!--//21-11-2022 : #9744 : Quote Refactoring: Quotes> company type ahead> company info sticks to the page-->
                                            <!--<p:ajax event="keyup" onstart="applyCompanyType(2);delayForkeyup();" oncomplete="applyCompanyType(2);"/>-->
                                            <p:ajax event="itemSelect" onstart="PF('dlgProcessingOnTask').show();" listener="#{quotesHdr.selectCustomer}" oncomplete="PF('dlgProcessingOnTask').hide();"/>
                                            <!--<p:ajax event="change" onstart="applyCompanyType(2);"/>-->
                                            <!--<p:ajax event="blur" oncomplete="PF('oppCustomerWidget').close();"/>-->
                                        </p:autoComplete>
                                        <!--2568 CRM-3365 Teps: Can't change quote customer-->
                                        <p:commandButton id="btnOppCustSearch" icon="fa fa-search" title="Choose Company" immediate="true" disabled="#{quotesHdr.editable}" 
                                                         actionListener="#{quotesHdr.listLookUpvalues('applyActivePrinci', 5)}"
                                                         update=":formCompLookup :formNew  quotefrom:tabQuot:quotNumb" tabindex="4" 
                                                         style="width: 68px;" styleClass="btn-info btn-xs" />
                                    </h:panelGroup>
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2 ">
                                    <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')} Contact" for="oppCustomerContact"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 " style="display: inline-flex;">
                                    <h:panelGroup class="ui-inputgroup" style="min-width: 365px;">
                                        <!--<p:inputText id="oppCustomerContact" value="#{quotesHdr.oppCustomerContName}"   />-->
                                        <!--//19-07-2022 :  #8487  : Direct Request: Quotes: Make Company/Contact typeahead-->
                                        <!--//09-12-2022 : #10035 : CRM-6132: Quote Refactoring: Changes to company/contact type ahead-->
                                        <p:autoComplete id="oppCustomerContact"  panelStyleClass="acCompCont" 
                                                        value="#{quotesHdr.oppCustomerContName}"  
                                                        disabled="#{quotesHdr.editable}"
                                                        completeMethod="#{quotesHdr.completeCustomerContacts}"
                                                        minQueryLength="3">
                                            <!--//08-12-2022 : #10023 : WEBINAR: Quote Line Items: line items do not enter after selection-->
                                            <!--                                            <f:facet name="itemtip">
                                                                                            <h:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Contact Information:"/>
                                                                                                </f:facet>
                                            
                                                                                                <h:outputText value="Name:"/>
                                                                                                <h:outputText value="#{viewCompContact.contFullName}"/>
                                            
                                                                                                <h:outputText value="Company:"/>
                                                                                                <h:outputText value="#{viewCompContact.compName}"/>
                                            
                                                                                                <h:outputText value="Job Title:"/>
                                                                                                <h:outputText value="#{viewCompContact.contJobTitle}"/>
                                            
                                                                                                <h:outputText value="Email:"/>
                                                                                                <h:outputText value="#{viewCompContact.contEmailBusiness}"/>
                                            
                                                                                                <h:outputText value="Phone:"/>
                                                                                                <h:outputText value="#{viewCompContact.contPhones}"/>
                                                                                            </h:panelGrid>
                                                                                        </f:facet>-->
                                            <!--//21-11-2022 : #9744 : Quote Refactoring: Quotes> company type ahead> company info sticks to the page-->
                                            <p:ajax event="keyup" onstart="applyCompanyContontact(2);delayForkeyup();" oncomplete="applyCompanyContontact(2);"/>
                                            <p:ajax event="itemSelect"  listener="#{quotesHdr.selectedCustomerContacts}"/>
                                            <!--<p:ajax event="change" onstart="applyCompanyContontact(2);"/>-->
                                            <!--<p:ajax event="blur" oncomplete="PF('oppCustomerContactWidget').close();"/>-->
                                        </p:autoComplete>
                                        <!--2568 CRM-3365 Teps: Can't change quote customer-->
                                        <!--                                        PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                                        <p:commandButton id="btnOppCustContSearch" icon="fa fa-search" title="Choose Contact" immediate="true" disabled="#{quotesHdr.editable}"
                                                         actionListener="#{viewContLookupService.list('applyCont1',2,quotesHdr.oppCustomer,1)}" 
                                                         action="#{viewContLookupService.disableContNewBtn(quotesHdr.oppCustomer)}"
                                                         update=":formContLookup :formNew" oncomplete="PF('lookupCont').show()"  
                                                         style="width: 68px;" styleClass="btn-info btn-xs" />
         <!--                                 <p:commandButton  disabled="#{opportunities.oppCloseStatus!=0}"
                                              icon="fa fa-search" title="Choose Contact" immediate="true" 
                                              actionListener="#{viewContLookupService.list('applyCont1',2,viewCompLookupService.selectedCompany.compId,1)}" 
                                              update=":frmOpp:tabOpp:dtCC :formContLookup" oncomplete="PF('lookupCont').show()"  
                                              styleClass="btn-info btn-xs" />  -->
                                    </h:panelGroup>
                                    <!--                                </div>
                                                                    <div class="ui-sm-12 ui-md-1 ui-lg-1 ">-->
                                    <!--2899 Quotes >  Show Direct Sales when Customer  is same as Distributor-->
                                    <h:panelGroup id="pnlclrcust" style="padding-left: 3px;" >
                                        <!--2988 CRM-3741: Quote Problem- remove asterisk from "Customer*" in a Quote-->
                                        <!--//19-07-2022 :  #8487  : Direct Request: Quotes: Make Company/Contact typeahead-->
                                        <p:commandLink rendered="#{quotesHdr.oppCustomer>0 and quotesHdr.oppsLnList.size()==0}" value="Clear" disabled="#{quotesHdr.editable}" 
                                                       title="Clear #{custom.labels.get('IDS_CUSTOMER')} selection" class="btnlukup " 
                                                       style="border: none;background: none"  
                                                       actionListener="#{quotesHdr.clearSelection(2)}"  
                                                       process="@this" 
                                                       update=":quotefrom:tabQuot:oppCustomer :quotefrom:tabQuot:oppCustomerContact :quotefrom:tabQuot:pnlclrcust  :quotefrom:tabQuot:oppCustomerLbl" />
                                    </h:panelGroup>
                                </div>


                                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                    <p:outputLabel value="#{custom.labels.get('IDS_DISTRI')}" for="oppDistributor"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                                    <h:panelGroup class="ui-inputgroup"  >
                                        <!--<h:inputHidden id="hdninputDistrName" value="#{commTransactionService.transHdr.trnhDistriName}"   />-->
                                        <!--<p:inputText id="oppDistributor" value="#{quotesHdr.oppDistriName}" />-->
                                        <!--//19-07-2022 :  #8487  : Direct Request: Quotes: Make Company/Contact typeahead-->
                                        <!--//09-12-2022 : #10035 : CRM-6132: Quote Refactoring: Changes to company/contact type ahead-->
                                        <p:autoComplete id="oppDistributor" value="#{quotesHdr.oppDistriName}" panelStyleClass="acCompCont" 
                                                        completeMethod="#{quotesHdr.completeDistibute}" disabled="#{quotesHdr.editable}" 
                                                        minQueryLength="3"  onkeyup="applyCompanyType(3);">
                                            <!--//08-12-2022 : #10023 : WEBINAR: Quote Line Items: line items do not enter after selection-->
                                            <!--<f:attribute name="selecetViewCompLookup" value="#{viewComp}"/>`-->
                                            <!--                                            <f:facet name="itemtip">
                                                                                            <h:panelGrid columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Company Information:"/>
                                                                                                </f:facet>
                                            
                                                                                                <h:outputText value="Name:"/>
                                                                                                <h:outputText value="#{viewComp.compName}"/>
                                            
                                                                                                <h:outputText value="Type:"/>
                                                                                                <h:outputText value="#{viewComp.compTypeName}"/>
                                            
                                                                                                <h:outputText value="Sales Team:" rendered="#{viewComp.smanName != null and viewComp.smanName != ''}"/>
                                                                                                <h:outputText value="#{viewComp.smanName}" rendered="#{viewComp.smanName != null and viewComp.smanName != ''}"/>
                                            
                                                                                                <h:outputText value="City:"/>
                                                                                                <h:outputText value="#{viewComp.compCity}"/>
                                            
                                                                                                <h:outputText value="State:"/>
                                                                                                <h:outputText value="#{viewComp.compState}"/>
                                            
                                                                                                <h:outputText value="Zip Code:"/>
                                                                                                <h:outputText value="#{viewComp.compZipCode}" />
                                                                                            </h:panelGrid>
                                                                                        </f:facet>-->
                                            <!--//21-11-2022 : #9744 : Quote Refactoring: Quotes> company type ahead> company info sticks to the page-->
                                            <!--<p:ajax event="keyup" onstart="applyCompanyType(3);delayForkeyup();" oncomplete="applyCompanyType(3);"/>-->
                                            <p:ajax event="itemSelect" onstart="PF('dlgProcessingOnTask').show();" listener="#{quotesHdr.selectDistibute}" oncomplete="PF('dlgProcessingOnTask').hide();"/>
                                            <!--<p:ajax event="change" onstart="applyCompanyType(3);"/>-->
                                            <!--<p:ajax event="blur" oncomplete="PF('oppDistributerWidget').close();"/>-->
                                        </p:autoComplete>
                                        <!--2568 CRM-3365 Teps: Can't change quote customer-->
                                        <p:commandButton id="btnDistSearch" icon="fa fa-search" title="Choose Company" immediate="true"  disabled="#{quotesHdr.editable}"
                                                         actionListener="#{quotesHdr.listLookUpvalues('applyActivePrinci', 3)}"
                                                         update=":formCompLookup" tabindex="3" 
                                                         style="width: 68px;" styleClass="btn-info  btn-xs" />
                                    </h:panelGroup>
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2 ">
                                    <p:outputLabel value="#{custom.labels.get('IDS_DISTRI')} Contact"  for="oppDistributorContact"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 " style="display: inline-flex;">
                                    <h:panelGroup class="ui-inputgroup" style="min-width: 365px;">
                                        <!--<p:inputText id="oppDistributorContact" value="#{quotesHdr.oppDistriContName}"   />-->
                                        <!--//19-07-2022 :  #8487  : Direct Request: Quotes: Make Company/Contact typeahead-->
                                        <!--//09-12-2022 : #10035 : CRM-6132: Quote Refactoring: Changes to company/contact type ahead-->
                                        <p:autoComplete id="oppDistributorContact" panelStyleClass="acCompCont" 
                                                        value="#{quotesHdr.oppDistriContName}" minQueryLength="3" 
                                                        disabled="#{quotesHdr.editable}"
                                                        completeMethod="#{quotesHdr.completeDistributContacts}">
                                            <!--//08-12-2022 : #10023 : WEBINAR: Quote Line Items: line items do not enter after selection-->
                                            <!--                                            <f:facet name="itemtip">
                                                                                            <h:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Contact Information:"/>
                                                                                                </f:facet>
                                            
                                                                                                <h:outputText value="Name:"/>
                                                                                                <h:outputText value="#{viewCompContact.contFullName}"/>
                                            
                                                                                                <h:outputText value="Company:"/>
                                                                                                <h:outputText value="#{viewCompContact.compName}"/>
                                            
                                                                                                <h:outputText value="Job Title:"/>
                                                                                                <h:outputText value="#{viewCompContact.contJobTitle}"/>
                                            
                                                                                                <h:outputText value="Email:"/>
                                                                                                <h:outputText value="#{viewCompContact.contEmailBusiness}"/>
                                            
                                                                                                <h:outputText value="Phone:"/>
                                                                                                <h:outputText value="#{viewCompContact.contPhones}"/>
                                                                                            </h:panelGrid>
                                                                                        </f:facet>-->
                                            <!--//21-11-2022 : #9744 : Quote Refactoring: Quotes> company type ahead> company info sticks to the page-->
                                            <p:ajax event="keyup" onstart="applyCompanyContontact(3);delayForkeyup();" oncomplete="applyCompanyContontact(3);"/>
                                            <p:ajax event="itemSelect" listener="#{quotesHdr.selectedDistributContacts}"/>
                                            <!--<p:ajax event="change" onstart="applyCompanyContontact(3);"/>-->
                                            <!--<p:ajax event="blur" oncomplete="PF('oppDistributerContactWidget').close();"/>-->
                                        </p:autoComplete>
                                        <!--2568 CRM-3365 Teps: Can't change quote customer-->
                                        <!--                                        PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                                        <p:commandButton id="btnDistContSearch" icon="fa fa-search" title="Choose Contact" immediate="true" disabled="#{quotesHdr.editable}"
                                                         actionListener="#{viewContLookupService.list('applyCont3',3,quotesHdr.oppDistri,1)}" 
                                                         action="#{viewContLookupService.disableContNewBtn(quotesHdr.oppDistri)}"
                                                         update=":formContLookup :formNew" oncomplete="PF('lookupCont').show()"  
                                                         style="width: 68px;" styleClass="btn-info btn-xs" />
         <!--                                 <p:commandButton  disabled="#{opportunities.oppCloseStatus!=0}"
                                              icon="fa fa-search" title="Choose Contact" immediate="true" 
                                              actionListener="#{viewContLookupService.list('applyCont3',3,0,1)}" 
                                              update=":frmOpp:tabOpp:tblDC :formContLookup" oncomplete="PF('lookupCont').show()"  
                                              styleClass="btn-info btn-xs" />  -->
                                    </h:panelGroup>
                                    <!--                                </div>
                                                                    <div class="ui-sm-12 ui-md-1 ui-lg-1 ">-->
                                    <h:panelGroup id="pnlclrdistri" style="padding-left: 3px;" >
                                        <!--2988 CRM-3741: Quote Problem- remove asterisk from "Customer*" in a Quote-->
                                        <p:commandLink rendered="#{quotesHdr.oppDistri>0 and quotesHdr.oppsLnList.size()==0}" value="Clear" disabled="#{quotesHdr.editable}" 
                                                       title="Clear #{custom.labels.get('IDS_DISTRI')} selection" class="btnlukup " 
                                                       style="border: none;background: none"  
                                                       actionListener="#{quotesHdr.clearSelection(3)}"  
                                                       process="@this" 
                                                       update=":quotefrom:tabQuot:oppDistributor :quotefrom:tabQuot:oppDistributorContact :quotefrom:tabQuot:pnlclrdistri :quotefrom:tabQuot:oppCustomerLbl" />
                                    </h:panelGroup>
                                </div>



                                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                    <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                    <p:outputLabel  value="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" for="oppSecCustomer"  styleClass=""/>

                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                                    <h:panelGroup class="ui-inputgroup"  >
                                        <!--<h:inputHidden id="hdninputcName" value="#{commTransactionService.transHdr.trnhCustName}"  required="true" requiredMessage="#{custom.labels.get('IDS_CUSTOMER')} is required" />-->
                                        <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                        <!--<p:inputText id="oppSecCustomer" value="#{quotesHdr.oppSecCustomerName}"  requiredMessage="#{custom.labels.get('IDS_SECOND_CUSTOMER')} is required" />-->
                                        <!--//19-07-2022 :  #8487  : Direct Request: Quotes: Make Company/Contact typeahead-->
                                        <!--//09-12-2022 : #10035 : CRM-6132: Quote Refactoring: Changes to company/contact type ahead-->
                                        <p:autoComplete id="oppSecCustomer" value="#{quotesHdr.oppSecCustomerName}" panelStyleClass="acCompCont" 
                                                        completeMethod="#{quotesHdr.completeSecCustomer}"
                                                        disabled="#{quotesHdr.editable}" minQueryLength="3" onkeyup="applyCompanyType(4);">
                                            <!--//08-12-2022 : #10023 : WEBINAR: Quote Line Items: line items do not enter after selection-->
                                            <!--<f:attribute name="selecetViewCompLookup" value="#{viewComp}"/>`-->
                                            <!--                                            <f:facet name="itemtip">
                                                                                            <h:panelGrid columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Company Information:"/>
                                                                                                </f:facet>
                                            
                                                                                                <h:outputText value="Name:"/>
                                                                                                <h:outputText value="#{viewComp.compName}"/>
                                            
                                                                                                <h:outputText value="Type:"/>
                                                                                                <h:outputText value="#{viewComp.compTypeName}"/>
                                            
                                                                                                <h:outputText value="Sales Team:" rendered="#{viewComp.smanName != null and viewComp.smanName != ''}"/>
                                                                                                <h:outputText value="#{viewComp.smanName}" rendered="#{viewComp.smanName != null and viewComp.smanName != ''}"/>
                                            
                                                                                                <h:outputText value="City:"/>
                                                                                                <h:outputText value="#{viewComp.compCity}"/>
                                            
                                                                                                <h:outputText value="State:"/>
                                                                                                <h:outputText value="#{viewComp.compState}"/>
                                            
                                                                                                <h:outputText value="Zip Code:"/>
                                                                                                <h:outputText value="#{viewComp.compZipCode}" />
                                                                                            </h:panelGrid>
                                                                                        </f:facet>-->
                                            <!--//21-11-2022 : #9744 : Quote Refactoring: Quotes> company type ahead> company info sticks to the page-->
                                            <!--<p:ajax event="keyup" onstart="applyCompanyType(4);delayForkeyup();" oncomplete="applyCompanyType(4);"/>-->
                                            <p:ajax event="itemSelect" onstart="PF('dlgProcessingOnTask').show();" listener="#{quotesHdr.selectSecCustomer}" oncomplete="PF('dlgProcessingOnTask').hide();"/>
                                            <!--<p:ajax event="change" onstart="applyCompanyType(4);"/>-->
                                            <!--<p:ajax event="blur" oncomplete="PF('oppSecCustomerWidget').close();"/>-->
                                        </p:autoComplete>
                                        <p:commandButton id="btnSecCustSearch" icon="fa fa-search" title="Choose Company" immediate="true" disabled="#{quotesHdr.editable}" 
                                                         actionListener="#{quotesHdr.listLookUpvalues('applyActivePrinci',6)}"
                                                         update=":formCompLookup" tabindex="4" style="width: 68px;"
                                                         styleClass="btn-info btn-xs" />
                                    </h:panelGroup>
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2 ">
                                    <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                    <p:outputLabel value="#{custom.labels.get('IDS_SECOND_CUSTOMER')} Contact"  for="oppSecCustomerContact"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 " style="display: inline-flex;">
                                    <h:panelGroup class="ui-inputgroup" style="min-width: 365px;">
                                        <!--                                        PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                                        <!--<p:inputText  value="#{quotesHdr.oppSecCustomerContName}" id="oppSecCustomerContact"  />-->
                                        <!--//19-07-2022 :  #8487  : Direct Request: Quotes: Make Company/Contact typeahead-->
                                        <!--//09-12-2022 : #10035 : CRM-6132: Quote Refactoring: Changes to company/contact type ahead-->
                                        <p:autoComplete id="oppSecCustomerContact" panelStyleClass="acCompCont" 
                                                        value="#{quotesHdr.oppSecCustomerContName}" minQueryLength="3"
                                                        disabled="#{quotesHdr.editable}"
                                                        completeMethod="#{quotesHdr.completeSpecifierContacts}">
                                            <!--//08-12-2022 : #10023 : WEBINAR: Quote Line Items: line items do not enter after selection-->
                                            <!--                                            <f:facet name="itemtip">
                                                                                            <h:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Contact Information:"/>
                                                                                                </f:facet>
                                            
                                                                                                <h:outputText value="Name:"/>
                                                                                                <h:outputText value="#{viewCompContact.contFullName}"/>
                                            
                                                                                                <h:outputText value="Company:"/>
                                                                                                <h:outputText value="#{viewCompContact.compName}"/>
                                            
                                                                                                <h:outputText value="Job Title:"/>
                                                                                                <h:outputText value="#{viewCompContact.contJobTitle}"/>
                                            
                                                                                                <h:outputText value="Email:"/>
                                                                                                <h:outputText value="#{viewCompContact.contEmailBusiness}"/>
                                            
                                                                                                <h:outputText value="Phone:"/>
                                                                                                <h:outputText value="#{viewCompContact.contPhones}"/>
                                                                                            </h:panelGrid>
                                                                                        </f:facet>-->
                                            <!--//21-11-2022 : #9744 : Quote Refactoring: Quotes> company type ahead> company info sticks to the page-->
                                            <p:ajax event="keyup" onstart="applyCompanyContontact(4);delayForkeyup();" oncomplete="applyCompanyContontact(4);"/>
                                            <p:ajax event="itemSelect" listener="#{quotesHdr.selectedSpecifierContacts}"/>
                                            <!--<p:ajax event="change" onstart="applyCompanyContontact(4);"/>-->
                                            <!--<p:ajax event="blur" oncomplete="PF('oppSecCustomerContactWidget').close();"/>-->
                                        </p:autoComplete>
                                        <p:commandButton id="btnSecCustContSearch" icon="fa fa-search" title="Choose Contact" immediate="true" disabled="#{quotesHdr.editable}" 
                                                         action="#{viewContLookupService.disableContNewBtn(quotesHdr.oppSecCustomer)}"
                                                         actionListener="#{viewContLookupService.list('applyCont4',2,quotesHdr.oppSecCustomer,1)}" 
                                                         style="width: 68px;"  update=":formContLookup" oncomplete="PF('lookupCont').show()"  
                                                         styleClass="btn-info btn-xs" />
                                    </h:panelGroup>
                                    <!--                                </div>
                                                                    <div class="ui-sm-12 ui-md-1 ui-lg-1 ">-->
                                    <h:panelGroup id="pnlclrseccust"  style="padding-left: 3px;">
                                        <p:commandLink rendered="#{quotesHdr.oppSecCustomer>0}" value="Clear" disabled="#{quotesHdr.editable}" 
                                                       title="Clear #{custom.labels.get('IDS_SECOND_CUSTOMER')} selection" class="btnlukup " 
                                                       style="border: none;background: none"  
                                                       actionListener="#{quotesHdr.clearSelection(4)}"  
                                                       process="@this" 
                                                       update=":quotefrom:tabQuot:oppSecCustomer :quotefrom:tabQuot:oppSecCustomerContact :quotefrom:tabQuot:pnlclrseccust" />
                                    </h:panelGroup>
                                </div>
                                <!--//04-08-2023 : #11805 : CRM-7169: Quotes: Other Parties Features-->
                                <div class="ui-sm-12 ui-md-12 ui-lg-12">
                                    <h:inputHidden value="#{quotesHdr.otherPartyListSize}" id="opOthrPartlisth"/>
                                    <p:dataList value="#{quotesHdr.otherPartyList}" rendered="#{quotesHdr.otherPartyListSize > 0}" style="margin-left:-10px;" 
                                                var="opot" id="tblOthrList" varStatus="status">    
                                        <div class="ui-g ui-fluid">
                                            <div class="ui-sm-12 ui-md-2 ui-lg-2">

                                                <p:outputLabel value="#{opot.compTypeName}" id="lbl#{status.index}"></p:outputLabel>
                                            </div>
                                            <div class="ui-sm-12 ui-md-4 ui-lg-4" >
                                                <p:inputText readonly="true" value="#{opot.compName}" id="txt#{status.index}" style="width:87%;margin-left:4px;"/><p:spacer width="6"/>
                                            </div> 
                                            <div class="ui-sm-12 ui-md-2 ui-lg-2">

                                                <p:outputLabel value="Contact" id="lblCont#{status.index}" style="margin-left:5px"></p:outputLabel>
                                            </div>
                                            <div class="ui-sm-12 ui-md-4 ui-lg-4"  style="display: inline-flex;">
                                                <h:panelGroup class="ui-inputgroup" style="min-width: 365px;margin-left:5px">
                                                    <p:autoComplete panelStyleClass="acCompCont" 
                                                                    readonly="true" value="#{opot.contFullName}" id="txtCont#{status.index}">
                                                    </p:autoComplete>
                                                    <p:commandButton icon="fa fa-search" title="Choose Contact" immediate="true"  
                                                                     action="#{viewContLookupService.disableContNewBtn(opot.compId)}"
                                                                     actionListener="#{viewContLookupService.listForOppOtherParties('applyContacts',0,opot.compId,1,opot.recId)}"
                                                                     update="quotefrom:dlgContAll :formContLookup" oncomplete="PF('lookupCont').show()"  
                                                                     styleClass="btn-info btn-xs" style="width: 68px;"
                                                                     />  
                                                </h:panelGroup>
                                                <h:panelGroup id="pnlclrseccust"  style="padding-left: 3px;">
                                                    <p:commandLink  actionListener="#{quotesHdr.removeOtherCompany}" update="quotefrom:tabQuot:tblOthrList" type="button" style="font-size: larger; color: red;" title="Remove Other parties">
                                                        <f:param  name="indexPtr" value="#{status.index}" />
                                                        <f:param  name="compTypeName" value="#{opot.compTypeName}" />
                                                        <f:param  name="compId" value="#{opot.compId}" />
                                                        <f:param  name="contId" value="#{opot.contId}" />
                                                        <f:param  name="contFullName" value="#{opot.contFullName}" />
                                                        <f:param  name="recId" value="#{opot.recId}" />
                                                        <h:outputText value="X"/>
                                                    </p:commandLink>
                                                </h:panelGroup>
            <!--                                                <h:panelGroup id="oppOthr#{status.index}"  class="ui-inputgroup" style="min-width: 365px;" >  
            
                                                                <p:inputText readonly="true" value="#{opot.contFullName}" id="txtCont#{status.index}" style="margin-left:5px;width:73%;margin-right: -7px"/><p:spacer width="6"/>
            
                                                                <p:commandButton icon="fa fa-search" title="Choose Contact" immediate="true"  
                                                                                 action="#{viewContLookupService.disableContNewBtn(opot.compId)}"
                                                                                 actionListener="#{viewContLookupService.listForOppOtherParties('applyContacts',0,opot.compId,1,opot.recId)}"
                                                                                 update="quotefrom:dlgContAll :formContLookup" oncomplete="PF('lookupCont').show()"  
                                                                                 styleClass="btn-info btn-xs" style="width: 60px;"
                                                                                 />  
                                                                <p:commandLink  actionListener="#{quotesHdr.removeOtherCompany}" update="quotefrom:tabQuot:tblOthrList" type="button" style="font-size: larger; color: red;" title="Remove Other parties">
                                                                    <f:param  name="indexPtr" value="#{status.index}" />
                                                                    <f:param  name="compTypeName" value="#{opot.compTypeName}" />
                                                                    <f:param  name="compId" value="#{opot.compId}" />
                                                                    <f:param  name="contId" value="#{opot.contId}" />
                                                                    <f:param  name="contFullName" value="#{opot.contFullName}" />
                                                                    <f:param  name="recId" value="#{opot.recId}" />
                                                                    <h:outputText value="X"/>
                                                                </p:commandLink>
                                                            </h:panelGroup> -->
                                            </div> 
                                        </div>
                                    </p:dataList>
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2" style="margin-top: -15px; ">
                                    <p:commandLink  style="font-size: x-large; color: #4ca75a;" title="Add other parties" immediate="true"                              
                                                    actionListener="#{viewCompLookupService.listAll('applyOtherComp')}"
                                                    disabled="#{quotesHdr.recId==null or quotesHdr.recId == 0}"
                                                    update=":formCompLookup" oncomplete="PF('lookupComp').show()">
                                        <f:param  name="indexPtr" value="#{status.index}" />
                                        <f:param  name="compTypeName" value="#{opot.compTypeName}" />
                                        <f:param  name="compName" value="#{opot.compName}" />
                                        <f:param  name="recId" value="#{opot.recId}" />
                                        <h:outputText value="+"/>
                                    </p:commandLink>
                                </div>
                            </div>

                            <div class="ui-g ui-fluid header-bar">
                                <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                                    <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Transaction Details</p:outputLabel>
                                </div>
                                <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                                </div>
                            </div>  
                            <div class="ui-g ui-fluid">
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">    
                                    <p:outputLabel value="#{custom.labels.get('IDS_PROGRAM')}"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">     
                                    <p:inputText id="qTopic" value="#{quotesHdr.oppCustProgram}" style="width: 100%"  maxlength="120" readonly="#{quotesHdr.editable}" />
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">    

                                    <p:outputLabel rendered="#{quotesHdr.disableCrmSyncBtn==false}" value="External Quote ID"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">  
                                    <p:inputText  id="quotSync" rendered="#{quotesHdr.disableCrmSyncBtn==false}" value="#{quotesHdr.crmTSQhdrId}" style="width: 100%"  maxlength="120" readonly="true" />

<!--                                    <h:panelGroup class="ui-inputgroup"  rendered="#{quotesHdr.disableCrmSyncBtn==false}"  >
          <p:inputText value="#{quotesHdr.crmTSQhdrId}" id="quotSync"  readonly="#{quotesHdr.editable}" placeholder="[Target Quote ID]"/>
          <p:commandButton  actionListener="#{oAuthLogin.populateISQuotes(quotesHdr.syncUser)}" icon="fa fa-search" class="btn-primary btn-xs"  style="width: 67px;" immediate="true" update=":quotefrom:esr"  oncomplete="PF('isQuotSyncDtls').show()"  >
<p:ajax listener="#{oAuthLogin.populateDlg(quotesHdr.syncUser,opportunities.oppId)}"/>
          </p:commandButton> 
          <p:spacer width="4"/>
          <p:commandButton  actionListener="#" value="Sync" class="btn-primary btn-xs"  style="width: 67px;"  >
          </p:commandButton> 
      </h:panelGroup>-->
<!--                                    <h:panelGroup class="ui-inputgroup" rendered="#{quotesHdr.disableCrmSyncBtn==false}" >
                                        <p:inputText value="#{quotesHdr.crmTSQhdrId}" id="quotSync"  readonly="#{quotesHdr.editable}" placeholder="[Target Quote ID]"/>

                                        <p:commandButton  actionListener="#{iSQuoteCrmClient.getISQuoteQuotesDetailsList(quotesHdr.recIdForISquote)}" icon="fa fa-search"
                                                          class="btn-primary btn-xs"  style="width: 67px;" immediate="true" update=":quotefrom:esr" 
                                                          oncomplete="PF('isQuotSyncDtls').show()"  >
                                        </p:commandButton> 
                                        <p:spacer width="4"/>
                                        <p:commandButton  actionListener="#" value="Sync" class="btn-primary btn-xs"  style="width: 67px;"  >
                                        </p:commandButton> 
                                    </h:panelGroup>-->



                                </div>

                                <div class="ui-sm-12 ui-md-2 ui-lg-2">   
                                    <p:outputLabel value="Quote Number"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">   
                                    <!--10740 : 21/02/2023: CRM-6199: Priority : Quote Number Customization for Quote Creation-->
                                    <!--//11079 03/05/2023: ESCALATIONS   CRM-6884   Quotes: Updating customer changes the quote number (Refresh)-->
                                    <h:panelGroup class="ui-inputgroup"  >
                                        <p:inputText value="#{quotesHdr.quotNumber}" id="quotNumb"  readonly="#{quotesHdr.editable}" maxlength="30"/>
                                        <p:commandButton class="btn-success btn-xs" icon="fa fa-refresh" action="#{quotesHdr.refreshQuoteNo()}" disabled="#{quotesHdr.editable}"
                                                         style="width: 67px;"    update="quotefrom:tabQuot:quotNumb" />
                                    </h:panelGroup>
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">     
                                    <p:outputLabel value="Quote Date"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">     
                                    <p:calendar value="#{quotesHdr.quotDate}"   pattern="#{globalParams.dateFormat}"  readonly="#{quotesHdr.editable}" >
                                        <!-- Sharvani - 16/05/2019 - CRM-526: Auto-populate quote expiration date on quote creation -->
        <!--                                <p:ajax event="dateSelect" listener="#{quotesHdr.expdateUpdate()}" update=" " /> -->
                                        <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                                    </p:calendar>
                                </div>

                                <div class="ui-sm-12 ui-md-2 ui-lg-2">    
                                    <p:outputLabel value="Recipient"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 "> 
                                    <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                                    <p:selectOneMenu id="quotReceipient" value="#{quotesHdr.quotReceipient}" style="width: 100%"  disabled="#{quotesHdr.editable}">
                                        <f:selectItem itemLabel="#{custom.labels.get('IDS_CUSTOMER')}" itemValue="Customer"/>
                                        <f:selectItem itemLabel="#{custom.labels.get('IDS_DISTRI')}" itemValue="Distributor"/>
                                        <f:selectItem itemLabel="#{custom.labels.get('IDS_CEM')}" itemValue="CEM"/>   

                                    </p:selectOneMenu>
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                                    <p:outputLabel value="#{custom.labels.get('IDS_RFQ_NUM')}"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">    
                                    <p:inputText value="#{quotesHdr.quotRfqNum}"   maxlength="30" readonly="#{quotesHdr.editable}" />
                                </div>

                                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                                    <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')} Ref.No"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">   
                                    <p:inputText value="#{quotesHdr.quotCustRefNum}"  maxlength="30" readonly="#{quotesHdr.editable}"/>
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                                    <p:outputLabel value="Quote Value"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">  
                                    <!-- 12911: 16/01/2024: CRM-7663   Quotes: Should be able to edit quote value without line item-->
                                    <p:inputText value="#{quotesHdr.oppValue}"  maxlength="30" readonly="#{quotesHdr.editable || quotesHdr.listQuotDtls.size()>0}" id="opVl"/>
                                    <!--Related to CRM-1040: FW: RepFabric 3.0 Issues-->
                                    <!--26/05/2022:  8057 : CRM-5090: can we add a comma to the "value" field in Quotes-->
<!--                                    <p:outputLabel value="#{quotesHdr.oppValue}"  id="opVl">

                                        <f:convertNumber groupingUsed="true"  maxIntegerDigits="15"  
                                                          minFractionDigits="2" maxFractionDigits="2"/>
                                    </p:outputLabel>-->
                                </div>

                                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                                    <p:outputLabel value="Status"  />
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">  
                                    <!--                            <h:panelGrid class="ui-inputgroup" columns="" >
                                                                    <p:selectOneRadio id="quotOpenStatus" value="#{quotesHdr.quotOpenStatus}"  disabled="#{quotesHdr.editable}">
                                                                        <f:selectItem itemLabel="Open" itemValue="0" />
                                                                        <f:selectItem itemLabel="Close" itemValue="1" />
                                                                    </p:selectOneRadio>
                                                                </h:panelGrid>-->
                                    <p:selectOneMenu id="quotOpenStatus" value="#{quotesHdr.quotOpenStatus}"   disabled="#{quotesHdr.editable}">
                                        <f:selectItem itemLabel="Open" itemValue="0" />
                                        <!--#3542 CRM-2135: #1 : Quotes > Filter by Status-->
                                        <f:selectItem itemLabel="Closed" itemValue="1" />
                                    </p:selectOneMenu>
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">  
                                    <p:outputLabel value="Quote Status"/>
                                </div>
                                <!--12209: 17/10/2023: Incidental Finding :CRM:7170 : Quote Status not displayed in Quote list page-->
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 " >   
        <!--                            <p:selectOneMenu value="#{quotesHdr.quotDelivStatus}" style="width: 100%" disabled="#{quotesHdr.editable}">
                                        <f:selectItem itemLabel="Draft" itemValue="0" />
                                        <f:selectItem itemLabel="Delivered" itemValue="1" />  
                                        <f:selectItem itemLabel="In Review" itemValue="2" />  
                                        <f:selectItem itemLabel="Booked" itemValue="3" />   
                                        <f:selectItem itemLabel="On Going Business" itemValue="4" /> 
                                    </p:selectOneMenu>-->
                                    <!--11755: 01/08/2023: CRM-7170: Quotes: Quote Details > Quote Status, Canned Notes-->
                                    <p:selectOneMenu value="#{quotesHdr.quotDelivStatus}" style="width: 100%" disabled="#{quotesHdr.editable}" id="delStatus">
                                        <f:selectItem itemLabel="Select One" itemValue="" />
                                        <f:selectItems value="#{quotesHdr.quoteStatusList}" var="sts" itemLabel="#{sts.optOption}" itemValue="#{sts.recId}"/>
                                        <!--<p:ajax event="change" process="@this" immediate="true" update="" listener="#{quotesHdr.updateRFStatus()}"/>-->
                                    </p:selectOneMenu>
                                </div>

                                <div class="ui-sm-12 ui-md-2 ui-lg-2">          
                                    <p:outputLabel value="Application"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">    
                                    <p:inputText value="#{quotesHdr.quotApplication}"   readonly="#{quotesHdr.editable}"/>
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">   
                                    <p:outputLabel value="Expiration Date"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">     
                                    <!--Sharvani -16/05/2019 CRM-526: Auto-populate quote epiration date on quote creation-->
                                    <p:calendar id="expdate" value="#{quotesHdr.quotExpiry}"   pattern="#{globalParams.dateFormat}"  readonly="#{quotesHdr.editable}" >
                                        <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                                    </p:calendar>
                                </div>

                                <div class="ui-sm-12 ui-md-2 ui-lg-2">       
                                    <p:outputLabel value="Bid/Buy"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 "> 
                                    <p:selectOneMenu value="#{quotesHdr.quotBidBuy}" style="width: 100%" disabled="#{quotesHdr.editable}">
                                        <f:selectItem itemLabel="Bid" itemValue="Bid"/>
                                        <f:selectItem itemLabel="Buy" itemValue="Buy"/>            
                                    </p:selectOneMenu>
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">    
                                    <p:outputLabel value="Follow Up"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">  
                                    <p:calendar value="#{quotesHdr.quotFollowUp}"   pattern="#{globalParams.dateFormat}"  readonly="#{quotesHdr.editable}"  >
                                        <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                                    </p:calendar>
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                    <p:outputLabel value="Owner"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4">  
                                    <!--4050 Quote Owner drop down issue-->
                                    <p:selectOneMenu value="#{quotesHdr.quotOwner}" id="ddOppOwner" style="width: 100%" disabled="#{quotesHdr.editable}">
                                        <f:selectItems value="#{users.dependentUserListForQuotes}" var="user" 
                                                       itemValue="#{user.userId}" itemLabel="#{user.userName}" />
                                    </p:selectOneMenu>
                                </div>
                                <!--Task#3843-CRM-3841: Quote Watchers  16-03-2021  by harshithad-->
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                    <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                                    <p:outputLabel  value="#{custom.labels.get('IDS_WATCHERS')}" />
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                                    <!--#8538  CRM-5935  ranvier: tool tip for watcher in every form (opp, job, quote, etc)-->
                                    <h:inputHidden   value="#{quotesHdr.listSize}" id="inpTxtQuoteWatcher" />
                                    <p:tooltip value="Type in the @ symbol for a selection list"  rendered="#{!quotesHdr.editable}" showEvent="mouseover"   hideEvent="mouseout" for="quotefrom:tabQuot:panelWatchers" />
                                    <h:panelGroup  id="panelWatchers" style="margin-bottom: 10px">
                                        <p:autoComplete   id="quoteWatchers" widgetVar="qtWatchers" maxlength="60" minQueryLength="1"  
                                                          maxResults="10" styleClass="actCont" 
                                                          style="width: 100%;height:50px;"  value="#{quotesHdr.setUsersList}"
                                                          completeMethod="#{quotesHdr.completeUsersName}" var="u"
                                                          itemLabel="#{u}" itemValue="#{u}" multiple="true"  forceSelection="true" 
                                                          disabled="#{quotesHdr.editable}">
                                            <!--//25-03-2022 : #7435 : CRM-5281: Watcher groups: Quote Watchers-->
                                            <p:ajax event="itemSelect"  listener="#{quotesHdr.getListValues}" update=":quotefrom:tabQuot:quoteWatchers"  />
                                            <p:ajax event="itemUnselect"  listener="#{quotesHdr.removingWatchers}" update=":quotefrom:tabQuot:quoteWatchers"/>   
                                        </p:autoComplete>
                                    </h:panelGroup>
                                </div>
                                <!--// 25-02-2022 : #7343 :Notify Watchers check box option in Quotes module-->
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                    <p:selectBooleanCheckbox  value="#{quotesHdr.quotEmailFlagBool}" />
                                    <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                                    <p:outputLabel  value="Notify #{custom.labels.get('IDS_WATCHERS')}" />
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                                </div>

                                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                    <!--//21-08-2024 : #14330 : CRM-8376 : Quote form updates for internal comments-->
                                    <p:outputLabel value="Quote form comments"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">   
                                    <h:panelGroup class="ui-inputgroup"  >
                                        <p:inputTextarea id="itquotComments" rows="2"  value="#{quotesHdr.quotComments}"  style=" text-overflow: scroll" autoResize="false" readonly="#{quotesHdr.editable}"   />
                                        <!--  Sharvani - 07-05-2019: CRM-464 :copy principal comments -->
                                        <!--//24-04-2023 : #11135 : CRM-6737 : Quotes:Add line item& clone>Manf part number not copied to clonned line item-->
                                        <!--11755: 01/08/2023: CRM-7170: Quotes: Quote Details > Quote Status, Canned Notes-->
                                        <p:commandButton style="width:23px;" icon="fa fa-copy" title="Copy #{custom.labels.get('IDS_PRINCI')} comments" actionListener="#{quotesHdr.getCompanyComments(quotesHdr.oppPrincipal)}" update="quotefrom:tabQuot:itquotComments" disabled="#{quotesHdr.editable or quotesHdr.oppPrincipal==0 or quotesHdr.oppPrincipal==-1}"  class="btn-primary btn-xs" id="btnPrincComment"/>
                                        <p:commandButton style="width:23px;" icon="fa fa-search" title="Select Canned comments" class="btn-primary btn-xs" id="btnCannedComment" onclick="PF('lookupCannedNotes').show()" disabled="#{quotesHdr.editable}"/>
                                    </h:panelGroup>
                                </div>
                                <!--#9086 CRM-6151  product master - update weight label to show "(lbs)"-->
                                <!--//19-08-2022 : #8767 : CRM-6002   Lestersales/Kim Heck/- Quotes Feature - when -Adding Weight-->
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                    <p:outputLabel value="Weight (lbs)"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4">  
                                    <p:inputText id="quotWeight" value="#{quotesHdr.totalWeight}" readonly="true"/>
                                </div>
                                <!--//21-08-2024 : #14330 : CRM-8376 : Quote form updates for internal comments-->
                                <!--//18-09-2024 : #14330 : CRM-8376 : Quote form updates for internal comments-->
                                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                                    <!--//21-08-2024 : #14330 : CRM-8376 : Quote form updates for internal comments-->
                                    <p:outputLabel value="Internal Comments"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">  
                                    <h:panelGroup class="ui-inputgroup"  >
                                        <p:inputTextarea id="inputInternalCommentsText" rows="2"  value="#{quotesHdr.quotNotes}" style=" text-overflow: scroll" autoResize="false" readonly="#{quotesHdr.editable}" />
                                        <!--19-09-2024 : #14537 : CRM-8376: Quotes: Internal Comments: Add Clear and Timestamp options-->
                                        <p:commandButton style="width:23px;" icon="fa fa-close" title="Clear" 
                                                         actionListener="#{quotesHdr.clearInternalNotes()}" 
                                                         update="quotefrom:tabQuot:inputInternalCommentsText" 
                                                         disabled="#{quotesHdr.editable}" 
                                                         class="btn-danger btn-xs" id="btnInternalCommentClear"/>
                                        <p:commandButton style="width:23px;" icon="fa fa-clock-o" title=" Add current date and time" 
                                                         class="btn-primary btn-xs" id="btnInternalCommentClock" 
                                                         disabled="#{quotesHdr.editable}" onclick="addDateInternalComment()"/>
                                    </h:panelGroup>    
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">  
                                    <!--08-01-2024 12761 CRM-7599   OASIS: Surface Project Rank for Quotes-->
                                    <p:outputLabel value="Rank"/>
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">       
                                    <!--08-01-2024 12761 CRM-7599   OASIS: Surface Project Rank for Quotes-->
                                    <p:inputText id="quotRank" value="#{quotesHdr.quotJobRank}" readonly="true"/>
                                </div>

                            </div>












                            <p:row>
                                <p:column>
                                    <!--<p:outputLabel value="#{custom.labels.get('IDS_PRINCI')}:"/>-->
                                </p:column>
                                <p:column>
        <!--                            <p:inputText value="#{quotesHdr.oppPrincipalName}"  readonly="true"
                                                 id="oppPrincipal" styleClass="updateCustDlg" style="width: 84%" placeholder="[Not selected]" />
                                    <p:commandButton  class="btnlukup" disabled="#{quotesHdr.disableOpp || quotesHdr.editable}"
                                                      icon="ui-icon-search" 
                                                      actionListener="#{viewCompanyLookUp.populateCompanyLists(1,'QUOTE')}" 
                                                      update=" "
                                                      oncomplete="PF('dlgPrinc').show()"/>-->
                                </p:column>

                                <p:column>
                                    <!--<p:outputLabel value="#{custom.labels.get('IDS_PRINCI')} Contact:"/>-->
                                </p:column>
                                <p:column>
        <!--                            <p:inputText value="#{quotesHdr.oppPrincipalContName}" style="width: 84%" 
                                                 id="oppPrincipalContact" styleClass="updateCustDlg" readonly="true" placeholder="[Not selected]" />
                                                        //Seema-10/04/2019 - removed disableopp condition used for disabled attribute -> disabled="#{quotesHdr.disableOpp || quotesHdr.editable}"  changed to disabled="#{quotesHdr.editable}"
                                                        //CRM 168:Conti younger :cant edit quote
                                    <p:commandButton class="btnlukup" update=" " disabled="#{quotesHdr.editable}"
                                                     process="@this"
                                                     icon="ui-icon-search"  
                                                     actionListener="#{viewContactList.showContactLookup(1, quotesHdr.oppPrincipalName, 'QUOTE')}" 
                                                     oncomplete="PF('dlgContPrin').show()"/>-->
                                </p:column>
                            </p:row>

                            <p:row>
                                <p:column>
                                    <!--<p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}:"/>-->
                                </p:column>
                                <p:column>
        <!--                            <p:inputText value="#{quotesHdr.oppCustomerName}"  readonly="true"
                                                 id="oppCustomer" styleClass="updateCustDlg"  style="width: 84%" placeholder="[Not selected]"/>
                                    <p:commandButton  class="btnlukup" disabled="#{quotesHdr.disableOpp || quotesHdr.editable}" 
                                                      icon="ui-icon-search" 
                                                      actionListener="#{viewCompanyLookUp.populateCompanyLists(2,'QUOTE',2)}" 
                                                      update=" "
                                                      oncomplete="PF('dlgCust').show()"/>-->
                                </p:column>

                                <p:column>
                                    <!--<p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')} Contact:"/>-->
                                </p:column>
                                <p:column>
        <!--                            <p:inputText value="#{quotesHdr.oppCustomerContName}"  readonly="true"
                                                 id="oppCustomerContact" styleClass="updateCustDlg" style="width: 84%" placeholder="[Not selected]"/>
                                                        //Seema-10/04/2019 - removed disableopp condition used for disabled attribute -> disabled="#{quotesHdr.disableOpp || quotesHdr.editable}"  changed to disabled="#{quotesHdr.editable}"
                                                        //CRM 168:Conti younger :cant edit quote
                                    <p:commandButton class="btnlukup" update=" " disabled="#{quotesHdr.editable}"
                                                     process="@this"
                                                     icon="ui-icon-search"  
                                                     actionListener="#{viewContactList.showContactLookup(2, quotesHdr.oppCustomerName, 'QUOTE',2)}" 
                                                     oncomplete="PF('dlgContCust').show()"/>-->
                                </p:column>
                            </p:row>

                            <p:row>
                                <p:column>
                                    <!--<p:outputLabel value="#{custom.labels.get('IDS_DISTRI')}:"/>-->
                                </p:column>
                                <p:column>
        <!--                            <p:inputText value="#{quotesHdr.oppDistriName}"  readonly="true"
                                                 id="oppDistributor" styleClass="updateCustDlg" style="width: 84%" placeholder="[Not selected]"/>
                                    <p:commandButton  class="btnlukup" disabled="#{quotesHdr.disableOpp || quotesHdr.editable}"
                                                      icon="ui-icon-search" 
                                                      actionListener="#{viewCompanyLookUp.populateCompanyLists(3, 'QUOTE')}" 
                                                      update=" "
                                                      oncomplete="PF('dlgDist').show()"/>-->
                                </p:column>

                                <p:column>
                                    <!--<p:outputLabel value="#{custom.labels.get('IDS_DISTRI')} Contact:"/>-->

                                </p:column>
                                <p:column>
        <!--                            <p:inputText value="#{quotesHdr.oppDistriContName}"  readonly="true"
                                                 id="oppDistributorContact" styleClass="updateCustDlg" style="width: 84%" placeholder="[Not selected]"/>
                                                         //Seema-10/04/2019 - removed disableopp condition used for disabled attribute -> disabled="#{quotesHdr.disableOpp || quotesHdr.editable}"  changed to disabled="#{quotesHdr.editable}"
                                                        //CRM 168:Conti younger :cant edit quote
                                    <p:commandButton class="btnlukup" update=" " disabled="#{quotesHdr.editable}"
                                                     process="@this"
                                                     icon="ui-icon-search"  
                                                     actionListener="#{viewContactList.showContactLookup(3, quotesHdr.oppDistriName, 'QUOTE')}" 
                                                     oncomplete="PF('dlgContDist').show()"/>-->
                                </p:column>
                            </p:row>

                            <p:row>
                                <p:column>
                                    <!--<p:outputLabel value="Secondary  #{custom.labels.get('IDS_CUSTOMER')}:"/>-->
                                </p:column>
                                <p:column>
        <!--                            <p:inputText value="#{quotesHdr.oppSecCustomerName}"  readonly="true"
                                                 id="oppSecCustomer" styleClass="updateCustDlg"  style="width: 84%" placeholder="[Not selected]"/>
                                    <p:commandButton  class="btnlukup" disabled="#{quotesHdr.disableOpp || quotesHdr.editable}" 
                                                      icon="ui-icon-search" 
                                                      actionListener="#{viewCompanyLookUp.populateCompanyLists(2,'QUOTE',4)}" 
                                                      update=" "
                                                      oncomplete="PF('dlgCust').show()"/>-->
                                </p:column>

                                <p:column>
                                    <!--<p:outputLabel value="Secondary #{custom.labels.get('IDS_CUSTOMER')} Contact:"/>-->
                                </p:column>
                                <!--                        <p:column>
                                                            <p:inputText value="#{quotesHdr.oppSecCustomerContName}"  readonly="true"
                                                                         id="oppSecCustomerContact" styleClass="updateCustDlg" style="width: 84%" placeholder="[Not selected]"/>
                                                                                 //Seema-10/04/2019 - removed disableopp condition used for disabled attribute -> disabled="#{quotesHdr.disableOpp || quotesHdr.editable}"  changed to disabled="#{quotesHdr.editable}"
                                                                                //CRM 168:Conti younger :cant edit quote
                                                            <p:commandButton class="btnlukup" update=" " disabled="#{quotesHdr.editable}"
                                                                             process="@this"
                                                                             icon="ui-icon-search"  
                                                                             actionListener="#{viewContactList.showContactLookup(2, quotesHdr.oppSecCustomerName, 'QUOTE', 4)}" 
                                                                             oncomplete="PF('dlgContCust').show()"/>
                                                        </p:column>-->
                            </p:row>

                            <!--                    <p:row>
                                                    <p:column>
                                                        <p:outputLabel value="#{custom.labels.get('IDS_PROGRAM')}:"/>
                                                    </p:column>
                                                    <p:column colspan="3">
                                                        <p:inputText value="#{quotesHdr.oppCustProgram}" style="width: 94%"  maxlength="120" disabled="#{quotesHdr.disableOpp || quotesHdr.editable}" />
                                                    </p:column>
                                                </p:row>
                            
                                                   _______________ end opportunities______________________  
                                                <p:row>
                                                    <p:column colspan="4">
                                                    </p:column>
                                                </p:row>
                            
                                                <p:row>
                                                    <p:column>
                                                        <p:outputLabel value="Quote Number:"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:inputText value="#{quotesHdr.quotNumber}" id="quotNumb"  disabled="#{quotesHdr.editable}"/>
                                                        <p:commandButton class="btnlukup" icon="ui-icon-refresh" action="#{quotesHdr.generateQuoteNumber()}" disabled="#{quotesHdr.editable}"
                                                                         update=" " />
                                                    </p:column>
                            
                                                    <p:column>
                                                        <p:outputLabel value="Quote Date:"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:calendar value="#{quotesHdr.quotDate}"   pattern="#{globalParams.dateFormat}"  disabled="#{quotesHdr.editable}" >
                                                             Sharvani - 16/05/2019 - CRM-526: Auto-populate quote expiration date on quote creation 
                                                            <p:ajax event="dateSelect" listener="#{quotesHdr.expdateUpdate()}" update=" " /> 
                                                            <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                                                        </p:calendar>
                                                    </p:column>
                            
                                                </p:row>
                            
                                                <p:row>
                                                    <p:column>
                                                        <p:outputLabel value="Recipient:"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:selectOneMenu value="#{quotesHdr.quotReceipient}" style="width: 178px"  disabled="#{quotesHdr.editable}">
                                                            <f:selectItem itemLabel="#{custom.labels.get('IDS_CUSTOMER')}" itemValue="#{custom.labels.get('IDS_CUSTOMER')}"/>
                                                            <f:selectItem itemLabel="#{custom.labels.get('IDS_DISTRI')}" itemValue="#{custom.labels.get('IDS_DISTRI')}"/>
                                                            <f:selectItem itemLabel="#{custom.labels.get('IDS_CEM')}" itemValue="#{custom.labels.get('IDS_CEM')}"/>                
                                                        </p:selectOneMenu>
                                                    </p:column>
                            
                                                    <p:column>
                                                        <p:outputLabel value="#{custom.labels.get('IDS_RFQ_NUM')}:"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:inputText value="#{quotesHdr.quotRfqNum}"   maxlength="30" disabled="#{quotesHdr.editable}" />
                                                    </p:column>
                                                </p:row>
                            
                                                <p:row>
                                                    <p:column>
                                                        <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')} Ref.No:"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:inputText value="#{quotesHdr.quotCustRefNum}"  maxlength="30" disabled="#{quotesHdr.editable}"/>
                                                    </p:column>
                            
                                                    <p:column>
                                                        <p:outputLabel value="Quote Value:"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:inputText value="#{quotesHdr.oppValue}" maxlength="12" id="opVl" disabled="true" >
                                                            <f:convertNumber pattern="#0.00" />
                                                        </p:inputText>
                                                    </p:column>
                                                </p:row>
                            
                                                <p:row>
                                                    <p:column>
                                                        <p:outputLabel value="Status:"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:selectOneRadio value="#{quotesHdr.quotOpenStatus}" style="width: 178px" disabled="#{quotesHdr.editable}">
                                                            <f:selectItem itemLabel="Open" itemValue="0" />
                                                            <f:selectItem itemLabel="Close" itemValue="1" />
                                                        </p:selectOneRadio>
                                                    </p:column>
                            
                                                    <p:column>
                                                        <p:outputLabel value="Quote Status:"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:selectOneMenu value="#{quotesHdr.quotDelivStatus}" style="width: 178px" disabled="#{quotesHdr.editable}">
                                                            <f:selectItem itemLabel="Draft" itemValue="0" />
                                                            <f:selectItem itemLabel="Delivered" itemValue="1" />  
                                                            <f:selectItem itemLabel="In Review" itemValue="2" />  
                                                            <f:selectItem itemLabel="Booked" itemValue="3" />   
                                                            <f:selectItem itemLabel="On Going Business" itemValue="4" /> 
                                                        </p:selectOneMenu>
                                                    </p:column>
                                                </p:row>
                            
                                                <p:row>
                                                    <p:column>
                                                        <p:outputLabel value="Application:"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:inputText value="#{quotesHdr.quotApplication}"   disabled="#{quotesHdr.editable}"/>
                                                    </p:column>
                            
                                                    <p:column>
                                                        <p:outputLabel value="Expiry Date:"/>
                                                    </p:column>
                                                    <p:column>
                                                        Sharvani -16/05/2019 CRM-526: Auto-populate quote epiration date on quote creation
                                                        <p:calendar id="expdate" value="#{quotesHdr.quotExpiry}"   pattern="#{globalParams.dateFormat}"  disabled="#{quotesHdr.editable}" >
                                                            <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                                                        </p:calendar>
                                                    </p:column>
                                                </p:row>
                            
                                                <p:row>
                                                    <p:column>
                                                        <p:outputLabel value="Bid/Buy:"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:selectOneMenu value="#{quotesHdr.quotBidBuy}" style="width: 178px" disabled="#{quotesHdr.editable}">
                                                            <f:selectItem itemLabel="Bid" itemValue="Bid"/>
                                                            <f:selectItem itemLabel="Buy" itemValue="Buy"/>            
                                                        </p:selectOneMenu>
                                                    </p:column>
                            
                                                    <p:column>
                                                        <p:outputLabel value="Follow Up:"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:calendar value="#{quotesHdr.quotFollowUp}"   pattern="#{globalParams.dateFormat}"  disabled="#{quotesHdr.editable}"  >
                                                            <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                                                        </p:calendar>
                                                    </p:column>
                                                </p:row>
                            
                                                <p:row>
                                                    <p:column>
                                                        <p:outputLabel value="Owner:"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:inputText value="#{quotesHdr.quotOwner}"  maxlength="40" disabled="#{quotesHdr.editable}" />
                                                    </p:column>
                            
                                                </p:row>
                                                <p:row>
                                                    <p:column> 
                                                        <p:outputLabel value="Comments:"/>
                                                    </p:column>
                            
                                                    <p:column colspan="4"> 
                                                        <p:inputTextarea id="itquotComments" rows="2"  value="#{quotesHdr.quotComments}"  style="width: 94%;text-overflow: scroll" autoResize="false" disabled="#{quotesHdr.editable}"   />
                                                          Sharvani - 07-05-2019: CRM-464 :copy principal comments 
                                                        <p:commandButton style="width:23px;top: -20px;left:5px;"  class="btnlukup" icon="ui-icon-clipboard" title="Copy #{custom.labels.get('IDS_PRINCI')} comments" actionListener="#{quotesHdr.getCompanyComments(quotesHdr.oppPrincipal)}" update=" " disabled="#{quotesHdr.editable or quotesHdr.oppPrincipal==0}"  styleClass="updateCustDlg" id="btnPrincComment"/>
                                                    </p:column>
                                                </p:row>
                            
                                                <p:row>
                                                    <p:column>
                                                        <p:outputLabel value="Internal Notes:"/>
                                                    </p:column>
                                                    <p:column colspan="3"> 
                                                        <p:inputTextarea rows="2"  value="#{quotesHdr.quotNotes}" style="width: 94%;text-overflow: scroll" autoResize="false" disabled="#{quotesHdr.editable}" />
                                                    </p:column>
                                                </p:row>-->

                        </p:panelGrid>



                    </p:tab>
                    <p:tab title="Line Items" disabled="#{quotesHdr.recId==0}" id="quotLineItems">
                        <!--//06-02-2023 : #10619 : CRM-6498 : Logic change for adding new line item to quotes-->
                        <p:remoteCommand autoRun="true"  actionListener="#{quotesDtl.lineItemHeaderPrincipal(quotesHdr.oppPrincipal)}" update="quotefrom:tabQuot:capitalize"/>
                        <!--//   #14850  CRM-8639 : part number authoring: optionally autocapitalize alphanumerics, especially quote-->
                        <h:inputHidden  id="capitalize" value="#{quotesDtl.autoCapsPartNo}"/>
                        <!--                         Task#3918-CRM-4017: quote2opp conversion - cherry pick which lines to generate an opp for from a quote  22-03-2021 by harshithad-->
                        <div class="ui-g ui-fluid header-bar">
                            <!--#12168 CRM-7323 Quotes: refresh pricing on quotes-->
                            <div class="ui-sm-5 ui-md-3 ui-lg-3">
                                <p:outputLabel styleClass="acct-name-hdr" class="sub_title"> Line Item Details </p:outputLabel>
                            </div>
                            <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                            <!--<div class="ui-sm-12 ui-md-2 ui-lg-2 "/>-->

                            <!--<div class="ui-sm-12 ui-md-1 ui-lg-1 ">-->
                            <!--//28-09-2022 : #9132 : Quote: Line Item , Growl message displayed though part # is not blank-->
                            <!--                            Task#3918-CRM-4017: quote2opp conversion - cherry pick which lines to generate an opp for from a quote  22-03-2021 by harshithad-->
                            <!--#7606 ys-204 opportunity icon-->
<!--                                <p:outputLabel value="listSize==#{quotesHdr.listQuotDtls.size()==0}"/>
                            <p:outputLabel value="!quotesHdr.editable==#{!quotesHdr.editable}"/>-->
                            <!--//17-04-2023 :  #11073 : CRM-6737: Existing opportunity linked to new Quote on Cloning Quote--> 

                            <!--</div>-->
                            <!--#12168 CRM-7323 Quotes: refresh pricing on quotes-->

                            <!--//06-10-2022 : #9226: : CRM-6137  quote: update selected for multi row updates on multipler, qty and lead time-->
                            <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                                <div style="display:flex;justify-content: end;padding:5px">
                                    <p:commandButton  rendered="#{quotesHdr.isSuperQuote==true}" class="linkorCreateOpp" styleClass="linkorCreateOpp"  id="spQtlnk" disabled="#{!quotesHdr.editable || quotesHdr.listQuotDtls.size()==0 }" 
                                                      oncomplete="PF('confirBulkQuot').show()" actionListener="#{quotesHdr.generateMsg}"   title="Bulk generate #{custom.labels.get('IDS_OPP')}" style="float: left; width: 30px; "/>    

                                    <p:commandButton  class="btn btn-primary btn-xs" style="width: 120px;margin-left:15px " disabled="#{quotesHdr.listQuotDtls.size()==0}" value="Refresh Pricing" update=":quotefrom:cnfrmRecalPricing"  id="recalculatePricing"  oncomplete="PF('RecalcPricingWidgVar').show()"/>

                                    <p:commandButton id="updateSelectBtn" value="Update Selected" disabled="#{quotesHdr.selectedquotDtl == null or quotesHdr.selectedquotDtl.size() == 0}" 
                                                     class="btn btn-primary btn-xs" style="width:120px;margin-left:15px " actionListener="#{quotesDtl.defaultMultipleValues()}" update=":formQuotPdf :formQuotMultipleAdd"
                                                     oncomplete="PF('quotMultipleAdd').show();"/>
                                </div>
                            </div>
                            <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                                <!--#3085 Quotes - Line Items - Import-->
                                <!--                                Task#3543-CRM-3924: quotes: need ability to import multiple manufacturer quote   :24-02-2021 by harshithad-->
                                <!--//17-02-2023 : #10734 : ESCALATIONS CRM-6704  Quotes: Unable to import excel file-->
                                <p:commandButton value="Import"  title="Import from Excel" oncomplete="PF('dlgImpQuoteDtls').show()"    actionListener="#{quotesDtl.clearImportQuotDtls()}" style="float: right" class="btn btn-primary btn-xs"
                                                 update="frmImportQuotDtl:op frmImportQuotDtl:er  frmImportQuotDtl:oq frmImportQuotDtl:qtUpld :frmImportQuotDtl" disabled="#{quotesHdr.recId == 0 || !quotesHdr.editable}"  />                
                            </div>
                            <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                                <!--#3551 CRM-2135: #5: Quotes > Line Items > Delete All-->
                                <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                                <!--//10-10-2023 : #12059 : CRM-7304   Repfabric: Quote line item add button doesn't add new part number to products master-->
                                <!--13653: 03/05/2024: ESCALATION CRM-8031 Quotes: When clicking the edit button, it stops you from editing a Line Item-->
                                <p:commandButton value="Add" title="Add Line Items"  oncomplete="PF('dlgQuoteDtls').show();" actionListener="#{quotesDtl.clearQuotDtls()}" style="float: right" class="btn btn-primary btn-xs"
                                                 update=":quotefrom:dlgQuoteDtls :quotefrom:gridQuoteDtls :quotefrom:hdrText  :quotefrom:tabQuot:delDtls :quotefrom:oppLnItmPrincipal" disabled="#{quotesHdr.recId == 0 }" id="addDtls" />                
                            </div>
                            <!--                            <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                                                            #3551 CRM-2135: #5: Quotes > Line Items > Delete All
                                                                          
                                                        </div>-->
                            <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                            <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                                <p:commandButton value="Move Up" style="float: right" class="btn btn-primary btn-xs" actionListener="#{quotesDtl.MoveUpQuot(quotesHdr.recId)}"
                                                 update=":quotefrom:tabQuot:tblQuotDtls :quotefrom:tabQuot:moveUp" id="moveUp" oncomplete="forQuotSequence()"
                                                 disabled="#{quotesHdr.recId == 0 || !quotesHdr.editable || quotesHdr.selectedquotDtl.size()!=1 || quotesDtl.minSeq}" />                
                            </div>
                            <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                            <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                                <p:commandButton value="Move Down" style="float: right" class="btn btn-primary btn-xs" actionListener="#{quotesDtl.MoveDownQuot(quotesHdr.recId)}"
                                                 update=":quotefrom:tabQuot:tblQuotDtls :quotefrom:tabQuot:moveDown" id="moveDown" oncomplete="forQuotSequence()" 
                                                 disabled="#{quotesHdr.recId == 0 || !quotesHdr.editable || quotesHdr.selectedquotDtl.size()!=1 || quotesDtl.maxSeq}" />                
                            </div>
                            <!--#3551 CRM-2135: #5: Quotes > Line Items > Delete All-->
                            <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
    <!--                                <p:commandButton  value="Delete" title="Delete Selected Line Items"  onclick="PF('delAllConfirmation').show()"       actionListener="#{quotesDtl.clearQuotDtls()}" style="float: right" class="btn btn-danger btn-xs"
                                                 update="" disabled="#{quotesHdr.recId == 0 || !quotesHdr.editable || quotesHdr.listQuotDtls.size() ==0}" id="delDtls" />                -->
                                <p:commandButton widgetVar="delBtns" value="Delete" title="Delete Selected Line Items" actionListener="#{quotesDtl.delQuotDtls()}" style="float: right" class="btn btn-danger btn-xs"
                                                 update="" disabled="#{quotesHdr.recId == 0 || !quotesHdr.editable ||quotesHdr.selectedquotDtl.size()==0}" id="delDtls" />                
                                <!--#{quotesHdr.selectedquotDtl.size()}-->
                            </div>
                        </div> 
                        <!--//   #14850  CRM-8639 : part number authoring: optionally autocapitalize alphanumerics, especially quote-->
                        <script>
                          function  capitalizeFunction(){
                            var autocapitalize = document.getElementById("quotefrom:tabQuot:capitalize").value;
                            var y = document.getElementById("quotefrom:tabQuot:tblQuotDtls:oppItemPartManf_input");
                             //console.log("---autocapitalize----",autocapitalize);
                            if (autocapitalize == 'true') {
                                y.style.textTransform = 'uppercase';
                            } else {
                                y.style.textTransform = 'none';
                            }
                        }

                            //25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value
                            //19-08-2022 : #8768 :Direct Request : Quick Add Line Item - auto place the cursor on the newly added part #
                            function selectFocus() {
//    dataEditableFocus = function (dataTableWidgetVar, hasRowIndex) {
                                var row;
                                var tb = $(document.getElementById(PF('tblQuotDtls1').id));
                                var multiple = $(tb).find('tr td.ui-selection-column');
                                if ($(multiple).length === 0) {
//                                    if (hasRowIndex) {
//                                        row = $(tb).find('tr td.ui-editable-column:nth-child(2)');
//                                    } else {
                                    row = $(tb).find('tr td.ui-editable-column:nth-child(1)');
//                                    }
                                    $(row).last().find('input').click();
                                } else {
//                                    if (hasRowIndex) {
//                                        row = $(tb).find('tr td.ui-editable-column:nth-child(3)');
//                                    } else {
                                    row = $(tb).find('tr td.ui-editable-column:nth-child(2)');
//                                    }
//                                    $(row).last().parent().children().first().click();
                                    $(row).last().find('input').click();
//                                    $(row).last().find('span.ui-icon-pencil').each(function(){jQuery(this).dblclick()})
                                }
//                                console.log('called double click');
                            }
                            //19-08-2022 : #8768 :Direct Request : Quick Add Line Item - auto place the cursor on the newly added part #
                            function select() {
//    dataEditableFocus = function (dataTableWidgetVar, hasRowIndex) {
                                var row;
                                var tb = $(document.getElementById(PF('tblQuotDtls1').id));
                                var multiple = $(tb).find('tr td.ui-selection-column');
                                if ($(multiple).length === 0) {
//                                    if (hasRowIndex) {
//                                        row = $(tb).find('tr td.ui-editable-column:nth-child(2)');
//                                    } else {
                                    row = $(tb).find('tr td.ui-editable-column:nth-child(1)');
//                                    }
                                    $(row).last().find('input').click();
                                } else {
//                                    if (hasRowIndex) {
//                                        row = $(tb).find('tr td.ui-editable-column:nth-child(3)');
//                                    } else {
                                    row = $(tb).find('tr td.ui-editable-column:nth-child(3)');
//                                    }
//                                    $(row).last().parent().children().first().click();
                                    $(row).last().find('input').click();
//                                    $(row).last().find('span.ui-icon-pencil').each(function(){jQuery(this).dblclick()})
                                }
//                                console.log('called double click');
                            }
                            function selectFocusOnDiscription(index) {
                                compName = document.getElementById("quotefrom:tabQuot:tblQuotDtls:" + index + ":quotGridDescription").click();
//                                
                            }
                            //06-12-2022 :  : #9526 : Quote Refactoring: CRM-6259   Quotes: POBCO Quote Needs
                            function selectFocusOnPart(index) {
                                compName = document.getElementById("quotefrom:tabQuot:tblQuotDtls:" + index + ":quotGridOppItemPartManf").click();
//                                
                            }
                            //19-08-2022 : #8768 :Direct Request : Quick Add Line Item - auto place the cursor on the newly added part #
                            //06-12-2022 :  : #9526Quote Refactoring: CRM-6259   Quotes: POBCO Quote Needs
                            function timeoutFocus(principal) {
                                console.log('called time out=' + principal);
                                if (principal > 0) {
                                    setTimeout(selectFocus, 1000);
                                } else {
                                    setTimeout(select, 1000);
                                }

                            }
//                            function timeoutHideTool() {
//                                var hideInput = document.getElementById('quotefrom:partNumberHide');
//                                hideInput.value = 'false'
//                                    console.log('hideInputvalue='+hideInput.value)
//                                setTimeout(HideToolTip,200);
//                            }
//                            function HideToolTip() {
//                                var hideInput = document.getElementById('quotefrom:partNumberHide');
//                                hideInput.value = 'false'
//                                        console.log('hideInputvalue='+hideInput.value)
//
//                            }






                        </script>


                        <!--<p:remoteCommand name="celleditcom" autoRun="false" actionListener="#{quotesDtl.cellEditComplete()}" />-->
                        <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                        <p:remoteCommand name="forQuotSequence" autoRun="false" actionListener="#{quotesDtl.setQuotSequence(quotesHdr.selectedquotDtl)}" update=":quotefrom:tabQuot:moveUp :quotefrom:tabQuot:moveDown"/>
                        <!--//25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value-->
                        <p:remoteCommand name="updateTimeAndUserQuotesHdr" autoRun="false" actionListener="#{quotesDtl.updateTimeAndUser(quotesHdr.recId,loginBean.userId)}"/>
                        <!--//06-12-2022 :  : #9526 : Quote Refactoring: CRM-6259   Quotes: POBCO Quote Needs-->
                        <p:remoteCommand name="gridQuotPrincipalLookup" autoRun="false" actionListener="#{quotesDtl.gridQuotPrincipalSelection(viewCompLookupService.selectedCompany,quotesHdr)}"/>
                        <p:remoteCommand name="applyPrincipalRemoteBol" autoRun="false" actionListener="#{viewCompLookupService.principalSelectedBol()}"/>
                        <p:remoteCommand name="multiplePrinciSinglePart" autoRun="false" actionListener="#{quotesDtl.selectPrincipalOfPart(viewCompLookupService.selectedCompany,quotesHdr)}"/>
                        <!--//23-02-2023 :  #10619 : CRM-6498 : Logic change for adding new line item to quotes-->
                        <!--//03-04-2023 : #10984 : CRM-6829   Quote: cursor not waiting on input field of line items-->
                        <p:remoteCommand name="multiplePrinciSelection" autoRun="false" actionListener="#{quotesHdr.selectPrincipalForPart(viewCompLookupService.selectedCompany)}" oncomplete="delayToWaitPart()"/>
                        <p:remoteCommand name="superQuotPrincipalSelect" autoRun="false" actionListener="#{quotesHdr.selectSuperQuotForPart(viewCompLookupService.selectedCompany)}" oncomplete="delayToWaitPart()"/>
                        <p:remoteCommand name="tabforDerscription" delay="300" autoRun="false" actionListener="#{quotesDtl.updateTabforPartNum()}"/>
                        <!--712 CRM-2640: Rouzer: Updates to Quote module-->
                        <!--6018 5858 > Open Quote > Filter n Edit Line item : Displays line item details of other line item-->
                        <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                        <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                        <!--//08-12-2023 : #12676 : ESCALATION CRM-7458 quotes: customer markup next to multiplier - Screen cut off UI issue-->
                        <p:dataTable id="tblQuotDtls" widgetVar="tblQuotDtls1" value="#{quotesHdr.listQuotDtls}"
                                     var="quot" rowIndexVar="index"  style="table-layout: fixed;width:100%" 
                                     rowKey="#{quot.recId}" sortBy="#{quot.quotSeqNum}" 
                                     selection="#{quotesHdr.selectedquotDtl}"
                                     paginatorAlwaysVisible="false" scrollable="true" scrollHeight="500" 
                                     paginatorPosition="top" editable="true" editMode="cell" 
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                     multiViewState="true" paginator="false">
                            <!--                        <p:dataTable id="tblQuotDtls" widgetVar="tblQuotDtls1" paginator="true" 
                                                                 value="#{quotesHdr.listQuotDtls}"
                                                                 var="quot" rowIndexVar="index" 
                                                                 rowKey="#{quot.recId}"  
                                                                 selection="#{quotesHdr.selectedquotDtl}"
                                                                 selectionMode="multiple" 
                                                                 paginatorAlwaysVisible="false"
                                                                 paginatorPosition="top"
                                                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                                                 rows="5"   class="tblmain"                         
                                                                 draggableColumns="true" multiViewState="true">-->
                            <!--                    <p:ajax event="rowSelect"                            
                                                        listener="#{quotesDtl.onRowDtl}"
                                                        update=" "
                                                        oncomplete=" "
                                                        />-->

                            <!--//27-09-2022 : #9151  : Direct Request : PRIORITY: Fwd: FW: TEC Price Code Discrepancy List-->
                            <p:ajax event="cellEdit" listener="#{quotesDtl.cellEdit}" delay="100"  update=":quotefrom:tabQuot:tblQuotDtls"/>


                            <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                            <!--//06-10-2022 : #9226: : CRM-6137  quote: update selected for multi row updates on multipler, qty and lead time-->
                            <p:ajax event="rowSelectCheckbox" immediate="true" listener="#{quotesDtl.maxQuoteSeq}" oncomplete="forQuotSequence()"  
                                    update=":quotefrom:tabQuot:delDtls :quotefrom:tabQuot:moveUp :quotefrom:tabQuot:moveDown :quotefrom:tabQuot:updateSelectBtn" process="@this" />
                            <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                            <!--//06-10-2022 : #9226: : CRM-6137  quote: update selected for multi row updates on multipler, qty and lead time-->
                            <p:ajax event="rowUnselectCheckbox" immediate="true" listener="#{quotesDtl.minQuoteSeq}" oncomplete="forQuotSequence()"
                                    update=":quotefrom:tabQuot:delDtls :quotefrom:tabQuot:moveUp :quotefrom:tabQuot:moveDown :quotefrom:tabQuot:updateSelectBtn" process="@this" />
                            <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                            <!--//06-10-2022 : #9226: : CRM-6137  quote: update selected for multi row updates on multipler, qty and lead time-->
                            <p:ajax event="rowSelect" immediate="true"  listener="#{quotesDtl.maxQuoteSeq}" oncomplete="forQuotSequence()"
                                    update=":quotefrom:tabQuot:delDtls :quotefrom:tabQuot:moveUp :quotefrom:tabQuot:moveDown :quotefrom:tabQuot:updateSelectBtn" process="@this" />
                            <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                            <!--//06-10-2022 : #9226: : CRM-6137  quote: update selected for multi row updates on multipler, qty and lead time-->
                            <p:ajax event="rowUnselect" immediate="true" listener="#{quotesDtl.minQuoteSeq}" oncomplete="forQuotSequence()" 
                                    update=":quotefrom:tabQuot:delDtls :quotefrom:tabQuot:moveUp :quotefrom:tabQuot:moveDown :quotefrom:tabQuot:updateSelectBtn" process="@this" />
                            <!--//06-10-2022 : #9226: : CRM-6137  quote: update selected for multi row updates on multipler, qty and lead time-->
                            <p:ajax event="toggleSelect" immediate="true" 
                                    update=":quotefrom:tabQuot:delDtls :quotefrom:tabQuot:updateSelectBtn" process="@this" oncomplete="forQuotSequence()" />
    <!--                            <p:ajax event="rowSelect" listener="#{quotesDtl.onRowDtl}"
                                    update=":quotefrom:gridQuoteDtls :quotefrom:hdrText :quotefrom:oppLnItmPrincipal"
                                    oncomplete="PF('dlgQuoteDtls').show()"
                                    >
                                <p:resetInput target=":frmSprQtOppMst:sprQtPrinci"/>
                            </p:ajax>-->
                <!--selectionMode="#{quotesHdr.recId == 0}"-->




                            <p:column selectionMode="multiple" style="width: 40px;text-align: center; " >                             
                            </p:column>
                            <p:column headerText="#{custom.labels.get('IDS_PRINCI')}"  filterBy="#{quot.quotPrincipalName}" id="distriName" filterMatchMode="contains" sortBy="#{quot.quotPrincipalName}"  
                                      rendered="#{quotesHdr.oppPrincipal==0}"  style="width:200px; min-width: 30%;max-width: 30%">
                                <!--09/08/2024 CRM-8351 Quote: Unable to update standard price field for robroy items: Line Item Principal is not editable-->
                                <p:outputLabel id="gridQuotPrincipalName" value="#{quot.quotPrincipalName}"    title="#{quot.quotPrincipalName}"></p:outputLabel> 
                                <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                                <!--//06-12-2022 :  : #9526 :Quote Refactoring: CRM-6259   Quotes: POBCO Quote Needs-->
<!--                                <p:cellEditor>
                                    <f:facet name="output">
                                        <p:outputLabel id="gridQuotPrincipalName" value="#{quot.quotPrincipalName}"    title="#{quot.quotPrincipalName}"></p:outputLabel> 
                                    </f:facet>
                                    <f:facet name="input">
                                        <p:autoComplete id="gridOppPrincipal" value="#{quot.quotPrincipalName}"
                                                        completeMethod="#{quotesDtl.completeCompGridPrincipal}" style="width:100%"
                                                        disabled="#{quot.oppItemId > 0}" widgetVar="gripOppPrincipalWidget#{index}" minQueryLength="3">
                                            <f:attribute name="selecedPrincipalQuot" value="#{quot}"/>`
                                            <p:ajax event="itemSelect" listener="#{quotesDtl.selectedPricipalForLineItem}" oncomplete="selectFocusOnPart(#{index})"/>
                                            <p:ajax event="blur" onstart="if(PF('gripOppPrincipalWidget#{index}').panel.is(':visible')) return false;" listener="#{quotesDtl.changeGridPrincipal(quot)}" oncomplete="selectFocusOnPart(#{index});" />
                                        </p:autoComplete>
                                    </f:facet>
                                </p:cellEditor>-->
                            </p:column>
                            <h:inputHidden id="ihIsPartNumberChange" value="#{quotesDtl.isChangePart}"/>
                            <p:column headerText="#{custom.labels.get('IDS_MFG_PART_NUM')}"  filterBy="#{quot.oppItemPartManf}"  filterMatchMode="contains" style="width:150px;min-width: 30%;max-width: 30%"  sortBy="#{quot.oppItemPartManf}">
                                <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                                <!--//16-09-2022 : #9024 : Quote Refactoring: Tooltip suggestion for part number-->
<!--                                <p:tooltip for="quotefrom:tabQuot:tblQuotDtls:#{index}:oppItemPartManf"  widgetVar="tollForQuotAutoComplete#{index}" 
                                           value="Type a few characters to get the assisted lookup" showEvent="mouseover" position="right" 
                                           hideEvent="keyup" />
                                //04-10-2022 :  #9132 : Quote: Line Item , Growl message displayed though part # is not blank
                                <p:tooltip for="quotefrom:tabQuot:tblQuotDtls:#{index}:dtQtLineItemOpPartManf"  widgetVar="tollForQuotCellEdit#{index}" 
                                           value="Type a few characters to get the assisted lookup" showEvent="mouseover" position="right" 
                                           hideEvent="mouseout" rendered="#{quot.oppItemPartManf == null || quot.oppItemPartManf == ''}" />
                                <p:tooltip for="quotefrom:tabQuot:tblQuotDtls:#{index}:dtQtLineItemOpPartManf"  widgetVar="tollForQuotCellEdit#{index}" 
                                           rendered="#{quot.oppItemPartManf != null || quot.oppItemPartManf != ''}" />-->
                                <!--<p:cellEditor id="cellEdit">-->
                                <!--<f:facet name="output">-->
                                <!--//25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value-->
                                <p:outputPanel id="dtQtLineItemOpPartManf" style="width:100%!important">
                                    <p:outputLabel id="quotGridOppItemPartManf"  value="#{quot.oppItemPartManf}" ></p:outputLabel> 
                                </p:outputPanel>
                                <!--<br/>-->
                                <!--#{quot.viewProdMst.prodPrinciPartno}-->
                                <!--</f:facet>-->
                                <!--<f:facet name="input">-->
                                <!--//16-11-2022 : #9294 : ESCALATIONS CRM-6212  pirsales/Quotes Line Item - part description box does not collapse after savin-->
                                <!--//17-11-2022 : #9770 : Quote Refactoring: Quote > Line Item > Part Number type ahead > Fine tune query for autocomplete-->
                                <!--//29-11-2022 : #9911 : Incidental Finding-9805: Quotes>Line Items Tabbing Issue-->
                                <!--//06-12-2022 :  : #9526 : Quote Refactoring: CRM-6259   Quotes: POBCO Quote Needs-->
<!--                                        <p:autoComplete id="oppItemPartManf" value="#{quot.viewProdMst}"  converter="ViewProductMstConverter"
                                                completeMethod="#{quotesDtl.completePartManNewLineV2}" style="width: 100%" var="varViewProd"
                                                itemLabel="#{varViewProd.prodPrinciPartno}" itemValue="#{varViewProd}" minQueryLength="3"
                                                widgetVar="gridPartNumWidget#{index}"
                                                onclick="PF('tollForQuotAutoComplete#{index}').hide()" onmouseout="PF('tollForQuotAutoComplete#{index}').hide();"
                                                placeholder="Type a few characters to get the assisted lookup"
                                                queryDelay="500">

                                    <f:attribute name="selecedQuoteDtl" value="#{quot}"/>`
                                    <f:facet name="itemtip">
                                        <p:outputPanel>
                                            <h:panelGrid columns="2" cellpadding="5" >
                                                <f:facet name="header">
                                                    <h:outputText value="Product Information:"/>
                                                </f:facet>

                                                <h:outputText value="#{custom.labels.get('IDS_PRINCI')}:"/>
                                                <h:outputText value="#{varViewProd.princiName}"/>
                                                //04-10-2022 : #9218 : CRM-6190: quotes: crosses does not do anything (at least in superquotes)
                                                <h:outputText value="#{custom.labels.get('IDS_PART_NUM')}:"/>
                                                <h:outputText value="#{varViewProd.crossProdPartNum}" />

                                                <h:outputText value="#{custom.labels.get('IDS_PROD_FAMILY')}:"/>
                                                <h:outputText value="#{varViewProd.prodFamily}"/>

                                                <h:outputText value="#{custom.labels.get('IDS_PROD_LINE')}:"/>
                                                <h:outputText value="#{varViewProd.prodLine}"/>

                                                <h:outputText value="Description:"/>
                                                <h:outputText value="#{varViewProd.prodDesc}"/>

                                                <h:outputText value="Standard Price:"/>
                                                <h:outputText value="#{varViewProd.prodStdPrice}" >
                                                    <f:convertNumber groupingUsed="true" minFractionDigits="0" maxFractionDigits="3"/>
                                                </h:outputText>

                                                <h:outputText value="Comm. Rate:"/>
                                                <h:outputText value="#{varViewProd.prodCommRate}"/>
                                            </h:panelGrid>
                                            //04-10-2022 : #9218 : CRM-6190: quotes: crosses does not do anything (at least in superquotes)
                                            <h:panelGrid columns="2" rendered="#{varViewProd.crossRecId > 0}" styleClass="top-align"
                                                         columnClasses="ui-grid-col-3,ui-grid-col-10" >
                                                <f:facet name="header">
                                                    <h:outputText value="Crosses Information:"/>
                                                </f:facet>

                                                <h:outputText value="#{custom.labels.get('IDS_PRINCI')}:"/>
                                                <p:spacer width="4"/>
                                                <h:outputText value="#{varViewProd.crossPrinciName}"/>

                                                <h:outputText value="#{custom.labels.get('IDS_PART_NUM')}:"/>
                                                <p:spacer width="4"/>
                                                <h:outputText value="#{varViewProd.prodCrossPartNum}"/>

                                                <h:outputText value="Description:"/>
                                                <p:spacer width="4"/>
                                                <h:outputText value="#{varViewProd.crossProdDiscription}"/>

                                            </h:panelGrid>
                                        </p:outputPanel>
                                    </f:facet>
                                    //01-02-2023 :  #10343 : CRM-6498  Product Info pop up boxes sticking again
                                    <p:ajax event="keyup" onstart="takingValueOppItem(#{index})" oncomplete="tabbing(#{index})"/>
                                    //09-01-2022 :  #10343 : CRM-6498  Product Info pop up boxes sticking again
                                    <p:ajax event="itemSelect" listener="#{quotesDtl.onselectProd}" immediate="true" update=":quotefrom:tabQuot:tblQuotDtls" oncomplete="PF('gridPartNumWidget#{index}').close();selectFocusOnDiscription(#{index});PF('gridPartNumWidget#{index}').close();"/>
                                    <p:ajax event="change" onstart="if(PF('gridPartNumWidget#{index}').panel.is(':visible')) return false;"  listener="#{quotesDtl.changePartNumEvent()}" oncomplete="PF('gridPartNumWidget#{index}').close();" />
                                    //19-08-2022 : #8768 :Direct Request : Quick Add Line Item - auto place the cursor on the newly added part 
                                    <p:ajax event="blur" onstart="if(PF('gridPartNumWidget#{index}').panel.is(':visible')) return false;" listener="#{quotesDtl.alertPartNumber(quot)}" oncomplete="PF('gridPartNumWidget#{index}').close();"/>

                                    //04-10-2022 :  #9132 : Quote: Line Item , Growl message displayed though part # is not blank
                                    <p:ajax rendered="#{index != null}" delay="500" event="keyup" listener="#{quotesDtl.partNumberKeyUp(quot,index)}"/>
                                </p:autoComplete>-->
                                <!--</f:facet>-->
                                <!--</p:cellEditor>-->
                            </p:column>
                            <!--#3701 CRM-2217: #3: Quotes > Line Items > Add Part description-->
                            <p:column headerText="Description"  style="width:200px;min-width: 30%;max-width: 30%"  filterBy="#{quot.oppItemPartDesc}" filterMatchMode="contains"  sortBy="#{quot.oppItemPartDesc}">
                                <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                                <!--//25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value-->
                                <p:cellEditor>
                                    <f:facet name="output">
                                        <p:outputLabel id="quotGridDescription" value="#{quot.oppItemPartDesc.length()>27?quot.oppItemPartDesc.substring(0,25).concat('..'):quot.oppItemPartDesc}" 
                                                       title="#{quot.oppItemPartDesc}"/>
                                    </f:facet>
                                    <f:facet name="input">
                                        <p:inputText value="#{quot.oppItemPartDesc}">
                                            <p:ajax event="change" 
                                                    listener="#{quotesDtl.onChange(quot,1)}" 
                                                    />
                                        </p:inputText>
                                    </f:facet>
                                </p:cellEditor>
                            </p:column>
                            <!--752 SuperQuotes : UI Issues-->
                            <p:column headerText="Qty" style="text-align: left; width:50px" id="qtyDId"   sortBy="#{quot.oppItemQnty}">
                                <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                                <!--//25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value-->
                                <p:cellEditor>
                                    <f:facet name="output">
                                        <p:outputLabel id="quotGridQty" value="#{quot.oppItemQnty}"    title="#{quot.oppItemQnty}">
                                            <f:convertNumber groupingUsed="true" minFractionDigits="3" maxFractionDigits="3"/>
                                        </p:outputLabel>
                                    </f:facet>
                                    <f:facet name="input">
                                        <!--//18-04-2024 : #13568 : CRM-7892   UOM issues in Quote/PO line items - needs to be solved-->
                                        <p:inputNumber id="quotGridQtyInput"  value="#{quot.oppItemQnty}" placeholder="0"  
                                                       maxlength="9" maxValue="999999999" decimalPlaces="3"
                                                       thousandSeparator=",">
                                            <!--                                            <p:ajax event="change" 
                                                                                                listener="#{quotesDtl.onChange(quot)}" 
                                                                                                update=":quotefrom:tabQuot:tblQuotDtls"/>-->
                                            <!--// 15-09-2022 : #9041 : CRM-6136  Quotes: line items not matching quote header value-->
                                            <!--//23-09-2022 : #9108 : CRM-6161  quotes: typeahead grid filtering has too be too perfect-->
                                            <p:ajax event="change" listener="#{quotesDtl.applyQtyCustPrice(quot)}"/>  
                                        </p:inputNumber>
                                    </f:facet>
                                </p:cellEditor>
                            </p:column>
                            <!--752 SuperQuotes : UI Issues-->
                            <p:column headerText="Cost" style="text-align: left;width:50px" id="costDId"  sortBy="#{quot.oppItemCost}">
                                <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                                <!--//25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value-->
                                <p:cellEditor>
                                    <f:facet name="output">
                                        <p:outputLabel id="quotGridCost" value="#{quot.oppItemCost}"    title="#{quot.oppItemCost}">

                                            <f:convertNumber minFractionDigits="3" maxFractionDigits="3" groupingUsed="true" maxIntegerDigits="10"/>
                                        </p:outputLabel>
                                    </f:facet>
                                    <f:facet name="input">
                                        <p:inputNumber value="#{quot.oppItemCost}" placeholder="0"  
                                                       maxlength="9" maxValue="999999999" decimalPlaces="3"
                                                       thousandSeparator=",">
                                            <p:ajax event="change"
                                                    listener="#{quotesDtl.onChange(quot,2)}"/>
                                        </p:inputNumber>
                                    </f:facet>
                                </p:cellEditor>
                            </p:column>
                            <!--752 SuperQuotes : UI Issues-->
                            <p:column headerText="#{custom.labels.get('IDS_STD_PRICE')}" style="text-align: left;width:70px" id="stdDId"  sortBy="#{quot.quotStdRate}" >
                                <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                                <!--//25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value-->
                                <!--//          #12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
                                <p:tooltip id="tooltips" showEvent="mouseover" position="right"  for="quotefrom:tabQuot:tblQuotDtls:#{index}:quotGridStdRate" >
                                    <p:outputLabel  value="#{quot.quotStdRate}"   >
                                        <f:convertNumber  groupingUsed="true"  minFractionDigits="2" maxFractionDigits="6"/>
                                    </p:outputLabel>
                                </p:tooltip>

                                <p:cellEditor>
                                    <f:facet name="output">
                                        <p:outputLabel id="quotGridStdRate" value="#{quot.quotStdRate}"  > 
                                            <!--//          #12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
                                            <!--9923 PRIORITY CRM-6404  quote decimal places for unit price not consistent with opps, downflow fails-->
                                            <f:convertNumber  groupingUsed="true"  minFractionDigits="2" maxFractionDigits="6"/>
                                        </p:outputLabel>  
                                    </f:facet>
                                    <f:facet name="input">
                                        <!--//          #12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
                                        <!--//23-09-2022 : #9108 : CRM-6161  quotes: typeahead grid filtering has too be too perfect-->
                                        <!--//06-10-2022 : #9226: : CRM-6137  quote: update selected for multi row updates on multipler, qty and lead time-->
                                        <!--9923 PRIORITY CRM-6404  quote decimal places for unit price not consistent with opps, downflow fails-->
                                        <!--//30-11-2023 : #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                                        <p:inputText  id="quotGridStdRateInp"  value="#{quot.quotStdRate}" onkeyup="window.myFunction6(1,#{index})"   onkeypress="return window.myFunction7(event, 1,#{index})"  placeholder="0"  
                                                      converterMessage="Must be a signed decimal number."  converter="javax.faces.BigDecimal"
                                                      >
                                            <f:convertNumber   maxFractionDigits="6" minFractionDigits="2"/>
                                            <!--//07-11-2023 :  #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                                            <!--                                            //25-09-2024 : #14246 : 2 ESCALATION CRM-8351 : Quote: Unable to update standard price field for robroy items-->
                                            <p:keyFilter mask="num" for="quotGridStdRateInp" preventPaste="false"/>
                                            <!--                                            <p:ajax event="change"  
                                                                                                listener="#{quotesDtl.onChange(quot)}" 
                                                                                                update=":quotefrom:tabQuot:tblQuotDtls"/>-->
                                            <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                                            <!--//30-11-2023 : #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                                            <p:ajax event="change" listener="#{quotesDtl.calculateUnitQuotPrice(quot,1,quotesHdr,1,1)}" 
                                                    />
                                        </p:inputText>
                                    </f:facet>
                                </p:cellEditor>                       
                            </p:column>
                            <!--752 SuperQuotes : UI Issues-->
                            <p:column headerText="Multiplier" style="text-align: left;width:70px" id="mulDId" sortBy="#{quot.quotMultiplier}"> 
                                 <!--sortBy="#{quot.quotMultiplier}"-->
                                <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                                <p:cellEditor>
                                    <!--#12199 CRM-7350   Uploaded multiplier at .3735 it changed to .374 - increase number of decimal places to 5-->
                                    <!--//25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value-->
                                    <f:facet name="output">
                                        <p:outputLabel id="quotGridMultiplier" value="#{quot.quotMultiplier}" title="#{quot.quotMultiplier}">
                                            <f:convertNumber groupingUsed="true" minFractionDigits="5" maxFractionDigits="5"/>
                                        </p:outputLabel>  
                                    </f:facet>
                                    <f:facet name="input">
                                        <!--#12199 CRM-7350   Uploaded multiplier at .3735 it changed to .374 - increase number of decimal places to 5-->
                                        <!--//23-09-2022 : #9108 : CRM-6161  quotes: typeahead grid filtering has too be too perfect-->
                                        <!--//06-10-2022 : #9226: : CRM-6137  quote: update selected for multi row updates on multipler, qty and lead time-->
                                        <!--//30-11-2023 : #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                                        <!--//31-01-2024 : #13018 : Incidental finding: Quotes>Incorrect display of Quote multiplier-->
                                        <p:inputNumber id="quotGridMultiplierInpt" value="#{quot.quotMultiplier}"  
                                                       maxlength="9" maxValue="999999999" decimalPlaces="5"
                                                       thousandSeparator=",">
                                            <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                                            <!--//30-11-2023 : #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                                            <p:ajax event="change" listener="#{quotesDtl.calculateUnitQuotPrice(quot,2,quotesHdr,1,1)}" 
                                                    />
                                        </p:inputNumber>
                                    </f:facet>
                                </p:cellEditor>
                            </p:column> 
                            <!--//02-11-2023 : #12462 : CRM-7458 quotes: customer markup next to multiplier-->
                            <p:column headerText="Customer Markup" style="text-align: left;width:70px" sortBy="#{quot.quotCustMarkupPct}"> 
                                <p:cellEditor>
                                    <f:facet name="output">
                                        <p:outputLabel id="quotGridMarkup" value="#{quot.quotCustMarkupPct}" title="#{quot.quotCustMarkupPct}">
                                            <f:convertNumber groupingUsed="true" minFractionDigits="2" maxFractionDigits="2"/>
                                        </p:outputLabel>  
                                    </f:facet>
                                    <f:facet name="input">
                                        <!--//30-11-2023 : #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                                        <p:inputNumber value="#{quot.quotCustMarkupPct}"  
                                                       maxlength="10" maxValue="9999999.99" decimalPlaces="2"
                                                       thousandSeparator="," >
                                            <!--//30-11-2023 : #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                                            <p:ajax event="change" listener="#{quotesDtl.calculateUnitQuotPrice(quot,2,quotesHdr,1,1)}" 
                                                    />
                                        </p:inputNumber>
                                    </f:facet>
                                </p:cellEditor>
                            </p:column> 
                            <!--752 SuperQuotes : UI Issues-->
                            <p:column headerText="#{custom.labels.get('IDS_UNIT_PRICE')}" style="text-align: left;width:70px" id="unitDId"  sortBy="#{quot.quotResale}" >
                                <!--//          #12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
                                <p:tooltip  showEvent="mouseover" position="right"  for="quotefrom:tabQuot:tblQuotDtls:#{index}:quotGridResale" >
                                    <p:outputLabel  value="#{quot.quotResale}"   >
                                        <f:convertNumber  groupingUsed="true"  minFractionDigits="2" maxFractionDigits="6"/>
                                    </p:outputLabel>
                                </p:tooltip>

                                <p:cellEditor>
                                    <!--//          #12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
                                    <!--//25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value-->
                                    <f:facet name="output">
                                        <p:outputLabel id="quotGridResale" value="#{quot.quotResale}"   title="#{quot.quotResale}"  >
                                            <f:convertNumber  groupingUsed="true"  minFractionDigits="2" maxFractionDigits="6"/>
                                        </p:outputLabel>  
                                    </f:facet>
                                    <f:facet name="input">
                                        <!--//          #12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->
                                        <!--//23-09-2022 : #9108 : CRM-6161  quotes: typeahead grid filtering has too be too perfect-->
                                        <!--//06-10-2022 : #9226: : CRM-6137  quote: update selected for multi row updates on multipler, qty and lead time-->
                                        <p:inputText id="quotGridResaleInp" value="#{quot.quotResale}" placeholder="0"  
                                                     onkeyup="window.myFunction6(2,#{index})"   converter="javax.faces.BigDecimal" converterMessage="Must be a signed decimal number."  onkeypress="return window.myFunction7(event, 2,#{index})"
                                                     disabled="#{quot.unitPriceFlag}">
                                            <f:convertNumber   maxFractionDigits="6" minFractionDigits="2"/>
                                            <!--//07-11-2023 :  #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                                            <!--//25-09-2024 : #14246 : 2 ESCALATION CRM-8351 : Quote: Unable to update standard price field for robroy items-->
                                            <p:keyFilter mask="num" for="quotGridResaleInp" preventPaste="false"/>
                                            <!--//07-11-2023 :  #12494 : ESCALATIONS CRM-7521   Quotes Module - Unable to paste value into Standard Pricing-->
                                            <p:ajax event="change" listener="#{quotesDtl.calculateQuotExtPrice(quot,1,quotesHdr,1)}" 
                                                    />
                                        </p:inputText>
                                    </f:facet>
                                </p:cellEditor>
                            </p:column>
                            <!--752 SuperQuotes : UI Issues-->
                            <p:column headerText="Extended Price" style="text-align: left;width:70px" id="extDId"  sortBy="#{quot.quotExtPrice}">
                                <p:outputLabel id="quotGridExtPrice" value="#{quot.quotExtPrice}" title="#{quot.quotExtPrice}">
                                    <!--#3547 Quotes Line item : Multiple time alert message displays. (Page Reloading issue)-->
                                    <f:convertNumber  groupingUsed="true"  minFractionDigits="2" maxFractionDigits="2"/>
                                </p:outputLabel> 
                            </p:column>
                            <!--752 SuperQuotes : UI Issues-->
                            <p:column headerText="Lead Time" style="text-align: left;width:70px"  sortBy="#{quot.quotLeadTime}">
                                <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                                <p:cellEditor>
                                    <!--//25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value-->
                                    <f:facet name="output">
                                        <p:outputLabel id="quotGridLeadTime" value="#{quot.quotLeadTime}" title="#{quot.quotLeadTime}">
                                        </p:outputLabel> 
                                    </f:facet>
                                    <f:facet name="input">
                                        <!--Feature #11228: CRM-6924   Quotes: lead time does not print  by harshithad on 24/05/23-->
                                        <p:inputText value="#{quot.quotLeadTime}" maxlength="40" >
                                            <p:ajax event="change"
                                                    listener="#{quotesDtl.onChange(quot,3)}"/>
                                        </p:inputText>
                                    </f:facet>
                                </p:cellEditor>
                            </p:column> 
                            <!--5858 IS Quotes: Quote Line Item > Location-->


                              <!--task:14847 :05-11-2024 added the custom label for the table header of "Location" and added the text-align: style-->
                            <p:column headerText="#{custom.labels.get('IDS_LOCATION')}" style="text-align: left;width:70px"  sortBy="#{quot.quotLocation}">

                                <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                                <p:cellEditor>
                                    <!--//25-11-2022 : #9805 : PRIORITY: Quote Refactoring: Line item grid - single click allow user to tab to enter the value-->
                                    <f:facet name="output">
                                        <p:outputLabel id="quotGridLocation" value="#{quot.quotLocation}" title="#{quot.quotLocation}">
                                        </p:outputLabel>
                                    </f:facet>
                                    <f:facet name="input">
                                        <p:inputText value="#{quot.quotLocation}" >
                                            <p:ajax event="change"
                                                    listener="#{quotesDtl.onChange(quot,4)}" />
                                        </p:inputText>
                                    </f:facet>
                                </p:cellEditor>
                            </p:column>

                            <p:column style="text-align: center; width: 40px;">
                                <!--                                <c:choose>
                                                                    <c:when test="#{quot.oppId!='0'}">
                                <div style="color: green;"><i class="fa fa-link"></i></div> #{quot.oppId}
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                      no  #{quot.oppId}
                                                                    </c:otherwise>
                                                                      </c:choose>   -->
                                <f:facet name="header">
                                    <!--#7606 ys-204 opportunity icon-->
                                    <h:graphicImage  library="images" name="rf_opp_icon_plus.svg" width="15" height="15" title="Status" />
                                </f:facet>
                                <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                                <h:outputText value=""   styleClass="fa #{quot.oppId > 0?'linkorCreateOpp':''}" />

                            </p:column> 
                            <p:column headerText="Sequence Number" style="min-width: 10%;max-width: 10%"  sortBy="#{quot.quotSeqNum}" rendered="false">
                                <p:outputLabel value="#{quot.quotSeqNum}" title="#{quot.quotSeqNum}">
                                </p:outputLabel>
                            </p:column>
                            <p:column headerText="Edit" style="text-align: center; width: 40px;">
                                <!--//10-10-2023 : #12059 : CRM-7304   Repfabric: Quote line item add button doesn't add new part number to products master-->
                                <!--13653: 03/05/2024: ESCALATION CRM-8031 Quotes: When clicking the edit button, it stops you from editing a Line Item-->
                                <p:commandButton styleClass="btn-primary btn-xs btn-icon-group btn-icon" 
                                                 title="Edit" icon="fa fa-pencil"  
                                                 actionListener="#{quotesDtl.onRowDtl}" disabled="#{quotesHdr.recId == 0 }" update=":quotefrom:gridQuoteDtls :quotefrom:hdrText :quotefrom:oppLnItmPrincipal quotefrom:dlgQuoteDtls"
                                                 oncomplete="PF('dlgQuoteDtls').show()" >
                                    <p:resetInput target=":frmSprQtOppMst:sprQtPrinci"/>
                                    <!--6098 - 5858 > Open Quote > Filter n Edit Line item : Displays line item details of other line item-->
                                    <f:param name="bookId" value="#{quot.recId}" />
                                </p:commandButton>

<!--<f:setPropertyActionListener value="#{quot}" target="#{quotesDtl}" />-->
<!--<f:attribute name="bf" value="#{quot}"  />-->

    <!--<f:setPropertyActionListener value="#{quot}" target="#{quotesHdr.selectedquotDtl}" />-->
                            </p:column>


                            <f:facet name="footer">
                                <!--//24-08-2022 : #8805 : CRM-6059  Quote: UAT: UI fixes for line items-->
                                <!--//06-02-2023 : #10619 : CRM-6498 : Logic change for adding new line item to quotes-->
                                <!--//06-02-2023 : #10619 : CRM-6498 : Logic change for adding new line item to quotes-->
                                <p:outputPanel id="footerPanel" style="text-align: left;margin-left: 10px;margin-bottom:-10px;">
                                    <!--//15-12-2023 : #12725 : CRM-7458	quotes: customer markup next to multiplier (Tool tip updates)-->
                                    <div class="ui-g ui-fluid header-bar" style="margin-bottom: 0px !important;border-radius: 0px !important;webkit-box-shadow: inset 0 !important">
                                        <!--                                        <div class="ui-sm-12 ui-md-1 ui-lg-1">
                                                                                    
                                                                                </div>-->
                                        <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                                            <p:outputLabel value="#{custom.labels.get('IDS_MFG_PART_NUM')}:" style="width:25%" />
                                            <p:spacer width="4"/>
                                            <!--<h:inputHidden id="quotPrincipalTextValue" value="#{quotesHdr.quotAddPrincipal}"/>-->
                                            <p:tooltip for=":quotefrom:tabQuot:tblQuotDtls:oppItemPartManf"  widgetVar="tollForQuotAutoComplete" 
                                                       value="Type a few characters to get the assisted lookup" showEvent="mouseover" position="right" 
                                                       hideEvent="mouseout" rendered="#{quotesHdr.quotAddPartNumber == null or quotesHdr.quotAddPartNumber == ''}" />
                                           <!--//   #14850  CRM-8639 : part number authoring: optionally autocapitalize alphanumerics, especially quote-->
                                            <!--11357:05/06/2023:  ESCALATIONS CRM-7033   Quotes: Search function needs to be refined-->
                                            <p:autoComplete id="oppItemPartManf" value="#{quotesHdr.quotAddPartNumber}"
                                                            completeMethod="#{quotesDtl.completePartManNewLineV2}" style="width:70%;" minQueryLength="3"
                                                            widgetVar="gridPartNumTextWidget" onkeyup="capitalizeFunction()"
                                                            onclick="PF('tollForQuotAutoComplete').hide()"
                                                            placeholder="Type a few characters to get the assisted lookup"
                                                            queryDelay="500" onkeypress="testing(event)" scrollHeight="250"
                                                            >
                                            <!--<f:attribute name="selecedQuoteDtl" value="#{quot}"/>`-->
                                                <f:facet name="itemtip">
                                                    <p:outputPanel id="itemTip">
                                                        <h:panelGrid columns="2" cellpadding="5" >
                                                            <f:facet name="header">
                                                                <h:outputText value="Product Information:"/>
                                                            </f:facet>
                                                            <h:outputText value="#{custom.labels.get('IDS_PRINCI')}:"/>
                                                            <h:outputText id="otnewquoteprinciname"/>
                                                            <!--//04-10-2022 : #9218 : CRM-6190: quotes: crosses does not do anything (at least in superquotes)-->
                                                            <h:outputText value="#{custom.labels.get('IDS_PART_NUM')}:"/>
                                                            <h:outputText id="otnewquotepartno" />
                                                            <h:outputText value="#{custom.labels.get('IDS_PROD_FAMILY')}:"/>
                                                            <h:outputText id="otnewquotefamily"/>
                                                            <h:outputText value="#{custom.labels.get('IDS_PROD_LINE')}:"/>
                                                            <h:outputText id="otnewquoteline"/>
                                                            <h:outputText value="Description:"/>
                                                            <h:outputText id="otnewquotedesc"/>
                                                            <h:outputText value="Standard Price:"/>
                                                            <h:outputText id="otnewquoteprodstdprice" >
                                                                <f:convertNumber groupingUsed="true" minFractionDigits="0" maxFractionDigits="3"/>
                                                            </h:outputText>
                                                            <h:outputText value="Comm. Rate:"/>
                                                            <h:outputText id="otnewquoteprodcommrate"/>
                                                        </h:panelGrid>
                                                        <!--//04-10-2022 : #9218 : CRM-6190: quotes: crosses does not do anything (at least in superquotes)-->
                                                        <h:panelGrid columns="2" cellpadding="5" id="pgCrossProd" >
                                                            <f:facet name="header">
                                                                <h:outputText value="Crosses Information:"/>
                                                            </f:facet>
                                                            <h:outputText value="#{custom.labels.get('IDS_PRINCI')}:"/>
                                                            <!--<p:spacer width="4"/>-->
                                                            <h:outputText id="otnewquotecrossprinciname"/>
                                                            <h:outputText value="#{custom.labels.get('IDS_PART_NUM')}:"/>
                                                            <!--<p:spacer width="4"/>-->
                                                            <h:outputText id="otnewquotecrosspartno"/>
                                                            <h:outputText value="Description:"/>
                                                            <!--<p:spacer width="4"/>-->
                                                            <h:outputText id="otnewquotecrossproddesc"/>
                                                        </h:panelGrid>
                                                    </p:outputPanel>
                                                </f:facet>
                                                <!--//01-02-2023 :  #10343 : CRM-6498  Product Info pop up boxes sticking again-->
                                                <!--<p:ajax event="keyup" update=":quotefrom:tabQuot:tblQuotDtls:btnQuotAdd" />-->
                                                <!--//09-01-2022 :  #10343 : CRM-6498  Product Info pop up boxes sticking again-->
                                                <!--//03-04-2023 : #10984 : CRM-6829   Quote: cursor not waiting on input field of line items-->
                                                <p:ajax event="itemSelect" onstart="PF('dlgProcessingOnTask').show()" listener="#{quotesHdr.onselectProd1234}" process="@this" oncomplete="PF('gridPartNumTextWidget').close();selectQtyFocus();PF('dlgProcessingOnTask').hide()" update=":quotefrom:tabQuot:tblQuotDtls:btnQuotAdd"/>
                                                <p:ajax event="change" onstart="if(PF('gridPartNumTextWidget').panel.is(':visible')) return false;" oncomplete="PF('gridPartNumTextWidget').close();selectQtyFocus();" />
                                                <!--//19-08-2022 : #8768 :Direct Request : Quick Add Line Item - auto place the cursor on the newly added part-->
                                                <!--//03-04-2023 : #10984 : CRM-6829   Quote: cursor not waiting on input field of line items-->
                                                <p:ajax event="blur" onstart="if(PF('gridPartNumTextWidget').panel.is(':visible')) return false;" update=":quotefrom:tabQuot:tblQuotDtls:btnQuotAdd" oncomplete="PF('gridPartNumTextWidget').close();"/>"
                                                <!--//04-10-2022 :  #9132 : Quote: Line Item , Growl message displayed though part # is not blank-->
                                                <!--<p:ajax rendered="#{index != null}" delay="500" event="keyup" listener="#{quotesDtl.partNumberKeyUp(quot,index)}"/>-->
                                            </p:autoComplete>
                                        </div>
                                        <div class="ui-sm-12 ui-md-1 ui-lg-1" style="width: 10%">
                                            <!--#11212 CRM-6918: Product: Set pricing per hundred in template-->
                                            <p:outputLabel value="Qty (in units):" style="float:right;margin-top:-12px;" />
                                        </div>
                                        <div class="ui-sm-12 ui-md-2 ui-lg-2 ">
                                            <p:inputNumber id="quotQtyNumber"  value="#{quotesHdr.quotQty}"  
                                                           maxlength="9" maxValue="999999999" decimalPlaces="3"
                                                           thousandSeparator="," onkeypress="return event.keyCode != 13;">
                                            </p:inputNumber>
                                        </div>
                                        <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                                            <!--//03-04-2023 : #10984 : CRM-6829   Quote: cursor not waiting on input field of line items-->
                                            <!--//17-04-2023 : #11073 : CRM-6737: Existing opportunity linked to new Quote on Cloning Quote-->
                                            <p:commandButton id="btnQuotAdd" value="Add" title="Quick Add Line Items" action="#{quotesHdr.addLineItemRow()}" class="btn btn-primary btn-xs"
                                                             update=":quotefrom:tabQuot:spQtlnk" disabled="#{quotesHdr.recId == 0 or !quotesHdr.editable or quotesHdr.quotAddPartNumber == null or quotesHdr.quotAddPartNumber == ''}" 
                                                             oncomplete="forQuotSequence();focusOnPartNumberTabChange();" widgetVar="addQuotWidget" onclick="PF('addQuotWidget').disable()"/>
                                            <!--//06-02-2023 : #10619 : CRM-6498 : Logic change for adding new line item to quotes-->
                                            <!--<p:remoteCommand name="addButtonDisable" autoRun="false" update=":quotefrom:tabQuot:tblQuotDtls:btnQuotAdd"/>-->
                                        </div>
                                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">

                                        </div>
                                    </div>
                                    <!--//15-12-2023 : #12725 : CRM-7458	quotes: customer markup next to multiplier (Tool tip updates)-->
                                    <div class="ui-g ui-fluid header-bar" style="margin-bottom: 0px !important;border-radius: 0px !important;webkit-box-shadow: inset 0 !important;background-color: white;">
                                        <div class="ui-sm-12 ui-md-12 ui-lg-12">
                                            <p:outputLabel value="Note: Unit Price is calculated as (Std. Price * Multiplier) / Customer Markup. Customer Markup is not considered if entered 0 or blank."/>
                                        </div>
                                    </div>

                                    <!--                                    <p:column>
                                                                        
                                                                            </p:column>
                                                                        <p:spacer width="4"/>
                                                                        <p:column>
                                                                            //21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option
                                                                            <p:commandButton value="Add Row" title="Quick Add Line Items" action="#{quotesDtl.insertData()}"   actionListener="#{quotesDtl.clearQuotDtls()}" class="btn btn-primary btn-xs"
                                                                                             update=":quotefrom:gridQuoteDtls :quotefrom:hdrText  :quotefrom:tabQuot:delDtls :quotefrom:oppLnItmPrincipal :quotefrom:tabQuot:tblQuotDtls" disabled="#{quotesHdr.recId == 0 || !quotesHdr.editable}" 
                                                                                             oncomplete="forQuotSequence();timeoutFocus()"/>
                                                                            
                                                                            9806 Quote Refactoring:  Quotes Line item grid add Row - taking time
                                                                            //06-12-2022 :  : #9526 : Quote Refactoring: CRM-6259   Quotes: POBCO Quote Needs
                                                                            
                                                                        </p:column>-->
                                    <!--</div>-->
                                </p:outputPanel>
                            </f:facet>

                        </p:dataTable>
                    </p:tab>
                    <!--3269 Quotes > Linked Docs-->
                    <!--                    //       #11089 Quotes Code Optimisation : Quotes Slowness issues for a Linked Documents tab-->
                    <p:tab title="Linked Docs" id="linkedDoc"  disabled="#{quotesHdr.recId==0}">
                        <div class="ui-g ui-fluid header-bar">
                            <!--3269 Quotes > Linked Docs-->
                            <!--//       #11089 Quotes Code Optimisation : Quotes Slowness issues for a Linked Documents tab-->
                            <!--                        Feature #5747:CRM-4725: Want to UNLINK a quote from an oppty by harshithad  11/09/2021-->
                            <p:remoteCommand name="linkedDocsCmd" onstart="PF('DialogProcessDlg').show()"  autoRun="false"  actionListener="#{quotesLinkedDocumentsService.loadQuoteLinkeddocuments(quotesHdr.recId)}" action="#{quotesHdr.checkQuoteLinkedToJob(quotesHdr.recId)}" oncomplete="PF('lnQtOppTable').clearFilters();PF('DialogProcessDlg').hide()" update=":quotefrom quotefrom:tabQuot:lnQtOppMstList :quotefrom:tabQuot:cmdBtnJob"/>

                            <!--                          Feature #5564:CRM-3319: Quote to PO comm trans downflow 2/09/21 by harshithad-->
                            <!--                            //11-03-2024 : #13280 : CRM-7791   Quotes: Link multiple PO-->
                            <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Linked Doc Details </p:outputLabel>
                            </div>
                            <!--                            <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                            
                                                        </div>
                                                        <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                            
                                                        </div>-->
                            <!--#3551 CRM-2135: #5: Quotes > Line Items > Delete All-->
                            <!--                            Feature #5564:CRM-3319: Quote to PO comm trans downflow 2/09/21 by harshithad-->
                            <!--                            //11-03-2024 : #13280 : CRM-7791   Quotes: Link multiple PO-->
                            <div class="ui-sm-12 ui-md-8 ui-lg-8 ">
                                <p:outputPanel id="btns" style="float: right;margin-left: 2px">
                                    <!--Feature #5747:CRM-4725: Want to UNLINK a quote from an oppty by harshithad  11/09/2021-->
                                    <!--Feature #11571: CRM-7094: Quotes: copy into purchase orders by harshithad on 03/07/23-->
                                    <!--        //08-03-2024 : #13280 : CRM-7791   Quotes: Link multiple PO-->
                                    <p:commandButton  class="btn-info btn-xs"  id="quotLinkPo" actionListener="#{quotesHdr.loadPoList(quotesHdr.oppPrincipal,quotesHdr.oppCustomer,quotesHdr.recId)}"
                                                      value="Link to #{custom.labels.get('IDS_PURCHASE_ORDER')}"  title="Link to #{custom.labels.get('IDS_PURCHASE_ORDER')}"  
                                                      style=" width: 155px; " disabled="#{quotesHdr.oppPrincipal == 0 and quotesHdr.listQuotDtls.size() == 0}"/>
                                    <p:spacer width="4" />
                                    <p:commandButton id="cmdBtnPoGen" value="Generate PO" rendered="#{(quotesHdr.listQuotDtls.size()>0 and quotesHdr.isSuperQuote) or !quotesHdr.isSuperQuote}" actionListener="#{quotesHdr.isQuotLinkdToPo(quotesHdr.recId)}"   style=" width: 150px;" styleClass="btn btn-primary btn-xs"  update="poGenerateFrm poGenerateFrm:cmdBtnGnrte "/>
                                    <p:spacer width="4" />
                                    <p:commandButton rendered="#{quotesHdr.isSuperQuote==true}"  style=" width: 150px;" class="btn btn-info btn-xs"  disabled="#{!quotesHdr.editable || quotesHdr.listQuotDtls.size()==0 || quotesHdr.isOppLinked==true}"  value="Link to #{custom.labels.get('IDS_OPP')}" title="Link to #{custom.labels.get('IDS_OPP')}" id="lnkSprQt" 
                                                     oncomplete="PF('oppSprQtMstDlg').show(); $('#frmSprQtOppMst\\:dtSprQtOppMstList\\:custName\\:filter').val($('#quotefrom\\:tabQuot\\:oppCustomer').val()); $('#frmSprQtOppMst\\:dtSprQtOppMstList\\:distriName\\:filter').val($('#quotefrom\\:tabQuot\\:oppDistributor').val());"   
                                                     actionListener="#{quotesHdr.resetLinkLookup()}" action="#{quotesHdr.loadCompanies()}" process="@this" update=":frmSprQtOppMst:dtSprQtOppMstList :frmSprQtOppMst:sprQtPrinci :frmSprQtOppMst"/>
                                    <p:spacer width="4" />


                                    <!--3269 Quotes > Linked Docs-->
                                    <p:commandButton  rendered="#{quotesHdr.isSuperQuote==false}" class="btn-info btn-xs"  id="lnk"
                                                      disabled="#{quotesHdr.oppId!=0}"   
                                                      oncomplete="PF('oppQtMstDlg').show(); $('#frmQtOppMst\\:dtQtOppMstList\\:custName\\:filter').val($('#quotefrom\\:tabQuot\\:oppCustomer').val()); $('#frmQtOppMst\\:dtQtOppMstList\\:distriName\\:filter').val($('#quotefrom\\:tabQuot\\:oppDistributor').val()); " process="@this"
                                                      actionListener="#{quotesHdr.loadOppList('QUOTE', quotesHdr.oppPrincipal)}" 
                                                      value="Link to #{custom.labels.get('IDS_OPP')}"  title="Link to #{custom.labels.get('IDS_OPP')}"  
                                                      style=" width: 150px; "  update=":quotefrom:btns :dlgQtoppMst :frmQtOppMst :frmQtOppMst:dtQtOppMstList :frmQtOppMst:updLnItmReq"/>
    <!--                            <p:commandButton class="btn-success btn-xs" rendered="#{quotesHdr.isSuperQuote==true and quotesHdr.clonedtls==false and  quotesHdr.clnSts==0}" icon="fa fa-link" id="spQtlnk" disabled="#{!quotesHdr.editable || quotesHdr.listQuotDtls.size()==0 || quotesHdr.isOppLinked==true}" 
                                                oncomplete="PF('confirBulkQuot').show()" value="Bulk generate #{custom.labels.get('IDS_OPP')}"   title="Bulk generate #{custom.labels.get('IDS_OPP')}" style="float: right; width: 30px; "/>-->


                                    <!--3269 Quotes > Linked Docs-->
                                    <p:spacer width="4" />
        <!--                            <p:commandButton  rendered="#{quotesHdr.disableOpp and quotesHdr.oppId != 0}" disabled="#{quotesHdr.quotJob !=0}"  
                                                    style="float: right; width: 150px;" value="Link to #{custom.labels.get('IDS_JOB')}" title="Link to #{custom.labels.get('IDS_JOB')}"
                                                    class="btn btn-primary btn-xs"   action="#{jobs.fetchJobsListForOpps()}"  process="@this"
                                                    update=":frmJobList:dtJobList"   oncomplete="PF('dlgQuotJobList').show(); $('#frmJobList\\:dtJobList\\:col1\\:filter').val($('#quotefrom\\:tabQuot\\:qTopic').val()); PF('dtJobList').filter();"/>-->

                                    <!--Feature #5747:CRM-4725: Want to UNLINK a quote from an oppty by harshithad  11/09/2021-->
                                    <p:commandButton  id="cmdBtnJob" rendered="#{quotesHdr.recId > 0}" disabled="#{quotesHdr.quotJob !=0}"  
                                                      style=" width: 150px;" value="Link to #{custom.labels.get('IDS_JOB')}" title="Link to #{custom.labels.get('IDS_JOB')}"
                                                      class="btn btn-primary btn-xs"   actionListener="#{jobs.fetchJobsListForOpps()}"  process="@this"
                                                      update=":frmJobList:dtJobList"   oncomplete="PF('dlgQuotJobList').show(); $('#frmJobList\\:dtJobList\\:col1\\:filter').val($('#quotefrom\\:tabQuot\\:qTopic').val()); PF('dtJobList').filter();"/>

                                </p:outputPanel>
                            </div>
                        </div>  
    <!--                        <p:dataTable id="lnQtOppMstList" widgetVar="oppLnMstListTable" value="#{quotesHdr.oppsLnList}" var = "oppL" 
                                     selectionMode="single" rowKey="#{oppL.oppId}"
                                     emptyMessage="No linked #{custom.labels.get('IDS_OPPS')} found." 
                                     style="table-layout: fixed" 
                                     paginatorAlwaysVisible="false"
                                     paginatorPosition="top"
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                     rows="5" multiViewState="true" paginator="true" >
                            <p:ajax event="rowSelect" listener="#{viewOppList.onselectQtOpps}" oncomplete="PF('oppQtMstDlg').hide()"  update="quotefrom quotefrom:vwlnk quotefrom:lnk" />
                            <p:column filterBy="#{oppL.custName}" id="custName" filterMatchMode="contains" sortBy="#{oppL.custName}" headerText="#{custom.labels.get('IDS_CUSTOMER')}">
                                 <h:link outcome="OpportunityView.xhtml?opid=#{oppL.oppId}" value="#{oppL.custName}"></h:link>
                                <h:link  value="#{oppL.custName}" outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{oppL.oppId}" >
                                         <f:param name="frm" value="#{view.viewId}" /> 
                                </h:link> 
                                Type
    Doc. No.
    Doc. Date
    Topic
    Customer
    Principal
    Value
                            </p:column>
                            <p:column filterBy="#{oppL.principalName}" id="principalName" filterMatchMode="contains" sortBy="#{oppL.principalName}"  headerText="#{custom.labels.get('IDS_PRINCI')}">
                                <h:link  value="#{oppL.principalName}" outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{oppL.oppId}" >
                                </h:link> 
                            </p:column> 
                            <p:column filterBy="#{oppL.distriName}" id="distriName" filterMatchMode="contains" sortBy="#{oppL.distriName}"  headerText="#{custom.labels.get('IDS_DISTRI')}">
                                <h:link  value="#{oppL.distriName}" outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{oppL.oppId}" >
                                </h:link> 
                            </p:column>
                            <p:column filterBy="#{oppL.oppCustProgram}" filterMatchMode="contains" sortBy="#{oppL.oppCustProgram}"  headerText="#{custom.labels.get('IDS_PROGRAM')}">
                                <h:link  value="#{oppL.oppCustProgram}" outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{oppL.oppId}" >
                                </h:link> 
                            </p:column> 
                            <p:column filterBy="#{oppL.oppActivity}" filterMatchMode="contains" sortBy="#{oppL.oppActivity}"  headerText="#{custom.labels.get('IDS_ACTIVITY')}">
                                <h:link  value="#{oppL.oppActivity}" outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{oppL.oppId}" >
                                </h:link> 
                            </p:column>
                            <p:column filterBy="#{oppL.oppStatus}" filterMatchMode="contains" sortBy="#{oppL.oppStatus}"  headerText="Status">
                                <h:link  value="#{oppL.oppStatus}" outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{oppL.oppId}" >
                                </h:link> 
                            </p:column>
                            <p:column filterBy="#{oppL.oppFollowUp}" filterMatchMode="contains" sortBy="#{oppL.oppFollowUp}"  headerText="#{custom.labels.get('IDS_FOLLOW_UP')}">
    <p:outputLabel value="#{oppL.oppFollowUp}" ><f:convertDateTime pattern="#{globalParams.dateFormat}" /></p:outputLabel>
                                1637 Quotes > Convert to Opp -  link to Job if the source quote is linked to Job
                                <h:link value="#{globalParams.formatDateTime(oppL.oppFollowUp,'da')}" outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{oppL.oppId}" >
                                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                </h:link>                                 
                            </p:column>
                            <p:column filterBy="#{oppL.oppNextStep}" filterMatchMode="contains" sortBy="#{oppL.oppNextStep}" headerText="Next Step">
                                <h:link  value="#{oppL.oppNextStep}" outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{oppL.oppId}" >
                                </h:link> 
                            </p:column>
                            1667 CRM-3223: Quotes > Opps tab
                            <p:column  filterMatchMode="contains" sortBy="#{oppL.oppCloseStatus}">
                                <f:facet name="header">Closed Status</f:facet> 
                                <p:outputLabel value="#{oppL.oppCloseStatus==0?'Open':'Closed'}" />
                            </p:column>
                        </p:dataTable>-->
                        <!--3269 Quotes > Linked Docs-->
                        <!--                        Feature #5747:CRM-4725: Want to UNLINK a quote from an oppty by harshithad  11/09/2021-->
                        <!--//27-09-2023 : #12180 : CRM-7324 Include cloned/parent quotes in the quote linked docs tab-->
                        <p:dataTable id="lnQtOppMstList" value="#{quotesLinkedDocumentsService.quotLinkedDocLst}" 
                                     var="lnkdDoc"
                                     scrollHeight="300"                                     
                                     tableStyle="table-layout:auto;"
                                     widgetVar="lnQtOppTable" 
                                     selection="#{quotesLinkedDocumentsService.selectdQuotLnkdDoc}"
                                     filteredValue="#{quotesLinkedDocumentsService.filteredQuotLnkdDoc}"
                                     draggableColumns="true"
                                     emptyMessage="No linked documents found."  
                                     scrollable="true"
                                     class="tblmain"
                                     rowKey="#{lnkdDoc.recId}" 
                                     >

                            <!--11328 : 26/05/2023: ESCALATIONS CRM-7020   Autoquotes Fixes-->
                            <p:column headerText="Type" filterBy="#{lnkdDoc.docType}" filterMatchMode="contains" sortBy="#{lnkdDoc.docType}">
                                <!--//27-09-2023 : #12180 : CRM-7324 Include cloned/parent quotes in the quote linked docs tab-->
                                <p:commandLink rendered="#{lnkdDoc.docType eq 'Quote'}"  value="#{lnkdDoc.docType}"
                                               onclick="window.open('../../opploop/quotes/NewQuote.xhtml?recId=#{lnkdDoc.docId}', '_blank');">
                                </p:commandLink>
                                <p:commandLink rendered="#{lnkdDoc.docType eq 'Opportunity'}"  value="#{custom.labels.get('IDS_OPP')}"
                                               onclick="window.open('../../opploop/opportunity/OpportunityView.xhtml?opid=#{lnkdDoc.docId}', '_blank');" 
                                               >
                                </p:commandLink>

                                <p:commandLink rendered="#{lnkdDoc.docType eq 'Job'}"  value="#{custom.labels.get('IDS_JOB')}"
                                               onclick="window.open('../../opploop/jobs/JobsView.xhtml?id=#{lnkdDoc.docId}', '_blank');"
                                               >
                                </p:commandLink>
                                <p:commandLink rendered="#{lnkdDoc.docType eq 'Project'}"  value="#{custom.labels.get('IDS_PROJ')}"
                                               onclick="window.open('../../opploop/projects/ProjectView.xhtml?id=#{lnkdDoc.docId}', '_blank');"
                                               >
                                </p:commandLink>
                                <!--                                Feature #5564:CRM-3319: Quote to PO comm trans downflow 2/09/21 by harshithad-->
                                <p:commandLink rendered="#{lnkdDoc.docType eq custom.labels.get('IDS_PURCHASE_ORDER')}"  value="#{custom.labels.get('IDS_PURCHASE_ORDER')}"
                                               onclick="window.open('../../opploop/po/PoDetails.xhtml?id=#{lnkdDoc.docId}', '_blank');"
                                               >
                                </p:commandLink><!--
            
                                <p:commandLink rendered="#{lnkdDoc.docType eq 'Comm. Trans.'}"  value="#{lnkdDoc.docType}"
                                               onclick="window.open('../../datamanagement/commtransactions/CommtransDetails.xhtml?id=#{lnkdDoc.docId}', '_blank');"
            
                                               >
                                </p:commandLink>-->

                            </p:column>
                            <p:column headerText="Doc. No." filterBy="#{lnkdDoc.docNo}" filterMatchMode="contains" sortBy="#{lnkdDoc.docNo}">
                                <h:outputText value="#{lnkdDoc.docNo}" />
                            </p:column>
                            <p:column headerText="Doc. Date" filterBy="#{oppLinkedDocumentsService.getFormattedDate(lnkdDoc.docDate, 'da')}" filterMatchMode="contains" sortBy="#{lnkdDoc.docDate}">
                                <h:outputText value="#{oppLinkedDocumentsService.getFormattedDate(lnkdDoc.docDate, 'da')}" />
                            </p:column>
                            <p:column headerText="Topic" filterBy="#{lnkdDoc.docTopic}" filterMatchMode="contains" sortBy="#{lnkdDoc.docTopic}">
                                <h:outputText value="#{lnkdDoc.docTopic}" />
                            </p:column>
                            <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{lnkdDoc.docCustomer}" filterMatchMode="contains" sortBy="#{lnkdDoc.docCustomer}">
                                <h:outputText value="#{lnkdDoc.docCustomer}" />
                            </p:column>
                            <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{lnkdDoc.docPrinci}" filterMatchMode="contains" sortBy="#{lnkdDoc.docPrinci}">
                                <h:outputText value="#{lnkdDoc.docPrinci}" />
                            </p:column>
                            <p:column headerText="Value" filterBy="#{lnkdDoc.docValue}" filterMatchMode="contains" sortBy="#{lnkdDoc.docValue}">
                                <h:outputText value="#{lnkdDoc.docValue}" style="float: right" />
                            </p:column>
                            <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
                            <p:column headerText="Linked Doc" style="text-align: center!important;">
                                <p:commandButton style="height:25px;width:25px;" 
                                                 styleClass="btn-primary  btn-xs" title="Linked Doc" icon="fa fa-eye"
                                                 actionListener="#{linkedDocService.loadQuotLinkedData(lnkdDoc.docType,lnkdDoc.docId)}"
                                                 action="#{linkedDocService.loadHeader(lnkdDoc.docType,lnkdDoc.docNo)}"
                                                 update="linkedDocumentForm:linkedDocumentDialogue" 
                                                 oncomplete="PF('linkedDocumentDlgVar').show();"
                                                 ></p:commandButton>
                            </p:column>
                            <!--Feature #5747:CRM-4725: Want to UNLINK a quote from an oppty by harshithad  11/09/2021-->
                            <p:column id="colUnlinkRec" style="width: 50px">                                              
                                <p:commandLink id="cmnLnkUnlink" title="Unlink" rendered="#{lnkdDoc.docType eq 'Opportunity' or lnkdDoc.docType eq 'Job' or lnkdDoc.docType eq custom.labels.get('IDS_PURCHASE_ORDER') }" action="#{quotesDtl.populateLnkdRec(lnkdDoc)}" oncomplete="PF('cnfrmUnlinkDlg').show()" update="quotefrom:cnfrmUnlnk " >
                                    <h:graphicImage name="unlink.png" library="images"  height="20" width="20"/> 
                                </p:commandLink>
                            </p:column>                             
                        </p:dataTable>
                    </p:tab>

                    <!--1329 IS Quotes > CRM Sync Tab > Update from IS Quote-->
                    <!--11-08-2023 11857 CRMSYNC-359 Sync Logic. Quote. RF UI-->
                    <p:tab id="quoteTabCrmsync" title="CRM Sync"  disabled="#{not quotesHdr.crmsyncTabEnabled}">  
                        <ui:include src="tabs/CRMSync.xhtml" />
                    </p:tab>

                    <!--#6214: CRM-4926: Add "Attachments" tab to Quotes -poornima-->
                    <p:tab title="Attachments"  disabled="#{quotesHdr.recId==0}" id="qAtttachment" >

                        <ui:include src="dialog/QuoteAttachments.xhtml"   />
                    </p:tab>
                    <!--#9564  CRM-6289  Quotes: show linked emails in quote detail with an emails tab-->
                    <p:tab id="quotEmail" title="Emails" disabled="#{quotesHdr.recId==0}" >
                        <ui:include src="dialog/QuoteEmails.xhtml"/>
                    </p:tab>
                    <!--//21-08-2024 : #14330 : CRM-8376 : Quote form updates for internal comments-->
                    <p:tab id="quotComments" title="Comments" disabled="#{quotesHdr.recId==0}" >
                        <ui:include src="tabs/QuoteComments.xhtml"/>
                    </p:tab>
                </p:tabView>
                <!--</p:dialog>-->
                <!--#3243 Quotes > Updates to Quote Owner-->
                <!--//27-10-2022 : #9470 : PRIORITY : Quote PDF settings-->
                <p:confirmDialog id="confirmPdfDlg" header="PDF" global="false"  widgetVar="pdf" class="dialogCSS" > 
                    <!--        <h:form id="confirmationDlg"> -->

                    <f:facet name="message">
                        <p:outputLabel value="Do you want download or send the generated pdf by email?"  />
                        <!--//27-10-2022 : #9470 : PRIORITY : Quote PDF settings-->
                        <p:outputPanel rendered="#{quotesHdr.quotCustomApp == 1}">
                            <br/><br/>
                        </p:outputPanel>

                        <p:panelGrid rendered="#{quotesHdr.quotCustomApp == 1}" columns="3" columnClasses="ui-grid-col-4,ui-grid-col-6,ui-grid-col-1"
                                     layout="grid" styleClass="box-primary no-border ui-fluid">
                            <p:outputLabel id="outptUseTamplate" value="Use Template"/>
                            <p:selectOneMenu id="dropDownUseTamp" value="#{quotesHdr.useTamplate}" 
                                             class="ui-select-btn">
                                <f:selectItem itemLabel="None" itemValue="0" />
                                <f:selectItems value="#{quotesHdr.tamplateList}"/>
                                <p:ajax event="itemSelect" onstart="PF('dlgProcessingOnTask').show();" update=":quotefrom:outputPanelDownload :quotefrom:btnPreview" oncomplete="PF('dlgProcessingOnTask').hide();"/>
                            </p:selectOneMenu>
                            <p:commandButton id="btnPreview" icon="fa fa-gear" styleClass="btn-primary  btn-xs" 
                                             style="float:right;width:30px;height:25px;" title="Template Settings"
                                             actionListener="#{quotesHdr.openPreviewLink(quotesHdr.useTamplate)}"
                                             />
                        </p:panelGrid>
                        <br/><br/>
<!--                        <p:selectBooleanCheckbox value="#{quotesHdr.isNotifyOwner}"  itemLabel="Notify Quote Owner #{quotesHdr.getUserById(quotesHdr.quotOwner)}" id="chkStatus"  >
                                                    <p:ajax update="chkStatus" listener="#{users.chkStatusAction()}"/>
                        </p:selectBooleanCheckbox>   -->


                    </f:facet>
                    <p:outputPanel id="outputPanelDownload">
                        <div align="center">
                            <p:commandButton id="btnNormalDownload" value="Download" class="btn btn-primary btn-xs " rendered="#{quotesHdr.useTamplate == 0}" action="#{quotesHdr.createPDF(1)}" ajax="false" oncomplete="PF('pdf').hide()"  /> 
                            <p:commandButton id="btnTemplateDownload" value="Download" class="btn btn-primary btn-xs " rendered="#{quotesHdr.quotCustomApp == 1 and quotesHdr.useTamplate > 0}" actionListener="#{quotesHdr.quoteFilePath(quotesHdr.recId,quotesHdr.useTamplate,1)}" ajax="false" oncomplete="PF('pdf').hide()">
<!--                                <p:fileDownload value="#{quotesHdr.fileDownload}"/>-->
                            </p:commandButton>
                            <p:spacer width="4"/>
                            <!--<p:commandButton value="Send by Email"  class="btn btn-primary btn-xs "   action="#{quotesHdr.createPDF(0)}" oncomplete="PF('pdf').hide()" />--> 

                            <p:commandButton id="btnNormalSendByEmail" value="Send by Email"  action="#{quotesHdr.createPDF(0)}" class="btn btn-primary btn-xs " oncomplete="PF('pdf').hide(); window.open('#{globalParams.emailUrl}?fn=sendquotes&amp;reqid=#{loginBean.randomUid}&amp;autocheckemail=1&amp;quote_id=#{quotesHdr.recId}', '_blank')"  
                                             rendered="#{quotesHdr.recId!=0 and quotesHdr.useTamplate == 0}"   /> 
                            <p:commandButton id="btnTemplateSendByEmail" value="Send by Email"  action="#{quotesHdr.quoteFilePath(quotesHdr.recId,quotesHdr.useTamplate,0)}" class="btn btn-primary btn-xs " oncomplete="PF('pdf').hide(); window.open('#{globalParams.emailUrl}?fn=sendquotes&amp;reqid=#{loginBean.randomUid}&amp;autocheckemail=1&amp;quote_id=#{quotesHdr.recId}', '_blank')"  
                                             rendered="#{quotesHdr.recId!=0 and quotesHdr.quotCustomApp == 1 and quotesHdr.useTamplate > 0}"   /> 
                            <!--21-05-2022 7965 OnTask Integration: Quote > Send to onTask-->
                            <p:spacer width="4"/> 
<!--                            <p:commandButton value="Send for Approval" class="btn btn-primary btn-xs " rendered="#{not empty onTaskService.workflowId}"
                                             actionListener="#{onTaskService.checkMandFieldsForOntask(quotesHdr.oppCustomer,quotesHdr.oppCustomerCont,quotesHdr.listQuotDtls.size())}"
                                             oncomplete="openOnTaskDlg();"
                                             update=":frmAurNewCompLookupHead  :formDlgQuoteOntaskProc"/>-->

                        </div> 
                    </p:outputPanel>
                    <!--</h:form>--> 
                </p:confirmDialog>


                <!--//16-03-2023 : #10892 : CRM-6737 Quote Clone: reprice according to the new customer selected-->
                <p:confirmDialog header="Alert" width="400" global="false" message="Quote cloned" widgetVar="cloneAlert" > 
                    <!--        <h:form id="confirmationDlg"> -->
                    <p:commandButton value="OK" class="btn btn-primary btn-xs " oncomplete="PF('cloneAlert').hide()"/> 
                    <!--</h:form>--> 
                </p:confirmDialog>
                <!--#12168 CRM-7323 Quotes: refresh pricing on quotes-->
                <p:confirmDialog id="cnfrmRecalPricing" width="400"  header="Confirmation"
                                 message="Do you wish to recalculate Pricing for #{quotesHdr.selectedquotDtl eq null or quotesHdr.selectedquotDtl.size() ==0 ? 'all' : 'selected' } line items based on the latest pricing ?"
                                 widgetVar="RecalcPricingWidgVar" >
                    <div class="div-center">

                        <p:commandButton value="Proceed"
                                         class="btn btn-success btn-xs" process="@this" actionListener="#{quotesHdr.recalculatePricing(quotesHdr.selectedquotDtl)}" action="#{quotesHdr.clearLineItmSelectn()}"    oncomplete="PF('RecalcPricingWidgVar').hide()" update=":quotefrom:tabQuot:tblQuotDtls"   />

                        <p:spacer width="4"/>
                        <p:commandButton value="Cancel" process="@this"  class="btn btn-warning btn-xs" actionListener="#{quotesHdr.clearLineItmSelectn()}" update=":quotefrom:tabQuot:tblQuotDtls"    oncomplete="PF('RecalcPricingWidgVar').hide()" />

                    </div>
                </p:confirmDialog>


                <!--Task#3918-CRM-4017: quote2opp conversion - cherry pick which lines to generate an opp for from a quote  22-03-2021 by harshithad-->
                <p:confirmDialog id="cnfrmDlgLineItm" header="Generate #{custom.labels.get('IDS_OPPS')}"  width="400" global="false"
                                 message="#{quotesHdr.message}"
                                 widgetVar="confirBulkQuot" >
                    <div class="div-center">
                        <!--1637 Quotes > Convert to Opp -  link to Job if the source quote is linked to Job-->

                        <!--                        Task#3918-CRM-4017: quote2opp conversion - cherry pick which lines to generate an opp for from a quote  22-03-2021 by harshithad-->
                        <p:commandButton value="Proceed" actionListener="#{quotesHdr.bulkGenerateOpp()}"
                                         class="btn btn-success btn-xs" process="@this"  oncomplete="PF('confirBulkQuot').hide()" update="quotefrom:tabQuot:lnQtOppMstList quotefrom:tabQuot:tblQuotDtls "    />

                        <p:spacer width="4"/>
                        <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"   actionListener="#{quotesHdr.clearLineItmSelectn()}"   oncomplete="PF('confirBulkQuot').hide()" 
                                         />
                    </div>
                </p:confirmDialog>
                <!--Feature #5747:CRM-4725: Want to UNLINK a quote from an oppty by harshithad  11/09/2021-->

                <p:confirmDialog id="cnfrmUnlnk" header="Confirmation" widgetVar="cnfrmUnlinkDlg" message="Are you sure you want to unlink?">                  
                    <div style="text-align: center">
                        <!--                        Feature #5564:CRM-3319: Quote to PO comm trans downflow Harshitha 4/10/21-->
                        <!--//21-07-2022 : #8522  : Direct Request : Quotes add line item should add blank row for grid edit option-->
                        <!--12716: 15/12/2023: Incidental Finding: CRM:7608:Unlinking All linked Opportunities when unlinking one opp in super quot-->
                        <!--23-01-2024 12761 CRM-7599   OASIS: Surface Project Rank for Quotes-->
                        <p:commandButton id="btnUnlinkYes"  
                                         value="Yes" actionListener="#{quotesDtl.unlinkFromQuotes()}"  update=":quotefrom:btns quotefrom:tabQuot:lnQtOppMstList quotefrom:tabQuot:lnk quotefrom:tabQuot:lnkSprQt quotefrom:tabQuot:cmdBtnJob :quotefrom:tabQuot:tblQuotDtls"
                                         oncomplete="refreshQuoteRank();PF('cnfrmUnlinkDlg').hide();PF('lnQtOppTable').filter();"
                                         immediate="true"   styleClass="ui-confirmdialog-yes btn btn-success btn-xs"/>
                        <p:spacer width="4px"/>
                        <p:commandButton id="btnUnlinkNo" value="No" onclick="PF('cnfrmUnlinkDlg').hide();" 
                                         styleClass="ui-confirmdialog-no btn btn-danger btn-xs"/>
                    </div>
                </p:confirmDialog>
                <!--                Feature #5564:CRM-3319: Quote to PO comm trans downflow  07-09-2021 by harshithad-->
                <p:confirmDialog id="cnfrmGnrtPo" header="Confirmation" widgetVar="cnfrmGenPoDlg" message="PO is already linked to the Quote. Do you want to generate a new PO?">
                    <div style="text-align: center">
                        <p:commandButton id="btnYess" 
                                         value="Yes"  actionListener="#{quotesHdr.loadPoGenrateDefault()}" 
                                         oncomplete="PF('cnfrmGenPoDlg').hide();PF('dlgGenpo').show();" update="poGenerateFrm poGenerateFrm:cmdBtnGnrte "
                                         styleClass="ui-confirmdialog-yes btn btn-success btn-xs"/>
                        <p:spacer width="4px"/>
                        <p:commandButton id="btnNoo" value="No" onclick="PF('cnfrmGenPoDlg').hide();" 
                                         styleClass="ui-confirmdialog-no btn btn-danger btn-xs"/>
                    </div>
                </p:confirmDialog>
                <!--Feature #11571: CRM-7094: Quotes: copy into purchase orders by harshithad on 03/07/23-->
                <p:confirmDialog id="cnfrmSprQtGnrtPo" header="Confirmation" widgetVar="cnfrmSprQtGnrtDlg" message="PO is already linked to the Quote. Do you want to generate a new PO?">
                    <div style="text-align: center">
                        <p:commandButton id="btnGnYes" 
                                         value="Yes"  actionListener="#{quotesHdr.generatePo(1)}" 
                                         oncomplete="PF('cnfrmSprQtGnrtDlg').hide();PF('dlgGenpo').hide();PF('lnQtOppTable').clearFilters();" update="poGenerateFrm:cmdBtnGnrte  quotefrom:tabQuot:lnQtOppMstList poGenerateFrm"
                                         styleClass="ui-confirmdialog-yes btn btn-success btn-xs"/>
                        <p:spacer width="4px"/>
                        <p:commandButton id="btnGnNo" value="No" onclick="PF('cnfrmSprQtGnrtDlg').hide();" 
                                         styleClass="ui-confirmdialog-no btn btn-danger btn-xs"/>
                    </div>
                </p:confirmDialog>

                <ui:include src="dialog/QuoteDtls.xhtml" />
                <!--//04-08-2023 : #11805 : CRM-7169: Quotes: Other Parties Features-->
                <ui:include src="../opportunity/tabs/dialogs/contacts/dlgContAll.xhtml" />


                <!--                March 23 2020-->
                <ui:include src="dialog/CRMSync.xhtml" />
                <!--780 Create Quotes from IS Quotes with Header and Line Items-->
                <!--2820 CRM-3684: bda-in: error message on CRMsync button ridiculously fast and unusable-->
                <p:growl id="valerrg" globalOnly="false" keepAlive="true" >
                    <p:autoUpdate />
                </p:growl>

            </h:form>

            <!--11-08-2023 11857 CRMSYNC-359 Sync Logic. Quote. RF UI-->
            <ui:include src="dialog/ChooseAsMaster.xhtml"/>
            <ui:include src="dialog/TargetSystemQuoteLookup.xhtml"/>
            <ui:include src="dialog/QuoteCompaniesAlias.xhtml"/>
            <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
            <ui:include src="dialog/LinkedDocDLg.xhtml"/>

            <!--//17-02-2023 : #10734 : ESCALATIONS CRM-6704  Quotes: Unable to import excel file-->
            <ui:include src="dialog/ImportQuoteDtls.xhtml" /> 
            <!--        //08-03-2024 : #13280 : CRM-7791   Quotes: Link multiple PO-->
            <ui:include src="dialog/QuotLinkedToPo.xhtml" />
            <ui:include src="dialog/QuotLinkSuperPo.xhtml" />
            <!--//06-10-2022 : #9226: : CRM-6137  quote: update selected for multi row updates on multipler, qty and lead time-->
            <h:form id="formQuotMultipleAdd">
                <p:dialog id="quotMultipleAdd"  widgetVar="quotMultipleAdd" width="450"  modal="true" class="dialogCSS" 
                          header="Update Selected" resizable="false">
                    <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                 layout="grid" styleClass="box-primary no-border ui-fluid">
                        <p:outputLabel value="Qty:" id="qtyLabel" />
                        <p:inputNumber id="qtyInput"  value="#{quotesDtl.multipleQty}" 
                                       maxlength="9" maxValue="999999999" decimalPlaces="3"
                                       thousandSeparator=","/>
                        <p:outputLabel value="Lead Time:" id="leadTimeLabel" />
                        <p:inputText id="leadTimeInput"  value="#{quotesDtl.multipleLeadTime}"  />
                        <p:outputLabel value="Multiplier:" id="multiplierLabel" />
                        <p:inputNumber id="multiplierInput"  value="#{quotesDtl.multipleMultiplier}" 
                                       maxlength="9" maxValue="999999999" decimalPlaces="6"
                                       thousandSeparator="," />
                    </p:panelGrid>
                    <p:spacer width='4px' />
                    <div class="div-center" >
                        <!--#13013 Quote Module Weight Calculation Inaccuracy-->
                        <p:commandButton  id="btnSave"  value="Update" actionListener="#{quotesDtl.saveMultipleValues(quotesHdr.selectedquotDtl)}"    styleClass="btn btn-success  btn-xs"
                                          update=":quotefrom:tabQuot:tblQuotDtls" oncomplete="PF('quotMultipleAdd').hide();PF('tblQuotDtls1').unselectAllRows()"/>
                        <p:spacer width="4px"/>
                        <p:commandButton id="btnCancel"  value="Cancel"  oncomplete="PF('quotMultipleAdd').hide();PF('tblQuotDtls1').unselectAllRows()" styleClass="btn btn-warning btn-xs" />
                    </div>
                </p:dialog>
            </h:form>
            <!--//               #11355 ESCALATIONS CRM-7034   Repfabric: Updating existing quote with new price-->
            <!--//16-03-2023 : #10892 : CRM-6737 Quote Clone: reprice according to the new customer selected-->
            <h:form id="formQuotClone">
                <p:dialog id="quotCloneDlg"  widgetVar="quotCloneDlg" width="500"  modal="true" class="dialogCSS" 
                          resizable="false" header="Clone quote">
                    <!--//14-04-2023 : #11072 : CRM-6737: Quotes: Customer Special Price-Clone Quote,  Quote Value is not updated.-->
                    <!--//25-09-2023 : #12159 : CRM-7324   quotes: version history or clone to a new version to the same customer-->
                   <p:panelGrid id="defCloneQuotPanel" columns="2"  columnClasses="ui-grid-col-4,ui-grid-col-8"
                                 layout="grid" styleClass="box-primary no-border ui-fluid" style="margin-start:-10px" >
                        <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}:" for="quotCloneCustomer"/>
                        <h:panelGroup class="ui-inputgroup" >
                            <!--//09-06-2023 : #11397 : ESCALATIONS CRM-7056   Quotes Module: unable to set a default customer when cloning-->
                            <p:inputText id="quotCloneCustomer" value="#{quotesHdr.defCloneCustomer}" placeholder="Select #{custom.labels.get('IDS_CUSTOMER')}" 
                                         readonly="true"/>
                            <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                              actionListener="#{viewCompLookupService.listLookUpvalues('quotCloneRemote',5 )}" 
                                              update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                              styleClass="btn-info btn-xs"/>

                        </h:panelGroup>
                        <p:outputLabel value="Clone as child quote"/>
                        <!--//25-09-2023 : #12159 : CRM-7324   quotes: version history or clone to a new version to the same customer-->
                        <p:selectBooleanCheckbox value="#{quotesHdr.defCloneQuotNumBol}">
                            <p:ajax event="change" update=":formQuotClone:defCloneQuotPanel" listener="#{quotesHdr.quotAsChildDefault()}" />
                        </p:selectBooleanCheckbox>
                        <p:outputLabel value="Quote Number" rendered="#{quotesHdr.defCloneQuotNumBol}"/>
                        <!--//        #14910 CRM-8638 : quote: when cloning as a child, automatically increment the child quote-->
                        <!--<h:panelGroup class="ui-inputgroup" id="quotCloneDefQuotNumber" >-->
                        <p:inputText disabled="#{globalParams.incrementChildQuoteNumber()}"  value="#{quotesHdr.defaultQuotValue}" rendered="#{quotesHdr.defCloneQuotNumBol}"  maxlength="30"/>
<!--                                        <p:commandButton class="btn-success btn-xs" icon="fa fa-refresh" action="#{quotesHdr.defQuotNumberRefresh()}" 
                                         style="width: 67px;"    update=":formQuotClone:quotCloneDefQuotNumber" />
                    </h:panelGroup>-->
                        <!---   //  #14381 : CRM:8360 Quotes in Clone tab added a new feature of "Include Attachments"-->         
                        <p:outputLabel value="Include attachments"/>
                        <!--//25-09-2023 : #12159 : CRM-7324   quotes: version history or clone to a new version to the same customer-->
                        <p:selectBooleanCheckbox value="#{quotesHdr.defQuoteAttachments}">

                        </p:selectBooleanCheckbox>
                    </p:panelGrid>
                    <!--//               #11355 ESCALATIONS CRM-7034   Repfabric: Updating existing quote with new price-->
                    <p:panelGrid columns="2" style="margin-top:10px"  columnClasses="ui-grid-col-1,ui-grid-col-12"
                                 layout="grid" styleClass="box-primary no-border ui-fluid" >
                        <p:spacer width="6px"/>
                        <h:panelGroup class="ui-inputgroup" >
                            <p:selectBooleanCheckbox value="#{quotesHdr.defCustomerPrice}">
                                <p:ajax event="change"/>
                            </p:selectBooleanCheckbox> 
                            <p:spacer width="4px"/>
                            <p:outputLabel value="#{quotesHdr.headerCustomer}" />
                        </h:panelGroup>

                    </p:panelGrid>
                    <p:spacer width='4px' />
                    <div class="div-center" style="margin-top:10px;" >
                        <p:commandButton  id="btnSaveClone" 
                                          value="Clone" widgetVar="btnSaveCloneWidget" styleClass="btn btn-success  btn-xs"    
                                          onclick="PF('btnSaveCloneWidget').disable();"
                                          oncomplete="PF('btnSaveCloneWidget').enable();"
                                          actionListener="#{quotesHdr.cloneQuotCustomer()}" 
                                          update=":quotefrom :quotefrom:tabQuot:tblQuotDtls :quotefrom:tabQuot:gridQuote :quotefrom:btns :quotefrom:tabQuot:addDtls :quotefrom">
                        </p:commandButton> 
                        <p:spacer width="4px"/>
                        <p:commandButton id="btnCancelClone"  value="Cancel"  oncomplete="PF('quotCloneDlg').hide();" styleClass="btn btn-warning btn-xs" />
                    </div>
                </p:dialog>
            </h:form>

            <h:form id="formQuotPdf">
                <p:dialog id="quotPdf"  widgetVar="quotPdf" width="450"  modal="true" class="dialogCSS" 
                          resizable="false" header="PDF">
                    <p:outputLabel value="Do you want download or send the generated pdf by email?"  /> 
                    <p:outputPanel rendered="#{quotesHdr.quotCustomApp == 1}">
                        <br/>
                    </p:outputPanel>
                    <!--//24-11-2022 : #9852 : Quote Refactoring:  Quotes > Generate PDF > PDF dialog-->
                    <p:panelGrid columns="3" rendered="#{quotesHdr.quotCustomApp == 1}" columnClasses="ui-grid-col-6,ui-grid-col-5,ui-grid-col-1"
                                 layout="grid" styleClass="box-primary no-border ui-fluid" style="margin-left:-10px;">
                        <p:outputLabel value="#{quotesHdr.oppPrincipal != null and quotesHdr.oppPrincipal > 0 ? 'Use regular quote template' : 'Use super quote template'}"/>
                        <p:selectOneMenu value="#{quotesHdr.stringUseTemplate}" 
                                         class="ui-select-btn" style="width:157px;">
                            <f:selectItem itemLabel="None" itemValue="0" />
                            <f:selectItems value="#{quotesHdr.tamplateList}"/>
                            <!--Task #11009 CRM-6719 Quote : implement export to excel  by harshithad on 06/07/23-->
                            <p:ajax event="itemSelect" onstart="PF('dlgProcessingOnTask').show();"  listener="#{quotesHdr.onChangeFieldTemplate(quotesHdr.stringUseTemplate)}" update=":formQuotPdf:testing :formQuotPdf:btntemplatePreviewOnClick :formQuotPdf:otptPnlExcl"  oncomplete="PF('dlgProcessingOnTask').hide();"/>

                        </p:selectOneMenu>
                        <p:commandButton id="btntemplatePreviewOnClick" icon="fa fa-gear" styleClass="btn-primary  btn-xs" 
                                         style="float:left;width:30px;height:25px;" title="Template Settings"
                                         actionListener="#{quotesHdr.openPreviewLink(quotesHdr.stringUseTemplate)}"
                                         />
                    </p:panelGrid>
                    <!--Task #11009 CRM-6719  Quote : implement export to excel  by harshithad on 06/07/23--> 
                    <br/>          
                    <p:outputPanel id="otptPnlExcl"  >
                        <p:selectOneRadio id="customRadio" rendered="#{quotesHdr.stringUseTemplate!=0}" value="#{quotesHdr.exportType}" layout="custom">
                            <f:selectItem itemLabel="Image1" itemValue="0" />
                            <f:selectItem itemLabel="Image2" itemValue="1" />
                            <!--Task #11009 CRM-6719 Quote : implement export to excel  by harshithad on 25/04/23-->   
                            <p:ajax listener="#{quotesHdr.changeFileType()}" update=":formQuotPdf:testing" onstart="PF('sendMailBtn').disable()"  oncomplete="PF('sendMailBtn').enable()" />
                        </p:selectOneRadio>

                        <h:panelGrid id="pnlGridImg" rendered="#{quotesHdr.stringUseTemplate!=0}" columns="5" cellpadding="5">
                            <p:outputLabel value="Generate" style="padding-right: 10px"/> 
                            <p:radioButton id="facet1" for="customRadio" itemIndex="0"/>
                            <h:graphicImage name="pdf.png" style="margin-right: 10px" library="images"  height="25" width="25"/> 
                            <p:radioButton id="facet2" for="customRadio" itemIndex="1"/>
                            <h:graphicImage name="excel27.png"  library="images"  height="30" width="30"/> 
                        </h:panelGrid>
                    </p:outputPanel>
                    <br/>
                    <p:selectBooleanCheckbox value="#{quotesHdr.isNotifyOwner}"  itemLabel="Notify Quote Owner #{quotesHdr.getUserById(quotesHdr.quotOwner)}" id="chkStatus"  >
                                                <!--<p:ajax update="chkStatus" listener="#{users.chkStatusAction()}"/>-->
                    </p:selectBooleanCheckbox>  

                    <br/><br/>
                    <p:outputPanel id="testing">
                        <div align="center">
                            <p:commandButton value="Download" class="btn btn-primary btn-xs " rendered="#{quotesHdr.stringUseTemplate == 0}" action="#{quotesHdr.createPDF(1)}" ajax="false" oncomplete="PF('pdf').hide()"  /> 
                            <p:commandButton value="Download" class="btn btn-primary btn-xs " rendered="#{quotesHdr.quotCustomApp == 1 and quotesHdr.stringUseTemplate > 0}" actionListener="#{quotesHdr.quoteFilePath(quotesHdr.recId,quotesHdr.stringUseTemplate,1)}" ajax="false" oncomplete="PF('pdf').hide()">
<!--                                <p:fileDownload value="#{quotesHdr.fileDownload}"/>-->
                            </p:commandButton>
                            <p:spacer width="4"/> 
                            <p:commandButton id="NormalSendByEmailBtn" value="Send by Email"  action="#{quotesHdr.createPDF(0)}" class="btn btn-primary btn-xs " oncomplete="PF('pdf').hide(); window.open('#{globalParams.emailUrl}?fn=sendquotes&amp;reqid=#{loginBean.randomUid}&amp;autocheckemail=1&amp;quote_id=#{quotesHdr.recId}', '_blank')"  
                                             rendered="#{quotesHdr.recId!=0 and quotesHdr.stringUseTemplate == 0}"   /> 
                            <!--Task #11009 CRM-6719 Quote : implement export to excel  by harshithad on 25/04/23-->   
                            <p:commandButton id="TemplateSendByEmailBtn" widgetVar="sendMailBtn" value="Send by Email"  action="#{quotesHdr.quoteFilePath(quotesHdr.recId,quotesHdr.stringUseTemplate,0)}" class="btn btn-primary btn-xs " oncomplete="PF('pdf').hide(); window.open('#{globalParams.emailUrl}?fn=sendquotes&amp;reqid=#{loginBean.randomUid}&amp;autocheckemail=1&amp;quote_id=#{quotesHdr.recId}&amp;file_type=#{quotesHdr.fileType}', '_blank')"  
                                             rendered="#{quotesHdr.recId!=0 and quotesHdr.quotCustomApp == 1 and quotesHdr.stringUseTemplate > 0}"   /> 
                            <!--21-05-2022 7965 OnTask Integration: Quote > Send to onTask-->
                            <p:spacer width="4"/> 
<!--                            <p:commandButton value="Send for Approval" class="btn btn-primary btn-xs " rendered="#{not empty onTaskService.workflowId}"
                                             actionListener="#{onTaskService.checkMandFieldsForOntask(quotesHdr.oppCustomer,quotesHdr.oppCustomerCont,quotesHdr.listQuotDtls.size())}"
                                             oncomplete="openOnTaskDlg();"
                                             update=":frmAurNewCompLookupHead  :formDlgQuoteOntaskProc"/>-->
                        </div> 
                    </p:outputPanel>

                </p:dialog>
            </h:form>

            <p:dialog id="dlgIdProcessingOnTask" widgetVar="dlgProcessingOnTask" closable="false" modal="true" header="Message" onShow="PF('dlgProcessingOnTask').initPosition();" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Processing please wait..." />
                        <br /><br />
                    </p:outputPanel>
                </h:form>
            </p:dialog>
                           
            <p:dialog id="dialogProcess" widgetVar="DialogProcessDlg" closable="false" modal="true" header="Message" onShow="PF('DialogProcessDlg').initPosition();" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Loading please wait..." />
                        <br /><br />
                    </p:outputPanel>
                </h:form>
            </p:dialog>

            <!--06-06-2023 11359 CRM-6968: Link Opp to job if the quote to be linked is already linked to job-->
            <h:form id="frmRelinkOppJob">
                <p:dialog id="dlgRelinkOppJob"  widgetVar="wvDlgRelinkOppJob" width="450"  modal="true" class="dialogCSS" 
                          header="Confirmation" resizable="false" closable="false">
                    <p:outputLabel value="Do you want to relink the #{custom.labels.get('IDS_OPP')} to #{custom.labels.get('IDS_QUOTES')} #{custom.labels.get('IDS_JOB')}?" />
                    <br/>
                    <br/>
                    <div class="div-center" >
                        <p:commandButton  id="btnRelinkYes" value="Yes" styleClass="btn btn-success btn-xs" onclick="PF('DialogProcessDlg').show();"
                                          actionListener="#{viewOppList.reLinkOppJob(viewOppList.linkOppId, viewOppList.linkOppJobId,viewOppList.linkQuotJobId)}"    
                                          oncomplete="PF('wvDlgRelinkOppJob').hide();PF('DialogProcessDlg').hide();"/>
                        <p:spacer width="4px"/>
                        <p:commandButton id="btnRelinkNo" value="No" styleClass="btn btn-warning btn-xs"
                                         oncomplete="PF('wvDlgRelinkOppJob').hide();"/>
                    </div>
                </p:dialog>
            </h:form>
            <!--14469: 08/10/2024: CRM-8480   ISQuote: job caught in aliaser hangs system when manually fetched-->
            <p:dialog header="Message" widgetVar="progressDlg123" closable="false" resizable="false" modal="true">       
                <p:outputPanel style="text-align: center" >
                    <br />
                    <h:graphicImage  library="images" name="ajax-loader.gif"    />
                    <p:spacer width="8" />
                    <h:outputLabel value="Processing....." />
                    <br /><br />
                </p:outputPanel>
                <br />
            </p:dialog>
            <!--//        4872 CRM Sync: Settings for Obulasis-->
            <ui:include src="dialog/ISQuoteList.xhtml" />
            <ui:include src="dialog/OasisQuoteList.xhtml" />
            <ui:include src="dialog/ISQuoteLineItemList.xhtml" />
            <ui:include src="dialog/OasisQuoteLineItemList.xhtml" />
            <ui:include src="../../lookup/OppSuperQuoteLookUp.xhtml" />
            <ui:include src="../../lookup/OppQuoteLookUp.xhtml" />
            <ui:include src="../../lookup/CompanyLookupDlg.xhtml"/>
            <ui:include src="../../lookup/ContactLookupDlg.xhtml"/>
            <ui:include src="../../lookup/PartNumDlg.xhtml" />
            <!--13375  CRM-7815   OASIS: Assign the quotes to the correct user when ingesting from OASIS-->
            <ui:include src="../../lookup/UserLookupDlg.xhtml"/>
            <ui:include src="dialog/JobQuotDialog.xhtml" />
            <ui:include src="dialog/AddJob.xhtml" /> 
            <!--            Feature #5564:CRM-3319: Quote to PO comm trans downflow 2/09/21 by harshithad-->
            <ui:include src="dialog/GeneratePoDlg.xhtml" />

            <ui:include src="../../resources/dialogs/DlgFetchAndProc.xhtml"/>
            <ui:include src="dialog/UnResAutoquoteLookup.xhtml" />
            <ui:include src="dialog/QuoteSyncCompanies.xhtml"/>
            <!--21-08-2023 11857 CRMSYNC-359 Sync Logic. Quote. RF UI > CRMSync tab - Options to add/link/sync/view-->
            <ui:include src="dialog/QuoteAurCompLookup.xhtml"/>
            <!--11755: 01/08/2023: CRM-7170: Quotes: Quote Details > Quote Status, Canned Notes-->
            <ui:include src="../../lookup/CannedNotesDlg.xhtml" />
            <!--19-03-2024 13234 Code Optimisation for Oasis Integration Quotes and Jobs : Manual fetch-->
            <ui:include src="dialog/OasisQuotesList.xhtml"/>
            <ui:include src="dialog/OasisCompanyResolve.xhtml"/>
            <ui:include src="dialog/quoteHistoryDlg.xhtml"/> 

            <style>
                /*        Issue Fix        Task#3843-CRM-3841: Quote Watchers  16-03-2021  by harshithad*/
                body .ui-autocomplete li.ui-autocomplete-token:hover {
                    background: #5f99bb!important; 

                }

                /*840 Quotes > Show Date and Fetch from IS Quote button only when iS Quote is configured*/
                #quotefrom\:isquotedateparam_input{
                    padding-top: 1px;
                    width: 76px!important;
                    height: 24px!important;
                }
                /*                #dtForm\:OppInq\:tblQuotDtls\:qtyDId
                                {
                                    text-align:center!important;
                                }
                                #dtForm\:OppInq\:tblQuotDtls\:eauDId
                                {
                                    text-align:center!important;
                                }
                                #dtForm\:OppInq\:tblQuotDtls\:costDId
                                {
                                    text-align:center!important;
                                }
                                #dtForm\:OppInq\:tblQuotDtls\:unitDId
                                {
                                    text-align:center!important;
                                }
                                #dtForm\:OppInq\:tblQuotDtls\:mulDId
                                {
                                    text-align:center!important;
                                }
                                #dtForm\:OppInq\:tblQuotDtls\:stdDId
                                {
                                    text-align:center!important;
                                }
                                #dtForm\:OppInq\:tblQuotDtls\:extDId
                                {
                                    text-align:center!important;
                                }*/
                label {
                    font-weight: normal !important; 
                }
                /*.ui-panelgrid .ui-widget .panlGrid { display: none !important;}*/
                .calendarClass1 input{
                    width: 85% !important;
                }
                .panlGrid tr, .panlGrid td{
                    border:0!important;
                    padding: 1px 3px;
                }
                #quotefrom\:gridQuote{
                    display: none !important;
                }
                .button\.ui-state-default {
                    background-image: initial !important;
                }
                .button_add_opp{
                    padding: 10px 0px !important;
                    text-decoration: none;
                    color:#000;
                    font-size: 12px;
                    border: none;
                    border-radius: 20px;
                    border:0px solid #888 !important;
                    background: url("#{request.contextPath}/resources/images/link-add.png") !important;
                }

                .ui-datatable-scrollable-header *,
                .ui-datatable-scrollable-theadclone * {
                    -moz-box-sizing: content-box;
                    -webkit-box-sizing: content-box;
                    box-sizing: content-box;
                }

                body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
                    width: 15px;
                }
                /*#7606 ys-204 opportunity icon*/
                .linkorCreateOpp{
                    padding: 13px 0px ! important;
                }
                /*27-10-2022 : #9470 : PRIORITY : Quote PDF settings*/
                .ui-grid-col-1{
                    margin-left:4px;
                }

                .acCompCont{
                    width: 410px!important;
                    height: 250px!important;
                }

                /*17-02-2023 10695 CRMSYNC-139  Add Autoquotes option to 'Fetch from' drop-down box*/
                .might-overflow {
                    text-overflow: ellipsis;
                    overflow : hidden;
                    white-space: nowrap;
                }

                .might-overflow:hover {
                    text-overflow: clip;
                    white-space: normal;
                    word-break: break-all;
                }
                /*//04-08-2023 : #11805 : CRM-7169: Quotes: Other Parties Features*/
                .ui-g li {
                    list-style: none;
                }
            </style>
            <script>
//                19-09-2024 : #14537 : CRM-8376: Quotes: Internal Comments: Add Clear and Timestamp options
                function addDateInternalComment() {
                    var curDate = '#{quotesHdr.getCurrentDate()}';
                    var cmt = document.getElementById('quotefrom:tabQuot:inputInternalCommentsText');
                    curDate = curDate + " ";
                    cmt.value = curDate + "\n \n" + cmt.value;
                    cmt.setSelectionRange(curDate.length + 1, curDate.length + 1);
                    cmt.focus();
                }

                $(document).ready(function () {
                    focusOnPrincipal();
                });
                //                function timeoutChange()) {
                //                                console.log('called time out');
                //                                setTimeout(focusOnPrincipal,300);
                //                            }
                function focusOnPrincipal() {
                    document.getElementById("quotefrom:tabQuot:oppPrincipal_input").focus();
                }

                //03-04-2023 : #10984 : CRM-6829   Quote: cursor not waiting on input field of line items
                function delayToWaitPart() {
                    setTimeout(focusOnPartNumberTabChange, 500);

                }
                //03-04-2023 : #10984 : CRM-6829   Quote: cursor not waiting on input field of line items
                function focusOnPartNumberTabChange() {
                    document.getElementById("quotefrom:tabQuot:tblQuotDtls:oppItemPartManf_input").focus();
                }

                function getUrlFromBlob(blob) {
                    var value = window.URL.createObjectURL(blob);
//                    console.log(value);
                }

                //06-02-2023 : #10619 : CRM-6498 : Logic change for adding new line item to quotes
                function partNoDetil(jsonProdpart) {
                    setTimeout(function () {
                        if ($("#quotefrom\\:tabQuot\\:tblQuotDtls\\:oppItemPartManf_panel").length !== 0) {
                            //27-03-2023 : #10343 : CRM-6498  Product Info pop up boxes sticking again
                            setTimeout(function () {
                                var d = document.getElementById('quotefrom:tabQuot:tblQuotDtls:oppItemPartManf_itemtip')
                                d.style.display = 'none';
                            }, 600)
                            loadQuoteItemtipData(jsonProdpart[0]);
                            $("#quotefrom\\:tabQuot\\:tblQuotDtls\\:oppItemPartManf_panel ul").each(function () {
                                $(this).find('li').hover(
                                        function () {
                                            var des = document.getElementById('quotefrom:tabQuot:tblQuotDtls:oppItemPartManf_itemtip');
                                            if (des.length !== 2) {
                                                des.style.display = 'none';
                                            }
                                            document.getElementById('quotefrom:tabQuot:tblQuotDtls:oppItemPartManf_itemtip').style.display = 'block';
                                            var ii = $(this).index() / 2;
                                            loadQuoteItemtipData(jsonProdpart[ii]);
                                            //                                            setTimeout(function () {
                                            ////                                                const element = document.getElementById('quotefrom:tabQuot:tblQuotDtls:oppItemPartManf_panel');
                                            ////                                                let pos = element.offsetTop;
                                            ////                                                document.getElementById("quotefrom:tabQuot:tblQuotDtls:oppItemPartManf_itemtip").style.marginTop = "200px";
                                            //                                                
                                            //
                                            //                                            }, 500)
                                        },
                                        function () {
                                        }
                                );
                                //                                $(this).find('li').focus(
                                //                                        function () {
                                //                                            console.log('testing the focus')
                                //                                        }
                                //                                );
                            });
                        }
                    }, 0);
                }
                //06-02-2023 : #10619 : CRM-6498 : Logic change for adding new line item to quotes
                function loadQuoteItemtipData(jsonObject) {
                    $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:otnewquoteprinciname').html(jsonObject["princiName"]);
                    $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:otnewquotepartno').html(jsonObject["partno"]);
                    $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:otnewquotefamily').html(jsonObject["family"]);
                    $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:otnewquoteline').html(jsonObject["line"]);
                    $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:otnewquotedesc').html(jsonObject["desc"]);
                    $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:otnewquoteprodstdprice').html(jsonObject["price"]);
                    $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:otnewquoteprodcommrate').html(jsonObject["commrate"]);
                    $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:otnewquotecrossprinciname').html(jsonObject["crossprinciname"]);
                    $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:otnewquotecrosspartno').html(jsonObject["crosspartno"]);
                    $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:otnewquotecrossproddesc').html(jsonObject["crosspartdesc"]);
                    //                    $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:quotPrincipalTextValue').val = principal.val();
                    //                    var date =document.getElementById('quotefrom:tabQuot:tblQuotDtls:otnewquoteprinciname').value;
                    //                    var update = document.getElementById('quotefrom:tabQuot:tblQuotDtls:quotPrincipalTextValue');
                    //                    update.value = date;
                    //                    console.log('updated value=',update.value)
                    if (jsonObject["crossrecid"] === null || jsonObject["crossrecid"] === 0) {
                        //                        $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:pgCrossProd').hide();
                        $("#quotefrom\\:tabQuot\\:tblQuotDtls\\:pgCrossProd").css("display", "none");
                        var s = document.getElementById("quotefrom:tabQuot:tblQuotDtls:pgCrossProd");
                        s.style.display = 'none';
                    } else {
                        //                        $('#quotefrom\\:tabQuot\\:tblQuotDtls\\:pgCrossProd').show();
                        $("#quotefrom\\:tabQuot\\:tblQuotDtls\\:pgCrossProd").css("display", "block");
                        var s = document.getElementById("quotefrom:tabQuot:tblQuotDtls:pgCrossProd");
                        s.style.display = 'block';
                    }
                }
                //06-02-2023 : #10619 : CRM-6498 : Logic change for adding new line item to quotes
                function selectQtyFocus() {
                    document.getElementById("quotefrom:tabQuot:tblQuotDtls:quotQtyNumber_input").focus();
                    //                                
                }
                //06-02-2023 : #10619 : CRM-6498 : Logic change for adding new line item to quotes
                function testing(e) {
                    console.log('e=', e.keyCode)
                    if (PF('gridPartNumTextWidget').panel.is(':visible'))
                        return false;
                    if (e.keyCode == 13) {
                        return false;
                    }
                }
                //06-02-2023 : #10619 : CRM-6498 : Logic change for adding new line item to quotes
                window.addEventListener('blur', function (e) {
                    if (e.target.id == "quotefrom:tabQuot:tblQuotDtls:oppItemPartManf_input") {
                        console.log('same');
                        setTimeout(hidePartNumBlur, 5000);
                        setTimeout(hidePartNumBlur, 7500);
                    } else {
                        PF('gridPartNumTextWidget').close();
                        console.log('not same')
                    }
                }, true);

                window.addEventListener('focus', function (e) {
                    if (e.target.id == "quotefrom:tabQuot:tblQuotDtls:oppItemPartManf_input") {
                        PF('addQuotWidget').disable();
                    }
                }, true);


                //06-02-2023 : #10619 : CRM-6498 : Logic change for adding new line item to quotes
                function hidePartNumBlur() {
                    console.log('is running dellay')
                    PF('gridPartNumTextWidget').close();
                    var d = document.getElementById('quotefrom:tabQuot:tblQuotDtls:oppItemPartManf_itemtip')
                    d.style.display = 'none';
                    console.log('d.style', d.style.display)

                }

                //                function hidePartNoAsTwo(){
                //                    var y = $("#quotefrom\\:tabQuot\\:tblQuotDtls\\:oppItemPartManf_input");
                //                                console.log('y=' + y.val())
                //                                if (y.val().length == 2 || y.val().length == 1 || y.val().length == 0) {
                //                                    PF('gridPartNumTextWidget').close();
                //                                }
                //                }\
                //11755: 01/08/2023: CRM-7170: Quotes: Quote Details > Quote Status, Canned Notes
                function addCannedNotes(notes) {
                    var canNote = notes;
                    var cmt = document.getElementById('quotefrom:tabQuot:itquotComments');
                    canNote = canNote + " ";
                    cmt.value = cmt.value + " " + canNote;
                    cmt.setSelectionRange(canNote.length + 1, canNote.length + 1);
                    cmt.focus();
                }
                //          #12273  CRM-7424 Unit Price: Decimal points - minimum 2, maximum 6-->         

                $(document).ready(function () {



                    /*<![CDATA[*/
                    function maxValueValidation(para) {

                        var value = "";


                        if (para === 1) {
                            value = document.getElementById("quotefrom:quotStdRate").value;
                        } else {
                            value = document.getElementById("quotefrom:unit").value;
                        }

                        var text = value.toString().split('.');
                        //console.log("----text--"+text[0]);
                        if (text[0].length > 9) {
                            PF('addDialogGrowl').show([{summary: "Max value exceeded", severity: "error"}]);
                        }



                    }

                    function maxDecimalValidation(event, para) {
                        var value = "";
                        if (para === 1) {
                            value = document.getElementById("quotefrom:quotStdRate").value;
                        } else {
                            value = document.getElementById("quotefrom:unit").value;
                        }
                        //console.log('---value--'+value);
                        //var value = document.getElementById("quotefrom:quotStdRate").value;
                        var text = value.toString().split('.');
                        //console.log("----text--"+text[0]);
                        if ((text[1] && text[1].length >= 6) || (event.keyCode >= 65 && event.keyCode <= 90) || (event.keyCode >= 97 && event.keyCode <= 122)) {
                            return false;
                        } else {
                            return true;
                        }

                    }

                    function maxValueValidateOnEdit(para, index) {

                        var value = "";


                        if (para === 1) {
                            value = document.getElementById("quotefrom:tabQuot:tblQuotDtls:" + index + ":quotGridStdRateInp").value;
                        } else {
                            value = document.getElementById("quotefrom:tabQuot:tblQuotDtls:" + index + ":quotGridResaleInp").value;
                        }
//                        console.log("----text--"+value);

                        var text = value.toString().split('.');
                        if (text[0].length > 9) {
                            PF('addDialogGrowl').show([{summary: "Max value exceeded", severity: "error"}]);
                        }
                    }


                    function maxDecimalValidateOnEdit(event, para, index) {
                        var value = "";
                        if (para === 1) {
                            value = document.getElementById("quotefrom:tabQuot:tblQuotDtls:" + index + ":quotGridStdRateInp").value;
                        } else {
                            value = document.getElementById("quotefrom:tabQuot:tblQuotDtls:" + index + ":quotGridResaleInp").value;
                        }
//                        console.log('---value--'+value);
                        //var value = document.getElementById("quotefrom:quotStdRate").value;
                        var text = value.toString().split('.');
                        //console.log("----text--"+text[0]);
                        //25-09-2024 : #14246 : 2 ESCALATION CRM-8351 : Quote: Unable to update standard price field for robroy items
                        if ((text[1] && text[1].length >= 6) || (event.keyCode >= 65 && event.keyCode <= 90) || (event.keyCode >= 97 && event.keyCode <= 122)) {
                            return false;
                        } else {
                            return true;
                        }

                    }









                    /*  ]]>*/

                    window.myFunction1 = maxValueValidation;
                    window.myFunction2 = maxDecimalValidation;
                    window.myFunction6 = maxValueValidateOnEdit;
                    window.myFunction7 = maxDecimalValidateOnEdit;




                });



            </script>
        </div>
    </ui:define>
</ui:composition>