<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}"
                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">

    <h:head>


    </h:head>

    <!--    //360- page title-->

    <ui:define name="head">


        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <!--//Feature #3030:CRM-3746: cronan: make purchase order" menu relabelable to "Sales Order" Purchase Orders-->
        <title>#{custom.labels.get('IDS_PURCHASE_ORDERS')}</title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>
            <!--Bug #13788 ESCALATIONS CRM-7988   URGENT - PO: Internal server error occurred  by harshithad on 11/06/2024-->
            <f:viewAction action="#{poServices.loadPoList(false)}"  />
            <!--#2880:CRM-3700: Purchase Order Screen tutorial button not tied to learning.repfabric.com page-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('PO_LIST'))}" />
            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('PO_LIST')}" />
        </f:metadata>
    </ui:define>

    <ui:define name="title">
        <!--//Feature #3030:CRM-3746: cronan: make purchase order" menu relabelable to "Sales Order" Purchase Orders-->
        <ui:param name="title" value="#{custom.labels.get('IDS_PURCHASE_ORDERS')}"/>
<!--        <f:event listener="#{commTransactionService.isCommTransProcessPageAccessible}" type="preRenderView"/>-->
        <div class="row">
            <div class="col-md-6">
                <!--//Feature #3030:CRM-3746: cronan: make purchase order" menu relabelable to "Sales Order" Purchase Orders-->
                #{custom.labels.get('IDS_PURCHASE_ORDERS')}
            </div>
            <ui:include src="/help/Help.xhtml"/>

        </div>
    </ui:define>

    <ui:define name="menu">

    </ui:define>

    <ui:define name="body">

        <h:form id="poLstForm"> 
            <div class="box box-info box-body">
                <div class="row">
                    <!--                           Feature #5404:CRM-4539: Need to be able to export from PO Module (sales order module for Rodenbeck)  by Harshitha Devadiga on 04-08-2021-->
                    <!--//24-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules-->
                    <div class="col-md-5">
                        <p:commandButton  id="btnNwPo" value="New" ajax="false" style="margin-left: 10px"
                                          title="New"    action="/opploop/po/PoDetails.xhtml?faces-redirect=true"
                                          styleClass="btn btn-primary btn-xs" /> 
                        <p:spacer width="4"/>
                        <!--2509-->
                        <!--                         //PMS:2844
                                                 //Seema - 31/12/2020-->
                        <p:commandButton value="Backlog Report"   
                                         title="Backlog Report" 
                                         oncomplete="PF('backlogBxportDlg').show();"
                                         actionListener="#{poBacklogService.backLogDefaultvalues()}"
                                         update=":backlogExport"
                                         styleClass="btn btn-primary btn-xs" 
                                         id="btnBlcklog"
                                         />
                        <!--                        Task #3966:Marking PO’s as shipped based on Sales data imported     08/04/21 by harshithad-->
                        <p:spacer width="4"/>
                        <!--                          Feature #4588: CRM-4287:  Match and Ship Line Item > Show All Unshipped Line Items  22/05/21 by harshithad-->
                        <p:commandButton id="cmdBtnMtchShip" value="Match and Ship"   
                                         title="Match and Ship #{custom.labels.get('IDS_PURCHASE_ORDER')} matching with sales records" 
                                         oncomplete="PF('mtchAndShip').show();"
                                         actionListener="#{poServices.resetFields()}"
                                         update="dlgMtchAndShip frmMatchAndShip:oneMenuShowAll frmMatchAndShip:rdBtn"
                                         styleClass="btn btn-primary btn-xs"                                        
                                         />
                        <!--                           Feature #5404:CRM-4539: Need to be able to export from PO Module (sales order module for Rodenbeck)  by Harshitha Devadiga on 04-08-2021-->
                        <p:spacer width="4"/>
                        <p:commandButton id="cmdBtnExpotPO" value="Export" styleClass="btn btn-primary btn-xs" title="Export #{custom.labels.get('IDS_PURCHASE_ORDERS')}" actionListener="#{poServices.resetPOExport()}" oncomplete="PF('ExportDlg').show()" update="frmExprtFrm"/>

                        <!--07-06-2024 13637 CRM-7948 PO: Accumatica Alias resolver for orders aliasing for CRMsync-->
                        <p:spacer width="4"/>
                        <p:commandButton id="btnSyncStatus" styleClass="btn btn-primary btn-xs" 
                                         value="Sync Status" title="Sync Status" 
                                         onclick="window.open('../../crmsync/SyncStatus.xhtml', '_self');" />

                    </div>
                </div>
            </div>
            <p:panelGrid styleClass="panelgrid"  id="pnlView">
                <p:row>
                    <p:column  style="width:90px">
                        <!--                          //PMS:2844
                                                 //Seema - 31/12/2020-->
                        <p:outputLabel value="View Mode" id="lblViewMode"/>
                    </p:column>
                    <p:column >
                        <p:selectOneRadio id="sumdtl" value="#{poServices.summaryDetals}" layout="responsive" columns="2" style="width: 227px">
                            <f:selectItem itemLabel="Summary" itemValue="0" />
                            <f:selectItem itemLabel="Detailed" itemValue="1" />
                            <!--Bug #13788 ESCALATIONS CRM-7988   URGENT - PO: Internal server error occurred  by harshithad on 11/06/2024-->
                            <!--//23-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                            <!--                    04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top-->
                            <p:ajax event="change" listener="#{poServices.onModeSelection(true)}"  
                                    process="@this"
                                    onstart="PF('loaderCommTransDlg').show()"
                                    oncomplete="PF('loaderCommTransDlg').hide();applyTopScrollbarPo();applyTopScrollbarPoQetails();"
                                    update="poLstForm"
                                    />
                        </p:selectOneRadio>
                    </p:column>
                    <p:column style="width:43%">               
                    </p:column>
                    <p:column class="ui-inputgroup" style="margin-top: 9px;text-align: right">
                        <p:inputText id="globalFilter" onkeyup="PF('poListTable').filter()" style="width:150px" 
                                     placeholder="Search fields" rendered="#{poServices.summaryDetals == 0}"/>
                        <p:commandButton id="toggler" type="button" value="Columns"  icon="fa fa-align-justify" 
                                         rendered="#{poServices.summaryDetals == 0}"
                                         style="float: right;text-align: justify"/>
                        <p:columnToggler datasource="dtPoList" trigger="toggler" id="togglerPosummary"
                                         rendered="#{poServices.summaryDetals == 0}">
                            <p:ajax event="toggle" listener="#{poServices.onToggle}" />
                        </p:columnToggler>
                        <!--//23-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                        <p:commandButton title="Save settings" 
                                         icon="fa fa-save"
                                         style="float: left;"
                                         id="btneSaveColumnSetting"  
                                         rendered="#{poServices.summaryDetals == 0}"
                                         action="#{poServices.saveSettings()}"
                                         update=":poLstForm" oncomplete="applyTopScrollbarPo();"
                                         class="btn-success btn-xs"  />
                        <!--//23-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                        <p:commandButton title="Clear Filters" 
                                         icon="fa fa-times-circle"
                                         id="btnFilterClear"
                                         rendered="#{poServices.summaryDetals == 0}"
                                         onclick="PF('poListTable').clearFilters();" oncomplete="applyTopScrollbarPo();"
                                         class="btn-danger btn-xs"   />

                        <!--04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top-->
                        <p:inputText id="globalFilter1" onkeyup="PF('poDtlTable').filter();applyTopScrollbarPoQetails();" style="width:150px" 
                                     placeholder="Search fields" rendered="#{poServices.summaryDetals == 1}" />
                        <p:commandButton id="toggler1" type="button" value="Columns"  icon="fa fa-align-justify" 
                                         rendered="#{poServices.summaryDetals == 1}"
                                         style="float: right;text-align: justify"/>
                        <p:columnToggler datasource="dtPoDtlLst" trigger="toggler1" id="togglerPoDtl"
                                         rendered="#{poServices.summaryDetals == 1}">

                            <!--Feature #1988:CRM-3243: Purchase Order Grid: add CPN :-->
                            <p:ajax event="toggle" listener="#{poServices.onToggle1}" />
                        </p:columnToggler>
                        <!--04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top-->
                        <p:commandButton title="Save settings" 
                                         icon="fa fa-save"
                                         style="float: left;"
                                         id="btneSaveColumnSetting1"   oncomplete="applyTopScrollbarPoQetails()"
                                         rendered="#{poServices.summaryDetals == 1}"
                                         action="#{poServices.saveSettings1()}"
                                         update=":poLstForm"
                                         class="btn-success btn-xs"  />
                        <!--04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top-->
                        <p:commandButton title="Clear Filters" 
                                         icon="fa fa-times-circle"
                                         id="btnFilterClear1" oncomplete="applyTopScrollbarPoQetails()" 
                                         onclick="PF('poDtlTable').clearFilters();"
                                         rendered="#{poServices.summaryDetals == 1}" 
                                         class="btn-danger btn-xs"   />
                    </p:column>
                </p:row>
            </p:panelGrid>
            <!--04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top-->
            <p:dataTable id="dtPoList"  widgetVar="poListTable" 
                         var="summary" 
                         rows="50" 
                         value="#{poServices.summaryList}" 
                         filteredValue="#{poServices.fileteredSummaryLst}"
                         filterEvent="keyup paste"
                         draggableColumns="true"
                         emptyMessage="No Transactions found."  
                         paginator="true" 
                         rendered="#{poServices.summaryDetals == 0}"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="50,100" 
                         paginatorAlwaysVisible="true" 
                         multiViewState="true"
                         rowKey="#{summary.recId}" scrollable="true" scrollHeight="2000"
                         resizableColumns="true">
                <f:facet name="filter">
                    <p:calendar id="cal1" pattern="yyyy-MM-dd">
                        <p:ajax event="dateSelect" oncomplete="PF('poListTable').filter()" update="dtPoList"/>
                        <p:ajax event="change" process="@this" oncomplete="PF('poListTable').filter()" update="dtPoList"/>
                    </p:calendar>
                </f:facet>
                <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                <p:ajax event="colReorder" listener="#{poServices.onColumnReorder}" />
                <!--#8459 CRM-5912   Dawn Mock/PIR Sales/Add paper clip icon to PO Module-->
                <p:column  style="width:30px;min-width:30px; max-width: 30px;align-content:center;" toggleable="false" exportable="false">
                    <!--Feature #11460: CRM-7082   Paperclip on orders list - file preview window popup by harshithad on 19/06/23-->
                    <p:commandLink  styleClass="attachment-icon" ajax="true" rendered="#{summary.poAttCount > 0}" title="Click to preview"  actionListener="#{poServices.findAttachments(summary.poId,summary.poNumber)}"  >
                        <i   class="fa fa-paperclip attachment-icon"  style="font-size: 1.5rem;cursor: auto " />  
                    </p:commandLink>
                </p:column>
                <!--                          //PMS:2844
                                               //Seema - 31/12/2020-->
                <c:forEach var="column" items="#{poServices.listSummaryColumns}" id="forEachSummaryColumns">

                    <!--                        //PMS:1552
                                                //Seema - 27/08/2020-->
                    <!--#13576 CRM-7988 PO: Internal server error occurred-->
                    <!--//09-06-2022 : 8181 : Direct Request: Purchase Orders: Hide Projected Commission at header and line item level-->
                    <!--04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top-->
                    <!--//25-10-2024 : #14773 : CRM-7447 : search improvements : inclusive contains on opps topic etc-->
                    <c:choose>
                        <c:when test="#{column.columnName=='poPrinciName' or column.columnName=='poCustName' or column.columnName=='poDistriName' or column.columnName=='poProgram' or column.columnName=='poSecondCustName'}">
                            <p:column headerText="#{column.columnHeader}" id="#{column.columnName}1"
                                      toggleable="#{column.columnName=='poCommProjected' ? poServices.accessCommissionBol:true}"
                                      visible="#{column.columnName=='poCommProjected' ? poServices.accessCommissionBol?column.columnVisibleFlag==1:false :column.columnVisibleFlag==1}"  
                                      filterFunction="#{poServices.filterByNormalizedText}" 
                                      filterBy="#{column.columnName=='poDate' ? poServices.getFormattedDate(summary[column.columnName], 'da') : summary[column.columnName]}" 
                                      sortBy="#{summary[column.columnName]}"
                                      style="width:150px" 
                                      >
                                <!--                         //PMS:1552
                                                        //Seema - 27/08/2020-->
                                <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
                                <c:choose>
                                    <c:when test="#{column.columnName=='poTotalPrice'}">
                                        <h:outputLabel class="hvr"  value="#{summary[column.columnName]}" style="float: right"  >
                                            <f:convertNumber maxIntegerDigits="14"  minFractionDigits="2" maxFractionDigits="2" groupingUsed="true"/>
                                            <p:ajax event="click" listener="#{poServices.redirectPage(summary.poId)}"/>
                                        </h:outputLabel>
                                    </c:when> 

                                    <c:when test="#{column.columnName=='poDate'}">
                                        <!--                          //PMS:2844
                                                       //Seema - 31/12/2020-->
                                        <h:link outcome="PoDetails.xhtml?id=#{summary.poId}" id="lnkDate1"
                                                value="#{poServices.getFormattedDate(summary[column.columnName], 'da')}"
                                                >
                                        </h:link>
                                    </c:when>
                                    <!--                            //PMS:2443
                                                                //seema - 20/11/2020-->
                                    <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
                                    <c:when test="#{ column.columnName=='poCommProjected'}">
        <!--                                <h:link outcome="PoDetails.xhtml?id=#{summary.poId}" 
                                                value="#{summary[column.columnName]}" style="float: right"
                                                >
                                            <f:convertNumber maxFractionDigits="3"/>
                                        </h:link>-->
                                        <h:outputLabel value="#{summary[column.columnName]}" style="float: right" id="oLblPrice1" >
                                            <f:convertNumber maxFractionDigits="3"/>
                                        </h:outputLabel>
                                    </c:when> 
                                    <c:when test="#{ column.columnName=='poCommRate'}">
        <!--                                <h:link outcome="PoDetails.xhtml?id=#{summary.poId}" 
                                                value="#{summary[column.columnName]}" style="float: right"
                                                >
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:link>-->
                                        <h:outputLabel value="#{summary[column.columnName]}" style="float: right" id="oLblCommRate1" >
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>
                                    </c:when> 
                                    <c:otherwise>
                                        <h:link outcome="PoDetails.xhtml?id=#{summary.poId}" id="lnkData1"
                                                value="#{summary[column.columnName]}"
                                                >
                                        </h:link>
                                    </c:otherwise>
                                </c:choose>
                            </p:column>
                        </c:when>
                        <c:otherwise>
                            <p:column headerText="#{column.columnHeader}" id="#{column.columnName}"
                                      toggleable="#{column.columnName=='poCommProjected' ? poServices.accessCommissionBol:true}"
                                      visible="#{column.columnName=='poCommProjected' ? poServices.accessCommissionBol?column.columnVisibleFlag==1:false :column.columnVisibleFlag==1}"  
                                      filterMatchMode="contains" 
                                      filterBy="#{column.columnName=='poDate' ? poServices.getFormattedDate(summary[column.columnName], 'da') : summary[column.columnName]}" 
                                      sortBy="#{summary[column.columnName]}"
                                      style="width:150px" 
                                      >
                                <!--                         //PMS:1552
                                                        //Seema - 27/08/2020-->
                                <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
                                <c:choose>
                                    <c:when test="#{column.columnName=='poTotalPrice'}">
                                        <h:outputLabel class="hvr"  value="#{summary[column.columnName]}" style="float: right"  >
                                            <f:convertNumber maxIntegerDigits="14"  minFractionDigits="2" maxFractionDigits="2" groupingUsed="true"/>
                                            <p:ajax event="click" listener="#{poServices.redirectPage(summary.poId)}"/>
                                        </h:outputLabel>
                                    </c:when> 

                                    <c:when test="#{column.columnName=='poDate'}">
                                        <!--                          //PMS:2844
                                                       //Seema - 31/12/2020-->
                                        <h:link outcome="PoDetails.xhtml?id=#{summary.poId}" id="lnkDate"
                                                value="#{poServices.getFormattedDate(summary[column.columnName], 'da')}"
                                                >
                                        </h:link>
                                    </c:when>
                                    <!--                            //PMS:2443
                                                                //seema - 20/11/2020-->
                                    <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
                                    <c:when test="#{ column.columnName=='poCommProjected'}">
        <!--                                <h:link outcome="PoDetails.xhtml?id=#{summary.poId}" 
                                                value="#{summary[column.columnName]}" style="float: right"
                                                >
                                            <f:convertNumber maxFractionDigits="3"/>
                                        </h:link>-->
                                        <h:outputLabel value="#{summary[column.columnName]}" style="float: right" id="oLblPrice" >
                                            <f:convertNumber maxFractionDigits="3"/>
                                        </h:outputLabel>
                                    </c:when> 
                                    <c:when test="#{ column.columnName=='poCommRate'}">
        <!--                                <h:link outcome="PoDetails.xhtml?id=#{summary.poId}" 
                                                value="#{summary[column.columnName]}" style="float: right"
                                                >
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:link>-->
                                        <h:outputLabel value="#{summary[column.columnName]}" style="float: right" id="oLblCommRate" >
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>
                                    </c:when> 
                                    <c:otherwise>
                                        <h:link outcome="PoDetails.xhtml?id=#{summary.poId}" id="lnkData"
                                                value="#{summary[column.columnName]}"
                                                >
                                        </h:link>
                                    </c:otherwise>
                                </c:choose>
                            </p:column>
                        </c:otherwise>
                    </c:choose>

                </c:forEach>
            </p:dataTable>

            <!--04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top-->
            <p:dataTable id="dtPoDtlLst" widgetVar="poDtlTable"  
                         var="detail"   
                         rows="50" 
                         value="#{poServices.detailedList}"
                         filteredValue="#{poServices.filteredDetailedList}"
                         filterEvent="keyup paste"
                         draggableColumns="true"
                         emptyMessage="No Transactions found." 
                         paginator="true"        
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="50,100" 
                         paginatorAlwaysVisible="true"      
                         multiViewState="true"
                         rowKey="#{detail.recId}"
                         rendered="#{poServices.summaryDetals == 1}"
                         resizableColumns="true" scrollable="true" scrollHeight="2000"
                         >
                <f:facet name="filter">
                    <p:calendar id="cal1" pattern="yyyy-MM-dd">
                        <p:ajax event="dateSelect" oncomplete="PF('poDtlTable').filter()" update="dtPoDtlLst"/>
                        <p:ajax event="change" process="@this" oncomplete="PF('poDtlTable').filter()" update="dtPoDtlLst"/>
                    </p:calendar>
                </f:facet>
                <p:ajax event="colReorder" listener="#{poServices.onColumnReorder1}"   
                        update=":poLstForm" />
                <c:forEach var="column" items="#{poServices.listDetailColumns}" id="forEachtDetailColumns">
                    <!--                        //PMS:1552
                                                //Seema - 27/08/2020-->
                    <!--#13576 CRM-7988 PO: Internal server error occurred-->
                    <!--#9155 PRIORITY PO List > Add new column "Accounting Date"-->
                    <!--//09-06-2022 : 8181 : Direct Request: Purchase Orders: Hide Projected Commission at header and line item level-->
                    <!--04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top-->
                    <!--//25-10-2024 : #14773 : CRM-7447 : search improvements : inclusive contains on opps topic etc-->
                    <c:choose>
                        <c:when test="#{column.columnName=='poPrinciName' || column.columnName=='poCustName'}">
                            <p:column headerText="#{column.columnHeader}" id="#{column.columnName}1" 
                                      toggleable="#{column.columnName=='poCommProjected' ? poServices.accessCommissionBol:true}"
                                      visible="#{column.columnName=='poCommProjected' ? poServices.accessCommissionBol?column.columnVisibleFlag==1:false :column.columnVisibleFlag==1}"  
                                      filterFunction="#{poServices.filterByNormalizedText}"
                                      filterBy="#{column.columnName=='poDate' || column.columnName=='poInvcDate' || column.columnName=='poShippingDate'  ||  column.columnName=='poAcctDate' ? poServices.getFormattedDate(detail[column.columnName], 'da') : detail[column.columnName]}" 
                                      sortBy="#{detail[column.columnName]}" style="width:150px" 
                                      >
                                <!--                        //PMS:1552
                                                        //Seema - 27/08/2020-->
                                <c:choose>
                                    <!--#9155 PRIORITY PO List > Add new column "Accounting Date"-->
                                    <c:when test="#{column.columnName=='poDate' || column.columnName=='poInvcDate' || column.columnName=='poShippingDate' ||  column.columnName=='poAcctDate'}">
                                        <p:outputLabel id="oLblDate1"
                                                       value="#{poServices.getFormattedDate(detail[column.columnName], 'da')}">
                                        </p:outputLabel>
                                    </c:when>

                                    <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
                                    <!--                            //PMS:2443
                                                               //seema - 20/11/2020-->
                                    <c:when test="#{column.columnName=='poCommProjected'}">
                                        <h:outputLabel value="#{detail[column.columnName]}" style="float: right" id="oLblCommProj1" >
                                            <f:convertNumber maxFractionDigits="3"/>
                                        </h:outputLabel>
                                    </c:when> 
                                    <c:when test="#{ column.columnName=='poCommRate'}">
                                        <h:outputLabel value="#{detail[column.columnName]}" style="float: right"  id="olblRate1">
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>
                                    </c:when> 
                                    <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
                                    <c:when test="#{column.columnName=='poTotalPrice'}">
                                        <h:outputLabel class="hvr" value="#{detail[column.columnName]}" style="float: right"  >
                                            <f:convertNumber maxIntegerDigits="14"  minFractionDigits="2" maxFractionDigits="2" groupingUsed="true"/>

                                        </h:outputLabel>
                                    </c:when> 
                                    <c:otherwise>
                                        <p:outputLabel id="oLblData1"
                                                       value="#{detail[column.columnName]}">
                                        </p:outputLabel>
                                    </c:otherwise>
                                </c:choose>

                            </p:column>
                        </c:when>
                        <c:otherwise>
                            <p:column headerText="#{column.columnHeader}" id="#{column.columnName}" 
                                      toggleable="#{column.columnName=='poCommProjected' ? poServices.accessCommissionBol:true}"
                                      visible="#{column.columnName=='poCommProjected' ? poServices.accessCommissionBol?column.columnVisibleFlag==1:false :column.columnVisibleFlag==1}"  
                                      filterMatchMode="contains" 
                                      filterBy="#{column.columnName=='poDate' || column.columnName=='poInvcDate' || column.columnName=='poShippingDate'  ||  column.columnName=='poAcctDate' ? poServices.getFormattedDate(detail[column.columnName], 'da') : detail[column.columnName]}" 
                                      sortBy="#{detail[column.columnName]}" style="width:150px" 
                                      >
                                <!--                        //PMS:1552
                                                        //Seema - 27/08/2020-->
                                <c:choose>
                                    <!--#9155 PRIORITY PO List > Add new column "Accounting Date"-->
                                    <c:when test="#{column.columnName=='poDate' || column.columnName=='poInvcDate' || column.columnName=='poShippingDate' ||  column.columnName=='poAcctDate'}">
                                        <p:outputLabel id="oLblDate"
                                                       value="#{poServices.getFormattedDate(detail[column.columnName], 'da')}">
                                        </p:outputLabel>
                                    </c:when>

                                    <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
                                    <!--                            //PMS:2443
                                                               //seema - 20/11/2020-->
                                    <c:when test="#{column.columnName=='poCommProjected'}">
                                        <h:outputLabel value="#{detail[column.columnName]}" style="float: right" id="oLblCommProj" >
                                            <f:convertNumber maxFractionDigits="3"/>
                                        </h:outputLabel>
                                    </c:when> 
                                    <c:when test="#{ column.columnName=='poCommRate'}">
                                        <h:outputLabel value="#{detail[column.columnName]}" style="float: right"  id="olblRate">
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </h:outputLabel>
                                    </c:when> 
                                    <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
                                    <c:when test="#{column.columnName=='poTotalPrice'}">
                                        <h:outputLabel class="hvr" value="#{detail[column.columnName]}" style="float: right"  >
                                            <f:convertNumber maxIntegerDigits="14"  minFractionDigits="2" maxFractionDigits="2" groupingUsed="true"/>

                                        </h:outputLabel>
                                    </c:when> 
                                    <c:otherwise>
                                        <p:outputLabel id="oLblData"
                                                       value="#{detail[column.columnName]}">
                                        </p:outputLabel>
                                    </c:otherwise>
                                </c:choose>

                            </p:column>
                        </c:otherwise>
                    </c:choose>

                </c:forEach>
                <!--pms:2982 11/01/2021 udaya b -->
                <p:column style="width:50px ">
                    <p:commandButton styleClass="btn-primary btn-xs btn-icon-group btn-icon" 
                                     title="Edit" icon="fa fa-pencil"  id="btnEdit" process="@this"
                                     actionListener="#{poServices.loadDtlDataFromLst(detail)}"
                                     update="poTransDlg"
                                     oncomplete="PF('dlgPoDetail').show();" 
                                     >

                    </p:commandButton>
                </p:column>
            </p:dataTable>
        </h:form>
        <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
        <style>

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top-->*/
            /*//25-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
            @media only screen and (min-height: 600px) and (max-height: 900px) {
                .ui-datatable-scrollable-body {
                    height: 60vh;
                    outline: 0px;
                }
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            /*//25-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
            @media only screen and (min-height: 901px) and (max-height: 1152px) {
                .ui-datatable-scrollable-body {
                    height: 65vh;
                    outline: 0px;
                }
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            @media only screen and (min-height: 1153px) and (max-height: 1440px) {
                .ui-datatable-scrollable-body {
                    /*//21-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
                    height: 70vh;
                    outline: 0px;
                }
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            @media only screen and (min-height: 1500px) and (max-height: 1640px) {
                .ui-datatable-scrollable-body {
                    /*//21-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
                    height: 70vh;
                    outline: 0px;
                }
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            .ui-datatable-scrollable-body {
                outline: 0px;
                overflow-x: hidden !important;
                overflow-y: auto !important;
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            .ui-datatable-scrollable-header *,
            .ui-datatable-scrollable-theadclone * {
                -moz-box-sizing: content-box;
                -webkit-box-sizing: content-box;
                box-sizing: content-box;
                width: 100%;
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
                width: 15px;
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            .ui-datatable-scrollable table {
                /* table-layout: fixed !important; */
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            .ui-datatable-scrollable-theadclone {
                visibility: collapse !important;
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            .ui-datatable-tablewrapper {
                overflow: initial !important;
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            .ui-paginator {
                display: block !important;
                background-color: #dedede !important;
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            .q {
                width: 100% !important;
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            .p {
                width: 80% !important;
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            .ui-datatable-scrollable-header-box,
            .ui-datatable-scrollable-footer-box {
                /*    overflow-x: auto !important;
                    overflow-y: hidden !important;*/
                position: relative;
                /*margin-left: 0px !important;*/
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            .ui-datatable-scrollable-view {
                position: relative;
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            .top-scrollbar {
                position: sticky;
                top: 0;
                left: 0;
                right: 0;
                height: 16px; /* Adjust height as needed */
                overflow-x: scroll;
                overflow-y: hidden;
                -ms-overflow-style: scrollbar;
                z-index: 10; /* Ensure it appears above the DataTable */
            }

            /*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
            @-moz-document url-prefix() {
                .top-scrollbar {
                    height: 12px; /* Height for Firefox */
                }
            }
            

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            .top-scrollbar div {
                width: 100%; /* Match the width of the DataTable */
                height: 1px; /* Match the height of the scrollbar container */
            }

            /*04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top*/
            .ui-datatable-scrollable-header {
                z-index: 20; /* Ensure it appears above the DataTable */
            }

            .hvr:hover{
                text-decoration:  underline;
            }
            label {
                font-weight: normal!important;
            }
            .ui-panelgrid tr, .ui-panelgrid td{
                border:0!important;
            }
            /*Feature #11460: CRM-7082   Paperclip on orders list - file preview window popup by harshithad on 19/06/23*/
            .attachment-icon:hover{
                cursor: pointer!important;
            }
        </style>  

        <script>
            //04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top
            function applyTopScrollbarPo() {
                var tableExists = document.getElementById('poLstForm:dtPoList') === null;
                console.log('return null sumary')
                if (tableExists) {
                    return;
                }
                var dataTable = PF('poListTable').jq;
                var scrollableHeaderBox = dataTable.find('.ui-datatable-scrollable-header-box');
                var scrollableBody = dataTable.find('.ui-datatable-scrollable-body');
                var scrollableView = dataTable.find('.ui-datatable-scrollable-view');

                // Check if the scrollbar already exists
                if ($('.top-scrollbar').length === 0) {
                    // Create the custom top scrollbar
                    var topScrollbar = document.createElement('div');
                    topScrollbar.className = 'top-scrollbar';
                    var innerDiv = document.createElement('div');
                    var width = scrollableBody.find('table').width();
        if(width > 1338){
            if(1378 > width){
                            width = 1382;
                        }
                    }
//                    console.log('width=', width)
                    innerDiv.style.width = width + 'px';
                    topScrollbar.appendChild(innerDiv);

                    // Insert the top scrollbar above the scrollable header box
                    scrollableHeaderBox.before(topScrollbar);

                    // Synchronize scroll positions
                    topScrollbar.addEventListener('scroll', function () {
                        scrollableBody.scrollLeft(topScrollbar.scrollLeft);
                        scrollableHeaderBox.scrollLeft(topScrollbar.scrollLeft);
                    });

                    scrollableBody.on('scroll', function () {
                        topScrollbar.scrollLeft = scrollableBody.scrollLeft();
                        scrollableHeaderBox.scrollLeft(scrollableBody.scrollLeft());
                    });

                    scrollableHeaderBox.on('scroll', function () {
                        topScrollbar.scrollLeft = scrollableHeaderBox.scrollLeft();
                        scrollableBody.scrollLeft(scrollableHeaderBox.scrollLeft());
                    });
                }
            }

//04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top
            function applyTopScrollbarPoQetails() {
                var tableExists = document.getElementById('poLstForm:dtPoDtlLst') === null;
                console.log('return null ')
                if (tableExists) {
                    return;
                }
                var dataTable = PF('poDtlTable').jq;
                var scrollableHeaderBox = dataTable.find('.ui-datatable-scrollable-header-box');
                var scrollableBody = dataTable.find('.ui-datatable-scrollable-body');
                var scrollableView = dataTable.find('.ui-datatable-scrollable-view');

                // Check if the scrollbar already exists
                if ($('.top-scrollbar').length === 0) {
                    // Create the custom top scrollbar
                    var topScrollbar = document.createElement('div');
                    topScrollbar.className = 'top-scrollbar';
                    var innerDiv = document.createElement('div');
                    var width = scrollableBody.find('table').width();
                    if (width > 1338) {
                        if (1378 > width) {
                            width = 1382;
                        }
                    }
//                    console.log('width=', width)
                    innerDiv.style.width = width + 'px';
                    topScrollbar.appendChild(innerDiv);

                    // Insert the top scrollbar above the scrollable header box
                    scrollableHeaderBox.before(topScrollbar);

                    // Synchronize scroll positions
                    topScrollbar.addEventListener('scroll', function () {
                        scrollableBody.scrollLeft(topScrollbar.scrollLeft);
                        scrollableHeaderBox.scrollLeft(topScrollbar.scrollLeft);
                    });

                    scrollableBody.on('scroll', function () {
                        topScrollbar.scrollLeft = scrollableBody.scrollLeft();
                        scrollableHeaderBox.scrollLeft(scrollableBody.scrollLeft());
                    });

                    scrollableHeaderBox.on('scroll', function () {
                        topScrollbar.scrollLeft = scrollableHeaderBox.scrollLeft();
                        scrollableBody.scrollLeft(scrollableHeaderBox.scrollLeft());
                    });
                }
            }

// 04-09-2024 : #14434 : ESCALATION CRM-8209 : Purchase Orders: Scroll bar needs to be at the top
            document.addEventListener('DOMContentLoaded', function () {
                setTimeout(applyTopScrollbarPo, 1000);
                setTimeout(applyTopScrollbarPoQetails, 1000);
            });
        </script>

        <p:dialog widgetVar="loaderCommTransDlg" closable="false" modal="true" header="Message" resizable="false" id="dlgPrgrsbar">
            <h:form id="frmProgressbar">
                <p:outputPanel >
                    <br />
                    <h:graphicImage  library="images" name="ajax-loader.gif"   />
                    <p:spacer width="5" />
                    <p:outputLabel value="Please wait...Processing records" />
                    <br /><br />

                </p:outputPanel>
            </h:form>
        </p:dialog>

        <!--//Feature #2509:Purchase Orders > Backlog Report.-->
        <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>
        <ui:include src="/lookup/CompanyLookupDlg.xhtml"/>
        <ui:include src="dlg/BacklogReportDlg.xhtml"/>
        <!--pms:2982 11/01/2021 udaya b -->
        <ui:include src="../../lookup/ProductFamily.xhtml"/>
        <ui:include src="../../lookup/PartNumDlg.xhtml"/>
        <ui:include src="../../lookup/CustPartNumDlg.xhtml"/>
        <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
        <!--Bug #8046: purchase order ->shipped line item clone issue by harshithad on 20/06/22-->
        <ui:include src="dlg/LineItemDlg.xhtml"/>

        <!--        Task #3966:Marking PO’s as shipped based on Sales data imported     08/04/21 by harshithad-->
        <ui:include src="dlg/POMatchAndShipDlg.xhtml"/>
        <!--                           Feature #5404:CRM-4539: Need to be able to export from PO Module (sales order module for Rodenbeck)  by Harshitha Devadiga on 04-08-2021-->
        <ui:include src="dlg/ExportPO.xhtml"/>
        <!--        Feature #11460: CRM-7082   Paperclip on orders list - file preview window popup by harshithad on 19/06/23-->
        <ui:include src="dlg/POAttachmentsDlg.xhtml"/>
        <ui:include src="dlg/POPreviewDlg.xhtml"/>
    </ui:define>
</ui:composition>