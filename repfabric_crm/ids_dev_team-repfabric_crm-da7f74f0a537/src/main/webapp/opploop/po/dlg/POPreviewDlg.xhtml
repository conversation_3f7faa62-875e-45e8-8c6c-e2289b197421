<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<!--Feature #11460: CRM-7082   Paperclip on orders list - file preview window popup by harshithad on 19/06/23-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"  
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:p="http://primefaces.org/ui" 
                 xmlns:f="http://xmlns.jcp.org/jsf/core">
    <!--01-11-2023 12461 Incidental Finding: CRM-7432 > Purchase Orders : preview of files remove extra space & block resize-->
    <!--17-01-2024 12461 Incidental Finding: CRM-7432 > Purchase Orders : preview of files remove extra space & block resize-->
    <p:dialog modal="true" widgetVar="wvDlgPreviewIframe" header="Preview: #{poServices.fileName}" id="dlgPreviewPoFile"  onShow="PF('wvDlgPreviewIframe').initPosition();"
              width="800" position="center" height="600" appendTo="@(body)"
              maximizable="true" resizable="false" draggable="true" closeOnEscape="true">
        <p:ajax event="maximize" onstart="handleDialogResize(true)" process="@none"/>
        <p:ajax event="restoreMaximize" onstart="handleDialogResize(false)" process="@none"/> 
        <f:facet name="header">
            <p:outputPanel id="headerOfficePreview">
                Preview: #{poServices.fileName}
            </p:outputPanel>
        </f:facet>
        <!--01-11-2023 12461 Incidental Finding: CRM-7432 > Purchase Orders : preview of files remove extra space & block resize-->
        <p:outputPanel id="officePreview"  styleClass="previewContainer">
            <br/>
            <br/>
            <!--01-11-2023 12461 Incidental Finding: CRM-7432 > Purchase Orders : preview of files remove extra space & block resize-->
            <p:outputPanel rendered="#{empty poServices.previewFile}"   styleClass="fileNoteFound">
                <h1>File not found</h1>
            </p:outputPanel>
            <!--01-11-2023 12461 Incidental Finding: CRM-7432 > Purchase Orders : preview of files remove extra space & block resize-->
            <p:outputPanel rendered="#{not empty poServices.previewFile}"  styleClass="iFrameContainer">
                <iframe id="officeFrame" src="#{poServices.previewFile}" width="1000px" height="600px" frameborder="0" class="styleIframe" ></iframe>
            </p:outputPanel>


        </p:outputPanel>
    </p:dialog>

    <!--01-11-2023 12461 Incidental Finding: CRM-7432 > Purchase Orders : preview of files remove extra space & block resize-->
    <style>
        .previewContainer{
            width:100%;
            height: 100%;
        }

        .iFrameContainer{
            width:100%;
            height: 100%;
        }

        .styleIframe{
            width:100%;
            height: 100%;
        }   

        .fileNoteFound{
            text-align: center;
            display:flex;
            justify-content: center;
            top:10px;
        }
    </style>

    <!--01-11-2023 12461 Incidental Finding: CRM-7432 > Purchase Orders : preview of files remove extra space & block resize-->
    <script>
        var isMaximize = false;
        function resetPreviewDialogSize() {
            if (isMaximize) {
                var dialog = PrimeFaces.widgets.wvDlgPreviewIframe;
                dialog.toggleMaximize();
                isMaximize = false;
            }
        }

        function handleDialogResize(updateMaximize) {
            isMaximize = updateMaximize;
            if (isMaximize) {
                document.getElementById('officeFrame').style.height = '100%';
            } else {
                document.getElementById('officeFrame').style.height = '100%';
            }
        }
    </script>

</ui:composition>

