<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"

                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
   <!--Bug #13933: CRM-8126  Orders: TIMELINE Allow "edit" of timeline for "Commission Paid"-->
    <p:dialog id="dlgEdtTimeLne" modal="true" styleClass="disable-scroll" header="#{poServices.timelineDlgHeader} Timeline" height="140" width="340"  resizable="false"
              widgetVar="edtTimeline" responsive="true" >
        <h:form id="frmEdtTmlne">
            <p:growl id="valerrg" globalOnly="false" keepAlive="true" >
                <p:autoUpdate />
            </p:growl>
            <p:panelGrid  styleClass="box-primary no-border ui-fluid" layout="grid" columns="2" columnClasses="ui-grid-col-5,ui-grid-col-8" style="min-height: 100px"   >
                <p:outputLabel value="Status" />
                <p:outputLabel id="otptLblSts" value="#{poServices.poStatusTimeline.statusLabel}"/>
                <p:outputLabel value="#{poServices.poStatusTimeline.dateLabel}"/>
                <p:calendar  id="calRefDate" pattern="#{globalParams.dateFormat}" 
                             value="#{poServices.poStatusTimeline.poRefDate}"   showOn="button"                                                 
                             converterMessage="Invalid Date format"   style="width: 135px"
                              maxlength="10" >   
                    <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                    <p:ajax event="dateSelect" process="@this" />
                </p:calendar>
<!--Bug #13933: CRM-8126  Orders: TIMELINE Allow "edit" of timeline for "Commission Paid"-->
                <p:outputLabel id="otptLblNoLbl"  value="#{poServices.poStatusTimeline.numberLabel}" />
                <p:inputText id="inptTxtRefNo"  style="width: 167px" value="#{poServices.poStatusTimeline.poRefNumber}"/>

            </p:panelGrid>
            <p:spacer height="2px"/>
            <div style="text-align: center">
                <p:commandButton id="cmdBtnSave" value="Save" actionListener="#{poServices.updateTimlineStatus(poServices.poStatusTimeline)}"  styleClass="btn btn-primary btn-xs" update=" frmEdtTmlne:valerrg"/>
                <p:spacer width="4"/> 
                <p:commandButton id="cmdnBtnCancel" value="Cancel" onclick="PF('edtTimeline').hide()" styleClass="btn btn-warning btn-xs"/>
            </div>

        </h:form>
    </p:dialog>
    <style>
        .disable-scroll .ui-dialog-content {
            overflow: hidden !important;
        }
    </style>
</ui:composition> 