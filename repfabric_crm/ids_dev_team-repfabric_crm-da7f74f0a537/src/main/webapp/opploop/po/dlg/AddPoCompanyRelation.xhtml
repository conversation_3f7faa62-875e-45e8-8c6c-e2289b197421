<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<!--Task#4349:PO Bulk Reconciliation  12/05/21 by harshithad-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    <p:dialog id="dlgException" widgetVar="poException" resizable="false" width="600" header="Related Company Not Defined">
         <p:outputPanel>
            <br />
            <p:spacer width="5" />
            <p:outputLabel id="otplLblTxt" value="Please define which #{custom.labels.get('IDS_CUSTOMER')} is the #{custom.labels.get('IDS_SPECIFIER')} and which is the #{custom.labels.get('IDS_PURCHASER')}" />
            <br/><br/>
        </p:outputPanel>

        <h:form id = "addReltdCompForm">
            <p:growl id="relatedMsg"/>
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel id="otptLblComp" value="Company" />
                </div>
                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                    <p:outputLabel id="otptLblCust" value="#{poServices.poDtlView.poCustName}" />
                </div>
                <div class="ui-sm-12 ui-md-5 ui-lg-5">

                </div>

                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="Related Company" />
                </div>
                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                    <p:outputLabel id="otptLblSecCust" value="#{poServices.poDtlView.poSecondCustName}" />

                </div>
                <div class="ui-sm-12 ui-md-5 ui-lg-5">

                </div>

                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel id="optLblcompCat" value="Related Company Category" />
                </div>
                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                    <p:selectOneMenu id="oneMenuCat" filter="true"  value="#{relatedComps.reltCategory}" style="width:280px">      
                        <f:selectItem id="itmSlct" itemLabel="Select Category" itemValue="0"/>
                        <f:selectItems id="itms" var="reln" value="#{relatedComps.relationCatgList}" 
                                       itemLabel="#{reln.reltCategoryName}" itemValue="#{reln.reltCategory}"  />
                    </p:selectOneMenu> 
                </div>
                <div class="ui-sm-12 ui-md-5 ui-lg-5">

                </div>

                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="Related Company Role" />
                </div>
                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                    <p:selectOneMenu id="oneMenuRole" widgetVar="roleType"  value="#{relatedComps.reltRole}" style="width:280px">      
                        <f:selectItem id="itmsSelect" itemLabel="#{custom.labels.get('IDS_SPECIFIER')}" itemValue="0"/>
                        <f:selectItem id="itm" itemLabel="#{custom.labels.get('IDS_PURCHASER')}" itemValue="1"/>
                    </p:selectOneMenu> 
                </div>
                <div class="ui-sm-12 ui-md-5 ui-lg-5">

                </div>
            </div>
            <div style="text-align: center">
                <p:commandButton id="btnSave" value="Save" styleClass="btn btn-success  btn-xs" 
                                 action="#{poServices.saveReltComp()}" oncomplete="PF('poException').hide()"
                                 />
                <p:commandButton id="btnCancel" type="button" value="Cancel" styleClass="btn btn-warning  btn-xs" 
                                 onclick="PF('poException').hide()"/>
            </div>
        </h:form>
    </p:dialog>
</ui:composition>
