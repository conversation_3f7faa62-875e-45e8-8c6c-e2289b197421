<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    <!--Task#3645:CRM-3946: PO partial shipment and over shipment   :02/03/2021 by harshithad-->
    <!--    Task#4349:PO Bulk Reconciliation  12/05/21 by harshithad-->
    <!--Bug #8821:CRM-6058  PO: UAT: various things #1, #3 by harshithad on 26/08/22-->
    <!--Feature #8913: CRM-6058  PO: UAT: various things #2, #4 (Feature)  by harshithad on 06/09/22-->  
    <!--Feature #11596:CRM-7133   PO: need to edit price without line item by harshithad on 13/07/23-->
    <!--Feature #13000:  CRM-7693   PO: entering commission, add projected vs actual comm input box    by harshithad on 02/02/2024-->
    <p:dialog id="poLIShiptDlg" header="Ship #{poServices.shipHeaderName}" width="700px" styleClass="disable-scroll"
              widgetVar="dlgPoLIShip" class="dialogCSS"  modal="true" responsive="true"  dynamic="true" closable="false" >

        <h:form id="frmPoLIShip"> 
            <!--Feature #8913: CRM-6058  PO: UAT: various things #2, #4 (Feature)  by harshithad on 06/09/22-->
            <p:focus for="frmPoLIShip:inpTxtrpoLIInvNum"/>
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="#{custom.labels.get('IDS_SHIPPED_DATE')}" id="lblLIShpDate" styleClass="required"/>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
                    <p:calendar  pattern="#{globalParams.dateFormat}" showOn="button"    id="calLIShpDate" value="#{poServices.poBulkShippingDate}">  
                        <!--                Task#3645:CRM-3946: PO partial shipment and over shipment   :02/03/2021 by harshithad-->
                        <p:ajax event="dateSelect" process="@this" listener="#{poServices.populateDate}" update="frmPoLIShip:calLIInvDate"/>
                    </p:calendar>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">

                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="Invoice Number" id="lblLIInvNum"  styleClass="required"/>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <p:inputText value="#{poServices.poBulkInvtNo}" id="inpTxtrpoLIInvNum"   maxlength="30">
                        <p:ajax process="@this"/>
                    </p:inputText>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">

                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="Invoice Date" id="lblLIInvDate" styleClass="required"/>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
                    <p:calendar  pattern="#{globalParams.dateFormat}" showOn="button"   id="calLIInvDate" 
                                 value="#{poServices.pobulkInvDate}"  converterMessage="Invalid Date format">  
                        <p:ajax event="dateSelect" process="@this"/>
                    </p:calendar>
                </div>
                <!--Bug #946 purchase order ->timeline issue by harshithad on 28/10/22-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                </div>
                <!--                Task#3645:CRM-3946: PO partial sment and over shipment   :02/03/2021 by harshithad-->
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--Feature #8913:CRM-6058  PO: UAT: various things #2, #4 (Feature) by harshithad on 06/09/22-->
                    <p:outputLabel value="Allow Partial/Over Shipment"  rendered="#{poServices.renderPartialShipment}"/>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <!--Feature #8913:CRM-6058  PO: UAT: various things #2, #4 (Feature) by harshithad on 06/09/22-->
                    <!--Feature #9051 : PRIORITY: CRM-6109/6135: PO  Adjustment entries for Shipments and Commission by harsithad-->
                    <p:selectBooleanCheckbox value="#{poServices.chckShipment}" rendered="#{poServices.renderPartialShipment}" disabled="#{poServices.disablePartialOrOverShipment}"> 
                  <!--Feature #13000:  CRM-7693   PO: entering commission, add projected vs actual comm input box    by harshithad on 02/02/2024-->      
                        <p:ajax event="change"   listener="#{poServices.onChangeShipment()}" update="frmPoLIShip:otptPnl"  />
                    </p:selectBooleanCheckbox>


                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">

                </div>
                <!--                Task#4349:PO Bulk Reconciliation  12/05/21 by harshithad-->
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel id="otptLblGenerate" value="Generate"/>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6">

                    <p:selectBooleanCheckbox id="chckSales" value="#{poServices.chckSales}" itemLabel="Sales"> 
                        <!--Bug #946 purchase order ->timeline issue by harshithad on 28/10/22-->
                        <p:ajax event="change" process="@this" update="frmPoLIShip:otptPnlChkcDateLbl frmPoLIShip:otptPnlchckDateInput frmPoLIShip:otptlPnlcheckNoLbl frmPoLIShip:otptlPnlRndrchckNoInput" />
                    </p:selectBooleanCheckbox>
                <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->   
                    <p:selectBooleanCheckbox id="chckComs"  value="#{poServices.chckComms}" itemLabel="#{custom.labels.get('IDS_COMMISSION')}"> 
                        <!--Bug #946 purchase order ->timeline issue by harshithad on 28/10/22-->
                        <!--Feature #13000:  CRM-7693   PO: entering commission, add projected vs actual comm input box    by harshithad on 02/02/2024-->         
                     <!--Feature #13000:  CRM-7693   PO: entering commission, add projected vs actual comm input box    by harshithad on 02/02/2024-->
                     <!--//26-03-2024 : #13416 : CRM-7693 PO Shipment: UI updates-->   
                     <p:ajax event="change" process="@this"  listener="#{poServices.onChangeShipment()}" update="frmPoLIShip:otptPnlChkcDateLbl frmPoLIShip:otptPnlchckDateInput frmPoLIShip:otptlPnlcheckNoLbl frmPoLIShip:otptlPnlRndrchckNoInput frmPoLIShip:otptPnl frmPoLIShip:headerComm"  />
                    </p:selectBooleanCheckbox>
                </div>
                <!--Bug #946 purchase order ->timeline issue by harshithad on 28/10/22-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4" >
                    <!--Bug #946 purchase order ->timeline issue by harshithad on 28/10/22-->
                    <p:outputPanel id="otptPnlChkcDateLbl">
                        <p:outputLabel id="otptLblChckDate" value="Check Date"  for="calCheckDate" styleClass="required"  rendered="#{poServices.chckComms}" />
                    </p:outputPanel>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <!--Bug #946 purchase order ->timeline issue by harshithad on 28/10/22-->
                    <p:outputPanel id="otptPnlchckDateInput">
                        <p:calendar  pattern="#{globalParams.dateFormat}"  id="calCheckDate"  rendered="#{poServices.chckComms}"
                                     value="#{poServices.chckDate}"  converterMessage="Invalid Date format" class="calendarClass" >  
                            <p:ajax event="dateSelect"  />
                            <p:ajax event="change" process="@this"/>
                        </p:calendar> 
                    </p:outputPanel>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <!--Bug #946 purchase order ->timeline issue by harshithad on 28/10/22-->
                    <p:outputPanel id="otptlPnlcheckNoLbl">
                        <p:outputLabel value="Check No."  for="calCheckNum" id="lblChkNum"  
                                       rendered="#{poServices.chckComms}" />
                    </p:outputPanel>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <!--Bug #946 purchase order ->timeline issue by harshithad on 28/10/22-->
                    <p:outputPanel id="otptlPnlRndrchckNoInput">
                        <p:inputText value="#{poServices.chckNum}" id="calCheckNum" maxlength="30"  rendered="#{poServices.chckComms}" >  

                        </p:inputText>
                    </p:outputPanel>
                </div>
                <!--Bug #946 purchase order ->timeline issue by harshithad on 28/10/22-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                </div>
            </div>
            <div class="button_bar" align="center">
                <!--                Task#4349:PO Bulk Reconciliation  12/05/21 by harshithad-->
                <!--   Bug #5926: Purchase Orders:  Line item list update issue on filter and add  bu harshithad 24/09/21-->
                <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
                <!--Feature #8913 CRM-6058  PO: UAT: various things #2, #4 (Feature)  by harshitha on 30/09/22-->    
                <!--Feature #9732: CRM-6360: PO: multilevel comms: edit mode not adjusting the projected commission by harshithad on 25/11/22-->
                <p:commandButton id="btnPostLI" value="Post" 
                                 styleClass="btn btn-success  btn-xs" 
                                 onclick="PF('postButton').disable()" 
                                 widgetVar="postButton" 
                                 actionListener="#{poServices.post()}"
                                 oncomplete="PF('postButton').enable();PF('lineItemDt').filter();" update="dlgException frmPo:leftColumn frmPo:listTimeLne frmPo:tabDetails:btnAdd"/>                 
                <p:spacer width="4px"/>
                <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
                <p:commandButton value="Cancel" 
                                 process="@this"
                                 actionListener="#{poServices.onCancel()}"
                                 id="btnPostCancel"
                                 class="btn btn-xs btn-warning" oncomplete="PF('lineItemDt').filter();"/>
            </div> 
            <!--            Task#3645:CRM-3946: PO partial shipment and over shipment   :02/03/2021 by harshithad-->
            <p:spacer width="4px"/>
            <!--//26-03-2024 : #13416 : CRM-7693 PO Shipment: UI updates-->
            <p:outputPanel id="headerComm">
                <div class="ui-sm-12 ui-md-12 ui-lg-12" style="#{poServices.editedLineItemList.size()>0 ? 'display:block' : 'display:none'}">
                                <p:outputLabel value="Enter #{custom.labels.get('IDS_COMM')} received"/>
                </div>
            </p:outputPanel>
            <!--            Task#3645:CRM-3946: PO partial shipment and over shipment   :02/03/2021 by harshithad-->
            <p:outputPanel id="otptPnl">
                <!--Feature #9051 : PRIORITY: CRM-6109/6135: PO  Adjustment entries for Shipments and Commission by harsithad-->
                <!--Feature #13000:  CRM-7693   PO: entering commlission, add projected vs actual comm input box    by harshithad on 02/02/2024--> 
                <!--//26-03-2024 : #13416 : CRM-7693 PO Shipment: UI updates-->
                <p:dataTable id="lineitem" widgetVar="shippedLineItems" var="item" value="#{poServices.editedLineItemList}"  editable="true" rowKey="#{item.recId}"
                             editMode="cell"  rendered="#{poServices.editedLineItemList.size()>0}" rowIndexVar="rowIdx"
                             tableStyle="table-layout:auto" resizableColumns="true"
                             draggableColumns="true" paginatorAlwaysVisible="true"
                             paginatorPosition="top" editingRow="true"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             selection="#{poServices.selectLineItmLst}"
                             filteredValue="#{poServices.filterLineItemLst}"
                             paginator="true"  rows="10" 
                             >
                    <!--Feature #13000:  CRM-7693   PO: entering commlission, a-->
                    <p:column headerText="Part #" filterMatchMode="contains" sortBy="#{item.poPartNo}" filterBy="#{item.poPartNo}">

                        <h:outputText value="#{item.poPartNo}" style="color:grey"/>
                    </p:column>

                    <p:column headerText="Order Qty">
                        <h:outputText value="#{item.poOrderQty}" style="color:grey" />
                    </p:column>

                    <p:column headerText="Plan Qty">
                        <h:outputText value="#{item.poPlanQty}" style="color:grey"/>

                    </p:column>

                    <p:column headerText="Ship Qty">
                        <p:cellEditor>
                            <f:facet name="output">
                                <!--Feature #13000:  CRM-7693   PO: entering commission, add projected vs actual comm input box    by harshithad on 02/02/2024-->
                                <p:outputLabel value="#{item.poShippingQty}" id="otptTxtShpQty">
                                    <f:convertNumber groupingUsed="true" minFractionDigits="2" />
                                </p:outputLabel>
                            </f:facet>
                            <!--Feature #13000:  CRM-7693   PO: entering commission, add projected vs actual comm input box    by harshithad on 02/02/2024-->
                            <f:facet name="input" rendered="#{poServices.chckComms==false}">
                                <p:inputNumber value="#{item.poShippingQty}" disabled="#{poServices.chckComms and !poServices.chckShipment}" style="width:100%" maxlength="9"  decimalPlaces="3" maxValue="999999999">

                                    <p:ajax  event="change" process="@this" listener="#{poServices.calculateTotalPrice(item)}"
                                             update="@form:lineitem:@row(#{rowIdx})"/>
                                </p:inputNumber>
                            </f:facet>
                        </p:cellEditor>
                    </p:column>
                    <p:column headerText="#{custom.labels.get('IDS_UNIT_PRICE')}">
                        <h:outputText value="#{item.poUnitPrice}" style="color:grey"/>
                    </p:column>
                    <p:column headerText="Total Price">
                        <!--Feature #13000:  CRM-7693   PO: entering commission, add projected vs actual comm input box    by harshithad on 02/02/2024-->
                        <p:outputLabel value="#{item.poTotalPrice}" id="otptTxtTotalPrice"   style="color:grey" >
                            <f:convertNumber groupingUsed="true" minFractionDigits="2" />
                        </p:outputLabel>
                    </p:column>
                    <!--Feature #13000:  CRM-7693   PO: entering commission, add projected vs actual comm input box    by harshithad on 02/02/2024-->
                <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->   
                    <p:column headerText="Proj. #{custom.labels.get('IDS_COMM')}" rendered="#{poServices.chckComms == true}">
                        <p:outputLabel id="otptLblprojCom" value="#{item.poCommProjected}"  style="color:grey" >
                            <f:convertNumber groupingUsed="true" minFractionDigits="2" />
                        </p:outputLabel>
                    </p:column>
                <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                    <p:column headerText="Actual #{custom.labels.get('IDS_COMM')}" rendered="#{poServices.chckComms eq true}">
                        <p:cellEditor>
                            <f:facet name="output">
                                <!--//26-03-2024 : #13416  : CRM-7693 PO Shipment: UI updates-->
                                <p:inputNumber inputStyle="#{item.poCommRecvd.compareTo(item.poCommProjected)==0?'color:green !important;':'color:red !important;'}" value="#{item.poCommRecvd}"  style="width:100%;" maxlength="14" decimalPlaces="2" maxValue="999999999999999" >
                                    <!--<p:ajax event="change" listener="#{poServices.onChangeCommison(item)}"  update="@form:lineitem:@row(#{rowIdx})"/>-->
                                    <p:ajax event="change" listener="#{poServices.onChangeCommison(item)}"  update="@form:lineitem:@row(#{rowIdx})"/>
                                </p:inputNumber>
                            </f:facet>
                            <f:facet name="input" rendered="#{poServices.chckComms == false}">
                            <!--//26-03-2024 : #13416 : CRM-7693 PO Shipment: UI updates-->
                                <p:inputNumber inputStyle="#{item.poCommRecvd.compareTo(item.poCommProjected)==0?'color:green !important;':'color:red !important;'}" value="#{item.poCommRecvd}"  style="width:100%;" maxlength="14" decimalPlaces="2" maxValue="999999999999999" >
                                    <!--<p:ajax event="change" listener="#{poServices.onChangeCommison(item)}"  update="@form:lineitem:@row(#{rowIdx})"/>-->
                                    <p:ajax event="change" listener="#{poServices.onChangeCommison(item)}"  update="@form:lineitem:@row(#{rowIdx})"/>
                                </p:inputNumber>
                            </f:facet>
                        </p:cellEditor>
                    </p:column>
                </p:dataTable>
                <!--Feature #9051:PRIORITY: CRM-6109/6135: PO  Adjustment entries for Shipments and Commission by harshithad-->
                <h:outputText rendered="#{poServices.renderNote}" value="Note: " style="font-weight: bold"/>
                <p:outputLabel  rendered="#{poServices.renderNote}" value="Partial/Over Shipment is not applicable for #{custom.labels.get('IDS_LINE_ITEMS')} with a negative quantity." />
               
            </p:outputPanel>

        </h:form>

    </p:dialog>
    <!--    Task#4349:PO Bulk Reconciliation  12/05/21 by harshithad-->
    <ui:include src="AddPoCompanyRelation.xhtml"/>
    <!--Bug #8821:CRM-6058  PO: UAT: various things #1, #3 by harshithad on 26/08/22-->
    <!--Feature #8913: CRM-6058  PO: UAT: various things #2, #4 (Feature)  by harshithad on 06/09/22-->
    <style>
        .ui-datatable-scrollable-header *,
        .ui-datatable-scrollable-theadclone * {
            -moz-box-sizing: content-box;
            -webkit-box-sizing: content-box;
            box-sizing: content-box;
        }

        body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
            width: 15px;
        }
    </style>
</ui:composition>