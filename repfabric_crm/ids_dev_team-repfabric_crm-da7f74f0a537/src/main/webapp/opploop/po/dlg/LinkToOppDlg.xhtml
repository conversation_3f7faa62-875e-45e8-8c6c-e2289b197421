
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:f="http://java.sun.com/jsf/core">
    <p:dialog id="dlgPoOpps" widgetVar="oppPoDlg"  modal="true" width="1280"  header="Add #{custom.labels.get('IDS_OPPS')}"
              height="550" maximizable="true" responsive="true" 
              closeOnEscape="true">
        <h:form id="frmOpps">     
            <p:growl id="linkedOppMsg" showDetail="false"/>
            <p:dataTable  id="dtPoOppList" widgetVar="poOppListTable" 
                          value="#{poServices.oppsLstToLink}" var="opp" selectionMode="single" 
                          rowKey="#{opp.oppId}" selection="#{poServices.selOpp}" 
                          emptyMessage="No open #{custom.labels.get('IDS_OPPS')} found." 
                          paginator="true" rows="10" filteredValue="#{poServices.filteredOpps}"
                          paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                          rowsPerPageTemplate="5,10,15" 
                          paginatorAlwaysVisible="false" class="tblmain">
                <p:column filterBy="#{opp.getCompNameById(opp.oppCustomer)}" filterMatchMode="contains" sortBy="#{opp.getCompNameById(opp.oppCustomer)}" id="clmnPOOppCust">
                    <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet> 
                    <p:commandLink value="#{opp.getCompNameById(opp.oppCustomer)}" actionListener="#{poServices.linkOpptoPo(opp.oppId)}" process="@this" id="cmndLnkoppCustomer">
                    </p:commandLink>
                </p:column>

                <p:column filterBy="#{opp.getCompNameById(opp.oppPrincipal)}" filterMatchMode="contains" sortBy="#{opp.getCompNameById(opp.oppPrincipal)}" id="clmnPOOppPrinci">
                    <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet> 
                    <p:commandLink value="#{opp.getCompNameById(opp.oppPrincipal)}" actionListener="#{poServices.linkOpptoPo(opp.oppId)}" process="@this" id="cmndLnkoppPrincipal">
                    </p:commandLink>
                </p:column>

                <p:column filterBy="#{opp.getCompNameById(opp.oppDistri)}" filterMatchMode="contains" sortBy="#{opp.getCompNameById(opp.oppDistri)}" id="clmnPOOppDistri">
                    <f:facet name="header">#{custom.labels.get('IDS_DISTRI')}</f:facet> 
                    <p:commandLink value="#{opp.getCompNameById(opp.oppDistri)}" actionListener="#{poServices.linkOpptoPo(opp.oppId)}" process="@this" id="cmndLnkoppDistri">
                    </p:commandLink>
                </p:column>


                <p:column filterBy="#{opp.oppCustProgram}" filterMatchMode="contains" sortBy="#{opp.oppCustProgram}" id="clmnPOOppPrgrm">
                    <f:facet name="header">#{custom.labels.get('IDS_PROGRAM')}</f:facet> 
                    <p:commandLink value="#{opp.oppCustProgram}" actionListener="#{poServices.linkOpptoPo(opp.oppId)}" process="@this" id="cmndLnkoppCustProgram">
                    </p:commandLink>
                </p:column> 

                <p:column filterBy="#{opp.oppActivity}" filterMatchMode="contains" sortBy="#{opp.oppActivity}" id="clmnPOOppCustActivity">
                    <f:facet name="header">#{custom.labels.get('IDS_ACTIVITY')}</f:facet>  
                    <p:commandLink value="#{opp.oppActivity}" actionListener="#{poServices.linkOpptoPo(opp.oppId)}" process="@this" id="cmndLnkoppActivity">
                    </p:commandLink>
                </p:column>
                <p:column filterBy="#{opp.oppStatus}" filterMatchMode="contains" sortBy="#{opp.oppStatus}" id="clmnPOOppStatus">
                    <f:facet name="header">Status</f:facet>  
                    <p:commandLink value="#{opp.oppStatus}" actionListener="#{poServices.linkOpptoPo(opp.oppId)}" process="@this" id="cmndLnkoppStatus">
                    </p:commandLink>
                </p:column>
                <p:column filterBy="#{opp.oppFollowUp}" filterMatchMode="contains" sortBy="#{opp.oppFollowUp}" id="clmnPOOppFollowup">
                    <f:facet name="header">#{custom.labels.get('IDS_FOLLOW_UP')}</f:facet> 
                    <p:commandLink value="#{poServices.formatDateTime(opp.oppFollowUp, 'da')}" actionListener="#{poServices.linkOpptoPo(opp.oppId)}" process="@this"  id="cmndLnkoppFollowUp">
                    </p:commandLink>
                </p:column>
                <!--Feature #5025 RE: Repfabric / AVX CRMSync / Relabeling Fields / "Next Step" (Pending)-->
                <!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
                <p:column filterBy="#{opp.oppNextStep}" filterMatchMode="contains" sortBy="#{opp.oppNextStep}" id="clmnPOOppNxtStep" styleClass="might-overflow">
                    <f:facet name="header">#{custom.labels.get('IDS_NEXT_STEP')}</f:facet> 
                    <p:commandLink value="#{opp.oppNextStep}" actionListener="#{poServices.linkOpptoPo(opp.oppId)}" process="@this" id="cmndLnkoppNextStep">
                    </p:commandLink>
                </p:column>
                <p:column  filterMatchMode="contains" sortBy="#{opp.oppCloseStatus}">
                    <f:facet name="header">Status</f:facet> 
                    <p:outputLabel value="#{opp.oppCloseStatus==0?'Open':'Closed'}" />
                </p:column>
            </p:dataTable>
        </h:form>
    </p:dialog>
</ui:composition>


