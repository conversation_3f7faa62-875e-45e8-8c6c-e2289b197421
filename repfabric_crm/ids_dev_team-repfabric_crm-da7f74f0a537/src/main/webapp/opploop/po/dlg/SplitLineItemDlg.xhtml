<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >

    <p:dialog id="poLISplitDlg" header="Split #{custom.labels.get('IDS_LINE_ITEM')} For Partial Shipment" width="500"
              widgetVar="dlgPoLISplit"  modal="true" responsive="true">
        <h:form id="frmPoLISplit">
            <!--pms:2982 11/01/2021 udaya b -->
            <p:focus context=":frmPoLISplit:iNumSplitQty"/>
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                    <p:outputLabel value="Ordered Qty." id="lblSplitOrdQty"/>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <h:outputText value="#{poServices.poDtl.poOrderQty}" id="oTxtSplitOrdQty"/>
                </div>
                <div class="ui-sm-12 ui-md-1 ui-lg-1">

                </div>
                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                    <p:outputLabel value="Planned Qty." id="lblSplitPlndQty"/>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <h:outputText value="#{poServices.poDtl.poPlanQty}" id="oTxtSplitPlndQty"/>
                </div>
                <div class="ui-sm-12 ui-md-1 ui-lg-1">

                </div>
                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                    <p:outputLabel value="Qty. for Current Shipment" id="lblSplitQty" styleClass="required"/>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <p:inputNumber value="#{poServices.poDtl.poSplitQty}"   
                                   id="iNumSplitQty"  maxlength="9" placeholder="0.000"
                                   decimalPlaces="3" maxValue="999999999"
                                   >  
                        <p:ajax process="@this"/>
                    </p:inputNumber>
                </div>
                <div class="ui-sm-12 ui-md-1 ui-lg-1">

                </div>
            </div>
            <div class="button_bar" align="center">
                <!--Bug #7018:CRM-5248 Line item doesn't show in invoice until you refresh screen   25/01/22 by harshithad-->
                <p:commandButton id="btnSplitLI" value="Split" 
                                 styleClass="btn btn-success  btn-xs" 
                                 onclick="PF('splitButton').disable()" 
                                 widgetVar="splitButton" process="@this"
                                 actionListener="#{poServices.saveSplitLineItems(poServices.poDtl)}"
                                 oncomplete="PF('splitButton').enable();PF('lineItemDt').clearFilters();" />                 
                <p:spacer width="4px"/>
                <p:commandButton value="Cancel" onclick="PF('dlgPoLISplit').hide()" id="btnSplitCancel"
                                 type="button" class="btn btn-xs btn-warning"  />
            </div> 
        </h:form>

    </p:dialog>
    <script type="text/javascript">
//        var qty;
//        function qtyFocus() {
//            qty = $('#frmPo\\:tabDetails\\:frmPoLISplit\\:iNumSplitQty').focus();
//            
//        }        
//         $(document).ready(function() {
//                $('#frmPo\\:tabDetails\\:frmPoLISplit\\:iNumSplitQty').focus();
//            });
    </script>
</ui:composition>