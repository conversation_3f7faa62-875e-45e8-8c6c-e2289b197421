<!--//**********************************************************
// Program: RepFabric Sys
// Filename: LineItemDlg.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    <!-- //PMS:1502
                //seema - 27/08/2020-->

    <!--#1484 - Purchase Orders > Line Items> Add : need to drag window every time to Save - sharvani - 27-08-2020-->
    <!--Feature #9183: ESCALATIONS CRM-6135   TMS: Accounting Date on PO by harshithad 29/09/22-->
    <p:dialog id="poTransDlg" header="#{poServices.poDtl.recId==0?'New':'Edit'} #{custom.labels.get('IDS_LINE_ITEM')} information" width="1000px"
              widgetVar="dlgPoDetail"  modal="true" responsive="true" class="dialogCSS" >
        <p:ajax event="close" listener="#{poServices.initialPoDtlValue()}" process="@this"/>
        <!--        Task#3765-CRM-4050: PO Entry: enter button in line item details triggers part number lookup and loses place ho    10-03-2021  by harshithad-->
        <h:form id="frmPoDetails" onkeypress="if (event.keyCode == 13) {
                    document.getElementById('frmPoDetails:submitButton').click();
                    return false;
                }">

            <!--pms:2982 11/01/2021 udaya b -->
            <p:remoteCommand name="applyPartNum" autoRun="false" actionListener="#{poServices.onPartNoSelection(viewProductLookUpService.selectedProduct,poServices.poDtl)}"/>
            <p:remoteCommand name="applyProductFamily" immediate="true" autoRun="false" actionListener="#{poServices.updateFieldsOnPartFam(prodFamilyMst.selectedProdFamilyMst)}"/>
            <p:remoteCommand name="applyCustProd" immediate="true" autoRun="false" 
                             actionListener="#{poServices.onCustPartNoSelection(viewCustProductLookUpService.selectedCustProduct,poServices.poDtl)}"/>
            <p:growl escape="false" life="6000" showDetail="false" showSummary="true" widgetVar="grlpartnumber" id="growlpartnumber"/>         
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="#{custom.labels.get('IDS_PART_NUM')}" for="inptxtpoPartNum"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <h:panelGroup class="ui-inputgroup"  >
                        <!--Bug #8045: Purchase Orders > Add line item > search and choose part number by harshithad on 26/05/22-->
                        <p:autoComplete  id="inptxtpoPartNum" value="#{poServices.poDtl.poPartNo}"
                                         maxlength="60" minQueryLength="2" panelStyleClass="acpartNum"
                                         tabindex="1" widgetVar="partNoWdgt"
                                         completeMethod="#{poServices.completePrinciPartNum}"
                                         >
                            <p:ajax event="itemSelect" listener="#{poServices.onSelectPrincipartno}"  />
                            <!--                            Task#4585:Customer Pricing > PO > Get the best Price   21/05/21  by harshithad-->
                            <!--Bug #8045: Purchase Orders > Add line item > search and choose part number by harshithad on 26/05/22-->
                            <p:ajax  event="change" 
                                     listener="#{poServices.onChangePartNo()}" onstart="if(PF('partNoWdgt').panel.is(':visible')) return false;" />


                        </p:autoComplete>
                        <h:inputHidden id="hdninputPoPartNum" value="#{poServices.poDtl.poPartNo}"   />
                        <!--pms:2982 11/01/2021 udaya b -->
                        <!--                        Task#3765-CRM-4050: PO Entry: enter button in line item details triggers part number lookup and loses place ho    10-03-2021  by harshithad-->
                        <p:commandButton  icon="fa fa-search" 
                                          title="Choose #{custom.labels.get('IDS_PART_NUM')}" 
                                          immediate="true" 
                                          tabindex="2"
                                          update = ":dtPartNumForm:productLookup"
                                          id="btnPOLIPartNo"
                                          actionListener="#{viewProductLookUpService.list('applyPartNum',poServices.poHdr.poPrinciId)}"
                                          oncomplete="PF('dlgPartNum').show();"
                                          styleClass="btn-info btn-xs" />
                    </h:panelGroup>

                </div>
                <!--Feature #8620 CRM-5990  PO: UAT: changing commission rate on line item not reflecting on header by harshithad--> 
                <!--#7942 CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad-->
                <div class="ui-sm-12 ui-md-6 ui-lg-6"></div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                    <p:outputLabel value="#{custom.labels.get('IDS_PROD_FAMILY')}" for="txtProdFamily"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <h:panelGroup class="ui-inputgroup"  >
                        <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                        <p:inputText id="txtProdFamily" tabindex="3" value="#{poServices.poDtl.prodFamily}" readonly="true"/>
                        <h:inputHidden id="transDtlProdFamilyID" value="#{poServices.poDtl.prodFamily}" />
                        <!--pms:2982 11/01/2021 udaya b -->
                        <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true"  tabindex="4"
                                          process="@this"
                                          id="btnPOLIProdFamly"
                                          update =":dtProdFamilyForm:prodFamilyDtl"
                                          actionListener="#{prodFamilyMst.listProduct('applyProductFamily')}" 
                                          oncomplete="PF('dlgProdfamilyLookup').show()"  
                                          styleClass="btn-info btn-xs" />
                    </h:panelGroup>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value=" #{custom.labels.get('IDS_CUSTOMER')} #{custom.labels.get('IDS_PART_NUM')}" for="inpTxtoCustPartNum"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
<!--                    <p:inputText value="#{poServices.poDtl.poCustPartNo}" id="inpTxtoCustPartNum" maxlength="60" 
                                 tabindex="3">
                                                //PMS:1375
                                                //Seema 06/08/2020
                        <p:ajax process="@this"/>
                    </p:inputText>-->
                    <h:panelGroup class="ui-inputgroup"  >
                        <!--                    //PMS:1506
                                            //seema - 29/08/2020-->
                        <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                        <p:autoComplete  id="inpTxtoCustPartNum" value="#{poServices.poDtl.poCustPartNo}"
                                         maxlength="60" minQueryLength="2"
                                         tabindex="5"
                                         completeMethod="#{poServices.completeCustPartNum}"
                                         >
                            <p:ajax event="itemSelect" listener="#{poServices.onCustSelectPrincipartno}"  />
                            <p:ajax process="@this" event="change" 
                                    listener="#{poServices.onCustPartNumberCahnge(poServices.poDtl)}"  />
                        </p:autoComplete>
                        <h:inputHidden id="hdninputCustPartNum" value="#{poServices.poDtl.poCustPartNo}"   />
                        <p:commandButton  icon="fa fa-search" 
                                          title="Choose  #{custom.labels.get('IDS_CUSTOMER')} #{custom.labels.get('IDS_PART_NUM')}"
                                          immediate="true" 
                                          tabindex="6"
                                          process="@this"
                                          id="btnPOLICustPartNo"
                                          actionListener="#{viewCustProductLookUpService.list('applyCustProd',poServices.poHdr.poPrinciId,poServices.poHdr.poCustId,poServices.poDtl.poPartNo)}"
                                          styleClass="btn-info btn-xs" />
                    </h:panelGroup>
                </div>
                <!--#7942 CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                    <p:outputLabel value="Part Description" for="inpTxtAreaPartNumDesc"/>
                </div>
                <div class="ui-sm-12 ui-md-10 ui-lg-10"> 
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                    <!--Feature #9318: CRMSYNC-56  STMicro Opportunity : specific p/n  PO, Imp Trans  by harshithad on 27/10/22-->
                    <p:inputTextarea styleClass="partNumBox" value="#{poServices.poDtl.poPartDesc}"  id="inpTxtAreaPartNumDesc" autoResize="false" tabindex="7" maxlength="350" rows="2">
                        <p:ajax event="keyup" listener="#{transDtl.checkvalidate(poServices.poDtl.poPartDesc,1 )}" />
                    </p:inputTextarea>
                </div>

                <div class="ui-sm-12 ui-md-2 ui-lg-2">

                    <p:outputLabel value="UOM" for="oneMenuUom"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
             <!--Bug #13355:Incidental Finding:Bookings/orders:Failure to Add Newly Imported UOM in Product Unit Measurement Tab  by harshitha-->
                    <p:selectOneMenu styleClass="tds1"  onfocus="this.select();" value="#{poServices.poDtl.poUomId}" style="border-radius: 5px; background: white;" id="oneMenuUom" tabindex="8">
                        <f:selectItem itemLabel="[Select]" itemValue=""/>
                       <!--Bug #13355:Incidental Finding:Bookings/orders:Failure to Add Newly Imported UOM in Product Unit Measurement Tab  by harshitha-->
                        <f:selectItems value="#{unitOfMeasurementsService.prodUoms()}"  var="prod" itemValue="#{prod.recId}" itemLabel="#{prod.uomName}"/>    

                        <!--//Feature #2941: #2945:CRM-3709-->  
                        <!--pms:2982 11/01/2021 udaya b -->
                        <!--Bug #13355:Incidental Finding:Bookings/orders:Failure to Add Newly Imported UOM in Product Unit Measurement Tab  by harshitha-->
                        <p:ajax  process="@this" event="change"
                                 update=":frmPoDetails:inpNumTotalPrice :frmPoDetails:inptxtUOM"

                                 listener="#{poServices.poDtl.calculateUomUnitsandExtprice(poServices.poDtl.poUnitPrice, poServices.poDtl.poUomId, poServices.poDtl.poPlanQty,poServices.poDtl.poOrderQty)}"     />

                    </p:selectOneMenu>

                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="UOM Units" for="inptxtUOM"/>

                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->    
                    <p:inputText value="#{poServices.poDtl.poUomunits}" tabindex="9" id="inptxtUOM"  maxlength="11" readonly="true" immediate="true">
                        <p:ajax process="@this"/>
                    </p:inputText>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Ordered Quantity" for="inpNumOrdrQty"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                    //PMS:2638
                                        //Seema - 09/12/2020-->

                    <!--Bug #2968:CRM-3714; PO entry: make mouse click enable commisison entry without backing out the zeros-->
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                    <p:inputNumber  id="inpNumOrdrQty"  maxlength="9" onfocus="this.select();" 
                                    decimalPlaces="3" maxValue="*********" placeholder="0.000"
                                    tabindex="10" value="#{poServices.poDtl.poOrderQty}">
                        <!--                          //PMS:1506
                                //Seema - 01/09/2020-->
                        <!--                        <p:ajax process="@this" event="blur" 
                                                        update=":frmPoDetails:inpNumTotalPrice :frmPoDetails:inpNumProjectedComm :frmPoDetails:poDtlPlnQty"
                                                        listener="#{poServices.poDtl.calculateExtPrice(poServices.poDtl.poShippingQty, poServices.poDtl.poUnitPrice, poServices.poDtl.poUomunits, poServices.poDtl.poOrderQty)}"     />-->
                        <!--//Feature #2941: #2945:CRM-3709-->  
                        <!--pms:2982 11/01/2021 udaya b -->
                        <!--Feature #7942 PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 29/08/22-->
                       <!--Task #12010:  Dashboard import feature for Booking/Order to generate POs, Bookings and Comm Trans (conditionally)   by harshithad on 20/09/23-->
                        <p:ajax process="@this" event="blur" 
                                update=":frmPoDetails:inpNumTotalPrice frmPoDetails:inpNumProjectedComm :frmPoDetails:inptNumbrOverageAmt"
                                listener="#{poServices.poDtl.onOdrQtyChangre( poServices.poDtl.poUnitPrice, poServices.poDtl.poUomunits, poServices.poDtl.poOrderQty,poServices.poDtl.poPlanQty ,poServices.poDtl.poOrderQty)}"     />
                        <!--Task#4585:Customer Pricing > PO > Get the best Price   21/05/21  by harshithad-->
                        <!--Feature #7942 PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 29/08/22-->
                       <!--Task #12010:  Dashboard import feature for Booking/Order to generate POs, Bookings and Comm Trans (conditionally)   by harshithad on 20/09/23-->
                        <p:ajax process="@this" event="change" 
                                listener="#{poServices.onQtyChange(poServices.poDtl)}" update="frmPoDetails:inpNumUnitPrice :frmPoDetails:inpNumTotalPrice frmPoDetails:inpNumProjectedComm :frmPoDetails:inptNumbrOverageAmt" />
                    </p:inputNumber>
                </div>
                <!--#7942 CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad-->
                <!--Feature #9051PRIORITY: CRM-6109: PO  Adjustment entries for Shipments and Commission by harshithad on 19/09/22-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--Feature #9183: ESCALATIONS CRM-6135   TMS: Accounting Date on PO by harshithad 29/09/22-->
                    <p:outputLabel value="Accounting Date" for="calAcctDate" />             
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4" >   
                    <!--Feature #9183: ESCALATIONS CRM-6135   TMS: Accounting Date on PO by harshithad 29/09/22-->
                    <p:calendar  pattern="#{globalParams.dateFormat}"   id="calAcctDate"  title="Accounting date is used in accrual accounting methods only" value="#{poServices.poDtl.poAcctDate}" 
                                 tabindex="12" >  
                        <p:ajax event="dateSelect" process="@this" />
                        <p:ajax event="change" process="@this"/>
                    </p:calendar>                 
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                    <p:outputLabel value="Planned Quantity" for="poDtlPlnQty"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                    //PMS:2638
                //Seema - 09/12/2020-->
                    <!--#7942 CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad-->
                    <p:inputNumber value="#{poServices.poDtl.poPlanQty}"   
                                   id="poDtlPlnQty"  maxlength="9" placeholder="0.000"
                                   decimalPlaces="3" maxValue="*********"
                                   tabindex="11">  
                        <!--                        <p:ajax process="@this"/>-->

                        <!--pms:2982 11/01/2021 udaya b -->
                      <!--Task #12010:  Dashboard import feature for Booking/Order to generate POs, Bookings and Comm Trans (conditionally)   by harshithad on 20/09/23-->
                        <p:ajax process="@this" event="blur" 
                                update=":frmPoDetails:inpNumTotalPrice :frmPoDetails:inpNumProjectedComm :frmPoDetails:inptNumbrOverageAmt"
                                listener="#{poServices.poDtl.calculateExtPrice(poServices.poDtl.poUnitPrice, poServices.poDtl.poUomunits, poServices.poDtl.poPlanQty,poServices.poDtl.poOrderQty)}"/>
                        <!--                    Task#4585:Customer Pricing > PO > Get the best Price   21/05/21  by harshithad-->
                        <!--Feature #7942 PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 29/08/22-->
                        <!--Task #12010:  Dashboard import feature for Booking/Order to generate POs, Bookings and Comm Trans (conditionally)   by harshithad on 20/09/23-->
                        <p:ajax process="@this" event="change" 
                                listener="#{poServices.onQtyChange(poServices.poDtl)}" update="frmPoDetails:inpNumUnitPrice :frmPoDetails:inpNumTotalPrice frmPoDetails:inpNumProjectedComm :frmPoDetails:inptNumbrOverageAmt"  />
                    </p:inputNumber>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                    <!--                     //PMS:1480
                                        //Seema - 19/08/2020-->
                    <p:outputLabel value="#{custom.labels.get('IDS_PLAN_DATE')}" for="calPlnDate"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                    <p:calendar  pattern="#{globalParams.dateFormat}"  id="calPlnDate" value="#{poServices.poDtl.poPlanDate}" 
                                 tabindex="12">  
                        <p:ajax event="dateSelect" process="@this" />
                    </p:calendar>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="#{custom.labels.get('IDS_STD_PRICE')}" for="inpNumStdPrice"/>

                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                    //PMS:2638
                    //Seema - 09/12/2020-->
                    <!--                    Task#3762-CRM-4000: PO unit price decimals lost during downflow to Invoice   10-03-2021  by harshithad-->
                    <!--Task#4585:Customer Pricing > PO > Get the best Price   21/05/21  by harshithad-->
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                    <p:inputNumber decimalPlaces="6" value="#{poServices.poDtl.poStdRate}"    maxValue="*********.999999" id="inpNumStdPrice" 
                                   style="width: 97%"  maxlength="20" styleClass="partNumBox" tabindex="13" placeholder="0.000000"
                                   converterMessage="Must be a signed decimal number." disabled="#{poServices.stdPrice}" >
                        <!--                          //PMS:1506
                                //Seema - 01/09/2020-->
                        <!--                        <p:ajax event="blur" 
                                                        update=":frmPoDetails:inpNumUnitPrice :frmPoDetails:inpNumTotalPrice :frmPoDetails:inpNumProjectedComm"
                                                        listener="#{poServices.poDtl.calculateUnitPrice(poServices.poDtl.poStdRate, poServices.poDtl.poMultiplier, poServices.poDtl.poShippingQty, poServices.poDtl.poUomunits,poServices.poDtl.poOrderQty)}" 
                                                        />-->
                        <!--                        //PMS:2758
                                                //seema - 18/12/2020-->
                        <!--//Feature #2941: #2945:CRM-3709-->  
                        <!--pms:2982 11/01/2021 udaya b -->
                        <!--                        Task#4585:Customer Pricing > PO > Get the best Price   21/05/21  by harshithad-->

                        <!--Bug #4982: purchase order->changed multiplier value->projected commission not changed  25/06/21 by harshithad-->
                     <!--Task #12010:  Dashboard import feature for Booking/Order to generate POs, Bookings and Comm Trans (conditionally)   by harshithad on 20/09/23-->
                        <p:ajax process="@this" event="blur" 
                                update=":frmPoDetails:inpNumUnitPrice :frmPoDetails:inpNumTotalPrice :frmPoDetails:inpNumProjectedComm :frmPoDetails:inptNumbrOverageAmt"
                                listener="#{poServices.poDtl.calculateUnitPrice(poServices.poDtl.poStdRate, poServices.poDtl.poMultiplier, poServices.poDtl.poPlanQty, poServices.poDtl.poUomunits ,poServices.poDtl.poOrderQty)}" 
                                />
                    </p:inputNumber>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Multiplier" for="inpNumMultiplr"/>

                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--Task#4585:Customer Pricing > PO > Get the best Price   21/05/21  by harshithad-->
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                    <p:inputNumber decimalPlaces="6"  maxValue="9999.999999" value="#{poServices.poDtl.poMultiplier}" id="inpNumMultiplr"  
                                   maxlength="20" converterMessage="Must be a signed decimal number." tabindex="14"
                                   disabled="#{poServices.multiplier}">       
                        <!--//Feature #2941: #2945:CRM-3709-->  
                        <!--pms:2982 11/01/2021 udaya b -->
                    <!--Task #12010:  Dashboard import feature for Booking/Order to generate POs, Bookings and Comm Trans (conditionally)   by harshithad on 20/09/23-->
                        <p:ajax process="@this" event="blur" 
                                update=":frmPoDetails:inpNumUnitPrice :frmPoDetails:inpNumTotalPrice :frmPoDetails:inpNumProjectedComm  :frmPoDetails:inptNumbrOverageAmt"
                                listener="#{poServices.poDtl.calculateUnitPrice(poServices.poDtl.poStdRate, poServices.poDtl.poMultiplier, poServices.poDtl.poPlanQty, poServices.poDtl.poUomunits,poServices.poDtl.poOrderQty)}" 
                                />
                    </p:inputNumber>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--Feature #9141:PRIORITY PO: Multiplier: Reverse calculate multiplier on best price  by harshitha on 28/09/22-->
                    <p:outputPanel id="otptnlRenderAstersk">
                        <p:outputLabel value="#{custom.labels.get('IDS_UNIT_PRICE')}" for="inpNumUnitPrice"/>
                        <!--Task#4585:Customer Pricing > PO > Get the best Price   21/05/21  by harshithad-->                   
                        <p:outputLabel id ="otptLblAstersk" value="**" rendered="#{poServices.poDtl.poCustPriceFlag==1}"/>
                    </p:outputPanel>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--Task#4585:Customer Pricing > PO > Get the best Price   21/05/21  by harshithad-->
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22--> 
                    <!--Bug #10010 PO>Line item> Edit >Decimal places for Unit price differs in UI and structure by harshithad on 07/12/22-->
                    <p:inputNumber value="#{poServices.poDtl.poUnitPrice}"   tabindex="15"
                                   id="inpNumUnitPrice" maxlength="14" styleClass="partNumBox" 
                                   decimalPlaces="6" maxValue="*********999999" disabled="#{poServices.unitPrice}"> 
                        <!--Task#4585:Customer Pricing > PO > Get the best Price   21/05/21  by harshithad-->
                        <p:ajax process="@this" event="blur" 
                                update=":frmPoDetails:inpNumTotalPrice :frmPoDetails:inpNumProjectedComm"
                                listener="#{poServices.poDtl.calculateExtPrice(poServices.poDtl.poUnitPrice, poServices.poDtl.poUomunits, poServices.poDtl.poPlanQty,poServices.poDtl.poOrderQty)}"/>
                        <!--Feature #9141:PRIORITY PO: Multiplier: Reverse calculate multiplier on best price  by harshitha on 28/09/22-->
                        <p:ajax event="change" process="@this" listener="#{poServices.calculateMultiplier(poServices.poDtl.poUnitPrice, poServices.poDtl.poStdRate)}" update="frmPoDetails:inpNumMultiplr" />
                    </p:inputNumber>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Total Amount" for="inpNumTotalPrice"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4" > 
                    <!--                    //PMS:2443
                  //seem a- 20/11/2020-->
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                    <p:inputNumber value="#{poServices.poDtl.poTotalPrice}" tabindex="16"  inputStyle="text-align:  right"
                                   id="inpNumTotalPrice" maxlength="14"  styleClass="partNumBox" 
                                   onfocus="this.select();" disabled="true"
                                   decimalPlaces="2" maxValue="*********999999"
                                   >

                    </p:inputNumber>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                    <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                    <p:outputLabel value="#{custom.labels.get('IDS_COMMISSION')} Rate" for="inpNumCommRate"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                    //PMS:2443
                 //seem a- 20/11/2020-->
                    <!--                    //PMS:2638
                  //Seema - 09/12/2020-->

                    <!--Feature #7942: CRM-5545: PO Flow: Line item: User Selectable commission percentage  by harshithad 11/05/2022--> 
                    <p:inputNumber value="#{poServices.poDtl.poCommRate}"  id="inpNumCommRate" maxlength="5" inputStyle="text-align:  right"
                                   tabindex="17" styleClass="partNumBox" placeholder="0.000"
                                   decimalPlaces="2" maxValue="100"
                                   > 
                        <!--                        <f:convertNumber maxFractionDigits="2" maxIntegerDigits="15"/>-->
                        <!--pms:2982 11/01/2021 udaya b -->
                        <p:ajax process="@this" event="blur" 
                                update=":frmPoDetails:inpNumProjectedComm"
                                listener="#{poServices.poDtl.calculateProjectedCommission(poServices.poDtl.poTotalPrice,poServices.poDtl.poCommRate)}"
                                />
                    </p:inputNumber>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--//09-06-2022 : 8181 : Direct Request: Purchase Orders: Hide Projected Commission at header and line item level-->
              <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->   
                    <p:outputLabel value="Projected #{custom.labels.get('IDS_COMMISSION')}" for="inpNumProjectedComm" rendered="#{poServices.accessCommissionBol}"/>
                </div>

                <div class="ui-sm-12 ui-md-4 ui-lg-4" > 
                    <h:panelGroup class="ui-inputgroup"  >
                        <!--                    //PMS:2443
                  //seem a- 20/11/2020-->
                        <!--//09-06-2022 : 8181 : Direct Request: Purchase Orders: Hide Projected Commission at header and line item level-->
                        <!--Feature #9051PRIORITY: CRM-6109: PO  Adjustment entries for Shipments and Commission by harshithad on 19/09/22-->
                        <p:inputNumber value="#{poServices.poDtl.poCommProjected}" rendered="#{poServices.accessCommissionBol}" disabled="#{poServices.disableProjCom}"   inputStyle="text-align:  right"
                                       id="inpNumProjectedComm" maxlength="14"  styleClass="partNumBox"  
                                       onfocus="this.select();" tabindex="18"
                                       decimalPlaces="2" maxValue="*********999999"
                                       >
                            <p:ajax event="change" process="@this" update=":frmPoDetails:inpNumProjectedComm"/>
                        </p:inputNumber>

                    </h:panelGroup>

                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                    <!--Task #12010:  Dashboard import feature for Booking/Order to generate POs, Bookings and Comm Trans (conditionally)   by harshithad on 20/09/23-->
                    <p:outputLabel value="Overage %" />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <p:inputNumber value="#{poServices.poDtl.poOveragePct}" id="inptNumbrPoOvrgePct"   inputStyle="text-align:  right"
                                    maxlength="14"  styleClass="partNumBox"  
                                   tabindex="18" placeholder="0.00"
                                   decimalPlaces="2" maxValue="*********999999" 
                                   >
                        <p:ajax event="blur"   listener="#{poServices.poDtl.calculateUnitPrice(poServices.poDtl.poStdRate, poServices.poDtl.poMultiplier, poServices.poDtl.poPlanQty, poServices.poDtl.poUomunits,poServices.poDtl.poOrderQty)}" update=":frmPoDetails:inpNumUnitPrice :frmPoDetails:inpNumTotalPrice :frmPoDetails:inpNumProjectedComm :frmPoDetails:inptNumbrOverageAmt" process="@this" />
                    </p:inputNumber>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                    <p:outputLabel value="Overage Amt." />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <p:inputNumber value="#{poServices.poDtl.poOverageAmt}" disabled="true" id="inptNumbrOverageAmt"    inputStyle="text-align:  right"
                                   maxlength="14"  styleClass="partNumBox"  
                                   tabindex="18" placeholder="0.00"
                                   decimalPlaces="2" maxValue="*********999999"
                                   >
                        <p:ajax process="@this"/>
                    </p:inputNumber>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                    <!--Task #12010:Dashboard import feature for Booking/Order to generate POs, Bookings and Comm Trans (conditionally)  by harshithad on 10/03/23-->
                    <p:outputLabel value="Unit Cost" for="inpNumUnitCost"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <p:inputNumber value="#{poServices.poDtl.poUnitCost}"    inputStyle="text-align:  right"
                                   id="inpNumUnitCost" maxlength="14"  styleClass="partNumBox"  
                                   tabindex="18" placeholder="0.000"
                                   decimalPlaces="2" maxValue="*********999999"
                                   >
                        <p:ajax process="@this"/>
                    </p:inputNumber>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                    <p:outputLabel value="Shipping Status" for="onemenuShpStatus"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--Bug #8046:  purchase order ->shipped line item clone issue by harshithad 09/06/22-->    
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                    <p:selectOneMenu id="onemenuShpStatus" value="#{poServices.poDtl.poShippingStatus}" 
                                     tabindex="19"   
                                     disabled="#{poServices.poDtl.poShippingStatus eq 1 and poServices.enableShipFields eq 0  }">
                        <!--                          //PMS:1499
                                //seema - 25/08/2020-->
                        <f:selectItem itemValue="1" itemLabel="Shipped"  />
                        <f:selectItem itemValue="0" itemLabel="Not Shipped"  />
                        <!--                        //PMS:1472
                                                //seema - 19/08/2020-->
                        <!--Bug #8046:  purchase order ->shipped line item clone issue by harshithad 09/06/22-->
                        <p:ajax  process="@this" event="change" listener="#{poServices.onStatusChange(poServices.poDtl.poShippingStatus)}" update="frmPoDetails:inpNumShpQty" />
                    </p:selectOneMenu>
                </div>
                <!--Task #12010:Dashboard import feature for Booking/Order to generate POs, Bookings and Comm Trans (conditionally)  by harshithad on 10/03/23-->
                <!--<div class="ui-sm-12 ui-md-6 ui-lg-6" ></div>-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Shipped Quantity" for="inpNumShpQty"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                    //PMS:2638
                    //Seema - 09/12/2020-->
                    <!--                    //PMS:2639
                    //Seema- 10/12/2020-->
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                    <p:inputNumber value="#{poServices.poDtl.poShippingQty}"   onfocus="this.select();" 
                                   disabled="true"
                                   id="inpNumShpQty"  maxlength="9" placeholder="0.000"
                                   decimalPlaces="3" maxValue="*********"
                                   tabindex="20">  
                        <!--                          //PMS:1506
                                //Seema - 01/09/2020-->
                        <!--                        <p:ajax process="@this" event="blur" 
                                                        update=":frmPoDetails:inpNumTotalPrice :frmPoDetails:inpNumProjectedComm"
                                                        listener="#{poServices.poDtl.calculateExtPrice(poServices.poDtl.poShippingQty,poServices.poDtl.poUnitPrice, poServices.poDtl.poUomunits, poServices.poDtl.poOrderQty)}"     />-->
                    </p:inputNumber>
                </div>     

                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                    <!--                    //PMS:1480
                                        //Seema - 19/08/2020-->
                    <p:outputLabel  value="#{custom.labels.get('IDS_SHIPPED_DATE')}" for="calShpDate"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                    //PMS:2639
                   //Seema- 10/12/2020-->
                    <!--Bug #8046:  purchase order ->shipped line item clone issue by harshithad 09/06/22-->
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                    <p:calendar  pattern="#{globalParams.dateFormat}"  id="calShpDate" 
                                 value="#{poServices.poDtl.poShippingDate}" 
                                 disabled="#{poServices.poDtl.poShippingStatus eq 1 and poServices.enableShipFields eq 0 }"
                                 tabindex="21">  
                        <p:ajax event="dateSelect" process="@this" />
                        <!--Bug #8046:  purchase order ->shipped line item clone issue by harshithad 09/06/22-->
                        <p:ajax event="change" process="@this" />
                    </p:calendar>
                </div>
                <!--#7942 CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Invoice Number" for="inpTxtrpoInvNum" />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                    //PMS:2639
                                        //Seema- 10/12/2020-->
                    <!--Bug #8046:  purchase e ->shipped line item clone issue by harshithad 09/06/22-->
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                    <p:inputText value="#{poServices.poDtl.poInvcNumber}"  id="inpTxtrpoInvNum"  maxlength="30"
                                 disabled="#{poServices.poDtl.poShippingStatus eq 1 and poServices.enableShipFields eq 0 }"
                                 tabindex="22">
                        <!--                         //PMS:1375
                                                //Seema 06/08/2020-->
                        <p:ajax process="@this"/>
                    </p:inputText>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Invoice Date" for="calInvDate" />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                    //PMS:2639
                    //Seema- 10/12/2020-->
                    <!--Bug #8046:  purchase order ->shipped line item clone issue by harshithad 09/06/22-->
                    <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                    <p:calendar  pattern="#{globalParams.dateFormat}"  id="calInvDate"  disabled="#{poServices.poDtl.poShippingStatus eq 1 and poServices.enableShipFields eq 0 }"
                                 value="#{poServices.poDtl.poInvcDate}"  converterMessage="Invalid Date format" 
                                 tabindex="23">  
                        <p:ajax event="dateSelect" process="@this"/>
                        <!--Bug #8046:  purchase order ->shipped line item clone issue by harshithad 09/06/22-->
                        <p:ajax event="change" process="@this"/>
                    </p:calendar>
                </div>
                <!--Task #12010:Dashboard import feature for Booking/Order to generate POs, Bookings and Comm Trans (conditionally)  by harshithad on 10/03/23-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Brand"  />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:inputText value="#{poServices.poDtl.poBrand}">
                        <p:ajax process="@this"/>
                    </p:inputText>
                </div>
            </div>
            <!--#7942 CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad-->
            <p:spacer height="2px"/>
            <div class="button_bar" align="center">
                <!--                Task#4585:Customer Pricing > PO > Get the best Price   21/05/21  by harshithad-->
                <!--              Bug #5926: Purchase Orders:  Line item list update issue on filter and add  bu harshithad 24/09/21-->
                <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
                <!--Bug #8046:  purchase order ->shipped line item clone issue by harshithad 09/06/22-->
                <!--Feature #7942: PRIORITY CRM-5545: PO Flow: Line item: User Selectable commission percentage by harshithad on 25/08/22-->
                <p:commandButton id="submitButton" value="Save" styleClass="btn btn-success  btn-xs" process="@this" 
                                 immediate="true" onclick="PF('saveButton').disable()" widgetVar="saveButton" 
                                 actionListener="#{poServices.validatePo()}" tabindex="24" update="frmPoDetails"
                                 oncomplete="PF('saveButton').enable();">    
                </p:commandButton>
                <p:spacer width="4px"/>
                <p:commandButton value="Cancel"    onclick="PF('dlgPoDetail').hide()" tabindex="25"
                                 id="btnPOLIcancel"
                                 type="button" class="btn btn-xs btn-warning"  />
            </div> 
            <!--Task#4585:Customer Pricing > PO > Get the best Price   21/05/21  by harshithad-->
            <!--Feature #9141:PRIORITY PO: Multiplier: Reverse calculate multiplier on best price  by harshitha on 28/09/22-->
            <p:outputPanel id="otptPnlRenderCustPriceMessage" >
                <p:outputLabel id="otptLblMessge" rendered="#{poServices.poDtl.poCustPriceFlag==1}" value="** #{custom.labels.get('IDS_CUSTOMER')} Special Price applied"/>
            </p:outputPanel>
        </h:form>
    </p:dialog>
    <!--    TASK#3760-Issue in PO Line item  11-03-2021  by harshithad-->
    <!--     TASK#3760-Issue in PO Line item  12-03-2021  BY harshithad-->
  <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
    <p:confirmDialog header="Confirmation" global="false" message="UOM, #{custom.labels.get('IDS_STD_PRICE')}, #{custom.labels.get('IDS_COMMISSION')} Rate, #{custom.labels.get('IDS_PROD_FAMILY')} and Part Description are not set. Do you want to continue?"   id="confirmPO"
                     widgetVar="confirmMsg" >


        <h:form >
            <div align="center">
                <!--Bug #5926: Purchase Orders:  Line item list update issue on filter and add  bu harshithad 24/09/21-->
                <!--Bug #8046: purchase order ->shipped line item clone issue by harshithad on 20/06/22-->
                <p:commandButton value="Yes"   oncomplete="PF('confirmMsg').hide();" 
                                 styleClass="btn btn-success  btn-xs" id="btnYes"
                                 actionListener="#{poServices.savePoDtl()}"                                                               
                                 />
                <p:spacer width="4px"/>

                <p:commandButton value="No" type="button" id="btnNo"
                                 styleClass="btn btn-danger  btn-xs" 
                                 onclick="PF('confirmMsg').hide()" />     
            </div>
        </h:form>
    </p:confirmDialog>




    <!--    //PMS:1749
        //Seema - 18/09/2020-->
    <style>
        .acpartNum.ui-autocomplete-panel{
            width: 235px!important;
            height: 350px!important;
        }
    </style>
    <script>
        $('body').on('mousedown', '.ui-autocomplete-panel', function (event) {
            event.stopImmediatePropagation();
        });
    </script>

</ui:composition>