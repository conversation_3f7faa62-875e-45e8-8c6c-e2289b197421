<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<!-- Feature #11460: CRM-7082   Paperclip on orders list - file preview window popup by harshith<PERSON> on 19/06/23-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"  
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:p="http://primefaces.org/ui" 
                 xmlns:h="http://xmlns.jcp.org/jsf/html">

    <p:dialog   id="dlgPOPrevw" widgetVar="dlgAttchmentPreview" styleClass="disable-scroll"   header="PO Number: #{poServices.poHdr.poNumber} - Attachments" width="500px"  height="400px">
        <h:form id="frmPreview">
            <p:dataTable id="dtAttchments" widgetVar="dtPoAtchmt" emptyMessage="No Attachments" value="#{poServices.attachmentLst}"   var="atch" scrollable="true"  scrollHeight="400" style="width: 100%">        
                <p:column headerText="File Name"  filterBy="#{atch.poAttaName}" filterMatchMode="contains"  sortBy="#{atch.poAttaName}" >
                    <p:commandLink value="#{atch.poAttaName}" ajax="true"  actionListener="#{poServices.showPreviewDlg(atch.poAttaName)}"/>
                </p:column>
            </p:dataTable>
        </h:form>
    </p:dialog>
   <style>
        
.disable-scroll .ui-dialog-content {
 
    overflow: hidden !important;
}

   </style>
</ui:composition>
