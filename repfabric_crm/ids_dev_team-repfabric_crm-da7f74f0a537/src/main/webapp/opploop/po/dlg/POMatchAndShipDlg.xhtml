<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"

                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--Task #3966:Marking PO’s as shipped based on Sales data imported     08/04/21 by harshithad-->
    <!--    Task #4521:CRM-4303: Match and Ship: Updates to UI  13/05/21  by harshithad-->
    <!--Task #14889: CRM-8681 PO: Match and Ship: UI adjustments-->
    <p:dialog id="dlgMtchAndShip" resizable="true"  maximizable="true" header="Match and Ship" width="1200" height="600"
              widgetVar="mtchAndShip"  modal="true" responsive="true">
        <p:ajax event="close" listener="#{poServices.onSelect()}"/>
        <h:form id="frmMatchAndShip">


            <div class="row" style="margin-left: 10px ;margin-right: 10px">
                <!--Feature #4944:CRM-4434: Purchase Orders > Match and Ship  25/06/21 by harshithad-->
                <!--Feature #4944:CRM-4434: Purchase Orders > Match and Ship  25/06/21 by harshithad-->
                <p:commandButton id="cmdBtnAutoMtch" value="Auto Match and Ship"   
                                 oncomplete="PF('poConfrm').show();PF('AutoMtchDt').clearFilters()"
                                 onclick="PF('AutoMtchDt').clearFilters()"
                                 actionListener="#{poServices.loadDefAutoMtch()}"
                                 update="frmCnfrm frmCnfrm:dtAutoLI"
                                 styleClass="btn btn-primary btn-xs" 

                                 />





                <p:commandButton id="cmdBtnShw" value="Load Unshipped"   
                                 actionListener="#{poServices.loadUnshipped()}"
                                 style="float:right "

                                 styleClass="btn btn-primary btn-xs" 
                                 update="frmMatchAndShip"
                                 />
                <p:selectOneRadio id="rdBtn" value="#{poServices.lineItemHeader}" style="float:right ">
                    <f:selectItem  itemLabel="By #{custom.labels.get('IDS_LINE_ITEM')}" itemValue="0" />
                    <f:selectItem itemLabel="By Header" itemValue="1" />
                    <p:ajax event="change" listener="#{poServices.onSelect()}"  
                            process="@this"

                            update="frmMatchAndShip :frmDlg"
                            />
                </p:selectOneRadio>
                <!--                 Feature #4588: CRM-4287:  Match and Ship Line Item > Show All Unshipped Line Items  22/05/21 by harshithad-->
                <!--Feature #11596:CRM-7133   PO: need to edit price without line item by harshithad on 13/07/23-->
                <p:selectBooleanCheckbox  id="oneMenuShowAll" disabled="#{poServices.lineItemHeader==1 and poServices.excludePoDefaultLineItemCreation}"  itemLabel="Show All" value="#{poServices.showAll}" style="float:right;margin-top:4px !important"/>

                <p:spacer width="4"/>


            </div>
            <p:spacer height="20px"/>



            <div style="margin-left: 10px;margin-right: 10px "  >
                <p:dataTable id="dtPOdtl" widgetVar="poDtl" var="dtl"  value="#{poServices.poDtlList}" rendered="#{poServices.lineItemHeader == 0}"  paginator="true"  rows="10"  tableStyle="table-layout:auto" resizableColumns="true">
                    <!--    Task #4521:CRM-4303: Match and Ship: Updates to UI  13/05/21  by harshithad-->
                    <!--4521-->
                    <p:column id="colPoNo" headerText="PO No." sortBy=" #{dtl.poNumber}" filterBy=" #{dtl.poNumber}" filterMatchMode="contains">
                        #{dtl.poNumber}
                    </p:column>
                    <p:column id="colPoDte" headerText="PO Date" sortBy=" #{dtl.poDate}" filterBy="#{globalParams.formatDateTime(dtl.poDate,'da')}" filterMatchMode="contains">
                        <p:outputLabel value="#{globalParams.formatDateTime(dtl.poDate,'da')}" />  
                    </p:column>
                    <p:column id="colPrinci"    headerText="#{custom.labels.get('IDS_PRINCI')}" sortBy="#{dtl.poPrinciName}" filterBy="#{dtl.poPrinciName}" filterMatchMode="contains">
                        #{dtl.poPrinciName}
                    </p:column>
                    <p:column id="colCust"  headerText="#{custom.labels.get('IDS_CUSTOMER')}" sortBy="#{dtl.poCustName}" filterBy="#{dtl.poCustName}" filterMatchMode="contains">
                        #{dtl.poCustName}
                    </p:column>
                    <p:column id="colPartNo" headerText="#{custom.labels.get('IDS_PART_NUM')}" sortBy=" #{dtl.poPartNo}" filterBy=" #{dtl.poPartNo}" filterMatchMode="contains">
                        #{dtl.poPartNo}
                    </p:column>
                    <p:column id="colPlnDte"  headerText="Pln.Date" sortBy="#{dtl.poPlanDate}" filterBy="#{globalParams.formatDateTime(dtl.poPlanDate,'da')}" filterMatchMode="contains" >
                        <p:outputLabel value="#{globalParams.formatDateTime(dtl.poPlanDate,'da')}" /> 
                    </p:column>
                    <p:column id="colPlnQty" headerText="Pln.Qty" sortBy="#{dtl.poPlanQty}" filterBy="#{dtl.poPlanQty}" filterMatchMode="contains">
                        <p:outputLabel id="otptLblPlanQty" value="#{dtl.poPlanQty}" style="float: right"/> 
                    </p:column>
                    <p:column id="colTotPrice" headerText="Total Price" sortBy="#{dtl.poTotalPrice}" filterBy="#{dtl.poTotalPrice}" filterMatchMode="contains" >
                        <p:outputLabel id="otptLblTotPric" value=" #{dtl.poTotalPrice}" style="float: right"/>

                    </p:column>

                    <p:column >   
                        <!--        Feature #5662:CRM-4667: Purchase Orders > Match and Ship > Manual Ship  by harshithad 30/08/21-->
                        <p:commandButton id="cmdBtnMtchDtl" value="Match"   styleClass="btn btn-primary btn-xs" actionListener="#{poServices.populateDtl(dtl)}" update="frmDlg frmDlg:oneMenuPartNum frmDlg:oneMenuPlnDte frmDlg:oneMenuPlnQty frmDlg:oneMenuTotalPrice  frmDlg:otptPnlSo"   oncomplete="PF('unshippd').show();" ></p:commandButton>

                    </p:column>
                </p:dataTable>
                <!--Feature #11596:CRM-7133   PO: need to edit price without line item by harshithad on 13/07/23-->
                <p:dataTable id="dtPOHdr" widgetVar="poHdr" var="hdr" value="#{poServices.viewPoHdrList}"  rendered="#{poServices.lineItemHeader == 1}"  paginator="true" rows="10"   tableStyle="table-layout:auto" resizableColumns="true" >
                    <!--    Task #4521:CRM-4303: Match and Ship: Updates to UI  13/05/21  by harshithad-->
                    <p:column headerText="PO No." sortBy="#{hdr.poNumber}" filterBy="#{hdr.poNumber}" filterMatchMode="contains">
                        #{hdr.poNumber}
                    </p:column>
                    <!--    Task #4521:CRM-4303: Match and Ship: Updates to UI  13/05/21  by harshithad-->
                    <p:column headerText="PO Date" sortBy="#{hdr.poDate}" filterBy="#{globalParams.formatDateTime(hdr.poDate,'da')}" filterMatchMode="contains">

                        <p:outputLabel value="#{globalParams.formatDateTime(hdr.poDate,'da')}"/>
                    </p:column>
                    <p:column  headerText="#{custom.labels.get('IDS_PRINCI')}" sortBy="#{hdr.poPrinciName}" filterBy="#{hdr.poPrinciName}" filterMatchMode="contains">
                        #{hdr.poPrinciName}
                    </p:column>
                    <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" sortBy="#{hdr.poCustName}" filterBy="#{hdr.poCustName}" filterMatchMode="contains">
                        #{hdr.poCustName}
                    </p:column>
                    <p:column headerText="#{custom.labels.get('IDS_PROGRAM')}" sortBy="#{hdr.poProgram}" filterBy="#{hdr.poProgram}" filterMatchMode="contains">
                        #{hdr.poProgram}
                    </p:column>
                    <p:column headerText="Total Price" sortBy="#{hdr.poTotalPrice}" filterBy="#{hdr.poTotalPrice}" filterMatchMode="contains">
                        <p:outputLabel value="#{hdr.poTotalPrice}" style="float: right"/>
                    </p:column>
                    <p:column >   
                        <!--        Feature #5662:CRM-4667: Purchase Orders > Match and Ship > Manual Ship  by harshithad 30/08/21-->
                        <p:commandButton id="cmdBtnMtchHdr" value="Match"  styleClass="btn btn-primary btn-xs"  actionListener="#{poServices.populateHdr(hdr)}" update="frmDlg frmDlg:otptPnlTolrnce frmDlg:oneMenuTotalPrice  frmDlg:otptPnlSo"  oncomplete="PF('unshippd').show();"  ></p:commandButton>

                    </p:column>
                </p:dataTable>
                <h:outputText value="Note:" style="font-weight: bold"/>
                <p:spacer width="4"/>
                <p:outputLabel value=" Match and Ship #{custom.labels.get('IDS_PURCHASE_ORDER')} by Header if #{custom.labels.get('IDS_PART_NUM')} is not defined in #{custom.labels.get('IDS_PURCHASE_ORDER')}. Otherwise, Match and Ship by #{custom.labels.get('IDS_LINE_ITEM')}."/>

            </div>
        </h:form>





    </p:dialog>



    <!--Task #14889: CRM-8681 PO: Match and Ship: UI adjustments-->
    <p:dialog id="dlgUnshippd" resizable="true" maximizable="true" header="Manual Ship"  width="900" height="600"
              widgetVar="unshippd"  modal="true" responsive="true">
        <!--        Feature #5662:CRM-4667: Purchase Orders > Match and Ship > Manual Ship  by harshithad 30/08/21-->
        <h:form id="frmDlg">
            <div>
                <p:outputLabel id="otptLblName" value="Fetch sales records to match and ship based on "/><br/>
                <!--        Feature #5662:CRM-4667: Purchase Orders > Match and Ship > Manual Ship  by harshithad 30/08/21-->
                <p:panelGrid id="pnlGrpChckBx" columns="3" layout="grid">
                    <p:selectBooleanCheckbox id="oneMenuPrinci" itemLabel="#{custom.labels.get('IDS_PRINCI')}" value="#{poServices.princi}"  disabled="true"/>
                    <p:selectBooleanCheckbox id="oneMenuCust" itemLabel="#{custom.labels.get('IDS_CUSTOMER')}" value="#{poServices.cust}" disabled="true"/>
                    <!--        Feature #5662:CRM-4667: Purchase Orders > Match and Ship > Manual Ship  by harshithad 30/08/21-->
                    <p:selectBooleanCheckbox id="oneMenupoNum" itemLabel="#{custom.labels.get('IDS_PURCHASE_ORDER')} Number"  disabled="true" value="#{poServices.poNum}" rendered="#{poServices.soFlag==false}" />
                    <p:selectBooleanCheckbox id="oneMenusoNum" itemLabel="#{custom.labels.get('IDS_SALES_ORDER')} Number"  disabled="true" value="#{poServices.soNum}" rendered="#{poServices.soFlag==true}"/>
<!--Bug #15181:CRM-8836   New match & ship test for adjustments-->
                    <p:selectBooleanCheckbox id="oneMenuPartNum" itemLabel="#{custom.labels.get('IDS_PART_NUM')}" value="#{poServices.partNum}" rendered="#{poServices.lineItemHeader==0}" >
                        <p:ajax event="change"/>
                    </p:selectBooleanCheckbox>
                    <p:selectBooleanCheckbox id="oneMenuPlnDte" itemLabel="Planned Date " value="#{poServices.plnDate}" rendered="#{poServices.lineItemHeader==0}">
                        <p:ajax event="change"/>
                    </p:selectBooleanCheckbox>
                    <p:selectBooleanCheckbox id="oneMenuPlnQty" itemLabel="Planned Qty " value="#{poServices.plnQty}" rendered="#{poServices.lineItemHeader==0}" >
                        <p:ajax event="change"/>
                    </p:selectBooleanCheckbox>
                    <p:selectBooleanCheckbox id="oneMenuTotalPrice" itemLabel="Total Price" value="#{poServices.totalPrice}">
                        <p:ajax event="change" listener="#{poServices.clear()}" update="pnlGrpTolrnce frmDlg:inptTxtTol"/>
                    </p:selectBooleanCheckbox>
                </p:panelGrid>
                <h:panelGroup id="pnlGrpTolrnce">
                    <p:outputPanel id="otptPnlTolrnce" rendered="#{poServices.lineItemHeader==1 and poServices.totalPrice==true }">
                        <p:outputLabel value="Tolerance %"/>
                        <p:spacer width="4"/>
                        <!--                        Task#4313:validation for purchase order->match and ship->by row ->tolerance   22/04/21  by harshithad-->
                        <p:inputNumber id="inptTxtTol"  placeholder="0.00" value="#{poServices.tolerancePerc}"/>
                    </p:outputPanel>
                    <!--Feature #5662:CRM-4667: Purchase Orders > Match and Ship > Manual Ship  by harshithad 30/08/21-->
                    <p:spacer height="4"/>
                    <p:outputPanel id="otptPnlSo" style="margin-top: 4px">
                        <p:selectBooleanCheckbox id="oneMenuSoFtch" style="" value="#{poServices.soFlag}" itemLabel="Match by #{custom.labels.get('IDS_SALES_ORDER')} Number" >
                            <!--Bug #15181: CRM-8836   New match & ship test for adjustments-->
                            <p:ajax event="change" listener="#{poServices.resetPoNum(poServices.soFlag)}" update="frmDlg:pnlGrpChckBx  frmDlg:otPnl"/>
                        </p:selectBooleanCheckbox>
                        <!--Bug #9383: ESCALATIONS CRM-6242  Auto Match & Ship in PO Module NOT WORKING  by harshithad on 14/12/22-->
                        <p:spacer width="4px"/>
                        <p:selectBooleanCheckbox id="oneMenuManualGenInv" value="#{poServices.invoiceFlag}" itemLabel="Do not generate Invoice" >
                   <!--Bug #15181:CRM-8836   New match & ship test for adjustments-->
                            <p:ajax event="change"/>
                        </p:selectBooleanCheckbox>
                    </p:outputPanel>
                </h:panelGroup> <br/>
                <p:spacer height="5"/>
                <p:commandButton id="cmdBtnFetchSales" value="Fetch Sales" onstart="PF('loaderFetchDlg').show()" oncomplete="PF('loaderFetchDlg').hide()" class="btn btn-primary btn-xs " update="frmDlg:dtSales" actionListener="#{poServices.fetchSales}" /> 
                <p:spacer height="3"/>
                <p:outputPanel id="otPnl" style="float: right">
                    <h:outputText id="otptTxtPrinci" value="#{custom.labels.get('IDS_PRINCI')}:" style="font-weight: bold" />
                    <p:spacer width="4"/>
                    <p:outputLabel id="otptLblPrinci" value="#{poServices.princiName}"  />

                    <p:spacer height="6"/>
                    <h:outputText id="otptTxtCust" value="#{custom.labels.get('IDS_CUSTOMER')}:" style="font-weight: bold"/>
                    <p:spacer width="4"/>
                    <p:outputLabel id="otptLblCust" value="#{poServices.custName}" />

                    <p:spacer height="6"/>
                    <!--        Feature #5662:CRM-4667: Purchase Orders > Match and Ship > Manual Ship  by harshithad 30/08/21-->
                    <h:outputText id="otptTxtPoNum" rendered="#{poServices.soFlag==false}"  value="#{custom.labels.get('IDS_PURCHASE_ORDER')} # :"  style="font-weight: bold"/>
                    <h:outputText id="otptTxtSoNum" rendered="#{poServices.soFlag==true}" value="#{custom.labels.get('IDS_SALES_ORDER')} # :"  style="font-weight: bold"/>
                    <p:spacer width="4"/>
                    <!--        Feature #5662:CRM-4667: Purchase Orders > Match and Ship > Manual Ship  by harshithad 30/08/21-->
                    <p:outputLabel id="otptLblPoNum" rendered="#{poServices.soFlag==false}" value="#{poServices.poNo}" />
                    <p:outputLabel id="otptLblSoNum" rendered="#{poServices.soFlag==true}" value="#{poServices.poSoNum}" />
                    <p:spacer height="6"/>
                    <h:outputText id="otptTotPric" value="Total Price :"  style="font-weight: bold"/>
                    <p:spacer width="4"/>
                    <p:outputLabel id="otptLblTotPric" value="#{poServices.totlPrice}" />

                    <p:spacer height="6"/>
                    <h:outputText id="otptTxtPartNum" value="#{custom.labels.get('IDS_PART_NUM')} :"  style="font-weight: bold" rendered="#{poServices.lineItemHeader==0}"/>
                    <p:spacer width="4"/>
                    <p:outputLabel id="otptLblPartNum" value="#{poServices.partNo}" rendered="#{poServices.lineItemHeader==0}"/>

                    <p:spacer height="6"/>
                    <h:outputText id="otptTxtPlnDte" value="Planned Date :"  style="font-weight: bold" rendered="#{poServices.lineItemHeader==0}"/>
                    <p:spacer width="4"/>
                    <p:outputLabel id="otptLblPlnDte" value="#{globalParams.formatDateTime(poServices.plandDate,'da')}" rendered="#{poServices.lineItemHeader==0}"/>

                    <p:spacer height="6"/>
                    <h:outputText id="otptTxtPlnQty" value="Pln Qty :"  style="font-weight: bold" rendered="#{poServices.lineItemHeader==0}"/>
                    <p:spacer width="4"/>
                    <p:outputLabel id="otptLblPlnQty" value="#{poServices.plndQty}" rendered="#{poServices.lineItemHeader==0}" />


                </p:outputPanel>
            </div>
            <p:spacer height="5"/>
            <div>
                <p:dataTable id="dtSales"  widgetVar="sales" rowKey="#{sm.recId}" value="#{poServices.salesMainList}" var="sm" tableStyle="table-layout:auto" resizableColumns="true">

                    <p:column id="colSalInv" headerText="Invoice #" sortBy="#{sm.salInvcNumber}" filterBy="#{sm.salInvcNumber}" filterMatchMode="contains">
                        #{sm.salInvcNumber}
                    </p:column>
                    <p:column id="colSalInvDat" headerText="Invoice Date" sortBy="#{sm.salDate}" filterBy="#{globalParams.formatDateTime(poServices.convertDate(sm.salDate),'da')}" filterMatchMode="contains">
                        <p:outputLabel value="#{globalParams.formatDateTime(poServices.convertDate(sm.salDate),'da')}"/>
                    </p:column>


                    <p:column id="colSalPatNp" headerText="#{custom.labels.get('IDS_PART_NUM')}" sortBy="#{sm.salPartNo}" filterBy="#{sm.salPartNo}" filterMatchMode="contains">
                        <p:outputLabel id="otptTxtSalPartNo" value="#{sm.salPartNo}" />
                    </p:column>
                    <p:column id="colSalQty" headerText="Qty" sortBy="#{sm.salQuantity}" filterBy="#{sm.salQuantity}" filterMatchMode="contains">
                        <p:outputLabel id="otptLblSalQty" value="#{sm.salQuantity}" style="float: right"/>
                    </p:column>
                    <p:column id="colTotalPric" headerText="Total Price" sortBy="#{sm.salTotalPrice}" filterBy="#{sm.salTotalPrice}" filterMatchMode="contains">
                        <p:outputLabel id="otptTxtTotl" value="#{sm.salTotalPrice}" style="float: right"/>
                    </p:column>
                    <p:column >   
                        <p:commandButton id="cmdBtnShip" value="Ship" styleClass="btn btn-primary btn-xs" update="frmMatchAndShip frmDlg:dtSales"   actionListener="#{poServices.manualShip(sm)}"   oncomplete="PF('poListTable').clearFilters()"  ></p:commandButton>

                    </p:column>
                </p:dataTable>
            </div>
            <!--        Feature #5662:CRM-4667: Purchase Orders > Match and Ship > Manual Ship  by harshithad 30/08/21-->
        </h:form>
    </p:dialog>


    <!--Feature #4944:CRM-4434: Purchase Orders > Match and Ship  25/06/21 by harshithad-->
    <!--#14856 CRM-8681 : MATCH & SHIP - auto or manual--> 
    <!--Task #14889: CRM-8681 PO: Match and Ship: UI adjustments-->
    <p:dialog    id="cnfrmDlgPo" header="Auto Match and Ship " resizable="true" maximizable="true" widgetVar="poConfrm" showEffect="fade" height="700px"  width="1200px" > 
        <!--        Feature #5662:CRM-4667: Purchase Orders > Match and Ship > Manual Ship  by harshithad 30/08/21-->
        <p:ajax event="close" oncomplete="PF('AutoMtchDt').clearFilters()" listener="#{poServices.loadUnshipped()}" update="frmCnfrm:dtAutoLI frmMatchAndShip:dtPOdtl frmMatchAndShip:dtPOHdr"/>
        <h:form id="frmCnfrm">
            <!--            Feature #5404:CRM-4539: Need to be able to export from PO Module (sales order module for Rodenbeck)  by Harshitha Devadiga on 10/8/2021-->
            <p:growl id="growlAutoMtch" showDetail="false" />

            <p:outputLabel id="otptLblTxt" value="#{custom.labels.get('IDS_PURCHASE_ORDER')} #{custom.labels.get('IDS_LINE_ITEMS')} will be matched and marked as Shipped based on the following criteria"  />
            <br/>
            <ul  style="margin-left: 2em;"  >
                <!--Task #14353: CRM-8184: Purchase Orders: Auto Match and Ship: Relax criteria  by harshithad on 28/08/2024-->
                <p:panelGrid id="pnlGridLabel" columns="5" layout="grid" columnClasses="col1,col1,col1,col1,col2">
                    <p:selectBooleanCheckbox  itemLabel="#{custom.labels.get('IDS_PRINCI')}" value="#{poServices.princi}"  />
                    <p:selectBooleanCheckbox  itemLabel="#{custom.labels.get('IDS_CUSTOMER')}" value="#{poServices.cust}"/>
                    <p:selectBooleanCheckbox id="oneMenupoNum" itemLabel="#{custom.labels.get('IDS_PURCHASE_ORDER')} Number"   value="#{poServices.poNum}"  />

                    <p:selectBooleanCheckbox id="oneMenuInvcNum" itemLabel="Invoice Number"   value="#{poServices.invcNumFlag}" />
                    <p:outputPanel id="otptPnlPric" style="border: none">    <li > 
                            PO Total Price            
                        </li></p:outputPanel>
                    <!--Task #14353: CRM-8184: Purchase Orders: Auto Match and Ship: Relax criteria  by harshithad on 28/08/2024-->
                    <p:selectBooleanCheckbox id="oneMenuPartNo"  value="#{poServices.partNoFlag}" itemLabel="#{custom.labels.get('IDS_PART_NUM')}" >
                    </p:selectBooleanCheckbox>
                    <!--Bug #11656 CRM-6919   PO Module - testing Auto Match & ship by PO header and by line item by harshithad on 24/07/23-->
                    <p:selectBooleanCheckbox id="oneMenuPlanDate"  value="#{poServices.planDateFlag}" itemLabel="Planned Date" >
                    </p:selectBooleanCheckbox>   
                    <p:selectBooleanCheckbox id="oneMenuPlanQty"  value="#{poServices.planQtyFlag}" itemLabel="Planned Qty" >
                    </p:selectBooleanCheckbox>               
             <!--Bug #15181:CRM-8836   New match & ship test for adjustments-->
                    <p:selectBooleanCheckbox id="oneMenuSoFlag" itemLabel="#{custom.labels.get('IDS_SALES_ORDER')} Number"   value="#{poServices.soNumFlag}"  >

                    </p:selectBooleanCheckbox>
                </p:panelGrid>

            </ul>

            <h:outputText style="font-weight: bold" value="Allow Variance For:"/><br/>
            <!--Bug #11153: URGENT CRM-6919   PO Module - testing Auto Match & ship by PO header and by line item by harshithad on 11/05/23-->    
            <p:panelGrid id="pnlGridVar" columns="3" layout="grid">
                <p:panelGrid columns="2"  layout="grid" columnClasses="ui-grid-col-8, ui-grid-col-4">
                    <p:outputLabel id="otptLblPlnDate" value="Planned Date(+/-Days):" style="margin-right: 3px;width: 1200px;text-align: right"/> 
                    <p:inputNumber id="inputPln" placeholder="0" value="#{poServices.dateVar}" inputStyle="width:60px;text-align: left"
                                   padControl="false" minValue="0" maxValue="100"/>
                </p:panelGrid>
                <p:panelGrid columns="2"  layout="grid" columnClasses="ui-grid-col-8 ui-grid-col-4">
                    <p:outputLabel id="otptLblplnQty" value="Planned Qty(+/-Percentage):" style="margin-right: 3px;width: 1200px;text-align: right"/> 
                    <p:inputNumber id="inputQty" placeholder="0" padControl="false"  inputStyle="width:60px;text-align: left" value="#{poServices.qtyVar}" minValue="0.00"
                                   maxValue="100" decimalPlaces="3"  />
                </p:panelGrid>
                <p:panelGrid columns="2"  layout="grid" columnClasses="ui-grid-col-8, ui-grid-col-4">
                    <p:outputLabel id="otptLblPrc" value="Total Price(+/-Percentage):"  style="margin-right: 3px;width: 1200px;text-align: right"/>
                    <p:inputNumber id="inputTotPr" placeholder="0" padControl="false"  inputStyle="width:60px;text-align: left" value="#{poServices.amtVar}" minValue="0.00"
                                   maxValue="100" decimalPlaces="3"  /></p:panelGrid>
            </p:panelGrid>
            <!--Bug #11153: URGENT CRM-6919   PO Module - testing Auto Match & ship by PO header and by line item by harshithad on 11/05/23-->    
            <!--Task #14353: CRM-8184: Pur-->
            <!--Bug #9383: ESCALATIONS CRM-6242  Auto Match & Ship in PO Module NOT WORKING  by harshithad on 14/12/22-->
            <!--Bug #11153: URGENT CRM-6919   PO Module - testing Auto Match & ship by PO header and by line item by harshithad on 11/05/23-->           
            <p:selectBooleanCheckbox id="oneMenuGenInv" style="margin-left: 40px"  value="#{poServices.invoiceFlag}" itemLabel="Do not generate Invoice" >
            </p:selectBooleanCheckbox>
            <!--Bug #11153: URGENT CRM-6919   PO Module - testing Auto Match & ship by PO header and by line item by harshithad on 11/05/23-->
            <!--<p:spacer height="4px"/>-->

            <div style="text-align:center">
                <!--Bug #11656 CRM-6919   PO Module - testing Auto Match & ship by PO header and by line item by harshithad on 24/07/23-->
                <!--Task #14035 CRM-8185   Orders Auto Match & Ship: caching issue - selection criteria not be used for re-query  by harshithad on 11/07/2024-->
                <!--Task #14353: CRM-8184: Purchase Orders: Auto Match and Ship: Relax criteria  by harshithad on 28/08/2024-->
                <p:commandButton id="cmdBtnLoadUnshipped" onstart="PF('AutoMatchDlg').show()" oncomplete="PF('AutoMatchDlg').hide();PF('AutoMtchDt').clearFilters();" actionListener="#{poServices.autoMatchandShip()}" update="frmCnfrm:dtAutoLI frmCnfrm:cmdBtnProceed frmCnfrm growlAutoMtch"  class="btn btn-primary btn-xs "   value="Show Matching PO" />
                <p:spacer width="4"/>
                <!--                Task#4307:auto match and ship message refresh issue   -22/04/21 by harshithad-->
                <p:commandButton id="cmdBtnProceed" value="Process" class="btn btn-primary btn-xs " disabled="#{poServices.processListFlag}" actionListener="#{poServices.processList()}"  onstart="PF('loaderAutoShippingDlg').show()"
                                 oncomplete="PF('loaderAutoShippingDlg').hide();PF('AutoMtchDt').clearFilters()" update="frmMatchAndShip:dtPOdtl frmCnfrm:dtAutoLI frmCnfrm growlAutoMtch"/> 
                <p:spacer width="4"/>

                <!--        Feature #5662:CRM-4667: Purchase Orders > Match and Ship > Manual Ship  by harshithad 30/08/21-->
                <p:commandButton id="cmdBtnCancel" value="Cancel"  class="btn btn-primary btn-xs " actionListener="#{poServices.loadUnshipped()}" update="frmMatchAndShip:dtPOdtl frmMatchAndShip:dtPOHdr" oncomplete="PF('poConfrm').hide();PF('AutoMtchDt').clearFilters();" 
                                 /> 
            </div>

            <p:spacer height="4px"/>
            <!--Task #14353: CRM-8184: Purchase Orders: Auto Match and Ship: Relax criteria  by harshithad on 28/08/2024-->
            <p:dataTable id="dtAutoLI" widgetVar="AutoMtchDt"  value="#{poServices.matchingPoList}"  selection="#{poServices.selectedPoDtlList}" rowKey="#{viewPodtl.recId}" resizableColumns="true" style="width: 100%"   scrollWidth="1900"       var="viewPodtl" emptyMessage="No #{custom.labels.get('IDS_LINE_ITEMS')} found" paginator="true" rows="10" >
                <p:ajax event="rowSelect" listener="#{poServices.enableProcess()}" update="frmCnfrm:cmdBtnProceed" />
                <p:ajax event="toggleSelect" listener="#{poServices.enableProcess()}" update="frmCnfrm:cmdBtnProceed"/>
                <p:ajax event="rowSelectCheckbox" listener="#{poServices.enableProcess()}" update="frmCnfrm:cmdBtnProceed"/>
                <p:ajax event="rowUnselectCheckbox" listener="#{poServices.enableProcess()}" update="frmCnfrm:cmdBtnProceed"/>
                <p:ajax event="rowUnselect"  listener="#{poServices.enableProcess()}" update="frmCnfrm:cmdBtnProceed"/>
                <p:column id="colSel" headerText="Select"  selectionMode="multiple" style="width:50px;text-align: center"/>

                <p:column id="colPono" headerText="PO No."  sortBy=" #{viewPodtl.poNumber}" filterBy=" #{viewPodtl.poNumber}" filterMatchMode="contains"> 
                    #{viewPodtl.poNumber} </p:column>
                <p:column id="colSoNo" headerText="SO No."  sortBy=" #{viewPodtl.poSoNumber}" filterBy=" #{viewPodtl.poSoNumber}" filterMatchMode="contains"> 
                    #{viewPodtl.poSoNumber} </p:column>
             <!--Bug #15181:CRM-8836   New match & ship test for adjustments-->
                    <p:column id="colPodate" headerText="PO Date"  rendered="#{poServices.soNumFlag==false}" sortBy="#{viewPodtl.poDate}" filterBy="#{globalParams.formatDateTime(poServices.convertDate(viewPodtl.poDate),'da')}" filterMatchMode="contains">#{globalParams.formatDateTime(poServices.convertDate(viewPodtl.poDate),'da')}</p:column>
                <p:column headerText="#{custom.labels.get('IDS_SO_DATE')}" rendered="#{poServices.soNumFlag==true}" sortBy="#{viewPodtl.poSoDate}" filterBy="#{globalParams.formatDateTime(poServices.convertDate(viewPodtl.poSoDate),'da')}" filterMatchMode="contains"> 
                    #{globalParams.formatDateTime(poServices.convertDate(viewPodtl.poSoDate),'da')} </p:column>
                <p:column id="colPrinc" headerText="#{custom.labels.get('IDS_PRINCI')}" sortBy=" #{viewPodtl.poPrinciName}" filterMatchMode="contains" filterBy=" #{viewPodtl.poPrinciName}">
                    #{viewPodtl.poPrinciName}</p:column>
                <p:column id="colCust" headerText="#{custom.labels.get('IDS_CUSTOMER')}"  sortBy="#{viewPodtl.poCustName}" filterMatchMode="contains" filterBy="#{viewPodtl.poCustName}">#{viewPodtl.poCustName}</p:column>
                <p:column id="colPartNo" headerText="#{custom.labels.get('IDS_PART_NUM')}" width="30px" sortBy=" #{viewPodtl.poPartNo}" filterBy=" #{viewPodtl.poPartNo}" filterMatchMode="contains"> 
                    #{viewPodtl.poPartNo} </p:column>
                <p:column id="colPlnDate" headerText="Pln.Date" sortBy="#{viewPodtl.poPlanDate}" filterMatchMode="contains" filterBy="#{globalParams.formatDateTime(poServices.convertDate(viewPodtl.poPlanDate),'da')}">
                    <!--#14856 CRM-8681 : MATCH & SHIP - auto or manual NOT pulling up all POs in Order mo-->
                    #{viewPodtl.poPlanDate!=null ?globalParams.formatDateTime(poServices.convertDate(viewPodtl.poPlanDate),'da'): ""}</p:column>

                <p:column id="colPlnQty" headerText="Pln.Qty" sortBy="#{viewPodtl.poPlanQty}" filterMatchMode="contains" filterBy="#{viewPodtl.poPlanQty}">
                    #{viewPodtl.poPlanQty}</p:column>
                <p:column id="colPric" headerText="PO Price" sortBy="#{viewPodtl.poTotalPrice}" filterBy="#{viewPodtl.poTotalPrice}" filterMatchMode="contains">
                    #{viewPodtl.poTotalPrice}</p:column>
                <p:column id="colInvNo" headerText="Inv. No." sortBy="#{viewPodtl.salInvcNo}" filterBy="#{viewPodtl.salInvcNo}" filterMatchMode="contains">
                    #{viewPodtl.salInvcNo}</p:column>
                <p:column id="colSalDate" headerText="Sales Date" width="30px" sortBy="#{viewPodtl.salDate}" filterBy="#{globalParams.formatDateTime(poServices.convertDate(viewPodtl.salDate),'da')}" filterMatchMode="contains">#{globalParams.formatDateTime(poServices.convertDate(viewPodtl.salDate),'da')}</p:column> 
                <p:column id="colSalQty" headerText="Sales Qty" width="30px" sortBy="#{viewPodtl.salQty}" filterBy="#{viewPodtl.salQty}" filterMatchMode="contains">#{viewPodtl.salQty}</p:column>
                <p:column id="colSalAmt" headerText="Sales Amt." width="30px" sortBy="#{viewPodtl.salInvAmt}" filterBy="#{viewPodtl.salInvAmt}" filterMatchMode="contains">#{viewPodtl.salInvAmt}</p:column>
                <!--Task #14933: CRM-8681: Auto Match and ship - checkbox pre-selection issue-->
                <p:column id="colProjComm" headerText="Proj. Comm." width="40px" sortBy="#{viewPodtl.poCommProjected}" filterBy="#{viewPodtl.poCommProjected}" filterMatchMode="contains">
                    #{viewPodtl.poCommProjected}</p:column>
            </p:dataTable>

        </h:form>

    </p:dialog>






    <h:form id="frmPrgrss">
        <p:dialog widgetVar="loaderAutoShippingDlg" closable="false" modal="true" header="Message" resizable="false" id="dlgPrgrsbar" >
            <h:form id="frmProgressbar">
                <p:outputPanel >
                    <br />
                    <h:graphicImage id="grphcImgLodr"  library="images" name="ajax-loader.gif"   />
                    <p:spacer width="5" />
                    <p:outputLabel id="otptLblPrgs" value="Please wait...Auto Match and Ship in Progress.." />
                    <br /><br />

                </p:outputPanel>
            </h:form>
        </p:dialog>
    </h:form>
    <!--Bug #11656 CRM-6919   PO Module - testing Auto Match & ship by PO header and by line item by harshithad on 24/07/23-->
    <h:form id="frmPrgrssBrAutoMcth">
        <p:dialog widgetVar="AutoMatchDlg" closable="false" modal="true" header="Message" resizable="false" id="dlgAtoMtch" >
            <h:form id="frmAutoMtch">
                <p:outputPanel >
                    <br />
                    <h:graphicImage id="grphcImgLodr1"  library="images" name="ajax-loader.gif"   />
                    <p:spacer width="5" />
                    <p:outputLabel id="otptLblPrgs1" value="Please wait...Auto Match in Progress.." />
                    <br /><br />

                </p:outputPanel>
            </h:form>
        </p:dialog>
    </h:form>
    <!--Feature #4944:CRM-4434: Purchase Orders > Match and Ship  25/06/21 by harshithad-->

    <h:form id="frmFtchPrgress">
        <p:dialog widgetVar="loaderFetchDlg" closable="false" modal="true" header="Message" resizable="false" id="dlgPrgrsbarFetch" >
            <h:form id="frmProgressbar">
                <p:outputPanel >
                    <br />
                    <h:graphicImage id="grphcImgLoadr"  library="images" name="ajax-loader.gif"   />
                    <p:spacer width="5" />
                    <p:outputLabel id="otptLblFetchSal" value="Fetching Sales records..." />
                    <br /><br />

                </p:outputPanel>
            </h:form>
        </p:dialog>
    </h:form>
    <p:confirmDialog header="Confirmation" message="Selected Sales record does not have Qty defined. Are you sure to mark the #{custom.labels.get('IDS_PURCHASE_ORDER')} #{custom.labels.get('IDS_LINE_ITEM')} as fully shipped " 
                     id="confirmShp" showEffect="fade" hideEffect="fade" global="true" widgetVar="confrmShp">  

        <h:form>
            <div class="div-center">
                <p:commandButton id="cmdBtnYs" value="Yes" action="#{poServices.processShip()}" update="frmDlg:dtSales"  oncomplete="PF('confrmShp').hide();"  styleClass="btn btn-xs btn-success"
                                 />
                <p:spacer width="4"/>
                <p:commandButton id="cmdBtnNo" value="No"  onclick="PF('confrmShp').hide();"  type="button" styleClass="btn btn-xs btn-danger"/>
            </div>
        </h:form>
    </p:confirmDialog>
    <!--Feature #4944:CRM-4434: Purchase Orders > Match and Ship  25/06/21 by harshithad-->
    <style>
        .ui-datatable-scrollable-header *,
        .ui-datatable-scrollable-theadclone * {
            -moz-box-sizing: content-box;
            -webkit-box-sizing: content-box;
            box-sizing: content-box;
        }

        body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
            width: 15px;
        }
        /*Task #14353: CRM-8184: Purchase Orders: Auto Match and Ship: Relax criteria  by harshithad on 28/08/2024*/ 
        .col1{
            width: 20%

        }

    </style>
</ui:composition>
