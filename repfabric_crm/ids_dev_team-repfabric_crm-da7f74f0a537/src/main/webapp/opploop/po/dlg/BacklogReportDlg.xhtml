<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <!--#630  Commissionable Transactions > Backlog Report-->
    <p:dialog  id="backlogExport" widgetVar="backlogBxportDlg" width="480px"  modal="true" header="Backlog Report" class="dialogCSS"  resizable="false">
        <!--<p:ajax event="close" update=":commTransListForm"  listener="#{commTransactionService.reSetSrc()}"/>--> 
        <h:form id="formBacklogexportDlg">
            <p:remoteCommand name="applyPrinci" actionListener="#{poBacklogService.applyPrincipal(viewCompLookupService.selectedCompany)}"  update=":formBacklogexportDlg:inputPrincName" />
            <p:remoteCommand name="applyCustomer" actionListener="#{poBacklogService.applyCustSelect(viewCompLookupService.selectedCompany)}"  update=":formBacklogexportDlg:inputCustName"/>


            <p:growl widgetVar="bulkGrowl3" id="bulkGrowl3" showDetail="false"  />   
            <p:panelGrid id="backlogReportExportDlg" >
                <p:row>
                    <p:column>

                        <p:outputLabel value="From" id="oLblPoBackLogFrom"/>

                    </p:column>
                    <p:column>
                        <!--//           #12799 CRM-7637/CRM-7612  incidental findings po backlog report issues-->
                        <p:calendar  pattern="#{globalParams.dateFormat}" value="#{poBacklogService.planFromDate}"  autocomplete="off" id="calBackLogFrom">
                            
                        </p:calendar>

                    </p:column>
                </p:row>
                <p:row>
                    <p:column>

                        <p:outputLabel value="To" id="oLblPoBackLogTo"/>

                    </p:column>
                    <p:column>
                        <!--//           #12799 CRM-7637/CRM-7612  incidental findings po backlog report issues-->
                        <p:calendar  pattern="#{globalParams.dateFormat}"  value="#{poBacklogService.planToDate}"   autocomplete="off"   id="calBackLogTo" />

                    </p:column>
                </p:row>
                <p:row>

                    <p:column>
                        <p:outputLabel value="Group by"  id="oLblBackLogGrpBy"/>
                    </p:column>
                    <p:column>
                        <p:selectOneMenu value="#{poBacklogService.grpBy}" style="width: 90%;" id="source">
                          
                            <f:selectItem itemLabel="#{custom.labels.get('IDS_CUSTOMER')} Sort by #{custom.labels.get('IDS_PRINCI')}" itemValue="2" />
                            <f:selectItem itemLabel="#{custom.labels.get('IDS_PRINCI')} Sort by #{custom.labels.get('IDS_CUSTOMER')}" itemValue="1" />

                        </p:selectOneMenu> 
                    </p:column>
                </p:row>
                <p:row>
                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')}" id="oLblBackLogsalesTeam" />
                    </p:column>
                    <p:column>

                        <h:panelGroup class="ui-inputgroup"  >
                            <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width: 100%" id="selSalesTeam">
                                <f:selectItems value="#{lookupService.salesTeams}" var="team"  
                                               itemValue="#{team.smanId}"
                                               itemLabel="#{team.smanName}"  />
                            </h:selectOneListbox>
                            <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  
                                             title="Select #{custom.labels.get('IDS_SALES_TEAM')}" 
                                             id="btnBackLogsalesTeam"
                                             actionListener="#{lookupService.list('SALES_TEAM')}" update=":frmSalesTeamLookup" 
                                             oncomplete="PF('dlgSalesTeamsLookup').show();" />
                            <p:commandLink styleClass="sel-salesteam" value="Clear" 
                                           id="cmndLnkSalesTeam"
                                           actionListener="#{lookupService.clear('SALES_TEAM')}" 
                                           update="@(.sel-salesteam)" 
                                           disabled="#{!(lookupService.salesTeams!=null and lookupService.salesTeams.size() != 0)}" />
                        </h:panelGroup>
                    </p:column>


                </p:row>
                <p:row>
                    <p:column>
                        <p:outputLabel for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}" id="oLblBackLogPrinci"></p:outputLabel>
                    </p:column>
                    <p:column>

                        <h:panelGroup class="ui-inputgroup"  >
                            <p:inputText id="inputPrincName" value="#{poBacklogService.princiName}" readonly="true" placeholder="All"/>
                            <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                              actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}" 
                                              oncomplete="PF('lookupComp').show()" 
                                              id="btnBackLogPrinci"
                                              update=":formCompLookup"
                                              styleClass="btn-info btn-xs" />
                        </h:panelGroup>

                    </p:column>                    

                </p:row>
                <p:row>
                    <p:column>
                        <p:outputLabel for="inputCustName" value="#{custom.labels.get('IDS_CUSTOMER')}"  />

                    </p:column>
                    <p:column>

                        <h:panelGroup class="ui-inputgroup"  >
                            <p:inputText id="inputCustName" value="#{poBacklogService.custName}" widgetVar="cust"   readonly="true"  placeholder="All"/>
                            <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                              id="btnBackLogCust"
                                              actionListener="#{viewCompLookupService.listAll('applyCustomer', 2, 0, 0)}" 
                                              oncomplete="PF('lookupComp').show()"
                                              update=":formCompLookup" 
                                              styleClass="btn-info btn-xs" />
                        </h:panelGroup>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column>
                        <p:outputLabel value=" Include all late shipments"></p:outputLabel>
                    </p:column>
                    <p:column>
                        <p:selectBooleanCheckbox  itemLabel="#{poBacklogService.shipmentFlag ? '':''}"  value="#{poBacklogService.shipmentFlag}"     id="shipFlag" widgetVar="shipmentFlag">


                        </p:selectBooleanCheckbox>
                    </p:column>

                </p:row>

                <p:row>
                    <div   class="div-center">



                        <p:column colspan="2"  style="text-align: center">


                            <p:commandButton value="View" action="#{poBacklogService.goToNavigationPage(1)}"  
                                             id="btnBackLogsView"
                                             actionListener="#{reportFilters.setSalesteamlst()}"   styleClass="btn btn-primary btn-xs" /> 
                            <p:spacer width="5" />
                            <!--#12728 CRM-7612  incidental finding Po backlog rpt private team/sales team visibility issue-->
                            <p:commandButton value="Export"   ajax="false"  
                                             actionListener="#{poBacklogService.exportData(1)}"  
                                             id="btnBackLogExport"
                                             styleClass="btn btn-primary btn-xs" onclick="PrimeFaces.monitorDownload(start, stop);" 
                                             oncomplete="PF('inBacklogProgressDlg').hide();"
                                             />
                        </p:column>
                    </div>
                </p:row>
            </p:panelGrid>
        </h:form>

    </p:dialog>
    <p:dialog widgetVar="inBacklogProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
        <h:form>
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif"   />
                <p:spacer width="5" />
                <p:outputLabel value="Please wait...Exporting records" />
                <br /><br />
                <p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />
            </p:outputPanel>
        </h:form>
    </p:dialog><!--
    -->         <script type="text/javascript">
        function start() {

            PF('inBacklogProgressDlg').show();
        }
        function stop() {

            PF('inBacklogProgressDlg').hide();
        }
    </script>

</ui:composition>