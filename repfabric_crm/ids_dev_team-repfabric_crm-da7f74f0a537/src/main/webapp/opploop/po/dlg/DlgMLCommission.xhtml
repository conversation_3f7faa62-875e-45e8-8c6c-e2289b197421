<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"> 
    <!--//21-04-2022 : #7801  : CRM-5545: PO Flow: User Selectable commission percentage addition during addin po entry-->
    <h:form id="frmPurchase">
     <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
        <p:dialog id="savePurchaseOrder" width="400" header="Edit Multi-Level #{custom.labels.get('IDS_COMMISSION')} %" 
                  showEffect="clip" hideEffect="clip" modal="true" widgetVar="savePurchaseDlg"  resizable="false"  >  

            <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                         style="min-height: 50px"       layout="grid" styleClass="box-primary no-border ui-fluid">

                <p:outputLabel value="Origin % "  id="poCommOrigPct"/>


                <!--Feature #7942: CRM-5545: PO Flow: Line item: User Selectable commission percentage  by harshithad 11/05/2022-->
                <p:inputNumber id="inpuTxtOrig" value="#{poServices.origPrc}"  decimalPlaces="2" maxValue="99" maxlength="2" placeholder="0.00"/>

                <p:outputLabel value="Spec % "  id="poCommSpecPct"/>

                <!--Feature #7942: CRM-5545: PO Flow: Line item: User Selectable commission percentage  by harshithad 11/05/2022-->
                <p:inputNumber id="inpuTxtSpec" value="#{poServices.specPrc}"  decimalPlaces="2" maxValue="99" maxlength="2"  placeholder="0.00"/>

                <p:outputLabel value="Dest % "  id="poCommDestPct"/>

                <!--Feature #7942: CRM-5545: PO Flow: Line item: User Selectable commission percentage  by harshithad 11/05/2022-->
                <p:inputNumber id="inpuTxtDest" value="#{poServices.destPerc}" decimalPlaces="2" maxValue="99" maxlength="2" placeholder="0.00"/>

            </p:panelGrid>
            <p:spacer width='4px' />
            <div class="div-center" >

                <!--Feature #7942: CRM-5545: PO Flow: Line item: User Selectable commission percentage  by harshithad 11/05/2022-->
                <p:commandButton  id="btnSave" value="Set" styleClass="btn btn-success  btn-xs"  actionListener="#{poServices.changeOrigSpecDest()}" 
                                  oncomplete="PF('savePurchaseDlg').hide();" update=":frmPo:tabDetails:txtMainCommRate :frmPo:tabDetails:txtMainProjectedComm"  />
                <p:spacer width="4px"/>
                <p:commandButton id="btnCancel"  value="Cancel"  oncomplete="PF('savePurchaseDlg').hide();" 
                                 styleClass="btn btn-warning btn-xs" />
            </div>
            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
            <p:outputLabel value="Note: Changes to the multi-level #{custom.labels.get('IDS_COMMISSION')} % will be saved only when the user clicks on the PO Save button"/>

        </p:dialog>
    </h:form>
</ui:composition>