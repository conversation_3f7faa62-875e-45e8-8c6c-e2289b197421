<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"

                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
    <p:dialog id="dlgAddAttchmnt" modal="true" class="dialogCSS" header="Add Attachments"  style="height: 160px;width: 300px" resizable="false"
              widgetVar="addAttchDlg" responsive="true">
        <h:form id="frmAtchDlg">
            <!--Bug #9079:ESCALATIONS: CRM-6153  PO Module drag and drop no longer available by harshithad-->
            <p:fileUpload  id="flUpldTimelne" class="timelineAttchmt"
                           mode="advanced" label="Choose from File Browser"
                           fileUploadListener="#{poServices.savePOAttchments}"
                           sizeLimit="31457280" auto="true"
                           invalidSizeMessage="Exceeds maximum size limit of 30MB" oncomplete="PF('addAttchDlg').hide()" update="frmAtchDlg" >
                <f:attribute name="foo" value="2" />
                <f:attribute name="timelineRecId" value="#{poServices.poStatusTimeline.recId}"/>

            </p:fileUpload>  <br/>
            <!--        <p:fileUpload 
                        
                        id="upldEmail"
                        mode="advanced" label="Choose from Email Attachments"
                        fileUploadListener="#{poServices.savePOAttchments}"
                        sizeLimit="31457280" auto="true"
                        invalidSizeMessage="Exceeds maximum size limit of 30MB" >
                        <f:attribute name="foo" value="2" />
                        <f:attribute name="timelineRecId" value="#{poServices.poStatusTimeline.recId}"/>
                        <p:tooltip showEffect="clip"
                                   widgetVar="chooseWV" position="top"
                                   for="@(#frmAtchDlg\:upldEmail)"
                                   value="Yet to be implemented"/>
                    </p:fileUpload>  -->
            <p:commandButton title="Yet to be implemented" class="btn btn-primary btn-xs" style="height: 26px" disabled="true" value="Choose from Email Attachments"/>
        </h:form>
    </p:dialog>
</ui:composition>