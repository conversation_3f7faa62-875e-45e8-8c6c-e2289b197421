<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"

                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
<!--    Feature #5404:CRM-4539: Need to be able to export from PO Module (sales order module for Rodenbeck)  by Harshitha Devadiga on 04-08-2021-->
<p:dialog id="exportDlgPO" class="dialogCSS" modal="true" widgetVar="ExportDlg" style="width: 300px" height="170px" header="Export #{custom.labels.get('IDS_PURCHASE_ORDERS')}"> 
      
        <h:form id="frmExprtFrm">
          
        <p:panelGrid id="pnlGridDate" columns="2">
            <p:outputLabel id="otptLblDateRnge" value="Date Range for:"/>
            <p:selectOneMenu id ="oneMenuDate"  value="#{poServices.dateType}">
                <f:selectItem itemLabel="PO Date" itemValue="1"/>
                <f:selectItem itemLabel="#{custom.labels.get('IDS_SO_DATE')}" itemValue="2"/>
            </p:selectOneMenu>
       
            <p:outputLabel id="otptLblFrom" value="From:"/>
            <p:calendar id="fromDate"  pattern="#{globalParams.dateFormat}"   
                        size="10" value="#{poServices.frmDate}"  autocomplete="off" converterMessage="Please enter Valid date" >
               
            </p:calendar> 
            <p:outputLabel id="otptLblTo" value="To:"/>
            <p:calendar id="toDate"  pattern="#{globalParams.dateFormat}"   
                        size="10" value="#{poServices.toDate}"  autocomplete="off" converterMessage="Please enter Valid date" >
               
            </p:calendar> 
        </p:panelGrid><br/>
        <div style="text-align: center">
            <p:commandButton id="cmdBtnSave" value="Export" styleClass="btn btn-primary btn-xs" actionListener="#{poServices.exportPO()}"  validateClient="true" onclick="PrimeFaces.monitorDownload(startdlg, stopdlg);"/>
            <p:spacer width="4"/>
            <p:commandButton id="cmdBtnCancel" value="Cancel" onclick="PF('ExportDlg').hide()" styleClass="btn btn-warning btn-xs" />
        </div>
        </h:form>
    </p:dialog>






</ui:composition>