<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: OppLinkedDocumentsDlg.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                > 
    <!--#7824: CRM-5543: PO Flow:  ability to link POs to Quotes and Jobs - poornima-->
    <p:dialog id="poLinkedDocumentsDlg" header="Linked Documents" width="1000px"
              widgetVar="dlgPoLinkedDocuments"  modal="true" responsive="true" styleClass="dialogCSS">
        <!--#7824: CRM-5543: PO Flow:  ability to link POs to Quotes and Jobs - poornima-->
        <f:facet name="header"> 
            <!--19-005-2022 7824 CRM-5543: PO Flow:  ability to link POs to Quotes and Jobs-->
            <h:form id="formPoLinkDoc"> 
                <!--//04-07-2024 : #13957 : Query usage analysis for viewjobs-->
                <p:remoteCommand id="rcGetPoLinkedData" name="rcGetPoLinkedData"  autoRun="false"  actionListener="#{poLinkedDocumentsService.loadPoLinkeddocuments(poServices.poHdr.poId)}" oncomplete="PF('dtPoLinkedDocsFilter').clearFilters();" update="frmPoLinksHeader frmPoLinkedDocs:dtPoLinkedDocs"/>
            </h:form>            
            <h:form id="frmPoLinksHeader">
                Linked Documents                
                <p:spacer width="440" height="0"/> 
                Link to 
                <p:spacer width="8" height="0"/>                
                <p:commandButton id="btnOppLink" value="#{custom.labels.get('IDS_OPP')}" process="@this" styleClass="btn btn-primary btn-xs" oncomplete="PF('dlgPoLinkedOpp').show()" title="Link to #{custom.labels.get('IDS_OPP')}"
                                 actionListener="#{poHdr.loadOppList(poServices.poHdr.poPrinciId,poServices.poHdr.poCustId)}" disabled="#{poServices.poHdr.poOppId != 0}" />
               
                <p:spacer width="4" height="0"/>
                <p:commandButton id="btnQuoteLink" value="#{custom.labels.get('IDS_QUOTES')}" process="@this" styleClass="btn btn-primary btn-xs" oncomplete="PF('dlgPoLinkedQuote').show()" title="Link to #{custom.labels.get('IDS_QUOTES')}"
                                 actionListener="#{quotesHdr.loadQuoteList(poServices.poHdr.poPrinciId,poServices.poHdr.poCustId)}" disabled="#{poServices.poHdr.poQuoteId != 0}"/>

                <p:spacer width="4" height="0"/>
                <p:commandButton id="btnJobLink" value="#{custom.labels.get('IDS_JOB')}" process="@this" styleClass="btn btn-primary btn-xs" oncomplete="PF('dlgPoLinkedJob').show()" title="Link to #{custom.labels.get('IDS_JOB')}" 
                                 action="#{jobs.fetchJobsListForOpps()}" disabled="#{poServices.poHdr.poJobId != 0}" update=":frmPoJobList:dtPoJobList"/> 

            </h:form>                
        </f:facet>            

        <h:form id="frmPoLinkedDocs">
<!--Bug #8050: From 7786: Time line status update issues by harshithad 02/06/22-->
            <p:dataTable id="dtPoLinkedDocs" value="#{poLinkedDocumentsService.poLinkedDocLst}" 
                         var="lnkdPoDoc"
                         rows="10"                                                  
                         filterEvent="keyup" 
                         widgetVar="dtPoLinkedDocsFilter"
                         draggableColumns="true"
                         emptyMessage="No Linked Documents found."  
                         paginator="true" 
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         paginatorAlwaysVisible="false" 
                         class="tblmain"
                         rowKey="#{lnkdPoDoc.recId}" resizableColumns="true" 
                         >

                <p:column headerText="Type" filterBy="#{lnkdPoDoc.docType}" filterMatchMode="contains" sortBy="#{lnkdPoDoc.docType}" id="clmnDocType">

                    <p:commandLink rendered="#{lnkdPoDoc.docType eq 'Opportunity'}"  value="#{lnkdPoDoc.docType}" id="cmndLnkOpp"
                                   onclick="window.open('../../opploop/opportunity/OpportunityView.xhtml?opid=#{lnkdPoDoc.docId}', '_blank');"
                                   >
                    </p:commandLink>
                    <p:commandLink rendered="#{lnkdPoDoc.docType eq 'Comm. Trans.'}"  value="#{lnkdPoDoc.docType}" id="cmndLnkCommTrans"
                                   onclick="window.open('../../datamanagement/commtransactions/CommtransDetails.xhtml?id=#{lnkdPoDoc.docId}', '_blank');"

                                   >
                    </p:commandLink>                    
                    <p:commandLink rendered="#{lnkdPoDoc.docType eq 'Quote'}"  value="#{lnkdPoDoc.docType}" id="cmndLnkQuotes"
                                   onclick="window.open('../../opploop/quotes/NewQuote.xhtml?recId=#{lnkdPoDoc.docId}', '_blank');"

                                   >
                    </p:commandLink>
                    <!--#7824: CRM-5543: PO Flow:  ability to link POs to Quotes and Jobs - poornima-->
                    <p:commandLink rendered="#{lnkdPoDoc.docType eq 'Job'}"  value="#{lnkdPoDoc.docType}" id="cmndLnkJob"
                                   onclick="window.open('../../opploop/jobs/JobsView.xhtml?id=#{lnkdPoDoc.docId}', '_blank');"

                                   >
                    </p:commandLink>
                </p:column>
                <p:column headerText="Doc. No." filterBy="#{lnkdPoDoc.docNo}" filterMatchMode="contains" sortBy="#{lnkdPoDoc.docNo}" id="clmnDocNo">
                    <h:outputText value="#{lnkdPoDoc.docNo}" />
                </p:column>
                <p:column headerText="Doc. Date" filterBy="#{oppLinkedDocumentsService.getFormattedDate(lnkdPoDoc.docDate, 'da')}" filterMatchMode="contains" sortBy="#{lnkdPoDoc.docDate}" id="clmnDocDate">
                    <h:outputText value="#{oppLinkedDocumentsService.getFormattedDate(lnkdPoDoc.docDate, 'da')}" />
                </p:column>
                <p:column headerText="Topic" filterBy="#{lnkdPoDoc.docTopic}" filterMatchMode="contains" sortBy="#{lnkdPoDoc.docTopic}" id="clmnPoTopic">
                    <h:outputText value="#{lnkdPoDoc.docTopic}" />
                </p:column>
                <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{lnkdPoDoc.docCustomer}" filterMatchMode="contains" sortBy="#{lnkdPoDoc.docCustomer}" id="clmnDocCust">
                    <h:outputText value="#{lnkdPoDoc.docCustomer}" />
                </p:column>
                <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{lnkdPoDoc.docPrinci}" filterMatchMode="contains" sortBy="#{lnkdPoDoc.docPrinci}" id="clmnDocPrinci">
                    <h:outputText value="#{lnkdPoDoc.docPrinci}" />
                </p:column>
                <p:column headerText="Value" filterBy="#{lnkdPoDoc.docValue}" filterMatchMode="contains" sortBy="#{lnkdPoDoc.docValue}" id="clmnDocValue">
                    <h:outputText value="#{lnkdPoDoc.docValue}" style="float: right" />
                </p:column>
            </p:dataTable>

        </h:form>
    </p:dialog>

    <!--#7824: CRM-5543: PO Flow:  ability to link POs to Quotes and Jobs - poornima-->
    <p:dialog id="poLinkedOppDlg" header="Link PO to #{custom.labels.get('IDS_OPPS')}" width="100%"
              widgetVar="dlgPoLinkedOpp" draggable="false" resizable="false" maximizable="true" closeOnEscape="true">
        <h:form id="frmPoOppList" >  
            <p:dataTable id="dtPoOppList" widgetVar="dtPoOppListTable"
                         value="#{poHdr.oppsList}" 
                         var = "opp" 
                         selectionMode="single" 
                         rowKey="#{opp.oppId}" 
                         selection="#{viewOppList.selectopp}" 
                         emptyMessage="No open #{custom.labels.get('IDS_OPPS')} found." 
                         paginator="true" rows="10"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="5,10,15" 
                         paginatorAlwaysVisible="true" 
                         class="tblmain">
                <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
                <p:ajax event="rowSelect" listener="#{viewOppList.onselectPoOpps}" oncomplete="PF('dlgPoLinkedOpp').hide()"  update="frmPoLinksHeader frmPoLinkedDocs:dtPoLinkedDocs :formPoLinkDoc frmPo" />  

                <p:column filterBy="#{opp.custName}" id="custName" filterMatchMode="contains" sortBy="#{opp.custName}" >
                    <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet> 
                        #{opp.custName}
                </p:column>
                <p:column filterBy="#{opp.principalName}" id="principalName" filterMatchMode="contains" sortBy="#{opp.principalName}">
                    <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet> 
                        #{opp.principalName}
                </p:column> 
                <p:column filterBy="#{opp.distriName}" id="distriName" filterMatchMode="contains" sortBy="#{opp.distriName}" >
                    <f:facet name="header">#{custom.labels.get('IDS_DISTRI')}</f:facet> 
                        #{opp.distriName}
                </p:column>

                <!--3614-->
                <p:column filterBy="#{opp.oppCustProgram}" filterMatchMode="contains" sortBy="#{opp.oppCustProgram}">
                    <f:facet name="header">#{custom.labels.get('IDS_PROGRAM')}</f:facet> 
                        #{opp.oppCustProgram}
                </p:column> 

                <p:column filterBy="#{opp.oppActivity}" filterMatchMode="contains" sortBy="#{opp.oppActivity}">
                    <f:facet name="header">#{custom.labels.get('IDS_ACTIVITY')}</f:facet>  
                        #{opp.oppActivity}
                </p:column>
                <!--#13178 ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
                <p:column filterBy="#{opp.oppStatus}" filterMatchMode="contains" sortBy="#{opp.oppStatus}">
                    <f:facet name="header">#{custom.labels.get('IDS_ACT_STATUS')}</f:facet>  
                        #{opp.oppStatus}
                </p:column>

                <p:column filterBy="#{opp.oppFollowUp}" filterMatchMode="contains" sortBy="#{opp.oppFollowUp}">
                    <f:facet name="header">#{custom.labels.get('IDS_FOLLOW_UP')}</f:facet> 
                    <p:outputLabel value="#{opp.oppFollowUp}" >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                    </p:outputLabel>
                </p:column>

                <!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
                <p:column filterBy="#{opp.oppNextStep}" filterMatchMode="contains" sortBy="#{opp.oppNextStep}" styleClass="might-overflow">
                    <f:facet name="header">#{custom.labels.get('IDS_NEXT_STEP')}</f:facet> 
                        #{opp.oppNextStep}
                </p:column>

            </p:dataTable>
        </h:form>

    </p:dialog>

    <p:dialog id="poLinkedQuoteDlg" header="Link PO to #{custom.labels.get('IDS_QUOTES')}" width="100%"
              widgetVar="dlgPoLinkedQuote"  modal="true" draggable="false" resizable="false" maximizable="true" closeOnEscape="true">
        <h:form id="frmPoQuoteList" >                     
            <p:dataTable  id="dtPoQuoteList" widgetVar="dtPoQuoteListTable" 
                          value="#{quotesHdr.quot}" var="quot" selectionMode="single" 
                          rowKey="#{quot.recId}"  selection="#{quotesHdr.selectquote}"
                          emptyMessage="No open #{custom.labels.get('IDS_QUOTES')} found." 
                          paginator="true" rows="10" 
                          paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                          rowsPerPageTemplate="5,10,15" 
                          paginatorAlwaysVisible="true" class="tblmain">
                <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
                <p:ajax event="rowSelect" listener="#{quotesHdr.onselectPoQuotes}" oncomplete="PF('dlgPoLinkedQuote').hide()" update="frmPoLinksHeader frmPoLinkedDocs :formPoLinkDoc frmPo"/> 

                <p:column  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{quot.oppCustomerName}"  sortBy="#{quot.oppCustomerName}" id="custH">

                    #{quot.oppCustomerName}
                </p:column>

                <p:column  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{quot.oppPrincipalName}" sortBy="#{quot.oppPrincipalName}" id="prinH">
                    #{quot.oppPrincipalName}                               
                </p:column>

                <p:column  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_SALES_TEAM')}" filterBy="#{quot.oppSManName}" sortBy="#{quot.oppSManName}"  
                           id="smanHId">
                    #{quot.oppSManName} 
                </p:column>

                <p:column  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_DISTRI')}" filterBy="#{quot.oppDistriName}"   sortBy="#{quot.oppDistriName}"  
                           id="distriHId">
                    #{quot.oppDistriName}  
                </p:column>

                <p:column  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_PROGRAM')}" filterBy="#{quot.oppCustProgram}"  sortBy="#{quot.oppCustProgram}"  id="prog">
                    #{quot.oppCustProgram}                          
                </p:column>

                <p:column  filterMatchMode="contains" headerText="Quote #" filterBy="#{quot.quotNumber}"  sortBy="#{quot.quotNumber}" >
                    #{quot.quotNumber}                     
                </p:column> 

                <p:column  filterMatchMode="contains" headerText="Date" filterBy="#{quot.quotDate}"  sortBy="#{quot.quotDate}" >
                    #{quot.quotDate}                    
                </p:column>

                <p:column  filterMatchMode="contains" headerText="Value" filterBy="#{quot.oppValue}"  sortBy="#{quot.oppValue}" 
                           id="valHId">
                    #{quot.oppValue}
                </p:column>

                <p:column  filterMatchMode="contains" headerText="Status" filterBy="#{quot.delivStatusName}"  sortBy="#{quot.delivStatusName}" id="statH">
                    #{quot.delivStatusName}                                 
                </p:column>
            </p:dataTable>
        </h:form>

    </p:dialog>

    <p:dialog id="poLinkedJobDlg" header="Link PO to #{custom.labels.get('IDS_JOB')}" widgetVar="dlgPoLinkedJob"  modal="true" >
        <h:form id="frmPoJobList" >

            <p:dataTable  id="dtPoJobList" widgetVar="dtPoJobList" paginator="true"                    
                          value="#{jobs.jobList}"                         
                          filterEvent="keyup"                          
                          filteredValue="#{jobs.filteredJobList}" 
                          selectionMode="single" 
                          var="viewjobs"                                       
                          rowKey="#{jobs.jobId}"                                    
                          paginatorAlwaysVisible="false"
                          paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                          rows="10"   class="tblmain"
                          emptyMessage="No jobs available"
                          draggableColumns="true">

                <f:facet name="header">
                    <p:inputText style="display: none"   id="globalFilter" onkeyup="PF('dtPoJobList').filter()" />
                </f:facet>

                <p:column id="col1" headerText="#{custom.labels.get('IDS_JOB')} Name"  filterMatchMode="contains" filterBy="#{viewjobs.jobDescr}" sortBy="#{viewjobs.jobDescr}">
                    <p:commandLink value="#{viewjobs.jobDescr}" actionListener="#{jobs.onPoJobRowSelect(poServices.poHdr.poId,viewjobs.jobId)}" oncomplete="PF('dlgPoLinkedJob').hide()"  update=":frmPo :formPoLinkDoc">
                    </p:commandLink>
                </p:column>

                <p:column  id="col2"  headerText="Job Owner" filterMatchMode="contains" filterBy="#{viewjobs.jobOwnerName}" sortBy="#{viewjobs.jobOwnerName}">
                    <p:commandLink value="#{viewjobs.jobOwnerName}" actionListener="#{jobs.onPoJobRowSelect(poServices.poHdr.poId,viewjobs.jobId)}" oncomplete="PF('dlgPoLinkedJob').hide()"  update=":frmPo :formPoLinkDoc">
                    </p:commandLink>
                </p:column>

                <p:column  id="col3"  headerText="Address" filterMatchMode="contains" filterBy="#{viewjobs.jobFormattedAddr}" sortBy="#{viewjobs.jobFormattedAddr}">
                    <p:commandLink value="#{viewjobs.jobFormattedAddr}" actionListener="#{jobs.onPoJobRowSelect(poServices.poHdr.poId,viewjobs.jobId)}"  oncomplete="PF('dlgPoLinkedJob').hide()"  update=":frmPo :formPoLinkDoc">
                    </p:commandLink>
                </p:column>

                <p:column  id="col4"  headerText="#{custom.labels.get('IDS_JOB_STAGE')}" filterMatchMode="contains" filterBy="#{viewjobs.jobActivityName}" sortBy="#{viewjobs.jobActivityName}">
                    <p:commandLink value="#{viewjobs.jobActivityName}" actionListener="#{jobs.onPoJobRowSelect(poServices.poHdr.poId,viewjobs.jobId)}"  oncomplete="PF('dlgPoLinkedJob').hide()"  update=":frmPo :formPoLinkDoc">
                    </p:commandLink>
                </p:column>

                <p:column  id="col5"  headerText="Awarded To" filterMatchMode="contains" filterBy="#{viewjobs.jobAwardedToName}" sortBy="#{viewjobs.jobAwardedToName}">
                    <p:commandLink value="#{viewjobs.jobAwardedToName}" actionListener="#{jobs.onPoJobRowSelect(poServices.poHdr.poId,viewjobs.jobId)}"  oncomplete="PF('dlgPoLinkedJob').hide()"  update=":frmPo :formPoLinkDoc">
                    </p:commandLink>
                </p:column>

                <p:column  id="col6"  headerText="Awarded Date" filterMatchMode="contains" filterBy="#{viewjobs.jobAwardedDate}" sortBy="#{viewjobs.jobAwardedDate}">
                    <p:outputLabel  value="#{viewjobs.jobAwardedDate}"  >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                    </p:outputLabel> 
                </p:column>

                <p:column  id="col7"  headerText="Last Modified date" filterMatchMode="contains" filterBy="#{viewjobs.updDate}" sortBy="#{viewjobs.updDate}">
                    <p:outputLabel  value="#{rFUtilities.convertFromUTC(viewjobs.updDate)}"  >
                        <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                    </p:outputLabel>   
                </p:column>

                <p:column  id="col8"  headerText="Created date" filterMatchMode="contains" filterBy="#{viewjobs.insDate}" sortBy="#{viewjobs.insDate}">
                    <p:outputLabel  value="#{rFUtilities.convertFromUTC(viewjobs.insDate)}"  >
                        <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                    </p:outputLabel> 
                </p:column>

            </p:dataTable>     

        </h:form>

    </p:dialog>


</ui:composition>