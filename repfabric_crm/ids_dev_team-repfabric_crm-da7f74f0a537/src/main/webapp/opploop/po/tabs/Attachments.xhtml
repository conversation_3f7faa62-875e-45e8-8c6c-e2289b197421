<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    <!--     //PMS:2816
            //Seema - 24/12/2020-->
    <p:remoteCommand name="loadAttachment" autoRun="true" actionListener="#{poServices.loadAttachments()}" />
    <h:panelGroup id="pnlAttach" class="pnls">
        <!--        <p:fileUpload 
                    fileUploadListener="#{poServices.savePOAttchments}" 
                    mode="advanced" dragDropSupport="false"  
                    auto="false"  
                    sizeLimit="31457280" label="Choose File" update="dtAtc  @this" 
                    invalidSizeMessage="Exceeds maximum size limit of 30MB" />-->
        <!--         PMS 3898: CRM-4046: PO/company/Opp/Job: ID drag and drop area and eliminate the "upload" button -->
       <!-- Feature #11460: CRM-7082   Paperclip on orders list - file preview window popup by harshithad on 19/06/23-->
        <p:fileUpload dragDropSupport="true"  fileUploadListener="#{poServices.savePOAttchments}"
                      sizeLimit="31457280" id="fileUpdAtch" auto="true" oncomplete="PF('atccDlg').clearFilters();"
                      invalidSizeMessage="Exceeds maximum size limit of 30MB" 
                      label="Choose File"  update="dtAtc  @this" >
            <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
            <f:attribute name="foo" value="1" />
        </p:fileUpload>
        <p:dataTable  value="#{poServices.attachmentLst}" widgetVar="atccDlg"
                      filteredValue="#{poServices.filterattachmentLst}"
                      style="width: 80%"
                      var="atch" id="dtAtc">
            <!--Feature #9892 CRM-5541: PO Flow: ability to quickly see documents throughout the reconciliation process in instant by harshithad on 08/12/22-->
            <p:column width="15%" >
                <!--                 //PMS:2795
                            //Seema - 22/12/2020-->

                <!--Feature #9892 CRM-5541: PO Flow: ability to quickly see documents throughout the reconciliation process in instant by harshithad on 08/12/22-->

                <p:commandButton   immediate="true" process="@none" title="Download Attachment" 
                                   class="btn-primary btn-xs"  icon="fa fa-download" id="btnDownLoad"
                                   onclick="primeFaces.monitorDownload(start, stop)" style="height:23px;width:23px;"
                                   >
                    <p:fileDownload    value="#{poServices.getDownlaodFile(atch)}" id="dwnLoadfile"/>
                </p:commandButton>
                <p:spacer width="4" />
                <!--23-01-2024 12461 Incidental Finding: CRM-7432 > Purchase Orders : preview of files remove extra space & block resize-->
                <p:commandButton icon="fa fa-eye" title="Preview file in a dialog" styleClass="btn-primary btn-xs" 
                                 style="min-width: 0px; height:23px;width:23px;" immediate="true" process="@this"
                                 onclick="PF('dlgWaitPreviewing').show();" 
                                 actionListener="#{poServices.previewInDialog(atch.poAttaName)}"
                                 oncomplete="PF('wvDlgPreviewIframe').show();PF('dlgWaitPreviewing').hide();" update="dlgPreviewPoFile"  /> 

                <p:spacer width="4" />
                <!--Feature #9892 CRM-5541: PO Flow: ability to quickly see documents throughout the reconciliation process in instant by harshithad on 08/12/22-->
                <p:commandButton styleClass="btn-danger btn-xs btn-icon" title="Delete" 
                                 icon="fa fa-trash" process="@this" id="btnAtchDelete" style="height:20px;width:20px;"
                                 actionListener="#{poServices.attachementDelete(atch)}"  >
                </p:commandButton>
            </p:column>
            <!--Feature #11460: CRM-7082   Paperclip on orders list - file preview window popup by harshithad on 19/06/23-->
            <p:column headerText="File Name" filterBy="#{atch.poAttaName}" filterMatchMode="contains"  sortBy="#{atch.poAttaName}"  style="text-align: left">
                #{atch.poAttaName}

                <div style="float: right;">
                    <p:outputLabel value="#{atch.poAttaFile/1024}"   id="oLblFileSize"
                                   style="font-weight: lighter;font-size: smaller;" >
                        <f:convertNumber  maxFractionDigits="1" type="number"/>
                    </p:outputLabel>
                    <p:outputLabel value=" kb" 
                                   style="font-weight: lighter;font-size: smaller;"/>
                </div>
            </p:column>
           <!--12866:10/01/2024: CRM-7598   Orders: Add email attachments automatically to attachments tab-->
            <p:column headerText="Uploaded On" style="text-align: center;width: 15%" filterBy="#{globalParams.formatDateTime(atch.insDate,'dt')}" filterMatchMode="contains" sortBy="#{globalParams.formatDateTime(atch.insDate,'dt')}"  >
            <p:outputLabel id="oTxtPoAtchDateTime" value="#{globalParams.formatDateTime(atch.insDate,'dt')}"  >
                <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>

            </p:outputLabel>
        </p:column>
            <!--Feature #9892 CRM-5541: PO Flow: ability to quickly see documents throughout the reconciliation process in instant by harshithad on 08/12/22-->
        </p:dataTable>
    </h:panelGroup>  
    <!--     PMS 3898: CRM-4046: PO/company/Opp/Job: ID drag and drop area and eliminate the "upload" button -->
    <style>
        .ui-fileupload-content:before{
            content: "Please Drop Files Here";
            margin-left: 50vh;
            text-justify: auto;
            font-size: 20px;
            color:#b4c3d0;
            align-content: center;

        } 
    </style>
</ui:composition>