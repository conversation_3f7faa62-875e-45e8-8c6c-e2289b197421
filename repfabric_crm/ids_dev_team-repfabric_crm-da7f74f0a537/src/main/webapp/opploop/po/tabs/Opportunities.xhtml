<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                >
    <p:remoteCommand name="loadOpps" autoRun="true" actionListener="#{poServices.loadOpps()}" />
    <div class="ui-g ui-fluid header-bar">
        <div class="ui-sm-12 ui-md-9 ui-lg-9 ">
            <p:outputLabel styleClass="acct-name-hdr" class="sub_title"> #{custom.labels.get('IDS_OPP')} Details </p:outputLabel>
        </div>
        <div class="ui-sm-12 ui-md-1 ui-lg-1 ">

        </div>
        <div class="ui-sm-12 ui-md-1 ui-lg-1 ">

        </div>
        <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
            <p:outputPanel id="btnLnkdOpp" >
                <!--#8287: po linked opp unlinking issue - poornima - added update and oncomplete to fix click issue in opportunity list-->
                <p:commandButton style="float: right"
                                 class="btn btn-info btn-xs"  
                                 disabled="#{poServices.oppLst.size()>0}"  
                                 value="Link to #{custom.labels.get('IDS_OPP')}" 
                                 title="Link to #{custom.labels.get('IDS_OPP')}" 
                                 id="btnPoLnkToOpp" oncomplete="PF('oppPoDlg').show();"
                                 actionListener="#{poServices.loadOppsToLink()}"
                                 process="@this" update="dlgPoOpps"/>
            </p:outputPanel>
        </div>
    </div>  
    <p:dataTable id="dtOppList" widgetVar="oppListTable" 
                 value="#{poServices.oppLst}" var = "opp" selectionMode="single" rowKey="#{opp.oppId}"  
                 emptyMessage="No open #{custom.labels.get('IDS_OPPS')} found." paginator="true" rows="10" 
                 filteredValue="#{poServices.filteroppLst}"
                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                 rowsPerPageTemplate="5,10,15" paginatorAlwaysVisible="false" class="tblmain"> 
        <p:column filterBy="#{opp.custName}" filterMatchMode="contains" sortBy="#{opp.custName}" rendered="#{not (companyServiceImpl.comp.getCompType()==2)}" id="clmnCustomertomer">
            <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet> 
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                #{opp.custName}
            </h:link>
        </p:column>

        <p:column filterBy="#{opp.principalName}" filterMatchMode="contains" sortBy="#{opp.principalName}" rendered="#{not (companyServiceImpl.comp.getCompType()==1)}" id="clmnPrinci">
            <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet> 
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                #{opp.principalName}
            </h:link>
        </p:column>

        <p:column filterBy="#{opp.oppCustProgram}" filterMatchMode="contains" sortBy="#{opp.oppCustProgram}" id="clmnPrgrm">
            <f:facet name="header">#{custom.labels.get('IDS_PROGRAM')}</f:facet> 
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                #{opp.oppCustProgram}
            </h:link>
        </p:column> 

        <p:column filterBy="#{opp.oppActivity}" filterMatchMode="contains" sortBy="#{opp.oppActivity}" id="clmnActivity">
            <f:facet name="header">#{custom.labels.get('IDS_ACTIVITY')}</f:facet>  
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                #{opp.oppActivity}
            </h:link>
        </p:column>
       <!--#13178 ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
        <p:column filterBy="#{opp.oppStatus}" filterMatchMode="contains" sortBy="#{opp.oppStatus}" id="clmnStatus">
            <f:facet name="header">#{custom.labels.get('IDS_ACT_STATUS')}</f:facet>  
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                #{opp.oppStatus}
            </h:link>
        </p:column>
        <p:column filterBy="#{opp.oppFollowUp}" filterMatchMode="contains" sortBy="#{opp.oppFollowUp}" id="clmnFollowUp">
            <f:facet name="header">#{custom.labels.get('IDS_FOLLOW_UP')}</f:facet> 
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                <p:outputLabel value="#{opp.oppFollowUp}" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:outputLabel>

            </h:link>
        </p:column>
        <!--Feature #5025 RE: Repfabric / AVX CRMSync / Relabeling Fields / "Next Step" (Pending)-->
        <p:column filterBy="#{opp.oppNextStep}" filterMatchMode="contains" sortBy="#{opp.oppNextStep}" id="clmnCNextstep">
            <f:facet name="header">#{custom.labels.get('IDS_NEXT_STEP')}</f:facet> 
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                #{opp.oppNextStep}
            </h:link>
        </p:column>
        <p:column filterBy="#{opp.oppValue}" filterMatchMode="contains" sortBy="#{opp.oppValue}" id="clmnOppValue">
            <f:facet name="header">Value</f:facet> 
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                #{opp.oppValue}
            </h:link>
        </p:column>
        <p:column filterBy="#{opp.oppLines}" filterMatchMode="contains" sortBy="#{opp.oppLines}" id="clmnLineItem">
            <f:facet name="header">#{custom.labels.get('IDS_LINE_ITEM')}</f:facet>  
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                #{opp.oppLines}
            </h:link>
        </p:column>
        <p:column  filterMatchMode="contains" sortBy="#{opp.oppCloseStatus}" id="clmnCloseStatus">
            <f:facet name="header">Closed Status</f:facet> 
            <p:outputLabel value="#{opp.oppCloseStatus==0?'Open':'Closed'}" />
        </p:column>
    </p:dataTable>
    
    <!--#8287: po linked opp unlinking issue - poornima - commented-->
      <!--<ui:include src="../dlg/LinkToOppDlg.xhtml"/>-->

</ui:composition>