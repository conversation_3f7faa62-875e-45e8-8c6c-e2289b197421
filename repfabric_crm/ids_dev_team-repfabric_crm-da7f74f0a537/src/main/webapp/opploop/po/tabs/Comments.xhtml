<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    <p:remoteCommand name="loadCmnts" autoRun="true" actionListener="#{poServices.loadComments()}" />

    <div class="with-border">
        <h:panelGroup id="pnlCmnts" class="pnls" >
            <p:inputTextarea rows="10" cols="60" 
                             value="#{poServices.pocomments.poCommText}" id="inpTxtAreaComnt"
                             style="width: 100%">
                <p:ajax process="@this" />
            </p:inputTextarea>
            <p:watermark for="inpTxtAreaComnt" value="Enter comment here"/>
            <br></br>
              <!--                        //PMS:1997
                        //seema - 15/10/2020-->
            <p:commandButton  value="Save"  
                              class="btn btn-xs btn-success"
                              style="margin-left: 0px" 
                              actionListener="#{poServices.saveComments()}"
                              process="@this"
                              id="btnPoComntSave"
                              widgetVar="cmntSave" 
                              onclick="PF('cmntSave').disable()" 
                              oncomplete="PF('cmntSave').enable();"
                              />
            <br/>
            <div class="dtnoborder"  >
                <p:dataTable  value="#{poServices.commentLst}" 
                              emptyMessage="No #{custom.labels.get('IDS_COMMENTS')} Added" 
                              filteredValue="#{poServices.filterCommentLst}"
                              widgetVar="cmntDt"
                              var="cmnt" id="dtCmnt">
                    <p:column style="padding: 0px;" >  
                        <div class="comment_div">  
                            <div>
                                <h:outputLabel id="cmntDate" value="#{poServices.getFormattedDate(cmnt.poCommDate,'dt')}"
                                               style="float: right;"/>
                                <p:spacer width="4" style="float: right;"/>
                                <h:graphicImage library="images" name="dummy.jpg" width="20" height="20"/>                 
                                <h:outputLabel value="#{users.getUserName(cmnt.poCommUser)} : " style="font-weight: bold"/>

                                <h:outputLabel class="dtcomnt"  value="#{params.convertFromUTC(cmnt.poCommDate)}" >
                                    <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                                </h:outputLabel>

                                <p:commandButton disabled="#{poServices.poHdr.poCloseStatus==0}"
                                                 rendered="#{cmnt.poCommUser eq loginBean.userId}"
                                                 title="Remove comment"
                                                 process="@this" 
                                                 actionListener="#{poServices.deletePOComment(cmnt)}" 
                                                 update="dtCmnt " 
                                                 style="  background: none;float: right"
                                                 class="btn-danger btn-xs"
                                                 id="btnPoRemoveCmnt"
                                                 icon="fa fa-trash" />
                            </div>
                            <div class="comment_cmnt" style="white-space: pre-wrap">
                                <h:outputLabel id="cmnt" value="#{cmnt.poCommText}" escape="false"/>  
                            </div>
                        </div>
                    </p:column>
                </p:dataTable>   
            </div>
        </h:panelGroup>
    </div>
</ui:composition>