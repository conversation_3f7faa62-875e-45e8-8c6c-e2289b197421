
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    <p:remoteCommand name="applyActivePrinci" 
                     autoRun="false" immediate="true" process="@this"
                     actionListener="#{poServices.updateFieldsOnSelection(viewCompLookupService.selectedCompany)}"/>
    <!--Feature #8620 CRM-5990  PO: UAT: changing commission rate on line item not reflecting on header by harshithad--> 
    <p:remoteCommand autoRun="true" immediate="true" process="@this" actionListener="#{poServices.populatePoNetPercentange()}" update="frmPo:tabDetails:inptNumNet"/>

    <div class="ui-g ui-fluid header-bar">
        <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Primary Information</p:outputLabel>
        </div>
        <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
        </div>
    </div> 

    <div class="ui-g ui-fluid">
        <div class="ui-sm-6 ui-md-6 ui-lg-6">
            <div class="ui-g ui-fluid">
                <!--<div class="ui-sm-12 ui-md-12 ui-lg-12">-->
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="#{custom.labels.get('IDS_PRINCI')}" for="inputActivePrincName"  styleClass="required" id="oLblPrinci"/>
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8"> 
                    <h:panelGroup class="ui-inputgroup"  >
                        <!--                         //PMS:1498
                          //Seema - 28/08/2020-->
                        <!--//21-04-2022 : #7801  : CRM-5545: PO Flow: User Selectable commission percentage addition during addin po entry-->
                        <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                        <p:autoComplete id="inputActivePrincName" maxlength="30" 
                                        readonly="#{poServices.btnName=='Edit' or (poServices.purchaseCustomApp and poServices.principalBol)}" minQueryLength="2"
                                        completeMethod="#{poServices.completePrinci}"
                                        value="#{poServices.poHdr.princiName}">
                            <p:ajax event="itemSelect" listener="#{poServices.onSelectPrinci}"  />
                            <!--                            //PMS:1500
                            //seema - 27/08/202-->
                            <p:ajax event="change" listener="#{poServices.onprinciClear(poServices.poHdr.princiName)}" />
                        </p:autoComplete>
                        <h:inputHidden id="hdninputActivePrincName"  value="#{poServices.poHdr.princiName}"    />
                        <!--//21-04-2022 : #7801  : CRM-5545: PO Flow: User Selectable commission percentage addition during addin po entry-->
                        <p:commandButton  icon="fa fa-search" title="Choose Company" 
                                          process="@this" tabindex="2"
                                          disabled="#{poServices.btnName=='Edit' or (poServices.purchaseCustomApp and poServices.principalBol)}"
                                          actionListener="#{poServices.listLookUpvalues('applyActivePrinci', 1)}"
                                          update=":formCompLookup" 
                                          id="btnprinci"
                                          styleClass="btn-info btn-xs" />
                        <!--Bug #9764  ESCALATIONS CRM-6368  PO - can clear but not add new mfg by harshithad on 17/11/2022-->
                        <p:commandButton   icon="fa fa-close" 
                                           title="Clear" process="@this"
                                           disabled="#{poServices.btnName=='Edit' or poServices.principalBol}" 
                                           id="btnPoPrinciClear"
                                           actionListener="#{poServices.clearLookUp(1)}"
                                           class="button_top btn-danger btn-xs"  />
                    </h:panelGroup>
                </div>
                <!--</div>-->
                <!--<div class="ui-sm-12 ui-md-12 ui-lg-12">-->
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}"  
                                   for="inputPrimCustName"  styleClass="required" id="oLblCustomer"/>
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8" > 
                    <h:panelGroup class="ui-inputgroup"  >
                        <!--                         //PMS:1498
                          //Seema - 28/08/2020-->
                        <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                        <p:autoComplete id="inputPrimCustName" maxlength="30"  minQueryLength="2"
                                        readonly="#{poServices.btnName=='Edit'}"
                                        completeMethod="#{poServices.completeCust}"
                                        value="#{poServices.poHdr.custName}" >
                            <p:ajax event="itemSelect" listener="#{poServices.onSelectCust}"/>
                            <!--                            //PMS:1500
                                                        //seema - 27/08/202-->
                            <p:ajax event="change" listener="#{poServices.onCustClear(poServices.poHdr.custName)}" />
                        </p:autoComplete>

                        <h:inputHidden id="hdninputcName" value="#{poServices.poHdr.custName}" />
                        <!--Feature #11574 CRM-7105   PO: Access to Customer to Create Access Limited by Setting by harshithad on 04/07/23-->
                        <p:commandButton  icon="fa fa-search" title="Choose Company" 
                                          tabindex="4"
                                          actionListener="#{poServices.lookup(2)}" 
                                          action="#{viewCompLookupService.listCRM(2, 'applyActivePrinci')}" 
                                          oncomplete="PF('lookupComp').show()"
                                          update=":formCompLookup" 
                                          disabled="#{poServices.btnName=='Edit'}" 
                                          id="btnCustomer"
                                          styleClass="btn-info btn-xs" />
                        <p:commandButton   icon="fa fa-close" 
                                           title="Clear" 
                                           process="@this"
                                           id="btnPoCusotmerClear"
                                           disabled="#{poServices.btnName=='Edit'}" 
                                           actionListener="#{poServices.clearLookUp(2)}"
                                           class="button_top btn-danger btn-xs"   />
                    </h:panelGroup>
                </div>
                <!--</div>-->
                <!--<div class="ui-sm-12 ui-md-12 ui-lg-12">-->
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="#{custom.labels.get('IDS_DISTRI')}"   for="inputDistriName" id="oLblDistri"/>
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8"> 
                    <h:panelGroup class="ui-inputgroup"  >
                        <!--                         //PMS:1498
                           //Seema - 28/08/2020-->
                        <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23--> 
                        <p:autoComplete id="inputDistriName" maxlength="30" completeMethod="#{poServices.completeDistri}"  
                                        readonly="#{poServices.btnName=='Edit'}" minQueryLength="2"
                                        value="#{poServices.poHdr.distriName}" >
                            <p:ajax event="itemSelect" listener="#{poServices.onSelectDistri}" />
                            <!--                            //PMS:1500
                          //seema - 27/08/202-->
                            <p:ajax event="change" listener="#{poServices.onDistriClear(poServices.poHdr.distriName)}" />
                        </p:autoComplete>
                        <h:inputHidden id="hdninputDistrName"  value="#{poServices.poHdr.distriName}"  />

                        <p:commandButton  icon="fa fa-search" title="Choose Company" tabindex="6"
                                          actionListener="#{poServices.lookup(3)}" 
                                          action="#{viewCompLookupService.listAll('applyActivePrinci', 3, 0, 1)}"
                                          oncomplete="PF('lookupComp').show()"
                                          update=":formCompLookup" 
                                          id="btnPoDistri"
                                          disabled="#{poServices.btnName=='Edit'}" 
                                          styleClass="btn-info  btn-xs" />
                        <p:commandButton   icon="fa fa-close"  title="Clear" 
                                           process="@this"
                                           id="btnPoDistriClear"
                                           disabled="#{poServices.btnName=='Edit'}" 
                                           actionListener="#{poServices.clearLookUp(3)}"
                                           class="button_top btn-danger btn-xs"  
                                           />
                    </h:panelGroup>
                </div>
                <!--</div>-->
                <!--<div class="ui-sm-12 ui-md-12 ui-lg-12">-->
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="#{custom.labels.get('IDS_SECOND_CUSTOMER')}"   for="inputsPrimCustName"/>
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8"> 
                    <h:panelGroup class="ui-inputgroup"  >  
                        <!--                         //PMS:1498
                                                   //Seema - 28/08/2020-->
                        <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                        <p:autoComplete id="inputsPrimCustName" maxlength="30"  readonly="#{poServices.btnName=='Edit'}"  
                                        completeMethod="#{poServices.completeSecCust}" minQueryLength="2"
                                        value="#{poServices.poHdr.secCustName}" >                >
                            <p:ajax event="itemSelect" listener="#{poServices.onSelectSecCust}" process="@this"/>
                            <!--                            //PMS:1500
                         //seema - 27/08/202-->
                            <p:ajax event="change" listener="#{poServices.onsCustClear(poServices.poHdr.secCustName)}" />
                        </p:autoComplete>

                        <h:inputHidden id="hdninputSecCustName" value="#{poServices.poHdr.secCustName}"   />
                        <p:commandButton  icon="fa fa-search" title="Choose Company" 
                                          actionListener="#{poServices.lookup(4)}" 
                                          disabled="#{poServices.btnName=='Edit'}" tabindex="8"
                                          action="#{viewCompLookupService.listAll('applyActivePrinci',2, 0, 1)}"
                                          oncomplete="PF('lookupComp').show()"
                                          update=":formCompLookup" 
                                          id="btnPoSecondCustomer"
                                          styleClass="btn-info btn-xs" />
                        <p:commandButton   icon="fa fa-close" 
                                           title="Clear" 
                                           process="@this"
                                           id="btnPoSecCustomerClear"
                                           disabled="#{poServices.btnName=='Edit'}" 
                                           actionListener="#{poServices.clearLookUp(5)}"
                                           class="button_top btn-danger btn-xs"  />
                    </h:panelGroup>
                </div>
                <!--</div>-->
                <!--<div class="ui-sm-12 ui-md-12 ui-lg-12">-->
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')}" for="inptTxtSalesTeam" id="oLblPoSalesTeam"/>
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8"> 
                    <h:panelGroup class="ui-inputgroup"  style="width: 94%" >
                        <!--                        //PMS:2637
                                                //Seema - 08/12/2020-->
                        <p:inputText id="inptTxtSalesTeam" value="#{poServices.poHdr.smanName}" readonly="true"/>
                        <h:inputHidden id="hdninputcSTName" value="#{poServices.poHdr.smanName}"   />
                        <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_SALES_TEAM')}" 
                                          actionListener="#{poServices.loadSalesTeam()}"
                                          disabled="#{poServices.btnName=='Edit'}" style="width: 17%"
                                          tabindex="9"
                                          oncomplete="PF('dlgSalesManLookup').show()"
                                          update=":frmSalesManReconLookup" 
                                          id="btnPOSalesTeam"
                                          styleClass="btn-info  btn-xs" />
                        <!--                        <p:commandButton   icon="fa fa-close" 
                                                                   process="@this" title="Clear"
                                                                   rendered="false"
                                                                   class="button_top btn-danger btn-xs"
                                                                   />-->
                    </h:panelGroup>
                </div>
                <!--</div>-->
            </div>
        </div>
        <div class="ui-sm-6 ui-md-6 ui-lg-6">
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-12 ui-lg-12" style="margin-top: -1px">
                    <div class="ui-g ui-fluid"  style="align-items: center;background-color: #ecf0f5;border-radius: 4px; -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0.05);height: 30px;">
                        <div class="ui-sm-12 ui-md-10 ui-lg-10">
                            <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Billboard Message</p:outputLabel>
                        </div>
                        <div class="ui-sm-12 ui-md-2 ui-lg-2" style="text-align: right">
                            <p:commandButton styleClass="btn-primary  btn-xs btn-icon" id="btnBbAdd"
                                             icon="fa fa-plus" title="Add Billboard"  
                                             actionListener="#{poServices.popluateBillboard()}"
                                             onclick="PF('billMsgDlg').show()" 
                                             disabled="#{!(poServices.billBoards.recId==0 and poServices.btnName=='Save')}"
                                             style="width:2em;">

                            </p:commandButton>
                            <!--                            14-01-2021:Task#3056-CRM-3654: PO Module feedback for TW/John #7-->
                            <!--// 14-06-2022 : 8227 : PF 7 related bugs: PO-->
                            <!--Bug #11661:ESCALATIONS CRM-7152   Purchase Orders: Billboard Message functionality by harshithad on 14/07/23-->
                            <!--Bug #13223 CRM-7729  URGENT!!!! Purchase Orders: PO ID# 1093 duplicated (causing errors in PBI refrersh)   by harshithad on 29/02/2024-->
                            <p:commandButton class="btn-primary btn-xs"  id="btnBbEdit"
                                             styleClass="btn-primary  btn-xs btn-icon" 
                                             title="Edit Billboard" icon="fa fa-pencil"
                                               
                                             actionListener="#{poServices.popluateBillboard()}"
                                             oncomplete="PF('billMsgDlg').show()"
                                             update=":frmPo:tabDetails:otptPnlBlbrdMsg"
                                             style="width:2em;">

                            </p:commandButton>
                        </div>
                    </div> 
                </div>
                <div class="ui-sm-12 ui-md-12 ui-lg-12">
                    <p:inputTextarea  value="#{poServices.billBoards.bbMessage}"  
                                      readonly="true" id="inpttxtareamsg" >
                    </p:inputTextarea>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="#{custom.labels.get('IDS_SECOND_CUSTOMER')} #{custom.labels.get('IDS_SALES_TEAM')}" for="inpTxtSecSalesTeam" id="oLblSecST"/>
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8"> 
                    <h:panelGroup class="ui-inputgroup">
                        <p:inputText id="inpTxtSecSalesTeam" value="#{poServices.poHdr.secSmanName}" readonly="true"/>            
                    </h:panelGroup>
                </div>
            </div>
        </div>
    </div>



    <div class="ui-g ui-fluid header-bar">
        <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Transaction Details</p:outputLabel>
        </div>
        <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
        </div>
    </div>
    <div class="ui-g ui-fluid">
        <div class="ui-sm-12 ui-md-12 ui-lg-12">
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="#{custom.labels.get('IDS_PROGRAM')}" id="oLblPoPrgm"/>
                </div>
                <div class="ui-sm-12 ui-md-10 ui-lg-10">  
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputText value="#{poServices.poHdr.poProgram}" id="inpTxtProgram" tabindex="10"  readonly="#{poServices.btnName=='Edit'}" >
                    </p:inputText> 
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Quote Number" for="inptxtQuoteNum" id="oLblPOQutoeNum"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputText  value="#{poServices.poHdr.poQuoteNumber}" id="inptxtQuoteNum" tabindex="11"  readonly="#{poServices.btnName=='Edit'}" >
                    </p:inputText>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Quote Date" for="calQuoteDate" id="oLblQuoteDate"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                     //PMS:1485
                                //semea - 28/08/2020-->
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:calendar    pattern="#{globalParams.dateFormat}" 
                                   value="#{poServices.poHdr.poQuoteDate}"  
                                   tabindex="12"
                                   readonly="#{poServices.btnName=='Edit'}"  
                                   class="calendarClass" 
                                   converterMessage="Invalid Date format"
                                   id="calQuoteDate" >  
                    </p:calendar> 
                </div>

                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="PO Number" for="txtMainPOnum" styleClass="required" id="oLblPoNum"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputText value="#{poServices.poHdr.poNumber}"   readonly="#{poServices.btnName=='Edit'}" 
                                 id="txtMainPOnum"  maxlength="60" tabindex="13">
                    </p:inputText> 
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="PO Date" for="calPODate" styleClass="required" id="oLblPoDate"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:calendar  pattern="#{globalParams.dateFormat}" 
                                 value="#{poServices.poHdr.poDate}"  
                                 tabindex="14"
                                 readonly="#{poServices.btnName=='Edit'}" 
                                 converterMessage="Invalid Date format"
                                 class="calendarClass" 
                                 id="calPODate">      
                    </p:calendar>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--                 CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad  -->
                    <p:outputLabel id="otptLblSoNum" value="SO Number" for="inptTxtSoNum"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                     CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO  19/05/21 by harshithad-->
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputText id="inptTxtSoNum" value="#{poServices.poHdr.poSoNumber}"   readonly="#{poServices.btnName=='Edit'}" 
                                 maxlength="40" tabindex="15">
                    </p:inputText> 
                </div>
                <!--                            Feature #5358  Relabel "SO Date" to "Transmit Date"-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel id="otptLblSoDate" value="#{custom.labels.get('IDS_SO_DATE')}" for="calPODate"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:calendar  id="calSoDate" pattern="#{globalParams.dateFormat}" 
                                 value="#{poServices.poHdr.poSoDate}"  
                                 tabindex="16" 
                                 readonly="#{poServices.btnName=='Edit'}" 
                                 converterMessage="Invalid Date format"
                                 class="calendarClass" 
                                 >      
                    </p:calendar>
                </div>

                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--Task #12010:Dashboard import feature for Booking/Order to generate POs, Bookings and Comm Trans (conditionally)  by harshithad on 10/03/23-->
                    <!--                 CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad  -->
                    <!--13944: 01/07/2024: CRM-8151    ORDERS: rename "Alternate Number" to "Fulfilled By"-->
                    <p:outputLabel  value="#{custom.labels.get('IDS_PO_ALT_NUM')}" />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputText value="#{poServices.poHdr.poAlternateNumber}"   readonly="#{poServices.btnName=='Edit'}" 
                                 maxlength="40" tabindex="17">
                    </p:inputText> 
                </div>
                <!--                            Feature #5358  Relabel "SO Date" to "Transmit Date"-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel  value="Follow Up Date" />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:calendar  pattern="#{globalParams.dateFormat}" 
                                 value="#{poServices.poHdr.poFollowUpDate}"  
                                 tabindex="18"
                                 readonly="#{poServices.btnName=='Edit'}" 
                                 converterMessage="Invalid Date format"
                                 class="calendarClass" 
                                 >      
                    </p:calendar>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">

                    <p:outputLabel value="Total Amount" for="txtMainTotalPrice" id="oLblPoamt"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                    //PMS:2443
                    //seem a- 20/11/2020-->
                    <!--Bug #2968:CRM-3714; PO entry: make mouse click enable commisison entry without backing out the zeros-->
                    <!--                       CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   17/05/21 by harshithad-->
                    <!--Feature #11596:CRM-7133   PO: need to edit price without line item by harshithad on 13/07/23-->
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputNumber value="#{poServices.poHdr.poTotalPrice}" tabindex="19"  inputStyle="text-align:  right"
                                   readonly="#{(poServices.btnName=='Edit' or poServices.poHdr.recId!=0 and poServices.excludePoDefaultLineItemCreation and poServices.lineItemLst.size()>0) or (poServices.poHdr.recId!=0 and !poServices.excludePoDefaultLineItemCreation)}" placeholder="0.00"
                                   decimalPlaces="2" maxValue="999999999" maxlength="14"
                                   id="txtMainTotalPrice" >
                        <!--Feature #8620 CRM-5990  PO: UAT: changing commission rate on line item not reflecting on header by harshithad--> 
                        <!--                        <p:ajax process="@this" event="blur" 
                                                        listener="#{poServices.poHdr.calculateProjectedCommission()}" 
                                                        update=":frmPo:tabDetails:txtMainProjectedComm frmPo:tabDetails:inptNumNet"
                                                        />-->
                    </p:inputNumber>

                </div>
                <!--//21-04-2022 : #7801  : CRM-5545: PO Flow: User Selectable commission percentage addition during addin po entry-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Status " for="oneMenuStatus" id="oLblPoStatus"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                     CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
                    <!--Feature #8913:CRM-6058  PO: UAT: various things #2, #4 (Feature) by harshithad on 06/09/22-->
<!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:selectOneMenu id="oneMenuStatus" value="#{poServices.poHdr.poCloseStatus}" tabindex="20"   valueChangeListener="#{poServices.setOldValue}" styleClass="#{poServices.btnName=='Edit'?'readonly-select-menu':''}" >
                        <!--Feature #8913CRM-6058  PO: UAT: various things #2, #4 (Feature)  by harshitha on 30/09/22-->
                        <!--Bug #11925: ESCALATIONS CRM-7276   PO: Internal Server Error by harshithad on 18/08/23-->
                         <!--13680:10/05/2024  CRM-8030  JSF  Sub Tables: Add Quotes and PO Statuses to the Subtables so users can add/edit Quote &-->
                        <f:selectItems value="#{poServices.poStatusList.entrySet()}" var="entry" itemLabel="#{entry.key}" itemValue="#{entry.value}"  itemDisabled="#{poServices.disableItem(entry.key)}" />                                                              
                        <!--Feature #8913:CRM-6058  PO: UAT: various things #2, #4 (Feature) by harshithad on 06/09/22-->                  
                        <p:ajax event="change" listener="#{poServices.onHdrStatusChange(1, poServices.poHdr.poCloseStatus)}"  />
                    </p:selectOneMenu>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                    <p:outputLabel value="#{custom.labels.get('IDS_COMMISSION')} Rate %" for="txtMainCommRate" id="oLblRate"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
                    <!--                    //PMS:2443
                                        //seem a- 20/11/2020-->
                    <!--Bug #2968:CRM-3714; PO entry: make mouse click enable commisison entry without backing out the zeros-->
                    <!--                       CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
                    <!--//21-04-2022 : #7801  : CRM-5545: PO Flow: User Selectable commission percentage addition during addin po entry-->
                    <!--Feature #8620 CRM-5990  PO: UAT: changing commission rate on line item not reflecting on header by harshithad--> 
                    <!--Feature #8620:CRM-5990  PO: UAT: changing commission rate on line item not reflecting on header  by harshithad on 25/08/22-->
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->      
                    <p:inputNumber value="#{poServices.poHdr.poCommRate}"   tabindex="21" inputStyle="text-align:  right"
                                   readonly="#{poServices.purchaseCustomApp and poServices.origSpectDestBol or poServices.btnName eq 'Edit'}"
                                   maxlength="7" decimalPlaces="2" maxValue="100" placeholder="0.00" 
                                   id="txtMainCommRate" > 
                        <!--                                                <p:ajax process="@this" event="blur"
                                                                                listener="#{poServices.poHdr.calculateProjectedCommission()}"
                                                                                update=":frmPo:tabDetails:txtMainProjectedComm frmPo:tabDetails:inptNumNet"
                                                                                />-->
                    </p:inputNumber>
                </div>

                <!--//21-04-2022 : #7801  : CRM-5545: PO Flow: User Selectable commission percentage addition during addin po entry-->
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <p:panelGrid id="purchasePanel" columns="4" layout="grid" styleClass="box-primary no-border ui-fluid"
                                 columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3" >
                        <p:outputPanel rendered="#{poServices.purchaseCustomApp and poServices.origSpectDestBol}">
                            <p:outputLabel value="Origin %"  />
                            <p:spacer width="4px" />
                            <p:selectBooleanCheckbox value="#{poServices.poCommOrigBol}" disabled="#{poServices.btnName=='Edit'}" >
                                <p:ajax event="change" listener="#{poServices.changeOrigSpecDest()}" 
                                        update=":frmPo:tabDetails:txtMainCommRate :frmPo:tabDetails:txtMainProjectedComm"/>
                            </p:selectBooleanCheckbox>
                        </p:outputPanel>
                        <p:outputPanel rendered="#{poServices.purchaseCustomApp and poServices.origSpectDestBol}">
                            <p:outputLabel value="Spec %"  />
                            <p:spacer width="4px" />
                            <p:selectBooleanCheckbox value="#{poServices.poCommSpecBol}" disabled="#{poServices.btnName=='Edit'}">
                                <p:ajax event="change" listener="#{poServices.changeOrigSpecDest()}" 
                                        update=":frmPo:tabDetails:txtMainCommRate :frmPo:tabDetails:txtMainProjectedComm"/>
                            </p:selectBooleanCheckbox>
                        </p:outputPanel>
                        <p:outputPanel rendered="#{poServices.purchaseCustomApp and poServices.origSpectDestBol}">
                            <p:outputLabel value="Dest %" />
                            <p:spacer width="4px" />
                            <p:selectBooleanCheckbox value="#{poServices.poCommDestBol}" disabled="#{poServices.btnName=='Edit'}">
                                <p:ajax event="change" listener="#{poServices.changeOrigSpecDest()}" 
                                        update=":frmPo:tabDetails:txtMainCommRate :frmPo:tabDetails:txtMainProjectedComm"/>
                            </p:selectBooleanCheckbox>
                        </p:outputPanel>
                        <p:outputPanel rendered="#{poServices.purchaseCustomApp and poServices.origSpectDestBol}">

                            <!--Feature #7942: CRM-5545: PO Flow: Line item: User Selectable commission percentage  by harshithad 11/05/2022-->
                            <p:commandButton  oncomplete="PF('savePurchaseDlg').show()" update=":frmPurchase" 
                                              styleClass="btn-primary btn-xs btn-icon-group btn-icon" title="Edit" icon="fa fa-pencil"
                                              disabled="#{poServices.btnName=='Edit'}"/>
                        </p:outputPanel>
                    </p:panelGrid>
                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <!--//09-06-2022 : 8181 : Direct Request: Purchase Orders: Hide Projected Commission at header and line item level-->
                  <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                    <p:outputLabel value="Projected #{custom.labels.get('IDS_COMMISSION')}" for="txtMainProjectedComm" id="oLblProjComm" rendered="#{poServices.accessCommissionBol}"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <!--                    //PMS:2443
                    //seem a- 20/11/2020-->
                    <!--                     CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
                    <!--//09-06-2022 : 8181 : Direct Request: Purchase Orders: Hide Projected Commission at header and line item level-->
                    <!--Feature #8620 CRM-5990  PO: UAT: changing commission rate on line item not reflecting on header by harshithad--> 
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputNumber value="#{poServices.poHdr.poCommProjected}" rendered="#{poServices.accessCommissionBol}"    tabindex="22" readonly="true"
                                   decimalPlaces="3" maxValue="999999999" maxlength="14" inputStyle="text-align:  right" placeholder="0.00"
                                   id="txtMainProjectedComm" >
                    </p:inputNumber>
                </div>
                <!--Feature #8620 CRM-5990  PO: UAT: changing commission rate on line item not reflecting on header by harshithad--> 
                <div class="ui-sm-12 ui-md-2 ui-lg-2">

                    <p:outputLabel value="Net %"  id="otptLblNet"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 

                    <p:inputNumber value="#{poServices.poHdr.poNetPerc}" readonly="true"
                                   decimalPlaces="3" maxValue="999999999" maxlength="14" placeholder="0.00"
                                   id="inptNumNet" >
                    </p:inputNumber>
                </div>
                <!--Task #12010:Dashboard import feature for Booking/Order to generate POs, Bookings and Comm Trans (conditionally)  by harshithad on 10/03/23-->
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Overage %"  />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                    <p:inputNumber id="inpNumOvrgPct" value="#{poServices.poHdr.poOveragePct}"     tabindex="23" readonly="#{poServices.btnName=='Edit'}"
                                   decimalPlaces="2" maxValue="999999999" maxlength="14" inputStyle="text-align:  right" placeholder="0.00">
<!--                        <p:ajax  process="@this" event="blur"  listener="#{poServices.calculateOverageAmount(poServices.poHdr.poOveragePct,poServices.poHdr.poTotalPrice)}" 
                                update="frmPo:tabDetails:inpNumOvrgAmt"/>-->
                    </p:inputNumber>
                </div>

                <div class="ui-sm-12 ui-md-2 ui-lg-2">

                    <p:outputLabel value="Overage Amount"  />
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4"> 

                    <p:inputNumber id="inpNumOvrgAmt" value="#{poServices.poHdr.poProjectedOverage}" readonly="true"
                                   decimalPlaces="2" maxValue="999999999" maxlength="14" placeholder="0.00" tabindex="24"
                                   >
<!--                        <p:ajax  process="@this" event="blur"  listener="#{poServices.calculateOveragePcet(poServices.poHdr.poProjectedOverage,poServices.poHdr.poTotalPrice)}" 
                                update="frmPo:tabDetails:inpNumOvrgPct"/>-->
                    </p:inputNumber>
                </div>
            </div>
        </div>
    </div>
    <!--Feature #9216 Direct Commission: Purchase Orders > Basic tab > Add Direct Comm. section by harshithad on 13/10/22-->
    <p:outputPanel rendered="#{directCommService.renderDirectComm}">
        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
          <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->     
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Direct #{custom.labels.get('IDS_COMMISSION')}</p:outputLabel>
            </div>
        </div> 

        <ui:include src="/config/directcomms/DirectCommSettings.xhtml" /> 
    </p:outputPanel>
    <div class="ui-g ui-fluid header-bar">
        <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
            <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Address</p:outputLabel>
        </div>
    </div> 
    <div class="ui-g ui-fluid">
        <div class="ui-sm-12 ui-md-2 ui-lg-2">
            <p:outputLabel value="Bill To" style="color: #0078D7" id="oLblPoBillto"/>
        </div>
        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
            <!--             //PMS:1137
                    //Seema - 13/07/2020-->
            <!--             CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
            <p:selectOneRadio id="sumdtl" value="#{poServices.poHdr.poBillToType}"  tabindex="25" 
                              disabled="#{poServices.btnName=='Edit'}" columns="2" style="width: 227px">
                <f:selectItem itemValue="0" itemLabel="Primary"  />
                <f:selectItem itemValue="1" itemLabel="Secondary"  />  
                <p:ajax event="change" process="@this" listener="#{poServices.autoPopulateBillToAdress(poServices.poHdr.poBillToType)}"/>
            </p:selectOneRadio>
        </div>
        <div class="ui-sm-12 ui-md-6 ui-lg-6 "></div>



        <div class="ui-sm-6 ui-md-6 ui-lg-6">
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="Address" id="oLblPoAdress"/>
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8">
                    <!--                     CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputTextarea  id="inptTxtAreBillToAdrs"  style="resize: none; margin-bottom: -2px;"  styleClass="tds1" 
                                      value="#{poServices.poHdr.poBilltoAdress1}"    tabindex="26"
                                      readonly="#{poServices.btnName=='Edit'}" 
                                      rows="2" autoResize="false"   maxlength="180"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="Zip code" for="txtbillzip" style="margin-top: -3px;" id="oLblPoZip"/>
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8" >
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputText id="txtbillzip" value="#{poServices.poHdr.poBilltoZip}"   readonly="#{poServices.btnName=='Edit'}"  style="margin-top: -3px;"
                                 tabindex="27"  maxlength="40">
                    </p:inputText>
                </div>
            </div>
        </div>
        <div class="ui-sm-6 ui-md-6 ui-lg-6">
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="City" id="oLblPoCity"
                                   />
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8" >
                    <!--                     CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23--> 
                    <p:inputText id="txtbilltoCity" value="#{poServices.poHdr.poBilltoCity}"    readonly="#{poServices.btnName=='Edit'}"  
                                 tabindex="28"  maxlength="40">
                    </p:inputText>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="State"  id="oLblPoState"/>
                </div>

                <div class="ui-sm-12 ui-md-8 ui-lg-8" >
                    <!--                     CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputText  id="txtbilltoState" value="#{poServices.poHdr.poBilltoState}"   readonly="#{poServices.btnName=='Edit'}" 
                                  tabindex="29"  maxlength="40">
                    </p:inputText>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="Country"  id="oLblPocountry"/>
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8" for="txtbillCountry">
                    <!--                     CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23--> 
                    <p:inputText id="txtbillCountry" value="#{poServices.poHdr.poBilltoCountry}"  readonly="#{poServices.btnName=='Edit'}" 
                                 tabindex="30"  maxlength="40">
                    </p:inputText>
                </div>
            </div>
        </div>
    </div>
    <p:separator style="margin-bottom:7px;margin-top: 7px"/>
    <div class="ui-g ui-fluid">
        <div class="ui-sm-12 ui-md-2 ui-lg-2">
            <p:outputLabel value="Ship To" style="color: #0078D7"  id="oLblPoShipto"/>
        </div>
        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
            <!--            //PMS:1137
                        //Seema - 13/07/2020-->
            <!--             CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
            <p:selectOneRadio id="sumdtl1" value="#{poServices.poHdr.poShipToType}"  disabled="#{poServices.btnName=='Edit'}" tabindex="31"
                              columns="2" style="width: 227px">
                <f:selectItem itemValue="0" itemLabel="Primary"  />
                <f:selectItem itemValue="1" itemLabel="Secondary"  />  
                <p:ajax event="change" process="@this" listener="#{poServices.autoPopulateShipToAdress(poServices.poHdr.poShipToType)}"/>
            </p:selectOneRadio>
        </div>
        <div class="ui-sm-12 ui-md-6 ui-lg-6 "></div>

        <div class="ui-sm-6 ui-md-6 ui-lg-6">
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="Address"  id="oLblPoShiptoAdres"/>
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8">
                    <!--                     CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputTextarea  id="inptTxtAreShpToAdrs" style="resize: none; margin-bottom: -2px;" styleClass="tds1" 
                                      rows="2" autoResize="false"  readonly="#{poServices.btnName=='Edit'}" 
                                      value="#{poServices.poHdr.poShiptoAdress1}"    tabindex="32"  maxlength="180"/>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="Zip code" for="txtshpzip" style="margin-top: -3px;"  id="oLblPoShiptoZip"/>
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8" >
                    <!--                     CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputText id="txtshpzip"  value="#{poServices.poHdr.poShiptoZip}" readonly="#{poServices.btnName=='Edit'}" style="margin-top: -3px;"
                                 tabindex="33"  maxlength="29">
                    </p:inputText>
                </div>
            </div>
        </div>
        <div class="ui-sm-6 ui-md-6 ui-lg-6">
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="City"  id="oLblPoShiptoACity"/>
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8" >
                    <!--                     CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputText id="shpCity"  value="#{poServices.poHdr.poShiptoCity}"   readonly="#{poServices.btnName=='Edit'}" 
                                 tabindex="34"  maxlength="40">
                    </p:inputText>
                </div>

                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="State"  id="oLblPoShiptoState"/>
                </div>

                <div class="ui-sm-12 ui-md-8 ui-lg-8" >
                    <!--                     CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputText  id="shpState"  value="#{poServices.poHdr.poshiptoState}" 
                                  readonly="#{poServices.btnName=='Edit'}" 
                                  tabindex="35"  maxlength="40">
                    </p:inputText>
                </div>
                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                    <p:outputLabel value="Country"  id="oLblPoShiptoCntry"/>
                </div>
                <div class="ui-sm-12 ui-md-8 ui-lg-8" for="txtshpCountry">
                    <!--                     CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
                    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
                    <p:inputText id="txtshpCountry"  value="#{poServices.poHdr.poShiptoCountry}" 
                                 readonly="#{poServices.btnName=='Edit'}" tabindex="36"  maxlength="40">
                    </p:inputText>
                </div>
            </div>
        </div>
    </div>
    <div class="ui-g ui-fluid header-bar">
        <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
            <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Shipping and Freight</p:outputLabel>
        </div>
    </div>  
    <div class="ui-g ui-fluid">

        <div class="ui-sm-12 ui-md-2 ui-lg-2">

            <p:outputLabel value="Freight Cost"  />
        </div>
        <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
            <!--Task #12010:Dashboard import feature for Booking/Order to generate POs, Bookings and Comm Trans (conditionally)  by harshithad on 10/03/23-->
            <p:inputNumber value="#{poServices.poHdr.poFreightAmt}" readonly="#{poServices.btnName=='Edit'}"
                           decimalPlaces="2" maxValue="999999999" maxlength="14" placeholder="0.00" tabindex="37"
                           >
            </p:inputNumber>
        </div>
        <div class="ui-sm-12 ui-md-2 ui-lg-2">
            <p:outputLabel value="Freight Details"  id="oLblPoFreighttoAdres"/>
        </div>
        <div class="ui-sm-12 ui-md-4 ui-lg-4">
            <!--            Task#3846-Purchase Orders > Text boxes editable but cannot save    15-03-2021   by harshithad-->
            <!-- CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
            <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
            <p:inputTextarea  value="#{poServices.poHdr.poFreightDetails}"  tabindex="38"
                              id="inpttxtareFrDtl"  readonly="#{poServices.btnName=='Edit'}" >
            </p:inputTextarea>
        </div>


        <div class="ui-sm-12 ui-md-2 ui-lg-2">
            <p:outputLabel value="Shipping Details"  id="oLblPoShiptoDetails"/>
        </div>

        <div class="ui-sm-12 ui-md-4 ui-lg-4">
            <!--            Task#3846-Purchase Orders > Text boxes editable but cannot save    15-03-2021   by harshithad-->
            <!-- CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
            <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
            <p:inputTextarea  value="#{poServices.poHdr.poShipDetails}" tabindex="39"
                              id="inpttxtareShpDtl"  readonly="#{poServices.btnName=='Edit'}" >
            </p:inputTextarea>
        </div>
        <div class="ui-sm-12 ui-md-2 ui-lg-2">
            <p:outputLabel value="Notes" id="oLblPoNote"/>
        </div>
        <div class="ui-sm-12 ui-md-4 ui-lg-4">
            <!--            Task#3846-Purchase Orders > Text boxes editable but cannot save    15-03-2021   by harshithad-->
            <!--            CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO   13/05/21 by harshithad-->
            <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
            <p:inputTextarea  value="#{poServices.poHdr.poNote}"  tabindex="40"
                              id="inpttxtareaNote"  readonly="#{poServices.btnName=='Edit'}" >
            </p:inputTextarea>
        </div>
    </div>

    <p:dialog id="dlgBillMsg" widgetVar="billMsgDlg" header="Billboard Message" width="600" height="150">
        <!--// 14-06-2022 : 8227 : PF 7 related bugs: PO-->
        <!--Bug #11661:ESCALATIONS CRM-7152   Purchase Orders: Billboard Message functionality by harshithad on 14/07/23-->
        <p:outputPanel id="otptPnlBlbrdMsg">
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                    <p:outputLabel value="Billboard Message" />
                </div>
                <div class="ui-sm-12 ui-md-10 ui-lg-10">
                    <p:inputTextarea value="#{poServices.billBoards.bbMessage}" id="txtBbmsg"/>
                </div>
            </div>
            <div class="button_bar" align="center">
                <!--                        //PMS:1997
                     //seema - 15/10/2020-->
                <p:commandButton id="btnMsgSave" value="Save" styleClass="btn btn-success  btn-xs" 
                                 actionListener="#{poServices.saveBillBoard()}"
                                 widgetVar="bbSave" 
                                 onclick="PF('bbSave').disable()" 
                                 oncomplete="PF('bbSave').enable();"
                                 />
                <p:spacer width="4px"/>
                <p:commandButton id="btnMsgCancel" value="Cancel" styleClass="btn btn-warning  btn-xs"  onclick="PF('billMsgDlg').hide()"/>
                <p:spacer width="4px"/>
                <p:commandButton id="btnMsgClear"  
                                 class="button_top btn-danger btn-xs" title="Clears the Data" value="Clear" process="@this"
                                 actionListener="#{poServices.clearBBoard()}"
                                 />

            </div>
        </p:outputPanel>
    </p:dialog>
    <!--Feature #8913:CRM-6058  PO: UAT: various things #2, #4 (Feature) by harshithad on 13/10/22-->
    <p:dialog id="dlgWait" widgetVar="dlgWait" closable="false" modal="true" header="Message" >
        <p:outputPanel >
            <br />
            <h:graphicImage  library="images" name="ajax-loader.gif"   />
            <p:spacer width="5" />
            <p:outputLabel value="Please wait.." />
            <br /><br />
        </p:outputPanel>
    </p:dialog>
    <!--Bug #12324  CRM-7448  orders: grayed out text too difficult to see when not in edit mode  by harshithad on 10/13/23-->
    <style>
        .readonly-select-menu {
            background-color: #f2f2f2; /* Change the background color to indicate readonly */
            pointer-events: none; /* Disable pointer events to prevent interaction */
        }
    </style>
</ui:composition>