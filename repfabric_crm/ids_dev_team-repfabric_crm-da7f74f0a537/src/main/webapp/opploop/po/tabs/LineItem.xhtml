<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    <!--    Bug #5926: Purchase Orders:  Line item list update issue on filter and add  bu harshithad 24/09/21-->
    <!--Bug #11916: CRM-7156   Repfabric: Instance latency, especially in POs by harshithad on 17/08/23-->
    <!--<p:remoteCommand name="loadLineItems" autoRun="false" immediate="true" actionListener="#{poServices.loadLineItems()}"   oncomplete="PF('dialogProcessDlg').hide()" />-->
    <div class="ui-g ui-fluid header-bar">
        <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
        <div class="ui-sm-12 ui-md-8 ui-lg-8 ">
            <p:outputLabel styleClass="acct-name-hdr" class="sub_title"> Line Item Details </p:outputLabel>
        </div>
        <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
            <!--pms:2982 11/01/2021 udaya b -->


            <!--        //PMS:2639
                    //Seema - 09/12/2020-->

            <!--            Task#3645:CRM-3946: PO partial shipment and over shipment   :02/03/2021 by harshithad-->           
            <!--Feature #8913:CRM-6058  PO: UAT: various things #2, #4 (Feature) by harshithad on 06/09/22-->
            <p:commandButton value="Ship Selected" 
                             title="Ship Selected"  
                             style="float: right;width: 100px;margin-left: 7px" 
                             class="btn btn-primary btn-xs"     
                             disabled="#{poServices.shipedFlag}"
                             actionListener="#{poServices.loadInitiaValues(0)}"
                             update="frmPoLIShip"
                             id="btnShipLI" />   
            <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
            <!--Feature #9051PRIORITY: CRM-6109: PO  Adjustment entries for Shipments and Commission by harshithad on 19/09/22-->
            <!--Feature #8913 CRM-6058  PO: UAT: various things #2, #4 (Feature)  by harshitha on 30/09/22-->
            <p:commandButton value="Add" title="Add Line Items"  disabled="#{poServices.disableAddLineItem}" 
                             style="float: right;width: 100px;" class="btn btn-primary btn-xs"
                             actionListener="#{poServices.initialPoDtlValue()}"
                             oncomplete="PF('dlgPoDetail').show();"  update=":poTransDlg frmPoDetails:inpNumProjectedComm"
                             id="btnAdd" /> 

        </div>
    </div> 

    <!-- pms:2919 pms:2019 14/01/2021 -->
    <!--    Task#4300:PO : Choose Line item to ship > 'Ship selected' button disabled  21/04/21-->
    <p:dataTable id="dtLineItems" widgetVar="lineItemDt" var="lineitem" 
                 filteredValue="#{poServices.filterLineItemLst}"
                 selection="#{poServices.selectedDtl}"
                 value="#{poServices.lineItemLst}"
                 tableStyle="table-layout:auto"
                 rowKey="#{lineitem.recId}" 
                 paginatorAlwaysVisible="false"
                 paginatorPosition="top"
                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                 multiViewState="true"
                 paginator="false"
                 resizableColumns="true"
                 draggableColumns="true"
                 rowSelectMode="add"
                 >
        <!--        //PMS:2639
            //Seema - 09/12/2020-->

        <!--Task #8639: CRM-5519  CRM-5991  PO Line items - where are the action buttons?  by harshithad on 05/08/22-->
        <p:column  selectionMode="multiple" style="text-align: center">
        </p:column>
        <p:column style="width:200px ">
            <!--pms:2982 11/01/2021 udaya b -->
            <!--Bug #8046:  purchase order ->shipped line item clone issue by harshithad 09/06/22-->
            <p:commandButton styleClass="btn-primary btn-xs btn-icon-group btn-icon" 
                             title="Edit" icon="fa fa-pencil"  id="btnEdit" process="@this"
                             actionListener="#{poServices.popualteLineItem(lineitem,0)}"
                             update=":poTransDlg"
                             oncomplete="PF('dlgPoDetail').show();" 
                             >
            </p:commandButton>
            <p:spacer width="4px"/>
            <p:commandButton styleClass="btn-danger btn-xs btn-icon-group btn-icon" 
                             id="btnLIDelete" 
                             title="Delete" icon="fa fa-trash" process="@this"
                             actionListener="#{poServices.deleteLI(lineitem)}">
            </p:commandButton>
            <p:spacer width="4px"/>
            <!--            //PMS:1503
                //Seema - 01/09/2020-->
            <p:commandButton title="Clone" icon="fa fa-copy"  
                             styleClass="btn-primary  btn-xs btn-icon"   
                             process="@this"
                             id="btnClone"
                             actionListener="#{poServices.cloneData(lineitem)}" />
            <p:spacer width="4px"/>
            <!--            //PMS:2639
                        //seema - 09/12/2020-->
            <!--Feature #9051:PRIORITY: CRM-6109/6135: PO  Adjustment entries for Shipments and Commission by harshithad-->
            <p:commandButton title="Split" icon="fa fa-cut"  
                             styleClass="btn-primary  btn-xs btn-icon"   
                             process="@this"
                             disabled="#{lineitem.poShippingStatus eq 1 or lineitem.disableQtySplit}"
                             id="btnLineItemSplit"
                             actionListener="#{poServices.loadSplitLineITem(lineitem)}"
                             />
        </p:column> 

        <!--        Task#3645:CRM-3946: PO partial shipment and over shipment   :02/03/2021 by harshithad-->
        <p:ajax event="toggleSelect" listener="#{poServices.onLIToggleSelect}" process="@this" update="frmPo:tabDetails:btnShipLI"/>
        <p:ajax event="rowSelectCheckbox" listener="#{poServices.onLISelect}" process="@this" update="frmPo:tabDetails:btnShipLI"/>
        <p:ajax event="rowUnselectCheckbox"  listener="#{poServices.onLIUnSelect}" process="@this" update="frmPo:tabDetails:btnShipLI"/>
        <!--        Task#4300:PO : Choose Line item to ship > 'Ship selected' button disabled  21/04/21  by harshithad-->
        <p:ajax event="rowSelect" listener="#{poServices.onLISelect}" process="@this" update="frmPo:tabDetails:btnShipLI"/>
        <p:ajax event="rowUnselect"  listener="#{poServices.onLIUnSelect}" process="@this" update="frmPo:tabDetails:btnShipLI"/>
        <p:column headerText="#{custom.labels.get('IDS_MFG_PART_NUM')}"  filterBy="#{lineitem.poPartNo}"  filterMatchMode="contains"  sortBy="#{lineitem.poPartNo}" id="clmnPoPartNo">
            <p:outputLabel value="#{lineitem.poPartNo}"    title="#{lineitem.poPartNo}"></p:outputLabel> 
        </p:column>
        <p:column headerText="Description"  filterBy="#{lineitem.poPartDesc}" filterMatchMode="contains"  sortBy="#{lineitem.poPartDesc}" id="clmnPoPartDesc">
            <p:outputLabel value="#{lineitem.poPartDesc.length()>27?lineitem.poPartDesc.substring(0,25).concat('..'):lineitem.poPartDesc}" 
                           title="#{lineitem.poPartDesc}"/>
        </p:column>
        <!--            //PMS:2639
                       //seema - 09/12/2020-->
        <p:column headerText="Odr.Qty." style="text-align: right;" id="oqtyDId"   sortBy="#{lineitem.poOrderQty}" >
            <p:outputLabel value="#{lineitem.poOrderQty}"    title="#{lineitem.poOrderQty}"></p:outputLabel> 
        </p:column>
        <p:column headerText="Pln.Qty." style="text-align: right;" id="pqtyDId"   sortBy="#{lineitem.poPlanQty}" >
            <p:outputLabel value="#{lineitem.poPlanQty}"    title="#{lineitem.poPlanQty}"></p:outputLabel> 
        </p:column>
        <p:column headerText="Shp.Qty." style="text-align: right;" id="sqtyDId"   sortBy="#{lineitem.poShippingQty}" >
            <p:outputLabel value="#{lineitem.poShippingQty}"    title="#{lineitem.poShippingQty}"></p:outputLabel> 
        </p:column>
        <p:column headerText="Pln.Date"  id="LIplndate"  
                  sortBy="#{poServices.getFormattedDate(lineitem.poPlanDate,'da')}">
            <p:outputLabel value="#{poServices.getFormattedDate(lineitem.poPlanDate,'da')}" 
                           title="#{poServices.getFormattedDate(lineitem.poPlanDate,'da')}">

            </p:outputLabel>
        </p:column>
        <p:column headerText="Shp.Date"  id="LIshpdate"  
                  sortBy="#{poServices.getFormattedDate(lineitem.poShippingDate,'da')}">
            <p:outputLabel value="#{poServices.getFormattedDate(lineitem.poShippingDate,'da')}" 
                           title="#{poServices.getFormattedDate(lineitem.poShippingDate,'da')}">

            </p:outputLabel>
        </p:column>
        <p:column headerText="Shp.Status"  id="LIshpStatus"  sortBy="#{lineitem.poShippingStatus}">
            <!--//Feature #2906: PO > Line Items > Link to Comm. trans-->
            <!--Bug #8046:  purchase order ->shipped line item clone issue by harshithad 09/06/22-->

            <p:outputLabel value="#{lineitem.poShippingStatus eq 1?'Shipped':'Not Shipped'} " 
                           title="#{lineitem.poShippingStatus eq 1?'Shipped':'Not Shipped'}" rendered="#{lineitem.poShippingStatus ne 1 or lineitem.poShippingStatus eq 1 and  lineitem.displayCommLink eq false}">

            </p:outputLabel>
            <!--//Feature #2906: PO > Line Items > Link to Comm. trans-->
            <!--Bug #8046:  purchase order ->shipped line item clone issue by harshithad 09/06/22-->

            <p:commandLink  rendered="#{lineitem.poShippingStatus eq 1 and lineitem.displayCommLink eq true}" 
                            value="#{lineitem.poShippingStatus eq 1?'Shipped':'Not Shipped'}"  
                            id="cmndLnkCommTrans"
                            style="text-decoration: underline;color: #73b2d9"
                            onclick="window.open('../../datamanagement/commtransactions/CommtransDetails.xhtml?id=#{poServices.getTrnhId(lineitem.trnsDtlId)}', '_blank');"  
                            title="#{lineitem.poShippingStatus eq 1?'Shipped':'Not Shipped'}"  ></p:commandLink>

        </p:column>
        <!--#9155 PRIORITY PO List > Add new column "Accounting Date"-->
        <p:column headerText="Acct.Date"  id="Acctdate"  
                  sortBy="#{poServices.getFormattedDate(lineitem.poAcctDate,'da')}">
            <p:outputLabel value="#{poServices.getFormattedDate(lineitem.poAcctDate,'da')}" 
                           title="#{poServices.getFormattedDate(lineitem.poAcctDate,'da')}">

            </p:outputLabel>
        </p:column>
        <!--Bug #3033:  PO > Line items: Unit/Std price custom label column remains unaffected-->
        <p:column headerText="#{custom.labels.get('IDS_STD_PRICE')}" style="text-align: right;" id="stdDId"  sortBy="#{lineitem.poStdRate}" >
            <p:outputLabel value="#{lineitem.poStdRate}"     title="#{lineitem.poStdRate}" > 
                <f:convertNumber  groupingUsed="true"  minFractionDigits="3" maxFractionDigits="6"/>
            </p:outputLabel>                         
        </p:column>
        <!--            //PMS:2639
                      //seema - 09/12/2020-->
        <p:column headerText="Mult." style="text-align:" id="mulDId" >      
            <p:outputLabel value="#{lineitem.poMultiplier}"    title="#{lineitem.poMultiplier}">
                <f:convertNumber  groupingUsed="true"  minFractionDigits="3" maxFractionDigits="2"/>
            </p:outputLabel> 
        </p:column> 

        <!--Bug #3033:  PO > Line items: Unit/Std price custom label column remains unaffected-->
        <p:column headerText="#{custom.labels.get('IDS_UNIT_PRICE')}" style="text-align: right;" id="unitDId"  sortBy="#{lineitem.poUnitPrice}" >
            <p:outputLabel value="#{lineitem.poUnitPrice}"   title="#{lineitem.poUnitPrice}"  >
                <f:convertNumber  groupingUsed="true"  minFractionDigits="3" maxFractionDigits="6"/>
            </p:outputLabel>  
        </p:column>

        <p:column headerText="Total.Pr." style="text-align: right;" id="extDId"  sortBy="#{lineitem.poTotalPrice}">
            <p:outputLabel value="#{lineitem.poTotalPrice}" title="#{lineitem.poTotalPrice}">
                <f:convertNumber  groupingUsed="true"  minFractionDigits="2" maxFractionDigits="2"/>
            </p:outputLabel>
        </p:column>
<!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
        <p:column headerText="#{custom.labels.get('IDS_COMM')} Rate" style="text-align: right;" id="commrateDId"  sortBy="#{lineitem.poCommRate}">
            <p:outputLabel value="#{lineitem.poCommRate}" title="#{lineitem.poCommRate}">
                <f:convertNumber  groupingUsed="true"  minFractionDigits="2" maxFractionDigits="2"/>
            </p:outputLabel>
        </p:column>
        <!--//09-06-2022 : 8181 : Direct Request: Purchase Orders: Hide Projected Commission at header and line item level-->
     <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23--> 
        <p:column headerText="Proj. #{custom.labels.get('IDS_COMM')}" rendered="#{poServices.accessCommissionBol}" style="text-align: right;" id="projcommId"  sortBy="#{lineitem.poCommProjected}">
            <p:outputLabel value="#{lineitem.poCommProjected}" title="#{lineitem.poCommProjected}">
                <f:convertNumber  groupingUsed="true"  minFractionDigits="2" maxFractionDigits="2"/>
            </p:outputLabel>
        </p:column>

    </p:dataTable>
    <!--Bug #11916: CRM-7156   Repfabric: Instance latency, especially in POs by harshithad on 17/08/23-->
    <p:dialog id="dialogProcess" widgetVar="dialogProcessDlg" closable="false" modal="true" header="Message" onShow="PF('dialogProcessDlg').initPosition();" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Loading please wait..." />
                        <br /><br />
                    </p:outputPanel>
                </h:form>
            </p:dialog>
</ui:composition>