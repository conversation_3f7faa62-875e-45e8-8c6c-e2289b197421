<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Dashboard
// Filename: ytdcomparison.xhtml
// Author: Priyadarshini.
//*********************************************************
/*
*
* Copyright (c) 2019 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}"
                xmlns:pe="http://primefaces.org/ui/extensions">



    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>    Backlog Report  </title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata>
            <f:viewParam name="p" value="#{poBacklogService.princiId}"/>
            <f:viewParam name="c" value="#{poBacklogService.custId}"/>
            <f:viewParam name="grp" value="#{poBacklogService.grpBy}"/>
            <f:viewParam name="f" value="#{poBacklogService.repFrom}"/>
            <f:viewParam name="t" value="#{poBacklogService.repTo}"/>
            <f:viewParam name="sf" value="#{poBacklogService.shipmentFlag}"/>
            <!--#12799 CRM-7637/CRM-7612  incidental findings po backlog report issues-->
            <f:viewParam name="fd" value="#{poBacklogService.startDate}"/>
            <f:viewParam name="td" value="#{poBacklogService.endDate}"/>
            <!--2644:Commission reports-->
            <f:viewParam name="rf" value="#{poBacklogService.repFlag}"/>
            <f:viewAction action="#{poBacklogService.defaultDatevalues()}" />
           <!--#12799 CRM-7637/CRM-7612  incidental findings po backlog report issues-->
            <f:viewAction action="#{reportFilters.assignSalesTeams()}" />  
            <!--#12728 CRM-7612  incidental finding Po backlog rpt private team/sales team visibility issue-->
            <f:viewAction action="#{poBacklogService.getSortByList(poBacklogService.repFlag)}"/>
     
            <!--<f:viewAction action="#{poBacklogService.getSalesTeamList()}" />--> 
            <f:viewAction action="#{poBacklogService.getPrinciName(poBacklogService.princiId)}" />
            <f:viewAction action="#{poBacklogService.getSelectedCustName(poBacklogService.custId)}" />



        </f:metadata>
    </ui:define>
    <ui:define  name="body">
        <!--2644:Commission reports-->
        <f:event listener="#{poBacklogService.isPageAccessible}" type="preRenderView"/>  

        <p:dialog header="Please wait" draggable="false"  widgetVar="dlgSalComCompaj" modal="true" closable="false" resizable="false" >
            <h:graphicImage  library="images" name="ajax-loader.gif" />
            <p:spacer width="5"/>
            <h:outputLabel value="Loading data.."/>
        </p:dialog>


        <div class="box box-info box-body" style="vertical-align: top;pointer-events:none;opacity:0.4"  id="commCompRpt">



            <h:graphicImage id="loading-image"  library="images" name="loading_icon.gif" alt="Loading..."  style="position: fixed;
                            left: 600px;
                            top: 300px;
                            width: 150px;
                            height: 150px;
                            z-index: 9999; opacity: .5 !important; " />   



            <h:form id="bckDetForm">
                <!-- //Bug #6787 commission reports ->date blank issue-->
                <p:growl id="dateMsg"  />
                <p:remoteCommand name="applyPrinci" actionListener="#{poBacklogService.applyPrincipal(viewCompLookupService.selectedCompany)}" update=":bckDetForm:inputPrincName"  />
                <p:remoteCommand name="applyCustomer" actionListener="#{poBacklogService.applyCustSelect(viewCompLookupService.selectedCompany)}"  update=":bckDetForm:inputCustName" />





                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >

                        <!--Bug #3059:PO Backlog Report Export - Fix Date issue-->
                        <div class="compNamebg" style="float:left;width:410px">

                            <h:outputLabel value="#{loginBean.subscriber_name}"  style="float:left;width:350px" />
                            <!--Feature #3744:CRM-4016: PObacklog report: improve readability and comm subtotal-->
                            <br/>
                            <h:outputLabel value="Backlog Report"  style="float:left;width:150px" />


                        </div>
                        <!--Feature #3744:CRM-4016: PObacklog report: improve readability and comm subtotal commented -->
                        <!--Feature #3744:CRM-4016: PObacklog report: improve readability and comm subtotal-->    
                        <!--Bug #3059:PO Backlog Report Export - Fix Date issue-->
                        <!--                        <div class="compNamebg" style="float:left;width:380px;padding-top:-50px">
                         <h:outputLabel value="Backlog Report"  style="float:left;width:150px" />
                                                 </div>-->
                    </div>
                    <div class="ui-sm-12 ui-md-2 ui-lg-2" >

                    </div>  
                    <div class="ui-sm-12 ui-md-3 ui-lg-3" >

                        <p:panelGrid id="grid2" columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                     style="min-height: 50px;width:100%;margin-left: 100px"       layout="grid" styleClass="box-primary no-border ui-fluid "  >


                            <p:outputLabel value="From :" class="fmt-b" />

                            <p:calendar id="fromDate"    pattern="#{globalParams.dateFormat}" value="#{reportFilters.fdate}"  autocomplete="off">
                                <p:ajax  event="keyup"  process="@this" />
                                <p:ajax event="dateSelect" process="@this"  />


                            </p:calendar>

                            <!--#4775:CRM-4398: Sales and Commission Reports: Remember date range for session-->

                            <p:outputLabel value="To:" class="required" />

                            <p:calendar  id="toDate"  pattern="#{globalParams.dateFormat}"  value="#{reportFilters.tdate}"   autocomplete="off"   >
                                <p:ajax  event="keyup"  process="@this" />
                                <p:ajax event="dateSelect" process="@this"  />
                            </p:calendar>



                        </p:panelGrid>
                        <br/>
                        <p:panelGrid id="grid7" columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                     style="min-height: 50px;width:100%;margin-left: 100px"       layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel value="Group by"/>


                            <p:selectOneMenu value="#{poBacklogService.grpBy}"  id="grp"  style="width:225px">
                                <!--//2643-->
                                <!--5011: CRM-4397: cleaning up End User terminology in Commission reports-->
                                <f:selectItem itemLabel="#{custom.labels.get('IDS_DB_CUSTOMER')} Sort by #{custom.labels.get('IDS_PRINCI')}" itemValue="2" />
                                <f:selectItem itemLabel="#{custom.labels.get('IDS_PRINCI')} Sort by #{custom.labels.get('IDS_DB_CUSTOMER')}" itemValue="1" />

                            </p:selectOneMenu> 
                        </p:panelGrid>
                    </div>
                    <div class="ui-sm-12 ui-md-4 ui-lg-4" style="margin-left:100px">
                        <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                     style="float: right;width: 350px;"     layout="grid" styleClass="box-primary no-border ui-fluid "  >

                            <p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')}" />
                            <h:panelGroup class="ui-inputgroup"  style="width: 112%">
                                <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width: 200%;" id="selSalesTeam">
                                    <f:selectItems value="#{lookupService.salesTeams}" var="team"  
                                                   itemValue="#{team.smanId}"
                                                   itemLabel="#{team.smanName}"  />
                                </h:selectOneListbox>
                                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_SALES_TEAM')}" actionListener="#{lookupService.list('SALES_TEAM')}" update=":frmSalesTeamLookup" oncomplete="PF('dlgSalesTeamsLookup').show();" />
                                <p:commandLink styleClass="sel-salesteam" value="Clear" action="#{lookupService.clear('SALES_TEAM')}"   actionListener="#{poBacklogService.clearSalesTeam()}"      update="@(.sel-salesteam)" disabled="#{!(lookupService.salesTeams!=null and lookupService.salesTeams.size() != 0)}" />
                            </h:panelGroup>



                            <p:outputLabel  value="#{custom.labels.get('IDS_PRINCI')}"></p:outputLabel>
                            <h:panelGroup class="ui-inputgroup" id="princigrp" >
                                <p:inputText id="inputPrincName" value="#{poBacklogService.princiName}"  style="width: 200%" readonly="true"
                                             />
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listAll('applyPrinci', 1, 0, 0)}" 
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>


                            <!--5011: CRM-4397: cleaning up End User terminology in Commission reports-->
                            <p:outputLabel for="inputCustName" value="#{custom.labels.get('IDS_DB_CUSTOMER')}"  />
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="inputCustName" value="#{poBacklogService.custName}" widgetVar="cust"  style="width: 200%" readonly="true"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  actionListener="#{viewCompLookupService.listActive('applyCustomer', 2, 0, 0)}"
                                                  update=":formCompLookup" oncomplete="PF('lookupComp').show();"  

                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                            <!-- //Bug #6787 commission reports ->date blank issue-->
                             <!--#12728 CRM-7612  incidental finding Po backlog rpt private team/sales team visibility issue-->
                            <p:commandButton id="btnViewRport" value="View Report"  onstart="processGif();"  oncomplete="hide();"  action="#{poBacklogService.getSortByList(poBacklogService.repFlag)}"  update=":bckDetForm:tblPoServicedata  :bckDetForm:dateMsg"   styleClass="btn btn-primary btn-xs" style="width:30px"   /> 
                             <!--#12728 CRM-7612  incidental finding Po backlog rpt private team/sales team visibility issue-->
                            <p:commandButton id="btnExport" value="Export"   style="width:30px"  ajax="false"  actionListener="#{poBacklogService.exportData(poBacklogService.repFlag)}"  styleClass="btn btn-primary btn-xs" onclick="PrimeFaces.monitorDownload(start, stop);" oncomplete="PF('inBacklogProgressDlg').hide();"
                                             />

                        </p:panelGrid>


                    </div>
                </div>  
                <div class="ui-g ui-fluid" id="dt" style="overflow-x:scroll;transform:rotateX(180deg);">

                    <div class="ui-g ui-fluid" id="dt1" style="transform:rotateX(180deg);">

                        <h:panelGroup id="tblPoServicedata" >
                            <p:dataTable id="dtrpt1" emptyMessage=""  >
                                <!--#13325 CRM-7720   PO Backlog Export/View: Include Batch Number-->
                                <p:column  class="col_left"  headerText="Batch Number" width="150" >

                                </p:column>
                                
                                <p:column  class="col_left"  width="150"  >  
                                    <f:facet name="header">
                                        <h:outputText value="Order number"/>
                                    </f:facet>

                                </p:column>
                                <p:column  class="col_left"  headerText="Date" width="150" >

                                </p:column>

                                <p:column class="col_left"  headerText="Part Number" width="150" >

                                </p:column>
                                <p:column  class="col_left"   headerText="Product Description" width="150">

                                </p:column>
                                <p:column class="col_right"  headerText="Unit Price" width="150">

                                </p:column>
                                <p:column class="col_right"  headerText="Line Total" width="150">

                                </p:column>
                                <p:column  class="col_right" width="150"  headerText="Shipped Quantity" >
                                </p:column>
                                <p:column  class="col_right" width="150" headerText="Planned Quantity" >
                                </p:column>
                                <p:column  class="col_right" width="150" headerText="Shipped Total" >
                                </p:column>
                                <p:column  class="col_right" width="150" headerText="Unshipped Total"  >
                                </p:column>

                                <p:column class="col_left" width="150"  headerText="Planned Date">


                                </p:column>
                                <p:column class="col_left" width="150" headerText="Shipped Date"  >

                                </p:column>
                                <p:column class="col_left" width="150" headerText="Status">

                                </p:column>
                                <!--Feature #3744:CRM-4016: PObacklog report: improve readability and comm subtotal-->
                                <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                <p:column class="col_right" width="150" headerText="Proj. #{custom.labels.get('IDS_COMM')}"  rendered="#{poBacklogService.repFlag ==2}">

                                </p:column>


                                <p:column class="col_left" width="150" headerText="PO Creator" >

                                </p:column>




                                <p:column class="col_left" width="150" headerText="Creation Date" >


                                </p:column>
                                <p:column class="col_left" width="150"  headerText="Modified User" >

                                </p:column>


                                <p:column class="col_left" width="150" headerText="Modified Date" >


                                </p:column>
                            </p:dataTable>
                            <div style="width:100%;white-space:nowrap;overflow-y:scroll;direction: rtl;" id="tbldata"> 
                                <div style="direction: ltr;" id="tbldata1"> 
                                    <ui:repeat  value="#{poBacklogService.listBackLogRep}" var="backlist"  id="sid">
                                        <div class="div-selected" style="height:25px;width: 100%;" >
                                            <label style="font-size: 1.3em;font-weight: bold;color:white;line-height: 25px;padding-left: 5px;">
                                                <!--Feature #3744:CRM-4016: PObacklog report: improve readability and comm subtotal-->
                                                <p:outputLabel  value="#{backlist.sortName}"/>
                                            </label>
                                        </div>
                                        <ui:repeat  value="#{backlist.grpList}" var="grp"  id="sid">
                                            <div  style="height:28px;width: 100%;background-color:#c4d4e0;vertical-align:central" >
                                                <label style="font-size: 1.3em;font-weight: bold;color:black;line-height: 25px;padding-left: 5px;">
                                                    <p:outputLabel  value="For: "/>&nbsp;&nbsp;<p:outputLabel  value="#{grp.itemName}"/>
                                                </label>
                                            </div>
                                            <p:dataList  type="definition" id="dtrpt1" value="#{grp.orderList}" var="orderlst" >

                                                <!--Feature #3744:CRM-4016: PObacklog report: improve readability and comm subtotal-->
                                                <!--Feature #3092:PO Backlog Report >  Link to PO-->
                                                <p:column >
                                                    <!--                                    <div class="div-selected" style="height:25px;width: 100%;" >
                                                                                            <label style="font-size: 1.3em;font-weight: bold;color: white;line-height: 25px;padding-left: 5px;">
                                                                                                <h:link                  target="_blank" 
                                                                                                                         onclick="assignWinName('/opploop/po/PoDetails.xhtml?id=#{orderlst.poHdrId}', 'organizer', event)"     
                                                                                                                         style="text-decoration:underline;color: white;"> 
                                                    
                                                                                                    Order Number: #{orderlst.poOrder}   
                                                                                                </h:link>
                                                                                            </label>               
                                                                                        </div>-->

                                                    <p:dataTable id="dtrpt"  value="#{orderlst.orderDetList}" var="orderDet" paginatorAlwaysVisible="false" paginator="true"
                                                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink}
                                                                 {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                                                 emptyMessage="No data found" >
                                                       <!--#13325 CRM-7720   PO Backlog Export/View: Include Batch Number-->
                                                        <p:column  class="col_left" width="150" >
                                                            <p:outputLabel value="#{orderDet.poBatchNumber}"    style="text-align:center" >  

                                                            </p:outputLabel>
                                                        </p:column>
                                                        <p:column  class="col_left" width="150" >
                                                            <p:outputLabel value="#{orderDet.poOrder}"    style="text-align:center" >  

                                                            </p:outputLabel>
                                                        </p:column>
                                                        <p:column  class="col_left"   width="150" >
                                                            <p:outputLabel value="#{orderDet.poDate}"    style="text-align:center" >  
                                                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                                            </p:outputLabel>
                                                        </p:column>

                                                        <p:column class="col_left"  width="150" >
                                                            <p:outputLabel value="#{orderDet.poPartNum}"  style="text-align:center" />
                                                        </p:column>
                                                        <p:column  class="col_left"    width="150">
                                                            <p:outputLabel value="#{orderDet.poPartDesc}"  style="text-align:center"  />
                                                        </p:column>
                                                        <p:column class="col_right"    width="150">
                                                            <p:outputLabel value="#{poBacklogService.convertBigDecimalNumber(orderDet.poUnitPrice)}"  style="text-align:center"  >
                                                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>

                                                            </p:outputLabel>
                                                        </p:column>
                                                        <p:column class="col_right"   width="150">
                                                            <p:outputLabel value="#{poBacklogService.convertBigDecimalNumber(orderDet.poLineTotal)}"  style="text-align:center" >
                                                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                                                            </p:outputLabel>
                                                        </p:column>
                                                        <p:column  class="col_right"    width="150">
                                                            <p:outputLabel value="#{poBacklogService.convertBigDecimalNumber(orderDet.poShippingQty)}"  style="text-align:center"  />
                                                        </p:column>
                                                        <p:column  class="col_right"   width="150">
                                                            <p:outputLabel value="#{poBacklogService.convertBigDecimalNumber(orderDet.poPlanQty)}"  style="text-align:center"  />
                                                        </p:column>
                                                        <p:column  class="col_right" width="150"   >
                                                            <p:outputLabel value="#{poBacklogService.convertBigDecimalNumber(orderDet.shippedTotal)}"  style="text-align:center"  />
                                                        </p:column>
                                                        <p:column  class="col_right" width="150"   >
                                                            <p:outputLabel value="#{poBacklogService.convertBigDecimalNumber(orderDet.unShippedTotal)}"  style="text-align:center"  />
                                                        </p:column>

                                                        <!--Feature #3744:CRM-4016: PObacklog report: improve readability and comm subtotal-->

                                                        <p:column class="col_left" width="150" >
                                                            <p:outputLabel value="#{orderDet.poPlannedDate}"  style="text-align:center"  >
                                                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                                            </p:outputLabel>

                                                        </p:column>
                                                        <p:column class="col_left" width="150"  >
                                                            <p:outputLabel value="#{orderDet.poShippingDate}"  style="text-align:center"  >
                                                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                                            </p:outputLabel>
                                                        </p:column>
                                                        <p:column class="col_left" width="150" >
                                                            <p:outputLabel value="#{poBacklogService.setPoStatus(orderDet.poPlannedDate,orderDet.poShippingQty,orderDet.poPlanQty)}"  style="text-align:center"  />
                                                        </p:column>
                                                        <!--Feature #3744:CRM-4016: PObacklog report: improve readability and comm subtotal-->
                                                        <p:column class="col_right" width="150"  rendered="#{poBacklogService.repFlag ==2}">
                                                            <p:outputLabel value="#{poBacklogService.convertBigDecimalNumber(orderDet.poCommProjected)}"  style="text-align:center" >
                                                                <f:convertNumber groupingUsed="true" minFractionDigits="0"   maxFractionDigits="0"/>
                                                            </p:outputLabel>
                                                        </p:column>

                                                        <!--                                                                                //Bug #2991:  CRM-3749: PO backlog report blank and needs created user added to report and screen view
                                                                                                //            PO creator, creation date and modified user, modified date.-->

                                                        <p:column class="col_left" width="150" >
                                                            <p:outputLabel value="#{orderDet.insUsr}"  style="text-align:center" />
                                                        </p:column>




                                                        <p:column class="col_left" width="150"  >
                                                            <p:outputLabel value="#{orderDet.insDate}"  style="text-align:center"  >
                                                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                                            </p:outputLabel>

                                                        </p:column>
                                                        <p:column class="col_left" width="150"   >
                                                            <p:outputLabel value="#{orderDet.upUser}"  style="text-align:center" />
                                                        </p:column>


                                                        <p:column class="col_left" width="150"  >
                                                            <p:outputLabel value="#{orderDet.updDate}"  style="text-align:center"  >
                                                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                                            </p:outputLabel>

                                                        </p:column>


                                                        <p:columnGroup type="footer" >

                                                            <p:row>
                                                                <!--13325 CRM-7720   PO Backlog Export/View: Include Batch Number-->
                                                                <!--Feature #3744:CRM-4016: PObacklog report: improve readability and comm subtotal-->
                                                                <p:column colspan="5" style="text-align:right;font-weight:bold" footerText="PO Total: " />
                                                                <p:column colspan="2" style="text-align:right;font-weight:bold" footerText="$#{poBacklogService.convertNumber(poBacklogService.calculateTotal(orderlst.totals[3],orderlst.totals[2]))}" />
                                                                <p:column  colspan="3" footerText="$#{poBacklogService.convertNumber(orderlst.totals[3])}"  style="text-align: right;font-weight:bold"  class="col_right"  />
                                                                <p:column    footerText="$#{poBacklogService.convertNumber(orderlst.totals[2])}"   style="text-align: right;font-weight:bold"  class="col_right" />
                                                                <p:column  rendered="#{poBacklogService.repFlag ==2}"/>

                                                                <!--//Bug #2991:  CRM-3749: PO backlog report blank and needs created user added to report and screen view-->
                                                                <!--//            PO creator, creation date and modified user, modified date.-->

                                                                <p:column />
                                                                <p:column />
                                                                <!--#3744-->
                                                                <p:column footerText="$#{poBacklogService.convertNumber(orderlst.totals[4])}"  rendered="#{poBacklogService.repFlag ==2}"  style="text-align: right;font-weight:bold"  class="col_right" />
                                                                <p:column />
                                                                <p:column />
                                                                <p:column />
                                                                <!--#3744 po backlog format-->
                                                                <p:column />
                                                                <p:column  rendered="#{poBacklogService.repFlag ==1}"/>
                                                            </p:row>



                                                        </p:columnGroup>

                                                    </p:dataTable>  

                                                </p:column>

                                            </p:dataList>


                                            <!--Feature #3744:CRM-4016: PObacklog report: improve readability and comm subtotal-->
                                            <div  style="height:28px;width: 100%;background-color:#b4c3d0;vertical-align:central" >
                                                <label style="color:black;line-height: 25px;padding-left: 5px;width:100%">

                                                    <p:outputLabel  value="#{grp.itemName}   Total : $#{poBacklogService.convertBigDecimalNumber(poBacklogService.calculateTotal(poBacklogService.getSumByGrpNmShippedTotal(grp.itemId,backlist.sortId),poBacklogService.getSumByGrpNmUnshippedTotal(grp.itemId,backlist.sortId)))}"  style="width:35%"/>

                                                    <p:outputLabel  value="Shipped Total : $#{poBacklogService.convertBigDecimalNumber(poBacklogService.getSumByGrpNmShippedTotal(grp.itemId,backlist.sortId))}"  style="width:35%" />


                                                    <p:outputLabel  value="Unshipped Total : $#{poBacklogService.convertBigDecimalNumber(poBacklogService.getSumByGrpNmUnshippedTotal(grp.itemId,backlist.sortId))}"  style="width:30%"    />

                                                </label>
                                            </div>




                                        </ui:repeat>
                                        <!--Feature #3744:CRM-4016: PObacklog report: improve readability and comm subtotal-->
                                        <div  style="height:28px;width: 100%;background-color:#f6f6f6;vertical-align:central" >
                                            <label style="color:black;line-height: 25px;padding-left: 5px;width:100%">


                                                <p:outputLabel  value="#{backlist.sortName}   Total : $#{poBacklogService.convertBigDecimalNumber(poBacklogService.calculateTotal(poBacklogService.getSortTotalsByIdTotal(backlist.sortId),poBacklogService.getSortTotalsByIdUnshiped(backlist.sortId))) }" style="font-weight:bold;width:35%" />

                                                <p:outputLabel  value="Shipped Total : $#{poBacklogService.convertBigDecimalNumber(poBacklogService.getSortTotalsByIdTotal(backlist.sortId))}" style="font-weight:bold;width: 35%" />


                                                <p:outputLabel  value="Unshipped Total : $#{poBacklogService.convertBigDecimalNumber(poBacklogService.getSortTotalsByIdUnshiped(backlist.sortId))}"   style="font-weight:bold;width:30%" />


                                            </label>
                                        </div>
                                    </ui:repeat>
                                </div>
                                <!--Feature #3744:CRM-4016: PObacklog report: improve readability and comm subtotal-->
                                <div  style="height:28px;width: 100%;background-color:#5d92bf;vertical-align:central" >
                                    <label style="color:white;line-height: 25px;padding-left: 5px;width:100%">


                                        <p:outputLabel  value="Report   Total : $#{poBacklogService.convertBigDecimalNumber(poBacklogService.calculateTotal(poBacklogService.getReportTotalShipped(), poBacklogService.getReportTotalUnshipped()))}" style="font-weight:bold;width:35%" />


                                        <p:outputLabel  value="Shipped Total : $#{poBacklogService.convertBigDecimalNumber(poBacklogService.getReportTotalShipped())}" style="font-weight:bold;width:35%" />



                                        <p:outputLabel  value="Unshipped Total : $#{poBacklogService.convertBigDecimalNumber(poBacklogService.getReportTotalUnshipped())}"  style="font-weight:bold;width:30%" />

                                    </label>
                                </div>
                            </div>

                        </h:panelGroup>
                    </div>
                </div>



                <style>

                    html,body{
                        min-height:100%;
                        overflow-y: auto;
                    }

                    .ui-datatable{
                        overflow: auto;
                    }
                    .ui-panelgrid .ui-panelgrid-cell {
                        padding-top: 1px !important;
                        padding-bottom: 1px !important;
                    }

                    [role="gridcell"]{

                        padding: 0px 10px !important;
                    }
                    .content-header{     display: none; }


                    .jqplot-target {

                        font-size: 0.75em!important;
                    }

                    /*            .ui-datatable-data .ui-widget-content
                                {
                                    display: none;
                                }*/

                    body .ui-datatable th.ui-state-default[role="columnheader"], body .ui-treetable th.ui-state-default[role="columnheader"] {
                        padding-top: 0px!important;

                        padding-bottom: 0px!important;

                        /*#d2dadc*/
                    }

                    .ui-datatable-tablewrapper {
                        /*overflow: hidden;*/
                    }


                    /*                    body .ui-datatable tbody>tr.ui-widget-content, body .ui-treetable tbody>tr.ui-widget-content{
                                          display: none;  
                                        }*/

                    /*pms :3808*/
                    #bckDetForm\:dtrpt1_data
                    {
                        display: none;
                    }


                    /*1920 * 1080*/
                    /*pms :3808*/
                    @media only screen and (min-height : 901px) and (max-height : 1152px)  {

                        #tbldata{
                            height: 800px; 

                        }

                        #dt1{
                            height: 73vh;
                        }

                    }
                    .main-footer{
                        display: none;
                    }

                    @media only screen and (min-height : 600px) and (max-height : 900px)  {

                        #tbldata{
                            height: 540px; 

                        }

                        #dt1{
                            height: 71vh;
                        }

                    }     

                    /*
                    
                    div.tbldata
                    {
                        overflow: auto;
                    }*/
                    /*                        #755 :CRM-2658: Backlog report issues - #2, #3 datatable hieght removed
                    
                                            #bckDetForm\:backogrecordDT ,body .ui-datatable .ui-datatable-scrollable-header{
                                                margin-left: -1000px;
                                                                    } 
                                            #bckDetForm\:backogrecordDT ,body .ui-paginator .ui-paginator-bottom
                                            {
                                                margin-left: -1000px;
                                                                    } */

                </style>

                <!--#3249: Task Reporting > Commission Reports > Add loading gif START -->
                <script>

                    window.onload = function () {
                        //                $('#loading-image').show();
                        setTimeout(function () {
                            var t = performance.timing;
                            console.log(t.loadEventEnd - t.responseEnd);
                            //      alert(""+url("#{request.contextPath}/resources/images/progressicon.gif"));
                            document.getElementById("commCompRpt").style.pointerEvents = "all";
                            document.getElementById("commCompRpt").style.opacity = "1";

                            if (screen.width === 1024)
                            {
                                document.getElementById("commCompRpt").style.width = "330%";
                            }
                            console.log("" + document.getElementById("commCompRpt").style.width);
                            console.log("::::" + screen.width);
                            $('#loading-image').hide();
                            //            $('#prodrpt').css({pointer - events:"all"});
                        }, 0);
                        /*#755 :CRM-2658: Backlog report issues - #2, #3 datatable hieght removed*/
                        myFunction();
                    }

                    /*#755 :CRM-2658: Backlog report issues - #2, #3 datatable hieght removed*/
                    function myFunction() {
                        var x = "Total Height: " + screen.height + "px";
                        console.log("height :::" + (parseInt(screen.height)));
                        //                        document.getElementById("demo").innerHTML = x;
                //        document.getElementById("repDiv").style.height = "" + (parseInt(screen.height) - 175) + "px";


                    }


                    function hide()
                    {

                        $('#loading-image').hide();
                        document.getElementById("commCompRpt").style.pointerEvents = "all";
                        document.getElementById("commCompRpt").style.opacity = "1";
                    }

                    function processGif()
                    {
                        $('#loading-image').show();
                        document.getElementById("commCompRpt").style.pointerEvents = "none";
                        document.getElementById("commCompRpt").style.opacity = "0.5";
                    }

//          
                </script>

                <!--#630  Commissionable Transactions > Backlog Report-->


            </h:form>

            <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>
            <ui:include src="/lookup/CompanyLookupDlg.xhtml"/>

        </div>
        <!--</div>-->
    </ui:define>

</ui:composition>