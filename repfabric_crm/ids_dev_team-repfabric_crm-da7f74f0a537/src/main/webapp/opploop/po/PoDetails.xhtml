<ui:composition  xmlns="http://www.w3.org/1999/xhtml" 
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:f="http://java.sun.com/jsf/core" 
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:p="http://primefaces.org/ui" 
                 xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                 template="#{layoutMB.template}">

    <h:head>
    </h:head>
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>
            <!--//Feature #3030:CRM-3746: cronan: make purchase order" menu relabelable to "Sales Order"-->
            #{custom.labels.get('IDS_PURCHASE_ORDER')}
        </title>
    </ui:define>
    <ui:define name="metadata">  
        <f:metadata>
            <f:viewParam name="id" value="#{poServices.poHdr.poId}"/> 
            <f:viewAction action="#{poServices.populatePo(poServices.poHdr.poId)}"/>
            <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
            <f:viewAction action="#{poServices.populateTimeline(poServices.poHdr.poId)}"/>
            <!--#2880:CRM-3700: Purchase Order Screen tutorial button not tied to learning.repfabric.com page-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('PO_DTL'))}" />
            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('PO_DTL')}" />
        </f:metadata>
    </ui:define>
    <ui:define name="title">
        <div class="row">
            <div class="col-md-6"> 
                <!--//Feature #3030:CRM-3746: cronan: make purchase order" menu relabelable to "Sales Order"-->
                #{custom.labels.get('IDS_PURCHASE_ORDER')}
                <!--                Purchase Order-->
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>
    <ui:define name="menu">
    </ui:define>

    <ui:define name="body">
        <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
        <style>
            .noBorders *{
                border: none !important;
            }

            .flexbox-container {
                display: flex;
                flex-direction: row;
            }
            .ui-fileupload-content {


                padding: .2em .4em;
                border-top-width: 0;
                border: 0px;
            }
            .ui-fileupload .ui-fileupload-buttonbar span.ui-icon-plusthick {
                color: #fff;
                text-indent: 0;
                top: 10px;
                height: 24px;
                display: none;             
                visibility: hidden;
            }

            .ui-fileupload-buttonbar .ui-fileupload-choose .ui-button-text{
                padding-left:1em !important;
                font-size:12px;                
            }
            /*Bug #9079:ESCALATIONS: CRM-6153  PO Module drag and drop no longer available by harshithad*/
            #frmAtchDlg\:flUpldTimelne>.ui-fileupload-content.ui-widget-content.ui-corner-bottom{
                display:none;
            }
            .ui-fileupload-content:before{
                content: "";
                width: 100%;
                color:#0000FF;


            } 
            /* The actual timeline (the vertical ruler) */
            .timeline {
                position: relative;
                max-width: 1200px;
                margin: 0 auto;
                     /*Bug #13933: CRM-8126  Orders: TIMELINE Allow "edit" of tim*/
                max-height: 350px;  
            }

            /* The actual timeline (the vertical ruler) */
            .timeline::after {
                content: '';
                position: absolute;
                width: 1px;
                /*// 25-05-2022 : #8049 : Purchase Order: Time line > Firefox UI issue*/
                min-height: 85%;
                height:-webkit-fill-available; 
                background-color: black;
                top: 20px;
                bottom: 40px;
                margin-bottom: 40px;
                margin-left: 19px;
            }
            .timeline:before {
                background-color: white;
                margin-left: 18px;
                left: 18px             
            }
            /* Container around content */
            .containerz {

                padding: 10px 40px;
                position: relative;
                background-color: white;
                width: 100%;
            }
            .circleActive::after{
                background-color: #0078D7 !important;
            }

            /* The circles on the timeline */
            .containerz::after {
                content: '';
                position: absolute;
                width: 25px;
                height: 25px;
                right: 40px;
                margin-left: 23px;
                background-color: white;
                border: 4px solid #0078D7 ;

                top: 15px;
                border-radius: 50%;
                z-index: 1;
            }
            .containerz1::after {
                content: '';
                position: absolute;
                width: 25px;
                height: 25px;
                right: 200px;

                background-color:#0078D7; 
                border: 4px solid #0078D7;

                top: 15px;
                border-radius: 50%;
                z-index: 1;
            }

            /* Place the container to the left */
            .left {
                left: 0;
            }

            /* Place the container to the right */
            .right {

            }

            /* Add arrows to the left container (pointing right) */
            .left::before {
                content: " ";
                height: 0;
                position: absolute;
                top: 22px;
                width: 0;
                z-index: 1;
                right: 30px;
                border: medium solid white;
                border-width: 10px 0 10px 10px;
                border-color: transparent transparent transparent white;
            }

            /* Add arrows to the right container (pointing left) */
            .right::before {
                content: " ";
                height: 0;
                position: absolute;
                top: 22px;
                width: 0;
                z-index: 1;
                left: 30px;
                border: medium solid white;
                border-width: 10px 10px 10px 0;
                border-color: transparent white transparent transparent;
            }

            /* Fix the circle for containers on the right side */
            .right::after {
                left: -16px;
            }

            /* The actual content */
            .contentz {

                background-color: white;
                position: relative;
                border-radius: 6px;
            }

            /* Media queries - Responsive timeline on screens less than 600px wide */
            @media screen and (max-width: 600px) {
                /* Place the timelime to the left */
                .timeline::after {
                    left: 31px;
                    margin-bottom: -27px
                }

                /* Full-width containers */
                .containerz {

                    width: 100%;
                    margin-left: 20px;
                    padding-left: 70px;
                    padding-right: 25px;
                }

                /* Make sure that all arrows are pointing leftwards */
                .containerz::before {
                    left: 60px;
                    border: medium solid white;
                    border-width: 10px 10px 10px 0;
                    border-color: transparent white transparent transparent;
                }

                /* Make sure all circles are at the same spot */
                .left::after, .right::after {
                    left: 15px;

                }

                /* Make all right containers behave like the left ones */
                .right {
                    left: 0%;
                }
            }

        </style>


        <div class="box box-info box-body">
            <h:form id="frmPo">
                <!--#12732 ESCALATIONS CRM-7627 Invalid access to Purchase Order via link-->
                <f:event listener="#{poServices.isPageAccessible(poServices.poHdr.poId,poServices.poHdr.poSmanId)}" type="preRenderView"/>
                <f:passThroughAttribute name="autocomplete" value="off"/>
                <div class="box-header with-border">
                    <div class="row">
                        <div class="col-md-8" >
                            <p:outputPanel id="btns" style="display: inline-table"  >
                                <!--                                Feature #5500: CRM-4627: add "Save and New" button in PO Module updated by Harshitha Devadiga on 11/08/2021-->
                                <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
                                <!--Feature #8913 CRM-6058  PO: UAT: various things #2, #4 (Feature)-->
                                <p:commandButton value="#{poServices.btnName}" 
                                                 id="btnSave"
                                                 widgetVar="hdrSave" 
                                                 actionListener="#{poServices.savePoHdr(0)}"
                                                 onclick="PF('hdrSave').disable()" 
                                                 oncomplete="PF('hdrSave').enable();"
                                                 class="btn btn-success btn-xs"
                                                 update="frmPo:leftColumn"
                                                 disabled="#{poServices.disablePOHdrButtons}"
                                                 >
                                </p:commandButton>
                                <p:spacer width="10"/>
                                <!--                                Feature #5500: CRM-4627: add "Save and New" button in PO Module updated by Harshitha Devadiga on 11/08/2021-->
                                <!--Feature #8913 CRM-6058  PO: UAT: various things #2, #4 (Feature)-->
                                <p:commandButton id="cmdBtnSveandNw" value="Save and New" actionListener="#{poServices.savePoHdr(1)}" class="btn btn-success btn-xs" update="frmPo frmPo:tabDetails" disabled="#{poServices.disablePOHdrButtons}" />
                                <p:spacer width="10"/>
                                <!--                          //PMS:2844
                                               //Seema - 31/12/2020-->
                                <!--Feature #8913 CRM-6058  PO: UAT: various things #2, #4 (Feature)-->
                                <p:commandButton value="Clear" id="btnPoClear" disabled="#{poServices.disablePOHdrButtons}"
                                                 actionListener="#{poServices.populateHeaderDataOnCancel(poServices.poHdr.poId)}"
                                                 class="btn btn-danger btn-xs"  >
                                </p:commandButton>
                                <p:spacer width="10"/>
                                <!--                          //PMS:2844
                                               //Seema - 31/12/2020-->
                                <!--Feature #8913 CRM-6058  PO: UAT: various things #2, #4 (Feature)-->   
                                <p:commandButton value="Cancel"  id="btnPoCancel" disabled="#{poServices.disablePOHdrButtons}"
                                                 actionListener="#{poServices.cancelData()}"
                                                 class="btn btn-warning btn-xs" >

                                </p:commandButton>
                                <p:spacer width="10"/>
                                <!--Feature #8913 CRM-6058  PO: UAT: various things #2, #4 (Feature)-->
                                <p:commandButton value="Delete" id="btnPoDelete" disabled="#{poServices.disablePOHdrButtons}"
                                                 actionListener="#{poServices.deleteHdr()}"
                                                 class="btn btn-danger btn-xs"  >
                                </p:commandButton>
                                <!--#925 - Purchase Orders > PDF Generation - 18-06-2020 - sharvani-->
                                <p:spacer width="10"/>
                                <!--                          //PMS:2844
                                              //Seema - 31/12/2020-->
                                <p:commandButton value="Generate PDF" rendered="#{poServices.poHdr.recId > 0}" 
                                                 disabled="#{poServices.btnName!='Edit'}" onclick="PF('pdf').show()"
                                                 id="btnGenratePo"
                                                 class="btn btn-primary btn-xs"  >
                                </p:commandButton>
                                <p:spacer width="10"/>
                                <!--#7824: CRM-5543: PO Flow:  ability to link POs to Quotes and Jobs - poornima-->
                                <!--Feature #11596:CRM-7133   PO: need to edit price without line item by harshithad on 13/07/23-->
                                <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
                                <!--                                <p:commandButton value="Linked Documents" 
                                                                                 rendered="#{poServices.poHdr.recId > 0}"
                                                                                 id="btnLinkeddocuments"
                                                                                 actionListener="#{poLinkedDocumentsService.loadPoLinkeddocuments(poServices.poHdr.poId)}"
                                                                                 disabled="#{poServices.btnName!='Edit'}" 
                                                                                 onclick="PF('dlgPoLinkedDocuments').show()" oncomplete="PF('dtPoLinkedDocsFilter').clearFilters();"
                                                                                 class="btn btn-primary btn-xs"  update=":frmPoLinksHeader :frmPoLinkedDocs:dtPoLinkedDocs">
                                                                </p:commandButton>-->
                            </p:outputPanel>
                        </div>
                    </div>
                </div>
                <!--#925 - Purchase Orders > PDF Generation - 18-06-2020 - sharvani-->
                <!--                          //PMS:2844
                                              //Seema - 31/12/2020-->
                <p:confirmDialog header="PDF" global="false"  widgetVar="pdf" id="dlgconfirmPdf" > 
                    <!--        <h:form id="confirmationDlg"> -->

                    <f:facet name="message">
                        <!--                          //PMS:2844
                                              //Seema - 31/12/2020-->
                        <p:outputLabel value="Do you want download or send the generated pdf by email?" id="oLblPdfText" />
                        <!--<br/><br/><br/>-->
<!--                        <p:selectBooleanCheckbox value="#{poServices.isNotifyOwner}"   id="chkStatus"  >
                                                    
                        </p:selectBooleanCheckbox>   -->


                    </f:facet>
                    <div align="center">

                        <p:commandButton value="Download" class="btn btn-primary btn-xs " id="btnPoPdfDownload"
                                         action="#{poServices.createPDF(1)}" ajax="false" oncomplete="PF('pdf').hide()"  /> 
                        <p:spacer width="4"/>
                        <!--                          //PMS:2844
                                              //Seema - 31/12/2020-->
                        <p:commandButton value="Send by Email"  action="#{poServices.createPDF(0)}" 
                                         id="btnPoPdfEmail"
                                         class="btn btn-primary btn-xs " oncomplete="PF('pdf').hide();  window.open('#{globalParams.emailUrl}?fn=sendpodetails&amp;reqid=#{loginBean.randomUid}&amp;autocheckemail=1&amp;po_id=#{poServices.poHdr.poId}', '_blank')"  
                                         rendered="#{poServices.poHdr.recId!=0}"   /> 

                    </div> 
                    <!--</h:form>--> 
                </p:confirmDialog>
                <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
                <h:panelGroup id="leftColumn">
                    <div class="left-column" style="min-height: 1190px">
                        <div class="ui-g ui-fluid header-bar" style="margin-top: 10px">
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 " style="text-align: center;">
                                <p:outputLabel id="otptLblPoSmry"  value="Summary" styleClass="acct-name-hdr" class="sub_title" style="font-weight: bold;text-align: center"/>
                            </div>
                        </div>
                        <div>
                            <p:outputLabel id="otptLblPoPgm" value="#{custom.labels.get('IDS_PROGRAM')} :" style="margin-right: 4px"/><p:outputLabel value="#{poServices.poHdr.poProgram}" style="font-weight:normal"/><br/>
                            <p:outputLabel id="otptLblPoVal" value="Value :" style="margin-right: 4px;"/><p:outputLabel value="#{poServices.poHdr.poTotalPrice}" style="font-weight: normal"/><br/>
                        </div>
                        <div class="ui-g ui-fluid header-bar">
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 " style="text-align: center;">
                                <p:outputLabel id="otptLblPoTmlne"  value="Timeline" styleClass="acct-name-hdr" class="sub_title" style="font-weight: bold;text-align: center"/>
                            </div>
                        </div>
                        <div class="timeline">


                                    <!--<p:outputPanel id="otptPnl" rendered="#{poServices.poStatusTimeline.showOrdered}" >-->


                            <p:dataList id="listTimeLne" value="#{poServices.statusTimelneList}" styleClass="noBorders" style="width: 100%"  var="status" type="definition" emptyMessage="">
                                <div  class="#{status.rndrItems==true?'containerz right circleActive ':'containerz right'}">
                                    <div class="contentz" >
                                        <!--// 25-05-2022 : #8049 : Purchase Order: Time line > Firefox UI issue-->
                                        <div class="flexbox-container"  style="#{status.rndrItems==true? 'width: 210px;margin-top: 5px':'width: 210px;height:20px;margin-top: 5px'}">
                                            <p:outputLabel id="otptLblSts" style="color: black;padding: 2px;background-color:#c8d6f9;text-align: center;width:150px;height: 26px" value="#{status.statusLabel}"/><br/>
                                            <p:spacer width="4px"/>
<!--Bug #13933: CRM-8126  Orders: TIMELINE Allow "edit" of timeline for "Commission Paid"-->
                                            <p:commandButton id="cmdBtnEdit" rendered="#{status.rndrItems}"  actionListener="#{poServices.populatetatusTimeline(status,0)}" styleClass="btn-primary  btn-xs btn-icon"  icon="fa fa-pencil" update="frmEdtTmlne dlgEdtTimeLne" oncomplete="PF('edtTimeline').show()" /> <p:spacer width="2px"/>
                                            <p:commandButton id="cmdBtnAdd" rendered="#{status.poStatus==5 and !status.rndrItems and (poServices.poHdr.poCloseStatus eq 2 or poServices.poHdr.poCloseStatus eq 3)}"  actionListener="#{poServices.populatetatusTimeline(status,1)}" styleClass="btn-primary  btn-xs btn-icon"  icon="fa fa-plus" update="frmEdtTmlne dlgEdtTimeLne" oncomplete="PF('edtTimeline').show()" /> <p:spacer width="2px"/>

                                            <p:commandButton id="cmdBtnAdAtch" rendered="#{status.rndrItems}" icon="fa fa-paperclip" styleClass="btn-primary  btn-xs btn-icon" actionListener="#{poServices.populatetatusTimeline(status,0)}" oncomplete="PF('addAttchDlg').show()" /> 
                                        </div>




                                        <p:outputPanel id="otptPnlItems" rendered="#{status.rndrItems}" >

                                            <p:outputLabel id="otptLblOn" value="On:" style="margin-right: 4px" /> 
                                            <p:outputLabel id="otptLblDate" value="#{globalParams.formatDateTime(status.poRefDate,'da')}" style="margin-right: 4px"   /> 
                                            <!--<br/>-->
                                            <!--Bug #13933: CRM-8126  Orders: TIMELINE Allow "edit" of timeline for "Commission Paid"-->
                                            <p:outputPanel > 
                                                <p:outputLabel id="otptLblNoLbl" value="#{status.numberLabel}:"  style="margin-right: 4px" /> 
                                                <p:outputLabel id="otptLblRefNo" value="#{status.poRefNumber}" />


                                            </p:outputPanel>



                                            <p:dataList id="dtTimelneAttch"  styleClass="noBorders" value="#{poServices.getPostatusTimelineList(status.poId, status.recId)}" widgetVar="atchList"
                                                        emptyMessage=""  type="definition" 
                                                        style="width: max-content;"
                                                        var="atch">
                                                <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
                                                <!--Feature #7999   CRM-5540: PO Flow: Download Attachments   18/05/022 by harshithad-->
                                                <!--Feature #7940: CRM-5540: PO Flow: Preview Attachments by harshihad on 30/09/22-->
                                                <!--23-01-2024 12461 Incidental Finding: CRM-7432 > Purchase Orders : preview of files remove extra space & block resize-->
                                                <p:commandLink value="#{atch.poAttaName}" onclick="PF('dlgWaitPreviewing').show();" actionListener="#{poServices.previewInDialog(atch.poAttaName)}" style="color: #0078D7;font-weight: normal" id="cmdLnkAttch" immediate="true" process="@this"  
                                                               oncomplete="PF('wvDlgPreviewIframe').show();PF('dlgWaitPreviewing').hide();" update="dlgPreviewPoFile">
<!--                                                    <p:fileDownload  contentDisposition="attachment"  value="#{poServices.getDownlaodFile(atch)}" id="dwnLoadfile"/>-->

                                                </p:commandLink>
                                            </p:dataList>

                                        </p:outputPanel>



                                    </div>
                                </div>
                            </p:dataList>


                        </div>
                        <!--Feature #8841:CRM-6069  UAT: purchase orders: add create date create by modified date modified by to form   by harshithad on 26/08/22-->
                        <p:outputPanel id="otptPnlCrtdDate"  rendered="#{poServices.poHdr.recId!=0}" 
                                       style="font-size: 0.795em;
                                       color: #6A6F73;text-align: center">
                            <h:outputLabel value="Created:"  style="font-weight: bold"  />   
                            <p:spacer width="4"/>
                            <h:outputLabel   value="#{rFUtilities.convertFromUTC(poServices.poHdr.insDate)}" >
                                <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                            </h:outputLabel>
                            <br></br>
                            by 
                            <h:outputLabel   value=" #{poServices.poHdr.insUserName}" />

                            <br></br>
                            <br></br>

                            <h:outputLabel value="Last update:"  style="font-weight: bold"  />   
                            <p:spacer width="4"/>                          
                            <h:outputLabel   value="#{rFUtilities.convertFromUTC(poServices.poHdr.updDate)}" >
                                <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                            </h:outputLabel>

                            <br></br>
                            by 

                            <h:outputLabel   value=" #{poServices.poHdr.updUserName}" />
                        </p:outputPanel>
                    </div>
                </h:panelGroup>
                <h:panelGroup styleClass="right-column" >
                    <!--//12-09-2023 : #12058 : CRM-7156   Repfabric: Instance latency, especially in POs-->
                    <script>
                        function initializeTabClickTracking() {
                            var tabView = document.getElementById('frmPo:tabDetails'); // Replace 'yourForm' with your actual form ID
                            var tabs = tabView.querySelectorAll('.ui-state-default');

                            tabs.forEach(function (tab) {
                                tab.addEventListener('click', function () {
//                                var tabId = this.getAttribute('id');
//                                console.log('tabId=',tab)
                                    var tabI = this.getAttribute('data-index');
                                    if (tabI === null) {
                                        tabI = '0';
                                    }
                                    //29-09-2023 : #12058 : CRM-7156   Repfabric: Instance latency, especially in POs
//                                if(tabI === '1'){
//                                        console.log('is tab working')
//                                        PF('dialogProcessDlg').show();
//                                        loadLineItems()
//                                    }
                                    //25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place
                                if(tabI === '6'){
//                                        PF('dialogProcessDlg').show();
                                        loadPoLinkedDoc();
                                    }
//                                console.log('tabI=',tabI)
                                });
                            });
                        }

                        // Call the function to initialize tab click tracking
                        initializeTabClickTracking();


                    </script>
                    <!--//12-09-2023 : #12058 : CRM-7156   Repfabric: Instance latency, especially in POs-->
                    <p:dialog id="dialogProcess" widgetVar="dialogProcessDlg" closable="false" modal="true" header="Message" onShow="PF('dialogProcessDlg').initPosition();" resizable="false" >
                        <!--<h:form>-->
                        <p:outputPanel >
                            <br />
                            <h:graphicImage  library="images" name="ajax-loader.gif"   />
                            <p:spacer width="5" />
                            <p:outputLabel value="Loading please wait..." />
                            <br /><br />
                        </p:outputPanel>
                        <!--</h:form>-->
                    </p:dialog>
                    <p:tabView id="tabDetails"  >
                        <!--Feature #7942: CRM-5545: PO Flow: Line item: User Selectable commission percentage  by harshithad 11/05/2022-->
                        <p:ajax event="tabChange"  listener="#{poServices.onTabChange}"/>
                        <p:tab  title="Basic" id="tabBasic"   >
                            <ui:include src="tabs/Basic.xhtml"/>
                        </p:tab>
                        <!--                    Feature #5500: CRM-4627: add "Save and New" button in PO Module updated by Harshitha Devadiga on 11/08/2021-->
                        <p:tab title="Line Items"  rendered="#{poServices.poHdr.recId!=0}" >
                            <ui:include src="tabs/LineItem.xhtml" />
                        </p:tab>
                        <!--                    Feature #5500: CRM-4627: add "Save and New" button in PO Module updated by Harshitha Devadiga on 11/08/2021-->            
                        <p:tab title="Comments"  rendered="#{poServices.poHdr.recId!=0}">
                            <ui:include src="tabs/Comments.xhtml" />
                        </p:tab>
                        <!--                    Feature #5500: CRM-4627: add "Save and New" button in PO Module updated by Harshitha Devadiga on 11/08/2021-->
                        <p:tab title="Attachments"  rendered="#{poServices.poHdr.recId!=0}">
                            <ui:include src="tabs/Attachments.xhtml" />
                        </p:tab>
                        <!--                    //PMS:2636
                                            //Seema - 08/12/2020-->
                        <!--                    Feature #5500: CRM-4627: add "Save and New" button in PO Module updated by Harshitha Devadiga on 11/08/2021-->
                        <p:tab title="Opportunities"  rendered="#{poServices.poHdr.recId!=0}">
                            <!--                         //PMS:2323
                                //Seema - 12/11/2020-->
                            <ui:include src="tabs/Opportunities.xhtml" />
                        </p:tab>
                        <!--                    //PMS:2830 - commenting the quote tab for time being as there is no requirement given
                                            //seema - 26/12/2020-->
                        <!--                    <p:tab title="Quote" disabled="#{poServices.poHdr.recId==0}">
                                            </p:tab>-->
                        <!--#7654: CRM-5517 PO - show emails tab - poornima-->
                        <p:tab title="Emails" rendered="#{poServices.poHdr.recId!=0}">
                            <ui:include src="tabs/Emails.xhtml" />
                        </p:tab>
                        <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
                        <p:tab title="Linked Docs" rendered="#{poServices.poHdr.recId!=0}" >
                            <div class="ui-g ui-fluid header-bar">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel styleClass="acct-name-hdr sub_title">Linked Doc Details</p:outputLabel>
                                </div>

                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputPanel id="btns" style="text-align: right;margin-left: 2px">
                                        <p:outputLabel value="Link to" style="width:10%"/>
                                        <p:spacer width="4" />
                                        <p:commandButton id="btnOppLink" value="#{custom.labels.get('IDS_OPP')}" process="@this"
                                                         styleClass="btn btn-primary btn-xs" oncomplete="PF('dlgPoLinkedOpp').show()"
                                                         title="Link to #{custom.labels.get('IDS_OPP')}"
                                                         actionListener="#{poHdr.loadOppList(poServices.poHdr.poPrinciId,poServices.poHdr.poCustId)}"
                                                         disabled="#{poServices.poHdr.poOppId != 0}" style="width:25%" />
                                        <p:spacer width="4" />
                                        <p:commandButton id="btnQuoteLink" value="#{custom.labels.get('IDS_QUOTES')}" process="@this"
                                                         styleClass="btn btn-primary btn-xs" oncomplete="PF('dlgPoLinkedQuote').show()"
                                                         title="Link to #{custom.labels.get('IDS_QUOTES')}"
                                                         actionListener="#{quotesHdr.loadQuoteList(poServices.poHdr.poPrinciId,poServices.poHdr.poCustId)}"
                                                         disabled="#{poServices.poHdr.poQuoteId != 0}" style="width:25%;"/>
                                        <p:spacer width="4" />
                                        <p:commandButton id="btnJobLink" value="#{custom.labels.get('IDS_JOB')}" process="@this"
                                                         styleClass="btn btn-primary btn-xs" oncomplete="PF('dlgPoLinkedJob').show()"
                                                         title="Link to #{custom.labels.get('IDS_JOB')}" action="#{jobs.fetchJobsListForOpps()}"
                                                         disabled="#{poServices.poHdr.poJobId != 0}" update=":frmPoJobList:dtPoJobList" style="width:25%;"/>
                                    </p:outputPanel>
                                </div>
                            </div>

                            <div class="ui-g ui-fluid">
                                <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                                    <!--//04-07-2024 : #13957 : Query usage analysis for viewjobs-->
                                    <!--14406-->
                                    <p:remoteCommand name="loadPoLinkedDoc" autoRun="false" actionListener="#{poLinkedDocumentsService.loadPoLinkeddocuments(poServices.poHdr.poId)}" 
                                                     update=":frmPo:tabDetails:poLinkedDocs  :frmPo:tabDetails:btnOppLink :frmPo:tabDetails:btnQuoteLink :frmPo:tabDetails:btnJobLink" oncomplete="PF('dialogProcessDlg').hide()"/>
                                    <p:dataTable id="poLinkedDocs" value="#{poLinkedDocumentsService.poLinkedDocLst}" 
                                                 var="poLnkdDoc"
                                                 rows="10" 
                                                 selectionMode="single" 
                                                 filterEvent="keyup" 
                                                 draggableColumns="true"
                                                 emptyMessage="No Linked Documents found."  
                                                 paginator="true" 
                                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                                 paginatorAlwaysVisible="false" 
                                                 class="tblmain"
                                                 rowKey="#{poLnkdDoc.recId}" resizableColumns="true"
                                                 >
                                        <p:column headerText="Type" filterBy="#{poLnkdDoc.docType}" filterMatchMode="contains" sortBy="#{poLnkdDoc.docType}">
                                            <p:commandLink rendered="#{poLnkdDoc.docType eq 'Quote'}"  value="#{poLnkdDoc.docType}"
                                                           onclick="window.open('../../opploop/quotes/NewQuote.xhtml?recId=#{poLnkdDoc.docId}', '_blank');" 
                                                           >
                                            </p:commandLink>
                                            <p:commandLink rendered="#{poLnkdDoc.docType eq 'Opportunity'}"  value="#{poLnkdDoc.docType}"
                                                           onclick="window.open('../../opploop/opportunity/OpportunityView.xhtml?opid=#{poLnkdDoc.docId}', '_blank');"
                                                           ></p:commandLink>
                                            <p:commandLink rendered="#{poLnkdDoc.docType eq 'Job'}"  value="#{poLnkdDoc.docType}"
                                                           onclick="window.open('../../opploop/jobs/JobsView.xhtml?id=#{poLnkdDoc.docId}', '_blank');"
                                                           >
                                            </p:commandLink>
                                            <!--//20-11-2023 : #12558 : CRM-7205  Linked documents: inconsistencies all over the place-->
                                            <p:commandLink rendered="#{poLnkdDoc.docType eq 'Invoice'}"  value="#{poLnkdDoc.docType}"
                                                           onclick="window.open('../../datamanagement/commtransactions/CommtransDetails.xhtml?id=#{poLnkdDoc.docId}', '_blank');"

                                                           >
                                            </p:commandLink>
                                        </p:column>
                                        <p:column headerText="Doc. No." filterBy="#{poLnkdDoc.docNo}" filterMatchMode="contains" sortBy="#{poLnkdDoc.docNo}">
                                            <h:outputText value="#{poLnkdDoc.docNo}" />
                                        </p:column>
                                        <p:column headerText="Doc. Date" filterBy="#{oppLinkedDocumentsService.getFormattedDate(poLnkdDoc.docDate, 'da')}" filterMatchMode="contains" sortBy="#{poLnkdDoc.docDate}">
                                            <h:outputText value="#{oppLinkedDocumentsService.getFormattedDate(poLnkdDoc.docDate, 'da')}" />
                                        </p:column>
                                        <p:column headerText="Topic" filterBy="#{poLnkdDoc.docTopic}" filterMatchMode="contains" sortBy="#{poLnkdDoc.docTopic}">
                                            <h:outputText value="#{poLnkdDoc.docTopic}" />
                                        </p:column>
                                        <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{poLnkdDoc.docCustomer}" filterMatchMode="contains" sortBy="#{poLnkdDoc.docCustomer}">
                                            <h:outputText value="#{poLnkdDoc.docCustomer}" />
                                        </p:column>
                                        <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{poLnkdDoc.docPrinci}" filterMatchMode="contains" sortBy="#{poLnkdDoc.docPrinci}">
                                            <h:outputText value="#{poLnkdDoc.docPrinci}" />
                                        </p:column>
                                        <p:column headerText="Value" filterBy="#{poLnkdDoc.docValue}" filterMatchMode="contains" sortBy="#{poLnkdDoc.docValue}">
                                            <h:outputText value="#{poLnkdDoc.docValue}" style="float: right" />
                                        </p:column>
                                        <p:column headerText="Linked Doc" style="text-align: center!important;">
                                            <p:commandButton style="height:25px;width:25px;" 
                                                             styleClass="btn-primary  btn-xs" title="Linked Doc" icon="fa fa-eye"
                                                             actionListener="#{linkedDocService.loadQuotLinkedData(poLnkdDoc.docType,poLnkdDoc.docId)}"
                                                             action="#{linkedDocService.loadHeader(poLnkdDoc.docType,poLnkdDoc.docNo)}"
                                                             update="linkedDocumentForm:linkedDocumentDialogue" 
                                                             oncomplete="PF('linkedDocumentDlgVar').show();"
                                                             ></p:commandButton>
                                            <!--14406-->
                                            <p:spacer width="4" />
                                            <p:commandButton style="height:25px;width:25px;" 
                                                             styleClass="btn-primary  btn-xs" title="UnLink Doc" icon="fa fa-unlink"
                                                             actionListener="#{linkedDocService.loadUnLinkData(poLnkdDoc.docType,poServices.poHdr.poId,poServices.poHdr)}"
                                                             oncomplete="PF('unlinkDlg').show();"
                                                             ></p:commandButton>
                                        </p:column>
                                    </p:dataTable>
                                </div>
                            </div>
                        </p:tab>
                    </p:tabView>
                    <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
                </h:panelGroup>
            </h:form>
            <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
            <ui:include src="../quotes/dialog/LinkedDocDLg.xhtml"/>
            <ui:include src="../../lookup/CompanyLookupDlg.xhtml"/>
            <!--Task #12780: CRM-7582 Direct commission: 3 way Customer Splits   by harshithad on 10/01/2023-->
            <ui:include src="../../opploop/po/SalesTeamLookup.xhtml"/>
            <ui:include src="../../lookup/ProductFamily.xhtml"/>
            <ui:include src="../../lookup/PartNumDlg.xhtml"/>
            <!--pms:2982 11/01/2021 udaya b -->
            <ui:include src="dlg/LineItemDlg.xhtml"/>
            <ui:include src="dlg/SplitLineItemDlg.xhtml"/> 
            <ui:include src="dlg/BulkLineItemShiping.xhtml"/>
            <!--                                //PMS:1506
                                                       //seema - 29/08/2020-->
            <ui:include src="../../lookup/CustPartNumDlg.xhtml"/>
            <!--                        //PMS:2830
                                    //Seema - 26/12/2020-->
            <ui:include src="dlg/PoLinkedDocuments.xhtml"/>

            <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->
            <ui:include src="dlg/EditPoTimelineDlg.xhtml"/>
            <ui:include src="dlg/poAddTimelineAttachmentsDlg.xhtml"/>
            <!--//21-04-2022 : #7801  : CRM-5545: PO Flow: User Selectable commission percentage addition during addin po entry-->
            <ui:include src="dlg/DlgMLCommission.xhtml"/>

            <!--#8287: po linked opp unlinking issue - poornima -->
            <ui:include src="dlg/LinkToOppDlg.xhtml"/> 
            <!--Feature #9216 Direct Commission: Purchase Orders > Basic tab > Add Direct Comm. section by harshithad on 13/10/22-->
            <ui:include src="../../lookup/UsersLookupDlg.xhtml"/>
            <!--Feature #11460: CRM-7082   Paperclip on orders list - file preview window popup by harshithad on 19/06/23-->
            <ui:include src="dlg/POPreviewDlg.xhtml"/>
            <!--</div>-->
            <!--Task #12780: CRM-7582 Direct commission: 3 way Customer Splits   by harshithad on 10/01/2023-->
            <ui:include src="../../lookup/UserLookupDlg.xhtml"/>
            <ui:include src="../../lookup/SalesTeamLookupDlg.xhtml"/>
            <ui:include src="../../config/directcomms/CompanyBasedDirectComDlg.xhtml"/>
            <ui:include src="../../config/directcomms/DirComTypeConfirmDialogue.xhtml"/>
            <p:confirmDialog header="Confirmation" global="true" severity="alert"   showEffect="fade" hideEffect="fade" 
                             widgetVar="redundancyWarningDlg" id="redundancyMsg" responsive="true"
                             message="PO Number exists for the chosen #{custom.labels.get('IDS_PRINCI')}.Do you want to continue?">
                <h:form id="redundancyWarning">
                    <div align="center">
                        <!--Task #12780: CRM-7582 Direct commission: 3 way Customer Splits   by harshithad on 10/01/2023-->
                        <p:commandButton value="Yes" id="btnRedundancyYes"
                                         actionListener="#{poServices.hdrSave(0)}"   
                                         styleClass="btn btn-success  btn-xs"  process="@this" 
                                         oncomplete="PF('redundancyWarningDlg').hide()"
                                         >
                        </p:commandButton>
                        <p:spacer width="4px"/>
                        <p:commandButton value="No" styleClass="btn btn-danger  btn-xs" id="btnRedundancyNo"
                                         onclick="PF('redundancyWarningDlg').hide()" 
                                         type="button" />
                    </div>
                </h:form>
            </p:confirmDialog>
            <!--14406-->
            <p:confirmDialog header="Confirmation" global="true" severity="alert"   showEffect="fade" hideEffect="fade" 
                             widgetVar="unlinkDlg" id="unlinkMsg" responsive="true"
                             message="Are you sure to unlink?">
                <h:form id="unlinkWarning">
                    <div align="center">
                        <!--Task #12780: CRM-7582 Direct commission: 3 way Customer Splits   by harshithad on 10/01/2023-->
                        <p:commandButton value="Yes" id="btnUnlinkYes"
                                         actionListener="#{linkedDocService.unLinkPO()}"  
                                         styleClass="btn btn-success  btn-xs"  process="@this" 
                                         update="frmPoLinksHeader frmPoLinkedDocs :formPoLinkDoc frmPo"
                                         oncomplete="PF('unlinkDlg').hide()"
                                         >
                        </p:commandButton>
                        <p:spacer width="4px"/>
                        <p:commandButton value="No" styleClass="btn btn-danger  btn-xs" id="btnUnlinkNo"
                                         onclick="PF('unlinkDlg').hide()" 
                                         type="button" />
                    </div>
                </h:form>
            </p:confirmDialog>

            <!--            //PMS:1482
                        //Seema - 03/09/2020-->
            <!--            <p:confirmDialog header="Confirmation" global="true" severity="alert"   showEffect="fade" hideEffect="fade" 
                                         widgetVar="commTransWarningDlg" id="commtransMsg" responsive="true"
                                         message="Duplicate record found in Commissionable Transaction for the chosen #{custom.labels.get('IDS_PRINCI')}/#{custom.labels.get('IDS_CUSTOMER')}/.Do you want to Save?">
                            <h:form id="redundancyWarning">
                                <div align="center">
                                    <p:commandButton value="Save" 
                                                     actionListener="#{poServices.hdrSave()}"   
                                                     styleClass="btn btn-success  btn-xs"  process="@this" 
                                                     oncomplete="PF('commTransWarningDlg').hide()"
                                                     >
                                    </p:commandButton>
                                    <p:spacer width="4px"/>
                                    <p:commandButton value="Cancel" styleClass="btn btn-danger  btn-xs" 
                                                     onclick="PF('commTransWarningDlg').hide()" 
                                                     type="button" />
                                </div>
                            </h:form>
                        </p:confirmDialog>-->

            <p:confirmDialog header="Confirm Deletion" global="false"  
                             id="dlgPoConfirmDelete"
                             message=" Associated #{custom.labels.get('IDS_LINE_ITEM')} records will be deleted.Are you sure to delete this record?"   
                             widgetVar="confirmation" >
                <h:form id="confirmationDlg">
                    <div align="center">
                        <p:commandButton value="Yes"  actionListener="#{poServices.deleteLinkedRecords()}" 
                                         id="btnHeaderDeleteYes"
                                         styleClass="btn btn-success  btn-xs"   process="@this" />
                        <p:spacer width="4px"/>
                        <p:commandButton value="No"   styleClass="btn btn-danger  btn-xs"
                                         id="btnHeaderDeleteNo"
                                         onclick="PF('confirmation').hide()" 
                                         />
                    </div>
                </h:form>
            </p:confirmDialog>

            <p:confirmDialog header="Confirm Deletion" global="false"  id="dlgCnfrmPoDtlDlt"
                             message="Do you want to delete this Line Item?"   
                             widgetVar="confirmationdtl" >
                <h:form id="confirmationDtlDlg">
                    <div align="center">
                        <!--                        Bug #5926: Purchase Orders:  Line item list update issue on filter and add  bu harshithad 24/09/21-->
                        <!--Feature #8841: CRM-6069  UAT: purchase orders: add create date create by modified date modified by to form  by harshithad 0n 20/09/21-->    
                        <p:commandButton value="Yes"  actionListener="#{poServices.deleteLineItem()}" 
                                         id="btnPoDtlDltYes" update="frmPo:tabDetails:dtLineItems :frmPo:otptPnlCrtdDate"
                                         oncomplete="PF('confirmationdtl').hide();PF('lineItemDt').filter();"
                                         styleClass="btn btn-success  btn-xs"   process="@this" />
                        <p:spacer width="4px"/>
                        <p:commandButton value="No"   styleClass="btn btn-danger  btn-xs"
                                         id="btnPoDtlDltNo"
                                         onclick="PF('confirmationdtl').hide()" 
                                         />
                    </div>
                </h:form>
            </p:confirmDialog>

            <!--            //PMS:2795
                        //Seema - 22/12/2020-->
            <p:confirmDialog header="Confirm Deletion" global="false"  
                             message="Are you sure to delete attachment?"   
                             widgetVar="confirmationAtchment" >
                <h:form id="confirmationAtchDtlDlg">
                    <div align="center">
                        <!--                          //PMS:2844
                                              //Seema - 31/12/2020-->
                        <p:commandButton value="Yes"  actionListener="#{poServices.deleteAttachment()}" 
                                         oncomplete="PF('confirmationAtchment').hide()"
                                         id="btnPoAtchDeleteYes"
                                         styleClass="btn btn-success  btn-xs"   process="@this" />
                        <p:spacer width="4px"/>
                        <!--                          //PMS:2844
                                              //Seema - 31/12/2020-->
                        <p:commandButton value="No"   styleClass="btn btn-danger  btn-xs"
                                         id="btnPoAtchDeleteNo"
                                         onclick="PF('confirmationAtchment').hide()" 
                                         />
                    </div>
                </h:form>
            </p:confirmDialog>
            <!--Feature #7786:CRM-5540: PO Flow: Clean consistent sidebar or billboard for reconciliation flow status  10/05/22 by harshithad-->

            <p:dialog widgetVar="dlgWaitPreviewing" closable="false" modal="true" header="Message" >
                <p:outputPanel >
                    <br />
                    <h:graphicImage  library="images" name="ajax-loader.gif"   />
                    <p:spacer width="5" />
                    <p:outputLabel value="Opening file Please wait..." />
                    <br /><br />
                </p:outputPanel>
            </p:dialog>
            <!--Feature #9892: CRM-5541: PO Flow: ability to quickly see documents throughout the reconciliation process in instant by harshithad on 06/01/22-->
            <!--Feature #11460: CRM-7082   Paperclip on orders list - file preview window popup by harshithad on 19/06/23-->
            <!--            <p:dialog modal="true" widgetVar="wvDlgPreviewIframe" header="Preview: #{poServices.fileName}" id="dlgPreviewIframeNikilSir"  onShow="PF('wvDlgPreviewIframe').initPosition();" width="85%" position="center" height="80%" appendTo="@(body)">
                            <f:facet name="header">
                                <p:outputPanel id="headerOfficePreview">
                                    Preview: #{poServices.fileName}
                                </p:outputPanel>
                            </f:facet>
                            <p:outputPanel id="officePreview">
                                <br/>
                                <br/>
                                <p:outputPanel rendered="#{empty poServices.previewFile}">
                                    <h1>File not found</h1>
                                </p:outputPanel>
                                <p:outputPanel rendered="#{not empty poServices.previewFile}">
                                    <iframe id="officeFrame" src="#{poServices.previewFile}"
                                            width="1000px" height="600px" frameborder="0" ></iframe>
                                </p:outputPanel>
            
            
                            </p:outputPanel>
                        </p:dialog>-->
            <!--Feature #9424:Direct Commission: Import Transactions > Aliasing by harshithad on 02/11/22-->
            <!--Feature #9216 Direct Commission: Purchase Orders > Basic tab > Add Direct Comm. section by harshithad on 13/10/22-->
            <h:form>
                <p:confirmDialog id="cnfrmDlgDelt" header="Confirm Deletion" global="false"
                                 message="Do you want to delete this Sales rep?"   
                                 widgetVar="confirmComDel" >

                    <div align="center">
                        <p:commandButton value="Yes"  actionListener="#{directCommService.removeItem()}" 
                                         oncomplete="PF('confirmComDel').hide()"                                 
                                         styleClass="btn btn-success  btn-xs" />
                        <p:spacer width="4px"/>
                        <p:commandButton value="No"  styleClass="btn btn-danger  btn-xs" onclick="PF('confirmComDel').hide()" 
                                         />
                    </div>

                </p:confirmDialog>
            </h:form>
            <!--Feature #9732:CRM-6360: PO: multilevel comms: edit mode not adjusting the projected commission by harshithad on 21/11/22-->
            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
            <p:confirmDialog header="Confirmation" global="false"  
                             message="You have changed the projected commission percentage. In order for the PO header to be adjusted, should the new projected #{custom.labels.get('IDS_COMMISSION')} be applied to all unshipped #{custom.labels.get('IDS_LINE_ITEMS')}?   Please manually update #{custom.labels.get('IDS_LINE_ITEMS')} if you choose No, otherwise the projected #{custom.labels.get('IDS_COMMISSION')} will not change."   
                             widgetVar="confirmCommRateChange" closable="false" >
                <h:form id="frmconfirmCommRate">

                    <div align="center">
                        <p:commandButton value="Yes"  actionListener="#{poServices.applyCommRateToLineItems()}" 
                                         oncomplete="PF('confirmCommRateChange').hide()"
                                         id="btnChangeYes"
                                         styleClass="btn btn-success  btn-xs" />
                        <p:spacer width="4px"/>

                        <p:commandButton value="No"   styleClass="btn btn-danger  btn-xs"
                                         id="btnChangeNo" actionListener="#{poServices.noCommRateAppliedToLineItems()}"
                                         onclick="PF('confirmCommRateChange').hide()" 
                                         />
                    </div>
                </h:form>
            </p:confirmDialog>
            <!--Feature #10226 Feature CRM-6466 Direct Commission PO entry: no option shown when header created by addin by harshithad-->
            <h:form>
                <p:confirmDialog id="cnfrmDlgDelt" header="Confirm Deletion" global="false"
                                 message="Are you sure to delete this rule?"   
                                 widgetVar="confirmRuleDel" >

                    <div align="center">
                        <p:commandButton value="Yes"  actionListener="#{directCommService.delete(directCommService.dcRuleName)}" 
                                         oncomplete="PF('confirmRuleDel').hide()"                                 
                                         styleClass="btn btn-success  btn-xs" />
                        <p:spacer width="4px"/>
                        <p:commandButton value="No"  styleClass="btn btn-danger  btn-xs" onclick="PF('confirmRuleDel').hide()" 
                                         />
                    </div>

                </p:confirmDialog>
            </h:form>
            <!--Task #12780: CRM-7582 Direct commission: 3 way Customer Splits   by harshithad on 10/01/2023-->
            <h:form>
                <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                <p:confirmDialog id="cnfrmDlgPrcd" header="Confirmation" global="false"
                                 message="There are direct #{custom.labels.get('IDS_COMMISSION')} inputs with no percentage allocated. Proceed?"   
                                 widgetVar="confirmDlgPrcd" >

                    <div align="center">
                        <p:commandButton value="Yes"  actionListener="#{poServices.hdrSave(1)}" 
                                         oncomplete="PF('confirmRuleDel').hide()"                                 
                                         styleClass="btn btn-success  btn-xs" />
                        <p:spacer width="4px"/>
                        <p:commandButton value="No"  styleClass="btn btn-danger  btn-xs" onclick="PF('confirmDlgPrcd').hide()" 
                                         />
                    </div>

                </p:confirmDialog>
            </h:form>
        </div>
    </ui:define>

</ui:composition>