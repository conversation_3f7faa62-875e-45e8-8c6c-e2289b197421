<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: SalesTeamLookUp.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui">
    <!--  //PMS:2637
                            //Seema - 08/12/2020-->
    <p:dialog header="#{custom.labels.get('IDS_SALES_TEAM')} Lookup" widgetVar="dlgSalesManLookup" width="400"  resizable="false"
              modal="true">
        <h:form id="frmSalesManReconLookup" > 
            <p:dataTable  widgetVar="dtSmanLookup" 
                          value="#{poServices.salesTeams}" 
                          var="sman"    
                          rows="10" id="transSalesTeam" >
                <p:column headerText="Sales Team">
                    <p:commandLink process="@this"  value="#{sman.smanName}"
                                   actionListener="#{poServices.onSalesTeamSelection(sman.smanId,sman.smanName)}" 
                                   oncomplete="PF('dlgSalesManLookup').hide();"
                                   />

                </p:column>
            </p:dataTable>
        </h:form>  
    </p:dialog>
</ui:composition>