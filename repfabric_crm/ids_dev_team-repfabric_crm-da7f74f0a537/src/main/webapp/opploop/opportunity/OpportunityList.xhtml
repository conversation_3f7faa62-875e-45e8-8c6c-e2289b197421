<ui:composition  xmlns="http://www.w3.org/1999/xhtml" 
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:f="http://java.sun.com/jsf/core" 
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                 xmlns:pe="http://primefaces.org/ui/extensions"
                 template="#{layoutMB.template}"
                 >




    <h:head>
    </h:head>
    <!--#142 Page Title for All pages-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>
            #{custom.labels.get('IDS_OPP')} List   
        </title>
    </ui:define>
    <ui:define name="metadata">
        <!--#5122: Opportunity List > Implement lazy loading-->
        <f:view>
            <f:metadata>
                <!--Retain Opp Filter - Initialize the parameters-->
                <!--<f:viewAction action="#{oppService.defaultList()}"/>-->
                <f:viewAction action="#{oppService.loadOpps()}"/>
                <f:viewAction action="#{oppService.loadOppsColumnsList()}"/>
                <!--<f:viewAction action="#{helpService.prepareHelpLinks('OPP_LIST')}"/>-->
                <!--2751 - CRM-1736: Autlogin for opps list page to learning.repfabric.com - please add - sharvani - 23-12-2019-->
                <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('OPP_LIST'))}" />
                <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
                <f:viewAction action="#{helpService.setPageTag('OPP_LIST')}" />
            </f:metadata>
              <style>
                .ui-button-icon-right {
                    right: 36.5em !important;
                }
                .ui-select-btn{
                    border-radius: 0px;
                    margin-top: 0px;
                    background: #fff; 
                    height:27px;
                }


                .ui-chkbox .ui-chkbox-box {
                    display: initial !important;
                }
                /*<!--//01-06-2022 : 8033 : CRM-4972  MAKE HEADERS AT TOP OF PAGE STICK so you can scroll and still see what column you are--> */
                /*//09-08-2023 : #11832 : CRM-7217   Change wrapping properties on list view screens for all modules*/
                .ellipsis-column {
                    width: 100px ;/* Set the desired width for the column */
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

            </style>
        </f:view>

    </ui:define>
    <!--
        <ui:define name="title">
            <ui:param name="title" value="#{custom.labels.get('IDS_OPP')} List"/>
            <div class="row">
                <div class="col-md-4 col-sm-4 col-lg-4">
                    <f:event type="preRenderView" listener="#{facesContext.externalContext.setResponseHeader('Cache-Control','no-cache, no-store')}" />
                    <f:event listener="#{oppService.isPageAccessible}" type="preRenderView"/>
                    #3354 Fix Sonarqube issues
                    <title>
    #{custom.labels.get('IDS_OPP')} List   
</title>
    
    </ui:define>
</div>
#1451: Use common template in Opportunities, Funnel report, Roles, CRM Sync

<div class="col-md-8 col-sm-8 col-lg-8" align="right">
<ui:include src="/help/Help.xhtml"/>

</div>
</div>
</ui:define>-->

    <ui:define name="title">
        <!--#5122: Opportunity List > Implement lazy loading-->
        <f:view>
            <f:event type="preRenderView" listener="#{facesContext.externalContext.setResponseHeader('Cache-Control','no-cache, no-store')}" />
            <f:event listener="#{oppService.isPageAccessible}" type="preRenderView"/>
        </f:view>
        <ui:param name="title" value=" #{custom.labels.get('IDS_OPP')} List  "/>
        <div class="row">
            <div class="col-md-6">
                #{custom.labels.get('IDS_OPP')} List  
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>
    <ui:define name="menu">
    </ui:define>

    <ui:define name="body" >
        <!--#5122: Opportunity List > Implement lazy loading-->
        <f:view>
            <style>
                /*            .ui-datatable .ui-column-filter {
                                width: 85px!important;
                            }*/
            </style>
            <!--02-03-2022 : #7386 : Opportunity List: Create collapsible left filter(panel) option-->
            <div class="box box-info box-body">
                <div>

                    <h:form id="left_menu">
                        <!--//25-05-2022 : 8051 : Opportunity: Left Panel Collapsible button-->
                        <div  class="left-column" id="p" style="width:#{uIControlService.oppFilterCollapse ? '20%' : '3%'};">

                            <ui:include src="LeftOpportunity.xhtml" /> 

                        </div>
                    </h:form>
                </div>
                <!--//12-07-2022 :  #8446 : ESCALATIONS CRM-5903 Epic/Tina Grohn/Opportunity List Collapsing drops the list to the bottom of t-->
                <h:form id="dtForm" style="#{!uIControlService.oppFilterCollapse ? 'margin-top: -600px;': ''}">
                    <!--02-03-2022 : #7386 : Opportunity List: Create collapsible left filter(panel) option-->
                    <!--//25-05-2022 : 8051 : Opportunity: Left Panel Collapsible button-->
                    <div id="q" class="#{uIControlService.oppFilterCollapse ? 'right-column' : 'right-column-collapse'}">

                        <!--                <button id="hide">Hide</button>
                                        <button id="show">Show</button>-->

                        <!--                3726    Opportunities > Retain pagination-->
                        <p:blockUI  widgetVar="blockUI"  block="tblOpp" id="bui"  >
                            <h:outputText  value="Loading #{custom.labels.get('IDS_OPPS')}..."/>
                        </p:blockUI>
                        <p:panel id="opo" >  
                            <!--hhhooooooooorizontal scroll-->
                            <!--#2387 CRM-1466: Fwd: Column settings-->
                            <!--                        3726    Opportunities > Retain pagination-->
                            <!--#1247:  CRM-2950: UI Changes: #3: Opportunities > Option to change number of records-->
                            <!--#5122: Opportunity List > Implement lazy loading-->
                            <!--//01-06-2022 : 8033 : CRM-4972  MAKE HEADERS AT TOP OF PAGE STICK so you can scroll and still see what column you are--> 
                            <!--//20-06-2023 : #11420 : ESCALATIONS CRM-7065/ CRM-7075   RF Web: Columns adjust when hovering over them and searching-->
                            <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                            <!--//17-10-2024 : #14707 : 2 vertical scroll bars showing in Company,Contacts,Opportunity,Quotes,Jobs and PO module-->
                            <p:dataTable id="tblOpp" value="#{oppService}"
                                         selection="#{opportunities.selectedOpp}" 
                                         filteredValue="#{opportunities.listFilteredOpp}" 
                                         var="opp"
                                         multiViewState="true"  
                                         rows="50"
                                         widgetVar="tblOpp1" filterEvent="keyup"
                                         draggableColumns="true"
                                         emptyMessage="No #{custom.labels.get('IDS_OPP')} found"
                                         paginator="true"  
                                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                         rowsPerPageTemplate="50,100" 
                                         paginatorAlwaysVisible="true"
                                         rowKey="#{opp.oppId}"
                                         resizableColumns="true" resizeMode="expand"
                                         rowSelectMode="add"
                                         lazy="true" scrollable="true" scrollHeight="2000"
                                         rowIndexVar="index"
                                         style="margin: -10px!important;">

                                <!--#5122: Opportunity List > Implement lazy loading-->
                                <!--//31-07-2023 : #11673 : ESCALATIONS CRM-7159   Companies: column adjustments reset after logging out-->
                                <p:ajax event="colResize" listener="#{oppService.columnResize}"/>
                                <p:ajax event="rowSelectCheckbox" listener="#{oppService.onRowSelectCheckbox}" update=":dtForm:tblOpp:btnOppMassClose :dtForm:tblOpp:btnOppBlkUpdate" />
                                <p:ajax event="rowUnselectCheckbox" listener="#{oppService.onRowUnselectCheckbox}" update=":dtForm:tblOpp:btnOppMassClose :dtForm:tblOpp:btnOppBlkUpdate" />
                                <p:ajax event="toggleSelect" listener="#{oppService.onToggleSelectOpp}"  update=":dtForm:tblOpp:btnOppMassClose :dtForm:tblOpp:btnOppBlkUpdate"  />

                                <!--style="height: 85vh"--> 
                                <!--                        <p:dataTable  id="tblOpp" widgetVar="tblOpp1" paginator="true"  
                                                                      value="#{opportunities.listOpportunities}"                         
                                                                      filterEvent="keyup"  rowIndexVar="index"                      
                                                                      filteredValue="#{opportunities.listFilteredOpp}" 
                                                                      var="opp"                                       
                                                                      rowKey="#{opp.oppId}"
                                                                      selection="#{opportunities.selectedOpp}" 
                                                                      selectionMode="single"                                       
                                                                      paginatorAlwaysVisible="false"
                                                                      paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                                                      rows="50"   class="tblmain"
                                                                      emptyMessage="No #{custom.labels.get('IDS_OPP')} found"
                                                                      draggableColumns="true" 
                                                                      tableStyle="table-layout:auto" resizableColumns="true">-->
                                <p:ajax event="page" listener="#{oppFilter.onPageChange}" /> 

                                <!--#5122: Opportunity List > Implement lazy loading-->
                                <p:ajax event="page" listener="#{oppService.onPagination}" update=":dtForm:tblOpp"/>  
                                <p:ajax event="colReorder" listener="#{oppService.onColumnReorder}"/>
                                <f:facet name="header">
                                    <div style="display: inline-flex; text-align: left;float: left">
                                        <h:panelGroup>
                                            <p:commandButton id="opNewBtn"  value="New" ajax="false" process="none" title="Create new #{custom.labels.get('IDS_OPP')}"  action="/opploop/opportunity/OpportunityView.xhtml?faces-redirect=true"  styleClass="btn btn-primary btn-xs" /> 
                                            <p:spacer width="4px"/>
                                            <!--                                        PMS 3814: Opportunity List  Show Total-->
                                            <!--#5122: Opportunity List > Implement lazy loading-->
                                            <p:commandButton value="Show Totals" immediate="true"  id="btnSubTotal" 
                                                             styleClass="btn btn-primary btn-xs" update="ovrLaySubTtl"
                                                             oncomplete="PF('ovrLaySubTtl').show();"  />
                                            <p:overlayPanel  showCloseIcon="true" widgetVar="ovrLaySubTtl" id="ovrLaySubTtl" style="width: auto;height: auto;">
                                                <p:outputLabel id="olOppSubTtlLbl" value="Value:"  style="font-weight: bold !important;"/>
                                                <p:spacer width="4px"/>
                                                <p:outputLabel id="olOppSubTt" value="#{opportunities.oppSubTotal}" >
                                                    <f:convertNumber maxFractionDigits="2" groupingUsed="true"/>
                                                </p:outputLabel>
                                            </p:overlayPanel>
                                            <p:spacer width="4px"/>
                                            <!--                                        PMS 4013: Opportunities > Bulk Update-->
                                            <!--#5122: Opportunity List > Implement lazy loading-->
                                            <!--                                            Bug #6183 CRM-4913: Update Selected button not working on Opportunities List-->
                                            <p:commandButton value="Update Selected" immediate="true"  id="btnOppBlkUpdate"  
                                                             styleClass="btn btn-primary btn-xs"  
                                                             actionListener="#{dataSelfService.resetOppDefaultValues()}"
                                                             update="blkUpdateForm:bulkOppUpdate"
                                                             action="#{dataSelfService.showDialog(opportunities.selectedOppSave, 'OPP','NORMAL')}" 
                                                             disabled="#{opportunities.selectedOppSave.size() == 0}"
                                                             />
                                            <p:spacer width="4px" />
                                            <!--#5122: Opportunity List > Implement lazy loading-->
                                            <p:commandButton value="Close Selected" immediate="true"  id="btnOppMassClose"  
                                                             styleClass="btn btn-primary btn-xs"
                                                             update="blkUpdateForm:bulkOppUpdate"
                                                             action="#{dataSelfService.massClose(opportunities.selectedOppSave, 'OPP')}" 
                                                             disabled="#{opportunities.selectedOppSave.size() eq 0 or oppFilter.leftFilter.filterName eq 'CLOSED'}"
                                                             />

                                            <!--#6591: Aurinko CRMSync: Sync Conflicts-->
                                            <!--                                            #6801: Aurinko CRMSync: Sync Status-->
                                            <p:spacer width="4px" />
                                            <p:commandButton   value="Sync Status" class="btn btn-primary btn-xs"
                                                               onclick="window.open('../../crmsync/SyncConflicts.xhtml', '_self');"/>

                                        </h:panelGroup>
                                    </div>
                                    <div style="display: inline-flex; text-align: right">
                                        <div style="float:right; ">
                                            <!--                                        <h:commandLink title="Excel">
                                                                                        <p:graphicImage name="/images/excel.png" width="24"/>
                                                                                        <p:dataExporter type="xls" target="tblOpp" fileName="Opportunity_List" />
                                            
                                            
                                                                                    </h:commandLink>-->

                                            <!--#4893: Opportunities  List > Clear the info messages related to deprecated attribute-->
                                            <h:commandLink id="exp1" title="xlsx" actionListener="#{tableDataExporter.configure('OPPORTUNITY')}" >
                                                <p:graphicImage name="/images/excel27.png" width="24"/>
                                                <p:dataExporter type="xlsx" target="tblOpp"  fileName="Opportunity_List"  postProcessor="#{tableDataExporter.postProcessXLS}"  />  
                                            </h:commandLink>
                                            <!--                                            <p:commandButton    ajax="false" id="exp1" 
                                                                                                            image="imgClassName"  style="border: none!important;"  >
                                                                                            <p:graphicImage name="/images/excel.png" width="24"/>             
                                            
                                                                                            
                                                                                        </p:commandButton> -->



                                            <!--                                        <p:spacer width="4"/>
                                            <h:commandLink title="PDF">
                                                <p:graphicImage name="/images/pdf.png" width="24"/>
                                                <p:dataExporter type="pdf" target="tblOpp" fileName="tblOpp1"/>
                                                                                    </h:commandLink>-->
                                            <p:spacer width="4"/>
                                            <h:commandLink  title="CSV" >
                                                <p:graphicImage name="/images/csv.png" width="24"/>
                                                <p:dataExporter type="csv" target="tblOpp" fileName="Opportunity_List" />
                                            </h:commandLink>
                                        </div>
                                        <p:spacer width="4"/>
                                        <h:panelGroup>
                                            <p:selectOneMenu id="opFilter" value="#{oppFilter.topFilterExt.filterValue}"  class="ui-select-btn" style="width:150px">
                                                <f:selectItem itemLabel="None" itemValue="0"/>
                                                <f:selectItem itemLabel="#{custom.labels.get('IDS_FOLLOW_UP')} today or older" itemValue="1"/> 
                                                <f:selectItem itemLabel="Future #{custom.labels.get('IDS_FOLLOW_UP')}" itemValue="2"/>  
                                                <f:selectItem itemLabel="My #{custom.labels.get('IDS_SALES_TEAM')}" itemValue="3"/>
                                                <f:selectItem itemLabel="My #{custom.labels.get('IDS_OPPS')}" itemValue="4"/>
                                                <!--#5122: Opportunity List > Implement lazy loading-->
                                                <!--//23-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                                                <p:ajax event="change" listener="#{oppService.showFollowUp()}" update="tblOpp"  oncomplete="PF('tblOpp1').clearFilters();applyTopScrollbarOpp();"/>
                                            </p:selectOneMenu> 
                                        </h:panelGroup>
                                        <p:spacer width="4"/>
                                        <h:panelGroup>
                                            <!--#5122: Opportunity List > Implement lazy loading-->
                                            <p:inputText id="globalFilter" style="width:150px" placeholder="Search fields"/>
                                        </h:panelGroup>
                                        <p:spacer width="4"/>
                                        <p:column>
                                            <p:commandButton id="toggler" type="button" value="Columns" style="float: right" icon="fa fa-align-justify"/>
                                            <p:columnToggler datasource="tblOpp" trigger="toggler">
                                                <p:ajax event="toggle" listener="#{oppService.onToggle}"/>
                                            </p:columnToggler>
                                        </p:column>
                                        <p:spacer width="4"/>
                                        <p:column>
                                            <p:commandButton title="Save settings" 
                                                             icon="fa fa-save" oncomplete="applyTopScrollbarOpp()"
                                                             id="btnclonesave" update=":dtForm:tblOpp"  
                                                             action="#{oppService.saveSettings()}" 
                                                             class="btn-success btn-xs"  />
                                        </p:column>
                                        <p:spacer width="4"/>
                                        <p:column>
                                            <!--                                        3726    Opportunities > Retain pagination-update removed-->
                                            <!--                                            3791 opportunities clear filter not working-->
                                            <!--#5122: Opportunity List > Implement lazy loading-->
                                            <!--//23-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                                            <p:commandButton title="Clear Filters" 
                                                             icon="fa fa-times-circle"
                                                             id="btnfilterclear"  
                                                             oncomplete="applyTopScrollbarOpp();"
                                                             actionListener="#{oppFilter.clearFilter()}"
                                                             onclick="PF('tblOpp1').clearFilters();"
                                                             class="btn-danger btn-xs" />
                                        </p:column>
                                    </div>  
                                </f:facet>
                                <p:column exportable="false" toggleable="false" selectionMode="multiple" style="width: 10px;text-align: center;"/>

                                <!--Feature #3539:CRM-3938: Add Opp ID to Excel export of opportunities-->
                                <p:column headerText="Id"  style="display: none" exportable="true"  visible="false" toggleable="false">
                                    <p:outputLabel value="#{opp.oppId}"  />
                                </p:column>
                                <!--                                        #task 6286 CRM-4977 : add commas to "value" field and right justify the column-->
                                <c:forEach var="column" items="#{opportunities.listOppColumns}">
                                    <!--#1656 - CRM-3220: Hughes Cain - add column selector for opp last modified date - sharvani - 11-09-2020-->
                                    <!--26-11-2020: #2473: CRM-3588: you cannot filter opps by year - Condition added for follow up in filterBy clause-->
                                   <!--//01-06-2022 : 8033 : CRM-4972  MAKE HEADERS AT TOP OF PAGE STICK so you can scroll and still see what column you are--> 
                                   <!--//31-07-2023 : #11673 : ESCALATIONS CRM-7159   Companies: column adjustments reset after logging out-->
                                   <!--//09-08-2023 : #11832 : CRM-7217   Change wrapping properties on list view screens for all modules-->
                                   <p:column headerText="#{column.columnHeader}" id="#{column.columnName}"  style="#{column.columnName=='oppValue' ? 'text-align: right; ' : ''}width:#{column.columnWidth == 'null' or column.columnWidth == '10%' ? '200' : column.columnWidth}px"
                                              visible="#{column.columnVisibleFlag==1}" styleClass="ellipsis-column"  
                                              filterMatchMode="#{column.columnFilterMatchMode}" 
                                              filterBy="#{column.columnName=='insDate' || column.columnName=='updDate' ? globalParams.formatDateTime(opp[column.columnName], 'dt') : column.columnName=='oppFollowUp' ? globalParams.formatDateTime(opp[column.columnName], 'da') : opp[column.columnName]}" 
                                              sortBy="#{opp[column.columnName]}" exportable="#{column.columnVisibleFlag==1}"
                                              field="#{column.columnName}"> 
                                       
                                       <f:facet name="header">
                                        <h:outputText value="#{column.columnHeader}" style="display: inline"  title="#{column.columnHeader}" />
                                    </f:facet> 
                                        <!--<f:attribute name="myCol" value="column"/>-->
                                        <!--                                        #task 6286 CRM-4977 : add commas to "value" field and right justify the column-->
                                        <c:choose>
                                            <c:when test="#{column.columnName=='oppValue'}">

                                                <h:outputLabel  class="hvr"   value="#{opp.oppValue}" style="display: inline" title="#{opp.oppValue}">


                                                    <f:convertNumber groupingUsed="true"  maxIntegerDigits="15"  
                                                                     minFractionDigits="2" maxFractionDigits="2"/>

                                                    <p:ajax event="click" listener="#{opportunities.redirectPage(opp.oppId)}"/>

                                                </h:outputLabel>





                                            </c:when>
                                            <!--<c:when test="#{column.columnName=='oppCloseStatusLabel'}">-->
    <!--                                            <h:link outcome="OpportunityView.xhtml?opid=#{opp.oppId}"    
                                                        value="#{opp[column.columnName]==1?'Won':opp[column.columnName]==2?'Lost':'Open'}"  />-->
    <!--                                             <h:link outcome="OpportunityView.xhtml?opid=#{opp.oppId}"    
                                                        value="#{opp[column.columnName]}"  />
                                            </c:when>-->
                                            <c:when test="#{column.columnName=='oppFollowUp'}">
                                                <!--#3212 CRM-2010: Opportunities: Hawkins: opps creation date missing on export-->
                                                <h:link outcome="OpportunityView.xhtml?opid=#{opp.oppId}" value="#{globalParams.formatDateTime(opp[column.columnName],'da')}"
                                                        style="display: inline" title="#{globalParams.formatDateTime(opp[column.columnName],'da')}">
    <!--                                                <h:outputText value="#{opp[column.columnName]}">
              <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
          </h:outputText>-->
                                                    <!--#5122: Opportunity List > Implement lazy loading-->
                                                    <f:param name="frm" value="#{view.viewId}" />
                                                    <f:param name="moduleId" value="0" />
                                                </h:link>
                                            </c:when>
                                            <!--export-->
                                            <c:when test="#{column.columnName=='oppProtoDate'}">

                                                <h:link outcome="OpportunityView.xhtml?opid=#{opp.oppId}" value="#{globalParams.formatDateTime(opp[column.columnName],'da')}"
                                                        style="display: inline" title="#{globalParams.formatDateTime(opp[column.columnName],'da')}">
                                                    <!--#5122: Opportunity List > Implement lazy loading-->
                                                    <f:param name="frm" value="#{view.viewId}" />
                                                    <f:param name="moduleId" value="0" />
                                                </h:link>
                                            </c:when>
                                            <!--export-->
                                            <c:when test="#{column.columnName=='oppProdDate'}">

                                                <h:link outcome="OpportunityView.xhtml?opid=#{opp.oppId}" value="#{globalParams.formatDateTime(opp[column.columnName],'da')}"
                                                        style="display: inline"  title="#{globalParams.formatDateTime(opp[column.columnName],'da')}">
                                                    <!--#5122: Opportunity List > Implement lazy loading-->
                                                    <f:param name="frm" value="#{view.viewId}" />
                                                    <f:param name="moduleId" value="0" />
                                                </h:link>
                                            </c:when>
                                            <c:when test="#{column.columnName=='insDate'}">
                                                <h:link outcome="OpportunityView.xhtml?opid=#{opp.oppId}" value="#{globalParams.formatDateTime(opp[column.columnName],'dt')}"
                                                        style="display: inline" title="#{globalParams.formatDateTime(opp[column.columnName],'dt')}">
                                                    <!--<h:outputLabel   value="#{rFUtilities.convertFromUTC(opp[column.columnName])}" >-->
                                                        <!--<f:convertDateTime pattern="#{globalParams.dateTimeFormat}" for="#{column.columnName}"/>-->
                                                    <!--</h:outputLabel>-->
                                                    <!--#5122: Opportunity List > Implement lazy loading-->
                                                    <f:param name="frm" value="#{view.viewId}" />
                                                    <f:param name="moduleId" value="0" />
                                                </h:link>
                                            </c:when>
                                            <!--                                        <c:otherwise>
                                                                                        <h:link outcome="OpportunityView.xhtml?opid=#{opp.oppId}"    
                                                                                                value="#{opp[column.columnName]}"  />
                                                                                    </c:otherwise>-->
                                            <!--#1656 - CRM-3220: Hughes Cain - add column selector for opp last modified date - sharvani - 11-09-2020-->
                                            <c:when test="#{column.columnName=='updDate'}">
                                                <h:link outcome="OpportunityView.xhtml?opid=#{opp.oppId}" value="#{globalParams.formatDateTime(opp[column.columnName],'dt')}"
                                                        style="display: inline" title="#{globalParams.formatDateTime(opp[column.columnName],'dt')}">
                                                    <!--#5122: Opportunity List > Implement lazy loading-->
                                                    <f:param name="frm" value="#{view.viewId}" />
                                                    <f:param name="moduleId" value="0" />
                                                </h:link>
                                            </c:when>
                                            <c:otherwise>
                                                <h:link outcome="OpportunityView.xhtml?opid=#{opp.oppId}"    
                                                        value="#{opp[column.columnName]}" style="display: inline" title="#{opp[column.columnName]}" >
                                                    <!--#5122: Opportunity List > Implement lazy loading-->
                                                    <f:param name="frm" value="#{view.viewId}" />
                                                    <f:param name="moduleId" value="0" />
                                                </h:link>
                                            </c:otherwise>
                                        </c:choose>   
                                    </p:column>
                                </c:forEach> 
                            </p:dataTable>
                        </p:panel>
                        <!--Retain Opp Filter-->
                        <p:remoteCommand name="callRetainFilter" actionListener="#{oppFilter.retainFilter()}"  />
                        <p:remoteCommand name="callClearFilter" actionListener="#{oppFilter.clearFilter()}"  />
                        <h:inputHidden id="inpTxt_id_filterCust" value="#{oppFilter.filterCust}" />
                        <h:inputHidden id="inpTxt_id_filterPrinci" value="#{oppFilter.filterPrinci}" />
                        <h:inputHidden id="inpTxt_id_filterDistri" value="#{oppFilter.filterDistri}" />                    
                        <h:inputHidden id="inpTxt_id_filterOppSmanName" value="#{oppFilter.filterOppSmanName}" />
                        <h:inputHidden id="inpTxt_id_filterActivity" value="#{oppFilter.filterActivity}" />                    
                        <h:inputHidden id="inpTxt_id_filterStatus" value="#{oppFilter.filterStatus}" />
                        <h:inputHidden id="inpTxt_id_filterProgram" value="#{oppFilter.filterProgram}" />
                        <h:inputHidden id="inpTxt_id_filterNextStep" value="#{oppFilter.filterNextStep}" />
                        <h:inputHidden id="inpTxt_id_filterFollowUp" value="#{oppFilter.filterFollowUp}" />
                        <h:inputHidden id="inpTxt_id_filterOppPriority" value="#{oppFilter.filterOppPriority}" />
                        <h:inputHidden id="inpTxt_id_filterOppPotential" value="#{oppFilter.filterOppPotential}" />                     
                        <h:inputHidden id="inpTxt_id_filterValue" value="#{oppFilter.filterValue}" />
                        <h:inputHidden id="inpTxt_id_filterOppEau" value="#{oppFilter.filterOppEau}" />
                        <h:inputHidden id="inpTxt_id_filterOppProtoDate" value="#{oppFilter.filterOppProtoDate}" />
                        <h:inputHidden id="inpTxt_id_filterOppProdDate" value="#{oppFilter.filterOppProdDate}" />
                        <h:inputHidden id="inpTxt_id_filterOppCloseStatusLabel" value="#{oppFilter.filterOppCloseStatusLabel}" />
                        <h:inputHidden id="inpTxt_id_filterOwnerName" value="#{oppFilter.filterOwnerName}" />
                        <h:inputHidden id="inpTxt_id_filterOppParties" value="#{oppFilter.filterOppParties}" />
                        <h:inputHidden id="inpTxt_id_filterOppLines" value="#{oppFilter.filterOppLines}" />
                        <h:inputHidden id="inpTxt_id_filterOppTypeName" value="#{oppFilter.filterOppTypeName}" />
                        <h:inputHidden id="inpTxt_id_filterOppCloseReason" value="#{oppFilter.filterOppCloseReason}" />
    <!--                    <h:inputHidden id="inpTxt_id_paginationNumber" value="#{oppFilter.first}" />-->


                    </div>
                </h:form>
            </div>

            <!--#5369: CRM-4591: Search is displaying OPPs wrong and other screens are acting up as well-->
            <p:dialog widgetVar="inProgressDlg1" closable="false" modal="true" header="Message" resizable="false"  >

                <h:form id ="frmPoll">
                    <h:outputText value="Updating..." id="txtStatus" />
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif" id="loadingImg1"   />
                        <p:spacer width="5" />
                        <!--<p:outputLabel value="Processing..." />-->
                        <br /><br />
                    </p:outputPanel>
                </h:form>
            </p:dialog>

            <!--3726    Opportunities > Retain pagination-->
            <!--<ui:include src="OppFilter.xhtml"/>-->

            <ui:include src="/bulkupdate/DataSelfServiceDlg.xhtml"/>



            <!-- #4729: Opportunities: Mass Close -->
            <p:dialog  header="Close #{custom.labels.get('IDS_OPP')}" 
                       widgetVar="dlgMassCloseOpp" class="dialogCSS" modal="true">
                <p:ajax event="close" process="@this" update="frmMassClose" listener="#{oppService.massOppClsCancelSts()}"/> 
                <h:form id="frmMassClose">
                    <p:growl id="grlDlgMassCloseOpp" globalOnly="false"/>
                    <p:focus context="massOppclstat" />
                    <p:panel id="pnlMassCloseOpp" class="pnldlgs" style="border:none !important; " >
                        <p:panelGrid style="border: none">
                            <p:row>
                                <p:column colspan="4">
                                    <p:outputLabel value="Status"/>
                                </p:column>
                                <p:column>
                                    <p:selectOneRadio  id="massOppclstat" value="#{oppService.massOppCloseStatus}"
                                                       requiredMessage="Please select closing status" 
                                                       required="true" >
                                        <f:selectItem   itemValue="1" itemLabel="Won"/> 
                                        <f:selectItem itemValue="2" itemLabel="Lost"/>
                                        <f:selectItem itemValue="3" itemLabel="Cancelled"/>
                                        <p:ajax event="change" process="@this" update="frmMassClose:pgCTDlgMassOppCls frmMassClose:pnlFailedReeason" />
                                    </p:selectOneRadio>
                                </p:column>
                                <p:column colspan="4">
                                    <p:outputLabel value="Date"/>
                                </p:column>
                                <p:column>
                                    <p:calendar  id="massOppdtClose"  pattern="#{globalParams.dateFormat}" style="width:100%" 
                                                 converterMessage="Closing date not valid" required="true" requiredMessage="Closing Date required"
                                                 value="#{oppService.massOppCloseDateVal}" immediate="true">
                                        <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                    </p:calendar>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column colspan="4">
                                    <p:outputLabel id="massOppClsReason" value="Closed Reason" />
                                </p:column>
                                <p:column colspan="8">
                                    <h:panelGroup class="ui-inputgroup"  >  
                                        <p:inputText id="massOppClsReasn" value="#{oppService.massOppCloseReason}" style="width:90%" maxlength="200" />
                                        <p:commandButton  icon="fa fa-search" title="Choose reason" immediate="true" 
                                                          actionListener="#{oppCloseReasonMstService.listOppCloseReason()}"
                                                          oncomplete="PF('dlgMassOppClsRsn').show();PF('massOppCloseRsnDTl').clearFilters();"
                                                          update="frmMassOppClsRsndlg:dTableMassOppClsRsnLokup"
                                                          styleClass="btn-info btn-xs" />
                                    </h:panelGroup>
                                </p:column>

                            </p:row>
                        </p:panelGrid>

                        <!-- #7195: Activity/Close Status Linking: Close Selected - Cancelled - poornima -->
                        <p:panelGrid id="pnlFailedReeason" style="border: none">
                            <p:row rendered="#{oppService.massOppCloseStatus == 2 or oppService.massOppCloseStatus == 3}">
                                <p:column colspan="4">
                                    <p:outputLabel id="failedreason" value="Failed Reason" />
                                </p:column>
                                <p:column colspan="8">
                                    <h:panelGroup class="ui-inputgroup"  >                                          
                                        <p:inputText id="massOppFldReasn" value="#{oppService.massOppFailedReason}" style="width:90%;margin-left: 4px;padding-right: 270px;" maxlength="200" />                                        
                                        <p:commandButton  icon="fa fa-search" title="Choose reason" immediate="true" 
                                                          actionListener="#{oppFailedReasonMstService.listOppFailedReason()}"
                                                          oncomplete="PF('dlgMassOppFldRsn').show();PF('massOppFailedRsnDTl').clearFilters();"  
                                                          update="frmMassOppFldRsndlg:dTableMassOppFldRsnLokup"
                                                          styleClass="btn-info btn-xs" />
                                    </h:panelGroup>
                                </p:column>

                            </p:row>
                        </p:panelGrid>
                        <p:panelGrid id="pgCTDlgMassOppCls" style="border: none">
                            <p:row rendered="#{oppService.massOppCloseStatus == 1}">
                                <p:column colspan="4">
                                    <strong>Commissionable Transaction:</strong>
                                </p:column>
                            </p:row>
                            <p:row rendered="#{oppService.massOppCloseStatus == 1}">
                                <p:column colspan="4">
                         <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->    
                                    <p:outputLabel value="#{custom.labels.get('IDS_COMMISSION')} Rate " />
                                </p:column>
                                <p:column>
                                    <p:inputNumber decimalPlaces="2" maxValue="99.99"  id="massOppClstCommPrice" value="#{oppService.massOppTrnhCommRate}"    maxlength="4" converterMessage="Must be a signed decimal number." >
                                    </p:inputNumber>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:panel>
                          <!--#8737   CRM-6024: Option to clear actions on Close-->
            <p:panel id="clearPendingCheckbox"  style="border:none !important; " >
                <p:selectBooleanCheckbox   id="actPendingCheckbox" value="#{opportunities.clearPendingActions}"  >
                        <p:ajax  event="change" listener="#{opportunities.onChecked()}" />
                    </p:selectBooleanCheckbox>
                    <p:spacer width="10px"/>
                    <p:column>    
                        <p:outputLabel value="Cancel all pending actions ?"/>
                    </p:column>
              </p:panel>
                    <!--#5122: Opportunity List > Implement lazy loading-->
                    <p:commandButton value="Save" action="#{oppService.setMassOppValue(opportunities.selectedOppSave,opportunities.clearPendingActions)}"
                                     onclick="PF('dlgMassOppClsProc').show()" oncomplete="PF('dlgMassOppClsProc').hide()"
                                     class="btn btn-xs btn-success" update="frmMassClose:grlDlgMassCloseOpp dtForm"/>
                    <p:spacer width="4"/> 
                    <p:commandButton value="Cancel" process="@this"    
                                     class="btn btn-xs btn-warning" oncomplete="PF('dlgMassCloseOpp').hide();"  />
                    <br/>
                    <br/>
                    <strong>Note: Closed #{custom.labels.get('IDS_OPP')} will not get updated</strong>

                </h:form>
            </p:dialog>

            <!-- #4729: Opportunities: Mass Close processing dialog -->
            <p:dialog widgetVar="dlgMassOppClsProc" closable="false" modal="true" header="Message" resizable="false" height="100" width="400" >
                <h:form>
                    <div  style=" text-align: center">
                        <p:outputPanel >
                            <br />
                            <h:graphicImage  library="images" name="ajax-loader.gif"   />
                            <p:spacer width="5" />
                            <p:outputLabel value="Processing..." />
                            <br /><br />
                        </p:outputPanel>
                    </div>
                </h:form>
            </p:dialog>

            <p:dialog header="#{custom.labels.get('IDS_OPP')} Close Reason Lookup" widgetVar="dlgMassOppClsRsn" width="450"  resizable="false" 
                      modal="true" id='massOppClsRsndlg'>
                <!--//    Task #461: CRM-2505:  Opportunities > Opp Close Reason lookup-->

                <h:form id="frmMassOppClsRsndlg">

                    <p:dataTable value="#{oppCloseReasonMstService.oppCloseReasonList}" var="oppCloseRsn"
                                 rowKey="#{oppCloseRsn.recId}" selectionMode="single"
                                 id="dTableMassOppClsRsnLokup" rowsPerPageTemplate="5,10,15" 
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 paginatorPosition="top" styleClass="dt-lookup" 
                                 widgetVar="massOppCloseRsnDTl" style="margin-top:-5px"
                                 selection="#{oppCloseReasonMstService.selectedOppCloseRsnMst}">

                        <p:ajax event="rowSelect" oncomplete="PF('dlgMassOppClsRsn').hide();" listener="#{oppCloseReasonMstService.rsnMassOppClose(oppCloseReasonMstService.selectedOppCloseRsnMst.oppCloseReason)}"
                                update="frmMassClose:massOppClsReasn"/>


                        <p:column filterBy="#{oppCloseRsn.oppCloseReason}"  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_OPP')} Close Reason"  >
                            <h:outputText value="#{oppCloseRsn.oppCloseReason}"/>
                        </p:column>
                    </p:dataTable>
                </h:form>


            </p:dialog>

            <!-- #7195: Activity/Close Status Linking: Close Selected - Cancelled - poornima -->
            <p:dialog header="#{custom.labels.get('IDS_OPP')} Failed Reason Lookup" widgetVar="dlgMassOppFldRsn" width="450"  resizable="false" 
                      modal="true" id='massOppFldRsndlg'>
                <!--//    Task #461: CRM-2505:  Opportunities > Opp Close Reason lookup-->

                <h:form id="frmMassOppFldRsndlg">

                    <p:dataTable value="#{oppFailedReasonMstService.oppFailedReasonList}" var="oppFailedRsn"
                                 rowKey="#{oppFailedRsn.recId}" selectionMode="single"
                                 id="dTableMassOppFldRsnLokup" rowsPerPageTemplate="5,10,15" 
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 paginatorPosition="top" styleClass="dt-lookup" 
                                 widgetVar="massOppFailedRsnDTl" style="margin-top:-5px"
                                 selection="#{oppFailedReasonMstService.selectedOppFailedRsnMst}">

                        <p:ajax event="rowSelect" oncomplete="PF('dlgMassOppFldRsn').hide();" listener="#{oppFailedReasonMstService.rsnMassOppFailed(oppFailedReasonMstService.selectedOppFailedRsnMst.oppFailedReason)}"
                                update="frmMassClose:massOppFldReasn"/>


                        <p:column filterBy="#{oppFailedRsn.oppFailedReason}"  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_OPP')} Failed Reason"  >
                            <h:outputText value="#{oppFailedRsn.oppFailedReason}"/>
                        </p:column>
                    </p:dataTable>
                </h:form>


            </p:dialog>
            <script>

                //20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
function applyTopScrollbarOpp() {
    var tableExists = document.getElementById('dtForm:tblOpp') === null;
                  if(tableExists){
                      return;
                  }
    var dataTable = PF('tblOpp1').jq;
    var scrollableHeaderBox = dataTable.find('.ui-datatable-scrollable-header-box');
    var scrollableBody = dataTable.find('.ui-datatable-scrollable-body');
    var scrollableView = dataTable.find('.ui-datatable-scrollable-view');

    // Check if the scrollbar already exists
    if ($('.top-scrollbar').length === 0) {
        // Create the custom top scrollbar
        var topScrollbar = document.createElement('div');
        topScrollbar.className = 'top-scrollbar';
        var innerDiv = document.createElement('div');
        var width = scrollableBody.find('table').width();
        if(width > 1338){
            if(1358 > width){
                width = 1360;
            }
        }
        console.log('width=',width)
        innerDiv.style.width = width + 'px';
        topScrollbar.appendChild(innerDiv);

        // Insert the top scrollbar above the scrollable header box
        scrollableHeaderBox.before(topScrollbar);

        // Synchronize scroll positions
        topScrollbar.addEventListener('scroll', function () {
            scrollableBody.scrollLeft(topScrollbar.scrollLeft);
            scrollableHeaderBox.scrollLeft(topScrollbar.scrollLeft);
        });

        scrollableBody.on('scroll', function () {
            topScrollbar.scrollLeft = scrollableBody.scrollLeft();
            scrollableHeaderBox.scrollLeft(scrollableBody.scrollLeft());
        });

        scrollableHeaderBox.on('scroll', function () {
            topScrollbar.scrollLeft = scrollableHeaderBox.scrollLeft();
            scrollableBody.scrollLeft(scrollableHeaderBox.scrollLeft());
        });
    }
}

// Call applyTopScrollbar on DOM load
document.addEventListener('DOMContentLoaded', function () {
    applyTopScrollbarOpp();
});

                function keyHandler() {
                    var searchValue = document.getElementById('dtForm:globalFilter1').value;
                    document.getElementById('dtForm:tblOpp:globalFilter').value = searchValue;
                    PF('tblOpp1').filter();
                }

                function openTab(id, winName, event) {
                    var url1;
                    event.preventDefault();
                    if (id !== '') {
                        url1 = 'OpportunityView.xhtml?opid=' + id;
                    } else {
                        url1 = 'OpportunityView.xhtml';
                    }
                    this.target = '_blank';
                    var win = window.open(url1, winName, '', false);
                    win.focus();
                }



                //       $(document).ready(function (){
                //            //Get the scrollheight
                //        var s = jQuery('#tblOpp .ui-datatable-scrollable-body').prop('scrollHeight');
                //
                //        //Get total size of datatable
                //        var o = jQuery('#tblOpp .ui-datatable-scrollable-body').prop('offsetHeight');
                //
                //        //calculate how many times it can scrolldown to set your timer
                //        var t = Math.ceil(s/o);
                //        //Excute scrolldown animation (max scrolldown is  scrollHeight - offsetHeight)
                //           $('#tblOpp .ui-datatable-scrollable-body').animate({scrollTop:s-0}, t*1000);     
                //           alert("s"+s+"o"+o+"t"+t);
                //       });

//#5122: Opportunity List > Implement lazy loading
                function delay(callback, ms) {
                    var timer = 0;
                    return function () {
                        var context = this, args = arguments;
                        clearTimeout(timer);
                        timer = setTimeout(function () {
                            callback.apply(context, args);
                        }, ms || 0);
                    };
                }
                $('#dtForm\\:tblOpp\\:globalFilter').on("input change keyup paste propertychange ", delay(function () {
                    PF('tblOpp1').filter();
                }, 1000));
            </script>
            <style>
                .ui-panelgrid tr, .ui-panelgrid td{
                    border:0!important;
                }
                .hvr:hover{
                    text-decoration:  underline;
                }
                /*<!--//12-07-2022 :  #8446 : ESCALATIONS CRM-5903 Epic/Tina Grohn/Opportunity List Collapsing drops the list to the bottom of t-->*/
                .right-column-collapse {
                    float: right;
                    width: 97%;
                    padding: 2px;
                    margin: 0;     
                    /*background: rebeccapurple;*/            
                }
                /*<!--//25-05-2022 : 8051 : Opportunity: Left Panel Collapsible button-->*/
                .right-column1 {
                    float: right;
                    width: 80%;
                    position: relative;
                    padding: 2px;
                    margin: 0;     
                    /*background: rebeccapurple;*/            
                }
               /*<!--//01-06-2022 : 8033 : CRM-4972  MAKE HEADERS AT TOP OF PAGE STICK so you can scroll and still see what column you are--> */
                .content-header {
                    height: 51px!important;
                } 
                .content {
                    padding: 0!important;
                    margin-top: -9px!important;
                }
                /*<!--//01-06-2022 : 8033 : CRM-4972  MAKE HEADERS AT TOP OF PAGE STICK so you can scroll and still see what column you are--> */
                /*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->*/
                @media only screen and (min-height: 600px) and (max-height: 900px) {
    .ui-datatable-scrollable-body {
        height: 65vh;
        outline: 0px;
    }
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
@media only screen and (min-height: 901px) and (max-height: 1152px) {
    .ui-datatable-scrollable-body {
        height: 70vh;
        outline: 0px;
    }
}

@media only screen and (min-height: 1153px) and (max-height: 1440px) {
    .ui-datatable-scrollable-body {
        /*//21-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
        height: 70vh;
        outline: 0px;
    }
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
@media only screen and (min-height: 1500px) and (max-height: 1640px) {
    .ui-datatable-scrollable-body {
        /*//21-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
        height: 70vh;
        outline: 0px;
    }
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-body {
    outline: 0px;
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-header *,
.ui-datatable-scrollable-theadclone * {
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    /*width: 100%;*/
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
    width: 15px;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable table {
    /* table-layout: fixed !important; */
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-theadclone {
    visibility: collapse !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-tablewrapper {
    overflow: initial !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-paginator {
    background-color: #dedede !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.q {
    width: 100% !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.p {
    width: 80% !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-header-box,
.ui-datatable-scrollable-footer-box {
/*    overflow-x: auto !important;
    overflow-y: hidden !important;*/
    position: relative;
    /*margin-left: 0px !important;*/
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-view {
    position: relative;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.top-scrollbar {
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    height: 16px; /* Adjust height as needed */
    overflow-x: auto;
    overflow-y: hidden;
    z-index: 10; /* Ensure it appears above the DataTable */
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
@-moz-document url-prefix() {
    .top-scrollbar {
        height: 12px; /* Height for Firefox */
    }
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.top-scrollbar div {
    width: 100%; /* Match the width of the DataTable */
    height: 20px; /* Match the height of the scrollbar container */
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-header {
    z-index: 20; /* Ensure it appears above the DataTable */
}

            </style>
        </f:view>
    </ui:define> 

</ui:composition>