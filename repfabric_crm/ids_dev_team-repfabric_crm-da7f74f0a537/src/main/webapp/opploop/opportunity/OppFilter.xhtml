<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: OppFilter.xhtml
// Author: Nisha
//*********************************************************
/*
*
* Copyright (c) 2017 Indea Design Systems Pvt Ltd.
*/-->
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets">  
    <script type="text/javascript">
//        var myEvent = window.attachEvent || window.addEventListener;
//        var chkevent = window.attachEvent ? 'onbeforeunload' : 'beforeunload'; /// make IE7, IE8 compitable
//
//        myEvent(chkevent, function(e) { // For >=IE7, Chrome, Firefox
//            var confirmationMessage = 'Are you sure to leave the page?';  // a space
//            (e || window.event).returnValue = confirmationMessage;
//            return confirmationMessage;
//        });

        jQuery(document).ready(function() {
//            $('body').append("window width is" + $(window).height());

            $("#tblOpp").css({'height': ($(window).height() - 100) + 'px'});




            $(window).resize(function() {



                if ($(this).width() >= 1299) {
                    console.log("775px");
                    // code
                }
                if ($(this).width() >= 1299) {
                    console.log("600px");
                    // code
                }

            });
            $("#hide").click(function() {
                $("#p").hide();
                $("#q").addClass("q");
                $("#q").removeClass("p");

            });
            $("#show").click(function() {
                $("#p").show();
                $("#q").addClass("p");
                $("#q").removeClass("q");

            });
        });


//        jQuery(document).ready(function() {
//            tblOpp1.paginator.rppSelect.change(function() {
//                console.log()
//                persistRows([{name: 'rows', value: this.value}]);
//            });
//        });

        window.onload = function() {
            applyFilters();
        };


//        window.unload=function() {
//         
//            console.log('Bye.');
//        }

        $(window).on('beforeunload', function() {
            setFilterValues();
        });

//        window.onbeforeunload = function() {
//            setFilterValues();
//            return 'Are you sure you want to laaeave?';
//        };

        function applyFilters() {
//            PF('blockUI').show();
            //Left side 
            getMenuFilter();

            //Datatable columns
            getColumnFilters();
//            PF('blockUI').hide();
        }

        function setFilterValues() {


            //Left side menu filter
            var left_filter;
            var searchFilter = document.getElementById('left_menu:dt:left_filter:filter');

            if (searchFilter !== null) {
                left_filter = !$(searchFilter).val() ? '' : $(searchFilter).val();
            }

            var callPattFilter = document.getElementById("left_menu:dt1:left_filter_call_patt:filter");
            if (callPattFilter !== null) {
                left_filter = !$(callPattFilter).val() ? '' : $(callPattFilter).val();
            }

            //Customer
            var cust = document.getElementById('dtForm:tblOpp:custName:filter').value;

            cust = cust === null ? '' : cust;

            //Principal
            var princi = document.getElementById('dtForm:tblOpp:principalName:filter').value;
            princi = princi === null ? '' : princi;

            var distri = document.getElementById("dtForm:tblOpp:distriName:filter").value;
            distri = distri === null ? '' : distri;

            var sman = document.getElementById('dtForm:tblOpp:oppSmanName:filter').value;
            sman = sman === null ? '' : sman;
            //Activity
            var activity = document.getElementById('dtForm:tblOpp:oppActivity:filter').value;
            activity = activity === null ? '' : activity;
            //Status
            var status = document.getElementById('dtForm:tblOpp:oppStatus:filter').value;
            status = status === null ? '' : status;
            //Program
            var program = document.getElementById('dtForm:tblOpp:oppCustProgram:filter').value;
            program = program === null ? '' : program;
            //Next Step
            var next_step = document.getElementById('dtForm:tblOpp:oppNextStep:filter').value;
            next_step = next_step === null ? '' : next_step;
            //Follow up
            var follow_up = document.getElementById('dtForm:tblOpp:oppFollowUp:filter').value;
            follow_up = follow_up === null ? '' : follow_up;

            var priority = document.getElementById('dtForm:tblOpp:oppPriority:filter').value;
            priority = priority ? '' : priority;

            var potential = document.getElementById('dtForm:tblOpp:oppPotential:filter').value;
            potential = potential === null ? '' : potential;
            //Opp Value
            var opp_value = document.getElementById('dtForm:tblOpp:oppValue:filter').value;
            opp_value = opp_value === null ? '' : opp_value;

            var eau = document.getElementById('dtForm:tblOpp:oppEau:filter').value;
            eau = eau === null ? '' : eau;

            var protod = document.getElementById('dtForm:tblOpp:oppProtoDate:filter').value;
            protod = protod === null ? '' : protod;

            var prodd = document.getElementById('dtForm:tblOpp:oppProdDate:filter').value;
            prodd = prodd === null ? '' : prodd;

            var clsts = document.getElementById('dtForm:tblOpp:oppCloseStatusLabel:filter').value;
            clsts = clsts === null ? '' : clsts;

            var oname = document.getElementById('dtForm:tblOpp:ownerName:filter').value;
            oname = oname === null ? '' : oname;

            var oppp = document.getElementById('dtForm:tblOpp:oppParties:filter').value;
            oppp = oppp === null ? '' : oppp;

            //Line Items 
            var opptype = document.getElementById('dtForm:tblOpp:oppTypeName:filter').value;
            opptype = opptype === null ? '' : opptype;

            var line_items = document.getElementById('dtForm:tblOpp:oppLines:filter').value;
            line_items = line_items === null ? '' : line_items;

            var reason = document.getElementById('dtForm:tblOpp:oppCloseReason:filter').value;
            reason = reason === null ? '' : reason;
            //Populate filter map
            var filters = [
                {name: 'search_filter', value: left_filter},
                {name: 'inpTxt_id_filterCust', value: cust},
                {name: 'inpTxt_id_filterPrinci', value: princi},
                {name: 'inpTxt_id_filterDistri', value: distri},
                {name: 'inpTxt_id_filterOppSmanName', value: sman},
                {name: 'inpTxt_id_filterActivity', value: activity},
                {name: 'inpTxt_id_filterStatus', value: status},
                {name: 'inpTxt_id_filterProgram', value: program},
                {name: 'inpTxt_id_filterNextStep', value: next_step},
                {name: 'inpTxt_id_filterFollowUp', value: follow_up},
                {name: 'inpTxt_id_filterOppPriority', value: priority},
                {name: 'inpTxt_id_filterOppPotential', value: potential},
                {name: 'inpTxt_id_filterValue', value: opp_value},
                {name: 'inpTxt_id_filterOppEau', value: eau},
                {name: 'inpTxt_id_filterOppProtoDate', value: protod},
                {name: 'inpTxt_id_filterOppProdDate', value: prodd},
                {name: 'inpTxt_id_filterOppCloseStatusLabel', value: clsts},
                {name: 'inpTxt_id_filterOwnerName', value: oname},
                {name: 'inpTxt_id_filterOppParties', value: oppp},
                {name: 'inpTxt_id_filterOppTypeName', value: opptype},
                {name: 'inpTxt_id_filterOppLines', value: line_items},
                {name: 'inpTxt_id_filterOppCloseReason', value: reason}
            ];

            //Call remote procedure - pass filters
            callRetainFilter(filters);
        }



        function getMenuFilter() {
            var left_search = document.getElementById("left_menu:search_filter").value;
            if (left_search) {
                var single_search = document.getElementById("left_menu:dt:left_filter:filter");
                var multiple_search = document.getElementById("left_menu:dt1:left_filter_call_patt:filter");
                if (single_search) {
                    $(single_search).val(left_search);
                    PF('wdtside').filter();
                }

                if (multiple_search) {
                    $(multiple_search).val(left_search);
                    PF('callPattDT').filter();
                }
            }

        }


        function getColumnFilters() {

            var cust = document.getElementById("dtForm:inpTxt_id_filterCust").value;
            if (cust) {
                document.getElementById("dtForm:tblOpp:custName:filter").value = cust;
            }

            var princi = document.getElementById("dtForm:inpTxt_id_filterPrinci").value;
            if (princi) {
                document.getElementById("dtForm:tblOpp:principalName:filter").value = princi;
            }

            var distri = document.getElementById("dtForm:inpTxt_id_filterDistri").value;
            if (distri) {
                document.getElementById("dtForm:tblOpp:distriName:filter").value = distri;
            }

            var sman = document.getElementById('dtForm:inpTxt_id_filterOppSmanName').value;
            if (sman) {
                document.getElementById("dtForm:tblOpp:oppSmanName:filter").value = sman;
            }


            var activity = document.getElementById('dtForm:inpTxt_id_filterActivity').value;
            if (activity) {
                document.getElementById("dtForm:tblOpp:oppActivity:filter").value = activity;
            }

            var status = document.getElementById('dtForm:inpTxt_id_filterStatus').value;
            if (status) {
                document.getElementById("dtForm:tblOpp:oppStatus:filter").value = status;
            }

            var program = document.getElementById('dtForm:inpTxt_id_filterProgram').value;
            if (program) {
                document.getElementById("dtForm:tblOpp:oppCustProgram:filter").value = program;
            }

            var next_step = document.getElementById('dtForm:inpTxt_id_filterNextStep').value;
            if (next_step) {
                document.getElementById("dtForm:tblOpp:oppNextStep:filter").value = next_step;
            }

            var follow_up = document.getElementById('dtForm:inpTxt_id_filterFollowUp').value;
            if (follow_up) {
                document.getElementById("dtForm:tblOpp:oppFollowUp:filter").value = follow_up;
            }

            var priority = document.getElementById('dtForm:inpTxt_id_filterOppPriority').value;
            if (priority) {
                document.getElementById("dtForm:tblOpp:oppPriority:filter").value = priority;
            }

            var potential = document.getElementById('dtForm:inpTxt_id_filterOppPotential').value;
            if (potential) {
                document.getElementById("dtForm:tblOpp:oppPotential:filter").value = potential;
            }

            var opp_value = document.getElementById('dtForm:inpTxt_id_filterValue').value;
            if (opp_value) {
                document.getElementById("dtForm:tblOpp:oppValue:filter").value = opp_value;
            }

            var eau = document.getElementById('dtForm:inpTxt_id_filterOppEau').value;
            if (eau) {
                document.getElementById("dtForm:tblOpp:oppEau:filter").value = eau;
            }

            var protod = document.getElementById('dtForm:inpTxt_id_filterOppProtoDate').value;
            if (protod) {
                document.getElementById("dtForm:tblOpp:oppProtoDate:filter").value = protod;
            }

            var prodd = document.getElementById('dtForm:inpTxt_id_filterOppProdDate').value;
            if (prodd) {
                document.getElementById("dtForm:tblOpp:oppProdDate:filter").value = prodd;
            }

            var clsts = document.getElementById('dtForm:inpTxt_id_filterOppCloseStatusLabel').value;
            if (clsts) {
                document.getElementById("dtForm:tblOpp:oppCloseStatusLabel:filter").value = clsts;
            }

            var oname = document.getElementById('dtForm:inpTxt_id_filterOwnerName').value;
            if (oname) {
                document.getElementById("dtForm:tblOpp:ownerName:filter").value = oname;
            }

            var oppp = document.getElementById('dtForm:inpTxt_id_filterOppParties').value;
            if (oppp) {
                document.getElementById("dtForm:tblOpp:oppParties:filter").value = oppp;
            }

            var opptype = document.getElementById('dtForm:inpTxt_id_filterOppTypeName').value;
            if (opptype) {
                document.getElementById("dtForm:tblOpp:oppTypeName:filter").value = opptype;
            }

//            var line_item_ele = document.getElementById("dtForm:inpTxt_id_filterLineItems");
//            if (line_item_ele !== null) {
//                $(line_item_ele).val(document.getElementById("dtForm:tblOpp:id_filterOppLines:filter").value);
//            }



            var line_items = document.getElementById('dtForm:inpTxt_id_filterOppLines').value;
            if (line_items) {
                document.getElementById("dtForm:tblOpp:oppLines:filter").value = line_items;
            }

            var reason = document.getElementById('dtForm:inpTxt_id_filterOppCloseReason').value;
            if (reason) {
                document.getElementById("dtForm:tblOpp:oppCloseReason:filter").value = reason;
            }



            PF('tblOpp1').filter();
        }






















        function clearFilterValues() {


            //Left side menu filter
            var left_filter;
            var searchFilter = document.getElementById('left_menu:dt:left_filter:filter');
            if (searchFilter) {
                left_filter = !$(searchFilter).val() ? '' : $(searchFilter).val();
            }

            var callPattFilter = document.getElementById("left_menu:dt1:left_filter_call_patt:filter");
            if (callPattFilter) {
                left_filter = !$(callPattFilter).val() ? '' : $(callPattFilter).val();
            }

//            console.log("left_filter = " + left_filter);
            //Customer
            var cust = document.getElementById('dtForm:tblOpp:custName:filter').value;
            cust = !cust ? '' : '';

            //Principal
            var princi = document.getElementById('dtForm:tblOpp:principalName:filter').value;
            princi = !princi ? '' : '';

            var distri = document.getElementById("dtForm:tblOpp:distriName:filter").value;
            distri = !distri ? '' : '';


            var sman = document.getElementById('dtForm:tblOpp:oppSmanName:filter').value;
            sman = !sman ? '' : '';

            //Activity
            var activity = document.getElementById('dtForm:tblOpp:oppActivity:filter').value;
            activity = !activity ? '' : '';
            //Status
            var status = document.getElementById('dtForm:tblOpp:oppStatus:filter').value;
            status = !status ? '' : '';
            //Program
            var program = document.getElementById('dtForm:tblOpp:oppCustProgram:filter').value;
            program = !program ? '' : '';
            //Next Step
            var next_step = document.getElementById('dtForm:tblOpp:oppNextStep:filter').value;
            next_step = !next_step ? '' : '';
            //Follow up
            var follow_up = document.getElementById('dtForm:tblOpp:oppFollowUp:filter').value;
            follow_up = !follow_up ? '' : '';



            var priority = document.getElementById('dtForm:tblOpp:oppPriority:filter').value;
            priority = !priority ? '' : '';


            var potential = document.getElementById('dtForm:tblOpp:oppPotential:filter').value;
            potential = !potential ? '' : '';

            //Opp Value
            var opp_value = document.getElementById('dtForm:tblOpp:oppValue:filter').value;
            opp_value = !opp_value ? '' : '';


            var eau = document.getElementById('dtForm:tblOpp:oppEau:filter').value;
            eau = !eau ? '' : '';


            var protod = document.getElementById('dtForm:tblOpp:oppProtoDate:filter').value;
            protod = !protod ? '' : '';


            var prodd = document.getElementById('dtForm:tblOpp:oppProdDate:filter').value;
            prodd = !prodd ? '' : '';


            var clsts = document.getElementById('dtForm:tblOpp:oppCloseStatusLabel:filter').value;
            clsts = !clsts ? '' : '';


            var oname = document.getElementById('dtForm:tblOpp:ownerName:filter').value;
            oname = !oname ? '' : '';


            var oppp = document.getElementById('dtForm:tblOpp:oppParties:filter').value;
            oppp = !oppp ? '' : '';


            //Line Items
            var opptype = document.getElementById('dtForm:tblOpp:oppTypeName:filter').value;
            opptype = !opptype ? '' : '';

            var line_items = document.getElementById('dtForm:tblOpp:oppLines:filter').value;
            line_items = !line_items ? '' : '';

            //Line Items
            var reason = document.getElementById('dtForm:tblOpp:oppCloseReason:filter').value;
            reason = !reason ? '' : '';

            //Populate filter map
            var filters = [
                {name: 'search_filter', value: left_filter},
                {name: 'inpTxt_id_filterCust', value: cust},
                {name: 'inpTxt_id_filterPrinci', value: princi},
                {name: 'inpTxt_id_filterDistri', value: distri},
                {name: 'inpTxt_id_filterOppSmanName', value: sman},
                {name: 'inpTxt_id_filterActivity', value: activity},
                {name: 'inpTxt_id_filterStatus', value: status},
                {name: 'inpTxt_id_filterProgram', value: program},
                {name: 'inpTxt_id_filterNextStep', value: next_step},
                {name: 'inpTxt_id_filterFollowUp', value: follow_up},
                {name: 'inpTxt_id_filterOppPriority', value: priority},
                {name: 'inpTxt_id_filterOppPotential', value: potential},
                {name: 'inpTxt_id_filterValue', value: opp_value},
                {name: 'inpTxt_id_filterOppEau', value: eau},
                {name: 'inpTxt_id_filterOppProtoDate', value: protod},
                {name: 'inpTxt_id_filterOppProdDate', value: prodd},
                {name: 'inpTxt_id_filterOppCloseStatusLabel', value: clsts},
                {name: 'inpTxt_id_filterOwnerName', value: oname},
                {name: 'inpTxt_id_filterOppParties', value: oppp},
                {name: 'inpTxt_id_filterOppTypeName', value: opptype},
                {name: 'inpTxt_id_filterOppLines', value: line_items},
                {name: 'inpTxt_id_filterOppCloseReason', value: reason}
            ];
            //Call remote procedure - pass filters
            callRetainFilter(filters);
        }





    </script>
</ui:composition>


