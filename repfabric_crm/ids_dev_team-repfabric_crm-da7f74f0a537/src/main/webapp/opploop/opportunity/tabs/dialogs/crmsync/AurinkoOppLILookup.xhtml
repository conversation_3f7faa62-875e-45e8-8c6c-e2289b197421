<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--#5695: Aurinko CRMSync: Opportunity Line Items -->
    <p:dialog header="#{custom.labels.get('IDS_LINE_ITEM')} lookup" width="900" modal="true" id="dlgIdAurinkoOppLILookup" widgetVar="dlgAurinkoOppLILookup" >
        <h:form id="frmAurinkoOppLILookup" >
             <!--#6327: <PERSON>rinko CRMSync: Line Item Lookup-->
             <f:facet name="header">
                <h:outputText value="#{custom.labels.get('IDS_LINE_ITEM')} lookup" style="font-weight: bold"/>
                <p:spacer width="600" height="0" />
                <h:panelGroup id="btnAurinkoMoreOppLI" >
                    <p:commandButton icon="fa fa-angle-right" title="Load more #{custom.labels.get('IDS_LINE_ITEM')}" onclick="PF('dlgFetchingRecords').show();"
                                     class="btn btn-primary btn-xs" rendered="#{not oAuthBasedSyncService.aurMoreOppLIS}"
                                     actionListener="#{oAuthBasedSyncService.ppltAurinkoLineItem(oAuthBasedSyncService.linkedPrinciId,oAuthBasedSyncService.liList.size())}"
                                     oncomplete="PF('dlgFetchingRecords').hide();PF('wvDtAurinkoOppLI').filter()" 
                                     update=":frmAurinkoOppLILookup:dtAurinkoOppLI :frmAurinkoOppLILookup:btnAurinkoMoreOppLI"/>
                </h:panelGroup>
            </f:facet>

            <!--outerheight error at console-->
            <p:dataTable id="dtAurinkoOppLI" widgetVar="wvDtAurinkoOppLI" paginator="true"
                         value="#{oAuthBasedSyncService.liList}"                         
                         filterEvent="keyup"
                         filteredValue="#{oAuthBasedSyncService.liListFlt}"
                         var="oppLI"
                         emptyMessage="No #{custom.labels.get('IDS_LINE_ITEM')} found"
                         selection="#{oAuthBasedSyncService.selectedAurinkoOppLI}"
                         selectionMode="single"
                         paginatorAlwaysVisible="true"
                         multiViewState="false"
                         rowKey="#{oppLI.oppItemSyncId}">
                <p:ajax event="rowSelect" oncomplete="PF('dlgAurinkoOppLILookup').hide();PF('dlgMasterAurinkoOppLI').show();" 
                        update=":frmAurinkoOppLIChsMstr"/>
                <p:column headerText="oppItemSyncId" rendered="false">
                    #{oppLI.oppItemSyncId}
                </p:column>
                <p:column headerText="oppItemSyncOppId" rendered="false">
                    #{oppLI.oppItemSyncOppId}
                </p:column>
<!--                <p:column headerText="Idd"  rendered="false">
                    #{oppLI.crmsyncTsCustom}
                </p:column>-->
                <p:column headerText="Id" filterBy="#{oppLI.oppItemSyncId}" filterMatchMode="contains" sortBy="#{oppLI.oppItemSyncId}"  >
                    #{oppLI.oppItemSyncId}
                </p:column>

                <p:column headerText="Product Name" filterBy="#{oppLI.oppItemRegnum}" filterMatchMode="contains" sortBy="#{oppLI.oppItemRegnum}"  >
                    #{oppLI.oppItemRegnum}
                </p:column>

                <p:column headerText="Product Id" filterBy="#{oppLI.oppItemPartManf}" filterMatchMode="contains" sortBy="#{oppLI.oppItemPartManf}"  >
                    #{oppLI.oppItemPartManf}
                </p:column> 
                <p:column headerText="Quantity" filterBy="#{oppLI.oppItemQnty}" filterMatchMode="contains" sortBy="#{oppLI.oppItemQnty}"  >
                    #{oppLI.oppItemQnty}
                </p:column> 
                <p:column headerText="Price" filterBy="#{oppLI.oppItemCost}" filterMatchMode="contains" sortBy="#{oppLI.oppItemCost}"  >
                    #{oppLI.oppItemCost}
                </p:column> 

                <p:column headerText="Product Family" filterBy="#{oppLI.btnName}" filterMatchMode="contains" 
                          sortBy="#{oppLI.btnName}">
                    #{not empty oppLI.btnName ? oppLI.btnName : ''}
                </p:column> 


            </p:dataTable>
        </h:form>
    </p:dialog>

    <!--#5695: Aurinko CRMSync: Opportunity Line Items-->
    <p:dialog id="dlgIdMasterAurinkoOppLI" widgetVar="dlgMasterAurinkoOppLI" width="600" closable="false" 
              modal="true" header="Choose master data as" resizable="false" >
        <h:form id="frmAurinkoOppLIChsMstr" >
            <h:panelGroup>

                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                        <p:outputLabel value="Last Update Timestamp of:" />
                    </div>

                    <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                        <p:outputLabel value="Repfabric #{custom.labels.get('IDS_OPP')}"/>
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8 ">
                        <p:outputLabel value="#{oppLineItems.updDate}"/>
                    </div>

                    <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                        <p:outputLabel value="#{oAuthBasedSyncService.selectedAurinkoOppLI.oppItemCustOrdNum} #{custom.labels.get('IDS_OPP')}"/>
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8 ">
                        <p:outputLabel value=" #{oAuthBasedSyncService.selectedAurinkoOppLI.oppItemPartCust}"/>
                    </div>

                    <div class="ui-sm-12 ui-md-12 ui-lg-12 "/>
                    <div class="ui-sm-12 ui-md-12 ui-lg-12 "/>

                    <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                        <p:outputLabel value="Choose master data as:"/>
                    </div>

                    <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                        <p:selectOneRadio id="radioIntMasterSel" value="#{oAuthBasedSyncService.oppLIMasterId}" 
                                          style="width:100%;" layout="lineDirection" >
                            <f:selectItem itemLabel="Repfabric #{custom.labels.get('IDS_OPP')}" itemValue="0"/>
                            <f:selectItem itemLabel="Target System #{custom.labels.get('IDS_OPP')}" itemValue="1"/>
                            <f:ajax execute="@this" />
                        </p:selectOneRadio>
                    </div>
                </div> 

                <br/>
                <div style="text-align: center">
                    <!--#6251: Aurinko CRMSync: Configure Sync for Parent company-->
                    <!--#6019: line item link->blue icon showing for unlinked line item-->
                    <!--20-04-2022 7776 Aurinko CRMSync: Update Last Modified timestamp of Opportunity-->
                    <p:commandButton value="Ok" class="btn-primary btn-xs"
                                     actionListener="#{oAuthBasedSyncService.onAUrinkoOppLISelect(oAuthBasedSyncService.linkedPrinciId,oppLineItems)}" 
                                     update=":frmOpp:tabOpp:lnItmId :frmOpp:tabOpp:dtLine :frmSummary"
                                     onclick="PF('dlgMasterAurinkoOppLI').hide();"
                                     oncomplete="PF('wvDtLine').filter();"/>
                </div>
            </h:panelGroup>
        </h:form>
    </p:dialog> 
    
    <style>
        .ui-datatable-scrollable-header *,
        .ui-datatable-scrollable-theadclone * {
            -moz-box-sizing: content-box;
            -webkit-box-sizing: content-box;
            box-sizing: content-box;
        }

        body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
            width: 15px;
        }

    </style>
</ui:composition>
