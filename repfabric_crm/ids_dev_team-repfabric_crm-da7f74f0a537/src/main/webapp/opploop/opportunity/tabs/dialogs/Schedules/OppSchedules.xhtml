<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// 
//*********************************************************
/*
*//02-02-2022: #7387 : CRM-5404: Opportunity > Schedules
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    <!--//01-06-2022 : 8087 : Opp Schedules : tab  > UI crashes and modifying fails-->
    <p:dialog id="oppSchedulesDlg" header="Schedules" width="750px" class="dialogCSS"
              widgetVar="dlgOppSchedules"  modal="true" responsive="true">
        <h:form id="frmOppSchedules">

            <p:panelGrid id="grid" columns="3" columnClasses="ui-grid-col-1,ui-grid-col-3,ui-grid-col-3"
                         layout="grid" styleClass="box-primary no-border ui-fluid">
                <p:outputLabel for="@next" value="Year:"/>
                <p:spinner id="year" value="#{oppScheduleService.sppinerValue}" min="2022" maxlength="4"  />
                <p:commandButton value="Add to Schedule" styleClass="btn btn-success  btn-xs"  action="#{oppScheduleService.saveData()}"
                                 update=":frmOppSchedules"/>
            </p:panelGrid>

            <p:dataTable id="dtOppSchedules" widgetVar="dtOppSchedules" value="#{oppScheduleService.oppScheduleList}" 
                         style="margin-top: 10px; max-height: 300px; scrollX:true" scrollable="true" scrollHeight="300" 
                         var="oppSchdle" selectionMode="single" rowKey="#{oppSchdle.recId}"
                         emptyMessage="No schedules" editable="true" editMode="cell">

                <p:column width="13%" headerText="Year" filterMatchMode="contains" filterBy="#{oppSchdle.oppScheduleYear}" sortBy="#{oppSchdle.oppScheduleYear}">
                    <h:outputText value="#{oppSchdle.oppScheduleYear}" />
                </p:column>
                <p:column width="27%" filterMatchMode="contains" filterBy="#{oppSchdle.oppScheduleType}"  headerText="Schedule" >
                    <p:cellEditor>
                        <f:facet name="output">
                            <h:outputText value="#{oppSchdle.oppScheduleType}" />
                        </f:facet>
                        <f:facet name="input">
                            <p:selectOneMenu value="#{oppSchdle.oppScheduleType}">
                                <p:ajax event="change" listener="#{oppScheduleService.onChangeYear(oppSchdle)}" 
                                        update=":frmOppSchedules"/>
                                <f:selectItem itemValue="Quarterly" itemLabel="Quarterly" />
                                <f:selectItem itemValue="Yearly" itemLabel="Yearly" />
                            </p:selectOneMenu>
                        </f:facet>
                    </p:cellEditor>

                </p:column>
                <!--//08-11-2022 : #8087 : Opp Schedules : tab  > UI crashes and modifying fails-->
                <p:column  width="15%" headerText="Q1" filterMatchMode="contains" 
                          filterBy="#{oppSchdle.oppUnitsQ1}" sortBy="#{oppSchdle.oppUnitsQ1}">
                    <p:cellEditor>
                        <f:facet name="output">
                            <h:outputText id="outputOppUnitsQ1" value="#{oppSchdle.oppUnitsQ1}" >
                                <f:convertNumber groupingUsed="true" minFractionDigits="0" maxFractionDigits="3"/>
                            </h:outputText>
                        </f:facet>
                        <f:facet name="input">
                            <p:inputNumber value="#{oppSchdle.oppUnitsQ1}" placeholder="0"  
                                           maxlength="9" maxValue="999999999" decimalPlaces="3"
                                           disabled="#{oppSchdle.oppScheduleType == 'Yearly'}"
                                           thousandSeparator=",">
                                <p:ajax event="change" listener="#{oppScheduleService.onChangeQty(oppSchdle)}" 
                                        update=":frmOppSchedules"/>
                            </p:inputNumber>
                        </f:facet>
                    </p:cellEditor>

                </p:column>
                <!--//08-11-2022 : #8087 : Opp Schedules : tab  > UI crashes and modifying fails-->
                <p:column width="15%" filterMatchMode="contains" filterBy="#{oppSchdle.oppUnitsQ2}" headerText="Q2" sortBy="#{oppSchdle.oppUnitsQ2}">
                    <p:cellEditor>
                        <f:facet name="output">
                            <h:outputText id="outputOppUnitsQ2" value="#{oppSchdle.oppUnitsQ2}" >
                                <f:convertNumber groupingUsed="true" minFractionDigits="0" maxFractionDigits="3"/>
                            </h:outputText>
                        </f:facet>
                        <f:facet name="input">
                            <p:inputNumber value="#{oppSchdle.oppUnitsQ2}"
                                           placeholder="0"  maxlength="9" maxValue="999999999" decimalPlaces="3"
                                           disabled="#{oppSchdle.oppScheduleType == 'Yearly'}"
                                           thousandSeparator=",">
                                <p:ajax event="change" listener="#{oppScheduleService.onChangeQty(oppSchdle)}" 
                                        update=":frmOppSchedules"/>
                            </p:inputNumber>
                        </f:facet>
                    </p:cellEditor>
                </p:column>
                <!--//08-11-2022 : #8087 : Opp Schedules : tab  > UI crashes and modifying fails-->
                <p:column width="15%" headerText="Q3" filterMatchMode="contains" filterBy="#{oppSchdle.oppUnitsQ3}" sortBy="#{oppSchdle.oppUnitsQ3}">
                    <p:cellEditor>
                        <f:facet name="output">
                            <h:outputText id="outputOppUnitsQ3" value="#{oppSchdle.oppUnitsQ3}" >
                                <f:convertNumber groupingUsed="true" minFractionDigits="0" maxFractionDigits="3"/>
                            </h:outputText>
                        </f:facet>
                        <f:facet name="input">
                            <p:inputNumber value="#{oppSchdle.oppUnitsQ3}" disabled="#{oppSchdle.oppScheduleType == 'Yearly'}"
                                           placeholder="0"  maxlength="9" maxValue="999999999" decimalPlaces="3"
                                           thousandSeparator=",">
                                <p:ajax event="change" listener="#{oppScheduleService.onChangeQty(oppSchdle)}" 
                                        update=":frmOppSchedules"/>
                            </p:inputNumber>
                        </f:facet>
                    </p:cellEditor>
                </p:column>
                <!--//08-11-2022 : #8087 : Opp Schedules : tab  > UI crashes and modifying fails-->
                <p:column width="15%" headerText="Q4" filterMatchMode="contains" filterBy="#{oppSchdle.oppUnitsQ4}" sortBy="#{oppSchdle.oppUnitsQ4}">
                    <p:cellEditor>
                        <f:facet name="output">
                            <h:outputText id="outputOppUnitsQ4" value="#{oppSchdle.oppUnitsQ4}">
                                <f:convertNumber groupingUsed="true" minFractionDigits="0" maxFractionDigits="3"/>
                            </h:outputText>
                        </f:facet>
                        <f:facet name="input">
                            <p:inputNumber value="#{oppSchdle.oppUnitsQ4}" disabled="#{oppSchdle.oppScheduleType == 'Yearly'}"
                                           placeholder="0"  maxlength="9" maxValue="999999999" decimalPlaces="3"
                                           thousandSeparator=",">
                                <p:ajax event="change" listener="#{oppScheduleService.onChangeQty(oppSchdle)}" 
                                        update=":frmOppSchedules"/>
                            </p:inputNumber>
                        </f:facet>
                    </p:cellEditor>
                </p:column>
                <p:column width="20%" headerText="Yearly Units" filterMatchMode="contains" 
                          filterBy="#{oppSchdle.oppUnitsYear}" sortBy="#{oppSchdle.oppUnitsYear}">
                    <p:cellEditor>
                        <f:facet name="output">
                            <p:outputPanel id="panelYearlyUnits">
                            <h:outputText id="outputYearlyUnits" value="#{oppSchdle.oppUnitsYear}" >
                                <f:convertNumber groupingUsed="true" minFractionDigits="0" maxFractionDigits="3"/>
                            </h:outputText>
                                </p:outputPanel>
                        </f:facet>
                        <f:facet name="input">
                            <p:inputNumber value="#{oppSchdle.oppUnitsYear}" disabled="#{oppSchdle.oppScheduleType == 'Quarterly'}"
                                           placeholder="0"  maxlength="9" maxValue="999999999" decimalPlaces="3" thousandSeparator=",">
                                <p:ajax event="change" listener="#{oppScheduleService.onChangeYearlyValue(oppSchdle)}" 
                                        update=":frmOppSchedules"/>
                            </p:inputNumber>
                        </f:facet>
                    </p:cellEditor>
                </p:column>
                <p:column width="5%">
                    <p:commandButton title="Delete" icon="fa fa-trash" id="btnDelete" 
                                     styleClass="btn-danger btn-xs btn-icon"
                                     actionListener="#{oppScheduleService.deleteData(oppSchdle.recId)}" 
                                     update=":frmOppSchedules">
                        <p:confirm icon="ui-icon-alert" header="Confirmation" message="Are you sure to delete?"  />
                    </p:commandButton> 
                </p:column>

            </p:dataTable>

        </h:form>
    </p:dialog>

    <h:form id="form2">
        <p:confirmDialog global="true" showEffect="fade" width="200">
            <div class="align-center">
                <p:commandButton value="Yes" type="button" styleClass="ui-confirmdialog-yes btn btn-success btn-xs"   />
                <p:spacer width="4px"/>
                <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no btn btn-danger btn-xs" />
            </div>
        </p:confirmDialog>
    </h:form> 
</ui:composition>