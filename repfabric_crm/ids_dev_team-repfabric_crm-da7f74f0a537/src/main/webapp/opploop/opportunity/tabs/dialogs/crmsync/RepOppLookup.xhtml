<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"> 
    <!-- #6801: <PERSON>rinko CRMSync: Sync Status-->
    <!--03-03-2022 7367 CRM-5410  crmsync: when searching for opps, default the first 4 char of company name-->
    <p:dialog header="Repfabric Opportunities" width="1000" resizable="true" modal="true" id="iddlgSyncStatusRepOpp" 
              widgetVar="dlgSyncStatusRepOpp" >
        <!--03-03-2022 7367 CRM-5410  crmsync: when searching for opps, default the first 4 char of company name-->
        <p:ajax event="close" listener="#{oAuthBasedSyncService.setTargetCompName(null)}"/>
        <h:form id="frmSyncStatusRepOppLookup" >
           
            <!--03-03-2022 7367 CRM-5410  crmsync: when searching for opps, default the first 4 char of company name-->
            <p:dataTable id="dtSyncStatusRepOpp" widgetVar="wvDtSyncStatusRepOpp" reflow="true"
                        
                         value="#{oAuthBasedSyncService.repOpps}"  var="opp"
                         filteredValue="#{oAuthBasedSyncService.repOppsFlt}"
                         selectionMode="single" selection="#{oAuthBasedSyncService.seldRepOpp}"
                         emptyMessage="No #{custom.labels.get('IDS_OPP')} found"
                         paginatorAlwaysVisible="true" 
                         rowKey="#{opp.oppId}" rows="50"  pageLinks="4" 
                         paginator="true" rowsPerPageTemplate="50,100" 
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         filterEvent="keyup"  paginatorPosition="top" scrollable="true" scrollHeight="320" >

                <!--                21-02-2022 7191 Aurinko CRMSync: Sync Status > Unlinked RF opps[changed filter to clear filter due to table update issue] -->
                <!--03-03-2022 7367 CRM-5410  crmsync: when searching for opps, default the first 4 char of company name-->
                <p:ajax event="rowSelect" onstart="PF('dlgProcessing').show();"
                        listener="#{oAuthBasedSyncService.resolveOppFromSyncStatus(oAuthBasedSyncService.seldRepOpp.oppPrincipal, oAuthBasedSyncService.seldRepOpp.oppId, oAuthBasedSyncService.selectedAurinkoOpp.oppCrmId, oAuthBasedSyncService.selectedAurinkoOpp.oppId, oAuthBasedSyncService.selectedAurinkoOpp.btnName)}"
                        update=":frmAurSyncExc:dtAurSyncExc :frmAurSyncExc:grlAurSyncExc" 
                        oncomplete="PF('wvDTAurSyncExc').clearFilters();PF('dlgProcessing').hide();PF('dlgSyncStatusRepOpp').hide();" />

                <!--28-02-2022 7367 CRM-5410  crmsync: when searching for opps, default the first 4 char of company name-->
                <p:column width="18%"  headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{opp.custName}" filterMatchMode="contains" 
                          sortBy="#{opp.custName}" filterValue="#{oAuthBasedSyncService.targetCompName}">
                    <p:outputLabel id="olRepOppLookupCustName" value="#{opp.custName}" />
                </p:column>
                <!--02-03-2022 7367 CRM-5410  crmsync: when searching for opps, default the first 4 char of company name-->
                <p:column width="15%"  headerText="#{custom.labels.get('IDS_PROGRAM')}" filterBy="#{opp.oppCustProgram}"
                          filterMatchMode="contains" sortBy="#{opp.oppCustProgram}">
                    #{opp.oppCustProgram}
                </p:column>
                <!--02-03-2022 7367 CRM-5410  crmsync: when searching for opps, default the first 4 char of company name-->
                <p:column width="18%" headerText="#{custom.labels.get('IDS_ACTIVITY')}" filterBy="#{opp.oppActivity}" filterMatchMode="contains" sortBy="#{opp.oppActivity}"  >
                    #{not empty opp.oppActivity ? opp.oppActivity : ""}
                </p:column>
                <p:column width="18%" headerText="Status" filterBy="#{opp.oppStatus}" filterMatchMode="contains" sortBy="#{opp.oppStatus}"  >
                    #{opp.oppStatus}
                </p:column>
                <!--02-03-2022 7367 CRM-5410  crmsync: when searching for opps, default the first 4 char of company name-->
                <p:column width="12%" headerText="#{custom.labels.get('IDS_FOLLOW_UP')}" filterBy="#{opp.oppFollowUp}" 
                          filterMatchMode="contains" sortBy="#{opp.oppFollowUp}"  >
                    #{opp.oppFollowUp}
                </p:column>
                <!--02-03-2022 7367 CRM-5410  crmsync: when searching for opps, default the first 4 char of company name-->
                <p:column width="10%" headerText="#{custom.labels.get('IDS_VALUE')}" filterBy="#{opp.oppValue}" filterMatchMode="contains" 
                          sortBy="#{opp.oppValue}"  >
                    #{opp.oppValue}
                </p:column>
            </p:dataTable>

            <!--03-03-2022 7367 CRM-5410  crmsync: when searching for opps, default the first 4 char of company name-->
<!--            <p:remoteCommand autoRun="true" rendered="#{oAuthBasedSyncService.repOpps != null}"  onstart="PF('wvDtSyncStatusRepOpp').clearFilters();"
                             actionListener="#{oAuthBasedSyncService.prePopulateRfOpp(oAuthBasedSyncService.selectedAurinkoOpp)}" process="@form"
                             update=":frmSyncStatusRepOppLookup:dtSyncStatusRepOpp :frmSyncStatusRepOppLookup:olRepOppLookuveriFy" oncomplete="PF('wvDtSyncStatusRepOpp').filter();" />-->
        </h:form>
    </p:dialog>

        <style>
            .ui-datatable-scrollable-header *,
            .ui-datatable-scrollable-theadclone * {
                -moz-box-sizing: content-box;
                -webkit-box-sizing: content-box;
                box-sizing: content-box;
            }
    
            body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
                width: 15px;
            }
        </style>

</ui:composition> 
