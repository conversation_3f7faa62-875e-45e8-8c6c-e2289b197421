<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui" 
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">
    <div>
        <p:dialog widgetVar="dlgProjectList" header="Link #{custom.labels.get('IDS_OPP_PROJ')} to #{custom.labels.get('IDS_OPP')}" modal="true">
            <h:form id="frmProjectList" >
                 <!--#169 :CRM-2304 :new projects cannot be created from promotions to opps-->
                <p:commandButton value="New"  class="btn btn-primary btn-xs"  oncomplete="PF('dlgLinkProject').show();"   update="frmLinkProject" />
                <p:dataTable  id="dtProjListId" widgetVar="tblProj" paginator="true"                    
                              value="#{projects.projectList}"                         
                              filterEvent="keyup"                          
                              filteredValue="#{projects.filteredProjList}" 
                              var="proj"                                       
                              rowKey="#{proj.recId}"                                    
                              paginatorAlwaysVisible="false"
                              paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                              rows="50"   class="tblmain"
                              emptyMessage="No #{custom.labels.get('IDS_OPP_PROJ')}s available"
                              draggableColumns="true">
                    <f:facet name="header">
                        <p:inputText style="display: none"   id="globalFilter" onkeyup="PF('tblProj').filter()" />
                    </f:facet>
                    <p:column  headerText="#{custom.labels.get('IDS_CUSTOMER')}"  filterMatchMode="contains" 
                               filterBy="#{proj.projCustName}"
                               sortBy="#{proj.projCustName.toUpperCase()}">
                        <p:commandLink value=" #{proj.projCustName} " action="#{proj.onRowSelect(proj.projId,opportunities.oppId)}" oncomplete="PF('dlgProjectList').hide()" update=":frmOpp">
                        </p:commandLink>                   
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_PROGRAM')}" filterMatchMode="contains" 
                              filterBy="#{proj.projProgram}"
                              sortBy="#{proj.projProgram.toUpperCase()}">
                        #{proj.projProgram} 
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_VALUE')}" filterMatchMode="contains" 
                              filterBy="#{proj.projValue}" 
                              style="max-width: 100px;
                              padding: 5px 2px 1px 2px !important;"  
                              sortBy="#{proj.projValue}">
                        <p:outputLabel value="#{proj.projValue}" style="float: right">
                                <f:convertNumber maxFractionDigits="0" groupingUsed="true" maxIntegerDigits="15"/>
                        </p:outputLabel>
                    </p:column> 
                    <p:column headerText="#{custom.labels.get('IDS_SALES_TEAM')}" filterMatchMode="contains" 
                              filterBy="#{proj.smanName.toUpperCase()}"
                              sortBy="#{proj.smanName}">
                        #{proj.smanName} 
                    </p:column> 
                </p:dataTable>
            </h:form>
        </p:dialog>
    </div>
</ui:composition>