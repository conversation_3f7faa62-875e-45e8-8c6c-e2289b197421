<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <!--13-10-2023 12315 CRMSYNC-349 Legrand Salesforce Integration: Opp Details > CRMSync tab updates - Project, Record Type-->
    <h:form id="frmSFProjectLookupDlg">

        <p:dialog id="dlgSFProjectLookup" header="Project Lookup"  widgetVar="wvDlgSFProjectLookup" responsive="true" onShow="PF('wvDlgSFProjectLookup').initPosition();" 
                  width="1100" modal="true" closeOnEscape="true">
            <f:facet name ="header">
                Project Lookup
                <p:spacer width="800" height="0"/>

                <p:commandButton id ="btnNewSFProject" widgetVar="wvBtnNewSFProject" styleClass="btn btn-primary btn-xs"
                                 value="New" resetValues="true" title="Add new project" 
                                 onclick="PF('wvBtnNewSFProject').disable();"                               
                                 action="#{oAuthBasedSyncService.onclickNewSFProject(opportunities.oppCustomer)}" 
                                 update=":frmNewSFProjectDlg:opNewSFProjectDlg" 
                                 oncomplete="PF('wvDlgNewSFProject').show();PF('wvBtnNewSFProject').enable();"
                                 /> 

            </f:facet>
            <p:outputPanel id="opSFPRojectLookup">
                <h:panelGroup id="pgSFPRojectLookupFlt">
                    <div class="ui-g ui-fluid">
                        <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                            <h:panelGroup style="width: 100%">
                                <p:outputLabel value="Name: " />
                                <p:spacer width="8"/>
                                <p:inputText value="#{oAuthBasedSyncService.nameFilter}" style="width: 70%"/>
                            </h:panelGroup>
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                            <h:panelGroup style="width: 100%">

                                <p:outputLabel value="City: " />
                                <p:spacer width="8"/>
                                <p:inputText value="#{oAuthBasedSyncService.cityFilter}" style="width: 70%"/>  
                            </h:panelGroup>
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                            <h:panelGroup style="width: 100%" >
                                <p:outputLabel  value="Zip Code: " />
                                <p:spacer width="8"/>
                                <p:inputText value="#{oAuthBasedSyncService.postalCodeFilter}" style="width: 70%" />  
                            </h:panelGroup>
                        </div>
                        <div class="ui-sm-12 ui-md-1 ui-lg-1">
                            <p:commandButton value="Filter"  style="min-width: 100%;width: 100%"
                                             class="btn btn-primary btn-xs"   
                                             title="Fetch records"
                                             onclick="PF('dlgFetchRec').show();"
                                             actionListener="#{oAuthBasedSyncService.filterProjectTable(oAuthBasedSyncService.linkedPrinciId,oAuthBasedSyncService.nameFilter,oAuthBasedSyncService.cityFilter,oAuthBasedSyncService.postalCodeFilter)}"
                                             update=":frmSFProjectLookupDlg:opSFPRojectLookup"
                                             oncomplete="PF('dlgFetchRec').hide();PF('wvDtSFProjectLookup').clearFilters()" />
                        </div>
                        <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                            <p:commandButton value="Clear" class="btn btn-primary btn-xs" style="min-width: 100%;width: 100%"
                                             rendered="#{oAuthBasedSyncService.resetFilters}"
                                             title="Clear Filters" onclick="PF('dlgProcessing').show();"
                                             actionListener="#{oAuthBasedSyncService.populateProject(oAuthBasedSyncService.linkedPrinciId)}"
                                             update=":frmSFProjectLookupDlg:opSFPRojectLookup"
                                             oncomplete="PF('dlgProcessing').hide();PF('wvDtSFProjectLookup').clearFilters();" />
                        </div>
                        <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                            <p:commandButton value="Load More" class="btn btn-success btn-xs" style="min-width: 100%;width: 100%"
                                             rendered="#{not empty oAuthBasedSyncService.aurAccNextPage}"
                                             onclick="PF('dlgFetchRec').show()" 
                                             action="#{oAuthBasedSyncService.loadMoreProjects(oAuthBasedSyncService.linkedPrinciId,oAuthBasedSyncService.aurAccNextPage)}"
                                             update=":frmSFProjectLookupDlg:opSFPRojectLookup"
                                             oncomplete="PF('wvDtSFProjectLookup').filter();PF('dlgFetchRec').hide();" />
                        </div>
                    </div>
                </h:panelGroup>

                <p:dataTable id="dtSFProjectLookup" widgetVar="wvDtSFProjectLookup" reflow="true"
                             value="#{oAuthBasedSyncService.aurinkoProjectList}"  
                             filteredValue="#{oAuthBasedSyncService.aurinkoProjectListFlt}" 
                             selection="#{oAuthBasedSyncService.selectedProject}"
                             var="project" scrollHeight="300" scrollable="true"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rowsPerPageTemplate="50,100" selectionMode="single"
                             filterEvent="keyup" pageLinks="4" 
                             rowKey="#{project.id}"  paginator="true"  rows="50" paginatorPosition="top" >
                    <p:ajax event="rowSelect" update=":frmAurinkoOppCrt" oncomplete="PF('wvDlgSFProjectLookup').hide()" />
                                                                                    <!--#{oAuthBasedSyncService.rcName}(); -->

                    <p:column id="id"  filterMatchMode="contains" filterBy="#{project.id}" headerText="Id" sortBy="#{project.id}"    >
                        #{project.id}
                    </p:column>  
                    <p:column id="name"  filterMatchMode="contains" filterBy="#{project.name}" headerText="Name" sortBy="#{project.name}"    >
                        #{project.name}
                    </p:column>    
                    <p:column  id="stage" filterMatchMode="contains" filterBy="#{project.stage}" headerText="Stage" sortBy="#{project.stage}" >
                        #{project.stage}
                    </p:column>   
                    <p:column id="amount" filterMatchMode="contains" filterBy="#{project.amount}" headerText="Amount" sortBy="#{project.amount}" >
                        #{project.amount}
                    </p:column>  
                    <p:column id="city" filterMatchMode="contains" filterBy="#{project.city}" headerText="City" sortBy="#{project.city}" >
                        #{project.city}
                    </p:column>
                    <p:column id="state" filterMatchMode="contains" filterBy="#{project.state}" headerText="State" sortBy="#{project.state}" >
                        #{project.state}
                    </p:column>
                    <p:column id="postalCode" filterMatchMode="contains" filterBy="#{project.postalCode}" headerText="Zip Code" sortBy="#{project.postalCode}" >
                        #{project.postalCode}
                    </p:column>
                </p:dataTable>
            </p:outputPanel>
        </p:dialog>
    </h:form>
    <ui:include src="NewSFProjectDlg.xhtml"/>

</ui:composition>

