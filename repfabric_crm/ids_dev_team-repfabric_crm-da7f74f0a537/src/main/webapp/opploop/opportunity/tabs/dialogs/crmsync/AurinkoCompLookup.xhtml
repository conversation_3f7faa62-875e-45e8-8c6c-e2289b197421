<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"> 
    <!--#5881: Aurinko CRMSync:  Provision for Account Lookup-->
    <!--#6507: <PERSON>rinko CRMSync: Account Lookup: Updates to Pre-filter-->

    <!--#6477: <PERSON>rinko CRMSync > UI Updates to Lookups - Opp/Account/Pricebook entry-->
    <!--    #6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/New-->
    <p:dialog header="Target System Account Lookup" modal="true" width="1200" id="iddlgAurinkoComp" widgetVar="dlgAurinkoComp" 
              closeOnEscape="true" responsive="true" onShow="PF('dlgAurinkoComp').initPosition();" height="550">
<!--20-04-2022 7595 CRM-5488: CRMsync: new acct creation in target system-->
        <f:facet name ="header">
            <h:form id="frmOppAurNewCompLookupHead">
                Target System Account Lookup
                <p:spacer width="750" height="0"/>
                <!--13-06-2022 8180 CRM-5738: crmsync: extra companies: reverse  input-->
                <p:commandButton id="btnFrmOppNewAurComp" value="New" title="Add new company in target system" immediate="true"
                                 styleClass="btn btn-primary btn-xs"
                                 action="#{oAuthBasedSyncService.ppltAurNewComp(oAuthBasedSyncService.crmsyncPrinciOfOpp.crmsyncSysId==3 ? oAuthBasedSyncService.selectedCompForAlias.rfCompId : oAuthBasedSyncService.seldAurComp.compId,oAuthBasedSyncService.rcName)}"
                                 oncomplete="PF('dlgAurNewComp').show();" 
                                 update=":frmNewAurCompLookup" /> 
                
            </h:form>
        </f:facet>
        <h:form id="frmAuinkoCompLookup" >

            <!--#6457: Aurinko CRMSync:  Account Lookup > Filters-->
            <!--#6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/New[removed update attribute]-->
            <!--15-02-2022 7191 Aurinko CRMSync: Sync Status > Unlinked RF opps-->
            <p:remoteCommand id="rcAurCompSetPage" name="rcAurCompSetPage"  autoRun="false"
                             oncomplete="PF('wvDtSFAurinkoComp').paginator.setPage(PF('wvDtSFAurinkoComp').paginator.cfg.pageCount-1);" />

            <!--#6457: Aurinko CRMSync:  Account Lookup > Filters-->
            <!--#6507: Aurinko CRMSync: Account Lookup: Updates to Pre-filter-->
            <h:panelGroup id="pgAurCompFlt">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                        <h:panelGroup style="width: 100%">
                            <p:outputLabel id="lblAurFltCompName" value="Name: " />
                            <p:spacer width="8"/>
                            <p:inputText value="#{oAuthBasedSyncService.aurOppNameFlt}"
                                         style="width: 70%"
                                         id="itAurFltCompName"  />
                        </h:panelGroup>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                        <!--14-04-2023 10999 ** APRIL PRIORITY ** CRM-6620: Netsuite: Opp creation from RF to TS /  View TS opp-->
                        <h:panelGroup style="width: 100%" rendered="#{oAuthBasedSyncService.crmsyncPrinciOfOpp.crmsyncSysId!=15}">

                            <p:outputLabel id="lblAurFltCompCity" value="City: " />
                            <p:spacer width="8"/>
                            <p:inputText value="#{oAuthBasedSyncService.aurOppCompFlt}"
                                         style="width: 70%"
                                         id="itAurCompCity" />  
                        </h:panelGroup>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                        <!--14-04-2023 10999 ** APRIL PRIORITY ** CRM-6620: Netsuite: Opp creation from RF to TS /  View TS opp-->
                        <h:panelGroup style="width: 100%" rendered="#{oAuthBasedSyncService.crmsyncPrinciOfOpp.crmsyncSysId!=15}">
                            <p:outputLabel id="lblAurFltCompZip" value="Zip: " />
                            <p:spacer width="8"/>
                            <p:inputText value="#{oAuthBasedSyncService.aurCompZipFlt}"
                                         style="width: 70%"
                                         id="itAurFltCompZip" />  
                        </h:panelGroup>
                    </div>
                    <!--#6507: Aurinko CRMSync: Account Lookup: Updates to Pre-filter-->
                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <!--#6313: Aurinko CRMSync: Opportunity Lookup > Next Page-->
                        <!--#6507: Aurinko CRMSync: Account Lookup: Updates to Pre-filter-->
                        <p:commandButton id="btnAurCompFlt" value="Filter" 
                                         class="btn btn-primary btn-xs"   
                                         title="Fetch records"
                                         onclick="PF('dlgFetchRec').show();"
                                         actionListener="#{oAuthBasedSyncService.ppltAurCompFlt(oAuthBasedSyncService.linkedPrinciId, null)}"
                                         update=":frmAuinkoCompLookup:dtAurinkoComp :frmAuinkoCompLookup:pgAurCompFlt"
                                         oncomplete="PF('dlgFetchRec').hide();PF('wvDtSFAurinkoComp').clearFilters()" />
                    </div>
                    <!--#6507: Aurinko CRMSync: Account Lookup: Updates to Pre-filter-->
                    <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                        <p:commandButton id="btnRstAurComplookup" value="Clear" 
                                         class="btn btn-primary btn-xs" rendered="#{oAuthBasedSyncService.resetAurCompTbl}"
                                         title="Clear everything"
                                         actionListener="#{oAuthBasedSyncService.aurinkoOppCrtCancel()}"
                                         update=":frmAuinkoCompLookup:dtAurinkoComp :frmAuinkoCompLookup:pgAurCompFlt"
                                         oncomplete="PF('wvDtSFAurinkoComp').clearFilters()" />
                    </div>
                    <!--#6507: Aurinko CRMSync: Account Lookup: Updates to Pre-filter-->
                    <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                        <!--#6457: Aurinko CRMSync:  Account Lookup > Filters-->
                        <!--#6477: Aurinko CRMSync > UI Updates to Lookups - Opp/Account/Pricebook entry-->
                        <!--#6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/New[rearranged the oncomplete attribute]-->
                        <!--15-02-2022 7191 Aurinko CRMSync: Sync Status > Unlinked RF opps-->
                        <p:commandButton value="Load More" onclick="PF('dlgFetchRec').show()"
                                         class="btn btn-success btn-xs" rendered="#{not empty oAuthBasedSyncService.aurAccNextPage}"
                                         action="#{oAuthBasedSyncService.aurinkoCompLookup(oAuthBasedSyncService.linkedPrinciId,oAuthBasedSyncService.aurAccNextPage)}"
                                         oncomplete="PF('wvDtSFAurinkoComp').filter();PF('dlgFetchRec').hide();rcAurCompSetPage();" 
                                         update=":frmAuinkoCompLookup:pgAurCompFlt :frmAuinkoCompLookup:dtAurinkoComp"/>
                    </div>
                </div>
            </h:panelGroup>
            <!--#6098: CRM-4873: #1 Company creation selection for CRMSync-->
            <!--#6457: Aurinko CRMSync:  Account Lookup > Filters-->
            <p:dataTable id="dtAurinkoComp" widgetVar="wvDtSFAurinkoComp"
                         scrollHeight="320"
                         scrollable="true"
                         value="#{oAuthBasedSyncService.aurinkoComps}"                         
                         filterEvent="keyup"
                         filteredValue="#{oAuthBasedSyncService.aurinkoCompsFlt}"
                         var="comp"
                         emptyMessage="No Account found"
                         multiViewState="true"
                         selection="#{oAuthBasedSyncService.selectedAurinkoComp}"
                         selectionMode="single"
                         rowKey="#{comp.colorCode}"
                         paginator="true" paginatorAlwaysVisible="true"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="100">
                <!--#6507: Aurinko CRMSync: Account Lookup: Updates to Pre-filter-->
                <!--#6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/New[row select changed]-->
                <!--#6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/New[changed on row select]-->
                <!--#6734: Aurinko CRMSync: Opp Creation > Auto populate Account ID if avalable-->
                <!--23-03-2022 7402 CRM-5397/YS-195: crmsync rheem: downflow all companies for aliasing and creation-->
                <p:ajax event="rowSelect"
                        listener="#{oAuthBasedSyncService.setAurAccId(oAuthBasedSyncService.fromOpp,oAuthBasedSyncService.selectedAurinkoComp.colorCode, oAuthBasedSyncService.selectedAurinkoComp.comments)}" />
                 <!--oncomplete="#{oAuthBasedSyncService.rcName}"--> 
                  <!--<p:ajax event="rowSelect" oncomplete="#{viewCompLookupService.remoteCmdName}(); PF('lookupComp').hide()" />-->

                <p:column width="25" headerText="Company" filterBy="#{comp.comments}" filterMatchMode="contains" sortBy="#{comp.comments}" >
                    #{not empty comp.comments ? comp.comments : ""}
                </p:column>
                <p:column width="12" headerText="Type" filterBy="#{comp.compCity}" filterMatchMode="contains" sortBy="#{comp.compCity}"  >
                    #{not empty comp.compCity ? comp.compCity : ""}
                </p:column>
                <p:column width="12" headerText="Phone" filterBy="#{comp.compComments}" filterMatchMode="contains" sortBy="#{comp.compComments}"  >
                    #{not empty comp.compComments?comp.compComments:""}
                </p:column>
                <!--#6380: Aurinko CRMSync: Account Lookup: Show City, State, Zip-->
                <p:column width="12" headerText="City" filterBy="#{comp.compDataSource}" filterMatchMode="contains" sortBy="#{comp.compDataSource}"  >
                    #{not empty comp.compDataSource?comp.compDataSource:""}
                </p:column>
                <!--#6380: Aurinko CRMSync: Account Lookup: Show City, State, Zip-->
                <p:column width="12" headerText="State" filterBy="#{comp.compFax}" filterMatchMode="contains" sortBy="#{comp.compFax}"  >
                    #{not empty comp.compFax?comp.compFax:""}
                </p:column>
                <!--#6380: Aurinko CRMSync: Account Lookup: Show City, State, Zip-->
                <p:column width="12" headerText="Zip Code" filterBy="#{comp.compFormattedAddr}" filterMatchMode="contains" sortBy="#{comp.compFormattedAddr}"  >
                    #{not empty comp.compFormattedAddr?comp.compFormattedAddr:""}
                </p:column>
                <p:column width="12" headerText="Website" filterBy="#{comp.compCountryCode}" filterMatchMode="contains" sortBy="#{comp.compCountryCode}"  >
                    #{not empty comp.compCountryCode?comp.compCountryCode:""}
                </p:column>
                <!--#6380: Aurinko CRMSync: Account Lookup: Show City, State, Zip-->
                <p:column width="25" headerText="Last Modified Time" filterBy="#{comp.compAddress1}" filterMatchMode="contains" sortBy="#{comp.compAddress1}"  >
                    #{not empty comp.compAddress1?comp.compAddress1:""}
                </p:column>
            </p:dataTable>
        </h:form>
    </p:dialog>

    <style>
        .ui-paginator {
            background-color:#ffffff !important;
        }
    </style>
</ui:composition> 
