<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <p:dialog id="dlgacr"  widgetVar="dlgacr"  header="#{custom.labels.get('IDS_ACC_REGS')}" >
        <p:dataTable var="acc"
                     paginator="true"
                     paginatorAlwaysVisible="false"
                     rows="15"
                     value="#{accregistrations.listAregCopied}">
            <p:column headerText="#{custom.labels.get('IDS_MFG_REG_NUM')}">
                #{acc.regiManfRegNum}
            </p:column>
            <p:column headerText="#{custom.labels.get('IDS_APPROVED_DATE')}">
                <p:outputLabel value="#{acc.regiApproveDate}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:outputLabel>

            </p:column>
            <p:column headerText="Expiry">
                <p:outputLabel value="#{acc.regiExpiryDate}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:outputLabel>

            </p:column>
            <p:column headerText="Approved By">
                #{acc.regiApprovedBy}
            </p:column>
            <p:column headerText="Level">
                #{acc.regiLevel}
            </p:column>
            <p:column headerText="Notes">
                #{acc.regiNotes}
            </p:column>
        </p:dataTable>
    </p:dialog>

</ui:composition>

