<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui">

    <p:dialog modal="true"   header="#{custom.labels.get('IDS_SAMPLE')}" widgetVar="dlgSample1" >
        <p:panel id="pnlSA" class="pnldlgs">
            <p:panelGrid columns="2">
                <p:outputLabel value="#{custom.labels.get('IDS_SAMPLE')} Order No."/>
                <p:inputText value="#{samples.sampOrdNum}" maxlength="30" />

                <p:outputLabel value="#{custom.labels.get('IDS_SHIPPER')} Ref.No"/>
                <p:inputText value="#{samples.sampShipRefNum}" maxlength="30" />
                <p:outputLabel value="#{custom.labels.get('IDS_SHIPPER')} A/c No."/>
                <p:inputText value="#{samples.sampShipAccNum}" maxlength="30" />             
                <p:outputLabel value="#{custom.labels.get('IDS_SHIPPING')} method"/>
                <p:inputText value="#{samples.sampShipMethod}"  maxlength="30" />   
                <p:outputLabel value="Status"/>
                <p:inputText value="#{samples.sampShipStatus}" maxlength="30" />
            </p:panelGrid>
            <p:commandButton process=":frmOpp:tabOpp:pnlSA" value="Save" 
                             actionListener="#{samples.save()}"
                             class="button_top btn_tabs"
                             oncomplete="if (args &amp;&amp; !args.validationFailed) PF('dlgSample1').hide()" 
                             update=" "     />
            <p:commandButton type="button" value="Cancel" 
                             class="button_top btn_tabs"
                             onclick="PF('dlgSample1').hide()"/>


        </p:panel>
    </p:dialog>


</ui:composition>

