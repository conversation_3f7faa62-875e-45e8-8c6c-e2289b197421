<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <!--update=":frmOpp:tabOpp:dtc :frmOpp:tabOpp:dtceml"-->
    <h:panelGroup id="pnlCONTACTS" class="pnls">
        <p:remoteCommand actionListener="#{oppContacts.loadOppContacts()}" autoRun="true" name="loadOppConts" update=":frmOpp:tabOpp:dtc" />
        <p:remoteCommand name="applyConta" 
                         actionListener="#{oppContacts.applyContactOpp(viewContLookupService.selectedContact)}"
                         action="#{oppContacts.selectContact(viewContLookupService.selectedContact)}"
                         update=":frmOpp:tabOpp:dtc"  oncomplete="loadOppConts();"/>
        <!--#920 Contacts & Contact lookups-->
        <!--New Contact-->
        <!--15-11-2023 12506 CRM-7335 FEATURE REQUEST: Required fields for new contacts in company module-->
        <p:commandButton  value="New" title="Add new contact" class="btn btn-primary btn-xs" update=":formNewContact:newContDlgFields" 
                          actionListener="#{viewContLookupService.setRemoteCmdName('applyConta')}" immediate="true"
                          action="#{viewContLookupService.initliazeContact(companyServiceImpl.comp.compId)}" resetValues="true" 
                          oncomplete="PF('newContDlg').show();" />

        <p:spacer width="4"/>
        <!--Link Contact-->
        <p:commandButton value="Link" title="Link contact" class="btn btn-primary btn-xs"  immediate="true"
                         actionListener="#{viewContLookupService.listAll('applyConta')}"
                         update=":formContLookup" oncomplete="PF('lookupCont').show()"/>
        <br/> <br/> 
        <!--01/02/2017 - Ticket #720824 - Changing the list -->
        <p:dataTable value="#{oppContacts.oppContacts}"
                     emptyMessage="No Contacts found" 
                     var="opl" id="dtc">
            <p:column width="5%" >
                <!--1531 CRM-3150: add "Confirm deletion" warning to Contacts in an Opp-->
                <p:commandButton  title="Remove Contact" onclick="PF('confirmationContact').show()"
                                  disabled="#{opportunities.oppCloseStatus!=0}"
                                  process="@this" 
                                  action="#{oppContacts.delContact(opl)}"                                   
                                  icon="fa fa-trash" class="btn-danger btn-xs">
                    <!--<p:confirm header="Confirmation" message="Are you sure to delete?"  />--> 
                </p:commandButton>

                

                <!--                        update=":frmOpp:tabOpp:dtc 
                                                      :frmOpp:tabOpp:dtceml
                                                      :frmOpp:tabOpp:oppCustomerContactName
                                                      :frmOpp:tabOpp:oppPrincipalContactName
                                                      :frmOpp:tabOpp:oppDistriContactName  "
                                                      icon="ui-icon-trash"-->
            </p:column>

            <p:column headerText="Name">
                <!--#3750CRM-2234: Fwd: editing contacts within companies-->
                <h:link  value="#{opl.contFullName}" outcome="/opploop/contacts/ContactDetails.xhtml?id=#{opl.contId}" >
                    <f:param name="frm" value="#{view.viewId}" />
                    <f:param name="moduleId" value="#{opportunities.oppId}" />
                </h:link>
            </p:column>
            <p:column headerText="Company" >
                <p:outputLabel value="#{opl.contCompName}" />
            </p:column>
            <p:column headerText="#{custom.labels.get('IDS_JOB_TITLE')}">
                <p:outputLabel value="#{opl.contJobTitle}" />
            </p:column>
            <p:column headerText="Business Email ">
                <p:outputLabel value="#{opl.contEmails}" />
            </p:column>

        </p:dataTable>
        <!--        <p:confirmDialog global="true" showEffect="fade" hideEffect="fade">
                    <p:commandButton value="Yes" type="button" class="btn btn-success btn-xs"/>
                    <p:spacer width="4"/>
                    <p:commandButton value="No" type="button" class="btn btn-warning btn-xs"/>
                </p:confirmDialog>-->
<!--1531 CRM-3150: add "Confirm deletion" warning to Contacts in an Opp-->
        <p:confirmDialog header="Confirm deletion"  width="400" global="false"
                         message="Are you sure to delete this Contact? "
                         widgetVar="confirmationContact" >
            <div class="div-center">
                <p:commandButton value="Delete" actionListener="#{oppContacts.removeContact()}" class="btn btn-danger btn-xs" process="@this"   
                                 update=":frmOpp:tabOpp:dtc 
                                  :frmOpp:tabOpp:oppCustomerContactName
                                  :frmOpp:tabOpp:oppPrincipalContactName
                                  :frmOpp:tabOpp:oppDistriContactName  " oncomplete="PF('confirmationContact').hide()"  />
                <p:spacer width="4"/>
                <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"    onclick="PF('confirmationContact').hide()" 
                                 type="button"  />
            </div>
        </p:confirmDialog>
    </h:panelGroup>

    <!--update=":frmOpp:tabOpp:dtc :frmOpp:tabOpp:dtceml"-->
<!--    <p:dialog   header="Link Contact to #{custom.labels.get('IDS_OPP')}" widgetVar="dlgC"  style="float: initial !important; width: 75% !important;" >
        <p:remoteCommand autoRun="true" actionListener="#{viewContList.loadContacts()}" update="contList" />
        01/02/2017 - Ticket #720824 -Addind filteredValue 
        <p:dataTable id="contList" value="#{viewContList.conList}"  var="con" widgetVar="dtcl" 
                     filteredValue="#{viewContList.filteredContacts}"
                     paginator="true" rows="15"
                     paginatorAlwaysVisible="false"
                     paginatorPosition="top">
            <p:column  headerText="Full Name" filterMatchMode="contains" filterBy="#{con.contFullName}">
                <p:commandLink value="#{con.contFullName}"  
                               oncomplete="PF('dlgC').hide();loadOppConts();"
                               process="@this" 
                               actionListener="#{oppContacts.selectContact(con)}" >
                </p:commandLink>

            </p:column>
            <p:column headerText="Company" filterMatchMode="contains" filterBy="#{con.contCompany}">
                <p:outputLabel value="#{con.contCompany}" />
            </p:column>
            <p:column  headerText="#{custom.labels.get('IDS_JOB_TITLE')}" filterMatchMode="contains" filterBy="#{con.contJobTitle}">
                <p:outputLabel value="#{con.contJobTitle}" />
            </p:column>
            <p:column headerText="Business email" filterMatchMode="contains" filterBy="#{con.contEmailBusiness}">
                <p:outputLabel value="#{con.contEmailBusiness}" />
            </p:column>
        </p:dataTable>

    </p:dialog>-->

    <script>
        function showAddContDlg() {

//            document.getElementById('addCompContactForm1:inpTxtFirstName').value = '';
//            document.getElementById('addCompContactForm1:inpTxtLastName').value = '';
//            document.getElementById('addCompContactForm1:inpTxtFullName').innerHTML = '';
//            document.getElementById('addCompContactForm1:inpTxtjt').value = '';
//            document.getElementById('addCompContactForm1:bizEmail').value = '';
//            document.getElementById('addCompContactForm1:txtContBizMobile').value = '';
        }
    </script>

</ui:composition>



