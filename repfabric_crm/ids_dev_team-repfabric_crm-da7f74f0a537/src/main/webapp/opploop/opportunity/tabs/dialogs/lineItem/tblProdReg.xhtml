<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <p:dialog id="dlgpreg"  widgetVar="dlgpreg"  header="#{custom.labels.get('IDS_PROD_REGS')}" >
        <p:dataTable var="prd"
                     paginator="true"
                     paginatorAlwaysVisible="false"
                     rows="15"
                     value="#{registrations.listPregCopied}">
            <p:column headerText="#{custom.labels.get('IDS_MFG_REG_NUM')}">
                #{prd.regiManfRegNum}
            </p:column>
            <p:column headerText="#{custom.labels.get('IDS_APPROVED_DATE')}">
                <p:outputLabel value="#{prd.regiApproveDate}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:outputLabel>

            </p:column>
            <p:column headerText="Expiry">
                <p:outputLabel value="#{prd.regiExpiryDate}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:outputLabel>

            </p:column>
            <p:column headerText="Approved By">
                #{prd.regiApprovedBy}
            </p:column>
            <p:column headerText="Level">
                #{prd.regiLevel}
            </p:column>
            <p:column headerText="Notes">
                #{prd.regiNotes}
            </p:column>
        </p:dataTable>
    </p:dialog>

</ui:composition>

