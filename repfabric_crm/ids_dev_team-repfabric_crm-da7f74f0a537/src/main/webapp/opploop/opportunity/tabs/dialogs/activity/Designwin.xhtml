<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui">
    <p:dialog modal="true"   header="#{custom.labels.get('IDS_DWINS')}" widgetVar="dlgDesign1">
        
        <p:panel id="pnlDW" class="pnldlgs">
            <p:panelGrid columns="2" >
                <p:outputLabel value="#{custom.labels.get('IDS_DWINS')} No. : "/>
                <p:inputText value="#{designWin.dwinNum}"  maxlength="45"/>

                <p:outputLabel value="Date : "/>
                <p:calendar value="#{designWin.dwinDate}"   pattern="#{globalParams.dateFormat}"  
                            converterMessage="Date not valid"   >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>
                <p:outputLabel value="Exp.Date : "/>
                <p:calendar value="#{designWin.dwinExpiryDate}"   pattern="#{globalParams.dateFormat}" 
                            converterMessage="Date not valid" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>

                <p:outputLabel value="Status  : "/>
                <p:inputText value="#{designWin.dwinStatus}" maxlength="30" />
                <p:outputLabel value="Type  : "/>
                <p:inputText value="#{designWin.dwinType}" maxlength="30" />

            </p:panelGrid>
 <!--update=":frmOpp:growl :frmOpp:tabOpp:dtLine"-->  
            <p:commandButton process=":frmOpp:tabOpp:pnlDW" value="Save" 
                             class="button_top btn_tabs"
                             actionListener="#{designWin.save()}" 
                             oncomplete="if (args &amp;&amp; !args.validationFailed) PF('dlgDesign1').hide()" update=" "   />
            <p:commandButton type="button"  escape="true" 
                             class="button_top btn_tabs"
                             value="Cancel" onclick="PF('dlgDesign1').hide()"/>
        </p:panel>
    </p:dialog>


</ui:composition>

