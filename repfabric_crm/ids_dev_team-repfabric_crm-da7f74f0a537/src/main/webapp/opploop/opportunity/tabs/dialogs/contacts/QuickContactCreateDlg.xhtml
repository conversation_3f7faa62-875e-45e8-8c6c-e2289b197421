<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: QuickContactCreateDlg.xhtml
// Author: Sarayu
//*********************************************************
* Copyright (c) 2016 Indea Design Systems, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets">   

    <p:dialog id="CreateQuickContactDlg" header="Add Contact Info" widgetVar="dlgCreateContact">
        <h:form id="addCompContactForm1" >
            <p:panelGrid id="pgAddCompCon" layout="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8">
                <f:facet name="header">

                    <h:outputText value="Please enter the details below "/>
                    <br/>
                </f:facet>

                <h:outputText value="First Name:" styleClass="required" />
                <p:inputText id="inpTxtFirstName" styleClass="tds1" value="#{oppService.contFname}" maxlength="30" style="text-transform: capitalize" required="true" requiredMessage="First name cannot be blank">
                    <p:ajax event="blur" process="inpTxtFirstName" update="inpTxtFullName" listener="#{oppService.fillFullName}" />
                </p:inputText>

                <h:outputText value="Last Name:"  />
                <p:inputText id="inpTxtLastName" styleClass="tds1" value="#{oppService.contLname}"  maxlength="30" style="text-transform: capitalize">
                    <p:ajax event="blur" process="inpTxtLastName" update="inpTxtFullName" listener="#{oppService.fillFullName}" />
                </p:inputText>

                <h:outputText value="Full Name:"  />
                <p:outputLabel id="inpTxtFullName" styleClass="tds1" value="#{oppService.contFullName}"  style="text-transform: capitalize"/>

                <h:outputText value="Company:"  />
                <p:selectOneMenu id="ddCustomers" styleClass="new-cont-select"   value="#{oppService.contCompId}" filter="true" filterMatchMode="contains">
                    <f:selectItem itemLabel="Select #{custom.labels.get('IDS_CUSTOMER')}" itemValue="0" />
                    <f:selectItems value="#{oppService.customersMap}"/>
                    <p:ajax listener="#{oppService.onSelectCompany()}"/>
                </p:selectOneMenu>

                <h:outputText value="#{custom.labels.get('IDS_JOB_TITLE')}:" />
                <p:inputText id="inpTxtjt" styleClass="tds1" value="#{oppService.contJobTitle}" style="text-transform: capitalize" maxlength="60" >
                    <p:ajax event="blur" process="inpTxtjt"  />
                </p:inputText>

                <h:outputText value="Business Email:" />
                <p:inputText id="bizEmail" styleClass="tds1" value="#{oppService.contEmailBusiness}" maxlength="60" validatorMessage="Invalid email format.">
                        <f:validateRegex
                            pattern="^[_A-Za-z0-9-\+]+(\.[_A-Za-z0-9-]+)*@[A-Za-z0-9-]+(\.[A-Za-z0-9-]+)*(\.[A-Za-z]{2,})$" />              
                    <p:ajax event="blur" process="bizEmail"  />
                </p:inputText>

                <h:outputText value="Mobile:" />
                <p:inputText styleClass="tds1" id="txtContBizMobile" value="#{oppService.contPhoneMobile}" maxlength="20"/>

                <f:facet name="footer">
                    <h:panelGroup id = "pgrpAddCompCon" style="display:block;text-align: center">
                        <br/>
                        <!--update="@form :frmOpp:growl :frmOpp:tabOpp:dtc :frmOpp:tabOpp:dtceml"-->
                        <p:commandButton class="btn btn-xs btn-success"  id="addContact" value="Create contact" action="#{oppService.addContact()}" oncomplete="if(args &amp;&amp; !args.validationFailed) PF('dlgCreateContact').hide()" update="@form "/>
                        <br/><br/>
                    </h:panelGroup>
                </f:facet>
            </p:panelGrid>
        </h:form>
    </p:dialog>



</ui:composition>

