<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <p:dialog modal="true"   header="#{custom.labels.get('IDS_ACC_REGS')}" widgetVar="dlgRegistAcc">
        <h:form id="oppLineAreg" >
            <p:panel id="pnlLAR" class="pnldlgs">
                <p:panelGrid columns="2">
                    <p:outputLabel value="#{custom.labels.get('IDS_MFG_REG_NUM')}"/>
                    <p:inputText value="#{accregistrations.regiManfRegNum}" maxlength="30" />

                    <p:outputLabel value="#{custom.labels.get('IDS_APPROVED_DATE')}"/>
                    <p:calendar value="#{accregistrations.regiApproveDate}"   pattern="#{globalParams.dateFormat}"
                                converterMessage="Date not valid">
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                    </p:calendar>

                    <p:outputLabel value="Expiry Date"/>
                    <p:calendar value="#{accregistrations.regiExpiryDate}"   pattern="#{globalParams.dateFormat}"
                                converterMessage="Date not valid">
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                    </p:calendar>             

                    <p:outputLabel value="Approved By"/>
                    <p:inputText value="#{accregistrations.regiApprovedBy}" maxlength="40" />

                    <p:outputLabel value="Notes"/>
                    <p:inputTextarea value="#{accregistrations.regiNotes}" cols="25" rows="3"/>

                    <p:outputLabel value="Registration Level"/>
                    <p:inputText value="#{accregistrations.regiLevel}"  maxlength="30"/>
                </p:panelGrid>
                <p:commandButton  process="oppLineAreg:pnlLAR" disabled="#{opportunities.oppCloseStatus!=0}" 
                                  value="Save" actionListener="#{accregistrations.save2()}"
                                  icon="ui-icon-disk"  
                                  class="button_top btn_tabs" 
                                  oncomplete="if (args &amp;&amp; !args.validationFailed) PF('dlgRegistAcc').hide()" 
                                  update=":frmOpp:tabOpp:dtLine"                 />
                <p:commandButton  class="button_top btn_tabs"  type="button" value="Cancel" 
                                  icon="ui-icon-cancel"    
                                  onclick="PF('dlgRegistAcc').hide()"/>

            </p:panel>
        </h:form>
    </p:dialog>

</ui:composition>

