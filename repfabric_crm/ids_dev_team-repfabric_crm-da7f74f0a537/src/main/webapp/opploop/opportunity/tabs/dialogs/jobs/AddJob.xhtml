<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui" 
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">
    
    <p:dialog id="addJobDlgId" header="New #{custom.labels.get('IDS_JOB')}" widgetVar="addJobDlg" modal="true" responsive="true">
        <h:form id="jobAddNewForm">
            <p:panelGrid columns="3">
                <p:outputLabel value="#{custom.labels.get('IDS_JOB')} Name"  />
                <p:outputLabel styleClass="m" value=" " />
                <!--Task #10632: CRMSYNC-183   Sync Error - Error message when assigning Sugar opportunity to a user Gibran by harshithad on 08/02/23-->
                <p:inputText id="inptTxtJobDesscr" value="#{jobs.jobDescr}" maxlength="120"/>
<!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:outputLabel value="#{custom.labels.get('IDS_JOB_TYPE')} "  />
                 <p:outputLabel styleClass="m" value=" " />
                <p:selectOneMenu   value="#{jobs.jobType}" >
                    <f:selectItem itemValue="0" itemLabel="Select #{custom.labels.get('IDS_JOB')} Type"/>
                    <f:selectItems value="#{oppTypesMst.getOppTypes()}"  
                                   var="oppType"   
                                   itemValue="#{oppType.oppTypeId}"
                                   itemLabel="#{oppType.oppTypeName}"/>
                </p:selectOneMenu>
                <!--#888  Opportunities > Link to Job >  Change Activity-->
                <p:outputLabel value="#{custom.labels.get('IDS_JOB_STAGE')}"  />
                <p:outputLabel styleClass="m" value=" " />
                <p:selectOneMenu  value="#{jobs.jobActivity}" >
                  <!--#888  Opportunities > Link to Job >  Change Activity-->
                    <f:selectItems value="#{jobActivitiesMstService.getJobActivity()}"  
                                   var="act"   
                                   itemValue="#{act.recId}"
                                   itemLabel="#{act.actName}"/>
                </p:selectOneMenu>
<!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:outputLabel value="#{custom.labels.get('IDS_JOB_VALUE')}"  />
                <p:outputLabel styleClass="m" value=" " />
                <p:inputText   value="#{jobs.jobvalue}"  converterMessage="not a number" maxlength="14">
                    <f:convertNumber   groupingUsed="true" maxIntegerDigits="15" minFractionDigits="2" maxFractionDigits="2" />
                </p:inputText>

                <p:outputLabel value="#{custom.labels.get('IDS_JOB')} No"  />
                <p:outputLabel styleClass="m" value=" " />
                <p:inputText value="#{jobs.jobNo}"  maxlength="40" />
<!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:outputLabel value="#{custom.labels.get('IDS_JOB_BID_DATE')}"  />
                <p:outputLabel styleClass="m" value=" " />
                <p:calendar  id="dtBidDate" value="#{jobs.jobBidDate}" class="calendarClass" disabled="#{jobs.disableFields}"
                             widgetVar="wobiddate"
                             pattern="#{globalParams.dateFormat}" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>
<!--22-01-2021:Task# #3131-CRM-3794: jobs edit  label -  Order Date-->
                <p:outputLabel value="#{custom.labels.get('IDS_JOB_ORDER_DATE')}"  />
                <p:outputLabel styleClass="m" value=" " />
                <p:calendar  id="dtprodDate" value="#{jobs.jobProdDate}" class="calendarClass" disabled="#{jobs.disableFields}"
                             widgetVar="woproddate"
                             pattern="#{globalParams.dateFormat}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>
                <!--#3354 Fix Sonarqube issues-->
                <div style="text-align: center">
                    <p:commandButton value="Save" class="btn btn-success btn-xs" actionListener="#{jobs.quickJobCreate()}" update=":frmOpp"  />
                    <p:commandButton value="Cancel" class="btn btn-warning btn-xs" onclick="PF('addJobDlg').hide()" />
                </div>
                
            </p:panelGrid>
        </h:form>
    </p:dialog>
       
</ui:composition>