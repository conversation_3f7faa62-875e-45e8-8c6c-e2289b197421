<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui" 
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">
    <div>
         <!--#169 :CRM-2304 :new projects cannot be created from promotions to opps-->
         <p:dialog widgetVar="dlgLinkProject" header="Create  #{custom.labels.get('IDS_OPP_PROJ')} to #{custom.labels.get('IDS_OPP')}" modal="true" resizable="false">
            <h:form id="frmLinkProject" >
                <p:panelGrid columns="2">
                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}"/>
                    </p:column>
                    <p:column>
                        <p:inputText value="#{projects.projCustName}"  disabled="true"/>  
                    </p:column>
                    <p:column>
                         <p:outputLabel value="Topic"/>
                    </p:column>
                    <p:column>
                        <!--Task #10632: CRMSYNC-183   Sync Error - Error message when assigning Sugar opportunity to a user Gibran by harshithad on 08/02/23-->
                        <p:inputText id="inptTxtProjPrgm" value="#{projects.projProgram}"  maxlength="120"/>  
                    </p:column>
                    <p:column>
                        <p:outputLabel   value="Value " />
                        
                    </p:column>
                    <p:column>
                        <p:inputText value="#{projects.projValue}" disabled="true"/>  
                    </p:column>
                    <p:column>
                         <p:outputLabel   value="Start Date" />
                    </p:column>
                    <p:column>
                       <p:calendar  pattern="#{globalParams.dateFormat}"
                             size="10"  
                             converterMessage="Start date not valid" 
                             required="true" 
                             requiredMessage="Please select start date"
                             value="#{projects.projStartDate}"  >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:calendar>
                        <!--<p:inputText value="#{projects.projStartDate}" />-->  
                    </p:column>
                   
                        <div class="div-center" >
                        <p:commandButton value="Save"  class="btn btn-success btn-xs" action="#{projects.saveLinkproject(projects)}" oncomplete="PF('dlgLinkProject').hide();PF('dlgProjectList').hide();" update=":frmOpp"/>
                        </div>
                                        
                </p:panelGrid>
<!--         .      ustomer [default to Opp Customer] [Non-editable]
Topic [Default to Opp Topic] [ Editable] 
Value [Default to Opp Value] [non-editable]
Start Date [ Default to Opp Creation Date] [Editable]
on save, create a new project and link the opportunity to the newly created project
Close the dialog
View Project option should be visible instead of Link to Project-->
            </h:form>
        </p:dialog>
    </div>
</ui:composition>