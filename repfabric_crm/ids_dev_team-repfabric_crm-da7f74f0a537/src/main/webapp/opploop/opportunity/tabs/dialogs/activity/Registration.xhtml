<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui">
    <p:dialog modal="true"   header="#{custom.labels.get('IDS_PROD_REGS')}" widgetVar="dlgRegist2">
        <p:panel id="pnlPR" class="pnldlgs">
            <p:panelGrid columns="2">
                <p:outputLabel value="#{custom.labels.get('IDS_MFG_REG_NUM')}"/>
                <p:inputText value="#{registrations.regiManfRegNum}" maxlength="30" />

                <p:outputLabel value="#{custom.labels.get('IDS_APPROVED_DATE')}"/>
                <p:calendar value="#{registrations.regiApproveDate}"   pattern="#{globalParams.dateFormat}"                               
                            converterMessage="Date not valid"  >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>

                <p:outputLabel value="Expiry Date"/>
                <p:calendar value="#{registrations.regiExpiryDate}"   pattern="#{globalParams.dateFormat}"
                            converterMessage="Date not valid"  >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>             

                <p:outputLabel value="Approved By"/>
                <p:inputText value="#{registrations.regiApprovedBy}" maxlength="40" />

                <p:outputLabel value="Notes"/>
                <p:inputTextarea value="#{registrations.regiNotes}" cols="25" rows="3"/>

                <p:outputLabel value="Registration Level"/>
                <p:inputText value="#{registrations.regiLevel}"  maxlength="30"/>
            </p:panelGrid>
            <!--update=":frmOpp:growl :frmOpp:tabOpp:dtLine"-->  
            <p:commandButton process="@this :frmOpp:tabOpp:pnlPR" 
                             class="button_top btn_tabs"
                             value="Save" actionListener="#{registrations.save()}"
                             oncomplete="if (args &amp;&amp; !args.validationFailed) PF('dlgRegist2').hide()" update=" "                />
            <p:commandButton type="button" value="Cancel" 
                             class="button_top btn_tabs"
                             onclick="PF('dlgRegist2').hide()"/>

        </p:panel>
    </p:dialog>


</ui:composition>

