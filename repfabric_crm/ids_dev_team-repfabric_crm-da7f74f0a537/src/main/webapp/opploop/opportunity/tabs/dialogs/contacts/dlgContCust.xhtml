<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets">   

    <p:dialog     header="#{custom.labels.get('IDS_CUST_CONTACT')} Lookup List" id="dlgContCust" widgetVar="dlgContCust"                   
                  closeOnEscape="true" position="200,50" maximizable="true" 
                  modal="true">
        <p:dataTable  id="dtCC"   value="#{opportunities.listContLookup}"   
                      filteredValue="#{opportunities.listContLookupFiltered}"  
                      var="con" 
                      paginatorAlwaysVisible="=false"
                      paginatorPosition="top"
                      paginator="true"  
                      rows="15" 
                      widgetVar="dtCC" >

            <p:column  filterMatchMode="contains"  filterBy="#{con.contFullName}" headerText="Full Name" >
                <p:commandLink value="#{con.contFullName}" 
                               process="@this"
                               onclick="PF('dlgContCust').hide();" 
                               update=":frmOpp:tabOpp:oppCustomerContactName   :frmOpp:tabOpp:pnlclrcust
                               :frmOpp:tabOpp:oppCustomerName 
                               :frmOpp:tabOpp:oppSman :frmOpp:tabOpp:inpTxtDistriName  :frmOpp:tabOpp:oppDistriContactName :frmOpp:tabOpp:pnlclrdist"              
                               oncomplete="PF('dtCC').clearFilters()"
                               actionListener="#{oppService.selectPrimaryContact(2, con.contId, con.contFullName, con.contCompany, con.contType)}" >
                </p:commandLink>

            </p:column>
            <p:column headerText="Company" filterMatchMode="contains"  filterBy="#{con.contCompany}">
                <h:outputLabel value="#{con.contCompany}" />

            </p:column>
            <p:column headerText="#{custom.labels.get('IDS_JOB_TITLE')}" filterMatchMode="contains"  filterBy="#{con.contJobTitle}">
                <h:outputLabel value="#{con.contJobTitle}" />

            </p:column>
            <p:column headerText="Email" filterMatchMode="contains"  filterBy="#{con.contEmailBusiness}">
                <h:outputLabel value="#{con.contEmailBusiness}" />
            </p:column>

            <p:column headerText="Phone" filterMatchMode="contains"  filterBy="#{con.contPhoneBusi}"> 
                <h:outputLabel  value="#{con.contPhoneBusi}" />
            </p:column>
        </p:dataTable>

   <!--update=":quickContForm"-->
        <p:commandButton icon="ui-icon-plus"  
                         value="New Contact" 
                         onclick="PF('dlgContCust').hide();" 
                         process="@this" 
                         actionListener="#{oppService.initContact(2)}" 
                         oncomplete="PF('dlgContCreate').show();loadComp();"
                         update=" "
                         class="button_top btn_tabs"  />

    </p:dialog>

</ui:composition>

