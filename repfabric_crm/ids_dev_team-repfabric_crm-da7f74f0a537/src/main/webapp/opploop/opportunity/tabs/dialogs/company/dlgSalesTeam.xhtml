<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets">   
    
    <p:dialog   header="#{custom.labels.get('IDS_SALES_TEAM')} Lookup List" widgetVar="dlgSalesTeam" 
                id="dlgSalesTeam"
                closeOnEscape="true" height="50%" width="50%"
                modal="true">
        <p:dataTable  widgetVar="wdDlgSalesTeam" 
                      value="#{opportunities.salesTeams}" 
                      var="st"    
                      rows="3" id="tblSalesTeam" >
            <p:column headerText="Sales Team">
                <p:commandLink process="@this"  value="#{st.smanName}"
                               onclick="PF('dlgSalesTeam').hide()" 
                               update=":frmOpp:tabOpp:oppSman "
                               actionListener="#{oppService.setSalesTeam(st.smanId,st.smanName)}"/>
            </p:column>
        </p:dataTable>
    </p:dialog>
</ui:composition>
