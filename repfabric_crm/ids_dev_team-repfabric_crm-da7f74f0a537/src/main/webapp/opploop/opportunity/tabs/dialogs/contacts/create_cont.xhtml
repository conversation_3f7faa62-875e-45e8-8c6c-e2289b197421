<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets">   

    <p:dialog widgetVar="dlgContCreate" header="Quick create contact" resizable="false">
        <h:form id="quickContForm">
            <p:remoteCommand name="loadComp" autoRun="false" process="@this"
                             actionListener="#{viewCompanyLookUp.populateCompanyList(oppService.compType)}" 
                             update="cc2"
                             />
            <h:panelGrid columns="2" cellpadding="5" cellspacing="5">
                <p:outputLabel value="First Name:"  />
                <p:inputText id='cfn'  value="#{oppService.contFname}" maxlength="30" styleClass="tds1" required="true" requiredMessage="First name is mandatory" /> 

                <p:outputLabel value="Last Name"  />
                <p:inputText id="cln" value="#{oppService.contLname}" maxlength="30" styleClass="tds1" />

                <p:outputLabel value="Company" />
                <p:selectOneMenu filter="true"  style="width: 100%"   id="cc2"  widgetVar="w2" value="#{oppService.contCompId}" rendered="#{oppService.contCompany==''}" >
                    <f:selectItems value="#{viewCompanyLookUp.companyList}" var="cm" 
                                   itemLabel="#{cm.compName}" itemValue="#{cm.compId}" />
                </p:selectOneMenu>
                <p:outputLabel  id="clabel" value="#{oppService.contCompany}" rendered="#{oppService.contCompany!=''}" />

                <p:outputLabel value="Email"  />
                <p:inputText id="qceml" value="#{oppService.contEmailBusiness}" type="email" maxlength="60" styleClass="tds1"
                             validatorMessage="Email not correct" />                                     

                <p:outputLabel value="#{custom.labels.get('IDS_JOB_TITLE')}"  />
                <p:inputText id="qctitle" value="#{oppService.contJobTitle}" styleClass="tds1" maxlength="100" />

                <p:outputLabel value="Mobile"  />
                <p:inputText id="qcmob" value="#{oppService.contPhoneMobile}" styleClass="tds1" maxlength="20"/>                                     
            </h:panelGrid>

            <p:commandButton icon="ui-icon-disk" id="qb2" value="Save" 
                             update=":frmOpp:tabOpp:oppCustomerContactName   :frmOpp:tabOpp:pnlclrcust
                             :frmOpp:tabOpp:oppSman
                             :frmOpp:tabOpp:oppCustomerName" rendered="#{oppService.compType ==2}"
                             actionListener="#{oppService.quickCreateContact()}"   class="button_top btn_tabs"    /> 
            <p:commandButton icon="ui-icon-disk" id="qb1"  value="Save" 
                             update=":frmOpp:tabOpp:oppPrincipalContactName   :frmOpp:tabOpp:pnlclrprin                               
                             :frmOpp:tabOpp:inpTxtPrinciName" rendered="#{oppService.compType ==1}"
                             actionListener="#{oppService.quickCreateContact()}"    class="button_top btn_tabs"  />  
            <p:commandButton icon="ui-icon-disk" id="qb3"  value="Save" 
                             update=":frmOpp:tabOpp:oppDistriContactName   :frmOpp:tabOpp:pnlclrdist
                             :frmOpp:tabOpp:inpTxtDistriName " rendered="#{oppService.compType ==3}"
                             actionListener="#{oppService.quickCreateContact()}"    class="button_top btn_tabs"  />  
        </h:form>
    </p:dialog>

</ui:composition>

