<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--#5821: Aurinko CRMSync: Opp Line Items > Conflict/Error-->
    <p:dialog widgetVar="dlgAurinkoOppLICnftDet" header="Conflicts" responsive="true" modal="true">
        <h:form id="frmAurinkoOppLICnftDet">
            <p:outputPanel id="pnlAurinkoOppLICnftDet" rendered="#{oAuthBasedSyncService.aurinkoOppLICnftTS.btnName ne null}">
                <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-4" >

                    <h:outputText value="Field" style=" font-weight: bold"/>
                    <h:outputText value="#{oAuthBasedSyncService.aurinkoOppLICnfltRF.btnName}" style=" font-weight: bold"/>
                    <h:outputText value="#{oAuthBasedSyncService.aurinkoOppLICnftTS.btnName}" style=" font-weight: bold"/>

                    <p:outputLabel value="name" />
                    <p:outputLabel value="#{oAuthBasedSyncService.aurinkoOppLICnfltRF.oppItemPartCust}"/>
                    <p:outputLabel value="#{oAuthBasedSyncService.aurinkoOppLICnftTS.oppItemPartCust}"/>

                    <p:outputLabel value="Value" />
                    <p:outputLabel value="#{oAuthBasedSyncService.aurinkoOppLICnfltRF.oppItemCost}"/>
                    <p:outputLabel value="#{oAuthBasedSyncService.aurinkoOppLICnftTS.oppItemCost}"/>

                    <p:outputLabel value="Quantity" />
                    <p:outputLabel value="#{oAuthBasedSyncService.aurinkoOppLICnfltRF.oppItemQnty}"/>
                    <p:outputLabel value="#{oAuthBasedSyncService.aurinkoOppLICnftTS.oppItemQnty}"/>

                    <p:outputLabel value="Created Date" />
                    <p:outputLabel value="#{oAuthBasedSyncService.aurinkoOppLICnfltRF.oppItemCustOrdNum}"/>
                    <p:outputLabel value="#{oAuthBasedSyncService.aurinkoOppLICnftTS.oppItemCustOrdNum}"/>

                    <p:outputLabel value="Last Modified Date" />
                    <p:outputLabel value="#{oAuthBasedSyncService.aurinkoOppLICnfltRF.oppItemCustRfqNum}"/>
                    <p:outputLabel value="#{oAuthBasedSyncService.aurinkoOppLICnftTS.oppItemCustRfqNum}"/>

                    <br/>
                    <!--#6251: Aurinko CRMSync: Configure Sync for Parent company-->
                    <!--#6591: Aurinko CRMSync: Sync Conflicts[changed action method]-->
                    <p:commandButton value="Keep #{oAuthBasedSyncService.aurinkoOppLICnfltRF.btnName}" class="btn-primary btn-xs"
                                     actionListener="#{oAuthBasedSyncService.onResolveConflict(oAuthBasedSyncService.linkedPrinciId,opportunities.oppId,oAuthBasedSyncService.aurinkoOppLICnfltRF)}"
                                     action="#{oAuthBasedSyncService.chkAurinkoOppCnflct()}"
                                     oncomplete="PF('dlgAurinkoOppLICnftDet').hide();PF('wvDtLine').filter();" 
                                     update=":frmOpp:tabOpp"/>
                    <!--#6251: Aurinko CRMSync: Configure Sync for Parent company-->
                    <!--#6591: Aurinko CRMSync: Sync Conflicts[changed action mehtod]-->
                    <p:commandButton value="Keep #{oAuthBasedSyncService.aurinkoOppLICnftTS.btnName}" class="btn-primary btn-xs"
                                     actionListener="#{oAuthBasedSyncService.onResolveConflict(oAuthBasedSyncService.linkedPrinciId,opportunities.oppId, oAuthBasedSyncService.aurinkoOppLICnftTS)}"
                                     action="#{oAuthBasedSyncService.chkAurinkoOppCnflct()}"
                                     oncomplete="PF('dlgAurinkoOppLICnftDet').hide();PF('wvDtLine').filter();" 
                                     update=":frmOpp:tabOpp"/>
                    <!--:warnLI :frmOpp:tabOpp:dtLine-->

                </p:panelGrid>
            </p:outputPanel>

            <!--#5821: Aurinko CRMSync: Opp Line Items > Conflict/Error-->
            <p:outputPanel id="pnlAurinkoOppLIErrorDet" rendered="#{ not empty oAuthBasedSyncService.aurinkoOppLICnfltRF.oppLinkFlags}">
<!--                <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-4" >

                    <br/>-->
                    <p:outputLabel id="lblAurinkoOppLIErrorLabel" value="Error: " style=" color: red;font-weight: bold"/>
                    <!--<p:spacer width="4" />-->
                    <br/>
                    <p:outputLabel id="lblAurinkoOppLIError" value="#{oAuthBasedSyncService.aurinkoOppLICnfltRF.oppLinkFlags}" style=" color: red" />
                <!--</p:panelGrid>-->
            </p:outputPanel>

        </h:form>
    </p:dialog>


</ui:composition>
