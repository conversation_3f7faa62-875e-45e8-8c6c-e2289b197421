<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"    
    xmlns:fn="http://xmlns.jcp.org/jsp/jstl/functions"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">

    <h:panelGroup id="pnlLINE" class="pnls">

        <!--<h4  class="sub_title">#{custom.labels.get('IDS_OPP')} #{custom.labels.get('IDS_LINE_ITEMS')} </h4>-->

        <!--#6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp-->
        <!--//02-02-2022: #7387 : CRM-5404: Opportunity > Schedules-->
        <p:commandButton id="addLine" class="btn btn-primary btn-xs"  update="pnlLineItem"                            
                         oncomplete="PF('dll').show();" 
                         value="New" actionListener="#{oppLineItems.clear()}" />

        <p:selectOneMenu id="selectCopyInto" value="#{quotesHdr.inquiryNme}"  style="float: right;  border-radius: 5px; background: white; height: 25px;" >
            <p:ajax event="change" listener="#{quotesHdr.initialize('',0)}" update="@this "/>
            <f:selectItem itemValue="" itemLabel="[--Select--]" />
            <!--#6704: CRM-5122: Hide from view Design Wins, Product Registrations, Account Registrations (Opp Line Items)[removed itemDisabled="#{oppLineItems.princFlags[1]==0}" anditemDisabled="#{oppLineItems.princFlags[2]==0}"] -->
            <f:selectItem itemValue="samp" itemLabel="#{custom.labels.get('IDS_SAMPLES')}" />
            <f:selectItem itemValue="quot" itemLabel="#{custom.labels.get('IDS_QUOTES')}" />
<!--            <f:selectItem itemValue="dwin" itemLabel="#{custom.labels.get('IDS_DWINS')}" itemDisabled="#{oppLineItems.princFlags[3]==0}" />
            <f:selectItem itemValue="preg" itemLabel="#{custom.labels.get('IDS_PROD_REGS')}" itemDisabled="#{oppLineItems.princFlags[4]==0}" />
            <f:selectItem itemValue="areg" itemLabel="#{custom.labels.get('IDS_ACC_REGS')}" />-->
        </p:selectOneMenu>

        <p:outputLabel value="#{custom.labels.get('IDS_COPY_INTO')} : " style="float: right;padding: 5px;" />


        <br/> <br/>

        <!--#5821: Aurinko CRMSync: Opp Line Items > Conflict/Error-->
        <p:dataTable
            value="#{oppLineItems.listLineItems}" 
            rowKey="#{line.recId}"
            selection="#{oppLineItems.selected}" 
            selectionMode="single"    
            emptyMessage="No #{custom.labels.get('IDS_LINE_ITEM')} found"
            var="line" 
            id="dtLine"
            editable ="true" editMode="cell" tableStyle="width:auto"
            rowIndexVar="rowIdx" widgetVar="wvDtLine">
            <!--#6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp-->
            <!--//02-02-2022: #7387 : CRM-5404: Opportunity > Schedules-->
            <!--            20-04-2022 7776 Aurinko CRMSync: Update Last Modified timestamp of Opportunity-->
            <!--//20-4-2022 : #7781 :  CRM-5601 schedules: added pns automatically need to inherit the header schedule-->
            <p:ajax  event="rowSelect" listener="#{oppLineItems.onRowSelect1()}" 
                     process="@this" 
                     oncomplete="PF('dll').show();PF('itemSch').clearFilters();PF('waddschditm').disable();"
                     update=":frmOpp:tabOpp:dll :frmOpp:tabOpp:tabLine :frmSummary "  />
            <p:ajax event="cellEdit" listener="#{oppLineItems.onStatusUpdate}" update=":frmOpp:tabOpp:tabLine" />

            <p:column width="2%" >
                <!--#200 CRM-2413:  Bridgerep: Deletion Confirmation message for opp line items-->
                <!--#6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp-->
                <p:commandButton process="@this" 
                                 icon="fa fa-trash" 
                                 styleClass="btn-danger"
                                 title="Remove #{custom.labels.get('IDS_LINE_ITEM')}" 
                                 style="width: 20px;height: 20px;background: none"
                                 onclick="PF('confirmationLnItm').show()"
                                 actionListener="#{oppLineItems.delLnItms(line)}">
                    <!--<p:confirm  header="Confirmation" message="Are you sure to delete?"  icon="ui-icon-alert" />--> 
                </p:commandButton>

            </p:column>

            <!-- #4730 new column oppline item status -->
            <p:column width="4%" headerText="Status">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{line.oppLIStatus}"/>
                    </f:facet>
                    <!--#5176: Opportunity Line Item - Status - Change update timestamp-->
                   
                    <f:facet name="input">
                        <p:selectOneMenu value="#{line.oppLineItemStatus}"  style=" border-radius: 5px; background: white; height: 25px;  min-width: 0 !important;"  >
                            <p:ajax event="change" listener="#{oppLineItems.updateOppLineItemStatus(line.recId, line.oppLineItemStatus)}" 
                                    update=":frmSummary :frmOpp:tabOpp:inpTxtValue "/>
                            <!--//01-07-2022 : #8374 : CRM-5687: Opportunity > Line Item Status-->
                            <f:selectItems value="#{oppLineItems.statusList}" var="status" itemLabel="#{status.itemStatusName}" 
                                           itemValue="#{status.itemStatusId}"/>
                        </p:selectOneMenu >
                    </f:facet>
                </p:cellEditor>
            </p:column >            

            <p:column width="1%"  headerText="#{custom.labels.get('IDS_SAMPLE_ABBR')}"   >   
<!--                <p:panel  rendered="#{line.oppLinkFlags.contains('S')}" >
                3
                </p:panel>
                     <p:panel  rendered="#{!line.oppLinkFlags.contains('S')}" >
              4
                </p:panel>-->
                <!--                 #{line.oppLinkFlags.contains('S')}
                                    <p:graphicImage  width="10" height="10" value="/resources/images/tick.ico" rendered="#{line.oppLinkFlags.contains('S')}" >
                                    1
                                </p:graphicImage>
                                <p:graphicImage  width="10" height="10" value="/resources/images/notick.png" rendered=" #{!line.oppLinkFlags.contains('S')}">
                                   2 
                                </p:graphicImage>
                -->
                <h:graphicImage library="images" name="greenFlag.png" rendered="#{line.oppLinkFlags.contains('S')}" width="12" height="12">
                    <!--#6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp-->
                    <p:ajax event="click"  listener="#{samplesHdr.subList(line.oppItemOppId, line.oppItemPartManf)}"  
                            oncomplete="PF('dlgsam').show()"
                            update=":frmOpp:tabOpp:dlgsam1" />
                </h:graphicImage>
                <h:graphicImage library="images" name="redFlag.png" rendered="#{!line.oppLinkFlags.contains('S')}" width="12" height="12">
                    <!--#6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp-->
                    <p:ajax event="click"  listener="#{samplesHdr.subList(line.oppItemOppId, line.oppItemPartManf)}"  
                            oncomplete="PF('dlgsam').show();"
                            update=":frmOpp:tabOpp:dlgsam1" />
                </h:graphicImage>



                <!--
                
                
                                <h:graphicImage   title="#{custom.labels.get('IDS_SAMPLES')}" 
                                                  library="images" name="#{line.oppLinkFlags.contains('S') ? 'tick.ico' : 'notick.png'}" 
                                                  style="width: 85%;padding: 2px">
                                    <p:ajax disabled="#{opportunities.oppCloseStatus!=0}" event="click"  listener="#{oppSamplesHdr.subList(line.oppItemOppId, line.oppItemPartManf)}"  
                                            oncomplete="PF('dlgsam').show();"
                                            update="" />
                                    update=":frmOpp:tabOpp:dlgsam1" 
                                </h:graphicImage>-->
            </p:column>  
            <p:column width="1%" headerText="#{custom.labels.get('IDS_QUOTES_ABBR')}"  >  

                <h:graphicImage library="images" name="greenFlag.png" rendered="#{line.oppLinkFlags.contains('Q')}" width="12" height="12" >
                    <!--#6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp-->
                    <p:ajax event="click" listener="#{quotesHdr.subListByItemId(line.oppItemOppId, line.oppItemId)}"  
                            oncomplete="PF('dlgqot').show();"
                            update=":frmOpp:tabOpp:dlgqot" />
                </h:graphicImage>
                <h:graphicImage library="images" name="redFlag.png" rendered="#{!line.oppLinkFlags.contains('Q')}" width="12" height="12" >
                    <!--#6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp-->
                    <p:ajax event="click" listener="#{quotesHdr.subList(line.oppItemOppId, line.oppItemPartManf)}"  
                            oncomplete="PF('dlgqot').show();"
                            update=":frmOpp:tabOpp:dlgqot" />
                </h:graphicImage>

<!--                <h:graphicImage title="#{custom.labels.get('IDS_QUOTES')}" 
                                library="images" name="#{line.oppLinkFlags.contains('Q') ? 'tick.ico' : 'notick.png'}" 
                                style="width: 85%;padding: 2px">
                    <p:ajax disabled="#{opportunities.oppCloseStatus!=0}" event="click" listener="#{quotesHdr.subList(line.oppItemOppId, line.oppItemPartManf)}"  
                            oncomplete="PF('dlgqot').show();"
                            update="" />
                    :frmOpp:tabOpp:dlgqot
                </h:graphicImage>-->
            </p:column>  

            <!--#6704: CRM-5122: Hide from view Design Wins, Product Registrations, Account Registrations (Opp Line Items)-->
<!--            <p:column width="1%" headerText="#{custom.labels.get('IDS_DWIN_ABBR')}"  >
                <h:graphicImage library="images" name="greenFlag.png" rendered="#{line.oppLinkFlags.contains('D')}" width="12" height="12">
          3258          listener="#{designWin.subList(line.oppItemOppId, line.oppItemPartManf)}" listener removed
                    #6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp
                    <p:ajax event="click"   
                            oncomplete="PF('dlgdw').show();" 
                            update=":frmOpp:tabOpp:dlgdw" />
                </h:graphicImage>
                <h:graphicImage library="images" name="redFlag.png" rendered="#{!line.oppLinkFlags.contains('D')}" width="12" height="12" >
                    #6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp
                    <p:ajax event="click" oncomplete="PF('dlgdw').show();" 
                            update=":frmOpp:tabOpp:dlgdw" />
                </h:graphicImage>

                <h:graphicImage title="#{custom.labels.get('IDS_DWINS')}" library="images" 
                                name="#{line.oppLinkFlags.contains('D') ? 'tick.ico' : 'notick.png'}" style="width: 85%;padding: 2px">
                    <p:ajax disabled="#{opportunities.oppCloseStatus!=0}" event="click" listener="#{designWin.subList(line.oppItemOppId, line.oppItemPartManf)}"  
                            oncomplete="PF('dlgdw').show();"
                            update="" />
                    :frmOpp:tabOpp:dlgdw
                </h:graphicImage>
            </p:column>  
            <p:column width="1%" headerText="#{custom.labels.get('IDS_OPP_PRODREG_ABBR')}" 
                      style="padding: 0px 0px; width: 20px">
            #3258    listener="#{registrations.subList(line.oppItemOppId, line.oppItemPartManf)}" linstener is commented
                <h:graphicImage library="images" name="greenFlag.png" rendered="#{line.oppLinkFlags.contains('R')}" width="12" height="12" >
                    #6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp
                    <p:ajax event="click"  oncomplete="PF('dlgpreg').show();"
                            update=":frmOpp:tabOpp:dlgpreg" />
                </h:graphicImage>
                <h:graphicImage library="images" name="redFlag.png" rendered="#{!line.oppLinkFlags.contains('R')}" width="12" height="12" >
                    #6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp
                    <p:ajax event="click" oncomplete="PF('dlgpreg').show();"
                            update=":frmOpp:tabOpp:dlgpreg" />
                </h:graphicImage>

                <h:graphicImage title="#{custom.labels.get('IDS_PROD_REGS')}" library="images" 
                                name="#{line.oppLinkFlags.contains('R') ? 'tick.ico' : 'notick.png'}">
                    <p:ajax disabled="#{opportunities.oppCloseStatus!=0}" event="click" listener="#{registrations.subList(line.oppItemOppId, line.oppItemPartManf)}" 
                            oncomplete="PF('dlgpreg').show();"
                            update="" />
                    :frmOpp:tabOpp:dlgpreg
                </h:graphicImage>
            </p:column>  
            <p:column width="1%" headerText="#{custom.labels.get('IDS_ACC_REG_ABBR')}"  >
          3258      listener="#{accregistrations.subList(line.oppItemOppId, line.oppItemPartManf)}" listener removed
                <h:graphicImage library="images" name="greenFlag.png" rendered="#{line.oppLinkFlags.contains('A')}" width="12" height="12" >
                    #6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp
                    <p:ajax event="click" oncomplete="PF('dlgacr').show();"
                            update=":frmOpp:tabOpp:dlgacr" />
                </h:graphicImage>
                <h:graphicImage library="images" name="redFlag.png" rendered="#{!line.oppLinkFlags.contains('A')}" width="12" height="12" >
                    #6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp
                    <p:ajax event="click"  oncomplete="PF('dlgacr').show();"
                            update=":frmOpp:tabOpp:dlgacr" />
                </h:graphicImage>

                <h:graphicImage title="#{custom.labels.get('IDS_ACC_REGS')}" library="images" 
                                name="#{line.oppLinkFlags.contains('A') ? 'tick.ico' : 'notick.png'}" style="width: 85%;padding: 2px">
                    <p:ajax disabled="#{opportunities.oppCloseStatus!=0}" event="click" listener="#{accregistrations.subList(line.oppItemOppId, line.oppItemPartManf)}" 
                            oncomplete="PF('dlgacr').show();"
                            update="" />
                    :frmOpp:tabOpp:dlgacr
                </h:graphicImage>
            </p:column>-->

            <p:column width="5%" headerText="#{custom.labels.get('IDS_LINE_ITEM_DESC')}">
                #{line.oppItemPartDesc}
            </p:column>

            <p:column width="5%" headerText="#{custom.labels.get('IDS_MFG_PART_NUM')}">
                #{line.oppItemPartManf}
            </p:column>

            <p:column  width="5%" headerText="#{custom.labels.get('IDS_CUST_PART')}">
                #{line.oppItemPartCust}
            </p:column>

            <!--#396281: Priatek LI relabels not found-->
            <p:column width="5%" headerText="#{custom.labels.get('IDS_LINE_ITEM_QTY')}" style="text-align: right">          
                <p:outputLabel value="#{line.oppItemQnty}">
                    <!--#601237: thorsonrm - increase qty decimals: changed quantity length to 12--> 
                    <f:convertNumber maxFractionDigits="3" groupingUsed="false" maxIntegerDigits="9"/>
                </p:outputLabel>
            </p:column>
            <!--//19-10-2022 : #9369 : Feature CRM-6201  Opp Line item: add column for extended amount-->
            <p:column width="5%" visible="false" headerText="#{custom.labels.get('IDS_COST')}" style="text-align: right">
<!--                <p:outputLabel value="#{line.oppItemCost}">
                    Bug 1045 : #982069 :unit price decimals messing up report
                    <f:convertNumber maxFractionDigits="3" groupingUsed="true" maxIntegerDigits="10"/>
                </p:outputLabel>-->
                <!--1372 CRM-3047: EarleAZ: line item rounding errors-->
                <h:outputLabel  value="#{line.oppItemCost}">
                    <f:convertNumber  groupingUsed="true"  maxFractionDigits="2"  minFractionDigits="2" />
                </h:outputLabel>
            </p:column>
            <!--3340 CRM-3888: STMicro CRMsync: part number selector dies-->
            <p:column rendered="false"> 
                #{line.crmsyncTsCustom}
            </p:column>
            <!--#1372 CRM-3047: EarleAZ: line item rounding errors-->
            <p:column width="5%" headerText="#{custom.labels.get('IDS_RESALE')}" style="text-align: right">           
<!--                <p:outputLabel value="#{line.oppItemResale}">
                    Bug 1045 : #982069 :unit price decimals messing up report
                    <f:convertNumber minFractionDigits="3" maxFractionDigits="5" groupingUsed="false" maxIntegerDigits="15"/>
                </p:outputLabel>-->
                <h:outputLabel  value="#{line.oppItemResale}">
                    <!--9910  CRM-6404  quote decimal places for unit price not consistent with opps, downflow fails-->
                    <!--9923 Quote Refactoring : CRM-6404  quote decimal places for unit price not consistent with opps, downf-->
                    <f:convertNumber  groupingUsed="true"  maxFractionDigits="6"  minFractionDigits="6" />
                </h:outputLabel>
            </p:column>
            <!--//19-10-2022 : #9369 : Feature CRM-6201  Opp Line item: add column for extended amount-->
            <p:column width="5%" headerText="Extended Amt" style="text-align: right">
                <!--//31-10-2022 : #9504 : ESCALATION CRM-6295: Extended amount rounding16 places in Line Items-->
                <h:outputText  value="#{line.oppItemQnty*line.oppItemResale}">
                    <f:convertNumber groupingUsed="true" maxFractionDigits="2"/>
                </h:outputText>
            </p:column>
            <!--1709 Salesforce > Opportunity Line items sync-->
            <!-- PMS 2801: CRM-3668: GUI CRMsyncs surfacing part number sync button incorrectly-this column is only visible if Opp princi is configured to Salesforce-->
            <!--#5821: Aurinko CRMSync: Opp Line Items > Conflict/Error-->
            <!--#6265: Sugar-CRM -  for API->  line items - sync showing-->
            <p:column width="5%" rendered="#{opportunities.slaeForceFlag or (oAuthBasedSyncService.princiInterface ==3)}">
                <!--class="#{line.oppItemSyncId != ''?'btn-primary':''}"--> 
                <!--1461 CRM-3100: FW: Questions/Comments from SOUTHWEST-->
                <!--#5695: Aurinko CRMSync: Opportunity Line Items-->
                <!--#6019: line irem link->blue icon showing for unlinked line item-->
                <!--#6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp-->
                <p:commandButton icon="fa fa-refresh" 
                                 class="#{empty line.oppItemSyncId ? 'rpt_btn0' : 'btn-primary'}"   
                                 title="Sync" style="width: 20px;height: 20px;background: none"
                                 onclick="PF('dlgProcAurinkoOpLI').show();"
                                 oncomplete="PF('dlgProcAurinkoOpLI').hide();PF('syncLnDlg').show()" update="frmOpp:tabOpp:lnItmId"
                                 actionListener="#{oppLineItems.selLnItms(line)}" rendered="#{opportunities.oppCrmId != ''}">
                </p:commandButton>
                <h:panelGroup id="pgLIAurError" >
                    <!--#6591: Aurinko CRMSync: Sync Conflicts[added oncomplete and passing princiId and oppId]-->
                    <!--#5821: Aurinko CRMSync: Opp Line Items > Conflict/Error-->
                    <p:commandLink id="clLIAurError" onclick="PF('dlgProcAurinkoOpLI').show();"
                                   action="#{oAuthBasedSyncService.ppltAurinkoOppLICnflct(oAuthBasedSyncService.linkedPrinciId ,opportunities.oppId,line.oppItemId)}"
                                   update=":frmAurinkoOppLICnftDet" oncomplete="PF('dlgProcAurinkoOpLI').hide();PF('dlgAurinkoOppLICnftDet').show()"
                                   style=" margin-left: 5px" 
                                   rendered="#{(not empty line.aurinkoError) or (line.aurinkoWarning)}">
                        <i class="fa fa-warning" style="font-size: 1.5rem; color: red" ></i>
                    </p:commandLink>
                </h:panelGroup>
            </p:column>
        </p:dataTable> 
    </h:panelGroup>

    <!--#200 CRM-2413:  Bridgerep: Deletion Confirmation message for opp line items-->
    <p:confirmDialog header="Confirm delete"  width="400" global="false"
                     message="Are you sure to delete this #{custom.labels.get('IDS_LINE_ITEM')}? "
                     widgetVar="confirmationLnItm" >
        <div class="div-center">
            <!--#6265: Sugar-CRM -  for API->  line items - sync showing-->
            <!--14-04-2022 7426 Aurinko CRMSync: external line item not displaying issue-->
            <!--21-04-2022 7776 Aurinko CRMSync: Update Last Modified timestamp of Opportunity-->
            <p:commandButton value="Delete" onclick="PF('dlgProcessing').show();" actionListener="#{oppLineItems.delete(oppLineItems.recId)}"
                             class="btn btn-danger btn-xs" process="@this"  
                             update="dtLine :frmOpp:tabOpp:inpTxtValue :frmOpp:tabOpp:dtLine :frmSummary"  oncomplete="PF('confirmationLnItm').hide();PF('wvDtLine').filter();PF('dlgProcessing').hide();"  />
            <p:spacer width="4"/>
            <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"    onclick="PF('confirmationLnItm').hide()" 
                             type="button"  />
        </div>
    </p:confirmDialog>

    <!--#1709 Salesforce > Opportunity Line items sync-->
    <!--#5695: Aurinko CRMSync: Opportunity Line Items-->
    <p:dialog id="syncLnDlg"  widgetVar="syncLnDlg"   height="200" width="700"  modal="true"
              header="Sync #{custom.labels.get('IDS_LINE_ITEM')}" >
        <p:ajax event="close" update=":frmOpp:tabOpp:dtLine"  listener="#{oppLineItems.refreshTable()}"/>
        <!--4267 CRM-4212: Salesforce CRMSync updates--> 
        <!--<p:remoteCommand autoRun="true" update=":frmOpp:tabOpp:lnItmId"/>-->
        <!--       <p:ajax event="close" update="@all" oncomplete="zxz()"/>
               <p:ajax event="close" oncomplete="PF('oli').filter(); PF('oli').clearFilters();" /> -->
        <h:panelGrid columns="3" cellpadding="5">
            <p:outputLabel  value="External #{custom.labels.get('IDS_LINE_ITEM')} ID" />
            <p:outputLabel class="m" value=":" />
            <h:panelGroup id="lnItmId">
                <p:spacer width="150" height="0"/>
                <p:outputLabel rendered="#{opportunities.oppCrmLineItemId == ''}"  value="[Target #{custom.labels.get('IDS_LINE_ITEM')} ID]"    />
                <p:outputLabel    value="#{opportunities.oppCrmLineItemId}"  />

<!--                <br/>
                <p:outputLabel  value="Part number==> #{oppLineItems.oppItemPartManf}" />
                <br/>
                <p:outputLabel  value="Part Description==> #{oppLineItems.oppItemPartDesc}" />
                <br/>-->
                <p:spacer width="8"/>
                <!--#1709 Salesforce > Opportunity Line items sync-->

<!--                     <p:commandButton   actionListener="#{oAuthLogin.populateSFOppLineItems(opportunities.syncUser,opportunities.oppCrmId)}"  icon="fa fa-search" class="btn-primary btn-xs"  update=":frmOpp:tabOpp:limappingList" immediate="true" oncomplete="PF('prcDlg1').hide();" rendered="#{not oAuthBasedSyncService.isAuthenticated}" >
                    <p:ajax listener="#{oAuthLogin.populateLineItemDlg(opportunities.syncUser,opportunities.oppId,opportunities.oppCrmId)}"/>
                    </p:commandButton>-->

                <!--#5695: Aurinko CRMSync: Opportunity Line Items-->
                <!--#6251: Aurinko CRMSync: Configure Sync for Parent company-->
                <!--#6327: Aurinko CRMSync: Line Item Lookup-->
                <p:commandButton actionListener="#{oAuthBasedSyncService.ppltAurinkoLineItem(oAuthBasedSyncService.linkedPrinciId,null)}"
                                 onclick="PF('prcDlg1').show();" immediate="true" oncomplete="PF('wvDtAurinkoOppLI').clearFilters();PF('prcDlg1').hide();PF('dlgAurinkoOppLILookup').show();" 
                                 icon="fa fa-search" class="btn-primary btn-xs" update=":frmAurinkoOppLILookup:dtAurinkoOppLI :frmAurinkoOppLILookup:btnAurinkoMoreOppLI"
                                 rendered="#{oAuthBasedSyncService.princiInterface ==3 }" disabled="#{not empty opportunities.oppCrmLineItemId}"/>
                <p:spacer width="4"/>

                <p:commandButton actionListener="#{oppLineItems.syncOpp(opportunities.oppCrmLineItemId)}" id="syncLnItmBtn"
                                 immediate="true" update=":frmOpp:tabOpp:dtLine" 
                                 value="Sync" rendered="#{(opportunities.oppCrmLineItemId != '') and (oAuthBasedSyncService.princiInterface!=3)}"
                                 style="height:26px !important;"
                                 title="Sync to CRM System"
                                 styleClass="viewOpp btn-primary btn-sm" />  

                <p:commandLink style="float:right;padding:4px;"  value="Clear"  rendered="#{opportunities.oppCrmLineItemId != ''}"
                               onclick="PF('dlgCnfUnlinkAruinkoOppLI').show();"
                               immediate="true"  />

                <!--#6081: Aurinko CRM Sync: Line Item Creation-->
                <!--#6801: Aurinko CRMSync: Sync Status[update attribute]-->
                <!--14-06-2022 8215 Aurinko CRMSync: Usability updates on Line Items tab > New TS Line item-->
                <p:remoteCommand name="rcCheckIdAndPrefilter" id="rcCheckIdAndPrefilter" autoRun="false" onstart="PF('dlgFetchingRecords').show();"
                                 actionListener="#{oAuthBasedSyncService.pricebookIdAndPrefilter(oAuthBasedSyncService.linkedPrinciId ,oAuthBasedSyncService.seldPBook.oppItemSyncId,oAuthBasedSyncService.partNumber, loginBean.userId)}" 
                                 oncomplete="PF('dlgFetchingRecords').hide();"/>

                <!--14-06-2022 8215 Aurinko CRMSync: Usability updates on Line Items tab > New TS Line item-->
                <p:commandButton value="New" actionListener="#{oAuthBasedSyncService.newAurinkoOppLineItem(opportunities.oppPrincipal,opportunities.oppId,oppLineItems.oppItemId, oppLineItems.oppItemPartManf,oppLineItems.oppItemPartDesc)}"
                                 immediate="true" oncomplete="PF('dlgAurinkoNewLineItem').show();rcCheckIdAndPrefilter()" 
                                 class="btn-primary btn-xs" update=":frmAuinkoNewLineItem :frmAuinkoNewLineItem:btnCrtNewLI"
                                 rendered="#{(oAuthBasedSyncService.princiInterface ==3) and (empty opportunities.oppCrmLineItemId) }"/>
            </h:panelGroup>

        </h:panelGrid>
    </p:dialog>
    <p:confirmDialog header="Confirm Unlinking"  width="400" global="false"
                     message="Are you sure to unlink this #{custom.labels.get('IDS_LINE_ITEM')}? "
                     widgetVar="dlgCnfUnlinkAruinkoOppLI" >
        <div class="div-center">
            <!--#6019: line item link->blue icon showing for unlinked line item-->
            <!--20-04-2022 7776 Aurinko CRMSync: Update Last Modified timestamp of Opportunity-->
            <p:commandButton value="Yes" actionListener="#{oppService.removeOppItemLink}"
                             class="btn btn-danger btn-xs"  update="frmOpp:tabOpp:lnItmId :frmOpp:tabOpp:dtLine :frmSummary"
                             oncomplete="PF('dlgCnfUnlinkAruinkoOppLI').hide();PF('wvDtLine').filter();"  />
            <p:spacer width="4"/>
            <p:commandButton value="No"  class="btn btn-warning btn-xs"    onclick="PF('dlgCnfUnlinkAruinkoOppLI').hide()" 
                             type="button"  />
        </div>
    </p:confirmDialog>

    <!--#5695: Aurinko CRMSync: Opportunity Line Items-->
    <p:dialog widgetVar="dlgProcAurinkoOpLI" closable="false" modal="true" showHeader="false" resizable="false" >
        <p:outputPanel >
            <br />
            <h:graphicImage  library="images" name="ajax-loader.gif"   />
            <p:spacer width="5" />
            <p:outputLabel value="Please wait fetching details..." />
            <br /><br />
        </p:outputPanel>
    </p:dialog>



    <script type="text/javascript">

        function rowEdit() {
            PF('dll').show();
            document.getElementById('frmOpp:tabOpp:lbtsave').style.display = 'none';
            document.getElementById('frmOpp:tabOpp:lbtupd').style.display = 'block';
        }
        function showAddDialog() {
            //            PF('dll').show();
            //            document.getElementById('frmOpp:tabOpp:tabLine:lpm').value = '';
            //            document.getElementById('frmOpp:tabOpp:tabLine:lpc').value = '';
            //            document.getElementById('frmOpp:tabOpp:tabLine:lqty').value = '';
            //            document.getElementById('frmOpp:tabOpp:tabLine:lcost').value = '';
            //            document.getElementById('frmOpp:tabOpp:tabLine:lres').value = '';
            //            document.getElementById('frmOpp:tabOpp:tabLine:ldes').value = '';
            document.getElementById('frmOpp:tabOpp:lbtupd').style.display = 'none';
            document.getElementById('frmOpp:tabOpp:lbtsave').style.display = 'block';
        }

        function showDlg() {
            alert(1);
            var e = PF('ee1').value;
            var itm = e.options[e.selectedIndex].value;
            alert(itm);
            switch (itm) {
                case '#{custom.labels.get('IDS_SAMPLES')}' :
                    
                    PF('dlgSample').show();
                    break;
                case '#{custom.labels.get('IDS_QUOTES')}' :
                    PF('dlgQuote').show();
                    break;
                case '#{custom.labels.get('IDS_DWINS')}' :
                    PF('dlgDesign').show();
                    break;
                case '#{custom.labels.get('IDS_PROD_REGS')}' :
                    PF('dlgRegist').show();
                    break;
                case '#{custom.labels.get('IDS_ACC_REGS')}' :
                    PF('dlgRegistAcc').show();
                    break;
            }
            e.selectedIndex = 0;

        }
        function smb() {
            alert(12);
        }
    </script>
</ui:composition>

