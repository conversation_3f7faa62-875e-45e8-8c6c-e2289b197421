<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">
    <p:dialog   header="#{custom.labels.get('IDS_QUOTES')}" widgetVar="dlgQuote" modal="true" >
        <h:form id="oppLineQuote">
            <p:remoteCommand name="initialize1" update=":oppLineQuote" actionListener="#{enquiry.initialize('quot', 0)}" autoRun="false"/>
            <p:panelGrid id="gridQuote" styleClass="inqDig">
                <p:row>
                    <p:column colspan="2">
                        <!--#6096: CRM-4827: JOHN <PERSON>IM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp-->
                        <!--#6265: Sugar-CRM -  for API->  line items - sync showing-->
                        <p:commandButton  value="Save" actionListener="#{quotesHdr.save(quotesHdr.recId)}"
                                          class="btn btn-xs btn-success"
                                          update=":frmOpp:tabOpp:dtLine" oncomplete="PF('wvDtLine').filter();"/>
                         <p:spacer width="4"/>
                        <p:commandButton type="button" value="Cancel" 
                                         class="btn btn-xs btn-warning" 
                                         onclick="PF('dlgQuote').hide()"/>
                    </p:column>
                </p:row>


                <p:row>
                    <p:column>
                        <p:outputLabel value="Quote Number:"/>
                    </p:column>
                    <p:column>
                        <p:inputText value="#{quotesHdr.quotNumber}" maxlength="30" />
                    </p:column>

                    <p:column>
                        <p:outputLabel value="Recipient:"/>
                    </p:column>
                    <p:column>
                        <p:selectOneMenu value="#{quotesHdr.quotReceipient}" style="width:100%">
                            <!--01/11/2023: 12426 CRM-7454: Quote - Quote templates: single mfg quotes tie to specific mfg (JSF)-->
                            <!--Correction to the internal values-->
                            <f:selectItem itemLabel="#{custom.labels.get('IDS_CUSTOMER')}" itemValue="Customer"/>
                            <f:selectItem itemLabel="#{custom.labels.get('IDS_DISTRI')}" itemValue="Distributor"/>
                            <f:selectItem itemLabel="#{custom.labels.get('IDS_CEM')}" itemValue="CEM"/>                
                        </p:selectOneMenu>
                    </p:column>
                </p:row>

                <p:row>
                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_RFQ_NUM')}:"/>
                    </p:column>
                    <p:column>
                        <p:inputText value="#{quotesHdr.quotRfqNum}"  maxlength="30"  />
                    </p:column>

                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')} Ref.No:"/>
                    </p:column>
                    <p:column>
                        <p:inputText value="#{quotesHdr.quotCustRefNum}" maxlength="30" />
                    </p:column>
                </p:row>

                <p:row>
                    <p:column>
                        <p:outputLabel value="Quote Date:"/>
                    </p:column>
                    <p:column>
                        <p:calendar value="#{quotesHdr.quotDate}"   pattern="#{globalParams.dateFormat}"   >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                        </p:calendar>
                    </p:column>

                    <p:column>
                        <p:outputLabel value="Expiry Date:"/>
                    </p:column>
                    <p:column>
                        <p:calendar value="#{quotesHdr.quotExpiry}"   pattern="#{globalParams.dateFormat}"   >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                        </p:calendar>
                    </p:column>
                </p:row>

                <p:row rendered="#{quotesHdr.fromOpp}">
                    <p:column>
                        <p:outputLabel value="Status:"/>
                    </p:column>
                    <p:column>
                        <p:selectOneRadio value="#{quotesHdr.quotOpenStatus}">
                            <f:selectItem itemLabel="Open" itemValue="0" />
                            <f:selectItem itemLabel="Close" itemValue="1" />
                        </p:selectOneRadio>
                    </p:column>

                    <p:column>
                        <p:outputLabel value="Quote Status:"/>
                    </p:column>
                    <p:column>
                        <!--11755: 01/08/2023: CRM-7170: Quotes: Quote Details > Quote Status, Canned Notes-->
                        <p:selectOneMenu value="#{quotesHdr.quotDelivStatus}" style="width: 100%" disabled="#{enquiry.editable}">
                                        <f:selectItem itemLabel="Select One" itemValue="" />
                                       <f:selectItems value="#{quotesHdr.quoteStatusList}" var="sts" itemLabel="#{sts.optOption}" itemValue="#{sts.recId}"/>
                                    </p:selectOneMenu>
                    </p:column>
                </p:row>

                <p:row rendered="#{quotesHdr.fromOpp}">
                    <p:column>
                        <p:outputLabel value="Application:"/>
                    </p:column>
                    <p:column>
                        <p:inputText value="#{quotesHdr.quotApplication}" />
                    </p:column>

                    <p:column>
                        <p:outputLabel value="Owner"/>
                    </p:column>
                 <!--#8363    Opp > Copy into : Owner field displays id-->
                    <p:column>
                        <p:selectOneMenu value="#{quotesHdr.quotOwner}" id="ddOppOwner" style="width: 100%" >
                                        <f:selectItems value="#{users.dependentUserListForQuotes}" var="user" 
                                                       itemValue="#{user.userId}" itemLabel="#{user.userName}" />
                                    </p:selectOneMenu>
                    </p:column>
                </p:row>

                <p:row rendered="#{quotesHdr.fromOpp}">
                    <p:column>
                        <p:outputLabel value="Bid/Buy:"/>
                    </p:column>
                    <p:column>
                        <p:selectOneMenu value="#{quotesHdr.quotBidBuy}" style="width:92%">
                            <f:selectItem itemLabel="Bid" itemValue="Bid"/>
                            <f:selectItem itemLabel="Buy" itemValue="Buy"/>            
                        </p:selectOneMenu>
                    </p:column>

                    <p:column>
                        <p:outputLabel value="FollowUp:"/>
                    </p:column>
                    <p:column>
                        <p:calendar value="#{quotesHdr.quotFollowUp}"   pattern="#{globalParams.dateFormat}"   >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                        </p:calendar>
                    </p:column>
                </p:row>

                <p:row rendered="#{quotesHdr.fromOpp}">
                    <p:column>
                        <p:outputLabel value="Comments:"/>
                    </p:column>
                    <p:column colspan="3"> 
                        <p:inputTextarea rows="2"  value="#{quotesHdr.quotComments}" style="width: 99%;text-overflow: scroll" autoResize="false" />
                    </p:column>
                </p:row>

                <p:row>
                    <p:column colspan="3" >

                    </p:column>
                    <p:column style="float: right">
                        <p:commandButton id="btnLoadQuote" value="#{!quotesHdr.fromOpp ? 'More Options...' : 'Less Options'}" style="float: right;height: 30px" actionListener="#{quotesHdr.oppCall()}" update=":oppLineQuote" class="btn btn-xs btn-primary"/>
                    </p:column>
                </p:row>
            </p:panelGrid>

        </h:form>
    </p:dialog>

    <p:confirmDialog header="Confirmation" global="false"  message=" Quote already exists, Please select the option"   
                     widgetVar="confirmQuoteChange" >
        <h:form id="confirmationDlg">
            <p:commandButton value="Create" class="btn btn-xs btn-primary"
                             action="#{quotesHdr.quoteSaveOrUpdateFromOpp(1)}"  process="@this"      />
            <p:spacer width="4"/>
            <p:commandButton value="Update" class="btn btn-xs btn-success"
                             action="#{quotesHdr.quoteSaveOrUpdateFromOpp(0)}"  process="@this"      />
             <p:spacer width="4"/>
            <p:commandButton value="Cancel" class="btn btn-xs btn-warning"   onclick="PF('confirmation').hide()" 
                             type="button"  />
        </h:form>
    </p:confirmDialog>

</ui:composition>

