<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: LineItemCustomFields.xhtml
// Author: Sarayu
//*********************************************************
* Copyright (c) 2017 Indea Design Systems Private Limited, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:pe="http://primefaces.org/ui/extensions">
    <!--//20-4-2022 : #7781 :  CRM-5601 schedules: added pns automatically need to inherit the header schedule-->
    <!--    <p:remoteCommand name="rcSIList" id="rcSIList" immediate="true" 
                         actionListener="#{oppItemSchedulesService.load(opportunities.oppId,oppLineItems.recId)}" oncomplete="PF('itemSch').clearFilters();" 
                         autoRun="true" update="frmOpp:tabOpp:tabLine:pnlSIList" />-->
    <!--//20-4-2022 : #7781 :  CRM-5601 schedules: added pns automatically need to inherit the header schedule-->
    <pe:blockUI target="dTableSIList" widgetVar="blockUiLoading">
        <h:graphicImage library="images" name="ajax-loader1.gif"
                        style="margin-right: 12px; vertical-align: middle;"/>
        <h:outputText value="Checking for Line Item Schedules..." style="white-space: nowrap;"/>
    </pe:blockUI>
    <p:remoteCommand onstart="PF('blockUiLoading').block();" autoRun="true" />
    <p:remoteCommand name="rcLoadschedules" autoRun="false" action="#{oppItemSchedulesService.load(opportunities.oppId,oppLineItems.recId)}" 
                     oncomplete="PF('itemSch').clearFilters();PF('waddschditm').enable(); PF('blockUiLoading').unblock();" 
                     update=":frmOpp:tabOpp:tabLine:pnlGrpSIList" />

    <h:panelGroup id="pnlGrpSIList" class="pnls">

        <p:outputPanel id="pnlSIList">
             <!--//20-4-2022 : #7781 :  CRM-5601 schedules: added pns automatically need to inherit the header schedule-->
            <p:outputPanel style="width: 100%;" >
                <!--16-03-2022 : #7514 :CRM-5404: Opportunity Line Items-->
                <p:commandButton id="addschditm"  
                                 class="btn btn-primary btn-xs"
                                 action="#{oppItemSchedulesService.getOppSchedules(opportunities.oppId,oppItemSchedulesService.oppItemSchedules.oppItemScheduleYear)}"
                                 actionListener="#{oppItemSchedulesService.clearScheduleItem()}"
                                 oncomplete="PF('dlgSIDet').show();"
                                 value="New" widgetVar="waddschditm"
                                 update=":frmDlgSIDet"/>
                <p:outputPanel style="float: right">
                    <h:outputLabel for="olSIListSumQty"  value="Total Quantity:" />
                    <p:spacer width="4"/>
                    <h:outputText id="olSIListSumQty" value="#{oppItemSchedulesService.totalQty}" />
                </p:outputPanel>

            </p:outputPanel>

            <br/>
            <p:dataTable value="#{oppItemSchedulesService.scheduleItemList}" 
                         rowKey="#{itemSch.recId}"
                         selectionMode="single"
                         var="itemSch" widgetVar="itemSch"
                         emptyMessage="No Schedules Item found"
                         id="dTableSIList">

                <p:column headerText="Year" filterBy="#{itemSch.oppItemScheduleYear}" filterMatchMode="contains" sortBy="#{itemSch.oppItemScheduleYear}">

                    <h:outputText value="#{itemSch.oppItemScheduleYear}" />
                </p:column >            

                <p:column  headerText="Schedule" filterBy="#{itemSch.oppItemScheduleType}" filterMatchMode="contains" sortBy="#{itemSch.oppItemScheduleType}">   
                    <h:outputText value="#{itemSch.oppItemScheduleType}" />
                </p:column>  

                <p:column headerText="Q1" filterBy="#{itemSch.oppItemQtyQ1}" filterMatchMode="contains" sortBy="#{itemSch.oppItemQtyQ1}">  
                    <h:outputText value="#{itemSch.oppItemQtyQ1}" />
                </p:column>  

                <p:column headerText="Q2"  filterBy="#{itemSch.oppItemQtyQ2}" filterMatchMode="contains" sortBy="#{itemSch.oppItemQtyQ2}">
                    <h:outputText value="#{itemSch.oppItemQtyQ2}" />
                </p:column>  

                <p:column headerText="Q3" filterBy="#{itemSch.oppItemQtyQ3}" filterMatchMode="contains" sortBy="#{itemSch.oppItemQtyQ3}">
                    <h:outputText value="#{itemSch.oppItemQtyQ3}" />
                </p:column>  

                <p:column headerText="Q4"  filterBy="#{itemSch.oppItemQtyQ4}" filterMatchMode="contains" sortBy="#{itemSch.oppItemQtyQ4}">
                    <h:outputText value="#{itemSch.oppItemQtyQ4}" />
                </p:column>

                <p:column headerText="Year Qty" filterBy="#{itemSch.oppItemQtyYear}" filterMatchMode="contains" sortBy="#{itemSch.oppItemQtyYear}">
                    <h:outputText value="#{itemSch.oppItemQtyYear}" />
                </p:column>

                <p:column >
                    <p:commandButton class="btn-primary btn-xs"  
                                     icon="fa fa-pencil" 
                                     title="Edit"
                                     action="#{oppItemSchedulesService.oppItemForUpdate(itemSch)}"
                                     update="frmDlgSIDet:pnlSIDet"
                                     oncomplete="PF('dlgSIDet').show();"/>

                    <p:spacer width="4" />
                    <p:commandButton icon="fa fa-trash" 
                                     styleClass="btn-danger btn-xs"
                                     title="Remove Schedule Item"
                                     action="#{oppItemSchedulesService.setOppItemSchedules(itemSch)}"
                                     oncomplete="PF('dlgCnfmSIList').show();">
                    </p:commandButton>
                </p:column>
            </p:dataTable> 
            <p:dialog header="Confirm delete"  width="400" id="dlgCnfmSIListid" widgetVar="dlgCnfmSIList" >

                <p:outputLabel value="Are you sure?" />
                <br/>
                <br/>
                <div class="div-center">
                    <!--//31-03-2022 : #7514 : CRM-5404: Opportunity Line Items: Default EAU based on Opp Schedules-->
                    <p:commandButton action="#{oppItemSchedulesService.delete(oppItemSchedulesService.oppItemSchedules.recId)}" 
                                     value="Yes" update=":frmOpp:tabOpp:tabLine:pnlSIList"
                                     oncomplete="PF('dlgCnfmSIList').hide();PF('itemSch').filter();" class="btn btn-danger btn-xs"/>
                    <p:spacer width="4" />
                    <p:commandButton value="No" oncomplete="PF('dlgCnfmSIList').hide();" class="btn btn-warning btn-xs" />
                </div>
            </p:dialog>

        </p:outputPanel>
    </h:panelGroup>
    <style>
        .greyClr{
            color: gray;

        }
        .blackClr{
            color: black;

        }

    </style>
</ui:composition>
