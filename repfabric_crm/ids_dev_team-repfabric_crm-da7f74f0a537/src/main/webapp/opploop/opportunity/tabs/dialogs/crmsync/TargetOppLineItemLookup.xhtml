<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui" >
    <!-- 1709 Salesforce > Opportunity Line items sync - Line Items-->
    <p:dialog id="lineItemLookup" widgetVar="lineItemLookup" modal="true" header="Target System Opp Look up" height="400" width="600" maximizable="true" resizable="false">

        <p:ajax event="close" update="limappingList" listener="#{oAuthLogin.clearDialog()}"/>
        <!--      <h:outputLabel value="Fetching Opportunity Records..." />
              <h:graphicImage  library="images" name="ajax-loader.gif" id="loadingImg"  />-->

        <p:dataTable id="limappingList" widgetVar="cmapDlg" paginator="true"
                     scrollHeight="320"
                     scrollable="true"
                     value="#{oAuthLogin.oppLineItemLookup}"                         
                     filterEvent="keyup"
                     filteredValue=""
                     var="cmap"
                     selection="#{oppService.selectedTSoppLineItems}"
                     selectionMode="single"
                     paginatorAlwaysVisible="false"
                     rowKey="#{cmap.oppItemSyncId}" 
                     class="tblmain"
                     >
            <p:ajax event="rowSelect" listener="#{oppService.createOppItemLink}" update=":frmOpp:tabOpp:lnItmId :frmOpp:tabOpp:dtLine"  oncomplete="PF('lineItemLookup').hide();" />
            <!--update=":frmOpp:tabOpp:pnlCRMSYNC :frmOpp:tabOpp :frmOpp:tabOpp:syncLnDlg"-->
            <p:column headerText="oppItemSyncId" rendered="false">
                #{cmap.oppItemSyncId}
            </p:column>
            <p:column headerText="oppItemSyncOppId" rendered="false">
                #{cmap.oppItemSyncOppId}
            </p:column>
            <!--3340 CRM-3888: STMicro CRMsync: part number selector dies-->
            <p:column headerText="Id"  rendered="false">
                #{cmap.crmsyncTsCustom}
            </p:column>
            <p:column headerText="Manf. Part. No." filterBy="#{cmap.oppItemPartManf}" filterMatchMode="contains" sortBy="#{cmap.oppItemPartManf}"  >
                #{cmap.oppItemPartManf}
            </p:column>
            <p:column headerText="Qty" filterBy="#{cmap.oppItemQnty}" filterMatchMode="contains" sortBy="#{cmap.oppItemQnty}"  >
                #{cmap.oppItemQnty}
            </p:column> 
            <p:column headerText="Unit Price" filterBy="#{cmap.oppItemCost}" filterMatchMode="contains" sortBy="#{cmap.oppItemCost}"  >
                #{cmap.oppItemCost}
            </p:column> 
            <!--3340 CRM-3888: STMicro CRMsync: part number selector dies-->
            <p:column rendered="#{!oAuthLogin.oppProdLineStatus}"    headerText="Total Price" filterBy="#{cmap.oppItemResale}" filterMatchMode="contains" sortBy="#{cmap.oppItemResale}"  >
                #{cmap.oppItemResale}
            </p:column> 
        </p:dataTable>
        <!--    2844 CRM Sync: Delay loading opps and tab set to Basic Tab     -->
    </p:dialog>


    <p:dialog widgetVar="prcDlg1" closable="false" modal="true" header="Message" resizable="false" width="250" responsive="true">
        <p:outputPanel >
            <br />
            <p:spacer width="5" />
            <p:outputLabel value="Fetching records... Please wait..." />
            <br /><br />
        </p:outputPanel> 
    </p:dialog>

</ui:composition>

