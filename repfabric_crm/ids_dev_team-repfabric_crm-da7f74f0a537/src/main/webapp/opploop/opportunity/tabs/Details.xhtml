<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">


    <h:inputHidden value="#{opportunities.selTaskId}" id="oppTaskIdh"/>
    <h:inputHidden value="#{opportunities.oppValue}" id="opvalh"/>
    <!--<ui:include src="dialogs/activity/Quotes.xhtml" />  including outside form OpportunityView.xhtml  -->       
    <!--#2441 Related to CRM-1510: SELTEC Fwd: Issues with the new version-->
    <!--<ui:include src="dialogs/activity/Designwin.xhtml" />-->
    <!--<ui:include src="dialogs/activity/Samples.xhtml" />-->
    <!--<ui:include src="dialogs/activity/AccountReg.xhtml" />-->
    <!--<ui:include src="dialogs/activity/Registration.xhtml" />-->

    <!--update=":frmOpp:tabOpp:tblCust"-->
    <!--    <p:remoteCommand name="loadCust" autoRun="false" actionListener="#{viewCompanyLookUp.populateCompanyList(2)}" 
    update=" " oncomplete="PF('dlgCust').show()"/>-->

 <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
    <p:remoteCommand name="applyCRMCust" 
                     actionListener="#{opportunities.applyCompany(viewCompLookupService.selectedCompany)}" 
                     action="#{oppService.selectCompany(2,viewCompLookupService.selectedCompany.compId,
                               viewCompLookupService.selectedCompany.compName,
                               viewCompLookupService.selectedCompany.compSmanId, 
                               viewCompLookupService.selectedCompany.compType)}"
                     update=" :frmOpp:tabOpp:oppCustomerName  
                     :frmOpp:tabOpp:pnlclrcust                               
                     :frmOpp:tabOpp:oppCustomerContactName 
                     :frmOpp:tabOpp:oppSman
                     :frmOpp:tabOpp:oppSman
                     :frmOpp:tabOpp:inpTxtDistriName   
                     :frmOpp:tabOpp:pnlclrdist                
                     :frmOpp:tabOpp:oppDistriContactName 
                     :frmOpp:btnRelated 
                     :frmOpp:tabOpp:custTypeName
                     :frmOpp:tabOpp:lblCust
                     :frmOpp:tabOpp:inpTxtCustName
                      :frmOpp:tabOpp:inpTxtCustName2
                     "/>  
    <!--// 27-12-2022 : #10262 : Registrations: App Custom Feature-->
    <!--<p:remoteCommand name="saveOppRegister" autoRun="false" process="@this" immediate="true" actionListener="#{registrationService.saveOppRegistration(opportunities.oppId)}"/>-->
    <!--Bug #11349:ESCALATIONS CRM-7030   Design Registrations: Get duplicated if same number is used by harshithad on 06/06/23-->
    <p:remoteCommand name="loadOppRegister" autoRun="false" process="@this" immediate="true" actionListener="#{registrationService.loadOppRegistration(opportunities.oppId,opportunities.cloneFlag)}" update=":frmOpp:tabOpp:oppRegistrationPanel"/>
    <!--10-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
    <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
    <p:remoteCommand name="applyCont1" 
                     actionListener="#{opportunities.applyContact1(viewContLookupService.selectedContact)}"
                     update=":frmOpp:tabOpp:oppCustomerContactName1 :frmOpp:tabOpp:inpTxtCustName  :frmOpp:tabOpp:inpTxtCustName2 :frmOpp:tabOpp:oppSman :frmOpp:tabOpp:pnlclrcust :frmOpp:tabOpp:lblCust :frmOpp:tabOpp:oppCustomerName
                     " />
    <!--    Feature 5416 Opportunities:  Fetch Potential by Opp Activity (% by stage relationship)-->
    <!--18-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
    <!--29-08-2022 8606 CRMSYNC-48   {CRMSync} When you change the principle in a linked opp, the link with the old opp is n-->
     <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
    <p:remoteCommand name="applyActivePrinci" 
                     actionListener="#{opportunities.checkAllowedPrinciChange(viewCompLookupService.selectedCompany,oppLineItems.listLineItems)}" 
                     action="#{oppService.selectPrincipal(opportunities.allowPrinciChange ,1,viewCompLookupService.selectedCompany.compId,
                               viewCompLookupService.selectedCompany.compName,
                               viewCompLookupService.selectedCompany.compSmanId, 
                               viewCompLookupService.selectedCompany.compType)}" 
                     update=":frmOpp:tabOpp:inpTxtPrinciName   
                     :frmOpp:tabOpp:pnlclrprin
                     :frmOpp:tabOpp:oppPrincipalContactName
                     :frmOpp:tabOpp:oppSman :frmOpp:btnRelated
                     :frmOpp:tabOpp:ddActivity :frmOpp:tabOpp:ddOppStatus 
                     :frmOpp:tabOpp:tsk :frmOpp:tabOpp:lblPrinci :frmOpp:tabOpp:spinnerPotential"   />
    <!--    <p:remoteCommand name="updateSpinner" autoRun="true" update=":frmOpp:tabOpp:spinnerPotential"  />-->
    <!--#162 CRM-2385 Manu-tek: can't access "Activity" drop down in Opp-->
    <!--#1163: CRM-2898: Opp Stage/Status change if Principal Contact is changed - Moved the update elements to the backend code-->
    <!--31-03-2021: #4010: CRM-4117: Clear button not shown on contact selection-->
    <!--10-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
    <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
    <p:remoteCommand name="applyCont2" 
                     actionListener="#{opportunities.applyContact2(viewContLookupService.selectedContact)}"
                     update=":frmOpp:tabOpp:oppPrincipalContactName :frmOpp:tabOpp:pnlclrprin " /> 
    <!--3206 CRM-3820: Remove mandatory field from "Contractor" (used to be customer)-->
    <!--10-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
     <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
    <p:remoteCommand name="applyDistri"    
                     actionListener="#{opportunities.applyDistri(viewCompLookupService.selectedCompany)}" 
                     action="#{oppService.selectCompany(3,viewCompLookupService.selectedCompany.compId,
                               viewCompLookupService.selectedCompany.compName,
                               viewCompLookupService.selectedCompany.compSmanId, 
                               viewCompLookupService.selectedCompany.compType)}"
                     update=":frmOpp:tabOpp:inpTxtDistriName    :frmOpp:tabOpp:pnlclrdist                            
                     :frmOpp:tabOpp:oppDistriContactName                                                                
                     :frmOpp:tabOpp:oppSman :frmOpp:tabOpp:pnlclrcust 
                     :frmOpp:btnRelated frmOpp:tabOpp:oppCustomerContactName1 :frmOpp:tabOpp:lblDistri 
                     :frmOpp:tabOpp:inpTxtCustName2 :frmOpp:tabOpp:lblCust :frmOpp:tabOpp:oppCustomerName
                     " />
    <!--31-03-2021: #4010: CRM-4117: Clear button not shown on contact selection-->
    <!--10-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
    <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
    <p:remoteCommand name="applyCont3" 
                     actionListener="#{opportunities.applyContact3(viewContLookupService.selectedContact)}"
                     update=":frmOpp:tabOpp:oppDistriContactName :frmOpp:tabOpp:inpTxtDistriName  :frmOpp:tabOpp:oppSman :frmOpp:tabOpp:lblDistri :frmOpp:tabOpp:pnlclrdist
                    " /> 
    <p:remoteCommand name="loadEndUser" autoRun="false"
                     actionListener="#{viewCompanyLookUp.populateCompanyList(4)}" 
                     update=" "
                     oncomplete="PF('dlgEndUser').show()"/>
    <!--4142 CRM-4179: contacts: parties contacts not showing under the contacts tab of opp-->
    <p:remoteCommand name="applyOtherComp"                     
                     action="#{oppService.selectCompany(viewCompLookupService.selectedCompany.compId,
                               viewCompLookupService.selectedCompany.compName,
                               viewCompLookupService.selectedCompany.compTypeName)}" 
                     update="frmOpp:tabOpp:tblOthrList :frmOpp:tabOpp:dtc"  oncomplete="loadOppConts();" />
    <p:remoteCommand id="rcOppCustomFields" name="rcOppCustomFields" update="pnlOppCustFields" actionListener="#{oppCustomFields.populateCustomFields()}" autoRun="true"/>
    <!--1638 Opportunities > Other Parties - Contacts-->
    <p:remoteCommand name="applyContacts" 
                     actionListener="#{oppService.applyContacts(viewContLookupService.selectedContact)}"
                     update="frmOpp:tabOpp:tblOthrList :frmOpp:tabOpp:dtc"  oncomplete="loadOppConts();" /> 


    <p:blockUI block="pnlOppCustFields" trigger="rcOppCustomFields"/>

    <h:panelGroup id="pnlBASIC">

        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Primary Information</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            </div>
        </div>  
        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-2 ui-lg-2"  style="height: 34px !important;">
                <!--3206 CRM-3820: Remove mandatory field from "Contractor" (used to be customer)-->
                <p:outputLabel id="lblCust" for="#{opportunities.directSales ? 'inpTxtCustName2' : 'inpTxtCustName'}" value="#{custom.labels.get('IDS_CUSTOMER')}"
                               class="#{fieldsMstService.fields.get('OPP_CUST') == 1 ? opportunities.directSales and opportunities.oppCustomer!=0 ?'':'required':''}" /><br/>
                <p:outputLabel class="td_txt" id="custTypeName" value="#{opportunities.oppCustType > 3 ? companyTypesMst.getCompanytypeName(opportunities.oppCustType) : ''}"  />



            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-4">   
                <h:panelGroup class="ui-inputgroup"  >  
                    <h:panelGroup id="oppCustomerName"  >
                         <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
<!--                        <p:commandLink id="lnkC" onclick="if (#{opportunities.oppCustomer!=0}) {
                                    window.open('../../opploop/companies/CompanyDetails.xhtml?id=#{opportunities.oppCustomer}', '_self');
                                }"   >   -->
                            <!--3206 CRM-3820: Remove mandatory field from "Contractor" (used to be customer)-->
                            <!--                            05-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
                            <p:autoComplete value="#{opportunities.oppCustomerName}" styleClass="#{(opportunities.oppCustomer==0) ? '' : 'cursor-link'};"
                                          completeMethod="#{opportunities.completeCustomer}" minQueryLength="3"
                                         placeholder="[Not selected]" panelStyleClass="acCompCont"
                                         widgetVar="wopc" rendered="#{!opportunities.directSales}"
                                         id="inpTxtCustName" scrollHeight="200"
                                         style="#{opportunities.directSales ? 'display: none': 'width:100%;'}; #{empty opportunities.oppCustomerName ? 'cursor: default!important;':'cursor: pointer!important;'}" >
                                         <p:ajax event="itemSelect" onstart="PF('dlgProcessingOnTask').show();" listener="#{opportunities.selectCustomer}" oncomplete="PF('dlgProcessingOnTask').hide();"/>
                            </p:autoComplete>
                            <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
                            <p:autoComplete value="#{custom.labels.get('IDS_DIRECT_SALES') }" styleClass="#{(opportunities.oppCustomer==0) ? '' : 'cursor-link'};"
                                          completeMethod="#{opportunities.completeCustomer}" minQueryLength="3"
                                         placeholder="[Not selected]" panelStyleClass="acCompCont"
                                         widgetVar="wopc" rendered="#{opportunities.directSales}"
                                         id="inpTxtCustName2" scrollHeight="200"
                                         >
                                         <p:ajax event="itemSelect" onstart="PF('dlgProcessingOnTask').show();" listener="#{opportunities.selectCustomer}" oncomplete="PF('dlgProcessingOnTask').hide();"/>
                            </p:autoComplete>
                        <!--</p:commandLink>-->
                        <!--2205 CRM-3472: Jobs:  when creating a opp from within a job, default the relevant job fields-->
                        <!--//                   Feature #8415 Direct Request: ISQuotes:  Related Companies - Add option to create opportunity-->
                        <!--//             #11426   CRM-6997   Jobs related: copy into opp not copying job details like name, parties, $s, reporting com-->
                                                <!--<p:inputText id="inpTxtCustName2" value="#{opportunities.oppCustomer == opportunities.oppDistri ? '&lt;': opportunities.oppCustomerName} #{opportunities.oppCustomer == opportunities.oppDistri ? custom.labels.get('IDS_DIRECT_SALES') : opportunities.oppCustomerName} #{opportunities.oppCustomer == opportunities.oppDistri ? '&gt;': opportunities.oppCustomerName}"-->
                         <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
<!--                        <p:inputText id="inpTxtCustName2" value="#{'&lt;'.concat(custom.labels.get('IDS_DIRECT_SALES')).concat('&gt;')}"
                                     required="true"  placeholder="[Not selected]"  readonly="true"  rendered="#{opportunities.directSales}" 
                                     class="#{opportunities.directSales ?'required': fieldsMstService.fields.get('OPP_CUST') == 1 ? 'required':''}" />-->
                    </h:panelGroup>
                    <!-- #7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - update cust btn when close status close -->
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" id="oppCustBtn"
                                      actionListener="#{viewCompLookupService.listCRM(2, 'applyCRMCust')}" 
                                      oncomplete="PF('lookupComp').show()"
                                      update=":frmOpp:tabOpp:tblCust :formCompLookup" 
                                      styleClass="btn-info btn-xs" disabled="#{opportunities.oppCloseStatus!=0 or opportunities.oppCloseFlag==true}"/>
                </h:panelGroup>
            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel value="Contact"/>
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4" style="display: inherit;">
                <h:panelGroup id="oppCustomerContactName"  class="ui-inputgroup"  > 
       <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->              
                    <!--                    #3750CRM-2234: Fwd: editing contacts within companies-->
<!--                    <h:link id="lnkCc" disabled="#{opportunities.oppCustomerContact==0 or opportunities.oppCloseFlag==true}" outcome="/opploop/contacts/ContactDetails.xhtml?id=#{opportunities.oppCustomerContact}">
                        #2962 CRM-1868: Fwd: New Opportunity - Customer Contact
                        <f:param name="frm" value="#{view.viewId}" />
                        <f:param name="moduleId" value="#{opportunities.oppId}" /> -->
                        <!--05-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
                       <p:autoComplete value="#{opportunities.oppCustomer == opportunities.oppDistri ? opportunities.oppCustomerContact == opportunities.oppDistriContact ? '': opportunities.oppCustomerContactName : opportunities.oppCustomerContactName}"
                                     style="width: 100% ;#{((opportunities.oppCustomerContact==0) or (opportunities.oppCloseFlag==true)) ? 'cursor: default!important;':'cursor: pointer!important;'}"
                                     completeMethod="#{opportunities.completeCustomerContacts}"
                                     minQueryLength="3"
                                     readonly="true"
                                     rendered="false"
                                     placeholder="[Not selected]" 
                                     id="oppCustomerContactName3" > 
                                  <p:ajax event="itemSelect"  listener="#{opportunities.selectedCustomerContacts}"/>
                       </p:autoComplete>
                         <p:autoComplete value="#{opportunities.oppCustomerContactName}"
                                     style="width: 100% ;#{((opportunities.oppCustomerContact==0) or (opportunities.oppCloseFlag==true)) ? 'cursor: default!important;':'cursor: pointer!important;'}"
                                     completeMethod="#{opportunities.completeCustomerContacts}"
                                     minQueryLength="3"
                                     placeholder="[Not selected]" scrollHeight="200"
                                     id="oppCustomerContactName1" > 
                                  <p:ajax event="itemSelect"  listener="#{opportunities.selectedCustomerContacts}"/>
                       </p:autoComplete>
                    <!--</h:link>-->
                    <!--PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                    <!-- #7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - update cust cnt btn when close status close -->
                    <p:commandButton   disabled="#{opportunities.oppCloseStatus!=0 or opportunities.oppCloseFlag==true}"
                                       icon="fa fa-search" title="Choose Contact" immediate="true" id="oppCustCntBtn"
                                       action="#{viewContLookupService.disableContNewBtn(opportunities.oppCustomer)}"
                                       actionListener="#{viewContLookupService.list('applyCont1',2,opportunities.oppCustomer,1)}" 
                                       update=":frmOpp:tabOpp:dtCC :formContLookup :formNew" oncomplete="PF('lookupCont').show()"  
                                       styleClass="btn-info btn-xs" /> 

                </h:panelGroup> 
                <h:panelGroup id="pnlclrcust"  >
                    <!--2679 CRM-3618  Fields self populating-->
                    <!--3206 CRM-3820: Remove mandatory field from "Contractor" (used to be customer)-->
                    <!-- 13-04-2021 #4010: CRM-4117 Tool tip for Clear needs to be changed accordingly-->
                     <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
                    <p:commandLink rendered="#{opportunities.oppCustomer>0 or opportunities.oppCustomerContact>0}" value="Clear" 
                                   title="Clear #{custom.labels.get('IDS_CUSTOMER')} #{opportunities.oppCustomerContact>0 ? 'contact':''} selection" class="btnlukup " style="border: none;background: none"  
                                   actionListener="#{oppService.clearSelection(2)}"  
                                   process="@this" 
                                   update=" oppCustomerContactName oppSman pnlclrcust
                                   
                                   frmOpp:tabOpp:inpTxtCustName
                                   
                                   frmOpp:tabOpp:oppCustomerContactName
                                    :frmOpp:tabOpp:lblCust :frmOpp:tabOpp:custTypeName"  disabled="#{opportunities.oppCloseStatus!=0 or opportunities.oppCloseFlag==true}"/>
                </h:panelGroup>
            </div>

            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel id="lblPrinci" for="inpTxtPrinciName" value="#{custom.labels.get('IDS_PRINCI')}" class="#{fieldsMstService.fields.get('OPP_CUST') == 1 ? 'required':''}" />


            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">  
                <h:panelGroup   class="ui-inputgroup" >
                    <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
                    <h:panelGroup id="lnkP">

<!--                    <p:commandLink id="lnkP" onclick="if (#{opportunities.oppPrincipal!=0}) {
                                window.open('../../opploop/companies/CompanyDetails.xhtml?id=#{opportunities.oppPrincipal}', '_self');
                            }"   >  -->
                        <!--05-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
                        <p:autoComplete value="#{opportunities.oppPrincipalName}"
                                     style="width: 100% ;#{empty opportunities.oppPrincipalName ? 'cursor: default!important;':'cursor: pointer!important;'}"
                                     completeMethod="#{opportunities.completeBasicComp}"
                                     placeholder="[Not selected]" scrollHeight="200"
                                     id="inpTxtPrinciName" >
                              <p:ajax event="itemSelect" onstart="PF('dlgProcessingOnTask').show();" listener="#{opportunities.selectedPricipal}" oncomplete="PF('dlgProcessingOnTask').hide();" />
                        </p:autoComplete>
                    <!--</p:commandLink>-->
                    <p:message for="inpTxtPrinciName" id="am"/>
                     </h:panelGroup>
                    <!-- #7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - update princi btn when close status close -->
                    <!--//01-07-2022 : #8374 : CRM-5687: Opportunity > Line Item Status-->
                    <p:commandButton  id="oppPrinciBtn" icon="fa fa-search" title="Choose Company" immediate="true"                                      
                                      actionListener="#{viewCompLookupService.listPrincipals(0, 'applyActivePrinci')}" 
                                      update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                      styleClass="btn-info btn-xs"  disabled="#{(opportunities.oppCloseStatus!=0 or opportunities.oppCloseFlag==true) or (oppLineItems.princiBol)}"/>
                </h:panelGroup>
            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel value="Contact"/>
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"  style="display: inherit;">
                <h:panelGroup   class="ui-inputgroup"  >
                    <!--                    #3750CRM-2234: Fwd: editing contacts within companies-->
 <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
<!--                    <h:link id="lnkPc" disabled="#{opportunities.oppPrincipalContact==0 or opportunities.oppCloseFlag==true}" outcome="/opploop/contacts/ContactDetails.xhtml?id=#{opportunities.oppPrincipalContact}">
                        <f:param name="frm" value="#{view.viewId}" />
                        <f:param name="moduleId" value="#{opportunities.oppId}" /> -->
                        <!--05-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
                        <p:autoComplete value="#{opportunities.oppPrincipalContactName}"
                                     style="width: 100%; #{((opportunities.oppPrincipalContact==0) or (opportunities.oppCloseFlag==true)) ? 'cursor: default!important;':'cursor: pointer!important;'}"
                                      minQueryLength="3" completeMethod="#{opportunities.completeCompContacts}" 
                                     placeholder="[Not selected]" scrollHeight="200"
                                     id="oppPrincipalContactName">
                                                <p:ajax event="itemSelect" listener="#{opportunities.selectedCompContacts}" />
                        </p:autoComplete>
                    <!--</h:link>-->
                    <!--                    PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                    <!-- #7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - update princi cnt btn when close status close -->
                    <p:commandButton   id="oppPrinciCntBtn" disabled="#{opportunities.oppCloseStatus!=0 or opportunities.oppCloseFlag==true}"
                                       icon="fa fa-search" title="Choose Contact" immediate="true" 
                                       action="#{viewContLookupService.disableContNewBtn(opportunities.oppPrincipal)}"
                                       actionListener="#{viewContLookupService.list('applyCont2',1,opportunities.oppPrincipal,1)}" 
                                       update=":frmOpp:tabOpp:tblPC :formContLookup :formNew" oncomplete="PF('lookupCont').show()"  
                                       styleClass="btn-info btn-xs" /> 

                </h:panelGroup>
                <h:panelGroup id="pnlclrprin"  >
                    <!--2679 CRM-3618  Fields self populating-->
                    <!-- 13-04-2021 #4010: CRM-4117 Tool tip for Clear needs to be changed accordingly-->
                    <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
                    <p:commandLink rendered="#{opportunities.oppPrincipal>0 or opportunities.oppPrincipalContact>0}" value="Clear" 
                                   title="Clear #{custom.labels.get('IDS_PRINCI')} #{opportunities.oppPrincipalContact>0? 'contact':''} selection" class="btnlukup " style="border: none;background: none"  
                                   actionListener="#{oppService.clearSelection(1)}"   disabled="#{opportunities.oppCloseStatus!=0 or opportunities.oppCloseFlag==true}"
                                   process="@this" 
                                   update="inpTxtPrinciName
                                   
                                   oppPrincipalContactName pnlclrprin"   />
                </h:panelGroup>

            </div>
            <!--#3073 JSF - Application Settings - Master Fields all the fileds are updated for required and disabled status-->
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel id="lblDistri" for="inpTxtDistriName" value="#{custom.labels.get('IDS_DISTRI')}"
                               class="#{fieldsMstService.fields.get('OPP_DISTRI') == 1 ? 'required':''}"
                               />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">   
                <h:panelGroup   class="ui-inputgroup"  >
 <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
                    <h:panelGroup id="lnkD">
<!--                    <p:commandLink id="lnkD" onclick="if (#{opportunities.oppDistri!=0}) {
                                window.open('../../opploop/companies/CompanyDetails.xhtml?id=#{opportunities.oppDistri}', '_self');
                            }"  >  -->
                        <!--05-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
                        <p:autoComplete value="#{opportunities.oppDistributorName}"
                                     style="width: 100%;#{empty opportunities.oppDistributorName ? 'cursor: default!important;':'cursor: pointer!important;'}" 
                                    scrollHeight="200"
                                     placeholder="[Not selected]" 
                                     class="#{fieldsMstService.fields.get('OPP_DISTRI') == 1 ? 'required':''}"
                                     id="inpTxtDistriName" minQueryLength="3"
                                     completeMethod="#{opportunities.completeDistibute}">
                             <p:ajax event="itemSelect" onstart="PF('dlgProcessingOnTask').show();" listener="#{opportunities.selectDistibute}" oncomplete="PF('dlgProcessingOnTask').hide();"/>
                        </p:autoComplete>
                    </h:panelGroup>
                    <!--</p:commandLink>-->
                    <!-- #7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - update distri btn when close status close -->
                    <p:commandButton  id="oppDistriBtn" icon="fa fa-search" title="Choose Company" immediate="true" 
                                      disabled="#{opportunities.oppCloseStatus!=0 or fieldsMstService.invisibleFields.get('OPP_DISTRI')==1 or opportunities.oppCloseFlag==true}"  
                                      actionListener="#{viewCompLookupService.listCRM(3, 'applyDistri')}" 
                                      update=":frmOpp:tabOpp:tblDist :formCompLookup" oncomplete="PF('lookupComp').show()"  
                                      styleClass="btn-info  btn-xs"
                                      />

                </h:panelGroup>
            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel value="Contact" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"  style="display: inherit;">
                <h:panelGroup   class="ui-inputgroup"  >
                    <!--                    #3750CRM-2234: Fwd: editing contacts within companies-->
 <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
<!--                    <h:link id="lnkDc" disabled="#{opportunities.oppCustomerContact==0 or opportunities.oppCloseFlag==true}" outcome="/opploop/contacts/ContactDetails.xhtml?id=#{opportunities.oppDistriContact}">
                        <f:param name="frm" value="#{view.viewId}" />
                        <f:param name="moduleId" value="#{opportunities.oppId}" /> -->
                        <!--05-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
                        <p:autoComplete value="#{opportunities.oppDistriContactName}"
                                     style="width: 100%;text-decoration:underline; #{empty opportunities.oppDistriContactName ? 'cursor: default!important;':'cursor: pointer!important;'}"                                 
                                     placeholder="[Not selected]" scrollHeight="200"
                                     id="oppDistriContactName" minQueryLength="3" completeMethod="#{opportunities.completeDistributContacts}"
                                     >  
                                            <p:ajax event="itemSelect" listener="#{opportunities.selectedDistributContacts}"/>
                        </p:autoComplete>
                    <!--</h:link>-->
                    <!--PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                    <!-- #7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - update distri cnt btn when close status close -->
                    <p:commandButton  id="oppDistriCntBtn" disabled="#{opportunities.oppCloseStatus!=0 or fieldsMstService.invisibleFields.get('OPP_DISTRI')==1 or opportunities.oppCloseFlag==true}"
                                      icon="fa fa-search" title="Choose Contact" immediate="true" 
                                      action="#{viewContLookupService.disableContNewBtn(opportunities.oppDistri)}"
                                      actionListener="#{viewContLookupService.list('applyCont3',3,opportunities.oppDistri,1)}" 
                                      update=":frmOpp:tabOpp:tblDC :formContLookup :formNew" oncomplete="PF('lookupCont').show()"  
                                      styleClass="btn-info btn-xs" 
                                      />  
                </h:panelGroup>
                <h:panelGroup id="pnlclrdist"  >
                    <!--2679 CRM-3618  Fields self populating-->
                    <!--3206 CRM-3820: Remove mandatory field from "Contractor" (used to be customer)-->
                    <!-- 13-04-2021 #4010: CRM-4117 Tool tip for Clear needs to be changed accordingly-->
                     <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
                     <p:commandLink rendered="#{opportunities.oppDistributorName != '' or opportunities.oppDistriContact>0}"  value="Clear" disabled="#{opportunities.oppCloseStatus!=0 or fieldsMstService.invisibleFields.get('OPP_DISTRI')==1 or opportunities.oppCloseFlag==true}"
                                   title="Clear #{custom.labels.get('IDS_DISTRI')} #{opportunities.oppDistriContact>0 ? 'contact':''} selection" class="btnlukup " style="border: none;background: none;#{fieldsMstService.invisibleFields.get('OPP_DISTRI')== 1?'display:none':''};"  
                                   actionListener="#{oppService.clearSelection(3)}"  
                                   process="@this" 
                                   update="inpTxtDistriName 
                                   oppDistriContactName pnlclrdist 
                                  
                                   frmOpp:tabOpp:oppSman :frmOpp:tabOpp:lblCust "  />
                </h:panelGroup>
            </div>
            <!--1638 Opportunities > Other Parties - Contacts-->
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                <!--#3598 Opportunities > Warning message not displayed when user moves out of screen after adding opp parties-->
                <h:inputHidden value="#{oppService.opp.otherPartyListSize}" id="opOthrPartlisth"/>
                <!--//14-06-2022 : 8233 : opportunity parties ->design issue-->
                <p:dataList value="#{oppService.opp.otherPartyList}" style="margin-left:-10px;" var="opot" id="tblOthrList" varStatus="status">    
                    <div class="ui-g ui-fluid">
                        <div class="ui-sm-12 ui-md-2 ui-lg-2" style="padding-left: 0px">

                            <p:outputLabel value="#{opot.compTypeName}" id="lbl#{status.index}"></p:outputLabel>
                        </div>
                        <!--//14-06-2022 : 8233 : opportunity parties ->design issue-->
                        <div class="ui-sm-12 ui-md-4 ui-lg-4" style="padding-left: 2px">
                            <!--<h:panelGroup   class="ui-inputgroup" style="width: 250px;" >-->
                            <!--//14-06-2022 : 8233 : opportunity parties ->design issue-->
                            <!--18-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
                            <p:inputText readonly="true" value="#{opot.compName}" id="txt#{status.index}"  style="width: 200px;cursor: default!important;"/><p:spacer width="6"/>
<!--                            <p:commandLink actionListener="#{oppService.removeOtherComp}" update="frmOpp:tabOpp:tblOthrList" type="button" style="font-size: larger; color: red;" title="Remove Other Parties">
                                <f:param  name="indexPtr" value="#{status.index}" />
                                <f:param  name="compTypeName" value="#{opot.compTypeName}" />
                                <f:param  name="compName" value="#{opot.compName}" />
                                <h:outputText value="X"/>
                            </p:commandLink>-->

<!--                                <p:commandButton actionListener="#{oppService.removeOtherComp}" style="width: 90px;" update="frmOpp:tabOpp:tblOthrList"  icon="fa fa-remove" class="btn-danger btn-xs" title="Remove Company">
                                    <f:param  name="indexPtr" value="#{status.index}" />
                                    <f:param  name="compTypeName" value="#{opot.compTypeName}" />
                                    <f:param  name="compName" value="#{opot.compName}" />                                 
                                </p:commandButton>-->
                            <!--</h:panelGroup>-->
                        </div> 
                        <div class="ui-sm-12 ui-md-2 ui-lg-2">

                            <p:outputLabel value="Contact" id="lblCont#{status.index}"></p:outputLabel>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4"  >
                             <!--actionListener="#{viewContLookupService.list('applyContacts',0,opot.compId,1)}"--> 
                            <!--<h:panelGroup   class="ui-inputgroup" style="width: 250px;" >-->
                            <!--//14-06-2022 : 8233 : opportunity parties ->design issue-->
                            <h:panelGroup id="oppOthr#{status.index}"  class="ui-inputgroup" style="width: 200px;" >  






                                <!--//14-06-2022 : 8233 : opportunity parties ->design issue-->
                                <!--18-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
                                <p:inputText readonly="true" value="#{opot.contFullName}" id="txtCont#{status.index}"  style="width: 200px;cursor: default!important;"/><p:spacer width="6"/>
                                <!--1638 Opportunities > Other Parties - Contacts-->
                                <!--                                PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                                <!--4142 CRM-4179: contacts: parties contacts not showing under the contacts tab of opp--> 
                                <!--04-03-2022 6047: Opportunity > Contact tab: Error while creating new Contact-->
                                <!--//14-06-2022 : 8233 : opportunity parties ->design issue-->
                                <p:commandButton icon="fa fa-search" title="Choose Contact" immediate="true"  
                                                 action="#{viewContLookupService.disableContNewBtn(opot.compId)}"
                                                 actionListener="#{viewContLookupService.listForOppOtherParties('applyContacts',0,opot.compId,1,opot.recId)}"
                                                 update=":frmOpp:tabOpp:dlgContAll :formContLookup" oncomplete="PF('lookupCont').show()"  
                                                 styleClass="btn-info btn-xs" 
                                                 />  
                                <!--4142 CRM-4179: contacts: parties contacts not showing under the contacts tab of opp-->
                                <!--                Feature #5408 CRM-4603: Opportunities > Custom Label for Parties-->
                                <p:commandLink  actionListener="#{oppService.removeOtherCompany}" update="frmOpp:tabOpp:tblOthrList" type="button" style="font-size: larger; color: red;" title="Remove Other #{custom.labels.get('IDS_OPP_PARTIES')}">
                                    <f:param  name="indexPtr" value="#{status.index}" />
                                    <f:param  name="compTypeName" value="#{opot.compTypeName}" />
                                    <!--1638 Opportunities > Other Parties - Contacts-->
      <!--<f:param  name="compName" value="#{opot.compName}" />-->
                                    <f:param  name="compId" value="#{opot.compId}" />
                                    <f:param  name="contId" value="#{opot.contId}" />
                                    <f:param  name="contFullName" value="#{opot.contFullName}" />
                                    <!--4142 CRM-4179: contacts: parties contacts not showing under the contacts tab of opp-->
                                    <f:param  name="recId" value="#{opot.recId}" />
                                    <h:outputText value="X"/>
                                </p:commandLink>
                            </h:panelGroup> 
                        <!--                                <p:commandButton actionListener="#{oppService.removeOtherComp}" style="width: 90px;" update="frmOpp:tabOpp:tblOthrList"  icon="fa fa-remove" class="btn-danger btn-xs" title="Remove Company">
                                                            <f:param  name="indexPtr" value="#{status.index}" />
                                                            <f:param  name="compTypeName" value="#{opot.compTypeName}" />
                                                            <f:param  name="compName" value="#{opot.compName}" />                                 
                                                        </p:commandButton>-->
                            <!--</h:panelGroup>-->
                        </div> 
                    </div>
                </p:dataList>
                <!--                <h:panelGrid id="tblOthrList" columns="2" cellpadding="5">                     
                                    <ui:repeat value="#{oppService.opp.otherPartyList}" var="author" varStatus="status">
                                        <h:inputText value="#{opot.compTypeName}" id="author#{status.index}"/>
                                        <p:commandLink actionListener="#{oppService.removeAuthor}" update="authors">
                                            <f:attribute name="index" value="#{status.index}"/>
                                            <h:outputText value="remove"/>
                                        </p:commandLink>
                                        <br/>
                                    </ui:repeat>
                                </h:panelGrid>-->
                <!--
                
                -->






            </div>
            <!--            <div class="ui-sm-12 ui-md-6 ui-lg-6">
            
                        </div>-->
            <!--            <div class="ui-sm-12 ui-md-4 ui-lg-4"  style="display: inherit;">
            
                        </div>-->
            <div class="ui-sm-12 ui-md-2 ui-lg-2" style="margin-top: -15px; ">
                <!--1638 Opportunities > Other Parties - Contacts-->
                <!--                Feature #5408 CRM-4603: Opportunities > Custom Label for Parties-->
                <p:commandLink  style="font-size: x-large; color: #4ca75a;" title="Add Other #{custom.labels.get('IDS_OPP_PARTIES')}" immediate="true"  
                                disabled="#{opportunities.oppCloseFlag==true or opportunities.oppId==0}"                              
                                actionListener="#{viewCompLookupService.listAll('applyOtherComp')}"
                                update=":formCompLookup" oncomplete="PF('lookupComp').show()">
                    <f:param  name="indexPtr" value="#{status.index}" />
                    <f:param  name="compTypeName" value="#{opot.compTypeName}" />
                    <f:param  name="compName" value="#{opot.compName}" />
                    <f:param  name="recId" value="#{opot.recId}" />
                    <h:outputText value="+"/>
                </p:commandLink>
                <!--                <p:commandButton   icon="fa fa-plus" title="Add Company" immediate="true"                                 
                                                   actionListener="#{viewCompLookupService.listAll('applyOtherComp')}"
                                                   update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                                   styleClass="btn-info btn-xs"  
                                                   style="width: 30px" />-->
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"  style="display: inherit;">




            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">

            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"  style="display: inherit;">

            </div>
        </div>

        <!--        <div class="ui-g ui-fluid header-bar">
                    <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                        <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Other Parties</p:outputLabel>
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                        <h:dataTable id="tblFields" value="#{oppService.m_lFields}" var="field">
                            <h:column>
                                <h:inputText value="#{field.value}" />
                            </h:column>
        
                            <h:column>
                                <h:commandButton value="Remove">
                                    <f:ajax listener="#{oppService.onButtonRemoveFieldClick(field)}" immediate="true" render="@form" /> 
                                </h:commandButton>
                            </h:column>
                        </h:dataTable>
                        <h:commandButton value="Add">
                            <f:ajax listener="#{oppService.onButtonAddFieldClick}" execute="@form" render="tblFields" /> 
                        </h:commandButton>
                    </div>
                </div>  -->
        <!--        <br/>
                <br/> -->
        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">#{custom.labels.get('IDS_OPP')} Details</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            </div>
        </div>  
        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel for="inpTxtProgram" value="#{custom.labels.get('IDS_PROGRAM')}" class="#{fieldsMstService.fields.get('TOPIC') == 1 ? 'required':''}"/>
            </div>
            <div class="ui-sm-12 ui-md-10 ui-lg-10">  
                <!--Task #10632: CRMSYNC-183   Sync Error - Error message when assigning Sugar opportunity to a user Gibran by harshithad on 08/02/23-->
                <p:inputText value="#{opportunities.oppCustProgram}" class="#{fieldsMstService.fields.get('TOPIC') == 1 ? 'required':''}"
                             disabled="#{fieldsMstService.invisibleFields.get('TOPIC')==1 or opportunities.oppCloseFlag==true}"
                             style="width: 100%"
                             id="inpTxtProgram"
                             maxlength="250" />
            </div>

            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--                // 18/05/2021 Feature  #4889 Repfabric / AVX CRMSync / Relabeling Fields / "Next Step"-->
                <p:outputLabel for="inpTxtNextStep" value="#{custom.labels.get('IDS_NEXT_STEP')}"
                               class="#{fieldsMstService.fields.get('NEXT_STEP') == 1 ? 'required':''}" />
            </div>
            <div class="ui-sm-12 ui-md-10 ui-lg-10">   
                <h:panelGroup   class="ui-inputgroup"  >
                    <!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
                    <p:inputText value="#{opportunities.oppNextStep}"
                                 id="inpTxtNextStep"  
                                 style="width: 100%" maxlength="250" class="form-control form-control-xs #{fieldsMstService.fields.get('NEXT_STEP') == 1 ? 'required':''}" disabled="#{fieldsMstService.invisibleFields.get('NEXT_STEP')==1 or opportunities.oppCloseFlag==true}"
                                 />
                    <p:commandButton  id="btnOppNextClsoe"  
                                      actionListener="#{opportunities.clearField(1)}" 
                                      process="@this" update=":frmOpp:tabOpp:inpTxtNextStep" title="Clear"
                                      class="btn btn-danger btn-xs" disabled="#{fieldsMstService.invisibleFields.get('NEXT_STEP')==1 or opportunities.oppCloseFlag==true}"
                                      icon="fa fa-close" 
                                      />
                </h:panelGroup>
            </div>

            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel value="#{custom.labels.get('IDS_ACTIVITY')}" 
                               class="#{fieldsMstService.fields.get('ACTIVITY') == 1 ? 'required':''}" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">     
                <h:panelGroup   class="ui-inputgroup"  >
                    <p:selectOneMenu style="width: 100%" widgetVar="wopselact" id="ddActivity" value="#{opportunities.selActivity}"
                                     class="#{fieldsMstService.fields.get('ACTIVITY') == 1 ? 'required':''}" disabled="#{fieldsMstService.invisibleFields.get('ACTIVITY')==1 or opportunities.oppCloseFlag==true}">
                        <!--or opportunities.oppCloseFlag==true or opportunities.oppCloseStatus!=0-->
                        <f:selectItems value="#{oppActivitiesMst.listOppActivities}"  
                                       var="act"   
                                       itemValue="#{act.recId}"
                                       itemLabel="#{act.actName}"/>
                        <!--    Feature 5416 Opportunities:  Fetch Potential by Opp Activity (% by stage relationship)-->
                        <p:ajax process="@this"  listener="#{oppService.activityChanged}"   update="ddOppStatus tsk oppTaskIdh  spinnerPotential" />
                        <!--disabled="#{opportunities.oppCloseStatus!=0 or opportunities.oppCloseFlag==true}"-->
                    </p:selectOneMenu>

                    <h:panelGroup id="actionBtnGrp">
                        <!--update=":dlgoppActActionForm"-->
                        <!--#7194: Activity/Close Status Linking: Update Activity on Close Opp - poornima - removed blank value-->
                        <p:commandButton id="actionBtn" class="#{opportunities.actionSetStatus.CSS_CLASS} btn-warning btn-xs" action="#{oppActivityActions.getActionList(opportunities.oppId)}" 
                                         rendered="#{opportunities.actionSetStatus.STATUS != 'NO_ACTIONS' and opportunities.oppId!=0}" disabled="#{opportunities.oppCloseFlag==true}"
                                         update=":dlgoppActActionForm" oncomplete="PF('dlgOppActAction').show()" icon="fa fa-info-circle" 
                                         title="#{opportunities.actionSetStatus.TITLE}"  style="text-align: left;width:25px;height:25px;vertical-align: top;"/>
                    </h:panelGroup>
                </h:panelGroup>
            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--#13178 ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
                <p:outputLabel value="#{custom.labels.get('IDS_ACT_STATUS')}" class="#{fieldsMstService.fields.get('STATUS') == 1 ? 'required':''}" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">   
                <h:panelGroup   class="ui-inputgroup"  >
                    <p:selectOneMenu style="width: 100%"   widgetVar="wopstat" id="ddOppStatus" value="#{opportunities.oppStatus}"
                                     class="#{fieldsMstService.fields.get('STATUS') == 1 ? 'required':''}" disabled="#{fieldsMstService.invisibleFields.get('STATUS')==1 or opportunities.oppCloseFlag==true}" >
                        <f:selectItem itemLabel="#{opportunities.oppStatus}" itemValue="#{opportunities.oppStatus}"/>
                        <f:selectItem itemLabel="#{opportunities.status1}" itemValue="#{opportunities.status1}"/>  
                    </p:selectOneMenu>
                    <h:panelGroup id="statusBtnGrp">
                        <p:commandButton id="tsk" icon="fa fa-info-circle"  class="#{opportunities.actionSetStatus.CSS_CLASS} btn-warning btn-xs"  title="#{opportunities.selTaskName == 'None' ? '' : opportunities.selTaskName}" 
                                         actionListener="#{opportunities.setOppActivityRedirect(opportunities.selTaskId)}"  style="text-align: left;width:25px;height:25px;vertical-align: top;" >
                            <!--<span>  #{opportunities.selTaskName == 'None' ? '' : opportunities.selTaskName} </span>-->
                            <!--#3162 New Opportunity : opp saved on clicking create sample-->

                            <p:ajax event="click" disabled="#{opportunities.selTaskName!= 'Create Sample' or opportunities.oppCloseFlag==true}" listener="#{oppService.validateForCreateSample}" />
                            <!--                            update=":frmSummary
                                                        :frmOpp:tabOpp:mailCust
                                                        :frmOpp:tabOpp:mailPrinci 
                                                        :frmOpp:tabOpp:dtceml
                                                        :frmOpp:tabOpp:dtc    
                                                        :frmOpp:tabOpp:opvalh
                                                        :frmOpp:tabOpp:pnlCRMSYNC
                                                        :frmOpp:btnRelated
                                                        :frmOpp:tabOpp:pnlOppCustFields
                                                        :frmOpp:tabOpp:inpTxtPrinciName
                                                        :frmOpp:tabOpp:inpTxtCustName
                                                        :frmOpp:tabOpp:inpTxtCustName2" 
                                                        oncomplete="loadValues();"/>-->
                            <!--#{opportunities.oppId!=0?oppService.saveOrUpdate:oppService.validateForCreateSample}-->
                        </p:commandButton> 
                    </h:panelGroup>
                </h:panelGroup >
            </div>

            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')}"/>
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">   
                <h:panelGroup   class="ui-inputgroup"  >
                    <p:inputText readonly="true" id="oppSman" placeholder="[No #{custom.labels.get('IDS_SALES_TEAM')}]"
                                 style="width: 100%" 
                                 value="#{opportunities.oppSman}"/> 
                    <!--#265 CRM-2290: Opportunities > Opp sales team defaults to disti based on application setting flag-->
                    <p:commandButton  rendered="#{globalParams.allowDistriCommEnabled or globalParams.defaultOppDistriSales}" disabled="#{opportunities.oppCloseFlag==true}"
                                      class="btn btn-primary btn-xs"
                                      icon="fa fa-search" 
                                      actionListener="#{opportunities.loadSalesTeam()}"
                                      update=":frmOpp:tabOpp:tblSalesTeam" 
                                      oncomplete="PF('dlgSalesTeam').show()"/>
                </h:panelGroup>
            </div>



            <!--Feature #3454:   Opportunities > Watchers-->
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:outputLabel  value="#{custom.labels.get('IDS_WATCHERS')}"   class="#{fieldsMstService.fields.get('OPP_WATCHERS') == 1 ? 'required':''}" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                <!--Feature #3573:CRM-3970: TWDev: watcher testing: can leave the opp screen without saving data-->
                <!--//            feature #3861 CRM-4073: Field Settings > Opp Watchers tab index removed-->

                <h:inputHidden value="#{oppService.listSize}" id="inpTxtOppWatcher" />

                <!--#8538  CRM-5935  ranvier: tool tip for watcher in every form (opp, job, quote, etc)-->
                <p:tooltip value="Type in the @ symbol for a selection list"  rendered="#{!(fieldsMstService.invisibleFields.get('OPP_WATCHERS')==1 or opportunities.oppCloseFlag==true)}"  showEvent="mouseover"   hideEvent="mouseout" for="frmOpp:tabOpp:panelWatchers" />
                <h:panelGroup  id="panelWatchers" style="margin-bottom: 10px">
                    <p:autoComplete  id="oppWatchers" widgetVar="oppWatchers"  maxlength="60" minQueryLength="1"  
                                     maxResults="10" styleClass="actCont" style="width: 100%;height:36px;"  
                                     value="#{oppService.setUsersList}"
                                     completeMethod="#{oppService.completeUsersName}" var="u" 
                                     itemLabel="#{u}" itemValue="#{u}" multiple="true"  forceSelection="true" 
                                     disabled="#{fieldsMstService.invisibleFields.get('OPP_WATCHERS')==1 or opportunities.oppCloseFlag==true}"
                                     >
                        <!--Feature #3573:CRM-3970: TWDev: watcher testing: can leave the opp screen without saving data-->
                        <!--//Bug #5563: opportunity single quotes and double quotes issue-->
                        <p:ajax event="itemSelect"  listener="#{oppService.getListValues}"  
                                update=":frmOpp:tabOpp:oppWatchers"/>
                        <!--// 10-03-2022 : #7433 : CRM-5281: Watcher groups: Opp Watchers-->
                        <p:ajax event="itemUnselect"  listener="#{oppService.removingWatchers}"  
                                update=":frmOpp:tabOpp:oppWatchers"/>        
                    </p:autoComplete>
                </h:panelGroup>
            </div>

            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--2624 - CRM-977: Kai: Allow for field relabel under custom relabel for Opp Owner - sharvani - 11-12-2019-->
                <p:outputLabel value="#{custom.labels.get('IDS_OPP_OWNER')}"
                               class="#{fieldsMstService.fields.get('OPP_OWNER') == 1 ? 'required':''}"/>
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 

                <!--Feature #3454:CRM-3841: Opportunities > Watchers-->
                <p:selectOneMenu value="#{opportunities.oppOwner}" id="ddOppOwner" style="width: 100%"
                                 class="#{fieldsMstService.fields.get('OPP_OWNER') == 1 ? 'required':''}" disabled="#{fieldsMstService.invisibleFields.get('OPP_OWNER')==1 or opportunities.oppCloseFlag==true}">
                    <f:selectItems value="#{oppService.getDependentUserList()}" var="user" 
                                   itemValue="#{user.userId}" itemLabel="#{user.userName}" />
                </p:selectOneMenu>
            </div>  

            <!--11-02-2022 7163 CRM-5279 watchers: needs to email notify on all changes, not initial assignment (Opportunities)-->
            <div class="ui-sm-12 ui-md-6 ui-lg-6">
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:selectBooleanCheckbox style="margin-top: 5px;" value="#{opportunities.oppEmailFlagBool}" 
                                         itemLabel="Notify #{custom.labels.get('IDS_WATCHERS')}" id="notiWatcherMail" >
                </p:selectBooleanCheckbox> 
            </div>
            <!--Feature #3454:   Opportunities > Watchers-->
            <!--            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                            <p:outputLabel  />
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4">     
            
                            #2441 Related to CRM-1510: SELTEC Fwd: Issues with the new version
                            <p:commandLink id="tsk1"  style="color: blue;text-decoration: underline;cursor: pointer"  actionListener="#{opportunities.setOppActivityRedirect(opportunities.selTaskId)}" >
                                <span>  #{opportunities.selTaskName == 'None' ? '' : opportunities.selTaskName} </span>
                                #3162 New Opportunity : opp saved on clicking create sample
                                <p:ajax event="click" disabled="#{opportunities.selTaskName!= 'Create Sample' or opportunities.oppCloseFlag==true}" listener="#{oppService.validateForCreateSample}" />
                             </p:commandLink>
            
            
                        </div>-->


            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel value="#{custom.labels.get('IDS_FOLLOW_UP')}"
                               class="#{fieldsMstService.fields.get('FOLLOW_UP') == 1 ? 'required':''}" />
            </div>
            <!--            Bug #6228 CRM-4936: calendar bounces around screen-->
            <div class="ui-sm-12 ui-md-4 ui-lg-4">      
                <p:calendar class="example" id="calOppFollowUp"  pattern="#{globalParams.dateFormat}"
                            widgetVar="wopfollow"
                            converterMessage="#{custom.labels.get('IDS_FOLLOW_UP')} date not valid" 
                            required="true" navigator="true" 
                            requiredMessage="#{custom.labels.get('IDS_FOLLOW_UP')} date not entered"
                            value="#{opportunities.oppFollowUp}" disabled="#{fieldsMstService.invisibleFields.get('FOLLOW_UP')==1 or opportunities.oppCloseFlag==true}"
                            >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                    <!--                    <p:ajax event="blur" listener="execute();"/>-->
                </p:calendar>
            </div>
            <!--            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                            2624 - CRM-977: Kai: Allow for field relabel under custom relabel for Opp Owner - sharvani - 11-12-2019
                            <p:outputLabel value="#{custom.labels.get('IDS_OPP_OWNER')}"
                                           class="#{fieldsMstService.fields.get('OPP_OWNER') == 1 ? 'required':''}"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4">  
                            <p:selectOneMenu value="#{opportunities.oppOwner}" id="ddOppOwner" style="width: 100%"
                                             class="#{fieldsMstService.fields.get('OPP_OWNER') == 1 ? 'required':''}" disabled="#{fieldsMstService.invisibleFields.get('OPP_OWNER')==1 or opportunities.oppCloseFlag==true}">
                                <f:selectItems value="#{users.dependentUserList}" var="user" 
                                               itemValue="#{user.userId}" itemLabel="#{user.userName}" />
                            </p:selectOneMenu>
                        </div>-->

            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel for="spinnerPriority" value="#{custom.labels.get('IDS_PRIORITY')}"
                               class="#{fieldsMstService.fields.get('PRIORITY') == 1 ? 'required':''}" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                <p:spinner min="1" max="10"    maxlength="2"  
                           size="8" 
                           id="spinnerPriority"
                           value="#{opportunities.oppPriority}" disabled="#{fieldsMstService.invisibleFields.get('PRIORITY')==1 or opportunities.oppCloseFlag==true}" >
                </p:spinner>
            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel for="spinnerPotential" value="#{custom.labels.get('IDS_POTENTIAL')}"
                               class="#{fieldsMstService.fields.get('POTENTIAL') == 1 ? 'required':''}" />
            </div>

            <!--1744 Opportunities > Updates to Potential when closing opp-->
            <div class="ui-sm-12 ui-md-4 ui-lg-4">  
                <p:spinner min="0" max="100"   maxlength="3" 
                           size="9" 
                           id="spinnerPotential"
                           value="#{opportunities.oppPotential}" disabled="#{fieldsMstService.invisibleFields.get('POTENTIAL')==1 or opportunities.oppCloseFlag==true}" >

                </p:spinner>
            </div>



            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel for="inpTxtEau" value="#{custom.labels.get('IDS_EAU')}"
                               class="#{fieldsMstService.fields.get('EAU') == 1 ? 'required':''}" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">  
                <!--onkeypress="if ((event.which & lt; 48 & amp; & amp; event.which != 44 & amp; & amp; event.which != 46 & amp; & amp; event.which != 127) || event.which & gt; 57)return false;">-->
                <p:inputText     
                    value="#{opportunities.oppEau}" 
                    maxlength="10" 
                    id="inpTxtEau"
                    class="#{fieldsMstService.fields.get('EAU') == 1 ? 'required':''}" disabled="#{fieldsMstService.invisibleFields.get('EAU')==1 or opportunities.oppCloseFlag==true}">
                    <f:convertNumber  type="number" integerOnly="true"/>
                    <p:ajax event="blur" process="@this" listener="#{oppLineItems.calculateValue()}" />
                </p:inputText>
            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel for="inpTxtValue" value="#{custom.labels.get('IDS_VALUE')}"
                               class="#{fieldsMstService.fields.get('VALUE') == 1 ? 'required':''}" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
<!--                <p:inputText   value="#{opportunities.oppValue}" id="inpTxtValue" onselect="loadOnOppValChange();"
                               converterMessage="#{custom.labels.get('IDS_VALUE')} is not a number" 
                               maxlength="12"
                               class="#{fieldsMstService.fields.get('VALUE') == 1 ? 'required':''}" 
                               disabled="#{fieldsMstService.invisibleFields.get('VALUE')==1 or opportunities.oppCloseFlag==true}">

                    <f:convertNumber   groupingUsed="true"                                                                                      
                                       maxIntegerDigits="12"  
                                       minFractionDigits="2" maxFractionDigits="2" />
                </p:inputText>-->
<!--                 <p:inputText   value="#{opportunities.oppValue}" id="inpTxtOldValue"/>-->
                <!--1354 CRM-3024: ALL INSTANCES: Opp Error??  "Leave page"-->
                <!--10-02-2022 7158 CRM-5294 Opportunity saved with no value - Mandatory Field-->
                <p:inputNumber decimalPlaces="2"   maxValue="9999999990.99"  value="#{opportunities.oppValue}" id="inpTxtValue" 
                               maxlength="12" converterMessage="#{custom.labels.get('IDS_VALUE')} is not a number" onselect="onOppValChange();"
                               disabled="#{fieldsMstService.invisibleFields.get('VALUE')==1 or opportunities.oppCloseFlag==true}">
                </p:inputNumber>
                <!--                #3060 CRM-1948: Fwd: Updating Details in Repfabric-->
                <!--PMS 2796:CRM-3675: TW fix: error message display time is not long enough-->

                <!--Bug #3038:CRM-3756: Make pop up warnings last longer and make "x" useable to--> 
                <p:growl id="valerrg" globalOnly="false" keepAlive="true"  life="5500" >
                    <p:autoUpdate />
                </p:growl>
            </div>

            <!--            Bug #6228 CRM-4936: calendar bounces around screen-->
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel for="calProtoDate" value="#{custom.labels.get('IDS_PROTO_DATE')}" class="#{fieldsMstService.fields.get('PROTO_DATE') == 1 ? 'required':''}"/>
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">  
                <p:calendar  id="calProtoDate" value="#{opportunities.oppProtoDate}" navigator="true"
                             widgetVar="wopproto" disabled="#{fieldsMstService.invisibleFields.get('PROTO_DATE')==1 or opportunities.oppCloseFlag==true}"
                             converterMessage="#{custom.labels.get('IDS_PROTO_DATE')} date not valid" 
                             pattern="#{globalParams.dateFormat}" class="#{fieldsMstService.fields.get('PROTO_DATE') == 1 ? 'verify':''}" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>
            </div>
            <!--            Bug #6228 CRM-4936: calendar bounces around screen-->
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel for="calProdDate" value="#{custom.labels.get('IDS_PRODUCTION_DATE')}"
                               class="#{fieldsMstService.fields.get('PROD_DATE') == 1 ? 'required':''}" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">  
                <p:calendar  id="calProdDate" value="#{opportunities.oppProdDate}"  
                             widgetVar="wopprod" navigator="true"
                             converterMessage="#{custom.labels.get('IDS_PRODUCTION_DATE')} not valid" 
                             pattern="#{globalParams.dateFormat}" 
                             class="#{fieldsMstService.fields.get('PROD_DATE') == 1 ? 'verify':''}" disabled="#{fieldsMstService.invisibleFields.get('PROD_DATE')==1 or opportunities.oppCloseFlag==true}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>
            </div>

            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel for="inpTxtComp1" value="#{custom.labels.get('IDS_COMPETITOR1')}"
                               class="#{fieldsMstService.fields.get('COMPETITOR_1') == 1 ? 'required':''}" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                <p:inputText value="#{opportunities.oppCompetitor1}" id="inpTxtComp1" 
                             style="width: 100%" 
                             maxlength="40"
                             class="#{fieldsMstService.fields.get('COMPETITOR_1') == 1 ? 'required':''}" disabled="#{fieldsMstService.invisibleFields.get('COMPETITOR_1')==1 or opportunities.oppCloseFlag==true}"/>
            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel for="inpTxtComp2" value="#{custom.labels.get('IDS_COMPETITOR2')}"
                               class="#{fieldsMstService.fields.get('COMPETITOR_2') == 1 ? 'required':''}" /> 
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">  
                <p:inputText value="#{opportunities.oppCompetitor2}" id="inpTxtComp2"
                             style="width: 100%"
                             maxlength="40"
                             class="#{fieldsMstService.fields.get('COMPETITOR_2') == 1 ? 'required':''}" disabled="#{fieldsMstService.invisibleFields.get('COMPETITOR_2')==1 or opportunities.oppCloseFlag==true}"/>
            </div>


            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel for="ddOppType" value="#{custom.labels.get('IDS_OPP_TYPE')}"
                               class="#{fieldsMstService.fields.get('TYPE') == 1 ? 'required':''}" />

            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">

                <p:selectOneMenu style="width: 100%" widgetVar="wopOppTypes" value="#{opportunities.oppTypeId}"
                                 id="ddOppType" 
                                 class="#{fieldsMstService.fields.get('TYPE') == 1 ? 'required':''}" disabled="#{fieldsMstService.invisibleFields.get('TYPE')==1 or opportunities.oppCloseFlag==true}">
                    <f:selectItem itemValue="0" itemLabel="Select Type" />
                    <f:selectItems value="#{oppTypesMst.getOppTypes()}"  
                                   var="oppType"   
                                   itemValue="#{oppType.oppTypeId}"
                                   itemLabel="#{oppType.oppTypeName}"/>
                </p:selectOneMenu>
            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel for="ddLeadSrc" value="#{custom.labels.get('IDS_LEAD_SRC')}"
                               class="#{fieldsMstService.fields.get('LEAD_SRC') == 1 ? 'required':''}" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">             
                <p:selectOneMenu id="ddLeadSrc" style="width: 100%" widgetVar="wopOppLeadSrc" value="#{opportunities.oppLeadSrcId}" 
                                 class="#{fieldsMstService.fields.get('LEAD_SRC') == 1 ? 'required':''}" disabled="#{fieldsMstService.invisibleFields.get('LEAD_SRC')==1 or opportunities.oppCloseFlag==true}">
                    <f:selectItem itemValue="0" itemLabel="Select #{custom.labels.get('IDS_LEAD_SRC')}" />
                    <f:selectItems value="#{oppLeadSourceMst.getLeadSrcList()}"  
                                   var="leadSrc"   
                                   itemValue="#{leadSrc.oppLeadSrcId}"
                                   itemLabel="#{leadSrc.oppLeadSrcName}"/>
                </p:selectOneMenu>
            </div>

        </div>




        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title #{fieldsMstService.fields.get('DESCR') == 1 ? 'required':''}">#{custom.labels.get('IDS_OPP_DESC')}</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            </div>
        </div> 
        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                <p:inputTextarea placeholder="Add Description here"  
                                 rows="4"  autoResize="false" 
                                 style="width: 100%" 
                                 value="#{opportunities.oppDescr}"
                                 id="inpTxtAreaDescr"
                                 class="#{fieldsMstService.fields.get('DESCR') == 1 ? 'required':''}" disabled="#{fieldsMstService.invisibleFields.get('DESCR')==1 or opportunities.oppCloseFlag==true}"/>
            </div>
        </div>



        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title #{fieldsMstService.fields.get('RPT_COMMENTS') == 1 ? 'required':''}">#{custom.labels.get('IDS_RPT_COMMENTS')}</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <!--onclick="addDate()"-->
                <p:commandButton  id="btnOppRptCmtDate" icon="fa fa-clock-o"   
                                  onclick="addDate()" disabled="#{fieldsMstService.invisibleFields.get('RPT_COMMENTS')==1 or opportunities.oppCloseFlag==true}"
                                  class="btn-primary btnClr"  style="float:right;margin-top: -5px;" title="Add Current DateTime"
                                  />
                <p:spacer width="4"/>
                <p:commandButton  id="btnOppReportingCoomentClose" icon="fa fa-close" 
                                  actionListener="#{opportunities.clearField(2)}" 
                                  process="@this" title="Clear" 
                                  update=":frmOpp:tabOpp:inpTxtAreaRptCmts" disabled="#{fieldsMstService.invisibleFields.get('RPT_COMMENTS')==1 or opportunities.oppCloseFlag==true}"
                                  class="btn-danger btnClr"  style="float:right;margin-top: -5px;"
                                  />
            </div>
        </div> 
        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                <p:inputTextarea placeholder="Add Comments here"  
                                 rows="3"  autoResize="false"  immediate="true"
                                 style="width: 100%" 
                                 value="#{opportunities.oppRptComments}"
                                 id="inpTxtAreaRptCmts"
                                 class="#{fieldsMstService.fields.get('RPT_COMMENTS') == 1 ? 'required':''}" disabled="#{fieldsMstService.invisibleFields.get('RPT_COMMENTS')==1 or opportunities.oppCloseFlag==true}"/>
            </div>
        </div>



    </h:panelGroup>
    <!--// 27-12-2022 : #10262 : Registrations: App Custom Feature / Opp Registrations-->
    <p:outputPanel id="oppRegistrationPanel" rendered="#{globalParams.isRegistrationsEnabled()}">
        <div class="ui-g ui-fluid header-bar" style="margin-left:-2px;">
            <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                <!--//10-02-2023 : #10672 : Relabel Registrations to "Design Registrations"-->
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Design Registrations</p:outputLabel>
            </div>
            <!--                <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                            </div>-->
        </div> 
        <div class="ui-g ui-fluid" style="margin-left:-10px;">
            <div class="ui-sm-6 ui-md-6 ui-lg-6">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputLabel value="Registration No."/>
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8">
                        <p:inputText id="inpTxtRegNum" value="#{registrationService.oppReg.regiRegNumber}" disabled="#{opportunities.oppCloseFlag==true}" maxlength="40">
                            <p:ajax event="keyup" update="@(.oppRegisterClass)"/>
                            <p:ajax event="blur" onstart="updateOppReg()" listener="#{registrationService.changeValues()}"/>
                        </p:inputText>
                    </div>
                </div>
            </div>
            <div class="ui-sm-6 ui-md-6 ui-lg-6">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputLabel value="Status"/>
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8">
                        <p:selectOneMenu id="regiStatus" widgetVar="regiStatusWidget" styleClass="oppRegisterClass" value="#{registrationService.oppReg.regiStatus}" disabled="#{registrationService.oppReg.regiRegNumber == null or registrationService.oppReg.regiRegNumber == '' or opportunities.oppCloseFlag==true}">
                            <p:ajax event="change" listener="#{registrationService.getRgisterStatus()}" update="@this "/>
                            <f:selectItem itemValue="0" itemLabel="Requested" />
                            <f:selectItem itemValue="1" itemLabel="Approved" />
                            <f:selectItem itemValue="2" itemLabel="Pending" />
                            <f:selectItem itemValue="3" itemLabel="Denied" />
                        </p:selectOneMenu>
                    </div>
                </div>
            </div>
            <div class="ui-sm-6 ui-md-6 ui-lg-6">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputLabel value="Approved Date"/>
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8">
                        <p:datePicker id="regiApproveDate" styleClass="oppRegisterClass" converterMessage="Invalid date format" 
                                      disabled="#{registrationService.oppReg.regiRegNumber == null or registrationService.oppReg.regiRegNumber == '' or opportunities.oppCloseFlag==true}" 
                                      value="#{registrationService.oppReg.regiApproveDate}"
                                      pattern="#{globalParams.dateFormat}" >
                            <p:ajax event="blur" process="@this" listener="#{registrationService.changeApprovedDate(registrationService.oppReg.regiApproveDate)}"/>
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                            <p:ajax event="dateSelect" listener="#{registrationService.onDateSelect}" update=":frmOpp:tabOpp:regiExpiryDate"/>
                        </p:datePicker>
                    </div>
                </div>
            </div>
            <div class="ui-sm-6 ui-md-6 ui-lg-6">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputLabel value="Approved By"/>
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8">
                        <p:inputText id="regiApprovedBy"  styleClass="oppRegisterClass" disabled="#{registrationService.oppReg.regiRegNumber == null or registrationService.oppReg.regiRegNumber == '' or opportunities.oppCloseFlag==true}" value="#{registrationService.oppReg.regiApprovedBy}" maxlength="40">
                            <p:ajax event="change" listener="#{registrationService.changeRegApproveBy(registrationService.oppReg.regiApprovedBy)}"/>
                        </p:inputText>
                    </div>
                </div>
            </div>
            <div class="ui-sm-6 ui-md-6 ui-lg-6">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputLabel value="Expiry Date"/>
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8">
                        <p:datePicker id="regiExpiryDate" validatorMessage="Approved Date cannot be greater than Expiry Date" converterMessage="Invalid date format"  styleClass="oppRegisterClass" 
                                      disabled="#{registrationService.oppReg.regiRegNumber == null or registrationService.oppReg.regiRegNumber == '' or opportunities.oppCloseFlag==true}" 
                                      value="#{registrationService.oppReg.regiExpiryDate}" disabledDates="#{registrationService.dateList}"
                                      pattern="#{globalParams.dateFormat}" mindate="#{registrationService.forMinExpiryDate}">
                            <p:ajax event="blur" process="@this"/>
                            <p:ajax event="dateSelect" listener="#{registrationService.changeExpiryDate}"/>
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </p:datePicker>
                    </div>
                </div>
            </div>
            <div class="ui-sm-6 ui-md-6 ui-lg-6">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputLabel value="Registration Level"/>
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8">
                        <p:inputText id="registrationLevel" styleClass="oppRegisterClass" disabled="#{registrationService.oppReg.regiRegNumber == null or registrationService.oppReg.regiRegNumber == '' or opportunities.oppCloseFlag==true}" value="#{registrationService.oppReg.regiLevel}" maxlength="40">
                            <p:ajax event="change" listener="#{registrationService.changeRegLevel(registrationService.oppReg.regiLevel)}"/>
                        </p:inputText>
                    </div>
                </div>
            </div>
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-2 ui-lg-2">
                        <p:outputLabel value="Notes"/>
                    </div>
                    <div class="ui-sm-12 ui-md-10 ui-lg-10">
                        <p:inputTextarea id="regiNotes" styleClass="oppRegisterClass" disabled="#{registrationService.oppReg.regiRegNumber == null or registrationService.oppReg.regiRegNumber == '' or opportunities.oppCloseFlag==true}" value="#{registrationService.oppReg.regiNotes}" 
                                         rows="2" autoResize="false" >
                            <p:ajax event="change" listener="#{registrationService.changeRegNotes(registrationService.oppReg.regiNotes)}"/>
                        </p:inputTextarea>
                    </div>
                </div>
            </div>
        </div>
    </p:outputPanel>
     <!--14091: 01/08/2024: CRM-6542   typeahead on opportunities-->
            <p:dialog id="dlgIdProcessingOnTask" widgetVar="dlgProcessingOnTask" closable="false" modal="true" header="Message" onShow="PF('dlgProcessingOnTask').initPosition();" resizable="false" >
                <h:form>
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel value="Processing please wait..." />
                        <br /><br />
                    </p:outputPanel>
                </h:form>
            </p:dialog>
    <!--    
        //#3241- Opportunities - move custom fields to basic tab
        //Seema - 08/02/2020-->
    <h:panelGroup  layout="grid"  id="pnlOppCustFields">
        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">#{custom.labels.get('IDS_CUSTOM_FIELDS')}</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            </div>
        </div>  
        <style>
            .firstColumn{
                min-width: 200px !important;
            }

            .secondColumn{
                min-width: 360px !important;
            }


            .grpBox{
                padding:0 20px!important;
                margin:0px!important;
                border: solid 1px #d1d4de;


            }

        </style>

        <p:outputLabel value="No #{custom.labels.get('IDS_CUSTOM_FIELDS')}" rendered="#{oppCustomFields.oppCustomFieldsList eq null or oppCustomFields.oppCustomFieldsList.size() eq 0}"/>

        <ui:repeat value="#{oppCustomFields.oppCustomFieldsList}" var="cf">

            <p:outputPanel rendered="#{cf.custFieldType eq 'in'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputPanel>
                            <p:spinner value="#{cf.customFieldValue}" min="#{cf.custFieldMin}" max="#{cf.custFieldMax}" size="8"/>
                            <p:spacer height="0" width="10"/>
                            <p:outputLabel value="(min. #{cf.custFieldMin}, max. #{cf.custFieldMax})" />
                        </p:outputPanel>
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>

            <p:outputPanel rendered="#{cf.custFieldType eq 'tx'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:inputText value="#{cf.customFieldValue}" disabled="#{opportunities.oppCloseFlag==true}" maxlength="#{customFieldsMstService.fieldMaxLength}"/>

                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>

            <p:outputPanel rendered="#{cf.custFieldType eq 'da'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 

                    </div>
                    <!--            Bug #6228 CRM-4936: calendar bounces around screen-->
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:calendar value="#{cf.customFieldValue}" navigator="true" disabled="#{opportunities.oppCloseFlag==true}" pattern="#{globalParams.dateFormat}" style="width:80%" converterMessage="#{cf.custFieldLabel} is invalid">
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </p:calendar>
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>

            <p:outputPanel rendered="#{cf.custFieldType eq 'dt'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>
                    <!--            Bug #6228 CRM-4936: calendar bounces around screen-->
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">

                        <p:calendar value="#{cf.customFieldValue}" navigator="true" disabled="#{opportunities.oppCloseFlag==true}" pattern="#{globalParams.dateTimeFormat}" style="width: 100%" converterMessage="#{cf.custFieldLabel} is invalid">
                            <f:convertDateTime pattern="#{globalParams.dateTimeFormat}" />
                        </p:calendar>
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6">   </div> 

                </div>
            </p:outputPanel>

            <p:outputPanel rendered="#{cf.custFieldType eq 'dd'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:selectOneMenu value="#{cf.customFieldValue}" disabled="#{opportunities.oppCloseFlag==true}" >
                            <f:selectItem itemLabel="[--Select--]" itemValue="" />
                            <f:selectItems var="cfOption" value="#{cf.custSelectOptions}" itemLabel="#{cfOption}" itemValue="#{cfOption}" />
                        </p:selectOneMenu> 
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>
            <!--CRM-265 :MultiSelect Checkbox--> 
            <p:outputPanel rendered="#{cf.custFieldType eq 'md'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 

<!--                        <p:selectCheckboxMenu  value="#{cf.multiSelectedOptions}" label="#{cf.custFieldLabel}" filter="true" filterMatchMode="startsWith"   >
                            <f:selectItems value="#{cf.allMultiSelect}" />
                            <p:ajax  event="change" update="displayOption" />
                            <p:ajax event="toggleSelect" update="displayOption"  />

                        </p:selectCheckboxMenu>-->
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <!--                        #2940 CRM-1847: Golden: custom field with multi select is awkward-->
                        <p:selectCheckboxMenu  value="#{cf.multiSelectedOptions}" disabled="#{opportunities.oppCloseFlag==true}" label="#{cf.custFieldLabel}" multiple="true" filter="true" filterMatchMode="startsWith"   >
                            <f:selectItems value="#{cf.allMultiSelect}" var="mulVal" itemLabel="#{mulVal}" itemValue="#{mulVal}" />
                            <!--                            <p:ajax  event="change" update="displayOption" />
                                                        <p:ajax event="toggleSelect" update="displayOption"  />-->

                        </p:selectCheckboxMenu>
                        <!--                        <p:outputPanel id="displayOption" class="ui-selectonemenu-label ui-inputfield ui-corner-all potential-css " style=" border: solid 1px #d1d4de; min-height: 28px;" >
                                                    <p:dataList value="#{cf.multiSelectedOptions}" var="cust" emptyMessage="No options selected">
                        #{cust.toString()}
                    </p:dataList> 
                </p:outputPanel>-->
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>



            <p:outputPanel rendered="#{cf.custFieldType eq 'cb'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:selectBooleanCheckbox value="#{cf.cbValue}" style="width:80%" disabled="#{opportunities.oppCloseFlag==true}"/>             
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>

            <p:outputPanel rendered="#{cf.custFieldType eq 'de'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:inputText value="#{cf.customFieldValue}" disabled="#{opportunities.oppCloseFlag==true}" maxlength="12" converterMessage="#{cf.custFieldLabel} is not a number">
                            <f:convertNumber groupingUsed="false" maxIntegerDigits="15"  maxFractionDigits="4" minFractionDigits="2" />
                        </p:inputText>
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>
            <!--CRM 158-->
            <p:outputPanel rendered="#{cf.custFieldType eq 'ta'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:inputTextarea  disabled="#{opportunities.oppCloseFlag==true}" rows="5" value="#{cf.customFieldValue}" style="overflow-y: scroll; height: 50px;" maxlength="#{customFieldsMstService.getLargeTextMaxLength()}">
                        </p:inputTextarea>
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>
            <!--CRM 101-->
            <p:outputPanel rendered="#{cf.custFieldType eq 'lk'}">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{cf.custFieldLabel}"  styleClass="#{cf.custFieldReqd!=0?'required':''}"/> 
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <h:panelGroup>
                            <!--Sharvani-03/06/2019:User story:CRM-589:Custom Fields-->
                            <p:inputText  disabled="#{opportunities.oppCloseFlag==true}" value="#{cf.customFieldValue}"   validatorMessage="Invalid URL" maxlength="#{customFieldsMstService.hyprTxtFieldMaxLength}">
                                <!--<f:validateRegex pattern="^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$" />-->
                                <!--//04-01-2022 : #10330 : CRM-6490  Onedrive link not working when Cloning Opp-->
                                <p:ajax event="change"/>
                            </p:inputText> 
                            <p:spacer width="10" />
                            <!--#3362 Custom field : Hyper link issue-->
                            <!--1505 CRM-3131: Manu-tek custom field update-->
                            <!--2873 Code Optimization - Company Details screen    -->  
                            <!--15-06-2023 11410 CRM-7057   Microsoft OneNote Section link URL being redirected from rf url custom field popout: Bulk-->
                            <p:link  id="opnLinkOpp" value="Open link" style="color: blue;" disabled="#{cf.customFieldValue == ''}" href="#{companyService.getValidURL(cf.customFieldValue)}" target="_blank" />
                            <p:tooltip id="toolTopOpnLinkOpp"  for="opnLinkOpp" value="#{cf.customFieldValue}" showEvent="mouseover" position="top" />
                            <style type="text/css">
                                .ui-tooltip {
                                    word-wrap: break-word;
                                    max-width: 800px;
                                    white-space:pre-wrap;
                                    text-overflow: ellipsis
                                }
                            </style>
                        </h:panelGroup>
                    </div>
                    <div class="ui-sm-12 ui-md-6 ui-lg-6"></div> 
                </div>
            </p:outputPanel>

        </ui:repeat>


    </h:panelGroup>

    <!--26-08-2022 8606 CRMSYNC-48   {CRMSync} When you change the principle in a linked opp, the link with the old opp is n-->
    <p:dialog id="dlgIdPrinciChangeAlert"  widgetVar="dlgPrinciChangeAlert"   modal="true" width="500" 
              header="Message" responsive="false" onShow="PF('dlgPrinciChangeAlert').initPosition();" >

        <p:outputLabel>
            Principal cannot be changed as the opportunity is already linked to target system opportunity. If you still want to change, first unlink the target system opportunity.
        </p:outputLabel>

        <div class="button_bar" style=" text-align: center">    
            <p:commandButton value="Ok" 
                             class="btn btn-success btn-xs" onclick="PF('dlgPrinciChangeAlert').hide();" />

        </div>

    </p:dialog>

    <!--29-08-2022 8606 CRMSYNC-48   {CRMSync} When you change the principle in a linked opp, the link with the old opp is n-->
    <p:dialog id="dlgIdLineItemDelete"  widgetVar="dlgLineItemDelete"   modal="true" width="500" 
              header="Message" responsive="false" onShow="PF('dlgLineItemDelete').initPosition();" >

        <p:outputLabel>
            This will clear all the line items. Are you sure to change?
        </p:outputLabel>
        <br/>
        <br/>
        <div style=" text-align: center">    
            <!--Bug #11349:ESCALATIONS CRM-7030   Design Registrations: Get duplicated if same number is used by harshithad on 06/06/23-->
            <p:commandButton value="Yes" class="btn btn-success btn-xs"
                             actionListener="#{opportunities.setDeleteLineItems(true)}" action="#{oppService.saveOrUpdate(0)}" 
                             onclick="PF('dlgLineItemDelete').hide();" update=":frmOpp" />
            <p:spacer width="4"/>
            <p:commandButton value="No"  class="btn btn-warning btn-xs"  onclick="PF('dlgLineItemDelete').hide()" />

        </div>

    </p:dialog>

    <!--21-09-2023 12132 CRM-7266   Opportunities: Value reduced to zero even with a line item-->
    <p:dialog id="dlgIdConfirmOppValueZero"  widgetVar="dlgConfirmOppValueZero" 
              modal="true" width="500" header="Confirmation" responsive="false" 
              onShow="PF('dlgConfirmOppValueZero').initPosition();" >

        <p:outputLabel>
            Are you sure to set #{custom.labels.get('IDS_OPP')} #{custom.labels.get('IDS_VALUE')} to 0?
        </p:outputLabel>
        <br/>
        <br/>
        <div style=" text-align: center">    
            <p:commandButton value="Yes" class="btn btn-success btn-xs" 
                             actionListener="#{oppService.confirmedOppValueZero()}" 
                             onclick="PF('dlgConfirmOppValueZero').hide();"/>
            <p:spacer width="4"/>
           <!--#12308 CRM-7266   Opportunities: Value reduced to zero even with a line item-->
            <p:commandButton value="No" actionListener="#{oppService.resetPrevOppValue()}"   class="btn btn-warning btn-xs" 
                             onclick="PF('dlgConfirmOppValueZero').hide();" update="frmOpp:tabOpp:inpTxtValue"/>

        </div>

    </p:dialog>

    <style>
        .ui-selectcheckboxmenu-multiple-container{
            width:100%; 
            height: auto !important;

        }
 /*14091: 01/08/2024: CRM-6542   typeahead on opportunities*/
        

        body .ui-autocomplete li.ui-autocomplete-token:hover {
            background: #5f99bb!important; 

        }

    </style>


    <script type="text/javascript">



        function execute() {
            document.getElementsByClassName("example")[0].style.display = "none";

        }
        function addDate() {
            var curDate = '#{opportunities.getCurrentDate()}';
            var cmt = document.getElementById('frmOpp:tabOpp:inpTxtAreaRptCmts');
            curDate = curDate + " ";
            //        12/11/2021 : Pms Task #6341: CRM-4968: Time stamp in reporting comments doesn't go where your cursor is
            cmt.value = curDate + "\n \n" + cmt.value;
            //        #3262    CRM-2044: Opportunities: Fwd: Reporting comments - date button issue
            //            cmt.focus();
            cmt.setSelectionRange(curDate.length + 1, curDate.length + 1);
            cmt.focus();
        }
        function calc() {

            var v = document.getElementById('frmOpp:tabOpp:opvalh').value;
            var eau = document.getElementById('frmOpp:tabOpp:inpTxtEau').value;
            if (eau > 0) {
                var v1 = eau * v;
                document.getElementById('frmOpp:tabOpp:inpTxtValue').value = v1;
            }
        }

        function showDlg1() {
            var e = document.getElementById('frmOpp:tabOpp:oppTaskIdh');
            var itm = e.value;
            switch (itm) {
                case "1" :
                    PF('dlgRegist1').show();
                    break;
                case "2" :
                    PF('dlgRegist2').show();
                    break;
                case "4" :
                    PF('dlgSample1').show();
                    break;
                case "5" :
                    initialize1();
                    PF('dlgQuote').show();
                    break;
                case "3" :
                    PF('dlgDesign1').show();
                    break;
            }
            e.selectedIndex = 0;
        }

        function disable(c) {
            var a, b;
            switch (c) {
                case 2:
                    e = document.getElementById('frmOpp:tabOpp:c2');
                    b = document.getElementById('frmOpp:tabOpp:q2');
                    break;
                case 1:
                    e = document.getElementById('frmOpp:tabOpp:c1');
                    b = document.getElementById('frmOpp:tabOpp:q1');
                    break;
                case 3:
                    e = document.getElementById('frmOpp:tabOpp:c3');
                    b = document.getElementById('frmOpp:tabOpp:q3');
                    break;
            }
            var a = e.value;
            if ((a.trim()) === "") {
                b.disabled = true;
            } else {
                b.disabled = false;
            }
        }
        // 27-12-2022 : #10262 : Registrations: App Custom Feature-->
        function updateOppReg() {
            var y = $("#frmOpp\\:tabOpp\\:inpTxtRegNum");
            if (y.val().length > 0) {
            } else {
                document.getElementById('frmOpp:tabOpp:regiApprovedBy').value = "";
                document.getElementById('frmOpp:tabOpp:registrationLevel').value = "";
                document.getElementById('frmOpp:tabOpp:regiNotes').value = "";
                document.getElementById('frmOpp:tabOpp:regiExpiryDate_input').value = "";
                let objectDate = new Date();
                let year = objectDate.getFullYear();
                let month = objectDate.getMonth() + 1;
                let day = objectDate.getDate();
                let format3;
                if (month.length > 9) {
                    format3 = day + "-" + month + "-" + year;
                } else {
                    format3 = day + "-0" + month + "-" + year;
                }

                document.getElementById('frmOpp:tabOpp:regiApproveDate_input').value = format3;
                PF('regiStatusWidget').selectValue('0');
                PF('regiStatusWidget').selectItem(PF('regiStatusWidget').jq.find('.ui-selectonemenu-item[data-label*="Requested"]'))
            }
        }

//21-09-2023 12132 CRM-7266   Opportunities: Value reduced to zero even with a line item
        function triggerButtonClick(btnId) {

            var button = document.getElementById(btnId);
            button.click();
        }
// 14091: 01/08/2024: CRM-6542   typeahead on opportunities
         function applyCompanyType(compType) {
//                        var compName = '';
                        switch (compType) {
                            case 1:
                            {
                                var y = $("#frmOpp\\tabOpp\\:oppPrincipal_input");
                                console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                    console.log('length is greater')
                                } else {
                                    var s = document.getElementById("frmOpp:tabOpp:oppPrincipal_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                            case 2:
                            {
//                    compName = document.getElementById("frmOpp:tabOpp:oppCustomer_input").value;
                                var y = $("#frmOpp\\tabOpp\\:oppCustomer_input");
                                console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                    console.log('length is greater')
                                } else {
                                    var s = document.getElementById("frmOpp:tabOpp:oppCustomer_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                            case 3:
                            {
//                    compName = document.getElementById("frmOpp:tabOpp:oppDistributor_input").value;
                                var y = $("#frmOpp\\tabOpp\\:oppDistributor_input");
                                console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                    console.log('length is greater')
                                } else {
                                    var s = document.getElementById("frmOpp:tabOpp:oppDistributor_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                            case 4:
                            {
//                    compName = document.getElementById("frmOpp:tabOpp:oppSecCustomer_input").value;
                                var y = $("#frmOpp\\tabOpp\\:oppSecCustomer_input");
                                console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                    console.log('length is greater')
                                } else {
                                    var s = document.getElementById("frmOpp:tabOpp:oppSecCustomer_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                        }
//                        applyForAllComp([{name: 'compName', value: compName}, {name: 'compType', value: compType}]);
//                        removeFocusWithTime();
                    }
                    function applyCompanyContontact(compContType) {
//                        var compContName = '';
                        switch (compContType) {
                            case 1:
                            {
//                                compContName = document.getElementById("frmOpp:tabOpp:oppPrincipalContact_input").value;
                                var y = $("#frmOpp\\tabOpp\\:oppPrincipalContact_input");
                                console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                    console.log('length is greater')
                                } else {
                                    var s = document.getElementById("frmOpp:tabOpp:oppPrincipalContact_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                            case 2:
                            {
//                                compContName = document.getElementById("frmOpp:tabOpp:oppCustomerContact_input").value;
                                var y = $("#frmOpp\\tabOpp\\:oppCustomerContact_input");
                                console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                    console.log('length is greater')
                                } else {
                                    var s = document.getElementById("frmOpp:tabOpp:oppCustomerContact_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                            case 3:
                            {
//                                compContName = document.getElementById("frmOpp:tabOpp:oppDistributorContact_input").value;
                                var y = $("#frmOpp\\tabOpp\\:oppDistributorContact_input");
                                console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                    console.log('length is greater')
                                } else {
                                    var s = document.getElementById("frmOpp:tabOpp:oppDistributorContact_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                            case 4:
                            {
//                                compContName = document.getElementById("frmOpp:tabOpp:oppSecCustomerContact_input").value;
                                var y = $("#frmOpp\\tabOpp\\:oppSecCustomerContact_input");
                                console.log('y=' + y.val())
                                if (y.val().length > 2) {
                                    console.log('length is greater')
                                } else {
                                    var s = document.getElementById("frmOpp:tabOpp:oppSecCustomerContact_panel");
                                    s.style.display = 'none';
                                }
                                break;
                            }
                        }
//                        applyContForAllComp([{name: 'compContName', value: compContName}, {name: 'compContType', value: compContType}]);
//                        removeFocusWithTime();
                    }
                    function delayForkeyup() {
                        setTimeout(function () {

                        }, 500);
                    }

                    function removeingFocus() {
                        document.activeElement.blur();
                    }
    </script>
    <style>
        .table_general .td_clr{
            width:80px;   
        }

        .btnClr{
            width: 25px !important;
            height: 27px !important;
        }
        .tr-h{
            height:1px!important;
        }

        .td_txt{
            color: #8f8f8f;
            font-size: 12px !important;
        }

        /*        .ui-sm-*,.ui-md-*,.ui-lg-*{ 
                              padding-top: 0px !important;
                              padding-bottom: 0px !important;
                          }*/
        .ui-datalist-empty-message{    display: none;}
        ul, ol {list-style-type: none;}
        .fa .fa-info-circle{ padding-right: 6px !important;}

        .cursor-link{
            cursor: pointer !important;
        }
        /*// 27-12-2022 : #10262 : Registrations: App Custom Feature*/
        .oppRegisterClass{

        }
         /*14091: 01/08/2024: CRM-6542   typeahead on opportunities*/
        .acCompCont{
                    width: 410px!important;
                    height: 250px!important;
                }
    </style>



</ui:composition>