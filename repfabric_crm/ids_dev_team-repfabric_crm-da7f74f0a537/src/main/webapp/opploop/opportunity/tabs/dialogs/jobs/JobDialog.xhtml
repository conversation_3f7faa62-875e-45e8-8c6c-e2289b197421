<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui" 
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">
    <div>
        <p:dialog widgetVar="dlgJobList" header="Link #{custom.labels.get('IDS_JOB')} to #{custom.labels.get('IDS_OPP')}" modal="true">
            <h:form id="frmJobList" >
                <p:commandButton class="btn btn-primary btn-xs" id="add1" value="New" onstart="PF('dlgJobList').hide()" actionListener="#{jobs.resetfields()}" action="#{jobs.populateOppFields()}" oncomplete="PF('addJobDlg').show()"
                                 update=":jobAddNewForm">
                </p:commandButton>
                <p:dataTable  id="dtJobListId" widgetVar="dtJobList" paginator="true"                    
                              value="#{jobs.jobList}"                         
                              filterEvent="keyup"                          
                              filteredValue="#{jobs.filteredJobList}" 
                              selectionMode="single" 
                              var="viewjobs"                                       
                              rowKey="#{jobs.jobId}"                                    
                              paginatorAlwaysVisible="false"
                              paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                              rows="10"   class="tblmain"
                              emptyMessage="No jobs available"
                              draggableColumns="true">


                    <f:facet name="header">
                        <p:inputText style="display: none"   id="globalFilter" onkeyup="PF('tblJob').filter()" />
                    </f:facet>

                    <p:column  headerText="#{custom.labels.get('IDS_JOB')} Name"  filterMatchMode="contains" filterBy="#{viewjobs.jobDescr}" sortBy="#{viewjobs.jobDescr}">
                        <p:commandLink value="#{viewjobs.jobDescr}" action="#{jobs.onRowSelect(viewjobs.jobId)}" oncomplete="PF('dlgJobList').hide()" update=":frmOpp">

                        </p:commandLink>
                    </p:column>
<!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                    <p:column headerText="#{custom.labels.get('IDS_JOB_TYPE')}" filterMatchMode="contains" filterBy="#{viewjobs.jobTypeName}" sortBy="#{viewjobs.jobTypeName}">
                        <p:outputLabel value="#{viewjobs.jobTypeName}"/>
                    </p:column>

                    <p:column headerText="Job Specifier" filterMatchMode="contains"  filterBy="#{viewjobs.jobSpecifierName}" sortBy="#{viewjobs.jobSpecifierName}"
                              style="max-width: 100px;
                              padding: 5px 2px 1px 2px !important">
                        <p:outputLabel value="#{viewjobs.jobSpecifierName}"/>
                    </p:column>

                    <p:column headerText="Activity Stage" filterMatchMode="contains" filterBy="#{viewjobs.jobActivityName}" sortBy="#{viewjobs.jobActivityName}">
                        <p:outputLabel value="#{viewjobs.jobActivityName}"/>
                    </p:column>
<!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                    <p:column headerText="#{custom.labels.get('IDS_JOB_VALUE')}" filterMatchMode="contains" filterBy="#{viewjobs.jobvalue}" sortBy="#{viewjobs.jobvalue}">
                        <p:outputLabel value="#{viewjobs.jobvalue}"/>
                    </p:column>

                    <p:column headerText="Status" filterMatchMode="contains" filterBy="#{viewjobs.jobStatus}" sortBy="#{viewjobs.jobStatus}">
                        <p:outputLabel value="#{viewjobs.jobStatus}"/>
                    </p:column>
                    <!-- Task#3413-CRM-3818: Rouzer Make jobs sortable by creation date? :12-02-2021 by harshithad -->
                    <p:column id="colCreationDate" headerText="Creation Date" filterMatchMode="contains" filterBy="#{globalParams.formatDateTime(viewjobs.insDate, 'dt')}" sortBy="#{viewjobs.insDate}">
                        <p:outputLabel id="oLblCreationDate" value="#{globalParams.formatDateTime(viewjobs.insDate, 'dt')}" />


                    </p:column>
                </p:dataTable>
            </h:form>
        </p:dialog>
    </div>
</ui:composition>