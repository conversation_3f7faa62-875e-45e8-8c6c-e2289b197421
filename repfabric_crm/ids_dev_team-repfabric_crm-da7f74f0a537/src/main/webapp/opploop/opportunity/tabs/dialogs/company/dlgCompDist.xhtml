<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets">   

    <p:dialog    header="#{custom.labels.get('IDS_DISTRI')} Lookup List" widgetVar="dlgDist"  
                 closeOnEscape="true"
                 modal="true">

        <p:dataTable  value="#{viewCompanyLookUp.companyList}" 
                      filteredValue="#{viewCompanyLookUp.filteredCompanyList}" var="com"
                      widgetVar="wdDlgDist"
                      paginatorPosition="top"
                      paginator="true"  rows="15"  id="tblDist">
            <p:column filterMatchMode="contains" filterBy="#{com.compName}" headerText="#{custom.labels.get('IDS_DISTRI')} Company" >
                <p:commandLink process="@this" value="#{com.compName}" 
                               onclick="PF('dlgDist').hide()"  
                               update=":frmOpp:tabOpp:inpTxtDistriName     :frmOpp:tabOpp:pnlclrdist                            
                               :frmOpp:tabOpp:oppDistriContactName                                                                
                               :frmOpp:tabOpp:oppSman
                               :frmOpp:tabOpp:oppCustomerName  :frmOpp:tabOpp:pnlclrcust
                               :frmOpp:tabOpp:oppCustomerContactName  :frmOpp:btnRelated" 
                               actionListener="#{oppService.selectCompany(3,com.compId,com.compName,com.compSmanId, com.compType)}"/>

            </p:column>
            <p:column headerText="Address">
                <h:outputLabel value="#{com.compAddress1}" />
            </p:column>
            <p:column headerText="City">
                <h:outputLabel value="#{com.compCity}" />
            </p:column>
            <p:column headerText="Phone">
                <h:outputLabel value="#{com.compPhone1}" />
            </p:column>
            <p:column headerText="Region">
                <h:outputLabel value="#{com.compRegion}" />
            </p:column>
        </p:dataTable>
         <!--update=":frmOpp:tabOpp:pnlQkComp"-->
        <p:commandButton icon="ui-icon-plus" value="New Company" 
                         update=" "
                         onclick="PF('dlgDist').hide();" 
                         action="#{oppService.showCompCreateDlg(3)}" 
                         oncomplete="PF('dlgCompCreate').show();"  
                         class="button_top btn_tabs"  />

    </p:dialog>

</ui:composition>

