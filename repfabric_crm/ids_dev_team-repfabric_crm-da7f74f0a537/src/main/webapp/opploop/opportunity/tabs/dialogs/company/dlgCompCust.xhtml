<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets">   

    <p:dialog     header="#{custom.labels.get('IDS_CUSTOMER')} Lookup List" widgetVar="dlgCust" 
                  id="dlgCust"
                  closeOnEscape="true"
                  modal="true">

        <p:dataTable  widgetVar="wdDlgCust" 
                      value="#{viewCompanyLookUp.companyList}" 
                      filteredValue="#{viewCompanyLookUp.filteredCompanyList}"
                      var="com"                            
                      paginator="true" 
                      paginatorPosition="top"  
                      rows="15" id="tblCust"  
                      >

            <p:column filterMatchMode="contains" filterBy="#{com.compName}"  headerText="#{custom.labels.get('IDS_CUSTOMER')} Company" >
                <p:commandLink process="@this"  value="#{com.compName}"
                               onclick="PF('dlgCust').hide()" 

                               update=":frmOpp:tabOpp:oppCustomerName  :frmOpp:tabOpp:pnlclrcust
                               :frmOpp:tabOpp:oppCustomerContactName 
                               :frmOpp:tabOpp:oppSman
                               :frmOpp:tabOpp:inpTxtDistriName     :frmOpp:tabOpp:pnlclrdist                            
                               :frmOpp:tabOpp:oppDistriContactName :frmOpp:btnRelated :frmOpp:tabOpp:custTypeName"
                               actionListener="#{oppService.selectCompany(2,com.compId,com.compName,com.compSmanId, com.compType)}"/>

            </p:column>
            <p:column headerText="Address">
                <h:outputLabel value="#{com.compAddress1}" />
            </p:column>
            <p:column headerText="City">
                <h:outputLabel value="#{com.compCity}" />
            </p:column>
            <p:column headerText="Phone">
                <h:outputLabel value="#{com.compPhone1}" />
            </p:column>
            <p:column headerText="Region">
                <h:outputLabel value="#{com.compRegion}" />
            </p:column>
        </p:dataTable>
 <!--update=":frmOpp:tabOpp:qcsg :frmOpp:tabOpp:pnlQkComp"-->   
        <p:commandButton icon="ui-icon-plus"  value="New Company" process="@this"  
                         update=" "   
                         onclick="PF('dlgCust').hide();" 
                         action="#{oppService.showCompCreateDlg(2)}" 
                         oncomplete="PF('dlgCompCreate').show();"               
                         class="button_top btn_tabs"  />

    </p:dialog>
</ui:composition>

