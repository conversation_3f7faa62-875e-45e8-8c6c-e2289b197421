<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    
    <p:dialog  position="center"   header="#{custom.labels.get('IDS_PRINCI')} Lookup List" widgetVar="dlgPrinc"  
               closeOnEscape="true" 
               modal="true">


        <p:dataTable                       
            value="#{viewCompanyLookUp.companyList}" 
            filteredValue="#{viewCompanyLookUp.filteredCompanyList}"
            var="com"  id="tblPrin"
            widgetVar="wdDlgPrin" 
            paginator="true" paginatorPosition="top"  rows="15"  >
            <p:column  filterMatchMode="contains" filterBy="#{com.compName}" headerText="#{custom.labels.get('IDS_PRINCI')} Company" >
                <p:commandLink process="@this" value="#{com.compName}"
                               onclick="PF('dlgPrinc').hide()"  
                               update=":frmOpp:tabOpp:inpTxtPrinciName    :frmOpp:tabOpp:pnlclrprin                            
                               :frmOpp:tabOpp:oppPrincipalContactName                                 
                               :frmOpp:tabOpp:oppSman :frmOpp:btnRelated :frmOpp:tabOpp:ddActivity :frmOpp:tabOpp:opstat1 :frmOpp:tabOpp:tsk "     
                               actionListener="#{oppService.selectCompany(1,com.compId,com.compName,com.compSmanId, com.compType)}"/>
            </p:column>
            <p:column headerText="Address">
                <h:outputLabel value="#{com.compAddress1}" />
            </p:column>
            <p:column headerText="City">
                <h:outputLabel value="#{com.compCity}" />
            </p:column>
            <p:column headerText="Phone">
                <h:outputLabel value="#{com.compPhone1}" />
            </p:column>
            <p:column headerText="Region">
                <h:outputLabel value="#{com.compRegion}" />
            </p:column>
        </p:dataTable>
          <!--update=":frmOpp:tabOpp:pnlQkComp"-->
        <p:commandButton icon="ui-icon-plus" value="New Company" 
                         onclick="PF('dlgPrinc').hide();"   
                         update=" "
                         action="#{oppService.showCompCreateDlg(1)}" 
                         oncomplete="PF('dlgCompCreate').show();"
                         class="button_top btn_tabs"  />


    </p:dialog>

</ui:composition>
