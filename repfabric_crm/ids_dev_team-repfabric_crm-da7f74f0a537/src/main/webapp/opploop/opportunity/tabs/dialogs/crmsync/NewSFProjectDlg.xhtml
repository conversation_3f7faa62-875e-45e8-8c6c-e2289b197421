<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <!--13-10-2023 12315 CRMSYNC-349 Legrand Salesforce Integration: Opp Details > CRMSync tab updates - Project, Record Type-->
    <h:form id="frmNewSFProjectDlg">
        <p:dialog id="dlgNewSFProject" header="New Project" widgetVar="wvDlgNewSFProject" responsive="true" width="650" modal="true" 
                  onShow="PF('wvDlgNewSFProject').initPosition();">

            <p:outputPanel id="opNewSFProjectDlg">
                <p:panelGrid  columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9" styleClass="box-primary no-border ui-fluid" layout="grid" >

                    <p:outputLabel for="inpTxtNewSFProjectName" value="Name" styleClass="required" />
                    <p:inputText id="inpTxtNewSFProjectName" value="#{oAuthBasedSyncService.projectName}"  />

                    <p:outputLabel for="inpTxtNewSFProjectStreet" value="Street" />
                    <p:inputText id="inpTxtNewSFProjectStreet" value="#{oAuthBasedSyncService.projectStreet}"  />

                    <p:outputLabel for="inpTxtNewSFProjectCity" value="City" styleClass="required" />
                    <p:inputText id="inpTxtNewSFProjectCity" value="#{oAuthBasedSyncService.projectCity}"  />

                    <p:outputLabel for="inpTxtNewSFProjectState" value="State" styleClass="required" />
                    <p:inputText id="inpTxtNewSFProjectState" value="#{oAuthBasedSyncService.projectState}"  />

                    <p:outputLabel for="inpTxtNewSFProjectCountry" value="Country" />
                    <p:inputText id="inpTxtNewSFProjectCountry" value="#{oAuthBasedSyncService.projectCountry}"  />

                    <p:outputLabel for="inpTxtNewSFProjectZipCode" value="Zip Code" />
                    <p:inputText id="inpTxtNewSFProjectZipCode" value="#{oAuthBasedSyncService.projectZipCode}"  />

                </p:panelGrid>
                <br/>

                <div class="button_bar" align="center">           
                    <p:commandButton value="Save" styleClass="btn btn-success btn-xs" widgetVar="btnSaveSFNewProjectWidget"  
                                     onclick="PF('btnSaveSFNewProjectWidget').disable();PF('dlgProcessing').show();"
                                     actionListener="#{oAuthBasedSyncService.onclickSaveNewSFProject()}" 
                                     update=":frmAurinkoOppCrt" 
                                     oncomplete="PF('btnSaveSFNewProjectWidget').enable();PF('dlgProcessing').hide();"/>


                    <p:spacer width="4px"/>
                    <p:commandButton  value="Cancel" styleClass="btn btn-danger btn-xs"
                                      oncomplete="PF('wvDlgNewSFProject').hide();" />
                </div> 

            </p:outputPanel>
        </p:dialog>
    </h:form>
</ui:composition>
