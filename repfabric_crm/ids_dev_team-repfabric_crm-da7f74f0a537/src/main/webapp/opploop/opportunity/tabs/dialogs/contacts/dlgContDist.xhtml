<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets">   

    <p:dialog   header="#{custom.labels.get('IDS_DISTRI')} Contact Lookup List" id="dlgContDist" widgetVar="dlgContDist" 
                closeOnEscape="true" position="200,50" maximizable="true" 
                modal="true">
        <p:dataTable id="tblDC" value="#{opportunities.listContLookup}"  
                     filteredValue="#{opportunities.listContLookupFiltered}"     
                     var="con"  
                     paginatorAlwaysVisible="=false"
                     paginatorPosition="top"
                     paginator="true"  
                     rows="15" 
                     widgetVar="tblDC" >

            <p:column  filterMatchMode="contains"  filterBy="#{con.contFullName}" headerText="Full Name" >

                <p:commandLink value="#{con.contFullName}"  
                               process="@this"
                               onclick="PF('dlgContDist').hide()"  
                               update=":frmOpp:tabOpp:oppDistriContactName   :frmOpp:tabOpp:pnlclrdist
                               :frmOpp:tabOpp:inpTxtDistriName  :frmOpp:tabOpp:oppCustomerContactName   :frmOpp:tabOpp:pnlclrcust
                               :frmOpp:tabOpp:oppCustomerName 
                               :frmOpp:tabOpp:oppSman"
                               oncomplete="PF('tblDC').clearFilters()"
                               actionListener="#{oppService.selectPrimaryContact(3, con.contId, con.contFullName, con.contCompany, con.contType)}" />
            </p:column>

            <p:column headerText="Company" filterMatchMode="contains"  filterBy="#{con.contCompany}">
                <h:outputLabel value="#{con.contCompany}" />

            </p:column>

            <p:column headerText="#{custom.labels.get('IDS_JOB_TITLE')}" filterMatchMode="contains"  filterBy="#{con.contJobTitle}">
                <h:outputLabel value="#{con.contJobTitle}" />

            </p:column>
            <p:column headerText="Email" filterMatchMode="contains"  filterBy="#{con.contEmailBusiness}">
                <h:outputLabel value="#{con.contEmailBusiness}" />
            </p:column>

            <p:column headerText="Phone" filterMatchMode="contains"  filterBy="#{con.contPhoneBusi}"> 
                <h:outputLabel  value="#{con.contPhoneBusi}" />
            </p:column>
        </p:dataTable>
 <!--update=":quickContForm"-->
        <p:commandButton  icon="ui-icon-plus" value="New Contact" onclick="PF('dlgContDist').hide();"   
                          process="@this" 
                          actionListener="#{oppService.initContact(3)}" 
                          oncomplete="PF('dlgContCreate').show();loadComp();"
                          update=" "
                          class="button_top btn_tabs"  />
    </p:dialog>

</ui:composition>

