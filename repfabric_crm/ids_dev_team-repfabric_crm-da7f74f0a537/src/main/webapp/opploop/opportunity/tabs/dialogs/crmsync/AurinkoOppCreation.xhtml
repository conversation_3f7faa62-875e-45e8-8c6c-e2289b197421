<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--#5881: Aurinko CRMSync:  Opportunity Creation-->
    <!--27-04-2022 7402 CRM-5397/YS-195: crmsync rheem: downflow all companies for aliasing and creation-->
    <p:dialog width="#{oAuthBasedSyncService.crmsyncPrinciOfOpp.crmsyncSysId==1?'900':'1200'}" 
              header="Target System #{custom.labels.get('IDS_OPP')} Creation" id="iddlgAurinkoOppCrt" widgetVar="dlgAurinkoOppCrt" 
              modal="true">
        <h:form id="frmAurinkoOppCrt" >

            <!--#6734: Aurinko CRMSync: Opp Creation > Auto populate Account ID if avalable-->
            <!--            <p:remoteCommand id="rcOpenAccLookup" name="rcOpenAccLookup" autoRun="true"
                                         action="#{oAuthBasedSyncService.isOpenAccountLookup()}" 
                                         update=":frmAurinkoOppCrt frmAurinkoOppCrt" />-->

            <!--#6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/New-->
            <!--#6743: Aurinko CRMSync: Opp Creation > Auto populate Account ID if avalable-->
            <p:remoteCommand id="rcUpdFrmAurOppCrt" name="rcUpdFrmAurOppCrt" autoRun="false" 
                             action="#{oAuthBasedSyncService.updateAurOppCrtForm(oAuthBasedSyncService.selectedAurinkoComp.colorCode)}"
                             update=":frmAurinkoOppCrt" process="@this" immediate="true" />
            <!--20-04-2022 7595 CRM-5488: CRMsync: new acct creation in target system-->
            <!--27-04-2022 7402 CRM-5397/YS-195: crmsync rheem: downflow all companies for aliasing and creation-->
            <p:remoteCommand id="rcFrmOppNewAurOpp" name="rcFrmOppNewAurOpp" 
                             onstart="PF('dlgProcessing').show();" 
                             actionListener="#{oAuthBasedSyncService.crtAurNewComp(oAuthBasedSyncService.linkedPrinciId,oAuthBasedSyncService.aurNewComp)}"
                             update=":frmAurinkoOppCrt" oncomplete="PF('dlgProcessing').hide();"/>

            <!--22-03-2022 7402 CRM-5397/YS-195: crmsync rheem: downflow all companies for aliasing and creation-->
            Show Target system Account for:
            <!--27-04-2022 7402 CRM-5397/YS-195: crmsync rheem: downflow all companies for aliasing and creation-->
            <!--16-06-2022 8180 CRM-5738: crmsync: extra companies: reverse  input-->
            <!--04-04-2023 10999 CRM-6620: Netsuite: Opp creation from RF to TS /  View TS opp-->
            <!--03-07-2023 11558 CRM-7113: Repfabric to Repfabric: Options for link, create, sync-->
            <p:dataList value="#{oAuthBasedSyncService.auroOppCompRef}" var="oppoComp" id="tblOppCompMap" varStatus="compMap"
                        rendered="#{oAuthBasedSyncService.crmsyncPrinciOfOpp.crmsyncSysId!=3}" 
                        styleClass="noBorders">    
                <div class="ui-g ui-fluid">

                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputLabel value="#{oppoComp.compName}: " />
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8">
                        <!--17-04-2023 10999 ** APRIL PRIORITY ** CRM-6620: Netsuite: Opp creation from RF to TS /  View TS opp-->
                        <p:inputText id="ipFrmAurCompId" value="#{oAuthBasedSyncService.crmsyncPrinciOfOpp.crmsyncSysId==15 ? oppoComp.compAddress1 : oppoComp.comments}" readonly="true" style="width: 70%"/>
                        <p:spacer width="4" />
                        <!--30-03-2022 7595 CRM-5488: CRMsync: new acct creation in target system-->
                        <!--27-04-2022 7402 CRM-5397/YS-195: crmsync rheem: downflow all companies for aliasing and creation-->            
                        <p:commandButton 
                            icon="fa fa-search" class="btn-primary btn-xs" style="width:50px"
                            actionListener="#{oAuthBasedSyncService.setRcName('rcFrmOppNewAurOpp')}" onclick="PF('dlgProcessing').show();"
                            action="#{oAuthBasedSyncService.filterAurComp(oppoComp,oAuthBasedSyncService.linkedPrinciId)}"
                            oncomplete="PF('dlgProcessing').hide();PF('wvDtSFAurinkoComp').clearFilters();PF('dlgAurinkoComp').show()"
                            update=":frmAuinkoCompLookup:pgAurCompFlt :frmAuinkoCompLookup:dtAurinkoComp :frmAurinkoOppCrt" />
                    </div>
                </div>
            </p:dataList>

            <!--13-10-2023 12315 CRMSYNC-349 Legrand Salesforce Integration: Opp Details > CRMSync tab updates - Project, Record Type-->
            <p:outputPanel id="opSFProjectRecordLookup" rendered="#{oAuthBasedSyncService.showProjectRecordLookups}" styleClass="noBorders" style="margin-left:10px;margin-top: -10px;">
                <div class="ui-g ui-fluid">

                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputLabel value="Project: " />
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8">
                        <p:inputText value="#{oAuthBasedSyncService.selectedProject!=null ? oAuthBasedSyncService.selectedProject.id : ''}" readonly="true" style="width: 70%"/>
                        <p:spacer width="4" />
                        <p:commandButton 
                            icon="fa fa-search" class="btn-primary btn-xs" style="width:50px"
                            onclick="PF('dlgProcessing').show();"
                            action="#{oAuthBasedSyncService.populateProject(oAuthBasedSyncService.linkedPrinciId)}"
                            update=":frmSFProjectLookupDlg:btnNewSFProject  :frmSFProjectLookupDlg:opSFPRojectLookup"
                            oncomplete="PF('wvDtSFProjectLookup').clearFilters();PF('wvDlgSFProjectLookup').show();PF('dlgProcessing').hide();" />
                    </div>

                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputLabel value="Record Type: " />
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8">
                        <p:inputText value="#{oAuthBasedSyncService.selectedRecordType!=null ? oAuthBasedSyncService.selectedRecordType.id : ''}" readonly="true" style="width: 70%"/>
                        <p:spacer width="4" />
                        <p:commandButton 
                            icon="fa fa-search" class="btn-primary btn-xs" style="width:50px"
                            onclick="PF('dlgProcessing').show();"
                            action="#{oAuthBasedSyncService.populateRecordType(oAuthBasedSyncService.linkedPrinciId)}"
                            update=":frmSFRecordTypeLookupDlg:dtSFRecordTypeLookup"
                            oncomplete="PF('wvDtSFRecordTypeLookup').clearFilters();PF('wvDlgSFRecordTypeLookup').show();PF('dlgProcessing').hide();"/>
                    </div>
                </div>
            </p:outputPanel>

            <!--13-06-2022 8180 CRM-5738: crmsync: extra companies: reverse  input-->
            <p:remoteCommand autoRun="false" id="checkAliased" name="checkAliased"  onstart="PF('dlgProcessingMsg').show();"
                             action="#{oAuthBasedSyncService.getLinkedCompanies()}" update=":frmAurinkoOppCrt" oncomplete="PF('dlgProcessingMsg').hide()" />

            <!--16-06-2022 8180 CRM-5738: crmsync: extra companies: reverse  input-->
            <p:dataList value="#{oAuthBasedSyncService.aurOpp.companies}" var="oppoComp" id="tblSugarOppCompMap" varStatus="sugarCompMap"
                        rendered="#{oAuthBasedSyncService.crmsyncPrinciOfOpp.crmsyncSysId==3}" styleClass="noBorders">   
                <div class="ui-g ui-fluid">

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="#{oppoComp.rfCompName} " />
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:selectOneMenu value="#{oppoComp.aurType}" disabled="#{oppoComp.aurType=='companyId' or oppoComp.aurType=='company2Id'}" 
                                         style="border-radius: 5px; background: white; height: 25px; width: 70%" >
                            <f:selectItem itemLabel="Select RF company" itemValue=""/>
                            <f:selectItems value="#{oAuthBasedSyncService.compTypes}"  var="compo" 
                                           itemLabel="#{compo.aurDescription}" itemValue="#{compo.aurType}" itemDisabled="#{compo.aurType=='companyId' or compo.aurType=='company2Id'}"/>
                        </p:selectOneMenu>
                    </div>

                    <div class="ui-sm-12 ui-md-6 ui-lg-6">
                        <p:inputText value="#{oppoComp.tsCompId}" readonly="true" style="width: 70%"/>
                        <p:spacer width="4" /> 
                        <p:commandButton 
                            icon="fa fa-search" class="btn-primary btn-xs" style="width:50px"
                            actionListener="#{oAuthBasedSyncService.setRcName('rcFrmOppNewAurOpp')}" onclick="PF('dlgProcessing').show();"
                            action="#{oAuthBasedSyncService.prfilterAurinkoComps(oppoComp,oAuthBasedSyncService.linkedPrinciId)}"
                            oncomplete="PF('dlgProcessing').hide();PF('wvDtSFAurinkoComp').clearFilters();PF('dlgAurinkoComp').show()"
                            update=":frmAuinkoCompLookup:pgAurCompFlt :frmAuinkoCompLookup:dtAurinkoComp :frmAurinkoOppCrt" />
                    </div>
                </div>
            </p:dataList>
            <!--08-11-2022 9391 Aurinko- CRM sync  ->while creating distributor customer company showing-->
            <br/>
            <!--13-06-2022 8180 CRM-5738: crmsync: extra companies: reverse  input-->
            <p:outputLabel value="Note: Please make sure to select target account type if aliased." />
            <br/>

            <div style=" text-align: center">
                <!--#6507: Aurinko CRMSync: Account Lookup: Updates to Pre-filter [seldAurComp]-->
                <!--#6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/Newpassing principal and princimode==3 in action added remotecommand in oncomplete]-->
                <!--#6734: Aurinko CRMSync: Opp Creation > Auto populate Account ID if avalable[changed to accId in disadble and actions]-->
                <!--#6743: Aurinko CRMSync: Opp Creation > Auto populate Account ID if avalable-->
                <!--22-03-2022 7402 CRM-5397/YS-195: crmsync rheem: downflow all companies for aliasing and creation-->
                <!--06-04-2022 7676 CRM-5530  CRMSync: screen refresh problem on initial linkage in manual mode-->
                <!--20-04-2022 7776 Aurinko CRMSync: Update Last Modified timestamp of Opportunity-->
                <!--27-04-2022 7402 CRM-5397/YS-195: crmsync rheem: downflow all companies for aliasing and creation-->
                <!--13-06-2022 8180 CRM-5738: crmsync: extra companies: reverse  input-->
                <p:commandButton id="cbFrmAurCompCrtOpp" value="Create #{custom.labels.get('IDS_OPP')}" class="btn-primary btn-xs"
                                 onclick="PF('dlgProcessing').show();" disabled="#{empty oAuthBasedSyncService.aurAccId}"
                                 action="#{oAuthBasedSyncService.validateSugarOppCreate(users.userId,oAuthBasedSyncService.linkedPrinciId,oAuthBasedSyncService.oppId,oAuthBasedSyncService.aurAccId ,oAuthBasedSyncService.crmsyncPrinciOfOpp.crmsyncMode==3)}"
                                 oncomplete="PF('dlgProcessing').hide();" />
                <p:spacer width="4" />
                <p:commandButton value="Cancel" class="btn-primary btn-xs"
                                 action="#{oAuthBasedSyncService.aurinkoOppCrtCancel()}"
                                 oncomplete="PF('dlgAurinkoOppCrt').hide();" />
            </div>
        </h:form>

    </p:dialog>

    <!--13-06-2022 8180 CRM-5738: crmsync: extra companies: reverse  input-->
    <ui:include src="../../../../../crmsync/dialogs/NotAllAliasConfirmation.xhtml" />
    <!--13-10-2023 12315 CRMSYNC-349 Legrand Salesforce Integration: Opp Details > CRMSync tab updates - Project, Record Type-->
    <ui:include src="SFProjectLookupDlg.xhtml"/>
    <ui:include src="SFRecordTypeLookupDlg.xhtml"/>

    <!--13-06-2022 8180 CRM-5738: crmsync: extra companies: reverse  input-->
    <p:dialog id="dlgIdProcessingMsg" widgetVar="dlgProcessingMsg" closable="false" modal="true" header="Message" onShow="PF('dlgProcessing').initPosition();" resizable="false" >
        <h:form>
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif"   />
                <p:spacer width="5" />
                <p:outputLabel value="Checking aliases please wait..." />
                <br /><br />
            </p:outputPanel>
        </h:form>
    </p:dialog>

    <!--16-06-2022 8180 CRM-5738: crmsync: extra companies: reverse  input-->
    <style>
        .noBorders *{
            border: none !important;
            list-style-type: none;
        }
    </style>

</ui:composition> 