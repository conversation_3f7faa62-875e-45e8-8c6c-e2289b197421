<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">
    <p:dialog modal="true"   header="#{custom.labels.get('IDS_PROD_REGS')}" widgetVar="dlgRegist">
        <h:form id="oppLinePreg" >
            <p:panel id="pnlLPR" class="pnldlgs">
                <p:panelGrid columns="2">
                    <p:outputLabel value="#{custom.labels.get('IDS_MFG_REG_NUM')}"/>
                    <p:inputText value="#{registrations.regiManfRegNum}" maxlength="30" />

                    <p:outputLabel value="#{custom.labels.get('IDS_APPROVED_DATE')}"/>
                    <p:calendar value="#{registrations.regiApproveDate}"   pattern="#{globalParams.dateFormat}"
                                converterMessage="Date not valid">
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                    </p:calendar>

                    <p:outputLabel value="Expiry Date"/>
                    <p:calendar value="#{registrations.regiExpiryDate}"   pattern="#{globalParams.dateFormat}"
                                converterMessage="Date not valid">
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                    </p:calendar>             

                    <p:outputLabel value="Approved By"/>
                    <p:inputText value="#{registrations.regiApprovedBy}" maxlength="40" />

                    <p:outputLabel value="Notes"/>
                    <p:inputTextarea value="#{registrations.regiNotes}" cols="25" rows="3"/>

                    <p:outputLabel value="Registration Level"/>
                    <p:inputText value="#{registrations.regiLevel}"  maxlength="30"/>

                    <p:commandButton  process=":oppLinePreg:pnlLPR" disabled="#{opportunities.oppCloseStatus!=0}" 
                                      value="Save" actionListener="#{registrations.save()}"
                                      class="button_top btn_tabs" 
                                      oncomplete="if (args &amp;&amp; !args.validationFailed) PF('dlgRegist').hide()" 
                                      icon="ui-icon-disk" 
                                      update=":frmOpp:tabOpp:dtLine"                />
                    <p:commandButton type="button" value="Cancel"
                                     class="button_top btn_tabs"
                                     icon="ui-icon-cancel" 
                                     onclick="PF('dlgRegist').hide()"/>
                </p:panelGrid>
            </p:panel>
        </h:form>
    </p:dialog>


</ui:composition>

