<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">

    <!--04-09-17 - CR #24 -  Load Comments -->
    <p:remoteCommand actionListener="#{comments.loadComments()}" name="cmtLoad" autoRun="false" update=":frmOpp:tabOpp:dtCmnt" />
    <div class="with-border">
        <h:panelGroup id="pnlCMNTS" class="pnls" >
          <!--<h4  class="sub_title">#{custom.labels.get('IDS_OPP')} Comments</h4>-->
            <!--#408039:Make comments window wider-->
            <!--1721 CRM-3148: "Update" button does not save comments-->
            <!--//06-06-2022 : #8141 : CRM-5452  Sabrina Perez w/Mega Western Sales - OSR (Alex Padilla) is not getting notifications on co-->
            <p:outputPanel style="display:flex;">
                <p:inputTextarea rows="10" cols="60" value="#{comments.oppCommText}" id="cmnt" style="width: 70%">
                    <p:ajax event="valueChange" listener="#{comments.onChange(comments.oppCommText)}"/>
                </p:inputTextarea>
                <p:watermark for="cmnt" value="Enter comment here"/>
                <p:spacer width="4"/>
                <p:dataTable id="watcher" value="#{comments.setUserList}" filteredValue="#{comments.filterList}"
                             selection="#{comments.selectedList}"
                             var="user" rowSelectMode="add"
                             multiViewState="true" styleClass="color"
                             widgetVar="user" style="width:30%;" scrollable="true" scrollHeight="150"
                             rowKey="#{user.recId}" >
                    <p:ajax event="rowSelectCheckbox" process="@this" update=":frmOpp:tabOpp:watcher"/>
                        <f:facet name="header">
                            <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                        <div style="background:#c8d6f9;font-weight: bold; text-align: left; font-size:17px;margin-left: -10px;margin-right: -10px;padding:5px;margin-bottom:-5px;">
                            Notify #{custom.labels.get('IDS_WATCHERS')}
                        </div>
                    </f:facet>
                    <p:column    selectionMode="multiple"  style="width: 20%;text-align:center;" toggleable="false" exportable="false" id="chkMultipleWatchr">
                    </p:column>
                    <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                    <p:column headerText="#{custom.labels.get('IDS_WATCHERS')}" filterMatchMode="contains" style="width: 80%" filterBy="#{user.watcherName}">
                        <h:outputText value="#{user.watcherName}" />
                    </p:column>
                </p:dataTable>
            </p:outputPanel>

            <br></br>
            <!--1512 CRM-3142: Error when editing a closed Opp - comments tab-->
            <p:commandButton value="Save"  
                             update="dtCmnt cmnt"
                             process=":frmOpp:tabOpp:pnlCMNTS" 
                             class="btn btn-xs btn-success"
                             style="margin-left: 0px" 
                             actionListener="#{comments.save()}"/>
            <br/>
            <div class="dtnoborder"  >
                <!--#807698 08/11/2017-->
                <p:dataTable  value="#{comments.listComments}" emptyMessage="No #{custom.labels.get('IDS_COMMENTS')} Added" 
                              var="cmnt" id="dtCmnt">
                    <p:column style="padding: 0px;" >  
                        <div class="comment_div">  
                            <div>
                                <!--                             TASK #1943: CRM-1137: Manu-Tek: Internal Comment (Displaying  comment date ) -->
                                <h:outputLabel id="cmntDate" value="#{opportunities.getFormattedDate(cmnt.oppCommDate,'dt')}" style="float: right;"/>
                                #{history.getFormattedDate(cmnt.oppCommDate,'dt')}
                                <p:spacer width="4" style="float: right;"/>
                                <h:graphicImage library="images" name="dummy.jpg" width="20" height="20"/>                 
                                <h:outputLabel value="#{users.getUserName(cmnt.oppCommUser)} : " style="font-weight: bold"/>

                                <h:outputLabel class="dtcomnt"  value="#{rFUtilities.convertFromUTC(cmnt.oppCommDate)}" >
                                    <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                                </h:outputLabel>
                                <!--1512 CRM-3142: Error when editing a closed Opp - comments tab-->
                                <p:commandButton rendered="#{cmnt.oppCommUser eq loginBean.userId}"
                                                 title="Remove comment"
                                                 process="@this" 
                                                 actionListener="#{comments.delete(cmnt.recId)}" 
                                                 update="dtCmnt " 
                                                 style="  background: none;float: right"
                                                 class="btn-danger btn-xs"
                                                 icon="fa fa-trash" />
                            </div>
                            <div class="comment_cmnt" style="white-space: pre-wrap">
                                <h:outputLabel id="cmnt" value="#{cmnt.oppCommText}" escape="false"/>  
                            </div>

                        </div>

                    </p:column>

                </p:dataTable>   
            </div>
        </h:panelGroup>
    </div>
</ui:composition>



