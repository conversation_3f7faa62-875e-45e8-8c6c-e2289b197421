<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <p:dialog modal="true"   header="#{custom.labels.get('IDS_DWINS')}" widgetVar="dlgCDesign" id="dlgCDesign1">
        <h:form id="oppLineDesign" >
            <p:panel id="pnlLDW" class="pnldlgs">
                <p:panelGrid columns="2" >
                    <p:outputLabel value="#{custom.labels.get('IDS_DWIN')} No: "/>
                    <p:inputText value="#{designWin.dwinNum}"  maxlength="45"/>

                    <p:outputLabel value="Date: "/>
                    <p:calendar value="#{designWin.dwinDate}"   pattern="#{globalParams.dateFormat}"   
                                converterMessage="Date not valid"    >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                    </p:calendar>
                    <p:outputLabel value="Exp.Date: "/>
                    <p:calendar value="#{designWin.dwinExpiryDate}"   pattern="#{globalParams.dateFormat}"   
                                converterMessage="Date not valid"      >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                    </p:calendar>
                    <p:outputLabel value="Status: "/>
                    <p:inputText value="#{designWin.dwinStatus}" maxlength="30" />
                    <p:outputLabel value="Type: "/>
                    <p:inputText value="#{designWin.dwinType}" maxlength="30" />
                </p:panelGrid>

                <p:commandButton  class="button_top btn_tabs"   process=":oppLineDesign:pnlLDW" 
                                  disabled="#{opportunities.oppCloseStatus!=0}" 
                                  value="Save" actionListener="#{designWin.save()}" 
                                  oncomplete="if (args &amp;&amp; !args.validationFailed) PF('dlgCDesign').hide()" 
                                  icon="ui-icon-disk" 
                                  update=":frmOpp:tabOpp:dtLine"   />
                <p:commandButton type="button"  escape="true" value="Cancel" 
                                 class="button_top btn_tabs" 
                                 icon="ui-icon-cancel" 
                                 onclick="PF('dlgCDesign').hide()"/>
            </p:panel>
        </h:form>
    </p:dialog>

</ui:composition>

