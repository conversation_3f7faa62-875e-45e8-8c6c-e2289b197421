<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui">
    <p:dialog id="dlgqot"  widgetVar="dlgqot"  header="#{custom.labels.get('IDS_QUOTES')}" >
        
        <p:commandButton class="btn btn-primary btn-xs" disabled="#{quotesHdr.listQuotesCopied.size()==0}" onclick="window.open('../quotes/NewQuote.xhtml?recId=0&amp;oppId=#{opportunities.oppId}', '_self');"
                                 value="View Quote"  title="View Quote" />
    
        
        <p:dataTable var="quot"
                     paginator="true"
                     paginatorAlwaysVisible="false"
                     rows="15"
                     value="#{quotesHdr.listQuotesCopied}">
            <p:column headerText="#{custom.labels.get('IDS_MFG_PART_NUM')}">
                #{quot.oppItemPartManf}
            </p:column>

            <p:column headerText="Qty"  >
                #{quot.oppItemQnty.intValue()}
            </p:column>

            <p:column headerText="EAU" >
                #{quot.oppEau}                                  
            </p:column>

            <p:column headerText="Cost" >
                #{quot.oppItemCost}                               
            </p:column>

            <p:column headerText="Approved Sales">
                #{quot.quotResale}
            </p:column>

            <p:column headerText="Lead Time" >
                #{quot.quotLeadTime}
            </p:column>
            
<!--            <p:column headerText="Recipient">
                #{qot.quotReceipient}
            </p:column>
            <p:column headerText="#{custom.labels.get('IDS_RFQ_NUM')}">
                #{qot.quotRfqNum}
            </p:column>
            <p:column headerText="#{custom.labels.get('IDS_PRINCI')} Quote No.">
                #{qot.quotPrinciQotNum}
            </p:column>
            <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')} Ref.No">
                #{qot.quotCustRefNum}
            </p:column>
            <p:column headerText="Expiry">
                <p:outputLabel value="#{qot.quotExpiry}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:outputLabel>
            </p:column>-->
        </p:dataTable>
    </p:dialog>

</ui:composition>

