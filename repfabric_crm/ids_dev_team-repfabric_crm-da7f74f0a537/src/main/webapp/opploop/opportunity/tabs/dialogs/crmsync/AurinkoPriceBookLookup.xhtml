<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"> 

    <!--#6081: Aurinko CRM Sync: Line Item Creation-->

    <p:dialog header="Create new line item at Target System" modal="true" width="700" id="iddlgAurinkoNewLineItem" widgetVar="dlgAurinkoNewLineItem" >
        <h:form id="frmAuinkoNewLineItem" >
            <h:panelGrid columns="3" cellpadding="5">
                <p:outputLabel  value="Price Book ID" />
                <p:outputLabel class="m" value=":" />
                <h:panelGroup id="aurinkoLineItemId">
                    <p:spacer width="150" height="0"/>
                    <!--#6522: Aurinko CRMSync: Price Book Entry Lookup: Updates to Pre-filter-->
                    <p:inputText  value="#{oAuthBasedSyncService.seldPBook.oppItemSyncId}" readonly="true" />
                    <p:spacer width="4"/>
                    <!--#6251: Aurinko CRMSync: Configure Sync for Parent company-->
                    <!--#6326: Aurinko CRMSync: Pricebook Entry Lookup-->
                    <!--#6459: Aurinko CRMSync:  Pricebook Entry Lookup > Filters-->
                    <!--#6903: YS-173: Changes to Pricebook Lookup[added action and actionlistener is changed]-->
                    <!--05-07-2022 8215 Aurinko CRMSync: Usability updates on Line Items tab > New TS Line item-->
                    <p:commandButton id="btnSrchAurinkoPriceBook"
                                     actionListener="#{oAuthBasedSyncService.clearPriceBooksList(false)}"
                                     action="#{oAuthBasedSyncService.pricebookIdAndPrefilter(oAuthBasedSyncService.linkedPrinciId ,null,oAuthBasedSyncService.partNumber, loginBean.userId)}"
                                     icon="fa fa-search" class="btn-primary btn-xs" 
                                     onclick="PF('prcDlg1').show();" immediate="true" 
                                     oncomplete="PF('prcDlg1').hide();PF('wvDtSFAurinkoPriceBook').clearFilters();PF('dlgAurinkoPriceBook').show();" 
                                     update=":frmAuinkoPriceBookLookup:dtAurinkoPriceBook :frmAuinkoPriceBookLookup:btnAurinkoMorePriceBook :frmAuinkoPriceBookLookup:pgAurPBook :frmAuinkoPriceBookLookup:opLineitemPriceBookName"/>
                </h:panelGroup>
            </h:panelGrid>
            <br/>
            <br/>
            <div style="text-align: center">
                <!--#6019: line item link->blue icon showing for unlinked line item-->
                <!--#6801: Aurinko CRMSync: Sync Status[added id]-->
                <!--20-04-2022 7776 Aurinko CRMSync: Update Last Modified timestamp of Opportunity-->
                <p:commandButton  id="btnCrtNewLI" value="Create Line Item" class="btn-primary btn-xs"
                                  onclick="PF('dlgProc').show();" 
                                  actionListener="#{oAuthBasedSyncService.createLineItem(oAuthBasedSyncService.linkedPrinciId, opportunities,oppLineItems)}"
                                  oncomplete="PF('dlgProc').hide();PF('dlgAurinkoNewLineItem').hide();PF('wvDtLine').filter();" 
                                  update=":frmOpp:tabOpp:lnItmId :frmOpp:tabOpp:dtLine :frmSummary" disabled="#{empty oAuthBasedSyncService.seldPBook.oppItemSyncId}"/>
                <p:spacer width="4" />

                <p:commandButton value="Cancel" class="btn-primary btn-xs"
                                 onclick="PF('dlgAurinkoNewLineItem').hide();" />
            </div>
        </h:form>
    </p:dialog>


    <!--#6081: Aurinko CRM Sync: Line Item Creation-->
    <h:form id="frmAuinkoPriceBookLookup" >
        <!--16-06-2022 8215Aurinko CRMSync: Usability updates on Line Items tab > New TS Line item-->
        <p:remoteCommand autoRun="false" id="rcUpdAurinkoPriceBookLooup" name="rcUpdAurinkoPriceBookLooup" 
                         update=":frmAuinkoPriceBookLookup:pgAurPBook :frmAuinkoPriceBookLookup:dtAurinkoPriceBook " 
                         oncomplete="PF('wvDtSFAurinkoPriceBook').clearFilters();" immediate="true"/>
        <!--#6477: Aurinko CRMSync > UI Updates to Lookups - Opp/Account/Pricebook entry-->
        <!--07-07-2022 8381 CRM-5871   {Line items} The table in the "Price book lookup" does not adjust to the window size-->
        <p:dialog header="Price Book Lookup" modal="true" width="1200" id="iddlgAurinkoPriceBook" widgetVar="dlgAurinkoPriceBook" resizable="false" onShow="PF('dlgAurinkoPriceBook').initPosition();" >
            <!--#6522: Aurinko CRMSync: Price Book Entry Lookup: Updates to Pre-filter-->
            <p:ajax event="close" listener="#{oAuthBasedSyncService.clearPriceBooksList(oAuthBasedSyncService.isUpdateLineitem)}" update=":frmAuinkoPriceBookLookup"/>
            <!--#6459: Aurinko CRMSync:  Pricebook Entry Lookup > Filters-->
            <p:remoteCommand id="rcSetPagePBook" name="rcSetPagePBook" oncomplete="PF('wvDtSFAurinkoPriceBook').paginator.setPage(PF('wvDtSFAurinkoPriceBook').paginator.cfg.pageCount-1);" />

            <!--#6459: Aurinko CRMSync:  Pricebook Entry Lookup > Filters-->
            <!--08-07-2022 8215 Aurinko CRMSync: Usability updates on Line Items tab > New TS Line item-->
            <h:panelGroup id="pgAurPBook">
                <p:outputPanel id="opPartDescSection" rendered="#{not oAuthBasedSyncService.isUpdateLineitem}">
                    <!--15-06-2022 8215Aurinko CRMSync: Usability updates on Line Items tab > New TS Line item-->
                    <div class="ui-g ui-fluid header-bar">
                        <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                            <p:outputLabel styleClass="acct-name-hdr" class="sub_title" rendered="#{not oAuthBasedSyncService.isUpdateLineitem}">#{custom.labels.get('IDS_MFG_PART_NUM')}: #{oAuthBasedSyncService.partNumber}</p:outputLabel>
                        </div>
                        <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                            <p:outputLabel styleClass="acct-name-hdr" class="sub_title" rendered="#{not oAuthBasedSyncService.isUpdateLineitem}">#{custom.labels.get('IDS_LINE_ITEM_DESC')}: #{oAuthBasedSyncService.partDescription}</p:outputLabel>
                        </div>
                    </div>
                </p:outputPanel>
                <div class="ui-g ui-fluid">
                    <!--#6522: Aurinko CRMSync: Price Book Entry Lookup: Updates to Pre-filter-->
                    <!--#6903: YS-173: Changes to Pricebook Lookup[changed ui design]-->
                    <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                        <!--                        <p:remoteCommand autoRun="true" 
                                                                 update=":frmAuinkoPriceBookLookup:itLineitemPriceBookName" />-->
                        <p:outputLabel id="lblAurPriceBookProduct" value="Product Name: " />
                        <p:spacer width="8"/>
                        <!--30-06-2022 8215 Aurinko CRMSync: Usability updates on Line Items tab > New TS Line item-->
                        <h:panelGroup id="opLineitemPriceBookName" style="width: 30%" >
                            <p:inputText id="itLineitemPriceBookName" value="#{oAuthBasedSyncService.aurOppNameFlt}" style="width: 30%"  />
                        </h:panelGroup>

                        <p:spacer width="8"/>
                        <h:panelGroup id="pgAurPBookPFamily">
                            <p:outputLabel id="lblAurFltPBookPFmily" value="Product Family:" />
                            <p:spacer width="8" />
                            <p:inputText value="#{oAuthBasedSyncService.aurOppCompFlt}"
                                         style="width: 30%;"
                                         id="itAurFltPBookPFamily" />  
                        </h:panelGroup>
                    </div>
                    <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                        <h:panelGroup id="pgAurPBookOptions">
                            <p:outputLabel id="lblAurFltPBookPOption" value="Price Book:" />
                        </h:panelGroup>
                    </div>

                    <div class="ui-sm-12 ui-md-2 ui-lg-2 ">
                        <h:panelGroup id="pgAurPBookOptionsDrpDown">
                            <!--#6903: YS-173: Changes to Pricebook Lookup[addedd filter and footer]-->
                            <p:selectOneMenu value="#{oAuthBasedSyncService.pbookOptionSelected}"  
                                             style="display: flex; border-radius: 5px; background: white; height: 25px;  min-width: 0 !important; width: 100%;"
                                             filter="true" filterMatchMode="contains">
                                <p:ajax event="change" process="@this" immediate="true" update="@this"/>
                                <f:selectItems value="#{oAuthBasedSyncService.pbookDrpDown}" />
                                <f:facet name="footer">
                                    <h:outputText value="#{oAuthBasedSyncService.pbookDrpDown.size()} options"
                                                  style="font-weight:bold;"/>
                                </f:facet>
                            </p:selectOneMenu >
                        </h:panelGroup>
                    </div>

                    <!--#6522: Aurinko CRMSync: Price Book Entry Lookup: Updates to Pre-filter-->
                    <!--#6903: YS-173: Changes to Pricebook Lookup[removed div and changed 1 to 3]-->
                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <!--#6313: Aurinko CRMSync: Opportunity Lookup > Next Page-->
                        <!--#6903: YS-173: Changes to Pricebook Lookup[passing id in action listener]-->
                        <!--15-07-2022 8480 Analysis task related to #5381 CRM-5871  ~{Line items} The table in the "Price book lookup" does not-->
                        <p:commandButton id="btnAurPBookFlt" value="Filter"
                                         class="btn btn-primary btn-xs"   
                                         title="Fetch price books" 
                                         onclick="PF('dlgFetchingRecords').show();"
                                         action="#{oAuthBasedSyncService.ppltAurPriceBokFlt(oAuthBasedSyncService.pbookOptionSelected, oAuthBasedSyncService.linkedPrinciId, loginBean.loggedInUser.userId, null)}"
                                         update=":frmAuinkoPriceBookLookup:dtAurinkoPriceBook :frmAuinkoPriceBookLookup:pgAurPBook"
                                         oncomplete="PF('dlgFetchingRecords').hide();PF('wvDtSFAurinkoPriceBook').clearFilters();PF('dlgAurinkoPriceBook').initPosition();" />
                    </div>
                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <p:commandButton id="btnRstAurPBookLookup" value="Clear"
                                         class="btn btn-primary btn-xs" rendered="#{oAuthBasedSyncService.resetAurCompTbl}"
                                         title="Clear everything"
                                         actionListener="#{oAuthBasedSyncService.clearPriceBooksList(oAuthBasedSyncService.isUpdateLineitem)}"
                                         update=":frmAuinkoPriceBookLookup:dtAurinkoPriceBook :frmAuinkoPriceBookLookup:pgAurPBook"
                                         oncomplete="PF('wvDtSFAurinkoPriceBook').clearFilters()" />
                    </div>
                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <!--#6459: Aurinko CRMSync:  Pricebook Entry Lookup > Filters-->
                        <!--#6477: Aurinko CRMSync > UI Updates to Lookups - Opp/Account/Pricebook entry-->
                        <p:commandButton value="Load More" onclick="PF('dlgFetchingRecords').show()" id="btnAurinkoMorePriceBook" style=" width: 100px"
                                         class="btn btn-success btn-xs" rendered="#{not empty oAuthBasedSyncService.aurPriceNextPage}"
                                         action="#{oAuthBasedSyncService.ppltAurinkoPriceBook(oAuthBasedSyncService.pbookOptionSelected,oAuthBasedSyncService.linkedPrinciId,loginBean.loggedInUser.userId,oAuthBasedSyncService.aurPriceNextPage)}"
                                         oncomplete="PF('dlgFetchingRecords').hide();PF('wvDtSFAurinkoPriceBook').filter();" 
                                         update=":frmAuinkoPriceBookLookup:dtAurinkoPriceBook :frmAuinkoPriceBookLookup:pgAurPBook"/>
                    </div>
                </div>
            </h:panelGroup>

            <!--07-07-2022 8381 CRM-5871   {Line items} The table in the "Price book lookup" does not adjust to the window size-->
            <p:dataTable id="dtAurinkoPriceBook" widgetVar="wvDtSFAurinkoPriceBook"
                         scrollHeight="400"
                         scrollable="true"
                         value="#{oAuthBasedSyncService.aurinkoPriceBooks}"                         
                         filterEvent="keyup"
                         var="priceBook"
                         emptyMessage="No Price Book entries found"
                         multiViewState="true"
                         selection="#{oAuthBasedSyncService.selectedPriceBook}"
                         selectionMode="single"
                         rowKey="#{priceBook.oppItemSyncId}"
                         paginator="true" paginatorAlwaysVisible="true"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="100">

                <!--#6126: Aurinko CRMSync: Checkbox - Create new line items using target system Part No.-->
                <p:ajax event="rowSelect" listener="#{oAuthBasedSyncService.onPriceBookSelected()}"
                        oncomplete="PF('dlgAurinkoPriceBook').hide();" update=":frmAuinkoNewLineItem" />

                <p:column width="25" headerText="xId" filterBy="#{priceBook.oppItemSyncId}" rendered="false">
                    #{priceBook.oppItemSyncId}
                </p:column>
                <p:column width="25" headerText="Pricebook Name" filterBy="#{priceBook.btnName}" filterMatchMode="contains" 
                          sortBy="#{priceBook.btnName}" >
                    #{not empty priceBook.btnName ? priceBook.btnName : ""}
                </p:column>
                <p:column width="25" headerText="Product Name" filterBy="#{priceBook.oppItemCustOrdNum}" filterMatchMode="contains" 
                          sortBy="#{priceBook.oppItemCustOrdNum}"  >
                    #{not empty priceBook.oppItemCustOrdNum ? priceBook.oppItemCustOrdNum : ""}
                </p:column>
                <p:column width="25" headerText="Product Family" filterBy="#{priceBook.oppItemPrinciQotNum}" filterMatchMode="contains" 
                          sortBy="#{priceBook.oppItemPrinciQotNum}"  >
                    #{not empty priceBook.oppItemPrinciQotNum ? priceBook.oppItemPrinciQotNum : ""}
                </p:column>
                <p:column width="30" headerText="Product Description" filterBy="#{priceBook.oppItemCustRfqNum}" filterMatchMode="contains" 
                          sortBy="#{priceBook.oppItemCustRfqNum}"  >
                    #{not empty priceBook.oppItemCustRfqNum ? priceBook.oppItemCustRfqNum : ""}
                </p:column>
                <p:column width="25" headerText="Code" filterBy="#{priceBook.oppItemPartCust}" filterMatchMode="contains" 
                          sortBy="#{priceBook.oppItemPartCust}"  >
                    #{not empty priceBook.oppItemPartCust ? priceBook.oppItemPartCust : ""}
                </p:column>
                <p:column width="25" headerText="Unit Price" filterBy="#{priceBook.oppItemCost}" filterMatchMode="contains" 
                          sortBy="#{priceBook.oppItemCost}">
                    #{priceBook.oppItemCost}
                </p:column>
            </p:dataTable>
        </p:dialog>
    </h:form>

    <!--#6081: Aurinko CRM Sync: Line Item Creation-->
    <!--    <p:dialog id="dlgIdMasterAurinkoNewOppLI" widgetVar="dlgMasterAurinkoNewOppLI" width="600" closable="false" 
                  modal="true" header="Choose master data as" resizable="false" >
            <h:form id="frmAurinkoNewOppLIChsMstr" >
                <h:panelGroup>
    
                    <div class="ui-g ui-fluid">
                        <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                            <p:outputLabel value="Last Update Timestamp of:" />
                        </div>
    
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                            <p:outputLabel value="Repfabric #{custom.labels.get('IDS_OPP')}"/>
                        </div>
                        <div class="ui-sm-12 ui-md-8 ui-lg-8 ">
                            <p:outputLabel value="#{oppLineItems.updDate}"/>
                        </div>
                        
                                            <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                                                <p:outputLabel value="#{oAuthBasedSyncService.selectedAurinkoOppLI.oppItemCustOrdNum} #{custom.labels.get('IDS_OPP')}"/>
                                            </div>
                                            <div class="ui-sm-12 ui-md-8 ui-lg-8 ">
                                                <p:outputLabel value=" #{oAuthBasedSyncService.selectedAurinkoOppLI.oppItemPartCust}"/>
                                            </div>
    
                        <div class="ui-sm-12 ui-md-12 ui-lg-12 "/>
                        <div class="ui-sm-12 ui-md-12 ui-lg-12 "/>
    
                        <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                            <p:outputLabel value="Choose master data as:"/>
                        </div>
    
                        <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                            <p:selectOneRadio id="radioIntMasterSel" value="#{oAuthBasedSyncService.oppLIMasterId}" 
                                              style="width:100%;" layout="lineDirection" >
                                <f:selectItem itemLabel="Repfabric #{custom.labels.get('IDS_OPP')}" itemValue="0"/>
                                <f:selectItem itemLabel="Target System #{custom.labels.get('IDS_OPP')}" itemValue="1"/>
                                <f:ajax execute="@this" />
                            </p:selectOneRadio>
                        </div>
                    </div> 
    
                    <br/>
                    <div style="text-align: center">
                        #6251: Aurinko CRMSync: Configure Sync for Parent company
                        <p:commandButton value="Ok" class="btn-primary btn-xs"
                                         update=""
                                         onclick="doneCreation();"
                                         oncomplete="PF('dlgProc').hide();"/>
                    </div>
                </h:panelGroup>
            </h:form>
        </p:dialog> -->

</ui:composition> 
