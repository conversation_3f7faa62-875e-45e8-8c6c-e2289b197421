<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:f="http://xmlns.jcp.org/jsf/core"> 
    <!--- PMS:8668 : 12-11-2024 : Task:14863 added new feature of Journal Activity on the Opportunites Module --> 
    <p:outputPanel style="margin-bottom: 10px" rendered="#{activityJournalHdr.ajAccessflag==1}">        
        <p:commandLink value="Print"   
                       onclick="rmUpdateAJTbl1();" process="@this" class="button_top btn-primary btn-xs" id="cmndLnkPrint"  >
            <p:printer target="pnlJrnlList"  />
        </p:commandLink>
        <p:spacer width="10"/>
        <p:commandLink value="Export" class="button_top btn-primary btn-xs" 
                       oncomplete="PF('dlgExportType').show();"  process="@this" id="cmndLnkExport"
                       actionListener="#{exportService.prepareExportDialog('OPP_ACTIVITY_JOURNAL')}" update=":exportJournalDetails"/>
    </p:outputPanel>
    <p:remoteCommand name="rmUpdateAJTbl1" id="rmUpdateAJTbl" update=":frmOpp:tabOpp:pnlCompanyJournalList  :frmOpp:tabOpp:AddPrincipalDialog :frmOpp:tabOpp:pnlLoadMore" autoRun="false"/>

    <p:outputPanel style="height:910px;overflow-x: scroll;overflow-x: hidden" id="pnlCompanyJournalList" 
                   rendered="#{activityJournalHdr.ajAccessflag==1}">
        <ui:repeat value="#{activityJournalHdr.journalHeaderList}" var="jrnl" id="tblJrnlList" >

            <table>
                <tr>
                    <p:separator style="margin-bottom:7px;margin-top: 3px"/>
                </tr>
                <tr>
                    <td style="width: 100%">

                        <p:outputLabel id="oLblAjCompany"
                                       value="#{jrnl.strAcjhDate},&#160;&#160;&#160;By #{jrnl.getUserName(jrnl.acjhUser)},&#160;&#160;&#160;Company: #{jrnl.acjhCompName},&#160;&#160;&#160; Contacts: #{jrnl.getAttedee(jrnl.acjhId)}">

                        </p:outputLabel>   

                    </td>
                    <td style="width: 100%">

                        <p:commandButton styleClass="btn-primary  btn-xs btn-icon" 
                                         id="btnAjCompEdit"
                                         icon="fa fa-pencil" title="Edit" style="margin-right: 10px!important"
                                         onclick="window.open('../../Journal/JournalEntry.xhtml?id=#{jrnl.acjhId}', '_self');"/>

                    </td>
                </tr>
                <tr>
                    <td style="width: 100%">

                        <p:outputLabel value=" Regarding: #{jrnl.acjhTitle}" id="oLblRegarding"/>
                    </td>
                </tr>
                <tr>
                    <td style="width: 100%">

                        <p:inputTextarea value="Notes: #{jrnl.gettrimmedAjNote(jrnl.acjhId)}" readonly="true" rows="1"
                                         rendered="#{not empty jrnl.getActivityDesc(jrnl.acjhId)}" id="txtAjNote"
                                         style="width: 110%;border: none;margin: 2px 2px 2px -3px"  
                                         >
                        </p:inputTextarea>
                    </td>

                </tr>
                <tr>
                    <td>
                        <p:commandLink value="More..." rendered="#{jrnl.overLayFlag eq true}"  
                                       id="btnOverLay" style="color: blue"
                                       actionListener="#{activityJournalHdr.populateAJnotes(jrnl.acjhId)}"
                                       />
                    </td>
                </tr>
                <tr>
                    <p:outputLabel rendered="#{activityJournalHdr.journalHeaderList.size()==0}" id="oLblNoRecordsMsg">
                        No records found
                    </p:outputLabel>
                </tr>
            </table>
            <br></br>
            <p:dataTable rowIndexVar="rowIndex"  
                         rowStyleClass="#{(rowIndex mod 2) eq 0? 'jrnl-tbl-even':'jrnl-tbl-odd'}" 
                         value="#{jrnl.actMap}" 
                         var="dtl" 

                         id="tbljrnlDetail" 
                         class="hide-column-names jrnl-tbl aj-header">

                <p:column style="width:10%!important">
                    <p:outputLabel value="#{dtl.value}" id="oLblactPrinciName"/>
                </p:column>

                <p:column  style="text-overflow: ellipsis;width:70%!important;
                           overflow: hidden;">
                    #{dtl.key}
                </p:column>
            </p:dataTable>
            <p:spacer width="2px" rendered="#{jrnl.journalDetailList.size()>0}"/>
        </ui:repeat>
    </p:outputPanel>
    <br/>
    <p:outputPanel id="pnlLoadMore" >
  <p:commandButton id="btnLoadMore" value="Load More..." style="float: right;height: 30px" actionListener="#{activityJournalHdr.loadPreviewList(2,activityJournalHdr.journalHeaderList.size() , 10,0)}"  rendered="#{activityJournalHdr.journalHeaderList.size() lt activityJournalHdr.count and !companyService.unified}" update=":frmOpp:tabOpp:pnlCompanyJournalList :frmOpp:tabOpp:pnlLoadMore" onmousedown="saveScrollPosition();" oncomplete="setScrollPosition();" immediate="true"/>
  <p:commandButton id="btnLoadMorePerson" value="Load More..." style="float: right;height: 30px" actionListener="#{activityJournalHdr.loadPreviewList(4,activityJournalHdr.journalHeaderList.size() , 10,0)}"  rendered="#{activityJournalHdr.journalHeaderList.size() lt activityJournalHdr.count and companyService.unified}" update=":frmOpp:tabOpp:pnlCompanyJournalList :frmOpp:tabOpp:pnlLoadMore" onmousedown="saveScrollPosition();" oncomplete="setScrollPosition();" immediate="true"/>  
    </p:outputPanel>
    <p:dialog id="AddPrincipalDialog" header="Add #{custom.labels.get('IDS_PRINCI')}" widgetVar="dlgAddPrincipal">
        <p:outputPanel id="pnlJrnlList" style="overflow-x: hidden">
            <ui:repeat value="#{activityJournalHdr.journalHeaderList}" var="jrnl" id="tblJrnlList1" >
                <div style="padding-top: 5px;">
                    <h:panelGrid columns="2" style="width: 99%" >

                        <p:column>
                            <p:outputLabel value="#{jrnl.acjhDate}" id="oLblHdrAjdateEEE">
                                <f:convertDateTime pattern="EEE"/>
                            </p:outputLabel>
                            <p:outputLabel style="padding-left: 5px;" value="#{jrnl.acjhDate}" id="oLblHdrAjdatedd">
                                <f:convertDateTime pattern="dd"/>
                            </p:outputLabel>
                            <p:outputLabel style="padding-left: 5px;" value="#{jrnl.acjhDate}" id="oLblHdrAjdateMMM">
                                <f:convertDateTime pattern="MMM"/>
                            </p:outputLabel>
                            <p:outputLabel style="padding-left: 5px;" value="#{jrnl.acjhDate}" id="oLblHdrAjdateyyyy">
                                <f:convertDateTime pattern="yyyy"/>
                            </p:outputLabel>

                            <br/>

                            <p:outputLabel value=" Regarding : #{jrnl.acjhTitle}" id="oLblHdrAjRegrd"/>

                            <br/>
                            <p:outputLabel styleClass="jrnl-rgrd" value="By #{jrnl.getUserName(jrnl.acjhUser)}" id="oLblHdrAjBy" />

                            <p:outputLabel value=", Contacts : #{jrnl.getAttedee(jrnl.acjhId)}"  id="oLblHdrAjCont"
                                           rendered="#{not empty jrnl.getAttedee(jrnl.acjhId)}"/>

                        </p:column>

                    </h:panelGrid>

                    <p:dataTable rowIndexVar="rowIndex" style="width: 99%;padding-top: 1px" rowStyleClass="#{(rowIndex mod 2) eq 0? 'jrnl-tbl-even':'jrnl-tbl-odd'}" value="#{jrnl.actMap}" var="dtl" id="tbljrnlDetail1" class="hide-column-names jrnl-tbl">
                        <p:column style="width:20%">
                            <p:outputLabel value="#{dtl.value}" id="oLblDtlAjPrinci"/>
                        </p:column>
                        <p:column style="width:50%">
                            <p:outputLabel style="text-overflow: ellipsis" value="#{dtl.key}"  id="oLblDtlAjPrinciComment"/>
                        </p:column>
                        <!--                        <p:column style="width:15%">
                                                    <p:outputLabel value="#{dtl.acjdFollowup}"  id="oLblDtlAjFollowup">
                                                        <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                                                    </p:outputLabel>   
                                                </p:column>-->
                    </p:dataTable>
                </div>

            </ui:repeat>
            <p:outputPanel rendered="#{activityJournalHdr.journalHeaderList.size()==0}">
                No records found
            </p:outputPanel>
        </p:outputPanel>
    </p:dialog>

    <p:outputPanel id="pnlAcessDenied" rendered="#{activityJournalHdr.ajAccessflag==0}">
        <div style="padding-top: 150px;text-align: center">
            <h3>
                You are not authorized to access this page.
            </h3>

        </div>
    </p:outputPanel>


    <style>
        .jrnl-tbl-even{
            /*background: rgba(150, 160, 177, 0.44);*/
            /*background: #F2F5F9;*/
            /*background: #FFF;*/
            background: rgba(199, 205, 213, 0.05);
        }

        .jrnl-tbl-odd{
            /*background: #F2F5F9;*/
            /*background: #FFF;*/
            background: rgba(199, 205, 213, 0.05);
        }

        .jrnl-tbl tbody,
        .jrnl-tbl tbody tr,
        .jrnl-tbl tbody td{
            border: 1px solid #C4C4C4 !important;
            /*border: none;*/
        }
        .jrnl-tbl-noborder{
            /*background: rgba(150, 160, 177, 0.44);*/
            /*background: #F2F5F9;*/
            /*background: #FFF;*/
            border: none;
        }
        .jrnl-tbl-withborder{
            /*background: rgba(150, 160, 177, 0.44);*/
            /*background: #F2F5F9;*/
            /*background: #FFF;*/
            border: 2px solid #fff !important;
        }

        .editButton{
            height: 20px;
            width: 20px
        }

        .jrnl-rgrd{
            /*color: #7b7b7b;*/
            font-weight: bold;
            font-size: 14px;
        }

        .jrnl-time{
            /*            color: #1891c1;
                        font-size: 12px !important;
                        margin-left: 5px;
                        margin-left: 89px;
                        line-height: 32px;*/



            font-size: 14px !important;
            line-height: 20px;
            font-weight: bold;
        }

        .date{
            position: relative;
            width: 55px;
            font-family: Georgia,serif;
            color: #999;
            font-weight: bold;
        }

        .day, .month, .year{
            position: absolute;
        }

        .day{
            font-size: 40px !important;
            top: -6px;
            left: -6px;
        }

        .month{
            top: 0px;
            left: 44px;
            font-size: 16px !important;
        }

        .year{
            top: 15px;
            left: 42px;
            font-size: 15px !important;
        }

        .print-space{
            padding-left: 15px;
        }
        /*
                //Used for Load more*/
        .ui-datatable td,.ui-datatable tr {
            /*border-style: none !important*/
        }
        .aj-header thead{
            display: none;
        }
        .ui-datatable-empty-message{
            margin-left: 10px;
        }
        /*        .ui-datatable-wrapper {
                    height: 410px;
                    margin: 0 auto;
                }*/
        .truncate{
            width:900px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            /*                    margin-top: -20px;
                                padding-top: 10px;*/
        }

        .fa-link ,.fa-external-link {
            margin-left: -3px !important;
        } 


    </style>

    <script>
        var scrollPosition;
        function saveScrollPosition() {
            scrollPosition = $('#frmOpp\\tabOpp\\:pnlCompanyJournalList').scrollTop();
        }
        function setScrollPosition() {
            $('#frmOpp\\tabOpp\\:pnlCompanyJournalList').scrollTop(scrollPosition);
        }

    </script>


</ui:composition>
