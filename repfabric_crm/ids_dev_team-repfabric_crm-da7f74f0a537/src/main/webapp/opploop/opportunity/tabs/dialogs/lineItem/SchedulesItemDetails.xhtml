<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <p:dialog id="dlgSIDet"  widgetVar="dlgSIDet"   modal="true" width="500" 
              header="Line Item Schedule Details" responsive="true">

        <h:form id="frmDlgSIDet">
            <p:outputPanel id="pnlSIDet">
                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-4" id="gridSIDet">

                    <p:outputLabel value="Schedule" for="dlgSiDetSchTyp" />
                    <p:selectOneMenu id="dlgSiDetSchTyp"
                                     value="#{oppItemSchedulesService.oppItemSchedules.oppItemScheduleType}"
                                     style="border-radius: 5px; 
                                     background: white; height: 25px; width: 50%" label="Schedule" required="true" requiredMessage="Schedule is required">
                        <f:selectItem itemValue="Quarterly" itemLabel="Quarterly"/>
                        <f:selectItem itemValue="Yearly" itemLabel="Yearly" />
                        <f:ajax event="change" render="frmDlgSIDet:DlgSiDetq1qty 
                                frmDlgSIDet:DlgSiDetq2qty 
                                frmDlgSIDet:DlgSiDetq3qty 
                                frmDlgSIDet:DlgSiDetq4qty 
                                frmDlgSIDet:DlgSiDetyearqty" execute="@this" />
                    </p:selectOneMenu>

                    <p:outputLabel value="Year" for="dlgSiDetSchYr" />
                    <p:selectOneMenu id="dlgSiDetSchYr"
                                     value="#{oppItemSchedulesService.oppItemSchedules.oppItemScheduleYear}"
                                     style="border-radius: 5px; background: white; height: 25px; width: 50%" >
                        <f:selectItem itemLabel="Select year" />
                        <f:selectItems value="#{oppItemSchedulesService.yearsRange}" />
                        <!--16-03-2022 : #7514 :CRM-5404: Opportunity Line Items-->
                        <p:ajax event="change" rendered="#{(oppItemSchedulesService.oppItemSchedules.getRecId() == 0) or (oppItemSchedulesService.oppItemSchedules.getRecId() == null)}" 
                                listener="#{oppItemSchedulesService.getOppSchedules(opportunities.oppId,oppItemSchedulesService.oppItemSchedules.oppItemScheduleYear)}" update="@this :frmDlgSIDet" process="@this"/>
                    </p:selectOneMenu>

                    <p:outputLabel value="Q1 Qty"  for="DlgSiDetq1qty"/>
                    <p:inputNumber id="DlgSiDetq1qty" value="#{oppItemSchedulesService.oppItemSchedules.oppItemQtyQ1}"
                                   disabled="#{oppItemSchedulesService.oppItemSchedules.oppItemScheduleType eq 'Yearly'}"
                                   placeholder="0.000" maxlength="9" maxValue="999999999" decimalPlaces="3" autocomplete="nope" >
                        <p:ajax event="change" listener="#{oppItemSchedulesService.fillYear()}" update="frmDlgSIDet:DlgSiDetyearqty" process="@this" />
                    </p:inputNumber>


                    <p:outputLabel value="Q2 Qty" for="DlgSiDetq2qty"/> 
                    <p:inputNumber value="#{oppItemSchedulesService.oppItemSchedules.oppItemQtyQ2}" id="DlgSiDetq2qty" 
                                   disabled="#{oppItemSchedulesService.oppItemSchedules.oppItemScheduleType eq 'Yearly'}"
                                   placeholder="0.000" maxlength="9" maxValue="999999999" decimalPlaces="3" autocomplete="nope" >
                        <p:ajax event="change" listener="#{oppItemSchedulesService.fillYear()}" update="frmDlgSIDet:DlgSiDetyearqty" process="@this"/>
                    </p:inputNumber>

                    <p:outputLabel value="Q3 Qty" for="DlgSiDetq3qty"/>
                    <p:inputNumber value="#{oppItemSchedulesService.oppItemSchedules.oppItemQtyQ3}" id="DlgSiDetq3qty" 
                                   disabled="#{oppItemSchedulesService.oppItemSchedules.oppItemScheduleType eq 'Yearly'}"
                                   placeholder="0.000"  maxlength="9" maxValue="999999999" decimalPlaces="3" autocomplete="nope" >
                        <p:ajax event="change" listener="#{oppItemSchedulesService.fillYear()}" update="frmDlgSIDet:DlgSiDetyearqty" process="@this"/>
                    </p:inputNumber>

                    <p:outputLabel value="Q4 Qty" for="DlgSiDetq4qty"/>
                    <p:inputNumber value="#{oppItemSchedulesService.oppItemSchedules.oppItemQtyQ4}" id="DlgSiDetq4qty"  
                                   disabled="#{oppItemSchedulesService.oppItemSchedules.oppItemScheduleType eq 'Yearly'}"
                                   placeholder="0.000"  maxlength="9" maxValue="999999999" decimalPlaces="3" autocomplete="nope" >
                        <p:ajax event="change" listener="#{oppItemSchedulesService.fillYear()}" update="frmDlgSIDet:DlgSiDetyearqty" process="@this"/>
                    </p:inputNumber>

                    <p:outputLabel value="Year Qty" for="DlgSiDetyearqty"/>
                    <p:inputNumber value="#{oppItemSchedulesService.oppItemSchedules.oppItemQtyYear}" id="DlgSiDetyearqty" 
                                   disabled="#{oppItemSchedulesService.oppItemSchedules.oppItemScheduleType eq 'Quarterly'}"
                                   placeholder="0.000"  maxlength="9" maxValue="999999999" decimalPlaces="3" autocomplete="nope" >
                        <p:ajax event="change" listener="#{oppItemSchedulesService.fillQty()}" update="frmDlgSIDet:DlgSiDetq1qty 
                                frmDlgSIDet:DlgSiDetq2qty frmDlgSIDet:DlgSiDetq3qty frmDlgSIDet:DlgSiDetq4qty" process="@this" />
                    </p:inputNumber>
                </p:panelGrid> 
                <br/>

                <!--#5176: Opportunity Line Item - Status - Change update timestamp-->
                <!--//31-03-2022 : #7514 : CRM-5404: Opportunity Line Items: Default EAU based on Opp Schedules-->
                <!--//20-4-2022 : #7781 :  CRM-5601 schedules: added pns automatically need to inherit the header schedule-->
                <div class="button_bar" style=" text-align: center">    
                    <p:commandButton value="Save" 
                                     class="btn btn-success btn-xs" id="btnSaveSIDet"
                                     action="#{oppItemSchedulesService.saveOrUpdate()}"
                                     update=":frmOpp:tabOpp:tabLine:pnlSIList" oncomplete="PF('itemSch').clearFilters();" />
                    <p:spacer width="7"/>
                    <p:commandButton class="btn btn-warning btn-xs"                        
                                     id="dlgSIDetbtnCancel"
                                     value="Cancel"
                                     update="frmOpp:tabOpp:tabLine:pnlSIList"
                                     oncomplete="PF('dlgSIDet').hide();" />
                </div>
            </p:outputPanel>

        </h:form>
    </p:dialog>
</ui:composition>

