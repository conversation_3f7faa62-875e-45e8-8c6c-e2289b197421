<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <h:panelGroup id="pnlATTACH" class="pnls">
        <!--<h4  class="sub_title">#{custom.labels.get('IDS_OPP')} Attachments</h4>-->
        <!--596509  As per this ticket, removed file limit on Opp Attachements to allow mulitple file upload at a time-->
        <!--Bug 1796 -#699049: rm Clark:Questions for john : Size limit for file upload is set to 30MB-->
    <!--#3755:  CRM-2256: Tennant specs - allow file attachments to be added to closed status opp #{opportunities.oppCloseStatus!=0}"-->
        <!--         PMS 3898: CRM-4046: PO/company/Opp/Job: ID drag and drop area and eliminate the "upload" button -->
        <p:fileUpload 
            fileUploadListener="#{attachments.uploadAttchments}" 
            mode="advanced" dragDropSupport="true"  
            auto="true"  
            sizeLimit="31457280" label="Choose File" update="frmOpp:tabOpp:dtAtc @this" invalidSizeMessage="Exceeds maximum size limit of 30MB" />

<!--        <p:commandButton disabled="true" actionListener="#{attachments.testDel()}"
                         icon="ui-icon-closethick"
                         class="button_top btn_tabs"
                         value="Remove All files"/>-->

        <p:dataTable  value="#{attachments.listOppAttachments}" rowKey="#{opt.oppAttaId}" selectionMode="single" filteredValue="#{attachments.filteredOppAttachments}"
                      style="width: 80%"
                      var="opt" id="dtAtc" widgetVar="oppAttachTable">
            <p:column width="6%" >

                <!--#3786:Task Related to CRM-2256: Tennant specs - allow file attachments to be added to closed status opp-->
                <p:commandButton 
                    actionListener="#{attachments.delATt(opt)}"
                    oncomplete="PF('confirmations').show()"  

                    icon="fa fa-trash" 
                    title="Remove Attachment" 
                    class="btn-xs btn-danger">
                    <!--<p:confirm header="Confirmation" message="Are you sure to delete?"  icon="ui-icon-alert" />--> 
                </p:commandButton>

            </p:column>
            <p:column headerText="File Name" style="text-align: left" filterBy="#{opt.oppAttaName}" filterMatchMode="contains" sortBy="#{opt.oppAttaName}">

                #{opt.oppAttaName}

                <div style="float: right;">
                    <p:outputLabel value="#{opt.oppAttaFile/1024}"   
                                   style="font-weight: lighter;font-size: smaller;" >
                        <f:convertNumber  maxFractionDigits="1" type="number"/>
                    </p:outputLabel>
                    <p:outputLabel value=" kb" 
                                   style="font-weight: lighter;font-size: smaller;"/>
                </div>
            </p:column>
            <!--            Feature #5244 Opportunity Attachments > Uploaded on-->
            <p:column headerText="Uploaded On" style="text-align: left" filterBy="#{globalParams.formatDateTime(opt.insDate,'dt')}" filterMatchMode="contains" sortBy="#{globalParams.formatDateTime(opt.insDate,'dt')}"  >
                <p:outputLabel id="oTxtOppAtchDateTime" value="#{globalParams.formatDateTime(opt.insDate,'dt')}"  >
                    <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>

                </p:outputLabel>
            </p:column>
            <p:column>
                <p:commandButton   immediate="true" process="@none" title="Download Attachment"  class="btn-primary btn-xs"  icon="fa fa-download" 
                                   onclick="primeFaces.monitorDownload(start, stop)">
                    <p:fileDownload    value="#{opt.file}"/>
                </p:commandButton>
            </p:column>

            <!--27-07-2023 11740 YS-409: Opportunities > Attachments tab -->
            <p:column headerText="Use in Stage Actions" style="text-align: center">
                <p:selectBooleanCheckbox value="#{opt.oppAttaActionFlagBool}" title="Attachments will be automatically sent in stage action.">
                    <p:ajax listener="#{attachments.oppAttachActionFlagChanged(opt.recId, opt.oppAttaActionFlagBool)}"/>
                </p:selectBooleanCheckbox> 
            </p:column>


        </p:dataTable>
        <p:confirmDialog header="Confirm delete"  width="400" global="false"
                         message="Are you sure to delete attachment?" 
                         widgetVar="confirmations" >
            <div class="div-center">


                <p:commandButton value="Delete" actionListener="#{attachments.delete()}" update="frmOpp:tabOpp:dtAtc"
                                 class="btn btn-danger btn-xs"   oncomplete="PF('confirmations').hide()"     />
                <p:spacer width="4"/>
                <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"    onclick="PF('confirmations').hide()" 
                                 type="button"  />
            </div>
        </p:confirmDialog>
        <!--        <p:confirmDialog global="true" showEffect="fade" hideEffect="fade">
                    <p:commandButton value="Yes" type="button" class="btn btn-xs btn-success" />
                    <p:commandButton value="No" type="button" class="btn btn-xs btn-danger" />
                </p:confirmDialog>-->


        <style>
            div.ui-fileupload .ui-fileupload-buttonbar span, div.ui-fileupload .ui-fileupload-buttonbar button {
                padding-bottom:27px !important;
            }

            /*   PMS 3898: CRM-4046: PO/company/Opp/Job: ID drag and drop area and eliminate the "upload" button           */
            .ui-fileupload-content:before{
                content: "Please Drop Files Here";
                margin-left: 50vh;
                text-justify: auto;
                font-size: 20px;
                color:#b4c3d0;
                align-content: center;

            } 
        </style>
    </h:panelGroup>

</ui:composition>

