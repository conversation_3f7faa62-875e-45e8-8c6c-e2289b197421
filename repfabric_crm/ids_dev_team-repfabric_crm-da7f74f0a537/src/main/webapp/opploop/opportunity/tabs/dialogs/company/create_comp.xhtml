<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets">   

    <p:dialog widgetVar="dlgCompCreate" header="Quick create company">
        <p:panel id="pnlQkComp" class="pnldlgs" >
            <p:panelGrid >   
                <p:row>
                    <p:column style="width: 120px">
                        <p:outputLabel value="Company name" />
                    </p:column>
                    <p:column>
                        <p:inputText id="tqcom" value="#{opportunities.quickCompName}" 
                                     placeholder="Enter Company Name" 
                                     size="25" maxlength="60"/> 
                    </p:column>
                </p:row>
                <p:row>
                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_COMP_TYPE')}" />
                    </p:column>
                    <p:column>
                        <!-- #995438 : Opploop new comp from opp not saving sales team - Disable type -->
                        <p:selectOneMenu filter="true"  value="#{oppService.compType}" >
                            <f:selectItem itemLabel="Select #{custom.labels.get('IDS_COMP_TYPE')}" itemValue="0"/>
<!--                            <f:selectItems  value="#{companyTypesMst.getAllCompanyTypes()}" />-->
                            <f:selectItems  value="#{companyTypesMst.getCompanyTypesForOpp()}" var="compTyps"
                                            itemLabel="#{compTyps.compTypeName}" itemValue="#{compTyps.compTypeId}"/>
                            <p:ajax update=":frmOpp:tabOpp:qcsg :frmOpp:tabOpp:qcm2" listener="#{oppService.onSelectCustType()}"/>
                        </p:selectOneMenu>
                    </p:column> 
                </p:row>
                <p:row>
                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')}" />
                    </p:column>
                    <p:column>
                        <!--Bug 177 : No option to create new company as principal __ disabled="#{!oppService.enableSalesman}" added-->
                        <p:selectOneMenu filter="true" id="qcsg" value="#{oppService.newSmanId}" disabled="#{!oppService.enableSalesman}" >
                            <f:selectItem itemLabel="Select #{custom.labels.get('IDS_SALES_TEAM')}" itemValue="0"/>
                            <f:selectItems value="#{salesGroup.getSalesmenUserId(loginBean.userId)}" var="sm" 
                                           itemLabel="#{sm.smanName}" itemValue="#{sm.smanId}" />
                            <p:ajax update=":frmOpp:tabOpp:qcm2" />
                        </p:selectOneMenu>

                    </p:column> 
                </p:row>
            </p:panelGrid>
            <p:commandButton id="qcm2" value="Save"                                   
                             icon="ui-icon-disk" 
                             class="button_top btn_tabs" 
                             style="height: 27px"  
                             process="@this :frmOpp:tabOpp:pnlQkComp"
                             disabled="#{oppService.compType eq 0 or (oppService.compType eq 2 and oppService.newSmanId eq 0)}"
                             update="#{oppService.compType eq 1? ':frmOpp:tabOpp:inpTxtPrinciName :frmOpp:tabOpp:pnlclrprin :frmOpp:tabOpp:oppPrincipalContactName':
                                       oppService.compType eq 2?':frmOpp:tabOpp:oppCustomerName :frmOpp:tabOpp:pnlclrcust :frmOpp:tabOpp:oppSman :frmOpp:tabOpp:oppCustomerContactName':
                                       ':frmOpp:tabOpp:inpTxtDistriName :frmOpp:tabOpp:pnlclrdist :frmOpp:tabOpp:oppDistriContactName :frmOpp:tabOpp:oppCustomerName :frmOpp:tabOpp:pnlclrcust :frmOpp:tabOpp:oppSman :frmOpp:tabOpp:oppCustomerContactName'}  " 
                             actionListener="#{oppService.quickCreateCompany}" />
        </p:panel>
    </p:dialog>

</ui:composition>

