<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"> 
    <!--#5462: Aurinko CRMSync: Opportunity Lookup-->
    <h:form id="frmAuinkoOppLookup" >
        <!--#6477: Aurinko CRMSync > UI Updates to Lookups - Opp/Account/Pricebook entry-->
        <p:dialog header="#{custom.labels.get('IDS_OPP')} lookup" modal="true" width="1200" id="iddlgsfAurinkoOpp" widgetVar="dlgsfAurinkoOpp" >
            <!--#6313: Aurinko CRMSync: Opportunity Lookup > Next Page-->
            <!--#6748: Aurinko CRMSync: Opportunity Lookup: Updates to Pre-filter[removed facet and addedd autorun false in below remote command]-->
            <p:remoteCommand autoRun="false" id="rcAurOppSetPage" name="rcAurOppSetPage" oncomplete="PF('wvDtSFAurinkoOpp').paginator.setPage(PF('wvDtSFAurinkoOpp').paginator.cfg.pageCount-1);" />

            <!--#6323: Aurinko CRMSync:  Opp Lookup > Filters-->
            <h:panelGroup id="pgAurOppFlt">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                        <p:outputLabel id="lblAurFltOppName" value="Name" />
                    </div>

                    <!--#6748: Aurinko CRMSync: Opportunity Lookup: Updates to Pre-filter[changed ui-lg-3]-->
                    <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                        <p:inputText value="#{oAuthBasedSyncService.aurOppNameFlt}"
                                     style="width: 100% ;"
                                     id="itAurFltOppName"  />  
                    </div>
                    <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                        <p:outputLabel id="lblAurFltOppComp" value="Company" />
                    </div>

                    <!--#6748: Aurinko CRMSync: Opportunity Lookup: Updates to Pre-filter[changed ui-lg-3]-->
                    <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                        <p:inputText value="#{oAuthBasedSyncService.aurOppCompFlt}"
                                     style="width: 100% ;"
                                     id="itAurFltOppComp" />  
                    </div>
                    <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                        <!--#6313: Aurinko CRMSync: Opportunity Lookup > Next Page-->
                        <!--#6748: Aurinko CRMSync: Opportunity Lookup: Updates to Pre-filter[icon to value and update attribute changes]-->
                        <p:commandButton id="btnAurOppFlt" value="Filter"
                                         class="btn btn-primary btn-xs"
                                         onclick="PF('dlgFetchingRecords').show();"
                                         actionListener="#{oAuthBasedSyncService.ppltAurFltOpp(opportunities.oppId, oAuthBasedSyncService.linkedPrinciId, opportunities.updDate, loginBean.loggedInUser.userId,null)}"
                                         update=":frmAuinkoOppLookup:dtSFAurinkoOpp :frmAuinkoOppLookup:pgAurOppFlt"
                                         oncomplete="PF('dlgFetchingRecords').hide();PF('wvDtSFAurinkoOpp').clearFilters()" />
                    </div>
                    <!--#6748: Aurinko CRMSync: Opportunity Lookup: Updates to Pre-filter-->
                    <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                        <p:commandButton id="btnRstAurComplookup" value="Clear" 
                                         class="btn btn-primary btn-xs" rendered="#{oAuthBasedSyncService.resetAurCompTbl}"
                                         actionListener="#{oAuthBasedSyncService.clearAurOppFlt()}"
                                         update=":frmAuinkoOppLookup:pgAurOppFlt"
                                         oncomplete="PF('wvDtSFAurinkoOpp').clearFilters()" />
                    </div>
                    <!--#6748: Aurinko CRMSync: Opportunity Lookup: Updates to Pre-filter-->
                    <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                        <p:commandButton value="Load More" onclick="PF('dlgFetchingRecords').show()"
                                         class="btn btn-success btn-xs" rendered="#{not oAuthBasedSyncService.moreAurinkoOpps}"
                                         action="#{oAuthBasedSyncService.populateAurinkoOpp(opportunities.oppId,oAuthBasedSyncService.linkedPrinciId,opportunities.updDate, opportunities.syncUser.crmsyncUserId,oAuthBasedSyncService.aurinkoOpps.size())}"
                                         oncomplete="PF('dlgFetchingRecords').hide();PF('wvDtSFAurinkoOpp').filter();rcAurOppSetPage();" 
                                         update=":frmAuinkoOppLookup:pgAurOppFlt" />
                    </div>
                    <div class="ui-sm-12 ui-md-1 ui-lg-1 "/>
                </div>
            </h:panelGroup>
            <!--#6313: Aurinko CRMSync: Opportunity Lookup > Next Page-->
            <p:dataTable id="dtSFAurinkoOpp" widgetVar="wvDtSFAurinkoOpp"
                         scrollHeight="320"
                         scrollable="true"
                         value="#{oAuthBasedSyncService.aurinkoOpps}"                         
                         filterEvent="keyup"
                         filteredValue="#{oAuthBasedSyncService.aurinkoOppsFilter}"
                         var="opp"
                         emptyMessage="No #{custom.labels.get('IDS_OPP')} found"
                         multiViewState="true"
                         selection="#{oAuthBasedSyncService.selectedAurinkoOpp}"
                         selectionMode="single"
                         rowKey="#{opp.oppCrmId}"
                         paginator="true"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="100" >
                <!--#5944: Aurinko CRMSync > Manual Linking-->
                <!--20-04-2022 7776 Aurinko CRMSync: Update Last Modified timestamp of Opportunity-->
                <p:ajax event="rowSelect" onstart="PF('dlgsfAurinkoOpp').hide();PF('dlgProc').show();"
                        listener="#{oAuthBasedSyncService.onAUrinkoOppSelect}" 
                        oncomplete="PF('dlgProc').hide();PF('dlgMasterAurinkoOpp').show();"
                        update=":frmOpp:tabOpp :frmAurinkoChsMstr :frmOpp:tabOpp:opCrmsyncAPI :frmSummary" />
                <p:column width="25" headerText="Name" filterBy="#{opp.oppCustProgram}" filterMatchMode="contains" sortBy="#{opp.oppCustProgram}" >
                    #{not empty opp.oppCustProgram ? opp.oppCustProgram:""}
                </p:column>
                <p:column width="25" headerText="Company" filterBy="#{opp.oppCustomerName}" filterMatchMode="contains" sortBy="#{opp.oppCustomerName}"  >
                    #{not empty opp.oppCustomerName?opp.oppCustomerName:""}
                </p:column>
                <!--#6746: Aurinko CRMSync: CRM-5069: crymsync:opp linking - surface searchable target system City column-->
                <p:column width="25" headerText="City" filterBy="#{opp.btnName}" filterMatchMode="contains" sortBy="#{opp.btnName}"  >
                    #{not empty opp.btnName?opp.btnName:""}
                </p:column>
                <p:column width="25" headerText="Close Date" filterBy="#{opp.archRender}" filterMatchMode="contains" sortBy="#{opp.archRender}"  >
                    #{not empty opp.archRender?opp.archRender:""}
                </p:column>
                <p:column width="25" headerText="Stage" filterBy="#{opp.oppActivity}" filterMatchMode="contains" sortBy="#{opp.oppActivity}"  >
                    #{not empty opp.oppActivity?opp.oppActivity:""}
                </p:column>
                <p:column width="25" headerText="Amount" filterBy="#{opp.oppValue}" filterMatchMode="contains" sortBy="#{opp.oppValue}"  >
                    #{not empty opp.oppValue?opp.oppValue:""}
                </p:column>
                <!--#6589: Aurinko CRMSync: CRM-5069: crymsync:opp linking - surface searchable target system id column-->
                <p:column width="25" headerText="ID" filterBy="#{opp.oppCrmId}" filterMatchMode="contains" sortBy="#{opp.oppCrmId}"  >
                    #{opp.oppCrmId}
                </p:column>
            </p:dataTable>
        </p:dialog>
    </h:form>
    <!--#5462: Aurinko CRMSync: Opportunity Lookup-->
    <p:dialog widgetVar="dlgFetchingRecords" closable="false" modal="true" header="Message" resizable="false" width="250" responsive="true">

        <h:form>
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif"   />
                <p:spacer width="5" />
                <p:outputLabel value=" Please wait fetching records..." />
                <br /><br />
            </p:outputPanel>
        </h:form>
    </p:dialog>

    <p:dialog widgetVar="dlgProc" closable="false" modal="true" header="Message" resizable="false" >
        <h:form>
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif"   />
                <p:spacer width="5" />
                <p:outputLabel value="Processing please wait..." />
                <br /><br />
            </p:outputPanel>
        </h:form>
    </p:dialog>
</ui:composition> 
