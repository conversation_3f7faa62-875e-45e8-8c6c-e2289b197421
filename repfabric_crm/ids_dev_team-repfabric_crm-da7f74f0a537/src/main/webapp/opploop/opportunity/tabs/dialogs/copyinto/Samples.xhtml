<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">

    <p:dialog modal="true"   header="#{custom.labels.get('IDS_SAMPLE')}" id="dlgSample1" widgetVar="dlgSample" responsive="true">
        <h:form id="oppLineSampl">
            <p:remoteCommand name="applyComp" actionListener="#{samplesHdr.applyCompany1(viewCompLookupService.selectedCompany)}" update=":oppLineSampl:oppCemSample" />
            <p:remoteCommand name="applyContCEM"  autoRun="false" immediate="true" actionListener="#{samplesHdr.applyContCEM1(viewContLookupService.selectedContact)}"/> 

            <p:panelGrid id="gridSample" styleClass="inqDlg">

                <p:row>
                    <p:column colspan="2">
                        <!--#6096: CRM-4827: JOHN KIM: Repfabric / Closed Won/Lost Notes - retain them when reopening an opp-->
                        <!--#6265: Sugar-CRM -  for API->  line items - sync showing-->
                        <p:commandButton  class="btn btn-xs btn-success" 
                                          value="#{samplesHdr.recId > 0 ? 'Update' : 'Save'}" action="#{samplesHdr.save(samplesHdr.recId)}" 
                                          update=":frmOpp:tabOpp:dtLine" oncomplete="PF('wvDtLine').filter();"/>
                        <p:spacer width="4"/>
                        <p:commandButton type="button" value="Cancel"
                                         class="btn btn-xs btn-warning"                             
                                         onclick="PF('dlgSample').hide()"/>
                    </p:column>
                </p:row>
<!--                         //Feature #6127 CRM-4717: KIMINPROGRESS: make sample fields available in "custom labels"-->
                <p:row>
                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_SAMP_NUM')}:"/>
                    </p:column>
                    <p:column>
                        <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                        <p:inputText value="#{samplesHdr.sampOrdNum}" maxlength="30" onkeypress="return event.keyCode != 13;" id="sord" />
                    </p:column>

                    <p:column>
                        <!--//Task #15135: PRIORITY CRM-8820   Samples- Make Order Date a Required Field-->
                        <p:outputLabel value="#{custom.labels.get('IDS_REQ_DATE')}:" styleClass="required"/>
                    </p:column>
                    <p:column>
                        <p:calendar value="#{samplesHdr.sampOrdDate}"   pattern="#{globalParams.dateFormat}" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                        </p:calendar>
                    </p:column>
                </p:row>

                <p:row>
                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_SHIP_REF_NUM')}"/>
                    </p:column>
                    <p:column>
                        <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                        <p:inputText value="#{samplesHdr.sampShipRefNum}" onkeypress="return event.keyCode != 13;"  maxlength="30" />
                    </p:column>

                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_SHIP_ACCT_NUM')}:"/>
                    </p:column>
                    <p:column>
                        <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                        <p:inputText value="#{samplesHdr.sampShipAccNum}" onkeypress="return event.keyCode != 13;" maxlength="30" />
                    </p:column>
                </p:row>

                <p:row>
                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_SHIP_VIA')} :"/>
                    </p:column>
                    <p:column>
                        <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                        <p:inputText value="#{samplesHdr.sampShipMethod}"  maxlength="30" onkeypress="return event.keyCode != 13;" />
                    </p:column>

                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_SHIPPING')} to:"/>
                    </p:column>
                     <!--#8994 ESCALATIONS CRM-6125   Samples - limited character entry-->
                    <p:column>
                        <p:inputTextarea value="#{samplesHdr.sampShipTo}"  style="width:187px;max-height:82px;height: 82px" maxlength="120" />
                    </p:column>
                </p:row>

                <p:row>
                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_SHIP_STATUS')}:"/>
                    </p:column>
                    <p:column>
                        <p:selectOneMenu value="#{samplesHdr.sampShipStatus}" style="width:97%">
                            <f:selectItem itemLabel="Open" itemValue="Open" />
                            <f:selectItem itemLabel="Close" itemValue="Close" />           
                        </p:selectOneMenu>
                    </p:column>

                    <p:column rendered="#{samplesHdr.fromOpp}">
                        <p:outputLabel value="Date Required:"/>
                    </p:column>
                    <p:column rendered="#{samplesHdr.fromOpp}">
                        <p:calendar value="#{samplesHdr.sampReqDate}"   pattern="#{globalParams.dateFormat}" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                        </p:calendar>
                    </p:column>
                </p:row>

                <p:row rendered="#{samplesHdr.fromOpp}" id="cem">
                    <p:column>
                        <p:outputLabel value="CEM:"/>
                    </p:column>
                    <p:column>
<!--                                        <p:inputText value="#{enquiry.samp.sampCemName}"
                                     id="oppCemSample" styleClass="updateCustDlg" />
                        <p:commandButton  class="btnlukup" process="@this" immediate="true"
                                          icon="ui-icon-search" 
                                          action="#{viewCompanyLookUp.populateCompanyLists(5,'SAMPLE')}" 
                                          update="@(.cemupdate)"
                                          oncomplete="PF('dlgCem').show()"/>-->
                        <h:panelGroup class="ui-inputgroup"  > 
                            <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                            <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                            <p:inputText id="oppCemSample" readonly="true" onkeypress="return event.keyCode != 13;"   value="#{samplesHdr.sampCemName}"  />
                            <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                              disabled="#{samplesHdr.editable}"
                                              actionListener="#{viewCompLookupService.listAll('applyComp')}"
                                              update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                              styleClass="btn-info btn-xs" />
                        </h:panelGroup>

                    </p:column>












                    <p:column>
                        <p:outputLabel value="CEM Contact:"/>
                    </p:column>
                    <p:column>
    <!--                    <p:inputText value="#{enquiry.samp.sampCemContName}"
                                     id="oppCemContactSample" styleClass="updateCustDlg"/>
                        <p:commandButton class="btnlukup" update="@(.cemContupdate)"
                                         process="@this"
                                         icon="ui-icon-search"  
                                         actionListener="#{viewContactList.showContactLookup(5, samplesHdr.sampCemName, 'SAMPLE')}" 
                                         oncomplete="PF('dlgContCem').show()"/>-->
                        <h:panelGroup class="ui-inputgroup"  >
                            <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                            <p:inputText   readonly="true" onkeypress="return event.keyCode != 13;"  id="oppCemContactSample"  value="#{samplesHdr.sampCemContName}"/>
                            <p:commandButton  icon="fa fa-search" title="Choose Contact" immediate="true" disabled="#{samplesHdr.editable}" 
                                              actionListener="#{viewContLookupService.list('applyContCEM',0,samplesHdr.sampCem,0)}" 
                                              update=":formContLookup" oncomplete="PF('lookupCont').show()"  
                                              styleClass="btn-info btn-xs" />
                        </h:panelGroup>
                    </p:column>
                </p:row>
<!--                         //Feature #6127 CRM-4717: KIMINPROGRESS: make sample fields available in "custom labels"-->
                <p:row rendered="#{samplesHdr.fromOpp}">
                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_SAMP_REF_NUM')}:"/>
                    </p:column>
                    <p:column>
                        <p:inputText value="#{samplesHdr.sampRefNumber}" onkeypress="return event.keyCode != 13;"  maxlength="30" />
                    </p:column>


                    <p:column>
                        <p:outputLabel value="Follow Up:"/>
                    </p:column>
                    <p:column>
                        <p:calendar value="#{samplesHdr.sampFollowUp}"   pattern="#{globalParams.dateFormat}" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                        </p:calendar>
                    </p:column>
                </p:row>


                <p:row rendered="#{samplesHdr.fromOpp}">
                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_STATUS')}:"/>
                    </p:column>
                    <p:column>
                        <p:selectOneRadio value="#{samplesHdr.sampOpenStatus}">
                            <f:selectItem itemValue="0" itemLabel="Open" />
                            <f:selectItem itemValue="1" itemLabel="Close" />
                        </p:selectOneRadio>
                    </p:column>

                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_SAMP_STATUS')}:"/>
                    </p:column>
                    <p:column>
                        <p:selectOneMenu value="#{samplesHdr.sampDelivStatus}" style="width:97%">
                            <f:selectItem itemLabel="Requested" itemValue="1" />
                            <f:selectItem itemLabel="Shipped" itemValue="2" />   
                            <f:selectItem itemLabel="Received" itemValue="3" />
                            <f:selectItem itemLabel="Delivered" itemValue="4" />  
                            <f:selectItem itemLabel="In Review" itemValue="5" /> 
                        </p:selectOneMenu>
                    </p:column>

                </p:row>
  <!--#8994 ESCALATIONS CRM-6125   Samples - limited character entry-->
                <p:row rendered="#{samplesHdr.fromOpp}" >
                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_SAMP_APP')}:"/>
                    </p:column>
                    <p:column  colspan="3">
                        <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                        <p:inputText value="#{samplesHdr.sampApplication}" onkeypress="return event.keyCode != 13;" style="width:94%"  maxlength="60" />
                    </p:column>
                </p:row>

                <p:row rendered="#{samplesHdr.fromOpp}" >
                    <p:column>
                        <p:outputLabel value="Notes:"/>
                    </p:column>
                    <p:column colspan="3"> 
                        <p:inputTextarea rows="2"  value="#{samplesHdr.sampComments}" style="width: 94%;text-overflow: scroll" autoResize="false" />
                    </p:column>
                </p:row>

                <p:row>
                    <p:column colspan="3" >
                    </p:column>
                    <p:column style="float: right">
                        <p:commandButton id="btnLoadMore" value="#{!samplesHdr.fromOpp ? 'More Options...' : 'Less Options'}" style="float: right;height: 30px" actionListener="#{samplesHdr.oppCall()}" update=":oppLineSampl" class="btn btn-xs btn-primary"/>
                    </p:column>
                </p:row>

            </p:panelGrid>

            <!--        <p:panel id="pnlLSA" class="pnldlgs">
                        <p:panelGrid columns="2">
                            <p:outputLabel value="Order No."/>
                            <p:inputText value="#{samples.sampOrdNum}" maxlength="30" />   
                            <p:outputLabel value="Order Date"/>
                            <p:calendar value="#{samples.sampOrdDate}"   pattern="#{globalParams.dateFormat}"
                                        converterMessage="Date not valid">
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                            </p:calendar>
            
                            <p:outputLabel value="#{custom.labels.get('IDS_SHIPPER')} Ref.No"/>
                            <p:inputText value="#{samples.sampShipRefNum}" maxlength="30" />
                            <p:outputLabel value="#{custom.labels.get('IDS_SHIPPER')} A/c No."/>
                            <p:inputText value="#{samples.sampShipAccNum}" maxlength="30" />             
                            <p:outputLabel value="#{custom.labels.get('IDS_SHIPPING')} method"/>
                            <p:inputText value="#{samples.sampShipMethod}"  maxlength="30" />   
                            <p:outputLabel value="Status"/>
                            <p:inputText value="#{samples.sampShipStatus}" maxlength="30" /> 
                            <p:outputLabel value="#{custom.labels.get('IDS_SHIPPING')} Date"/>
                            <p:calendar value="#{samples.sampShipDate}"   pattern="#{globalParams.dateFormat}"
                                        converterMessage="Date not valid">
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                            </p:calendar>
                        </p:panelGrid>
            
                        <p:commandButton  process=":frmOpp:tabOpp:pnlLSA" 
                                          class="button_top btn_tabs" 
                                          disabled="#{opportunities.oppCloseStatus!=0}" 
                                          value="Save" actionListener="#{samples.save()}" 
                                          oncomplete="if (args &amp;&amp; !args.validationFailed) PF('dlgSample').hide()" 
                                          icon="ui-icon-disk" 
                                          update=":frmOpp:growl :frmOpp:tabOpp:dtLine"     />
                        <p:commandButton type="button" value="Cancel"
                                         class="button_top btn_tabs"
                                         icon="ui-icon-cancel" 
                                         onclick="PF('dlgSample').hide()"/>
                    </p:panel>-->

        </h:form>

    </p:dialog>


</ui:composition>

