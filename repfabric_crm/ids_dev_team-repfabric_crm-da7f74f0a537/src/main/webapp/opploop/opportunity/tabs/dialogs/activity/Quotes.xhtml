<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui">
    <p:dialog   header="#{custom.labels.get('IDS_QUOTES')}" widgetVar="dlgQuote1" modal="true" >
        <p:panel id="pnlQT" class="pnldlgs">
            <p:panelGrid columns="2">
                <p:outputLabel value="Recipient"/>
                <p:selectOneMenu value="#{quotes.quotReceipient}">
                    <f:selectItem itemLabel="#{custom.labels.get('IDS_CUSTOMER')}" itemValue="#{custom.labels.get('IDS_CUSTOMER')}"/>
                    <f:selectItem itemLabel="#{custom.labels.get('IDS_DISTRI')}" itemValue="#{custom.labels.get('IDS_DISTRI')}"/>
                    <f:selectItem itemLabel="#{custom.labels.get('IDS_CEM')}" itemValue="#{custom.labels.get('IDS_CEM')}"/>                
                </p:selectOneMenu>
                <p:outputLabel value="#{custom.labels.get('IDS_RFQ_NUM')}"/>
                <p:inputText value="#{quotes.quotRfqNum}"  maxlength="30" />
                <p:outputLabel value="#{custom.labels.get('IDS_PRINCI')} Quote No."/>
                <p:inputText value="#{quotes.quotPrinciQotNum}" maxlength="30" />
                <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')} Ref.No."/>
                <p:inputText value="#{quotes.quotCustRefNum}" maxlength="30" />
                <p:outputLabel value="Expiration Date"/>
                <p:calendar value="#{quotes.quotExpiry}"   pattern="#{globalParams.dateFormat}"
                            converterMessage="Date not valid" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>
            </p:panelGrid>
            <p:commandButton process=":frmOpp:tabOpp:pnlQT"  value="Save" 
                             class="button_top btn_tabs"
                             actionListener="#{quotes.save()}"
                             oncomplete="if (args &amp;&amp; !args.validationFailed) PF('dlgQuote1').hide()" update="" />
            <p:commandButton type="button" value="Cancel"
                             class="button_top btn_tabs"
                             onclick="PF('dlgQuote1').hide()"/>


        </p:panel>
    </p:dialog>


</ui:composition>

