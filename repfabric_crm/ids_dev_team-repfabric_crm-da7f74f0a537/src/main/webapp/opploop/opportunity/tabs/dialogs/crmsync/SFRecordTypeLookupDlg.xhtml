<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <!--13-10-2023 12315 CRMSYNC-349 Legrand Salesforce Integration: Opp Details > CRMSync tab updates - Project, Record Type-->
    <h:form id="frmSFRecordTypeLookupDlg">

        <p:dialog id="dlgSFRecordTypeLookup" header="Record Type Lookup"  widgetVar="wvDlgSFRecordTypeLookup" responsive="true" 
                  onShow="PF('wvDlgSFRecordTypeLookup').initPosition();" width="1000" modal="true" closeOnEscape="true">

            <p:dataTable id="dtSFRecordTypeLookup" widgetVar="wvDtSFRecordTypeLookup" reflow="true"
                         value="#{oAuthBasedSyncService.aurinkoRecordTypeList}"  
                         filteredValue="#{oAuthBasedSyncService.aurinkoRecordTypeListFlt}" 
                         selection="#{oAuthBasedSyncService.selectedRecordType}"
                         var="recordType" scrollHeight="300" scrollable="true"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="50,100" selectionMode="single"
                         filterEvent="keyup" pageLinks="4" 
                         rowKey="#{recordType.id}"  paginator="true"  rows="50" paginatorPosition="top" >
                <p:ajax event="rowSelect" update=":frmAurinkoOppCrt" oncomplete="PF('wvDlgSFRecordTypeLookup').hide()" />
                                                        <!--#{oAuthBasedSyncService.rcName}(); -->

                <p:column id="id"  filterMatchMode="contains" filterBy="#{recordType.id}" headerText="Id" sortBy="#{recordType.id}"    >
                    #{recordType.id}
                </p:column>  
                <p:column id="name"  filterMatchMode="contains" filterBy="#{recordType.name}" headerText="Name" sortBy="#{recordType.name}"    >
                    #{recordType.name}
                </p:column>    

            </p:dataTable>

        </p:dialog>
    </h:form>

</ui:composition>

