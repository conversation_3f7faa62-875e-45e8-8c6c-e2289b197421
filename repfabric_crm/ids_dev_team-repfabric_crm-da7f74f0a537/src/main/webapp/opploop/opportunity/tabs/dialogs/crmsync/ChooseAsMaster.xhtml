<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--#5592: Aurinko CRMSync: Opportunity > CRMSync tab updates-->
    <p:dialog id="dlgIdMasterAurinkoOpp" widgetVar="dlgMasterAurinkoOpp" width="600" closable="false" 
              modal="true" header="Choose master data as" resizable="false" >
        <h:form id="frmAurinkoChsMstr" >
            <h:panelGroup>

                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                        <p:outputLabel value="Last Update Timestamp of:" />
                    </div>

                    <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                        <p:outputLabel value="Repfabric #{custom.labels.get('IDS_OPP')}"/>
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8 ">
                        <p:outputLabel value="#{oAuthBasedSyncService.oppUpdDateForAurinko}"/>
                    </div>

                    <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                        <p:outputLabel value="#{oAuthBasedSyncService.selectedAurinkoOpp.NEEDSREVIEW} #{custom.labels.get('IDS_OPP')}"/>
                    </div>
                    <div class="ui-sm-12 ui-md-8 ui-lg-8 ">
                        <p:outputLabel value=" #{oAuthBasedSyncService.selectedAurinkoOpp.oppCloseReason}"/>
                    </div>

                    <div class="ui-sm-12 ui-md-12 ui-lg-12 "/>
                    <div class="ui-sm-12 ui-md-12 ui-lg-12 "/>

                    <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                        <p:outputLabel value="Choose master data as:"/>
                    </div>

                    <div class="ui-sm-12 ui-md-12 ui-lg-12 ">
                        <p:selectOneRadio id="radioIntMasterSel" value="#{oAuthBasedSyncService.rfOppMasterValue}" 
                                          style="width:100%;" layout="lineDirection" >
                            <f:selectItem itemLabel="Repfabric #{custom.labels.get('IDS_OPP')}" itemValue="0"/>
                            <f:selectItem itemLabel="Target System #{custom.labels.get('IDS_OPP')}" itemValue="1"/>
                            <f:ajax execute="@this" />
                        </p:selectOneRadio>
                    </div>
                </div> 

                <br/>
                <div style="text-align: center">
                    <!--#5944: Aurinko CRMSync > Manual Linking-->
                    <!--20-04-2022 7776 Aurinko CRMSync: Update Last Modified timestamp of Opportunity-->
                    <p:commandButton value="Ok" class="btn-primary btn-xs"
                                     actionListener="#{oAuthBasedSyncService.resolvedAurinkoOpp()}"
                                     onclick="PF('prcDlg').show();" oncomplete="PF('dlgMasterAurinkoOpp').hide();PF('prcDlg').hide();" 
                                     update=":frmOpp:tabOpp :frmSummary"/>
                </div>
            </h:panelGroup>
        </h:form>
    </p:dialog> 
</ui:composition>
