<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <style>
        /*        IP-1514 View button in new tab*/
        .viewOpp {
            border:1px solid #a8a8a8;
            border-radius:1px;
            background: #c4c4c4 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
            padding: .4em 1em;
            text-decoration: none;
            font-size: 14px;
            padding-bottom:.5em;
            height:20px;
            border: 1px solid #a8a8a8;
            background: #c4c4c4 url(/RepfabricCRM/javax.faces.resource/images/ui-bg_highlight-hard_80_c4c4c4_1x100.png?ln=primefaces-aristo) 50% 50% repeat-x;
            background: #c4c4c4 linear-gradient(top, rgba(255,255,255,0.8), rgba(255,255,255,0));
            background: #c4c4c4 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
            background: #c4c4c4 -moz-linear-gradient(top, rgba(255,255,255,0.8), rgba(255,255,255,0));
            font-weight: bold;
            color: #4f4f4f;
            text-shadow: 0px 1px 0px rgba(255,255,255,0.7);


        }


    </style>
    <p:panel id="pnlCRMSYNC" class="pnls" rendered="#{opportunities.crmSyncSettings.crmsyncEnabled == 1}">

        <p:outputPanel rendered="#{opportunities.crmSyncSettings.crmsyncInterface == 1}" id="gui">
            <h4  class="sub_title">CRMSync GUI Settings</h4>
            <!-- 17/03/2017 - Updated button to handle - opening Salesforce screen from Javascript onclick event handler-->
            <!--#704202: sierra: gui crmsync javalang-->

            <h:panelGrid columns="3" cellpadding="5">
                <p:outputLabel  value="Target #{custom.labels.get('IDS_OPP')} URL" />
                <p:outputLabel class="m" value=":" />
                <h:panelGroup>
                    <!--#5319: CRM-4471: CRMSync: workflow updates-->
                    <p:spacer width="8"/>
                    <p:inputText  value="#{opportunities.oppCrmLink}" styleClass="tds1"   />
                    <p:spacer width="8"/>
                    <!-- 17/03/2017 - Updated button to handle - opening Salesforce screen from Javascript onclick event handler-->
                    <!--#704202: sierra: gui crmsync javalang-->
                    <p:commandButton 
                        value="View" 
                        type="button"
                        class="btn btn-primary btn-xs" disabled="#{opportunities.oppCrmLink == ''}"
                        onclick="openSyncWin('#{opportunities.oppCrmLink}', 'sync', event);"/> 
                    <!--#5319: CRM-4471: CRMSync: workflow updates-->
                    <p:spacer width="4"/>
                    <p:commandButton 
                        value="New" 
                        type="button" 
                        class="btn btn-primary btn-xs" onclick="openSyncWin('#{opportunities.crmSyncSettings.crmsysNewOppUrl}', 'sync', event);" />
                </h:panelGroup>

            </h:panelGrid>
        </p:outputPanel> 
        <!--#5319: CRM-4471: CRMSync: workflow updates-->
        <p:outputPanel id="opCrmsyncAPI" rendered="#{opportunities.crmSyncSettings.crmsyncInterface > 1}">
            <h4  class="sub_title">CRMSync API Settings</h4>

            <h:panelGrid columns="3" cellpadding="5">
                <p:outputLabel  value="External #{custom.labels.get('IDS_OPP')} ID" />
                <p:outputLabel class="m" value=":" />

                <h:panelGroup>
                    <p:spacer width="150" height="0"/>
                    <!--<p:inputText  value="#{opportunities.oppCrmId}" styleClass="tds1" />-->
                    <p:outputLabel value="[Target #{custom.labels.get('IDS_OPP')} ID]" rendered="#{opportunities.oppCrmId == ''}"  />
                    <!--06-04-2022 7676 CRM-5530  CRMSync: screen refresh problem on initial linkage in manual mode-->
                    <p:outputLabel id="olTsOppId" value="#{opportunities.oppCrmId}" rendered="#{opportunities.oppCrmId != ''}" />
                    <h:inputHidden id="ihCheckStatus" value="#{oAuthBasedSyncService.tsOppIdStatus}" />
                    <!--07-04-2022 7676 CRM-5530  CRMSync: screen refresh problem on initial linkage in manual mode-->
                    <h:inputHidden id="ihOppResolved" value="#{oAuthBasedSyncService.crmSys}" />
                    <p:spacer width="8"/>

                    <!--//                4267 CRM-4212: Salesforce CRMSync updates-->
                    <!--#5462: Aurinko CRMSync: Opportunity Lookup-->
                    <!--#5748: crm sync sales force and sugar crm->issues-->
                    <p:commandButton  actionListener="#{oAuthLogin.populateSFOpps(opportunities.syncUser)}" id="srchCrmOpp" 
                                      oncomplete="PF('prcDlg').hide();" icon="fa fa-search" class="btn-primary btn-xs"  
                                      update=":frmOpp:tabOpp:cmappingList" immediate="true" 
                                      rendered="#{oAuthBasedSyncService.princiInterface!=3}"
                                      disabled="#{not empty opportunities.oppCrmId}">
                        <p:ajax listener="#{oAuthLogin.populateDlg(opportunities.syncUser,opportunities.oppId)}"/>
                    </p:commandButton>  

                    <!--#5462: Aurinko CRMSync: Opportunity Lookup-->
                    <!--#5944: Aurinko CRMSync > Manual Linking-->
                    <!--#6251: Aurinko CRMSync: Configure Sync for Parent company-->
                    <!--#6313: Aurinko CRMSync: Opportunity Lookup > Next Page-->
                    <!--#6323: Aurinko CRMSync:  Opp Lookup > Filters-->
                    <!--#6748: Aurinko CRMSync: Opportunity Lookup: Updates to Pre-filter[action removed oncomplete changed update changed]-->
                    <!--17-03-2022 7479 Aurinko CRMSync: CRM-5438  crmsync: opportunity lookup should default topic name and acct name-->
                    <p:commandButton  onclick="PF('dlgFetchingRecords').show();"
                                      actionListener="#{oAuthBasedSyncService.clearAurOppFlt()}"
                                      action="#{oAuthBasedSyncService.preFilterAurinkoOpp(opportunities.oppId, oAuthBasedSyncService.linkedPrinciId, opportunities.updDate, loginBean.loggedInUser.userId,null, opportunities.oppCustProgram, opportunities.oppCustomerName)}"
                                      oncomplete="PF('wvDtSFAurinkoOpp').clearFilters();PF('dlgsfAurinkoOpp').show();PF('dlgFetchingRecords').hide()"
                                      icon="fa fa-search" class="btn-primary btn-xs"  
                                      update=":frmAuinkoOppLookup:dtSFAurinkoOpp :frmAuinkoOppLookup:pgAurOppFlt" immediate="true" 
                                      rendered="#{oAuthBasedSyncService.princiInterface==3}"
                                      disabled="#{not empty opportunities.oppCrmId}">

                    </p:commandButton>  
                    <p:spacer width="4"/>
                    <!--#5319: CRM-4471: CRMSync: workflow updates-->
                    <!--                    <p:commandButton 
                                            value="New" class="btn btn-primary btn-xs" rendered="#{(opportunities.oppCrmId == '') and (oAuthBasedSyncService.princiInterface!=3)}"
                                            action="#{oAuthLogin.verifyMandetoryField()}" update=":frmOpp:tabOpp:iddlgsfAcclokup" >
                                        </p:commandButton>-->

                    <!--#5881: Aurinko CRMSync:  Provision for Account Lookup-->
                    <!--#6008: Aurinko CRMSync: Admin Managed Mode-->
                    <!--#6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/New-->
                    <!--06-04-2022 7676 CRM-5530  CRMSync: screen refresh problem on initial linkage in manual mode-->
                    <!--20-04-2022 7776 Aurinko CRMSync: Update Last Modified timestamp of Opportunity-->
                    <!--29-04-2022 7402 CRM-5397/YS-195: crmsync rheem: downflow all companies for aliasing and creation-->
                    <p:remoteCommand autoRun="false" id="rcUpdOppSyncId" name="rcUpdOppSyncId" action="#{oAuthBasedSyncService.setOppSyncId()}" 
                                     update=":frmOpp:tabOpp:opCrmsyncAPI :frmSummary" />

                    <h:panelGroup id="dsbAurinkoOppCrt" styleClass="display: inline-block; position: relative;">
                        <!--#6507: Aurinko CRMSync: Account Lookup: Updates to Pre-filter-->
                        <!--#6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/New[removed disable and added action listener]-->
                        <!--#6734: Aurinko CRMSync: Opp Creation > Auto populate Account ID if avalable[changed action update and oncomplete]-->
                        <!--22-03-2022 7402 CRM-5397/YS-195: crmsync rheem: downflow all companies for aliasing and creation-->
                        <!--27-04-2022 7402 CRM-5397/YS-195: crmsync rheem: downflow all companies for aliasing and creation-->
                        <p:commandButton id="cbAurinkoOppCrt"
                                         value="New" class="btn btn-primary btn-xs" onclick="PF('dlgProc').show();"
                                         rendered="#{(opportunities.oppCrmId == '') and (oAuthBasedSyncService.princiInterface==3)}"
                                         actionListener="#{oAuthBasedSyncService.clearAccList()}"
                                         action="#{oAuthBasedSyncService.fromOppSettings(1,opportunities.oppId,oAuthBasedSyncService.linkedPrinciId)}"
                                         update=":frmAurinkoOppCrt" oncomplete="PF('dlgProc').hide();" >
                        </p:commandButton>

                    </h:panelGroup>
                    <!--#6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/New[]removed tooltip]-->
                    <!--                    #6008: Aurinko CRMSync: Admin Managed Mode
                                      //  <p:tooltip rendered="#{oAuthBasedSyncService.princiMode==3}" for="dsbAurinkoOppCrt" 
                                                   value="Restricted under Admin Managed Mode" />-->

                    <p:spacer width="4"/>

                    <!--#5319: CRM-4471: CRMSync: workflow updates-->
                    <!--#5462: Aurinko CRMSync: Opportunity Lookup-->
                    <p:commandButton actionListener="#{opportunities.syncOpp()}" id="syncBtn"
                                     immediate="true"
                                     value="Sync"
                                     style="height:26px !important;"
                                     title="Sync to CRM System"
                                     styleClass="viewOpp btn-primary btn-sm"
                                     rendered="#{(opportunities.oppCrmId != '') and (oAuthBasedSyncService.princiInterface!=3)}" />

                    <!--#5462: Aurinko CRMSync: Opportunity Lookup-->
                    <!--#5758: Aurinko CRMSync:  Changes to call to Sync API-->
                    <!--#6008: Aurinko CRMSync: Admin Managed Mode-->
                    <h:panelGroup styleClass="display: inline-block; position: relative;" id="dsbAurikoOppSync">
                        <!--#6251: Aurinko CRMSync: Configure Sync for Parent company-->
                        <!--#6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/New[aded princimode==3 and removed disable attribute]-->
                        <!--17-03-2022 7533 CRM-5447: Update Opp Last Modified Timestamp on click of Sync button in CRMSync tab-->
                        <p:commandButton actionListener="#{oAuthBasedSyncService.syncAurinkoOpps(oAuthBasedSyncService.linkedPrinciId,oAuthBasedSyncService.princiMode==3)}"
                                         id="btnAurikoOppSync"
                                         immediate="true"
                                         value="Sync"
                                         style="height:26px !important;"
                                         title="Sync to CRM System"
                                         styleClass="viewOpp btn-primary btn-sm" update=":frmSummary:oppSummary"
                                         rendered="#{(opportunities.oppCrmId != '') and (oAuthBasedSyncService.princiInterface==3)}"/>
                    </h:panelGroup>
                    <!--#6008: Aurinko CRMSync: Admin Managed Mode-->
                    <!--#6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/New-->         
         <!--<p:tooltip for="dsbAurikoOppSync" value="Restricted under Admin Managed Mode" rendered="#{oAuthBasedSyncService.princiMode==3}" />-->

                    <p:spacer width="4"/>
                    <!--23-08-2022 8270 CRMSYNC-18: JM:  crmnsync demo: updates for manual sync showing success although there i-->
                    <!--<p:remoteCommand id="rcSynStatus" name="rcSynStatus" action="#{oAuthBasedSyncService.checkSyncStatus(opportunities.oppId)}" />-->

                    <!--#5462: Aurinko CRMSync: Opportunity Lookup-->
                    <!--#6574: syncronized data successfully message showing multiple times-->
                    <!--20-04-2022 7776 Aurinko CRMSync: Update Last Modified timestamp of Opportunity-->
                    <!--17-05-2022 7984 Aurinko CRMSync:  Opp Details > Sync button (Pass Opp ID)-->
                    <!--24-08-2022 8270 CRMSYNC-18: JM:  crmnsync demo: updates for manual sync showing success although there i-->
                    <p:poll id="pollAurSyncStatus" widgetVar="pollAurinkoSyncView" interval="3" autoStart="false"
                            listener="#{oAuthBasedSyncService.checkSyncStatus(opportunities.oppId)}" 
                            stop="#{oAuthBasedSyncService.stopAurStatusPoll}" update="@this :frmOpp:tabOpp:pollAurSyncStatus :frmSummary" />


                    <p:spacer width="4"/>

                    <!--1743 Salesforce > Sync linked TS line items on Sync Opp-->
                    <p:commandButton actionListener="#{opportunities.viewOpp()}" value="View"
                                     style="height:26px !important;" onclick="target = '_blank'"
                                     styleClass="viewOpp btn-primary btn-sm" id="viewOppbtn" 
                                     rendered="#{opportunities.oppCrmId != ''}"/>

                    <!--#5592: Aurinko CRMSync: Opportunity > CRMSync tab updates-->
                    <p:spacer width="4"/>
                    <!--#6591: Aurinko CRMSync: Sync Conflicts[passing princiId and oppId]-->
                    <p:commandButton id="btnAurinkoOppRslvCnfl"  value="Resolve Conflict" 
                                     actionListener="#{oAuthBasedSyncService.ppltAurinkoOppCnflcts(oAuthBasedSyncService.linkedPrinciId, opportunities.oppId)}" 
                                     style="height:26px !important;" styleClass="viewOpp btn-primary btn-sm"
                                     onclick="PF('dlgppltAurinkoOppCnft').show()" oncomplete="PF('dlgppltAurinkoOppCnft').hide();PF('dlgAurinkoOppCnftDet').show()"
                                     update="pnlAurinkoOppCnftDet"
                                     rendered="#{oAuthBasedSyncService.isaurinkoOppConflict}"/>

                    <!--                    <p:spacer width="15"/>-->
                    <!--#5748: crm sync sales force and sugar crm->issues-->
                    <p:commandLink style="float:right;padding:4px;"  value="Clear" 
                                   immediate="true" rendered="#{opportunities.oppCrmId != ''}" 
                                   onclick="PF('dlgcfmClrOppLink').show()">
                    </p:commandLink>

                </h:panelGroup>

            </h:panelGrid>

            <!--//    #5592 : Aurinko CRMSync: Opportunity > CRMSync tab updates-->
            <br/>
            <p:outputLabel id="lblAurinkoOppErrorLabel" value="Please fix the following error:" style=" color: red"
                           rendered="#{not empty oAuthBasedSyncService.conflictErrors}"/>
            <p:spacer width="4" />
            <p:outputLabel id="lblAurinkoOppError" value="#{oAuthBasedSyncService.conflictErrors}" style=" color: red"
                           rendered="#{not empty oAuthBasedSyncService.conflictErrors}" />


            <!--#3012 CRM Sync: Research on SalesForce - Line Items-->  

            <!--              <h:panelGrid columns="3" cellpadding="5">
                            <p:outputLabel  value="External Opportunity Line Item ID" />
                            <p:outputLabel class="m" value=":" />
            
                            <h:panelGroup>
                                <p:spacer width="150" height="0"/>
                                <p:outputLabel value="[Target Opportunity Line Item ID]" rendered="#{opportunities.oppCrmId == ''}"  />
                                <p:outputLabel value="#{opportunities.oppCrmId}" rendered="#{opportunities.oppCrmId != ''}"/>
            
                                <p:spacer width="8"/>
                                <p:commandButton  actionListener="#{oAuthLogin.populateSFOppLineItems(opportunities.syncUser)}" oncomplete="PF('prcDlg').hide();" icon="fa fa-search" class="btn-primary btn-xs"  update=":frmOpp:tabOpp:cmappingList" immediate="true" >
                                    <p:ajax listener="#{oAuthLogin.populateDlgForOppLineItems(opportunities.syncUser,opportunities.oppId)}"/>
                                </p:commandButton>
                            </h:panelGroup>
            
                        </h:panelGrid>-->

        </p:outputPanel>

    </p:panel>


    <!--populating Aurinko Opp Conflicts-->
    <p:dialog widgetVar="dlgppltAurinkoOppCnft" closable="false" modal="true" showHeader="false" resizable="false" >
        <p:outputPanel >
            <br />
            <h:graphicImage  library="images" name="ajax-loader.gif"   />
            <p:spacer width="5" />
            <p:outputLabel value="Please wait fetching conflicts..." />
            <br /><br />
        </p:outputPanel>
    </p:dialog>

    <!--#5592: Aurinko CRMSync: Opportunity > CRMSync tab updates-->
    <p:dialog widgetVar="dlgAurinkoOppCnftDet" header="Conflicts" responsive="true">
        <p:outputPanel id="pnlAurinkoOppCnftDet">
            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-4" >

                <h:outputText value="Field" style=" font-weight: bold"/>
                <h:outputText value="#{oAuthBasedSyncService.conflictRepOpp.oppSman}" style=" font-weight: bold"/>
                <h:outputText value="#{oAuthBasedSyncService.conflictTSOpp.oppSman}" style=" font-weight: bold"/>

                <p:outputLabel value="Topic" />
                <p:outputLabel value="#{oAuthBasedSyncService.conflictRepOpp.oppCustProgram}"/>
                <p:outputLabel value="#{oAuthBasedSyncService.conflictTSOpp.oppCustProgram}"/>

                <p:outputLabel value="Customer" />
                <p:outputLabel value="#{oAuthBasedSyncService.conflictRepOpp.oppDistriContactName}"/>
                <p:outputLabel value="#{oAuthBasedSyncService.conflictTSOpp.oppDistriContactName}"/>

                <p:outputLabel value="Stage" />
                <p:outputLabel value="#{oAuthBasedSyncService.conflictRepOpp.oppActivity}"/>
                <p:outputLabel value="#{oAuthBasedSyncService.conflictTSOpp.oppActivity}"/>

                <p:outputLabel value="Value" />
                <p:outputLabel value="#{oAuthBasedSyncService.conflictRepOpp.oppEau}"/>
                <p:outputLabel value="#{oAuthBasedSyncService.conflictTSOpp.oppEau}"/>

                <p:outputLabel value="Description" 
                               rendered="#{(not empty oAuthBasedSyncService.conflictRepOpp.oppDescr) or (not empty oAuthBasedSyncService.conflictRepOpp.oppDescr)}"/>
                <p:outputLabel value="#{oAuthBasedSyncService.conflictRepOpp.oppDescr}"
                               rendered="#{(not empty oAuthBasedSyncService.conflictRepOpp.oppDescr) or (not empty oAuthBasedSyncService.conflictRepOpp.oppDescr)}"/>
                <p:outputLabel value="#{oAuthBasedSyncService.conflictTSOpp.oppDescr}"
                               rendered="#{(not empty oAuthBasedSyncService.conflictRepOpp.oppDescr) or (not empty oAuthBasedSyncService.conflictRepOpp.oppDescr)}"/>

                <p:outputLabel value="Last Modified Date" />
                <p:outputLabel value="#{oAuthBasedSyncService.conflictRepOpp.oppCloseReason}"/>
                <p:outputLabel value="#{oAuthBasedSyncService.conflictTSOpp.oppCloseReason}"/>

                <br/>
                <p:commandButton value="Keep #{oAuthBasedSyncService.conflictRepOpp.oppSman}" class="btn-primary btn-xs"
                                 actionListener="#{oAuthBasedSyncService.onMergeAurinkoOppCnft(oAuthBasedSyncService.conflictRepOpp)}"
                                 oncomplete="PF('dlgAurinkoOppCnftDet').hide();" update=":frmOpp"/>
                <p:commandButton value="Keep #{oAuthBasedSyncService.conflictTSOpp.oppSman}" class="btn-primary btn-xs"
                                 actionListener="#{oAuthBasedSyncService.onMergeAurinkoOppCnft(oAuthBasedSyncService.conflictTSOpp)}"
                                 oncomplete="PF('dlgAurinkoOppCnftDet').hide();" update=":frmOpp"/>

            </p:panelGrid>
        </p:outputPanel>
    </p:dialog>

    <!--#5748: crm sync sales force and sugar crm->issues-->
    <p:confirmDialog global="true" showEffect="fade" hideEffect="fade">
        <p:commandButton value="Yes"  styleClass="ui-confirmdialog-yes btn btn-success btn-xs" />
        <p:spacer width="4"/>
        <p:commandButton value="No" styleClass="ui-confirmdialog-no btn btn-warning btn-xs" />
    </p:confirmDialog>

    <p:dialog  widgetVar="dlgcfmClrOppLink" header="Confirmation" modal="true" showEffect="fade" hideEffect="fade">
        <div class="div-center">
            <p:outputLabel value="Are you sure to unlink?" />
            <br/>
            <br/>
            <!--#5821: Aurinko CRMSync: Opp Line Items > Conflict/Error[update atttribute adedd warn ids]-->
            <!--20-04-2022 7776 Aurinko CRMSync: Update Last Modified timestamp of Opportunity-->
            <p:commandButton value="Yes" onclick="PF('dlgProc').show(); PF('dlgcfmClrOppLink').hide();"
                             actionListener="#{oppService.deleteOppLink()}" 
                             action="#{oppLineItems.refreshTable()}" 
                             update=":frmOpp:tabOpp:pnlCRMSYNC :frmOpp:tabOpp:dtLine :frmOpp:tabOpp:iconWarningSyncSnflct :frmOpp:tabOpp:warnLI :frmSummary" 
                             styleClass="ui-confirmdialog-yes btn btn-success btn-xs"
                             oncomplete="PF('dlgProc').hide();"/>
            <p:spacer width="4"/>
            <p:commandButton value="No" onclick="PF('dlgcfmClrOppLink').hide()"
                             styleClass="ui-confirmdialog-no btn btn-danger btn-xs"  />
        </div>
    </p:dialog>

    <script>
        //        #704202: sierra: gui crmsync javalang
        function openSyncWin(url, winName, event) {
            //            #884407 - Spectrum - CRM Sync not working
            if (url.indexOf("http://") === -1 &amp;&amp; url.indexOf("https://") === -1) {
                url = "http://" + url;
            }
            this.target = '_blank';
            var win = window.open(url, winName);
            win.focus();
            var browser = navigator.userAgent.toLowerCase();
            if (browser.indexOf('firefox') > -1) {
                win.close();
                var win1 = window.open(url, winName);
                win1.focus();
            }
            event.preventDefault();
        }
        /*IP-1514 View button in new tab*/
        $("#frmOpp\\:tabOpp\\:viewOppbtn").hover(function () {
            $(this).toggleClass("ui-state-hover")
        });

//06-04-2022 7676 CRM-5530  CRMSync: screen refresh problem on initial linkage in manual mode
        function checkTsOppId() {
            setTimeout(function () {
                var isUpdated = document.getElementById('frmOpp:tabOpp:ihCheckStatus');
                var oppStatus = document.getElementById('frmOpp:tabOpp:ihOppResolved');
                if (oppStatus.value === 'Resolved') {
                    if (isUpdated.value === 'true') {
                        isUpdated.value = false;
                    } else {
                        rcUpdOppSyncId();
                    }
                } else {
                    rcUpdOppSyncId();
                }
            }, 2000);
        }
    </script>
</ui:composition>



