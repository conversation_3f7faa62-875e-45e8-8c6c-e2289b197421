
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <!--//01-06-2022 : 8087 : Opp Schedules : tab  > UI crashes and modifying fails-->
    <p:dialog id="dll"  widgetVar="dll"   modal="true"  height="400" width="900" class="dialogCSS" 
              closable="false" onShow="PF('tabLineWVar').select(0)" >
        <!--// 21-09-2022 : #9096 : ESCALATIONS CRM-6156  Part number changes and quotes-->
        <f:facet name="header">
            <h:outputText id="hdrText" style="width:90%;" value="Add/Edit #{custom.labels.get('IDS_LINE_ITEM')}" />
            <p:commandButton id="closeBtn" icon="fa fa-close" style="margin-left: 79%;background-color:#C4D9F6;margin-top:-20px;"
                             process="@this" onclick="oppMouseValue(false);closeEvent();oppMouseValueOnCOmplete(true);" onmouseover="oppMouseValue(false)"
                             onmouseout="oppMouseOut(true)" oncomplete="oppMouseValueOnCOmplete(true)"/>
        </f:facet>
        <!--// 21-09-2022 : #9096 : ESCALATIONS CRM-6156  Part number changes and quotes-->
        <!--<p:ajax event="close" onstart="closeEvent()"/>-->
        <p:remoteCommand name="closeEvent" autoRun="false" actionListener="#{oppLineItems.onClose()}"/>
        <p:remoteCommand name="saveUpdateCall" autoRun="false" actionListener="#{oppLineItems.saveUpdateCall()}"/>
        <!--23-03-2022 7509 Aurinko CRMSync: Option to select TS PN lookup should be system wide by Principal-->
        <!--//02-02-2022: #7387 : CRM-5404: Opportunity > Schedules-->
        <!--//20-4-2022 : #7781 :  CRM-5601 schedules: added pns automatically need to inherit the header schedule-->
        <p:remoteCommand id="checkPNLookupFlag" name="checkPNLookupFlag" autoRun="true" 
                         action="#{oAuthBasedSyncService.checkPNLookupFlag(oAuthBasedSyncService.linkedPrinciId)}"
                         actionListener="#{oppItemSchedulesService.setTotalQty(null)}"
                         update=":frmOpp:tabOpp:tabLine:btnPart :frmOpp:tabOpp:tabLine:btnAurPart :frmOpp:tabOpp:tabLine:olSIListSumQty" />
        <p:outputPanel id="pnlLineItem">
            <h:panelGrid columns="2"  >
                <h:column>
                    <!--#7405: CRM-5404: Opp Line Items > Comm Rate - poornima - save and update btn disable onclick --> 
                    <!--//02-02-2022: #7387 : CRM-5404: Opportunity > Schedules-->
                    <!--//12-10-2022 : 9096 : ESCALATIONS CRM-6156  Part number changes and quotes-->
                    <p:commandButton     value="Save"  widgetVar="saveBtn"
                                         id="lbtsave"                                     
                                         actionListener="#{oppLineItems.save}"
                                         onclick="saveUpdateCall();PF('saveBtn').disable();"
                                         process=":frmOpp:tabOpp:pnlLineItem" 
                                         class="btn btn-success btn-xs" rendered="#{!oppLineItems.isEdit}" 
                                         oncomplete="PF('wvDtLine').filter();PF('saveBtn').enable();"
                                         onerror="PF('saveBtn').enable();"
                                         update=":frmOpp:tabOpp:dtLine 
                                         :frmOpp:tabOpp:opvalh
                                         :frmOpp:tabOpp:inpTxtValue
                                         :frmSummary" />
                    <!--//02-02-2022: #7387 : CRM-5404: Opportunity > Schedules-->
                    <!--//12-10-2022 : 9096 : ESCALATIONS CRM-6156  Part number changes and quotes-->
                    <p:commandButton    value="Update"  widgetVar="updateBtn"
                                        id="lbtupd" 
                                        onclick="saveUpdateCall();PF('updateBtn').disable();"
                                        process=":frmOpp:tabOpp:pnlLineItem" 
                                        class="btn btn-success btn-xs"                        
                                        action="#{oppLineItems.update()}"
                                        rendered="#{oppLineItems.isEdit}"
                                        oncomplete="PF('wvDtLine').filter();PF('updateBtn').enable();"
                                        update=":frmOpp:tabOpp:dtLine                                         
                                        :frmOpp:tabOpp:inpTxtValue
                                        :frmSummary"/>
                </h:column>

                <h:column>
                    <p:spacer width="4"/>
                    <!--// 21-09-2022 : #9096 : ESCALATIONS CRM-6156  Part number changes and quotes-->
                    <p:commandButton id="cancelBtn" widgetVar="btnCancel" process="@this" onclick="oppMouseValue(false);closeEvent();"
                                     class="btn btn-warning btn-xs" onmouseover="oppMouseValue(false)"
                                     value="Cancel"  onmouseout="oppMouseOut(true)" oncomplete="oppMouseValueOnCOmplete(true);"/>
                </h:column>
            </h:panelGrid> 
            <!--// 21-09-2022 : #9096 : ESCALATIONS CRM-6156  Part number changes and quotes-->
            <style>
                /*Dialog CSS fix*/
                #frmOpp\:tabOpp\:dll .ui-dialog-title {
                    width:100%;
                    height:100%;
                    margin-left:3px;
                }
            </style>
            <script>
                var isNotmouse = true;
                var isClickBol = false;
                function oppMouseValue(isOnMouse){
                    isNotmouse = isOnMouse;
                }
                function oppMouseOut(isOnMouse){
                    if(isClickBol == false){
                        isNotmouse = isOnMouse;
                    }
                }
                function oppMouseValueOnCOmplete(isOnMouse){
                    isNotmouse = isOnMouse;
                    isClickBol = false;
                }
                document.getElementById("frmOpp:tabOpp:cancelBtn").addEventListener("click", function () {
                    isClickBol = true;
                });
                document.getElementById("frmOpp:tabOpp:closeBtn").addEventListener("click", function () {
                    isClickBol = true;
                });
                function mouseValue123(){
                    return isNotmouse;
                }
            
            </script>

            <p:tabView  id="tabLine" widgetVar="tabLineWVar">
                <!--//20-4-2022 : #7781 :  CRM-5601 schedules: added pns automatically need to inherit the header schedule-->
                <p:ajax event="tabChange" listener="#{oppLineItems.onTabChange}"/>
                <p:tab title="#{custom.labels.get('IDS_LINE_ITEM')}">
                    <!--<p:remoteCommand name="applyProd" actionListener="#{example.applyProduct(viewProductLookUpService.selectedProduct)}" update="" />-->
                    <!--#7405: CRM-5404: Opp Line Items > Comm Rate - poornima - update grd for description in remote command -->
                    <p:remoteCommand name="applyProd" 
                                     actionListener="#{oppLineItems.applyProd(viewProductLookUpService.selectedProduct)}"
                                     update=":frmOpp:tabOpp:tabLine:grid :frmOpp:tabOpp:tabLine:grd" />
                    <!--//08-08-2022 : #8599 : CRM-5978: Opportunity Line Items: Aurora: keep or clear part number pricing & other fields-->
                    <p:remoteCommand name="changePartNumber" autoRun="false" 
                                     actionListener="#{oppLineItems.onPartNumChange()}"
                                     update=":frmOpp:tabOpp:tabLine:lres  :frmOpp:tabOpp:tabLine:ldes :frmOpp:tabOpp:tabLine:lpc  :frmOpp:tabOpp:tabLine:lcost  :frmOpp:tabOpp:tabLine:lCommRate" />

                    <!--#7405: CRM-5404: Opp Line Items > Comm Rate - poornima - altered panelGrid -->
                    <p:panelGrid columns="4"  id="grid"  columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" style="width: 100%">
                        <p:outputLabel value="#{custom.labels.get('IDS_MFG_PART_NUM')}"/>
                        <!--<p:outputLabel styleClass="m" value=" " />-->    
                        <h:panelGroup>
                            <!--                              //#5284 multi-level pricing not copying down from Opportunity-->
                            <!--#5126: Aurinko CRMSync: Checkbox - Create new line items using target system Part No.[added read only attribute]-->
                            <!--30-03-2022 7549 Aurinko CRMSync: Option to select TS PN lookup should be system wide by Principal-->
                            <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                            <p:inputText id="partno" value="#{oppLineItems.oppItemPartManf}" maxlength="60" styleClass="partNumBox" 
                                         readonly="#{(oAuthBasedSyncService.pnLookupFlag == 1)}" onkeypress="return event.keyCode != 13;">
                                <!--readonly="#{((oppLineItems.frmAurCrt) or (oAuthBasedSyncService.priceBookSeleted)) and (opportunities.oppReportFlag==1)}"-->
                                <!--#5752: CRM-4735: Repfabric / Changing Qty Per Unit Deletes the Description and Resale-->
                                <!--#7405: CRM-5404: Opp Line Items > Comm Rate - poornima - update cust Part,cost,commRate -->
                                <!--//08-08-2022 : #8599 : CRM-5978: Opportunity Line Items: Aurora: keep or clear part number pricing & other fields-->
<!--                                <p:ajax  event="blur"  
                                         listener="#{oppLineItems.onPartNumChange()}" update=":frmOpp:tabOpp:tabLine:lres  :frmOpp:tabOpp:tabLine:ldes :frmOpp:tabOpp:tabLine:lpc  :frmOpp:tabOpp:tabLine:lcost  :frmOpp:tabOpp:tabLine:lCommRate"/>-->
                                    <!--//01-07-2022 : #8374 : CRM-5687: Opportunity > Line Item Status-->     
                                    <!--//12-10-2022 : 9096 : ESCALATIONS CRM-6156  Part number changes and quotes-->
                                    <p:ajax event="change" onstart="if(mouseValue123() == false){return false};PF('dlgProcessing').show();" listener="#{oppLineItems.onPartNumChange()}" />
                            </p:inputText>
                            <!--                            <p:commandButton id="btnPart"  
                                                                         class="btnlukup"
                                                                         style="height: 27px;width:32px;"
                                                                         icon="ui-icon-search" 
                                                                         title="#{custom.labels.get('IDS_PART_NUM')} Look Up"
                                                                         actionListener="#{lookupSelection.loadMenuOption('OPP_LINE_ITEM')}"
                                                                         action="#{productsMst.loadProducts()}" 
                                                                         update=" " 
                                                                         oncomplete="PF('dlgPartNum').show();"
                                                                         />-->
                            <!--update=":dtPartNumForm"--> 
                            <!--#6126: Aurinko CRMSync: Checkbox - Create new line items using target system Part No.[condition report flag]-->
                            <!--#6587: CRM-5070: Error Message (twdev)[changed render attribute]-->
                            <!--#6996: CRM-5240: Errors while working on an opportunity[disabling and enabling button]-->
                            <!--23-03-2022 7509 Aurinko CRMSync: Option to select TS PN lookup should be system wide by Principal-->
                            <p:commandButton id="btnPart" widgetVar="choosPart" onclick="PF('choosPart').disable();" icon="fa fa-search" title="Choose Part Number" immediate="true" 
                                             actionListener="#{viewProductLookUpService.list('applyProd',opportunities.oppPrincipal)}" 
                                             update=":dtPartNumForm" oncomplete="PF('dlgPartNum').show();PF('choosPart').enable();"  styleClass="btn-info btn-xs"
                                             rendered="#{(oAuthBasedSyncService.pnLookupFlag != 1)}"/>

                            <!--#6126: Aurinko CRMSync: Checkbox - Create new line items using target system Part No.-->
                            <!--#6251: Aurinko CRMSync: Configure Sync for Parent company-->
                            <!--#6326: Aurinko CRMSync: Pricebook Entry Lookup-->
                            <!--#6522: Aurinko CRMSync: Price Book Entry Lookup: Updates to Pre-filter  removed actionListener and changed update more btn to pg-->
                            <!--#6126: Aurinko CRMSync: Checkbox - Create new line items using target system Part No. [render atribute changed due condition report flag]-->
                            <!--#6587: CRM-5070: Error Message (twdev)[changed render attribute]-->
                            <!--#6903: YS-173: Changes to Pricebook Lookup[added action]-->
                            <!--#6996: CRM-5240: Errors while working on an opportunity[disabling and enabling button]-->
                            <!--16-03-2022 7474 Aurinko CRMSync CRM-5445  crmsync: downflow company aliasing creation dialog not reinitializing on n[modified widgetVar in onclick and oncomplete]-->
                            <!--23-03-2022 7549 Aurinko CRMSync: Option to select TS PN lookup should be system wide by Principal-->
                            <!--02-02-2022: #7387 : CRM-5404: Opportunity > Schedules-->
                            <p:commandButton id="btnAurPart" icon="fa fa-search" title="Choose Part Number" widgetVar="choosPartAur" immediate="true" onclick="PF('choosPartAur').disable();PF('dlgFetchingRecords').show();"
                                             action="#{oAuthBasedSyncService.getPBookOptions(oAuthBasedSyncService.linkedPrinciId, null)}"
                                             actionListener="#{oAuthBasedSyncService.clearPriceBooksList(true)}"
                                             oncomplete="PF('wvDtSFAurinkoPriceBook').clearFilters();PF('dlgFetchingRecords').hide();PF('dlgAurinkoPriceBook').show();PF('choosPartAur').enable();" 
                                             update=":frmAuinkoPriceBookLookup:dtAurinkoPriceBook :frmAuinkoPriceBookLookup:pgAurPBook"  styleClass="btn-info btn-xs"
                                             rendered="#{(oAuthBasedSyncService.pnLookupFlag == 1)}"/>

                        </h:panelGroup>
                        <p:outputLabel value="#{custom.labels.get('IDS_CUST_PART')}"/>
                        <!--<p:outputLabel styleClass="m" value=" " />-->
                        <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                        <p:inputText value="#{oppLineItems.oppItemPartCust}" onkeypress="return event.keyCode != 13;" id="lpc" maxlength="60">
                            <!--//08-08-2022 : #8599 : CRM-5978: Opportunity Line Items: Aurora: keep or clear part number pricing & other fields-->
                            <p:ajax event="change" listener="#{oppLineItems.changeOppItem()}"/>
                        </p:inputText>


                        <!--#396281: Priatek LI relabels not found-->
                        <p:outputLabel value="#{custom.labels.get('IDS_LINE_ITEM_QTY')}"/>
                        <!--<p:outputLabel styleClass="m" value=" " />-->  
                        <!-- #440468:THORSON: Lock quantity to number only-->
                        <!--                          //#5284 multi-level pricing not copying down from Opportunity-->
                        <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                        <p:inputText value="#{oppLineItems.oppItemQnty}" id="lqty" maxlength="12"   
                                     onkeypress="if((event.which &lt; 48 &amp;&amp; event.which!=44 &amp;&amp; event.which!=46  &amp;&amp;event.which!=127  )|| event.which &gt; 57)return false;return event.keyCode != 13;"
                                     converterMessage="#{custom.labels.get('IDS_LINE_ITEM_QTY')} not valid"  >
                            <f:convertNumber groupingUsed="false"   maxFractionDigits="3" />
                            <!--#5752: CRM-4735: Repfabric / Changing Qty Per Unit Deletes the Description and Resale: Need not update description. so removed :frmOpp:tabOpp:tabLine:ldes-->
                            <!--Bug #6850:CRM-5170: URGENT_ line item -opportunity - if you put price in first, then quantity, it reverts back     07/12/21 by harshithad-->
                            <p:ajax  event="change" listener="#{oppLineItems.onQtyChange()}"/>
                        </p:inputText>

                        <p:outputLabel value="#{custom.labels.get('IDS_COST')}"/>
                        <!--<p:outputLabel styleClass="m" value=" " />-->               
<!--                        <p:inputText value="#{oppLineItems.oppItemCost}" id="lcost" maxlength="12" styleClass="partNumBox"
                                     converterMessage="Item cost not valid" >
                              Bug 1045 : #982069 :unit price decimals messing up report  pattern="#0.00000"
                              #1372 CRM-3047: EarleAZ: line item rounding errors
                              <f:convertNumber groupingUsed="false" maxIntegerDigits="15" minFractionDigits="3" maxFractionDigits="5"/>
                        </p:inputText>-->
                        <!--1372 CRM-3047: EarleAZ: line item rounding errors-->
                        <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                        <p:inputNumber value="#{oppLineItems.oppItemCost}" onkeypress="return event.keyCode != 13;"
                                       id="lcost" maxlength="13" styleClass="partNumBox" 
                                       decimalPlaces="5" maxValue="99999999.99999" converterMessage="Item cost not valid">
                            <!--//08-08-2022 : #8599 : CRM-5978: Opportunity Line Items: Aurora: keep or clear part number pricing & other fields-->
                            <p:ajax event="change" listener="#{oppLineItems.changeOppItem()}"/>
                        </p:inputNumber>



                        <p:outputLabel value="#{custom.labels.get('IDS_RESALE')}"/>

                        <!--<p:outputLabel styleClass="m" value=" " />-->   
                        <!--#{oppLineItems.oppItemResale}-->
<!--                        <p:inputText value="#{oppLineItems.oppItemResale}" id="lres" maxlength="12" styleClass="partNumBox"
                                     converterMessage="#{custom.labels.get('IDS_RESALE')} not valid" >
                              Bug 1045 : #982069 :unit price decimals messing up report pattern="#0.00000"
                            #1372 CRM-3047: EarleAZ: line item rounding errors
                              <f:convertNumber groupingUsed="false" maxIntegerDigits="15" minFractionDigits="3" maxFractionDigits="5"/>
                        <f:convertNumber  groupingUsed="true"  minFractionDigits="3" pattern="#0.000#"/>
                        </p:inputText>-->

    <!--                    <p:inputNumber decimalPlaces="5"  maxValue="99999999.99999"   value="#{oppLineItems.oppItemResale}"  id="lres" 
                 style="width: 97%" maxlength="13" converterMessage="#{custom.labels.get('IDS_RESALE')} not valid" styleClass="partNumBox">
    </p:inputNumber>-->
                        <!--1372 CRM-3047: EarleAZ: line item rounding errors-->
<!--9923 WEBINAR: Quote Refactoring : CRM-6404  quote decimal places for unit price not consistent with opps-->
                        <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                        <p:inputNumber value="#{oppLineItems.oppItemResale}" onkeypress="return event.keyCode != 13;"
                                       id="lres" maxlength="14" styleClass="partNumBox" 
                                       decimalPlaces="6" maxValue="99999999.999999" converterMessage="#{custom.labels.get('IDS_RESALE')} not valid" > 
                            <!--//08-08-2022 : #8599 : CRM-5978: Opportunity Line Items: Aurora: keep or clear part number pricing & other fields-->
                            <p:ajax event="change" listener="#{oppLineItems.changeOppItem()}"/>
                        </p:inputNumber>

                        <!--#7405: CRM-5404: Opp Line Items > Comm Rate - poornima -->
          <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                        <p:outputLabel value="#{custom.labels.get('IDS_COMMISSION')} Rate"/>
                        <!--//16-02-2023 : #10723 : CRM-6661 : OPPS Adding a line item pop up disappears when hitting the enter button-->
                        <p:inputNumber decimalPlaces="2" maxlength="9" maxValue="9999999.99"  id="lCommRate" value="#{oppLineItems.oppCommRate}"
                                       onkeypress="return event.keyCode != 13;">
                            <!--//08-08-2022 : #8599 : CRM-5978: Opportunity Line Items: Aurora: keep or clear part number pricing & other fields-->
                            <p:ajax event="change" listener="#{oppLineItems.changeOppItem()}"/>
                        </p:inputNumber>
                    </p:panelGrid>

                    <!--#7405: CRM-5404: Opp Line Items > Comm Rate - poornima - added panelGrid -->
                    <p:panelGrid columns="2"  id="grd"  columnClasses="ui-grid-col-4,ui-grid-col-8" style="width: 100%">
                        <p:outputLabel value="#{custom.labels.get('IDS_LINE_ITEM_DESC')}"/>
                        <!--<p:outputLabel styleClass="m" value=" " />-->     
                        <!--//    Bug 1266 -#193088 :EPIC/GOLDEN INSTANCE:part number UI-->
                        <!--#7032 : lend item/opp-part dersciption scroll and growl message blink when bottom line-->
                        <!--#7405: CRM-5404: Opp Line Items > Comm Rate - poornima - added style -->
                        <!--22-07-2022 8540 Aurinko CRMSync: Expected to transfer the product description to RF-->
                        <p:outputPanel>
<!--                            22-11-2022 9310 CRMSYNC-56  STMicro Opportunity : specific p/n  Opportunity-->
                            <p:inputTextarea id="ldes" rows="3" autoResize="false" value="#{oppLineItems.oppItemPartDesc}"  styleClass="partNumBox"
                                             style="margin-left: -136px;width: 112%" maxlength="350" validator="#{oppLineItems.valiidateItmDescription}"  >
                                <!--//08-08-2022 : #8599 : CRM-5978: Opportunity Line Items: Aurora: keep or clear part number pricing & other fields-->
                                <p:ajax event="change" listener="#{oppLineItems.changeOppItem()}"/>
                            </p:inputTextarea>
                        </p:outputPanel>
                        <!--Bug #4005:CRM-4129: Opportunities save line item button broken-->

 <!--<p:ajax event="keyup" listener="#{transDtl.checkvalidate(oppLineItems.oppItemPartDesc ,2)}" update="" />-->
                        <!--22-07-2022 8540 Aurinko CRMSync: Expected to transfer the product description to RF-->

                        <!-- #4730 new field status of line item -->
                        <p:outputLabel value="Status"/>
                        <!--#7405: CRM-5404: Opp Line Items > Comm Rate - poornima - added margin left -->
                        <p:selectOneMenu value="#{oppLineItems.oppLineItemStatus}" 
                                         id="lstatus" 
                                         style="border-radius: 5px; background: white; height: 25px; width: 50%;margin-left: -136px">
                            <p:ajax event="change" update="@this" />
                            <!--//01-07-2022 : #8374 : CRM-5687: Opportunity > Line Item Status-->
                            <f:selectItems value="#{oppLineItems.statusList}" var="status" itemLabel="#{status.itemStatusName}" 
                                           itemValue="#{status.itemStatusId}"/>
                        </p:selectOneMenu>

                        <p:selectBooleanCheckbox id="updQuotLnItmReq" itemLabel="Update linked Quote line item" style="width: max-content;"
                                                 value="#{oppLineItems.updateQuotLnItm}"  />
                    </p:panelGrid>            
                </p:tab>   
                <p:tab title="#{custom.labels.get('IDS_CUSTOM_FIELDS')}">
                    <ui:include src="../../../../../opploop/customfields/LineItemCustomFields.xhtml"/>
                </p:tab>
                <!-- #4760: Line Item Forecast Opp Line Items > Schedules tab-->
                <p:tab title="Schedules" id="schedules" disabled="#{oppLineItems.recId == 0}" >
                    <ui:include src="../../SchedulesItemList.xhtml"/>
                </p:tab>
            </p:tabView>
        </p:outputPanel>
    </p:dialog>
    
</ui:composition>

