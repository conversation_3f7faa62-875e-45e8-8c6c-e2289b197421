
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:p="http://primefaces.org/ui">
<!--//10-07-2023 : #11622 : CRM-7134 Opportunities: Need to be able to link an opportunity to existing quote-->
    <p:dialog id="dlgQuotLinkToOpp" widgetVar="dlgQuotLinkToOpp"  modal="true" width="900"  
              header="Add #{custom.labels.get('IDS_QUOTES')}" resizable="false"
              responsive="false" closeOnEscape="true" onShow="PF('dlgQuotLinkToOpp').initPosition();" >
        <h:form id="frmQuotLinkToOpp">
            <p:remoteCommand name="linkBtnStatus" autoRun="false" actionListener="#{opportunities.loadLinkOppBtn(opportunities.oppId)}"/>
            <p:dataTable  id="dtQuotLinkToOppList" widgetVar="quotLinkToOppListTable" 
                          value="#{opportunities.quotList}" var="quot" selectionMode="single" 
                          rowKey="#{quot.recId}" selection="#{opportunities.selQuot}" 
                          emptyMessage="No open #{custom.labels.get('IDS_QUOTES')} found." 
                          paginator="true" rows="10" filteredValue="#{opportunities.filteredQuotes}"
                          paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                          rowsPerPageTemplate="5,10,15"
                          paginatorAlwaysVisible="true" >

                <p:ajax event="rowSelect" listener="#{opportunities.linkOppToQuot(opportunities.oppId)}" oncomplete="PF('dlgQuotLinkToOpp').hide();linkBtnStatus();" />

                <p:column width="12%"  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{quot.custName}"  sortBy="#{quot.custName}" id="custH">

                    #{quot.custName}
                </p:column>

                <p:column width="13%"  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{quot.princiName}" sortBy="#{quot.princiName}" id="prinH">
                    #{quot.princiName}                               
                </p:column>

                <p:column width="13%" filterMatchMode="contains" headerText="#{custom.labels.get('IDS_SALES_TEAM')}" filterBy="#{quot.smanName}" sortBy="#{quot.smanName}"  
                           id="smanHId">
                    #{quot.smanName} 
                </p:column>

                <p:column width="13%" filterMatchMode="contains" headerText="#{custom.labels.get('IDS_DISTRI')}" filterBy="#{quot.distriName}"   sortBy="#{quot.distriName}"  
                           id="distriHId">
                    #{quot.distriName}  
                </p:column>

                <p:column width="10%" filterMatchMode="contains" headerText="#{custom.labels.get('IDS_PROGRAM')}" filterBy="#{quot.quotCustProgram}"  sortBy="#{quot.quotCustProgram}"  id="prog">
                    #{quot.quotCustProgram}                          
                </p:column>

                <p:column width="10%" filterMatchMode="contains" headerText="Quote #" filterBy="#{quot.quotNumber}"  sortBy="#{quot.quotNumber}" >
                    #{quot.quotNumber}                     
                </p:column> 

                <p:column width="9%" filterMatchMode="contains" headerText="Date" filterBy="#{quot.quotDate}"  sortBy="#{quot.quotDate}" >
                    #{quot.quotDate}                    
                </p:column>
                <p:column width="10%"  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_JOB_VALUE')}" filterBy="#{quot.quotValue}"  sortBy="#{quot.quotValue}" 
                            id="valHId">
                    #{quot.quotValue}
                </p:column>

                <p:column width="10%"  filterMatchMode="contains" headerText="Status" filterBy="#{quot.quotDelivStatusName}"  sortBy="#{quot.quotDelivStatusName}" id="statH">
                    #{quot.quotDelivStatusName}                                 
                </p:column>


            </p:dataTable>
            </h:form>
    </p:dialog>



</ui:composition>


