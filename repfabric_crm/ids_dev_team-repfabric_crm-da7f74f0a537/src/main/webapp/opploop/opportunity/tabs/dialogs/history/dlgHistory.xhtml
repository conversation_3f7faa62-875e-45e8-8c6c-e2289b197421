<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <p:dialog id="historyDlg" header="#{custom.labels.get('IDS_OPP')} History" widgetVar="dlgHistory" width="1200" height="500">
        <h:form id="historyForm" >
            <p:dataTable  value="#{oppHistory.lstOppHistory}" 
                          var="history" id="dtHistory" class="hide-column-names"
                          rowStyleClass="tbl-row" 
                          >
                <p:column style="height: auto">  
                    <div style="margin-bottom: -10px;">
                        <h:outputLabel class="header-label" value="#{history.getFormattedDate(history.updDate,'dt')} ">
                            <p:spacer width="4"/>
                            <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                        </h:outputLabel>
                        <h:outputLabel value=" #{history.oppHistDescr}" styleClass="header-label" style="color:black!important;"/>
                        <p:spacer width="4"/>
                        <h:outputLabel value=" by #{history.userName}" styleClass="header-label" />
                    </div>
                    <br/>

                    
                    <table>
                        <tr>
                            <h:outputLabel value="#{custom.labels.get('IDS_FOLLOW_UP')} : "  rendered="#{history.oppFollowUp ne null}" styleClass="opp-label"/>
                            <h:outputLabel value="#{history.oppFollowUp}" rendered="#{history.oppFollowUp ne null}" styleClass="opp-value">
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                            </h:outputLabel> 
                            <p:spacer width="4" height="0" rendered="#{history.oppFollowUp ne null}"/>
<!--                            // 18/05/2021 Feature  #4889 Repfabric / AVX CRMSync / Relabeling Fields / "Next Step"-->
                            <h:outputLabel value="#{custom.labels.get('IDS_NEXT_STEP')} : "  rendered="#{history.oppNextStep ne ''}" styleClass="opp-label"/>
                            <h:outputLabel value="#{history.oppNextStep}  " rendered="#{history.oppNextStep ne ''}" styleClass="opp-value"/>
                        </tr>
                        <tr>
                            <p:spacer width="4px" rendered="#{history.oppActivity ne ''}"/>
                            <h:outputLabel   value=" #{custom.labels.get('IDS_ACTIVITY')} : "  rendered="#{history.oppActivity ne ''}" styleClass="opp-label"/>  
                            <h:outputLabel   value="#{history.oppActivity} " rendered="#{history.oppActivity ne ''}" styleClass="opp-value"/>  
                        </tr>
                        <tr>
                            <p:spacer width="4px" rendered="#{history.oppStatus ne ''}"/>
                           <!--#13178 ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
                            <h:outputLabel   value="#{custom.labels.get('IDS_ACT_STATUS')} : "  rendered="#{history.oppStatus ne ''}" styleClass="opp-label"/>  
                            <h:outputLabel   value="#{history.oppStatus} " rendered="#{history.oppStatus ne ''}" styleClass="opp-value"/>  
                        </tr>
                        <tr>
                            <p:spacer width="4px" rendered="#{history.oppPotential ne ''}"/>
                            <h:outputLabel   value=" #{custom.labels.get('IDS_POTENTIAL')} : "  rendered="#{history.oppPotential ne ''}" styleClass="opp-label"/>  
                            <h:outputLabel   value="#{history.oppPotential}% " rendered="#{history.oppPotential ne ''}" styleClass="opp-value"/>  
                        </tr>
                        <tr>
                            <p:spacer width="4px" rendered="#{history.oppPriority ne ''}"/>
                            <h:outputLabel   value=" #{custom.labels.get('IDS_PRIORITY')} : "  rendered="#{history.oppPriority ne ''}" styleClass="opp-label"/>  
                            <h:outputLabel   value="#{history.oppPriority} " rendered="#{history.oppPriority ne ''}" styleClass="opp-value"/>  
                        </tr>
                        <tr>
                            <p:spacer width="4px" rendered="#{history.oppEau ne ''}"/>
                            <h:outputLabel   value=" #{custom.labels.get('IDS_EAU')} : "  rendered="#{history.oppEau ne ''}" styleClass="opp-label"/>  
                            <h:outputLabel   value="#{history.oppEau} " rendered="#{history.oppEau ne ''}" styleClass="opp-value"/>  
                        </tr>
                        <tr>
                            <p:spacer width="4px" rendered="#{history.oppValue ne ''}"/>
                            <h:outputLabel   value=" #{custom.labels.get('IDS_VALUE')} : "  rendered="#{history.oppValue ne ''}" styleClass="opp-label"/>  
                            <h:outputLabel   value="#{history.oppValue} " rendered="#{history.oppValue ne ''}" styleClass="opp-value"/>  
                        </tr>
                        <tr>
                            <p:spacer width="4px" rendered="#{history.oppProtoDate ne null}"/>
                            <h:outputLabel value="#{custom.labels.get('IDS_PROTO_DATE')} : "  rendered="#{history.oppProtoDate ne null}" styleClass="opp-label"/>
                            <h:outputLabel value="#{history.oppProtoDate}" rendered="#{history.oppProtoDate ne null}" styleClass="opp-value">                         
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                            </h:outputLabel> 
                            <p:spacer width="4px" height="0" rendered="#{history.oppProtoDate ne null}"/>
                            <h:outputLabel value="#{custom.labels.get('IDS_PRODUCTION_DATE')} : "   rendered="#{history.oppProdDate ne null}" styleClass="opp-label" />
                            <h:outputLabel value="#{history.oppProdDate}" rendered="#{history.oppProdDate ne null}" styleClass="opp-value">
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                            </h:outputLabel>
                        </tr> 

                        <tr>
                            <p:spacer width="4px" rendered="#{history.oppComment ne ''}"/>
                            <h:outputLabel value=" Comments :  "  rendered="#{history.oppComment ne ''}" styleClass="opp-label"/>
                            <h:outputLabel value="#{history.oppComment}  " rendered="#{history.oppComment ne ''}" styleClass="opp-value"/>
                        </tr>
                        <tr>
                            <p:spacer width="4px" rendered="#{history.oppRptComments ne ''}"/>
                            <!--#807698 08/11/2017-->
                            <h:outputLabel   value="#{custom.labels.get('IDS_RPT_COMMENTS')} : "  rendered="#{history.oppRptComments ne ''}" styleClass="opp-label"/>  
                            <h:outputLabel   value="#{history.oppRptComments}" rendered="#{history.oppRptComments ne ''}" styleClass="opp-value"/>  
                        </tr>

                    </table>
                </p:column>

            </p:dataTable>   
            <!--            </div>-->
        </h:form>
    </p:dialog>

    <style>
        .opp-value{
            color:black!important;
            line-height: 1.5em;
        }
        .opp-label{
            color:rgba(0, 0, 0, 0.51)!important;
        }
        .header-label{
            font: initial;
            font-size: small;
        }
        .history-label{
            color:black!important;
        }
        .tbl-row{
            background: rgba(199, 205, 213, 0.21);
        }
    </style>

</ui:composition>

