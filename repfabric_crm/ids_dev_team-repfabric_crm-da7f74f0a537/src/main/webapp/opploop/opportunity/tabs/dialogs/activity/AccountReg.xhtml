<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui">
    <p:dialog modal="true"   header="#{custom.labels.get('IDS_ACC_REG')}" widgetVar="dlgRegist1">
        <p:panel id="pnlAR" class="pnldlgs">
            <p:panelGrid columns="2">
                <p:outputLabel value="#{custom.labels.get('IDS_MFG_REG_NUM')}"/>
                <p:inputText value="#{accregistrations.regiManfRegNum}" maxlength="30" />

                <p:outputLabel value="#{custom.labels.get('IDS_APPROVED_DATE')}"/>
                <p:calendar value="#{accregistrations.regiApproveDate}"   pattern="#{globalParams.dateFormat}"   
                            converterMessage="Date not valid" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>

                <p:outputLabel value="Expiry Date"/>
                <p:calendar value="#{accregistrations.regiExpiryDate}"   pattern="#{globalParams.dateFormat}"   
                            converterMessage="Date not valid"  >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>             

                <p:outputLabel value="Approved By"/>
                <p:inputText value="#{accregistrations.regiApprovedBy}" maxlength="40" />

                <p:outputLabel value="Notes"/>
                <p:inputTextarea value="#{accregistrations.regiNotes}" cols="25" rows="3"/>

                <p:outputLabel value="Registration Level"/>
                <p:inputText value="#{accregistrations.regiLevel}"  maxlength="30"/>
            </p:panelGrid>
            <p:commandButton class="button_top btn_tabs" process=":frmOpp:tabOpp:pnlAR"
                             value="Save" 
                             actionListener="#{accregistrations.save()}"
                             oncomplete="if (args &amp;&amp; !args.validationFailed) PF('dlgRegist1').hide()" update=""                 />
            <p:commandButton type="button" value="Cancel" 
                             class="button_top btn_tabs"
                             onclick="PF('dlgRegist1').hide()"/>

        </p:panel>
    </p:dialog>

</ui:composition>

