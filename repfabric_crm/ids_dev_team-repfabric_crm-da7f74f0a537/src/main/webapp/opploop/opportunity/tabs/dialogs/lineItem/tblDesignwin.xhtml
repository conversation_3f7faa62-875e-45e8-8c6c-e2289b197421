<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <p:dialog id="dlgdw"  widgetVar="dlgdw"  header="#{custom.labels.get('IDS_DWINS')}" >
        <p:dataTable var="dwin"
                     paginator="true"
                     paginatorAlwaysVisible="false"
                     rows="15"
                     value="#{designWin.listQuotesCopied}">
            <p:column headerText="#{custom.labels.get('IDS_DWIN_NUM')}">
                #{dwin.dwinNum}
            </p:column>
            <p:column headerText="Date">
                <p:outputLabel value="#{dwin.dwinDate}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:outputLabel>            
            </p:column>
            <p:column headerText="Expiry">
                <p:outputLabel value="#{dwin.dwinExpiryDate}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:outputLabel>
            </p:column>
            <p:column headerText="Status">
                #{dwin.dwinStatus}
            </p:column>
            <p:column headerText="Type">
                #{dwin.dwinType}
            </p:column>
            <!--Ticket #541347-->
<!--            <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')} #{custom.labels.get('IDS_SPLIT')}">
            #{dwin.dwinCustSplit}
        </p:column>
        <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')} Acm #{custom.labels.get('IDS_SPLIT')}">
            #{dwin.dwinCustAcmSplit}
        </p:column>
        <p:column headerText="#{custom.labels.get('IDS_DISTRI')} #{custom.labels.get('IDS_SPLIT')}">
            #{dwin.regiDistriSplit}
        </p:column>
        <p:column headerText="#{custom.labels.get('IDS_DISTRI')} Acm #{custom.labels.get('IDS_SPLIT')}">
            #{dwin.regiDistriAcmSplit}
        </p:column>-->
        </p:dataTable>
    </p:dialog>

</ui:composition>

