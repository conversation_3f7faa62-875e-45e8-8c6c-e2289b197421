<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <p:dialog id="dlgsam1"  widgetVar="dlgsam"  header="#{custom.labels.get('IDS_SAMPLES')}"   responsive="true">

<!--onclick="window.open('../enquiry/List.xhtml?type=SAMPLES&amp;id=#{opportunities.oppId}', '_self');"-->
        <!--                         //Feature #6127 CRM-4717: KIMINPROGRESS: make sample fields available in "custom labels"-->
        <!--#2441: Related to CRM-1510: SELTEC Fwd: Issues with the new version-->
        <p:commandButton  class="btn btn-primary btn-xs" onclick="window.open('../samples/Sample.xhtml?recId=#{samplesHdr.smpHrdId}&amp;oppId=#{opportunities.oppId}', '_self');"
                          value="View Samples"  title="View Quote" />
        <p:dataTable var="samp" 
                     paginator="true"
                     paginatorAlwaysVisible="false"
                     rows="15"
                     value="#{samplesHdr.listSamplesCopied}" >
            <p:column headerText="#{custom.labels.get('IDS_MFG_PART_NUM')}" >
                #{samp.sampItemPartManf}
            </p:column>

            <p:column headerText="Qty"  >
                #{samp.sampItemQnty.intValue()}
            </p:column>

            <p:column headerText="EAU" >
                #{samp.sampEau}                                  
            </p:column>

            <p:column headerText="Prod Line">
                #{samp.sampProdLine}
            </p:column>
            <!--                         //Feature #6127 CRM-4717: KIMINPROGRESS: make sample fields available in "custom labels"-->
            <p:column headerText="#{custom.labels.get('IDS_SAMP_STATUS')}" >
                #{samp.sampStatus}
            </p:column>

            <p:column headerText="Order No">
                #{sam.sam}
            </p:column>
            <!--            22-01-2021:Task# #3131-CRM-3794: jobs edit  label -  Order Date-->
            <p:column headerText="#{custom.labels.get('IDS_JOB_ORDER_DATE')}">

                <p:outputLabel value="#{sam.sampOrdDate}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:outputLabel>

            </p:column>
            <!--                         //Feature #6127 CRM-4717: KIMINPROGRESS: make sample fields available in "custom labels"-->
            <p:column  headerText="#{custom.labels.get('IDS_SHIP_REF_NUM')}">
                #{sam.sampShipRefNum}
            </p:column>
            <p:column headerText="#{custom.labels.get('IDS_SHIP_ACCT_NUM')}">
                #{sam.sampShipAccNum}
            </p:column>
            <p:column headerText="#{custom.labels.get('IDS_SHIPPING')} Date">
                <p:outputLabel value="#{sam.sampShipDate}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:outputLabel>

            </p:column>
            <p:column headerText="#{custom.labels.get('IDS_SHIPPING')} Method">
                #{sam.sampShipMethod}
            </p:column>
            <p:column headerText="#{custom.labels.get('IDS_STATUS')}">
                #{sam.sampShipStatus}
            </p:column>
        </p:dataTable>
    </p:dialog>
     <!--                         //Feature #6127 CRM-4717: KIMINPROGRESS: make sample fields available in "custom labels"-->
    <style>
        
        table thead th {
            word-wrap:break-word; 
        }
    </style>


</ui:composition>

