<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui" >
   
    <p:dialog id="lookup" widgetVar="oppLookup" header="Target System Opp Look up" height="400" width="600" maximizable="true" resizable="false">
        
        <p:ajax event="close" update="cmappingList" listener="#{oAuthLogin.clearDialog()}"/>
        <!--      <h:outputLabel value="Fetching Opportunity Records..." />
              <h:graphicImage  library="images" name="ajax-loader.gif" id="loadingImg"  />-->

        <p:dataTable id="cmappingList" widgetVar="cmapDlg" paginator="true"
                     scrollHeight="320"
                     scrollable="true"
                     value="#{oAuthLogin.oppLookUp}"                         
                     filterEvent="keyup"
                     filteredValue=""
                     var="cmap"
                     selection="#{oppService.selectedTSopp}"
                     selectionMode="single"
                     paginatorAlwaysVisible="false"
                     rowKey="#{cmap.oppTitle}" 
                     class="tblmain"
                     >
            <p:ajax event="rowSelect" listener="#{oppService.createOppLink}" update=":frmOpp:tabOpp:pnlCRMSYNC" oncomplete="PF('oppLookup').hide();" />
            <p:column headerText="Opportunity Name" filterBy="#{cmap.oppCustProgram}" filterMatchMode="contains" sortBy="#{cmap.oppCustProgram}"  >
                #{cmap.oppCustProgram}
            </p:column>
            <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')} Name" filterBy="#{cmap.oppCustomerName}" filterMatchMode="contains" sortBy="#{cmap.oppCustomerName}"  >
                #{cmap.oppCustomerName}
            </p:column>

        </p:dataTable>
<!--    2844 CRM Sync: Delay loading opps and tab set to Basic Tab     -->
    </p:dialog>
                    <p:dialog widgetVar="prcDlg" closable="false" modal="true" header="Message" resizable="false" width="250" responsive="true">
                
                <p:outputPanel >
                    <br />
                    
                    <p:spacer width="5" />
                    <p:outputLabel value="Fetching records... Please wait..." />
                    <br /><br />
                </p:outputPanel>

               

            </p:dialog>

</ui:composition>

