<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: RelatedCompsData.xhtml
// Author: Sarayu
//*********************************************************
* Copyright (c) 2016 Indea Design Systems, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <style>
        #dialog .ui-dialog-content{
            overflow: hidden;
        }
        #reltdOppCompForm\3AreldCompDT .ui-datatable-scrollable-header{
            background-color: white;
        }
    </style>
    <p:dialog id="dialog" widgetVar="reltdOppCompDlg" header="Related Companies" width="700px" height="420px" modal="true" resizable="false"> 
        <h:form id = "reltdOppCompForm">
<!--update=":frmOpp:growl"/-->
            <p:commandButton value="Save"  class="btn btn-xs btn-success"   actionListener="#{oppRelated.saveRelatedComps()}" />
            <p:spacer width="4"/>
            <p:commandButton value="Cancel" class="btn btn-xs btn-warning"   onclick="PF('reltdOppCompDlg').hide();"/>
<!--update=":addReltdCompForm"--> 
<p:spacer width="4"/>
            <p:commandButton style="float: right;"  class="btn btn-xs btn-primary"  update=":addReltdCompForm"  value="New" action="#{oppRelated.reltdCompDefaults()}"  oncomplete="PF('reltdCompAddDlg').show()"> 
            </p:commandButton>
            <p:dataTable value="#{oppRelated.reldCompsList}" var="comp" 
                         id="reldCompDT" 
                         widgetVar="relatedCompTbl"
                         selection="#{oppRelated.oppCompsList}"
                         rowKey="#{comp.recId}"
                         emptyMessage="No companies found"
                         scrollable="true"
                         scrollHeight="300"
                         style="width: 650px;margin-top: 10px;"
                         >
                <p:column selectionMode="multiple" headerText="Included" style="width: 45px;text-align: center"/>
                <p:column headerText="Related Company" style="width: 100px;" filterBy="#{comp.relatedCompName}" filterMatchMode="contains">
                    <h:commandLink target="_blank" title="View Company Details" action="#{productsMst.goToCompanyDetailsPage(comp.reltCompId)}" value="#{comp.relatedCompName}" />
                </p:column>
                <p:column headerText="Category" style="width: 100px;" filterBy="#{comp.strReltCatg}"  filterMatchMode="contains">
                    <p:outputLabel value="#{comp.strReltCatg}"/>
                </p:column>
                <p:column headerText="Role" style="width: 100px;" filterBy="#{comp.strRole}"  filterMatchMode="contains">
                    <p:outputLabel value="#{comp.strRole}"/>
                </p:column>
            </p:dataTable>
        </h:form>
    </p:dialog>
</ui:composition>
