<ui:composition  
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

    <div class="ui-g ui-fluid header-bar">
        <div class="ui-sm-12 ui-md-12 ui-lg-12 " style="text-align: center;">

            <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Summary</p:outputLabel>

        </div>
    </div>
    <!--Bug #8268  Opportunity>Open existing Opportunity>Clone>Close and PO button shows for new opp, before save.-->
    <!--Ticket #347650 - separated out forms--> 
    <h:form id="frmOppStatus" rendered="#{opportunities.recId>0 and opportunities.cloneFlag==0}">

        <table   style="width: 100%;table-layout: fixed" >
            <tr>
                <td  style="text-align: right">
                    Reporting:
                </td>
                <td style="width: 10px"> : </td>
                <td>
                    <p:selectBooleanButton 
                        value="#{opportunities.oppReportFlag1}"  id="btnbool"    
                        style="font-weight: normal"
                        class="#{opportunities.oppReportFlag1==true ? 'rpt_btn0' : 'rpt_btn1' } button_top"                                   
                        onLabel="Included" offLabel="Excluded"
                        onIcon="ui-icon-circle-check"                                       
                        offIcon="ui-icon-circle-close" >
                        <!--update=":frmOpp:growl :frmOppStatus"-->
                        <p:ajax process="@this" listener="#{oppService.changeReporting()}"  update=":frmOppStatus"/>
                    </p:selectBooleanButton>
                </td>
            </tr>
            <tr>
                <td style="text-align: right">
                    Open/Closed                       
                </td>
                <td>:</td>
                <td>
                    <!--1328 Pending Issues from 1300: Opps > Close : Won/Lost Issues-->
                    <!--1557 CRM-3161: Opp - Open/closed Field  - Unable to Edit?-->
                    <p:commandButton rendered="#{opportunities.recId>0}" 
                                     id="btnclos"  style="width: 98px; height: 25px;margin-top: 4px"
                                     title="Closing Status" 
                                     class="btn btn-xs"
                                     value="#{opportunities.closeName}" 
                                     action="#{opportunities.updateCloseDate()}"
                                     update=":frmUpdateStatus"
                                     oncomplete="PF('cnfClose').show();" />
                </td>
            </tr>
            <!--            PMS 2631: PO Downflow: Opportunity > #2 Generate PO-->
            <tr>
                <td style="text-align: right;">
                    PO                       
                </td>
                <td>:</td>
                <td>

                    <!--PMS 2759:CRM-3643: generate new - too difficult to see if you already had generated a PO                     -->
                    <p:outputPanel>

                        <p:commandButton rendered="#{opportunities.recId>0}" 
                                         id="btnGenerateNew"  style="width: 98px; height: 25px;margin-top: 4px"
                                         title="Generate New" action="#{oppService.genLineItems()}"
                                         class="btn btn-primary btn-xs" update=":frmGenNew" oncomplete="PF('dlgGenerateNewPO').show()"
                                         value="Generate New"  />


                        <!-- PMS 2759:CRM-3643: generate new - too difficult to see if you already had generated a PO              -->
                        <p:outputLabel id="opLblOpCount" value="(#{opportunities.poCount})"  title="PO Count" style="color: blue; height: 25px;margin-top: 4px; text-align: justify;" rendered="#{opportunities.poCount!=null and opportunities.poCount!=0 }" />
                    </p:outputPanel>
                </td>

            </tr>
        </table>
    </h:form>
    <h:form id="frmSummary">
        <p:outputPanel id="oppSummary"  style="text-align: center" >
            <table style="width: 100%;background: white;table-layout: fixed;padding: 0px 6px;" >

                <tr>
                    <td style="text-align: left">
                        <h:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}:  " style="font-weight: bold"/>
                        <p:spacer width="4"/>
                        <h:link   outcome="/opploop/companies/CompanyDetails.xhtml?id=#{opportunities.oppCustomer}"  
                                  value="#{opportunities.directSales ? '':opportunities.oppCustomerName}" />
                    </td>
                </tr>  
                <tr>
                    <td style="text-align: center">
                        <!--11373: 06/06/2023: ESCALATIONS CRM-7048   Repfabric: Turning text phone numbers into tel: links-->
                        <h:outputLabel value="#{opportunities.directSales ? '':opportunities.getContSummaryLabel(opportunities.oppCustomerContact)}" 
                                       class="lbl_cnt"/>  <h:outputLink value="#{rFUtilities.returnTelNum(opportunities.directSales ? '':opportunities.getContSummaryContLabel(opportunities.oppCustomerContact))}" rendered="#{!opportunities.getContSummaryContLabel(opportunities.oppCustomerContact).equals(', ')}">#{opportunities.directSales ? '':opportunities.getContSummaryContLabel(opportunities.oppCustomerContact)}</h:outputLink>
                        <h:outputLink value="#{rFUtilities.returnTelNum(opportunities.directSales ? '':opportunities.getContSummaryAlterContLabel(opportunities.oppCustomerContact))}" rendered="#{!opportunities.getContSummaryAlterContLabel(opportunities.oppCustomerContact).equals(', ')}">#{opportunities.directSales ? '':opportunities.getContSummaryAlterContLabel(opportunities.oppCustomerContact)}</h:outputLink>
                    </td>

                </tr>
                <tr style="height: 20px">
                    <td>
                        <p:commandButton title="Mail to #{custom.labels.get('IDS_CUSTOMER')}"    type="button" icon="ui-icon-mail-closed"  style="background: #ffffff;border: none;height: 15px" 
                                         onclick="window.open('#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=mailtocustomer&amp;cont_id=#{opportunities.oppCustomerContact}&amp;opp_id=#{opportunities.oppId}', '_blank')" />
                    </td>
                </tr>

                <tr>
                    <td style="text-align: left">
                        <h:outputLabel value="#{custom.labels.get('IDS_PRINCI')}:"  style="font-weight: bold"  />
                        <p:spacer width="4"/>
                        <h:link outcome="/opploop/companies/CompanyDetails.xhtml?id=#{opportunities.oppPrincipal}"  
                                value="#{opportunities.oppPrincipalName}" />
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;color: #F64C21">
                        <!--11373: 06/06/2023: ESCALATIONS CRM-7048   Repfabric: Turning text phone numbers into tel: links-->
                        <h:outputLabel value="#{opportunities.getContSummaryLabel(opportunities.oppPrincipalContact)}" 
                                       class="lbl_cnt"/> <h:outputLink value="#{rFUtilities.returnTelNum(opportunities.getContSummaryContLabel(opportunities.oppPrincipalContact))}" rendered="#{!opportunities.getContSummaryContLabel(opportunities.oppPrincipalContact).equals(', ')}">#{opportunities.getContSummaryContLabel(opportunities.oppPrincipalContact)}</h:outputLink>                         
                        <h:outputLink value="#{rFUtilities.returnTelNum(opportunities.getContSummaryAlterContLabel(opportunities.oppPrincipalContact))}" rendered="#{!opportunities.getContSummaryAlterContLabel(opportunities.oppPrincipalContact).equals(', ')}">#{opportunities.getContSummaryAlterContLabel(opportunities.oppPrincipalContact)}</h:outputLink>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <td>
                        <p:commandButton title="Mail to #{custom.labels.get('IDS_PRINCI')}"    type="button" icon="ui-icon-mail-closed"  style="background: #ffffff;border: none;height: 15px" 
                                         onclick="window.open('#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=mailtoprincipal&amp;cont_id=#{opportunities.oppPrincipalContact}&amp;opp_id=#{opportunities.oppId}', '_blank')" />
                    </td>
                </tr>


                <tr>
                    <td style="text-align: left">
                        <h:outputLabel value="#{custom.labels.get('IDS_DISTRI')}:"  style="font-weight: bold"  />
                        <p:spacer width="4"/>
                        <h:link outcome="/opploop/companies/CompanyDetails.xhtml?id=#{opportunities.oppDistri}"  
                                value="#{opportunities.oppDistributorName}" />
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center">
                        <!--11373: 06/06/2023: ESCALATIONS CRM-7048   Repfabric: Turning text phone numbers into tel: links-->
                        <h:outputLabel value="#{opportunities.getContSummaryLabel(opportunities.oppDistriContact)}" 
                                       class="lbl_cnt"/> <h:outputLink value="#{rFUtilities.returnTelNum(opportunities.getContSummaryContLabel(opportunities.oppDistriContact))}" rendered="#{!opportunities.getContSummaryContLabel(opportunities.oppDistriContact).equals(', ')}">#{opportunities.getContSummaryContLabel(opportunities.oppDistriContact)} </h:outputLink>               
                        <h:outputLink value="#{rFUtilities.returnTelNum(opportunities.getContSummaryAlterContLabel(opportunities.oppDistriContact))}" rendered="#{!opportunities.getContSummaryAlterContLabel(opportunities.oppDistriContact).equals(', ')}">#{opportunities.getContSummaryAlterContLabel(opportunities.oppDistriContact)}</h:outputLink>
                    </td>
                </tr>

                <tr style="height: 20px">
                    <td>
                        <p:commandButton title="Mail to #{custom.labels.get('IDS_DISTRI')}"    type="button" icon="ui-icon-mail-closed"  style="background: #ffffff;border: none;height: 15px"  
                                         onclick="window.open('#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=mailtocustomer&amp;cont_id=#{opportunities.oppDistriContact}&amp;opp_id=#{opportunities.oppId}', '_blank')"/>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left">
                        <h:outputLabel value="#{custom.labels.get('IDS_ACTIVITY')}:" style="font-weight: bold"    />
                        <p:spacer width="4"/>
                        <h:outputLabel class="lbl_val"  value="#{opportunities.oppActivity}" />
                    </td>
                </tr>


                <tr>
                    <td style="text-align: left">
                        <!--#13178 ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
                        <h:outputLabel value="#{custom.labels.get('IDS_ACT_STATUS')}" style="font-weight: bold"    />
                        <p:spacer width="4"/>
                        <h:outputLabel class="lbl_val"  value="#{opportunities.oppStatus}" />
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left">
                        <h:outputLabel value="#{custom.labels.get('IDS_FOLLOW_UP')}:" style="font-weight: bold"    />
                        <p:spacer width="4"/>
                        <h:outputLabel class="lbl_val"  value="#{opportunities.oppFollowUp}" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </h:outputLabel>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left">
                        <h:outputLabel value="#{custom.labels.get('IDS_VALUE')}:" style="font-weight: bold"    />
                        <p:spacer width="4"/>
                        <h:outputLabel class="lbl_val"  value="#{opportunities.oppValue}" >
                            <f:convertNumber  minFractionDigits="2" />
                        </h:outputLabel>
                    </td>
                </tr>
                <p:outputPanel rendered="#{projOpps.projId ne null and projOpps.projId ne 0}">
                    <tr>
                        <td style="text-align: left">
                            <h:outputLabel value="#{custom.labels.get('IDS_OPP_PROJ')}:"  style="font-weight: bold"  />
                            <p:spacer width="4"/>
                            <h:link outcome="/opploop/projects/ProjectView.xhtml?id=#{projOpps.projId}"  
                                    value="#{projOpps.projPrgm}" />
                        </td>
                    </tr>
                </p:outputPanel>
                <tr style="height: 20px"></tr>
                <tr>
                    <td style="text-align: left">
                        <h:outputLabel value="#{custom.labels.get('IDS_PROGRAM')}:"  style="font-weight: bold"  />
                        <p:spacer width="4"/>
                        <h:outputLabel class="lbl_val"  value="#{opportunities.oppCustProgram}" />
                    </td>
                </tr>


                <tr>
                    <td style="text-align: left">
                        <!--                        // 18/05/2021 Feature  #4889 Repfabric / AVX CRMSync / Relabeling Fields / "Next Step"-->
                        <h:outputLabel value="#{custom.labels.get('IDS_NEXT_STEP')} :" style="font-weight: bold"    />
                        <p:spacer width="4"/>
                        <h:outputLabel class="lbl_val"  value="#{opportunities.oppNextStep}" />
                    </td>
                </tr>

                <tr style="height: 20px"/>
                <tr>
                    <td style="text-align: left">
                        <h:outputLabel value="Description:" style="font-weight: bold"    />  
                        <p:spacer width="4"/>
                        <h:outputLabel class="lbl_val"  value="#{opportunities.oppDescr}" />
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left">
                        <!--2624 - CRM-977: Kai: Allow for field relabel under custom relabel for Opp Owner - sharvani - 11-12-2019-->
                        <h:outputLabel value="#{custom.labels.get('IDS_OPP_OWNER')}:" style="font-weight: bold"    />
                        <p:spacer width="4"/>
                        <h:outputLabel  id="ownerLbl" class="lbl_val"  value="#{opportunities.oppOwnerName}" />
                    </td>
                </tr>
            </table>
            <br></br>
            <!--      
                  <div class="ui-icon ui-icon-info"></div>
                  <div >Opportunity info</div>-->
            <p:outputPanel  rendered="#{opportunities.oppId>0}" 
                            style="font-size: 0.795em;
                            color: #6A6F73;">
                <h:outputLabel value="Created:"  style="font-weight: bold"  />   
                <p:spacer width="4"/>
                <h:outputLabel   value="#{rFUtilities.convertFromUTC(opportunities.insDate)}" >
                    <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                </h:outputLabel>
                <br></br>
                by 
                <h:outputLabel   value=" #{opportunities.createdUser}" />

                <br></br>
                <!--Feature #11393 CRM-7044   Leadloader insertions: denote where in the history the record came from by harshithad on 07/06/23-->
                <h:outputLabel id="otptLblOppSrc" value="Data Source:"  style="font-weight: bold"  /> 
                <p:spacer width="4"/>
                <h:outputLabel   value=" #{opportunities.oppDataSource}" />
                <br></br>
                <!--Feature #11393 CRM-7044   Leadloader insertions: denote where in the history the record came from by harshithad on 07/06/23-->
                <br/>
                <h:outputLabel value="Last update:"  style="font-weight: bold"  />   
                <p:spacer width="4"/>
                <h:outputLabel   value="#{rFUtilities.convertFromUTC(opportunities.updDate)}" >
                    <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                </h:outputLabel>

                <br></br>
                by 

                <h:outputLabel   value=" #{opportunities.updatedUser}" />
            </p:outputPanel>


        </p:outputPanel>
    </h:form>

    <!--//    Task #461: CRM-2505:  Opportunities > Opp Close Reason lookup-->

    <p:dialog  header="Close #{custom.labels.get('IDS_OPP')}" 
               widgetVar="cnfClose" class="dialogCSS" modal="true">
        <!--1354 CRM-3024: ALL INSTANCES: Opp Error??  "Leave page"-->
        <!-- #7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - update primary info button when close status dialog close -->
        <p:ajax event="close" process="@this" update=":frmOpp:tabOpp:oppCustBtn :frmOpp:tabOpp:oppCustCntBtn :frmOpp:tabOpp:oppPrinciBtn :frmOpp:tabOpp:oppPrinciCntBtn :frmOpp:tabOpp:oppDistriBtn :frmOpp:tabOpp:oppDistriCntBtn" listener="#{oppService.closeCancelOpenSts}"/> 
        <!--2204 :CRM-3487:  move "Won" and "Lost" in close opp window-->

        <h:form id="frmUpdateStatus">   
            <!--#7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - commented focus on won-->
            <!--            <p:focus context="clstat" />-->
                        <!--<p:remoteCommand name="applycloseReason" actionListener="#{oppCloseReasonMstService.applyOppCloseReason(oppCloseReasonMstService.selectedOppCloseRsnMst)}" />-->

 <!--<p:remoteCommand actionListener="#{commTransactionService.populateHdrFromOpp(opportunities.oppId)}" name="pupulateTM" />-->
            <p:panel id="pnlCLOSE" class="pnldlgs" style="border:none !important; " >


                <p:panelGrid>
                    <p:row>

                        <!--2204 :CRM-3487:  move "Won" and "Lost" in close opp window-->
                        <p:column colspan="4">
                            <p:outputLabel value="Status"/>
                        </p:column>
                        <p:column>
                            <!--750 close opp  <p:selectOneRadio  id="clstat" onchange="#{opportunities.onChangeCloseStatus(onChangeCloseStatus)}" value="#{onChangeCloseStatus}"-->
                            <!--                            PMS 2628: PO Downflow: Opportunity > #1 Updates to Close Opp Screen- diasbled radio buttons-->
                            <!--  #7044: Opportunities > Updates to Close Dialog - poornima - added cancelled and updated the form,panel  -->
                            <!-- #7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - added condition for disable and update panel note -->
                            <!--#7906: Update Close Status/Activity on saving opportunity->URL issue - poornima - added closename to disabled-->
                            <p:selectOneRadio  id="clstat" onchange="#{opportunities.onChangeCloseStatus(opportunities.oppCloseStatus)}" value="#{opportunities.oppCloseStatus}"
                                               requiredMessage="Closing status not selected" 
                                               required="true" disabled="#{opportunities.prevOppCloseStatus!=0 and opportunities.urlCloseStatus == '' || opportunities.closeName == 'Won' || opportunities.closeName == 'Lost' || opportunities.closeName == 'Cancelled'}" >
                                <f:selectItem itemValue="1" itemLabel="Won"/> 
                                <f:selectItem itemValue="2" itemLabel="Lost"/>
                                <f:selectItem itemValue="3" itemLabel="Cancelled"/>
                                <!--                                PMS 2628:PO Downflow: Opportunity > #1 Updates to Close Opp Screen-->
                                <p:ajax event="change" process="@this" 
                                        update="frmUpdateStatus:pnlCLOSE" />
                                <!--oncomplete="$('#frmUpdateStatus\\:closeTotalPrice_input').val($('#frmOpp\\:tabOpp\\:inpTxtValue').val());"/>-->
                                <!--update="frmUpdateStatus:pnlCLOSE"  oncomplete="$('#frmUpdateStatus\\:closeTotalPrice_input').val(parseFloat($('#frmOpp\\:tabOpp\\:inpTxtValue').val().replace(/,/g, '')));"/>-->

                            </p:selectOneRadio>
                        </p:column>

                        <!--2204 :CRM-3487:  move "Won" and "Lost" in close opp window-->
                        <p:column colspan="4">
                            <p:outputLabel value="Date"/>
                        </p:column>
                        <p:column>
                            <!--#932:  Opportunities > won and lost->reopen date issue-->
                            <p:calendar  id="dtClose"  pattern="#{globalParams.dateFormat}" style="width:100%" 
                                         converterMessage="Closing date not valid" required="true" requiredMessage="Closing Date required"
                                         value="#{opportunities.oppCloseDateVal}" >
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                            </p:calendar>
                        </p:column>
                    </p:row>

                    <p:row>
                        <p:column colspan="4">
                            <!--                            Task #6393- Opportunity>Close Reason/Fail Reason by Vithin-->
                            <p:outputLabel id="reason" value="Closed Reason" />
                        </p:column>
                        <p:column colspan="8">
                            <h:panelGroup class="ui-inputgroup"  >  
                                <!--#2573 CRM-1594: Fwd: Close Out-->
                                <!--//    Task #461: CRM-2505:  Opportunities > Opp Close Reason lookup-->
                                <p:inputText id="clsReasn" value="#{opportunities.oppCloseReason}" style="width:90%" maxlength="200" />
                                <!--//    Task #461: CRM-2505:  Opportunities > Opp Close Reason lookup-->
                                <!--Task #6393- Opportunity>Close Reason/Fail Reason by Vithin-->
                                <p:commandButton  icon="fa fa-search" title="Choose reason" immediate="true" 
                                                  actionListener="#{oppCloseReasonMstService.closeListOpp(opportunities.oppPrincipal)}"
                                                  oncomplete="PF('dlgOppCloseReason').show();PF('oppCloseRsnDtl').clearFilters();"  update=":dtOppCloseForm:oppCloseDtl"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                        </p:column>

                    </p:row>

                    <!--Task #6393- Opportunity>Close Reason/Fail Reason by Vithin-->  
                    <!-- #7044: Opportunities > Updates to Close Dialog - poornima - added condtion for cancelled -->
                    <p:row rendered="#{opportunities.oppCloseStatus == 2 or opportunities.oppCloseStatus == 3}">
                        <p:column colspan="4">
                            <p:outputLabel id="failedreason" value="Failed Reason" />
                        </p:column>
                        <p:column colspan="8">
                            <h:panelGroup class="ui-inputgroup"  >  
                                <!--#2573 CRM-1594: Fwd: Close Out-->
                                <!--//    Task #461: CRM-2505:  Opportunities > Opp Close Reason lookup-->
                                <p:inputText id="flReasn" value="#{opportunities.oppFailedReason}" style="width:90%" maxlength="200" />
                                <!--//    Task #461: CRM-2505:  Opportunities > Opp Close Reason lookup-->
                                <p:commandButton  icon="fa fa-search" title="Choose reason" immediate="true" 
                                                  actionListener="#{oppFailedReasonMstService.failedOppRsn(opportunities.oppPrincipal)}"
                                                  oncomplete="PF('dlgOppFailedReason').show();PF('oppFailedRsnDtl').clearFilters();"  update=":dtOppFailedForm:oppFailedDtl"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                        </p:column>

                    </p:row>

                    <!--                    PMS 2628:PO Downflow: Opportunity > #1 Updates to Close Opp Screen: 3 rows commentted-->

<!--                    <p:row rendered="#{opportunities.oppCloseStatus == 1}">
                        <p:column colspan="4">
                            <p:outputLabel value="Quote No" />
                        </p:column>
                        <p:column>
                            <p:inputText value="#{opportunities.trnhQuoteNumber}" maxlength="60" />
                        </p:column>
                        <p:column colspan="4">
                            <p:outputLabel value="Quote Date" />
                        </p:column>
                        <p:column>
                            <p:calendar  id="qtdate"  pattern="#{globalParams.dateFormat}" style="width:100%"
                                         converterMessage="Quotation date not valid" 
                                         value="#{opportunities.trnhQuoteDate}" >
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                            </p:calendar>
                        </p:column>
                    </p:row>


                    <p:row rendered="#{opportunities.oppCloseStatus == 1}">
                        <p:column colspan="4">
                            <p:outputLabel value="PO No" />
                        </p:column>
                        <p:column>
                                                         PMS 466
                            <p:inputText value="#{opportunities.trnhPoNumber}" maxlength="60">
                                <p:ajax event="change" listener="#{opportunities.onChangePoNum()}" 
                                        update=":frmUpdateStatus:chck"  />
                            </p:inputText>
                        </p:column>

                        <p:column colspan="4">
                            <p:outputLabel value="PO Date" />
                        </p:column>
                        <p:column>
                            <p:calendar  id="pdate"  pattern="#{globalParams.dateFormat}" style="width:100%"  
                                         converterMessage="PO date not valid"
                                         value="#{opportunities.trnhPoDate}" >
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                            </p:calendar>
                        </p:column>
                    </p:row>

                    <p:row rendered="#{opportunities.oppCloseStatus == 1}">
                        <p:column colspan="4">
                            <p:outputLabel value="SO No" />
                        </p:column>
                        <p:column>
                            <p:inputText value="#{opportunities.trnhSoNumber}" maxlength="60" />
                        </p:column>

                        <p:column colspan="4">
                            <p:outputLabel value="SO Date" />
                        </p:column>
                        <p:column>
                            <p:calendar  id="sdate"  pattern="#{globalParams.dateFormat}" style="width:100%" 
                                         converterMessage="SO date not valid"
                                         value="#{opportunities.trnhSoDate}" >
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                            </p:calendar>
                        </p:column>
                    </p:row>-->
                    <!--PMS 2628: PO Downflow: Opportunity > #1 Updates to Close Opp Screen-->
                    <p:row rendered="#{opportunities.oppCloseStatus == 1}">
                        <p:column colspan="4">
                            <b>Commissionable Transaction:</b>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{opportunities.oppCloseStatus == 1}">
                        <p:column colspan="4">
                            <!--                            Bug 1086:#501257:POKLAR & GOLDEN INSTANCE :spell total-->
                            <p:outputLabel value="Total Price" />
                        </p:column>
                        <p:column>
                            <!--<p:inputText id="closeTotalPrice" value="#{opportunities.trnhTotalPrice}" maxlength="14" />-->
                            <!-- PMS 2628: PO Downflow: Opportunity > #1 Updates to Close Opp Screen-->                            
                            <p:inputNumber decimalPlaces="2" disabled="#{opportunities.prevOppCloseStatus==1 or opportunities.disable}"   maxValue="9999999999.99"  id="closeTotalPrice" value="#{opportunities.trnhTotalPrice}"   maxlength="12" converterMessage="Must be a signed decimal number." >
                            </p:inputNumber>
                        </p:column>

                        <p:column colspan="4">
        <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                            <p:outputLabel  value="#{custom.labels.get('IDS_COMMISSION')}  Rate " />
                        </p:column>
                        <p:column>
                            <!--<p:inputText value="#{opportunities.trnhCommRate}" maxlength="8" />-->
                            <!-- PMS 2628: PO Downflow: Opportunity > #1 Updates to Close Opp Screen-->                            
                            <p:inputNumber decimalPlaces="2"  disabled="#{opportunities.prevOppCloseStatus==1 or opportunities.disable}" maxValue="99.99"  id="tCommPrice" value="#{opportunities.trnhCommRate}"    maxlength="4" converterMessage="Must be a signed decimal number." >
                            </p:inputNumber>
                        </p:column>
                    </p:row>
                    <!--                     PMS 466-->

                </p:panelGrid>


            </p:panel>

            <!--#8737   CRM-6024: Option to clear actions on Close-->
            <p:panel id="clearPendingCheckbox" rendered="#{opportunities.closeName=='Open' and opportunities.actionSetStatus.STATUS == 'PENDING'}">
                <p:selectBooleanCheckbox   id="actPendingCheckbox" value="#{opportunities.clearPendingActions}"  >
                    <p:ajax  event="change" listener="#{opportunities.onChecked()}" />
                </p:selectBooleanCheckbox>
                <p:spacer width="10px"/>
                <p:column>    
                    <p:outputLabel value="Cancel all pending actions ?"/>
                </p:column>
            </p:panel>

            <!--            PMS 2628:PO Downflow: Opportunity > #1 Updates to Close Opp Screen-->
            <!--            <p:panel id="chck">
                            <p:row id="poCheckRow" rendered="#{opportunities.oppCloseStatus == 1 and opportunities.trnhPoNumber.length()>0}">
                                <p:column colspan="4">
                                    <p:outputLabel value="Push to PO "  />
                                </p:column>
                                <p:column> 
                                    <p:selectBooleanCheckbox id="poCheck" value="#{opportunities.pushtoPOFlag}"    />
                                </p:column>
                            </p:row>
                        </p:panel>-->

            <!--update=":frmOppStatus :frmUpdateStatus :frmOpp"--> 
            <!--actionListener="#{commTransactionService.saveHdr(opportunities.oppCloseStatus)}"-->
            <!--1557 CRM-3161: Opp - Open/closed Field  - Unable to Edit?-->
            <p:commandButton value="Save" actionListener="#{oppService.setOppValue(opportunities)}" action="#{oppService.checkPOCombination(opportunities)}"
                             onstart="removeEventListener('beforeunload', loadValuesOnClose());" onclick="loadValuesOnClose();"
                             class="btn btn-xs btn-success" process="@this,:frmUpdateStatus,:frmOpp" partialSubmit="true"
                             />
            <!--   process="@this"   update=":frmSummary :frmOppStatus :frmOpp"    update=":frmSummary :frmOppStatus :frmOpp"    update=":frmOppStatus :frmUpdateStatus  :frmOpp" -->   
            <p:spacer width="4"/>
            <!--#911 :opp reopen issue                   rendered="#{opportunities.oppCloseFlag==true}"-->
            <!--1557 CRM-3161: Opp - Open/closed Field  - Unable to Edit?-->
            <!-- pms:3093 23/01/2021 udaya b -->             
            <!-- #7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - rendering for close -->
            <!--#7906: Update Close Status/Activity on saving opportunity->URL issue - poornima - added closename to rendered-->   
            <!--#8737   CRM-6024: Option to clear actions on Close-->
            <p:commandButton  rendered="#{opportunities.closeName!='Open'  and opportunities.urlCloseStatus == '' || opportunities.closeName == 'Won' || opportunities.closeName == 'Lost' || opportunities.closeName == 'Cancelled'}"
                              value="Reopen" 
                              process="@this"  
                              actionListener="#{oppService.reOpen()}"
                              class="btn btn-xs btn-primary"
                              update=":frmSummary :frmOppStatus :frmOpp :frmUpdateStatus" 
                              oncomplete="PF('cnfClose').hide();"/> 
            <p:spacer width="4"/>
            <!--1354 CRM-3024: ALL INSTANCES: Opp Error??  "Leave page"-->
            <p:commandButton value="Cancel" process="@this"  
                             actionListener="#{oppService.closeCancelOpenSts()}"  
                             class="btn btn-xs btn-warning" oncomplete="PF('cnfClose').hide();" 
                             />
            <!-- pms:3093 29/01/2021 -->
            <br/>
            <!-- #7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - added panel -->
            <!--            #8283: CRMSYNC-10: ledvancesfdc: closed status and reason parsing - poornima - commented id panelNote1-->            
            <!--            <p:panel id="panlNote1">-->
            <!--22-06-2023 11430 ESCALATIONS CRM-7073   Opportunities: Unable to change commissions % when reopening an opp-->
            <p:panel rendered="#{opportunities.showCloseTransNote}">
                <b>Note: </b>There is a commisionable transaction linked to this opportunity. Please make the necessary updates.    
            </p:panel> 
            <!--</p:panel>-->

            <!--#8283: CRMSYNC-10: ledvancesfdc: closed status and reason parsing - poornima - commented note for lost/cancel-->
            <!--   #7044: Opportunities > Updates to Close Dialog - poornima - based on condition Note will display -->
            <!--            <p:panel id="panlNote">
                            <p:panel rendered="#{opportunities.oppCloseStatus == 2 or  opportunities.oppCloseStatus == 3}">
                                <b>Note: </b>Please change the #{custom.labels.get('IDS_ACTIVITY')} accordingly.
                            </p:panel>
                        </p:panel>-->
        </h:form>
    </p:dialog>

    <!--   PMS 466 -->
    <p:dialog header="Confirmation"   showEffect="fade" hideEffect="fade" 
              widgetVar="dupPoCombDlg" id="dupPoComb" responsive="true" >
        <h:form id="dupPoCombFrm">
            <div class="cntr">
                <p:outputLabel value="PO Number exist for #{custom.labels.get('IDS_PRINCI')}. Do you want to continue?" />
                <br/>
                <br/>
                <p:commandButton value="Yes" 
                                 actionListener="#{oppService.closeOpp()}"   
                                 styleClass="btn btn-success  btn-xs" 
                                 oncomplete="PF('cnfClose').hide();PF('dupPoCombDlg').hide()"
                                 >
                </p:commandButton>
                <p:spacer width="4px"/>
                <p:commandButton value="No" styleClass="btn btn-danger  btn-xs" 
                                 onclick="PF('dupPoCombDlg').hide()" 
                                 type="button" />
            </div>
        </h:form>
    </p:dialog>
    <!-- pms:3093 23/01/2021 udaya b -->
    <h:form id="delCon">
        <p:growl id="nodelete" widgetVar="nodelete"  />
        <!--                Task#3408-Enhancements to 3093: Opportunity >  Re-Open   11-02-2021 by harshithad-->
        <p:confirmDialog widgetVar="compDelete1" id="compDelete1" showEffect="fade" hideEffect="fade" header="Confirmation" >
            <f:facet name="message">
                <p:outputLabel value="Do you want to delete the linked commissionable transaction and repost when the Opportunity is closed again or leave it as is?" />
                <br/>
                <p:outputLabel value= "Note: If you leave it as is, then any updates made to this opportunity will not be updated in the linked commissionable transactions." style="color:blue"/>

            </f:facet>




            <div align="center">
                <!--                            //PMS:2562
                //Seema - 03/12/2020-->
                <!--#7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - called ZXZ method to refresh the page-->
                <p:commandButton value="Delete" actionListener="#{oppService.deleteTrans()}" 
                                 oncomplete="PF('compDelete1').hide();zxz()"   id="btnConfirmCompDeleteYes"
                                 styleClass="btn btn-success  btn-xs"/>
                <p:spacer width="5px"/>
                <p:commandButton value="Leave It"  onclick="PF('compDelete1').hide()" 
                                 id="btnConfirmCompDeleteNo" oncomplete="zxz()"
                                 styleClass="btn btn-danger  btn-xs"/>
            </div>

        </p:confirmDialog>
    </h:form>
    <!--1328 Pending Issues from 1300: Opps > Close : Won/Lost Issues-->
    <p:dialog widgetVar="inProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
        <h:form>
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif"   />
                <p:spacer width="5" />
                <p:outputLabel value="Please wait..." />
                <br /><br />
                <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
            </p:outputPanel>
        </h:form>
    </p:dialog>



    <p:dialog id="dlgGenerateNew" widgetVar="dlgGenerateNewPO" header="PO Generation" modal="true" resizable="false" width="1200">
        <h:form id="frmGenNew">
            <p:panel  id="pnlGenNew" class="pnldlgs" style="border:none !important; " >


                <p:panelGrid style="width: 600px;" columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" layout="grid" styleClass="box-primary no-border ui-fluid" >
                    <!--                                <p:row  >-->
                    <p:column colspan="20"   >
                        <p:outputLabel value="Quote No." />
                    </p:column>
                    <p:column colspan="20">
                        <p:inputText  value="#{opportunities.trnhQuoteNumber}" maxlength="60" />
                    </p:column>
                    <p:column colspan="20">
                        <p:outputLabel value="Quote Date" />
                    </p:column>
                    <p:column>
                        <p:calendar  id="qtdate"  pattern="#{globalParams.dateFormat}" style="width:100%"
                                     converterMessage="Quotation date not valid" 
                                     value="#{opportunities.trnhQuoteDate}" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </p:calendar>
                    </p:column>
                    <!--                    </p:row>
                    
                    
                                        <p:row >-->
                    <p:column colspan="20">
                        <p:outputLabel value="PO No." styleClass="required"  />
                    </p:column>
                    <p:column colspan="20">
                        <p:inputText value="#{opportunities.trnhPoNumber}" required="true" requiredMessage="PO Number is required" maxlength="60">
                            <!--/Bug #2962: CRM-3736: Close Opportunity--> 
                            <!--                                <p:ajax event="change" listener="#{opportunities.onChangePoNum()}" 
                                                                     />-->
                        </p:inputText>
                    </p:column>

                    <p:column colspan="20">
                        <p:outputLabel value="PO Date" styleClass="required"  />
                    </p:column>
                    <p:column colspan="20">
                        <p:calendar  id="pdate" required="true" requiredMessage="PO date is required"  pattern="#{globalParams.dateFormat}" style="width:100%"  
                                     converterMessage="PO date not valid"
                                     value="#{opportunities.trnhPoDate}" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </p:calendar>
                    </p:column>
                    <!--                    </p:row>
                    
                                        <p:row >-->
                    <p:column colspan="20">
                        <p:outputLabel value="SO No." />
                    </p:column>
                    <p:column>
                        <!--                      Task#4513  CRM-4283: Sales order number does NOT show in "Purchase Order" screen in the PO  19/05/21-->
                        <p:inputText value="#{opportunities.trnhSoNumber}" maxlength="40" />
                    </p:column>
                    <!--                            Feature #5358  Relabel "SO Date" to "Transmit Date"                                                                                               -->
                    <p:column colspan="20">
                        <p:outputLabel value="#{custom.labels.get('IDS_SO_DATE')}" />
                    </p:column>
                    <p:column colspan="20">
                        <p:calendar  id="sdate"  pattern="#{globalParams.dateFormat}" style="width:100%" 
                                     converterMessage="SO date not valid"
                                     value="#{opportunities.trnhSoDate}" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </p:calendar>
                    </p:column>
                    <!--                    </p:row>
                                        <p:row >-->
                    <p:column colspan="20">
                        <p:outputLabel value="#{custom.labels.get('IDS_COMM')} Rate " />
                    </p:column>
                    <p:column colspan="20">

                        <!--Feature #3031:Refer#2968: Opp>generate new PO-make mouse click enable commisison entry without backing--> 
                        <p:inputNumber decimalPlaces="2"   maxValue="99.99"  id="tCommPricePo" value="#{opportunities.trnhCommRate}"  placeholder="0.00"   maxlength="4" converterMessage="Must be a signed decimal number." >
                        </p:inputNumber>
                    </p:column>

                    <!--                    </p:row>-->
                </p:panelGrid>
            </p:panel>
            <!--            PMS 3970:CRM-4121: Turning a quote into a PO- Paginaion is added-->
            <p:dataTable value="#{oppService.listLineItems}" 
                         rowKey="#{line.recId}"
                         selection="#{oppService.selectedForPo}" 
                         rowSelectMode="add"   
                         emptyMessage="No #{custom.labels.get('IDS_LINE_ITEM')} found"
                         var="line" editable="true" editMode="cell" 
                         rows="5" 
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="5,10,15" paginator="true" paginatorAlwaysVisible="true"
                         id="dtLineItms">
                <p:column  selectionMode="multiple" style="text-align: center;" width="2%"/>
                <p:column filterBy="#{line.oppItemPartManf}"  sortBy="#{line.oppItemPartManf}" filterMatchMode="contains" width="5%" headerText="#{custom.labels.get('IDS_MFG_PART_NUM')}">
                    #{line.oppItemPartManf}
                </p:column>
                <p:column width="5%" filterBy="#{line.oppItemResale}" sortBy="#{line.oppItemResale}"  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_RESALE')}" >           

                    <h:outputLabel  value="#{line.oppItemResale}">
                    </h:outputLabel>
                </p:column>
                <p:column width="5%" filterBy="#{line.oppItemQnty}" sortBy="#{line.oppItemQnty}"  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_LINE_ITEM_QTY')}" >          
                    <p:outputLabel value="#{line.oppItemQnty}">
                        <f:convertNumber maxFractionDigits="3" groupingUsed="false" maxIntegerDigits="9"/>
                    </p:outputLabel>
                </p:column>
                <p:column width="5%" filterBy="#{line.oppEAU}" sortBy="#{line.oppEAU}"  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_EAU')}" >

                    <h:outputLabel  value="#{line.oppEAU}">
                        <f:convertNumber  groupingUsed="true"  maxFractionDigits="2"  minFractionDigits="2" />
                    </h:outputLabel>
                </p:column>
                <p:column width="5%" filterBy="#{line.totalQty}" sortBy="#{line.totalQty}"  filterMatchMode="contains" headerText="Total Qty" >
                    #{line.totalQty}
                </p:column>
                <p:column width="5%" filterBy="#{line.plannedqty}" sortBy="#{line.plannedqty}"  filterMatchMode="contains" headerText="Planned Qty"   >
                    <p:cellEditor>
                        <f:facet name="output" id="plnndQtyOut">
                            <h:outputText  value="#{line.plannedqty}" />
                        </f:facet>
                        <f:facet name="input">
                            <p:inputNumber value="#{line.plannedqty}"  style="font-size: 11px;" decimalPlaces="2" >

                            </p:inputNumber>
<!--                            <h:inputText value="#{line.plannedqty}"  style="font-size: 11px;">
                                <f:convertNumber maxFractionDigits="0"  groupingUsed="true"  maxIntegerDigits="15"/>
                                <p:ajax process="@this" event="blur"   />
                            </h:inputText>-->
                        </f:facet>
                    </p:cellEditor> 

                </p:column>
                <p:column width="5%" filterBy="#{line.shippingDate}" sortBy="#{line.shippingDate}"  filterMatchMode="contains" headerText="Planned Shipping Date">
                    <p:cellEditor>
                        <f:facet name="output" id="plnndShpngDate">
                            <p:calendar  id="shipDate"  pattern="#{globalParams.dateFormat}" style="width:100%"
                                         converterMessage="Quotation date not valid" 
                                         value="#{line.shippingDate}" >
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                            </p:calendar>
                        </f:facet>
                        <f:facet name="input">
                            <p:calendar  id="shipDateIp"  pattern="#{globalParams.dateFormat}" style="width:100%"
                                         converterMessage="Quotation date not valid" 
                                         value="#{line.shippingDate}" >
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                            </p:calendar>
                        </f:facet>
                    </p:cellEditor> 
                </p:column>






            </p:dataTable>
            <div style="text-align:center;padding: 9px;" >
                <!--PMS 2881 :CRM-3699: PO downflow: demo : line items getting lost-->
                <!-- 
    Feature #2957  CRM-3715: Opportunties > PO generation on no line item selection-->
                <!--Task #13025:  Analysis: Duplicate Order Data    by harshithad on 01/02/2024-->
                <p:commandButton value="Generate" id="btnGenerateSave"  actionListener="#{oppService.getOppValueById(opportunities.oppId)}" action="#{oppService.checkLineItemSelected()}"  styleClass="btn btn-success btn-xs"
                                 update=":dlgConfirmPOLine"  widgetVar="genPo"  onclick="PF('genPo').disable()" 
                                 oncomplete="PF('genPo').enable();"       >
                </p:commandButton>
                <p:spacer width="4px"/>
                <p:commandButton value="Cancel" id="btnGenerateCancel" type="button" styleClass="btn btn-danger btn-xs" onclick="PF('dlgGenerateNewPO').hide();"/>
            </div>    
        </h:form>

    </p:dialog>
    <!--PMS 2881 :CRM-3699: PO downflow: demo : line items getting lost-->
    <p:dialog header="Confirmation" id="dlgConfirmPOLine" widgetVar="dlgCnfPOGen" modal="true" resizable="false">
        <h:form id="frmCnfPO">
            <div style="text-align:center;padding: 9px;" >
                <!-- 
    Feature #2957  CRM-3715: Opportunties > PO generation on no line item selection-->
                <p:outputLabel id="poAlertMessage" value="#{opportunities.oppValue gt 0.00 ? 'No line items are selected. Are you sure to generate PO with a line item having default values?':'No line items are selected. Are you sure to generate PO without any line items?'}"/>
                <br/>
                <br/>
                <!--Task #13025:  Analysis: Duplicate Order Data    by harshithad on 01/02/2024-->
                <p:commandButton value="Yes" id="btnGenerateSave" action="#{oppService.generatePORecord}" oncomplete="PF('dlgCnfPOGen').hide();PF('cnfrmGenPO').enable();"  styleClass="btn btn-success btn-xs"
                                 widgetVar="cnfrmGenPO"   onclick="PF('cnfrmGenPO').disable()" >
                </p:commandButton>
                <p:spacer width="4px"/>
                <p:commandButton value="No" id="btnGenerateCancel" type="button" styleClass="btn btn-danger btn-xs" onclick="PF('dlgCnfPOGen').hide();"/>
            </div> 
        </h:form>
    </p:dialog>
    <!--bug #8648  ESCALATIONS: CRM-6000  Getting an error when editing an opp AFTER its been closed-->
    <script type="text/javascript">

        function start() {
            PF('inProgressDlg').show();
        }
        function stop() {
            PF('inProgressDlg').hide();
        }
        function zxz() {
            location.reload(true);
        }
    </script>
    <style>
        .ui-panelgrid tr, .ui-panelgrid td{
            border:0!important;
        }
        .cntr{
            text-align:center;
            padding: 9px;
        }
    </style>
    <!--//    Task #461: CRM-2505:  Opportunities > Opp Close Reason lookup-->
    <ui:include src="oppCloseReasonLookUp.xhtml"  />
    <!--Task #6393- Opportunity>Close Reason/Fail Reason by Vithin-->
    <ui:include src="oppFailedReasonLookUp.xhtml"  />

</ui:composition>