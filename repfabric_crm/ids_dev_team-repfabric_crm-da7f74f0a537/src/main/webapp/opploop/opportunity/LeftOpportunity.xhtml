
<ui:composition  
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

    <f:view>



<!--        <h5>#{custom.labels.get('IDS_OPP')}</h5>-->
        <h:inputHidden id="search_filter" value="#{oppFilter.leftFilter.searchFilter}" />

        <!--02-03-2022 : #7386 : Opportunity List: Create collapsible left filter(panel) option-->
        <p:outputPanel id="selectMenuWitButton" style="display:flex;">

            <!--Filter List-->
            <p:selectOneMenu    value="#{oppFilter.leftFilter.filterName}" autoWidth="true" rendered="#{uIControlService.oppFilterCollapse}" style="min-width:91%; max-width:94%; margin-top:3px;">
                <f:selectItem itemValue="ALL" itemLabel="All Open #{custom.labels.get('IDS_OPPS')}" />
                <f:selectItem itemValue="ALL_OPP" itemLabel="All #{custom.labels.get('IDS_OPPS')}" />
                <f:selectItem itemValue="NEEDS_REVIEW" itemLabel="#{opportunities.NEEDSREVIEW}"/>
                <f:selectItem itemValue="CUST" itemLabel="#{custom.labels.get('IDS_CUSTOMER')}"/>
                <f:selectItem itemValue="PRINCI" itemLabel="#{custom.labels.get('IDS_PRINCI')}" />
                <f:selectItem itemValue="DISTRI" itemLabel="#{custom.labels.get('IDS_DISTRI')}" />
                <f:selectItem itemValue="CONT" itemLabel="Contact" />
                <f:selectItem itemValue="SALES_TEAM" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}" />
                <f:selectItem itemValue="FOLLOW_UP" itemLabel="#{custom.labels.get('IDS_FOLLOW_UP')}" />
                <!--#13178 ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
                <f:selectItem itemValue="STATUS" itemLabel="#{custom.labels.get('IDS_ACT_STATUS')}" />
                <f:selectItem itemValue="CALL_PATT" itemLabel="#{custom.labels.get('IDS_CALL_PATTERN')}" />
                <f:selectItem itemValue="CLOSED" itemLabel="Closed" />
                <!--28/05/2016 New requirement-->
                <f:selectItem itemValue="ACTIVITY" itemLabel="#{custom.labels.get('IDS_ACTIVITY')}" />
                <f:selectItem itemValue="PROD_DATE" itemLabel="#{custom.labels.get('IDS_PRODUCTION_DATE')}" />
                <f:selectItem itemValue="PROTO_DATE" itemLabel="#{custom.labels.get('IDS_PROTO_DATE')}" />
                <!--29-06-2016-->
                <f:selectItem itemValue="RPT_FLAG" itemLabel="Reporting" />
                <f:selectItem itemValue="PRIORITY" itemLabel="#{custom.labels.get('IDS_PRIORITY')}" />
                <f:selectItem itemValue="POTENTIAL" itemLabel="#{custom.labels.get('IDS_POTENTIAL')}" />
                <!--2624 - CRM-977: Kai: Allow for field relabel under custom relabel for Opp Owner - sharvani - 11-12-2019-->
                <f:selectItem itemValue="OPP_OWNER" itemLabel="#{custom.labels.get('IDS_OPP_OWNER')}" />
                <!--           21/06/2021  Feature #4928   watcher question - please add to Opp drop down selections-->
                <!--#5122: Opportunity List > Implement lazy loading-->           
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <f:selectItem itemValue="OPP_WATCHERS" itemLabel="#{custom.labels.get('IDS_WATCHERS')}"/>
                <!-- #934307 09-06-2017-->
                <f:selectItem itemValue="OPP_WITH_ACTIONS" itemLabel="Opp with Actions" />
                <f:selectItem itemValue="CRMSYNCED_OPP" itemLabel="CRMsynced Opps" />
                <!--pms tassk 6570:Aurinko CRMSync: Opportunity List: Add filter: CRM-Unsynced Opps-->
                <f:selectItem itemValue="CRMUNSYNCED_OPP" itemLabel="CRM-Unsynced Opps" />

                <!--24/10/2017-->
                <f:selectItem itemValue="OPP_TYPE" itemLabel="#{custom.labels.get('IDS_OPP_TYPE')}" />
                <!--28/10/2017-->
                <f:selectItem itemValue="OPP_LEAD_SRC" itemLabel="#{custom.labels.get('IDS_LEAD_SRC')}" />
                <!--//CRM-129 KELLER : show stage from the opp list sort menu 30March2019-->
                <!--2739- Related to CRM-1646: Golden updates needed: Dev Ops - Opportunity ‘Stage’- sharvani - 24-12-2019-->
                <f:selectItem itemValue="OPP_ACT_STAGE" itemLabel="#{custom.labels.get('IDS_OPP')} #{custom.labels.get('IDS_OPP_STAGE')}" />
               <!--#11030 ESCALATIONS CRM-6856   Opportunities: Linked samples in Opportunities-->
                <f:selectItem itemValue="OPP_LINKED_SAMPLES" itemLabel="#{custom.labels.get('IDS_OPPS')} with Samples"/>
           <!--Task #15046:CRM-8401   Opportunity Stage Actions - Opps with Actions that have been competed are not clearing fr-->
                <f:selectItem itemValue="OPP_WITH_PENDING_ACTNS" itemLabel="#{custom.labels.get('IDS_OPPS')} with Pending Actions"/>
               
                <!--           #5122: Opportunity List > Implement lazy loading-->
                <!--8186   :14/06/2022: PF 7 related bugs: Opportunities-->
                <!--//23-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                <p:ajax event="change"   update="pnl :dtForm:opo" listener="#{oppService.loadSideList()}" oncomplete="clearFilterBoxq(); PF('tblOpp1').clearFilters();applyTopScrollbarOpp();"/> 

            </p:selectOneMenu>

            <p:spacer width="5"/>
            <!--//25-05-2022 : 8051 : Opportunity: Left Panel Collapsible button-->
            <!--02-03-2022 : #7386 : Opportunity List: Create collapsible left filter(panel) option-->
<!--            <p:commandButton  icon="#{uIControlService.oppFilterCollapse ? 'fa fa-arrow-circle-left' : 'fa fa-arrow-circle-right'}"
                              action="#{uIControlService.oppFilterCollapseHideShowColum()}" title="#{uIControlService.oppFilterCollapse ? 'Collapse' : 'Expand'}" 
                              update=":dtForm :left_menu :left_menu:pnl" styleClass="btn btn-primary btn-xs" 
                              style="float:right; display:inline-block;min-width: 0px;margin-top:3px; height: 25px; width: 25px;"/>-->
            <!--//23-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
            <p:commandLink action="#{uIControlService.oppFilterCollapseHideShowColum()}" title="#{uIControlService.oppFilterCollapse ? 'Collapse' : 'Expand'}"
                           update=":dtForm :left_menu :left_menu:pnl"
                           oncomplete="applyTopScrollbarOpp();"
                           style="float:right; display:inline-block; font-size:2rem; background:transparent;margin-top:3px;">
                <i class="#{uIControlService.oppFilterCollapse ? 'fa fa-arrow-circle-left' : 'fa fa-arrow-circle-right'}" style="font-size:2.3rem; color: #0078D7; "/>
            </p:commandLink>
            <p:spacer width="2"/>

        </p:outputPanel>
        <script>
            function clearFilterBox() {
                try {
                    PF('wdtside').clearFilters();
                } catch (err) {
                }

            }
            //            #5193: opportunities left side filter issue
            function clearFilterBoxq() {
                try {
                    PF('wdtside').clearFilters();
                    PF('wdtside').unselectAllRows();
                    PF('wdtside').filter();
                    PF('callPattDT').clearFilters();
                    PF('callPattDT').unselectAllRows();
                    //PF('dt').clearFilters();
                } catch (err) {
                }

            }
        </script>

        <!--              // Feature #5737 Multi select option for stage / activity in opps list left side filter-->
        <h:panelGroup id="pnl">
            <!-- this table will not rendered if allopp, needs review selected'-->
            <!--#5193: opportunities left side filter issue-->
            <!--02-03-2022 : #7386 : Opportunity List: Create collapsible left filter(panel) option-->
            <p:dataTable scrollable="true" 
                         rendered="#{((opportunities.tableRender eq 1) and (opportunities.renderCallPat eq 0) and (opportunities.renderActivity eq 0)) and uIControlService.oppFilterCollapse}"
                         value="#{opportunities.listSideList}" var="dt"
                         filteredValue="#{opportunities.listSideListFilter}"
                         selectionMode="single" 
                         selection="#{opportunities.selectedSideList}"
                         rowKey="#{dt.itemId}" 
                         emptyMessage=" "  
                         paginator="true" 
                         paginatorTemplate="{FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                         pageLinks="3"
                         rows="150" paginatorAlwaysVisible="false" widgetVar="wdtside"
                         id="dt" multiViewState="true">
                <p:ajax process="@this" event="rowSelect"    listener="#{oppService.onRowSelect1}" oncomplete=" PF('tblOpp1').filter(); "/>

                <!--// #795742 opportunities.renderPotential == 0-->
                <p:column filterMatchMode="contains"  filterBy="#{dt.itemName}"  rendered="#{opportunities.renderCallPat==0 and opportunities.renderPotential == 0 and 
                                                                  opportunities.renderOppType == 0 and opportunities.renderLeadSrc == 0 and opportunities.renderActivity ==0}" id="left_filter" >                    
    <!--                    <h:outputText value="#{dt.itemName.length()>35?dt.itemName.substring(0,32).concat('..'):dt.itemName}"
                                  title="#{dt.itemName}"/>-->
                    <h:outputText value="#{dt.itemName}" />
                </p:column>
            </p:dataTable>  
            <!--// #795742 opportunities.renderPotential == 1-->
            <!--#5193: opportunities left side filter issue-->
            <!--              // Feature #5737 Multi select option for stage / activity in opps list left side filter-->
            <!--02-03-2022 : #7386 : Opportunity List: Create collapsible left filter(panel) option-->
            <p:dataTable  rendered="#{(opportunities.renderCallPat==1 || opportunities.renderPotential == 1 || 
                                      opportunities.renderOppType == 1 || opportunities.renderLeadSrc == 1 || opportunities.renderActivity ==1) and uIControlService.oppFilterCollapse}"
                          value="#{opportunities.listSideList}" var="dt"
                          selection="#{opportunities.selList}"
                          filteredValue="#{opportunities.filteredSideList}"
                          rowKey="#{dt.itemId}" 
                          emptyMessage=" "  
                          paginator="true"   
                          paginatorTemplate="{FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                          pageLinks="3"
                          rows="30" paginatorAlwaysVisible="false"
                          id="dt1" rowSelectMode="add" widgetVar="callPattDT"
                          multiViewState="true">
                <p:ajax process="@this" event="rowSelectCheckbox"   listener="#{oppService.onSelectCheckBox}" oncomplete="PF('tblOpp1').filter();"/>
                <p:ajax process="@this" event="rowUnselectCheckbox"  listener="#{oppService.onUnselectCheckBox}" oncomplete="PF('tblOpp1').filter();"/>
                <!--// #795742 toggleselect-->
                <p:ajax process="@this" event="toggleSelect"   listener="#{oppService.onToggleSelect}" oncomplete="PF('tblOpp1').filter();"/>
                <!--15/03/2017 - Adding this event handler will enable checkbox on row selection-->
                <p:ajax process="@this" event="rowSelect"  listener="#{oppService.onSelectCheckBox}" oncomplete="PF('tblOpp1').filter();"/>
                <!--#5193: opportunities left side filter issue-->
                <p:ajax process="@this" event="rowUnselect"  listener="#{oppService.onUnselectCheckBox}" oncomplete="PF('tblOpp1').filter();"/>
                <!--#5193: opportunities left side filter issue-->
                <p:column width="30" selectionMode="multiple" />
                <p:column filterMatchMode="contains"  filterBy="#{dt.itemName}" id="left_filter_call_patt" >  
    <!--                    <h:outputText value="#{dt.itemName.length()>35?dt.itemName.substring(0,32).concat('..'):dt.itemName}"
                                  title="#{dt.itemName}"/>-->
                    <h:outputText value="#{dt.itemName}" />
                </p:column>
            </p:dataTable>
        </h:panelGroup>
    </f:view>
    <!--//25-05-2022 : 8051 : Opportunity: Left Panel Collapsible button-->
    <style>
        .fa-arrow-circle-left:hover{
            color: #0D6BB9 !important;
        }
        .fa-arrow-circle-right:hover{
            color: #0D6BB9 !important;
        }
        
           /*Bug  #8399  Opp :  Left Panel Filters UI Issue >  Lead Source/Type/Potential*/
                @media only screen and (min-height : 600px) and (max-height : 900px)  {

                #left_menu\:dt{
                    height: 0vh; 

                }



            }       

            @media only screen and (min-height : 901px) and (max-height : 1152px)  {

               #left_menu\:dt{
                    height: 0vh; 

                }


            }
            /*1920 * 1080*/
            @media only screen and (min-height : 1153px) and (max-height : 1440px)  {

               #left_menu\:dt{
                    height: 0vh; 

                }
            }

            @media only screen and (min-height : 1500px) and (max-height : 1640px)  {

               #left_menu\:dt{
                    height: 0vh; 

                }


            } 
    </style>
</ui:composition>
