<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: AddRelatedCompDlg.xhtml
// Author: Sarayu
//*********************************************************
* Copyright (c) 2016 Indea Design Systems, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <p:dialog widgetVar="reltdCompAddDlg" header="Add Relation to #{opportunities.oppCustomerName}"  modal="true" resizable="false" responsive="true"> 
        <h:form>
            <p:remoteCommand immediate="true" name="applyRelatedComp" actionListener="#{relatedComps.applyCompany(viewCompLookupService.selectedCompany)}" update=":addReltdCompForm:inputCompName :addReltdCompForm:inputCompId" />
        </h:form>
        
        <h:form id = "addReltdCompForm">
       
            <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-4" styleClass="box-primary no-border ui-fluid"
                         layout="grid" >
                <p:outputLabel value="Company" class="required"/>
<!--                <p:selectOneMenu filter="true" widgetVar="comp"  value="#{relatedComps.reltCompId}" style="width:280px" filterMatchMode="contains">      
                    <f:selectItem itemLabel="Select Company" itemValue="0"/>
                    <f:selectItems var="comp" value="#{relatedComps.loadCompsList()}"  itemLabel="#{comp.compName}" itemValue="#{comp.compId}"  />
                </p:selectOneMenu>  -->
                 <h:panelGroup class="ui-inputgroup" style="width:280px"  >
                    <p:inputText id="inputCompName" value="#{relatedComps.company.compName}" readonly="true"  />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      action="#{viewCompLookupService.listForRelated(companyServiceImpl.comp.compId,'applyRelatedComp')}"
                                      update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                      styleClass="btn-info btn-xs" />
                    <h:inputHidden id="inputCompId" value="#{relatedComps.reltCompId}"  />
                </h:panelGroup>
                <p:outputLabel value="Category" class="required"/>
                <p:selectOneMenu filter="true"  value="#{relatedComps.reltCategory}" style="width:280px">      
                    <f:selectItem itemLabel="Select Category" itemValue="0"/>
                    <f:selectItems var="reln" value="#{relatedComps.relationCatgList}"  itemLabel="#{reln.reltCategoryName}" itemValue="#{reln.reltCategory}"  />
                </p:selectOneMenu> 

                <p:outputLabel value="Role" />
                <p:selectOneMenu widgetVar="roleType"  value="#{relatedComps.reltRole}" style="width:280px">      
                    <f:selectItem itemLabel="#{custom.labels.get('IDS_SPECIFIER')}" itemValue="0"/>
                    <f:selectItem itemLabel="#{custom.labels.get('IDS_PURCHASER')}" itemValue="1"/>
                </p:selectOneMenu> 

            </p:panelGrid>
            <!--#3354 Fix Sonarqube issues-->
            <div style="text-align: center">
            <!--update=":reltdOppCompForm :frmOpp:growl"-->
<!--            Task#4349:PO Bulk Reconciliation  12/05/21 by harshithad-->
            <p:commandButton value="Save" class="btn btn-xs btn-success"  action="#{oppRelated.addRelatedComp()}" oncomplete="PF('reltdCompAddDlg').hide()" update=":reltdOppCompForm " />
            <p:spacer width="4"/>    
            <p:commandButton type="button" value="Cancel" class="btn btn-xs btn-warning"  onclick="PF('reltdCompAddDlg').hide()"/>
           </div><br/>
        </h:form>
    </p:dialog>
</ui:composition>

