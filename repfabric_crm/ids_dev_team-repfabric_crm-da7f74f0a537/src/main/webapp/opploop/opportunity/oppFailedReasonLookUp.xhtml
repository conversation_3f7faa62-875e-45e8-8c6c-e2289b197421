<!--Task #6393- Opportunity>Close Reason/Fail Reason by Vithin-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui">

    <p:dialog header="#{custom.labels.get('IDS_OPP')} Failed Reason Lookup" widgetVar="dlgOppFailedReason" width="450"  resizable="false" 
              modal="true" id='oppFailedRsnDl'>
    <!--//    Task #461: CRM-2505:  Opportunities > Opp Close Reason lookup-->

        <h:form id="dtOppFailedForm">
           
            <p:dataTable value="#{oppFailedReasonMstService.lookupFailedReason}" var="oppFailedRsn"
                         rowKey="#{oppFailedRsn.recId}" selectionMode="single"
                         id="oppFailedDtl" rowsPerPageTemplate="5,10,15" 
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         paginatorPosition="top" styleClass="dt-lookup"  
                         selection="#{oppFailedReasonMstService.selectedOppFailedRsnMst}"
                         widgetVar="oppFailedRsnDtl" style="margin-top:-5px"
                         >
                
                <p:ajax event="rowSelect" oncomplete="PF('dlgOppFailedReason').hide();"  listener="#{oppFailedReasonMstService.applyOppFailedReason(oppFailedReasonMstService.selectedOppFailedRsnMst)}" />


                <p:column filterBy="#{oppFailedRsn.oppFailedReason}"  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_OPP')} Failed Reason"  >
                    <h:outputText value="#{oppFailedRsn.oppFailedReason}"/>
                </p:column>
            </p:dataTable>
        </h:form>
        
      
    </p:dialog>
    
</ui:composition>