<ui:composition  xmlns="http://www.w3.org/1999/xhtml" 
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:f="http://java.sun.com/jsf/core" 
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                 template="#{layoutMB.template}">


    <h:head>
        <ui:include src="../../lookup/NewContactDlg.xhtml"/>
    </h:head>
    <!--#142 Page Title for All pages-->
    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>
            <!--#142-->
            #{custom.labels.get('IDS_OPP')} details
        </title>
    </ui:define>
    <ui:define name="metadeta">
        <f:metadata>
            <f:viewParam name="opid" value="#{opportunities.oppId}"/>
            <f:viewParam name="jid" value="#{opportunities.oppJobId}"/>
            <f:viewParam name="oppTabIndex" value="#{opportunities.oppTabIndex}"/>
            <f:viewParam name="cm" value="#{opportunities.comType}"/>
            <f:viewParam name="cmid" value="#{opportunities.compId}"/>
            <f:viewParam name="cnid" value="#{opportunities.contId}"/>
            <f:viewParam name="emailId" value="#{opportunities.oppEmlId}" />
            <f:viewParam name="inetMsgId" value="#{opportunities.oppEmlParentId}" />
            <f:viewParam name="clone" value="#{opportunities.cloneFlag}" />
            <f:viewParam name="projid" value="#{opportunities.projId}"/>
            <!--4267 CRM-4212: Salesforce CRMSync updates-->
            <f:viewParam name="dialogRedirect" value="#{opportunities.dialogRedirect}"/>
            <f:viewParam name="opitemid" value="#{opportunities.opitemid}"/>
            <!--#6008: Aurinko CRMSync: Admin Managed Mode-->
            <f:viewParam name="syncNow" value="#{oAuthBasedSyncService.syncNow}"/>
            <!--  #7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - to get clsstatus value -->
            <f:viewParam name="clsStatus" value="#{opportunities.urlCloseStatus}"/>
            <!--17-06-2022 8216 Direct Request: Aurinko CRMSync: Sync Status: Partially Synced Opps-->
            <f:viewParam name="tab" value="#{opportunities.tab}"/>
            <!--#6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/New-->
            <f:viewAction action="#{oAuthBasedSyncService.setRcName('rcUpdFrmAurOppCrt();')}" />
            <!--#4814: CRM-4406: *Required Fields not flagged- Deleted code for Cont Dtl-->
            <!--09-02-2022 7158 CRM-5294 Opportunity saved with no value - Mandatory Field-->
            <f:viewAction action="#{fieldsMstService.load('OPP_DTL,CONT_DTL')}" />
            <f:viewAction action="#{oppService.displayOpportunity()}" />
            <!--//10-07-2023 : #11622 : CRM-7134 Opportunities: Need to be able to link an opportunity to existing quote-->
            <f:viewAction action="#{opportunities.isLinkedToQuot(opportunities.oppId)}"/>
            <!--Feature #3454:   Opportunities > Watchers-->
            <!--//01-07-2022 : #8374 : CRM-5687: Opportunity > Line Item Status-->
            <f:viewAction action="#{oppService.listOppWatchers(opportunities.oppId,opportunities.oppPrincipal)}" />
            <!--//06-06-2022 : #8141 : CRM-5452  Sabrina Perez w/Mega Western Sales - OSR (Alex Padilla) is not getting notifications on co-->
            <f:viewAction action="#{comments.getUserWatcherList(opportunities.oppId)}" />
            <!--// 27-12-2022 : #10262 : Registrations: App Custom Feature-->
            <!--Bug #11349:ESCALATIONS CRM-7030   Design Registrations: Get duplicated if same number is used by harshithad on 06/06/23-->
            <f:viewAction action="#{registrationService.loadOppRegistration(opportunities.oppId,opportunities.cloneFlag)}"/>
            <f:viewAction action="#{oppService.listOthr()}" />
            <f:viewAction action="#{opportunities.loadOppLabels()}" />
            <!--<f:viewAction action="#{helpService.prepareHelpLinks('OPP_DTL')}"/>-->
            <!--2751 - CRM-1736: Autlogin for opps list page to learning.repfabric.com - please add - sharvani - 23-12-2019-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('OPP_DTL'))}" />
            <!--            15-07-2022 8286 PRIORITY CRM-5847:  Aurinko CRMSync: Convert to Opp - auto populate product id for opp line items-->
            <f:viewAction action="#{oppService.updateTabIndexValue(opportunities.tab)}"/>
            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('OPP_DTL')}" />
        </f:metadata>
    </ui:define>
    <ui:define name="title">
        <f:event type="preRenderView" listener="#{facesContext.externalContext.setResponseHeader('Cache-Control','no-cache, no-store')}" />
        <f:event listener="#{oppService.isPageAccessible}" type="preRenderView"/>
        <!--<ui:param name="title" value=" #{custom.labels.get('IDS_OPP')} List  "/>-->
        <div class="row">
            <div class="col-md-6">
                <p:outputLabel  value="#{opportunities.oppId == 0 ? 'New' : ''}"  style="font-weight: normal;" />
                #{custom.labels.get('IDS_OPP')}
                <p:outputLabel  value="#{opportunities.oppId > 0 ? 'Details' : ''}"  style="font-weight: normal;" />
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>
    <!--    <ui:define name="title">
            <div class="row">
                <div class="col-md-6">
                    <f:event type="preRenderView" listener="#{facesContext.externalContext.setResponseHeader('Cache-Control','no-cache, no-store')}" />
                    <f:event listener="#{oppService.isPageAccessible}" type="preRenderView"/>
    #{opportunities.pageHeader}
    <title>
        <p:outputLabel  value="#{opportunities.oppId == 0 ? 'New' : ''}"  style="font-weight: normal;" />
        Opportunity 
        <p:outputLabel  value="#{opportunities.oppId > 0 ? 'Details' : ''}"  style="font-weight: normal;" />
    </title>
</div>
#1451: Use common template in Opportunities, Funnel report, Roles, CRM Sync
<ui:include src="/help/Help.xhtml"/>
<div class="col-md-6" align="right">

</div>
</div>
</ui:define>-->

    <ui:define name="menu">
    </ui:define>

    <ui:define name="body">

        <ui:include src="RelatedOppComps.xhtml" />
        <ui:include src="AddRelatedCompDlg.xhtml" />
        <!--<ui:include src="tabs/dialogs/contacts/QuickContactCreateDlg.xhtml" />-->
        <ui:include src="../../lookup/CompanyLookupDlg.xhtml"/>
        <ui:include src="../../lookup/ContactLookupDlg.xhtml"/>
        <ui:include src="../../lookup/PartNumDlg.xhtml" />

        <!--           <ui:include src="../../lookup/NewContactDlg.xhtml"/>-->
        <div class="box box-info box-body">
            <div class="left-column">
                <ui:include src="Summary.xhtml" /> 
            </div>
            <div class="right-column">

                <!--#5884: Code Improvisation: Aurinko CRMSync > Code to check conflict-->
                <h:form rendered="#{opportunities.renderCrmSyncTab}" id="frmAurinko">
                    <!--#5280: Aurinko CRMSync updates for Web/DB-->
                    <!--#5695: Aurinko CRMSync: Opportunity Line Items-->
                    <!--#6251: Aurinko CRMSync: Configure Sync for Parent company-->
                    <p:remoteCommand autoRun="true" 
                                     action="#{oAuthBasedSyncService.checkAlreadyAuthenticated(oAuthBasedSyncService.linkedPrinciId, opportunities.syncUser.crmSyncSysId,opportunities.syncUser.crmsyncUserId)}" 
                                     />
                    <!--#6251: Aurinko CRMSync: Configure Sync for Parent company-->
                    <!--#5592 : Aurinko CRMSync: Opportunity > CRMSync tab updates-->
                    <p:remoteCommand autoRun="true" 
                                     action="#{oAuthBasedSyncService.oAuthInterface(oAuthBasedSyncService.linkedPrinciId)}" 
                                     update=":frmOpp:tabOpp:opCrmsyncAPI" oncomplete="PF('pollAurinkoConflictChk').start();"/>
                    <!--#5974: CRM-4819 URGENT * Repfabric / Pulling and entering data issues-->
                    <!--#5821: Aurinko CRMSync: Opp Line Items > Conflict/Error-->
                    <!--#6571: CRM-4819: Repfabric / Pulling and entering data issues-->
                    <p:poll id="pollAurChkCft" interval="10" widgetVar="pollAurinkoConflictChk" stop="#{oAuthBasedSyncService.stopChkCnf}" autoStart="false" 
                            listener="#{oAuthBasedSyncService.chkAurinkoOppCnflct()}" update=":frmAurinko:pollAurChkCft :frmOpp:tabOpp:dtLine"
                            oncomplete="PF('wvDtLine').filter();"/>

                    <!--                    <p:remoteCommand name="rcAssnWrnToLI" id="rcAssnWrnToLI" autoRun="true" immediate="true" 
                                             action="#{oAuthBasedSyncService.assignWarnToLI()}"
                                             oncomplete="PF('wvDtLine').filter();"
                                             update=":frmOpp:tabOpp:dtLine"/>-->

                    <!--17-06-2022 8216 Direct Request: Aurinko CRMSync: Sync Status: Partially Synced Opps-->
                    <!--                    <p:remoteCommand id="updateUrlAfterDone" name="updateUrlAfterDone" autoRun="true" actionListener="#{oppService.updateTabIndexValue(opportunities.tab)}"
                                                         update=":frmOpp:tabOpp" />-->
                </h:form>
                <h:form id="frmOpp"   >
                    <!--           PMS 2759:CRM-3643: generate new - too difficult to see if you already had generated a PO          -->
                    <p:remoteCommand id="rmOppCount" autoRun="true" name="rmOppCount" action="#{opportunities.fetchPOCount()}" update=":frmOppStatus" />
                    <!--//05-10-2022 : #9223 : CRMSYNC-53: Opportunity update on line item schedule update-->
                    <p:remoteCommand name="updateDateTimeOpp" autoRun="false" actionListener="#{oppItemSchedulesService.updateOppAndLineItem(opportunities.oppId,oppLineItems.recId)}" action="#{oppService.setUpdateInfo()}" update=":frmSummary"/>
                    <p:remoteCommand name="headerScheduleTimeOpp" autoRun="false" actionListener="#{oppItemSchedulesService.updateHeaderOpp(opportunities.oppId)}" action="#{oppService.setUpdateInfo()}" update=":frmSummary"/>
                    <!--#5592: Aurinko CRMSync: Opportunity > CRMSync tab updates-->
                    <h:inputHidden id="oppsiD" value="#{opportunities.oppId}"/>
                    <!--1382 CRM-3065: Manu - tek: Error when cloning an OPP??-->
                    <h:inputHidden id="cloneFlg" value="#{opportunities.cloneFlag}" />
                    <!--#2441 Related to CRM-1510: SELTEC Fwd: Issues with the new version-->
                    <!--update=":frmOpp:tabOpp:actionBtnGrp frmOpp:tabOpp:dtemail"--> 
                    <!--#5897: Code Improvisation: Opportunities > Load Emails only on selection of Emails tab-->
                    <p:remoteCommand name="loadActionBtn" actionListener="#{opportunities.findActionStatus(1)}" autoRun="false" update=":frmOpp:tabOpp:actionBtnGrp frmOpp:tabOpp:dtemail"/>
                    <!--process="@tt :frmOpp:tabOpp:pnlBASIC :frmOpp:tabOpp:pnlCRMSYNC :frmOpp:tabOpp:pnlOppCustFields"-->
                    <!--                    update=":frmSummary
                                                         :frmOpp:tabOpp:mailCust
                                                         :frmOpp:tabOpp:mailPrinci 
                                                         :frmOpp:tabOpp:dtceml
                                                         :frmOpp:tabOpp:dtc    
                                                         :frmOpp:tabOpp:opvalh
                                                         :frmOpp:growl
                                                         :frmOpp:tabOpp:pnlCRMSYNC
                                                         :frmOpp:btnRelated
                                                         :frmOpp:tabOpp:pnlOppCustFields" -->
                    <!--oncomplete="cmtLoad();loadActionBtn();loadOldonSave()"-->

<!--                    <p:commandButton actionListener="#{oppService.saveOrUpdate}"  
                                     title="#{opportunities.btnName} Basic details" 
                                     value="#{opportunities.btnName}" onclick="loadValues();"
                                     rendered="#{opportunities.cloneFlag==0}"
                                     id="btnsave"  oncomplete="loadActionBtn();loadOldonSave()"                                     
                                     disabled="#{opportunities.oppCloseStatus>0}" 
                                     update=":frmSummary"  
                                     class="btn btn-success btn-xs" />-->
                    <!--<p:spacer width="4" rendered="#{opportunities.cloneFlag==0}"/>-->
                    <!--process="@this :frmOpp:tabOpp:pnlBASIC :frmOpp:tabOpp:pnlCRMSYNC"-->
                    <!--  update=":frmSummary 
                                                         :frmOpp:tabOpp:mailCust
                                                         :frmOpp:tabOpp:mailPrinci 
                                                         :frmOpp:tabOpp:dtceml
                                                         :frmOpp:tabOpp:dtc    
                                                         :frmOpp:tabOpp:opvalh
                                                         :frmOpp:growl :frmOpp:btnRelated
                                                         :frmOpp:tabOpp:pnlCRMSYNC" -->
<!--                    <p:commandButton actionListener="#{oppService.cloneOpportunity(false)}"  
                                     title="#{opportunities.btnName} Basic details" 
                                     value="#{opportunities.btnName}" 
                                     id="btnclonesave" onclick="loadValues();"
                                     process="@this "
                                     rendered="#{opportunities.cloneFlag==1}"
                                     disabled="#{opportunities.oppCloseStatus>0}" 
                                     update=":frmSummary" 
                                     class="btn btn-primary btn-xs"  />
                   

-
                    #{opportunities.oppCloseStatus}==
                    #{opportunities.oppCloseStatVal}=   =#{opportunities.cloneFlag}==#{opportunities.oppCloseFlag}-
                   -->
                    <!--1219 CRM-2918: MANU-TEK:  WON  Opps that were closed and now re-opened?-->

                    <p:commandButton title="Edit Basic details" 
                                     value="Edit" onclick="loadValues();"
                                     rendered="#{opportunities.cloneFlag==0 and opportunities.oppCloseFlag==true}"
                                     id="btnedit" actionListener="#{opportunities.changeCloseStatus()}"
                                     process="@this" update=":frmOpp:btnsave :frmOpp:btnedit :frmOpp" 
                                     class="btn btn-info btn-xs" /> 
                    <!--oncomplete="cmtLoad();loadActionBtn();loadOldonSave()"--> 
                    <!--1354 CRM-3024: ALL INSTANCES: Opp Error??  "Leave page"-->
                    <!--1999 CRM-3393: Avoid double click - CRM-->
                    <!-- #7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - update close status form with note -->
                    <!--#8283: CRMSYNC-10: ledvancesfdc: closed status and reason parsing - poornima - commented panel note-->
                    <!--04-08-2022 8499 ESCALATION: CRM-5938  Do Not Select Icon over Selectable Fields in Opportunities-->
                    <!--bug #8648  ESCALATIONS: CRM-6000  Getting an error when editing an opp AFTER its been closed-->
                    <!--29-08-2022 8060 CRMSYNC-48   {CRMSync} When you change the principle in a linked opp, the link with the old opp is n-->
                    <!--// 27-12-2022 : #10262 : Registrations: App Custom Feature / Opp Registrations-->

                    <!--Bug #11349:ESCALATIONS CRM-7030   Design Registrations: Get duplicated if same number is used by harshithad on 06/06/23-->    
                    <!--//             #11426   CRM-6997   Jobs related: copy into opp not copying job details like name, parties, $s, reporting com-->
                    <!--22-09-2023 12132 CRM-7266   Opportunities: Value reduced to zero even with a line item-->
                    <p:commandButton actionListener="#{oppService.checkReuiredField(fieldsMstService,oppLineItems.listLineItems,0)}" widgetVar="oppSaveVar"

                                     title="#{opportunities.btnName} Basic details" 
                                     value="#{opportunities.btnName}" onclick="loadValues();PF('oppSaveVar').disable();" onsuccess="PF('oppSaveVar').enable()"
                                     rendered="#{opportunities.cloneFlag==0 and opportunities.oppCloseFlag==false}"
                                     id="btnsave"  
                                     process="@this :frmOpp:tabOpp:oppRegistrationPanel :frmOpp:tabOpp:pnlBASIC :frmOpp:tabOpp:pnlCRMSYNC :frmOpp:tabOpp:pnlOppCustFields"
                                     update=":frmSummary :frmOppStatus:btnclos :frmUpdateStatus:pnlCLOSE 
                                     :frmOpp:tabOpp:tblOthrList
                                     :frmSummary:ownerLbl
                                     :frmOpp
                                     :frmOpp:btnsave
                                     :frmOpp:btnedit
                                     :frmOpp:tabOpp:mailCust
                                     :frmOpp:tabOpp:mailPrinci 
                                     :frmOpp:tabOpp:dtceml
                                     :frmOpp:tabOpp:dtc    
                                     :frmOpp:tabOpp:opvalh
                                     :frmOpp:tabOpp:pnlCRMSYNC
                                     :frmOpp:btnRelated
                                     :frmOpp:tabOpp:pnlOppCustFields
                                     @(.required)
                                     @(.verify)" 
                                     class="btn btn-success btn-xs" />
                    <p:spacer width="4" rendered="#{opportunities.cloneFlag==0}"/>
                    <!--1382 CRM-3065: Manu - tek: Error when cloning an OPP??-->
                    <!-- #7193: Activity/Close Status Linking: Update Close Status/Activity on saving opportunity - poornima - update close status form with note -->
                    <!--#8283: CRMSYNC-10: ledvancesfdc: closed status and reason parsing - poornima - commented panel note-->
                    <p:commandButton actionListener="#{oppService.cloneOpportunity(false)}"  
                                     title="#{opportunities.btnName} Basic details" 
                                     value="#{opportunities.btnName}" widgetVar="cloneSaveBtn"
                                     id="btnclonesave" onclick="loadValues(); PF('cloneSaveBtn').disable();" oncomplete="loadOnOppValChange(); PF('cloneSaveBtn').enable();"
                                     process="@this :frmOpp:tabOpp:pnlBASIC :frmOpp:tabOpp:pnlCRMSYNC"
                                     rendered="#{opportunities.cloneFlag==1}"                                      
                                     update=":frmSummary :frmUpdateStatus:pnlCLOSE 
                                     :frmOpp:tabOpp:mailCust
                                     :frmOpp:tabOpp:mailPrinci 
                                     :frmOpp:tabOpp:dtceml
                                     :frmOpp:tabOpp:dtc    
                                     :frmOpp:tabOpp:opvalh
                                     :frmOpp:btnRelated
                                     :frmOpp:tabOpp:pnlCRMSYNC" 
                                     class="btn btn-primary btn-xs"    />
<!--disabled="#{opportunities.oppCloseStatVal>0}"-->
                    <p:spacer width="4"  rendered="#{opportunities.cloneFlag==1}"/>
                    <p:commandButton value="Cancel"  type="button"    class="btn btn-warning btn-xs" 
                                     onclick="window.open('OpportunityList.xhtml', '_self')" 
                                     >                        
                    </p:commandButton>
                    <p:spacer width="4"/>
                    <!--#6788: Aurinko CRMSync: Handle Unlink TS opp on RF opp deletion[added action removed type="button" adedd update]-->
                    <p:commandButton  rendered="#{opportunities.delRender}" actionListener="#{oAuthBasedSyncService.setOppDltMsg(opportunities.oppCrmId)}"
                                      value="Delete"   class="btn btn-danger btn-xs" immediate="true" update="cnfmOppDlt" oncomplete="PF('confirmation').show();"/>
                    <p:spacer width="4" rendered="#{opportunities.delRender}"/>
                    <!--#6788: Aurinko CRMSync: Handle Unlink TS opp on RF opp deletion[addedd id and changed message]-->
                    <p:confirmDialog id="cnfmOppDlt" header="Confirm delete"  width="400" global="false"
                                     message="#{oAuthBasedSyncService.deleteOppMsg}" 
                                     widgetVar="confirmation" >
                        <div class="div-center">
                            <p:commandButton value="Delete" actionListener="#{oppService.delete()}"
                                             class="btn btn-danger btn-xs" process="@this"      />
                            <p:spacer width="4"/>
                            <p:commandButton value="Cancel" class="btn btn-warning btn-xs" onclick="PF('confirmation').hide()" 
                                             type="button" />
                        </div>
                    </p:confirmDialog>
                    <!--Bug #11349:ESCALATIONS CRM-7030   Design Registrations: Get duplicated if same number is used by harshithad on 06/06/23-->
                    <p:confirmDialog id="cnfrmDupeRegNum" header="Confirmation dialogue"  width="400" global="false"
                                     message="Same registration number has been already assigned in another  #{custom.labels.get('IDS_OPP')}. Are you sure to proceed?" 
                                     widgetVar="confirmDupeRegNo" >
                        <div class="div-center">
                            <p:commandButton value="Yes" actionListener="#{oppService.saveOrUpdate(1)}"
                                             class="btn btn-danger btn-xs" process="@this"  oncomplete="PF('confirmDupeRegNo').hide()"    />
                            <p:spacer width="4"/>
                            <p:commandButton value="No" class="btn btn-warning btn-xs" onclick="PF('confirmDupeRegNo').hide()" 
                                             type="button" />
                        </div>
                    </p:confirmDialog>
                    <p:commandButton  
                        value="History" oncomplete="PF('dlgHistory').show()"
                        class="btn btn-primary btn-xs" action="#{oppHistory.fetchOppHistory(opportunities.oppId)}" update=":historyForm" />
                    <p:spacer width="4"  />
                    <p:commandButton  rendered="#{opportunities.cloneFlag eq 0 and opportunities.delRender}"
                                      value="Clone" class="btn btn-primary btn-xs"  onclick="cloneOpp()"/>
                    <p:spacer width="4"   rendered="#{opportunities.cloneFlag eq 0 and opportunities.delRender}"/>
                    <!-- process="@this :frmOpp:tabOpp:pnlBASIC" title="Related Companies"
                                                         update=":reltdOppCompForm :frmOpp:btnRelated" -->
                    <p:commandButton actionListener="#{oppRelated.loadOppCompsData}"  
                                     value="Related" rendered="#{opportunities.oppId!=0 and opportunities.cloneFlag!=1}"
                                     disabled="#{opportunities.btnReltFlag}"
                                     id="btnRelated"  oncomplete="PF('reltdOppCompDlg').show();"
                                     process="@this" title="Related Companies"
                                     update=":reltdOppCompForm :frmOpp:btnRelated" 
                                     styleClass="#{oppRelated.insertCount>0?'reltd_btn_update':''} btn btn-primary btn-xs" />
                    <p:spacer width="4" rendered="#{opportunities.oppId!=0 and opportunities.cloneFlag!=1}"/>
                    <!--1000 CRM-2802: 3.0 can't link opp to Job-->
                    <!--#2543: CRM-3371: Fwd: RE: Repfabric- Sales Outsource Solutions-->
                    <p:commandButton  rendered="#{opportunities.oppId > 0 and opportunities.oppJobId eq 0 and opportunities.cloneFlag!=1 and opportunities.customParamJobFlag!=0}"
                                      value="Link to #{custom.labels.get('IDS_JOB')}" title="Link to #{custom.labels.get('IDS_JOB')}"
                                      class="btn btn-primary btn-xs"
                                      actionListener="#{jobOpps.setJobOppId(opportunities.oppId)}" action="#{jobs.fetchJobsListForOpps()}" oncomplete="PF('dlgJobList').show()"
                                      update=":frmJobList"  />
                    <!--update=":frmJobList "-->
                    <p:spacer width="4"  rendered="#{opportunities.oppId > 0 and opportunities.oppJobId eq 0 and opportunities.cloneFlag!=1 and opportunities.customParamJobFlag!=0}"/>
                    <!--Bug #1346:CRM-3012:3.0 hid project or job button-->
                    <!--#2543: CRM-3371: Fwd: RE: Repfabric- Sales Outsource Solutions--> 
                    <p:commandButton  rendered="#{opportunities.oppId > 0 and opportunities.oppJobId > 0 and opportunities.cloneFlag!=1 and opportunities.customParamJobFlag!=0 and  opportunities.isPageAccessibleJobs() }"
                                      class="btn btn-primary btn-xs" title="Linked #{custom.labels.get('IDS_JOB')}"
                                      value="Linked #{custom.labels.get('IDS_JOB')}"  onclick="window.open('../jobs/JobsView.xhtml?id=#{opportunities.oppJobId}', '_self');"
                                      />
                    <p:spacer width="4px"  rendered="#{opportunities.oppId > 0 and opportunities.oppJobId > 0 and opportunities.cloneFlag!=1 and opportunities.customParamJobFlag!=0}"/>
                    <!--#3142 CRM-1967: Opportunities: Victory/Golden: when clone inside project auto adds to project-->
                    <!--#169 :CRM-2304 :new projects cannot be created from promotions to opps-->

                    <!--Bug #1346:CRM-3012:3.0 hid project or job button-->
                    <p:commandButton  action="#{projects.fetchProjectListForOppLinking(opportunities.oppCustomer)}" value="Link to #{custom.labels.get('IDS_OPP_PROJ')}" class="btn btn-primary btn-xs"   
                                      rendered="#{opportunities.oppId > 0 and projOpps.projId eq 0 and opportunities.cloneFlag ne 1 and opportunities.isPageAccessibleProjects()}"
                                      oncomplete="PF('dlgProjectList').show();"   update=":frmProjectList"
                                      actionListener="#{projects.getDefaultValue(opportunities.oppCustomer,opportunities.oppCustomerName,opportunities.oppCustProgram,opportunities.oppValue ,opportunities.insDate)}"
                                      />

                    <p:spacer width="4px"   rendered="#{opportunities.oppId > 0 and projOpps.projId eq 0 and opportunities.cloneFlag ne 1}"/>


                    <!--Bug #1346:CRM-3012:3.0 hid project or job button-->
                    <p:commandButton   value="View #{custom.labels.get('IDS_OPP_PROJ')}" class="btn btn-primary btn-xs"   
                                       rendered="#{opportunities.oppId > 0 and projOpps.projId > 0 and opportunities.cloneFlag ne 1 and opportunities.isPageAccessibleProjects()}"
                                       onclick="window.open('../projects/ProjectView.xhtml?id=#{projOpps.projId}', '_self');"     action="#{projOpps.cloneClear()}" update=""/>

                    <!--                    //PMS:2623
                                        //Seema - 07/12/2020-->
                    <p:spacer width="4px"   rendered="#{opportunities.oppId > 0 and projOpps.projId > 0 and opportunities.cloneFlag ne 1}"/>
                    <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
                    <!--                    <p:commandButton   value="Linked Documents" class="btn btn-primary btn-xs"   
                                                           rendered="#{opportunities.oppId > 0 and opportunities.cloneFlag ne 1}"
                                                           actionListener="#{oppLinkedDocumentsService.loadOppLinkeddocuments(opportunities.oppId)}"
                                                           oncomplete="PF('dlgOppLinkedDocuments').show()"
                                                           />-->
                    <!--//02-02-2022: #7387 : CRM-5404: Opportunity > Schedules-->
                    <p:spacer width="4px"   rendered="#{opportunities.oppId > 0 and opportunities.cloneFlag ne 1}"/>
                    <p:commandButton   value="Schedules" class="btn btn-primary btn-xs" action="#{oppScheduleService.getMaxValue()}"   
                                       rendered="#{opportunities.oppId > 0 and opportunities.cloneFlag ne 1}" update=":frmOppSchedules"/>
                    <!--//10-07-2023 : #11622 : CRM-7134 Opportunities: Need to be able to link an opportunity to existing quote-->
                    <p:spacer width="4px"   rendered="#{opportunities.oppId > 0 and opportunities.cloneFlag ne 1}"/>
                    <p:commandButton id="linkToQuotBtn" onstart="PF('quotLinkToOppListTable').clearFilters()"   value="Link to #{custom.labels.get('IDS_QUOTES')}" class="btn btn-primary btn-xs"  actionListener="#{opportunities.loadQuotes(opportunities.oppPrincipal)}"
                                     rendered="#{(opportunities.oppId > 0 and opportunities.cloneFlag ne 1) and !opportunities.linkedQuotBol}" update=":frmQuotLinkToOpp"/>

                    <style>
                        .button_top.ui-button-text-only .ui-button-text{
                            padding-left: 1.8em;
                        }

                        .reltd_btn{
                            font-weight: normal;
                            font-size: 14px;
                            float: right;
                        }

                        .reltd_btn_update{
                            color: blue;
                        }
                        .ui-widget-content {
                            border: #d1d4de !important;

                        }
                    </style>

                    <script>
                        function cloneOpp() {
                            var url = "#{request.contextPath}/opploop/opportunity/OpportunityView.xhtml?opid=#{opportunities.oppId}&amp;clone=1";
                            window.location = url;
                        }

                        function loadValues() {
                            oldFormData = loadAllValues();
//                    #294625:seltec:firefox challenge lvg page on new opp
                            if (#{opportunities.oppId} === 0) {
                                newFormData = oldFormData;
                            }
                        }

                    </script>


                    <p:confirmDialog header="Confirm Cancel" message="Are you sure, Changes will not be saved" widgetVar="wcan">
                        <p:commandButton class="btn btn-xs btn-success" value="Yes" type="button" onclick="window.open('OpportunityList.xhtml', '_self')" />
                        <p:commandButton class="btn btn-xs btn-warning" value="No" type="button" onclick="PF('wcan').hide()" />
                    </p:confirmDialog>


                    <div style="width: 20px;height: 20px;display: inline-block;margin-left: 10px;">
                        <!--<ui:include src="/opploop/opportunity/ajaxStatus.xhtml"  />-->
                    </div>
                    <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
                    <!--27-11-2024 15019 Incidental finding:Opportunity> Linked Documents Not Displaying in "Linked Documents" Tab-->
                    <p:tabView id="tabOpp" activeIndex="#{opportunities.oppTabIndex}" widgetVar="tabw">
                        <p:ajax event="tabChange" listener="#{tabController.onOppTabChange}"/>

                        <p:tab title="Basic"   >
                            <ui:include src="tabs/Details.xhtml" /> 

                            <!--<ui:include src="lookup/ContactLookupDlg.xhtml"/>-->
                            <ui:include src="tabs/dialogs/company/dlgCompCust.xhtml" />
                            <ui:include src="tabs/dialogs/company/dlgCompPrin.xhtml" />
                            <ui:include src="tabs/dialogs/company/dlgCompDist.xhtml" />
                            <ui:include src="tabs/dialogs/contacts/dlgContCust.xhtml" />
                            <ui:include src="tabs/dialogs/contacts/dlgContPrin.xhtml" />
                            <ui:include src="tabs/dialogs/contacts/dlgContDist.xhtml" /> 
                            <!--1638 Opportunities > Other Parties - Contacts-->
                            <ui:include src="tabs/dialogs/contacts/dlgContAll.xhtml" />
                            <!--PMS 2873: Code Optimization - Company Details screen-->
                            <!--                            <ui:include src="tabs/dialogs/company/dlgCompEndUser.xhtml" />-->
                            <ui:include src="tabs/dialogs/company/dlgSalesTeam.xhtml" />
                            <!--PMS 2873: Code Optimization - Company Details screen-->
                            <!--                            <ui:include src="tabs/dialogs/company/create_comp.xhtml"/>-->
                        </p:tab>

<!--                        <p:tab title="#{custom.labels.get('IDS_CUSTOM_FIELDS')}"  disabled="#{opportunities.tabDisabled or opportunities.cloneFlag == 1}" >
                            <ui:include src="../customfields/OppCustomFields.xhtml"/>
                        </p:tab>-->

                        <!--                        #5897: Code Improvisation: Opportunities > Load Emails only on selection of Emails tab-->
                        <p:tab title="Emails"   disabled="#{opportunities.tabDisabled or opportunities.cloneFlag == 1}" id="tabEmails" >
                            <ui:include src="tabs/Emails.xhtml" />
                        </p:tab>

                       


                        <p:tab title="Attachments"  disabled="#{opportunities.tabDisabled or opportunities.cloneFlag == 1}" >
                            <ui:include src="tabs/Attachments.xhtml" />
                        </p:tab>

                        <!--Aurinko CRMSync: Opp Line Items > Conflict/Error-->
                        <!--#5974: CRM-4819 URGENT * Repfabric / Pulling and entering data issues-->
                        <p:tab id="tabLineItems" disabled="#{opportunities.tabDisabled or opportunities.cloneFlag == 1}">
                            <!--#5821: Aurinko CRMSync: Opp Line Items > Conflict/Error-->
                            <f:facet name="title" id="aurinkoOppLIWar">
                                <h:outputText value="#{custom.labels.get('IDS_LINE_ITEMS')}"/>
                                <h:panelGroup id="warnLI" style=" margin-left: 5px" >
                                    <i class="fa fa-warning" style="font-size: 1.5rem; color: red; #{oAuthBasedSyncService.aurinkoWarnOnLI ? 'width:100%;' : 'display: none'};"></i>
                                </h:panelGroup>

                            </f:facet>

                            <ui:include src="tabs/LineItems.xhtml" />
                            <ui:include src="tabs/dialogs/lineItem/AddItem.xhtml" /> 
                            <ui:include src="tabs/dialogs/lineItem/tblSamples.xhtml" />
                            <ui:include src="tabs/dialogs/lineItem/tblQuotes.xhtml" />
                            <ui:include src="tabs/dialogs/lineItem/tblDesignwin.xhtml" />
                            <ui:include src="tabs/dialogs/lineItem/tblProdReg.xhtml" />
                            <ui:include src="tabs/dialogs/lineItem/tblAccReg.xhtml" /> 
                            <ui:include src="../enquiry/dialog/company/dlgCompCem.xhtml" />
                            <ui:include src="../enquiry/dialog/contacts/dlgContCem.xhtml" /> 
                            <!--#1709 Salesforce > Opportunity Line items sync-->
                            <ui:include src="tabs/dialogs/crmsync/TargetOppLineItemLookup.xhtml" />
                        </p:tab>

                        <p:tab title="Contacts"  disabled="#{opportunities.tabDisabled or opportunities.cloneFlag == 1}" >
                            <ui:include src="tabs/Contacts.xhtml" />
                        </p:tab>

                        <p:tab title="#{custom.labels.get('IDS_COMMENTS')}"  disabled="#{opportunities.tabDisabled or opportunities.cloneFlag == 1}" >
                            <ui:include src="tabs/Comments.xhtml" />
                        </p:tab>
                        
                        

                        <!--#5974: CRM-4819 URGENT * Repfabric / Pulling and entering data issues-->
                        <p:tab id="tabCrmsync"  disabled="#{opportunities.tabDisabled or opportunities.cloneFlag == 1}" rendered="#{opportunities.renderCrmSyncTab}">

                            <!--#5592: Aurinko CRMSync: Opportunity > CRMSync tab updates-->
                            <f:facet name="title">
                                <h:outputText value="CRMSync"/>
                                <!--#5758: Aurinko CRMSync:  Changes to call to Sync API-->
                                <!--#5821: Aurinko CRMSync: Opp Line Items > Conflict/Error-->
                                <h:panelGroup id="iconWarningSyncSnflct" style=" margin-left: 5px" >
                                    <i class="fa fa-warning" style="font-size: 1.5rem; color: red; #{((not empty oAuthBasedSyncService.conflictErrors) or (oAuthBasedSyncService.isaurinkoOppConflict)) ? 'width:100%;' : 'display: none'};"></i>
                                </h:panelGroup>
                            </f:facet>

                            <ui:include src="tabs/dialogs/crmsync/TargetOppLookup.xhtml" />
                            <!--<ui:include src="tabs/dialogs/crmsync/TargetLineItemLookup.xhtml" />-->

                            <ui:include src="tabs/CRMSync.xhtml" />
                        </p:tab>
                         <!--- PMS:8668 : 12-11-2024 :  Task:14863 added new feature of Journal Activity on the Opportunites Module --> 
                         <p:tab id="tabAJ"  disabled="#{opportunities.tabDisabled or opportunities.cloneFlag == 1}" title="#{custom.labels.get('IDS_ACT_JOURNALS')}">  
                             <ui:include src="tabs/JournalOppourtunity.xhtml"/>
                         </p:tab>
                        
                        
                        <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
                        <p:tab id="oppLinkedDocsTab" title="Linked Docs"  disabled="#{opportunities.tabDisabled or opportunities.cloneFlag == 1}">
                            <p:remoteCommand  autoRun="false" name="loadOppLinkedDoc" actionListener="#{oppLinkedDocumentsService.loadOppLinkeddocuments(opportunities.oppId)}" 
                                              update=":frmOpp:tabOpp:oppLinkedDocs" oncomplete="PF('inProgressDlg1').hide()"/>
                            <p:dataTable id="oppLinkedDocs" value="#{oppLinkedDocumentsService.oppLinkedDocLst}" 
                                         var="oppLnkdDoc"
                                         rows="10" 
                                         selectionMode="single" 
                                         filterEvent="keyup" 
                                         draggableColumns="true"
                                         emptyMessage="No Linked Documents found."  
                                         paginator="true" 
                                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                         paginatorAlwaysVisible="false" 
                                         class="tblmain"
                                         rowKey="#{oppLnkdDoc.recId}" resizableColumns="true"
                                         >
                                <p:column headerText="Type" filterBy="#{oppLnkdDoc.docType}" filterMatchMode="contains" sortBy="#{oppLnkdDoc.docType}">
                                    <p:commandLink rendered="#{oppLnkdDoc.docType eq 'Quote'}"  value="#{oppLnkdDoc.docType}"
                                                   onclick="window.open('../../opploop/quotes/NewQuote.xhtml?recId=#{oppLnkdDoc.docId}', '_blank');" 
                                                   >
                                    </p:commandLink>

                                    <p:commandLink rendered="#{oppLnkdDoc.docType eq 'Job'}"  value="#{oppLnkdDoc.docType}"
                                                   onclick="window.open('../../opploop/jobs/JobsView.xhtml?id=#{oppLnkdDoc.docId}', '_blank');"
                                                   >
                                    </p:commandLink>
                                    <p:commandLink rendered="#{oppLnkdDoc.docType eq 'Project'}"  value="#{oppLnkdDoc.docType}"
                                                   onclick="window.open('../../opploop/projects/ProjectView.xhtml?id=#{oppLnkdDoc.docId}', '_blank');"
                                                   >
                                    </p:commandLink>
                                    <!--//Feature #3030:CRM-3746: cronan: make purchase order" menu relabelable to "Sales Order" IDS_PURCHASE_ORDERS-->
                                    <p:commandLink rendered="#{oppLnkdDoc.docType eq custom.labels.get('IDS_PURCHASE_ORDERS')}"  value="#{oppLnkdDoc.docType}"
                                                   onclick="window.open('../../opploop/po/PoDetails.xhtml?id=#{oppLnkdDoc.docId}', '_blank');"
                                                   >
                                    </p:commandLink>
                                    <!--//20-11-2023 : #12558 : CRM-7205  Linked documents: inconsistencies all over the place-->
                                    <p:commandLink rendered="#{oppLnkdDoc.docType eq 'Invoice'}"  value="#{oppLnkdDoc.docType}"
                                                   onclick="window.open('../../datamanagement/commtransactions/CommtransDetails.xhtml?id=#{oppLnkdDoc.docId}', '_blank');"

                                                   >
                                    </p:commandLink>
                                    <p:commandLink rendered="#{oppLnkdDoc.docType eq 'Sample'}"  value="#{oppLnkdDoc.docType}"
                                                   onclick="window.open('../../opploop/samples/Sample.xhtml?recId=#{oppLnkdDoc.docId}', '_blank');"

                                                   >
                                    </p:commandLink>

                                </p:column>
                                <p:column headerText="Doc. No." filterBy="#{oppLnkdDoc.docNo}" filterMatchMode="contains" sortBy="#{oppLnkdDoc.docNo}">
                                    <h:outputText value="#{oppLnkdDoc.docNo}" />
                                </p:column>
                                <p:column headerText="Doc. Date" filterBy="#{oppLinkedDocumentsService.getFormattedDate(oppLnkdDoc.docDate, 'da')}" filterMatchMode="contains" sortBy="#{oppLnkdDoc.docDate}">
                                    <h:outputText value="#{oppLinkedDocumentsService.getFormattedDate(oppLnkdDoc.docDate, 'da')}" />
                                </p:column>
                                <p:column headerText="Topic" filterBy="#{oppLnkdDoc.docTopic}" filterMatchMode="contains" sortBy="#{oppLnkdDoc.docTopic}">
                                    <h:outputText value="#{oppLnkdDoc.docTopic}" />
                                </p:column>
                                <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{oppLnkdDoc.docCustomer}" filterMatchMode="contains" sortBy="#{oppLnkdDoc.docCustomer}">
                                    <h:outputText value="#{oppLnkdDoc.docCustomer}" />
                                </p:column>
                                <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{oppLnkdDoc.docPrinci}" filterMatchMode="contains" sortBy="#{oppLnkdDoc.docPrinci}">
                                    <h:outputText value="#{oppLnkdDoc.docPrinci}" />
                                </p:column>
                                <p:column headerText="Value" filterBy="#{oppLnkdDoc.docValue}" filterMatchMode="contains" sortBy="#{oppLnkdDoc.docValue}">
                                    <h:outputText value="#{oppLnkdDoc.docValue}" style="float: right" />
                                </p:column>
                                <p:column headerText="Linked Doc" style="text-align: center!important;">
                                    <p:commandButton style="height:25px;width:25px;" 
                                                     styleClass="btn-primary  btn-xs" title="Linked Doc" icon="fa fa-eye"
                                                     actionListener="#{linkedDocService.loadQuotLinkedData(oppLnkdDoc.docType,oppLnkdDoc.docId)}"
                                                     action="#{linkedDocService.loadHeader(oppLnkdDoc.docType,oppLnkdDoc.docNo)}"
                                                     update="linkedDocumentForm:linkedDocumentDialogue" 
                                                     oncomplete="PF('linkedDocumentDlgVar').show();"
                                                     ></p:commandButton>
                                </p:column>
                            </p:dataTable>
                        </p:tab>

                    </p:tabView>

                </h:form>
                <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
                <ui:include src="../quotes/dialog/LinkedDocDLg.xhtml"/>
                <!--//10-07-2023 : #11622 : CRM-7134 Opportunities: Need to be able to link an opportunity to existing quote-->
                <ui:include src="tabs/dialogs/LinkQuotesDlg.xhtml" />
                
                <!--//08-08-2022 : #8599 : CRM-5978: Opportunity Line Items: Aurora: keep or clear part number pricing & other fields-->

                <p:dialog id="dlgChangingpart" width="450" header="Confirmation" widgetVar="changingpartdlg" responsive="true"  
                          modal="true" resizable="false" closable="false">  
                    <h:form id="frmPartNumber">
                        <p:outputLabel style="font-weight-bold" value="#{oppLineItems.header}"/>
                        <p:spacer width='4px' />
                        <div class="div-center" >
                            <!--// 21-09-2022 : #9096 : ESCALATIONS CRM-6156  Part number changes and quotes-->
                            <p:commandButton onstart="PF('btnKeepPartWidget').disable();" oncomplete="PF('changingpartdlg').hide();PF('btnKeepPartWidget').enable();"
                                             value="Keep" widgetVar="btnKeepPartWidget" actionListener="#{oppLineItems.keepPartNumber()}" 
                                             styleClass="btn btn-success  btn-xs" update=":frmOpp:tabOpp:tabLine:partno" >
                            </p:commandButton> 
                            <p:spacer width="4px"/>
                            <!--// 21-09-2022 : #9096 : ESCALATIONS CRM-6156  Part number changes and quotes-->
                            <p:commandButton  value="Replace" process="@this" onstart="PF('btnErasePartWidget').disable();"
                                              widgetVar="btnErasePartWidget" oncomplete="PF('btnErasePartWidget').enable();"
                                              styleClass="btn btn-warning btn-xs" actionListener="#{oppLineItems.erasePartNumberData()}"
                                              update=":frmOpp:tabOpp:tabLine:partno :frmOpp:tabOpp:tabLine:lres  :frmOpp:tabOpp:tabLine:ldes :frmOpp:tabOpp:tabLine:lpc  :frmOpp:tabOpp:tabLine:lcost  :frmOpp:tabOpp:tabLine:lCommRate">
                            </p:commandButton>
                        </div>
                    </h:form>
                </p:dialog>


            </div>
        </div> 
        <!--Bug #8076  Summernote issues from #7814-->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote.js"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css" />
        <!--<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.4.1/js/bootstrap.min.js"></script>-->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote.css" rel="stylesheet"></link>

        <!--<ui:include src="tabs/dialogs/contacts/create_cont.xhtml"/>-->
        <ui:include src="../activityactions/OppActActionDlg.xhtml" />
        <ui:include src="../activityactions/OppActActionSettingsDlg.xhtml" />
        <ui:include src="../../config/subtables/activityactions/OppStaticEmailParamsDlg.xhtml" />
        <ui:include src="../activityactions/OppPitchEmailDlg.xhtml" />
        <ui:include src="../activityactions/InsertPitchDlg.xhtml" />
        <ui:include src="tabs/dialogs/history/dlgHistory.xhtml"/>

        <ui:include src="tabs/dialogs/copyinto/Quotes.xhtml" />
        <ui:include src="tabs/dialogs/copyinto/Samples.xhtml" />
        <ui:include src="tabs/dialogs/copyinto/Designwin.xhtml" />
        <ui:include src="tabs/dialogs/copyinto/Registration.xhtml" />
        <ui:include src="tabs/dialogs/copyinto/AccountReg.xhtml" />

        <ui:include src="tabs/dialogs/jobs/JobDialog.xhtml" />
        <ui:include src="tabs/dialogs/projects/projectDialog.xhtml" />
        <ui:include src="tabs/dialogs/jobs/AddJob.xhtml" /> 


        <!--#169 :CRM-2304 :new projects cannot be created from promotions to opps-->
        <ui:include src="tabs/dialogs/projects/LinkToProject.xhtml" /> 
        <!--        #4760 Line Item Forecast: Opp Line Item> schedules tab -->
        <ui:include src="tabs/dialogs/lineItem/SchedulesItemDetails.xhtml" />

        <!--#5592: Aurinko CRMSync: Opportunity > CRMSync tab updates-->
        <ui:include src="tabs/dialogs/crmsync/ChooseAsMaster.xhtml" />
        <ui:include src="tabs/dialogs/crmsync/AurinkoOppLookup.xhtml" />
        <!--#5695: Aurinko CRMSync: Opportunity Line Items-->
        <ui:include src="tabs/dialogs/crmsync/AurinkoOppLILookup.xhtml" />

        <!--#5821: Aurinko CRMSync: Opp Line Items > Conflict/Error-->         
        <ui:include src="tabs/dialogs/crmsync/AurinkoOppLIErrors.xhtml" />
        <!--#5881: Aurinko CRMSync:  Opportunity Creation-->
        <ui:include src="tabs/dialogs/crmsync/AurinkoOppCreation.xhtml" />
        <ui:include src="tabs/dialogs/crmsync/AurinkoCompLookup.xhtml" />
        <!--#6081: Aurinko CRM Sync: Line Item Creation-->
        <ui:include src="tabs/dialogs/crmsync/AurinkoPriceBookLookup.xhtml" />

        <!--        //PMS:2623
                //Seema - 07/12/2020-->

        <ui:include src="tabs/dialogs/LinkedDocs/OppLinkedDocumentsDlg.xhtml"/>

        <!--//02-02-2022: #7387 : CRM-5404: Opportunity > Schedules-->
        <ui:include src="tabs/dialogs/Schedules/OppSchedules.xhtml"/>

        <!--#6590: Aurinko CRMSync: Admin Managed Mode - Updates on Sync/New-->
        <ui:include src="../../resources/dialogs/DlgFetchAndProc.xhtml"/>

        <!--20-04-2022 7595 CRM-5488: CRMsync: new acct creation in target system-->        
        <ui:include src="../../crmsync/dialogs/NewAurinkoCompany.xhtml"/>      

        <!--20-07-2022 8286 PRIORITY CRM-5847:  Aurinko CRMSync: Convert to Opp - auto populate product id for opp line items-->
        <ui:include src="../../crmsync/dialogs/CheckOppSynced.xhtml"/>
        
        <!--- PMS:8668 : 12-11-2024 :  Task:14863 added new feature of Journal Activity on the Opportunites Module --> 
         <ui:include src="../../Journal/JournalActivityExport.xhtml"/>

        <p:dialog widgetVar="inProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif" id="loadingImg"   />
                <p:spacer width="5" />
                <p:outputLabel value="Please wait... Actions are being executed" />
                <br /><br />
            </p:outputPanel>
        </p:dialog>
        <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
        <p:dialog widgetVar="inProgressDlg1" closable="false" modal="true" header="Message" resizable="false" >
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif"  />
                <p:spacer width="5" />
                <p:outputLabel value="Please wait..." />
                <br /><br />
            </p:outputPanel>
        </p:dialog>

        <script>
                                                       var oldFormData, newFormData;
                                                       var oldOppVal, newOppVal;
//            1354 CRM-3024: ALL INSTANCES: Opp Error??  "Leave page"
                                                       var oppValChanged;
                                                       $(window).on('beforeunload', function () {

                                                           alert("hii");
                                                           newFormData = loadAllValues();
                                                           if (newFormData !== oldFormData) {
                                                               return "Changes have not been saved";
                                                               //          alert("hello");
                                                           }
                                                           var oppaid = $('#frmOpp\\:oppsiD').val();
//                1382 CRM-3065: Manu - tek: Error when cloning an OPP??
                                                           var clnid = $('#frmOpp\\:cloneFlg').val();
                                                           if (clnid === '0') {
                                                               if (oppaid !== '0') {
                                                                   newOppVal = loadOppValue();
                                                                   //                     console.log(oppValChanged+"oldOppVal    "+oldOppVal+"newOppVal    "+newOppVal);
                                                                   if (oppValChanged !== undefined) {
                                                                       if (oldOppVal !== undefined) {
                                                                           if (newOppVal !== oldOppVal) {
                                                                               return "Changes have not been saved";
                                                                               //          alert("hello");
                                                                           }
                                                                       }
                                                                   }
                                                               }
                                                           }
                                                       });

                                                       function loadOldonSave() {
                                                           oldFormData = loadAllValues();
//                19-10-2022 9387 ESCALATIONS CRM-6193   Reload Site message when updating Value Field in Opps
                                                           loadOnOppValChange();
                                                       }

                                                       function loadOnOppValChange() {
                                                           oldOppVal = loadOppValue();
                                                       }
//1354 CRM-3024: ALL INSTANCES: Opp Error??  "Leave page"
                                                       window.onload = function () {
                                                           oldFormData = loadAllValues();
                                                           pageLoadOppValue();
                                                       };
//           8186   :14/06/2022: PF 7 related bugs: Opportunities
                                                       function loadingPage() {
                                                           window.location.reload();
                                                       }
                                                       function loadAllValues() {

                                                           //this function for checking unsaved data, onload it store all values
                                                           var oldval = $('#frmOpp\\:tabOpp\\:oppCustomerName').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:inpTxtPrinciName').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:inpTxtDistriName').val();

                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:oppCustomerContactName').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:oppPrincipalContactName').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:oppDistriContactName').val();

                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:inpTxtProgram').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:inpTxtNextStep').val();
                                                           // 27-12-2022 : #10262 : Registrations: App Custom Feature / Opp Registrations
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:inpTxtRegNum').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:regiStatus_input').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:regiApproveDate_input').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:regiApprovedBy').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:regiNotes').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:regiExpiryDate_input').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:registrationLevel').val();

                                                           oldval = oldval + PF('wopselact').getSelectedValue();
                                                           oldval = oldval + PF('wopstat').getSelectedValue();
                                                           oldval = oldval + PF('wopfollow').getDate();

                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:spinnerPriority_input').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:spinnerPotential_input').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:inpTxtEau').val();

//                oldval = oldval + $('#frmOpp\\:tabOpp\\:inpTxtValue').val();
                                                           oldval = oldval + PF('wopproto').getDate();
                                                           oldval = oldval + PF('wopprod').getDate();

                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:inpTxtComp1').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:inpTxtComp2').val();

                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:inpTxtAreaDescr').val();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:inpTxtAreaRptCmts').val();


                                                           oldval = oldval + PF('wopOppTypes').getSelectedValue();
                                                           oldval = oldval + PF('wopOppLeadSrc').getSelectedValue();
                                                           oldval = oldval + $('#frmOpp\\:tabOpp\\:inpTxtOppWatcher').val();
// Feature #3573:CRM-3970: TWDev: watcher testing: can leave the opp screen without saving data
                                                           console.log("oldval::::" + $('#frmOpp\\:tabOpp\\:inpTxtOppWatcher').val());
//              
//                #3840 Opportunities > Updates to Opp Parties
//           #3840     oldval = oldval + $('#frmOpp\\:tabOpp\\:tblOthrList\\:'+parseInt($('#frmOpp\\:tabOpp\\:opOthrPartlisth').val())+'\\:txt').val();

                                                           return oldval;
                                                           //alert(e1);
                                                       }
//1354 CRM-3024: ALL INSTANCES: Opp Error??  "Leave page"
                                                       function loadOppValue() {
                                                           var oldOppVal = $('#frmOpp\\:tabOpp\\:inpTxtValue_input').val();
                                                           return oldOppVal;
                                                       }
//            1354 CRM-3024: ALL INSTANCES: Opp Error??  "Leave page"
                                                       $('#frmOpp\\:tabOpp\\:inpTxtValue_input').on('input', function (e) {
                                                           var opaid = $('#frmOpp\\:oppsiD').val();
//                  console.log(opaid+"opaid    ");
                                                           if (opaid !== '0') {
                                                               oppValChanged = "1";
                                                               loadOnOppValChange();
                                                           }

                                                       });
//            1354 CRM-3024: ALL INSTANCES: Opp Error??  "Leave page"
                                                       function pageLoadOppValue() {
                                                           oldOppVal = $('#frmOpp\\:tabOpp\\:inpTxtValue_input').val();

                                                       }
//            1354 CRM-3024: ALL INSTANCES: Opp Error??  "Leave page"
                                                       function onOppValChange() {
                                                           var opaid = $('#frmOpp\\:oppsiD').val();
                                                           console.log(opaid + "opaid    ");
                                                           if (opaid !== '0') {
                                                               oppValChanged = "1";
//                          loadOnOppValChange(); 
                                                           }
                                                       }
//            function loadOldOppValue() {
//                oldOppVal = $('#frmOpp\\:tabOpp\\:inpTxtOldValue').val();                
//            }
//1557 CRM-3161: Opp - Open/closed Field  - Unable to Edit?
                                                       function loadValuesOnClose() {
                                                           oldFormData = loadAllValues();
                                                       }
//                            4267 CRM-4212: Salesforce CRMSync updates
                                                       function loadCrmOppList() {
                                                           var btnO = document.getElementById('frmOpp:tabOpp:srchCrmOpp');
//                    console.log('var ' + btnO);
                                                           btnO.click();
                                                       }
                                                       function loadCrmOppLineItemList() {
                                                           var btnOL = document.getElementById('frmOpp:tabOpp:btnSrchCrmOppLn');
//                    console.log('var ' + btnOL);
                                                           btnOL.click();
                                                       }




        </script>


    </ui:define> 

</ui:composition>