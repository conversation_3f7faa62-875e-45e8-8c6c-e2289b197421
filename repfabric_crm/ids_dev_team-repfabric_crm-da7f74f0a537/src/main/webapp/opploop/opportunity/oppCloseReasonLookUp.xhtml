<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui">

    <p:dialog header="#{custom.labels.get('IDS_OPP')} Close Reason Lookup" widgetVar="dlgOppCloseReason" width="450"  resizable="false" 
              modal="true" id='oppCloseRsnDlg'>
    <!--//    Task #461: CRM-2505:  Opportunities > Opp Close Reason lookup-->

        <h:form id="dtOppCloseForm">
           <!--Task #6393- Opportunity>Close Reason/Fail Reason by Vithin-->
            <p:dataTable value="#{oppCloseReasonMstService.oppCloseReasonLookup}" var="oppCloseRsn"
                         rowKey="#{oppCloseRsn.recId}" selectionMode="single"
                         id="oppCloseDtl" rowsPerPageTemplate="5,10,15" 
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         paginatorPosition="top" styleClass="dt-lookup"  
                         selection="#{oppCloseReasonMstService.selectedOppCloseRsnMst}"
                         widgetVar="oppCloseRsnDtl" style="margin-top:-5px"
                         >
                
                <p:ajax event="rowSelect" oncomplete="PF('dlgOppCloseReason').hide();"  listener="#{oppCloseReasonMstService.applyOppCloseReason(oppCloseReasonMstService.selectedOppCloseRsnMst)}" />


                <p:column filterBy="#{oppCloseRsn.oppCloseReason}"  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_OPP')} Close Reason"  >
                    <h:outputText value="#{oppCloseRsn.oppCloseReason}"/>
                </p:column>
            </p:dataTable>
        </h:form>
        
      
    </p:dialog>
    
</ui:composition>