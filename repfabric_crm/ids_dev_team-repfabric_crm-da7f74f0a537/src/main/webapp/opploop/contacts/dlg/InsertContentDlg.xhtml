<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

<!--Feature #4457:Contacts > Send Bulk Email  8/06/21 by harshithad-->
    <p:dialog  id="contInsertContentDlg" resizable="false" header="Insert Data Tags" style="width:350px;"  modal="true" widgetVar="contInsertContentDlg" responsive="true">  
        <h:form id="insertTagContentForm">
            <p:panelGrid id="grid" columns="1" 
                         style="min-height: 50px"       layout="grid" styleClass="box-primary no-border ui-fluid ">
                
                <p:outputLabel id="otptLblCont" value="Select data item to be substituted for the tag" style="width:100%" /> 

                <p:selectOneMenu id="oneMenuconsole" widgetVar="DDSubTag"  style="width:125px">
        
                    <f:selectItems value="#{contactService.dataTags}" var="data_tag" />

                </p:selectOneMenu>
            </p:panelGrid>

            <p:spacer width="5px"/>
            <div class="div-center"> 
                <p:commandButton id="cmdBtnCont"  value="Insert into Content"   onclick="insertOnContent();" oncomplete="PF('contInsertContentDlg').hide();"
                                  styleClass="btn btn btn-primary btn-xs"  />
                <p:commandButton id="cmdBtnSub"  value="Insert into subject"  onclick="insertOnSubject();"  oncomplete="PF('contInsertContentDlg').hide();"
                                   styleClass="btn btn btn-primary btn-xs"  style="margin-left:5px"/>
                <p:commandButton id="cmdBTnClose" value="Close" type="button" onclick="PF('contInsertContentDlg').hide()" styleClass="btn btn btn-danger btn-xs" style="margin-left:5px"/>
            </div>

        </h:form>
      <script>
            function insertOnContent() {
                
                $('#summernoteCont').summernote('editor.restoreRange');
                const rng = $('#summernoteCont').summernote('editor.getLastRange');   
                var text = PF('DDSubTag').getSelectedValue();

                $('#summernoteCont').summernote({focus: true});
                document.execCommand('insertText', false, text);


            }

            function insertOnSubject() {
               
                var element1 = $('#SendEmailForm\\:inptTxtSubjectCont');

                var text = PF('DDSubTag').getSelectedValue();
                var caretPos = element1[0].selectionStart,
                        currentValue = element1.val();

                element1.val(currentValue.substring(0, caretPos) + text + currentValue.substring(caretPos));

            }



            function loadSummerNote() {
                $('#summernote').summernote({

                    airMode: false, height: 250, minHeight: 200, maxHeight: 250, disableResizeEditor: true
                });
                var m = $('#summernote').summernote('code');
                $('div.note-editable').height(250);
                console.log('afsfgfdgg');
                console.log(m);
             
            }
        </script>
        
    </p:dialog>

</ui:composition>

