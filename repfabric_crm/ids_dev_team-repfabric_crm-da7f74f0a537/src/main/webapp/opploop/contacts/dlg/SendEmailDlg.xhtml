<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <!--    Feature #4457:Contacts > Send Bulk Email  8/06/21 by harshithad-->
    <p:dialog  id="dlgSendEmailCont"  header="Sending email"  modal="true"    widgetVar="sendEmailCont" width="1050"   height="550" resizable="false" onShow="loadSummerNote()">  
        <p:ajax event="close" listener="#{contactService.onCancelEmailDlg()}" />
        <h:form id="SendEmailForm">
            <div>
                <!--11/02/2021 7184: Pitch Selector: Handle Pitch Attachments in Contacts > Send Email > Insert Pitch-->
                 <!--12796: 01/01/2024: CRM-7574 Email: Inserting pitches populating (Contacts > Send Email)-->
                <p:remoteCommand id="ajUpdate" name="ajUpdate" update=":SendEmailForm:dtAttListCont :SendEmailForm:inptTxtSubjectCont" actionListener="#{contactService.addPitchAttach()}"/>
                <!--4867 Contacts > Send Bulk Email > Add Attachment : Fix button size issue-->
                <p:commandButton id="btnSend" onclick="saveEmail();"  value="Send" onstart="PF('loaderDlg').show()" oncomplete="PF('loaderDlg').hide()" actionListener="#{contactService.sendBulkEmail()}" styleClass="btn btn-primary btn-xs" style="height: 26px;"/>
                <p:spacer width="4" />
                <p:commandButton id="btnCancl" value="Cancel" actionListener="#{contactService.onCancelEmailDlg()}"  oncomplete="PF('sendEmailCont').hide();"   styleClass="btn btn-danger btn-xs" style="height: 26px;"/>
                <!--4867 Contacts > Send Bulk Email > Add Attachment : Fix button size issue-->
                <p:fileUpload id="fleUpldAttch" fileUploadListener="#{contactService.uploadEmailAttachment}" 
                              label="Add Attachment" mode="advanced"
                              auto="true"
                              style="float: right;margin-left: 2px;font-size: 0px;"
                              update="SendEmailForm:dtAttListCont"
                              />
                <!--style="float: rig ht;margin-left: 2px;z-index: -1;position: relative;margin: 0;height: 20px;padding-bottom: 26px;line-height: 0em;"-->
                <!--font-size: 10px;-->
                <!--4867 Contacts > Send Bulk Email > Add Attachment : Fix button size issue-->
                <!--               Feature #5470:Contacts: Send Bulk Email:            by Harshitha Devadiga on 09/08/2021-->
                <p:commandButton id="btnInsertDataTagsCont" value="Insert Data Tags" onclick="PF('contInsertContentDlg').show();"  
                                 onblur="saverng();" update="insertTagContentForm" style="float:right;margin-top:2px !important; height: 26px;margin-left: 5px;"  
                                 styleClass="btn btn-primary  btn-xs" />
                <!--Pms Task 6838:Pitch Selector: Contacts > Send Email-->
                <p:spacer width="4" />
                <p:commandButton id="btnPitch" value="Insert Pitch"  onblur="saverng();" styleClass="btn btn-primary btn-xs" oncomplete="PF('insertPitchDlg').show();" style="float:right; margin-top:2px !important; height: 26px;margin-left: 2px;" actionListener="#{pitchSelectorService.resetDefalutPitch()}" update=":pitchSelForm:hdnContCompName1 :pitchSelForm:itCompName1 :pitchSelForm:pitchName :pitchSelForm"/>
                <p:spacer width="4" />
                <!--Feature #5470:Contacts: Send Bulk Email: :           by Harshitha Devadiga on 09/08/2021-->
                <p:selectOneRadio widgetVar="emlTypeRadio" id="rdBtn" value="#{contactService.bulkEmailHdr.bulkEmailType}" style="float:right"  rendered="#{contactService.sendEmailLocation==2}" >
                    <f:selectItem  itemLabel="Single Email" itemValue="1" />
                    <f:selectItem itemLabel="Standard Email" itemValue="0" />
                    <p:ajax event="change" listener="#{contactService.onChangeType()}"  onstart="saveEmail();"
                            process="@this insertTagContentForm SendEmailForm:inptTxtSubjectCont SendEmailForm:hiddenText"
                            />
                </p:selectOneRadio>

                <h:outputText id="otptLblSnd" value="Send as:" style="font-weight: bold;float:right;margin-right: 6px; "  rendered="#{contactService.sendEmailLocation==2}"/>

            </div>
            <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                         layout="grid" styleClass="box-primary no-border ui-fluid " >
                <p:outputLabel id="otptLblSub" value="Subject" />

                <p:inputText id="inptTxtSubjectCont" value="#{contactService.bulkEmailHdr.bulkEmailSubject}" maxlength="150" style="width:100%;margin-left:3px">
                </p:inputText>
            </p:panelGrid>
            <!--            Feature #5470:Contacts: Send Bulk Email: Updates by Harshithad on 16-08-2021-->
            <p:inputText  id="hiddenText" type="hidden"  value="#{contactService.bulkEmailHdr.bulkEmailContent}" />
            <textArea id="summernoteCont" class="summernote summernote-btns" height="300">
                </textArea>

            <br/>


            <p:dataTable id="dtAttListCont" value="#{contactService.emailAttchList}" style="width: 100%;margin-left: 7px;" var="att" emptyMessage="No files Found">
                <p:column headerText="Attachment"   style="width:90%;"  >
                    <h:outputText id="otptTxtFlNme" value="#{contactService.fileName(att)}" />
                </p:column>

                <p:column style="width:15%;" >
                    <p:commandButton id="btnDelAttch" action="#{contactService.removeEmailAttachment(att)}" icon="fa fa-trash" title="Delete" styleClass="btn-danger btn-xs btn-icon"
                                     style="height:20px;width:20px;" update=":SendEmailForm:dtAttListCont" >
                        <p:confirm  header="Confirmation" message="Are you sure to delete?" icon="ui-icon-alert" />
                    </p:commandButton>
                </p:column>
            </p:dataTable>
            <p:spacer  width="4" />

        </h:form>
        <h:form id="frmPrgrssDlg">
            <p:dialog widgetVar="loaderDlg" closable="false" modal="true" header="Message" resizable="false" id="dlgPrgrsbarCont" >
                <h:form id="frmProgressbar">
                    <p:outputPanel >
                        <br />
                        <h:graphicImage id="grphcImgLodr"  library="images" name="ajax-loader.gif"   />
                        <p:spacer width="5" />
                        <p:outputLabel id="otptLblPrgs" value="Sending Email.." />
                        <br /><br />

                    </p:outputPanel>
                </h:form>
            </p:dialog>
        </h:form>
    </p:dialog>
  <!--               Feature #5470:Contacts: Send Bulk Email:            by Harshitha Devadiga on 09/08/2021-->
    <p:confirmDialog id="cnfrmSwitch" closable="false"  message="Any changes made to the email content will be lost. Are you sure to proceed?" widgetVar="cnfrmSwthcDlg" header="Confirmation">
        <h:form id="cnfrmFrm">           
            <div style="text-align: center">
                <p:commandButton id="cmdBtnYes" value="Yes"   styleClass="btn btn-primary btn-xs" actionListener="#{contactService.clearEmailFields()}" update="SendEmailForm" oncomplete="PF('cnfrmSwthcDlg').hide(); loadSummerNote()" />
                <p:spacer width="4px"/>
                <p:commandButton id="cmdBtnNo" value="No" styleClass="btn btn-warning btn-xs" actionListener="#{contactService.onCancelSwitch()}" oncomplete="PF('cnfrmSwthcDlg').hide();" update="SendEmailForm:rdBtn"/>
            </div>
        </h:form>
    </p:confirmDialog>
  <!--11/02/2021 7184: Pitch Selector: Handle Pitch Attachments in Contacts > Send Email > Insert Pitch-->
    <p:confirmDialog id="cnfDlgCnt" global="true" showEffect="fade" hideEffect="fade">
        <h:form> 
                        <!--Feature #11258: CRM-6986: Commission Split two lines of exceptions for the same co /related co but for two diff mfgs  by harshithad on 23/06/23-->
            <div style="text-align: center">
                        <p:commandButton value="Yes" type="button" styleClass="ui-confirmdialog-yes btn btn-success btn-xs" icon="ui-icon-check"/>
                        <p:spacer width="4" />
                        <p:commandButton value="No" type="button"  styleClass="ui-confirmdialog-no btn btn-danger btn-xs"  icon="ui-icon-close"/>
            </div>
        </h:form>
    </p:confirmDialog>
  
    <ui:include src="/opploop/contacts/dlg/InsertContentDlg.xhtml"/>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css" />
<!--<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.4.1/js/bootstrap.min.js"></script>-->
     <link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote.css" rel="stylesheet"></link>

    <!--    Feature #5470:Contacts: Send Bulk Email:            by Harshitha Devadiga on 09/08/2021-->
    <script>



                    function saveEmail() {
                        var m = $('#summernoteCont').summernote('code');
        #{compProdPitch.appendBeforeSave('m')};
        #{compProdPitch.appendBeforeSave(m)};
                        var sum = document.getElementById('SendEmailForm:hiddenText').value = m;


                    }

                    function  clearSummerNote() {

                        $('#summernoteCont').summernote('reset');
                    }
                    function saverng()
                    {

                        $('#summernoteCont').summernote('editor.saveRange');
                    }
                    function loadEditorContent1(pitchContent) {
                        console.log(pitchContent);
                        console.log(pitchContent);
                        $("#dlgSendEmailForm\\:summernoteCont").html(pitchContent);
                        $("#summernoteCont").summernote('code', pitchContent);

                    }


                    function loadSummerNote() {
                        $('#summernoteCont').summernote({

                            airMode: false, height: 250, minHeight: 200, maxHeight: 250, disableResizeEditor: true
                        });
                        var m = $('#summernoteCont').summernote('code');
                        $('div.note-editable').height(250);

                    }

                    function insertCode() {
                        // alert('&lt;br /&gt;'+ content);

                        $('#summernoteCont').summernote('editor.restoreRange');
                        const rng = $('#summernoteCont').summernote('editor.getLastRange');
                        $('#summernoteCont').summernote('editor.focus');

                        $('#summernoteCont').summernote('pasteHTML', content);
//                                        $("p").each(function(){
//                                            if (!$(this).text().trim().length) {
//                                                $(this).remove();
//                                            }
//                                        });
                    }



    </script>


    <style>
        .modal-backdrop {
            z-index: 0;
        }
        ul li{
            /*    list-style: disc !important;*/
            list-style-position: inside;
            margin-left: 10px;
        }
        ol li{
            /*    list-style: decimal;*/
            list-style-position: inside;
            margin-left: 10px;
        }

        .note-btn{
            min-width: 50px !important;

        }
        /*4867 Contacts > Send Bulk Email > Add Attachment : Fix button size issue*/

        /*        .ui-fileupload-content {
                    padding: .1em .1em;
                    border-top-width: 0;
                    border: 0px;
                    height: 23px!important;
                }*/
        /*.ui-fileupload .ui-fileupload-buttonbar span.ui-icon-plusthick*/
        #SendEmailForm\:fleUpldAttch>.ui-fileupload-buttonbar.ui-widget-header.ui-corner-top>
        .ui-button.ui-widget.ui-state-default.ui-corner-all.ui-button-text-icon-left.ui-fileupload-choose>
        .ui-button-icon-left.ui-icon.ui-c.ui-icon-plusthick
        {
            color: #fff;
            text-indent: 0;

            display: none;
            visibility: hidden;
        }

        /*        .ui-fileupload-buttonbar .ui-fileupload-choose .ui-button-text{
                    padding-left:1em !important;
                    font-size:12px;
                        padding-top: 6px !important;
                }*/

        #SendEmailForm\:fleUpldAttch>.ui-fileupload-content{
            display:none;
        }
        /*<!--4867 Contacts > Send Bulk Email > Add Attachment : Fix button size issue-->*/
        #SendEmailForm\:fleUpldAttch_label{ 
            padding-left:1em !important;
            font-size:12px;
            padding-top: 6px !important;
        }
        /*4867 Contacts > Send Bulk Email > Add Attachment : Fix button size issue*/
        /*#SendEmailForm\:fleUpldAttch_label{margin-top: -4px !important;};*/
    </style>
</ui:composition>