<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: WebUrl.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >

    <!--    <p:focus for="itPStreet" />-->
   
    <h:panelGroup id="contPnl2">
        <!--Task #8073: CRM-5494  Contact Detail List view - page unresponsive (Edge Browser)    by harshithad 06/06/22--> 
        <h:inputHidden id="inptHiddnWebUrls" value="#{contactService.hiddenContactWeb.contWebUrl}"/>
        <h:inputHidden id="inptHiddnFbUrls" value="#{contactService.hiddenContactWeb.facebookLink}"/>
        <h:inputHidden id="inptHiddnGoogleUrls" value="#{contactService.hiddenContactWeb.googlePlusLink}"/>
        <h:inputHidden id="inptHiddnInstaUrls" value="#{contactService.hiddenContactWeb.instagramLink}"/>
        <h:inputHidden id="inptHiddnLinkdInUrls" value="#{contactService.hiddenContactWeb.linkedInLink}"/>
        <h:inputHidden id="inptHiddnTwtrUrls" value="#{contactService.hiddenContactWeb.twitterLink}"/>



        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Contact WebSite Details</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            </div>
        </div>  
        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <!--            //PMS:2565
                              //seema -03/12/2020-->
                        <p:outputLabel value="Website" for="itWebsite" id="oLblWebsite"/>                 
                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5">
                        <p:inputText id="itWebsite" value="#{contactService.contactsWeb.contWebUrl}" label="Website" styleClass="copyBusFld">
                            <p:ajax event="blur" process="itWebsite"   />
                        </p:inputText> 
                    </div>  
                    <div class="ui-sm-12 ui-md-6 ui-lg-6">

                    </div>  
                </div>   
            </div>
        </div>
        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Social Media Links</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            </div>
        </div> 
        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <!--Feature #7649 CRM-5150  need to be able to import "Web URL" tab - all fields-->
                        <a href="#{contactService.contactsWeb.facebookLink}" title="Facebook"  target="_blank">
                            <p:graphicImage  width="25" height="25" value="/resources/images/socialMedia/facebook.png" id="grpImgFacebook"/>
                        </a>               
                    </div>
                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputPanel id="facebook">
<!--                            <h:outputLink value="#{contactService.contactsWeb.facebookLink}" title ="Facebook" target="_blank" >
                                <h:outputText value="#{contactService.contactsWeb.facebookLink}" rendered="#"/>
                            </h:outputLink>-->
                            <p:inputText value="#{contactService.contactsWeb.facebookLink}" id="iTFacebook"/>
                        </p:outputPanel>
                    </div> 


                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <p:outputPanel>
                            <!--<p:commandButton resetValues="true"   styleClass="btn-primary  btn-xs btn-icon" title="Edit Link" icon="fa fa-pencil" />-->
                            <p:commandButton  styleClass="btn-primary  btn-xs btn-icon" title="Save Link" icon="fa fa-floppy-o" action="#{contactService.saveLink(contactService.contactsWeb.facebookLink, 1, contactService.contacts.contId)}" update=":contDetailsButtonForm:tvCont:opnLinkFb" id="btnSaveFacebook"/>
                        </p:outputPanel>
                    </div> 

                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <p:commandLink id="opnLinkFb" value="Open link" style="color: blue;" title="#{contactService.contactsWeb.facebookLink}" onclick="window.open('#{contactService.getValidURL(contactService.contactsWeb.facebookLink)}', '_blank');" disabled="#{contactService.contactsWeb.opfacebookLink}"/>
                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5">
                    </div> 

                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <a href="#{contactService.contactsWeb.twitterLink}" title="Twitter" target="_blank">
                            <p:graphicImage  width="25" height="25" value="/resources/images/socialMedia/twitter.png" id="grpImgTwitter"/>
                        </a>               
                    </div>
                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputPanel>
<!--                            <h:outputLink value="#{contactService.contactsWeb.twitterLink}" title ="Twitter" target="_blank" >
                                <h:outputText value="#{contactService.contactsWeb.twitterLink}" rendered="#"/>
                            </h:outputLink>-->
                            <p:inputText value="#{contactService.contactsWeb.twitterLink}" id="iTTwitter"/>
                        </p:outputPanel>
                    </div> 

                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <!--<p:commandButton resetValues="true"   styleClass="btn-primary  btn-xs btn-icon" title="Edit Link" icon="fa fa-pencil" />-->
                        <p:commandButton  styleClass="btn-primary  btn-xs btn-icon" title="Save Link" icon="fa fa-floppy-o" action="#{contactService.saveLink(contactService.contactsWeb.twitterLink, 2, contactService.contacts.contId)}" update=":contDetailsButtonForm:tvCont:opnLinkTw" id="btnSaveTwitter"/>
                    </div> 
                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <p:commandLink id="opnLinkTw" value="Open link" style="color: blue;" title="#{contactService.contactsWeb.twitterLink}" onclick="window.open('#{contactService.getValidURL(contactService.contactsWeb.twitterLink)}', '_blank');" disabled="#{contactService.contactsWeb.optwitterLink}"/>
                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5">
                    </div> 


                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <a href="#{contactService.contactsWeb.linkedInLink}" title="Linked In" target="_blank">
                            <p:graphicImage  width="25" height="25" value="/resources/images/socialMedia/linkedin.png" id="grpImgLinkedIn" />
                        </a>               
                    </div>
                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputPanel>
<!--                            <h:outputLink value="#{contactService.contactsWeb.linkedInLink}" title ="Linked In" target="_blank" >
                                <h:outputText value="#{contactService.contactsWeb.linkedInLink}" rendered="#"/>
                            </h:outputLink>-->
                            <p:inputText value="#{contactService.contactsWeb.linkedInLink}" id="iTLinkedIn"/>
                        </p:outputPanel>
                    </div> 

                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <!--<p:commandButton resetValues="true"   styleClass="btn-primary  btn-xs btn-icon" title="Edit Link" icon="fa fa-pencil" />-->
                        <p:commandButton  styleClass="btn-primary  btn-xs btn-icon" title="Save Link" icon="fa fa-floppy-o" action="#{contactService.saveLink(contactService.contactsWeb.linkedInLink, 3, contactService.contacts.contId)}" update=":contDetailsButtonForm:tvCont:opnLinkLi" id="btnSaveLinkedIn"/>
                    </div> 
                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <p:commandLink id="opnLinkLi" value="Open link" style="color: blue;" title="#{contactService.contactsWeb.linkedInLink}" onclick="window.open('#{contactService.getValidURL(contactService.contactsWeb.linkedInLink)}', '_blank');" disabled="#{contactService.contactsWeb.oplinkedInLink}"/>
                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5">
                    </div> 

                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <a href="#{contactService.contactsWeb.googlePlusLink}" title="Google Plus" target="_blank">
                            <p:graphicImage  width="25" height="25" value="/resources/images/socialMedia/googleplus.png" id="grpImgGoolePlusLnk"/>
                        </a>               
                    </div>
                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputPanel>
<!--                            <h:outputLink value="#{contactService.contactsWeb.googlePlusLink}" title ="Google Plus" target="_blank" >
                                <h:outputText value="#{contactService.contactsWeb.googlePlusLink}" rendered="#"/>
                            </h:outputLink>-->
                            <p:inputText value="#{contactService.contactsWeb.googlePlusLink}" id="iTGooglePlus"/>
                        </p:outputPanel>
                    </div> 

                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <!--<p:commandButton resetValues="true"   styleClass="btn-primary  btn-xs btn-icon" title="Edit Link" icon="fa fa-pencil" />-->
                        <p:commandButton  styleClass="btn-primary  btn-xs btn-icon" title="Save Link" icon="fa fa-floppy-o" action="#{contactService.saveLink(contactService.contactsWeb.googlePlusLink, 4, contactService.contacts.contId)}" update=":contDetailsButtonForm:tvCont:opnLinkGp" id="btnSaveGooglePlus"/>
                    </div> 
                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <p:commandLink id="opnLinkGp" value="Open link" style="color: blue;" title="#{contactService.contactsWeb.googlePlusLink}" onclick="window.open('#{contactService.getValidURL(contactService.contactsWeb.googlePlusLink)}', '_blank');" disabled="#{contactService.contactsWeb.opgooglePlusLink}"/>
                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5">
                    </div> 
                    <!--                    Feature #7649 CRM-5150  need to be able to import "Web URL" tab - all fields-->

                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <a href="#{contactService.contactsWeb.instagramLink}" title="Instagram" target="_blank">
                            <p:graphicImage  width="25" height="25" value="/resources/images/socialMedia/instagram.png" id="grpImgInstagramLnk"/>
                        </a>               
                    </div>
                    <div class="ui-sm-12 ui-md-4 ui-lg-4">
                        <p:outputPanel>

                            <p:inputText value="#{contactService.contactsWeb.instagramLink}" id="iTinstgramLink"/>
                        </p:outputPanel>
                    </div> 
                    <div class="ui-sm-12 ui-md-1 ui-lg-1">

                        <p:commandButton  styleClass="btn-primary  btn-xs btn-icon" title="Save Link" icon="fa fa-floppy-o" action="#{contactService.saveLink(contactService.contactsWeb.instagramLink, 5, contactService.contacts.contId)}" update=":contDetailsButtonForm:tvCont:opnLinkInsta" id="btnSaveInstagram"/>
                    </div> 
                    <div class="ui-sm-12 ui-md-1 ui-lg-1">
                        <p:commandLink id="opnLinkInsta" value="Open link" style="color: blue;" title="#{contactService.contactsWeb.instagramLink}" onclick="window.open('#{contactService.getValidURL(contactService.contactsWeb.instagramLink)}', '_blank');" disabled="#{contactService.contactsWeb.opinstagramLink}"/>
                    </div>
                    <div class="ui-sm-12 ui-md-5 ui-lg-5">
                    </div> 


                </div>   
            </div>
        </div>
    </h:panelGroup>





</ui:composition>