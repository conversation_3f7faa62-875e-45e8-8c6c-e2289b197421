<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: Personal.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >

    <!--    <p:focus for="itPStreet" />-->

    <h:panelGroup id="contPnl1">
        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Personal Information</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            </div>
        </div>  
        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-6 ui-lg-6">
                <div class="ui-g ui-fluid">
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                           <!--            //PMS:2565
                                 //seema -03/12/2020-->
                           <p:outputLabel value="Street" for="itPStreet" id="oLblPersonalStreet"/>                 
                    </div>
                    <div class="ui-sm-12 ui-md-9 ui-lg-9">
                        <p:inputTextarea id="itPStreet" value="#{contactService.contacts.contHomeAddress1}" label="Street" style="width: 100%;resize: none" styleClass="tds1" rows="3" autoResize="false"  maxlength="180" tabindex="1"/>
                    </div> 
                    <!--2805 - JSF - Add PO Box below Street in Contact Details -> Basic and Personal tab- sharvani -02-01-2020-->
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="PO Box" for="itPpoBox" id="oLblPersonalPOBox"/>                 
                    </div>
                    <div class="ui-sm-12 ui-md-9 ui-lg-9">
                        <p:inputText maxlength="30"  styleClass="tds1" id="itPpoBox" value="#{contactService.contacts.contHomePoBox}" style="width: 100%"  tabindex="2"/>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="City" for="itPCity"  id="oLblPersonalCity"/>                 
                    </div>
                    <div class="ui-sm-12 ui-md-9 ui-lg-9">
                        <p:inputText maxlength="40"  styleClass="tds1" id="itPCity" value="#{contactService.contacts.contHomeCity}" label="City" style="width: 100%"  tabindex="3"/>
                    </div>

                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <!--#1828 - CRM-3042: Intralec: change labels for state and zip - sharvanim - 25-09-2020-->
                        <p:outputLabel value="#{custom.labels.get('IDS_STATE')}"  for="itPState"  id="oLblPersonalState"/>                 
                    </div>
                    <div class="ui-sm-12 ui-md-9 ui-lg-9">
                        <p:inputText maxlength="40"  styleClass="tds1" id="itPState" value="#{contactService.contacts.contHomeState}" label="State" style="width: 100%"  tabindex="4"/>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <!--#1828 - CRM-3042: Intralec: change labels for state and zip - sharvanim - 25-09-2020-->
                        <p:outputLabel value="#{custom.labels.get('IDS_ZIP')}"  for="itPZipCode"  id="oLblPersonalZip"/>                 
                    </div>
                    <div class="ui-sm-12 ui-md-9 ui-lg-9">
                        <p:inputText maxlength="12"  styleClass="tds1" id="itPZipCode" value="#{contactService.contacts.contHomeZipCode}" label="Zip code" style="width: 100%" tabindex="5" />
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="Country" for="itPCntryCode"  id="oLblPersonalCntry"/>                 
                    </div>
                    <div class="ui-sm-12 ui-md-9 ui-lg-9">
                        <p:inputText maxlength="40"  styleClass="tds1" id="itPCntryCode" value="#{contactService.contacts.contHomeCountry}" label="Country" style="width: 100%"  tabindex="6"/>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="Personal Phone" for="itPPhn"  id="oLblPersonalPhone"/>                 
                    </div>
                    <div class="ui-sm-12 ui-md-9 ui-lg-9">
                        <p:inputText maxlength="40"  styleClass="tds1" id="itPPhn" value="#{contactService.contacts.contPhoneHome}" label="Personal Phone" style="width: 100%"  tabindex="7"/>
                    </div>
                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                        <p:outputLabel value="Personal Email" for="itPEmail" id="oLblPersonalEmail"/>                 
                    </div>
                    <div class="ui-sm-12 ui-md-9 ui-lg-9">
                        <p:inputText maxlength="100"  styleClass="tds1" id="itPEmail" value="#{contactService.contacts.contEmailPersonal}" label="Personal Email" style="width: 100%" validatorMessage="Invalid email format." tabindex="8">
                            <!--#32- CRM-2344:Contacts:email address entry:chop off leading and trailing white - sharvani - 03-04-2020-->
                           <!--#84 - CRM-2348: arkco: email address w subdomain wont save   13-04-2020- sharvani--> 
                           <!--#328- CRM-2487: Error with Email address? Paul Gotceitas - 29-04-2020- sharvani-->
                           <!--#5783: CRM-4746: invalid email format -poornima - commented validateRegex -->
<!--                            <f:validateRegex
                                pattern="^\s*[_A-Za-z0-9-\+']+(\.[_A-Za-z0-9-']+)*@(([A-Za-z0-9]+\.)*[A-Za-z0-9-_]{2,}\.)+[A-Za-z]{2,}\s*$"  />    -->
                        </p:inputText>
                    </div>
                </div>   
            </div>
        </div>
    </h:panelGroup>
    <!--<p:outputPanel id="eventsPanel" rendered="#{contactService.contacts.recId>0}">-->
<!--    <p:outputPanel id="eventsPanel" rendered="false">
        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Dates to Remember</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            </div>
        </div>  
        <div class="ui-g ui-fluid">
            <div class="box-header with-border">
                <div class="row">
                    <div class="col-md-5">
                        <p:commandButton value="New"  styleClass="btn btn-primary btn-xs"  actionListener="#{contactService.initiaContEventValues()}" immediate="true" oncomplete="PF('contEventDlg').show();" update=":contDetailsButtonForm:tvCont:dlgContEvent contDetailsButtonForm:tvCont:addDateForm"/> 
                    </div>                        
                </div> 
            </div>
        </div>
        <p:dataTable id="dtContDateList" 
                     var="contEvent" 
                     rows="50" 
                     selectionMode="single" 
                     widgetVar="contDateListTable" 
                     filterEvent="keyup" 
                     draggableColumns="true"
                     emptyMessage="No companies found."  
                     paginator="true" 
                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                     rowsPerPageTemplate="50,100" 
                     paginatorAlwaysVisible="false" 
                     value="#{contactService.listContDate}"
                     selection="#{contactService.selectedContDate}"
                     class="tblmain"
                     rowKey="#{contEvent.recId}">
            <p:ajax event="rowSelect" listener="#{contactService.populateContactDate(conEvent)}" oncomplete="PF('contEventDlg').show();" update=":contDetailsButtonForm:tvCont:dlgContEvent contDetailsButtonForm:tvCont:addDateForm"/>

            <p:column headerText="Date" style="width:300px">
                <h:outputText value="#{contEvent.contDate}" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}"/>
                </h:outputText>
            </p:column>
            <p:column headerText="Type" style="width:100px">
                <h:outputText value="#{contactService.retrieveTypeName(contEvent.contDateEventType)}" />
            </p:column>
            <p:column headerText="Description">
                <h:outputText value="#{contEvent.contDateEventDesc}" />
            </p:column>
            <p:column style="width: 32px">
                <p:commandButton update=":contactDetailsForm:tv_cTabs:eventsPanel" icon="ui-icon-trash" title="Delete Date" action="#{conEvent.saveContactDate(contacts.contId, 'delete')}" style="height:20px;width:20px;">
                    <f:setPropertyActionListener target="#{contacts.itemToDelete}" value="#{conEvent}" />
                    <p:confirm header="Confirmation" message="Are you sure to delete?" icon="ui-icon-alert" />
                </p:commandButton>
            </p:column>
        </p:dataTable>

    </p:outputPanel>-->

    <style>
        .grpBox  .ui-widget-content  ul{
            padding:0 20px!important;
            margin:0px!important;

        }

    </style>

<!--    <p:dialog header="#{contactService.contactsDates.recId==0?'Add':'Edit'} Date to Remember" modal="true" responsive="true" widgetVar="contEventDlg" id="dlgContEvent" >
        <h:form id="addDateForm" >
            <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                         style="min-height: 50px"       layout="grid" styleClass="box-primary no-border ui-fluid">
                <p:outputLabel for="clnDate" value="Date" styleClass="required" />
                <p:calendar id="clnDate" value="#{contactService.contactsDates.contDate}" showOn="button"  pattern="#{globalParams.dateFormat}" >
                </p:calendar>
                <p:outputLabel for="oneMenuType" value="Type"/>
                <p:selectOneMenu value="#{contactService.contactsDates.contDateEventType}"   id="oneMenuType">
                    <f:selectItem itemLabel="Type 1" itemValue="1"/>
                    <f:selectItem itemLabel="Type 2" itemValue="2"/>
                    <f:selectItem itemLabel="Type 3" itemValue="3"/>
                </p:selectOneMenu>
                <p:outputLabel  for="txtDesc" value="Description"/>
                <p:inputText id="txtDesc" value="#{contactService.contactsDates.contDateEventDesc}"/>
            </p:panelGrid>
            <div class="button_bar" align="center">
                <p:commandButton id="submitButton" value="Save" styleClass="btn btn-success  btn-xs" />                 
                <p:spacer width="4px"/>
                <p:commandButton value="Cancel" styleClass="btn btn-warning  btn-xs"  onclick="PF('contEventDlg').hide();"/>      
            </div> 

        </h:form>
    </p:dialog>-->

</ui:composition>