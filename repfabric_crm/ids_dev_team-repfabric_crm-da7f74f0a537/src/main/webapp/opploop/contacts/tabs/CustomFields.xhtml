<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: CustomFields.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >


    <div class="ui-g ui-fluid">
        <div class="ui-sm-12 ui-md-12 ui-lg-12">
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                    <p:outputLabel value="No #{custom.labels.get('IDS_CUSTOM_FIELDS')}" rendered="#{contactService.contCustomFieldsList eq null or contactService.contCustomFieldsList.size() eq 0}" id="lblMsg"/> 
                </div>
                <div class="ui-sm-12 ui-md-9 ui-lg-9">         
                </div>

                <div class="ui-sm-12 ui-md-12 ui-lg-12">  

                    <ui:repeat value="#{contactService.contCustomFieldsList}" var="cf" id="uiRptcontCustomFieldsList">


                        <p:outputPanel rendered="#{cf.custFieldType eq 'in'}" id="oPnlSpinr">
                            <div class="ui-g ui-fluid">
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <!--PMS 3096:CRM-3779  companies: multiple save button pushes sends "need to enter a company error" Styleclass updated-->
                                    <p:outputLabel value="#{cf.custFieldLabel}" styleClass="#{cf.custFieldReqd ==1 ? 'required':''}" id="oLblSpncustFieldLabel"/>
                                </div>
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">    
                                    <p:outputPanel>
                                        <p:spinner value="#{cf.customFieldValue}" min="#{cf.custFieldMin}" max="#{cf.custFieldMax}" size="8" id="spncustomFieldValue"/>
                                        <!--                                        <p:spacer height="0" width="10"/>-->
                                        <p:outputLabel value="(min. #{cf.custFieldMin}, max. #{cf.custFieldMax})" id="oLblcustFieldMax"/>
                                    </p:outputPanel>
                                </div>
                                <div class="ui-sm-12 ui-md-6 ui-lg-6">   </div> 

                            </div>
                        </p:outputPanel>


                        <p:outputPanel rendered="#{cf.custFieldType eq 'tx'}" id="oPnlTxtcustFieldType">
                            <div class="ui-g ui-fluid">
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <!--PMS 3096:CRM-3779  companies: multiple save button pushes sends "need to enter a company error" Styleclass updated-->
                                    <p:outputLabel value="#{cf.custFieldLabel}" styleClass="#{cf.custFieldReqd ==1 ? 'required':''}" id="oLblTxtcustFieldLabel"/>
                                </div>
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <p:inputText value="#{cf.customFieldValue}" style="width: 97%"  required="#{cf.custFieldReqd =='1'}" requiredMessage="#{cf.custFieldLabel} is required" maxlength="#{contactService.fieldMaxLength}" styleClass="required" id="iTtxtCustFieldLabel"/>
                                </div>
                                <div class="ui-sm-12 ui-md-6 ui-lg-6">   </div> 
                            </div>
                        </p:outputPanel>


                        <p:outputPanel rendered="#{cf.custFieldType eq 'da'}" id="oPnlDatecustFieldType">
                            <div class="ui-g ui-fluid">
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <!--PMS 3096:CRM-3779  companies: multiple save button pushes sends "need to enter a company error" Styleclass updated-->                                    
                                    <p:outputLabel value="#{cf.custFieldLabel}" styleClass="#{cf.custFieldReqd ==1 ? 'required':''}" id="oLblDatecustFieldLabel"/>
                                </div>

                                <div class="ui-sm-12 ui-md-3 ui-lg-3">

                                    <p:calendar value="#{cf.customFieldValue}" pattern="#{globalParams.dateFormat}" style="width: 100%" required="#{cf.custFieldReqd =='1'}" requiredMessage="#{cf.custFieldLabel} is required" converterMessage="#{cf.custFieldLabel} is invalid" id="calDatecustomFieldValue">
                                        <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                                    </p:calendar>
                                </div>
                                <div class="ui-sm-12 ui-md-6 ui-lg-6">   </div> 

                            </div>
                        </p:outputPanel>


                        <p:outputPanel rendered="#{cf.custFieldType eq 'dt'}" id="oPnlDateTimecustFieldType">
                            <div class="ui-g ui-fluid">
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <!--PMS 3096:CRM-3779  companies: multiple save button pushes sends "need to enter a company error" Styleclass updated-->                                    
                                    <p:outputLabel value="#{cf.custFieldLabel}" styleClass="#{cf.custFieldReqd ==1 ? 'required':''}" id="oLblDateTimecustFieldLabel"/>
                                </div>

                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <p:calendar value="#{cf.customFieldValue}" pattern="#{globalParams.dateTimeFormat}" style="width: 100%" required="#{cf.custFieldReqd =='1'}" requiredMessage="#{cf.custFieldLabel} is required" converterMessage="#{cf.custFieldLabel} is invalid" id="calDateTimecustomFieldValue">
                                        <f:convertDateTime pattern="#{globalParams.dateTimeFormat}" />
                                    </p:calendar>
                                </div>
                                <div class="ui-sm-12 ui-md-6 ui-lg-6">   </div> 
                            </div>
                        </p:outputPanel>

                        <p:outputPanel rendered="#{cf.custFieldType eq 'dd'}" id="oPnlDropDwncustFieldType">
                            <div class="ui-g ui-fluid">
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <!--PMS 3096:CRM-3779  companies: multiple save button pushes sends "need to enter a company error" Styleclass updated-->                                    
                                    <p:outputLabel value="#{cf.custFieldLabel}" styleClass="#{cf.custFieldReqd ==1 ? 'required':''}" id="oLblDropDwncustFieldLabel"/>
                                </div>

                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <!--#631003: Custom Fields: dropdown defaults to 1st in list-->
                                    <p:selectOneMenu value="#{cf.customFieldValue}" style="width: 100%"  required="#{cf.custFieldReqd =='1'}" requiredMessage="#{cf.custFieldLabel} is required" id="oneMenuDropDwncustomFieldValue">
                                        <f:selectItem itemLabel="[--Select--]" itemValue="" />
                                        <f:selectItems var="cfOption" value="#{cf.custSelectOptions}" itemLabel="#{cfOption}" itemValue="#{cfOption}" />
                                    </p:selectOneMenu>   
                                </div>
                                <div class="ui-sm-12 ui-md-6 ui-lg-6">   </div> 
                            </div>
                        </p:outputPanel>

                        <!--//CRM-265 MultiSelect Checkbox-->
                        <p:outputPanel rendered="#{cf.custFieldType eq 'md'}" id="oPnlMultiSlctCustFieldType">
                            <div class="ui-g ui-fluid">
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                    <!--                                    #2940 CRM-1847: Golden: custom field with multi select is awkward-->
                                    <!--PMS 3096:CRM-3779  companies: multiple save button pushes sends "need to enter a company error" Styleclass updated-->                                    
                                    <p:outputLabel value="#{cf.custFieldLabel}" styleClass="#{cf.custFieldReqd ==1 ? 'required':''}" id="oLblMultiSlctcustFieldLabel"/>
                                    <!--                                    <p:selectCheckboxMenu   value="#{cf.multiSelectedOptions}" label="#{cf.custFieldLabel}" filter="true" filterMatchMode="startsWith"   required="#{cf.custFieldReqd =='1'}" requiredMessage="Please select a #{cf.custFieldLabel}" >
                                                                            <f:selectItems value="#{cf.allMultiSelect}" />
                                                                            <p:ajax  event="change" update="displayContOption" />
                                                                            <p:ajax event="toggleSelect" update="displayContOption"  />
                                    
                                                                        </p:selectCheckboxMenu>-->
                                </div>
                                <div class="ui-sm-12 ui-md-1 ui-lg-1">

                                </div>
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <p:selectCheckboxMenu   value="#{cf.multiSelectedOptions}" multiple="true" label="#{cf.custFieldLabel}" filter="true" filterMatchMode="startsWith"   required="#{cf.custFieldReqd =='1'}" requiredMessage="Please select a #{cf.custFieldLabel}" id="multiSlctmultiSelectedOptions">
                                        <f:selectItems value="#{cf.allMultiSelect}" var="cust" itemLabel="#{cust}" itemValue="#{cust}" />
                                        <!--                                        <p:ajax  event="change" update="displayContOption" />
                                                                                <p:ajax event="toggleSelect" update="displayContOption"  />-->

                                    </p:selectCheckboxMenu>
                                    <!--                                    <p:outputPanel id="displayContOption" class="ui-selectonemenu-label ui-inputfield ui-corner-all potential-css " >
                                                                            <p:dataList value="#{cf.multiSelectedOptions}" var="cust" emptyMessage="No options selected" class="grpBox">
                                    #{cust}
                                </p:dataList>
                            </p:outputPanel>-->
                                </div>

                            </div>         
                        </p:outputPanel>

                        <p:outputPanel rendered="#{cf.custFieldType eq 'cb'}" id="oPnlChkCustFieldType">
                            <div class="ui-g ui-fluid">
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <!--PMS 3096:CRM-3779  companies: multiple save button pushes sends "need to enter a company error" Styleclass updated-->                                    
                                    <p:outputLabel value="#{cf.custFieldLabel}" styleClass="#{cf.custFieldReqd ==1 ? 'required':''}" id="oLblChkcustFieldLabel"/>
                                </div>

                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <p:selectBooleanCheckbox value="#{cf.cbValue}"  id="chkCbValue"/>       
                                </div>
                                <div class="ui-sm-12 ui-md-6 ui-lg-6">

                                </div>  
                            </div>

                        </p:outputPanel>

                        <p:outputPanel rendered="#{cf.custFieldType eq 'de'}" id="oPnlInptNumCustFieldType">
                            <div class="ui-g ui-fluid">
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <p:outputLabel value="#{cf.custFieldLabel}" id="oLblInptNumCustFieldLabel"/>
                                </div>
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <!--PMS 3096:CRM-3779  companies: multiple save button pushes sends "need to enter a company error" Styleclass updated-->                                    
                                    <p:inputText value="#{cf.customFieldValue}" maxlength="12" styleClass="#{cf.custFieldReqd ==1 ? 'required':''}"  required="#{cf.custFieldReqd =='1'}" requiredMessage="#{cf.custFieldLabel} is required" converterMessage="#{cf.custFieldLabel} is not a number" id="inptNumcustomFieldValue">
                                        <f:convertNumber groupingUsed="false" maxIntegerDigits="15"  maxFractionDigits="4" minFractionDigits="2" />
                                    </p:inputText>
                                </div>    
                                <div class="ui-sm-12 ui-md-6 ui-lg-6">

                                </div>
                            </div>

                        </p:outputPanel>
                        <!--CRM 158-->
                        <p:outputPanel rendered="#{cf.custFieldType eq 'ta'}" id="oPnlTxtAreacustFieldType">
                            <div class="ui-g ui-fluid">
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <!--PMS 3096:CRM-3779  companies: multiple save button pushes sends "need to enter a company error" Styleclass updated-->                                    
                                    <p:outputLabel value="#{cf.custFieldLabel}" styleClass="#{cf.custFieldReqd ==1 ? 'required':''}" id="oLblTxtAreaCustFieldLabel"/>
                                </div>
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <p:inputTextarea rows="5" value="#{cf.customFieldValue}" style="overflow-y: scroll; height: 50px; width: 97%;"  required="#{cf.custFieldReqd =='1'}" requiredMessage="#{cf.custFieldLabel} is required" maxlength="#{contactService.getLargeTextMaxLength()}" id="txtAreacustomFieldValue">
                                    </p:inputTextarea>
                                </div>
                            </div>

                        </p:outputPanel>
                        <!--CRM 101-->
                        <p:outputPanel rendered="#{cf.custFieldType eq 'lk'}" id="oPnlLnkCustFieldType">
                            <div class="ui-g ui-fluid">
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <!--PMS 3096:CRM-3779  companies: multiple save button pushes sends "need to enter a company error" Styleclass updated-->                                  
                                    <p:outputLabel value="#{cf.custFieldLabel}" styleClass="#{cf.custFieldReqd ==1 ? 'required':''}" id="lnkcustFieldLabel"/>
                                </div>
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">  
                                    <!--09-06-2023  11410 CRM-7057   Microsoft OneNote Section link URL being redirected from rf url custom field popout: Bulk-->
                                    <p:inputText value="#{cf.customFieldValue}"   required="#{cf.custFieldReqd =='1'}" validatorMessage="Invalid URL" requiredMessage="#{cf.custFieldLabel} is required" maxlength="#{contactService.largeTextMaxLength}" id="txtURLCustFieldLabel">
                                        <f:validateRegex pattern="^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$" />
                                    </p:inputText>                             
                                </div>
                                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                    <!--2873 Code Optimization - Company Details screen    --> 
                                    <!--15-06-2023 11410 CRM-7057   Microsoft OneNote Section link URL being redirected from rf url custom field popout: Bulk-->
                                    <p:commandLink id="opnLinkCont" value="Open link" style="color: blue;" disabled="#{cf.customFieldValue == ''}"  onclick="window.open('#{companyService.getValidURL(cf.customFieldValue)}', '_blank');"/>
                                    <p:tooltip id="toolTopOpnLinkContact"  for="opnLinkCont" value="#{cf.customFieldValue}" showEvent="mouseover" position="top" />
                                    <style type="text/css">
                                        .ui-tooltip {
                                            word-wrap: break-word;
                                            max-width: 800px;
                                            white-space:pre-wrap;
                                            text-overflow: ellipsis
                                        }
                                    </style>
                                </div>
                            </div>
                        </p:outputPanel>
                    </ui:repeat>

                </div>
            </div>   
        </div>
    </div>
    <style>
        .ui-selectcheckboxmenu-multiple-container{
            width:100%; 
            height: auto !important;

        }
    </style>

</ui:composition>