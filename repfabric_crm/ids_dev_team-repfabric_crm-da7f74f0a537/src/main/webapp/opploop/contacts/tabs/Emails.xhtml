<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: Emails.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    
    <!--//Feature #4580: CRM-4327: TEAM: Search Bar 'disappeared' in Email tab in a Contact-->
    <div style="text-align: right">
     <p:inputText id="deepContEmailSearch"  value="#{contactService.deepEmailSearch}"
                                                     style="width:150px;" placeholder=" Search">
         <p:ajax  event="keyup" update="contDetailsButtonForm:tvCont:btnDeepSearch :contDetailsButtonForm:tvCont:btnEmailClear" />
     </p:inputText>
         <p:commandButton id="btnDeepSearch" styleClass="btn-info btn-xs" icon="fa fa-search" title="Search"
                           immediate="true"
                                                          style="margin-top:-5px;"
             actionListener="#{contactService.searchEmail(contactService.deepEmailSearch,contactService.contacts.contId)}"
             update=":contDetailsButtonForm:tvCont:dtContEmailList"            
                                                          />
           <!--//Feature #4580: CRM-4327: TEAM: Search Bar 'disappeared' in Email tab in a Contact-->
               <p:commandButton   icon="fa fa-close" 
                                           title="Clear" process="@this"
                                          style="margin-top:-5px;"
                                          disabled="#{contactService.deepEmailSearch == null}"
                                           id="btnEmailClear"
                                           actionListener="#{contactService.searchEmail('',contactService.contacts.contId)}"
                                           update=":contDetailsButtonForm:tvCont:deepContEmailSearch"
                                           class="button_top btn-danger btn-xs"  />
    </div>
                                        
    <!--PMS 2141 :CRM-3452  Can't see second page of emails, no "next page" button: paginatorAlwaysVisible=true -->
    <!--Feature #4374:  removed pagination--> 
    <p:dataTable id="dtContEmailList" value="#{contactService.listcontEmails}" 
                 var="contEmail" 
                
                 selectionMode="single" 
                 widgetVar="contEmailListTable" 
                 filterEvent="keyup" 
                 draggableColumns="true"
                 emptyMessage="No Emails found."  
                 rowKey="#{contEmail.recId}"
                 class="tblmain">

        <p:column style="width: 50px">
            <!--            //PMS:2565
                        //seema -03/12/2020-->
            <p:commandButton  value=""  title="Preview" icon="fa fa-eye" id="btnEmailPreview"
                              style="border: none;height: 28px;width: 30px"
                              actionListener="#{contactService.storeEmailContent(contEmail)}"
                              update=":contDetailsButtonForm:tvCont:prevContEmail"
                              oncomplete="PF('overlay').show()" 
                              process="@this"
                              />
        </p:column>
        <!--         //#2263 :CRM 1363:Email privacy
                //Seema - 07/11/2019-added header text-->
        <p:column style="width: 60px" headerText="Private"  id="searchcontEmailPrv">
<!--            <p:selectBooleanCheckbox value="#{contEmail.privateFlag}" id="checkPrivate" title="Private" >
                <p:ajax event="change" listener="#{contactService.chnagePrivatFlag(eml.emailId, eml.privateFlag)}" />
            </p:selectBooleanCheckbox>-->
            <!--            //As confirmed by manager on 17/10/2019 checkbox is changed to graphic image-->
            <h:graphicImage library="images" name="greenFlag.png" rendered="#{contEmail.privateFlag}" id="checkPrivate" title="Private"
                            width="16" height="16" />
        </p:column>
    <!--        <p:column headerText="id" sortBy="#{contEmail.emailId}">
        #{contEmail.emailId}
    </p:column>-->
        <p:column headerText="Subject"  class="emlpad"  style="min-width: 200px;max-width: 200px"  
                  sortBy="#{contEmail.emailSubject}" filterMatchMode="contains" id="searchcontEmailSubject" > 
            <!--CRM-216: Gentsch: Lynn cant open email: updated sales team id parameter for setRandomUid from opp to contacts -->
            <!--            //PMS:2565
                     //seema -03/12/2020-->
            <p:commandLink rendered="#{(contEmail.emailUserId ne loginBean.userId)}"
                           process="@this" target="_blank" id="cmndLnkEmailSubject"
                           actionListener="#{contactService.setRandomSmanid(contEmail.emailUserId,contEmail.contId)}" 
                           oncomplete="emailAction(#{contEmail.emailUserId},#{contEmail.emailId},'read',false)"
                           update=":contDetailsButtonForm:tvCont:lblreq :contDetailsButtonForm:tvCont:lbldeleg"
                           >
                #{contEmail.emailSubject}
            </p:commandLink>
            <!--            //PMS:2565
                                 //seema -03/12/2020-->
            <p:commandLink process="@none" type="button" id="cmndLnkEmailBody"
                           rendered="#{(contEmail.emailUserId eq loginBean.userId)}"
                           onclick="emailAction(#{contEmail.emailUserId},#{contEmail.emailId}, 'read', true)" 
                           value="#{contEmail.emailSubject}" />
        </p:column>

        <p:column style="width: 40px;" >
            <f:facet name="header">
                  <!--            //PMS:2565
                                 //seema -03/12/2020-->
                  <h:graphicImage library="images" class="fa fa-paperclip" id="grpImgAtch"/>
            </f:facet>
            <h:graphicImage library="images" class="fa fa-paperclip" rendered="#{contEmail.emailAttachFlag==1}" id="grpImgAtch1"/>
        </p:column>


        <p:column headerText="From" class="emlpad" style="min-width: 200px;max-width: 200px"  sortBy="#{contEmail.emailFrom}"
                  filterMatchMode="contains" id="searchcontEmailFrm">
            #{contEmail.emailFrom}
        </p:column>

        <p:column headerText="To"  class="emlpad" style="min-width: 200px;max-width: 200px" sortBy="#{contEmail.emailTo}" 
                  filterMatchMode="contains" id="searchcontEmailTo" >
            #{contEmail.emailTo}
        </p:column>


        <p:column headerText="Date"  class="emlpad"   style="width: 115px;text-align: center"  >
              <!--            //PMS:2565
                                 //seema -03/12/2020-->
              <h:outputLabel  style="font-size: 0.95em" value="#{rFUtilities.getCurrentTimeZoneDate(contEmail.emailDate, contEmail.emailTime)}" id="oLblEmailDate">
                <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
            </h:outputLabel>

        </p:column>

        <p:column headerText="Action"  style="width: 111px"  >
            <!--reply-->
              <!--            //PMS:2565
                                 //seema -03/12/2020-->
            <p:commandButton rendered="#{(contEmail.emailUserId ne loginBean.userId)}" title="Reply"
                             style="border: none;height: 28px;width: 30px" id="btnEmailReply"
                             process="@this" value="" icon="fa fa-mail-reply"
                             actionListener="#{contactService.setRandomSmanid(contEmail.emailUserId,contEmail.contId)}" 
                             update=":contDetailsButtonForm:tvCont:lblreq :contDetailsButtonForm:tvCont:lbldeleg"
                             oncomplete="emailAction(#{contEmail.emailUserId},#{contEmail.emailId},'reply',false)" 
                             />

            <!--this will render if user is owner, or email userid is same as loggedin user-->
            <p:commandButton value="" icon="fa fa-mail-reply" title="Reply" id="btnEmailReply1"
                             rendered="#{(contEmail.emailUserId eq loginBean.userId)}" 
                             style="border: none;height: 28px;width: 30px"
                             onclick="emailAction(#{contEmail.emailUserId},#{contEmail.emailId}, 'reply', true)"
                             />


            <!--replyall-->
            <p:commandButton rendered="#{(contEmail.emailUserId ne loginBean.userId)}" title="Reply All"  
                             style="border: none;height: 28px;width: 30px" id="btnEmailReplyAll"
                             actionListener="#{contactService.setRandomSmanid(contEmail.emailUserId,contEmail.contId)}" 
                             oncomplete="emailAction(#{contEmail.emailUserId},#{contEmail.emailId}, 'replyall',false)" 
                             update=":contDetailsButtonForm:tvCont:lblreq :contDetailsButtonForm:tvCont:lbldeleg"
                             process="@this"  value="" icon="fa fa-mail-reply-all" 
                             />

            <!--this will render if user is owner, or email userid is same as loggedin user-->
            <p:commandButton value="" icon="fa fa-mail-reply-all"  title="Reply All" 
                             style="border: none;height: 28px;width: 30px" id="btnEmailReplyAll1"
                             onclick="emailAction(#{contEmail.emailUserId},#{contEmail.emailId}, 'replyall', true)"
                             rendered="#{(contEmail.emailUserId eq loginBean.userId)}" />


            <!--forward-->
            <p:commandButton rendered="#{(contEmail.emailUserId ne loginBean.userId)}"
                             style="border: none;height: 28px;width: 30px" id="btnEmailFwd"
                             process="@this"  value="" icon="fa fa-mail-forward"
                             actionListener="#{contactService.setRandomSmanid(contEmail.emailUserId,contEmail.contId)}" 
                             oncomplete="emailAction(#{contEmail.emailUserId},#{contEmail.emailId}, 'forward',false)" 
                             update=":contDetailsButtonForm:tvCont:lblreq :contDetailsButtonForm:tvCont:lbldeleg"
                             title="Forward" 

                             />
            <!--this will render if user is owner, or email userid is same as loggedin user-->
            <p:commandButton value=""  title="Forward" 
                             icon="fa fa-mail-forward" id="btnEmailFwd1"
                             style="border: none;height: 28px;width: 30px"
                             onclick="emailAction(#{contEmail.emailUserId},#{contEmail.emailId}, 'forward', true)"
                             rendered="#{(contEmail.emailUserId eq loginBean.userId)}" />
        </p:column>

    </p:dataTable>
    <!--//   Feature #4374: CRM-4255: Company details: surface emails tab-->

    <p:commandButton value="Load More..."  immediate="true"    style="float: right;height: 30px" 
                      class="btn btn-success btn-xs" 
                     actionListener="#{contactService.loadMore(contactService.listcontEmails.size(),contactService.contacts.contId )}"  
                       />

    <h:inputHidden id="lblreq" value="#{contactService.rnd}"/>
    <h:inputHidden id="lbldeleg" value="#{contactService.chkDeleg}"/>

    <p:dialog id="deml" header="Send Email" widgetVar="wgdlgDeleg" height="60" width="380" closeOnEscape="true" >
        <h:panelGrid columns="3">
            <h:outputLabel id="emulabel" value="Send as" />
            <p:commandButton onclick="window.open(urlUser);
                PF('wgdlgDeleg').hide();" value="Current User"  type="button" title="Reply"  class="button_top btn_tabs" style="background: none;" id="btnEmailCurrentUser" />
            <p:commandButton onclick="window.open(urlDeleg);
                    PF('wgdlgDeleg').hide();" value ="Delegate" type="button"  title="Reply"  class="button_top btn_tabs" style="background: none;" id="btnEmailDelegate"/>
        </h:panelGrid>
    </p:dialog>


    <!--<p:dialog resizable="false"  position="center" header="Email Preview" id="prevContEmail" widgetVar="overlay" closeOnEscape="true" closable="true" responsive="true" >-->
    <p:dialog resizable="false"  position="center" header="Email Preview" height="450" width="580"  id="prevContEmail" widgetVar="overlay" closeOnEscape="true" closable="true" >

        <table>              
            <tr style="height: 25px">
                <td style="width: 70px">
                    <h:outputLabel value="To" style="font-weight: bold" id="oLblEmailTo"/></td>
                <td style="width: 15px">:</td>
                <td >
                    <h:outputText id="txtEmlTo"   value="#{contactService.viewContEmails.emailTo}"  /></td>
            </tr>
            <tr style="height: 25px">
                <td>
                    <h:outputLabel value="From" style="font-weight: bold" id="oLblEmailFrom"/></td>
                <td style="width: 15px">:</td>
                <td >
                    <h:outputText id="txtEmlFrm"  value="#{contactService.viewContEmails.emailFrom}" /></td>
            </tr>
<!--            //      #11078  ESCALATIONS CRM-6883   Companies: Email tab under company not working as expected-->
            <tr style="height: 25px">
                <td>
                    <h:outputLabel value="Cc" style="font-weight: bold" id="oLblEmailCC"/></td>
                <td style="width: 15px">:</td>
                <td >
                    <h:outputText id="txtEmlCC"  value="#{contactService.viewContEmails.emailCc}" /></td>
            </tr>
            <tr style="height: 25px">
                <td >
                    <h:outputLabel value="Subject"  style="font-weight: bold" id="oLblSubejct"/></td> 
                <td style="width: 15px">:</td>
                <td>
                    <h:outputText id="txtEmlSubj"  value="#{contactService.viewContEmails.emailSubject}" /></td>
                <td>
                    <h:graphicImage id="imgEmlAtch" value="#{contactService.viewContEmails.emailAttachFlag}" rendered="#{contactService.viewContEmails.emailAttachFlag == 1}"  library="images" name="attach_icon.png" /></td>
            </tr>
        </table>
        <hr></hr>
        <div style="width: 548px;overflow-wrap: break-word;">
            <h:outputText id="txtEmlBody" value="#{contactService.viewContEmails.emailContent}" escape="false"  style="width: 548px"    />
        </div>
    </p:dialog>
    <script>
        var logusr =#{loginBean.userId};
        var urlUser, urlDeleg, chk = 0, rq;
        var intrval, indx = 0;
        var hide = 1;
        // set enable/disable preview email hide
        function setHidePreview() {
            if (hide == 1) {
                hide = 2;
            } else {
                hide = 1;
            }
            //PF('overlay').show();
        }

        // email fields are stored on string array in list
        // on mouse over email details are passing as params
        // fields are passed in single quote, no need to add here

        function emailAction(eusr, mailid, act, acces) {
            if (act === 'read') {
                urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=maillist&amp;email_id=' + mailid;
            } else {
                if (act === 'forward') {
                    urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=forward&amp;email_id=' + mailid;
                } else {
                    if (act === 'reply') {
                        urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=reply&amp;email_id=' + mailid;
                    } else {
                        urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=replyall&amp;email_id=' + mailid;
                    }
                }
            }


            //current user have accss, so openemail tab
            if (acces == true) {
                openTab(urlUser);
                return;
            }

            // if not owner, not same user,chek value of lbldeleg
            // 1=in salesteam,2=in delegates, 3=both
            chk = document.getElementById('contDetailsButtonForm:tvCont:lbldeleg').value;
            // random id generated for email user id
            rq = document.getElementById('contDetailsButtonForm:tvCont:lblreq').value;
            //alert('chk : ' + chk + '; act : ' + act);
            //user in sales team
            if (chk == 1) {
                openTab(urlUser);
                return;
            }


            //user in deleg or in both sales tem and deleg
            if (chk === 2 || chk === 3) {


                if (act === 'read') {
                    urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=maillist&amp;email_id=' + mailid;
                    alert(urlUser);
                } else {
                    if (act === 'forward') {
                        urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=forward&amp;email_id=' + mailid;
                    } else {
                        if (act === 'reply') {
                            urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=reply&amp;email_id=' + mailid;
                        } else {
                            urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=replyall&amp;email_id=' + mailid;
                        }
                    }
                }

                document.getElementById('contDetailsButtonForm:tvCont:emulabel').innerHTML = getLabel(act);
                PF('wgdlgDeleg').show();
            } else {
                // email user not in deleg,sales team
                PF('growlCont').renderMessage({"summary": " No access to email ", "severity": "warn"});
            }

        }

        function openTab(url) {
            var win = window.open(url, '_blank');
            win.focus();
        }

        function getLabel(act) {
            var lblAction;
            switch (act) {
                case 'read':
                    lblAction = "View email as : ";
                    break;
                case 'reply':
                    lblAction = "Reply as : ";
                    break;
                case 'replyall':
                    lblAction = "Reply All as : ";
                    break;
                case 'forward':
                    lblAction = "Forward as : ";
                    break;
            }
            return lblAction;
        }

    </script>
</ui:composition>