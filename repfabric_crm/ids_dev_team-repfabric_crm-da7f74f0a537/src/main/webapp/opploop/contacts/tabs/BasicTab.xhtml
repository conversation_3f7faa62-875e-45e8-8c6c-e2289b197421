<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: BasicTab.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >




<!--    <p:remoteCommand immediate="true" autoRun="false" name="applyComp" actionListener="#{contactService.updateCompany(viewCompLookupService.selectedCompany)}" update=":contDetailsButtonForm:tvCont:itContCompName1 :contDetailsButtonForm:tvCont:itcompType :contDetailsButtonForm:tvCont:itSalesTeam"/>-->
    <!--    <p:focus for="itFName"/>    -->
    <p:remoteCommand immediate="true" autoRun="false" name="updatComp" actionListener="#{contactService.setIndContComp(false)}" />
  <!--Bug #11265 ESCALATIONS CRM-7002   Repfabric: Inquiry on Globally Visible by harshithad on 31/05/23-->
  <!--26-06-2023 11433 CRM-7048   Repfabric: Turning text phone numbers into tel: links-->
    <!--<p:remoteCommand immediate="true" autoRun="true" update="chkglobalvisble" />-->
    <div>
        <h:panelGroup id="contPnl">
            <div class="ui-g ui-fluid header-bar">
                <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                    <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Basic Information</p:outputLabel>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                </div>
            </div>  
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <div class="ui-g ui-fluid">
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Title" for="onemenuTitle" rendered="false" id="lblTitle"/>                        
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                            <p:selectOneMenu id="onemenuTitle" value="#{contactService.contacts.contTitle}" rendered="false">
                                <f:selectItem itemLabel="Select Title" itemValue=""/>
                                <f:selectItem itemLabel="Mr." itemValue="Mr."/>
                                <f:selectItem itemLabel="Ms." itemValue="Ms."/>
                                <f:selectItem itemLabel="Mrs." itemValue="Mrs."/>
                                <f:selectItem itemLabel="Dr." itemValue="Dr."/>
                            </p:selectOneMenu>

                        </div>                     
                        <div class="ui-sm-12 ui-md-6 ui-lg-6">                            
                        </div>

                        <div class="ui-sm-12 ui-md-3 ui-lg-3">

                            <!-- #4644: added class at outputLables of all fields. class and disabled attribute at input fields -->
                            <p:outputLabel value="First Name" styleClass="required" for="itFName" id="lblFirstName"/>                        
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9" >
                            <!--2812 - Contact Details>First Name, Last name Not auto capitalises first letter on user value entry - sharvani -31-12-2019-->
                            <p:inputText maxlength="30"   id="itFName"  value="#{contactService.contacts.contFname}"  label="First Name" style="text-transform: capitalize; width: 100%" required="true" requiredMessage="First name is required" styleClass="required" tabindex="1">
                                <p:ajax event="blur" rendered="itLFulame" update=":contDetailsButtonForm:tvCont:itLFulame" listener="#{contactService.fillFullName()}"/>      
                            </p:inputText>

                        </div>   
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Last Name" for="itLName" id="lblLastName"
                                           class="#{fieldsMstService.fields.get('LAST_NAME') == 1 ? 'required':''}" />                        
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9" >
                            <!--2812 - Contact Details>First Name, Last name Not auto capitalises first letter on user value entry - sharvani -31-12-2019-->
                            <p:inputText maxlength="30"  styleClass="tds1" id="itLName" value="#{contactService.contacts.contLname}" label="Last Name" style="text-transform: capitalize;width: 100%" tabindex="2"
                                         required="#{fieldsMstService.fields.get('LAST_NAME') == 1}" 
                                         requiredMessage="Last Name is required"
                                         disabled="#{fieldsMstService.invisibleFields.get('LAST_NAME')==1}">
                                <p:ajax event="blur" rendered="itLFulame" update=":contDetailsButtonForm:tvCont:itLFulame" listener="#{contactService.fillFullName()}" />     
                            </p:inputText> 
                        </div>  

                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Full Name" for="itLFulame" id="lblFullName"/>                        
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9" >
                            <p:outputLabel styleClass="tds1" id="itLFulame" value="#{contactService.contacts.contFullName}" style="width: 100%"  />
                        </div> 
                    </div>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                </div>
            </div>
            <div class="ui-g ui-fluid header-bar">
                <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                    <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Business Information</p:outputLabel>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                </div>
            </div> 

            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <div class="ui-g ui-fluid">
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <!--2403 - Updates to Contact creation - sharvani - 22-11-2019-->
                            <p:outputLabel value="Company" for="itContCompName1" styleClass="#{contactService.contacts.recId ==0?'required' : ''}" id="lblCompany"/> 
                           
                        </div>
                        <div class="ui-sm-12 ui-md9 ui-lg-9">
                            <h:panelGroup class="ui-inputgroup"  > 
                                  <!--//       #11288 ESCALATIONS CRM-6882   Quotes: Unable to edit-->
                                <h:inputHidden id="contCompId" value="#{contactService.contCompanyId}"/>
                                <!-- #4978: CRM-4457: SilverMountain: Contacts cant be orphaned anymore -->
                                <p:inputText  id="itContCompName1" value="#{contactService.contacts.contCompName}" styleClass="copyBusFld" style="width: 100%" 
                                              required="#{contactService.contacts.recId==0}" requiredMessage="Company is required" readonly="#{facesContext.renderResponse}"
                                              placeholder="[Select Company]">                                
                                </p:inputText> 
                                 <!--//#6641: CRM-5093: need SAVE AND NEW button under CONTACTS - poornima-->
                                <!--<h:inputText id="hdnContCompName1" value="#{contactService.contacts.contCompName}"  styleClass="copyBusFld"  />-->
                                <!--                                PMS 1969 Individual Contact company option-->
                                <!--                                PMS 3618:Restrict person company in contact company lookup-->
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                  tabindex="3" rendered="#{contactService.contacts.recId==0}"
                                                  action="#{viewCompLookupService.listAll('applyContComp',0)}" 
                                                  actionListener="#{viewCompLookupService.setFromCont(true)}"
                                                  update=":formCompLookup :contDetailsButtonForm:tvCont:contCompId" oncomplete="PF('lookupComp').show()"  
                                                  id="btnCreateComp"
                                                  styleClass="btn-info btn-xs" />
                                <!--                                1969 Individual Contact company option-->
                                <!--                                PMS 3618:Restrict person company in contact company lookup-->
                                <p:commandButton  icon="fa fa-search" title="Choose Company" 
                                                  immediate="true" rendered="#{contactService.contacts.recId>0}"  
                                                  tabindex="3"
                                                  action="#{viewCompLookupService.listAll('applyUpdContComp',1)}"  
                                                  actionListener="#{viewCompLookupService.setFromCont(true)}"
                                                  update=":formCompLookup :contDetailsButtonForm:tvCont:contCompId" oncomplete="PF('lookupComp').show()" 
                                                  onclick="updatComp();" 
                                                  id="btnUpdateComp"
                                                  styleClass="btn-info btn-xs" />
                                <!--#1335: MIGUAT-170: contacts: creating a company within a contact way too buried-->
                                <p:spacer width="4" />
                                <p:commandButton  styleClass="btn-primary btn-xs" style="width:25px" title="#{!loginBean.userCompanyCreateAccess()? 'Add New Company': 'You are not authorized to create company'}" icon="fa fa-plus"
                                                  immediate="true" resetValues="true" 
                                                  disabled="#{loginBean.userCompanyCreateAccess()}"
                                                  id="btnAuthorize"
                                                  actionListener="#{viewCompLookupService.initFromContact('applyContComp')}"
                                                  action="#{viewCompLookupService.initiliaze()}" update=":formNewComp :contDetailsButtonForm:tvCont:contCompId" 
                                                  oncomplete="PF('newCompDlg').show()"/> 
                                <p:spacer width="4" />
                                <!--                            #2164: Contact Details -> Option to remove company-->
                                <p:commandButton id="clearcomp"  styleClass="btn-danger btn-xs" style="width:25px" title="Clear Company" icon="fa fa-times"
                                                 immediate="true" resetValues="true" onclick="PF('confirmResetBusi').show();"
                                                 update=":formNewComp :contDetailsButtonForm:tvCont:contCompId" disabled="#{contactService.contacts.contCompName.length()==0 or contactService.contacts.contCompName==null }" /> 
                            </h:panelGroup>
                        </div>
                    </div>
                </div>
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <div class="ui-g ui-fluid">
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Company Type" for="itcompType" id="lblCompanyType"/>                 
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel styleClass="copyBusFld" id="itcompType" value="#{contactService.contacts.contComytype}"  style="width: 100%"  />
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')}" for="itSalesTeam" id="lblSaleaTeam"/>                 
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel id="itSalesTeam" value="#{contactService.contacts.compSalesTeam}"  styleClass="copyBusFld"/>
                        </div>
                    </div>
                </div>

                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <div class="ui-g ui-fluid">
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="#{custom.labels.get('IDS_JOB_TITLE')}" for="itJobTitle" id="lblJobtitle"
                                           class="#{fieldsMstService.fields.get('JOB_TITLE') == 1 ? 'required':''}" />                 
                        </div>
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="100"   styleClass="tds1" id="itJobTitle" value="#{contactService.contacts.contJobTitle}" label="#{custom.labels.get('IDS_JOB_TITLE')}" style="width: 100%" tabindex="4"
                                         required="#{fieldsMstService.fields.get('JOB_TITLE') == 1}" 
                                         requiredMessage="#{custom.labels.get('IDS_JOB_TITLE')} is required"
                                         disabled="#{fieldsMstService.invisibleFields.get('JOB_TITLE') == 1}">
                                <p:ajax event="blur" process="itJobTitle"/>
                            </p:inputText>                    
                        </div>
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Manager" for="itManager" id="lblManager"
                                           class="#{fieldsMstService.fields.get('MANAGER') == 1 ? 'required':''}" />                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="40"  styleClass="tds1" id="itManager" value="#{contactService.contacts.contManagerName}" label="Manager" style="width: 100%"  tabindex="6" 
                                         required="#{fieldsMstService.fields.get('MANAGER') == 1}" 
                                         requiredMessage="Manager is required"
                                         disabled="#{fieldsMstService.invisibleFields.get('MANAGER') == 1}">
                                <p:ajax event="blur" process="itManager"/>
                            </p:inputText>
                        </div>
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Referred By" for="itRefferedBy" id="lblReferredBy"
                                           class="#{fieldsMstService.fields.get('REFERRED_BY') == 1 ? 'required':''}" />                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="40"   styleClass="tds1" id="itRefferedBy" value="#{contactService.contacts.contReferredBy}" label="Referred By" style="width: 100%" tabindex="8" 
                                         required="#{fieldsMstService.fields.get('REFERRED_BY') == 1}" 
                                         requiredMessage="Refferred By is required"
                                         disabled="#{fieldsMstService.invisibleFields.get('REFERRED_BY')==1}">
                                <p:ajax event="blur" process="itRefferedBy"/>
                            </p:inputText>
                        </div>


                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            
                            <!--11/11/2021: PMS 6312: CRM-4950: Be able to re-label "Primary" field to "RF Admin" in Prod1-->
                            <p:outputLabel  value="#{custom.labels.get('IDS_PRIMARY_CONTACT')}" for="chkPrimary" id="lblPrimary"/>                        
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                             <!--#5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact-->
                            <p:selectBooleanCheckbox widgetVar="wvChkPrimary" id="chkPrimary" value="#{contactService.contacts.chkcontPrimaryFlag}" tabindex="10"> 
                            </p:selectBooleanCheckbox>
                        </div> 
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Globally Visible" for="chkglobalvisble" id="lblGloballyVisible"/>                        
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                             <!--#5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact-->
                            <p:selectBooleanCheckbox widgetVar="wvChkglobalvisble" id="chkglobalvisble" value="#{contactService.contacts.chkglobalVisibleFlag}" tabindex="11">                            
                            </p:selectBooleanCheckbox>
                        </div> 
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Street" for="itStreet" id="lblStreet"
                                           class="#{fieldsMstService.fields.get('STREET') == 1 ? 'required':''}"/>                 
                        </div>
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputTextarea  id="itStreet" 
                                              value="#{contactService.contacts.contBusiAddress1}" 
                                              label="Street" style="width: 100%;resize: none" 
                                              styleClass="copyBusFld" rows="2" autoResize="false"  
                                              maxlength="180" tabindex="13"
                                              required="#{fieldsMstService.fields.get('STREET') == 1}" 
                                              requiredMessage="Street is required"
                                              disabled="#{fieldsMstService.invisibleFields.get('STREET')==1}">
                                <p:ajax event="blur" process="itStreet"/>
                            </p:inputTextarea>
                        </div>
                        <!--2805 - JSF - Add PO Box below Street in Contact Details -> Basic and Personal tab- sharvani -02-01-2020-->
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="PO Box" for="itPObox" id="lblPOBox"
                                           class="#{fieldsMstService.fields.get('PO_BOX') == 1 ? 'required':''}"/>                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <!--                            PMS 3130:CRM-3805: Aliased contacts not being absorbed-->
                            <p:inputText id="itPObox" 
                                         value="#{contactService.contacts.contBusiPoBox}" 
                                         label="PO Box" style="width: 100%;" 
                                         styleClass="copyBusFld"  
                                         maxlength="30" tabindex="16"
                                         required="#{fieldsMstService.fields.get('PO_BOX') == 1}"
                                         requiredMessage="PO Box is required"
                                         disabled="#{fieldsMstService.invisibleFields.get('PO_BOX')==1}">
                                <p:ajax event="blur" process="itPObox"/> 
                            </p:inputText>
                        </div>
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <!--#1828 - CRM-3042: Intralec: change labels for state and zip - sharvanim - 25-09-2020-->
                            <p:outputLabel value="#{custom.labels.get('IDS_ZIP')}" for="itZipCode" id="lblZip"
                                           class="#{fieldsMstService.fields.get('ZIP_CODE') == 1 ? 'required':''}" />                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="12"  styleClass="copyBusFld" id="itZipCode" value="#{contactService.contacts.contBusiZipCode}" label="Zip code" style="width: 100%"  tabindex="18"
                                         required="#{fieldsMstService.fields.get('ZIP_CODE') == 1}" 
                                         requiredMessage="#{custom.labels.get('IDS_ZIP')} is required"
                                         disabled="#{fieldsMstService.invisibleFields.get('ZIP_CODE')==1}">
                                <p:ajax event="blur" process="itZipCode"/>
                            </p:inputText> 
                        </div>
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Mobile" for="itMobile" id="lblMobile"
                                           class="#{fieldsMstService.fields.get('MOBILE') == 1 ? 'required':''}" />                 
                                             
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="40"  styleClass="copyBusFld" id="itMobile" value="#{contactService.contacts.contPhoneMobile}" label="Mobile" style="width: 100%"  tabindex="20"
                                         required="#{fieldsMstService.fields.get('MOBILE') == 1}"  
                                         requiredMessage="Mobile is required"
                                         disabled="#{fieldsMstService.invisibleFields.get('MOBILE')==1}">
                                <p:ajax event="blur" process="itMobile"/>
                            </p:inputText>
                        </div>
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="#{custom.labels.get('IDS_ALT_PHONE')}" for="itAltPhn" id="lblAltPhn"
                                           class="#{fieldsMstService.fields.get('ALT_PHONE') == 1 ? 'required':''}"/>                  
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <!--2469 - CRM-1522: MANUTEK Fw: Business Phone Field Limited Characters - ext # cuts off - sharvani - 23-11-2019-->
                            <p:inputText  maxlength="40"  styleClass="copyBusFld" id="itAltPhn" value="#{contactService.contacts.contPhoneAlternate}" label="#{custom.labels.get('IDS_ALT_PHONE')}" style="width: 100%" tabindex="22"
                                          required="#{fieldsMstService.fields.get('ALT_PHONE') == 1}" 
                                          requiredMessage="#{custom.labels.get('IDS_ALT_PHONE')} is required" 
                                          disabled="#{fieldsMstService.invisibleFields.get('ALT_PHONE')==1}">
                                <p:ajax event="blur" process="itAltPhn"/>
                            </p:inputText>
                        </div>
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Business Email" for="itBusiEmail" id="lblBusinessEmail"
                                           class="#{fieldsMstService.fields.get('BIZ_EMAIL1') == 1 ? 'required':''}" />                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="100"  styleClass="tds1" id="itBusiEmail" value="#{contactService.contacts.contEmailBusiness}" label="Business Email" style="width: 100%" validatorMessage="Invalid email format" tabindex="24"
                                         required="#{fieldsMstService.fields.get('BIZ_EMAIL1') == 1}" 
                                         requiredMessage="Business Email is required" 
                                         disabled="#{fieldsMstService.invisibleFields.get('BIZ_EMAIL1')==1}">
                                <!--                          Task 2533:  CRM-1562: Fwd: Jim O'Donnell email address in RepFabric-->
                                <!--#32- - sharvani - CRM-2344:Contacts:email address entry:chop off leading and trailing white - sharvani - 03-04-2020-->
                                <!--                                <f:validateRegex
                                                                    pattern="^[_A-Za-z0-9-\+']+(\.[_A-Za-z0-9-']+)*@[A-Za-z0-9-]+(\.[A-Za-z0-9]+)*(\.[A-Za-z]{2,})$" /> -->
                                <!--#84 - CRM-2348: arkco: email address w subdomain wont save   13-04-2020- sharvani--> 
                                <!--#328- CRM-2487: Error with Email address? Paul Gotceitas - 29-04-2020- sharvani-->
                            <!--//Bug #5783:   CRM-4746: invalid email format-->
                                <!--                               
                     <f:validateRegex
                                    pattern="^\s*[_A-Za-z0-9-\+']+(\.[_A-Za-z0-9-']+)*@(([A-Za-z0-9]+\.)*[A-Za-z0-9-_]{2,}\.)+[A-Za-z]{2,}\s*$" /> 
                                <p:ajax event="blur" process="itBusiEmail"/>-->
                            </p:inputText> 
                        </div>

                        <!-- #4672: added sync enable flag -->
                       
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                              <p:outputLabel value="Enable Forced Sync" for="chkSyncEnable" id="lblSyncEnable"
                                           />                
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                             <!--#5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact-->
                            <p:selectBooleanCheckbox widgetVar="wvChkSyncEnable" id="chkSyncEnable" value="#{contactService.contacts.chkContSyncFlag}" tabindex="26"
                                                     /> 
                        </div>

                        
                        <!--                #2854    Related to CRM-1757: Hoffstetter: Product Potential vs. Contact Group:moved to next column-->
                        <!--                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                                <p:outputLabel value="Alternate Email" for="itAlterEmail"/>                 
                                            </div>
                                            <div class="ui-sm-12 ui-md-9 ui-lg-9">
                                                <p:inputText maxlength="100"  styleClass="tds1" id="itAlterEmail" value="#{contactService.contacts.contEmailAlternate}" label="Alternate Email" style="width: 100%" validatorMessage="Invalid email format" tabindex="25">
                                                  Task 2533:  CRM-1562: Fwd: Jim O'Donnell email address in RepFabric
                        
                                                    <f:validateRegex
                                                        pattern="^[_A-Za-z0-9-\+']+(\.[_A-Za-z0-9-']+)*@[A-Za-z0-9-]+(\.[A-Za-z0-9]+)*(\.[A-Za-z]{2,})$" />     
                                                    <p:ajax event="blur" process="itAlterEmail"/>
                                                </p:inputText>
                                            </div>-->

                    </div>
                </div>
                <!-- #4644: mandatory field from settings -->
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <div class="ui-g ui-fluid">
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Department" for="itDepartment" id="lblDepartment"
                                           class="#{fieldsMstService.fields.get('DEPARTMENT') == 1 ? 'required':''}" />                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="60"  styleClass="tds1" id="itDepartment" value="#{contactService.contacts.contDept}" label="Department" style="width: 100%" tabindex="5"
                                         required="#{fieldsMstService.fields.get('DEPARTMENT') == 1}" 
                                         requiredMessage="Department is required" 
                                         disabled="#{fieldsMstService.invisibleFields.get('DEPARTMENT')==1}">
                                <p:ajax event="blur" process="itDepartment"/>
                            </p:inputText> 
                        </div>
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Assistant" for="itAssistant" id="lblAssistant"
                                           class="#{fieldsMstService.fields.get('ASSISTANT') == 1 ? 'required':''}" />                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="40"  styleClass="tds1" id="itAssistant" value="#{contactService.contacts.contAssistantName}" label="Assistant" style="width: 100%" tabindex="7"
                                         required="#{fieldsMstService.fields.get('ASSISTANT') == 1}" 
                                         requiredMessage="Assistant is required" 
                                         disabled="#{fieldsMstService.invisibleFields.get('ASSISTANT')==1}">
                                <p:ajax event="blur" process="itAssistant"/>
                            </p:inputText> 
                        </div>
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <!--3-12-2020 TASK#2585: CRM-3606: Custom Fields -->
                            <p:outputLabel value="#{custom.labels.get('IDS_CONTEXT')}" for="itcontext" id="lblContext"
                                           class="#{fieldsMstService.fields.get('CONTEXT') == 1 ? 'required':''}" />                 

                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="80"  styleClass="tds1" id="itcontext" value="#{contactService.contacts.contContext}" label="Context" style="width: 100%"  tabindex="9"
                                         required="#{fieldsMstService.fields.get('CONTEXT') == 1}" 
                                         requiredMessage="#{custom.labels.get('IDS_CONTEXT')} is required"
                                         disabled="#{fieldsMstService.invisibleFields.get('CONTEXT')==1}">
                                <p:ajax event="blur" process="itcontext"/>
                            </p:inputText>
                        </div>
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="#{custom.labels.get('IDS_REGION')}" for="oneMenuRegion" id="lblRegion"
                                           class="#{fieldsMstService.fields.get('REGION') == 1 ? 'required':''}" />                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                             <!--#5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact-->
                             <!--//03-02-2023 : #10598 : ESCALATIONS CRM-6640  CONTACTS: some in the system twice-->
                            <p:selectOneMenu  widgetVar="wvOneMenuRegion2" id ="oneMenuRegion" value="#{contactService.contacts.contRegionId}" tabindex="11"  styleClass="copyBusFld"
                                              required="#{fieldsMstService.fields.get('REGION') == 1}"
                                              requiredMessage="#{custom.labels.get('IDS_REGION')} is required" 
                                              disabled="#{fieldsMstService.invisibleFields.get('REGION')==1}">
                                <f:selectItem itemLabel="Select #{custom.labels.get('IDS_REGION')}" />
                                <f:selectItems var="c" value="#{companyRegionsMstService.compRegionsLst}" itemLabel="#{c.compRegion}" itemValue="#{c.recId}" />
                                <p:ajax event="change" process="oneMenuRegion"/>
                            </p:selectOneMenu>
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="City" for="itCity" id="lblCity"
                                           class="#{fieldsMstService.fields.get('CITY') == 1 ? 'required':''}" />                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="40"  styleClass="copyBusFld" id="itCity" value="#{contactService.contacts.contBusiCity}" label="City" style="width: 100%"  tabindex="13"
                                         required="#{fieldsMstService.fields.get('CITY') == 1}" 
                                         requiredMessage="City is required" 
                                         disabled="#{fieldsMstService.invisibleFields.get('CITY')==1}">
                                <p:ajax event="blur" process="itCity"/>
                            </p:inputText>
    <!--                    <p:outputLabel   styleClass="tds1" value="#{contactService.contacts.contBusiCity}"  style="width: 100%"  tabindex="14"/>-->
                        </div>
                        <!-- #4644: mandatory field from settings --> 
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <!--#1828 - CRM-3042: Intralec: change labels for state and zip - sharvanim - 25-09-2020-->
                            <p:outputLabel value="#{custom.labels.get('IDS_STATE')}" for="itState" id="lblState"
                                           class="#{fieldsMstService.fields.get('STATE') == 1 ? 'required':''}" />                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="40"  styleClass="copyBusFld" id="itState" value="#{contactService.contacts.contBusiState}" label="State" style="width: 100%"  tabindex="14"
                                         required="#{fieldsMstService.fields.get('STATE') == 1}" 
                                         requiredMessage="#{custom.labels.get('IDS_STATE')} is reuired"
                                         disabled="#{fieldsMstService.invisibleFields.get('STATE')==1}">
                                <p:ajax event="blur" process="itState"/>
                            </p:inputText>
                        </div>
                        <!-- #4644: mandatory field from settings --> 
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Country" for="itCntryCode" id="lblCountry"
                                           class="#{fieldsMstService.fields.get('COUNTRY') == 1 ? 'required':''}" />                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="40"  styleClass="copyBusFld"  id="itCntryCode" value="#{contactService.contacts.contBusiCountry}" label="Country Code" style="width: 100%"  tabindex="16"
                                         required="#{fieldsMstService.fields.get('COUNTRY') == 1}" 
                                         requiredMessage="Country is required"
                                         disabled="#{fieldsMstService.invisibleFields.get('COUNTRY')==1}">
                                <p:ajax event="blur" process="itCntryCode"/>
                            </p:inputText>
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <!--                28-12-2020:TASK##2848-CRM-1756: Hofstetter: Custom Labels-->  
                            <!-- #4644: mandatory field from settings -->
                            <p:outputLabel value="#{custom.labels.get('IDS_CONT_BIZ_PHONE')}" for="itBusiPhn" id="lblBusinessPhone"
                                           class="#{fieldsMstService.fields.get('BIZ_PHON1') == 1 ? 'required':''}"/>                  
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <!--2469 - CRM-1522: MANUTEK Fw: Business Phone Field Limited Characters - ext # cuts off - sharvani - 23-11-2019-->
                            <p:inputText 
                                maxlength="40" styleClass="copyBusFld" id="itBusiPhn" 
                                value="#{contactService.contacts.contPhoneBusiness}" 
                                label="Business Phone" style="width: 100%" 
                                tabindex="18"
                                required="#{fieldsMstService.fields.get('BIZ_PHON1') == 1}" 
                                requiredMessage="#{custom.labels.get('IDS_CONT_BIZ_PHONE')} is required"
                                disabled="#{fieldsMstService.invisibleFields.get('BIZ_PHON1')==1}">
                                <p:ajax event="blur" process="itBusiPhn"/>
                            </p:inputText>
                        </div>
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Fax" for="itFax" id="lblFax"
                                           class="#{fieldsMstService.fields.get('FAX') == 1 ? 'required':''}"/>                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="20"  styleClass="tds1" id="itFax" value="#{contactService.contacts.contFax}" label="Fax" style="width: 100%" tabindex="20"
                                         required="#{fieldsMstService.fields.get('FAX') == 1}" 
                                         requiredMessage="Fax is required"
                                         disabled="#{fieldsMstService.invisibleFields.get('FAX')==1}">
                                <p:ajax event="blur" process="itFax"/>
                            </p:inputText> 
                        </div>
                        <!-- #4644: mandatory field from settings --> 
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Business Email 2" for="itBusiEmail2" id="lblBusinessEmail2"
                                           class="#{fieldsMstService.fields.get('BIZ_EMAIL2') == 1 ? 'required':''}" />                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="100"  styleClass="tds1" id="itBusiEmail2" value="#{contactService.contacts.contEmailBusiness2}" label="Business Email 2" style="width: 100%" validatorMessage="Invalid email format" tabindex="22"
                                         required="#{fieldsMstService.fields.get('BIZ_EMAIL2') == 1}" 
                                         requiredMessage="Business Email 2 is required"
                                         disabled="#{fieldsMstService.invisibleFields.get('BIZ_EMAIL2')==1}">
                                <!--                          Task 2533:  CRM-1562: Fwd: Jim O'Donnell email address in RepFabric-->
                                <!--#32- - sharvani - CRM-2344:Contacts:email address entry:chop off leading and trailing white - sharvani - 03-04-2020-->
                                <!--#84 - CRM-2348: arkco: email address w subdomain wont save   13-04-2020- sharvani-->  
                                <!--#328- CRM-2487: Error with Email address? Paul Gotceitas - 29-04-2020- sharvani-->                                
                            </p:inputText>
                        </div>
                        <!--                    #2854    Related to CRM-1757: Hoffstetter: Product Potential vs. Contact Group-->
                        <!-- #4644: mandatory field from settings -->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Alternate Email" for="itAlterEmail" id="lblAlternateEmail"
                                           class="#{fieldsMstService.fields.get('ALT_EMAIL') == 1 ? 'required':''}" />                 
                        </div>
                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                            <p:inputText maxlength="100"  styleClass="tds1" id="itAlterEmail" value="#{contactService.contacts.contEmailAlternate}" label="Alternate Email" style="width: 100%" validatorMessage="Invalid email format" tabindex="25"
                                         required="#{fieldsMstService.fields.get('ALT_EMAIL') == 1}" 
                                         requiredMessage="Alternate Email is required"
                                         disabled="#{fieldsMstService.invisibleFields.get('ALT_EMAIL')==1}">
                                <!--                          Task 2533:  CRM-1562: Fwd: Jim O'Donnell email address in RepFabric-->
                                <!--#32- - sharvani - CRM-2344:Contacts:email address entry:chop off leading and trailing white - sharvani - 03-04-2020-->
                                <!--#84 - CRM-2348: arkco: email address w subdomain wont save   13-04-2020- sharvani-->  
                                <!--#328- CRM-2487: Error with Email address? Paul Gotceitas - 29-04-2020- sharvani-->                                
                            </p:inputText>


                        </div>


                        <!--                    <div class="ui-sm-12 ui-md-3 ui-lg-3">
                                                <p:outputLabel value="#{custom.labels.get('IDS_CON_GRP')}" for="menuContGrp"/>                 
                                            </div>-->
                        <!--#2854    Related to CRM-1757: Hoffstetter: Product Potential vs. Contact Group-->
                        <!--<div class="ui-sm-12 ui-md-9 ui-lg-9">-->
                        <!--                        <h:panelGroup class="ui-inputgroup"  >
                                                    <p:outputPanel id="displayContGrp" style="width: 100%" 
                                                                   class="ui-selectonemenu-label ui-inputfield ui-corner-all potential-css"  >
                                                        <p:dataList  id="menuContGrp" style="height:90px;overflow: auto;width: 296px"
                                                                     value="#{contactService.contacts.selectDisContGrp}" 
                                                                     var="grp" styleClass="grpBox"
                                                                     emptyMessage="No #{custom.labels.get('IDS_CON_GRP')} selected">
                        #{grp.contGroupName}
                    </p:dataList>
                </p:outputPanel> 
                <p:commandButton icon="fa fa-search" style="width: 35px;height: 29px"
                                 title="Select #{custom.labels.get('IDS_CON_GRP')}" 
                                 styleClass="btn-info btn-xs"
                                 tabindex="24"
                                 onclick="PF('groupDlg').show()">

                </p:commandButton>
            </h:panelGroup>-->
                        <!--</div>-->
                        <!--#PMS-221 :Do not email-->
                        <div class="ui-sm-12 ui-md-3 ui-lg-3">
                            <p:outputLabel value="Do Not Email" for="doNotEmail" id="lblDoNotEmail"/>                        
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3" >
                             <!--#5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact-->
                            <p:selectBooleanCheckbox widgetVar="wvDoNotEmail" id="doNotEmail" value="#{contactService.contacts.contBncFlg}" tabindex="27"> 
                            </p:selectBooleanCheckbox>
                        </div> 
                        <!--                        <div class="ui-sm-12 ui-md-9 ui-lg-9">
                                                    <p:outputLabel  value="Do not emaill" />
                                                    
                                                </div>-->
                    </div>       
                </div>
            </div>

            <!-- #4644: mandatory field from settings -->
            <div class="ui-g ui-fluid header-bar">
                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                    <p:outputLabel styleClass="acct-name-hdr" style="color: #3c8dbc;"
                                   class="#{fieldsMstService.fields.get('NOTES') == 1 ? 'required':''}">
                        Notes
                    </p:outputLabel>
                </div>
                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                </div>
                <div class="ui-sm-12 ui-md-1 ui-lg-1">
                    <!-- #6369: Company Comments / Contact Notes: Timestamp cursor issue - poornima 
                    reduced width in sytle-->
                    <!--//22-01-2024 : #12967 : ESCALATIONS CRM-7657   Contacts : Add Current DateTime not working-->
                    <p:commandButton  id="btnCmntDate" icon="fa fa-clock-o" onclick="addDateContact()" class="btnClr btn-primary btn-xs"
                                      style="width:40px;float:right;margin-top: -5px;" title="Add Current DateTime" tabindex="28"  
                                      />
                </div>
            </div> 
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                <div class="ui-g ui-fluid">
                    <!-- #6548: contact  notes scrolling bar not showing - poornima //added autoResize to scroll -->
                    <p:inputTextarea  styleClass="tds1" id="itNote" value="#{contactService.contacts.contNotes}" label="Notes" style="width: 100%"  tabindex="29"
                                      autoResize="false" required="#{fieldsMstService.fields.get('NOTES') == 1 }" 
                                      requiredMessage="Notes is required" 
                                      disabled="#{fieldsMstService.invisibleFields.get('NOTES')==1}"/>
                </div>
            </div>
            <!-- #4644: mandatory field from settings -->
            <div class="ui-g ui-fluid header-bar">
                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                    <p:outputLabel styleClass="acct-name-hdr" value="#{custom.labels.get('IDS_CON_GRP')}" for="menuContGrp" id="lblContGrp" style="color: #3c8dbc;"
                                   class="#{fieldsMstService.fields.get('CONT_GROUP') == 1 ? 'required':''}"/>                 
                </div>
            </div>
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                 <!--#5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact-->
                <p:selectCheckboxMenu widgetVar="wvMenuContGrp" id="menuContGrp" value="#{contactService.contacts.selectDisplayContGrp}" multiple="true" filter="true"  filterMatchMode="contains" scrollHeight="300" style="width: 100%;  margin-left: -5px;" 
                                      required="#{fieldsMstService.fields.get('CONT_GROUP') == 1 }" 
                                      requiredMessage="#{custom.labels.get('IDS_CON_GRP')} is required"
                                      disabled="#{fieldsMstService.invisibleFields.get('CONT_GROUP')==1}"
                                      tabindex="30">
                    <f:selectItems value="#{contactGroupMstService.contactGroupLst}" var="grp" itemLabel="#{grp.contGroupName}" itemValue="#{grp.contGroupName}" />
                </p:selectCheckboxMenu>
                <p:outputPanel id="displayContGrp"  />
            </div>                
            <!--#238 - Contacts > Product Interest - 21-04-2020- sharvanim-->
            <!-- #4644: mandatory field from settings -->
            <div class="ui-g ui-fluid header-bar">
                <div class="ui-sm-12 ui-md-3 ui-lg-3">
                    <p:outputLabel styleClass="acct-name-hdr" value="#{custom.labels.get('IDS_PROD_INTEREST')}" for="prodIntrestLbl" id="lblProdIntrest" style="color: #3c8dbc;"
                                   class="#{fieldsMstService.fields.get('PROD_INTEREST') == 1 ? 'required':''}"/>                 
                </div>
            </div>
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                <!--#238 - Contacts > Product Interest - 21-04-2020- sharvanim-->   
                 <!--#5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact-->
                <p:selectCheckboxMenu widgetVar="wvProdIntrestLbl" id="prodIntrestLbl" value="#{contactService.contacts.selectDisplayProdInterset}" multiple="true" filter="true"  filterMatchMode="contains" scrollHeight="300" style="width: 100%;  margin-left: -5px;" 
                                      required="#{fieldsMstService.fields.get('PROD_INTEREST') == 1 }" 
                                      requiredMessage="#{custom.labels.get('IDS_PROD_INTEREST')} is required"
                                      disabled="#{fieldsMstService.invisibleFields.get('PROD_INTEREST')==1}"
                                      tabindex="31">
                    <f:selectItems value="#{productInterestService.prodInterestLst}" var="prdIntItems" itemLabel="#{prdIntItems.prodName}" itemValue="#{prdIntItems.prodName}"  />
                </p:selectCheckboxMenu>
                <p:outputPanel id="prodIntrestPanel"  />
            </div>

        </h:panelGroup>
    </div>

    <style>
        .grpBox  .ui-widget-content  ul{
            padding:0 20px!important;
            margin:0px!important;

        }
        .ui-selectcheckboxmenu-multiple-container{
            width:100%; 
            height: auto !important;

        }

    </style>
    <!--  05/04/2021  Bug #4433 divine: comment insertion: start at the front as a new row and properly format-->

    <script type="text/javascript" >
        //22-01-2024 : #12967 : ESCALATIONS CRM-7657   Contacts : Add Current DateTime not working
        function addDateContact() {
            var curDate = '#{contactService.getCurrentDate()}';
            var cmt = document.getElementById('contDetailsButtonForm:tvCont:itNote');
            curDate = curDate + " ";
            //#6369: Company Comments / Contact Notes: Timestamp cursor issue - poornima 
            //on click of date - moving cusror to next line to add comment
            cmt.value = curDate + "\n \n" + cmt.value;            
            cmt.setSelectionRange(curDate.length + 1, curDate.length + 1);
            cmt.focus();
        }
    </script>
</ui:composition>