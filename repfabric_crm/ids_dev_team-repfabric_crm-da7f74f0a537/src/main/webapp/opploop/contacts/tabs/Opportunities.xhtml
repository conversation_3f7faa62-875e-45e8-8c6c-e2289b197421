<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: Opportunities.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                >
    <div class="box-header with-border">
        <div class="row">
            <div class="col-md-5">
<!--                <p:button value="New"  styleClass="btn btn-primary btn-xs"  href="#{contacts.createOpportunityPage(contactService.contacts.contCompName)}" 
                          includeViewParams="true"/>-->
                <!--#441 - Inactive companies: AJ,Opps > New button enabled and AJ,opps can be create - 12-05-2020- sharvani-->

                <!--            //PMS:2565
                              //seema -03/12/2020-->
                <p:button value="New"  styleClass="btn btn-primary btn-xs" 
                          href="../opportunity/OpportunityView.xhtml?cnid=#{contactService.contacts.contId}&amp;cmid=#{contactService.contacts.contCompId}&amp;cm=#{contactService.contacts.contcompTypeId}" 
                          includeViewParams="true" disabled="#{(contactService.contacts.compActivFlag==0)}"
                          id="btnContNewOpp"
                          />

                <p:spacer width="4"/>

                <p:commandButton action="#{contactService.refreshOppList()}"  icon="fa fa-refresh" title="Refresh"
                                 id="btnContOppRefresh"
                                 class="btn-xs btn-primary" update=":contDetailsButtonForm:tvCont:dtContOpList" />

            </div>  
            <!--//12-04-2023 : #11054 :  CRM-6807: Signalent: default contact opps to open opps only-->
            <div class="col-md-5"></div>
            <div class="col-md-2">
                <p:selectBooleanCheckbox value="#{contactService.contactRadio}" style="margin-top: 2px;" >
                <p:ajax event="change" listener="#{contactService.populateOppData(contactService.contacts.contId)}" 
                        update="contDetailsButtonForm:tvCont:dtContOpList" />
                </p:selectBooleanCheckbox>
                <p:spacer width="4"/>
                <p:outputLabel value="Show All" style="margin-top:-8px;" />
            </div>
        </div> 
    </div>
    <p:growl id="lstmsgs"  escape="false" life="6000" showDetail="false" showSummary="true"/>
    <p:dataTable id="dtContOpList" value="#{contactService.contOppList}" selection="#{contactService.selectedOpp}"
                 var="contOpp" 
                 rows="50" 
                 selectionMode="single" 
                 widgetVar="contOppListTable" 
                 filterEvent="keyup" 
                 draggableColumns="true"
                 emptyMessage="No #{custom.labels.get('IDS_OPP')} found."  
                 paginator="true" 
                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                 rowsPerPageTemplate="50,100" 
                 paginatorAlwaysVisible="false" 
                 rowKey="#{contOpp.recId}"
                 class="tblmain">

        <p:column filterBy="#{contOpp.custName}" filterMatchMode="contains" sortBy="#{contOpp.custName}" rendered="#{not (contactService.contacts.contcompTypeId==2)}" id="searchcontOppCustName">
            <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet> 
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{contOpp.oppId}" id="lnkcontOppCustName">
                #{contOpp.custName}
            </h:link>
        </p:column>

        <p:column filterBy="#{contOpp.principalName}" filterMatchMode="contains" sortBy="#{contOpp.principalName}" rendered="#{not (contactService.contacts.contcompTypeId==1)}"  id="searchcontOppPrinciName">
            <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet> 
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{contOpp.oppId}" id="lnkcontOppPrinciName">
                #{contOpp.principalName}
            </h:link>
        </p:column>

        <p:column filterBy="#{contOpp.oppCustProgram}" filterMatchMode="contains" sortBy="#{contOpp.oppCustProgram}"  id="searchcontOppCustPrgm">
            <f:facet name="header">#{custom.labels.get('IDS_PROGRAM')}</f:facet> 
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{contOpp.oppId}" id="lnkcontOppCustProgram">
                #{contOpp.oppCustProgram}
            </h:link>
        </p:column> 
        <p:column filterBy="#{contOpp.oppActivity}" filterMatchMode="contains" sortBy="#{contOpp.oppActivity}"  id="searchcontOppActvty">
            <f:facet name="header">#{custom.labels.get('IDS_ACTIVITY')}</f:facet>  
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{contOpp.oppId}" id="lnkcontOppActivity">
                #{contOpp.oppActivity}
            </h:link>
        </p:column>
        <!--#13178 ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
        <p:column filterBy="#{contOpp.oppStatus}" filterMatchMode="contains" sortBy="#{contOpp.oppStatus}"  id="searchcontOppStatus">
            <f:facet name="header">#{custom.labels.get('IDS_ACT_STATUS')}</f:facet>  
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{contOpp.oppId}" id="lnkcontOppStatus">
                #{contOpp.oppStatus}
            </h:link>
        </p:column>
        <p:column filterBy="#{contOpp.oppFollowUp}" filterMatchMode="contains" sortBy="#{contOpp.oppFollowUp}"  id="searchcontOppFollowup">
            <f:facet name="header">#{custom.labels.get('IDS_FOLLOW_UP')}</f:facet> 
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{contOpp.oppId}" id="lnkcontOppFollowUp">
                <p:outputLabel value="#{contOpp.oppFollowUp}" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:outputLabel>

            </h:link>
        </p:column>
        <!--Feature #5025 RE: Repfabric / AVX CRMSync / Relabeling Fields / "Next Step" (Pending)-->
        <!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
        <p:column filterBy="#{contOpp.oppNextStep}" filterMatchMode="contains" sortBy="#{contOpp.oppNextStep}"  id="searchcontOppNextStep" styleClass="might-overflow">
            <f:facet name="header">#{custom.labels.get('IDS_NEXT_STEP')}</f:facet> 
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{contOpp.oppId}" id="lnkcontOppNextStep" >
                #{contOpp.oppNextStep}
            </h:link>
        </p:column>
        <p:column filterBy="#{contOpp.oppLines}" filterMatchMode="contains" sortBy="#{contOpp.oppLines}"  id="searchcontOppLines">
            <f:facet name="header">Part Number</f:facet> 
            <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{contOpp.oppId}" id="lnkcontoppLines">
                #{contOpp.oppLines}
            </h:link>
        </p:column>
        <p:column filterBy="#{contOpp.oppCloseStatusLabel}" filterMatchMode="contains" sortBy="#{contOpp.oppCloseStatusLabel}"  id="searchcontOppCloseStatus">
            <f:facet name="header">Status</f:facet> 
            <!--<p:outputLabel value="#{contOpp.oppCloseStatus==0?'Open':'Closed'}" />-->
            <p:outputLabel value="#{contOpp.oppCloseStatusLabel}" id="lnkcontoppCloseStatusLabel"/>
        </p:column>

    </p:dataTable>

</ui:composition>