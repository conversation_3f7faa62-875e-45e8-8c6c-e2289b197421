<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<!--13088 ESCALATIONS   CRM-7758   Oasis: Contacts and Companies Tables, Fields and JDBC Connectors-->
<ui:composition
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:ui="http://java.sun.com/jsf/facelets">
    <f:event type="preRenderView" listener="#{oasisContactService.renderJson(oasisContactService.ctUpdDate,oasisContactService.ctPageNum,oasisContactService.ctPageSize)}" />
    <ui:define name="metadata">
        <f:metadata>
            <f:viewParam id="ctPageNum" name="ctPageNum" value="#{oasisContactService.ctPageNum}"/>
            <f:viewParam id="ctPageSize" name="ctPageSize" value="#{oasisContactService.ctPageSize}"/>
            <f:viewParam id="ctUpdDate" name="ctUpdDate" value="#{oasisContactService.ctUpdDate}"/>            
        </f:metadata>
    </ui:define>
</ui:composition>

<!--<f:view encoding="UTF-8" contentType="text/html"
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
 <h:outputText value="#{stationView.getClosestStationen(param.longitude, param.latitude)}" escape="false"/>
</f:view>-->