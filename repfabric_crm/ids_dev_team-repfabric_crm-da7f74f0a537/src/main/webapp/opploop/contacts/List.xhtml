<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: List.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->


<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                template="#{layoutMB.template}">

    <!--PMS 357:Page Title - Companies, Contacts, AJ, Tasks, Messages, Calendar, Planner-->
    <!--    <h:head>
            <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
        </h:head>-->
    <ui:define name="head">
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
        <title>Contact List</title>
    </ui:define>
    <!--//20-06-2023 : #11420 : ESCALATIONS CRM-7065/ CRM-7075   RF Web: Columns adjust when hovering over them and searching-->
    <ui:define name="meta">

            <f:metadata> 
                <f:viewAction action="#{contactService.loadSubMenu()}"/>
                <!--            PMS 180:bug fix-->
                <!--            <f:viewAction action="#{contactService.loadContacts()}"/>-->
                <!--            <f:viewAction action="#{contactService.filterActiveContacts()}"/>-->
                <!--            3526 Contact List -  Dynamic Columns (Similar to Opp List)-->
                <f:viewAction action="#{contactService.loadContsColumnsList()}"  />
                <!--            Related - CRM-1521: Tutorial button landing urls - Contacts-->
                <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('CONT_LIST'))}" />
                <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
                <f:viewAction action="#{helpService.setPageTag('CONT_LIST')}" />

            </f:metadata>
    </ui:define>

    <ui:define name="title">
        <ui:param name="title" value="Contact Management"/>
        <f:view>
            <!--            PMS 3380: if contact list feature turned off we can access contactsList.xhtml feature-->
            <f:event listener="#{contactService.isContactListPageAccessible}" type="preRenderView"/>
        </f:view>
        <div class="row">
            <div class="col-md-6">
                Contact Management 
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>

    <ui:define name="menu">

    </ui:define>

    <ui:define name="body">

        <!--11-10-2022 9281 Task related to #2075 Contact List >  Do not retain filter on Contact Full Name-->
        <script>
            setTimeout(function () {
                $('#contDetailForm\\:dtContList\\:contFullName\\:filter').val('');
                PF('contListTable').filter();
            }, 0);
        </script>

        <f:view>
            <div class="box box-info box-body" style="vertical-align: top">
                <div class="left-column">
                    <h:form id="filterList">
                        <!--2075 Contact List >  Do not retain filter on Contact Full Name-->
                        <!--<p:blockUI block="filterList" widgetVar="buii"/>--> 
                        <!--                        PMS 3380: if contact list feature turned off we can access contactsList.xhtml feature-->
                        <p:selectOneMenu id="contFilter"    value="#{contactFilter.leftFilter.filterName}"
                                         style="border-radius: 0px;margin-top: 0px; height: auto; max-height: 230px !important;">
                            <f:selectItem itemValue="ALL" itemLabel="All Contacts" />
                            <f:selectItem itemValue="ALPHABETICAL" itemLabel="Alphabetical Order"/>
                            <f:selectItem itemValue="COMPANY" itemLabel="Company"/>
                            <f:selectItem itemValue="REGION" itemLabel="#{custom.labels.get('IDS_REGION')}" />
                            <f:selectItem itemValue="CONT_TYPE" itemLabel="#{custom.labels.get('IDS_COMP_TYPE')}" />
                            <f:selectItem itemValue="CONT_GROUP" itemLabel="#{custom.labels.get('IDS_CON_GRP')}" />
                            <f:selectItem itemValue="SALES_TEAM" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}" />
                            <!--                            PMS 3229: CRM-3844: Product interest: add to left side of contact-->
                            <f:selectItem itemValue="PRODUCT_INTEREST" itemLabel="#{custom.labels.get('IDS_PROD_INTEREST')}" />
                            <!--                            PMS 3384: contact filter retain issue-->
                            <!--#4995: Contacts List (Normal):  Implement lazy loading-->
                            <!--                            //Related to 5845 :-->
                            <p:ajax event="change"  update="conListForm  :contDetailForm:dtContList:btnContBulkUpdate" listener="#{contactService.clearLeftSideFilter()}" oncomplete="PF('contListTable').clearFilters();" />
                        </p:selectOneMenu>
                    </h:form>

                    <!--Filter List -  Updated based on drop down-->
                    <h:form id="conListForm">

                        <!--<p:remoteCommand name="onload"  onstart="document.getElementById('contDetailForm:dtContList:contFullName:filter').value=''; PF('contListTable').filter();" autoRun="true" immediate="true"/>-->
                        <!--                        PMS 3229: CRM-3844: Product interest: add to left side of contact-->
                        <p:dataTable value="#{contactService.subFilterObj.subFilterList}" var="filtr"
                                     filteredValue="#{contactService.subFilterObj.filteredSubFilterList}"
                                     widgetVar="subMenuDT" rendered="#{contactFilter.leftFilter.filterName!= 'ALL'
                                                                       and contactFilter.leftFilter.filterName != 'CONT_GROUP' and contactFilter.leftFilter.filterName != 'CONT_TYPE' and contactFilter.leftFilter.filterName != 'PRODUCT_INTEREST'}"
                                     selection="#{contactService.subFilterObj.selectedSubFilter}" 
                                     selectionMode="single" 
                                     rowKey="#{filtr.itemId}"
                                     emptyMessage=" " paginator="true" rows="30" 
                                     paginatorAlwaysVisible="true" pageLinks="3"
                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                                     id="dtsubConList"
                                     multiViewState="true">
                            <!--#4995: Contacts List (Normal):  Implement lazy loading-->
                            <p:ajax event="rowSelect" listener="#{contactService.filterContacts()}" oncomplete="PF('contListTable').filter();"/>
                            <p:column filterBy="#{filtr.itemName}"  filterMatchMode="contains" id="left_filter" >
                                <!--                                        //PMS:2564
                                                                                //seema - 01/12/2020-->
                                <h:outputText value="#{filtr.itemName}" style="text-transform: capitalize;" id="oTxtcontName" />
                                <!--<h:outputText value="#{filtr.itemName.length()>26?filtr.itemName.substring(0,23).concat('..'):filtr.itemName}" style="text-transform: capitalize;" />-->

                            </p:column>
                        </p:dataTable>

                        <!--Contact Group  - Multi Select-->
                        <!--                        PMS 3229: CRM-3844: Product interest: add to left side of contact-->
                        <!--#5888: contact filter retain issues in reduced and normal list-->
                        <p:dataTable  value="#{contactService.subFilterObj.subFilterList}" widgetVar="subMenuMulDT"
                                      filteredValue="#{contactService.subFilterObj.filteredSubFilterList}" 
                                      selection="#{contactService.subFilterObj.selectedSubFilterList}" 
                                      rowKey="#{subFltr.itemId}"  pageLinks="3"
                                      paginatorTemplate="{FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                                      emptyMessage=" " paginator="true" rows="30" paginatorAlwaysVisible="true"
                                      rowSelectMode="add"   var="subFltr"   id="dlMulConList"
                                      multiViewState="true"
                                      rendered="#{contactFilter.leftFilter.filterName == 'CONT_GROUP' || contactFilter.leftFilter.filterName == 'CONT_TYPE' || contactFilter.leftFilter.filterName == 'PRODUCT_INTEREST'}">
                            <!--PMS 3384: contact filter retain issue-->
                            <p:ajax event="rowSelect" listener="#{contactService.onMultipleSelect}" oncomplete="PF('contListTable').filter();"/>
                            <p:ajax event="rowUnselect" listener="#{contactService.onMultipleSelect}" oncomplete="PF('contListTable').filter();"/>
                            <p:ajax event="rowSelectCheckbox" listener="#{contactService.onMultipleSelect}" oncomplete="PF('contListTable').filter();"/>
                            <p:ajax event="rowUnselectCheckbox" listener="#{contactService.onMultipleSelect}" oncomplete="PF('contListTable').filter();"/>
                            <p:ajax event="toggleSelect" listener="#{contactService.onMultipleSelect}" oncomplete="PF('contListTable').filter();"/>
                            <p:column selectionMode="multiple"  style="width: 31px">
                            </p:column>
                            <p:column filterBy="#{subFltr.itemName}"  filterMatchMode="contains" id="left_filter">
                                <!--                                        //PMS:2564
                                                                                //seema - 01/12/2020-->
                                <h:outputText value="#{subFltr.itemName}" style="text-transform: capitalize;" id="oTxtContName1"/>
                                <!--<h:outputText value="#{subFltr.itemName.length()>35?subFltr.itemName.substring(0,32).concat('..'):subFltr.itemName}" style="text-transform: capitalize;" />-->
                            </p:column>
                        </p:dataTable>


                    </h:form>
                </div>


                <div class="right-column">
                    <h:form id="contDetailForm">
                        <!--2075 Contact List >  Do not retain filter on Contact Full Name-->
                        <!--<p:blockUI block="contDetailForm" widgetVar="bui"/>-->

                        <!--Bug #8073:ESCALATIONS CRM-5764 / CRM-5494  Contact Detail List view - page unresponsive (Edge Browser)  by harshithad-->
                        <p:remoteCommand autoRun="false" name="onLoad" />
                        <p:defaultCommand target="noAction"/> 
                        <p:growl id="lstmsgs"  escape="false" life="6000" showDetail="false" showSummary="true"/>
                        <!--                        //#1609 - CRM-3185: Deletes keep repopulating #2 Bulk Contact Delete- sharvani - 02-09-2020-->
                        <!--#2544: Analysis -  Contacts Listing-->
                        <!--#4995: Contacts List (Normal):  Implement lazy loading-->
                        <!--//01-06-2022 : 8033 : CRM-4972  MAKE HEADERS AT TOP OF PAGE STICK so you can scroll and still see what column you are--> 
                        <!--//31-07-2023 : #11673 : ESCALATIONS CRM-7159   Companies: column adjustments reset after logging out-->
                        <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                        <p:dataTable id="dtContList" value="#{contactService}" 
                                     selection="#{contactService.selectedCont}" 
                                     filteredValue="#{contactService.filteredContList}"
                                     var="cont" multiViewState="true"
                                     rows="50" 
                                     widgetVar="contListTable" 
                                     filterEvent="keyup"
                                     draggableColumns="true"  
                                     emptyMessage="No Contacts found."  
                                     paginator="true" 
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                     rowsPerPageTemplate="50,100"  
                                     paginatorAlwaysVisible="true"
                                     rowKey="#{cont.contId}"
                                     resizableColumns="true" 
                                     rowSelectMode="add" scrollable="true" scrollHeight="2000"
                                     sortBy="#{cont.contFullName}" 
                                     lazy="true" resizeMode="expand">
    <!--                        <p:ajax event="page" listener="#{contactService.onPageChange}"/>  -->
                            <!--                                    Feature #6839 CRM-5142: have default format for phone numbers-->
                            <!--                        3683   CRM-2208  Taylormarketing:Doug Taylor Contacts list column indexing messed up-->
                            <!--//31-07-2023 : #11673 : ESCALATIONS CRM-7159   Companies: column adjustments reset after logging out-->
                            <p:ajax event="colResize" listener="#{contactService.columnResize}"/>
                            <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                            <p:ajax event="colReorder" listener="#{contactService.onColumnReorder}"  />
                            <!--#1609 - CRM-3185: Deletes keep repopulating #2 Bulk Contact Delete- sharvani - 02-09-2020-->
                            <!--                            PMS 2357:Contact List >  Multi-select Export and Send to Auoklose-->
                            <!--                   Feature #4457:Contacts > Send Bulk Email  8/06/21 by harshithad-->
                            <!-- Bug #6007 previous page selected contact update not working-->
                            <!--09/03/2022 7464 CRM-5430 "Update Selected" Bug? - not working on first try-->
                            <p:ajax event="rowSelectCheckbox" process="@this" listener="#{contactService.onSelect}"  update=":contDetailForm:dtContList:delBtton :contDetailForm:dtContList:exp :contDetailForm:dtContList:exptyp :contDetailForm:dtContList:btnSndEmail :contDetailForm:dtContList:btnContBulkUpdate" />
                            <!--#13438 ESCALATIONS CRM-7929   Companies: Adjusted Columns keep reverting back-->
                            <p:ajax event="rowUnselectCheckbox" listener="#{contactService.onUnSelect}" update=":contDetailForm:dtContList:delBtton :contDetailForm:dtContList:exp :contDetailForm:dtContList:exptyp :contDetailForm:dtContList:btnSndEmail :contDetailForm:dtContList:btnContBulkUpdate" />
                            <p:ajax event="toggleSelect" listener="#{contactService.onToggleSelect}" update=":contDetailForm:dtContList:delBtton :contDetailForm:dtContList:exp :contDetailForm:dtContList:exptyp :contDetailForm:dtContList:btnSndEmail :contDetailForm:dtContList:btnContBulkUpdate" />
                            <p:ajax event="page" listener="#{contactService.onPagination}" update=":contDetailForm:dtContList" />
                            <p:ajax event="rowSelectCheckbox" process="@this" listener="#{contactService.enableSendEmail()}" update=":contDetailForm:dtContList:delBtton :contDetailForm:dtContList:exp :contDetailForm:dtContList:exptyp :contDetailForm:dtContList:btnSndEmail :contDetailForm:dtContList:btnContBulkUpdate" />
                            <p:ajax event="rowUnselectCheckbox" listener="#{contactService.enableSendEmail()}" update=" :contDetailForm:dtContList:delBtton :contDetailForm:dtContList:exp :contDetailForm:dtContList:exptyp :contDetailForm:dtContList:btnSndEmail :contDetailForm:dtContList:btnContBulkUpdate"/>
                            <p:ajax event="toggleSelect" listener="#{contactService.enableSendEmail()}" update=":contDetailForm:dtContList:delBtton :contDetailForm:dtContList:exp :contDetailForm:dtContList:exptyp :contDetailForm:dtContList:btnSndEmail :contDetailForm:dtContList:btnContBulkUpdate"/>
                            <!--                             // Bug #7456 CRM-5424  companies and contacts list: default the cursor onto name column WESINC-->
                            <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                            <p:ajax   event="filter" listener="#{contactService.tableFlt(filters)}" />
                            <!--2075 Contact List >  Do not retain filter on Contact Full Name-->
                            <!--<p:ajax event="filter" oncomplete="afterLoadProc()"/>-->
                            <f:facet name="header">
                                <div style="display:inline;float: left;">
                                    <h:panelGroup>
                                        <!--                                        //PMS:2564
                                                                                //seema - 01/12/2020-->
                                        <p:commandButton value="New"  styleClass="btn btn-primary btn-xs" id="btnNewCont"
                                                         action="/opploop/contacts/ContactDetails.xhtml?faces-redirect=true" 
                                                         /> 
                                        <p:spacer width="4px"/>
                                        <!-- PMS 3459: Unified Company Contact - UI-Creation-Update-->
                                        <!--PMS 3667:CRM-3999: PersonCompany: no application setting to turn it off or on-->
                                        <!--PMS 3909: CRM-4090: PersonCompany: need relabel-->
                                        <!--PMS 3995:Issues related to Person Company-->
                                        <p:commandButton id="btnNewUnifiedcntComp" value="Create #{custom.labels.get('IDS_PERSON_COMPANY')}"  title="#{contactService.personComptitleLbl()}" disabled="#{loginBean.userCompanyCreateAccess()}" styleClass="btn btn-primary btn-xs" 
                                                         action="#{contactService.redirectToUnified()}" rendered="#{contactService.unifiedCompEnabled}" /> 
                                        <p:spacer width="4px"/>
                                        <!-- Bug #6007 previous page selected contact update not working-->
                                        <!--                                    PMS 988-->
                                        <!--                                        PMS 2357:Contact List >  Multi-select Export and Send to Auoklose-->
                                        <!--//Bug #5827: CRM-4763 Contact Export broken-->
                                        <p:commandButton value="Export"  
                                                         actionListener="#{exportService.prepareExportDialog('CONT')}"   
                                                         action="#{lookupService.clearContactsFilters()}"  
                                                         oncomplete="PF('dlgOppExport').show();" styleClass="btn btn-primary btn-xs" 
                                                         update=":exportOppForm :dlgheader :exportOppForm:selType :exportOppForm:filterName :exportOppForm:autoklse" 
                                                         style="#{!(contactService.selectedNormalContlistSave==null or contactService.selectedNormalContlistSave.size()==0 )?'display:none':'display:inline'}" id="exptyp" disabled="#{contactService.expDlgFlag}"/> 
                                        <!--                                    PMS 1593 -->
                                        <!--//Bug #5827: CRM-4763 Contact Export broken-->
                                         <!--#13438 ESCALATIONS CRM-7929   Companies: Adjusted Columns keep reverting back-->
                                        <p:commandButton value="Export"  styleClass="btn btn-primary btn-xs"   action="#{contactService.exportOnSelection()}"  
                                                         style="#{(contactService.selectedNormalContlistSave==null or contactService.selectedNormalContlistSave.size()==0 )?'display:none':'display:inline'}"  id="exp" 
                                                         disabled="#{(contactService.selectedNormalContlistSave.size() eq 0 or contactService.selectedNormalContlistSave.size() eq null) and contactService.expFlag}"  oncomplete="#{contactService.onDialogClose()}"
                                                         update=":contDetailForm:dtContList"
                                                         >

                                            <!--                                        <p:dataExporter type="xlsx" target="dtContList"  fileName="Contacts"  selectionOnly="true"/>  -->

                                        </p:commandButton> 

                                        <p:spacer width="4px"/>
                                        <!--                                    PMS 1853-->
                                        <!--                                        //PMS:2564
                                                                                //seema - 01/12/2020-->
                                        <p:commandButton value="Re-import" id="btnReImport"
                                                         actionListener="#{bulkUpdateService.initliazeSettings('CONT')}"  
                                                         styleClass="btn btn-primary btn-xs" update=":frmBlkUpd" /> 

                                        <!--#1609 - CRM-3185: Deletes keep repopulating #2 Bulk Contact Delete- sharvani - 02-09-2020-->
                                        <p:spacer width="4px" />
                                        <!--                                        PMS 2518:CRM-3245: Contacts: Delete button not keeping filter-->
                                        <p:commandButton value="Delete"
                                                         id="delBtton" 
                                                         disabled="#{contactService.selectedNormalContlistSave.size() eq 0 or contactService.selectedNormalContlistSave.size() eq null}" 
                                                         oncomplete="PF('dltBulkContDlg').show();" styleClass="btn btn-danger btn-xs"  > 

                                        </p:commandButton>

                                        <!--                        Feature #4457:Contacts > Send Bulk Email  8/06/21 by harshithad-->
                                        <p:spacer width="4px"/>
                                        <p:commandButton value="Send Email"
                                                         actionListener="#{contactService.loadEmailDefaults(1)}"
                                                         id="btnSndEmail" 
                                                         update="dlgSendEmailCont  SendEmailForm:dtAttListCont"
                                                         disabled="#{contactService.sendEmailFlag}"
                                                         oncomplete="PF('sendEmailCont').show();" styleClass="btn btn-primary btn-xs"/>
                                        <p:spacer width="4px"/>
                                        <!--        // Feature #5845 CRM-4658: Contacts List: update selected for Product potentials and product interest, buying groups  -->
                                        <!--#5146: Companies > Implement lazy loading-->
                                        <!--                                        Bug #6007 previous page selected contact update not working-->
                                        <!--   //Feature #6029 Reduced Contact List > Add Update Selected option-->
                                        <p:commandButton value="Update Selected"  styleClass="btn btn-primary btn-xs" id="btnContBulkUpdate" resetValues="true"
                                                         disabled="#{contactService.selectedNormalContlistSave.size() eq null || contactService.selectedNormalContlistSave.size() eq 0}"
                                                         actionListener="#{dataSelfService.reset()}" update=":blkUpdateForm "
                                                         action="#{dataSelfService.showDialog(contactService.selectedNormalContlistSave, 'CONT','NORMAL')}" 
                                                         />
                                    </h:panelGroup>
                                    <!--                                <div class="row">
                                                                        <div class="col-md-5">-->

                                    <!--                                    </div>                        
                                                                    </div> -->
                                </div>
                                <div style="text-align: right">
                                    <!--#5214: Contact List > Code Optmization-->
                                    <!--#5888: contact filter retain issues in reduced and normal list-->
                                    <h:panelGroup >
                                        <p:inputText id="globalFilter" style="width:150px" placeholder="Quick Search" />

                                        <!--PMS CRM-2268: Electra esi: deep search contact notes                                    -->
                                        <p:spacer width="4px" />
                                    </h:panelGroup>
                                    <!--                                    PMS 2357:Contact List >  Multi-select Export and Send to Auoklose-->
                                    <p:column exportable="false">
                                        <!--                                    3526 Contact List -  Dynamic Columns (Similar to Opp List)-->
                                        <p:commandButton title="Clear Filters" 
                                                         onclick="PF('contListTable').clearFilters();" 
                                                         icon="fa fa-times-circle"
                                                         id="btnFilterClear"
                                                         class="btn-danger btn-xs" style="float: right"  />
                                        <p:commandButton title="Save settings" 
                                                         icon="fa fa-save"
                                                         id="btnclonesave"  style="float: right"
                                                         action="#{contactService.saveSettings()}" 
                                                         class="btn-success btn-xs"  />
                                        <p:commandButton id="toggler" type="button" 
                                                         value="Columns" style="float: right" icon="fa fa-align-justify"/>

                                        <p:columnToggler datasource="dtContList"  trigger="toggler" id="togllerList" >
                                            <p:ajax event="toggle" listener="#{contactService.onToggle}"  />
                                        </p:columnToggler>

                                    </p:column>

                                </div>  
                            </f:facet>
                            <!--<p:ajax event="rowSelectCheckbox"/>-->
                            <!--                            PMS 2357:Contact List >  Multi-select Export and Send to Auoklose-->
                            <!--//01-06-2022 : 8033 : CRM-4972  MAKE HEADERS AT TOP OF PAGE STICK so you can scroll and still see what column you are--> 
                            <p:column    selectionMode="multiple"  style="width: 31px;text-align:center;" toggleable="false" exportable="false" id="chkMultipleCont">
                            </p:column>
    <!--                        <p:column toggleable="false"   id="contNameFilter" headerText="Name"  filterMatchMode="contains" sortBy="#{cont.contFullName}"  filterBy="#{cont.contFullName}">
                                <a href="ContactDetails.xhtml?id=#{cont.contId}" title="View Details">
                                    <h:outputText value="#{cont.contFullName}" style="text-decoration: underline;"/>
                                </a>
    
                            </p:column>-->
                            <!--                        3526 Contact List -  Dynamic Columns (Similar to Opp List)-->
                            <!--#4811:Contact: Multi Select Export-->
                            <p:column headerText="Id"  style="display: none" exportable="true"  visible="false" toggleable="false">
                                <p:outputLabel value="#{cont.contId}"  />
                            </p:column> 

                            <c:forEach id="each"  var="column" items="#{contactService.contacts.listContColumns}">
                               <!--#13438 ESCALATIONS CRM-7929   Companies: Adjusted Columns keep reverting back-->
                                <!--28-09-2023 12174 ESCALATION CRM-7390 Contact - Exporting Error-->
<!--                                <p:column   headerText="#{column.columnHeader}" 
                                            style="width:#{column.columnWidth == 'null' or column.columnWidth == '10%' ? '200' : column.columnWidth}px;"  
                                            id="#{column.columnName}_1" 
                                            visible="#{column.columnVisibleFlag==1 and ((column.columnName=='contPhoneMobile') or (column.columnName=='contPhoneBusiness'))}"
                                            exportable="false" toggleable="#{((column.columnName=='contPhoneMobile') or (column.columnName=='contPhoneBusiness'))}"
                                            filterMatchMode="#{column.columnFilterMatchMode}" 
                                            filterBy="#{cont[column.columnName]}"  
                                            sortBy="#{cont[column.columnName]}"  
                                            field="#{column.columnName}" 
                                            styleClass="ellipsis-column"> 
                                    <c:choose>
                                        28-09-2023 12174 ESCALATION CRM-7390 Contact - Exporting Error
                                        <c:when test="#{column.columnName=='contPhoneMobile' or column.columnName=='contPhoneBusiness'}">
                                            <f:facet   name="header" > 
                                                28-09-2023 12174 ESCALATION CRM-7390 Contact - Exporting Error
                                                <h:outputText value="#{column.columnHeader}" style="display: inline"  title="Please search for the last 4 digits" > </h:outputText>
                                            </f:facet>
                                            28-09-2023 12174 ESCALATION CRM-7390 Contact - Exporting Error
                                             
                                        </c:when>
                                        28-09-2023 12174 ESCALATION CRM-7390 Contact - Exporting Error
                                    </c:choose>
                                </p:column>-->
                                <!--                                    Feature #6839 CRM-5142: have default format for phone numbers-->
                                <!--                                PMS 2357:Contact List >  Multi-select Export and Send to Auoklose-->
                                <!--//01-06-2022 : 8033 : CRM-4972  MAKE HEADERS AT TOP OF PAGE STICK so you can scroll and still see what column you are--> 
                                <!--//31-07-2023 : #11673 : ESCALATIONS CRM-7159   Companies: column adjustments reset after logging out-->
                                <!--//09-08-2023 : #11832 : CRM-7217   Change wrapping properties on list view screens for all modules-->
                                <p:column   headerText="#{column.columnHeader}" style="width:#{column.columnWidth == 'null' or column.columnWidth == '10%' ? '200' : column.columnWidth}px;"  id="#{column.columnName}" 
                                            visible="#{column.columnVisibleFlag==1}" exportable="true"
                                            filterMatchMode="#{column.columnFilterMatchMode}" 
                                            filterBy="#{cont[column.columnName]}"  
                                            sortBy="#{cont[column.columnName]}"  
                                            field="#{column.columnName}"
                                            styleClass="ellipsis-column"> 
                                    <!--28-09-2023 12174 ESCALATION CRM-7390 Contact - Exporting Error-->
                                    <c:choose>
                                        <!--#13438 ESCALATIONS CRM-7929   Companies: Adjusted Columns keep reverting back-->
                                        <!--28-09-2023 12174 ESCALATION CRM-7390 Contact - Exporting Error-->
                                        <c:when test="#{column.columnName=='contPhoneMobile' or column.columnName=='contPhoneBusiness'}">
                                            <f:facet   name="header" > 
                                                <!--28-09-2023 12174 ESCALATION CRM-7390 Contact - Exporting Error-->
                                                <h:outputText  value="#{column.columnHeader}" style="display: inline" title="Please search for the last 4 digits" > </h:outputText>
                                            </f:facet>
                                            <!--28-09-2023 12174 ESCALATION CRM-7390 Contact - Exporting Error-->
                                            <ui:repeat rendered="#{!contactService.exportFlag}"  value="#{rFUtilities.returnNumList(cont[column.columnName])}" var="number" 
                                                       varStatus="numberStatus" id="lnkContPhoneData">
                                                #{(numberStatus.index != 0) ? ' | ':''}
                                             <h:outputLink  style="display: inline" value="#{rFUtilities.returnTelNum(number)}" title="#{number}">#{number}</h:outputLink> 
                                            </ui:repeat> 
                                            
                                            <h:outputText  rendered="#{contactService.exportFlag}" value="#{cont[column.columnName]}"> </h:outputText>
                                        </c:when>
                                        <!--28-09-2023 12174 ESCALATION CRM-7390 Contact - Exporting Error-->
                                        <c:otherwise>
                                            <!--28-09-2023 12174 ESCALATION CRM-7390 Contact - Exporting Error-->
                                            <f:facet name="header">
                                                <h:outputText value="#{column.columnHeader}" style="display: inline"  title="#{column.columnHeader}" />
                                            </f:facet> 
                                            <!--<f:attribute name="myCol" value="column"/>-->
                                            <!--                            3800   CRM-2274  Rouzer: deleting contacts -->
                                            <!--                                        //PMS:2564
                                                                                        //seema - 01/12/2020-->


                                            <h:link   outcome="ContactDetails.xhtml?id=#{cont.contId}"   id="col_#{column.columnName}" 
                                                      value="#{cont[column.columnName]}"  style="display: inline" title="#{cont[column.columnName]}">
                                                <f:param name="frm" value="#{view.viewId}" />
                                                <f:param name="moduleId" value="0" />
                                            </h:link>


                                        </c:otherwise>
                                    </c:choose>
                                </p:column>
                            </c:forEach> 
                            <!--                            PMS 2357:Contact List >  Multi-select Export and Send to Auoklose-->
                            <!--                                        //PMS:2564
                                                                                //seema - 01/12/2020-->
                            <p:column   style="width:81px "  toggleable="false" exportable="false">  
                                <!--                                PMS 3643:Person Company Contact > Deletion (Temporary Fix)-->
                                <!--PMS 3623 Person Company Contact  Deletion-->
                                <p:commandButton  styleClass="btn-danger btn-xs btn-icon-group" rendered="#{cont.contUnifiedId == 0}"
                                                  title="Delete" icon="fa fa-trash"  id="btnLstDelete" update="dltLstfrm"
                                                  actionListener="#{contactService.deleteContact(cont.contId,0)}" immediate="true">
                                </p:commandButton>
                                <!--PMS 3623 Person Company Contact  Deletion-->
                                <p:commandButton  styleClass="btn-danger btn-xs btn-icon-group" rendered="#{cont.contUnifiedId > 0}"
                                                  title="Delete" icon="fa fa-trash"  id="btnLstPersonDelete" update="dltLstfrm"
                                                  actionListener="#{contactService.initializePersonCompDelete(cont.contId,cont.contUnifiedId)}" immediate="true">
                                </p:commandButton>
                                <p:spacer width="4px"/>
                                <p:commandButton  styleClass="btn-primary  btn-xs btn-icon" id="btnSendEmail"
                                                  title="Send Email" icon="fa fa fa-envelope-o" 
                                                  onclick="window.open('#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=sendmailtocont&amp;cont_id=#{cont.contId}', '_blank')"  >
                                </p:commandButton>
                            </p:column>



<!--                        <p:column toggleable="false" headerText="tags" style="display: none;" filterBy="#{comp.compTags}" filterMatchMode="contains">
                            <a href="CompanyDetails.xhtml?id=#{comp.compId}" >
                                <h:outputText value="#{comp.compTags}"/>
                            </a>
                        </p:column>

                        <p:column toggleable="false">
                            <p:commandButton action="#{comp.deleteCompany(comp)}" icon="ui-icon-trash" update=":compDetailForm:dtCompList" title="Delete Company" style="height:25px;width:25px;" disabled="#{company.restrictDeleteflag}">
                                <p:confirm header="Confirmation" message="Are you sure to delete #{comp.compName}?" icon="ui-icon-alert" />
                            </p:commandButton>
                        </p:column>-->
                        </p:dataTable>
                        <!--  Feature #6839 CRM-5142: have default format for phone numbers-->
                        <p:tooltip for="@(#contDetailForm\:dtContList\:contPhoneBusiness\:filter)" style="font-size:16px"   value="Please search for the last 4 digits" />

                        <h:inputHidden id="noAction"/>
                        <h:inputHidden id="search_filter"  />
                        <h:inputHidden id="inpTxt_filter_cont_name"  />
                        <h:inputHidden id="inpTxt_filter_cont_comp_name"  />
                        <h:inputHidden id="inpTxt_filter_cont_email"  />
                        <h:inputHidden id="inpTxt_filter_cont_jobtitle"  />
                        <h:inputHidden id="inpTxt_filter_cont_phone"  />
                        <!--                        PMS 2357:Contact List >  Multi-select Export and Send to Auoklose-->
                        <p:dialog id="cnfrmExprt" widgetVar="cnfrmExprt" header="Export Selected Contacts" modal="true" resizable="false" >
                           

                            <!--                            <h:form id="cnfExport">-->

                            <!--                                <p:outputLabel value="Export contacts to"/>-->
                            <div style="text-align: center">
                                <!--#13438 ESCALATIONS CRM-7929   Companies: Adjusted Columns keep reverting back-->
                                <!--//Bug #5827: CRM-4763 Contact Export broken-->
                                <!-- Bug #6007 previous page selected contact update not working-->
                                <p:commandButton value="Export to File" id="exprtToXl" 
                                                 ajax="false" onclick="PF('cnfrmExprt').hide(); PrimeFaces.monitorDownload(null, rmClrSelectedCont)" 
                                                  
                                                 update=":filterList :contDetailForm  :contDetailForm:dtContList" 
                                                 styleClass="btn btn-success  btn-xs" >
                                    
                                    <p:dataExporter    type="xlsx" target="dtContList"   fileName="Contacts"  selectionOnly="true" pageOnly="false" preProcessor="#{contactService.exportpreProc}" postProcessor="#{contactService.exportpostProc}"/> 


                                </p:commandButton>
                                <p:spacer width="4" />
                                <p:commandButton id="btnSndAutoklose" 
                                                 value="Send to Autoklose" action="#{autokloseService.clearLst()}" 
                                                 oncomplete="PF('cnfrmExprt').hide();PF('addAutokloseDlg').show()" 
                                                 update=":autoFrm:pnl"  styleClass="btn btn-success  btn-xs" />

                                <!--                            </h:form>-->
                            </div>
                        </p:dialog>
                        <!--  PMS 2357:Contact List >  Multi-select Export and Send to Auoklose-->
                        <p:remoteCommand autoRun="false" id="rmClrSelectedCont" 
                                         name="rmClrSelectedCont" actionListener="#{contactService.clearSelectedContacts()}" 
                                         update=":filterList :contDetailForm" />
                    </h:form>
                </div>
            </div>


            <style>
                /*//09-08-2023 : #11832 : CRM-7217   Change wrapping properties on list view screens for all modules*/
                /*<!--//01-06-2022 : 8033 : CRM-4972  MAKE HEADERS AT TOP OF PAGE STICK so you can scroll and still see what column you are--> */
                /*                .might-overflow {
                                    text-overflow: ellipsis;
                                    overflow : hidden;
                                    white-space: nowrap;
                                    max-width: 1000px;
                                }
                
                                .might-overflow:hover {
                                    text-overflow: clip;
                                    white-space: normal;
                                    word-break: break-all;
                                    max-width: 1000px;
                                }*/
                /*//09-08-2023 : #11832 : CRM-7217   Change wrapping properties on list view screens for all modules*/
                .ellipsis-column {
                    width: 100px ;/* Set the desired width for the column */
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->*/
                @media only screen and (min-height: 600px) and (max-height: 900px) {
    .ui-datatable-scrollable-body {
        height: 65vh;
        outline: 0px;
    }
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
@media only screen and (min-height: 901px) and (max-height: 1152px) {
    .ui-datatable-scrollable-body {
        height: 70vh;
        outline: 0px;
    }
}

@media only screen and (min-height: 1153px) and (max-height: 1440px) {
    .ui-datatable-scrollable-body {
        /*//21-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
        height: 70vh;
        outline: 0px;
    }
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
@media only screen and (min-height: 1500px) and (max-height: 1640px) {
    .ui-datatable-scrollable-body {
        /*//21-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
        height: 70vh;
        outline: 0px;
    }
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-body {
    outline: 0px;
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-header *,
.ui-datatable-scrollable-theadclone * {
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    width: 100%;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
    width: 15px;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable table {
    /* table-layout: fixed !important; */
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-theadclone {
    visibility: collapse !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-tablewrapper {
    overflow: initial !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-paginator {
    background-color: #dedede !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.q {
    width: 100% !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.p {
    width: 80% !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-header-box,
.ui-datatable-scrollable-footer-box {
/*    overflow-x: auto !important;
    overflow-y: hidden !important;*/
    position: relative;
    /*margin-left: 0px !important;*/
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-view {
    position: relative;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.top-scrollbar {
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    height: 20px; /* Adjust height as needed */
    overflow-x: auto;
    overflow-y: hidden;
    z-index: 10; /* Ensure it appears above the DataTable */
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.top-scrollbar div {
    width: 100%; /* Match the width of the DataTable */
    height: 20px; /* Match the height of the scrollbar container */
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-header {
    z-index: 20; /* Ensure it appears above the DataTable */
}

                /*            .content-header {
                    height: 51px!important;
                            } */
                /*            .content {
                    padding: 0!important;
                                padding-left: 15px!important;
                                padding-right: 15px!important;
                    margin-top: -9px!important;
                                }*/
                label {
                    font-weight: normal!important;
                }
                #conListForm\:dlMulConList tbody div.ui-chkbox span.ui-icon-check {               
                    right: 3px!important;
                    bottom: 3px!important;
                }
                /*            #compFilterColumn tbody tr td:first-child{
                                vertical-align: top;
                            }*/
                /*PMS 3380: if contact list feature turned off we can access contactsList.xhtml feature*/
                .ui-selectonemenu-items-wrapper{
                    max-height: 230px !important;
                }
            </style>
            <!--#5214: Contact List > Code Optmization-->
            <script>
                function delay(callback, ms) {
                    var timer = 0;
                    return function () {
                        var context = this, args = arguments;
                        clearTimeout(timer);
                        timer = setTimeout(function () {
                            callback.apply(context, args);
                        }, ms || 0);
                    };
                }
                $('#contDetailForm\\:dtContList\\:globalFilter').on("input change keyup paste propertychange ", delay(function () {
                    PF('contListTable').filter();
                }, 1000));
                
               //20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                document.addEventListener('DOMContentLoaded', function () {
    var dataTable = PF('contListTable').jq;
    var scrollableHeaderBox = dataTable.find('.ui-datatable-scrollable-header-box');
    var scrollableBody = dataTable.find('.ui-datatable-scrollable-body');
    var scrollableView = dataTable.find('.ui-datatable-scrollable-view');

    // Create the custom top scrollbar
    var topScrollbar = document.createElement('div');
    topScrollbar.className = 'top-scrollbar';
    var innerDiv = document.createElement('div');
    innerDiv.style.width = scrollableBody.find('table').width() + 'px';
    topScrollbar.appendChild(innerDiv);

    // Insert the top scrollbar above the scrollable header box
    scrollableHeaderBox.before(topScrollbar);

    // Synchronize scroll positions
    topScrollbar.addEventListener('scroll', function () {
        scrollableBody.scrollLeft(topScrollbar.scrollLeft);
        scrollableHeaderBox.scrollLeft(topScrollbar.scrollLeft);
    });

    scrollableBody.on('scroll', function () {
        topScrollbar.scrollLeft = scrollableBody.scrollLeft();
        scrollableHeaderBox.scrollLeft(scrollableBody.scrollLeft());
    });

    scrollableHeaderBox.on('scroll', function () {
        topScrollbar.scrollLeft = scrollableHeaderBox.scrollLeft();
        scrollableBody.scrollLeft(scrollableHeaderBox.scrollLeft());
    });
});
            </script>

            <!--11/02/2021 7184: Pitch Selector: Handle Pitch Attachments in Contacts > Send Email > Insert Pitch-->

            <p:confirmDialog header="Confirmation" global="false" severity="alert" id="dlgListDeleteContact"
                             showEffect="fade" hideEffect="fade" widgetVar="confirmListDeleteContact" 
                             message="Are you sure to delete #{contactService.contacts.contFullName}?">
                <f:facet name="message">
                    <!--                                        //PMS:2564
                                                                                //seema - 01/12/2020-->
                    <p:outputLabel value="Are you sure to delete #{contactService.contacts.contFullName}?" id="lblConfirmMsg"/>
                    <br/>
                    <p:outputLabel id="linkMessageDlg" value="#{contactService.linkedMeassage}" style="color:blue"/>
                </f:facet>


                <h:form id="dltLstfrm">

                    <p:growl id="cnfmlstmsgs"  escape="false" life="6000" showDetail="false" showSummary="true"/>
                    <div align="center">
                        <!--                    #2439: CRM-1512: KELLERIND Fwd: bug found in contact list: clear filter is added-->
                        <!--                                        //PMS:2564
                                                                                //seema - 01/12/2020-->
                        <!--                        PMS 3623 Person Company Contact  Deletion-->
                        <p:commandButton value="Yes"   rendered="#{contactService.personCompUnifiedId==null or contactService.personCompUnifiedId==0}"
                                         oncomplete="PF('confirmListDeleteContact').hide();PF('contListTable').filter()" 
                                         styleClass="btn btn-success  btn-xs" 
                                         action="#{contactService.contactDelete(false)}"
                                         update=":dltLstfrm:cnfmlstmsgs :contDetailForm:dtContList" id="btnConfirmYes"
                                         />
                        <!--                        PMS 3623 Person Company Contact  Deletion-->
                        <p:commandButton value="Yes"   rendered="#{contactService.personCompUnifiedId!=null and contactService.personCompUnifiedId>0}"
                                         oncomplete="PF('confirmListDeleteContact').hide();PF('contListTable').filter()" 
                                         styleClass="btn btn-success  btn-xs" 
                                         action="#{contactService.deletePersonCompany(0)}"
                                         update=":dltLstfrm:cnfmlstmsgs :contDetailForm:dtContList" id="btnPersonConfirmYes"
                                         />
                        <p:spacer width="4px"/>
                        <!--                                        //PMS:2564
                                                                                //seema - 01/12/2020-->
                        <p:commandButton value="No" type="button" id="btnConfirmNo"
                                         styleClass="btn btn-danger  btn-xs" 
                                         onclick="PF('confirmListDeleteContact').hide()" />     
                    </div>
                </h:form>
            </p:confirmDialog>
            <!--#1609 - CRM-3185: Deletes keep repopulating #2 Bulk Contact Delete- sharvani - 02-09-2020-->

            <p:dialog widgetVar="inProgressDlg1" closable="false" modal="true" header="Message" resizable="false"  >

                <h:form id ="frmPoll">
                    <h:outputText value="Deleting contacts..." id="txtStatus" />
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif" id="loadingImg1"   />
                        <p:spacer width="5" />
                        <!--<p:outputLabel value="Processing..." />-->
                        <br /><br />
                    </p:outputPanel>

            <!--<p:poll stop="#{contactService.stopPoll()}"  interval="1" widgetVar="pollStat1" listener="#{contactService.updateStatus()}" update="txtStatus"  autoStart="false" async="true" partialSubmit="true"   />-->
                </h:form>
            </p:dialog>

            <ui:include src="/opploop/exporter/ExportDlg.xhtml"/>
            <!--        PMS 1593-->
            <ui:include src="/bulkupdate/ReImportDlg.xhtml"/>
            <!--          Feature #4457:Contacts > Send Bulk Email  8/06/21 by harshithad-->
            <ui:include src="/opploop/contacts/dlg/SendEmailDlg.xhtml"/>
            <!--Pms Task 6838:Pitch Selector: Contacts > Send Email-->
            <ui:include src="/lookup/PitchLookup.xhtml"/>
            <ui:include src="/bulkupdate/DataSelfServiceDlg.xhtml"/>

            <!--#2337: Contacts List screen >  Clear the list before page unload-->
            <h:form id="frmUnload">
                <p:remoteCommand autoRun="false" id="rcUnload" name="rcnUnload" action="#{contactService.cleanUp()}" />
            </h:form>
            <!--            PMS 2518:CRM-3245: Contacts: Delete button not keeping filter-->
            <h:form id="frmDlgCnfDlt">
                <!-- Bug #6007 previous page selected contact update not working-->
                <!--                                        //PMS:2564
                                                                               //seema - 01/12/2020-->
                <p:confirmDialog header="Confirmation" id="dlgConfirmDelete"
                                 message="You are about to delete #{contactService.selectedCont.size()} contacts. Are you sure to proceed?" 
                                 widgetVar="dltBulkContDlg" >
                    <!--<h:form id="dltBulkContDlg">--> 
                    <div align="left">
                        <!--                                        //PMS:2564
                                                                                //seema - 01/12/2020-->
                        <p:selectBooleanCheckbox value="#{contactService.forceFlag}" id="oneMenuForceFlag"/>
                        <p:spacer width="4px" />
                        <p:outputLabel value="Force delete contact links (This will clear the contacts from the linked transactions)" 
                                       id="olblForceMsg"
                                       /> 
                    </div>
                    <div align="center">
                        <!-- Bug #6007 previous page selected contact update not working-->  
                        <!--                                        //PMS:2564
                                                                                //seema - 01/12/2020-->
                        <p:commandButton value="Yes"  id="btnForceDeleteYes"
                                         actionListener="#{contactService.deleteBulkContacts(contactService.selectedNormalContlistSave,contactService.forceFlag)}" 
                                         onstart="PF('inProgressDlg1').show();PF('dltBulkContDlg').hide();"
                                         styleClass="btn btn-success  btn-xs" update=":contDetailForm"   />
                        <p:spacer width="4px"/>
                        <!--                                        //PMS:2564
                                                                                                        //seema - 01/12/2020-->
                        <p:commandButton value="No" styleClass="btn btn-danger  btn-xs"
                                         id="btnForceDeleteNo"
                                         onclick="PF('dltBulkContDlg').hide()"/>

                    </div>
                    <!--</h:form>-->

                </p:confirmDialog>
            </h:form>
            <script>
//                11-10-2022 9281 Task related to #2075 Contact List >  Do not retain filter on Contact Full Name
//                $('#contDetailForm\\:dtContList\\:contFullName\\:filter').val('');
//                PF('contListTable').filter();
                // Bug #7456 CRM-5424  companies and contacts list: default the cursor onto name column WESINC
                $(document).ready(function () {

//                    console.log(#{contactService.cursor});
                    if (#{contactService.cursor}) {
                        $('#contDetailForm\\:dtContList\\:contFullName\\:filter').focus();
                    }
                });
//                $(document).ready(function () {
//                    $('#contDetailForm\\:dtContList\\:contNameFilter\\:filter').focus();
////                    console.log("ready is called");
//                });
//                bug #2506 PRIORITY: CRM-5808: Contact List > Sign Out issue
//                2075 Contact List >  Do not retain filter on Contact Full Name
                $(window).bind('beforeunload', function () {
//                    11-10-2022 9281 Task related to #2075 Contact List >  Do not retain filter on Contact Full Name
//                     $('#contDetailForm\\:dtContList\\:contFullName\\:filter').val('');
//                     PF('contListTable').filter(); 
//                    2506  09-12-2021: contacts save settings->sign out loading issue

//       rcnUnload();     //code not required. Do not uncomment //#2337: Contacts List screen >  Clear the list before page unload
//                    console.log("bind is called");
                    // PF('statusDialog').show()
                    // $('#nonAjaxLoad').show();
                    // PF('bui').show();
                });


//                window.onload = function () {
//                    setTimeout(function () {
//                        var t = performance.timing;
////                    console.log(t.loadEventEnd - t.responseEnd); 
////                    PF('bui').hide();
////                    PF('buii').hide();
//                    }, 0);
//                };
////                2075 Contact List >  Do not retain filter on Contact Full Name
//                window.onpaint = preLoadProc();
////            2075 Contact List >  Do not retain filter on Contact Full Name
//                function preLoadProc() {
//                     //$('#contDetailForm\\:dtContList\\:contFullName\\:filter').val('');
////                    PF('bui').show();
////                    PF('buii').show();
////                    $('#contDetailForm\\:dtContList\\:contFullName\\:filter').val('');
////                    PF('contListTable').filter(); 
//                }
////                2075 Contact List >  Do not retain filter on Contact Full Name
//                function afterLoadProc() {
////                    PF('bui').hide();
////                    PF('buii').hide();
//                }
            </script> 
            <!--            PMS 2357:Contact List >  Multi-select Export and Send to Auoklose-->
            <script>
                function exportCol() {
//                    console.log();
                    var btn = document.getElementById('contDetailForm:exprtToXl');
//                    console.log('var ' + btn);
                    btn.click();
                }
            </script>             

        </f:view>
        <!--        PMS 3623 Person Company Contact  Deletion-->
        <h:form id="frmPersonCompDeletCnf">
            <p:dialog header="Confirmation" id="dlgPersonCompDel" widgetVar="dlgPersonCompDltDlg" modal="true" resizable="false">
                <div align="center">
                    <p:outputLabel id="olLnkCnfMsg" value="Are you sure to delete?" />
                    <br/>
                    <p:outputLabel id="olLnkMsg" value="#{contactService.personCompLinkMsg}" style="color:blue" />
                    <br/>

                    <p:commandButton id="cnfYes" value="Yes" styleClass="btn btn-success  btn-xs" action="#{contactService.deletePersonCompany(1)}"  oncomplete="PF('dlgPersonCompDltDlg').hide();PF('contListTable').filter()"/>
                    <p:spacer width="4px" />
                    <p:commandButton id="cnfNo" styleClass="btn btn-danger  btn-xs" oncomplete="PF('dlgPersonCompDltDlg').hide()" value="No"/>
                </div>
            </p:dialog>
        </h:form>
    </ui:define>  

</ui:composition>