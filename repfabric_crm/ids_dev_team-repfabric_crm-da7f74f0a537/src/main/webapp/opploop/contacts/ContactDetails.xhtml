<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: ContactDetails.xhtml
// Author: Seema
//*********************************************************
/*
*
*/-->


<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                xmlns:pe="http://primefaces.org/ui/extensions"
                template="#{layoutMB.template}"
                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">

    <!--PMS 357:Page Title - Companies, Contacts, AJ, Tasks, Messages, Calendar, Planner-->
    <!--    <h:head>
            <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
        </h:head>-->
    <ui:define name="head">
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
        <title>Contact Details</title>
    </ui:define>


    <ui:define name="title">
        <ui:param name="title" value="Contacts"/>
        <div class="row">
            <div class="col-md-6">
                <h:form id="lblHdr">
                    #{contactService.contacts.pageHeader}
                </h:form>  
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>



    <ui:define name="body">

        <f:metadata>
            <f:viewParam name="id" value="#{contactService.contacts.contId}"/>  
            <!--            #3750CRM-2234: Fwd: editing contacts within companies-->
            <f:viewParam name="frm" value="#{contactService.contacts.fromPage}"/>  
            <f:viewParam name="moduleId" value="#{contactService.contacts.moduleId}"/>  
            <f:viewAction action="#{contactService.populateContacts(contactService.contacts.contId)}"/>
            <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
            <f:viewAction action="#{contactService.redirectTOUnifiedCompany()}"/>

            <f:viewAction action="#{activityJournalHdr.loadPreviewList(3, 0, 10, 1)}" />
            <!--            Related - CRM-1521: Tutorial button landing urls - Contacts-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('CONT_DTL'))}" />
            <!-- #4644: contacts handled by field setting  -->
            <f:viewAction action="#{fieldsMstService.load('CONT_DTL')}"/>
            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('CONT_DTL')}" />
        </f:metadata>
        <!--#1289: Add Page Access Check - Companies, Contacts-->
        <f:event listener="#{contactService.isContactPageAccessible}" type="preRenderView"/>
        <div class="box box-info box-body">
            <div class="left-column">
                <h:panelGroup  id="pnlContsummary">
<!--                    <h3 class="sub_title" styleClass="acct-name-hdr">#{custom.labels.get('IDS_CON_SUMMARY')}</h3>       -->
                    <div class="ui-g ui-fluid header-bar">
                        <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                        </div>
                        <div class="ui-sm-12 ui-md-6 ui-lg-6 " >
                            <p:outputLabel styleClass="acct-name-hdr" class="sub_title">#{custom.labels.get('IDS_CON_SUMMARY')}</p:outputLabel>
                        </div>
                        <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
                        </div>
                    </div>

                    <p:outputPanel id="contSummary" style="text-align: center;"   rendered="#{contactService.contacts.contId>0}" >
                        <div class="ui-g ui-fluid" align="center">

                            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                                <!--                                //PMS:2565
                                                                //Seema - 01/12/2020-->
                                <h3><p:outputLabel style="font-size:20px;" 
                                                   id="oLblSmryContName"
                                                   value="#{contactService.contacts.contTitle} #{contactService.contacts.contFullName}" /></h3>
                            </div>
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                <!--                                <center>-->
                                <div style="align-content:center">
                                    <!--                                //PMS:2565
                                                           //Seema - 01/12/2020-->
                                    <p:outputPanel class="image_box" id="pnlImage" >


                                        <p:graphicImage  alt="[No Image]" class="img_p"  value="/images/dynamic/#{contactService.dispImagePath}" cache="false"  width="162px" height="150px" id="giConImage"/>
                                        <!--                                           Feature #5459 CRM-4580: Need option to delete image under contact record-->
                                        <!-- #7097: CRM-5277: This looks bad - "Change" or "Remove" buttons on Contact Picture - too big - poornima - added div -->    
                                        <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                            <h:form enctype="multipart/form-data" id="frmImage" style="display: inline-flex" >
                                                <p:growl id="messages"  escape="false" life="6000" showDetail="false" showSummary="true" />

                                                <h:panelGrid columns="2" style="width:150px" >

                                                    <p:fileUpload style="margin-top:4px"  class="imageUpload"  mode="advanced"  allowTypes="/(\.|\/)(gif|jpe?g|png)$/" label="Change" multiple="false" fileUploadListener="#{contactService.uploadImage}" update="giConImage pnlContsummary"  auto="true" id="fileUpldImage"/>

                                                    <p:commandButton  rendered="#{contactService.showImgFlag}"  value="Remove" styleClass="btn btn-danger btn-xs" id="delImage" onclick="PF('dlgDelImageDlg').show();" style="height: 25px;font-weight: bold;width: 20px;margin-top:4px;font-size: 12px"/>

                                                </h:panelGrid>



                                            </h:form>
                                        </div>

                                    </p:outputPanel>


                                </div>

                                <!--                                </center>-->
                            </div>
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding" >
                                <!--                                //PMS:2565
                                                           //Seema - 01/12/2020-->
                                <p:outputLabel value="#{contactService.contacts.contJobTitle}" style="font-size: 16px;" id="oLblSmrycontJobTitle"/>
                            </div>
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                <!--                                //PMS:2565
                                                               //Seema - 01/12/2020-->
                                <h:form id="frmSmryContComp">
                                    <h:commandLink title="View Company Details" action="#{contactService.goToCompanyDetailsPage(contactService.contacts.contCompName)}" value="#{contactService.contacts.contCompName}" immediate="true" id="lnkSmrycontCompName"/>
                                </h:form>
                            </div>
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                <em class="fa fa-phone" title="Phone" style="height:16px;width: 16px;"/>
                                <p:spacer width="2px;" />
                                <!--                                //PMS:2565
                                                            //Seema - 01/12/2020-->
                                <!--26-06-2023 11433 CRM-7048   Repfabric: Turning text phone numbers into tel: links-->
                                <h:outputLink id="oLblSmryContPhoneBusiness" style="display: inline" value="#{rFUtilities.returnTelNum(contactService.contacts.contPhoneBusiness)}" >#{contactService.contacts.contPhoneBusiness}</h:outputLink> 
                                <!--<p:outputLabel value="#{contactService.contacts.contPhoneBusiness}" id="oLblSmryContPhoneBusiness"/>-->
                            </div>
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                <!--                                <p:graphicImage  title="Mobile" library="images" class="fa fa-mobile-phone" style="height:16px;width: 16px;"/>-->
                                <em  title="Mobile"  class="fa fa-mobile-phone" style="height:16px;width: 16px;"/>
                                <p:spacer width="2px;"/>
                                <!--                                //PMS:2565
                                                          //Seema - 01/12/2020-->
                                <!--13-06-2023 11433 CRM-7048   Repfabric: Turning text phone numbers into tel: links-->
                                <h:outputLink id="oLblSmryContPhoneMobile" value="#{rFUtilities.returnTelNum(contactService.contacts.contPhoneMobile)}" >#{contactService.contacts.contPhoneMobile}</h:outputLink>
                                <!--<p:outputLabel value="#{contactService.contacts.contPhoneMobile}" id="oLblSmryContPhoneMobile"/>-->
                            </div>

                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                <!--                                <p:graphicImage  title="E-mail Address" library="images" class="fa fa-envelope-o" style="height:16px;width: 16px;"/>-->
                                <em  title="E-mail Address"  class="fa fa-envelope-o" style="height:16px;width: 16px;"/>
                                <p:spacer width="2px;"/>
                                <!--                                //PMS:2565
                                                           //Seema - 01/12/2020-->
                                <h:outputLink title="Send E-Mail"  target="_blank" id="lnkSmryContEmailBusiness"
                                              value="#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=sendmailtocont&amp;cont_id=#{contactService.contacts.contId}" >
                                    #{contactService.contacts.contEmailBusiness}
                                </h:outputLink>
                            </div>
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                <!--                                bug #6640 address blank companies reimport map issue-->
                                <!--                                <p:graphicImage title="Business Address" library="images" class="fa fa-map-marker"  style="height:16px;width: 16px;"/>-->
                                <em title="Business Address"  class="fa fa-map-marker"  style="height:16px;width: 16px;"/>
                                <!--                                //PMS:2565
                                                         //Seema - 01/12/2020-->
                                <h:outputLink id="lnkBusiAddr" title="View in Google Maps" style="padding-left:7px;" value="#{contactService.openBusinessAddrInMap()}" target="_blank" >#{contactService.contacts.contBusiFormattedAddr}</h:outputLink>
                                <!--                                <h:outputText style="font-weight: 500 !important"
                                                                               value="#{contactService.contacts.contBusiFormattedAddr}" 
                                                                               id="oLblSmryContBusiFormattedAddr"
                                                                               title="View in Google Maps"  />-->
                            </div>
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding" style="background-color: #EFF4FD; font-weight: bold; height:25px;">
                                <!--                                //PMS:2565
                                                         //Seema - 01/12/2020-->
                                <p:outputLabel value="Alternate"   id="oLblAltPhone"/>
                            </div>
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                <!--<p:graphicImage  title="Alternate Phone" library="images" class="fa fa-phone" style="height:16px;width: 16px;"/>-->
                                <em  title="Alternate Phone"  class="fa fa-phone" style="height:16px;width: 16px;"/>
                                <p:spacer />
                                <!--                                //PMS:2565
                                                         //Seema - 01/12/2020-->
                                <!--13-06-2023 11433 CRM-7048   Repfabric: Turning text phone numbers into tel: links-->
                                <h:outputLink id="oLblSmryContPhoneAlternate" value="#{rFUtilities.returnTelNum(contactService.contacts.contPhoneAlternate)}" >#{contactService.contacts.contPhoneAlternate}</h:outputLink>
                                <!--<p:outputLabel value="#{contactService.contacts.contPhoneAlternate}" id="oLblSmryContPhoneAlternate"/>-->
                            </div>
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                <!--                                <p:graphicImage title="Alternate E-mail Address" library="images" class="fa fa-envelope-o" style="height:16px;width: 16px;"/>-->
                                <em title="Alternate E-mail Address"  class="fa fa-envelope-o" style="height:16px;width: 16px;"/>
                                <p:spacer width="2px;"/>
                                <!--                                //PMS:2565
                                                      //Seema - 01/12/2020-->
                                <h:outputLink title="Send E-Mail"  target="_blank" id="oLnkSmryContEmailAlternate"
                                              value="#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=sendmailtoalt&amp;cont_id=#{contactService.contacts.contId}" >
                                    #{contactService.contacts.contEmailAlternate}
                                </h:outputLink>
                            </div>
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding" style="background-color: #EFF4FD; font-weight: bold; height:25px;">
                                <!--                                //PMS:2565
                                                         //Seema - 01/12/2020-->
                                <p:outputLabel value="Personal"  id="oLblSmryPhoneHome" />
                            </div>
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                <!--<p:graphicImage  title="Home Phone" library="images" class="fa fa-phone" style="height:16px;width: 16px;"/>-->
                                <em  title="Home Phone" class="fa fa-phone" style="height:16px;width: 16px;"/>
                                <p:spacer />
                                <!--                                //PMS:2565
                                                       //Seema - 01/12/2020-->
                                <!--13-06-2023 11433 CRM-7048   Repfabric: Turning text phone numbers into tel: links-->
                                <h:outputLink id="oLblSmryContPhoneHome" value="#{rFUtilities.returnTelNum(contactService.contacts.contPhoneHome)}" >#{contactService.contacts.contPhoneHome}</h:outputLink>
                                <!--<p:outputLabel value="#{contactService.contacts.contPhoneHome}" id="oLblSmryContPhoneHome"/>-->
                            </div>
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                <!--                                <p:graphicImage title="Personal E-mail Address" library="images" class="fa fa-envelope-o" style="height:16px;width: 16px;"/>-->
                                <em title="Personal E-mail Address"  class="fa fa-envelope-o" style="height:16px;width: 16px;"/>    
                                <p:spacer width="2px;"/>
                                <!--                                //PMS:2565
                                                        //Seema - 01/12/2020-->
                                <h:outputLink title="Send E-Mail"  target="_blank" id="lnkSmryContEmailPersonal"
                                              value="#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=sendmailtopers&amp;cont_id=#{contactService.contacts.contId}" >   
                                    #{contactService.contacts.contEmailPersonal}
                                </h:outputLink>
                            </div>
                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                <!--                                bug #6640 address blank companies reimport map issue-->
                                <!--                                <p:graphicImage  title="Home Address" library="images" class="fa fa-map-marker"  style="height:16px;width: 16px;"/>-->
                                <em  title="Home Address" class="fa fa-map-marker"  style="height:16px;width: 16px;"/> 
                                <h:outputLink id="lnkAddr" title="View in Google Maps" style="padding-left:7px;" value="#{contactService.openInMap()}" target="_blank" >#{contactService.contacts.contHomeFormattedAddr}</h:outputLink>
<!--                               <h:outputText style="font-weight: 500 !important" id ="home" class="address" value="#{contactService.contacts.contHomeFormattedAddr}"  title="View in Google Maps"  />-->
                            </div>
                        </div>

                        <!--                        //PMS:2214
                                                //Seema - 09/11/2020-->
                        <p:outputPanel  rendered="#{contactService.contacts.contId>0}" 
                                        style="font-size: 0.795em;
                                        color: #6A6F73;">
                            <!--                                //PMS:2565
                                                       //Seema - 01/12/2020-->
                            <!--Feature #11393 CRM-7044   Leadloader insertions: denote where in the history the record came from by harshithad on 07/06/23-->
                            <h:outputLabel value="Created:"  style="font-weight: bold !important"  id="lblcontCreated"/>   
                            <p:spacer width="4"/>
                            <h:outputLabel   value="#{rFUtilities.convertFromUTC(contactService.contacts.insDate)}" id="oLblContInsDate">
                                <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                            </h:outputLabel>
                            <br></br>
                            by 
                            <h:outputLabel   value=" #{contactService.contacts.createdUser}" id="oLblcreatedUser"/>

                            <br></br>
                            <!--Feature #11393 CRM-7044   Leadloader insertions: denote where in the history the record came from by harshithad on 07/06/23-->
                            <h:outputLabel id="otptLblContSrc" value="Data Source:"  style="font-weight: bold !important"  /> 
                            <p:spacer width="4"/>
                            <h:outputLabel   value=" #{contactService.contacts.contDataSource}" />
                            <br></br>
                            <!--Feature #11393 CRM-7044   Leadloader insertions: denote where in the history the record came from by harshithad on 07/06/23-->  
                            <br/>
                            <!--                                //PMS:2565
                                                                                     //Seema - 01/12/2020-->
                            <!--Feature #11393 CRM-7044   Leadloader insertions: denote where in the history the record came from by harshithad on 07/06/23-->
                            <h:outputLabel value="Last update:"  style="font-weight: bold !important" id="oLblLastUpdate" />   
                            <p:spacer width="4"/>

                            <h:outputLabel   value="#{rFUtilities.convertFromUTC(contactService.contacts.updDate)}" id="oLblContUpdDate">
                                <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                            </h:outputLabel>

                            <br></br>
                            by 

                            <h:outputLabel   value=" #{contactService.contacts.updatedUser}" id="oLblUpdatedUser"/>
                        </p:outputPanel>



                    </p:outputPanel>

                </h:panelGroup>
            </div>
            <div class="right-column">
                <h:form>
                    <!--//#6641: CRM-5093: need SAVE AND NEW button under CONTACTS - poornima-->
                    <p:remoteCommand immediate="true" autoRun="false" name="applyContComp" actionListener="#{contactService.updateCompanyNew(viewCompLookupService.selectedCompany,viewCompLookupService.copyBusinessFields,contactService.contacts)}" update="@(.copyBusFld) :contDetailsButtonForm:tvCont:clearcomp"/>
                    <p:remoteCommand immediate="true" autoRun="false" name="applyUpdContComp" actionListener="#{contactService.updateCompanyUpd(viewCompLookupService.selectedCompany,viewCompLookupService.phoneOnChange,viewCompLookupService.addressOnChange,viewCompLookupService.emailOnChange)}" update="@(.copyBusFld) :contDetailsButtonForm:tvCont:clearcomp"/>


                </h:form>
                <h:form id="contDetailsButtonForm" >

                    <!--#5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact-->
                    <h:inputHidden id="ihContUpdated" value="#{contactService.updated}" />
                    <f:passThroughAttribute name="autocomplete" value="off"/>
                    <!--#5161: CRM-3401 Make "merge contacts" function-->
                    <p:remoteCommand name="mergContacts" immediate="true" actionListener="#{contactService.initMerge(viewContLookupService.selectedContact)}" />

                    <!--22-11-2022 9670 ESCALATIONS CRM-6350  Contacts are duplicating-->
                    <p:growl id="msgs"  escape="false" life="6000" showDetail="false" showSummary="true" widgetVar="growlCont"/>
                    <div class="box-header with-border">
                        <div id="raj" class="row">
                            <!--#1432 - Task Updates: #3 Option to view linked Tasks in Company and Contacts - sharvani - 18-08-2020-->
                            <div class="col-md-12" >
                                <!--#1998 - CRM-3393: Avoid double click - Companies, Contacts - sharvani - 13-10-2020-->
                                <!--#5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact-->
                                <!--22-11-2022 9670 ESCALATIONS CRM-6350  Contacts are duplicating-->
                                <p:commandButton id="btnCompsave" value="Save" styleClass="btn btn-success  btn-xs" 
                                                 update=":lblHdr :contDetailsButtonForm :pnlContsummary @(.required)" 
                                                 actionListener="#{contactService.saveContact()}" widgetVar="btnSave" onclick="PF('btnSave').disable();" oncomplete="PF('btnSave').enable();onSave();"/>   
                                <p:spacer width="4px"/>

                                <!--   #6641: CRM-5093: need SAVE AND NEW button under CONTACTS - poornima -->
                                <!--17-03-2022 5988 CRM-4788: FEATURE REQUEST? - SAVE before leaving contact                                -->
                                <p:commandButton id="btnSaveandNew" value="Save and New"  styleClass="btn btn-success  btn-xs" 
                                                 update=":lblHdr :contDetailsButtonForm :contDetailsButtonForm:msgs :contDetailsButtonForm:tvCont :pnlContsummary @(.required)"
                                                 actionListener="#{contactService.saveNewContact()}" widgetVar="btnSaveNew" onclick="PF('btnSaveNew').disable();" oncomplete="PF('btnSaveNew').enable();onSaveAndNew();"
                                                 />
                                <p:spacer width="4px"/>
                                <!--                                //PMS:2565
                                                                                    //Seema - 01/12/2020-->
                                <p:commandButton value="Cancel" styleClass="btn btn-warning  btn-xs"  
                                                 id="btnContCancel"
                                                 actionListener="#{contactService.onCancel()}"  update=":contDetailsButtonForm" 
                                                 process="@this"/>    
                                <p:spacer width="4px"/>
                                <p:commandButton  value="Delete" immediate="true" 
                                                  styleClass="btn btn-danger  btn-xs"  id="btnDelete" 
                                                  rendered="#{contactService.contacts.contId>0}" 
                                                  actionListener="#{contactService.deleteContact(contactService.contacts.contId,1)}"  />
                                <p:spacer width="4px"/>  

                                <p:commandButton id="btnSendEmail" 
                                                 value="Send Email" 
                                                 styleClass="btn btn-success  btn-xs" 
                                                 rendered="#{contactService.contacts.contId>0}"
                                                 immediate="true" onclick="window.open('#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=sendmailtocont&amp;cont_id=#{contactService.contacts.contId}', '_blank')"
                                                 >
                                </p:commandButton>
                                <!--                                //#3055: CRM -1911:Contacts Hofstetter clone contact
                                                                //Seemma - 25/01/2020-->
                                <p:spacer width="4px"/> 
                                <!--31-03-2022 5988 CRM-4788: FEATURE REQUEST? - SAVE before leaving contact-->
                                <!--16-12-2022 10125 ESCALATIONS CRM-6448  Cloning a Contact clones Activity Journals-->
                                <p:commandButton title="Clone"  value="Clone" styleClass="btn btn-primary btn-xs"  
                                                 id="btnClone" immediate="true"  actionListener="#{contactService.cloneContact(contactService.contacts)}" 
                                                 action="#{activityJournalHdr.clearJournalHdrList()}"
                                                 rendered="#{contactService.contacts.contId>0}" update=":contDetailsButtonForm:tvCont :pnlContsummary"   
                                                 process="@this"
                                                 oncomplete="PF('wvTvCont').select(0);$('#contDetailsButtonForm\\:tvCont\\:itFName').focus();"/>
                                <p:spacer width="4px"/> 
                                <!--#1432 - Task Updates: #3 Option to view linked Tasks in Company and Contacts - sharvani - 18-08-2020-->
                                <!--                                PMS 2123 New Contact > Tasks Button Enabled-->
                                <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
                                <p:commandButton id="btnLinkedTasks" value="Tasks" 
                                                 actionListener="#{orgTasks.listLinked('CONTACT', contactService.contacts.contId,0)}" 
                                                 rendered="#{contactService.contacts.contId!=0}"
                                                 styleClass="btn btn-primary btn-xs" update=":frmLinkedTask" 
                                                 oncomplete="PF('linkedTaskDlg').show()"/>
                                <!--                                //PMS:2339
                                                                //Seema - 18/11/2020-->
                                <p:spacer width="4px"/> 
                                <!--                                <p:commandButton title="Show History"  value="Show History" 
                                                                                 styleClass="btn btn-primary btn-xs"  
                                                                                 id="btnShwHistry" immediate="true"  
                                                                                 rendered="#{contactService.contacts.hashistory>0}"
                                                                                 actionListener="#{recordHistoryService.showContactHistory(contactService.contacts.contId,'CONT')}"
                                                                                 process="@this"     
                                                                                 />-->
                                <!--//PMS - 2886
                                //01/01/2021-->
                                <p:commandButton title="Show History"  value="Show History" 
                                                 styleClass="btn btn-primary btn-xs"  
                                                 id="btnShwHistry" immediate="true"  
                                                 rendered="#{contactService.contacts.contId>0}"
                                                 actionListener="#{recordHistoryService.showContactHistory(contactService.contacts.contId,'CONT')}"
                                                 process="@this"     
                                                 />
                                <p:spacer width="4px"/> 



                                <!-- 21/04/2021 Feature # 4260: Clipboard $ VCF buttons on contact-->
                                <!--/*7648: CRM-5509  Per Sarah Cozzens: Please add third option to Online Instance Help Button*/-->

                                <p:menuButton id="vcf" value="VCF" rendered="#{contactService.contacts.contId>0}" style="height: 0px; "  >
                                    <p:menuitem value="Download VCF" title="Download VCF" immediate="true" ajax="false" action="#{contactService.populateVCF(1)}" process="@this" />
                                    <p:menuitem id="clipboardVCF" value="Copy to Clipboard as VCF" immediate="true" style="white-space: nowrap;width:auto !important"   action="#{contactService.populateVCF(2)}" process="@this" update=":frmVcf:pgVcf" oncomplete="simulateClick();"  />
                                    <!--                                     21/05/2021 Feature #4595  vcard: copy paste is pasting into emails with formatting-->
                                    <p:menuitem id="clipboardPlainTxt" value="Copy to Clipboard as PlainText" immediate="true"  style="width: auto !important;"  action="#{contactService.populateVCF(3)}" process="@this" update=":frmVcf:pgVcf" oncomplete="simulateClick();"  />
                                </p:menuButton>

                                <!--#5161: CRM-3401 Make "merge contacts" function-->
                                <p:spacer width="4px"/>
                                <!--Bug #11302: ESCALATIONS CRM-7010   Contacts: Merge  by harshithad on 23/05/23-->
                                <p:commandButton id="btnContMerge" value="Merge to" disabled="#{contactService.disableMerge}"  immediate="true"  styleClass="btn btn-primary btn-xs" 
                                                 rendered="#{contactService.contacts.contId > 0 and contactService.checkIsUnifiedCont()}"
                                                 actionListener="#{contactService.initViewContLookup()}" 
                                                 oncomplete="PF('lookupCont').show()" update=":formContLookup :formNew" />


                            </div>                        
                        </div> 
                    </div>
                    <!--                    PMS 2141 :CRM-3452  Can't see second page of emails, no "next page" button: dynamic=true-->
                    <!--                        PMS 3096:CRM-3779  companies: multiple save button pushes sends "need to enter a company error"-->
                    <p:tabView id="tvCont" widgetVar="wvTvCont" >
                        <!--                        PMS 1916 Contacts >  Emails tab - Change Logic-->
                        <p:ajax  event="tabChange"  listener="#{contactService.onTabChange}" />

                        <!--                                //PMS:2565
                                                                                    //Seema - 01/12/2020-->
                        <p:tab title="Basic" id="tabBasic" >
                            <ui:include src="tabs/BasicTab.xhtml"/>
                        </p:tab>
                        <!--Task #8073: CRM-5494  Contact Detail List view - page unresponsive (Edge Browser)    by harshithad 06/06/22-->       
                        <p:tab id="custTab" title="#{custom.labels.get('IDS_CUSTOM_FIELDS')}" rendered="#{contactService.contacts.recId>0}" >
                            <ui:include src="tabs/CustomFields.xhtml"/>
                        </p:tab>
                        <p:tab title="Personal">
                            <ui:include src="tabs/Personal.xhtml"/>
                        </p:tab>
                        <!--Task #8073: CRM-5494  Contact Detail List view - page unresponsive (Edge Browser)    by harshithad 06/06/22--> 
                        <p:tab id="oppId" title="#{custom.labels.get('IDS_OPPS')}" rendered="#{contactService.contacts.recId>0}" >
                            <ui:include src="tabs/Opportunities.xhtml"/>
                        </p:tab>
                        <!--Task #8073: CRM-5494  Contact Detail List view - page unresponsive (Edge Browser)    by harshithad 06/06/22--> 
                        <p:tab id="webUrlTab" title="Web URLs" rendered="#{contactService.contacts.recId>0}">
                            <ui:include src="tabs/WebUrl.xhtml"/>
                        </p:tab>
                        <!--Task #8073: CRM-5494  Contact Detail List view - page unresponsive (Edge Browser)    by harshithad 06/06/22--> 
                        <p:tab id="actTab" title="#{custom.labels.get('IDS_ACT_JOURNAL')}" rendered="#{contactService.contacts.recId>0}" >
                            <ui:include src="../../Journal/JournalContacts.xhtml"/>
                        </p:tab>
                        <p:tab title="Emails" id="emailTab" rendered="#{contactService.contacts.recId>0}" >
                            <ui:include src="tabs/Emails.xhtml"/>
                        </p:tab>
                    </p:tabView>
                </h:form>
            </div>
            <ui:include src="../../lookup/CompanyLookupDlg.xhtml"/>
            <ui:include src="../../Journal/JournalActivityExport.xhtml"/>
            <!--            PMS:273
                        Seema - 25/04/2020-->
            <ui:include src="../../Journal/JournalGenericNotes.xhtml"/>
            <!--#1432 - Task Updates: #3 Option to view linked Tasks in Company and Contacts - sharvani - 18-08-2020--> 
            <ui:include src="../../lookup/TaskListDlg.xhtml"/>
            <!--                                                //PMS:2339
                                              //Seema - 18/11/2020-->
            <ui:include src="../../opploop/history/HistoryDlg.xhtml"/>
            <!--#5161: CRM-3401 Make "merge contacts" function-->            
            <ui:include src="../../lookup/ContactLookupDlg.xhtml" />
        </div>
        <!-- 21/04/2021 Feature # 4260: Clipboard $ VCF buttons on contact-->
        <h:form id="frmVcf">
            <h:panelGroup id="pgVcf">
                <h:inputTextarea rendered="#{contactService.showVcf}"  id="txtAreaVcf" value="#{contactService.vcfContent}"   onclick="copyToClipboard();" />
            </h:panelGroup>



        </h:form>


        <!--                                           Feature #5459 CRM-4580: Need option to delete image under contact record-->
        <!--         Bug #7860 CSS issue for Help button in Planner, Contact/company details page -->
        <style>
            #helpFrm\:hlp_button,#helpFrm\:crt_button {
                height: 26px !important;
                width: 100px !important;
                background-color: #F4F4F4 !important;
                border-color: #ddd !important;
                color: #444 !important;
                border: 1px solid;
                margin-top : -3px;
                font-size: 14px;

            }
            /*7648: CRM-5509  Per Sarah Cozzens: Please add third option to Online Instance Help Button*/

            .ui-button.ui-widget.ui-state-default.ui-corner-all.ui-button-text-icon-left {
                height: 25px !important; font-size: 12px;width:75px;background-color: #3c8dbc ! important;color: #fff !important

            }
            .imageUpload .ui-fileupload-buttonbar .ui-fileupload-choose .ui-button-text {
                padding-left: 0em !important;

                font-weight: normal;
                font-size: 12px;
            }
            .ui-button-text-icon-left .ui-button-text {
                padding: 0 !important;
            }





            .content-header {
                height: 51px!important;
            } 
            .content {
                padding: 0!important;
                padding-left: 15px!important;
                padding-right: 15px!important;
                margin-top: -9px!important;
            }
            label {
                font-weight: normal!important;
            }
            .required:after{content:" * ";color: red;}
            .ext {
                height:40px;
                width:90px!important;
            }
            .summary_padding{
                padding-top: 0;
                padding-bottom: 0;

            }
            /*933 CRM-2732: LOW: contact - fix element shading*/
            body .ui-g label.ui-outputlabel, body .ui-grid label.ui-outputlabel, body .box label.ui-outputlabel {
                margin-bottom: 0!important;
            }

            body .ui-g .ui-outputlabel, body .ui-grid .ui-outputlabel, body .box .ui-outputlabel {
                /*line-height: 0!important;*/
                /*4789 contact mandatory label ui issue*/
                white-space: nowrap!important;
            }


        </style>
        <!--                                //PMS:2565
                                                                                    //Seema - 01/12/2020-->
        <p:confirmDialog header="Confirmation" global="true" severity="alert" id="dlgDeleteContact"
                         showEffect="fade" hideEffect="fade" widgetVar="confirmDeleteContact" 
                         >
            <f:facet name="message">
                <p:outputLabel value="Are you sure to delete #{contactService.contacts.contFullName}?" id="lblContDeleteMsg"/>
                <br/>
                <p:outputLabel id="linkMessage" value="#{contactService.linkedMeassage}" style="color:blue"/>
            </f:facet>
            <h:form id="conDelDialog">
                <div align="center">
                    <p:commandButton value="Yes" onclick="discardChanges();"   oncomplete="PF('confirmDeleteContact').hide();" 
                                     id="btnContDltYes"
                                     styleClass="btn btn-success  btn-xs" action="#{contactService.contactDelete(true)}"
                                     update=":contDetailsButtonForm:msgs"
                                     />
                    <p:spacer width="4px"/>
                    <p:commandButton value="No" type="button" styleClass="btn btn-danger  btn-xs" 
                                     id="btnContDltNo"
                                     onclick="PF('confirmDeleteContact').hide()" />     
                </div>
            </h:form>
        </p:confirmDialog>

        <p:dialog widgetVar="groupDlg" id="dialog" width="500" height="400"  header="Select #{custom.labels.get('IDS_GRPS')}" resizable="false">
            <h:form id="grpForm">
                <!--                                //PMS:2565
                                                                                   //Seema - 01/12/2020-->
                <p:dataTable value="#{contactGroupMstService.contactGroupLst}" var="grp" 
                             selection="#{contactService.contacts.selectDisContGrp}" id="dtContactGroupLst"
                             rowKey="#{grp.contGroupId}" >
                    <p:column selectionMode="multiple" style="width: 35px"/>
                    <p:column headerText="#{custom.labels.get('IDS_CON_GRP')}">
                        #{grp.contGroupName}
                    </p:column>
                </p:dataTable>
                <div style="text-align: center">
                    <p:commandButton value="OK" style="margin-top: 10px" id="btnContactGroupLstOk"
                                     onclick="PF('groupDlg').hide()"
                                     styleClass="btn-info btn-xs"
                                     update=":contDetailsButtonForm:tvCont:displayContGrp"
                                     />
                </div>

            </h:form>
        </p:dialog>
        <!--#2164: Contact Details -> Option to remove company-->

        <p:confirmDialog id="dlgResetBusi" widgetVar="confirmResetBusi" showEffect="fade" 
                         message="Are you sure to clear company?" header="Confirmation" >
            <h:form>
                <p:spacer width="25px"/>
                <!--                                //PMS:2565
                                                                                  //Seema - 01/12/2020-->
                <p:commandButton value="Yes" styleClass="ui-confirmdialog-yes btn btn-success btn-xs" 
                                 id="btnconfirmResetBusiYes"
                                 actionListener="#{contactService.clearCompany()}" oncomplete="PF('confirmResetBusi').hide();"
                                 update=":lblHdr :contDetailsButtonForm :contDetailsButtonForm:msgs :contDetailsButtonForm:tvCont :pnlContsummary @(.required) :contDetailsButtonForm" />
                <p:spacer width="4px"/>
                <p:commandButton value="No" type="button"   id="btnconfirmResetBusiNo"
                                 styleClass="ui-confirmdialog-no btn btn-danger btn-xs"  onclick="PF('confirmResetBusi').hide();"/>
            </h:form>
        </p:confirmDialog>
        <!--                                           Feature #5459 CRM-4580: Need option to delete image under contact record-->
        <p:dialog  header="Confirmation"  id="dlgDelImage" widgetVar="dlgDelImageDlg">
            <h:form>
                <div  class="cntr">
                    <p:outputLabel  id="olAlrtMsg"  value="Are you sure you want to delete the image ?" />


                    <br/> <br/>
                    <p:commandButton id="cnfYes" immediate="true" styleClass="btn btn-success  btn-xs" value="Yes" actionListener="#{contactService.deleteImage(contactService.contacts.contId)}"  update=":pnlContsummary"
                                     oncomplete="PF('dlgDelImageDlg').hide()"/>
                    <p:spacer width="4px"/>
                    <p:commandButton id="cnfNo" process="@this" value="No" styleClass="btn btn-danger  btn-xs" oncomplete="PF('dlgDelImageDlg').hide()"/>
                </div>
            </h:form>
        </p:dialog>
        <!--#5161: CRM-3401 Make "merge contacts" function-->        
        <h:form id="mergeContConfirmForm">             
            <p:confirmDialog header="Merge Contacts" widgetVar="mergeContConfirmDlg" showEffect="fade" hideEffect="fade" id="idMergeContConfirmDlg">
                <f:facet name="message">
                    <p:outputPanel style="width:650px">
                        <div>
                            Merging contacts will copy all related #{custom.labels.get('IDS_CON_GRP')}, #{custom.labels.get('IDS_PROD_INTEREST')}, #{custom.labels.get('IDS_ACT_JOURNALS')}, Events, Planners, #{custom.labels.get('IDS_TASKS')}, #{custom.labels.get('IDS_OPPS')}, #{custom.labels.get('IDS_QUOTES')}, #{custom.labels.get('IDS_SAMPLES')} into
                            <span style="font-weight:bold">#{contactService.mergeToCont.contFullName}</span>. Are you sure to proceed?
                        </div>
                    </p:outputPanel>
                </f:facet>

                <p:commandButton id="btnMergeYes" value="Yes" actionListener="#{contactService.mergeConts()}" styleClass="ui-confirmdialog-yes btn btn-success btn-xs" oncomplete="PF('mergeConfirmDlg').hide();" />
                <p:spacer width="4" />
                <p:commandButton id="btnMergeNo" value="No" styleClass="ui-confirmdialog-no btn btn-danger btn-xs"  onclick="PF('mergeConfirmDlg').hide();"/>
            </p:confirmDialog>

        </h:form>
        <!-- 21/04/2021 Feature # 4260: Clipboard $ VCF buttons on contact-->
        <!--#5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact-->
        <script>
            function copyToClipboard() {

                document.getElementById("frmVcf:txtAreaVcf").select();
                document.execCommand('copy');
                document.getElementById("frmVcf:txtAreaVcf").style.display = "none";



            }

            function simulateClick() {

                document.getElementById('frmVcf:txtAreaVcf').click();
            }

//#5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact
            var oldFormData, newFormData;
//             #5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact
            window.onload = function () {
                    oldFormData = loadAllValues();
            };
//            #5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact
            function onSave() {
                var ihUpdated = document.getElementById('contDetailsButtonForm:ihContUpdated');
                if (ihUpdated.value === '1') {
                    oldFormData = loadAllValues();
                    ihUpdated.value = '0';
                }
            }
//            17-03-2022 5988 CRM-4788: FEATURE REQUEST? - SAVE before leaving contact
            function onSaveAndNew() {
                var ihUpdated = document.getElementById('contDetailsButtonForm:ihContUpdated');
                if (ihUpdated.value === '1') {
                    oldFormData = loadAllValues();
                    ihUpdated.value = '0';
                    window.location.href = "/RepfabricCRM/opploop/contacts/ContactDetails.xhtml";
                }
            }
// #5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact
            function loadAllValues() {

                var oldval = $('#contDetailsButtonForm\\:tvCont\\:itFName').val().replace(/\"/g, '\\"');
                console.log(oldval);
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itLName').val().replace(/\"/g, '\\"');
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itContCompName1').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itJobTitle').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itManager').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itRefferedBy').val();
                oldval += $(PF('wvChkPrimary').input).is(':checked');
                oldval += $(PF('wvChkglobalvisble').input).is(':checked');
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itStreet').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itPObox').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itZipCode').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itMobile').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itAltPhn').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itBusiEmail').val();
                oldval += $(PF('wvChkSyncEnable').input).is(':checked');
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itDepartment').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itAssistant').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itcontext').val();
                //03-02-2023 : #10598 : ESCALATIONS CRM-6640  CONTACTS: some in the system twice-->
                oldval += PF('wvOneMenuRegion2').getSelectedValue();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itCity').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itState').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itCntryCode').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itBusiPhn').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itFax').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itBusiEmail2').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itAlterEmail').val();
                oldval += $(PF('wvDoNotEmail').input).is(':checked');
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itNote').val();

                var contGrp = document.getElementById("contDetailsButtonForm:tvCont:menuContGrp").getElementsByTagName('input');
                for (var i in contGrp) {
                    if (contGrp[i].type == 'checkbox') {
                        if (contGrp[i].checked) {
                            oldval += contGrp[i].value.replace(/\"/g, '\\"');
                        }
                    }
                }
                var prodInter = document.getElementById("contDetailsButtonForm:tvCont:prodIntrestLbl").getElementsByTagName('input');
                for (var i in prodInter) {
                    if (prodInter[i].type == 'checkbox') {
                        if (prodInter[i].checked) {
                            oldval += prodInter[i].value.replace(/\"/g, '\\"');
                        }
                    }
                }

                //custom fields
                //personal tab
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itPStreet').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itPpoBox').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itPCity').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itPState').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itPZipCode').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itPCntryCode').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itPPhn').val();
                oldval += $('#contDetailsButtonForm\\:tvCont\\:itPEmail').val();

                //website
                //        Task #8073: CRM-5494  Contact Detail List view - page unresponsive (Edge Browser)    by harshithad 06/06/22 
                console.log(oldval);
                return oldval;
            }
// #5988: CRM-4788: FEATURE REQUEST? - SAVE before leaving contact
            $(window).on('beforeunload', function () {
                newFormData = loadAllValues();
                if (newFormData !== oldFormData) {
                    return "Changes have not been saved";
                }
                //        Task #8073: CRM-5494  Contact Detail List view - page unresponsive (Edge Browser)    by harshithad 06/06/22 
                else if (loadWebUrlsFRomDb() !== loadWebUrlFromUi()) {
                    return "Changes have not been saved";

                }
            });
            //        Task #8073: CRM-5494  Contact Detail List view - page unresponsive (Edge Browser)    by harshithad 06/06/22 
            function discardChanges() {
                var ihUpdated = document.getElementById('contDetailsButtonForm:ihContUpdated');
                oldFormData = loadAllValues();
                ihUpdated.value = '0';
            }
            function loadWebUrlsFRomDb() {
                //        Task #8073: CRM-5494  Contact Detail List view - page unresponsive (Edge Browser)    by harshithad 06/06/22 
                var oldVal = "";
                oldVal += $('#contDetailsButtonForm\\:tvCont\\:inptHiddnWebUrls').val();
                oldVal += $('#contDetailsButtonForm\\:tvCont\\:inptHiddnFbUrls').val();
                oldVal += $('#contDetailsButtonForm\\:tvCont\\:inptHiddnGoogleUrls').val();
                oldVal += $('#contDetailsButtonForm\\:tvCont\\:inptHiddnInstaUrls').val();
                oldVal += $('#contDetailsButtonForm\\:tvCont\\:inptHiddnLinkdInUrls').val();
                oldVal += $('#contDetailsButtonForm\\:tvCont\\:inptHiddnTwtrUrls').val();
                return oldVal;
            }

            function loadWebUrlFromUi() {
                //        Task #8073: CRM-5494  Contact Detail List view - page unresponsive (Edge Browser)    by harshithad 06/06/22 
                var newVal = "";
                newVal += $('#contDetailsButtonForm\\:tvCont\\:itWebsite').val();
                newVal += $('#contDetailsButtonForm\\:tvCont\\:iTFacebook').val();
                newVal += $('#contDetailsButtonForm\\:tvCont\\:iTGooglePlus').val();
                newVal += $('#contDetailsButtonForm\\:tvCont\\:iTinstgramLink').val();
                newVal += $('#contDetailsButtonForm\\:tvCont\\:iTLinkedIn').val();
                newVal += $('#contDetailsButtonForm\\:tvCont\\:iTTwitter').val();
                return newVal;
            }

        </script>

    </ui:define>  


</ui:composition>