<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">



<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                template="#{layoutMB.template}">


    <ui:define name="head">
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
        <title>Contact List</title>
    </ui:define>
    <ui:define name="meta">
        <f:view>
            <f:metadata> 
                <f:viewAction action="#{shortContListService.loadSubMenu()}"/>
                <f:viewAction action="#{shortContListService.checkUnifiedCompEnable()}"/>
                <!--                PMS 3667:CRM-3999: PersonCompany: no application setting to turn it off or on-->
                <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('CONT_LIST'))}" />
                <!--                PMS 4655:Contact List: Render/Hide Column Search filters-->
                <f:viewAction action="#{shortContListService.personComptitleLbl()}" />
                <f:viewAction action="#{shortContListService.checkRestrictiveContact()}" />
                <!--#5883: Reduced Contact List: Remove unnecessary call to contacts table-->                
                 <!--<f:viewAction action="#{shortContListService.init1()}" />-->
            </f:metadata>

        </f:view>
    </ui:define>

    <ui:define name="title">
        <ui:param name="title" value="Contact Management"/>
        <f:view>
            <!--            PMS 3380: if contact list feature turned off we can access contactsList.xhtml feature-->
            <f:event listener="#{shortContListService.isContactPageAccessible}" type="preRenderView"/>
        </f:view>
        <div class="row">
            <div class="col-md-6">
                Contact Management 
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>

    <ui:define name="menu">

    </ui:define>

    <ui:define name="body">
        <f:view>
            <div class="box box-info box-body" style="vertical-align: top">
                <div class="left-column">
                    <h:form id="filterList">
                        <!--                        PMS 3380: if contact list feature turned off we can access contactsList.xhtml feature-->
                        <!--#5888: contact filter retain issues in reduced and normal list-->
                        <p:selectOneMenu id="contFilter"    value="#{contactFilter.leftFilter.filterName}"
                                         style="border-radius: 0px;margin-top: 0px; height: auto; max-height: 230px !important; "  >
                            <f:selectItem itemValue="ALL" itemLabel="All Contacts" />
                            <f:selectItem itemValue="ALPHABETICAL" itemLabel="Alphabetical Order"/>
                            <f:selectItem itemValue="COMPANY" itemLabel="Company"/>
                            <f:selectItem itemValue="REGION" itemLabel="#{custom.labels.get('IDS_REGION')}" />
                            <f:selectItem itemValue="CONT_TYPE" itemLabel="#{custom.labels.get('IDS_COMP_TYPE')}" />
                            <f:selectItem itemValue="CONT_GROUP" itemLabel="#{custom.labels.get('IDS_CON_GRP')}" />
                            <f:selectItem itemValue="SALES_TEAM" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}" />
                            <f:selectItem itemValue="PRODUCT_INTEREST" itemLabel="#{custom.labels.get('IDS_PROD_INTEREST')}" />
                            <!--PMS 3384: contact filter retain issue-->
                            <p:ajax event="change"  update=":frmContLst:dtContLstTbl conListForm" listener="#{shortContListService.clearLeftSideFilter()}" 
                                    oncomplete="PF('wvDtContLstTbl').clearFilters();" />
                        </p:selectOneMenu>
                    </h:form>
                    <h:form id="conListForm">
                        <p:dataTable value="#{shortContListService.subFilterObj.subFilterList}" var="filtr"
                                     filteredValue="#{shortContListService.subFilterObj.filteredSubFilterList}"
                                     widgetVar="subMenuDT" rendered="#{contactFilter.leftFilter.filterName!= 'ALL'
                                                                       and contactFilter.leftFilter.filterName != 'CONT_GROUP' and contactFilter.leftFilter.filterName != 'CONT_TYPE' and contactFilter.leftFilter.filterName != 'PRODUCT_INTEREST'}"
                                     selection="#{shortContListService.subFilterObj.selectedSubFilter}" 
                                     selectionMode="single" 
                                     rowKey="#{filtr.itemId}"
                                     emptyMessage=" " paginator="true" rows="30" 
                                     paginatorAlwaysVisible="true" pageLinks="3"
                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                                     id="dtsubConList"
                                     multiViewState="true">

                            <p:ajax event="rowSelect" listener="#{shortContListService.filterContacts()}" update="frmContLst:dtContLstTbl" oncomplete="PF('wvDtContLstTbl').filter();"/>
                            <p:column filterBy="#{filtr.itemName}"  filterMatchMode="contains" id="left_filter" >
                                <h:outputText value="#{filtr.itemName}" style="text-transform: capitalize;" id="oTxtcontName" />

                            </p:column>
                        </p:dataTable>

                        <!--#5888: contact filter retain issues in reduced and normal list-->
                        <p:dataTable  value="#{shortContListService.subFilterObj.subFilterList}" widgetVar="subMenuMulDT"
                                      filteredValue="#{shortContListService.subFilterObj.filteredSubFilterList}" 
                                      selection="#{shortContListService.subFilterObj.selectedSubFilterList}" 
                                      rowKey="#{subFltr.itemId}"  pageLinks="3"
                                      paginatorTemplate="{FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                                      emptyMessage=" " paginator="true" rows="30" paginatorAlwaysVisible="true"
                                      rowSelectMode="add"   var="subFltr"   id="dlMulConList"
                                      multiViewState="true"
                                      rendered="#{contactFilter.leftFilter.filterName == 'CONT_GROUP' || contactFilter.leftFilter.filterName == 'CONT_TYPE' || contactFilter.leftFilter.filterName == 'PRODUCT_INTEREST'}">

                            <p:ajax event="rowSelect" listener="#{shortContListService.onMultipleSelect}" update="frmContLst:dtContLstTbl" oncomplete="PF('wvDtContLstTbl').filter();"/>
                            <p:ajax event="rowUnselect" listener="#{shortContListService.onMultipleSelect}" update="frmContLst:dtContLstTbl" oncomplete="PF('wvDtContLstTbl').filter();"/>
                            <p:ajax event="rowSelectCheckbox" listener="#{shortContListService.onMultipleSelect}" update="frmContLst:dtContLstTbl" oncomplete="PF('wvDtContLstTbl').filter();"/>
                            <p:ajax event="rowUnselectCheckbox" listener="#{shortContListService.onMultipleSelect}" update="frmContLst:dtContLstTbl" oncomplete="PF('wvDtContLstTbl').filter();"/>
                            <p:ajax event="toggleSelect" listener="#{shortContListService.onMultipleSelect}" update="frmContLst:dtContLstTbl" oncomplete="PF('wvDtContLstTbl').filter();"/>
                            
                            <p:column selectionMode="multiple"  style="width: 31px">
                                
                            </p:column>
                            <p:column filterBy="#{subFltr.itemName}"  filterMatchMode="contains" id="left_filter">
                                <h:outputText value="#{subFltr.itemName}" style="text-transform: capitalize;" id="oTxtContName1"/>
                            </p:column>
                        </p:dataTable>


                    </h:form>

                </div>

<!--                                     //Feature #6029 Reduced Contact List > Add Update Selected option-->
                <div class="right-column">
                    <h:form id="frmContLst">
                        <!--PMS 4655:Contact List: Render/Hide Column Search filters-->
<!--                        <p:remoteCommand name="updateTbl" id="updateTbl" action="#{shortContListService.disabledFilter()}" oncomplete="PF('dtContLstTbl').clearFilters()" update=":frmContLst:dtContLstTbl :frmContLst:dtContLstTbl:deepSearch conListForm" />-->
                        <!--#5888: contact filter retain issues in reduced and normal list-->
                        <p:dataTable id="dtContLstTbl" widgetVar="wvDtContLstTbl" value="#{shortContListService.lazyModel}" 
                                     var="cont" multiViewState="true" rows="50"  filterEvent="keyup" emptyMessage="No Contacts found." paginator="true" 
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                     rowsPerPageTemplate="50,100" 
                                     paginatorAlwaysVisible="true" 
                                     rowKey="#{cont.recId}" lazy="true"
                                     tableStyle="table-layout:auto" resizableColumns="true" 
                                     selection="#{shortContListService.selectedCont}">

                            <p:ajax event="page" listener="#{shortContListService.onPagination}" update=":frmContLst:dtContLstTbl :frmContLst:dtContLstTbl:delBtton :frmContLst:dtContLstTbl:exptyp :frmContLst:dtContLstTbl:exp :frmContLst:dtContLstTbl:btnContBulkUpdate" />
                         <!--  Feature #6030:Reduced Contact List > Add Send Mail option by Harshitha D on 11/10/2021 -->
                            <p:ajax event="rowSelectCheckbox" listener="#{shortContListService.onSelect}" update=":frmContLst:dtContLstTbl:delBtton :frmContLst:dtContLstTbl:exptyp :frmContLst:dtContLstTbl:exp :frmContLst:dtContLstTbl:btnContBulkUpdate :frmContLst:dtContLstTbl" />
                            <p:ajax event="rowUnselectCheckbox" listener="#{shortContListService.onUnSelect}" update=":frmContLst:dtContLstTbl:delBtton :frmContLst:dtContLstTbl:exptyp :frmContLst:dtContLstTbl:exp :frmContLst:dtContLstTbl:btnContBulkUpdate :frmContLst:dtContLstTbl" />
                            <p:ajax event="toggleSelect" listener="#{shortContListService.onToggleSelect}" update=":frmContLst:dtContLstTbl:delBtton :frmContLst:dtContLstTbl:exptyp :frmContLst:dtContLstTbl:exp :frmContLst:dtContLstTbl:btnContBulkUpdate :frmContLst:dtContLstTbl" />
<!--                             // Bug #7456 CRM-5424  companies and contacts list: default the cursor onto name column WESINC-->-->
                            <p:ajax   event="filter"    update=":frmContLst:dtContLstTbl"  />
                            <f:facet name="header">
                                <div style="display:inline;float: left;">
                                    <p:commandButton value="New"  styleClass="btn btn-primary btn-xs" id="btnNewCont"
                                                     action="/opploop/contacts/ContactDetails.xhtml?faces-redirect=true" 
                                                     /> 
                                    <p:spacer width="4px"/>
                                    <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
                                    <!--PMS 3667:CRM-3999: PersonCompany: no application setting to turn it off or on-->
                                    <!--PMS 3909: CRM-4090: PersonCompany: need relabel-->
                                    <!--PMS 3995:Issues related to Person Company-->
                                    <!--PMS 4655:Contact List: Render/Hide Column Search filters-->
                                    <p:commandButton id="btnNewUnifiedcntComp" value="Create #{custom.labels.get('IDS_PERSON_COMPANY')}"  title="#{shortContListService.personcompTitle}" disabled="#{shortContListService.createCompAccess}" styleClass="btn btn-primary btn-xs" 
                                                     rendered="#{shortContListService.unifiedCompEnabled}"    action="#{shortContListService.redirectToUnified()}"/> 
                                    <p:spacer width="4px"/>
                                    <p:commandButton value="Export"  
                                                     actionListener="#{exportService.prepareExportDialog('CONT')}"   
                                                     action="#{lookupService.clearContactsFilters()}"  
                                                     oncomplete="PF('dlgOppExport').show();" styleClass="btn btn-primary btn-xs" 
                                                     update=":exportOppForm :dlgheader :exportOppForm:selType :exportOppForm:filterName :exportOppForm:autoklse" 
                                                     style="#{!(shortContListService.selectedCont==null or shortContListService.selectedCont.size()==0 )?'display:none':'display:inline'}" id="exptyp"/> 
                                    <p:commandButton value="Export"  styleClass="btn btn-primary btn-xs" update=":filterList :frmContLst" action="#{shortContListService.exportOnSelection()}"  
                                                     style="#{(shortContListService.selectedCont==null or shortContListService.selectedCont.size()==0 )?'display:none':'display:inline'}"  id="exp"  >
                                    </p:commandButton>
                                    <p:spacer width="4px"/>
                                    <p:commandButton value="Re-import" id="btnReImport"
                                                     actionListener="#{bulkUpdateService.initliazeSettings('CONT')}"  
                                                     styleClass="btn btn-primary btn-xs" update=":frmBlkUpd" /> 
                                    <p:spacer width="4px"/>
                                    <p:commandButton value="Delete"
                                                     id="delBtton" 
                                                     disabled="#{shortContListService.selectedCont.size() eq null or shortContListService.selectedCont.size() eq 0  }" 
                                                     oncomplete="PF('dltBulkContDlg').show();" styleClass="btn btn-danger btn-xs"  />


<!--                                  Feature #6030:Reduced Contact List > Add Send Mail option by Harshitha D on 11/10/2021 -->
                                    <p:spacer width="4px"/>
                                    <p:commandButton value="Send Email"
                                                     actionListener="#{contactService.loadEmailDefaults(3)}"
                                                     id="btnSndEmail" 
                                                     update="dlgSendEmailCont  SendEmailForm:dtAttListCont"
                                                     disabled="#{shortContListService.sendEmailFlag}"
                                                     oncomplete="PF('sendEmailCont').show();" styleClass="btn btn-primary btn-xs"/>
                                    <p:spacer width="4px"/>
                                    
<!--                                     //Feature #6029 Reduced Contact List > Add Update Selected option-->

                                    <p:commandButton value="Update Selected"  styleClass="btn btn-primary btn-xs" id="btnContBulkUpdate" 
                                                    disabled="#{shortContListService.selectedContlistSave.size() eq null or shortContListService.selectedContlistSave.size() eq 0  }" 
                                                     actionListener="#{dataSelfService.reset()}" update=":blkUpdateForm "
                                                     action="#{dataSelfService.showDialog(shortContListService.selectedContlistSave, 'CONT','REDUCED')}" 
                                                     />

                                </div>
                                <div style="text-align: right">
                                    <!--#5883: Reduced Contact List: Remove unnecessary call to contacts table-->
                                    <h:panelGroup >
                                        <p:inputText id="globalFilter" style="width:150px" placeholder="Quick Search" class="globalFlt"/>
                                        <p:spacer width="4px" />
                                    </h:panelGroup>
                                    <!--                                        PMS CRM-2268: Electra esi: deep search contact notes-->
                                    <!--                                        <p:spacer width="4px"/>
                                                                       
                                                                            PMS 4655:Contact List: Render/Hide Column Search filters
                                                                            <p:commandButton id="btnClrSearch" process="@this" immediate="true"  class="btn-danger btn-xs" icon="fa fa-times-circle" title="Clear Extended Search" onclick="PF('dtContLstTbl').clearFilters()" oncomplete="PF('dtContLstTbl').clearFilters()"
                                                                                             actionListener="#{shortContListService.clearDeepSearch()}"
                                                                                             update=":frmContLst:dtContLstTbl conListForm" />-->
                                </div>
                            </f:facet>

                            <p:column    selectionMode="multiple"  style="width: 31px" toggleable="false" exportable="false" id="chkMultipleCont">
                            </p:column>

                            <!--Bu                           #4811:Contact: Multi Select Export-->
                            <p:column id="clmnContId" headerText="Id" style="display: none" exportable="true" 
                                      filterMatchMode="contains">
                                <h:link outcome="ContactDetails.xhtml?id=#{cont.contId}"   id="lnkContDataId" 
                                        value="#{cont.contId}"  >
                                    <f:param name="frm" value="#{view.viewId}" />
                                    <f:param name="moduleId" value="0" />
                                </h:link>


                            </p:column>

                            <!--PMS 4655:Contact List: Render/Hide Column Search filters-->
                            <p:column id="clmnFullNm" headerText="Name" exportable="true" filterBy="#{cont.contFullName}"  filterMatchMode="contains">
                                <h:link outcome="ContactDetails.xhtml?id=#{cont.contId}"   id="lnkContDataName" 
                                        value="#{cont.contFullName}"  >
                                    <f:param name="frm" value="#{view.viewId}" />
                                    <f:param name="moduleId" value="0" />
                                </h:link>


                            </p:column>
                            <!--PMS 4655:Contact List: Render/Hide Column Search filters-->
                            <p:column id="clmnComp" headerText="Company" exportable="true" filterBy="#{cont.contCompName}"  filterMatchMode="contains">
                                <h:link outcome="ContactDetails.xhtml?id=#{cont.contId}"   id="lnkContDataComp" 
                                        value="#{cont.contCompName}"  >
                                    <f:param name="frm" value="#{view.viewId}" />
                                    <f:param name="moduleId" value="0" />
                                </h:link>

                            </p:column>
                            <!--Feature #6839 CRM-5142: have default format for phone numbers-->
                            <!--PMS 4655:Contact List: Render/Hide Column Search filters-->
                            <p:column id="clmnPhone" exportable="true"  filterBy="#{cont.contPhoneBusiness}"  filterMatchMode="contains">
                                <f:facet name="header" > 
                                    <h:outputText  value="Phone" > </h:outputText>
                                    <p:tooltip value="Please search for the last 4 digits" for="clmnPhone"/>
                                </f:facet>
                                <h:link  outcome="ContactDetails.xhtml?id=#{cont.contId}"   id="lnkContDataPhone" 
                                        value="#{cont.contPhoneBusiness}"  >
                                    <f:param name="frm" value="#{view.viewId}" />
                                    <f:param name="moduleId" value="0" />
                                </h:link>

                            </p:column>
                            <!--PMS 4655:Contact List: Render/Hide Column Search filters-->
                            <p:column id="clmnJob"  headerText="Job Title" exportable="true" filterBy="#{cont.contJobTitle}"  filterMatchMode="contains">
                                <h:link outcome="ContactDetails.xhtml?id=#{cont.contId}"   id="lnkContDataJob" 
                                        value="#{cont.contJobTitle}"  >
                                    <f:param name="frm" value="#{view.viewId}" />
                                    <f:param name="moduleId" value="0" />
                                </h:link>

                            </p:column>
                            <!--PMS 4655:Contact List: Render/Hide Column Search filters-->
                            <p:column id="clmnCity"  headerText="City" exportable="true" filterBy="#{cont.contBusiCity}"  filterMatchMode="contains">
                                <h:link outcome="ContactDetails.xhtml?id=#{cont.contId}"   id="lnkContDataCity" 
                                        value="#{cont.contBusiCity}"  >
                                    <f:param name="frm" value="#{view.viewId}" />
                                    <f:param name="moduleId" value="0" />
                                </h:link>

                            </p:column>
                            <!--PMS 4655:Contact List: Render/Hide Column Search filters-->
                            <p:column id="clmnState"  headerText="State" exportable="true" filterBy="#{cont.contBusiState}"  filterMatchMode="contains">
                                <h:link outcome="ContactDetails.xhtml?id=#{cont.contId}"   id="lnkContDataState" 
                                        value="#{cont.contBusiState}"  >
                                    <f:param name="frm" value="#{view.viewId}" />
                                    <f:param name="moduleId" value="0" />
                                </h:link>

                            </p:column>
                            <!--PMS 4655:Contact List: Render/Hide Column Search filters-->
                            <p:column id="clmnEmail"  headerText="Email" exportable="true" filterBy="#{cont.contEmails}"  filterMatchMode="contains">
                                <h:link outcome="ContactDetails.xhtml?id=#{cont.contId}"   id="lnkContDataEmail" 
                                        value="#{cont.contEmails}"  >
                                    <f:param name="frm" value="#{view.viewId}" />
                                    <f:param name="moduleId" value="0" />
                                </h:link>

                            </p:column>
                            <p:column   style="width:81px "  toggleable="false" exportable="false">  
                                <!--                                PMS 3643:Person Company Contact > Deletion (Temporary Fix)-->
                                <!--PMS 3623 Person Company Contact  Deletion-->
                                <p:commandButton  styleClass="btn-danger btn-xs btn-icon-group" rendered="#{cont.contUnifiedId == 0}"
                                                  title="Delete" icon="fa fa-trash"  id="btnLstDelete" update="dltLstfrm"
                                                  actionListener="#{shortContListService.deleteContact(cont.contId,0)}" immediate="true">
                                </p:commandButton>
                                <!--PMS 3623 Person Company Contact  Deletion-->
                                <p:commandButton  styleClass="btn-danger btn-xs btn-icon-group"  rendered="#{cont.contUnifiedId > 0}"
                                                  title="Delete" icon="fa fa-trash"  id="btnLstPersonDelete" update="dltLstfrm"
                                                  actionListener="#{shortContListService.initializePersonCompDelete(cont.contId,cont.contUnifiedId)}" immediate="true">
                                </p:commandButton>
                                <p:spacer width="4px"/>
                                <p:commandButton  styleClass="btn-primary  btn-xs btn-icon" id="btnSendEmail"
                                                  title="Send Email" icon="fa fa fa-envelope-o" 
                                                  onclick="window.open('#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=sendmailtocont&amp;cont_id=#{cont.contId}', '_blank')"  >
                                </p:commandButton>
                            </p:column>


                        </p:dataTable>

                        <p:dialog id="cnfrmExprt" widgetVar="cnfrmExprt" header="Export Selected Contacts" modal="true" resizable="false" >
                            <div style="text-align: center">
                                <p:commandButton value="Export to File" id="exprtToXl" 
                                                 ajax="false" onclick="PF('cnfrmExprt').hide(); PrimeFaces.monitorDownload(null, rmClrSelectedCont)" 
                                                 onsuccess="PF('wvDtContLstTbl').clearFilters()" 
                                                 action="#{shortContListService.loadContactList()}"
                                                 update=":filterList :frmContLst" 
                                                 styleClass="btn btn-success  btn-xs" >
                                    <p:dataExporter  type="xlsx" target="dtContLstTbl"   fileName="Contacts"  selectionOnly="true"/> 


                                </p:commandButton>
                                <p:spacer width="4" />
                                <p:commandButton id="btnSndAutoklose" 
                                                 value="Send to Autoklose" action="#{autokloseService.clearReducedLst()}" 
                                                 oncomplete="PF('cnfrmExprt').hide();PF('addAutokloseDlg').show()" 
                                                 update=":autoFrm:pnl"  styleClass="btn btn-success  btn-xs" />

                                <!--                            </h:form>-->
                            </div>
                        </p:dialog>
                        <p:remoteCommand autoRun="false" id="rmClrSelectedCont" 
                                         name="rmClrSelectedCont" actionListener="#{shortContListService.clearSelectedContacts()}" 
                                         update=":filterList :frmContLst" />
                    </h:form>

                </div>
            </div>
            <h:form id="frmDlgCnfDlt">
                <p:confirmDialog header="Confirmation" id="dlgConfirmDelete"
                                 message="You are about to delete #{shortContListService.selectedCont.size()} contacts. Are you sure to proceed?" 
                                 widgetVar="dltBulkContDlg" >
                    <div align="left">

                        <p:selectBooleanCheckbox value="#{shortContListService.forceFlag}" id="oneMenuForceFlag"/>
                        <p:spacer width="4px" />
                        <p:outputLabel value="Force delete contact links (This will clear the contacts from the linked transactions)" 
                                       id="olblForceMsg"
                                       /> 
                    </div>
                    <div align="center">
                        <p:commandButton value="Yes"  id="btnForceDeleteYes"
                                         actionListener="#{shortContListService.deleteBulkContacts(shortContListService.selectedCont,shortContListService.forceFlag)}" 
                                         onstart="PF('inProgressDlg1').show();PF('dltBulkContDlg').hide();"
                                         styleClass="btn btn-success  btn-xs" update="frmContLst"   />
                        <p:spacer width="4px"/>
                        <p:commandButton value="No" styleClass="btn btn-danger  btn-xs"
                                         id="btnForceDeleteNo"
                                         onclick="PF('dltBulkContDlg').hide()"/>

                    </div>

                </p:confirmDialog>
            </h:form>

            <p:dialog widgetVar="inProgressDlg1" closable="false" modal="true" header="Message" resizable="false"  >

                <h:form id ="frmPoll">
                    <h:outputText value="Deleting contacts..." id="txtStatus" />
                    <p:outputPanel >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif" id="loadingImg1"   />
                        <p:spacer width="5" />
                        <br /><br />
                    </p:outputPanel>
                </h:form>
            </p:dialog>

            <p:confirmDialog header="Confirmation" global="true" severity="alert" id="dlgListDeleteContact"
                             showEffect="fade" hideEffect="fade" widgetVar="confirmListDeleteContact" 
                             message="Are you sure to delete?">
                <f:facet name="message">
                    <!--                                        //PMS:2564
                                                                                //seema - 01/12/2020-->
                    <p:outputLabel value="Are you sure to delete?" id="lblConfirmMsg"/>
                    <br/>
                    <p:outputLabel id="linkMessageDlg" value="#{shortContListService.linkedMeassage}" style="color:blue"/>
                </f:facet>


                <h:form id="dltLstfrm">

                    <p:growl id="cnfmlstmsgs"  escape="false" life="6000" showDetail="false" showSummary="true"/>
                    <div align="center">
                        <!--                    #2439: CRM-1512: KELLERIND Fwd: bug found in contact list: clear filter is added-->
                        <!--                                        //PMS:2564
                                                                                //seema - 01/12/2020-->
                        <!--                         PMS 3643 Person Company Contact > Deletion (Temporary Fix)-->
                        <!--PMS 3623 Person Company Contact  Deletion-->
                        <p:commandButton value="Yes"   rendered="#{shortContListService.personCompUnifiedId==null or shortContListService.personCompUnifiedId==0}"
                                         oncomplete="PF('confirmListDeleteContact').hide();PF('wvDtContLstTbl').filter()" 
                                         styleClass="btn btn-success  btn-xs" 
                                         action="#{shortContListService.contactDelete(false)}"
                                         update=":dltLstfrm:cnfmlstmsgs frmContLst:dtContLstTbl" id="btnConfirmYes"
                                         />
                        <!--                        PMS 3623 Person Company Contact  Deletion-->
                        <!--PMS 3994:CRM-4061: Person Company: Show Title in the full name-->
                        <p:commandButton value="Yes"  rendered="#{shortContListService.personCompUnifiedId!=null and shortContListService.personCompUnifiedId>0}"
                                         oncomplete="PF('confirmListDeleteContact').hide();PF('wvDtContLstTbl').filter()" 
                                         styleClass="btn btn-success  btn-xs" 
                                         action="#{shortContListService.deletePersonCompany(0)}"
                                         update=":dltLstfrm:cnfmlstmsgs frmContLst:dtContLstTbl" id="btnPersonConfirmYes"
                                         />
                        <p:spacer width="4px"/>
                        <!--                                        //PMS:2564
                                                                                //seema - 01/12/2020-->
                        <p:commandButton value="No" type="button" id="btnConfirmNo"
                                         styleClass="btn btn-danger  btn-xs" 
                                         onclick="PF('confirmListDeleteContact').hide()" />     
                    </div>
                </h:form>
            </p:confirmDialog>
            <!--            PMS 3623 Person Company Contact  Deletion-->
            <!--PMS 3623 Person Company Contact  Deletion-->
            <h:form id="frmPersonCompDeletCnf">
                <p:dialog header="Confirmation" id="dlgPersonCompDel" widgetVar="dlgPersonCompDltDlg" modal="true" resizable="false">
                    <div align="center">
                        <p:outputLabel id="olLnkCnfMsg" value="Are you sure to delete?" />
                        <br/>
                        <p:outputLabel id="olLnkMsg" value="#{shortContListService.personCompLinkMsg}" style="color:blue"  />
                        <br/>

                        <p:commandButton id="cnfYes" value="Yes" styleClass="btn btn-success  btn-xs" update=":dltLstfrm:cnfmlstmsgs frmContLst:dtContLstTbl"
                                         action="#{shortContListService.deletePersonCompany(1)}"  oncomplete="PF('dlgPersonCompDltDlg').hide();PF('wvDtContLstTbl').filter()"/>
                        <p:spacer width="4px" />
                        <p:commandButton id="cnfNo" styleClass="btn btn-danger  btn-xs" oncomplete="PF('dlgPersonCompDltDlg').hide()" value="No"/>
                    </div>
                </p:dialog>
            </h:form>

            <script>
                // Bug #7456 CRM-5424  companies and contacts list: default the cursor onto name column WESINC-->
                $(document).ready(function () {
                
//                       console.log(#{shortContListService.cursor});
                    if (#{shortContListService.cursor}) {
                        $('#frmContLst\\:dtContLstTbl\\:clmnFullNm\\:filter').focus();
                    }
                 });
                 
                function exportCol() {
                    var btn = document.getElementById('frmContLst:exprtToXl');
                    btn.click();
                }
//                frmContLst:dtContLstTbl:globalFilter
//                $('#frmContLst\\:dtContLstTbl\\:globalFilter').on("input change keyup paste propertychange ", setTimeout(function () {
//                    PF('wvDtContLstTbl').filter();
//                }, 2000));
                function delay(callback, ms) {
                    var timer = 0;
                    return function () {
                        var context = this, args = arguments;
                        clearTimeout(timer);
                        timer = setTimeout(function () {
                            callback.apply(context, args);
                        }, ms || 0);
                    };
                }
                $('#frmContLst\\:dtContLstTbl\\:globalFilter').on("input change keyup paste propertychange ", delay(function () {
                    PF('wvDtContLstTbl').filter();
                }, 1000));
//                $('.globalFlt').on('change keypress paste focus textInput input', function () {
//                    PF('wvDtContLstTbl').filter();
//                });
            </script>  
<!--   //Feature #6029 Reduced Contact List > Add Update Selected option-->
            <ui:include src="/opploop/exporter/ExportDlg.xhtml"/>
            <ui:include src="/bulkupdate/ReImportDlg.xhtml"/>
            <ui:include src="/bulkupdate/DataSelfServiceDlg.xhtml"/>

             <!--Pms Task 6838:Pitch Selector: Contacts > Send Email-->
            <ui:include src="/lookup/PitchLookup.xhtml"/>
<!--            Feature #6030:Reduced Contact List > Add Send Mail option by Harshitha D on 11/10/2021-->
            <ui:include src="/opploop/contacts/dlg/SendEmailDlg.xhtml"/>

        </f:view>
    </ui:define>  

</ui:composition>