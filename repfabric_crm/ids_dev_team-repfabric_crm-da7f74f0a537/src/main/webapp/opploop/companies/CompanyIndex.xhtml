<?xml version='1.0' encoding='UTF-8' ?>

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}"
                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">

    <h:head>

        <!--PMS 357:Page Title - Companies, Contacts, AJ, Tasks, Messages, Calendar, Planner-->
    </h:head>
    <ui:define name="head">
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
   <!--    <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>-->
        <title>Company</title>
    </ui:define>
    <ui:define name="meta">
        <f:metadata> 

            <f:viewAction action="#{companyList.loadSubMenu()}"/>
            <f:viewAction action="#{companyServiceImpl.loadCompColumnsList()}"/>
            <!--<f:viewAction action="#{companyList.filterActiveCompanies()}" />-->
            <!--#2472 - CRM-1521: Tutorial button landing urls-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('COMP_LIST'))}" />
        </f:metadata>
        <style>
            .ui-button-icon-right {
                right: 36.5em !important;
            }
            .ui-select-btn{
                border-radius: 0px;
                margin-top: 0px;
                background: #fff; 
                height:27px;
            }

            .ui-datatable .ui-datatable-header, .ui-datatable .ui-datatable-footer {
                text-align: right !important;

            }

            .ui-chkbox .ui-chkbox-box {
                display: initial !important;
            }

            .ui-datatable-scrollable-theadclone {
                visibility: collapse !important;
            }
            .sss{
                overflow-x: scroll;
                overflow-y: scroll;
                overflow: auto;
                position: fixed;
                height: 400px;
            }

            .ui-datatable-tablewrapper {
                overflow: initial !important; 
            }
            .ui-paginator {
                background-color:#ffffff1f!important;
            }
            .q{
                width: 100%!important;
            }
            .p{
                width: 80%!important;
            }
            /*.ui-datatable .ui-widget .fixed-scrollbar .ui-datatable-resizable{margin: -14px!important;}*/

        </style>
    </ui:define>

    <ui:define name="title">
        <ui:param name="title" value="Company Management"/>
        <div class="row">
            <div class="col-md-6">
                Company Management 
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>

    <ui:define name="menu">

    </ui:define>

    <ui:define name="body">
        <!--#1215: Companies - Show Access Denied screen-->
        <f:event listener="#{companyServiceImpl.isPageAccessible}" type="preRenderView"/>
        <div class="box box-info box-body" style="vertical-align: top">
            <div class="left-column">


                <h:form id="roleList">
                    <h:inputHidden id="search_filter" value="#{compFilter.leftFilter.searchFilter}" />

                    <p:selectOneMenu id="compFilter"  value="#{compFilter.leftFilter.filterName}"
                                     style="border-radius: 0px;margin-top: 0px;">
                        <!--#2551: Add ids to UI components - Companies-->
                        <f:selectItem id="itemAll" itemValue="ALL" itemLabel="All Companies" />
                        <f:selectItem id="itemAlpha" itemValue="ALPHABETICAL" itemLabel="Alphabetical Order"/>
                        <f:selectItem id="itemCallPattern" itemValue="CALL_PATT" itemLabel="#{custom.labels.get('IDS_CALL_PATTERN')}"/>
                        <f:selectItem id="itemRegion" itemValue="REGION" itemLabel="#{custom.labels.get('IDS_REGION')}" />
                        <f:selectItem id="itemPotential" itemValue="POTENTIAL" itemLabel="#{custom.labels.get('IDS_POTENTIALS')}" />
                        <f:selectItem id="itemType" itemValue="COMP_TYPE" itemLabel="#{custom.labels.get('IDS_COMP_TYPE')}" />
                        <f:selectItem id="itemClass" itemValue="CLASS" itemLabel="#{custom.labels.get('IDS_COMP_CLASS')}" />
                        <f:selectItem id="itemCatg" itemValue="CATG" itemLabel="#{custom.labels.get('IDS_CATEGORY')}" />
                        <f:selectItem id="itemSalesTeam" itemValue="SALES_TEAM" itemLabel="#{custom.labels.get('IDS_SALES_TEAM')}" />
                        <!--                        PMS:401
                                                Seema - 06/05/2020-->
                        <f:selectItem id="itemIndustry" itemValue="COMP_INDUSTRY" itemLabel="#{custom.labels.get('IDS_INDUSTRY')}" />
                        <p:ajax event="change" update="compDetailForm:dtCompList compListForm" 
                                listener="#{companyList.loadSideList()}"
                                onstart="PF('companyListTable').clearFilters();" oncomplete="PF('companyListTable').clearFilters();"
                                />
                    </p:selectOneMenu>
                </h:form>
                <h:form id="compListForm">
                    <!--Sub Filter List-->
                    <!--                    //3458 - multiViewState=true
                                        //seema - 03/03/2020-->
                    <!--                    //#3710 - CRM 2219 :summit
                                    //Seema - 12/03/2020-->
                    <p:dataTable value="#{companyList.subFilterObj.subFilterList}" 
                                 widgetVar="leftMenuDt" 
                                 filteredValue="#{companyList.subFilterObj.filteredSubFilterList}" 
                                 selection="#{companyList.subFilterObj.selectedSubFilter}"  
                                 rowKey="#{filterField.itemId}" 
                                 selectionMode="single"
                                 emptyMessage=" " 
                                 paginator="true" 
                                 multiViewState="true"
                                 rows="30" 
                                 paginatorAlwaysVisible="false" 
                                 pageLinks="3" 
                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                                 var="filterField" 
                                 id="dlCompList"  
                                 rendered="#{compFilter.leftFilter.filterName != 'CALL_PATT' and compFilter.leftFilter.filterName != 'ALL'
                                             and compFilter.leftFilter.filterName != 'POTENTIAL' and compFilter.leftFilter.filterName != 'COMP_TYPE'
                                             and compFilter.leftFilter.filterName != 'CATG' and compFilter.leftFilter.filterName != 'COMP_INDUSTRY'}">

                        <p:ajax event="rowSelect" listener="#{companyList.filterCompanies}" 
                                update=":compDetailForm:dtCompList"
                                oncomplete="PF('companyListTable').clearFilters();"/>
                        <p:column filterBy="#{filterField.itemName}"  filterMatchMode="contains" id="left_filter">
<!--                            //PMS:2562
                            //Seema - 03/12/2020-->
                            <h:outputText value="#{filterField.itemName}" style="text-transform: capitalize;" id="oTxtitemName"/>
                        </p:column>
                    </p:dataTable>

                    <!--Call pattern sub filter-->
                    <!--                    //3458 -multiViewState=true
                                        //seema - 03/03/2020-->
                    <!--                    //#3710 - CRM 2219 :summit
                                    //Seema - 12/03/2020-->
                    <p:dataTable value="#{companyList.subFilterObj.subFilterList}"  widgetVar="leftMenuDtCallPatt" 
                                 filteredValue="#{companyList.subFilterObj.filteredSubFilterList}"
                                 selection="#{companyList.subFilterObj.selectedSubFilterList}"   
                                 rowKey="#{filterField.itemId}" pageLinks="3"
                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                                 emptyMessage=" " paginator="true" rows="30" paginatorAlwaysVisible="false"
                                 rowSelectMode="add" var="filterField" id="dlCompList1"  
                                 rendered="#{compFilter.leftFilter.filterName == 'CALL_PATT' || compFilter.leftFilter.filterName == 'POTENTIAL' ||
                                             compFilter.leftFilter.filterName == 'COMP_TYPE' || compFilter.leftFilter.filterName == 'CATG' || compFilter.leftFilter.filterName == 'COMP_INDUSTRY'}">
                        <p:ajax event="rowSelect" listener="#{companyList.onMultipleSelect}" update=":compDetailForm:dtCompList" oncomplete="PF('companyListTable').clearFilters();"/>
                        <p:ajax event="rowSelectCheckbox" listener="#{companyList.onMultipleSelect}" update=":compDetailForm:dtCompList" oncomplete="PF('companyListTable').clearFilters();"/>
                        <p:ajax event="rowUnselectCheckbox" listener="#{companyList.onMultipleSelect}" update=":compDetailForm:dtCompList" oncomplete="PF('companyListTable').clearFilters();"/>
                        <p:ajax event="toggleSelect" listener="#{companyList.onMultipleSelect}" update=":compDetailForm:dtCompList" oncomplete="PF('companyListTable').clearFilters();"/>
                        <p:column selectionMode="multiple" style="width: 35px;" >
                        </p:column>
                        <p:column filterBy="#{filterField.itemName}"  filterMatchMode="contains" id="left_filter_callpatt" >
                            <h:outputText value="#{filterField.itemName}" style="text-transform: capitalize;" id="oTxtitemName1"/>
                        </p:column>
                    </p:dataTable>
        <!--            <h:inputHidden id="search_filter" value="#{compFilter.leftFilter.searchFilter}" />
                    <h:inputHidden id="search_filter_callpatt" value="#{compFilter.leftFilter.searchFilter}" />-->
                </h:form>

            </div>
            <div class="right-column">


                <h:form id="compDetailForm">
                    <!--                    #2659:CRM-1641: prysm: contacts warning should show longer-->
                    <p:growl id="delcompWarn"  life="6000" />
                    <!--                    //3458 - added multiViewState=true
                                        //seema - 03/03/2020-->

                    <!--1076: CRM-2846: Users would like improvements to the Companies Form on rowsPerPageTemplate="50,100"-->
                    <!--03-12-2020: #2600: Company Listing Updates-->
                    <p:dataTable id="dtCompList" 
                                 widgetVar="companyListTable" 
                                 paginator="true"
                                 value="#{companyList.compList}" 
                                 filterEvent="keyup" 
                                 filteredValue="#{companyList.filteredCompanies}" 
                                 var="comp" 
                                 rowKey="#{comp.compId}"
                                 paginatorAlwaysVisible="true"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rows="50" class="fixed-scrollbar"
                                 selection="#{companyList.selectedCompanies}" 
                                 emptyMessage="No companies found." 
                                 draggableColumns="true"
                                 style="margin: -10px!important;"
                                 multiViewState="true"
                                 rowsPerPageTemplate="50,100"
                                 tableStyle="table-layout:auto; " resizableColumns="true" resizeMode="expand"
                                 sortBy="#{comp.compName}"
                                 >
                        <!--#1852: CRM-3310 Companies: Sales team and Type reassignments-->
                        <p:ajax event="rowSelectCheckbox" update=":compDetailForm:dtCompList:btnBulkUpd :compDetailForm:dtCompList:exp :compDetailForm:dtCompList:expTyp" />
                        <p:ajax event="rowUnselectCheckbox" update=":compDetailForm:dtCompList:btnBulkUpd :compDetailForm:dtCompList:exp :compDetailForm:dtCompList:expTyp" />
                        <p:ajax event="toggleSelect" update=":compDetailForm:dtCompList:btnBulkUpd :compDetailForm:dtCompList:exp :compDetailForm:dtCompList:expTyp" />
                        
                        <p:ajax event="page" listener="#{compFilter.onPageChange}"/>  
                        <p:ajax event="colReorder" listener="#{companyServiceImpl.onColumnReorder}" update=":compDetailForm:dtCompList"/>
                        <f:facet name="header">
                            <div style="display: inline-flex; text-align: left;float: left">
                                <h:panelGroup>
                                    <!--#2551: Add ids to UI components - Companies-->
                                    <p:commandButton id="btnNew" value="New"  title="#{!loginBean.userCompanyCreateAccess()? 'Create Company': 'You are not authorized to create company'}" disabled="#{loginBean.userCompanyCreateAccess()}" styleClass="btn btn-primary btn-xs" action="/opploop/companies/CompanyDetails.xhtml?faces-redirect=true"/> 
                                    <p:spacer width="4px"/>
                                    <!--//#2872:Task :CRM-1799: Fwd: Filter Companies Export.-->
                                    <!--//   #3141:Task CRM-1968: Companies: Golden: block user from exporting companies-->
                                    <!--//2267-->

                                    <p:commandButton value="Export" actionListener="#{exportService.prepareExportDialog('COMP')}" action="#{lookupService.clearContactsFilters()}" oncomplete="PF('dlgOppExport').show();" styleClass="btn btn-primary btn-xs" update=":exportOppForm :dlgheader"   rendered="#{loginBean.hideCompaniesExport()}" style="#{!(companyList.selectedCompanies.size() eq null || companyList.selectedCompanies.size() eq 0)?'display:none':'display:inline'}" id="expTyp"/>


<!--Feature #2267:CRM-3494: Multi-Select Company Export Feature-->
                                    <p:commandButton value="Export"   ajax="false"  id="exp"   styleClass="btn btn-primary btn-xs"  style="#{!(companyList.selectedCompanies.size() eq null || companyList.selectedCompanies.size() eq 0)?'display:inline':'display:none'}"  update=":roleList :compDetailForm"  oncomplete="PF('companyListTable').filter();" 
                                                     onclick="PrimeFaces.monitorDownload(null, rcClearLst)">
                                        <p:dataExporter type="xlsx" target="dtCompList"  fileName="Companies"  selectionOnly="true"   />  

                                    </p:commandButton> 
                                    <p:spacer width="4px"/>

                                    <!--#2551: Add ids to UI components - Companies-->
                                    <p:commandButton id="btnReImport" value="Re-import" 
                                                     actionListener="#{bulkUpdateService.initliazeSettings('COMP')}"  
                                                     styleClass="btn btn-primary btn-xs" update=":frmBlkUpd" /> 


                                    <!--1852-->
                                    <!--1852 :CRM-3310 Companies: Sales team and Type reassignments;-->
                                    <p:spacer width="4px"/>
                                    <p:commandButton value="Update Selected"  styleClass="btn btn-primary btn-xs" id="btnBulkUpd" 
                                                     disabled="#{companyList.selectedCompanies.size() eq null || companyList.selectedCompanies.size() eq 0}"
                                                     actionListener="#{dataSelfService.reset()}" 
                                                     action="#{dataSelfService.showDialog(companyList.selectedCompanies, 'COMP')}" 
                                                     update="blkUpdateForm:blkUpd" /> 


                                </h:panelGroup>
                            </div>
                            <div style="display: inline-flex; text-align:right;">
                                <h:panelGroup>
                                    <!--                                    //#3709 - CRM 2212
                                                                        //Seema - 11/03/2020-->
                                    <!--#2551: Add ids to UI components - Companies-->
                                    <p:selectOneMenu id="ddCompStatus" class="ui-select-btn" style="width:150px" value="#{companyList.compActiveFlag}">
                                        <f:selectItem id="itemAll" itemLabel="Show All" itemValue="0"/>
                                        <f:selectItem id="itemActive" itemLabel="Show Active" itemValue="1"/>
                                        <f:selectItem id="itemInactive" itemLabel="Show Inactive" itemValue="2"/>
                                        <p:ajax event="change" listener="#{companyList.filterActiveCompanies}" update=":compDetailForm:dtCompList"
                                                oncomplete="PF('companyListTable').filter();"/>
                                    </p:selectOneMenu>
                                </h:panelGroup>
                                <p:spacer width="4"/>
                                <h:panelGroup>
                                    <!--#2551: Add ids to UI components - Companies-->
                                    <p:inputText onkeyup="PF('companyListTable').filter()" id="globalFilter"  style="width:150px" placeholder="Search fields"/>
                                </h:panelGroup>
                                <p:spacer width="4"/>
                                <p:column >

                                    <p:commandButton id="toggler" type="button" value="Columns" style="float: right" icon="fa fa-align-justify"/>
                                    <p:columnToggler datasource="dtCompList" trigger="toggler" id="togglerCompList">
                                        <p:ajax event="toggle" listener="#{companyServiceImpl.onToggle}"/>
                                    </p:columnToggler>
                                </p:column>
                                <p:spacer width="4"/>
                                <p:column>
                                    <p:commandButton title="Save settings" 
                                                     icon="fa fa-save"
                                                     id="btneSaveColumnSetting" 
                                                     action="#{companyServiceImpl.saveSettings()}" 
                                                     class="btn-success btn-xs"  />
                                </p:column>
                                <p:spacer width="4"/>
                                <p:column>
                                    <!--#2805: Analyse code and remove unnecessary code-->
                                    <p:commandButton title="Clear Filters" 
                                                     icon="fa fa-times-circle"
                                                     id="btnFilterClear"
                                                     class="btn-danger btn-xs"   
                                                     actionListener="#{compFilter.clearFilter()}"
                                                     oncomplete="PF('companyListTable').getPaginator().setPage(0);" 
                                                     />  
<!--                                     update="compDetailForm:inpTxt_filter_comp_name
                                                     compDetailForm:inpTxt_filter_phone
                                                     compDetailForm:inpTxt_filter_type
                                                     compDetailForm:inpTxt_filter_call_patt
                                                     compDetailForm:inpTxt_filter_sales_team
                                                     compDetailForm:inpTxt_filter_city
                                                     compDetailForm:inpTxt_filter_state
                                                     compDetailForm:inpTxt_filter_zipcode
                                                     compDetailForm:inpTxt_filter_class
                                                     compDetailForm:inpTxt_filter_ctgry
                                                     compDetailForm:inpTxt_filter_pobox
                                                     compDetailForm:inpTxt_filter_fax
                                                     compDetailForm:inpTxt_filter_region
                                                     compDetailForm:inpTxt_filter_webstie
                                                     compDetailForm:inpTxt_filter_refno"-->

                                </p:column>
                            </div>
                        </f:facet>
                        <!--Feature #2267:CRM-3494: Multi-Select Company Export Feature-->
                        <!--#2551: Add ids to UI components - Companies-->
                        <p:column id="cboxMultiSelect" selectionMode="multiple" toggleable="false" style="width:30px;text-align: center"  exportable="false">


                        </p:column>
                        <!--#2969 CRM-1862: Fwd: Color Coding-->
                        <!--Feature #2267:CRM-3494: Multi-Select Company Export Feature-->
                        <!--#2551: Add ids to UI components - Companies-->
                        <p:column id="boxColor" width="2%" style="width: 1px; position:relative;background-color: ##{comp.compTypeColor};border-color:transparent;border-width: 10px;" toggleable="false"  exportable="false">
                        </p:column>

                        <c:forEach var="column" items="#{companyServiceImpl.comp.listCompColumns}" id="eachComp">
                            <p:column headerText="#{column.columnHeader}" id="#{column.columnName}"  
                                      visible="#{column.columnVisibleFlag==1}"  
                                      filterMatchMode="#{column.columnFilterMatchMode}" 
                                      filterBy="#{comp[column.columnName]}" 
                                      sortBy="#{comp[column.columnName]}" 
                                      >
                                <h:link outcome="CompanyDetails.xhtml?id=#{comp.compId}"  
                                        id="col_#{column.columnName}" 
                                        value="#{comp[column.columnName]}"  />
                            </p:column>
                        </c:forEach>
<!--Feature #2267:CRM-3494: Multi-Select Company Export Feature-->
                        <p:column toggleable="false" style="width: 60px;"  exportable="false">
                            <!--                            //#3664 :CRM 2193 
                                                        //Seema  - 09/03/2020-->
                            <p:commandButton id="confirmDialog" 
                                             action="#{companyServiceImpl.deleteCompFromList(comp,companyList.compList)}" 
                                             icon="fa fa-trash" class="btn-danger" 
                                             title="Delete Company"  style="height:20px;width:20px;"
                                             oncomplete="PF('compDelete').show();">

                            </p:commandButton>
                        </p:column>
<!--Feature #2267:CRM-3494: Multi-Select Company Export Feature-->
                        <p:column toggleable="false" visible="false" headerText="tags" style="display: none;" filterBy="#{comp.compTags}" filterMatchMode="contains"  exportable="false">
                            <a href="CompanyDetails.xhtml?id=#{comp.compId}" >
                                <h:outputText value="#{comp.compTags}" id="oTxtCompTags"/>
                            </a>
                        </p:column>


                    </p:dataTable>
                    <!--#2805: Analyse code and remove unnecessary code-->
                    <!--Following code is no longer required as we use Primefaces multiviewstate property to retain the datatable filters-->
                    <!--Retain company Filter-->
<!--                    <p:remoteCommand name="callRetainFilter" actionListener="#{compFilter.retainFilter()}"  />
                    <p:remoteCommand name="callClearFilter" actionListener="#{compFilter.clearFilter()}"  />-->
<!--                    <h:inputHidden id="inpTxt_filter_comp_name" value="#{compFilter.filterName}" />
                    <h:inputHidden id="inpTxt_filter_phone" value="#{compFilter.filterPhone}" />
                    <h:inputHidden id="inpTxt_filter_type" value="#{compFilter.filterType}" />
                    <h:inputHidden id="inpTxt_filter_call_patt" value="#{compFilter.filterCallPatt}" /> 
                    <h:inputHidden id="inpTxt_filter_sales_team" value="#{compFilter.filterSalesTeam}" />
                    <h:inputHidden id="inpTxt_filter_city" value="#{compFilter.filterCity}" />
                    <h:inputHidden id="inpTxt_filter_state" value="#{compFilter.filterState}" />-->
                    <!--                       //3210:CRM- 2011
                            //Seema - 08/02/2020 - add new filter variable for zip code-->
                    <!--<h:inputHidden id="inpTxt_filter_zipcode" value="#{compFilter.filterZipCode}" />-->

                    <!--                    //    #3512Company list
                                        //    Seema - 26/02/2020-->
<!--                    <h:inputHidden id="inpTxt_filter_class" value="#{compFilter.filterClass}" />
                    <h:inputHidden id="inpTxt_filter_ctgry" value="#{compFilter.filterCtgry}" />
                    <h:inputHidden id="inpTxt_filter_pobox" value="#{compFilter.filterPobox}" />
                    <h:inputHidden id="inpTxt_filter_fax" value="#{compFilter.filterFax}" />
                    <h:inputHidden id="inpTxt_filter_region" value="#{compFilter.filterRegion}" />
                    <h:inputHidden id="inpTxt_filter_webstie" value="#{compFilter.filterWebsite}" />
                    <h:inputHidden id="inpTxt_filter_refno" value="#{compFilter.filterRefno}" />-->

                    <p:confirmDialog widgetVar="compDelete" id="compDelete" showEffect="fade" hideEffect="fade" header="Confirmation" message="Are you sure to delete #{companyServiceImpl.comp.compName} ?">
                        <div align="center">
                            <!--                            //#3664 :CRM 2193 
                                                        //Seema  - 09/03/2020-->
                            <p:commandButton value="Yes"  id="btnConfirmDeleteYes"
                                             oncomplete="PF('compDelete').hide();PF('companyListTable').filter();"
                                             actionListener="#{companyServiceImpl.deleteComp(comp,companyServiceImpl.compList)}"
                                             styleClass="btn btn-success  btn-xs"/>
                            <p:spacer width="5px"/>
                            <p:commandButton value="No" onclick="PF('compDelete').hide()" styleClass="btn btn-danger  btn-xs"  id="btnConfirmDeleteNo"/>
                        </div>
                    </p:confirmDialog>
                    <!--1852 :CRM-3310 Companies: Sales team and Type reassignments;-->
                    <p:dialog widgetVar="inProgressDlg1" closable="false" modal="true" header="Message" resizable="false"  >

                        <h:form id ="frmPoll">
                            <h:outputText value="Updating..." id="txtStatus" />
                            <p:outputPanel >
                                <br />
                                <h:graphicImage  library="images" name="ajax-loader.gif" id="loadingImg1"   />
                                <p:spacer width="5" />
                                <!--<p:outputLabel value="Processing..." />-->
                                <br /><br />
                            </p:outputPanel>

            <!--<p:poll stop="#{contactService.stopPoll()}"  interval="1" widgetVar="pollStat1" listener="#{contactService.updateStatus()}" update="txtStatus"  autoStart="false" async="true" partialSubmit="true"   />-->
                        </h:form>
                    </p:dialog>
                    <!--Feature #2267:CRM-3494: Multi-Select Company Export Feature-->
                    <p:remoteCommand autoRun="false" id="rcClearLst" name="rcClearLst" action="#{companyServiceImpl.compClearFilters()}" />
                </h:form>


            </div>
        </div>


        <ui:include src="/opploop/exporter/ExportDlg.xhtml"/>
        <!--<ui:include src="CompanyFilter.xhtml"/>-->

        <style>

            .content-header {
                height: 51px!important;
            } 
            .content {
                padding: 0!important;

                margin-top: -9px!important;
            }
            label {
                font-weight: normal!important;
            }
            .required:after{content:" * ";color: red;}
            .ext {
                height:40px;
                width:90px!important;
            }
            #compFilterColumn tbody tr td:first-child{
                vertical-align: top;
            }
            #compListForm\:dlCompList1 tbody div.ui-chkbox span.ui-icon-check {
                /*position: relative;*/
                right: 3px!important;
                bottom: 3px!important;
            }
            #colorColumn {
                text-align:center;
            }
            /*#2969 CRM-1862: Fwd: Color Coding*/
            .right-column [role="gridcell"] {
                padding: 4px 6px !important;
            }
            /*#compListForm\:dlCompList1 tbody div.ui-chkbox span.ui-icon-check { 
                right: 3px!important;
                bottom: 3px!important;
            }*/

        </style>
        <script>
            $(document).ready(function() {
                $('#compDetailForm\\:dtCompList\\:compNameFilter\\:filter').focus();
            });
            //#2383: Companies > Clear out arraylist on page unload
            $(window).bind('beforeunload', function() {
                rcnUnload();
            });
        </script>
        <ui:include src="/bulkupdate/ReImportDlg.xhtml"/>
        <!--1852 :CRM-3310 Companies: Sales team and Type reassignments;-->
        <ui:include src="/bulkupdate/DataSelfServiceDlg.xhtml"/>
        <!--#2383: Companies > Clear out arraylist on page unload-->
        <h:form id="frmUnload">
            <p:remoteCommand autoRun="false" id="rcUnload" name="rcnUnload" action="#{companyList.cleanUp()}" />
        </h:form>
    </ui:define>  
</ui:composition>