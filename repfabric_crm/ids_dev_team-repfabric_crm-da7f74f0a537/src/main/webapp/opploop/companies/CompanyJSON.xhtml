<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<!--13088 ESCALATIONS   CRM-7758   Oasis: Contacts and Companies Tables, Fields and JDBC Connectors-->
<ui:composition
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:ui="http://java.sun.com/jsf/facelets">
    <f:event type="preRenderView" listener="#{oasisCompanyService.renderCompanyJson(oasisCompanyService.ctCompType,oasisCompanyService.ctCompId,oasisCompanyService.ctCompName)}" />
    <ui:define name="metadata">
        <f:metadata>
            <f:viewParam id="ctCompId" name="ctCompId" value="#{oasisCompanyService.ctCompId}"/>
            <f:viewParam id="ctCompName" name="ctCompName" value="#{oasisCompanyService.ctCompName}"/>
            <f:viewParam id="ctCompType" name="ctCompType" value="#{oasisCompanyService.ctCompType}"/>            
        </f:metadata>
    </ui:define>
</ui:composition>