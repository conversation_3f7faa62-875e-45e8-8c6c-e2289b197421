<?xml version='1.0' encoding='UTF-8' ?>

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">

    <ui:define name="head">
        <link rel="shortcut icon" href="#{request.contextPath}/resources/images/icon.png" type="image/png" />
        <title>Company Details</title>
    </ui:define>

    <ui:define name="meta">
        <f:metadata> 
            <!--PMS 2873 Code Optimization - Company Details screen-->
            <f:viewParam id="id" name="id" value="#{companyService.compId}"/>
            <f:viewParam name="id" value="#{relatedComps.reqCompId}"/>
            <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
            <f:viewParam name="unified" value="#{companyService.unified}"/>
            <f:viewAction action="#{companyService.init}" />
            <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
            <f:viewAction action="#{companyService.checkUnified()}" />
            <f:viewAction action="#{salesTeamService.loadSalesTeam()}" />
            <!--pms tassk 6518: CRM-5044: Mike Jones w/Midwest Industrial Products (RPMS) - cannot see all contacts for company-->
            <f:viewAction action="#{companyService.listContacts(companyService.companies.compId)}"/>
            <f:viewAction action="#{privateTeamMstService.loadPrivateTeams(loginBean.userId)}"/>
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('COMP_DTL'))}" />
            <!--//    7837: 30/05/2022: CRM-5623 Samples not showing in customer record/Samples Tab-->
            <f:viewAction action="#{samplesDtl.viewSampleList(companyService.companies.compId)}"/>
            <!-- #4644: handling mandatory field from settings -->
            <!--//03-11-2021 #6249: CRM-4846: Companies: Field Settings by Vithin-->
            <f:viewAction action="#{fieldsMstService.load('COMP_DTL')}"/>
            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('COMP_DTL')}" />

        </f:metadata>
    </ui:define>

    <ui:define name="title">
        <div class="row">
            <div class="col-md-6">
                <h:form id="frmHeader">
                    <!--PMS 2873 Code Optimization - Company Details screen-->
                    <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
                    <p:outputLabel  value="#{companyService.companies.compId == 0 ? 'New' : ''}" style="font-weight: normal;"  id="oLblHeader"/>
                    <p:spacer width="4px"/>
                    <!--                      PMS 3909: CRM-4090: PersonCompany: need relabel-->
                    <p:outputLabel  value="#{companyService.unified ? custom.labels.get('IDS_PERSON_COMPANY') : ' Company'}" style="font-weight: normal;"  id="oLblPageName"/>
                    <p:spacer width="4px"/>
                    <!--PMS 2873 Code Optimization - Company Details screen                    -->
                    <p:outputLabel  value="#{companyService.companies.compId > 0 ? 'Details' : ''}" style="font-weight: normal;"  id="oLblHeaderData"/>
                </h:form>

            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>



    <ui:define name="body">
        <!--#6270: Company: Attachments tab > Preview-->
        <f:view>
            <!--PMS 2873 Code Optimization - Company Details screen        -->
            <!--#1215: Companies - Show Access Denied screen-->
            <!--        PMS 3541:Company Detail  Visibility issue-->
            <f:event listener="#{companyService.isPageAccessible}" type="preRenderView"/>
            <f:event listener="#{companyService.isContactPageAccessible}" rendered="#{companyService.unified}" type="preRenderView"/>
            <!--        Bug #5006 Company Details > Samples tab-->
            <f:event listener="#{companyService.isSamplesTabAccessible}" rendered="#{companyService.samplesTabFlag}" type="preRenderView"/>
            <div class="box box-info box-body">
                <h:panelGroup id="leftColumn">
                    <div class="left-column">
                        <!--                      PMS 3909: CRM-4090: PersonCompany: need relabel-->
                        <!--                    <h3 class="sub_title" style="background:#c8d6f9;height:40px;font-size:18px;text-align:center;line-height:40px;">#{custom.labels.get('IDS_COMP_SUMMARY')}</h3> -->
                        <h3  class="sub_title" style="background:#c8d6f9;height:40px;font-size:18px;text-align:center;line-height:40px;">
                            <p:outputLabel  value="#{companyService.unified ? custom.labels.get('IDS_PCOMP_SUMMARY') : custom.labels.get('IDS_COMP_SUMMARY')}" style="font-weight: normal;"  id="oLblLeftPnlName"/>
                        </h3> 
                        <!--PMS 2873 Code Optimization - Company Details screen                    -->
                        <p:outputPanel id="compSummary" style="text-align: center;" rendered="#{!companyService.companies.compNew}">
                            <table style="width:100%;">
                                <tr>
                                    <td>
                                        <!--#2551: Add ids to UI components - Companies-->
                                        <!--PMS 2873 Code Optimization - Company Details screen                                    -->
                                        <h3><p:outputLabel id="lblSummaryName" value="#{companyService.companies.compName}" style="font-size:20px;"/></h3>
                                    </td>
                                </tr>
                                <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->                            
                                <tr >
                                    <td>
                                        <p:outputPanel id="cntPnl" rendered="#{companyService.unified}">
                                            <div style="align-content:center">
                                                <!--                                           Feature #6353 Contact > Person Company > Change image and Save : FAIL-->
                                                <p:outputPanel class="image_box" id="pnlImage" >
                                                    <p:graphicImage  alt="[No Image]" class="img_p"  value="/images/dynamic/#{companyService.dispImagePath}" cache="false"  width="170px" height="150px" id="giConImage"/>
                                                    <!-- #7097: CRM-5277: This looks bad - "Change" or "Remove" buttons on Contact Picture - too big - poornima - added div -->
                                                    <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                                        <h:form enctype="multipart/form-data" id="frmImage" style="display: inline-flex" >

                                                            <p:growl id="messages"  escape="false" life="6000" showDetail="false" showSummary="true" />
                                                            <!--  #7097: CRM-5277: This looks bad - "Change" or "Remove" buttons on Contact Picture - too big - poornima - removed layout & added width 150px  -->
                                                            <p:panelGrid columns="2" style="width:150px" >
                                                                <p:fileUpload    class="imageUpload"  mode="advanced"  allowTypes="/(\.|\/)(gif|jpe?g|png)$/" label="Change" multiple="false" fileUploadListener="#{companyService.uploadImage}" auto="true" update="giConImage :leftColumn :lblModifiedDate  :lblModifiedBy" id="fileUpldImage"> 

                                                                </p:fileUpload>  
                                                                <!--  #7097: CRM-5277: This looks bad - "Change" or "Remove" buttons on Contact Picture - too big - poornima - added style  -->
                                                                <p:commandButton  rendered="#{companyService.showImgFlag}"  value="Remove" styleClass="btn btn-danger btn-xs" id="delImage" onclick="PF('dlgDelImageDlg').show();" style="height: 25px;font-weight: bold;width: 20px;margin-top:4px;font-size: 12px"/>
                                                            </p:panelGrid>
                                                        </h:form>
                                                    </div>

                                                </p:outputPanel>
                                            </div>

                                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding" >
                                                <p:outputLabel value="#{companyService.contacts.contJobTitle}" style="font-size: 16px;" id="oLblSmrycontJobTitle"/>
                                                <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                                    <em class="fa fa-phone" title="Phone" style="height:16px;width: 16px;"/>
                                                    <p:spacer />
                                                    <!--13-06-2023 11433 CRM-7048   Repfabric: Turning text phone numbers into tel: links-->
                                                    <h:outputLink id="oLblSmryContPhoneBusiness" value="#{rFUtilities.returnTelNum(companyService.contacts.contPhoneBusiness)}" >#{companyService.contacts.contPhoneBusiness}</h:outputLink>
                                                    <!--<p:outputLabel value="#{companyService.contacts.contPhoneBusiness}" id="oLblSmryContPhoneBusiness"/>-->
                                                    <!--                            </div>
                                                                                <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">-->
                                                    <em  title="Mobile"  class="fa fa-mobile-phone" style="height:16px;width: 16px;"/>
                                                    <p:spacer />
                                                    <!--13-06-2023 11433 CRM-7048   Repfabric: Turning text phone numbers into tel: links-->
                                                    <h:outputLink id="oLblSmryContPhoneMobile" value="#{rFUtilities.returnTelNum(companyService.contacts.contPhoneMobile)}" >#{companyService.contacts.contPhoneMobile}</h:outputLink>
                                                    <!--<p:outputLabel value="#{companyService.contacts.contPhoneMobile}" id="oLblSmryContPhoneMobile"/>-->
                                                </div>
                                                <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                                    <em  title="E-mail Address"  class="fa fa-envelope-o" style="height:16px;width: 16px;"/>
                                                    <p:spacer width="2px;"/>
                                                    <h:outputLink title="Send E-Mail"  target="_blank" id="lnkSmryContEmailBusiness"
                                                                  value="#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=sendmailtocont&amp;cont_id=#{companyService.contacts.contId}" >
                                                        #{companyService.contacts.contEmailBusiness}
                                                    </h:outputLink>
                                                </div>
                                            </div>
                                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding" style="background-color: #EFF4FD; font-weight: bold; height:25px;">
                                                <p:outputLabel value="Alternate"   id="oLblAltPhone"/>
                                            </div>
                                            <div class="ui-sm-12 ui-md-12 ui-lg-12 summary_padding">
                                                <em  title="Alternate Phone"  class="fa fa-phone" style="height:16px;width: 16px;"/>
                                                <p:spacer />
                                                <!--13-06-2023 11433 CRM-7048   Repfabric: Turning text phone numbers into tel: links-->
                                                <h:outputLink id="oLblSmryContPhoneAlternate" value="#{rFUtilities.returnTelNum(companyService.contacts.contPhoneAlternate)}" >#{companyService.contacts.contPhoneAlternate}</h:outputLink>
                                                <!--<p:outputLabel value="#{companyService.contacts.contPhoneAlternate}" id="oLblSmryContPhoneAlternate"/>-->
                                                <em title="Alternate E-mail Address"  class="fa fa-envelope-o" style="height:16px;width: 16px;"/>
                                                <p:spacer width="2px;"/>
                                                <h:outputLink title="Send E-Mail"  target="_blank" id="oLnkSmryContEmailAlternate"
                                                              value="#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=sendmailtoalt&amp;cont_id=#{contactService.contacts.contId}" >
                                                    #{companyService.contacts.contEmailAlternate}
                                                </h:outputLink>
                                            </div>
                                        </p:outputPanel>
                                    </td>
                                </tr>

                                <tr>
                                    <td>
                                        <!--<p:button title="Address"  icon="fa fa-map-marker"   style="height:16px;width: 16px;display:inline;word-wrap: break-word"/>-->
                                        <i class="fa fa-map-marker"  style="height:16px;width: 16px;display:inline;word-wrap: break-word"/>
                                       <!--<p:outputLabel class="address" id="address" style="display:inline;" value="#{companyService.companies.compFormattedAddr}" title="View }in Google Maps" escape="false"  />-->

                                        <!--#2551: Add ids to UI components - Companies-->
                                        <!--PMS 2873 Code Optimization - Company Details screen                                    -->
                                        <h:outputLink id="lnkAddr" style="padding-left:7px;" value="#{companyService.redirect()}" target="_blank" >#{companyService.companies.compFormattedAddr}</h:outputLink>
                                    </td>
                                </tr>
                                <tr>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>
                                        <!--<p:graphicImage  title="Phone" library="images" class="fa fa-phone" style="height:16px;width: 16px;"/>-->
                                        <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->                                    
                                        <p:outputPanel id="compPhone1" rendered="#{!companyService.unified}">
                                            <i class="fa fa-phone"  style="height:16px;width: 16px;" />
                                            <p:spacer />
                                            <!--#2551: Add ids to UI components - Companies-->
                                            <!--PMS 2873 Code Optimization - Company Details screen                                    -->
                                            <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
                                            <!--11373: 06/06/2023: ESCALATIONS CRM-7048   Repfabric: Turning text phone numbers into tel: links-->
                                            <h:outputLink id="lblPhone1" value="#{rFUtilities.returnTelNum(companyService.companies.compPhone1)}" >#{companyService.companies.compPhone1}</h:outputLink>
                                        </p:outputPanel>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <!--<p:graphicImage title="Fax" library="images" class="fa fa-fax" style="height:16px;width: 16px;"/>-->
                                        <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->                                    
                                        <p:outputPanel id="compFax" rendered="#{!companyService.unified}">
                                            <i class="fa fa-fax" style="height:16px;width: 16px;"/>
                                            <p:spacer width="3px;"/>
                                            <!--#2551: Add ids to UI components - Companies-->
                                            <!--PMS 2873 Code Optimization - Company Details screen                                    -->
                                            <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
                                            <p:outputLabel id="lblFax" value="#{companyService.companies.compFax}" />
                                        </p:outputPanel>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <!--<p:graphicImage  title="Website" library="images" class="fa fa-globe" style="height:16px;width: 16px;"/>-->
                                        <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->                                    
                                        <h:panelGroup id="pnlCompLftWebSite" rendered="#{!companyService.unified}">
                                            <i class="fa fa-globe"  style="height:16px;width: 16px;"/>
                                            <p:spacer width="2px;"/>

                                            <!--#1533 Companies> Website>Left panel>Company Summary design broken-->
                                            <!--#2551: Add ids to UI components - Companies-->
                                            <!--PMS 2873 Code Optimization - Company Details screen                                    -->
                                            <h:outputLink id="lblWebsite" target="_blank" value="#{companyService.getValidURL(companyService.companies.compWebSite)}" title="#{companyService.companies.compWebSite}" style="word-break: break-word;" >
                                                <h:outputText value="#{companyService.companies.compWebSite}" />
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </td>
                                </tr>
                                <tr>
                                </tr>
                            </table>
                        </p:outputPanel>
                        <br/>
                        <!--//       #11705 CRM-7168   Company: Detail Page Division-->
                        <h:form id="divisionView">
                            <p:dataTable   rendered="#{companyService.companies.recId > 0 and companyService.companies.compTagged == 1 and !companyService.unified and companyService.divisionAccess}"  styleClass="mystyle"  id="divisonTbl" value="#{companyService.leftDivisionLst}" rows="10" class="table-layout:auto"    scrollable="true"   scrollHeight="100"  var="varDiv" emptyMessage="No Division-Rep Assignees found" >
                                <p:column  styleClass="division-overflow"  style="width:50px"    headerText="Division"  >
                                    <h:outputText  style="padding:4px;display: inline;" value="#{varDiv.divisionName}"/>
                                </p:column> 
                                <p:column styleClass="division-overflow" style="width:50px"   headerText="Sales Rep"    > 
                                    <h:outputText   style="padding:4px;display: inline" value="#{users.getUser(varDiv.userId)}"/>
                                 </p:column>
                            
                            </p:dataTable>
                        </h:form>
                        <h:form id="mapView">
                            <!--PMS 2873 Code Optimization - Company Details screen-->
                            <p:remoteCommand   actionListener="#{companyService.addMarker()}" autoRun="true" update=":mapView:gmap"/>
                            <!--05-09-2022 8921 ESCALATIONS CRM-6102  company name cannot be blank-->
                            <p:gmap id="gmap"  center="#{not empty companyService.longLat ? companyService.longLat:'0.0,0.0'}" zoom="10" type="ROADMAP" style="width:auto;height:400px"
                                    model="#{companyService.emptyModel}" rendered="#{!companyService.companies.compNew}"    widgetVar="map"  >
                                <p:ajax event="pointSelect" listener="#{companyService.onPointSelect}" update=":mapDlg:gmapdlg :mapDlg:srchAddr :mapDlg:txtAdrss" />
                            </p:gmap>
                        </h:form>
                        <!--PMS 2873 Code Optimization - Company Details screen-->
                    <!--Feature #11393 CRM-7044   Leadloader insertions: denote where in the history the record came from by harshithad on 07/06/23-->
                        <br/>
                        <p:outputPanel  rendered="#{companyService.companies.compId>0}"  
                                        style="font-size: 0.795em; text-align: center;
                                        color: #6A6F73;">
                            <!--#2551: Add ids to UI components - Companies-->
                            <h:outputLabel id="lblCreated" value="Created:"  style="font-weight: bold"  />   
                            <p:spacer width="4"/>
                            <h:outputLabel id="lblCreationDate"   value="#{rFUtilities.convertFromUTC(companyService.companies.insDate)}" >
                                <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                            </h:outputLabel>
                            <br></br>
                            by 
                            <h:outputLabel id="lblCreatedBy"  value=" #{companyService.companies.compInsUserName}" />

                            <br></br>
                            <!--Feature #11393 CRM-7044   Leadloader insertions: denote where in the history the record came from by harshithad on 07/06/23-->
                            <h:outputLabel id="otptLblCompSrc" value="Data Source:"  style="font-weight: bold"  /> 
                            <p:spacer width="4"/>
                            <h:outputLabel   value=" #{companyService.companies.compDataSource}" />
                            <br></br>
                           <!--Feature #11393 CRM-7044   Leadloader insertions: denote where in the history the record came from by harshithad on 07/06/23--> 
                            <br/>
                            <h:outputLabel id="lblModified" value="Last update:"  style="font-weight: bold"  />   
                            <p:spacer width="4"/>
                            <h:outputLabel id="lblModifiedDate"  value="#{rFUtilities.convertFromUTC(companyService.companies.updDate)}" >
                                <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                            </h:outputLabel>

                            <br></br>
                            by 
                            <h:outputLabel id="lblModifiedBy"  value=" #{companyService.companies.compUpdUserName}" />
                        </p:outputPanel>

                    </div>
                </h:panelGroup>
                <h:panelGroup styleClass="right-column">
                    <h:form>
    <!--                    <p:inputText value="#{companyService.pageUrl}" id="inpHiddenUrl" class="page_url" type="hidden" />-->
    <!--                    <p:remoteCommand   actionListener="#{companyService.addMarker()}" autoRun="true" update=":mapView:gmap "/>
                                            PMS 2049 CRM-3428: Merge Companies not working
                        <p:remoteCommand  id="rcGoogleMap" name="runGoogleMap" actionListener="#{companyService.updateGeoCordinates(companyService.companies.compId)}" autoRun="false" update=":mapView:gmap"/>
                        -->

                    </h:form>
                    <h:form id="compDetailsButtonForm" >
                        <f:passThroughAttribute name="autocomplete" value="off"/>
                        <!--PMS 2873 Code Optimization - Company Details screen-->                    
                        <p:remoteCommand name="applyMergeComp" immediate="true" actionListener="#{companyService.initMerge(viewCompLookupService.selectedCompany)}" />
                        <!--PMS 4128: CRM-4168: Contact Custom Fields Tab Not Showing in Person Company Profiles-->
                        <!--//09-11-2021 #6249: CRM-4846: Companies: Field Settings by Vithin-->
                        <!--#7347: Company -> New -> Save -> without Company type - validation error twice showing - poornima - added some attributes to growl msg-->
                        <p:growl id="msg" escape="false" life="6000" showDetail="false" showSummary="true" widgetVar="growlCont"/>
                        <div class="box-header with-border">
                            <div class="row">
                                <!--#9565  Companies: UI Design Issue-->
                                <div class="col-md-12" >
                                    <!--PMS 2873 Code Optimization - Company Details screen-->      
                                    <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
                                    <!--01-04-2022 4770 CRM-4378: FW: RF - Poklar-->
                                    <!--22-06-2022 8297 ESCALATIONS CRM-5827  Updating company accounts error message-->
                                    <!--<h:inputHidden id="ihCompUpdated" value="" />-->
                                    <h:inputHidden id="ihCompId" value="#{companyService.companies.compId}" />
                                    <h:inputHidden id="ihCompNewUnified" value="#{companyService.newUnifiedComp}" />
                                    <h:inputHidden id="ihCompUnified" value="#{companyService.unified}" />
                                    <!--#7347: Company -> New -> Save -> without Company type - validation error twice showing - poornima - added required in update--> 
                                    <!--22-06-2022 8297 ESCALATIONS CRM-5827  Updating company accounts error message-->
                                    <!--14-06-2023 11410 CRM-7057   Microsoft OneNote Section link URL being redirected from rf url custom field popout: Bulk-->
                                    <p:commandButton styleClass="btn btn-success btn-xs" id="cbSaveComp" action="#{companyService.saveCompany()}" rendered="#{!companyService.unified}" update=":compDetailsButtonForm:tvComp:compPnl :compDetailsButtonForm:msg :leftColumn :compDetailsButtonForm:tvComp:btnNewOpp :compDetailsButtonForm:tvComp:btnNewAJ :mapView:gmap :compDetailsButtonForm:btnChngTeam :compDetailsButtonForm:tvComp @(.required)" widgetVar="btnSave" oncomplete="PF('btnSave').enable();" onclick=" PF('btnSave').disable();" value="Save" />

                                    <!--01-04-2022 4770 CRM-4378: FW: RF - Poklar-->
                                    <!--22-06-2022 8297 ESCALATIONS CRM-5827  Updating company accounts error message-->
                                    <p:commandButton styleClass="btn btn-success btn-xs" id="cbSaveComp11" action="#{companyService.savePersonCompany()}" rendered="#{companyService.unified}"  update=":compDetailsButtonForm:tvComp:compPnl :compDetailsButtonForm:msg :leftColumn :compDetailsButtonForm:tvComp:btnNewOpp :compDetailsButtonForm:tvComp:btnNewAJ :mapView:gmap :compDetailsButtonForm:btnChngTeam" widgetVar="btnSave11" oncomplete="PF('btnSave11').enable();" onclick="PF('btnSave11').disable();" value="Save" />

<!--                                <p:commandButton  id="cbSaveComp1" value="Save"  action="#{companyService.dummySave()}" immediate="true" styleClass="btn btn-success btn-xs" rendered="#{companyService.unified}"
                  onclick="loadOldonSave();" oncomplete="upCompFields();"  update=":compDetailsButtonForm:tvComp:compPnl :compDetailsButtonForm:msg :leftColumn :compDetailsButtonForm:tvComp:btnNewOpp :compDetailsButtonForm:tvComp:btnNewAJ :mapView:gmap" />-->
                                    <p:spacer width="4px"/>
                                    <p:commandButton id="btnCancelExisting" value="Cancel" update=":compDetailsButtonForm" rendered="#{!(companyService.companies.compNew) and (!companyService.unified)}" action="#{companyService.getCompanyDetailsById(companyService.companies.compId)}" styleClass="btn btn-warning  btn-xs"/>
                                    <p:commandButton id="btnCancelPersonExisting" value="Cancel" update=":compDetailsButtonForm" rendered="#{(!companyService.companies.compNew) and (companyService.unified)}" action="#{companyService.cancelPersonCompanyEdit()}" styleClass="btn btn-warning  btn-xs"/>
                                    <p:commandButton id="btnCancelNew" value="Cancel" type="button"  onclick="window.open('./List.xhtml', '_self')"  rendered="#{companyService.companies.compNew}" styleClass="btn btn-warning  btn-xs"/>
                                    <p:spacer width="4px"/>
                                    <!--PMS 3383: app custom features Only Admin user or Owner can delete companiesnot working-->
                                    <!--PMS 3643:Person Company Contact > Deletion (Temporary Fix)-->
                                    <p:commandButton id="confirmDialog" onclick="PF('compDelete1').show()" rendered="#{companyService.companies.recId > 0 and companyService.companies.compUnifiedId==0}" disabled="#{companyService.disableDelteComp}"  class="button_top" styleClass="btn btn-danger btn-xs" value="Delete"
                                                     title="#{companyService.disableDelteComp? 'You are not authorized to delete company': 'Delete'}">
                                    </p:commandButton>
                                    <!--PMS 3623 Person Company Contact  Deletion-->
                                    <p:commandButton id="btnPersonCompDel" value="Delete" action="#{companyService.deletePersonCompany(0)}"  class="button_top" styleClass="btn btn-danger btn-xs" rendered="#{companyService.companies.recId > 0 and companyService.companies.compUnifiedId gt 0}"
                                                     disabled="#{companyService.disableDelteComp}" title="#{companyService.disableDelteComp? 'You are not authorized to delete company': 'Delete'}" />
                                    <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
                                    <p:spacer width="4px"/>
                                    <p:commandButton id="btnSendEmail" 
                                                     value="Send Email" 
                                                     styleClass="btn btn-success  btn-xs" 
                                                     rendered="#{companyService.contacts.contId>0}"
                                                     immediate="true" onclick="window.open('#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=sendmailtocont&amp;cont_id=#{companyService.contacts.contId}', '_blank')"
                                                     >
                                    </p:commandButton>
                                    <!--#9565  Companies: UI Design Issue-->
                                    <!--</div>-->
                                    <!--<div>-->
                                    <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->                                
                                    <!--Feature #11333 ESCALATIONS CRM-7023   Merge Company Not Deleting Old Company by harshithad on 29/05/23-->
                                    <p:commandButton id="btnMerge" value="Merge to" immediate="true"
                                                     rendered="#{companyService.companies.recId > 0 and !companyService.unified}"
                                                     actionListener="#{viewCompLookupService.initMergeLookup()}"
                                                     action="#{viewCompLookupService.listBySalesTeam(companyService.companies.compType, companyService.companies.compSmanId, 'applyMergeComp',true)}"
                                                     update=":formCompLookup :frmLookupHead" oncomplete="PF('lookupComp').show()"  
                                                     styleClass="btn btn-primary btn-xs" /> 
                                    <p:spacer width="4px"/>
                                    <!--19-07-2023 11674 ESCALATIONS CRM-7160   Companies: save button gets disabled after changing sales team-->
                                    <!--01-08-2023 11772 CRM-6434 Companies: user custom feature: forbid to change sales teams-->
                                    <p:commandButton id="btnChngTeam" value="Change #{custom.labels.get('IDS_SALES_TEAM')}" process="@this" disabled="#{companyService.salesTeamDisabled}"
                                                     rendered="#{companyService.companies.recId > 0 and companyService.companies.compTagged == 1}"
                                                     action="#{companyService.initOnTeamChange()}"
                                                     update=":formChangeTeam"
                                                     oncomplete="PF('dlgChangeTeam').show()"
                                                     styleClass="btn btn-primary btn-xs" /> 
                                    <p:spacer width="4px"/>
                                    <!--Feature #6751:Budde: Import Transactions: Logix Sales commission split records   by harshithad 2/2/022-->
                                    <p:commandButton id="btnRelated"  value="Related" title="Related Companies" styleClass="btn btn-primary btn-xs"
                                                     rendered="#{companyService.companies.compId ne 0}"  immediate="true"
                                                     actionListener="#{relatedComps.initializeData()}" update=":reltdCompForm"
                                                     oncomplete="PF('reltdCompDlg').show()"    />
                                    <p:spacer width="4px"/>
                                    <p:commandButton id="btnProducts" value="Products" styleClass="btn btn-primary btn-xs"
                                                     disabled="#{companyService.companies.compSmanId == 0}" rendered="#{companyService.companies.compId ne 0}"
                                                     onclick="window.open('#{request.contextPath}/datamanagement/CompCustomerProducts.xhtml?compId=#{companyService.companies.compId}', '_self');"
                                                     />
                                    <p:spacer width="4px"/>
                                    <!--13711: 15/05/2024: CRM-7998   Feature Request: Multiplier Pricing visibility on Mobile App-->
                                    <p:commandButton id="btnMultPrice" value="Pricing Multiplier" styleClass="btn btn-primary btn-xs"
                                                     disabled="#{companyService.companies.compSmanId == 0}" rendered="#{companyService.companies.compId ne 0}"
                                                     onclick="window.open('#{request.contextPath}/datamanagement/CompCustomerPriceCodes.xhtml?compId=#{companyService.companies.compId}', '_self');"
                                                     />
                                    <p:spacer width="4px"/>
                                    <!--#10213 CRM-6473  Customer master: quick link to customer summary report-->
                                    <!--   #6502: Logix Sales last 4 years report - poornima -->
                                    <!--/*7648: CRM-5509  Per Sarah Cozzens: Please add third option to Online Instance Help Button*/-->
                                    <p:menuButton id="btnDashboardManuSummary" value="Dashboard"  rendered="#{companyService.companies.compId ne 0 and companyService.companies.compType eq 1}">
                                        <p:menuitem id="btnManuSummary" value="#{custom.labels.get('IDS_PRINCI')} Summary Report" 

                                                    onclick="window.open('#{request.contextPath}/reporting/salesreports/rpt/rpt_princiSummary.xhtml?yr=#{companyService.salYear}&amp;mt=#{companyService.salMonth}&amp;p=#{companyService.companies.compId}&amp;c=0&amp;d=0&amp;prt=false&amp;reg=0&amp;repcode=3', '_blank')" 
                                                    />
                                    </p:menuButton>
                                    <p:spacer width="4px"/>
                                    <!--#10829 CRM-6740  Product Details: case navigation screen error-->
                                    <p:menuButton id="btnDashboard" value="Dashboard"   rendered="#{companyService.companies.compId ne 0 and companyService.companies.compTagged eq 1}" >
                                        <p:menuitem id="btnPrdctDtlsRpt"  value="Product Details Report" immediate="true" onclick="window.open('#{request.contextPath}/reporting/salesreports/rpt/rpt_ProdDtls_Cumu.xhtml?yr=#{companyService.salYear}&amp;mt=#{companyService.salMonth}&amp;p=0&amp;c=#{companyService.companies.compId}&amp;d=0&amp;s=0&amp;g=0&amp;cml=1&amp;pno=&amp;c3=0&amp;repcode=17', '_self');" process="@this"/>
                                        <p:menuitem id="btnLstSalesRpt" value="Last Sales Report" immediate="true" style="white-space: nowrap;width:auto !important" onclick="window.open('#{request.contextPath}/reporting/salesreports/rpt/rpt_LastSales.xhtml?c=#{companyService.companies.compId}', '_self');" process="@this"/>
                                        <p:menuitem id="btnCustDetails" rendered="#{companyService.companies.compTagged eq 1}"  value="#{custom.labels.get('IDS_CUSTOMER')} Details Report" immediate="true" onclick="window.open('#{request.contextPath}/reporting/salesreports/rpt/rpt_CustDtls.xhtml?yr=#{companyService.salYear}&amp;mt=#{companyService.salMonth}&amp;p=0&amp;c=#{companyService.companies.compId}&amp;d=0&amp;s=0&amp;repcode=2', '_blank');" process="@this"/>

                                    </p:menuButton>


                                    <p:spacer width="4px"/>
                                    <!--                                 PMS 2922 Companies: Jobs tab, Tasks button enabled while creating new Company-->
                                    <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
                                    <p:commandButton id="btnLinkedTasks" value="Tasks" actionListener="#{orgTasks.listLinked('COMPANY', companyService.companies.compId,0)}" 
                                                     styleClass="btn btn-primary btn-xs" rendered="#{companyService.companies.compId ne 0 and !companyService.unified}" update=":frmLinkedTask" oncomplete="PF('linkedTaskDlg').show()"/>
                                    <p:commandButton id="btnLinkedPersonTasks" value="Tasks"  actionListener="#{orgTasks.listLinked('PERSONCOMPANY', companyService.companies.compId,companyService.contacts.contId)}" 
                                                     styleClass="btn btn-primary btn-xs" rendered="#{companyService.companies.compId ne 0 and companyService.unified}" update=":frmLinkedTask" oncomplete="PF('linkedTaskDlg').show()"/>
                                    <p:spacer width="4px"/>
                                    <!--                          Feature #4855: Customer Pricing: Price Group   15/06/21 by harshithad-->
                                    <p:commandButton id="cmbBtnPriceGrp" value="Price Groups" oncomplete="PF('priceGrpDlg').show();" update="dlgPriceGrp"
                                                     styleClass="btn btn-primary btn-xs" rendered="#{companyService.companies.recId > 0 and companyService.companies.compTagged == 1 and companyService.companies.compSmanId > 0}"/>
                                    <p:spacer width="4px"/>
                                      <!--//       #11705 CRM-7168   Company: Detail Page Division-->
                                    <!--#9468  Divisions: Company Details > Divisions button-->
                                    <p:commandButton id="cmpyDivisions" value="Divisions" action="#{companyService.divisionCondition('compDetailsScreen')}"  actionListener="#{companyService.loadCompDivisions()}"  rendered="#{companyService.companies.recId > 0 and companyService.companies.compTagged == 1 and !companyService.unified and companyService.divisionAccess}"
                                                     styleClass="btn btn-primary btn-xs" oncomplete="PF('CompDivisionDlg').show()" update="dlgCompDivision" />

                                </div>
                            </div>
                        </div>
                        <!--PMS 3146: Company - Issue with Custom fields on Save-->
                        <!--06-09-2022 8921 ESCALATIONS CRM-6102  company name cannot be blank-->
                        <p:tabView id="tvComp" dynamic="true"  >
                            <p:ajax event="tabChange" listener="#{companyService.onTabChange}"  />
                            <p:tab id="tabBasic" title="Basic" >
                                <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
                                <ui:include src="#{companyService.basicTabPath}"/>
                            </p:tab>
                            <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
                            <p:tab id="tabCustom" title="#{custom.labels.get('IDS_CUSTOM_FIELDS')}" rendered="#{!companyService.companies.compNew and !companyService.unified}">
                                <ui:include src="tabs/CompCustomFields.xhtml"/>
                            </p:tab>
                            <!--                        19-01-2021:Task#3099-CRM-3776  change "Trip Report" to "Pitches" for ALL company types-->
                            <p:tab id="tabPitch"  rendered="#{!companyService.companies.compNew}" title="Pitches">
                                <ui:include  src="tabs/PitchTab.xhtml"/>
                            </p:tab>
                            <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
                            <p:tab id="tabContact" rendered="#{!companyService.companies.compNew and !companyService.unified}" title="Contacts" >
                                <ui:include src="tabs/ContactsTab.xhtml"/>
                            </p:tab>
                            <p:tab id="opp" rendered="#{!companyService.companies.compNew}" title="Opportunities">
                                <ui:include src="tabs/OpportunitiesTab.xhtml"/>
                            </p:tab>
                            <p:tab id="tabQuote"  rendered="#{!companyService.companies.compNew}" title="#{custom.labels.get('IDS_QUOTES')}">
                                <ui:include src="tabs/Quotes.xhtml"/>
                            </p:tab>
                            <p:tab id="tabAttachments" rendered="#{!companyService.companies.compNew}" title="Attachments">
                                <ui:include src="tabs/AttachmentsTab.xhtml"/> 
                            </p:tab>
                            <p:tab id="tabAJ" rendered="#{!companyService.companies.compNew}" title="Activity Journal">
                                <ui:include src="../../Journal/JournalCompany.xhtml"/>
                            </p:tab>
                            <p:tab id="tabBuyingFrom" rendered="#{companyService.companies.recId > 0 and (companyService.companies.compTagged == 1 or companyService.companies.compType == 3 or companyService.companies.compType == 4)}" title="Buying From" >    
                                <ui:include src="tabs/BuyingFromTab.xhtml" /> 
                            </p:tab>
                            <!--22-07-2021: 5305:  Code Optimization: Removing unnecessary calls to DB-->
                            <p:tab id="tabJobs"  title="#{custom.labels.get('IDS_JOBS')}" rendered="#{globalParams.isJobsEnabled() and companyService.companies.recId>0 and !companyService.unified}">    
                                <ui:include src="tabs/Jobs.xhtml" /> 
                            </p:tab>
                            <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
                            <!--//   Feature #4374: CRM-4255: Company details: surface emails tab-->
                            <p:tab id="tabEmail" title="Emails" rendered="#{!companyService.companies.compNew}">
                                <ui:include src="tabs/Emails.xhtml"/>
                            </p:tab>
                            <!--4375 CRM-4255: company details: surface samples tab-->
                            <!--        Bug #5006 Company Details > Samples tab-->
                            <p:tab id="tabSamples"  title="#{custom.labels.get('IDS_SAMPLES')}" rendered="#{companyService.companies.recId>0 and !companyService.unified and companyService.samplesTabFlag==1}">    
                                <ui:include src="tabs/Samples.xhtml" /> 
                            </p:tab>
                            <!--//31-05-2024 : #13804 : CRM-8043   Companies: Adding a PO section tab to the company's record-->
                            <p:tab id="tabPo"  title="#{custom.labels.get('IDS_PURCHASE_ORDERS')}" rendered="#{companyService.companies.recId>0 and !companyService.unified}">    
                                <p:remoteCommand immediate="true" process="@this" name="loadPOs" autoRun="true" actionListener="#{companyService.loadPos(companyService.compId)}"
                                        update=":compDetailsButtonForm:tvComp:companyLinkToPo"/>
                                <ui:include src="tabs/CompanyLinkToPo.xhtml" /> 
                            </p:tab>
                        </p:tabView>
                    </h:form>
                    <h:form id="delCon">
                        <p:confirmDialog widgetVar="compDelete1" id="compDelete1" showEffect="fade" hideEffect="fade" header="Confirmation" message="Are you sure to delete ?">
                            <div align="center">
                                <!--                            //PMS:2562
                                //Seema - 03/12/2020-->
                                <!--                            PMS 3623 Person Company Contact  Deletion-->
                                <!--01-04-2022 4770 CRM-4378: FW: RF - Poklar-->
                                <p:commandButton value="Yes" onclick="discardChanges();" actionListener="#{companyService.deleteCompany()}" rendered="#{!companyService.unified}"
                                                 oncomplete="PF('compDelete1').hide()"   id="btnConfirmCompDeleteYes"
                                                 styleClass="btn btn-success  btn-xs"/>
                                <!--                            PMS 3623 Person Company Contact  Deletion-->
                                <p:spacer width="5px"/>
                                <p:commandButton value="No" onclick="PF('compDelete1').hide()" 
                                                 id="btnConfirmCompDeleteNo"
                                                 styleClass="btn btn-danger  btn-xs"/>
                            </div>
                        </p:confirmDialog>




                    </h:form>
                </h:panelGroup>
                <!--                                           Feature #6353 Contact > Person Company > Change image and Save : FAIL-->
                <p:dialog  header="Confirmation"  id="dlgDelImage" widgetVar="dlgDelImageDlg">
                    <h:form>
                        <div  class="cntr">
                            <p:outputLabel  id="olAlrtMsg"  value="Are you sure you want to delete the image ?" />


                            <br/> <br/>
                            <p:commandButton id="cnfYes" process="@this" styleClass="btn btn-success  btn-xs" value="Yes" actionListener="#{companyService.deleteImage(companyService.contacts.contId)}"  update="leftColumn"
                                             oncomplete="PF('dlgDelImageDlg').hide()"/>
                            <p:spacer width="4px"/>
                            <p:commandButton id="cnfNo" process="@this" value="No" styleClass="btn btn-danger  btn-xs" oncomplete="PF('dlgDelImageDlg').hide()"/>
                        </div>
                    </h:form>
                </p:dialog>
                <!--Task#3409:Rathsburg: Related Company Exception   :16-02-2021 by harshithad-->
                <h:form id="form2">
                    <p:growl id="message" showDetail="true" />
                    <p:confirmDialog global="true" showEffect="fade" id="dlgConfirmComp1">
                        <div class="align-center">
                            <p:commandButton value="Yes" id="btnConfirmCompYes" type="button" 
                                             styleClass="ui-confirmdialog-yes btn btn-success  btn-xs"  />
                            <p:spacer width="4px"/>
                            <p:commandButton value="No" id="btnConfirmCompNo"
                                             type="button" styleClass="ui-confirmdialog-no btn btn-danger  btn-xs"   />
                        </div>
                    </p:confirmDialog>

                </h:form>

                <ui:include src="../../lookup/TaskListDlg.xhtml"/>
                <!--PMS 2873 Code Optimization - Company Details screen-->
                <h:form id="updLinkConForm">
                    <p:confirmDialog  header="Link Contact - #{viewContLookupService.selectedContact.contFullName}" 
                                      widgetVar="updLinkConDlg" id="DlgUpdLinkCon" >
                        <f:facet name="message">
                            <p:outputLabel value="There are #{custom.labels.get('IDS_OPPS')} linked to this contact. " 
                                           id="oLblCompLinkdOppMsg"
                                           rendered ="#{companyService.isContLinkedToOpp(viewContLookupService.selectedContact.contId)}" />
                            <p:outputLabel value="Do you want to proceed?" id="oLblCompLinkdOppMsg1" />
                            <br/><br/>
                            <p:selectBooleanCheckbox id="cbClear" value="#{companyService.copyBusiFields}" >
                                <p:ajax event="change" process="cbClear" />
                            </p:selectBooleanCheckbox>
                            <p:spacer width="8px"/><p:outputLabel value="Copy business fields of #{companyService.companies.compName}" id="oLblCompCopyBusiFeildsMsg" /><br/>
                        </f:facet>
                        <div class="div-center" >
                            <!--#11288  ESCALATIONS CRM-6882   Quotes: Unable to edit-->
                            <p:commandButton value="Yes" styleClass="ui-confirmdialog-yes btn btn-success btn-xs" id="oLblCompCopyBusiFeildsYes"
                                             update=":compDetailsButtonForm" actionListener="#{companyService.linkContact(viewContLookupService.selectedContact)}"
                                             oncomplete="PF('updLinkConDlg').hide();rcCont();" />
                            <p:spacer width="4px" />
                            <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no btn btn-danger btn-xs"
                                             id="oLblCompCopyBusiFeildsNo"
                                             onclick="PF('updLinkConDlg').hide();" />
                        </div>
                    </p:confirmDialog>

                </h:form>
                <!--                            //PMS:2562
                                          //Seema - 03/12/2020-->
                <p:dialog header="Change #{custom.labels.get('IDS_SALES_TEAM')}" modal="true" id="dlgcompChangeSalesTeam"
                          widgetVar="dlgChangeTeam" responsive="true" closeOnEscape="true" >
                    <style>
                        .first{
                            width: 50px;
                        }

                        .second{
                            width: 10px;
                        }
                        .panlGrid tr, .panlGrid td{
                            border:0!important;
                            padding: 1px 3px;
                        }
                    </style>
                    <h:form id="formChangeTeam">
                        <!--PMS 2873 Code Optimization - Company Details screen-->                    
                        <p:panelGrid id="gridTeamOption" columns="1" columnClasses="ui-grid-col-12"
                                     layout="grid" styleClass="box-primary no-border ui-fluid">
                            <p:selectOneRadio id="radioTeamOption"  value="#{companyService.teamOption}"  layout="pageDirection" required="true">
                                <f:selectItem itemValue="1" itemLabel="Change CRM - #{custom.labels.get('IDS_OPPS')}, #{custom.labels.get('IDS_ACT_JOURNAL')} and  #{custom.labels.get('IDS_PURCHASE_ORDERS')}"  />
                            <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                <f:selectItem itemValue="2" itemLabel="Change both CRM and historical sales and #{custom.labels.get('IDS_COMMISSION')} entries"/>
                                <f:selectItem itemValue="3" itemLabel="Effective only for future transaction" />  
                            </p:selectOneRadio>
                        </p:panelGrid>

                        <p:panelGrid id="gridFromTo" columns="2" columnClasses="ui-grid-col-3, ui-grid-col-6"
                                     layout="grid" styleClass="box-primary no-border ui-fluid">
                            <p:outputLabel id="lblfromTeam" for="fromTeam" value="From" />
                            <p:outputLabel id="fromTeam" value="#{companyService.oldSalesTeamName}"  />
                            <p:outputLabel id="lblToTeam" for="toTeam" value="To" styleClass="required"/>
                            <p:selectOneMenu id="toTeam" value="#{companyService.newSalesTeam}"   >
                                <f:selectItem itemLabel="Select #{custom.labels.get('IDS_SALES_TEAM')}" itemValue="0" />
                                <f:selectItems value="#{salesmenMst.linkedSalesmenMap}" />
                                <p:ajax event="change" process="@this" update="btnProceed" />
                            </p:selectOneMenu>
                        </p:panelGrid>

                        <br />
                        <p:spacer width="4" />
                        <p:outputLabel id="lblPushToPO" for="poExpn" value="Push pending PO to PO Exceptions" />
                        <p:spacer width="16" />
                        <p:selectBooleanCheckbox id="poExpn" value="#{companyService.poExceptionFlag}"/>
                        <div class="button_bar" align="center">
                            <!--PMS 3459: Unified Company Contact - UI-Creation-Update-->
                            <p:commandButton id="btnProceed" value="Proceed" disabled="#{companyService.companies.compType!=3 and companyService.newSalesTeam==0}"
                                             actionListener="#{companyService.changeSalesTeam()}" oncomplete="PF('dlgChangeTeam').hide();discardChanges();"
                                             update=":compDetailsButtonForm :formChangeTeam "
                                             styleClass="btn btn-success btn-xs" /> 
                            <p:spacer width="4" />
                            <p:commandButton id="btnCancel" value="Cancel" type="button"
                                             onclick="PF('dlgChangeTeam').hide()"
                                             styleClass="btn btn-warning btn-xs" /> 
                        </div>

                    </h:form>
                </p:dialog>
                <p:dialog width="490" header="Assign #{custom.labels.get('IDS_SALES_TEAM')} To Accounts" id="dlgCompAssignSalesTeam"
                          modal="true" widgetVar="dialogAssignSman"  showEffect="clip" hideEffect="clip" >
                    <h:form id="formAssignSman">
                        <style>
                            .first{
                                width: 50px;
                            }

                            .second{
                                width: 10px;
                            }
                            .panlGrid tr, .panlGrid td{
                                border:0!important;
                                padding: 1px 3px;
                            }
                            .cntr{
                                text-align:center;
                                padding: 9px;
                            }
                        </style>
                        <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                     style="min-height: 50px"  layout="grid" styleClass="box-primary no-border ui-fluid">
                            <!--                            //PMS:2562
                              //Seema - 03/12/2020-->
                            <p:selectOneRadio  value="#{salesTeamService.smanChange}"  layout="pageDirection" required=" true" id="oneRadioSmanChange">
                                <f:selectItem itemValue="1" itemLabel="Change CRM - #{custom.labels.get('IDS_OPPS')} and #{custom.labels.get('IDS_ACT_JOURNAL')}"  />
<!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                <f:selectItem itemValue="2" itemLabel="Change both CRM and historical sales and #{custom.labels.get('IDS_COMMISSION')} entries"/>

                                <f:selectItem   itemValue="3" itemLabel="Effective only for future transaction" />  
                            </p:selectOneRadio>
                        </p:panelGrid>
                        <p:panelGrid  columns="2" columnClasses="ui-grid-col-3,ui-grid-col-6"
                                      style="min-height: 50px"  layout="grid" styleClass="box-primary no-border ui-fluid">
                            <p:outputLabel for="lblSalesTeam" value="From" />
                            <p:outputLabel id="lblSalesTeam" value="#{salesTeamService.selectedSalesTeam.smanName}" />

                            <p:outputLabel for="somTosalesteam" value="To" styleClass="required"/>
                            <p:selectOneMenu  id="somTosalesteam" value="#{salesTeamService.assignTeamId}">
                                <f:selectItem itemLabel="Select #{custom.labels.get('IDS_SALES_TEAM')}" itemValue="0"/>
                                <f:selectItems value="#{salesTeamService.lstSalesTeam}" var="steam" itemLabel="#{steam.smanName}" itemValue="#{steam.smanId}"/>
                                <p:ajax update="btnAssignAcnt"/>
                            </p:selectOneMenu>
                        </p:panelGrid>
                        <br></br>
                        <div class="button_bar" align="center">
                            <p:commandButton value="Assign" styleClass="btn btn-success  btn-xs"  
                                             id="btnAssignAcnt" oncomplete="PF('dialogAssignSman').hide();PF('assignAcctProgressDlg').hide();" disabled="#{salesTeamService.assignTeamId==0}" onstart="PF('assignAcctProgressDlg').show();" actionListener="#{salesTeamService.proceedSteamChange(companyService.companies.compSmanId,salesTeamService.assignTeamId)}"/>                 
                            <p:spacer width="4px"/>
                            <!--                            //PMS:2562
                              //Seema - 03/12/2020-->
                            <p:commandButton value="Cancel" styleClass="btn btn-danger  btn-xs"  id="btnAssignAcntCancel"
                                             onclick="PF('dialogAssignSman').hide();"/>      
                        </div> 
                    </h:form>
                </p:dialog>  

                <!--#849303 - Assign Accounts - Show Progress dialog-->
                <!--                            //PMS:2562
                              //Seema - 03/12/2020-->
                <p:dialog header="Message" widgetVar="assignAcctProgressDlg" closable="false" resizable="false" modal="true" id="dlgassignAcctProgressDlg">
                    <p:outputPanel style="text-align: center" >
                        <br />
                        <h:graphicImage  library="images" name="ajax-loader.gif"    />
                        <p:spacer width="8" />
                        <h:outputLabel value="Assigning account. Please wait .." />
                        <br /><br />
                    </p:outputPanel>
                    <br />
                </p:dialog>


                <!--#1200: MIGUAT-151: companies: address changes not flowing down to contacts-->
                <p:dialog header="Update business fields for linked contacts" widgetVar="changeBusiDlg" id="dlgChangeBusi" >
                    <!--Bug #9080 ESCALATION: CRM-6150   Updating Company Screen - Dialog 'disappears' before option selected-->
                    <p:ajax event="close" listener="#{companyService.callPageRefreshCommand()}"/>
                    <h:form id="formBusiField">
                        <p:panelGrid id="gridUpdateBiz" columns="2">
                            <p:selectOneRadio id="radioUpdBizOption"  value="#{companyService.contUpdateOption}"  layout="pageDirection" required="true">
                                <f:selectItem itemValue="1" itemLabel="Apply updates if the contact had matching data"  />
                                <f:selectItem itemValue="2" itemLabel="Apply updates to all linked contacts"/>
                                <f:selectItem   itemValue="3" itemLabel="Do not change" />  
                            </p:selectOneRadio>
                        </p:panelGrid>

                        <p:commandButton id="btnApply" value="Apply" styleClass="btn btn-success btn-xs" 
                                         action="#{companyService.updateLinkedContacts()}" oncomplete="PF('changeBusiDlg').hide();" 
                                         update=":compDetailsButtonForm:tvComp:compConPanel"/>
                        <p:spacer width="4" />
                        <!--                            //PMS:2562
                               //Seema - 03/12/2020-->
                        <p:commandButton value="Cancel" styleClass="btn btn-danger btn-xs" onclick="PF('changeBusiDlg').hide();" id="btnLinkedcontactsCancel" />

                    </h:form>
                </p:dialog>

                <h:form id="mergeConfirmForm">                    
                    <!--PMS 2873 Code Optimization - Company Details screen-->                
                    <p:confirmDialog header="Merge Companies" widgetVar="mergeConfirmDlg" showEffect="fade" hideEffect="fade" id="mergeDlgConfirm">
                        <f:facet name="message">
                            <p:outputPanel style="width:650px">
                                <!--                            Bug #6600: CRM:5073: RE: Activity Journal?? - company name change isn't registering in AJ table  by harshithad  08/12/21-->
                                <!--#8387: CRM-4745 Repfabric/ Sales Reports / Inactive Customers showing up in the Company Lookup - poornima - replaced copy into move-->
                                <div>
                                    <!--Feature #13112 PRIORITY ESCALATION CRM-7752   New Relabeled Field - Commission fields  by harshithad on 13/02/23-->
                                    Merging companies will move all Contacts, #{custom.labels.get('IDS_PROD_POTENTIAL')}, #{custom.labels.get('IDS_OPPS')}, #{custom.labels.get('IDS_ACT_JOURNALS')}, #{custom.labels.get('IDS_QUOTES')}, #{custom.labels.get('IDS_PURCHASE_ORDERS')}, Sales and #{custom.labels.get('IDS_COMMISSION')} History into
                                    <span style="font-weight:bold">#{companyService.mergeToComp.compName}</span>. Are you sure to proceed?
                                </div>

                                <!--#8387: CRM-4745 Repfabric/ Sales Reports / Inactive Customers showing up in the Company Lookup - poornima-->
                                <br/>
                                <p:panelGrid style="margin-left: -8px;">
                                    <p:row>
                                        <p:column colspan="4">
                                            <p:outputLabel value="What to do with merged company?"/>
                                        </p:column>

                                        <p:column>
                                            <p:selectOneRadio  id="mergeOptions" requiredMessage="Please select the option" required="true"  value="#{companyService.mergeOption}">
                                                <f:selectItem itemValue="0" itemLabel="Mark as inactive"/> 
                                                <!--02-08-2023 11792 ESCALATIONS CRM-7211   Companies: Merging companies ignores app custom feature-->
                                                <f:selectItem itemValue="1" itemLabel="Delete" itemDisabled="#{companyService.disableDelteComp}"/>

                                            </p:selectOneRadio>
                                        </p:column>
                                    </p:row>
                                </p:panelGrid>
                               <!--#14491  CRM-8438   merging company: put checkbox to confirm that this operation cannot be undone-->
                                <div style="color: red; margin-top:20px;margin-bottom: 20px"> Warning: Merging companies is a permanent action and cannot be undone.Please confirm that you wish to proceed by checking the box below</div>
                            </p:outputPanel>
                             <!--#14491  CRM-8438   merging company: put checkbox to confirm that this operation cannot be undone-->
                            <p:selectBooleanCheckbox id="mergeFlag" value="#{viewCompLookupService.mergeCompFlag}"  >
                                <p:ajax update="mergeConfirmForm:btnMergeYes" />
                            </p:selectBooleanCheckbox><p:spacer width="9px"/><span>I understand that this operation cannot be undone and wish to continue with the merge</span>
                        </f:facet>
                         <!--#14491  CRM-8438   merging company: put checkbox to confirm that this operation cannot be undone-->
                        <!--#8387: CRM-4745 Repfabric/ Sales Reports / Inactive Customers showing up in the Company Lookup - poornima - added update-->
                        <p:commandButton id="btnMergeYes" disabled="#{!viewCompLookupService.mergeCompFlag}" value="Yes" actionListener="#{companyService.mergeComps()}" styleClass="ui-confirmdialog-yes btn btn-success btn-xs" update="mergeConfirmForm"/>
                        <p:spacer width="4" />
                        <p:commandButton id="btnMergeNo" value="No" styleClass="ui-confirmdialog-no btn btn-danger btn-xs"  onclick="PF('mergeConfirmDlg').hide();"/>
                    </p:confirmDialog>

                </h:form>

                <h:form id="unlnkConForm">
                    <!--PMS 2873 Code Optimization - Company Details screen-->                
                    <p:confirmDialog header="Unlink Contact" widgetVar="unlnkConDlg" id="DlgUnlnkCon" >
                        <f:facet name="message">
                            <p:outputLabel value="#{companyService.linkedMessage}" id="oLblunlnkConDlgMsg"/>
                            <br/>
                            <!--pms 7082:Unlink a contact from company, a default note shows that contact linked to opportunity.-->
                            <p:outputLabel value="Note: There are #{custom.labels.get('IDS_OPPS')} linked to this contact." 
                                           id="oLblunlnkConDlgNote"
                                           rendered ="#{viewContList.getOppCountByContId(companyService.linkedContId)>0}" style="color:blue" />
                        </f:facet>
                        <div class="cntr">
                            <!--//          #11288  ESCALATIONS CRM-6882   Quotes: Unable to edit-->
                            <p:commandButton id="btnContUnlinkYes" 
                                             value="Yes" action="#{companyService.clearContactBusinessDetails(companyService ,companyService.linkedContId,companyService.companies.compType,companyService.companies.compTagged)}" 
                                             oncomplete="PF('unlnkConDlg').hide();" update=":compDetailsButtonForm" 
                                             styleClass="ui-confirmdialog-yes btn btn-success btn-xs"/>
                            <p:spacer width="4px"/>
                            <p:commandButton id="btnContUnlinkNo" value="No" onclick="PF('unlnkConDlg').hide();" 
                                             styleClass="ui-confirmdialog-no btn btn-danger btn-xs"/>
                        </div>

                    </p:confirmDialog>
                </h:form>
                <!--            PMS 3623 Person Company Contact  Deletion-->
                <h:form id="frmPersonCompDeletCnf">
                    <p:dialog header="Confirmation" id="dlgPersonCompDel" widgetVar="dlgPersonCompDltDlg">
                        <div class="cntr">
                            <p:outputLabel id="olAlrtMsg" value="Are you sure to delete?" />
                            <br/>
                            <p:outputLabel id="olLnkMsg" value="#{companyService.personCompLinkMsg}" style="color:blue" />
                            <br/>
                            <!--01-04-2022 4770 CRM-4378: FW: RF - Poklar-->
                            <p:commandButton id="cnfYes" styleClass="btn btn-success  btn-xs" value="Yes" onclick="discardChanges();" action="#{companyService.forceDeleteCont()}" oncomplete="PF('dlgPersonCompDltDlg').hide()"/>
                            <p:spacer width="4px"/>
                            <p:commandButton id="cnfNo" value="No" styleClass="btn btn-danger  btn-xs" oncomplete="PF('dlgPersonCompDltDlg').hide()"/>
                        </div>
                    </p:dialog>
                </h:form>

            </div>
            <!--//       #11705 CRM-7168   Company: Detail Page Division-->
            <!--                                           Feature #6353 Contact > Person Company > Change image and Save : FAIL-->
            <style>
                .mystyle.ui-datatable .ui-datatable-scrollable-body{
                    overflow-x: hidden !important;
                }
               .division-overflow {
                    text-overflow: ellipsis;
                    overflow : hidden;
                    white-space: nowrap;
                }

               .division-overflow:hover {
                    text-overflow: clip;
                    white-space: normal;
                    word-break: break-all;
                }
                /*7648: CRM-5509  Per Sarah Cozzens: Please add third option to Online Instance Help Button*/
                /*10981 ESCALATIONS CRM-6827  */
                .ui-button.ui-widget.ui-state-default.ui-corner-all.ui-button-text-icon-left {
                    height: 25px !important; font-size: 13px; background-color: #3c8dbc ! important;color: #fff !important

                }
                /*<!--         Bug #7860 CSS issue for Help button in Planner, Contact/company details page -->*/
                #helpFrm\:hlp_button,#helpFrm\:crt_button {

                    font-size: 14px;

                }
                /*10981 ESCALATIONS CRM-6827  */
                #compDetailsButtonForm\:btnDashboard_button {
                    width:100px !important; height:23px !important; font-size: 12px !important; margin-top: -5px !important;
                }
                #compDetailsButtonForm\:btnDashboardManuSummary_button {
                    width:100px !important; height:23px !important; font-size: 12px !important; margin-top: -5px !important;
                }
                .imageUpload .ui-fileupload-buttonbar .ui-fileupload-choose .ui-button-text {
                    padding-left: 1em !important;
                    font-weight: normal;
                    font-size: 12px;
                }
                .cntr{
                    text-align:center;
                    padding: 9px;
                }
                .content {
                    margin:10px!important;
                } 
                #contactFields {
                    position:relative;
                    left:-7px;
                }
                #compSummary table tbody tr td label
                {
                    padding-left:5px!important;   
                }
                #contactFields input[type="text"]
                {
                    margin-left:3% !important;   
                }
                /*            <!--#9565  Companies: UI Design Issue-->*/
                .ui-button.btn{
                    margin-bottom: 5px !important;
                    min-width: 70px !important;
                }


            </style>

            <script>
                /*
                 * Function to convert to title case as the user types the company name in the company view page
                 * Example: Test dba company
                 */
                function convertTitleCase() {
                    var comp_name = document.getElementById('compDetailsButtonForm:tvComp:itCompName');
                    var currVal = $(comp_name).val().trim();
                    if (currVal === "") {
                        $(comp_name).val("");
                    }
                    //#352062 - Comp name java script issue with renaming: Saving the cursor position
                    var curPos = comp_name.selectionStart;
                    $(comp_name).val(currVal.charAt(0).toUpperCase() + currVal.slice(1));
                    comp_name.selectionStart = comp_name.selectionEnd = curPos;
                }

//01-04-2022 4770 CRM-4378: FW: RF - Poklar
                var oldFormData, newFormData;

//                01-04-2022 4770 CRM-4378: FW: RF - Poklar
                window.onload = function () {
                    //                PMS 3459: Unified Company Contact - UI-Creation-Update
                    if (#{companyService.unified}) {
                        oldFormData = loadAllPersonValues();
                    } else {
                        oldFormData = loadCompValues();
                    }
                };

//01-04-2022 4770 CRM-4378: FW: RF - Poklar
                //normal company
                function loadCompValues() {
                    //Basic information
                    var oldval = $('#compDetailsButtonForm\\:tvComp\\:itCompName').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:street').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:phone1').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:phone2').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:pobox').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:city').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:fax').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:state').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:website').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:zipcode').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:oneMenuRegion_input').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:iTCompCountryCode').val();

//                                Details
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:oneMenuCompType_input').val();
//                    17-07-2023 11674 ESCALATIONS CRM-7160   Companies: save button gets disabled after changing sales team
                    if (#{companyService.tagged or companyService.companies.compType == 4 }) {
                        oldval = oldval + PF('alias').input.is(":checked");
                    }

                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:oneMenuCompSalesman1_input').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:privTeam_input').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:frequency_input').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:inputCompName').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:pattern_input').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:printName').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:category_input').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:className_input').val();
                    oldval = oldval + PF('activeRadioFlag').getJQ().find(":checked").val();
                    oldval = oldval + PF('activeHolidayFlag').getJQ().find(":checked").val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:iTxtAreaTags').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:iTxtAreacompShipDetails').val();
                    oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:companyCmnts').val();


                    if (#{companyService.companies.compType} === 1) {
                        oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:quotesTandC').val();
                        oldval = oldval + $('#compDetailsButtonForm\\:tvComp\\:pofo').val();
                    }

                    PF('prodpotcheckbox').inputs.filter(":checked").each(function () {
                        var valu = $(this).val();
                        var newVal = valu.replace(/\"/g, '\\"');
                        newVal = newVal.replace(/\'/g, "\\'");
                        oldval = oldval + newVal;

                    });
                    PF('menu').inputs.filter(":checked").each(function () {
                        var valu = $(this).val();
                        var newVal = valu.replace(/\"/g, "\\\"");
                        newVal = newVal.replace(/\'/g, "\\'");
                        oldval = oldval + newVal;
                    });

                    return oldval;

                }
                //person company
                //            PMS 3459: Unified Company Contact - UI-Creation-Update
//01-04-2022 4770 CRM-4378: FW: RF - Poklar               
                function loadAllPersonValues() {
                    var oldvalperson = $('#compDetailsButtonForm\\:tvComp\\:contTitle').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:itFName').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:itLName').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:itJobTitle').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:itDepartment').val();

//                    contact Details
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:itMobile').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:fax').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:phone1').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:phone2').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:itBusiEmail').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:itBusiEmail2').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:itAlterEmail').val();
                    oldvalperson = oldvalperson + PF('donotemail').input.is(":checked");
                    oldvalperson = oldvalperson + PF('globalvisib').input.is(":checked");
                    oldvalperson = oldvalperson + PF('syncenab').input.is(":checked");
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:street').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:pobox').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:city').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:state').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:zipcode').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:iTCompCountryCode').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:itManager').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:itAssistant').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:itRefferedBy').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:itcontext').val();

//                    Compaye Details
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:oneMenuCompType_input').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:oneMenuCompSalesman1_input').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:privTeam_input').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:frequency_input').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:oneMenuRegion_input').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:website').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:pattern_input').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:printName').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:category_input').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:className_input').val();
                    oldvalperson = oldvalperson + PF('activeRadioFlag').getJQ().find(":checked").val();
                    oldvalperson = oldvalperson + PF('activeHolidayFlag').getJQ().find(":checked").val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:iTxtAreaTags').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:iTxtAreacompShipDetails').val();

                    //persnoal details
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:detailsCont\\:itPStreet').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:detailsCont\\:itPpoBox').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:detailsCont\\:itPCity').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:detailsCont\\:itPState').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:detailsCont\\:itPZipCode').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:detailsCont\\:itPCntryCode').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:detailsCont\\:itPPhn').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:detailsCont\\:itPEmail').val();

                    //websites
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:detailsCont\\:itWebsite').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:detailsCont\\:iTFacebook').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:detailsCont\\:iTTwitter').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:detailsCont\\:iTLinkedIn').val();
                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:detailsCont\\:iTGooglePlus').val();

                    PF('contgrup').inputs.filter(":checked").each(function () {
                        var contval = $(this).val();
                        contval = contval.replace(/\"/g, '\\"');
                        contval = contval.replace(/\'/g, "\\'");
                        oldvalperson = oldvalperson + contval;

                    });
                    PF('wvMenuProdPot').inputs.filter(":checked").each(function () {
                        var val = $(this).val();
                        val = val.replace(/\"/g, '\\"');
                        val = val.replace(/\'/g, "\\'");
                        oldvalperson = oldvalperson + val;
                    });

                    PF('prodinterest').inputs.filter(":checked").each(function () {
                        var prodval = $(this).val();
                        prodval = prodval.replace(/\"/g, '\\"');
                        prodval = prodval.replace(/\'/g, "\\'");
                        oldvalperson = oldvalperson + prodval;

                    });
                    PF('wvMenuIndustries').inputs.filter(":checked").each(function () {
                        var val = $(this).val();
                        val = val.replace(/\"/g, '\\"');
                        val = val.replace(/\'/g, "\\'");
                        oldvalperson = oldvalperson + val;
                    });


                    oldvalperson = oldvalperson + $('#compDetailsButtonForm\\:tvComp\\:contDetailaccordian\\:itNote').val();

                    //                oldvalperson = oldvalperson + PF('split').input.is(":checked");
                    //                oldvalperson = oldvalperson + PF('alias').input.is(":checked");
                    //                oldvalperson = oldvalperson + PF('forecastCheckbox').input.is(":checked");

                    return oldvalperson;
                }

                //PMS 3459: Unified Company Contact - UI-Creation-Update
//01-04-2022 4770 CRM-4378: FW: RF - Poklar  
//22-06-2022 8297 ESCALATIONS CRM-5827  Updating company accounts error message             
//                function loadOldonSave() {
//                    //                PMS 3459: Unified Company Contact - UI-Creation-Update
////        01-04-2022 4770 CRM-4378: FW: RF - Poklar           
//                    var ihUpdated = document.getElementById('compDetailsButtonForm:ihCompUpdated');
//                    console.log('unified ==> '+#{companyService.unified});
//                    console.log('updated value from inputhidden  ==> '+ihUpdated.value);
//                    console.log('updated value from bean  ==> '+);
//                    if (ihUpdated.value === '1') {
//                        if (#{companyService.unified}) {
//                            oldFormData = loadAllPersonValues();
//                            console.log("unified");
//                        } else {
//                            oldFormData = loadCompValues();
//                            console.log("not unified");
//                        }
//                        ihUpdated.value = '0';
//                    }
//                }

//01-04-2022 4770 CRM-4378: FW: RF - Poklar
                function discardChanges() {
                    if (#{companyService.unified}) {
                        oldFormData = loadAllPersonValues();
                    } else {
                        oldFormData = loadCompValues();
                    }
                }

//01-04-2022 4770 CRM-4378: FW: RF - Poklar
                $(window).on('beforeunload', function () {
                    if (#{companyService.unified}) {
                        newFormData = loadAllPersonValues();
                    } else {
                        newFormData = loadCompValues();
                    }
                    if (newFormData !== oldFormData) {
                        return "Changes have not been saved";
                    }
                });

                (function () {
                    convertToGoogleMapLink();
                })();
                function convertToGoogleMapLink() {

                    $('.address').each(function () {
                        //Feature #1662:CRM-3286: Strange URL Unsecured
                        var link = "<a href='https://maps.google.com/maps?q=" + encodeURIComponent($(this).text()) + "'>" + $(this).text() + "</a>";
                    });
                }

                $(document).ready(function () {
                    var url = window.location.protocol + "//" + window.location.host;
                    
                    $(".page_url").val(url);
                    $('#compDetailsButtonForm\\:tvComp\\:itCompName').focus();
                });

                function pageRefresh() {
//                    01-04-2022 4770 CRM-4378: FW: RF - Poklar
                    discardChanges();
                    location.reload();
                }
            </script>

            <!--        #2537 Map in Company Details -->
            <!--PMS 2873 Code Optimization - Company Details screen-->        
            <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDjuR7QEgLzgl9p8IpkFSmls50Tq-7sFrI"></script>
            <ui:include src="dlg/MapDialog.xhtml"/>
            <ui:include src="PitchDlg.xhtml"/>
            <!--5225 Company > Pitch > New > UI issue-->
            <ui:include src="PitchDlgNew.xhtml"/>
            <ui:include src="CompMapFilterDlg.xhtml"/>
            <ui:include src="RelatedCompsData.xhtml"/>
            <ui:include src="AddRelatedCompDlg.xhtml"/>
            <ui:include src="/lookup/ContactLookupDlg.xhtml"/>
            <ui:include src="../../Journal/JournalActivityExport.xhtml"/>
            <!--Pms Task 6838:Pitch Selector: Contacts > Send Email-->
            <ui:include src="../../lookup/PitchLookup.xhtml"/>


            <!--      Feature #4457:Contacts > Send Bulk Email  8/06/21 by harshithad-->
            <ui:include src="/opploop/contacts/dlg/SendEmailDlg.xhtml"/> 
            <!--       Feature #4855: Customer Pricing: Price Group   15/06/21 by harshithad-->
            <ui:include src="/opploop/companies/dlg/PriceGroupsDlg.xhtml"/>
            <!--#9468  Divisions: Company Details > Divisions button-->
            <ui:include src="/opploop/companies/dlg/compDivisionsDlg.xhtml"/>
            <ui:include src="/opploop/companies/dlg/AddPriceGroupDlg.xhtml"/>       
            <ui:include src="dlg/PersonCompLookUp.xhtml" />
            <!--Feature #11258: CRM-6986: Commission Split two lines of exceptions for the same co /related co but for two diff mfgs  by harshithad on 23/06/23-->
             <ui:include src="dlg/AddPrincipalCommException.xhtml" />

            <!--#6270: Company: Attachments tab > Preview-->
        </f:view>
    </ui:define>  


</ui:composition>