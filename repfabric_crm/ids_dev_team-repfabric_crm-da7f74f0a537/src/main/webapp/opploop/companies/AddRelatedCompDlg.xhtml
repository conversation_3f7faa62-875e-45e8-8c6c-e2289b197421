<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: AddRelatedCompDlg.xhtml
// Author: Sarayu
//*********************************************************
* Copyright (c) 2016 Indea Design Systems, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <!--Task#3409:Rathsburg: Related Company Exception   :16-02-2021 by harshithad-->
    <p:dialog id="dlgReltcomp" class="dialogCSS" widgetVar="reltdCompAddDlg"  header="#{relatedComps.recId== 0 ?'Add':'Edit'} Related Company" modal="true" resizable="false" responsive="true" width="500px"> 
        <h:form>
            <!--            Task#3409:Rathsburg: Related Company Exception   :16-02-2021 by harshithad-->
            <!--Feature #11258: CRM-6986: Commission Split two lines of exceptions for the same co /related co but for two diff mfgs  by harshithad on 23/06/23-->
            <p:remoteCommand id="rmtCmdComp" immediate="true" name="applyRelatedComp" actionListener="#{relatedComps.applyCompany(viewCompLookupService.selectedCompany)}" update=":addReltdCompForm:inputCompName :addReltdCompForm:inputCompId" />
        
        </h:form>
        <h:form id = "addReltdCompForm">
            <!--          Task#3409:Rathsburg: Related Company Exception   :16-02-2021 by harshithad-->
            <p:panelGrid id="pnl" columns="2" columnClasses="ui-grid-col-5,ui-grid-col-9" styleClass="box-primary no-border ui-fluid"
                         layout="grid" >
                <p:outputLabel for="inputCompName" value="Company" styleClass="required" id="oLblRealtedCompName" />
                <h:panelGroup id="pnlgrpComp" class="ui-inputgroup"  >
                    <p:inputText id="inputCompName" value="#{relatedComps.company.compName}" readonly="true"  />
                    <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                      id="btnRelatedComp"
                                      action="#{viewCompLookupService.listForRelated(companyServiceImpl.comp.compId,'applyRelatedComp')}"
                                      update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                      styleClass="btn-info btn-xs" />
                    <h:inputHidden id="inputCompId" value="#{relatedComps.reltCompId}"  />
                </h:panelGroup>

                <p:outputLabel for="oneMenuCatg" value="Category" styleClass="required" id="oLblReltCompCategry" style="max-width: 100px"/>
                <p:selectOneMenu id="oneMenuCatg" value="#{relatedComps.reltCategory}" styleClass="required" >      
                    <f:selectItem itemLabel="Select Category" itemValue="0"/>
                    <f:selectItems var="reln" value="#{relatedComps.relationCatgList}"  itemLabel="#{reln.reltCategoryName}" itemValue="#{reln.reltCategory}"  />
                    <!--                 Task#3409:Rathsburg: Related Company Exception   :16-02-2021 by harshithad-->
                    <p:ajax event="itemSelect"  process="@this" 
                            update="addReltdCompForm:pnl"/>
                </p:selectOneMenu> 
                <!--          Task#3409-Rathsburg: Related Company Exception   :16-02-2021 by harshithad-->
                <p:outputLabel value="Related Company Role" id="oLblReltCompRole"/>
                <p:selectOneMenu widgetVar="roleType"  value="#{relatedComps.reltRole}" id="oneMenuRole" >      
                    <f:selectItem itemLabel="#{custom.labels.get('IDS_SPECIFIER')}" itemValue="0"/>
                    <f:selectItem itemLabel="#{custom.labels.get('IDS_PURCHASER')}" itemValue="1"/>
                    <p:ajax event="itemSelect"  process="@this" 
                            update="addReltdCompForm:pnl"/>
                </p:selectOneMenu> 
                <!--             Task#3409:Rathsburg: Related Company Exception   :16-02-2021 by harshithad-->
                <!--Feature #11258: CRM-6986: Commission Split two lines of exceptions for the same co /related co but for two diff mfgs  by harshithad on 23/06/23-->
                <!--                <p:outputLabel id ="otptLblSplitType" value="Split Type"  rendered="#{relatedComps.reltCategory==1 and loginBean.loggedInUser.userCategory==1}"/>
                                <p:selectOneRadio id="oneRadioSplit"    value="#{relatedComps.reltCustomSplitFlag}" onchange="#{relatedComps.onChangeCloseStatus(relatedComps.reltCustomSplitFlag)}"
                                                  requiredMessage="Closing status not selected" 
                                                  required="true" rendered="#{relatedComps.reltCategory==1 and loginBean.loggedInUser.userCategory==1}">
                                    <f:selectItem   itemValue="0" itemLabel="Default"/> 
                                    <f:selectItem itemValue="1" itemLabel="Custom"/>
                
                                    <p:ajax event="change" process="@this" 
                                            update="addReltdCompForm:pnl"/>
                
                
                                </p:selectOneRadio>
                
                
                                <p:outputLabel id="otptLblSpecifierSplit" value="Specifier Split %" rendered="#{relatedComps.reltCustomSplitFlag==1 and relatedComps.reltCategory==1 and loginBean.loggedInUser.userCategory==1}" />
                                <p:inputText id="inptTxtSplitPerct" rendered="#{relatedComps.reltCustomSplitFlag==1 and relatedComps.reltCategory==1 and loginBean.loggedInUser.userCategory==1}" value="#{relatedComps.reltCustomSplit}" placeholder="0.00">
                                    <p:ajax event="blur"  process="@this"/> 
                                </p:inputText>
                                <p:outputLabel id="otptLblPrinciName" for="inputPrincName" value="#{custom.labels.get('IDS_PRINCI')}" rendered="#{relatedComps.reltCustomSplitFlag==1 and relatedComps.reltCategory==1 and loginBean.loggedInUser.userCategory==1}" />
                                <h:panelGroup id="pnlGrpPrinci" class="ui-inputgroup" rendered="#{relatedComps.reltCustomSplitFlag==1 and relatedComps.reltCategory==1 and loginBean.loggedInUser.userCategory==1}" >
                
                                    <p:inputText id="inputPrincName" value="#{relatedComps.reltPrinciName}"  placeholder="All" readonly="true"/>
                                    <p:commandButton id="cmdBtnPrinci" icon="fa fa-search" title="Choose #{custom.labels.get('IDS_PRINCI')}" immediate="true" 
                                                     actionListener="#{viewCompLookupService.listActive('applyPrinci', 1, 0, 0)}"
                                                     update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                     styleClass="btn-info btn-xs" />
                Feature #11258: CRM-6986: Commission Split two lines of exceptions for the same co /related co but for two diff mfgs  by harshithad on 23/06/23
                                </h:panelGroup>-->
            </p:panelGrid>
            <p:spacer width="4px" />
            <!--            Task#4349:PO Bulk Reconciliation  12/05/21 by harshithad-->
            <div class="button_bar" align="center">
                <!--                Task#3600-Related Company Exception - Handle dashboard   :26-01-2021 by harshithad-->
                <!--#5194: Comp > Records are not added under Contacts tab instantly, need to reload page.-->                
                <!--Feature #11258: CRM-6986: Commission Split two lines of exceptions for the same co /related co but for two diff mfgs  by harshithad on 23/06/23-->
                <p:commandButton  id="save"  value="Save" styleClass="btn btn-success  btn-xs"  
                                  action="#{relatedComps.createOrUpdateRelatedCompanies()}"
                                  update="@(.required) :reltdCompForm :compDetailsButtonForm:tvComp:dtCompConList " oncomplete="PF('relatedCompTbl').clearFilters();"/>
                <p:spacer width="4px"/>
                <p:commandButton  value="Cancel"  onclick="PF('reltdCompAddDlg').hide()" 
                                  id="btnReltCompCancel"
                                  styleClass="btn btn-warning btn-xs"/>
            </div>
            <!--Feature #11258: CRM-6986: Commission Split two lines of exceptions for the same co /related co but for two diff mfgs  by harshithad on 23/06/23-->
            <p:outputPanel id="otptPnlCommSplit" rendered="#{relatedComps.reltCategory==1 and relatedComps.isUserOwner}">
                <h:outputText value="Exceptions:" style="font-weight: bold"/>
                <p:commandButton id="cmdBtnAddCommSplt" value="Add"  class="btn btn-primary btn-xs" actionListener="#{relatedComps.viewComSplitExceptionDialogue(null)}"  oncomplete="PF('dlgPrinciSplit').show()" update="frmComSplit" style="margin-left: 390px;bottom: 5px" />
                <p:dataTable id="dtCommExcptn" editable="true" editMode="cell" rowKey="#{splitExcp.recId}"  value="#{relatedComps.viewCompSplitExp}" var="splitExcp">
                    <p:column headerText="#{custom.labels.get('IDS_PRINCI')}"> #{splitExcp.princiName}</p:column>
                    <p:column headerText="#{custom.labels.get('IDS_SPECIFIER')} Split %"> 
                        <p:cellEditor>
                            <f:facet name="output">
                                <!--Bug #9884: Direct commission :PO& commissionable transactions> decimal point issue by harshithad on 30/11/22-->
                                <p:outputLabel value="#{splitExcp.specifierSplit}">  
                                    <f:convertNumber groupingUsed="true" minFractionDigits="2" />
                                </p:outputLabel>
                            </f:facet>
                            <f:facet name="input">
                                <p:inputNumber  value="#{splitExcp.specifierSplit}" maxlength="5" decimalPlaces="2"  maxValue="100">        
                                    <p:ajax  event="change" process="@this" listener="#{relatedComps.changeSpecifierSplit(splitExcp)}"/>
                                </p:inputNumber>
                            </f:facet>
                        </p:cellEditor>
                    </p:column>
                    <p:column style="width:80px">
                        <div style="text-align: center">
                            <p:commandButton styleClass="btn-primary btn-xs btn-icon-group btn-icon"  actionListener="#{relatedComps.viewComSplitExceptionDialogue(splitExcp)}"
                                             oncomplete="PF('dlgPrinciSplit').show()" update="frmComSplit"
                                             icon="fa fa-pencil"
                                             />
                            <p:spacer width="4px"/>                           
                            <p:commandButton styleClass="btn-danger btn-xs btn-icon"                                              
                                             icon="fa fa-trash" actionListener="#{relatedComps.viewComSplitExceptionDialogue(splitExcp)}" oncomplete="PF('cnfrmDel').show();">                                                                           
                            </p:commandButton>
                        </div>
                    </p:column>
                </p:dataTable>
            </p:outputPanel>

        </h:form>
    </p:dialog>

    <!--Feature #11258: CRM-6986: Commission Split two lines of exceptions for the same co /related co but for two diff mfgs  by harshithad on 23/06/23-->
    <p:confirmDialog global="true" showEffect="fade" header="Confirmation" message="Are you sure to delete?" widgetVar="cnfrmDel" responsive="true" >
        <h:form id="formDeleteConfirm">
            <div class="button_bar" align="center">
                <p:commandButton value="Yes" actionListener="#{relatedComps.deleteComssSplitExcptn()}" oncomplete="PF('cnfrmDel').hide();"   styleClass="ui-confirmdialog-yes btn btn-success btn-xs"  id="btnCompSplitYes" />
                <p:spacer width="4px" />
                <p:commandButton value="No" type="button" onclick="PF('cnfrmDel').hide();" styleClass="ui-confirmdialog-no btn btn-danger btn-xs"  id="btnCompSplitlNo" />
            </div>
        </h:form> 
    </p:confirmDialog>


    <p:confirmDialog header="Confirmation" message="There are exceptions defined for this related company. Changing the category will delete the exceptions. Do you want to proceed?" 
                     id="confirmShp" showEffect="fade" hideEffect="fade" global="true" widgetVar="confrmChangCat">  

        <h:form>
            <div class="div-center">
                <p:commandButton id="cmdBtnYs" value="Yes" action="#{relatedComps.deleteAllCommExceptions()}"   oncomplete="PF('confrmChangCat').hide();"  styleClass="btn btn-xs btn-success"
                                 />
                <p:spacer width="4"/>
                <p:commandButton id="cmdBtnNo" value="No" actionListener="#{relatedComps.setOldCategoryValue()}" oncomplete="PF('confrmChangCat').hide();"  styleClass="btn btn-xs btn-danger"/>
            </div>
        </h:form>
    </p:confirmDialog>
</ui:composition>
