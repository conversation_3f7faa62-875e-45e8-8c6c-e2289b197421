<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:h="http://xmlns.jcp.org/jsf/html"
                 xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                 xmlns:f="http://xmlns.jcp.org/jsf/core"
                 xmlns:p="http://primefaces.org/ui">
    <p:dialog id="callPatFltr" header="#{custom.labels.get('IDS_CALL_PATTERN')}" resizable="false" modal="true"  responsive="true" widgetVar="callPatFltr" width="800px" height="500">
            <h:form id="callPatForm">
 <!--2873 Code Optimization - Company Details screen    -->               
                <p:commandButton value="Add" disabled="#{companyService.selfilterCallPattern.size()==0}" style="margin-bottom: 4px" action="#{companyService.onaddCal()}"  update=":filterdlgForm :filterdlgForm:callPat"  oncomplete=" PF('callPatFltr').hide();  "  styleClass="btn btn-primary btn-xs"/>
                <p:dataTable class="dtable" rowSelectMode="add" id="callPatTable" value="#{callPatternsMstService.callPattLst}" rowKey="#{callPat.recId}" selection="#{companyService.selfilterCallPattern}" var="callPat" widgetVar="callPatTable">
                   

                    <p:column  selectionMode="multiple"   style="width: 40px;" />
<!--2873 Code Optimization - Company Details screen    -->                    
                    <p:ajax event="rowSelect"  listener="#{companyService.inviteRendr1}" update=":callPatForm" /> 
                    <p:ajax event="rowUnselect"   listener="#{companyService.inviteRendr1}" update=":callPatForm"/> 
                    <p:ajax event="rowSelectCheckbox"  listener="#{companyService.inviteRendr1}" update=":callPatForm" /> 
                    <p:ajax event="rowUnselectCheckbox"   listener="#{companyService.inviteRendr1}" update=":callPatForm"/>
                    <p:ajax event="toggleSelect"   listener="#{companyService.inviteRendr1}" update=":callPatForm"/> 
                    <p:column  filterBy="#{callPat.callPattName}" filterMatchMode="contains" sortBy="#{callPat.callPattName}"  headerText="Name"   >
                            #{callPat.callPattName}
                    </p:column>
                </p:dataTable>
            </h:form> 
    
        </p:dialog>
    
        <p:dialog id="SalesTeamtFltr" header="#{custom.labels.get('IDS_SALES_TEAM')}" resizable="false" modal="true"  responsive="true" widgetVar="SalesTeamtFltr" width="800px" height="500" >
            <h:form id="salesTeamtFltrForm">
<!--2873 Code Optimization - Company Details screen    -->                
                <p:commandButton value="Add" disabled="#{companyService.selfilterSalesTeam.size()==0}" style="margin-bottom: 4px" action="#{companyService.onaddsalesTeam()}"  update=":filterdlgForm:filterPanel :filterdlgForm:salesTeam" oncomplete=" PF('SalesTeamtFltr').hide();  "  styleClass="btn btn-primary btn-xs"/>
                <p:dataTable class="dtable" rowSelectMode="add" id="SalesTeamTable" value="#{salesTeamService.lstSalesTeam}" rowKey="#{salesTeam.smanId}" selection="#{companyService.selfilterSalesTeam}" var="salesTeam" widgetVar="SalesTeamTable">
                   

                    <p:column  selectionMode="multiple"   style="width: 40px;" />
                    <p:ajax event="rowSelect"  listener="#{companyService.inviteRendr1}" update=":salesTeamtFltrForm" /> 
                    <p:ajax event="rowUnselect"   listener="#{companyService.inviteRendr1}" update=":salesTeamtFltrForm"/> 
                    <p:ajax event="rowSelectCheckbox"  listener="#{companyService.inviteRendr1}" update=":salesTeamtFltrForm" /> 
                    <p:ajax event="rowUnselectCheckbox"   listener="#{companyService.inviteRendr1}" update=":salesTeamtFltrForm"/> 
                    <p:ajax event="toggleSelect"   listener="#{companyService.inviteRendr1}" update=":salesTeamtFltrForm"/> 
                    <p:column  filterBy="#{salesTeam.smanName}" filterMatchMode="contains" sortBy="#{salesTeam.smanName}"  headerText="Name"   >
                            #{salesTeam.smanName}
                    </p:column>
                </p:dataTable>
            </h:form> 

        </p:dialog>

        <p:dialog id="compTypeFltr"  header="#{custom.labels.get('IDS_COMP_TYPE')}" resizable="false" modal="true"  responsive="true" widgetVar="compTypeFltr" width="800px" height="500" >
            <h:form id="compTypeFltrForm">
<!--2873 Code Optimization - Company Details screen    -->                
                <p:commandButton value="Add" disabled="#{companyService.selfilterCompType.size()==0}" style="margin-bottom: 4px" action="#{companyService.onaddcomptype()}" update=":filterdlgForm  :filterdlgForm:compType"  oncomplete=" PF('compTypeFltr').hide();  "  styleClass="btn btn-primary btn-xs"/>
                <p:dataTable class="dtable" rowSelectMode="add" id="compTypeTable" value="#{companyTypesMstService.compTypeLst}" rowKey="#{compType.compTypeId}" selection="#{companyService.selfilterCompType}" var="compType" widgetVar="compTypeTable">

<!--2873 Code Optimization - Company Details screen    -->
                    <p:column  selectionMode="multiple"   style="width: 40px;" />
                    <p:ajax event="rowSelect"  listener="#{companyService.inviteRendr1}" update=":compTypeFltrForm" /> 
                    <p:ajax event="rowUnselect"   listener="#{companyService.inviteRendr1}" update=":compTypeFltrForm"/> 
                    <p:ajax event="rowSelectCheckbox"  listener="#{companyService.inviteRendr1}" update=":compTypeFltrForm" /> 
                    <p:ajax event="rowUnselectCheckbox"   listener="#{companyService.inviteRendr1}" update=":compTypeFltrForm"/>
                    <p:ajax event="toggleSelect"   listener="#{companyService.inviteRendr1}" update=":compTypeFltrForm"/> 
                    <p:column  filterBy="#{compType.compTypeName}" filterMatchMode="contains" sortBy="#{compType.compTypeName}"  headerText="Name"   >
                            #{compType.compTypeName}
                    </p:column>
                </p:dataTable>
            </h:form> 

        </p:dialog>

        <p:dialog id="regionFltr" header="#{custom.labels.get('IDS_REGION')}" resizable="false" modal="true"  responsive="true" widgetVar="regionFltr" width="800px" height="500" >
            <h:form id="regionFltrForm">
 <!--2873 Code Optimization - Company Details screen    -->               
                <p:commandButton value="Add" disabled="#{companyService.selfilterRegion.size()==0}" style="margin-bottom: 4px" action="#{companyService.onaddregion()}" update=":filterdlgForm  :filterdlgForm:region"  oncomplete=" PF('regionFltr').hide();  "  styleClass="btn btn-primary btn-xs"/>
                <p:dataTable class="dtable" rowSelectMode="add" id="regionTable" value="#{companyRegionsMstService.compRegionsLst}" rowKey="#{region.recId}" selection="#{companyService.selfilterRegion}" var="region" widgetVar="regionTable">

<!--2873 Code Optimization - Company Details screen    -->
                    <p:column  selectionMode="multiple"   style="width: 40px;" />
                    <p:ajax event="rowSelect"  listener="#{companyService.inviteRendr1}" update=":regionFltrForm" /> 
                    <p:ajax event="rowUnselect"   listener="#{companyService.inviteRendr1}" update=":regionFltrForm"/> 
                    <p:ajax event="rowSelectCheckbox"  listener="#{companyService.inviteRendr1}" update=":regionFltrForm" /> 
                    <p:ajax event="rowUnselectCheckbox"   listener="#{companyService.inviteRendr1}" update=":regionFltrForm"/> 
                    <p:ajax event="toggleSelect"   listener="#{companyService.inviteRendr1}" update=":regionFltrForm"/> 
                    <p:column  filterBy="#{region.compRegion}" filterMatchMode="contains" sortBy="#{region.compRegion}"  headerText="Name"   >
                            #{region.compRegion}
                    </p:column>
                </p:dataTable>
            </h:form> 

        </p:dialog>

        <p:dialog id="prodPotFltr" header="#{custom.labels.get('IDS_POTENTIALS')}" resizable="false" modal="true"  responsive="true" widgetVar="prodPotFltr" width="800px" height="500" >
            <h:form id="prodPotFltrForm">
<!--2873 Code Optimization - Company Details screen    -->                
                <p:commandButton value="Add" disabled="#{companyService.selfilterProdPot.size()==0}" style="margin-bottom: 4px" action="#{companyService.onaddprod()}" update=":filterdlgForm :filterdlgForm:prodPot"  oncomplete=" PF('prodPotFltr').hide();  "  styleClass="btn btn-primary btn-xs"/>
                <p:dataTable class="dtable" rowSelectMode="add" id="prodPotTable" value="#{prodPotentialsMstService.prodPotentialsLst}" rowKey="#{prodPot.recId}" selection="#{companyService.selfilterProdPot}" var="prodPot" widgetVar="prodPotTable">


                    <p:column  selectionMode="multiple"   style="width: 40px;" />
                    <p:ajax event="rowSelect"  listener="#{companyService.inviteRendr1}" update=":prodPotFltrForm" /> 
                    <p:ajax event="rowUnselect"   listener="#{companyService.inviteRendr1}" update=":prodPotFltrForm"/> 
                    <p:ajax event="rowSelectCheckbox"  listener="#{companyService.inviteRendr1}" update=":prodPotFltrForm" /> 
                    <p:ajax event="rowUnselectCheckbox"   listener="#{companyService.inviteRendr1}" update=":prodPotFltrForm"/> 
                    <p:ajax event="toggleSelect"   listener="#{companyService.inviteRendr1}" update=":prodPotFltrForm"/> 
                    <p:column  filterBy="#{prodPot.prodPoteName}" filterMatchMode="contains" sortBy="#{prodPot.prodPoteName}"  headerText="Name"   >
                            #{prodPot.prodPoteName}
                    </p:column>
                </p:dataTable>
            </h:form> 

        </p:dialog>

        <p:dialog id="catFltr" header="#{custom.labels.get('IDS_CATEGORY')}" resizable="false"  modal="true"  responsive="true" widgetVar="catFltr" width="800px" height="500" >
            <h:form id="catFltrForm">
<!--2873 Code Optimization - Company Details screen    -->                
                <p:commandButton value="Add" disabled="#{companyService.selfilterCategory.size()==0}" style="margin-bottom: 4px" immediate="true" action="#{companyService.onaddcat()}" update=":filterdlgForm :filterdlgForm:cat"  oncomplete=" PF('catFltr').hide();  "  styleClass="btn btn-primary btn-xs"/>
                <p:dataTable class="dtable" rowSelectMode="add" id="catTable" value="#{companyCategoryMstService.compCatLst}" rowKey="#{cat.recId}" selection="#{companyService.selfilterCategory}" var="cat" widgetVar="catTable">

<!--2873 Code Optimization - Company Details screen    -->
                    <p:column  selectionMode="multiple"   style="width: 40px;" />
                    <p:ajax event="rowSelect"  listener="#{companyService.inviteRendr1}" update=":catFltrForm" /> 
                    <p:ajax event="rowUnselect"   listener="#{companyService.inviteRendr1}" update=":catFltrForm"/> 
                    <p:ajax event="rowSelectCheckbox"  listener="#{companyService.inviteRendr1}" update=":catFltrForm" /> 
                    <p:ajax event="rowUnselectCheckbox"   listener="#{companyService.inviteRendr1}" update=":catFltrForm"/> 
                    <p:ajax event="toggleSelect"   listener="#{companyService.inviteRendr1}" update=":catFltrForm"/> 
                    <p:column  filterBy="#{cat.compCatgName}" filterMatchMode="contains" sortBy="#{cat.compCatgName}"  headerText="Name"   >
                            #{cat.compCatgName}
                    </p:column>
                </p:dataTable>
            </h:form> 

        </p:dialog>
<!--    3136 CRM-1981:Companies : kellerindustrial: Add filter for "Class" to map filters online-->
            <p:dialog id="dlgClass" header="#{custom.labels.get('IDS_CLASS')}" resizable="false"  modal="true"  responsive="true" widgetVar="classFilter" width="800px" height="500" >
            <h:form id="classForm">
<!--2873 Code Optimization - Company Details screen    -->                
                <p:commandButton value="Add" disabled="#{companyService.selfilterClass.size()==0}" style="margin-bottom: 4px" immediate="true" action="#{companyService.onaddclass()}"   oncomplete=" PF('classFilter').hide(); " update=":filterdlgForm :filterdlgForm:class "  styleClass="btn btn-primary btn-xs"/>
                <p:dataTable class="dtable" rowSelectMode="add" id="classTable" value="#{compClassMstService.compClassLst}" rowKey="#{cls.recId}" selection="#{companyService.selfilterClass}" var="cls" widgetVar="classTable">


                    <p:column  selectionMode="multiple"   style="width: 40px;" />
<!--2873 Code Optimization - Company Details screen    -->                    
                    <p:ajax event="rowSelect"  listener="#{companyService.inviteRendr1}" update=":classForm" /> 
                    <p:ajax event="rowUnselect"   listener="#{companyService.inviteRendr1}" update=":classForm"/> 
                    <p:ajax event="rowSelectCheckbox"  listener="#{companyService.inviteRendr1}" update=":classForm" /> 
                    <p:ajax event="rowUnselectCheckbox"   listener="#{companyService.inviteRendr1}" update=":classForm"/> 
                    <p:ajax event="toggleSelect"   listener="#{companyService.inviteRendr1}" update=":classForm"/> 
                    <p:column  filterBy="#{cls.compClassName}" filterMatchMode="contains" sortBy="#{cls.compClassName}"  headerText="Name"   >
                            #{cls.compClassName}
                    </p:column>
                </p:dataTable>
            </h:form> 

        </p:dialog>
    
</ui:composition>

