<ui:composition  xmlns="http://www.w3.org/1999/xhtml" 
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:f="http://java.sun.com/jsf/core" 
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                 template="#{layoutMB.template}">
    <h:head>
    </h:head>


    <!--#358 Page Title sharvanim - 03-05-2020-->

    <ui:define name="head">
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>

        <title>#{custom.labels.get('IDS_JOB')} List </title>
    </ui:define>


    <ui:define name="meta">    


    </ui:define>

    <ui:define name="title">

        <ui:param name="title" value="Jobs List"/>
        <f:event listener="#{jobs.isJobsPageAccessible}" type="preRenderView"/>
        <div class="row">
            <div class="col-md-6">
                <f:metadata>
                    <f:viewAction action="#{jobs.fetchJobsList()}"/>
                    <f:viewAction action="#{jobs.loadJobsColumnsList()}"/>
                    <!--<f:viewAction action="#{helpService.prepareHelpLinks('JOBS_LIST')}"/>-->
                    <!--2520 - Related - CRM-1521: Tutorial button landing urls - Quotes, Samples, Opportunities, Jobs, Projects-->
                    <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('JOB_LIST'))}" />
                    <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
                    <f:viewAction action="#{helpService.setPageTag('JOB_LIST')}" />                     
                </f:metadata>
                #{custom.labels.get('IDS_JOB')} List   
            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>

    <ui:define name="top_menu">
        <ui:include src="/Opp_Menu.xhtml" />
    </ui:define>

    <ui:define name="PageHeader">
        <h1>#{custom.labels.get('IDS_JOB')} List</h1>                
        <!--    </ui:define>
        
            <ui:define name="leftpanel">               -->

    </ui:define>

    <ui:define name="slogan">

    </ui:define>

    <ui:define name="body">
        <div class="box box-info box-body" align="right">
            <h:form id="jobsForm">
                <!--861 CRM-2699: Jobs List view: changes-->
                <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                <!--<p:remoteCommand autoRun="true" update=":jobsForm:tblJobs"/>-->
                <p:growl id="jobGrowl"/>  
                <!--893 Jobs > Customizable Columns-->
                <p:blockUI  widgetVar="blockUI"  block="tblJobs" id="bui"  >
                    <h:outputText  value="Loading #{custom.labels.get('IDS_JOB')}..."/>
                </p:blockUI>
<!--selection="#{contactService.selectedCont}"-->
                <!--//#13180 ESCALATION CRM-7806 Thea Web - Slow-->
                <!-- 28-11-2020 #2543: CRM-3371: Use custom label for Jobs -->
                <!--//           #13430 ESCALATION CRM-7926 Jobs: Export to excel does not show the correct info-->
                <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                <p:dataTable  id="tblJobs" value="#{jobsLazyDataModel}"                               
                              lazy="true"
                              var="viewjobs" multiViewState="true"
                              rows="50"  
                              filteredValue="#{jobsLazyDataModel.filteredJobList}"
                              rowKey="#{viewjobs.recId}"
                              widgetVar="tblJob1"
                              filterEvent="keyup"
                              draggableColumns="true" scrollable="true" scrollHeight="2000"
                              emptyMessage="No #{custom.labels.get('IDS_JOBS')} available"
                              paginator="true" 
                              paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                              rowsPerPageTemplate="50,100,150,200" 
                              paginatorAlwaysVisible="true" 
                              resizableColumns="true" >


                   <!--<p:ajax event="page" listener="#{oppFilter.onPageChange}" />-->  
                    <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                    <p:ajax event="colReorder" listener="#{jobs.onColumnReorder}" />
                    <!--//           #13430 ESCALATION CRM-7926 Jobs: Export to excel does not show the correct info-->
                    <p:ajax   event="filter"  listener="#{jobsLazyDataModel.tableFlt(filters)}"  />

                    <f:facet name="header" >
                        <div style="display: inline-flex; text-align: left;float: left">
                            <h:panelGroup>
                                <h:link  outcome="JobsView.xhtml?id=0" title="Create New #{custom.labels.get('IDS_JOB')}" value="New"
                                         styleClass="btn btn-primary btn-xs">
                                </h:link>
                                                    <!--<p:commandButton id="opNewBtn"  value="New" ajax="false" process="none" title="Create new #{custom.labels.get('IDS_OPP')}"  action="/opploop/opportunity/OpportunityView.xhtml?faces-redirect=true"  styleClass="btn btn-primary btn-xs" />--> 
                            </h:panelGroup>
                            <p:spacer width="4"/>
                            <!--//15-03-2024 : #13260 : ESCALATIONS CRM-7838   OASIS: Ability to search jobs in the Job module by a quote's quote number tha-->
                            <!--//02-12-2014 : #15025 : CRM-8781 : jobs: terrible list filtering ui-->
                            <h:panelGroup style="width:130px">
                                <!--1720 CRM-3256: Jobs: UI issue (Southwest) - filter for "All jobs" not being kept-->
                                <p:selectOneMenu id="jobFilter1" value="#{jobsFilter.jobFilter}" widgetVar="jobFilter1w" class="ui-select-btn" >
                                    <!--28-11-2020 #2543: CRM-3371: Use Custom label for Jobs -->
                                    <f:selectItem itemLabel="My #{custom.labels.get('IDS_JOBS')}" itemValue="1"/> 
                                    <f:selectItem itemLabel="Show all" itemValue="0"/>
                                    <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                                    <p:ajax event="change"  process="@this" listener="#{jobs.fetchFilteredJobsList(jobsFilter.jobFilter,jobsFilter.jobOpenStatus)}"  
                                            update=":jobsForm:tblJobs" oncomplete="PF('tblJob1').filter();applyTopScrollbarJob()"/>
                                </p:selectOneMenu>
                            </h:panelGroup>
                            <p:spacer width="4"/>
                            <!--//15-03-2024 : #13260 : ESCALATIONS CRM-7838   OASIS: Ability to search jobs in the Job module by a quote's quote number tha-->
                            <h:panelGroup  style="width:90px">
                                <!--1720 CRM-3256: Jobs: UI issue (Southwest) - filter for "All jobs" not being kept-->
                                <p:selectOneMenu id="jobFilter" value="#{jobsFilter.jobOpenStatus}"  widgetVar="jobFilterw" class="ui-select-btn" >
                                    <f:selectItem itemLabel="Open" itemValue="1"/> 
                                    <f:selectItem itemLabel="Closed" itemValue="2"/>
                                    <f:selectItem itemLabel="All" itemValue="0"/>
                                    <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                                    <p:ajax event="change"  process="@this" listener="#{jobs.fetchFilteredJobsList(jobsFilter.jobFilter,jobsFilter.jobOpenStatus)}"  
                                            update=":jobsForm:tblJobs" oncomplete="PF('tblJob1').filter();applyTopScrollbarJob()"/>
                                </p:selectOneMenu>
                            </h:panelGroup> 
                            <!--//15-03-2024 : #13260 : ESCALATIONS CRM-7838   OASIS: Ability to search jobs in the Job module by a quote's quote number tha-->
                            <p:spacer width="10"/>
                            <div style="float:right; ">


                                <h:commandLink    title="Excel">
                                    <p:graphicImage name="/images/excel.png" width="24"/>
                                    <p:dataExporter  type="xls" target="tblJobs" fileName="Jobs_List" />
                                </h:commandLink>
                                <p:spacer width="4"/>
                                <h:commandLink  title="CSV">
                                    <p:graphicImage  name="/images/csv.png" width="24"/>
                                    <p:dataExporter    type="csv" target="tblJobs" fileName="Jobs_List" />
                                </h:commandLink>
                            </div>
                            <!--3437 Job Auto Download (cron job)-->
                            <p:spacer width="4"/>
                            <p:commandButton  value="Sync Exceptions" oncomplete="PF('dlgJobException').show(); PF('tblVarJbException').clearFilters();"  actionListener="#{crmsyncLog.exceptionListingForJobs()}"    styleClass="btn btn-primary btn-xs" update="" />
                        </div>
                        <!--                        <div style="display: inline-flex; text-align: right">
                                                    <p:spacer width="20"/>
                                                    <p:spacer width="20"/>
                                                    <p:spacer width="20"/>
                                                    <p:spacer width="20"/>
                                                </div>-->

                        <!--//01-04-2024 : #13440 : CRM-7927   Jobs: Add Bid Date as a Column to the Job List View-->
                        <div style="display: inline-flex; text-align: right;">


                            <!--                            <div style="float:right; ">
                                                            <h:commandLink title="Excel">
                                                                <p:graphicImage name="/images/excel.png" width="24"/>
                                                                <p:dataExporter type="xls" target="tblJobs" fileName="Jobs_List" />
                                                            </h:commandLink>
                                                            <p:spacer width="4"/>
                                                            <h:commandLink title="CSV">
                                                                <p:graphicImage name="/images/csv.png" width="24"/>
                                                                <p:dataExporter type="csv" target="tblJobs" fileName="Jobs_List" />
                                                            </h:commandLink>
                                                        </div>-->

                            <p:spacer width="4"/>
                            <h:panelGroup>
                                <!--//15-03-2024 : #13260 : ESCALATIONS CRM-7838   OASIS: Ability to search jobs in the Job module by a quote's quote number tha-->
                                <p:inputText id="globalFilter"  onkeyup="PF('tblJob1').filter()" style="width:90px" placeholder="Search fields">

                                </p:inputText>

                            </h:panelGroup>
                            <p:spacer width="4"/>
                            <h:panelGroup>
                                <p:commandButton value="Advanced Search" 
                                                 styleClass="btn btn-xs"
                                                 onclick="PF('advancedSearch').show()"
                                                 />
                            </h:panelGroup>
                            <p:spacer width="4"/>
                            <p:column>
                                <p:commandButton id="toggler" type="button" value="Columns" style="float: right" icon="fa fa-align-justify"/>
                                <p:columnToggler widgetVar="columnToggler" id="columnToggler" datasource="tblJobs" trigger="toggler">
                                    <p:ajax event="toggle" listener="#{jobs.onToggle}"/>
                                </p:columnToggler>
                            </p:column>
                            <p:spacer width="4"/>
                            <p:column>
                                <p:commandButton title="Save settings" 
                                                 icon="fa fa-save" oncomplete="applyTopScrollbarJob()"
                                                 id="btnclonesave"  update=":jobsForm:tblJobs"
                                                 action="#{jobs.saveSettings()}" 
                                                 class="btn-success btn-xs"  />
                            </p:column>
                            <p:spacer width="4"/>
                            <p:column>
                                <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                                <p:commandButton title="Clear Filters" process="@this"
                                                 icon="fa fa-times-circle"
                                                 id="btnfilterclear"
                                                 action="#{jobs.fetchJobsList()}" update=":jobsForm:tblJobs:jobFilter1 :jobsForm:tblJobs:jobFilter"
                                                 onclick="PF('tblJob1').clearFilters();"
                                                 class="btn-danger btn-xs" oncomplete="PF('tblJob1').getPaginator().setPage(0);PF('jobFilter1w').selectValue(1);PF('jobFilterw').selectValue(1);applyTopScrollbarJob()">
                                </p:commandButton>
                            </p:column>
                            <p:spacer width="4"/>
                            <p:column>
                                <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                                <p:commandButton value="Import" rendered="#{!jobs.newJob}"    title="Import from Excel" oncomplete="PF('dlgImpJobDtls').show();applyTopScrollbarJob()"    
                                                 actionListener="#{jobs.clearImportJobDtls()}" style="float: right" class="btn btn-primary btn-xs"
                                                 update=":jobsForm:tblJobs :jobsForm:tblJobs:jobFilter1 :jobsForm:tblJobs:jobFilter :jobsForm:op :jobsForm:impCnt :jobsForm:oq :jobsForm:qtUpld :jobsForm:er"/>                
                                <!--quotefrom:op quotefrom:er  quotefrom:oq quotefrom:qtUpld--> 

                            </p:column>
                            <!--//15-03-2024 : #13260 : ESCALATIONS CRM-7838   OASIS: Ability to search jobs in the Job module by a quote's quote number tha-->
                            <p:spacer width="8"/>
                            <p:column>
                                <p:inputText value="#{jobsLazyDataModel.quotNumber}"  style="width:120px" 
                                             placeholder="Search by Quote Number" title="Search by Quote Number" onkeypress="return handleKeyPress(event)"/>
                            </p:column>
                            <p:column>
                                <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                                <p:commandButton id="btnSearchJobsByQuoteNumber" title="Search" oncomplete="applyTopScrollbarJob()"
                                                 icon="fa fa-search" class="btn-success btn-xs" update=":jobsForm"/>
                            </p:column>
                            <p:spacer width="4"/>
                            <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                            <p:commandButton title="Clear Search" process="@this"
                                             icon="fa fa-times-circle" actionListener="#{jobsLazyDataModel.clearQuoteNumberSearch()}"
                                             id="btnClearSearchJobsByQuoteNumber" oncomplete="applyTopScrollbarJob()"
                                             update=":jobsForm" disabled="#{empty jobsLazyDataModel.quotNumber}"
                                             class="btn-danger btn-xs">
                            </p:commandButton>
                            <script>
                                function handleKeyPress(event) {
                                    if (event.keyCode === 13) {
                                        var btnSearch = document.getElementById('jobsForm:tblJobs:btnSearchJobsByQuoteNumber');
                                        console.log('enter button pressed');
                                        console.log(btnSearch);
                                        btnSearch.click();
                                        return false;
                                    }
                                    return true;
                                }
                            </script>
                        </div>  
                    </f:facet>

                    <!--                      //#13180 ESCALATION CRM-7806 Thea Web - Slow-->
                    <!--<p:column exportable="false" toggleable="false" selectionMode="multiple" style="width: 10px;text-align: center;"/>-->
                    <c:forEach var="column" items="#{jobs.listJobColumns}">


                        <!--   TASK#2542 Jobs: follow up date, awarded date, Last modified date, Creation date-->
                        <!--//23-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                        <p:column headerText="#{column.columnHeader}" id="#{column.columnName}"  
                                  visible="#{column.columnVisibleFlag==1}"  
                                  filterMatchMode="#{column.columnFilterMatchMode}" 
                                  field="#{column.columnName}"
                                  filterBy="#{column.columnName=='jobFollowUpDate'or column.columnName=='jobAwardedDate'or column.columnName=='updDate'or column.columnName=='insDate' ? globalParams.formatDateTime(jobs.convertDate(viewjobs[column.columnName]), 'da') : viewjobs[column.columnName]}" 
                                  sortBy="#{viewjobs[column.columnName]}" style="width:200px"
                                  >   
<!--                            <h:link  outcome="JobsView.xhtml?id=#{viewjobs.jobId}" 
                                     value="#{viewjobs[column.columnName]}"  />   -->
                            <c:choose>
                                <!--1678 CRM-3232: Add “Creation” date to Jobs screen/export-->
                                <c:when test="#{column.columnName=='jobFollowUpDate' or column.columnName=='jobAwardedDate'}">
                                    <h:link  outcome="JobsView.xhtml?id=#{viewjobs.jobId}" 
                                             value="#{viewjobs[column.columnName]!=null?globalParams.formatDateTime(viewjobs[column.columnName],'da'):''}">
                                        <f:convertDateTime pattern="#{globalParams.dateTimeFormat}" for="#{column.columnName}"/>
                                    </h:link>
                                </c:when>    
                                <c:when test="#{column.columnName=='updDate' or column.columnName=='insDate'}">
                                    <h:link  outcome="JobsView.xhtml?id=#{viewjobs.jobId}" 
                                             value="#{viewjobs[column.columnName]!=null?globalParams.formatDateTime(viewjobs[column.columnName],'dt'):''}">
                                        <f:convertDateTime pattern="#{globalParams.dateTimeFormat}" for="#{column.columnName}"/>
                                    </h:link>
                                </c:when>
                                <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
                                <c:when test="#{column.columnName=='jobvalue'}">

                                    <h:outputLabel  class="hvr"   value="#{viewjobs[column.columnName]}"  style="float: right" >


                                        <f:convertNumber groupingUsed="true"  maxIntegerDigits="15"   
                                                         minFractionDigits="2" maxFractionDigits="2"/>

                                        <p:ajax event="click" listener="#{jobs.redirectPage(viewjobs.jobId)}"/>

                                    </h:outputLabel>
                                </c:when>
                                <c:otherwise>
                                    <h:link  outcome="JobsView.xhtml?id=#{viewjobs.jobId}" 
                                             value="#{viewjobs[column.columnName]}"  />   
                                </c:otherwise>
                            </c:choose>
                        </p:column>
                    </c:forEach>
                </p:dataTable>
                <ui:include src="dlgs/ImportJobDtls.xhtml"/>
            </h:form>
        </div>

        <h:form id="delProjects">
            <p:confirmDialog global="true" showEffect="fade" responsive="true" >
                <div class="button_bar" align="center">
                    <p:commandButton value="Yes" type="button" styleClass="ui-confirmdialog-yes btn btn-success btn-xs"  />
                    <p:spacer width="4px" />
                    <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no btn btn-danger btn-xs"  />
                </div>
            </p:confirmDialog>
        </h:form>

        <ui:include src="/opploop/jobs/dlgs/AdvancedSearchDlg.xhtml"/>
        <!--04-11-2024 14809 ESCALATIONS CRM-8642   Jobs: Sorting by name not working-->
        <ui:include src="dlgs/InfoDlg.xhtml" />

        <script>

            //20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
            function applyTopScrollbarJob() {
                var tableExists = document.getElementById('jobsForm:tblJobs') === null;
                if (tableExists) {
                    return;
                }
                var dataTable = PF('tblJob1').jq;
                var scrollableHeaderBox = dataTable.find('.ui-datatable-scrollable-header-box');
                var scrollableBody = dataTable.find('.ui-datatable-scrollable-body');
                var scrollableView = dataTable.find('.ui-datatable-scrollable-view');

                // Check if the scrollbar already exists
                if ($('.top-scrollbar').length === 0) {
                    // Create the custom top scrollbar
                    var topScrollbar = document.createElement('div');
                    topScrollbar.className = 'top-scrollbar';
                    var innerDiv = document.createElement('div');
                    var width = scrollableBody.find('table').width();
                    if (width > 1338) {
                        if (1358 > width) {
                            width = 1360;
                        }
                    }
//                    console.log('width=', width)
                    innerDiv.style.width = width + 'px';
                    topScrollbar.appendChild(innerDiv);

                    // Insert the top scrollbar above the scrollable header box
                    scrollableHeaderBox.before(topScrollbar);

                    // Synchronize scroll positions
                    topScrollbar.addEventListener('scroll', function () {
                        scrollableBody.scrollLeft(topScrollbar.scrollLeft);
                        scrollableHeaderBox.scrollLeft(topScrollbar.scrollLeft);
                    });

                    scrollableBody.on('scroll', function () {
                        topScrollbar.scrollLeft = scrollableBody.scrollLeft();
                        scrollableHeaderBox.scrollLeft(scrollableBody.scrollLeft());
                    });

                    scrollableHeaderBox.on('scroll', function () {
                        topScrollbar.scrollLeft = scrollableHeaderBox.scrollLeft();
                        scrollableBody.scrollLeft(scrollableHeaderBox.scrollLeft());
                    });
                }
            }

//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top
            document.addEventListener('DOMContentLoaded', function () {
                applyTopScrollbarJob();
            });

            function keyHandler() {
                var searchValue = document.getElementById('jobsForm:globalFilter1').value;
                document.getElementById('jobsForm:tblJobs:globalFilter').value = searchValue;
                PF('tblJob1').filter();
            }
        </script>
        <!--                Feature #6630    CRM-5090: can we add a comma to the "value" field in Quotes, POs and Jobs-->
        <style>

            .hvr:hover{
                text-decoration:  underline;
            }
            /*//15-03-2024 : #13260 : ESCALATIONS CRM-7838   OASIS: Ability to search jobs in the Job module by a quote's quote number tha*/
            .ui-selectonemenu {
                width: 100%;
            }

            /*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->*/
                @media only screen and (min-height: 600px) and (max-height: 900px) {
    .ui-datatable-scrollable-body {
        height: 65vh;
        outline: 0px;
    }
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
@media only screen and (min-height: 901px) and (max-height: 1152px) {
    .ui-datatable-scrollable-body {
        height: 70vh;
        outline: 0px;
    }
}

@media only screen and (min-height: 1153px) and (max-height: 1440px) {
    .ui-datatable-scrollable-body {
        /*//21-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
        height: 70vh;
        outline: 0px;
    }
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
@media only screen and (min-height: 1500px) and (max-height: 1640px) {
    .ui-datatable-scrollable-body {
        /*//21-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
        height: 70vh;
        outline: 0px;
    }
}

/*//21-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
@media only screen and (min-height: 1641px) and (max-height: 1964px) {
    .ui-datatable-scrollable-body {
        height: 70vh; 
        outline: 0px;
    }
}

/*//21-10-2024 : #14732 : ESCALATIONS CRM-8599   RF Web : Empty space at the bottom on all modules*/
@media only screen and (min-height: 1964px) and (max-height: 2100px) {
    .ui-datatable-scrollable-body {
        height: 70vh; 
        outline: 0px;
    }
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-body {
    outline: 0px;
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-header *,
.ui-datatable-scrollable-theadclone * {
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    /*width: 100%;*/
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
body .ui-datatable .ui-datatable-scrollable-body::-webkit-scrollbar {
    width: 15px;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable table {
    /* table-layout: fixed !important; */
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-theadclone {
    visibility: collapse !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-tablewrapper {
    overflow: initial !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-paginator {
    background-color: #dedede !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.q {
    width: 100% !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.p {
    width: 80% !important;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-header-box,
.ui-datatable-scrollable-footer-box {
/*    overflow-x: auto !important;
    overflow-y: hidden !important;*/
    position: relative;
    /*margin-left: 0px !important;*/
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-view {
    position: relative;
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.top-scrollbar {
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    height: 16px; /* Adjust height as needed */
    overflow-x: auto;
    overflow-y: hidden;
    z-index: 10; /* Ensure it appears above the DataTable */
}

/*//04-09-2024 : #14435 : ESCALATION CRM-8209 : Quotes List: Scroll bar needs to be at the top*/
@-moz-document url-prefix() {
    .top-scrollbar {
        height: 12px; /* Height for Firefox */
    }
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.top-scrollbar div {
    width: 100%; /* Match the width of the DataTable */
    height: 20px; /* Match the height of the scrollbar container */
}

/*//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-*/
.ui-datatable-scrollable-header {
    z-index: 20; /* Ensure it appears above the DataTable */
}
            /*            .ui-datatable-header{
            
                            width: 1750px !important;
                        }*/
        </style>

        <!--3437 Job Auto Download (cron job)-->
        <ui:include src="dlgs/JobException.xhtml" />  
        <ui:include src="../../lookup/CompanyLookupDlg.xhtml"/> 
        <ui:include src="../../lookup/ContactLookupDlg.xhtml"/> 
        <ui:include src="../../lookup/UserLookupDlg.xhtml"/>
    </ui:define>



</ui:composition>
