
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                template="#{layoutMB.template}">
    <h:head> 

    </h:head>


    <!--#358 Page Title sharvanim - 03-05-2020-->

    <ui:define name="head">


        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>
        <title>#{custom.labels.get('IDS_JOB')} Details</title>
    </ui:define>

    <f:metadata>
        <!--<f:viewAction action="#{viewJobQuotes.populateJobQuotes(jobs.jobId)}"/>-->
        <f:viewParam name="id"  value="#{jobs.jobId}"/>
        <f:viewAction  action="#{viewJobs.populateJobs1(jobs.jobId)}"/>

<!--<f:viewAction action="#{helpService.prepareHelpLinks('JOBS_DTL')}"/>-->
        <!--2520 - Related - CRM-1521: Tutorial button landing urls - Quotes, Samples, Opportunities, Jobs, Projects-->
        <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('JOB_DTL'))}" />
        <!--        Task #4233:Job: Show Page not found if mentioned job Id does not exist  16/04/21 by harshithad-->
        <f:viewAction action="#{jobs.displayJob()}"/>

        <!-- #4948: Jobs > Watchers -->
        <f:viewAction action="#{jobs.listJobWatchers(jobs.jobId)}" />
        <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
        <f:viewAction action="#{helpService.setPageTag('JOB_DTL')}" />
    </f:metadata>


    <ui:define name="title">

        <p:outputLabel id="hdr" >

            #{jobs.jobId == 0? 'New Job' : "Job - ".concat(jobs.jobDescr)}
        </p:outputLabel>
        <ui:include src="/help/Help.xhtml"/>
    </ui:define>

    <ui:define name="script">
        <!--<h:outputScript library="js" name="repfabric.js" />-->
    </ui:define>

    <ui:define name="top_menu">
        <ui:include src="/Opp_Menu.xhtml" />
    </ui:define>


    <!--#2543: CRM-3371: Fwd: RE: Repfabric- Sales Outsource Solutions-->
    #{jobs.jobId == 0? 'New '.concat(custom.labels.get('IDS_JOB')) : custom.labels.get('IDS_JOB').concat(" - ").concat(jobs.jobDescr)}
    <ui:define name="body">
        <div class="box box-info box-body">
            <div class="left-column">

                <ui:include src="/opploop/jobs/JobsSummary.xhtml" />
            </div>
            <div class="right-column">
                <h:form>
                    <p:inputText value="#{jobs.pageUrl}" id="p1" class="page_url" type="hidden" />
                    <p:remoteCommand name="rc" id="rc11" actionListener="#{jobs.update_geo_cordinates(jobs.jobId)}" autoRun="false" />
                </h:form>
                <h:form id="jobDetailsButtonForm" >

                    <!--Growl message-->
                    <!--<p:growl id="growlJobSave" showDetail="false" />--> 
                    <p:growl id="growlJobSave" globalOnly="false" keepAlive="true" >
                        <p:autoUpdate />
                    </p:growl>
                    <!--Form Buttons - Edit, Save, Cancel, Delete-->
                    <div>
                        <h:panelGroup id="jobButtons" > 

                            <div class="box-header with-border">
                                <div class="row">
                                    <div class="col-md-12" >
                                        <p:commandButton rendered="#{jobs.recId>0}" styleClass="btn btn-info  btn-xs" value="Edit" disabled="#{jobs.disableEdit}" action="#{jobs.enableEditMode()}" update=":jobDetailsButtonForm:tabviewJobs :jobDetailsButtonForm:jobButtons" ></p:commandButton>
                                        <p:spacer width="4px"/> 
                                        <!--1999 CRM-3393: Avoid double click - CRM-->
                                        <!--4341 CRM-3257: NO RS- jobs: upflow/downflow comments job>opp and aj>opp>job-->
                                        <!--5802 Jobs: duplicate prevention for jobs-->
                                        <p:commandButton  styleClass="btn btn-success  btn-xs" id="cbSaveJobs"  value="Save" action="#{jobs.processSaveDetails()}" update=":jobDetailsButtonForm:tabviewJobs :jobDetailsButtonForm:growlJobSave :jobDetailsButtonForm:jobButtons :frmJobSummary :frmJobStatus :hdr :jobDetailsButtonForm"  widgetVar="jobSaveVar" onclick="PF('jobSaveVar').disable();"  oncomplete="convertToGoogleMapLink(); rc(); PF('progressDlg123').hide();" disabled="#{jobs.disablesave}"/>
                                        <p:spacer width="4px"/>
                                        <!--2122 JOBS Edit and Cancel any JOB, displays other JOB-->
                                        <!--                                        ISSUE FIX           task#4233-Job: Show Page not found if mentioned job Id does not exist   16/04/21 by harshithad-->
                                        <p:commandButton styleClass="btn btn-warning  btn-xs" value="Cancel" immediate="true" disabled="#{jobs.disablesave}" action="#{jobs.cancel()}" update=":jobDetailsButtonForm:tabviewJobs :jobDetailsButtonForm:jobButtons" />
                                        <p:spacer width="4px"/> 
                                        <!--//09-11-2023 : #12477 : CRM-6248 Jobs: Merge Feature including child objects-->
                                        <p:commandButton styleClass="btn btn-primary btn-xs" value="Merge" actionListener="#{jobs.fetchJobMergeList()}" 
                                                         update=":formJobMerge:jobMergeTable" oncomplete="PF('jobMergeDlgWidget').show()" 
                                                         rendered="#{jobs.newJob}" onstart="PF('jobMergeWidget').clearFilters()"/>
                                        <p:spacer width="4px"/>
                                        <!--//08-09-2023 : #12066 : ESCALATIONS CRM-7336   Jobs - latency in deleting a job-->
                                        <p:remoteCommand name="deletJobfunction" autoRun="false" actionListener="#{jobs.deleteJobsV2(jobs.jobId,jobs.flag)}" oncomplete="PF('deleteJobs').enable();PF('dialogProcessDlg').show();"/>
                                        <!--#8309: CRM-5838  whbsales/jon wiggs/Jobs match to opp and quote are totally broken - poornima - added Linked Document button-->
<!--                                        <p:commandButton id="btnLinkedDocs"  value="Linked Documents" class="btn btn-primary btn-xs" rendered="#{jobs.jobId>0}" disabled="#{jobs.jobOppList == 0 and jobs.jobQuoteList == 0}"
                                                           actionListener="#{jobLinkedDocumentService.loadJobLinkedDocuments(jobs.jobId)}"
                                                           oncomplete="PF('dlgJobLinkedDocs').show()" />-->

                                        <!--                                        05-01-2021:Task#2913:CRM-3666: User custom feature - Job Deletion Restriction-->
                                        <!--                                        09-01-202:Task#2901-CRM-3666: Rouzer priority dev Fix: DO NOT ALLOW delete on job-->
                                        <!-- #14608 :  CRM-8513   Jobs : Quotes: need a user custom feature "Do not allow user delete quotes"-->     
                                        <p:commandButton id="btnDeleteJob" style="float: right;"  styleClass="btn btn-danger btn-xs " value="Delete" 
                                                         rendered="#{jobs.newJob}" disabled="#{viewJobs.disableJobDeletion()}"  actionListener="#{viewJobs.disableQuoteDeletion()}" 
                                                         update=":jobDetailsButtonForm:confirmDeleteJob :jobDetailsButtonForm:chkDeleteJobStatus" 
                                                         onclick="#{jobs.changeDelFlag(true)}"
                                                         oncomplete="PF('confirmationJob').show()"/>
                                        <p:confirmDialog header="Confirm Deletion"  width="400" global="false"
                                                         id="confirmDeleteJob" class="dialogCSS"
                                                         widgetVar="confirmationJob"  >
                                            <f:facet name="message">
                                                <!-- #14608 :  CRM-8513   Jobs : Quotes: need a user custom feature "Do not allow user delete quotes"-->     
                                                <p:outputLabel  value="Are you sure to delete this #{custom.labels.get('IDS_JOB')}? All Associated records will be deleted" style="font-weight: bold" />
                                                <p:spacer height="4"/>
                                                <br/>           
                                                <p:selectBooleanCheckbox  value="#{jobs.flag}" disabled="#{!jobs.flag}" title= "#{!jobs.flag? 'You are not authorized to delete a quote' : ''} "  id="chkDeleteJobStatus" styleClass="#{jobs.flag ? '' : 'label-gray not-allowed'}" >
                                                    <p:ajax event="change" listener="#{jobs.changeDelFlag(jobs.flag)}"/>
                                                </p:selectBooleanCheckbox>
                                                <p:spacer width ="6px"/>
                                                <p:outputLabel id="deleteLbl"  value="Delete all related Quotes for this job" title= "#{!jobs.flag? 'You are not authorized to delete a quote': ''} " styleClass="#{jobs.flag ? '' : 'label-gray not-allowed'}" />
                                            </f:facet>
                                            <div class="button_bar" align="center">
                                                <!--                                                12-02-2021:Task#2901-CRM-3666: Rouzer priority dev Fix: DO NOT ALLOW delete on job-->
                                                <!--//08-09-2023 : #12066 : ESCALATIONS CRM-7336   Jobs - latency in deleting a job-->
                                                <p:commandButton value="Delete" onstart="PF('deleteJobs').disable();PF('dialogProcessDlg').show();deletJobfunction()" 
                                                                 widgetVar="deleteJobs" 
                                                                 styleClass="btn btn-danger btn-xs " 
                                                                 process="@this" />
                                                <p:spacer width ="4px"/>
                                                <!--                                                05-01-2021:Task#2933:CRM-3666: User custom feature - Job Deletion Restriction-->
                                                <p:commandButton value="Cancel"  styleClass="btn btn-warning btn-xs " onclick="PF('confirmationJob').hide()" 
                                                                 type="button"  />
                                            </div>

                                        </p:confirmDialog>
                                        <!--//08-09-2023 : #12066 : ESCALATIONS CRM-7336   Jobs - latency in deleting a job-->
                                        <p:dialog id="dialogProcess" widgetVar="dialogProcessDlg" closable="false" modal="true" header="Message" onShow="PF('dialogProcessDlg').initPosition();" resizable="false" >
                                            <p:outputPanel >
                                                <br />
                                                <h:graphicImage  library="images" name="ajax-loader.gif"   />
                                                <p:spacer width="5" />
                                                <p:outputLabel value="Loading please wait..." />
                                                <br /><br />
                                            </p:outputPanel>
                                        </p:dialog>
                                        <!--                                        <p:spacer width="4px"/>
                                                                                <p:commandButton value="Import" rendered="#{!jobs.newJob}"    title="Import from Excel" oncomplete="PF('dlgImpJobDtls').show()"    
                                                                                                 actionListener="#{jobs.clearImportQuotDtls()}" style="float: right" class="btn btn-primary btn-xs"
                                                                                                 update=""/>                -->
                                        <!--quotefrom:op quotefrom:er  quotefrom:oq quotefrom:qtUpld-->
                                        <!--5380 Oasis: Jobs > Fetch from Oasis--> 

                                        <!--4872 CRM Sync: Settings for Oasis-->
                                        <!--6194 Jobs: Sales Office issue when Oasis not configured-->
                                        <p:outputPanel id="btns" style="display: inline-flex; height: 24px;"  rendered="#{jobs.recId==0 and jobs.disableFetchOasisBtn==false or jobs.recId==0 and jobs.disableFetchISQuoteBtn==false  or jobs.recId==0 and jobs.disableFetchSalesOfficeBtn==false}">
                                            <p:outputLabel rendered="#{jobs.recId==0}" value="Fetch from"  />
                                            <p:spacer width="4"/>
                                            <h:panelGroup>
                                                <!--4872 CRM Sync: Settings for Oasis-->
                                                <p:selectOneMenu id="jobCRMTypeFilter" value="#{jobs.filterCRMSyncType}" widgetVar="jobCRMTypeFilterw" class="ui-select-btn" rendered="#{jobs.recId==0}">
                                                    <f:selectItems value="#{jobs.crmSystemMap}"/>
                                                    <!--                                        <f:selectItem itemLabel="ISQuote" itemValue="0"/>
                                                    <f:selectItem itemLabel="Oasis" itemValue="1"/> -->
                                                    <p:ajax event="change"  process="@this" listener="#{jobs.onChangeCRMTypeStatus(jobs.filterCRMSyncType)}"  
                                                            update=":jobDetailsButtonForm:isquotedateparam :jobDetailsButtonForm:isquoteparam :jobDetailsButtonForm :jobDetailsButtonForm:btns" />
                                                </p:selectOneMenu>
                                            </h:panelGroup>
                                            <p:spacer width="4"/>
                                            <p:spacer width="4"/>
                                            <h:panelGroup rendered="#{jobs.filterCRMSyncType==0}">
                                                <!--6194 Jobs: Sales Office issue when Oasis not configured--> 
                                                <p:selectOneMenu id="quotFilter" value="#{jobs.filterISQuotesStatus}"  widgetVar="jobFilterw" class="ui-select-btn"  rendered="#{jobs.recId==0 and jobs.filterCRMSyncType==0}"  >
                                                    <f:selectItem itemLabel="By Date Entered" itemValue="0"/>
                                                    <f:selectItem itemLabel="By Date Changed" itemValue="1"/>
                                                    <f:selectItem itemLabel="By Job No." itemValue="2"/>
                                                    <p:ajax event="change"  process="@this" listener="#{jobs.onChangeISQuotesStatus(jobs.filterISQuotesStatus)}"  
                                                            update=":jobDetailsButtonForm:isquotedateparam :jobDetailsButtonForm:isquoteparam :jobDetailsButtonForm " />
                                                </p:selectOneMenu>
                                            </h:panelGroup>
                                            <!--4872 CRM Sync: Settings for Oasis-->

                                            <h:panelGroup rendered="#{jobs.filterCRMSyncType==1  or jobs.disableFetchISQuoteBtn==true}">
                                                <!--6194 Jobs: Sales Office issue when Oasis not configured-->
                                                <p:selectOneMenu id="quotFilterO" value="#{jobs.filterISQuotesStatus}"  widgetVar="jobFilterwo" class="ui-select-btn"  rendered="#{jobs.recId==0 and jobs.filterCRMSyncType==1}"   >
                                                    <f:selectItem itemLabel="By Date Entered" itemValue="0"/>
                                                    <!--11462 CRM-7051   OASIS: Add option to fetch OASIS Jobs and Quotes by Job or Quote Number-->
                                                    <f:selectItem itemLabel="By Job No." itemValue="2"/>
                                                    <p:ajax event="change"  process="@this" listener="#{jobs.onChangeISQuotesStatus(jobs.filterISQuotesStatus)}"  
                                                            update=":jobDetailsButtonForm:isquotedateparam :jobDetailsButtonForm:isquoteparam :jobDetailsButtonForm " />
                                                </p:selectOneMenu>
                                            </h:panelGroup>
                                            <!--6048 CRM-4407: Sales Office Integration: Jobs: Manual Fetch-->
                                            <!--6194 Jobs: Sales Office issue when Oasis not configured-->
                                            <h:panelGroup rendered="#{jobs.filterCRMSyncType==2  or jobs.disableFetchISQuoteBtn==true }">
                                                <p:selectOneMenu id="quotFilter1" value="#{jobs.filterISQuotesStatus}"  widgetVar="jobFilterws" class="ui-select-btn"  rendered="#{jobs.recId==0 and jobs.filterCRMSyncType==2}"   >
                                                    <f:selectItem itemLabel="By Date Entered" itemValue="0"/>
                                                    <p:ajax event="change"  process="@this" listener="#{jobs.onChangeISQuotesStatus(jobs.filterISQuotesStatus)}"  
                                                            update=":jobDetailsButtonForm:isquotedateparam :jobDetailsButtonForm:isquoteparam :jobDetailsButtonForm " />
                                                </p:selectOneMenu>
                                            </h:panelGroup>

                                            <p:spacer width="4"/> 
                                            <p:inputText style="width: 76px!important;"   rendered="#{jobs.recId==0 and jobs.filterISQuotesStatus==2}"  id="isquoteparam"  value="#{jobs.filterISQuotes}"  />
                                            <p:calendar rendered="#{jobs.recId==0 and jobs.filterISQuotesStatus!=2}"  value="#{jobs.paramISQuotDate}" id="isquotedateparam" converterMessage="Invalid Date Format" 
                                                        style="width: 76px!important; margin: 0; padding: 1px;"   pattern="#{globalParams.dateFormat}"   >
                                                <p:ajax event="dateSelect" process="@this" listener="#{jobs.onDateChange(jobs.paramISQuotDate)}" update=":jobDetailsButtonForm:isquoteFetchBtn" />
                                                <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                                            </p:calendar>
                                            <p:spacer width="4"/> 
                                            <!--1461 CRM-3100: FW: Questions/Comments from SOUTHWEST-->
                                            <!--3437 Job Auto Download (cron job) -6-->
                                            <!--4872 CRM Sync: Settings for Oasis-->
                                            <!--6194 Jobs: Sales Office issue when Oasis not configured-->
                                             <!--14469: 08/10/2024: CRM-8480   ISQuote: job caught in aliaser hangs system when manually fetched-->
                                            <p:commandButton  value="Fetch" title="Fetch from ISQuote" id="isquoteFetchBtn" onstart="PF('progressDlg123').show()" onclick="PF('fetchButton').disable();" rendered="#{jobs.recId==0 and jobs.filterCRMSyncType==0}"
                                                              widgetVar="fetchButton" class="btn btn-primary btn-xs"  actionListener="#{iSQuoteCrmClient.populateAllISQuotesJobList(jobs.syncUserForIsQuote,jobs.paramISQuotDate,jobs.filterISQuotes,jobs.filterISQuotesStatus)}"
                                                              update=":isQfrm :isQfrm:isq :isQfrm:dtIsQtLbl :isQfrm:dtIsQt"   oncomplete="PF('isQuotJobListDtls').show();PF('fetchButton').enable();PF('progressDlg123').hide()"/>


                                            <!--:jobDetailsButtonForm:isQuotSyncDtlsx :jobDetailsButtonForm:isq :jobDetailsButtonForm:dtIsQt :jobDetailsButtonForm:dtIsQtLbl-->
                                            <!--6194 Jobs: Sales Office issue when Oasis not configured-->
                                            <!--12788  CRM-7639  OASIS: Synergy Pulling in Jobs with Incorrect Date-->
                                            <!--                                            <p:commandButton  value="Fetch" title="Fetch from Oasis" id="isquoteFetchBtn1"  class="btn btn-primary btn-xs"
                                                                                                          onclick="PF('fetchButtonO').disable();"   rendered="#{jobs.recId==0 and jobs.filterCRMSyncType==1}"
                                                                                                          widgetVar="fetchButtonO"   oncomplete="PF('isQuotJobListDtls1').show();PF('fetchButtonO').enable();"
                                                                                                          actionListener="#{oasisService.fetchOasisJobsData(jobs.syncUserForIsQuote,jobs.paramISQuotDate,jobs.filterISQuotes,jobs.filterISQuotesStatus,0)}"
                                                                                                          update=":isQfrm1"  />-->

                                            <!--13-03-2024 13234 Code Optimisation for Oasis Integration Quotes and Jobs : Manual fetch-->
                                            <!--<p:spacer width="4"/>--> 
                                            <p:commandButton  value="Fetch" title="Fetch from Oasis" id="btnFetchOasisJob" widgetVar="wvBtnFetchOasisJob" 
                                                              class="btn btn-primary btn-xs" rendered="#{jobs.recId==0 and jobs.filterCRMSyncType==1}"
                                                              onclick="PF('progressDlg123').show();PF('wvBtnFetchOasisJob').disable();"   
                                                              actionListener="#{oasisCrmService.manualFetchJobs(loginBean.userId,jobs.filterISQuotesStatus,jobs.paramISQuotDate,jobs.filterISQuotes)}"
                                                              update=":frmOasisJobs:opOasisJobsList" 
                                                              oncomplete="PF('wvBtnFetchOasisJob').enable();PF('progressDlg123').hide()" />

                                            <!--6048 CRM-4407: Sales Office Integration: Jobs: Manual Fetch-->
                                            <!--6194 Jobs: Sales Office issue when Oasis not configured-->                                
                                            <p:commandButton  value="Fetch" title="Fetch from Sales Office" id="isquoteFetchBtn2" onclick="PF('fetchButton1').disable();" 
                                                              rendered="#{jobs.recId==0 and jobs.filterCRMSyncType==2}"
                                                              widgetVar="fetchButton1" class="btn btn-primary btn-xs"  actionListener="#{salesofficeService.fetchSalesofficeJobsData(jobs.syncUserForIsQuote,jobs.paramISQuotDate,jobs.filterISQuotes,jobs.filterISQuotesStatus)}"
                                                              update=":isQfrm5"   oncomplete="PF('isQuotJobListDtls2').show();PF('fetchButton1').enable();"/>

                                        </p:outputPanel>

                                    </div>
                                </div>
                            </div>




                        </h:panelGroup>
                        <style>
                            .button_top.ui-button-text-only .ui-button-text{
                                padding-left: 1.8em;
                            }
                        </style>
                    </div>

                    <!--Tabs-->

                    <p:tabView dynamic="true"  id="tabviewJobs" style="background-color: white;" cache="false">
                        <!--12442: 01/11/2023: CRM-7332 jobs related: why are opps showing up in the list?-->
                        <p:ajax event="tabChange" onstart="#{jobComments.fecthJobComments(jobs.jobId)}" update="dtCmnt cmnt :jobDetailsButtonForm:growlJobSave"
                                process="@this" />
                        <p:tab title="Basic" >

                            <ui:include src="tabs/BasicTab.xhtml" />
                        </p:tab>
                        <p:tab title="Related Companies" rendered="#{jobs.renderTab}">
                            <ui:include src="tabs/RelatedComps.xhtml" />
                        </p:tab>
                        <p:tab title="#{custom.labels.get('IDS_OPPS')}" rendered="#{jobs.renderTab}" >
                            <ui:include src="tabs/Opportunity.xhtml" />
                        </p:tab>
                        <p:tab title="#{custom.labels.get('IDS_QUOTES')}" rendered="#{jobs.renderTab}" >
                            <ui:include src="tabs/Quotes.xhtml" />
                        </p:tab>
                        <p:tab title="Emails" rendered="#{jobs.renderTab}" >
                            <ui:include src="tabs/Emails.xhtml" />
                        </p:tab>
                        <p:tab title="#{custom.labels.get('IDS_COMMENTS')}" rendered="#{jobs.renderTab}">
                            <ui:include src="tabs/Comments.xhtml" />

                        </p:tab>
                        <p:tab title="Attachments" rendered="#{jobs.renderTab}" >
                            <ui:include src="tabs/Attachments.xhtml" />
                        </p:tab>
                        <!--                        24-12-2020: TASK#2793-Jobs > Job Contacts-->
                        <p:tab title="Contacts" rendered="#{jobs.renderTab}" >
                            <ui:include src="tabs/Contacts.xhtml"  />

                        </p:tab>
                        <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
                        <p:tab title="Linked Docs" rendered="#{jobs.renderTab}" >
                            <p:remoteCommand onstart="PF('progressDlg123').show()" name="loadJobLinkedDoc" autoRun="true" 
                                             actionListener="#{jobs.loadJobLinkedDoc(jobs.jobId)}" 
                                             update=":jobDetailsButtonForm:tabviewJobs:jobLinkedDocs" oncomplete="PF('progressDlg123').hide()"/>
                            <p:dataTable id="jobLinkedDocs" value="#{jobs.jobLinkedDocs}" 
                                         var="jobLnkdDoc"
                                         rows="10" 
                                         selectionMode="single" 
                                         filterEvent="keyup" 
                                         draggableColumns="true"
                                         emptyMessage="No Linked Documents found."  
                                         paginator="true" 
                                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                         paginatorAlwaysVisible="false" 
                                         class="tblmain"
                                         rowKey="#{jobLnkdDoc.recId}" resizableColumns="true"
                                         >
                                <p:column headerText="Type" filterBy="#{jobLnkdDoc.docType}" filterMatchMode="contains" sortBy="#{jobLnkdDoc.docType}">
                                    <p:commandLink rendered="#{jobLnkdDoc.docType eq 'Quote'}"  value="#{jobLnkdDoc.docType}"
                                                   onclick="window.open('../../opploop/quotes/NewQuote.xhtml?recId=#{jobLnkdDoc.docId}', '_blank');" 
                                                   >
                                    </p:commandLink>
                                    <p:commandLink rendered="#{jobLnkdDoc.docType eq 'Opportunity'}"  value="#{jobLnkdDoc.docType}"
                                                   onclick="window.open('../../opploop/opportunity/OpportunityView.xhtml?opid=#{jobLnkdDoc.docId}', '_blank');"
                                                   ></p:commandLink>
                                    <!--//24-05-2024 : #13765 : ESCALATIONS CRM-8083   Purchase Order: PO linked to Job but not showing in Linked Doc of the Job-->
                                    <p:commandLink rendered="#{jobLnkdDoc.docType eq custom.labels.get('IDS_PURCHASE_ORDERS')}"  value="#{jobLnkdDoc.docType}"
                                                   onclick="window.open('../../opploop/po/PoDetails.xhtml?id=#{jobLnkdDoc.docId}', '_blank');"
                                                   >
                                    </p:commandLink>
                                </p:column>
                                <p:column headerText="Doc. No." filterBy="#{jobLnkdDoc.docNo}" filterMatchMode="contains" sortBy="#{jobLnkdDoc.docNo}">
                                    <h:outputText value="#{jobLnkdDoc.docNo}" />
                                </p:column>
                                <p:column headerText="Doc. Date" filterBy="#{oppLinkedDocumentsService.getFormattedDate(jobLnkdDoc.docDate, 'da')}" filterMatchMode="contains" sortBy="#{jobLnkdDoc.docDate}">
                                    <h:outputText value="#{oppLinkedDocumentsService.getFormattedDate(oppLnkdDoc.docDate, 'da')}" />
                                </p:column>
                                <p:column headerText="Topic" filterBy="#{jobLnkdDoc.docTopic}" filterMatchMode="contains" sortBy="#{jobLnkdDoc.docTopic}">
                                    <h:outputText value="#{jobLnkdDoc.docTopic}" />
                                </p:column>
                                <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{jobLnkdDoc.docCustomer}" filterMatchMode="contains" sortBy="#{jobLnkdDoc.docCustomer}">
                                    <h:outputText value="#{jobLnkdDoc.docCustomer}" />
                                </p:column>
                                <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{jobLnkdDoc.docPrinci}" filterMatchMode="contains" sortBy="#{jobLnkdDoc.docPrinci}">
                                    <h:outputText value="#{jobLnkdDoc.docPrinci}" />
                                </p:column>
                                <p:column headerText="Value" filterBy="#{jobLnkdDoc.docValue}" filterMatchMode="contains" sortBy="#{jobLnkdDoc.docValue}">
                                    <h:outputText value="#{jobLnkdDoc.docValue}" style="float: right" />
                                </p:column>
                                <p:column headerText="Linked Doc" style="text-align: center!important;">
                                    <p:commandButton style="height:25px;width:25px;" 
                                                     styleClass="btn-primary  btn-xs" title="Linked Doc" icon="fa fa-eye"
                                                     actionListener="#{linkedDocService.loadQuotLinkedData(jobLnkdDoc.docType,jobLnkdDoc.docId)}"
                                                     action="#{linkedDocService.loadHeader(jobLnkdDoc.docType,jobLnkdDoc.docNo)}"
                                                     update="linkedDocumentForm:linkedDocumentDialogue" 
                                                     oncomplete="PF('linkedDocumentDlgVar').show();"
                                                     ></p:commandButton>
                                    <!--CRM-8441 : Jobs: Allow Deleting Links to other modules in Link Doc Tab-->
                                    <p:spacer width="4" />
                                    <p:commandButton style="height:25px;width:25px;" 
                                                     styleClass="btn-primary  btn-xs" title="UnLink Doc" icon="fa fa-unlink"
                                                     actionListener="#{linkedDocService.loadJobUnLink(jobs.jobId,jobLnkdDoc.docType,jobLnkdDoc.docId)}"
                                                     update="linkedDocumentForm:linkedDocumentDialogue" 
                                                     oncomplete="PF('unlinkDlg1').show();"
                                                     ></p:commandButton>
                                </p:column>
                            </p:dataTable>

                        </p:tab>
                    </p:tabView>
                    <!--4341 CRM-3257: NO RS- jobs: upflow/downflow comments job>opp and aj>opp>job-->
                    <p:confirmDialog id="cnfrmDlgUpdLinked" header="Confirmation for down flow of #{custom.labels.get('IDS_JOB')} Name" global="false" widgetVar="confirmationUpdLinked" >
                        <f:facet name="message">
                            <p:outputLabel value="#{custom.labels.get('IDS_JOB')} Name is changed. Do you want update the #{custom.labels.get('IDS_PROGRAM')} of the linked records ?" style="font-weight: bold" />
                            <br/>
                            <p:selectBooleanCheckbox value="#{jobs.confirmJobNameUpdOpp}"  
                                                     itemLabel="#{custom.labels.get('IDS_OPPS')}" id="confirmJobNameUpdOpp">
                                <p:ajax event="change" listener="#{jobs.changeOppFlag(jobs.confirmJobNameUpdOpp)}"/>
                            </p:selectBooleanCheckbox>  <p:spacer width="4"/> 
                            <p:selectBooleanCheckbox value="#{jobs.confirmJobNameUpdQuot}" 
                                                     itemLabel="#{custom.labels.get('IDS_QUOTES')}" id="confirmJobNameUpdQuot">
                                <p:ajax event="change" listener="#{jobs.changeQuotFlag(jobs.confirmJobNameUpdQuot)}"/>
                            </p:selectBooleanCheckbox>    
                        </f:facet>
                        <div align="center">
                            <p:commandButton id="btnYes" value="Yes"  actionListener="#{jobs.saveDetails()}" 
                                             update=":jobDetailsButtonForm:tabviewJobs :jobDetailsButtonForm:growlJobSave :jobDetailsButtonForm:jobButtons :frmJobSummary :frmJobStatus :hdr"
                                             styleClass="btn btn-success  btn-xs"   process="@this" 
                                             oncomplete="PF('confirmationUpdLinked').hide(); convertToGoogleMapLink(); rc();"  />



                            <p:spacer width="4px"/>
                            <p:commandButton id="btnNo" value="No"   actionListener="#{jobs.changeNoConfirmation()}" action="#{jobs.saveDetails()}"   styleClass="btn btn-danger  btn-xs"
                                             onclick="PF('confirmationUpdLinked').hide()"  update=":jobDetailsButtonForm:tabviewJobs :jobDetailsButtonForm:growlJobSave :jobDetailsButtonForm:jobButtons :frmJobSummary :frmJobStatus :hdr"
                                             />
                        </div>
                        <p:spacer width="10"/>
                    </p:confirmDialog>
                    <!--5802 Jobs: duplicate prevention for jobs-->

                    <p:confirmDialog id="cnfrmDlgDupJob" width="650px" header="Warning Message" global="false" widgetVar="dupJobconfirmation" >
                        <f:facet name="message">
                            <p:outputLabel value="Job already exists with same Job Name, City and #{custom.labels.get('IDS_ZIP')}. Do you want to proceed?" style="font-weight: bold" />

                            <p:remoteCommand  autoRun="true" update=":jobDetailsButtonForm:dupJobLst"  /> 
                            <p:dataTable value="#{jobs.jobDupList}" rows="10"
                                         var="jobDupVar" id="dupJobLst" 
                                         emptyMessage="No Jobs available"  
                                         > 
                                <p:column width="10%" >
                                    <f:facet name="header">Job No.</f:facet>
                                        #{jobDupVar.jobNo}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">Job Name</f:facet>
                                        #{jobDupVar.jobDescr}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">Street</f:facet>
                                        #{jobDupVar.jobAdress1}  
                                </p:column> 
                                <p:column width="10%" > 
                                    <f:facet name="header">City</f:facet>
                                        #{jobDupVar.jobcity}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">#{custom.labels.get('IDS_ZIP')}</f:facet>
                                        #{jobDupVar.jobZipCode}  
                                </p:column>
                            </p:dataTable>



                        </f:facet>
                        <div align="center">
                            <!--5802  Jobs: duplicate prevention for jobs-->
                            <p:commandButton id="btnDupJobYes" value="Yes" actionListener="#{jobs.preSaveDetails()}"  
                                             action="#{jobs.saveDetails()}"   oncomplete="PF('dupJobconfirmation').hide()" 
                                             styleClass="btn btn-success  btn-xs"   process="@this" 
                                             update=":jobDetailsButtonForm:tabviewJobs:reldCompDT" />
                            <p:spacer width="4px"/>
                            <p:commandButton id="btnDupJobNo" value="No"   styleClass="btn btn-danger  btn-xs"
                                             onclick="PF('dupJobconfirmation').hide()" 
                                             />
                        </div>
                        <p:spacer width="10"/>
                    </p:confirmDialog>
                    <!--5802  Jobs: duplicate prevention for jobs-->
                    <p:confirmDialog id="cnfrmDlgCrmDupJob" width="650px" header="Warning Message" global="false" widgetVar="dupCrmJobconfirmation" >
                        <f:facet name="message">
                            <p:outputLabel value="Job already exists with same Job Name, City and #{custom.labels.get('IDS_ZIP')}. Do you want to proceed?" style="font-weight: bold" />
                            <p:remoteCommand  autoRun="true" update=":jobDetailsButtonForm:dupJobLst1"  /> 
                            <p:dataTable value="#{jobs.jobDupList}" rows="10"
                                         var="jobDupVar1" id="dupJobLst1" 
                                         emptyMessage="No Jobs available"  
                                         > 
                                <p:column width="10%" >
                                    <f:facet name="header">Job No.</f:facet>
                                        #{jobDupVar1.jobNo}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">Job Name</f:facet>
                                        #{jobDupVar1.jobDescr}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">Street</f:facet>
                                        #{jobDupVar1.jobAdress1}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">City</f:facet>
                                        #{jobDupVar1.jobcity}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">#{custom.labels.get('IDS_ZIP')}</f:facet>
                                        #{jobDupVar1.jobZipCode}  
                                </p:column> 
                            </p:dataTable>
                        </f:facet>
                        <div align="center">
                            <p:commandButton id="btnDupCrmJobYes" value="Yes" actionListener="#{jobs.preSaveDetails()}" action="#{jobs.selectSFJobs(jobs.selectedCrmJobsHdr)}"
                                             styleClass="btn btn-success  btn-xs"  oncomplete="PF('dupCrmJobconfirmation').hide()" 
                                             update=":isQfrm2:isql :isQuotLnItmListDtlsx" process="@this"  />
                            <p:spacer width="4px"/>
                            <p:commandButton id="btnDupCrmJobNo" value="No"   styleClass="btn btn-danger  btn-xs"
                                             onclick="PF('dupCrmJobconfirmation').hide()" 
                                             />
                        </div>
                        <p:spacer width="10"/>
                    </p:confirmDialog>
                    <!--5802  Jobs: duplicate prevention for jobs-->
                    <p:confirmDialog id="cnfrmDlgCrmOasisDupJob" width="650px" header="Warning Message" global="false" widgetVar="dupCrmOasisJobconfirmation" >
                        <f:facet name="message">
                            <p:outputLabel value="Job already exists with same Job Name, City and #{custom.labels.get('IDS_ZIP')}. Do you want to proceed?" style="font-weight: bold" />
                            <p:remoteCommand  autoRun="true" update=":jobDetailsButtonForm:dupJobLst2"  /> 
                            <p:dataTable value="#{jobs.jobDupList}" rows="10"
                                         var="jobDupVar2" id="dupJobLst2" 
                                         emptyMessage="No Jobs available"  
                                         > 
                                <p:column width="10%" >
                                    <f:facet name="header">Job No.</f:facet>
                                        #{jobDupVar2.jobNo}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">Job Name</f:facet>
                                        #{jobDupVar2.jobDescr}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">Street</f:facet>
                                        #{jobDupVar2.jobAdress1}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">City</f:facet>
                                        #{jobDupVar2.jobcity}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">#{custom.labels.get('IDS_ZIP')}</f:facet>
                                        #{jobDupVar2.jobZipCode}  
                                </p:column> 
                            </p:dataTable>
                        </f:facet>
                        <div align="center">
                            <p:commandButton id="btnDupCrmOasisJobYes" value="Yes" actionListener="#{jobs.preSaveDetails()}" action="#{jobs.selectOasisJobs(jobs.selectedCrmJobsHdr,0,null)}"
                                             styleClass="btn btn-success  btn-xs"  oncomplete="PF('dupCrmOasisJobconfirmation').hide()" 
                                             update=":isQfrm2:isql :isQuotLnItmListDtlsx :isQfrm3:isql3 :isQfrm3" process="@this"  />
                            <p:spacer width="4px"/>
                            <p:commandButton id="btnDupCrmOasisJobNo" value="No"   styleClass="btn btn-danger  btn-xs"
                                             onclick="PF('dupCrmOasisJobconfirmation').hide()" 
                                             />
                        </div>
                        <p:spacer width="10"/>
                    </p:confirmDialog>

                    <!--13-03-2024 13234 Code Optimisation for Oasis Integration Quotes and Jobs : Manual fetch-->
                    <p:confirmDialog id="cnfrmDlgCrmOasisDupJobs" width="650px" header="Warning Message" global="false" widgetVar="wvCnfrmDlgCrmOasisDupJobs" >
                        <f:facet name="message">
                            <p:outputLabel value="Job already exists with same Job Name, City and #{custom.labels.get('IDS_ZIP')}. Do you want to proceed?" style="font-weight: bold" />
                            <p:remoteCommand  autoRun="true" update=":jobDetailsButtonForm:dtCrmOasisDupJobs"  /> 
                            <p:dataTable value="#{oasisCrmService.duplicateJobsList}" rows="10"
                                         var="duplicateOasisJob" id="dtCrmOasisDupJobs" widgetVar="wvDtCrmOasisDupJobs"
                                         emptyMessage="No Jobs available"  
                                         > 
                                <p:column width="10%" >
                                    <f:facet name="header">Job No.</f:facet>
                                        #{duplicateOasisJob.jobNo}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">Job Name</f:facet>
                                        #{duplicateOasisJob.jobDescr}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">Street</f:facet>
                                        #{duplicateOasisJob.jobAdress1}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">City</f:facet>
                                        #{duplicateOasisJob.jobcity}  
                                </p:column> 
                                <p:column width="10%" >
                                    <f:facet name="header">#{custom.labels.get('IDS_ZIP')}</f:facet>
                                        #{duplicateOasisJob.jobZipCode}  
                                </p:column> 
                            </p:dataTable>
                        </f:facet>
                        <div align="center">
                            <p:commandButton value="Yes" styleClass="btn btn-success  btn-xs" process="@this" 
                                             actionListener="#{oasisCrmService.onOasisJobSelected(oasisCrmService.selectedJob)}"
                                             update=":frmOasisCompanyLink:opOasisCompanyLinkList"
                                             oncomplete="PF('wvCnfrmDlgCrmOasisDupJobs').hide();" />
                            <p:spacer width="4px"/>
                            <p:commandButton value="No"   styleClass="btn btn-danger  btn-xs"
                                             onclick="PF('wvCnfrmDlgCrmOasisDupJobs').hide()" 
                                             />
                        </div>
                        <p:spacer width="10"/>
                    </p:confirmDialog>
                </h:form> 
                <!--//09-11-2023 : #12477 : CRM-6248 Jobs: Merge Feature including child objects-->
                <h:form id="formJobMerge">
                    <p:dialog id="jobMergeDlg"  widgetVar="jobMergeDlgWidget" width="1000"  modal="true" class="dialogCSS" 
                              resizable="false" header="#{custom.labels.get('IDS_JOB')} Lookup"
                              onShow="PF('jobMergeDlgWidget').initPosition();" style="height:480px">
                        <p:ajax event="close" onstart="PF('jobMergeWidget').clearFilters();"/>
                        <p:dataTable   value="#{jobs.jobMergesList}" var="jobMergevar" 
                                       id="jobMergeTable" paginator="true"  widgetVar="jobMergeWidget"  
                                       selectionMode="single" rowIndexVar="index" style="height:430px;"                                      
                                       rowKey="#{jobMergevar.jobId}"  selection="#{jobs.jobMergeSummary}"                                   
                                       paginatorAlwaysVisible="false" rowsPerPageTemplate="5,10,15"
                                       paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                       filterEvent="keyup" pageLinks="4" reflow="true"
                                       rows="10"   class="tblmain" paginatorPosition="top"
                                       emptyMessage="No jobs available">

                            <p:ajax event="rowSelect" immediate="true" listener="#{jobs.selectedJobMerge}" 
                                    oncomplete="PF('jobMergeSumamaryDlgWidget').show();delayToWaitMergeDate();"
                                    update=":formJobMergeSumamary:jobMergeSumamaryDlg" process="@this" />


                            <p:column headerText="#{custom.labels.get('IDS_JOB')} Name" filterMatchMode="contains" 
                                      filterBy="#{jobMergevar.jobDescr}" sortBy="#{jobMergevar.jobDescr}">
                                #{jobMergevar.jobDescr}
                            </p:column>
                            <p:column headerText="#{custom.labels.get('IDS_JOB')} No" filterMatchMode="contains" 
                                      filterBy="#{jobMergevar.jobNo}" sortBy="#{jobMergevar.jobNo}">
                                #{jobMergevar.jobNo}
                            </p:column>
                            <p:column headerText="Sales Person" sortBy="#{jobMergevar.jobOwnerName}" filterMatchMode="contains" 
                                      filterBy="#{jobMergevar.jobOwnerName}">
                                #{jobMergevar.jobOwnerName}
                            </p:column>
                            <p:column headerText="#{custom.labels.get('IDS_JOB_TYPE')}" sortBy="#{jobMergevar.jobTypeName}" 
                                      filterMatchMode="contains" filterBy="#{jobMergevar.jobTypeName}">
                                #{jobMergevar.jobTypeName}
                            </p:column>
                            <p:column headerText="#{custom.labels.get('IDS_JOB_STAGE')}" sortBy="#{jobMergevar.jobCatgName}" 
                                      filterMatchMode="contains" filterBy="#{jobMergevar.jobCatgName}">
                                #{jobMergevar.jobCatgName}
                            </p:column>
                            <p:column headerText="#{custom.labels.get('IDS_JOB_VALUE')}" sortBy="#{jobMergevar.jobvalue}" 
                                      filterMatchMode="contains" filterBy="#{jobMergevar.jobvalue}">
                                #{jobMergevar.jobvalue}
                            </p:column>
                        </p:dataTable>

                    </p:dialog>

                </h:form>
                <!--//09-11-2023 : #12477 : CRM-6248 Jobs: Merge Feature including child objects-->
                <h:form id="formJobMergeSumamary">
                    <p:dialog id="jobMergeSumamaryDlg"  widgetVar="jobMergeSumamaryDlgWidget" width="800"  modal="true" 
                              class="dialogCSS" resizable="false" header="#{custom.labels.get('IDS_JOB')} Merge Summary"
                              style="max-height: 680px;">
                        <p:outputPanel style="position: relative;max-height:520px; overflow: auto">
                            <!--02-02-2024 12761 ESCALATION CRM-7599   OASIS: Surface Project Rank for Quotes-->
                            <!--//13-02-2024 : #13135 : Incidental finding-Rank field is missing  in "Job merge summary" while merging Job-->
                            <div class="ui-g ui-fluid header-bar" 
                                 style="#{(jobs.jobMergeSummary.jobOwner == 0 and jobs.jobOwner != 0) or (empty jobs.jobMergeSummary.jobOwnerCompany and not empty jobs.jobOwnerCompany) or
                                          (empty jobs.jobMergeSummary.jobOwnerContact and not empty jobs.jobOwnerContact and (jobs.jobMergeSummary.jobOwnerCompany eq jobs.jobOwnerCompany or empty jobs.jobMergeSummary.jobOwnerCompany))
                                          or (jobs.jobMergeSummary.jobType == 0 and jobs.jobType != 0)  or
                                          (jobs.jobMergeSummary.jobCategory == 0 and jobs.jobCategory != 0) or
                                          (jobs.jobMergeSummary.jobActivity == 0 and jobs.jobActivity != 0) or 
                                          (jobs.jobValueBol) or 
                                          (jobs.jobMergeSummaryWacthers.size() != 0) or (jobs.specifierMergeSummaryList.size() != 0)
                                          or (empty jobs.jobMergeSummary.jobNo and not empty jobs.jobNo) or 
                                          (empty jobs.jobMergeSummary.jobUrl and not empty jobs.jobUrl) or 
                                          (empty jobs.jobMergeRank and not empty jobs.jobRank) or
                                          (jobs.jobMergeSummary.jobBidDate == null and jobs.jobBidDate != null) or 
                                          (jobs.jobMergeSummary.jobProdDate == null and jobs.jobProdDate != null) or 
                                          (jobs.jobMergeSummary.jobFollowUpDate == null and jobs.jobFollowUpDate != null) or 
                                          (empty jobs.jobMergeSummaryNotes and not empty jobs.jobNotes) or 
                                          (jobs.jobAwardedMergeSummaryList.size() != 0) or 
                                          (jobs.jobMergeSummary.jobAwardedDate == null and jobs.jobAwardedDate != null)
                                          ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                                    <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Merged Fields</p:outputLabel>
                                </div>
                                <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                                </div>
                            </div> 
                            <div class="ui-g ui-fluid" style="#{jobs.jobMergeSummary.jobOwner == 0 and jobs.jobOwner != 0 ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel value="Sales Person" style="font-weight: 550 !important;" />
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel value="#{jobs.jobMergeOwnerName}" />
                                </div>
                            </div>
                            <div class="ui-g ui-fluid" style="#{empty jobs.jobMergeSummary.jobOwnerCompany and not empty jobs.jobOwnerCompany ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="#{custom.labels.get('IDS_JOB_OWNER_COMP')}" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobOwnerCompany}"/>
                                </div>
                            </div>
                            <div class="ui-g ui-fluid" style="#{empty jobs.jobMergeSummary.jobOwnerContact and not empty jobs.jobOwnerContact and (jobs.jobMergeSummary.jobOwnerCompany eq jobs.jobOwnerCompany or empty jobs.jobMergeSummary.jobOwnerCompany) ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="#{custom.labels.get('IDS_JOB_OWNER_CONT')}" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobOwnerContact}"  />
                                </div>
                            </div>
                            <div class="ui-g ui-fluid" style="#{jobs.jobMergeSummary.jobType == 0 and jobs.jobType != 0 ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="#{custom.labels.get('IDS_JOB_TYPE')}" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobTypeName}"/>
                                </div>

                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.jobMergeSummary.jobCategory == 0 and jobs.jobCategory != 0 ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="#{custom.labels.get('IDS_JOB_CATEGORY')}" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobCatgName}"  />
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.jobMergeSummary.jobActivity == 0 and jobs.jobActivity != 0 ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="#{custom.labels.get('IDS_JOB_STAGE')}" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobActivityName}"/>
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.jobValueBol ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="#{custom.labels.get('IDS_JOB_VALUE')}" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobvalue}"  />
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.jobMergeSummaryWacthers.size() == 0 ? 'display:none' : ''}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel  value="#{custom.labels.get('IDS_WATCHERS')}" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel   style="width: 100%;"  
                                                     value="#{jobs.jobMergeWatchers}" />
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.specifierMergeSummaryList.size() == 0 ? 'display:none' : ''}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5 ">
                                    <p:outputLabel style="font-weight: 550 !important;">Specifiers</p:outputLabel>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel style="width: 100%" value="#{jobs.jobMergeSpecifier}"/>
                                </div>
                            </div> 

                            <div class="ui-g ui-fluid" style="#{empty jobs.jobMergeSummary.jobNo and not empty jobs.jobNo ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="#{custom.labels.get('IDS_JOB')} No" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobNo}"/>
                                </div>

                            </div>

                            <div class="ui-g ui-fluid" style="#{empty jobs.jobMergeSummary.jobUrl and not empty jobs.jobUrl ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="URL" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobUrl}"  />
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.jobMergeSummary.jobBidDate == null and jobs.jobBidDate != null ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="#{custom.labels.get('IDS_JOB_BID_DATE')}" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobMergeBidDate}"/>
                                </div>

                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.jobMergeSummary.jobProdDate == null and jobs.jobProdDate != null ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="#{custom.labels.get('IDS_JOB_ORDER_DATE')}" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobMergeOrderDate}"  />
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.jobMergeSummary.jobFollowUpDate == null and jobs.jobFollowUpDate != null ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="#{custom.labels.get('IDS_JOB_FOLLOWUP')}" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobFollowUpDate}"  />
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{empty jobs.jobMergeSummaryNotes and not empty jobs.jobNotes ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel value="Notes" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel   
                                        style="width: 100%" 
                                        value="#{jobs.jobNotes}"/>
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.jobAwardedMergeSummaryList.size() == 0 ? 'display:none' : ''}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel value="Awardees" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel style="width: 100%" value="#{jobs.jobMergeAwarded}"/>
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.jobMergeSummary.jobAwardedDate == null and jobs.jobAwardedDate != null ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="Awarded Date" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobMergeAwardnessDate}" id="jobMergeAwardedDate" />
                                </div>
                            </div>
                            <!--//13-02-2024 : #13135 : Incidental finding-Rank field is missing  in "Job merge summary" while merging Job-->
                            <div class="ui-g ui-fluid" style="#{empty jobs.jobMergeRank and not empty jobs.jobRank ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="Rank" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobRank}"  />
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" >
                                <div class="ui-sm-12 ui-md-12 ui-lg-12"></div>
                            </div>

                            <div class="ui-g ui-fluid header-bar" style="#{empty jobs.jobMergeSummary.jobAdress1 and empty jobs.jobMergeSummary.jobcity and empty jobs.jobMergeSummary.jobState and empty jobs.jobMergeSummary.jobZipCode and empty jobs.jobMergeSummary.jobCountryCode and (not empty jobs.jobAdress1 || not empty jobs.jobcity || not empty jobs.jobState || not empty jobs.jobZipCode || not empty jobs.jobCountryCode) ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                                    <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Address fields</p:outputLabel>
                                </div>
                                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                                </div>
                            </div> 

                            <div class="ui-g ui-fluid" style="#{not empty jobs.jobAdress1 and empty jobs.jobMergeSummary.jobAdress1 and empty jobs.jobMergeSummary.jobcity and empty jobs.jobMergeSummary.jobState and empty jobs.jobMergeSummary.jobZipCode and empty jobs.jobMergeSummary.jobCountryCode ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel value="Street" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel value="#{jobs.jobAdress1}"/>
                                </div>

                            </div>

                            <div class="ui-g ui-fluid" style="#{not empty jobs.jobcity and empty jobs.jobMergeSummary.jobAdress1 and empty jobs.jobMergeSummary.jobcity and empty jobs.jobMergeSummary.jobState and empty jobs.jobMergeSummary.jobZipCode and empty jobs.jobMergeSummary.jobCountryCode ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel value="City" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel value="#{jobs.jobcity}"  />
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{not empty jobs.jobState and empty jobs.jobMergeSummary.jobAdress1 and empty jobs.jobMergeSummary.jobcity and empty jobs.jobMergeSummary.jobState and empty jobs.jobMergeSummary.jobZipCode and empty jobs.jobMergeSummary.jobCountryCode ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel value="#{custom.labels.get('IDS_STATE')}" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel value="#{jobs.jobState}"/>
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{not empty jobs.jobZipCode and empty jobs.jobMergeSummary.jobAdress1 and empty jobs.jobMergeSummary.jobcity and empty jobs.jobMergeSummary.jobState and empty jobs.jobMergeSummary.jobZipCode and empty jobs.jobMergeSummary.jobCountryCode ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel value="#{custom.labels.get('IDS_ZIP')}" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel value="#{jobs.jobZipCode}" />
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{not empty jobs.jobCountryCode and empty jobs.jobMergeSummary.jobAdress1 and empty jobs.jobMergeSummary.jobcity  and empty jobs.jobMergeSummary.jobState and empty jobs.jobMergeSummary.jobZipCode and empty jobs.jobMergeSummary.jobCountryCode ? '' : 'display:none'}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5" >
                                    <p:outputLabel value="Country" style="font-weight: 550 !important;"/>
                                </div>
                                <div class="ui-sm-12 ui-md-7 ui-lg-7" >
                                    <p:outputLabel value="#{jobs.jobCountryCode}"/>
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" >
                                <div class="ui-sm-12 ui-md-12 ui-lg-12"></div>
                            </div>

                            <div class="ui-g ui-fluid header-bar" style="#{jobs.jobOppListSize.size() == 0 and jobs.jobQuotListSize.size() == 0 and jobs.jobEmailListSize.size() == 0 and jobs.jobCommentListSize.size() == 0 and jobs.jobAttachmentListSize.size() == 0  ? 'display:none' : ''}">
                                <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                                    <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Child records</p:outputLabel>
                                </div>
                                <div class="ui-sm-12 ui-md-6 ui-lg-6">
                                </div>
                            </div> 

                            <div class="ui-g ui-fluid" style="#{jobs.jobOppListSize.size() == 0 ? 'display:none' : ''}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel value="#{custom.labels.get('IDS_OPPS')}" style="font-weight: 550 !important;"/>
                                </div>

                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel value="#{jobs.jobOppListSize.size()}"  />
                                </div>

                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.jobQuotListSize.size() == 0 ? 'display:none' : ''}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel value="#{custom.labels.get('IDS_QUOTES')}" style="font-weight: 550 !important;"/>
                                </div>

                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel value="#{jobs.jobQuotListSize.size()}"/>
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.jobEmailListSize.size() == 0 ? 'display:none' : ''}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel value="Emails" style="font-weight: 550 !important;"/>
                                </div>

                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel value="#{jobs.jobEmailListSize.size()}"  />
                                </div>

                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.jobCommentListSize.size() == 0 ? 'display:none' : ''}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel value="#{custom.labels.get('IDS_COMMENTS')}" style="font-weight: 550 !important;"/>
                                </div>

                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel value="#{jobs.jobCommentListSize.size()}"  />
                                </div>
                            </div>

                            <div class="ui-g ui-fluid" style="#{jobs.jobAttachmentListSize.size() == 0 ? 'display:none' : ''}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel value="Attachments" style="font-weight: 550 !important;"/>
                                </div>

                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel value="#{jobs.jobAttachmentListSize.size()}"  />
                                </div>

                            </div>
                            <!--//13-02-2024 : #13135 : Incidental finding-Rank field is missing  in "Job merge summary" while merging Job-->
                            <!--02-02-2024 12761 ESCALATION CRM-7599   OASIS: Surface Project Rank for Quotes-->
<!--                            <div class="ui-g ui-fluid" style="#{(not empty jobMergeRank) and (empty jobs.jobRank) ? 'display:none' : ''}">
                                <div class="ui-sm-12 ui-md-5 ui-lg-5">
                                    <p:outputLabel value="Attachments" style="font-weight: 550 !important;"/>
                                </div>

                                <div class="ui-sm-12 ui-md-7 ui-lg-7">
                                    <p:outputLabel value="#{jobs.jobRank}"  />
                                </div>
                            </div>-->
                            <!--//13-02-2024 : #13135 : Incidental finding-Rank field is missing  in "Job merge summary" while merging Job-->
                            <div class="ui-g ui-fluid"
                                 style="#{(jobs.jobMergeSummary.jobOwner == 0 and jobs.jobOwner != 0) or (empty jobs.jobMergeSummary.jobOwnerCompany and not empty jobs.jobOwnerCompany) or
                                          (empty jobs.jobMergeSummary.jobOwnerContact and not empty jobs.jobOwnerContact and (jobs.jobMergeSummary.jobOwnerCompany eq jobs.jobOwnerCompany or empty jobs.jobMergeSummary.jobOwnerCompany))
                                          or (jobs.jobMergeSummary.jobType == 0 and jobs.jobType != 0)  or
                                          (jobs.jobMergeSummary.jobCategory == 0 and jobs.jobCategory != 0) or
                                          (jobs.jobMergeSummary.jobActivity == 0 and jobs.jobActivity != 0) or 
                                          (jobs.jobValueBol) or 
                                          (jobs.jobMergeSummaryWacthers.size() != 0) or (jobs.specifierMergeSummaryList.size() != 0)
                                          or (empty jobs.jobMergeSummary.jobNo and not empty jobs.jobNo) or 
                                          (empty jobs.jobMergeSummary.jobUrl and not empty jobs.jobUrl) or 
                                          (empty jobs.jobMergeRank and not empty jobs.jobRank) or
                                          (jobs.jobMergeSummary.jobBidDate == null and jobs.jobBidDate != null) or 
                                          (jobs.jobMergeSummary.jobProdDate == null and jobs.jobProdDate != null) or 
                                          (jobs.jobMergeSummary.jobFollowUpDate == null and jobs.jobFollowUpDate != null) or 
                                          (empty jobs.jobMergeSummaryNotes and not empty jobs.jobNotes) or 
                                          (jobs.jobAwardedMergeSummaryList.size() != 0) or 
                                          (jobs.jobMergeSummary.jobAwardedDate == null and jobs.jobAwardedDate != null)
                                          or (((jobs.jobOppListSize.size() != null and jobs.jobOppListSize.size() != 0) or (jobs.jobQuotListSize.size() != null and jobs.jobQuotListSize.size() != 0) 
                                          or (jobs.jobEmailListSize.size() != null and jobs.jobEmailListSize.size() != 0) or (jobs.jobCommentListSize.size() != null and
                                          jobs.jobCommentListSize.size() != 0) or (jobs.jobAttachmentListSize.size() != null and jobs.jobAttachmentListSize.size() != 0)) or (empty jobs.jobMergeSummary.jobAdress1 
                                          and empty jobs.jobMergeSummary.jobcity and empty jobs.jobMergeSummary.jobState and empty jobs.jobMergeSummary.jobZipCode 
                                          and empty jobs.jobMergeSummary.jobCountryCode and (not empty jobs.jobAdress1 || not empty jobs.jobcity || 
                                          not empty jobs.jobState || not empty jobs.jobZipCode || not empty jobs.jobCountryCode)))
                                          ? 'display:none;' : ''}">
                                <div class="ui-sm-12 ui-md-4 ui-lg-4"></div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4">
                                    <p:outputLabel value="Job merged fields are not available"  />
                                </div>
                                <div class="ui-sm-12 ui-md-4 ui-lg-4"></div>
                            </div>
                        </p:outputPanel>

<!--                        <div class="ui-g ui-fluid" style="#{jobs.jobContactListSize.size() == 0 ? 'display:none' : ''}">
    <div class="ui-sm-12 ui-md-6 ui-lg-6">
        <p:outputLabel value="Total Contacts Links" />
    </div>
    
    <div class="ui-sm-12 ui-md-6 ui-lg-6">
        <p:inputText readonly="true" value="#{jobs.jobContactListSize.size()}"  />
    </div>
</div>-->
                        <p:outputPanel style="margin-top:20px;">
                            <!--//13-02-2024 : #13135 : Incidental finding-Rank field is missing  in "Job merge summary" while merging Job-->
                            <div class="ui-g ui-fluid" style="#{(jobs.jobMergeSummary.jobOwner == 0 and jobs.jobOwner != 0) or (empty jobs.jobMergeSummary.jobOwnerCompany and not empty jobs.jobOwnerCompany) or
                                                                (empty jobs.jobMergeSummary.jobOwnerContact and not empty jobs.jobOwnerContact and (jobs.jobMergeSummary.jobOwnerCompany eq jobs.jobOwnerCompany or empty jobs.jobMergeSummary.jobOwnerCompany))
                                                                or (jobs.jobMergeSummary.jobType == 0 and jobs.jobType != 0)  or
                                                                (jobs.jobMergeSummary.jobCategory == 0 and jobs.jobCategory != 0) or
                                                                (jobs.jobMergeSummary.jobActivity == 0 and jobs.jobActivity != 0) or 
                                                                (jobs.jobValueBol) or 
                                                                (jobs.jobMergeSummaryWacthers.size() != 0) or (jobs.specifierMergeSummaryList.size() != 0)
                                                                or (empty jobs.jobMergeSummary.jobNo and not empty jobs.jobNo) or 
                                                                (empty jobs.jobMergeSummary.jobUrl and not empty jobs.jobUrl) or 
                                                                (empty jobs.jobMergeRank and not empty jobs.jobRank) or
                                                                (jobs.jobMergeSummary.jobBidDate == null and jobs.jobBidDate != null) or 
                                                                (jobs.jobMergeSummary.jobProdDate == null and jobs.jobProdDate != null) or 
                                                                (jobs.jobMergeSummary.jobFollowUpDate == null and jobs.jobFollowUpDate != null) or 
                                                                (empty jobs.jobMergeSummaryNotes and not empty jobs.jobNotes) or 
                                                                (jobs.jobAwardedMergeSummaryList.size() != 0) or 
                                                                (jobs.jobMergeSummary.jobAwardedDate == null and jobs.jobAwardedDate != null)
                                                                or ((jobs.jobOppListSize.size() != null and jobs.jobOppListSize.size() != 0) or (jobs.jobQuotListSize.size() != null and jobs.jobQuotListSize.size() != 0) 
                                                                or (jobs.jobEmailListSize.size() != null and jobs.jobEmailListSize.size() != 0) or (jobs.jobCommentListSize.size() != null and
                                                                jobs.jobCommentListSize.size() != 0) or (jobs.jobAttachmentListSize.size() != null and jobs.jobAttachmentListSize.size() != 0) or (empty jobs.jobMergeSummary.jobAdress1 
                                                                and empty jobs.jobMergeSummary.jobcity and empty jobs.jobMergeSummary.jobState and empty jobs.jobMergeSummary.jobZipCode 
                                                                and empty jobs.jobMergeSummary.jobCountryCode and (not empty jobs.jobAdress1 || not empty jobs.jobcity || 
                                                                not empty jobs.jobState || not empty jobs.jobZipCode || not empty jobs.jobCountryCode)))
                                                                ? '' : 'display:none;'}">
                                <div class="ui-sm-12 ui-md-2 ui-lg-2"></div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2"></div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                    <p:commandButton value="Merge" styleClass="btn btn-primary btn-xs" 
                                                     actionListener="#{jobs.mergeJObDetials(jobs)}" 
                                                     oncomplete="PF('jobMergeSumamaryDlgWidget').hide();PF('jobMergeDlgWidget').hide();"/>
                                </div>
                                <!--<div class="ui-sm-12 ui-md-2 ui-lg-2"></div>-->
                                <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                    <p:commandButton styleClass="btn btn-warning  btn-xs" value="Cancel" 
                                                     onclick="PF('jobMergeSumamaryDlgWidget').hide();PF('jobMergeDlgWidget').hide()"/>
                                </div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2"></div>
                                <div class="ui-sm-12 ui-md-2 ui-lg-2"></div>
                            </div>
                        </p:outputPanel>

                    </p:dialog>
                    <script>
                        //09-11-2023 : #12477 : CRM-6248 Jobs: Merge Feature including child objects-->
                        function delayToWaitMergeDate() {
                            setTimeout(testing, 500)

                        }
                        //09-11-2023 : #12477 : CRM-6248 Jobs: Merge Feature including child objects
                        function testing() {
                            var labelElement = document.getElementById("formJobMergeSumamary:jobMergeAwardedDate");
                            // Get the text content of the label
                            var labelText = labelElement.textContent;
                            // Log the label value to the console (or use it as needed)
                            console.log("Label Value: " + labelText);
                            var dateObject = new Date(labelText);

                            // Get only the date part in the desired format (YYYY-MM-DD)
                            var formattedDate = dateObject.toISOString().split('T')[0];

                            // Update the label content with the formatted date
                            var labelElement = document.getElementById("formJobMergeSumamary:jobMergeAwardedDate");
                            labelElement.textContent = formattedDate;
                        }






                    </script>
                </h:form>
            </div>
            <!--//25-09-2023 : #12077 : CRM-7205 Linked documents: inconsistencies all over the place-->
            <ui:include src="../quotes/dialog/LinkedDocDLg.xhtml"/>
            <ui:include src="dlgs/ExistingQuotesDlg.xhtml"/>
            <ui:include src="dlgs/ExistingOppsDlg.xhtml"/>
            <ui:include src="dlgs/JobOppsUpdatedDlg.xhtml"/>
            <ui:include src="dlgs/CloneOppDlg.xhtml"/>
            <ui:include src="dlgs/ISQuoteJobList.xhtml" />
            <ui:include src="dlgs/OasisJobList.xhtml" />   
            <!--6048 CRM-4407: Sales Office Integration: Jobs: Manual Fetch-->
            <ui:include src="dlgs/SalesOfficeJobList.xhtml" />
            <ui:include src="dlgs/ISQuoteJobResolve.xhtml" />
            <!--5380 Oasis: Jobs > Fetch from Oasis-->
            <ui:include src="dlgs/OasisJobResolve.xhtml" />
            <!--<ui:include src="dlgs/AddRelatedCompDlg.xhtml"/>-->
            <!--#8309: CRM-5838  whbsales/jon wiggs/Jobs match to opp and quote are totally broken - poornima - commented LinkedOpportunityDlg and added JobLinkedDocuments-->
            <!--<ui:include src="dlgs/LinkedOpportunityDlg.xhtml"/>-->
            <ui:include src="dlgs/JobLinkedDocuments.xhtml"/><!--
            -->        <p:dialog header="Message" widgetVar="progressDlg123" closable="false" resizable="false" modal="true">       
                <p:outputPanel style="text-align: center" >
                    <br />
                    <h:graphicImage  library="images" name="ajax-loader.gif"    />
                    <p:spacer width="8" />
                    <h:outputLabel value="Processing....." />
                    <br /><br />
                </p:outputPanel>
                <br />
            </p:dialog>
            <script type="text/javascript">
                $(document).ready(function () {
                    var url = window.location.protocol + "//" + window.location.host;
                    $(".page_url").val(url);
                });

                (function () {
                    convertToGoogleMapLink();
                })();

                function convertToGoogleMapLink() {

                    $('.address').each(function () {
                        //Feature #1662:CRM-3286: Strange URL Unsecured
                        var link = "<a href='https://maps.google.com/maps?q=" + encodeURIComponent($(this).text()) + "'>" + $(this).text() + "</a>";

                        console.log($(this).append(link));
                    });
                }
//                9876: 14/12/2022: Feature CRM-6205   ISQuote Aliasing: prefilter for company names in search screens
                function setPreFilter(filterVal) {
                    $('#formCompLookup\\:companyLookUp\\:name\\:filter').val(filterVal);
                    PF('compLookup').filter();
                }
            </script>
            <style>
                #jobDetailsButtonForm\:isquotedateparam_input{
                    padding-top: 1px;
                    width: 76px!important;
                    height: 24px!important;
                }
                /* <!-- #14608 :  CRM-8513   Jobs : Quotes: need a user custom feature "Do not allow user delete quotes"-->  */   
                .label-gray{
                    color:#B7B7B7;
                }
                .ui-chkbox-label{

                }
                .not-allowed{
                    cursor: not-allowed;
                }
                /*//09-11-2023 : #12477 : CRM-6248 Jobs: Merge Feature including child objects*/
                /*                .ui-widget-content, .ui-state-default, .ui-widget-header .ui-state-default {
                                    border: white; 
                                }*/
            </style>
        </div>
        <!--       30-12-2020:Task#2856-CRM-3683: bda-in: Jobs needs a related company delete button -->
        <!-- 31-12-2020:Task#2856-CRM-3683: bda-in: Jobs needs a related company delete button-->
        <h:form id="confirmtionDlg">
            <p:confirmDialog id="cnfrmDlgJobRelComp" header="Confirm Deletion" global="false"  

                             widgetVar="confirmation" >

                <f:facet name="message">
                    <p:outputLabel value="Are you sure to delete the selected record(s)?" style="font-weight: bold" />
                    <br/>
                    <p:outputLabel value="Note:" style="font-weight: bold; color:blue" />
                    <!--                     01-01-2021:Task#2856-CRM-3683: bda-in: Jobs needs a related company delete button-->
                    <p:outputLabel value="Selected companies not linked to #{custom.labels.get('IDS_OPP')} or quote will be unlinked from #{custom.labels.get('IDS_JOB')}." style="color:blue"/>
                </f:facet>
                <div align="center">
                    <p:commandButton id="btnYes" value="Yes"  actionListener="#{viewJobsRelatedComps.deleteRelComps(jobs.jobId)}" 
                                     styleClass="btn btn-success  btn-xs"   process="@this" update=":jobDetailsButtonForm:tabviewJobs:reldCompDT" />
                    <p:spacer width="4px"/>
                    <p:commandButton id="btnNo" value="No"   styleClass="btn btn-danger  btn-xs"
                                     onclick="PF('confirmation').hide()" 
                                     />
                </div>




                <p:spacer width="10"/>
            </p:confirmDialog>

        </h:form>
        <!--CRM-8441 : Jobs: Allow Deleting Links to other modules in Link Doc Tab-->
        <p:confirmDialog header="Confirmation" global="true" severity="alert"   showEffect="fade" hideEffect="fade" 
                             widgetVar="unlinkDlg1" id="unlinkMsg1" responsive="true"
                             message="Are you sure to unlink?">
                        <h:form id="unlinkWarning1">
                            <div align="center">
                                <!--Task #12780: CRM-7582 Direct commission: 3 way Customer Splits   by harshithad on 10/01/2023-->
                                <p:commandButton value="Yes" id="btnUnlinkYes"
                                                 actionListener="#{linkedDocService.unLinkJobs()}"  
                                                 styleClass="btn btn-success  btn-xs"  process="@this" 
                                                 oncomplete="PF('unlinkDlg1').hide()" update="jobDetailsButtonForm:tabviewJobs:jobLinkedDocs"
                                                 >
                                </p:commandButton>
                                <p:spacer width="4px"/>
                                <p:commandButton value="No" styleClass="btn btn-danger  btn-xs" id="btnUnlinkNo"
                                                 onclick="PF('unlinkDlg1').hide()" 
                                                 type="button" />
                            </div>
                        </h:form>
                    </p:confirmDialog>
        <ui:include src="/lookup/CompanyLookupDlg.xhtml"/>
        <!--1989 Jobs: Owner Company and Contact-->
        <ui:include src="/lookup/ContactLookupDlg.xhtml"/>
        <!--1933 CRM-3372: jobs: Southwest: mappings missing for jobs-->
        <ui:include src="/lookup/UserLookupDlg.xhtml"/>
        <!--13307: 08/03/2024: CRM-7828   Jobs: Add check boxes to add companies in "Related" tab-->
        <ui:include src="dlgs/compLookUpForMultiSelect.xhtml"/>
        <!--13-03-2024 13234 Code Optimisation for Oasis Integration Quotes and Jobs : Manual fetch-->
        <ui:include src="dlgs/OasisJobsList.xhtml"/>
        <ui:include src="dlgs/OasisCompanyResolve.xhtml"/>
        <ui:include src="../../resources/dialogs/DlgFetchAndProc.xhtml" />
    </ui:define>
</ui:composition>
