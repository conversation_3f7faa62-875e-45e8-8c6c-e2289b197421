<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"> 
    <p:dataTable value="#{jobs.statusList}" rendered="#{jobs.statusList.size()>0}"
                 var="errRec" id="er" rowIndexVar="erIndex"
                 emptyMessage="No errors" 
                 scrollable="true"
                 scrollHeight="300" filteredValue="#{jobs.statusList}"
                 widgetVar="errRec">











<!--
        <f:facet name="header" >
             
                    <h:commandLink title="Excel">
                        <p:graphicImage name="/images/excel.png" width="24"/>
                        <p:dataExporter type="xls" target="er" fileName="Error_List" />
                    </h:commandLink>
                    <p:spacer width="4"/>
                    <h:commandLink title="CSV">
                        <p:graphicImage name="/images/csv.png" width="24"/>
                        <p:dataExporter type="csv" target="er" fileName="Error_List" />
                    </h:commandLink>
                 

        </f:facet>


-->














        <p:column  width="2%"> 
            <f:facet name="header"></f:facet>
                #{erIndex+1}
        </p:column>
        <p:column  width="2%"> 
            <f:facet name="header"></f:facet>
            <p:graphicImage name="/images/greenFlag.png" width="12" rendered="#{errRec[24]=='Success'}" title="#{errRec[24]}"/>
            <p:graphicImage name="/images/redFlag.png" width="12" rendered="#{errRec[24]=='Failed'}" title="#{errRec[24]}"/>
        </p:column>

        <p:column width="10%">
            <!--Jira Task #14108 ESCALATION CRM-8238 : JOBS:  add "Download Template" to Jobs page-->
            <f:facet name="header">#{custom.labels.get('IDS_JOB')} Name</f:facet>
            <h:outputText value="#{errRec[0]}"/>
        </p:column>
        <p:column width="10%">
            <!--Jira Task #14108 ESCALATION CRM-8238 : JOBS:  add "Download Template" to Jobs page-->
            <f:facet name="header">#{custom.labels.get('IDS_JOB_TYPE')}</f:facet>
            <h:outputText value="#{errRec[1]}"/>
        </p:column>
        <p:column width="10%">
            <!--Jira Task #14108 ESCALATION CRM-8238 : JOBS:  add "Download Template" to Jobs page-->
            <f:facet name="header"> #{custom.labels.get('IDS_JOB_CATEGORY')}</f:facet>
            <h:outputText value="#{errRec[2]}"/>
        </p:column>
        <p:column width="10%">
            <!--Jira Task #14108 ESCALATION CRM-8238 : JOBS:  add "Download Template" to Jobs page-->
            <f:facet name="header"> #{custom.labels.get('IDS_JOB_SPECIFIER')}</f:facet>
            <h:outputText value="#{errRec[3]}"/>
        </p:column>
        <p:column width="10%">
            <!--Jira Task #14108 ESCALATION CRM-8238 : JOBS:  add "Download Template" to Jobs page-->
            <f:facet name="header">#{custom.labels.get('IDS_JOB_STAGE')}</f:facet>
            <h:outputText value="#{errRec[4]}"/>
        </p:column>
        <p:column width="10%">
            <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
            <f:facet name="header">#{custom.labels.get('IDS_JOB_VALUE')}</f:facet>
            <h:outputText value="#{errRec[5]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
            <f:facet name="header">Street Address</f:facet>
            <h:outputText value="#{errRec[6]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
            <f:facet name="header">City</f:facet>
            <h:outputText value="#{errRec[7]}"/>
        </p:column>
        <p:column width="10%">
            <!--Jira Task #14108 ESCALATION CRM-8238 : JOBS:  add "Download Template" to Jobs page-->
            <f:facet name="header"> #{custom.labels.get('IDS_STATE')}</f:facet>
            <h:outputText value="#{errRec[8]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
         <!--Jira Task #14108 ESCALATION CRM-8238 : JOBS:  add "Download Template" to Jobs page-->
            <f:facet name="header">#{custom.labels.get('IDS_ZIP')}</f:facet>
            <h:outputText value="#{errRec[9]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
            <f:facet name="header">Country</f:facet>
            <h:outputText value="#{errRec[10]}"/>
        </p:column>
        <p:column width="10%" >
            <!--Jira Task #14108 ESCALATION CRM-8238 : JOBS:  add "Download Template" to Jobs page-->
            <f:facet name="header">#{custom.labels.get('IDS_JOB')} No.</f:facet>
            <h:outputText value="#{errRec[11]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
         <!--Jira Task #14108 ESCALATION CRM-8238 : JOBS:  add "Download Template" to Jobs page-->
            <f:facet name="header">#{custom.labels.get('IDS_JOB')} URL</f:facet>
            <h:outputText value="#{errRec[12]}"/>
        </p:column>
        <p:column width="10%">
            <!--Jira Task #14108 ESCALATION CRM-8238 : JOBS:  add "Download Template" to Jobs page-->
            <f:facet name="header">#{custom.labels.get('IDS_JOB')} Owner</f:facet>
            <h:outputText value="#{errRec[13]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
            <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
            <f:facet name="header">#{custom.labels.get('IDS_JOB_BID_DATE')}</f:facet>
            <h:outputText value="#{errRec[14]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
            <!--            23-01-2021:Task# #3131-CRM-3794: jobs edit  label -  Order Date-->
            <f:facet name="header">#{custom.labels.get('IDS_JOB_ORDER_DATE')}</f:facet>
            <h:outputText value="#{errRec[15]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
            <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
            <f:facet name="header">#{custom.labels.get('IDS_JOB_FOLLOWUP')}</f:facet>
            <h:outputText value="#{errRec[16]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
            <f:facet name="header">Awarded to</f:facet>
            <h:outputText value="#{errRec[17]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
            <f:facet name="header">Awarded Date</f:facet>
            <h:outputText value="#{errRec[18]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
            <f:facet name="header">Notes</f:facet>
            <h:outputText value="#{errRec[19]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
            <f:facet name="header">Created By</f:facet>
            <h:outputText value="#{errRec[20]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
            <f:facet name="header">Creation Timestamp</f:facet>
            <h:outputText value="#{errRec[21]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
            <f:facet name="header">Last Modified By</f:facet>
            <h:outputText value="#{errRec[22]}"/>
        </p:column>
        <p:column width="10%" rendered="false">
            <f:facet name="header">Last Modified Timestamp</f:facet>
            <h:outputText value="#{errRec[23]}"/>
        </p:column>
        <!--        <p:column width="10%">
                    <f:facet name="header">Status</f:facet>
                    <h:outputText value="#{errRec[24]}"/>
                </p:column>-->
        <p:column width="50%">
            <f:facet name="header">Details</f:facet>
            <h:outputText value="#{errRec[25]}"/>
        </p:column>

    </p:dataTable>

    <!--</p:panelGrid>-->
</ui:composition>

