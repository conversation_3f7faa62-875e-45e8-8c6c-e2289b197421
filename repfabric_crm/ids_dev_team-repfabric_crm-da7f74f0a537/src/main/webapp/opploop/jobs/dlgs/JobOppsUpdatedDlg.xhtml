<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: JobOppsUpdateDlg.xhtml
// Author: Sharvani
//*********************************************************
* Copyright (c) 2016 Indea Design Systems, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:f="http://java.sun.com/jsf/core">

    <p:dialog widgetVar="jobOppsUpdateDlg" header="Update Selected" modal="true" resizable="false" width="530"  height="550"
              maximizable="true" closeOnEscape="true"> 
        <h:form id="jobsUpdate">
            <!--//              #10835  CRM-6741  Opportunities: error with closing state-->
            <h:inputHidden id="hiddenOppStatus" value="#{jobOpps.hiddenOppStatus}" />
            <p:panelGrid styleClass="box-primary no-border ui-fluid" layout="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" style="min-height: 100px"   >
                <p:outputLabel value="#{custom.labels.get('IDS_PROGRAM')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->      
                <p:inputText value="#{jobOpps.opp.oppCustProgram}" 
                             style="width: 100%"
                             id="opcustprgm"
                             maxlength="120" />
                <!-- 14-12-2020: TASK#2567-CRM-3546: bulk update for jobs did not change stage -->
                <!--ESCALATIONS CRM-8428   Jobs: Cannot Change Opportunity Stage from Job if not using default Opportuni-->
                <p:outputLabel value="#{custom.labels.get('IDS_ACTIVITY')}" title="#{custom.labels.get('IDS_PRINCIS')} with custom #{custom.labels.get('IDS_ACTIVITY')} cannot be updated. Only  #{custom.labels.get('IDS_PRINCIS')} with default #{custom.labels.get('IDS_ACTIVITY')} will be updated." />
                <!--<p:outputLabel styleClass="m" value=":" />-->               
<!--ESCALATIONS CRM-8428   Jobs: Cannot Change Opportunity Stage from Job if not using default Opportuni-->
                <p:selectOneMenu style="width: 100%" disabled="#{jobOpps.isDisabled eq true}" widgetVar="wopselact" id="opselact" value="#{jobOpps.opp.selActivity}"   >
                    <f:selectItem itemValue="" itemLabel="Select #{custom.labels.get('IDS_ACTIVITY')}"/>
                    <f:selectItems value="#{oppActivitiesMst.listOppActivities1}"  
                                   var="act"   
                                   itemValue="#{act.recId}"
                                   itemLabel="#{act.actName}"/>
                    <!--//25-10-2024 : #14398 : ESCALATIONS CRM-8430   Jobs: Updating Opportunity Stage to a Stage with a Close Status Does Not Clos-->
                    <p:ajax process="@this" listener="#{jobOpps.activityChanged}" update="opstat1 hiddenOppStatus claCLoseDateLabel calClose calJobOppStatus txtcloseReason"/>
                </p:selectOneMenu>  
<!--#13178 ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
                <p:outputLabel value="#{custom.labels.get('IDS_ACT_STATUS')}" />
                <!--<p:outputLabel styleClass="m" value=":" />-->
               <!--#13178 ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
<!--ESCALATIONS CRM-8428   Jobs: Cannot Change Opportunity Stage from Job if not using default Opportuni-->
               <p:selectOneMenu widgetVar="wopstat" disabled="#{jobOpps.isDisabled eq true}" id="opstat1" style="width: 100%" value="#{jobOpps.opp.oppStatus}" >
                    <f:selectItem itemValue="" itemLabel="Select #{custom.labels.get('IDS_ACT_STATUS')}"/>
                    <f:selectItems value="#{jobOpps.statusList}"  
                                   var="status"   
                                   itemValue="#{status}"
                                   itemLabel="#{status}"/>
                </p:selectOneMenu>

                <p:outputLabel value="#{custom.labels.get('IDS_FOLLOW_UP')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:calendar  id="calopfollow"  pattern="#{globalParams.dateFormat}"
                             size="10"  widgetVar="wopfollow"
                             converterMessage="#{custom.labels.get('IDS_FOLLOW_UP')} date not valid" 
                             value="#{jobOpps.opp.oppFollowUp}"  >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:calendar>

                <p:outputLabel value="#{custom.labels.get('IDS_PRIORITY')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:spinner min="1" max="10"    maxlength="2"  
                           size="8"  
                           id="oppriority"
                           value="#{jobOpps.opp.oppPriority}">
                </p:spinner>

                <p:outputLabel value="#{custom.labels.get('IDS_POTENTIAL')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:spinner min="1" max="100"   maxlength="3" 
                           size="8"
                           id="oppotential"
                           value="#{jobOpps.opp.oppPotential}">

                </p:spinner>

                <p:outputLabel value="#{custom.labels.get('IDS_EAU')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:inputText     
                    value="#{jobOpps.opp.oppEau}" 
                    maxlength="10"
                    size="10" 
                    id="opeau">
                    <f:convertNumber  type="number" integerOnly="true"/>

                </p:inputText>
                <!--Feature #5025 RE: Repfabric / AVX CRMSync / Relabeling Fields / "Next Step" (Pending)-->
                <p:outputLabel value="#{custom.labels.get('IDS_NEXT_STEP')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
                <p:inputText value="#{jobOpps.opp.oppNextStep}"
                             id="opnext" maxlength="250" style="width: 100%"/>

                <p:outputLabel value="#{custom.labels.get('IDS_OPP_DESC')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:inputTextarea rows="3" cols="30" value="#{jobOpps.jobOppCommText}" id="oppcmnt" style="width: 100%"/>

                <p:outputLabel value="#{custom.labels.get('IDS_OPP')} Comments"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:inputTextarea rows="3" cols="30" value="#{jobOpps.jobOppRptCmnts}" id="oppRptcmnt" style="width: 100%"/>
                <!--12983: 31/01/2024: ESCALATIONS CRM-7705   Jobs / opportunities updating comments-->
                <p:outputLabel  value="" />
                <p:selectBooleanCheckbox value="#{jobOpps.isOverwrite}" itemLabel="Overwrite existing" />
                 
                <p:outputLabel value="#{custom.labels.get('IDS_VALUE')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:inputText   value="#{jobOpps.opp.oppValue}" id="opval"
                               size="10"  converterMessage="not a number"   
                               maxlength="14">
                    <f:convertNumber   groupingUsed="true"                                                                                      
                                       maxIntegerDigits="15"  
                                       minFractionDigits="2" maxFractionDigits="2" />
                </p:inputText>


                <p:outputLabel value="#{custom.labels.get('IDS_PROTO_DATE')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:calendar  id="opproto" value="#{jobOpps.opp.oppProtoDate}"
                             size="10" widgetVar="wopproto"
                             converterMessage="#{custom.labels.get('IDS_PROTO_DATE')} date not valid" 
                             pattern="#{globalParams.dateFormat}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>


                <p:outputLabel value="#{custom.labels.get('IDS_PRODUCTION_DATE')}"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:calendar  id="opprod" value="#{jobOpps.opp.oppProdDate}"  
                             size="10" widgetVar="wopprod"
                             converterMessage="#{custom.labels.get('IDS_PRODUCTION_DATE')} not valid" 
                             pattern="#{globalParams.dateFormat}"  >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>

                <p:outputLabel value="Status"/>
                <!--<p:outputLabel styleClass="m" value=":" />-->
<!--                <h:selectOneRadio style="font-size:14px;" value="#{jobOpps.joboppStatus}" >
                    <f:selectItem itemLabel="Open" itemValue="0"/>
                    <f:selectItem itemLabel="Close" itemValue="2"/>
                    <p:ajax event="change" listener="#{jobOpps.updateCloseStatus()}"   update="calClose txtcloseReason"/>
                </h:selectOneRadio>-->
<!--//              #10835  CRM-6741  Opportunities: error with closing state-->
                <!--//25-10-2024 : #14398 : ESCALATIONS CRM-8430   Jobs: Updating Opportunity Stage to a Stage with a Close Status Does Not Clos-->
                <p:selectOneMenu  value="#{jobOpps.joboppStatus}" id="calJobOppStatus">     
                   <f:selectItem   itemLabel="Select Close Status"  itemValue="0"/>
                    <f:selectItem itemLabel="Open" itemValue="1"/>
                    <f:selectItem itemLabel="Won" itemValue="2"/>
                    <f:selectItem itemLabel="Lost" itemValue="3"/>
                     <f:selectItem itemLabel="Cancelled" itemValue="4"/>
                    <!--//25-10-2024 : #14398 : ESCALATIONS CRM-8430   Jobs: Updating Opportunity Stage to a Stage with a Close Status Does Not Clos-->
                    <p:ajax event="change" listener="#{jobOpps.updateCloseStatus()}"   update="hiddenOppStatus calClose txtcloseReason claCLoseDateLabel" />
                </p:selectOneMenu> 
                <!--//25-10-2024 : #14398 : ESCALATIONS CRM-8430   Jobs: Updating Opportunity Stage to a Stage with a Close Status Does Not Clos-->
                <p:outputLabel id="claCLoseDateLabel" class="#{jobOpps.joboppStatus gt 1 ? 'required' : ''}" value="Close Date"></p:outputLabel>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <!--//25-10-2024 : #14398 : ESCALATIONS CRM-8430   Jobs: Updating Opportunity Stage to a Stage with a Close Status Does Not Clos-->
                <p:calendar id="calClose" value="#{jobOpps.opp.oppCloseDate}"  size="10" widgetVar="wopclose" disabled="#{jobOpps.closeFlag}"
                            converterMessage="Close Date not valid" pattern="#{globalParams.dateFormat}" placeholder="#{globalParams.dateFormat}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>

                <p:outputLabel value="Reason"></p:outputLabel>
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:inputText value="#{jobOpps.opp.oppCloseReason}" style="width: 98%" disabled="#{jobOpps.closeFlag}" id="txtcloseReason">
                </p:inputText>


            </p:panelGrid>

            <p:spacer width="4 px"/>
            <!--SONARQUBE ISSUES - sharvani - 23-01-2020-->
            <!--<center>-->
            <div align="center">
                <!--12983: 31/01/2024: ESCALATIONS CRM-7705   Jobs / opportunities updating comments-->
                <p:commandButton value="Update" widgetVar="updBtn" class="btn btn-success btn-xs"  actionListener="#{jobOpps.updateBulkOpps(jobs.jobId)}" onclick="PF('updBtn').disable();" update=":jobDetailsButtonForm:tabviewJobs:dtOppList :jobDetailsButtonForm:tabviewJobs:dtCmnt :jobDetailsButtonForm:growlJobSave" oncomplete="PF('updBtn').enable(); loadJobOpps();"/>
                <p:spacer width="4px" />
                <p:commandButton value="Cancel" styleClass="btn btn-warning btn-xs" type="button"  onclick="PF('jobOppsUpdateDlg').hide()" />
            </div>
            <!--</center>-->
            <br/>
            <p:outputLabel value="Note: " style="font-weight: bold"/>
            <p:outputLabel value="Only the updated fields will be saved."/>
            <!-- 01-12-2020:TASK #2567 CRM-3546: bulk update for jobs did not change stage-->
<!--ESCALATIONS CRM-8428   Jobs: Cannot Change Opportunity Stage from Job if not using default Opportuni-->
            <!--<p:outputLabel value="** #{custom.labels.get('IDS_PRINCIS')} with custom #{custom.labels.get('IDS_ACTIVITY')} cannot be updated. Only  #{custom.labels.get('IDS_PRINCIS')} with default #{custom.labels.get('IDS_ACTIVITY')} will be updated."/>-->

        </h:form>
    </p:dialog>
</ui:composition>

