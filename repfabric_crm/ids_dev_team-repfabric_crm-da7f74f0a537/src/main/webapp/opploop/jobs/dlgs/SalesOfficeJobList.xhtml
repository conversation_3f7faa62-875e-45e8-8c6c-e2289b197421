<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
   <!--6048 CRM-4407: Sales Office Integration: Jobs: Manual Fetch-->
    <p:dialog  styleClass="dialogCSS" id="isQuotJobListDtlsx2" widgetVar="isQuotJobListDtls2"  responsive="true" draggable="false" resizable="false"
               style="text-overflow: scroll;"  height="80%" width="80%" modal="true">
        <f:facet name="header">
            <h:outputText id="isQuotListTxt2" value="Sales Office Job List" />
        </f:facet>
        <h:form id="isQfrm5"> 
            <!--1935 CRM-3373: jobs: southwest: fetching jobs not reinitializing #1-->
            <p:outputLabel   id="dtIsQtLbl2" value="Jobs for : " style="font-weight: bold;" /><p:spacer/>
            <p:outputLabel rendered="#{jobs.filterISQuotesStatus!=2}" id="dtIsQt2" value="#{jobs.paramISQuotDate}" style="font-weight: bolder;" >
                <f:convertDateTime pattern="#{globalParams.dateFormat}" />  
            </p:outputLabel>
            <p:outputLabel rendered="#{jobs.filterISQuotesStatus==2}" id="dtIsQts2" value="#{jobs.filterISQuotes}" style="font-weight: bolder;" />
            <p:spacer width="10"/>
            <!--4872 CRM Sync: Settings for Oasis-->
            <p:selectBooleanCheckbox  id="updNrmlLnItmReq2" style="text-align: right;"
                                             itemLabel="Pull related records" 
                                             value="#{jobs.updateISQutesQuoteDtls}" disabled="true" >
                <p:ajax event="change" listener="#{jobs.onChangeChckBox(jobs.updateISQutesQuoteDtls)}"/>
            </p:selectBooleanCheckbox>
           
           
            
            <p:dataTable value="#{iSQuoteCrmClient.jobLookUp}" rows="10"
                         var="isqHd2" id="isq3" rowIndexVar="isqRecc2" 
                         emptyMessage="No Jobs available" 
                         scrollable="true" scrollHeight="360" paginator="true" 
                         selection="#{jobs.selectedOasisJob}"
                         selectionMode="single" 
                         rowKey="#{isqHd2.jobIdForCrm}"
                         >
                <!--5802  Jobs: duplicate prevention for jobs-->
                <p:ajax event="rowSelect" listener="#{jobs.selectSalesOfficeJob}"
                        process="@this" immediate="true" update=":jobDetailsButtonForm"  oncomplete="" />
 
                <p:column  width="2%"> 
                    <f:facet name="header"></f:facet>
                        #{isqRecc2+1}
                </p:column> 
                <p:column width="10%" >
                    <f:facet name="header">Job No.</f:facet>
                        #{isqHd2.jobIdForCrm}  
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Job Name</f:facet>
                        #{isqHd2.jobDescr==null?'':isqHd2.jobDescr} 
                </p:column>
                <p:column width="10%" rendered="false">
                    <f:facet name="header">Job Specifier</f:facet>
                        #{isqHd2.jobSpecifierName==null?'':isqHd2.jobSpecifierName} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Location</f:facet>
                        #{isqHd2.jobLocation==null?'':isqHd.jobLocation} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Job Notes</f:facet>
                        #{isqHd2.jobNotes==null?'':isqHd2.jobNotes} 
                </p:column>
<!--               jb.setJobDescr(rs.getString(3));
                jb.setJobNotes(rs.getString(7));
                jb.setJobLocation <p:column width="10%" >
                    <f:facet name="header">Job City</f:facet>
                        #{isqHd.jobcity==null?'':isqHd.jobcity} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Est. Job Value</f:facet>
                        #{isqHd.jobEstValue==null?'':isqHd.jobEstValue} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Date Changed</f:facet> 
                    <h:outputLabel   value="#{rFUtilities.convertFromUTC(isqHd.updDate)}" >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" for="isqHd.updDate"/>
                    </h:outputLabel>
                </p:column> -->
            </p:dataTable>

        </h:form>
    </p:dialog>
</ui:composition>



