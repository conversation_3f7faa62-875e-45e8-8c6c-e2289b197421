<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core">
<!--//8628: 10/08/2022 : CRM-5934  Job overview: Advanced search filter on salesteam not working: ranvier-->
<p:dialog widgetVar="advancedSearch" header="Advanced Search" modal="true" resizable="true" class="dialogCSS" responsive="true" height="350" width="550"> 
        <h:form id="advancedSearchForm">
            <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                 style="min-height: 100px"       layout="grid" styleClass="box-primary no-border ui-fluid">
                <p:outputLabel value="#{custom.labels.get('IDS_JOB')} Name" />
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:inputText value="#{viewJobs.jobDescr}">

                </p:inputText>

                <p:outputLabel value="Company" />
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:inputText  value="#{viewJobs.jobSpecifierName}">

                </p:inputText>
                <!--861 CRM-2699: Jobs List view: changes-->
                <!--2980 Jobs: UI and Label Updates for Owner and Notify by Email-->
                <p:outputLabel value="Sales Person" />
                <!--<p:outputLabel styleClass="m" value=":" />-->
<!--                <p:inputText  value="#{viewJobs.jobOwnerName}">
//8628: 10/08/2022 : CRM-5934  Job overview: Advanced search filter on salesteam not working: ranvier
                </p:inputText>-->
<!--                <p:selectOneMenu value="#{viewJobs.jobOwner}" id="jobOwner" style="width: 100%" >
                    <f:selectItem itemLabel="[Select]" itemValue=""/>
                    <f:selectItems value="#{users.dependentUserList}" var="user" 
                                   itemValue="#{user.userId}" itemLabel="#{user.userName}" />
                //8628: 10/08/2022 : CRM-5934  Job overview: Advanced search filter on salesteam not working: ranvier
</p:selectOneMenu>-->
                <p:selectCheckboxMenu value="#{viewJobs.jobOwnerList}" label="Cities" style="width: 32rem; height: auto;" multiple="true"
                                  filter="true" filterMatchMode="contains" panelStyle="width: 30rem" scrollHeight="250" 
                                  emptyLabel="Please select..." updateLabel="true" dynamic="true">
                        <f:selectItems value="#{users.dependentUserList}" itemValue="#{user.userId}" var="user"  itemLabel="#{user.userName}"/>

                </p:selectCheckboxMenu>
                <!--14364 28/08/2024 CRM-8380   Add the ability to filter/search jobs by the quote owner of the quote that is linked to t-->
                <p:outputLabel value="Linked Quote Owners" />
                <p:selectCheckboxMenu value="#{viewJobs.jobQuotOwnerList}" label="Cities" style="width: 32rem; height: auto;" multiple="true"
                                  filter="true" filterMatchMode="contains" panelStyle="width: 30rem" scrollHeight="250" 
                                  emptyLabel="Please select..." updateLabel="true" dynamic="true">
                        <f:selectItems value="#{users.dependentUserList}" itemValue="#{user.userId}" var="user"  itemLabel="#{user.userName}"/>
                </p:selectCheckboxMenu>
                
                <p:outputLabel value="#{custom.labels.get('IDS_JOB_STAGE')}" />
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:selectOneMenu style="width:100%" id="smjobActivity" value="#{viewJobs.jobActivity}" >
                    <f:selectItem itemValue="0" itemLabel="All"/>
                    <f:selectItems value="#{jobActivitiesMst.getOppActivity()}"  
                                   var="act"   
                                   itemValue="#{act.recId}"
                                   itemLabel="#{act.actName}"/>
                </p:selectOneMenu>

                
                <p:outputLabel value="Street"  />
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:inputText value="#{viewJobs.jobAdress1}">

                </p:inputText>


                <p:outputLabel value="City"  />
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:inputText  value="#{viewJobs.jobcity}">

                </p:inputText>
    
<!--Bug #2113: #2088: Pending Issues  : JOBS: State and ZIP- Custom fields-->
                <p:outputLabel value="#{custom.labels.get('IDS_STATE')}"  />
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:inputText value="#{viewJobs.jobState}">

                </p:inputText>
   <!--Bug #2113: #2088: Pending Issues  : JOBS: State and ZIP- Custom fields-->
              
                <p:outputLabel value="#{custom.labels.get('IDS_ZIP')}"  />
                <!--<p:outputLabel styleClass="m" value=":" />-->
                <p:inputText  value="#{viewJobs.jobZipCode}">

                </p:inputText>
            </p:panelGrid>
            <p:spacer width="4px"/>
            <!--<center>-->
            <!--SONARQUBE ISSUES - sharvani - 23-01-2020-->
            <div align="center">
                <!--893 Jobs > Customizable Columns-->
                 <!--//#13180 ESCALATION CRM-7806 Thea Web - Slow-->
                 <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                <p:commandButton value="Filter"  styleClass="btn btn-primary btn-xs"   actionListener="#{jobsLazyDataModel.advancedSearch(viewJobs)}" update=":jobsForm:tblJobs" oncomplete="PF('tblJob1').clearFilters();applyTopScrollbarJob()"/>
                <p:spacer width="4px"/>
                <p:commandButton type="button" value="Cancel" styleClass="btn btn-warning btn-xs" onclick="PF('advancedSearch').hide()"/>
                <p:spacer width="4px"/>
                <!--//20-08-2024 : #14308 : ESCALATION CRM-8209   Contacts: Scroll bar needs to be at the top-->
                <p:commandButton value="Clear" styleClass="btn btn-danger btn-xs" actionListener="#{viewJobs.populateAdvancedSearchFields()}" update=":advancedSearchForm" oncomplete="PF('tblJob1').clearFilters();applyTopScrollbarJob()"/>
            </div>
                <!--</center>-->
            <br/>
        </h:form>
    </p:dialog>
<!--//8628: 10/08/2022 : CRM-5934  Job overview: Advanced search filter on salesteam not working: ranvier-->
    <style>
    .ui-selectcheckboxmenu-multiple-container{

            height: auto !important;

        }
</style>
</ui:composition>

