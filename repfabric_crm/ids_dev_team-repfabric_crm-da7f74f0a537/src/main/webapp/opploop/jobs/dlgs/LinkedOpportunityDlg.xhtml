<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: AddRelatedCompDlg.xhtml
// Author: Seema
//*********************************************************
* Copyright (c) 2016 Indea Design Systems, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core">

    <p:dialog widgetVar="linkedOppDlg" header="Linked #{custom.labels.get('IDS_OPP')}" modal="true" resizable="false"> 
        <h:form id = "linkedOppForm">
            <p:dataTable id="dtOpp" widgetVar="oppTable" value="#{jobOpps.oppList}" var = "opp" selectionMode="single" rowKey="#{opp.oppId}" selection="#{jobOpps.selectedOppLst}" 
                         emptyMessage="No open #{custom.labels.get('IDS_OPPS')} found." paginator="true" rows="10" filteredValue="#{jobOpps.filteredOppList}"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="5,10,15" paginatorAlwaysVisible="false" class="tblmain">

                <p:column filterBy="#{opp.getCompNameById(opp.oppCustomer)}" filterMatchMode="contains" sortBy="#{opp.getCompNameById(opp.oppCustomer)}" >
                    <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet> 
                        #{opp.getCompNameById(opp.oppCustomer)}
                </p:column>

                <p:column filterBy="#{opp.getCompNameById(opp.oppPrincipal)}" filterMatchMode="contains" sortBy="#{opp.getCompNameById(opp.oppPrincipal)}">
                    <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet> 
                        #{opp.getCompNameById(opp.oppPrincipal)}
                </p:column>
                <p:column filterBy="#{opp.getCompNameById(opp.oppDistri)}" filterMatchMode="contains" sortBy="#{opp.getCompNameById(opp.oppDistri)}">
                    <f:facet name="header">#{custom.labels.get('IDS_DISTRI')}</f:facet> 
                        #{opp.getCompNameById(opp.oppDistri)}
                </p:column>

                <p:column filterBy="#{opp.oppCustProgram}" filterMatchMode="contains" sortBy="#{opp.oppCustProgram}">
                    <f:facet name="header">#{custom.labels.get('IDS_PROGRAM')}</f:facet> 
                        #{opp.oppCustProgram}
                </p:column> 

                <p:column filterBy="#{opp.oppActivity}" filterMatchMode="contains" sortBy="#{opp.oppActivity}">
                    <f:facet name="header">#{custom.labels.get('IDS_ACTIVITY')}</f:facet>  
                        #{opp.oppActivity}
                </p:column>
                <p:column filterBy="#{opp.oppStatus}" filterMatchMode="contains" sortBy="#{opp.oppStatus}">
                    <f:facet name="header">Status</f:facet>  
                        #{opp.oppStatus}
                </p:column>
                <p:column filterBy="#{opp.oppFollowUp}" filterMatchMode="contains" sortBy="#{opp.oppFollowUp}">
                    <f:facet name="header">#{custom.labels.get('IDS_FOLLOW_UP')}</f:facet> 
                    <p:outputLabel value="#{opp.oppFollowUp}" >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                    </p:outputLabel>
                </p:column>
                <!--Feature #5025 RE: Repfabric / AVX CRMSync / Relabeling Fields / "Next Step" (Pending)-->
                <!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
                <p:column filterBy="#{opp.oppNextStep}" filterMatchMode="contains" sortBy="#{opp.oppNextStep}" styleClass="might-overflow">
                    <f:facet name="header">#{custom.labels.get('IDS_NEXT_STEP')}</f:facet> 
                        #{opp.oppNextStep}
                </p:column>
                <p:column  filterMatchMode="contains" sortBy="#{opp.oppCloseStatus}">
                    <f:facet name="header">Status</f:facet> 
                    <p:outputLabel value="#{opp.oppCloseStatus==0?'Open':'Closed'}" />
                </p:column>
            </p:dataTable>
        </h:form>
    </p:dialog>
    
</ui:composition>

