<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: JobLinkedDocuments.xhtml
// Author: Poornima
//*********************************************************
/*
*
*/-->

<!--#8309: CRM-5838  whbsales/jon wiggs/Jobs match to opp and quote are totally broken - poornima-->

<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:adm="http://github.com/adminfaces"
                > 
    
    <p:dialog id="poLinkedDocumentsDlg" header="Linked Documents" width="1000px"
              widgetVar="dlgJobLinkedDocs"  modal="true" responsive="true" styleClass="dialogCSS">
        <h:form id="frmJobLinkedDocs">
            <p:dataTable id="dtJobLinkedDocs" value="#{jobLinkedDocumentService.jobLinkedDocLst}" 
                         var="lnkdDoc"
                         rows="10" 
                         selectionMode="single" 
                         filterEvent="keyup" 
                         draggableColumns="true"
                         emptyMessage="No Linked Documents found."  
                         paginator="true" 
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         paginatorAlwaysVisible="false" 
                         class="tblmain"
                         rowKey="#{lnkdDoc.recId}" resizableColumns="true"
                         >
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:column headerText="#{custom.labels.get('IDS_JOB_TYPE')}" filterBy="#{lnkdDoc.docType}" filterMatchMode="contains" sortBy="#{lnkdDoc.docType}">
                    <p:commandLink rendered="#{lnkdDoc.docType eq 'Quote'}"  value="#{lnkdDoc.docType}"
                                   onclick="window.open('../../opploop/quotes/NewQuote.xhtml?recId=#{lnkdDoc.docId}', '_blank');" 
                                   >
                    </p:commandLink>

                    <p:commandLink rendered="#{lnkdDoc.docType eq 'Opportunity'}"  value="#{lnkdDoc.docType}"
                                   onclick="window.open('../../opploop/opportunity/OpportunityView.xhtml?opid=#{lnkdDoc.docId}', '_blank');"
                                   >
                    </p:commandLink>

                </p:column>
                <p:column headerText="Doc. No." filterBy="#{lnkdDoc.docNo}" filterMatchMode="contains" sortBy="#{lnkdDoc.docNo}">
                    <h:outputText value="#{lnkdDoc.docNo}" />
                </p:column>
                <p:column headerText="Doc. Date" filterBy="#{oppLinkedDocumentsService.getFormattedDate(lnkdDoc.docDate, 'da')}" filterMatchMode="contains" sortBy="#{lnkdDoc.docDate}">
                    <h:outputText value="#{oppLinkedDocumentsService.getFormattedDate(lnkdDoc.docDate, 'da')}" />
                </p:column>
                <p:column headerText="Topic" filterBy="#{lnkdDoc.docTopic}" filterMatchMode="contains" sortBy="#{lnkdDoc.docTopic}">
                    <h:outputText value="#{lnkdDoc.docTopic}" />
                </p:column>
                <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{lnkdDoc.docCustomer}" filterMatchMode="contains" sortBy="#{lnkdDoc.docCustomer}">
                    <h:outputText value="#{lnkdDoc.docCustomer}" />
                </p:column>
                <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{lnkdDoc.docPrinci}" filterMatchMode="contains" sortBy="#{lnkdDoc.docPrinci}">
                    <h:outputText value="#{lnkdDoc.docPrinci}" />
                </p:column>
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:column headerText="#{custom.labels.get('IDS_JOB_VALUE')}" filterBy="#{lnkdDoc.docValue}" filterMatchMode="contains" sortBy="#{lnkdDoc.docValue}">
                    <h:outputText value="#{lnkdDoc.docValue}" style="float: right" />
                </p:column>
            </p:dataTable>            
        </h:form>
    </p:dialog>

</ui:composition>
    
