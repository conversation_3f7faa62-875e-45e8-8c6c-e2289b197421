<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: CloneOppDlg.xhtml
// Author: Sharvani
//*********************************************************
* Copyright (c) 2016 Indea Design Systems, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:f="http://java.sun.com/jsf/core">
    <!--3281 CRM-3864: jobs: opp clone button very buggy-->
    <p:dialog widgetVar="cloneOppDlg" header="Clone #{custom.labels.get('IDS_OPP')}" modal="true" resizable="false" width="500"  styleClass="dialogCSS" > 
        <h:form id ="cloneDLG">

<!--3309 CRM-3864: Jobs > Clone Opp - Add duplicate validation-->
            <!--3309 CRM-3864: Jobs > Clone Opp - Add duplicate validation-->
<p:remoteCommand name="applyCRMCust" 
                             actionListener="#{jobOpps.applyCust(viewCompLookupService.selectedCompany)}" 
                             update=":cloneDLG:inpTxtCustName :cloneDLG:inpTxtDistriName :cloneDLG:pnlclrcust"  oncomplete="PF('cloneBtn').enable()"/>
            <p:remoteCommand name="applyActivePrinci" 
                             actionListener="#{jobOpps.applyPrinci(viewCompLookupService.selectedCompany)}" 
                             update=":cloneDLG:inpTxtPrinciName :cloneDLG:pnlclrprin"  oncomplete="PF('cloneBtn').enable()"/>
            <p:remoteCommand name="applyDistri"    
                             actionListener="#{jobOpps.applyDistrii(viewCompLookupService.selectedCompany)}" 
                             update=":cloneDLG:inpTxtDistriName :cloneDLG:inpTxtCustName :cloneDLG:pnlclrdist :cloneDLG:pnlclrcust"  oncomplete="PF('cloneBtn').enable()"/>
            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-3 ui-lg-3"  style="height: 34px !important;">       
                    <p:outputLabel value="#{custom.labels.get('IDS_PRINCI')}" />
                </div>
                <div class="ui-sm-12 ui-md-7 ui-lg-7"  style="height: 34px !important;">     
                    <h:panelGroup   class="ui-inputgroup"  >

                        <p:inputText value="#{jobOpps.clonePrinciName}"
                                     style="width: 100% ;text-decoration: underline;"
                                     readonly="true" 
                                     placeholder="[Not selected]" 
                                     id="inpTxtPrinciName" required="true"
                                     class="#{fieldsMstService.fields.get('IDS_PRINCI') == 1 ? 'required':''}" >
                        </p:inputText> 
                        <p:message for="inpTxtPrinciName" id="am"/>
                        <p:commandButton   icon="fa fa-search" title="Choose Company" immediate="true"          
                                          actionListener="#{viewCompLookupService.listPrincipals(0, 'applyActivePrinci')}" 
                                           update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                           styleClass="btn-info btn-xs" />
                    </h:panelGroup>
                </div>  
                <div class="ui-sm-12 ui-md-2 ui-lg-2"  style="height: 34px !important;">       
                    <h:panelGroup id="pnlclrprin"  >
                        <p:commandLink rendered="#{jobOpps.clonePrinciId>0}" value="Clear" 
                                       title="Clear #{custom.labels.get('IDS_PRINCI')} selection" 
                                       class="btnlukup " style="border: none;background: none; margin-left: -12px;"  
                                       actionListener="#{jobOpps.clearSelection(1)}" 
                                       process="@this"  
                                       update=":cloneDLG:inpTxtPrinciName  :cloneDLG:pnlclrprin"   />
                    </h:panelGroup>
                </div>
            </div>    



            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-3 ui-lg-3"  style="height: 34px !important;"> 
                    <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}" />
                </div>
                <div class="ui-sm-12 ui-md-7 ui-lg-7"  style="height: 34px !important;">  
                    <h:panelGroup class="ui-inputgroup"  >  
                        <p:inputText value="#{jobOpps.cloneCustName}"
                                     readonly="true" 
                                     placeholder="[Not selected]" 
                                     widgetVar="wopc" required="true"  
                                     id="inpTxtCustName" /> 
                        <p:message for="inpTxtCustName" id="acc"/>
                        <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                           actionListener="#{viewCompLookupService.listCRM(2, 'applyCRMCust')}" 
                                          oncomplete="PF('lookupComp').show()"
                                          update=":formCompLookup" 
                                          styleClass="btn-info btn-xs"  />
                    </h:panelGroup>
                </div>  
                <div class="ui-sm-12 ui-md-2 ui-lg-2"  style="height: 34px !important;">    
                    <h:panelGroup id="pnlclrcust"  >
                        <p:commandLink rendered="#{jobOpps.cloneCustId>0}" value="Clear" 
                                       title="Clear #{custom.labels.get('IDS_CUSTOMER')} selection"
                                       class="btnlukup " style="border: none;background: none; margin-left: -12px;"  
                                       actionListener="#{jobOpps.clearSelection(2)}" 
                                       process="@this" 
                                       update=":cloneDLG:inpTxtCustName :cloneDLG:pnlclrcust"   />
                    </h:panelGroup>
                </div>
            </div>

            <div class="ui-g ui-fluid">
                <div class="ui-sm-12 ui-md-3 ui-lg-3"  style="height: 34px !important;"> 
                    <p:outputLabel value="#{custom.labels.get('IDS_DISTRI')}" />
                </div>
                <div class="ui-sm-12 ui-md-7 ui-lg-7"  style="height: 34px !important;">  

                    <h:panelGroup   class="ui-inputgroup"  >
                        <p:inputText value="#{jobOpps.cloneDistriiName}"
                                     style="width: 100%;text-decoration:underline;" 
                                     readonly="true" 
                                     placeholder="[Not selected]" 
                                     class="#{fieldsMstService.fields.get('OPP_DISTRI') == 1 ? 'required':''}"
                                     id="inpTxtDistriName"
                                     />                   
                        <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" 
                                          actionListener="#{viewCompLookupService.listCRM(3, 'applyDistri')}" 
                                          update=":formCompLookup" oncomplete="PF('lookupComp').show()"  
                                          styleClass="btn-info  btn-xs"
                                          />
                    </h:panelGroup>
                </div>  
                <div class="ui-sm-12 ui-md-2 ui-lg-2"  style="height: 34px !important;">  
                    <h:panelGroup id="pnlclrdist"  >
                        <p:commandLink value="Clear"   rendered="#{jobOpps.cloneDistriiId>0}"  title="Clear #{custom.labels.get('IDS_DISTRI')} selection" 
                                       class="btnlukup " style="border: none;background: none; margin-left: -12px;"  
                                       actionListener="#{jobOpps.clearSelection(3)}" 
                                       process="@this"  
                                       update=":cloneDLG:inpTxtDistriName :cloneDLG:pnlclrdist"   />
                    </h:panelGroup>
                </div>
            </div>
            

            <!--SONARQUBE ISSUES - sharvani - 23-01-2020-->
            <!--<center>-->
            <div align="center">
                <p:commandButton value="Clone" widgetVar="cloneBtn" class="btn btn-primary btn-xs" action="#{jobOpps.cloneOpp()}"  update=":jobDetailsButtonForm:tabviewJobs:dtOppList :jobDetailsButtonForm:growlJobSave" oncomplete='loadJobOpps();' onclick="PF('cloneBtn').disable()"
                                 />
                <p:spacer width="4px" />
                <p:commandButton type="button" value="Cancel" styleClass="btn btn-warning btn-xs" onclick="PF('cloneOppDlg').hide()"/>
            </div>
            <!--</center>-->
            <br/>
        </h:form>
    </p:dialog>   
</ui:composition>

