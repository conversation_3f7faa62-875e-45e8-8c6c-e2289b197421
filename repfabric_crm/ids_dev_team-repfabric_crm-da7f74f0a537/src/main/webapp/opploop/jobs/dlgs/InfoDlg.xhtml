<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <!--#3085 Quotes - Line Items - Import-->
    <!--#3708 Quotes> Line Items -Import - Updates in template-->
    <!--04-11-2024 14809 ESCALATIONS CRM-8642   Jobs: Sorting by name not working-->
    <h:form>
        <p:dialog widgetVar="helpDlg" header="Info" height="530" width="950" modal="true" style="text-overflow: scroll; text-align: left !important;"  >
            <!--Jira Task #14108 ESCALATION CRM-8238 : JOBS:  add "Download Template" to Jobs page-->
            Import feature allows you to import #{custom.labels.get('IDS_JOB')} details data to the database from .csv / .xls /.xlsx file formats.

            <br/><br/>
            <strong>Guidelines</strong>
            <!--2490 - CRM-1544: CRM Import: help file hidden - 02-12-2019 - sharvani-->
            <div style="margin-left: 15px">
                <ul>
                    <li>
                        Provide a header column with column titles in the import file.
                    </li>
                    <li>
                        Make sure all the fields are present and in the same order as given below.
                    </li>
                    <li>
                        Provide blank value in case no data is available for a data cell.
                    </li>
                    <li>
                        <!--Jira Task #14108 ESCALATION CRM-8238 : JOBS:  add "Download Template" to Jobs page-->
                        Mandatory Fields: #{custom.labels.get('IDS_JOB')} Name, #{custom.labels.get('IDS_JOB_TYPE')}, #{custom.labels.get('IDS_JOB_STAGE')}.
                    </li> 
                    <li>
                        Date fields must be in <strong>MM-dd-yyyy</strong> format.
                    </li>
                    <li>
                        Timestamp fields must be in <strong>YYYY-MM-DD HH:mm:ss</strong> format.
                    </li>
                </ul>
            </div>

            <fieldset>

                <!--Jira Task #14108 ESCALATION CRM-8238 : JOBS:  add "Download Template" to Jobs page-->
                <legend style="font-weight: bold; position: relative;">
                    Column Order
                    <!-- Button positioned above the legend line -->
                    <p:commandButton id="btnJobTemplatDwnld" 
                                     styleClass="btn btn-primary btn-xs" 
                                     value="Download Template" 
                                     action="#{importManager.genTemplateFileds(30)}" 
                                     onclick="PrimeFaces.monitorDownload(start, stop);"  
                                     style="position: absolute; right: 0; top: 0;"/>
                </legend>  
                #{custom.labels.get('IDS_JOB')} Name* 	 <p:outputLabel value="|" styleClass="redColor"/>
                #{custom.labels.get('IDS_JOB_TYPE')}*  <p:outputLabel value="|" styleClass="redColor"/>
                #{custom.labels.get('IDS_JOB_CATEGORY')}  <p:outputLabel value="|" styleClass="redColor"/>
                #{custom.labels.get('IDS_JOB_SPECIFIER')}  <p:outputLabel value="|" styleClass="redColor"/>
                #{custom.labels.get('IDS_JOB_STAGE')}*  <p:outputLabel value="|" styleClass="redColor"/>
                #{custom.labels.get('IDS_JOB_VALUE')}  <p:outputLabel value="|" styleClass="redColor"/>
                Street Address  <p:outputLabel value="|" styleClass="redColor"/>
                City  <p:outputLabel value="|" styleClass="redColor"/>
                <!--#2088: CRM-3042: Intralec: change labels for state and zip - Jobs-->
                #{custom.labels.get('IDS_STATE')}  <p:outputLabel value="|" styleClass="redColor"/>
                #{custom.labels.get('IDS_ZIP')}  <p:outputLabel value="|" styleClass="redColor"/>
                Country  <p:outputLabel value="|" styleClass="redColor"/>
                #{custom.labels.get('IDS_JOB')} No.  <p:outputLabel value="|" styleClass="redColor"/>
                #{custom.labels.get('IDS_JOB')} URL  <p:outputLabel value="|" styleClass="redColor"/>
                Sales Person <p:outputLabel value="|" styleClass="redColor"/>
                #{custom.labels.get('IDS_JOB_BID_DATE')} <p:outputLabel value="|" styleClass="redColor"/>
                <!--            23-01-2021:Task# #3131-CRM-3794: jobs edit  label -  Order Date-->
                #{custom.labels.get('IDS_JOB_ORDER_DATE')} <p:outputLabel value="|" styleClass="redColor"/>
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                #{custom.labels.get('IDS_JOB_FOLLOWUP')}  <p:outputLabel value="|" styleClass="redColor"/>
                Awarded to  <p:outputLabel value="|" styleClass="redColor"/>
                Awarded Date  <p:outputLabel value="|" styleClass="redColor"/>
                <!--15144-->
                #{custom.labels.get('IDS_JOB_DESC')}   <p:outputLabel value="|" styleClass="redColor"/>
                Created By  <p:outputLabel value="|" styleClass="redColor"/>
                Creation Timestamp  <p:outputLabel value="|" styleClass="redColor"/>
                Last Modified By  <p:outputLabel value="|" styleClass="redColor"/>
                Last Modified Timestamp 



                <br/>
                <br/>


            </fieldset>
            <br/>
            <!--04-11-2024 14809 ESCALATIONS CRM-8642   Jobs: Sorting by name not working-->
            <div class="cntr">
                <p:commandButton value="OK" onclick="PF('helpDlg').hide();" />
            </div>
            <!--04-11-2024 14809 ESCALATIONS CRM-8642   Jobs: Sorting by name not working-->
        </p:dialog>
        <!--04-11-2024 14809 ESCALATIONS CRM-8642   Jobs: Sorting by name not working-->
    </h:form>
    <style>
        .redColor{
            color: red;
        }
        .fontSize{
            font-size: 2px;
        }
        .cntr{
            text-align:center;
            padding: 9px;
        }
    </style>
</ui:composition>