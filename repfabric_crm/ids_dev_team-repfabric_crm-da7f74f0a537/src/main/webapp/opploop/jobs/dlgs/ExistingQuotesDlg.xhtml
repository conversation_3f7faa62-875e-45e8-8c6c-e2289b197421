
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:p="http://primefaces.org/ui">
     <!--<h4 class="sub_title">#{custom.labels.get('IDS_OPPS')} Information</h4>-->

    <p:dialog id="dlgQuotMst" widgetVar="quotMstDlg"  modal="true" width="1280"  header="Add #{custom.labels.get('IDS_QUOTES')}"
              height="550" maximizable="true" responsive="true" 
              closeOnEscape="true">
        <h:form id="frmQuotMst">
            <!-- #2843: Jobs - Quotes tab: paginatorAlwaysVisible set to true -->
            <p:dataTable  id="dtQuotMstList" widgetVar="quotMstListTable" 
                          value="#{jobQuotes.quot}" var="quot" selectionMode="single" 
                          rowKey="#{quot.recId}" selection="#{jobQuotes.selQuot}" 
                          emptyMessage="No open #{custom.labels.get('IDS_QUOTES')} found." 
                          paginator="true" rows="10" filteredValue="#{jobQuotes.filteredQuotes}"
                          paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                          rowsPerPageTemplate="5,10,15" 
                          paginatorAlwaysVisible="true" class="tblmain">

                <p:ajax event="rowSelect" listener="#{jobQuotes.linkQuotToJob(jobs.jobId)}" oncomplete='loadJobQuotes();'/>

                <p:column  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{quot.oppCustomerName}"  sortBy="#{quot.oppCustomerName}" id="custH">

                    #{quot.oppCustomerName}
                </p:column>

                <p:column  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{quot.oppPrincipalName}" sortBy="#{quot.oppPrincipalName}" id="prinH">
                    #{quot.oppPrincipalName}                               
                </p:column>

                <p:column  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_SALES_TEAM')}" filterBy="#{quot.oppSManName}" sortBy="#{quot.oppSManName}"  
                           id="smanHId">
                    #{quot.oppSManName} 
                </p:column>

                <p:column  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_DISTRI')}" filterBy="#{quot.oppDistriName}"   sortBy="#{quot.oppDistriName}"  
                           id="distriHId">
                    #{quot.oppDistriName}  
                </p:column>
                <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
<!--                <p:column  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_SECOND_CUSTOMER')}" filterBy="#{quot.oppSecCustomerName}"  sortBy="#{quot.oppSecCustomerName}"  
                          id="secCustHId">
                #{quot.oppSecCustomerName}        
            </p:column>-->
                    <!--14519: 18/09/2024  ESCALATIONS CRM-8503   Job: No open Quotes found When Many Exist on All Jobs--> 
                 <p:column  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_OWNER')}" filterBy="#{quot.quotOwnerName}"  sortBy="#{quot.quotOwnerName}"  id="qwnr">
                    #{quot.quotOwnerName}                          
                </p:column>
                
                <p:column  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_PROGRAM')}" filterBy="#{quot.oppCustProgram}"  sortBy="#{quot.oppCustProgram}"  id="prog">
                    #{quot.oppCustProgram}                          
                </p:column>

                <p:column  filterMatchMode="contains" headerText="Quote #" filterBy="#{quot.quotNumber}"  sortBy="#{quot.quotNumber}" >
                    #{quot.quotNumber}                     
                </p:column> 

                <p:column  filterMatchMode="contains" headerText="Date" filterBy="#{quot.quotDate}"  sortBy="#{quot.quotDate}" >
                    #{quot.quotDate}                    
                </p:column>
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:column  filterMatchMode="contains" headerText="#{custom.labels.get('IDS_JOB_VALUE')}" filterBy="#{quot.oppValue}"  sortBy="#{quot.oppValue}" 
                            id="valHId">
                    #{quot.oppValue}
                </p:column>

                <p:column  filterMatchMode="contains" headerText="Status" filterBy="#{quot.delivStatusName}"  sortBy="#{quot.delivStatusName}" id="statH">
                    #{quot.delivStatusName}                                 
                </p:column>


<!--                     <p:column filterBy="#{opp.getCompNameById(opp.oppCustomer)}" filterMatchMode="contains" sortBy="#{opp.getCompNameById(opp.oppCustomer)}" >
 <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet>
                #{opp.getCompNameById(opp.oppCustomer)}
            </p:column>

            <p:column filterBy="#{opp.getCompNameById(opp.oppPrincipal)}" filterMatchMode="contains" sortBy="#{opp.getCompNameById(opp.oppPrincipal)}">
                <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet>
                #{opp.getCompNameById(opp.oppPrincipal)}
            </p:column>

            <p:column filterBy="#{opp.getCompNameById(opp.oppDistri)}" filterMatchMode="contains" sortBy="#{opp.getCompNameById(opp.oppDistri)}">
                <f:facet name="header">#{custom.labels.get('IDS_DISTRI')}</f:facet>
                #{opp.getCompNameById(opp.oppDistri)}
            </p:column>


            <p:column filterBy="#{opp.oppCustProgram}" filterMatchMode="contains" sortBy="#{opp.oppCustProgram}">
                <f:facet name="header">#{custom.labels.get('IDS_PROGRAM')}</f:facet>
                #{opp.oppCustProgram}
            </p:column> 

            <p:column filterBy="#{opp.oppActivity}" filterMatchMode="contains" sortBy="#{opp.oppActivity}">
                <f:facet name="header">#{custom.labels.get('IDS_ACTIVITY')}</f:facet>
                #{opp.oppActivity}
            </p:column>
            <p:column filterBy="#{opp.oppStatus}" filterMatchMode="contains" sortBy="#{opp.oppStatus}">
                <f:facet name="header">Status</f:facet>                    
                #{opp.oppStatus}
            </p:column>
            <p:column filterBy="#{opp.oppFollowUp}" filterMatchMode="contains" sortBy="#{opp.oppFollowUp}">
                <f:facet name="header">#{custom.labels.get('IDS_FOLLOW_UP')}</f:facet>
                <p:outputLabel value="#{opp.oppFollowUp}" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                </p:outputLabel>
            </p:column>
            <p:column filterBy="#{opp.oppNextStep}" filterMatchMode="contains" sortBy="#{opp.oppNextStep}">
                <f:facet name="header">Next Step</f:facet> 
                #{opp.oppNextStep}
            </p:column>
            <p:column  filterMatchMode="contains" sortBy="#{opp.oppCloseStatus}">
                <f:facet name="header">Status</f:facet> 
                <p:outputLabel value="#{opp.oppCloseStatus==0?'Open':'Closed'}" />
            </p:column>-->
            </p:dataTable>
        </h:form>
    </p:dialog>



</ui:composition>


