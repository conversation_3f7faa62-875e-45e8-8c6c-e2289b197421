<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--13-03-2024 13234 Code Optimisation for Oasis Integration Quotes and Jobs : Manual fetch-->
    <h:form id="frmOasisJobs"> 
        <p:dialog id="dlgOasisJobsList" widgetVar="wvDlgOasisJobsList" 
                   responsive="true" draggable="false" resizable="false"
                   style="text-overflow: scroll;"  height="80%" width="80%" modal="true">
            <f:facet name="header">
                <h:outputText value="Oasis Job List" /> 
            </f:facet>

            <p:outputPanel id="opOasisJobsList">
                <p:outputLabel value="Jobs for : " style="font-weight: bold;" /><p:spacer/>
                <p:outputLabel rendered="#{jobs.filterISQuotesStatus!=2}" value="#{jobs.paramISQuotDate}" style="font-weight: bolder;" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" />  
                </p:outputLabel>
                <p:outputLabel rendered="#{jobs.filterISQuotesStatus==2}" value="#{jobs.filterISQuotes}" style="font-weight: bolder;" />
                <p:spacer width="10"/>
                <p:selectBooleanCheckbox  style="text-align: right;"
                                          itemLabel="Pull related records" 
                                          value="#{jobs.updateISQutesQuoteDtls}" disabled="true" >
                    <p:ajax event="change" listener="#{jobs.onChangeChckBox(jobs.updateISQutesQuoteDtls)}"/>
                </p:selectBooleanCheckbox>

                <p:dataTable value="#{oasisCrmService.jobsList}" rows="10"
                             var="oasisJobVar" id="dtOasisJobsList" rowIndexVar="oasisJobRowIndex" 
                             emptyMessage="No Jobs available" widgetVar="wvDtOasisJobsList"
                             scrollable="true" scrollHeight="360" paginator="true" 
                             selection="#{oasisCrmService.selectedJob}"
                             selectionMode="single" 
                             rowKey="#{oasisJobVar.jobIdForCrm}#{oasisJobVar.jobPackage}" >

                    <p:ajax event="rowSelect" process="@this" immediate="true" onstart="PF('progressDlg123').show();"
                            listener="#{oasisCrmService.checkDuplicateJobAndApply}"
                            update=":jobDetailsButtonForm" 
                            oncomplete="PF('progressDlg123').hide();" />

                    <p:column  width="2%"> 
                        <f:facet name="header"></f:facet>
                            #{oasisJobRowIndex+1}
                    </p:column> 
                    <p:column width="10%" >
                        <f:facet name="header">Job No.</f:facet>
                            #{oasisJobVar.jobIdForCrm}  
                    </p:column>
                    <p:column width="10%" >
                        <f:facet name="header">Job Name</f:facet>
                            #{oasisJobVar.jobDescr} 
                    </p:column>
                    <p:column width="10%" >
                        <f:facet name="header">Package No.</f:facet>
                            #{oasisJobVar.jobPackageId} 
                    </p:column>
                    <p:column width="10%" >
                        <f:facet name="header">Package Name</f:facet>
                            #{oasisJobVar.jobPackage} 
                    </p:column>
                    <p:column width="10%" rendered="false">
                        <f:facet name="header">Job Specifier</f:facet>
                            #{oasisJobVar.jobSpecifierName} 
                    </p:column>
                    <p:column width="10%" >
                        <f:facet name="header">Location</f:facet>
                            #{oasisJobVar.jobLocation} 
                    </p:column>
                    <p:column width="10%" >
                        <f:facet name="header">Job Notes</f:facet>
                            #{oasisJobVar.jobNotes} 
                    </p:column>
                </p:dataTable>
            </p:outputPanel>

        </p:dialog>
    </h:form>

</ui:composition>



