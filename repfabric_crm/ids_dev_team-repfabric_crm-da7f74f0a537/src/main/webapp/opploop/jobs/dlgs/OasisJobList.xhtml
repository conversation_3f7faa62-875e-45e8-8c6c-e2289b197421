<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
   <!--5380 Oasis: Jobs > Fetch from Oasis-->
    <p:dialog  styleClass="dialogCSS" id="isQuotJobListDtlsx1" widgetVar="isQuotJobListDtls1"  responsive="true" draggable="false" resizable="false"
               style="text-overflow: scroll;"  height="80%" width="80%" modal="true">
        <f:facet name="header">
            <h:outputText id="isQuotListTxt1" value="Oasis Job List" /> 
        </f:facet>
        <h:form id="isQfrm1"> 
            <!--1935 CRM-3373: jobs: southwest: fetching jobs not reinitializing #1-->
            <p:outputLabel   id="dtIsQtLbl1" value="Jobs for : " style="font-weight: bold;" /><p:spacer/>
            <p:outputLabel rendered="#{jobs.filterISQuotesStatus!=2}" id="dtIsQt1" value="#{jobs.paramISQuotDate}" style="font-weight: bolder;" >
                <f:convertDateTime pattern="#{globalParams.dateFormat}" />  
            </p:outputLabel>
            <p:outputLabel rendered="#{jobs.filterISQuotesStatus==2}" id="dtIsQts1" value="#{jobs.filterISQuotes}" style="font-weight: bolder;" />
            <p:spacer width="10"/>
            <!--4872 CRM Sync: Settings for Oasis-->
            <p:selectBooleanCheckbox  id="updNrmlLnItmReq1" style="text-align: right;"
                                             itemLabel="Pull related records" 
                                             value="#{jobs.updateISQutesQuoteDtls}" disabled="true" >
                <p:ajax event="change" listener="#{jobs.onChangeChckBox(jobs.updateISQutesQuoteDtls)}"/>
            </p:selectBooleanCheckbox>
           
           
            <!--11957-->
            <p:dataTable value="#{iSQuoteCrmClient.jobLookUp}" rows="10"
                         var="isqHd" id="isq1" rowIndexVar="isqRecc" 
                         emptyMessage="No Jobs available" 
                         scrollable="true" scrollHeight="360" paginator="true" 
                         selection="#{jobs.selectedOasisJob}"
                         selectionMode="single" 
                         rowKey="#{isqHd.jobIdForCrm}#{isqHd.jobPackage}"
                         >
                <!--5802  Jobs: duplicate prevention for jobs-->
 
                <p:ajax event="rowSelect" listener="#{jobs.selectOasisJob}"
         process="@this" immediate="true" update=":isQfrm3:isql3 :isQfrm3  :jobDetailsButtonForm" oncomplete="" />
 
                <p:column  width="2%"> 
                    <f:facet name="header"></f:facet>
                        #{isqRecc+1}
                </p:column> 
                <p:column width="10%" >
                    <f:facet name="header">Job No.</f:facet>
                        #{isqHd.jobIdForCrm}  
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Job Name</f:facet>
                        #{isqHd.jobDescr==null?'':isqHd.jobDescr} 
                </p:column>
                 <p:column width="10%" >
                    <f:facet name="header">Package No.</f:facet>
                        #{isqHd.jobPackageId==null?'':isqHd.jobPackageId} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Package Name</f:facet>
                        #{isqHd.jobPackage==null?'':isqHd.jobPackage} 
                </p:column>
                <p:column width="10%" rendered="false">
                    <f:facet name="header">Job Specifier</f:facet>
                        #{isqHd.jobSpecifierName==null?'':isqHd.jobSpecifierName} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Location</f:facet>
                        #{isqHd.jobLocation==null?'':isqHd.jobLocation} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Job Notes</f:facet>
                        #{isqHd.jobNotes==null?'':isqHd.jobNotes} 
                </p:column>
<!--               jb.setJobDescr(rs.getString(3));
                jb.setJobNotes(rs.getString(7));
                jb.setJobLocation <p:column width="10%" >
                    <f:facet name="header">Job City</f:facet>
                        #{isqHd.jobcity==null?'':isqHd.jobcity} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Est. Job Value</f:facet>
                        #{isqHd.jobEstValue==null?'':isqHd.jobEstValue} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Date Changed</f:facet> 
                    <h:outputLabel   value="#{rFUtilities.convertFromUTC(isqHd.updDate)}" >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" for="isqHd.updDate"/>
                    </h:outputLabel>
                </p:column> -->
            </p:dataTable>

        </h:form>
    </p:dialog>
</ui:composition>



