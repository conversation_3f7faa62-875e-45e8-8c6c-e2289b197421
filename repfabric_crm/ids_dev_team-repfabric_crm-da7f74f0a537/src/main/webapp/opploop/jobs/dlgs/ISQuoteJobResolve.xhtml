<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">

    <!--responsive="true"--> 
    <p:dialog  styleClass="dialogCSS" id="isQuotLnItmListDtlsx" widgetVar="isQuotLnItmListDtls"  draggable="false" resizable="false"
               style="text-overflow: scroll;"  height="80%" width="80%" modal="true" >
        <f:facet name="header">
            <!--2903 Jobs > Fetch from ISQuote > Download Job Contacts-->
            <h:outputText id="isQuotLnItmListTxt" value="Resolve Entities" />
        </f:facet> 
        <h:form id="isQfrm2">
            <!--<p:remoteCommand name="applyISQCust" actionListener="#{jobs.applyISQuotJobSpecifier(viewCompLookupService.selectedCompany)}"  update=":isQfrm2:isql" />-->

            <!--<p:remoteCommand name="applyComps1" actionListener="#{jobs.updateOwnerCompanyOnSelection(viewCompLookupService.selectedCompany)}" update=":isQfrm2:isq"/>-->
            <!--<p:remoteCommand name="applyConts1" actionListener="#{jobs.applyOwnerCompanyContact(viewContLookupService.selectedContact)}" update=":isQfrm2:isq" />-->

            <!--   <p:remoteCommand name="applyActivePrinciResolve" autoRun="false" immediate="true" update=":isQfrm2:isql"
                                         actionListener="#{quotesHdr.updateFieldsOnSelectionResolve(viewCompLookupService.selectedCompany)}"/>-->
            <!--1995 CRM-3372: jobs: Southwest: mappings missing for jobs - JobMfgs, Related Companies-->
            <p:remoteCommand autoRun="true" update=":isQfrm2:isql"/>
<!--            =#{jobs.compResolveList.size()}=-->
            <p:remoteCommand name="applyActivePrinciResolve" autoRun="false" immediate="true" update=":isQfrm2:isql"
                             actionListener="#{jobs.updateFieldsOnSelectionResolve(viewCompLookupService.selectedCompany)}"/>
            <!--1989 Jobs: Owner Company and Contact-->
            <p:remoteCommand name="applyContForJobOwner" actionListener="#{jobs.updateJobOwnerContactOnSelectionResolve(viewContLookupService.selectedContact)}" update=":isQfrm2:isql" />

            <!--1933 CRM-3372: jobs: Southwest: mappings missing for jobs-->
            <p:remoteCommand name="applyUser" actionListener="#{jobs.applyUser(userLookupService.user)}" update=":isQfrm2:isql" />
            <p:remoteCommand name="applyComment" actionListener="#{jobs.applyUser(userLookupService.user)}" update=":isQfrm2:isql" />
            <p:remoteCommand name="applyJobcategory" autoRun="false" immediate="true" update=":isQfrm2:isql"
                             actionListener="#{jobs.updateJobCatgOnSelectionResolve(jobs.selectedJobCatgry)}"/>
            <!--2903 Jobs > Fetch from ISQuote > Download Job Contacts-->
            <p:remoteCommand name="applyContForJob" actionListener="#{jobs.updateJobContactOnSelectionResolve(viewContLookupService.selectedContact)}" update=":isQfrm2:isql" />

            <!--//    Feature #6456:IS Quotes: Download Job Notes to Job Comments on Manual/Auto Fetch-->
<!--2676 CRM-3379: Workflow of Quotes to jobs with ISQuote is bad #3-->
<!--12486: 01/12/2023: ESCALATIONS CRM-7514   ISQUOTES: Job showing unrelated companies-->
            <p:commandButton  value="Apply" title="Apply" class="btn btn-primary btn-xs" process="@this" 
                              immediate="true" actionListener="#{jobs.saveResolved()}" onclick="PF('dialogProcessDlg').show();"
                              update=":jobDetailsButtonForm:tabviewJobs:dtQuotList :jobDetailsButtonForm:tabviewJobs:compQuotPanel :jobDetailsButtonForm:tabviewJobs :jobDetailsButtonForm:tabviewJobs:pnlCMNTS"  oncomplete="PF('dialogProcessDlg').hide();"/> 

            
            <p:dataTable value="#{jobs.compResolveList}" 
                         var="errRec" id="isql" sortBy="#{errRec.recId}" sortOrder="ascending"
                         emptyMessage="No Companies available" 
                         scrollable="true" rowKey="#{errRec.recId}"
                         scrollHeight="350" filteredValue="#{jobs.compResolveList}" 
                         widgetVar="qlWVar"
                         rowStyleClass="#{errRec.childQuotesAlreadyExist=='false'?'':'ui-helper-hidden'}">
                
                    <p:column  width="10%" rendered="false">
                        <f:facet name="header">Company ID</f:facet>
                        <h:outputText value="#{errRec.companyTypeId}" />
                    </p:column>
                    <p:column  width="10%">
                        <!--1933 CRM-3372: jobs: Southwest: mappings missing for jobs-->
                        <f:facet name="header">Type</f:facet>
                        <h:outputText value="#{errRec.companyTypeName}"/>
                    </p:column>
                    <p:column width="10%">
                        <!--1933 CRM-3372: jobs: Southwest: mappings missing for jobs-->
                        <!--2903 Jobs > Fetch from ISQuote > Download Job Contacts-->
                        <f:facet name="header">ISQuote Entities</f:facet>
                        <!--<h:outputText value="#{errRec.quotISCustomerName}" />-->
                        <!--3437 Job Auto Download (cron job) - (CODE REVERTED)-->
                        <h:outputText value="#{errRec.quotISCustomerName}" rendered="#{errRec.companyTypeId==1}"/>
                        <h:outputText value="#{errRec.quotISPrincipalName}" rendered="#{errRec.companyTypeId==2}"/>
                        <!--2224 CRM-3379: Workflow of Quotes to jobs with ISQuote is bad-->
                        <h:outputText value="#{errRec.quotISDistrbutorName}" rendered="#{errRec.companyTypeId==3}"/>
                        <h:outputText value="#{errRec.quotISJobOwnerCompanyName}" rendered="#{errRec.companyTypeId==5}"/>
                        <h:outputText value="#{errRec.quotISJobOwnerContactName}" rendered="#{errRec.companyTypeId==6}"/>
                        <!--1933 CRM-3372: jobs: Southwest: mappings missing for jobs-->
                        <h:outputText value="#{errRec.quotISJobSalesperson}" rendered="#{errRec.companyTypeId==7}"/>
                        <h:outputText value="#{errRec.quotISJobCategory}" rendered="#{errRec.companyTypeId==8}"/>
                        <!--2903 Jobs > Fetch from ISQuote > Download Job Contacts-->
                        <h:outputText value="#{errRec.quotISJobContactName}" rendered="#{errRec.companyTypeId==9}"/>
<!--//    Feature #6456:IS Quotes: Download Job Notes to Job Comments on Manual/Auto Fetch-->
                        <h:outputText value="#{errRec.quotISJobCommentName}" rendered="#{errRec.companyTypeId==10}"/>
                        <!--13093: 21/02/2023: ESCALATIONS CRM-7762   ISQ Sync Issue-->
                        <h:outputText value="#{errRec.jobIsAwardeName}" rendered="#{errRec.companyTypeId==13}"/>
                    </p:column>

                    <p:column  width="10%"  rendered="false">
                        <f:facet name="header">REC id</f:facet>
                        <h:outputText value="#{errRec.recId}"  rendered="true"/>
                    </p:column>
                    <!--                 <p:column  width="10%"  >
                        <f:facet name="header">C CityName</f:facet>
                        <h:outputText value="#{errRec.quotISCustomerCityName}"  rendered="true"/>
                    </p:column>-->
                    <!--                 <p:column  width="10%"  >
                                        <f:facet name="header">State</f:facet>
                                        <h:outputText value="#{errRec.quotISState}"  rendered="true"/>
                                    </p:column>-->
                    <!--3285 CRM-3861: Jobs: HSArep: can't see company details like city state and zip in order to select the rig-->
                    <p:column  width="10%" >
                        <f:facet name="header">City</f:facet>
                        <h:outputText value="#{errRec.quotISCity}"  rendered="true"/></p:column>
                    <p:column  width="10%"  >
                        <f:facet name="header">State</f:facet>
                        <h:outputText value="#{errRec.quotISState}"  rendered="true"/>                      
                    </p:column>
                    <p:column  width="10%" >
                        <f:facet name="header">Zip</f:facet>
                        <h:outputText value="#{errRec.quotISZip}"  rendered="true"/>
                        <!--<h:outputText value="#{errRec.childQuotesAlreadyExist}"  rendered="true"/>-->
                    </p:column>
                    <p:column width="10%">
                        <!--1933 CRM-3372: jobs: Southwest: mappings missing for jobs-->
                        <!--2903 Jobs > Fetch from ISQuote > Download Job Contacts-->
                        <f:facet name="header">RF Entities</f:facet>
                        <!--1989 Jobs: Owner Company and Contact-->
                        <!--<h:outputText value="#{errRec.quotCustomerName}"/>--> 
                        <h:outputText value="#{errRec.quotCustomerName}" rendered="#{errRec.companyTypeId==1}"/>
                        <h:outputText value="#{errRec.quotPrincipalName}" rendered="#{errRec.companyTypeId==2}"/>
                        <!--2224 CRM-3379: Workflow of Quotes to jobs with ISQuote is bad-->
                        <h:outputText value="#{errRec.quotDistrbutorName}" rendered="#{errRec.companyTypeId==3}"/> 
                        <h:outputText value="#{errRec.quotJobOwnerCompanyName}" rendered="#{errRec.companyTypeId==5}"/>
                        <h:outputText value="#{errRec.quotJobOwnerContactName}" rendered="#{errRec.companyTypeId==6}"/>
                        <!--<h:outputText value="#{errRec.quotJobOwnerContactId}" rendered="#{errRec.companyTypeId==6}"/>-->
                        <!--1933 CRM-3372: jobs: Southwest: mappings missing for jobs-->
                        <h:outputText value="#{errRec.quotJobSalespersonName}" rendered="#{errRec.companyTypeId==7}"/>
                        <h:outputText value="#{errRec.quotJobCategoryName}" rendered="#{errRec.companyTypeId==8}"/>
                        <!--2903 Jobs > Fetch from ISQuote > Download Job Contacts-->
                        <h:outputText value="#{errRec.quotJobContactName}" rendered="#{errRec.companyTypeId==9}"/>
<!--//    Feature #6456:IS Quotes: Download Job Notes to Job Comments on Manual/Auto Fetch-->
                        <h:outputText value="#{errRec.quotJobCommentName}" rendered="#{errRec.companyTypeId==10}"/>
                       <!--13093: 21/02/2023: ESCALATIONS CRM-7762   ISQ Sync Issue-->
                        <h:outputText value="#{errRec.jobAwardedToName}" rendered="#{errRec.companyTypeId==13}"/>

                    </p:column>

                    <!--                <p:column width="10%" rendered="true">
                                        <f:facet name="header">Ids</f:facet> 
                                        <h:outputText value="#{errRec.quotJobOwnerCompanyName}" />
                                        <h:outputText value="#{errRec.quotJobOwnerCompanyId}" />
                                        <h:outputText value="#{errRec.quotISCustomerId}"/>  ==                   
                                        <h:outputText value="#{errRec.quotCustomerId}"/>
                                    </p:column>-->
                    <p:column width="10%">
                        <f:facet name="header"></f:facet>
                        <!--                    <p:commandButton id="btnCompCustSearch" icon="fa fa-search"  title="Choose Company"   
                                                             actionListener="#{viewCompLookupService.listAll('applyISQCust',2,0,1)}" 
                                                             update=":formCompLookup" immediate="true" process="@this" oncomplete="PF('lookupComp').show();" 
                                                             styleClass="btn-info btn-xs" rendered="#{errRec.companyTypeId==1}"/>-->
                        <p:inputText type="hidden" value="#{jobs.jobSpecifier}" id="itJobSpcId"/>
    <!--                    <p:commandButton id="btnCompPrinciSearch" icon="fa fa-search" title="Choose Company" rendered="#{errRec.companyTypeId==2}"
                                         actionListener="#{quotesHdr.listLookUpvaluesForResolve('applyActivePrinciResolve', 1,errRec.recId)}"
                                         update=":formCompLookup :quotefrom:btnPart" immediate="true" process="@this" 
                                         styleClass="btn-info btn-xs" />-->



 <!--9437: 09/11/2022: Feature CRM-6203  ISQuote: Aliasing - when creating a new company, use the ISQuote company details-->
                        <p:commandButton id="_btnCompPrinciSearch" icon="fa fa-search" title="Choose Company" rendered="#{errRec.companyTypeId==2}"
                                         actionListener="#{jobs.listLookUpvaluesForResolve1('applyActivePrinciResolve', 1,errRec.recId, errRec.quotISPrincipalName)}"
                                         update=":formCompLookup :frmLookupHead" immediate="true" process="@this" oncomplete="PF('compLookup').filter();"
                                         styleClass="btn-info btn-xs" />
 <!--9437: 09/11/2022: Feature CRM-6203  ISQuote: Aliasing - when creating a new company, use the ISQuote company details-->
 <!--9264 01/02/2023 CRM-6206: ISQuote: job notes, spec status and mfg value do not come over, nor Architect/engineer,-->
                        <p:commandButton id="_btnCompCustSearch" icon="fa fa-search"  title="Choose Company"  rendered="#{errRec.companyTypeId==1}"
                                         actionListener="#{jobs.listLookUpvaluesForResolve('applyActivePrinciResolve', 5,errRec.recId, errRec.quotISCustomerId, errRec.quotISCustomerName)}"
                                         update=":formCompLookup :frmLookupHead" immediate="true" process="@this" 
                                         styleClass="btn-info btn-xs" />
                        <!--2224 CRM-3379: Workflow of Quotes to jobs with ISQuote is bad-->
                         <!--9437: 09/11/2022: Feature CRM-6203  ISQuote: Aliasing - when creating a new company, use the ISQuote company details-->
                        <p:commandButton id="_btnCompDistriSearch" icon="fa fa-search" title="Choose Company" rendered="#{errRec.companyTypeId==3}"
                                         actionListener="#{jobs.listLookUpvaluesForResolve('applyActivePrinciResolve', 3,errRec.recId, errRec.quotISDistrbutorIdStr,errRec.quotISDistrbutorName)}"
                                         update=":formCompLookup :frmLookupHead" immediate="true" process="@this"
                                         styleClass="btn-info btn-xs" />
                        <!--1989 Jobs: Owner Company and Contact-->
                                                <!--9437: 09/11/2022: Feature CRM-6203  ISQuote: Aliasing - when creating a new company, use the ISQuote company details-->
                                                <!--14296: 22/08/2024: CRM-8382   IS Quote: Include Contractor company types to be brought into Repfabric as a Specifier-->
                        <!--14657: 11/10/2024: ISQUOTE-Owner company lookup is not working-->
                        <p:commandButton id="_btnOwnerCompSearch" icon="fa fa-search" title="Choose Company"  rendered="#{errRec.companyTypeId==5}"
                                         actionListener="#{jobs.listLookUpvaluesForResolve('applyActivePrinciResolve',15,errRec.recId, 0, errRec.quotISJobOwnerCompanyName)}"
                                         update=":formCompLookup :frmLookupHead" immediate="true" process="@this"
                                         styleClass="btn-info btn-xs" />
<!--13093: 21/02/2023: ESCALATIONS CRM-7762   ISQ Sync Issue-->
                        <p:commandButton id="_btnAwardeCompSearch" icon="fa fa-search" title="Choose Company"  rendered="#{errRec.companyTypeId==13}"
                                         actionListener="#{jobs.listLookUpvaluesForResolve('applyActivePrinciResolve',13,errRec.recId, 0, errRec.jobIsAwardeName)}"
                                         update=":formCompLookup :frmLookupHead" immediate="true" process="@this"
                                         styleClass="btn-info btn-xs" />
                        <!--1989 Jobs: Owner Company and Contact-->
                        <!--9436: 16/11/2022: Feature CRM-6204  ISQuote: contact aliasing - create contact with full details, prefilter search for-->
                        <p:commandButton id="_btnOwnerContSearch"  icon="fa fa-search" title="Choose Contact" immediate="true"  rendered="#{errRec.companyTypeId==6}"
                                         actionListener="#{viewContLookupService.listCont('applyContForJobOwner',0,jobs.jobOwnerCompTempId,1,errRec.recId, 0, errRec.quotISJobOwnerContactName)}"
                                         update=":formContLookup :formNew" oncomplete="PF('lookupCont').show();"  
                                         styleClass="btn-info btn-xs" />
                       <!--                  actionListener="#{viewContLookupService.listAll('applyContForJobOwner',errRec.recId)}" -->

                        <!--#{errRec.companyTypeId}-->
                        <!--1933 CRM-3372: jobs: Southwest: mappings missing for jobs-->
                        <p:commandButton id="_btnLookupUserSearch" icon="fa fa-search" title="Choose User" immediate="true"  rendered="#{errRec.companyTypeId==7}"
                                         actionListener="#{userLookupService.list('applyUser', 'Sales Rep',errRec.recId)}" 
                                         update=":formUserLookup" oncomplete="PF('lookupUser').show();"   
                                         styleClass="btn-info btn-xs" />
                        <!--1933 CRM-3372: jobs: Southwest: mappings missing for jobs-->
                        <p:commandButton id="_btnJobCategorySearch" icon="fa fa-search" title="Choose Job Category" rendered="#{errRec.companyTypeId==8}"
                                         actionListener="#{jobs.listLookUpvaluesForJobCategory('applyJobcategory',errRec.recId)}"
                                         update=":isQJobCtgrfrm :isQJobCtgrfrm:jobCtgryLst" immediate="true" process="@this" oncomplete="PF('isQuotJobCtgry').show();" 
                                         styleClass="btn-info btn-xs" />

                        <!--2903 Jobs > Fetch from ISQuote > Download Job Contacts-->
                        <!--9436: 16/11/2022: Feature CRM-6204  ISQuote: contact aliasing - create contact with full details, prefilter search for-->
                        <p:commandButton  icon="fa fa-search" title="Choose Contact" immediate="true"  rendered="#{errRec.companyTypeId==9}"
                                          actionListener="#{viewContLookupService.listAll('applyContForJob',errRec.recId, errRec.quotISJobContactIdStr)}" 
                                          update=":formContLookup :formNew" oncomplete="PF('lookupCont').show();"  
                                          styleClass="btn-info btn-xs" />
<!--//    Feature #6456:IS Quotes: Download Job Notes to Job Comments on Manual/Auto Fetch-->
                        <p:commandButton id="_btnLookupCommentSearch" icon="fa fa-search" title="Choose User" immediate="true"  rendered="#{errRec.companyTypeId==10}"
                                         actionListener="#{userLookupService.list('applyComment',errRec.recId)}" 
                                         update=":formUserLookup" oncomplete="PF('lookupUser').show();"   
                                         styleClass="btn-info btn-xs" />

                    </p:column> 
                 
            </p:dataTable>

        </h:form>


    </p:dialog>
    <!--1933 CRM-3372: jobs: Southwest: mappings missing for jobs-->
    <!--    <p:dialog  styleClass="dialogCSS" id="jobCtgryx" widgetVar="isQuotJobCtgry"  draggable="false" resizable="false"
                   style="text-overflow: scroll;"  height="80%" width="80%" modal="true" >-->
    <p:dialog id="jobCtgryx" header="Job Category Lookup" widgetVar="isQuotJobCtgry" responsive="true" width="1000" closeOnEscape="true"
              modal="true" onShow="PF('isQuotJobCtgry').initPosition();"  >   

        <h:form id="isQJobCtgrfrm">

            <p:dataTable id="jobCtgryLst" widgetVar="jobCtgryLookup" reflow="true" emptyMessage="No Job Category found."  
                         value="#{jobs.jobCtgryMstList}"  var="jbctg"  rowKey="#{jbctg.recId}"  selection="#{jobs.selectedJobCatgry}"
                         selectionMode="single"  >

                <!--            <p:dataTable id="jobCtgryLst" widgetVar="jobCtgryLookup" reflow="true"
                                         value="#{jobs.jobCtgryMstList}"  var="jbctg"    
                                             rows="10"  
                                             filterEvent="keyup" 
                                             draggableColumns="true"
                                             emptyMessage="No Job Category found."  
                                             paginator="true" 
                                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                             rowsPerPageTemplate="10,20" 
                                             paginatorAlwaysVisible="false" 
                                             rowKey="#{jbctg.recId}"
                                             selection="#{jobs.selectedJobCatgry}"
                                             filteredValue="#{jobs.jobCtgryMstList}"
                                             selectionMode="single"  >-->

<!--               filteredValue="#{viewProductLookUpService.filteredProducts}"
                 selection="#{viewProductLookUpService.selectedProduct}"  rowKey="#{prod.recId}" 
                 selectionMode="single" scrollable="true" scrollHeight="300"
                 emptyMessage="No #{custom.labels.get('IDS_PART_NUM')} found." 
                 paginator="true" rows="10" paginatorAlwaysVisible="true"
                 paginatorPosition="top" styleClass="dt-lookup"
                 var="prod"  widgetVar="prodLookUp">-->




                <p:ajax event="rowSelect" oncomplete="applyJobcategory(); PF('isQuotJobCtgry').hide()" />
                <p:column id="name" rendered="false" filterMatchMode="contains" filterBy="#{jbctg.jobCatgId}" headerText="Name" sortBy="#{jbctg.jobCatgId}"  >
                    #{jbctg.jobCatgId}
                </p:column> 
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:column  filterMatchMode="contains" filterBy="#{jbctg.jobCatgName}" headerText="#{custom.labels.get('IDS_JOB_TYPE')}" sortBy="#{jbctg.jobCatgName}" >
                    #{jbctg.jobCatgName}
                </p:column>


            </p:dataTable>

        </h:form>
    </p:dialog>
</ui:composition>



