
<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core">
    <!--<h4 class="sub_title">#{custom.labels.get('IDS_OPPS')} Information</h4>-->
    <!--//05-10-2023 : #12212 : ESCALATIONS CRM-7406   Jobs: Latency while attaching to opps-->
    <h:form id="frmOppMst">
    <p:dialog id="dlgoppMst" widgetVar="oppMstDlg"  modal="true" width="1280"  header="Add #{custom.labels.get('IDS_OPPS')}"
              height="550" maximizable="true" responsive="true" 
              closeOnEscape="true">
        <p:ajax event="close" onstart="PF('oppMstListTable').clearFilters();"/>
            <p:growl id="msg1" showDetail="false"/>
            <p:dataTable  id="dtOppMstList" widgetVar="oppMstListTable" 
                          value="#{jobOpps.opps}" var="opp" selectionMode="single" 
                          rowKey="#{opp.oppId}" selection="#{jobOpps.selOpp}" 
                         emptyMessage="No open #{custom.labels.get('IDS_OPPS')} found." 
                         paginator="true" rows="10" filteredValue="#{jobOpps.filteredOpps}"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="5,10,15" 
                         paginatorAlwaysVisible="false" class="tblmain">
                <p:ajax event="rowSelect" listener="#{jobOpps.linkOppToJob(jobs.jobId)}" oncomplete='loadJobOpps();' update=":jobDetailsButtonForm:tabviewJobs:pnlCMNTS"/>
                <!--//03-10-2023 : #12212 : ESCALATIONS CRM-7406   Jobs: Latency while attaching to opps-->
                <p:column filterBy="#{opp.custName}" filterMatchMode="contains" sortBy="#{opp.custName}" >
                    <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet> 
    
                    #{opp.custName}
                </p:column>
                
                <!--//03-10-2023 : #12212 : ESCALATIONS CRM-7406   Jobs: Latency while attaching to opps-->
                <p:column filterBy="#{opp.principalName}" filterMatchMode="contains" sortBy="#{opp.principalName}">
                    <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet> 
    
                    #{opp.principalName}
                </p:column>

                <!--//03-10-2023 : #12212 : ESCALATIONS CRM-7406   Jobs: Latency while attaching to opps-->
                <p:column filterBy="#{opp.distriName}" filterMatchMode="contains" sortBy="#{opp.distriName}">
                    <f:facet name="header">#{custom.labels.get('IDS_DISTRI')}</f:facet> 
    
                    #{opp.distriName}
                </p:column>


                <p:column filterBy="#{opp.oppCustProgram}" filterMatchMode="contains" sortBy="#{opp.oppCustProgram}">
                    <f:facet name="header">#{custom.labels.get('IDS_PROGRAM')}</f:facet> 
                    <!--//03-10-2023 : #12212 : ESCALATIONS CRM-7406   Jobs: Latency while attaching to opps-->
                    #{opp.oppCustProgram}
                </p:column> 

                <p:column filterBy="#{opp.oppActivity}" filterMatchMode="contains" sortBy="#{opp.oppActivity}">
                    <f:facet name="header">#{custom.labels.get('IDS_ACTIVITY')}</f:facet>  
                    <!--//03-10-2023 : #12212 : ESCALATIONS CRM-7406   Jobs: Latency while attaching to opps-->
                    #{opp.oppActivity}
                </p:column>
                <!--ESCALATION CRM-7804 please relable Opp "Status" to "Status (autofill)" for Recht only-->
                <p:column filterBy="#{opp.oppStatus}" filterMatchMode="contains" sortBy="#{opp.oppStatus}">
                    <f:facet name="header">#{custom.labels.get('IDS_ACT_STATUS')}</f:facet>  
                    <!--//03-10-2023 : #12212 : ESCALATIONS CRM-7406   Jobs: Latency while attaching to opps-->
                    #{opp.oppStatus}
                </p:column>
                <p:column filterBy="#{opp.oppFollowUp}" filterMatchMode="contains" sortBy="#{opp.oppFollowUp}">
                    <f:facet name="header">#{custom.labels.get('IDS_FOLLOW_UP')}</f:facet> 
                    <!--//03-10-2023 : #12212 : ESCALATIONS CRM-7406   Jobs: Latency while attaching to opps-->
                           <p:outputLabel value="#{opp.oppFollowUp}" >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                    </p:outputLabel>
                </p:column>
                <!--Feature #5025 RE: Repfabric / AVX CRMSync / Relabeling Fields / "Next Step" (Pending)-->
                <!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
                <p:column filterBy="#{opp.oppNextStep}" filterMatchMode="contains" sortBy="#{opp.oppNextStep}" styleClass="might-overflow">
                    <f:facet name="header">#{custom.labels.get('IDS_NEXT_STEP')}</f:facet> 
                    <!--//03-10-2023 : #12212 : ESCALATIONS CRM-7406   Jobs: Latency while attaching to opps-->
                    #{opp.oppNextStep}
                </p:column>
                <!--            Bug 1215:#388851-KAI closed opps showing up under company profile-->
                <!--//03-10-2023 : #12212 : ESCALATIONS CRM-7406   Jobs: Latency while attaching to opps-->
                <p:column  filterMatchMode="contains" sortBy="#{opp.oppCloseStatusLabel}">
                    <f:facet name="header">Status</f:facet> 
                    <p:outputLabel value="#{opp.oppCloseStatusLabel}" />
                </p:column>
            </p:dataTable>
<!--            <center>
                <p:commandButton id="btnProcess" widgetVar="btnBulkProcess" styleClass="btn btn-primary btn-xs" value="Add">

                </p:commandButton>
            </center>-->
        
    </p:dialog>
</h:form>


</ui:composition>


