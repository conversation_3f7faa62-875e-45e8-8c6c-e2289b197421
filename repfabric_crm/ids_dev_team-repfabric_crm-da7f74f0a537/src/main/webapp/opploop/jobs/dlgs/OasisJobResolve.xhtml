<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">

    <!--responsive="true"--> 
    <p:dialog  styleClass="dialogCSS" id="isQuotLnItmListDtlsx2" widgetVar="isQuotLnItmListDtls2"  draggable="false" resizable="false"
               style="text-overflow: scroll;"  height="80%" width="80%" modal="true" >
        <f:facet name="header">
            <h:outputText id="isQuotLnItmListTxt1" value="Resolve Entities" />
        </f:facet> 
        <h:form id="isQfrm3">
            <!--1995 CRM-3372: jobs: Southwest: mappings missing for jobs - JobMfgs, Related Companies-->
            <!--12332  ESCALATION CRM-7457 OASIS: Add specifiers on the job header/basics tab--> 
            <p:remoteCommand name="updResolve" autoRun="false" update=":isQfrm3:isql3"/>
            <!--#{jobs.compResolveList.size()}--> 
            <p:remoteCommand name="applyActivePrinciResolve" autoRun="false" immediate="true" update=":isQfrm3:isql3"
                             actionListener="#{jobs.updateFieldsOnSelectionForOasisResolve(viewCompLookupService.selectedCompany)}" oncomplete="updResolve();"/>
            <p:remoteCommand name="applyOwner" actionListener="#{jobs.applyOasisUser(userLookupService.user)}" update=":isQfrm3:isql3"  oncomplete="updResolve();"/>
            
            <p:commandButton  value="Apply" title="Apply" class="btn btn-primary btn-xs" process="@this" 
                              immediate="true" actionListener="#{jobs.saveResolvedOasisJob()}" disabled="#{jobs.compResolveList.size()==0}"
                              update=":jobDetailsButtonForm:tabviewJobs:dtQuotList :jobDetailsButtonForm:tabviewJobs:compQuotPanel :jobDetailsButtonForm:tabviewJobs"  /> 
 
<!--8472 CRM-5185: Handling Oasis quotes line item for jobs auto-download-->
            <p:dataTable value="#{jobs.compResolveList}" 
                         var="errRec3" id="isql3" sortBy="#{errRec3.recId}" sortOrder="ascending"
                         emptyMessage="No Companies available" 
                         scrollable="true" 
                         scrollHeight="350"   
                         widgetVar="qlWVar2">
                <p:column  width="10%" rendered="false">
                    <f:facet name="header">Company ID</f:facet>
                    <h:outputText value="#{errRec3.companyTypeId}" />
                </p:column>
                <p:column  width="10%">
                    <!--1933 CRM-3372: jobs: Southwest: mappings missing for jobs-->
                    <f:facet name="header">Type</f:facet>
                    <h:outputText value="#{errRec3.companyTypeName}"/>
                </p:column>
                 <!--//                                    12768   ESCALATIONS CRM-7634  Synergy Electrical Sales: Specifier Field-->
                <p:column  width="10%">
                     <f:facet name="header">Sub Type</f:facet>
                    <h:outputText value="#{errRec3.companySubTypeName}"/>
                </p:column>
                
                <p:column width="10%">
                    <f:facet name="header">Oasis Entities</f:facet>
                    <h:outputText value="#{errRec3.quotISCustomerName}" rendered="#{errRec3.companyTypeId==1}"/>
                    <h:outputText value="#{errRec3.quotISPrincipalName}" rendered="#{errRec3.companyTypeId==2}"/>
                    <h:outputText value="#{errRec3.quotISDistrbutorName}" rendered="#{errRec3.companyTypeId==3}"/>
                    <h:outputText value="#{errRec3.quotISDistrbutorName}" rendered="#{errRec3.companyTypeId==12}"/>
                    <h:outputText value="#{errRec3.quotISJobOwnerCompanyName}" rendered="#{errRec3.companyTypeId==5}"/>
                    <h:outputText value="#{errRec3.quotISJobOwnerContactName}" rendered="#{errRec3.companyTypeId==6}"/>
                    <h:outputText value="#{errRec3.quotISJobSalesperson}" rendered="#{errRec3.companyTypeId==7}"/>
                    <h:outputText value="#{errRec3.quotISJobCategory}" rendered="#{errRec3.companyTypeId==8}"/>
                    <h:outputText value="#{errRec3.quotISJobContactName}" rendered="#{errRec3.companyTypeId==9}"/>
                    <h:outputText value="#{errRec3.oasisQuoter}" rendered="#{errRec3.companyTypeId==16}"/>
                </p:column>

                <p:column  width="10%"  rendered="false">
                    <f:facet name="header">REC id</f:facet>
                    <h:outputText value="#{errRec3.recId}"  rendered="true"/>
                </p:column>
                <p:column  width="10%" >
                    <f:facet name="header">City</f:facet>
                    <h:outputText value="#{errRec3.quotISCity}"  rendered="true"/></p:column>
                <p:column  width="10%"  >
                    <f:facet name="header">State</f:facet>
                    <h:outputText value="#{errRec3.quotISState}"  rendered="true"/>                      
                </p:column>
                <p:column  width="10%" >
                    <f:facet name="header">Zip</f:facet>
                    <h:outputText value="#{errRec3.quotISZip}"  rendered="true"/>
                </p:column>
                <p:column width="10%">
                    <f:facet name="header">RF Entities</f:facet>
                    <h:outputText value="#{errRec3.quotCustomerName}" rendered="#{errRec3.companyTypeId==1}"/>
                    <h:outputText value="#{errRec3.quotPrincipalName}" rendered="#{errRec3.companyTypeId==2}"/>
                    <h:outputText value="#{errRec3.quotDistrbutorName}" rendered="#{errRec3.companyTypeId==3}"/>  
                    <h:outputText value="#{errRec3.quotDistrbutorName}" rendered="#{errRec3.companyTypeId==12}"/> 
                    <h:outputText value="#{errRec3.quotJobOwnerCompanyName}" rendered="#{errRec3.companyTypeId==5}"/>
                    <h:outputText value="#{errRec3.quotJobOwnerContactName}" rendered="#{errRec3.companyTypeId==6}"/>
                    <h:outputText value="#{errRec3.quotJobSalespersonName}" rendered="#{errRec3.companyTypeId==7}"/>
                    <h:outputText value="#{errRec3.quotJobCategoryName}" rendered="#{errRec3.companyTypeId==8}"/>
                    <h:outputText value="#{errRec3.quotJobContactName}" rendered="#{errRec3.companyTypeId==9}"/>
                    <h:outputText value="#{errRec3.quotOasisQuoter}" rendered="#{errRec3.companyTypeId==16}"/>

                </p:column>
                <p:column width="10%">
                    <f:facet name="header"></f:facet>
                    <p:inputText type="hidden" value="#{jobs.jobSpecifier}" id="itJobSpcId"/>
                   <!--13107 CRM-7735   OASIS: Pre-fill the new company screen with information from OASIS during aliasing-->
                    <p:commandButton id="_btnCompPrinciSearch" icon="fa fa-search" title="Choose Company" rendered="#{errRec3.companyTypeId==2}"
                                     actionListener="#{jobs.listLookUpvaluesForResolve('applyActivePrinciResolve', 1,errRec3.recId,errRec3.quotISPrincipalName)}"
                                     update=":formCompLookup :frmLookupHead" immediate="true" process="@this"  oncomplete="PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />
<!--                    <p:commandButton id="_btnCompCustSearch" icon="fa fa-search"  title="Choose Company"  rendered="#{errRec3.companyTypeId==1}"
                                     actionListener="#{jobs.listLookUpvaluesForResolve('applyActivePrinciResolve', 5,errRec3.recId,errRec3.quotISPrincipalName)}"
                                     update=":formCompLookup" immediate="true" process="@this"
                                     styleClass="btn-info btn-xs" />-->
                    <p:commandButton id="_btnCompDistriSearch" icon="fa fa-search" title="Choose Company" rendered="#{errRec3.companyTypeId==3}"
                                     actionListener="#{jobs.listLookUpvaluesForResolve('applyActivePrinciResolve', 9,errRec3.recId,errRec3.quotISDistrbutorName)}"
                                     update=":formCompLookup :frmLookupHead" immediate="true" process="@this"  oncomplete="PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />
<!--//                                   12332  ESCALATION CRM-7457 OASIS: Add specifiers on the job header/basics tab-->      
                    <p:commandButton id="_btnCompSpecifierSearch" icon="fa fa-search" title="Choose Company" rendered="#{errRec3.companyTypeId==12}"
                                     actionListener="#{jobs.listLookUpvaluesForResolve('applyActivePrinciResolve', 12,errRec3.recId,errRec3.quotISDistrbutorName)}"
                                     update=":formCompLookup :frmLookupHead" immediate="true" process="@this"  oncomplete="PF('compLookup').filter();"
                                     styleClass="btn-info btn-xs" />
                      <!--13375  CRM-7815   OASIS: Assign the quotes to the correct user when ingesting from OASIS-->
                    <p:commandButton id="btnOwnerSearch" icon="fa fa-search" title="Choose User" immediate="true"  rendered="#{errRec3.companyTypeId==16}"
                                     actionListener="#{userLookupService.listOasisOwner2('applyOwner',errRec3.recId)}" 
                                     update=":formUserLookup" oncomplete="PF('lookupUser').show();"   
                                     styleClass="btn-info btn-xs" />
                    
                <!--13107 CRM-7735   OASIS: Pre-fill the new company screen with information from OASIS during aliasing-->    
                </p:column> 
            </p:dataTable>
        </h:form>
    </p:dialog>

<!--    <p:dialog id="jobCtgryx2" header="Job Category Lookup" widgetVar="isQuotJobCtgry" responsive="true" width="1000" closeOnEscape="true"
              modal="true" onShow="PF('isQuotJobCtgry').initPosition();"  >   
        <h:form id="isQJobCtgrfrm2">
            <p:dataTable id="jobCtgryLst2" widgetVar="jobCtgryLookup" reflow="true" emptyMessage="No Job Category found."  
                         value="#{jobs.jobCtgryMstList}"  var="jbctg"  rowKey="#{jbctg.recId}"  selection="#{jobs.selectedJobCatgry}"
                         selectionMode="single"  >
                <p:ajax event="rowSelect" oncomplete="applyJobcategory(); PF('isQuotJobCtgry').hide()" />
                <p:column id="name2" rendered="false" filterMatchMode="contains" filterBy="#{jbctg.jobCatgId}" headerText="Name" sortBy="#{jbctg.jobCatgId}"  >
                    #{jbctg.jobCatgId}
                </p:column>    
                <p:column  filterMatchMode="contains" filterBy="#{jbctg.jobCatgName}" headerText="Type" sortBy="#{jbctg.jobCatgName}" >
                    #{jbctg.jobCatgName}
                </p:column>
            </p:dataTable>
        </h:form>
    </p:dialog>-->
</ui:composition>



