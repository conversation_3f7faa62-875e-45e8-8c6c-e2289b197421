<ui:composition
  xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
 <h:form id="formCompsMultiLookup">
    <p:dialog id="dlgCompsMultiLookup" header="Company Lookup" widgetVar="lookupMultiComps" responsive="true" width="1000" closeOnEscape="true"
              modal="true" onShow="PF('lookupMultiComps').initPosition();" height="550">
        <f:facet name ="header">
            <div id="frmMultiLookupHead" style="width: 100%; display: flex; justify-content: space-between">
                Company Lookup
                    <p:remoteCommand name="initNewComp" autoRun="false" id="initNewComp" immediate="true" process="@this" action="#{viewCompLookupService.initiliaze()}"
                                   update=":formNewComp" oncomplete="PF('newCompDlg').show()"/>
                    <p:outputPanel id="opBtnsCompMultiLooup" >
                        <p:commandButton value="Add" style="float: right; padding-right: 5px;"  styleClass="btn btn-primary btn-xs" actionListener="#{viewJobsRelatedComps.loadRelatedComms1(viewCompLookupService.selectedCompanies, jobs.jobId)}"  
                            oncomplete="#{viewCompLookupService.remoteCmdName}();PF('lookupMultiComps').hide()" />
                      
                        <p:commandButton id="btnNewCompany" value="New" style="float: left; margin-right: 13px"  resetValues="true" onclick="initNewComp();"
                            title="#{loginBean.userCompanyCreateAccess()? 'You are not authorized to create company': 'Add new Company'}" 
                            rendered="#{(viewCompLookupService.showNew ==1) and (viewCompLookupService.newCompViewMode==0) and (viewCompLookupService.llokupFlag==null) and (viewCompLookupService.typeId!=12)}"
                            disabled="#{loginBean.userCompanyCreateAccess()}" immediate="true"
                            styleClass="btn btn-primary btn-xs" />
                                                  <p:spacer width="4px" />

                    </p:outputPanel>
            </div>
        </f:facet>
       
           
           
            <p:focus context="companiesLookUp:name"/>
            
            <!--18-09-2024 14515 Job->Related companies Pagination not Working-->
            <p:dataTable id="companiesLookUp" value="#{viewCompLookupService.companies}" widgetVar="dlgCompLookup" var="comp"  filteredValue="#{viewCompLookupService.filteredCompanies}"
                         selection="#{viewCompLookupService.selectedCompanies}"  rowSelectMode="add" 
                        paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="10,15,25"
                         pageLinks="4" styleClass="comp-lookup"
                         rowKey="#{comp.compId}"  paginator="true"  rows="10" paginatorPosition="top" >
                <p:column  selectionMode="multiple" style="width: 7%;text-align: center" />
                <p:column id="name"  filterMatchMode="contains" filterBy="#{comp.compName}" headerText="Name" sortBy="#{comp.compName}"    >
                    #{comp.compName}
                </p:column>    
                <p:column  filterMatchMode="contains" filterBy="#{comp.compTypeName}" headerText="Type" sortBy="#{comp.compTypeName}" >
                    #{comp.compTypeName}
                </p:column>   
                <p:column  filterMatchMode="contains" filterBy="#{comp.smanName}" headerText="Sales Team" sortBy="#{comp.smanName}" >
                    #{comp.smanName}
                </p:column>  
                <p:column  filterMatchMode="contains" filterBy="#{comp.compPhone1}" headerText="Phone" sortBy="#{comp.compPhone1}" >
                    #{comp.compPhone1}
                </p:column>
                <p:column  filterMatchMode="contains" filterBy="#{comp.regionName}" headerText="Region" sortBy="#{comp.regionName}" >        
                    #{comp.regionName}
                </p:column>
                 <p:column  filterMatchMode="contains" filterBy="#{comp.compAddress1}" headerText="Street" sortBy="#{comp.compAddress1}" >
                    #{comp.compAddress1}
                </p:column>
                <p:column  filterMatchMode="contains" filterBy="#{comp.compCity}" headerText="City" sortBy="#{comp.compCity}" >
                    #{comp.compCity}
                </p:column>
                <p:column  filterMatchMode="contains" filterBy="#{comp.compState}" headerText="State" sortBy="#{comp.compState}" filterValue="#{viewCompLookupService.filterCompState}" >
                    #{comp.compState}
                </p:column>
                <p:column  filterMatchMode="contains" filterBy="#{comp.compZipCode}" headerText="Zip" sortBy="#{comp.compZipCode}" >
                    #{comp.compZipCode}
                </p:column>
            </p:dataTable>
       
    </p:dialog>
      </h:form> 
    <style>
        body .ui-dialog.ui-widget-content .ui-dialog-titlebar .ui-dialog-title, body .ui-dialog .ui-dialog-titlebar .ui-dialog-title {

            float: left;

            font-size: 18px;

            font-weight: 500;
            width: 95%;

            color: #444;

            margin: 0.3em 16px 0.3em 0;
            margin-right: 15px;

         }
    </style>
</ui:composition>



