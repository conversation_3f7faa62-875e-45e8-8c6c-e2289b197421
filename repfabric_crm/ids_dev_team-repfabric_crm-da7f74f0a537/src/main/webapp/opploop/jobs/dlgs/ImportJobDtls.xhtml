<?xml version="1.0" encoding="UTF-8"?>
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">

    <p:dialog  styleClass="dialogCSS" id="dlgImpJobDtls" widgetVar="dlgImpJobDtls"  responsive="true" 
               style="text-overflow: scroll; text-align: left !important;"  height="600" width="1000" modal="true">
        <f:facet name="header">
            <h:outputText id="hdrImpText" value="Import #{custom.labels.get('IDS_JOBS')}"/>
        </f:facet>
        <p:panelGrid id="gridImpJobDtls" styleClass="panlGrid">
        </p:panelGrid>
        <!--<p:ajax event="close" listener="#{quotesHdr.initializeQuotes()}"   process="@this"/>-->
        <div class="ui-g ui-fluid"> 
            <div class="ui-sm-12 ui-md-9 ui-lg-9">
                <p:outputLabel  value="Select a csv/xls/xlsx file to import"/>
            </div>
            <div class="ui-sm-12 ui-md-3 ui-lg-3">
                <p:commandButton icon="fa fa-info-circle" class="btn-xs btn-info" title="Help" onclick="PF('helpDlg').show();" style="height: 27px;width:27px; float: right"/>
            </div>

            <div class="ui-sm-12 ui-md-10 ui-lg-10">
                <p:fileUpload  fileUploadListener="#{jobs.startImport}" disabled="#{jobs.statusList.size()>0}"
                               label="Select File" id="qtUpld" multiple="false" fileLimit="1"
                               uploadLabel="Load File" mode="advanced"
                               update="jobsForm:er jobsForm:op jobsForm:impCnt jobsForm:oq jobsForm:qtUpld" />
                <!--quotefrom:op quotefrom:er quotefrom:oq quotefrom:qtUpld-->
            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputPanel id = "oq" >   
                    <p:commandButton value="Import" title="Import" rendered="#{jobs.statusList.size()>0 and jobs.showExportBtn==false}" id="btnImportJobs" onclick="PF('impDlg').show();" class="btn btn-xs btn-success" />
                    <p:spacer width="4"/>
                    <p:commandButton value="Export" title="Export error records"  rendered="#{jobs.statusList.size()>0 and jobs.showExportBtn==true}"  ajax="true" onclick="PrimeFaces.monitorDownload(startdlg, stopdlg);"  
                                     action="#{jobs.exportData()}" id="btnExportJobs" class="btn btn-xs btn-warning" />





                </p:outputPanel>   
            </div>
            <p:outputPanel id = "impCnt">
                <!--<div class="ui-sm-12 ui-md-2 ui-lg-2">-->
                Total records:  #{jobs.recordCounter} <p:spacer width="4"/>
                <!--</div>-->
                <!--<div class="ui-sm-12 ui-md-2 ui-lg-2">-->
                    Failed records: #{jobs.recordsFailed} <p:spacer width="4"/>
<!--                </div>
                <div class="ui-sm-12 ui-md-2 ui-lg-2">-->
                    Successful records: #{jobs.recordsPassed}
<!--                </div> 
                <div class="ui-sm-12 ui-md-6 ui-lg-6">

                </div> -->
            </p:outputPanel>
            <div class="ui-sm-12 ui-md-12 ui-lg-12" style="text-align: center">
                <h:outputText value="#{users.impStatusStr}"  id="txtStatus" style="color:red;font-weight: bold;font-size: 15px;"/> 
            </div>
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                <p:outputPanel id = "op">
                    <ui:include src="ErrorTable.xhtml" />
                </p:outputPanel>
            </div>
        </div>
    </p:dialog>
    <p:confirmDialog widgetVar="impDlg" header="Confirmation"  >
        <f:facet name="message">
            <p:outputLabel id="msg" value="Only data ready to be imported will be saved. Do you want to Continue?" />
        </f:facet>
        <div align="center">
            <p:commandButton value="Proceed" action="#{jobs.savePopulatedRecords()}"   update=":jobsForm:oq :jobsForm:tblJobs :jobsForm:tblJobs:jobFilter1 :jobsForm:tblJobs:jobFilter" onclick="PF('impDlg').hide();"  class="btn btn-xs btn-primary" >
            </p:commandButton>
            <p:spacer width="4px"/>
            <p:commandButton value="Cancel" class="btn btn-xs btn-danger" onclick="PF('impDlg').hide();"/>
        </div>
    </p:confirmDialog>  
<!--04-11-2024 14809 ESCALATIONS CRM-8642   Jobs: Sorting by name not working-->
</ui:composition>