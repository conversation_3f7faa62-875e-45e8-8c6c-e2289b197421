<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--March 23 2020-->

    <p:dialog  styleClass="dialogCSS" id="isQuotJobListDtlsx" widgetVar="isQuotJobListDtls"  responsive="true" draggable="false" resizable="false"
               style="text-overflow: scroll;"  height="80%" width="80%" modal="true">
        <f:facet name="header">
            <h:outputText id="isQuotListTxt" value="ISQuote List" />
        </f:facet>
        <h:form id="isQfrm"> 
            <!--1935 CRM-3373: jobs: southwest: fetching jobs not reinitializing #1-->
            <p:outputLabel   id="dtIsQtLbl" value="Jobs for : " style="font-weight: bold;" /><p:spacer/>
            <p:outputLabel rendered="#{jobs.filterISQuotesStatus!=2}" id="dtIsQt" value="#{jobs.paramISQuotDate}" style="font-weight: bolder;" >
                <f:convertDateTime pattern="#{globalParams.dateFormat}" />  
            </p:outputLabel>
            <!--1935 CRM-3373: jobs: southwest: fetching jobs not reinitializing #1-->
            <p:outputLabel rendered="#{jobs.filterISQuotesStatus==2}" id="dtIsQts" value="#{jobs.filterISQuotes}" style="font-weight: bolder;" />
            <!--2224 CRM-3379: Workflow of Quotes to jobs with ISQuote is bad-->
            <p:spacer width="10"/>
            <p:selectBooleanCheckbox  id="updNrmlLnItmReq" style="text-align: right;"
                                             itemLabel="Pull related records" 
                                             value="#{jobs.updateISQutesQuoteDtls}"  >
                <p:ajax event="change" listener="#{jobs.onChangeChckBox(jobs.updateISQutesQuoteDtls)}"/>
            </p:selectBooleanCheckbox>
              
            <!--#{oAuthLogin.quotLookUp.get(0)}-->
            <!--rendered="#{oAuthLogin.quotLookUp.size()>0}"-->
            <!--1461 CRM-3100: FW: Questions/Comments from SOUTHWEST-->
            <p:dataTable value="#{iSQuoteCrmClient.jobLookUp}" rows="10"
                         var="isqHd" id="isq" rowIndexVar="isqRecc" 
                         emptyMessage="No Jobs available" 
                         scrollable="true"
                         scrollHeight="360"  
                         widgetVar="isqRecc"
                         selection="#{jobs.selectedTSQhdr}"
                         selectionMode="single" 
                         rowKey="#{isqHd.recId}" paginator="true" 
                         >
                <!--2224 CRM-3379: Workflow of Quotes to jobs with ISQuote is bad-->
                <!--5802 Jobs: duplicate prevention for jobs-->
                <!--14469: 08/10/2024: CRM-8480   ISQuote: job caught in aliaser hangs system when manually fetched-->
                <p:ajax event="rowSelect" onstart="PF('progressDlg123').show()" listener="#{jobs.selectSFJob}" process="@this" immediate="true" update=":isQfrm2:isql :isQuotLnItmListDtlsx :jobDetailsButtonForm" oncomplete="PF('progressDlg123').hide()" />
                <!--  PF('qlWVar').clearFilters();  :quotefrom:isql  :quotefrom:tabQuot :isQuotLnItmListDtlsx-->
                <p:column  width="2%"> 
                    <f:facet name="header"></f:facet>
                        #{isqRecc+1}
                </p:column> 
                <p:column width="10%" >
                    <f:facet name="header">Job No.</f:facet>
                        #{isqHd.jobNo} 
                    <!--#{isqHd.recId}-->
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Job Name</f:facet>
                        #{isqHd.jobDescr==null?'':isqHd.jobDescr} 
                </p:column>
                <p:column width="10%" rendered="false">
                    <f:facet name="header">Job Specifier</f:facet>
                        #{isqHd.jobSpecifierName==null?'':isqHd.jobSpecifierName} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Salesperson ID</f:facet>
                        #{isqHd.salespersonIdStr==null?'':isqHd.salespersonIdStr} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Job Category</f:facet>
                        #{isqHd.jobCategoryStr==null?'':isqHd.jobCategoryStr} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Job City</f:facet>
                        #{isqHd.jobcity==null?'':isqHd.jobcity} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Est. Job Value</f:facet>
                        #{isqHd.jobEstValue==null?'':isqHd.jobEstValue} 
                </p:column>
                <p:column width="10%" >
                    <f:facet name="header">Date Changed</f:facet> 
                    <h:outputLabel   value="#{rFUtilities.convertFromUTC(isqHd.updDate)}" >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" for="isqHd.updDate"/>
                    </h:outputLabel>
                </p:column>


                <!--         <p:column width="10%">
                            <f:facet name="header">Salesperson ID</f:facet>
                #{isqHd.oppSManName=='null'?'':isqHd.oppSManName} 
        </p:column>
        <p:column width="10%">
            <f:facet name="header">Customer Name</f:facet>
                #{isqHd.oppCustomerName=='null'?'':isqHd.oppCustomerName} 
        </p:column>  
        <p:column width="10%">
            <f:facet name="header">Customer City</f:facet>
                #{isqHd.cityName=='null'?'':isqHd.cityName} 
        </p:column>
        <p:column width="10%" rendered="false">
            <f:facet name="header">Contact Name</f:facet>
                #{isqHd.oppPrincipalContName=='null'?'':isqHd.oppPrincipalContName} 
        </p:column>
        <p:column width="10%">
            <f:facet name="header">Quote Date</f:facet>
                #{isqHd.quotDate=='null'?'':isqHd.quotDate} 
                <h:outputLabel   value="#{rFUtilities.convertFromUTC(isqHd.quotDate)}" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" for="isqHd.quotDate"/>
                </h:outputLabel>
            </p:column>-->
            </p:dataTable>

        </h:form>
    </p:dialog>
</ui:composition>



