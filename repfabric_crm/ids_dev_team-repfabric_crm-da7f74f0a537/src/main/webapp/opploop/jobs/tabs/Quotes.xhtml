<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core">

    <p:remoteCommand autoRun="true" name="loadJobQuotes" actionListener="#{viewJobQuotes.populateJobQuotes(jobs.jobId)}" update="compQuotPanel"  oncomplete="PF('quotListTable').clearFilters()"  /> 
    <p:outputPanel id="compQuotPanel"> 
        <h:link>
            <p:commandButton class="btn btn-primary btn-xs" 
                             value="Add Existing Quote" 
                             oncomplete="PF('quotMstDlg').show(); PF('quotMstListTable').clearFilters();"                              
                             actionListener="#{jobQuotes.loadJobQuotes(jobs.jobId)}"
                             update=":frmQuotMst :frmQuotMst:dtQuotMstList :jobDetailsButtonForm:tabviewJobs:dtQuotList "
                             /> 
            <p:spacer width="4" ></p:spacer>
            <p:commandButton value="New"  
                             styleClass="btn btn-primary btn-xs"  oncomplete="window.open('../../opploop/quotes/NewQuote.xhtml?jobId=#{jobs.jobId}', '_blank');"  /> 


        </h:link> 
        <p:spacer width="4px"/>  
        <!--2676 CRM-3379: Workflow of Quotes to jobs with ISQuote is bad #3-->
        <!--4872 CRM Sync: Settings for Oasis-->
        <h:link rendered="#{jobs.crmsyncEnabled and jobs.crmSystemId==6}">
            <!--2676 CRM-3379: Workflow of Quotes to jobs with ISQuote is bad #3   rendered="#{jobs.crmsyncJobId.equalsIgnoreCase("")==false}"-->
            <!--3579 ISQuote > Updates to linking child (Distributor) quotes-->
            <!--4872 CRM Sync: Settings for Oasis-->
            <p:commandButton  value="Refresh" rendered="#{jobs.crmSystemId==6}" 
                              title="Refresh" 
                              class="btn-xs btn-primary" actionListener="#{jobs.pullReltdQuotes}"
                              process="@this" immediate="true"  
                              action="#{jobs.refreshSFJobss(jobs.syncUserForIsQuote)}"
                              update=":isQfrm2 :isQuotLnItmListDtlsx :isQfrm2:isql :isQuotLnItmListDtlsx :jobDetailsButtonForm:tabviewJobs"
                              />  
        </h:link> 
        <p:spacer width="4px"/>  


        <p:outputLabel value="Show Closed"  />
        <p:spacer width="4px"/>  
        <p:selectBooleanCheckbox  itemLabel="#{viewJobQuotes.checkFlag ? '':''}"  value="#{viewJobQuotes.checkFlag}"     id="quoteClose" widgetVar="closeStatChk">
            <p:ajax event="change"  listener="#{viewJobQuotes.populateJobQuotes(jobs.jobId)}"  />
        </p:selectBooleanCheckbox>
        <!--        <h:link>
                    <p:commandButton   icon="fa fa-refresh"  
                                       title="Refresh" 
                                       class="btn-xs btn-primary"
                                       action="#{viewJobQuotes.populateJobQuotes(jobs.jobId)}"
                                       onclick="PF('quotListTable').clearFilters();"
                                       update="dtQuotList"
                                       />
                </h:link>-->
        <br/><br/>
        <!--#2813: CRM-3686: BDA-In: isquote: 5 or more quotes in a job does not show all the quotes without refresh-->
        <p:dataTable id="dtQuotList" widgetVar="quotListTable" value="#{viewJobQuotes.jobQuotViewList}" 
                     var="quot" rowKey="#{quot.recId}" selection="#{viewJobQuotes.selectedQuotView}" 
                     emptyMessage="No open #{custom.labels.get('IDS_QUOTES')} found." 
                     paginator="true" rows="5" filteredValue="#{viewJobQuotes.filteredJobQuotViewList}"
                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                     rowsPerPageTemplate="5,10,15" 
                     paginatorAlwaysVisible="true" class="tblmain" draggableColumns="true">
            <f:facet name="header">
                <p:inputText style="display: none"   id="globalFilter" onkeyup="PF('quotListTable').filter()" />
            </f:facet>
            <!--//Feature #4339:CRM-3916: HSArep: ISQuote: surface quote type in job (download only type “Buy”)-->
            <!--
                          <p:column id="closedQuotes"  style="width:75px;height:15px">
                              
                          </p:column>-->
            <!--//Feature #14302:CRM-8381: Jobs > quotes tab: added a width for column "oppCustomerName")--> 
            <p:column width="15%" headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{quot.oppCustomerName}"  sortBy="#{quot.oppCustomerName}" id="custH" filterMatchMode="contains">
                <h:link outcome="/opploop/quotes/NewQuote.xhtml?recId=#{quot.recId}" >
                    #{quot.oppCustomerName}
                </h:link>
            </p:column>
            <!--//Feature #14302:CRM-8381: Jobs > quotes tab: added a width for column "oppPrincipalName")--> 
            <p:column width="15%" headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{quot.oppPrincipalName}" sortBy="#{quot.oppPrincipalName}" id="prinH"  filterMatchMode="contains">
                <h:link outcome="/opploop/quotes/NewQuote.xhtml?recId=#{quot.recId}" >
                    #{quot.oppPrincipalName} 
                </h:link>
            </p:column>

<!--            <p:column headerText="#{custom.labels.get('IDS_SALES_TEAM')}" filterBy="#{quot.oppSManName}" sortBy="#{quot.oppSManName}"  
                    filterMatchMode="contains"   id="smanHId">
                <h:link outcome="/opploop/quotes/NewQuote.xhtml?recId=#{quot.recId}" >
            #{quot.oppSManName} 
        </h:link>
    </p:column>-->
            <!--//Feature #14302:CRM-8381: Jobs > quotes tab: added a width for column "oppDistriName")--> 
            <p:column width="20%" headerText="#{custom.labels.get('IDS_DISTRI')}" filterBy="#{quot.oppDistriName}"   sortBy="#{quot.oppDistriName}"  
                      filterMatchMode="contains" id="distriHId">
                <h:link outcome="/opploop/quotes/NewQuote.xhtml?recId=#{quot.recId}" >
                    #{quot.oppDistriName}  
                </h:link>
            </p:column>
        <!--//Feature #14302:CRM-8381: Jobs > quotes tab: added new column "quotOwnerName")-->    
        <!--//Feature #14302:CRM-8381: Jobs > quotes tab: added a width for column "quotOwnerName")--> 
           <p:column width="20%" headerText="#{custom.labels.get('IDS_OWNER')}" filterBy="#{quot.quotOwnerName}"   sortBy="#{quot.quotOwnerName}"  
                      filterMatchMode="contains" id="quotOwnerName" >
                <h:link outcome="/opploop/quotes/NewQuote.xhtml?recId=#{quot.recId}" >
                    #{quot.quotOwnerName}  
                </h:link>
            </p:column> 

<!--            <p:column headerText="Secondary #{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{quot.oppSecCustomerName}"  sortBy="#{quot.oppSecCustomerName}"  
                      filterMatchMode="contains" id="secCustHId">
                <h:link outcome="/opploop/quotes/NewQuote.xhtml?recId=#{quot.recId}" >
            #{quot.oppSecCustomerName}      
        </h:link>
    </p:column>-->
            <!--//Feature #14302:CRM-8381: Jobs > quotes tab: added a width for column "oppCustProgram")--> 
            <p:column width="15%" headerText="#{custom.labels.get('IDS_PROGRAM')}" filterBy="#{quot.oppCustProgram}"  filterMatchMode="contains" sortBy="#{quot.oppCustProgram}"  id="prog">
                <h:link outcome="/opploop/quotes/NewQuote.xhtml?recId=#{quot.recId}" >
                    #{quot.oppCustProgram}       
                </h:link>
            </p:column>
 <!--//Feature #14302:CRM-8381: Jobs > quotes tab: added a width for column "quotNumber")--> 
            <p:column width="15%" filterMatchMode="contains" headerText="Quote #" filterBy="#{quot.quotNumber}"  sortBy="#{quot.quotNumber}" >
                <h:link outcome="/opploop/quotes/NewQuote.xhtml?recId=#{quot.recId}" >
                    #{quot.quotNumber}  
                </h:link>
            </p:column> 
 <!--//Feature #14302:CRM-8381: Jobs > quotes tab: added a width for column "quotDate")--> 
            <p:column width="12%" filterMatchMode="contains" headerText="Date" filterBy="#{quot.quotDate}"  sortBy="#{quot.quotDate}" >
                <h:link outcome="/opploop/quotes/NewQuote.xhtml?recId=#{quot.recId}" >
                    #{quot.quotDate} 
                </h:link>
            </p:column>
            <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
            <!--//Feature #14302:CRM-8381: Jobs > quotes tab: added a width for column "oppValue")--> 
            <p:column width="12%" filterMatchMode="contains" headerText="Value" filterBy="#{quot.oppValue}"  sortBy="#{quot.oppValue}" 
                       id="valHId">
                <h:link outcome="/opploop/quotes/NewQuote.xhtml?recId=#{quot.recId}" >
                    #{quot.oppValue}
                </h:link>
            </p:column>
            <!--//Feature #14302:CRM-8381: Jobs > quotes tab: added a width for column "delivStatusName")--> 
            <p:column width="12%" filterMatchMode="contains" headerText="Status" filterBy="#{quot.delivStatusName}"  sortBy="#{quot.delivStatusName}" id="statH">
                <h:link outcome="/opploop/quotes/NewQuote.xhtml?recId=#{quot.recId}" >
                    #{quot.delivStatusName}     
                </h:link>
            </p:column>



            <p:column width="12%">
                <p:commandLink title="Unlink #{custom.labels.get('IDS_QUOTES')}" oncomplete="PF('unlnkConDlg').show()" action="#{viewJobQuotes.setIDToUnlink(quot.recId,jobs.jobId)}">
                    <h:graphicImage name="unlink.png" library="images"  height="20" width="20"/> 
                </p:commandLink>
<!--//Feature #4339:CRM-3916: HSArep: ISQuote: surface quote type in job (download only type “Buy”)-->
    
                <p:spacer width="15" />
                <p:commandButton id="btnClose" style="height:25px;width:25px;" styleClass="#{viewJobQuotes.getJobQuoteStatus(quot.recId)==1 ? 'btn-danger':'btn-success'}  btn-xs" title="#{viewJobQuotes.setTitle(viewJobQuotes.getJobQuoteStatus(quot.recId))}" icon="#{viewJobQuotes.getJobQuoteStatus(quot.recId)==1 ?' fa fa-close':'fa fa-rotate-left'}"  actionListener="#{viewJobQuotes.setQuoteClose(viewJobQuotes.getJobQuoteStatus(quot.recId) ,quot.recId,jobs.jobId)}" update=":jobDetailsButtonForm:tabviewJobs:dtQuotList"  >
                    <p:confirm header="Confirmation" message="#{viewJobQuotes.setConfirmMsg(viewJobQuotes.getJobQuoteStatus(quot.recId))}" />
                </p:commandButton>
                 
            </p:column> 
        </p:dataTable>
    </p:outputPanel>



    <p:confirmDialog header="Confirm Unlinking"  global="false"
                     message="Are you sure to unlink this #{custom.labels.get('IDS_QUOTES')}?" 
                     widgetVar="unlnkConDlg" >
        <div style="text-align: center;">
            <p:commandButton value="Unlink" 
                             styleClass="btn btn-danger btn-xs"
                             action="#{viewJobQuotes.unlinkOpp()}"
                             oncomplete="PF('quotListTable').clearFilters();" 
                             update="dtQuotList"
                             />
            <p:spacer width="4 px" />
            <p:commandButton value="Cancel" 
                             styleClass="btn btn-warning btn-xs"
                             onclick="PF('unlnkConDlg').hide()"
                             type="button"  />
        </div>
    </p:confirmDialog>

    <!--//Feature #4339:CRM-3916: HSArep: ISQuote: surface quote type in job (download only type “Buy”)-->


    <p:growl id="message" showDetail="true" />
    <p:confirmDialog global="true" showEffect="fade">
        <div class="div-center" >
            <!--14-12-2020:TASK#2714-Sub Tables > Contact Group, Contact Product Interest - Add Ids -->
            <p:commandButton id="btnYes" value="Yes" type="button" styleClass="ui-confirmdialog-yes btn btn-success btn-xs"  />
            <p:spacer width="4px" />
            <!--14-12-2020:TASK#2714-Sub Tables > Contact Group, Contact Product Interest - Add Ids -->
            <p:commandButton id="btnNo" value="No" type="button" styleClass="ui-confirmdialog-no btn btn-danger btn-xs"  />
        </div>
    </p:confirmDialog>
    


</ui:composition>

