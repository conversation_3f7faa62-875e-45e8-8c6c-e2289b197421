<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: RelatedCompsData.xhtml
// Author: Sharvani
//*********************************************************
* Copyright (c) 2016 Indea Design Systems, A Proprietary Firm, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://java.sun.com/jsf/facelets"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://java.sun.com/jsf/core">


    <style>
        #reltdCompForm\3AreldCompDT .ui-datatable-scrollable-header{
            background-color: white;
        }
    </style>

    <!--Button to add a related company-->
    <!--    //PMS:2512
        //seema - 25/11/2020-->
    <!-- 30-12-2020:Task#2856-CRM-3683: bda-in: Jobs needs a related company delete button-->
    <div class="ui-g ui-fluid">
        <!--13307: 08/03/2024: CRM-7828   Jobs: Add check boxes to add companies in "Related" tab-->
 <p:remoteCommand name="applyMultiComps" actionListener="#{viewSalesMainService.applyCustomers(lookupService.customers)}"  update="reldCompDT" oncomplete="PF('relatedCompTbl').clearFilters()" />
        <div class="ui-sm-12 ui-md-1 ui-lg-1">
            <!--#8309: CRM-5838  whbsales/jon wiggs/Jobs match to opp and quote are totally broken - poornima - changed title-->
            <p:commandButton value="Add" title="Add Related Companies"  
                             class="btn btn-primary btn-xs"   
                             actionListener="#{viewCompLookupService.listAll('applyMultiComps')}" 
                             immediate="true"
                             update=":formCompsMultiLookup" oncomplete="PF('lookupMultiComps').show();PF('dlgCompLookup').clearFilters();"
                             id="btnRelatedCompAdd" />   
        </div>
        <!--       30-12-2020:Task#2856-CRM-3683: bda-in: Jobs needs a related company delete button-->
        <p:spacer width="10"/>
        <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
            <p:commandButton id="btnRelCompDelete" value="Delete" class="btn btn-danger btn-xs "  onclick="PF('confirmation').show();" disabled="#{viewJobsRelatedComps.selectedOppReltComps.size() == 0}"  >

            </p:commandButton>

        </div>
        <!--       30-12-2020:Task#2856-CRM-3683: bda-in: Jobs needs a related company delete button-->
        <div class="ui-sm-12 ui-md-10 ui-lg-10">

        </div>
    </div> 
    <!--//27-03-2023 : #10869 : CRM-6764 : Jobs: make related company tab fields editable.-->
    <br/>
    <div class="ui-g ui-fluid">
        <div class="ui-sm-12 ui-md-1 ui-lg-1">
            <p:outputLabel value="Show:" style="margin-top:7px;"/>
        </div>
        <div class="ui-sm-12 ui-md-10 ui-lg-10">
            <p:selectOneRadio id="console" value="#{viewJobsRelatedComps.jobRadio}" layout="responsive" columns="4">
                <f:selectItem itemLabel="ALL" itemValue="ALL"/>
                <f:selectItem itemLabel="#{custom.labels.get('IDS_PRINCIS')}" itemValue="MFGS"/>
                <f:selectItem itemLabel="#{custom.labels.get('IDS_DISTRIS')}" itemValue="DISTRIBUTOR"/>
                <f:selectItem itemLabel="#{custom.labels.get('IDS_CUSTOMERS')}" itemValue="CUSTOMERS"/>
                <p:ajax event="change" listener="#{viewJobsRelatedComps.radioValues(jobs.jobId)}" 
                        update=":jobDetailsButtonForm:tabviewJobs:reldCompDT" oncomplete="PF('relatedCompTbl').filter()"/>
            </p:selectOneRadio>
        </div>
        <div class="ui-sm-12 ui-md-1 ui-lg-1"></div>
    </div>
    <br/>
    <!--//27-03-2023 : #10869 : CRM-6764 : Jobs: make related company tab fields editable.-->
    <p:remoteCommand autoRun="false" name="jobMfgNotesRemote" actionListener="#{viewJobsRelatedComps.loadAllData()}" 
                     update=":jobDetailsButtonForm:tabviewJobs:panelGridJobMfgNotes" oncomplete="PF('dlgProcessingOnTask').hide();PF('mfgNotesdlg').show();"/>
    <p:remoteCommand autoRun="true" name="loadRelated" actionListener="#{viewJobsRelatedComps.populateViewRelatedComps(jobs.jobId)}" update="reldCompDT" oncomplete="PF('relatedCompTbl').clearFilters()"/>
    <p:remoteCommand name="applyRelatedComp"  
                     actionListener="#{viewJobsRelatedComps.loadRelatedComms(viewCompLookupService.selectedCompany, jobs.jobId)}"
                     update="reldCompDT" oncomplete="PF('relatedCompTbl').clearFilters()" />
    <!-- 30-12-2020:Task#2856-CRM-3683: bda-in: Jobs needs a related company delete button-->
    <!--//27-03-2023 : #10869 : CRM-6764 : Jobs: make related company tab fields editable.-->
   <!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
    <p:dataTable value="#{viewJobsRelatedComps.jobLOppReltList}" var="comp" 
                 id="reldCompDT" 
                 widgetVar="relatedCompTbl"
                 selection="#{viewJobsRelatedComps.selectedOppReltComps}"
                 multiViewState="true" scrollable="true"
                 rowKey="#{comp.recId}" resizableColumns="true"
                  draggableColumns="true" 
                 emptyMessage="No companies found" 
                 filteredValue="#{viewJobsRelatedComps.filteredJobOppReltwList}"
                 paginator="true" rowIndexVar="index" 
                 rowsPerPageTemplate="5,10,15" 
                 paginatorAlwaysVisible="true"
                 rows="10" editable="true" editMode="cell"
                 >
        <!-- 30-12-2020:Task#2856-CRM-3683: bda-in: Jobs needs a related company delete button-->
        <p:ajax event="rowSelect" update="jobDetailsButtonForm:tabviewJobs:btnRelCompDelete"/>
        <p:ajax event="toggleSelect"  update="jobDetailsButtonForm:tabviewJobs:btnRelCompDelete"/>
        <p:ajax event="rowSelectCheckbox"   update="jobDetailsButtonForm:tabviewJobs:btnRelCompDelete" />
        <p:ajax event="rowUnselectCheckbox"  update="jobDetailsButtonForm:tabviewJobs:btnRelCompDelete"/>
        <!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
       <!--11972: 28/08/2023: CRM-7274   Job: related company tab updates blowing away view filter-->
        <p:ajax event="cellEdit" update="jobDetailsButtonForm:tabviewJobs:reldCompDT"/>
        <!--5686 CRM-4691: jobs and line overview: weird column spacing-->
       <!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
        <p:column  selectionMode="multiple" style="width:calc(50px);text-align: center; padding: 5px;word-wrap: break-word;white-space: normal"/>
        
        <!--11283 17/05/2023: CRM-6996: jobs: related: mfg comments word wrap terrrible-->
        <!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
        <p:column style="width:calc(150px);" headerText="Related Company" filterBy="#{comp.compName}" filterMatchMode="contains" sortBy="#{comp.compName}">
            <p:outputLabel value="#{comp.compName}" style="word-wrap: break-word;white-space: normal"/>
        </p:column>
        <!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
        <p:column style="width:calc(150px);"  headerText="Category" filterBy="#{comp.jobMfgItemCategory}"  filterMatchMode="contains" sortBy="#{comp.jobMfgItemCategory}">

            <p:cellEditor>
                <f:facet name="output">
                    <p:outputLabel id="labelJobMfgCat" value="#{comp.jobMfgItemCategory}" style="word-wrap: break-word;white-space: normal">                       
                    </p:outputLabel>
                </f:facet>
                <f:facet name="input">
                    <p:inputText value="#{comp.jobMfgItemCategory}"  maxlength="40">
                        <p:ajax event="change" listener="#{viewJobsRelatedComps.updateJobReldCompFilds(comp,jobs.jobId,3)}" />
                    </p:inputText>
                </f:facet>
            </p:cellEditor>
        </p:column>
        <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
        <!--11283 17/05/2023: CRM-6996: jobs: related: mfg comments word wrap terrrible-->
        <!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
        <p:column style="width:calc(100px);"  headerText="Type" filterBy="#{comp.companyType}" filterMatchMode="contains" sortBy="#{comp.companyType}">
            <p:outputLabel value="#{comp.companyType}" style="word-wrap: break-word;white-space: normal"/>
        </p:column>
        <!--1995 CRM-3378: jobs: Southwest: mappings missing for jobs - JobMfgs, Related Companies-->
        <!--//27-03-2023 : #10869 : CRM-6764 : Jobs: make related company tab fields editable.-->
        <!--11283 17/05/2023: CRM-6996: jobs: related: mfg comments word wrap terrrible-->
<!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
        <p:column style="width:calc(170px);" headerText="Spec Status" filterBy="#{comp.jobCompStatus}" filterMatchMode="contains" sortBy="#{comp.jobCompStatus}">

            <p:cellEditor>
                <f:facet name="output">
                   <!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
                    <p:outputLabel id="labelJobCompStatus" value="#{comp.jobCompStatus}" style="word-wrap: break-word;white-space: normal"/>
                </f:facet>
                <f:facet name="input">
                    <p:selectOneMenu value="#{comp.jobCompStatus}">
                        <p:ajax event="change" listener="#{viewJobsRelatedComps.updateJobReldCompFilds(comp,jobs.jobId,0)}"/>
                        <f:selectItem itemLabel="Select Spec Status" itemValue="" /> 
                        <f:selectItems value="#{viewJobsRelatedComps.jobCompStatusList}" var="JobRel" 
                                       itemLabel="#{JobRel}" itemValue="#{JobRel}"/>
                    </p:selectOneMenu>
                </f:facet>
            </p:cellEditor>
        </p:column>
<!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
        <p:column style="width:calc(150px);" headerText="W/L" filterBy="#{comp.jobMfgWonLost}" filterMatchMode="contains" sortBy="#{comp.jobMfgWonLost}">

            <p:cellEditor>
                <f:facet name="output">
                    <p:outputLabel id="labelJobCompWL" value="#{comp.jobMfgWonLost}" style="word-wrap: break-word;white-space: normal"/>
                </f:facet>
                <f:facet name="input">
                    <p:selectOneMenu value="#{comp.jobMfgWonLost}">
                        <p:ajax event="change" listener="#{viewJobsRelatedComps.updateJobReldCompFilds(comp,jobs.jobId,4)}"/>
                        <f:selectItem itemLabel="Select W/L" itemValue="" />                       
                        <f:selectItem itemLabel="UNKNOWN" itemValue="UNKNOWN"/>
                        <f:selectItem itemLabel="WON" itemValue="WON"/>
                        <f:selectItem itemLabel="LOST" itemValue="LOST"/>
                    </p:selectOneMenu>
                </f:facet>
            </p:cellEditor>
        </p:column>
        <!--//    8210 14/06/2022 : Direct Request: ISQuotes: Job/Mfg Notes, Quote Value-->
        <!--//27-03-2023 : #10869 : CRM-6764 : Jobs: make related company tab fields editable.-->
        <!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
        <p:column style="width:calc(100px);" headerText="Mfg Value" filterBy="#{comp.jobMfgQuotValue}"  filterMatchMode="contains" sortBy="#{comp.jobMfgQuotValue}">

            <p:cellEditor>
                <f:facet name="output">
                   <!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
                    <p:outputLabel id="labelJobMfgQuotValue" value="#{comp.jobMfgQuotValue}" style="word-wrap: break-word;white-space: normal">
                        <f:convertNumber groupingUsed="true" minFractionDigits="2" maxFractionDigits="2"/>
                    </p:outputLabel>
                </f:facet>
                <f:facet name="input">
                    <p:inputNumber value="#{comp.jobMfgQuotValue}"  
                                   maxlength="9" maxValue="*********" decimalPlaces="2"
                                   thousandSeparator=",">
                        <p:ajax event="change" listener="#{viewJobsRelatedComps.updateJobReldCompFilds(comp,jobs.jobId,1)}"/>
                    </p:inputNumber>
                </f:facet>
            </p:cellEditor>
        </p:column>
        <!--//27-03-2023 : #10869 : CRM-6764 : Jobs: make related company tab fields editable.-->
<!--11283 17/05/2023: CRM-6996: jobs: related: mfg comments word wrap terrrible-->
<!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
<p:column style="width:calc(200px);"  headerText="Mfg Notes" filterBy="#{comp.jobMfgNotes}" filterMatchMode="contains" sortBy="#{comp.jobMfgNotes}">
                <!--<p:outputLabel id="labelJobMfgNotes" value="#{comp.jobMfgNotes.length()>50?comp.jobMfgNotes.substring(0,50).concat('...'):comp.jobMfgNotes}" title="#{comp.jobMfgNotes}" />-->
                    <!--11283 17/05/2023: CRM-6996: jobs: related: mfg comments word wrap terrrible-->
                    <!--12019: 01/09/2023:  Job: related company tab updates blowing away view filter-->
                   <p:cellEditor>
                <f:facet name="output">
                    <p:outputLabel id="labelJobMfgNotes" value="#{comp.jobMfgNotes}" styleClass="might-overflow grid-spl-style" />
                </f:facet>
                       <f:facet name="input">
                           <p:inputText value="#{comp.jobMfgNotes}" readonly="true" onfocus="jobMfgNotes(#{comp.recId});"></p:inputText>
                       </f:facet>
                   </p:cellEditor>
        </p:column>
<!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
<p:column  style="width:calc(100px);"  headerText="Sales Team" filterBy="#{comp.salesManName}"  filterMatchMode="contains" sortBy="#{comp.salesManName}">
            <p:outputLabel value="#{comp.salesManName}" style="word-wrap: break-word;white-space: normal"/>
        </p:column>
<!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
<p:column  style="width:calc(100px);"  headerText="Contact" filterBy="#{comp.contactName}"  filterMatchMode="contains" sortBy="#{comp.contactName}">
            <p:outputLabel value="#{comp.contactName}"/>
        </p:column>
<!--//             #11426   CRM-6997   Jobs related: copy into opp not copying job details like name, parties, $s, reporting com-->
        <!--        //      #8415  Direct Request: ISQuotes:  Related Companies - Add option to create opportunity-->
<!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
        <p:column style="width:calc(50px);text-align: center;" >
            <p:outputPanel id="pnlOpp">
                <p:button styleClass="linkorCreateOpp"  id="btnCreateOpp" href="#{viewJobsRelatedComps.openOpportunityPage(comp, jobs.jobId)}" 
                          includeViewParams="true" rendered="#{comp.enableCreateOpp}"  target="_blank" title="Create #{custom.labels.get('IDS_OPP')}"  /> 
            </p:outputPanel>

        </p:column>

        <!--#849 - CRM-2685: jobs: feedback items - Related Companies -12-06-2020-  sharvani-->

        <!--#8309: CRM-5838  whbsales/jon wiggs/Jobs match to opp and quote are totally broken - poornima - commented linked opportunity in related comps tab list--> 
        <!--        <p:column   style="width: 10%"  >
                    <p:commandLink title="Linked opportunity" value="Linked opportunity" oncomplete="PF('linkedOppDlg').show();" action="#{jobOpps.populateOppsForRelatedComp(comp.reltCompId)}" update=":linkedOppForm:dtOpp"/>
                </p:column>-->

        <!--#8309: CRM-5838  whbsales/jon wiggs/Jobs match to opp and quote are totally broken - poornima-->
       <!--11283 17/05/2023: CRM-6996: jobs: related: mfg comments word wrap terrrible-->
<!--11452: 04/07/2023: CRM-6995: Jobs Related: Won Lost column and item category column-->
       <p:column  headerText="Source" style="width:calc(100px);text-align: center;" > 
          <p:outputLabel value="#{custom.labels.get('IDS_JOB')}" rendered="#{comp.jobReltSrc=='JOB' || comp.jobReltSrc=='Job'}" />
           <h:commandLink value="#{custom.labels.get('IDS_QUOTES')}"  target="_blank"  title="Linked Docs" rendered="#{comp.jobReltSrc=='QUOTE'}" actionListener="#{jobs.sourceRedirect(comp.jobReltSrc,comp.jobReltSrcId)}"></h:commandLink>
           <h:commandLink value="#{custom.labels.get('IDS_OPP')}"  target="_blank"  title="Linked Docs" rendered="#{comp.jobReltSrc=='OPP'}" actionListener="#{jobs.sourceRedirect(comp.jobReltSrc,comp.jobReltSrcId)}"></h:commandLink>
       </p:column>

       <p:column  style="width:calc(100px);text-align: center;" >
            <!--#8477: CRM-5838  whbsales/jon wiggs/Jobs match to opp and quote are totally broken - poornima - enable or disable linked docs button-->
            <p:commandLink title="Linked Docs" value="Linked Docs" rendered="#{comp.enableLinkedDocOpp || comp.enableLinkedDocQuote}" oncomplete="PF('dlgJobLinkedDocs').show();" action="#{jobLinkedDocumentService.loadJobLinkedDocuments(comp.reltCompId,jobs.jobId)}" update=":frmJobLinkedDocs:dtJobLinkedDocs"/>
        </p:column>


        <!--                #849 - CRM-2685: jobs: feedback items - Related Companies -12-06-2020-  sharvani
                <p:column style="width:50px;" rendered="#{viewJobsRelatedComps.jobOppId >0}">
                    <p:commandLink title="Linked opportunity" value="Linked opportunity" oncomplete="PF('linkedOppDlg').show();" action="#{jobOpps.populateOppsForRelatedComp(comp.reltCompId)}" update=":linkedOppForm:dtOpp"/>
                </p:column>
                #849 - CRM-2685: jobs: feedback items - Related Companies -12-06-2020-  sharvani
                <p:column style="width:50px;" rendered="#{viewJobsRelatedComps.jobOppId =0}">
                    <p:commandLink title="Linked Quote" value="Linked Quote" />
                </p:column>-->


    </p:dataTable>
    <!--//27-03-2023 : #10869 : CRM-6764 : Jobs: make related company tab fields editable.-->
    <!--11283 17/05/2023: CRM-6996: jobs: related: mfg comments word wrap terrrible-->
    <p:dialog id="mfgNotesdlg" width="950" height="455" showEffect="clip" hideEffect="clip" modal="true" widgetVar="mfgNotesdlg"  
              resizable="false" header="Mfg Notes" styleClass="dialogCSS"> 
        <f:facet name="header" >
          Mfg Notes
          <!--11283 17/05/2023: CRM-6996: jobs: related: mfg comments word wrap terrrible-->
             <p:spacer width="720" height="0"/>
                <p:commandButton  id="btnOppRptCmtDate" icon="fa fa-clock-o"   
                                  onclick="addDate()" disabled="#{fieldsMstService.invisibleFields.get('RPT_COMMENTS')==1 or opportunities.oppCloseFlag==true}"
                                  class="btn-primary btnClr"  style="height: 30px;" title="Add Current DateTime"
                                  />
                <p:spacer width="4"/>
                <p:commandButton  id="btnOppReportingCoomentClose" icon="fa fa-eraser" 
                                  actionListener="#{viewJobsRelatedComps.clearField()}" 
                                  process="@this" title="Clear" 
                                  update=":jobDetailsButtonForm:tabviewJobs:mfgTextArea" 
                                  class="btn-danger btnClr"  style="height: 30px;"
                                  />
       
        </f:facet>
        <!--11283 17/05/2023: CRM-6996: jobs: related: mfg comments word wrap terrrible-->
        <p:panelGrid id="panelGridJobMfgNotes" columns="1" columnClasses="ui-grid-col-12"
                     style="min-height: 50px" layout="grid" styleClass="box-primary no-border ui-fluid">
            <!--11283 17/05/2023: CRM-6996: jobs: related: mfg comments word wrap terrrible-->
            <p:inputTextarea rows="16" id="mfgTextArea" autoResize="false" value="#{viewJobsRelatedComps.jobMfgNotesText}"  styleClass="partNumBox" style="resize:none;"/>
        </p:panelGrid>
        <p:spacer width='4px' />
        <div class="div-center" >
            <p:commandButton value="Save" actionListener="#{viewJobsRelatedComps.updateJobMfgNotes(jobs.jobId)}" 
                             update=":jobDetailsButtonForm:tabviewJobs:reldCompDT" widgetVar="btnSaveJobMfgNotes"
                             onclick="PF('btnSaveJobMfgNotes').disable();"
                             oncomplete="PF('mfgNotesdlg').hide();PF('btnSaveJobMfgNotes').enable();" styleClass="btn btn-success  btn-xs">
                <!--<p:confirm header="Confirmation" message="Do you want to update Mfg Notes?" />-->
            </p:commandButton>
            <p:spacer width="4px"/>
            <p:commandButton value="Cancel"  oncomplete="PF('mfgNotesdlg').hide();" styleClass="btn btn-warning btn-xs" />
        </div>
    </p:dialog>
    <!--//27-03-2023 : #10869 : CRM-6764 : Jobs: make related company tab fields editable.-->
    <p:dialog id="dlgIdProcessingOnTask" widgetVar="dlgProcessingOnTask" closable="false" modal="true" header="Message" onShow="PF('dlgProcessingOnTask').initPosition();" resizable="false" >
        <p:outputPanel >
            <br />
            <h:graphicImage  library="images" name="ajax-loader.gif"   />
            <p:spacer width="5" />
            <p:outputLabel value="Processing please wait..." />
            <br /><br />
        </p:outputPanel>
    </p:dialog>
    <style>
        .linkorCreateOpp{
            padding: 18px 0px !important;

        }
        /*11283 17/05/2023: CRM-6996: jobs: related: mfg comments word wrap terrrible*/
         .might-overflow {
          
                    text-overflow: ellipsis;
                    overflow : hidden;
                    white-space: nowrap;
                    margin-bottom: unset !important;
                }

                .might-overflow:hover {
                    text-overflow: clip;
                    white-space: normal;
                    word-break: break-all;
                }
    </style>
    <!--//27-03-2023 : #10869 : CRM-6764 : Jobs: make related company tab fields editable.-->
    <script>
        function jobMfgNotes(jobMfgNotes123) {
//          12019: 01/09/2023:  Job: related company tab updates blowing away view filter
//            PF('dlgProcessingOnTask').show();
            if (jobMfgNotes123 === undefined) {
                jobMfgNotes123 = "";
            }
            console.log('jobMfgNotes=', jobMfgNotes123)
            jobMfgNotesRemote([{name: 'jobMfgNotes', value: jobMfgNotes123}]);
        }
        function addDate() {
                    var curDate = '#{opportunities.getCurrentDate()}';
                    var cmt = document.getElementById('jobDetailsButtonForm:tabviewJobs:mfgTextArea');
                    curDate = curDate + " ";
                    //        12/11/2021 : Pms Task #6341: CRM-4968: Time stamp in reporting comments doesn't go where your cursor is
                    cmt.value = curDate + "\n \n" + cmt.value;
                    //        #3262    CRM-2044: Opportunities: Fwd: Reporting comments - date button issue
                    //            cmt.focus();
                    cmt.setSelectionRange(curDate.length + 1, curDate.length + 1);
                    cmt.focus();
                }
    </script>

</ui:composition>
