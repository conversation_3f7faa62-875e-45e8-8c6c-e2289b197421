<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core">
    <style>
        .ui-dialog-title{
            width: 93% !important;
        }
    </style>
    <p:remoteCommand autoRun="true" name="loadJobOpps" actionListener="#{viewJobOpps.populateJobOpps(jobs.jobId)}" update="compOppPanel" oncomplete="PF('oppListTable').clearFilters()" />
    <p:outputPanel id="compOppPanel">
        <h:link  outcome="/opploop/opportunity/OpportunityView.xhtml?jid=#{jobs.jobId}"   style="float:left;">

            <p:commandButton type="button" ajax="false" process="none"
                             title="Create new  #{custom.labels.get('IDS_OPP')}" class="btn btn-primary btn-xs" 
                             value="Create #{custom.labels.get('IDS_OPP')}" />

        </h:link>
        <p:spacer width="4px"/>
        <!--4049 Jobs: Clicking on disabled buttons 'Update Selected' and 'Clone'  > Navigates to New Job page-->
        <!--<h:link>-->
        <!--//05-10-2023 : #12212 : ESCALATIONS CRM-7406   Jobs: Latency while attaching to opps-->
            <p:commandButton class="btn btn-primary btn-xs" 
                             value="Add Existing Opp"
                             oncomplete="PF('oppMstDlg').show();" 
                             actionListener="#{jobOpps.loadJobOpportunities(jobs.jobId)}"
                             update=":frmOppMst :frmOppMst:dtOppMstList"
                             /> 
        <!--</h:link>--> 
        <p:spacer width="4px"/>
        <!--4049 Jobs: Clicking on disabled buttons 'Update Selected' and 'Clone'  > Navigates to New Job page-->
        <!--<h:link>-->
        <!--ESCALATIONS CRM-8428   Jobs: Cannot Change Opportunity Stage from Job if not using default Opportuni-->
            <p:commandButton  title="Update  Selected #{custom.labels.get('IDS_OPP')}"
                              value="Update Selected" 
                              class="btn btn-success btn-xs" 
                              disabled="#{viewJobOpps.selectedOppView.size() eq 0}" 
                              id="btnUpdateSelected" 
                              action="#{jobOpps.clearFields(viewJobOpps.selectedOppView)}"
                              update=":jobsUpdate"
                              />
        <!--</h:link>-->
        <p:spacer width="4px"/>
        <!--4049 Jobs: Clicking on disabled buttons 'Update Selected' and 'Clone'  > Navigates to New Job page-->
        <!--<h:link>-->
            <p:commandButton  value="Clone" id="btnCloneOpp" 
                              class="btn btn-primary btn-xs"
                              disabled="#{viewJobOpps.selectedOppView.size() eq 0}" 
                              action="#{jobOpps.cloneClearFields(viewJobOpps.selectedOppView)}" 
                              update=":cloneDLG"
                              oncomplete="PF('cloneOppDlg').show();"
                              />
        <!--</h:link>-->
        <p:spacer width="4px"/>
        <!--4049 Jobs: Clicking on disabled buttons 'Update Selected' and 'Clone'  > Navigates to New Job page-->
        <!--<h:link>-->
            <p:commandButton   icon="fa fa-refresh"  
                               title="Refresh" 
                               class="btn-xs btn-primary"
                               action="#{viewJobOpps.populateJobOpps(jobs.jobId)}"
                               onclick="oppListTable.clearFilters()"
                               update="dtOppList"
                               />
        <!--</h:link>-->
        <br/><br/>

        <!--Bug #2167: CRM-3471: SOS group: opp list on job missing page turner-->
        <p:dataTable id="dtOppList" widgetVar="oppListTable" value="#{viewJobOpps.jobLOppViewList}" 
                     var="opp" rowKey="#{opp.oppId}" selection="#{viewJobOpps.selectedOppView}" 
                     emptyMessage="No open #{custom.labels.get('IDS_OPPS')} found." 
                     paginator="true" rows="5" filteredValue="#{viewJobOpps.filteredJobOppViewList}"
                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                     rowsPerPageTemplate="5,10,15" tableStyle="table-layout:auto" resizableColumns="true"
                     paginatorAlwaysVisible="true" class="tblmain" draggableColumns="true">
            <f:facet name="header">
                <p:inputText style="display: none"   id="globalFilter" onkeyup="PF('oppListTable').filter()" />
            </f:facet>
            <p:column  selectionMode="multiple" style="width:50px;text-align: center"/>
            <p:ajax event="toggleSelect" update=":jobDetailsButtonForm:tabviewJobs:btnUpdateSelected :jobDetailsButtonForm:tabviewJobs:btnCloneOpp"/>
            <p:ajax event="rowSelectCheckbox" update=":jobDetailsButtonForm:tabviewJobs:btnUpdateSelected :jobDetailsButtonForm:tabviewJobs:btnCloneOpp"/>
            <p:ajax event="rowUnselectCheckbox" update=":jobDetailsButtonForm:tabviewJobs:btnUpdateSelected :jobDetailsButtonForm:tabviewJobs:btnCloneOpp"/>

            <p:column filterBy="#{opp.custName}" filterMatchMode="contains" sortBy="#{opp.custName}" >
                <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet> 
                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}" >
                    #{opp.custName}
                </h:link>
            </p:column>

            <p:column filterBy="#{opp.principalName}" filterMatchMode="contains" sortBy="#{opp.principalName}">
                <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet> 
                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                    #{opp.principalName}
                </h:link>
            </p:column>
            <p:column filterBy="#{opp.distriName}" filterMatchMode="contains" sortBy="#{opp.distriName}">
                <f:facet name="header">#{custom.labels.get('IDS_DISTRI')}</f:facet> 
                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                    #{opp.distriName}
                </h:link>
            </p:column>

            <p:column filterBy="#{opp.oppCustProgram}" filterMatchMode="contains" sortBy="#{opp.oppCustProgram}">
                <f:facet name="header">#{custom.labels.get('IDS_PROGRAM')}</f:facet> 
                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}" >
                    #{opp.oppCustProgram}
                </h:link>
            </p:column> 

            <p:column filterBy="#{opp.oppActivity}" filterMatchMode="contains" sortBy="#{opp.oppActivity}">
                <f:facet name="header">#{custom.labels.get('IDS_ACTIVITY')}</f:facet>  
                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}">
                    #{opp.oppActivity}
                </h:link>
            </p:column>
            <p:column filterBy="#{opp.oppFollowUp}" filterMatchMode="contains" sortBy="#{opp.oppFollowUp}">
                <f:facet name="header">#{custom.labels.get('IDS_FOLLOW_UP')}</f:facet> 
                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}" >
                    <p:outputLabel value="#{opp.oppFollowUp}" >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                    </p:outputLabel>

                </h:link>
            </p:column>
            <!--Feature #5025 RE: Repfabric / AVX CRMSync / Relabeling Fields / "Next Step" (Pending)-->
            <!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
            <p:column filterBy="#{opp.oppNextStep}" filterMatchMode="contains" sortBy="#{opp.oppNextStep}" styleClass="might-overflow">
                <f:facet name="header">#{custom.labels.get('IDS_NEXT_STEP')}</f:facet> 
                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}" >
                    #{opp.oppNextStep}
                </h:link>
            </p:column>

            <!--Feature #2912:   RS-3325: Opportunity Wishes-->
            <p:column filterBy="#{opp.oppValue}" filterMatchMode="contains" sortBy="#{opp.oppValue}">
                <f:facet name="header">Value</f:facet> 
                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}" value="#{opp.oppValue}" style="float: right">
                    <!--<p:outputLabel value="#{opp.oppValue}"  style="float:right" />-->
                </h:link>


            </p:column>
            <!--Bug 1215:#388851-KAI closed opps showing up under company profile-->
            <p:column  filterMatchMode="contains" sortBy="#{opp.oppCloseStatus}">
                <f:facet name="header">Status</f:facet> 
                <h:link outcome="/opploop/opportunity/OpportunityView.xhtml?opid=#{opp.oppId}" >
                    <p:outputLabel value="#{opp.oppCloseStatus==0?'Open':'Closed'}" />
                </h:link>    
            </p:column>

            <p:column >
                <p:commandLink  title="Unlink #{custom.labels.get('IDS_OPPS')}" oncomplete="PF('unlnkConDlg').show()" action="#{viewJobOpps.setIDToUnlink(opp.oppId,jobs.jobId)}">
                    <h:graphicImage name="unlink.png" library="images"  height="20" width="20"/> 
                </p:commandLink>
            </p:column>
        </p:dataTable>
    </p:outputPanel>


    <p:confirmDialog header="Confirm Unlinking"  global="false"
                     message="Are you sure to unlink this #{custom.labels.get('IDS_OPPS')}?" 
                     widgetVar="unlnkConDlg" >
        <!--<center>-->
        <!--SONARQUBE ISSUES - sharvani - 23-01-2020-->
        <div align="center">
            <p:commandButton value="Unlink" 
                             styleClass="btn btn-danger btn-xs"
                             action="#{viewJobOpps.unlinkOpp()}"
                             oncomplete="PF('oppListTable').clearFilters();" 
                             update=":jobDetailsButtonForm:tabviewJobs:dtOppList"
                             />

            <p:spacer width="4 px" />
            <p:commandButton value="Cancel" 
                             styleClass="btn btn-warning btn-xs"
                             onclick="PF('unlnkConDlg').hide()"
                             type="button"  />
        </div>
        <!--</center>-->
    </p:confirmDialog>
    
    <!--ESCALATIONS CRM-8428   Jobs: Cannot Change Opportunity Stage from Job if not using default Opportuni);-->
    <p:dialog header="Warning" id="dlgWarning" modal="true" resizable="false" width="750" closeOnEscape="false" 
                  showEffect="fade" hideEffect="fade" widgetVar="jobOppsWarningDlg">
            <h:form id="jobsWarning">
                <div>
                    <p:panel style=" border: 0px; padding: 0px !important;">
                        <h:outputText value="The #{custom.labels.get('IDS_ACTIVITY')} and #{custom.labels.get('IDS_ACT_STATUS')} options cannot be modified because the selected #{custom.labels.get('IDS_OPPS')} involve multiple #{custom.labels.get('IDS_PRINCIS')} with custom #{custom.labels.get('IDS_ACTIVITY')}. To update the #{custom.labels.get('IDS_ACTIVITY')} or #{custom.labels.get('IDS_ACT_STATUS')}, please select #{custom.labels.get('IDS_OPPS')} from a single #{custom.labels.get('IDS_PRINCI')}." style=" padding: 0px; font-size: 15px"/>
                    </p:panel>
                </div>
                <div align="center">
                    <p:commandButton value="Okay"   oncomplete="PF('jobOppsWarningDlg').hide();" 
                                     styleClass="btn btn-success  btn-xs" id="btnOkay"
                                     actionListener="#{goalService.deleteGoal()}"/>
                </div>
            </h:form>
        </p:dialog>
    
</ui:composition>



