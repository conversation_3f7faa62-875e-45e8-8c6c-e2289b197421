<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!--627 Jobs > Emails tab-->
<p:remoteCommand autoRun="true" name="loadJobEmails" actionListener="#{jobs.populateJobEmails(jobs.jobId)}" update="compQuotPanel"  oncomplete="PF('emlW').clearFilters()"  /> 
    
      <p:outputPanel id="pnlEMAIL"  > 
<!--        <p:commandButton id="mailCust" disabled="#{opportunities.oppCloseStatus!=0}" class="btn btn-primary btn-xs"    
                         immediate="true" value="Mail to #{opportunities.directSales ? custom.labels.get('IDS_DISTRI'):custom.labels.get('IDS_CUSTOMER')}"
                         onclick="window.open('#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=mailtocustomer&amp;cont_id=#{opportunities.oppCustomerContact}&amp;opp_id=#{opportunities.oppId}', '_blank')"
                         />
        <p:spacer width="4"/>
        <p:commandButton id='mailPrinci' disabled="#{opportunities.oppCloseStatus!=0}" class="btn btn-primary btn-xs"  
                         immediate="true" value="Mail to #{custom.labels.get('IDS_PRINCI')}"
                         onclick="window.open('#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=mailtoprincipal&amp;cont_id=#{opportunities.oppPrincipalContact}&amp;opp_id=#{opportunities.oppId}', '_blank')"
                         />
        <p:spacer width="4"/>
        <p:commandButton  onclick="PF('dlgCont').show();" 
                          type="button"                                           
                          value="Mail to Selected"   
                          disabled="#{opportunities.oppCloseStatus!=0}" 
                          class="btn btn-primary btn-xs" />-->

        <p:dialog resizable="false"   position="center"  header="Email Preview" height="450" width="580"    id="opnl"    widgetVar="overlay" closeOnEscape="true" closable="true" >
            <table>              
                <tr style="height: 25px">
                    <td style="width: 70px">
                        <h:outputLabel value="To" style="font-weight: bold" /></td>
                    <td style="width: 15px">:</td>
                    <td >
                        <h:outputText id="txtEmlTo"   value="#{emailHeader.emailTo}" /></td>
                </tr>
                <tr style="height: 25px">
                    <td>
                        <h:outputLabel value="From" style="font-weight: bold" /></td>
                    <td style="width: 15px">:</td>
                    <td >
                        <h:outputText id="txtEmlFrm"  value="#{emailHeader.emailFrom}"  /></td>
                </tr>
                <!--//      #11078   ESCALATIONS CRM-6883   Companies: Email tab under company not working as expected-->
                <tr style="height: 25px">
                    <td>
                        <h:outputLabel value="Cc" style="font-weight: bold" /></td>
                    <td style="width: 15px">:</td>
                    <td >
                        <h:outputText id="txtEmlCC"  value="#{emailHeader.emailCc}"  /></td>
                </tr>
                <tr style="height: 25px">
                    <td >
                        <h:outputLabel value="Subject"  style="font-weight: bold" /></td> 
                    <td style="width: 15px">:</td>
                    <td>
                        <h:outputText id="txtEmlSubj"  value="#{emailHeader.emailSubject}" /></td>
                    <td>
                        <h:graphicImage id="imgEmlAtch" value="#{eml.emailAttachFlag}"  library="images" name="attach_icon.png" /></td>
                </tr>
            </table>
            <hr></hr>
            <div style="width: 548px;overflow-wrap: break-word;">
                <h:outputText id="txtEmlBody" value="#{emailHeader.emailContent}" escape="false"  style="width: 548px"    />
            </div>

        </p:dialog>

        <!--#2541 Related to CRM-1447: Make attachments in jsf much easier to open-->
        <p:dialog resizable="true"   position="center"  header="Attachments" height="450" width="580"    id="atpnl"  class="dialogCSS"  widgetVar="attchmentVar" closeOnEscape="true" closable="true">

            <p:dataTable  value="#{emailAttachment.listEmailAttachments}"
                          style="width: 100%"
                          id="emAt"   var="emAt"  >
                <!--                #{emAt.recId}
                #{emAt.emailAttaEmailId}
                <p:column headerText="File Name" style="text-align: left">
                #{emAt.emailAttaLocation}
            </p:column>
              <p:column headerText="File Name" style="text-align: left">
                #{emAt.recId}
            </p:column>-->
                <p:column headerText="File Name" style="text-align: left" width="90">
                    #{emAt.emailAttaName}
                </p:column>
                <p:column width="10" style="text-align: center">
                    <p:commandButton immediate="true" process="@none" title="Download Attachment"  class="btn-primary btn-xs"  icon="fa fa-download" 
                                     onclick="primeFaces.monitorDownload(start, stop)">
                        <p:fileDownload    value="#{emAt.file}"/>
                    </p:commandButton>
                </p:column>
            </p:dataTable>
        </p:dialog>



        <br/><br/>
        <!--#2750 CRM-1741: Fwd: Opp - Q128793 Sentinel I28 Custom Configured Instrument - Urgent-->
        <p:dataTable value="#{jobs.listEmails}" var="eml"  filterEvent="keyup"  widgetVar="emlW" 
                     filteredValue="#{jobs.listEmailsFilter}"
                     id="dtemail"  style="table-layout: fixed" 
                     emptyMessage="No Emails found">
            <p:column style="width:30px;padding: 0px 4px 0px 5px;" >   

                <f:facet name="header">
                    <h:graphicImage  library="images" name="unlink.png" width="15" height="15" />
                </f:facet> 
                <p:commandLink  oncomplete="PF('unlnkEmlDlg').show()"   
                                title="Unlink this email"
                                update=""  style="margin-left: -3px;" action="#{emailHeader.setIDToUnlinkJob(jobs.jobId,eml.recId)}"
                                >
                    <h:graphicImage name="unlink.png" library="images" height="15" width="15"/> 
                    <!--<p:confirm header="Confirmation" message="Are you sure to Unlink?"  icon="ui-icon-alert" />--> 
                </p:commandLink>

            </p:column>


            <p:column style="width: 35px;padding: 0px 0px 0px 0px;">


                
                <p:commandButton  value=""  title="Preview" 
                                  class="fa fa-eye" style="border: none;margin-left:-12px; background: white"
                                  actionListener="#{emailHeader.storeEmailContent(eml)}"
                                  update=":jobDetailsButtonForm:tabviewJobs:opnl"
                                  oncomplete="PF('overlay').show()" 
                                  process="@this"
                                  />
                
            </p:column>


      <!--value="#{loginBean.emailURL}/action.php?action=view_content&amp;reqid=#{loginBean.randomUid}&amp;mail=#{eml.emailId}&amp;viewemail=1"-->
            <p:column headerText="Subject"  class="emlpad"  style="min-width: 30%;max-width: 30%" sortBy="#{eml.emailSubject}" 
                      filterBy="#{eml.emailSubject}" filterMatchMode="contains" >  
                <p:commandLink rendered="#{(eml.emailUserId ne loginBean.userId)}"
                               actionListener="#{emailHeader.setRandomUid(eml.emailUserId,0)}" 
                               process="@this" target="_blank" update=":jobDetailsButtonForm:tabviewJobs:lbldeleg :jobDetailsButtonForm:tabviewJobs:lblreq"   title="View Email"   
                               oncomplete="emailAction(#{eml.emailUserId},#{eml.emailId},'read',false)" >
                    #{eml.emailSubject}
                </p:commandLink>
                <!--actionListener="#{emailHeader.setRandomUid(eml.emailUserId,opportunities.oppSmanId)}"--> 
<!--:frmOpp:tabOpp:lblreq :frmOpp:tabOpp:lbldeleg-->
                <p:commandLink process="@none" type="button" 
                               rendered="#{(eml.emailUserId eq loginBean.userId)}"
                               onclick="emailAction(#{eml.emailUserId},#{eml.emailId}, 'read', true)"  value=" #{eml.emailSubject}" />
            </p:column>

            <p:column style="width:30px;padding: 0px 4px 0px 5px;"  class="emlpad" >
                <f:facet name="header">
                    <h:graphicImage  class="fa fa-paperclip"/>
                </f:facet>
                <!--<h:graphicImage library="images" name="attach_icon.png" rendered="#{eml.emailAttachFlag==1}"/>-->
                <p:commandButton  value=""  title="Attachments" 
                                  class="fa fa-paperclip" style="border: none;margin-left:-12px; background: white"
                                  action="#{emailAttachment.loadEmailAttchmentListForJob(eml.emailId)}"
                                  update="jobDetailsButtonForm:tabviewJobs:emAt" rendered="#{eml.emailAttachFlag==1}"
                                  oncomplete="PF('attchmentVar').show()" 
                                  process="@this"
                                  />
<!--frmOpp:tabOpp:emAt-->


            </p:column>


            <p:column headerText="From" class="emlpad" style="min-width: 20%;max-width: 20%" sortBy="#{eml.emailFrom}" filterBy="#{eml.emailFrom}"
                      filterMatchMode="contains" >
                #{eml.emailFrom}
            </p:column>

            <p:column headerText="To"  class="emlpad" style="min-width: 20%;max-width: 20%" sortBy="#{eml.emailTo}" filterBy="#{eml.emailTo}" 
                      filterMatchMode="contains" >
                #{eml.emailTo}
            </p:column>


            <p:column headerText="Date"  class="emlpad"   style="width: 140px!important;text-align: center"  >
                <h:outputLabel  style="font-size: 0.95em" value="#{rFUtilities.getCurrentTimeZoneDate(eml.emailDate, eml.emailTime)}" >
                    <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                </h:outputLabel>

            </p:column>

            <p:column headerText="Action"  style="width: 120px!important; "  >


                <!--reply-->
                <!--update=":frmOpp:tabOpp:lblreq :frmOpp:tabOpp:lbldeleg"-->  
                <p:commandButton rendered="#{(eml.emailUserId ne loginBean.userId)}" icon="fa fa-mail-reply" 
                                 immediate="true"    class="btn-primary  btn-icon"
                                 actionListener="#{opportunities.replyEmail(eml.emailId, 1)}" 
                                 title="Reply"  
                                 />

                <p:spacer width="4"/>
                <!--this will render if user is owner, or email userid is same as loggedin user-->
                <p:commandButton  class="btn-primary  btn-icon" title="Reply" icon="fa fa-mail-reply" 
                                  immediate="true"  action="#{opportunities.replyEmail(eml.emailId, 1)}" 
                                  rendered="#{(eml.emailUserId eq loginBean.userId)}" />

                <p:spacer width="4"/>
                <!--replyall-->
                <p:commandButton rendered="#{(eml.emailUserId ne loginBean.userId)}"  icon="fa fa-mail-reply-all" 
                                 immediate="true"    class="btn-primary  btn-icon"
                                 actionListener="#{opportunities.replyEmail(eml.emailId, 1)}" 
                                 update="" title="Reply All"  
                                 />
                <!--:frmOpp:tabOpp:lblreq :frmOpp:tabOpp:lbldeleg-->
                <p:spacer width="4"/>
                <!--this will render if user is owner, or email userid is same as loggedin user-->
                <p:commandButton  class="btn-primary btn-icon" title="Reply All"   icon="fa fa-mail-reply-all" 
                                  immediate="true"    actionListener="#{opportunities.replyEmail(eml.emailId,2)}" 
                                  rendered="#{(eml.emailUserId eq loginBean.userId)}" />

                <p:spacer width="4"/>
                <!--forward-->
                <!--#2459Issue in Opportunities Emails tab-->
                <p:commandButton rendered="#{(eml.emailUserId ne loginBean.userId)}"   icon="fa fa-mail-forward" 
                                 immediate="true"    process="@this"   class="btn-primary btn-icon"
                                 actionListener="#{opportunities.replyEmail(eml.emailId, 3)}" 
                                 update="" title="Forward" 
                                 />
                <!--:frmOpp:tabOpp:lblreq :frmOpp:tabOpp:lbldeleg-->
                <!--oncomplete="emailAction(#{eml.emailUserId},#{eml.emailId}, 'forward',false)"--> 
                <p:spacer width="4"/>               
                <!--this will render if user is owner, or email userid is same as loggedin user-->
                <p:commandButton  class="btn-primary btn-icon" title="Forwards"    icon="fa fa-mail-forward" 
                                  immediate="true"  actionListener="#{opportunities.replyEmail(eml.emailId,3)}" 
                                  rendered="#{(eml.emailUserId eq loginBean.userId)}" />
            </p:column>

        </p:dataTable>  

        <!--        <p:confirmDialog global="true" showEffect="fade" hideEffect="fade">
                    <p:commandButton value="Yes" type="button" class="btn btn-xs btn-success"/>
                    <p:spacer width="4"/>
                    <p:commandButton value="No" type="button" class="btn btn-xs btn-danger"/>
                </p:confirmDialog>-->

    </p:outputPanel>

    <p:confirmDialog header="Confirm Unlinking"  global="false"
                     message="Are you sure to Unlink?" 
                     widgetVar="unlnkEmlDlg" >
        <!--#3354 Fix Sonarqube issues-->
        <div style="text-align: center">
            #{eml.unlinkJobID} #{eml.unlinkEmlId} 
            <p:commandButton value="Unlink" 
                             styleClass="btn btn-danger btn-xs"
                             action="#{emailHeader.unlinkJob()}"
                             oncomplete="PF('unlnkEmlDlg').hide(); loadJobEmails();" 
                             update=":jobDetailsButtonForm:tabviewJobs:dtemail :jobDetailsButtonForm:tabviewJobs:pnlEMAIL"
                             />

            <p:spacer width="4 px" />
            <p:commandButton value="Cancel" 
                             styleClass="btn btn-warning btn-xs"
                             onclick="PF('unlnkEmlDlg').hide();"
                             type="button"  />
        </div>
    </p:confirmDialog>

    <p:dialog header="Email to Contacts" id="dlgCont" widgetVar="dlgCont" >
        <!--01/02/2017 - Ticket #720824 - Using ViewContactList (oppContacts)-->
        <p:dataTable  value="#{oppContacts.oppContacts}"  
                      emptyMessage="No Contacts found"
                      selection="#{emailHeader.selContacts}"                  
                      rowKey="#{opl.contId}"
                      var="opl" id="dtceml">

            <!--      
                  <p:ajax   event="rowSelectCheckbox"  update=":frmOpp:tabOpp:link1"
                          listener="#{emailHeader.onRowSelect1}"/>            
                  <p:ajax event="rowUnselectCheckbox"  update=":frmOpp:tabOpp:link1" 
                          listener="#{emailHeader.onRowUnselect1}"/>-->

            <p:column width="4"  selectionMode="multiple"  />
            <p:column width="32" headerText="Name">               
                <p:outputLabel value="#{opl.contFullName}" />
            </p:column>
            <p:column width="32" headerText="Company" >
                <!--#2080 Cannot Create and Link opp to Email-->
                <p:outputLabel value="#{opl.contCompName}" />
            </p:column>
            <p:column width="32" headerText="Email" >
                <!--#2080 Cannot Create and Link opp to Email-->
                <p:outputLabel value="#{opl.contEmails}" />
            </p:column>
        </p:dataTable>

   <!--<h:outputLink   id="link1" target="_blank"  value="#{loginBean.emailURL}/action.php?action=write&amp;opid=#{opportunities.oppId}&amp;reqid=#{loginBean.randomUid}&amp;write=1&amp;cid=#{emailHeader.selConts}&amp;composeemail=1">-->
        <!--      <p:commandButton  id="snd" class="button_top btn_tabs"  
                                style="margin-left: 0px" 
                                icon="ui-icon-mail-closed" 
                                value="Create email " type="button" />-->

        <!--</h:outputLink>-->

        <!--this button used to avoid slowness while selecting each contacts-->
        <!--        <p:commandButton    id="fgfg" process="@this dtceml"   value="Create email" 
                                    class="btn btn-xs btn-primary"
                                    actionListener="#{opportunities.sendEmail(opportunities.oppCustomerContact, opportunities.oppId,4)}"/>-->
                                    <!--actionListener="#{emailHeader.createEmail}"/>-->

        <!--        <p:commandButton    id="fgfg" process="@this dtceml"   value="Create email" 
                                    class="btn btn-xs btn-primary"
                                    onclick="window.open('#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=mailtoselectedId&amp;cont_id=#{opportunities.oppCustomerContact}&amp;opp_id=#{opportunities.oppId}', '_blank')" />-->
        <p:spacer />
        <!--#2080 Cannot Create and Link opp to Email-->
        <p:commandButton    id="fgfg" process="@this dtceml"   value="Create email" 
                            class="btn btn-xs btn-primary"  
                            actionListener="#{emailHeader.createEmail}"/>
    </p:dialog>

    <h:inputHidden id="lblreq" value="#{emailHeader.rnd}"/>
    <h:inputHidden id="lbldeleg" value="#{emailHeader.chkDeleg}"/>
    <p:dialog id="deml" header="Send Email" widgetVar="wgdlgDeleg" height="60" width="380" closeOnEscape="true" >
        <h:panelGrid columns="3">
            <h:outputLabel id="emulabel" value="Send as"/>
            <p:commandButton onclick="window.open(urlUser);
                    PF('wgdlgDeleg').hide();" value="Current User"  type="button"  disabled="#{opportunities.oppCloseStatus!=0}"  title="Reply"  class="button_top btn_tabs" style="background: none;"  />
            <p:commandButton onclick="window.open(urlDeleg);
                    PF('wgdlgDeleg').hide();" value ="Delegate" type="button"  disabled="#{opportunities.oppCloseStatus!=0}"  title="Reply"  class="button_top btn_tabs" style="background: none;"  />
        </h:panelGrid>
    </p:dialog>

    <p:commandButton style="visibility: hidden" type="button" id="hiddenBtn" onclick="openTab(urlUser);"/>


    <script>
        var logusr =#{loginBean.userId};
        var urlUser, urlDeleg, chk = 0, rq;
        var intrval, indx = 0;
        var hide = 1;

        // set enable/disable preview email hide
        function setHidePreview() {
            if (hide == 1) {
                hide = 2;
            } else {
                hide = 1;
            }
            //PF('overlay').show();
        }

        // email fields are stored on string array in list
        // on mouse over email details are passing as params
        // fields are passed in single quote, no need to add here
        function emailPreview(to, frm, subj, atch, body) {

            document.getElementById('frmOpp:tabOpp:txtEmlTo').innerHTML = to;
            document.getElementById('frmOpp:tabOpp:txtEmlFrm').innerHTML = frm;
            document.getElementById('frmOpp:tabOpp:txtEmlSubj').innerHTML = subj;
            document.getElementById('frmOpp:tabOpp:txtEmlBody').innerHTML = body;
            var display1 = atch == 1 ? 'block' : 'none';
            document.getElementById('frmOpp:tabOpp:imgEmlAtch').style.display = display1;

            PF('overlay').show();
        }

        function emailAction(eusr, mailid, act, acces) {
//           alert('role' + #{loginBean.loggedInUser.userRole} + '; user : ' + #{loginBean.userId} + '; mail user : ' + eusr + '; access : ' + acces );
//             alert('action');
            // set url for email action
            if (act == 'read') {
//                urlUser = '#{loginBean.emailURL}/action.php?action=view_content&amp;reqid=#{loginBean.randomUid}&amp;mail=' + mailid + '&amp;viewemail=1&amp;src=1';
                urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=maillist&amp;email_id=' + mailid;

            } else {
                //alert('else');
                if (act == 'forward') {
//                    urlUser = '#{loginBean.emailURL}/action.php?action=' + act + '&amp;mail=' + mailid + '&amp;reqid=#{loginBean.randomUid}&amp;composeemail=1&amp;forward=1&amp;src=1';
                    urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=forward&amp;email_id=' + mailid;

                } else {
//                    urlUser = '#{loginBean.emailURL}/action.php?action=' + act + '&amp;mail=' + mailid + '&amp;reqid=#{loginBean.randomUid}&amp;composeemail=1&amp;src=1';
                    if (act === 'reply') {
                        urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=reply&amp;email_id=' + mailid;
                    } else {
                        urlUser = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=replyall&amp;email_id=' + mailid;
                    }
                }
            }


            //current user have accss, so openemail tab
            if (acces == true) {
                openTab(urlUser);

                return;
            }

            // if not owner, not same user,chek value of lbldeleg
            // 1=in salesteam,2=in delegates, 3=both
            chk = document.getElementById('jobDetailsButtonForm:tabviewJobs:lbldeleg').value;

            // random id generated for email user id
            rq = document.getElementById('jobDetailsButtonForm:tabviewJobs:lblreq').value;


            //alert('chk : ' + chk + '; act : ' + act);
            //user in sales team
            if (chk == 1) {
                openTab(urlUser);
                return;
            }


            //user in deleg or in both sales tem and deleg
            if (chk == 2 || chk == 3) {


                if (act == 'read') {

                    // urlDeleg = '#{loginBean.emailURL}/action.php?action=view_content&amp;reqid=' + rq + '&amp;mail=' + mailid + '&amp;viewemail=1&amp;src=1';

                    urlDeleg = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=maillist&amp;email_id=' + mailid;


                } else {
                    if (act == 'forward') {
                        //    urlDeleg = '#{loginBean.emailURL}/action.php?action=' + act + '&amp;mail=' + mailid + '&amp;reqid=' + rq + '&amp;composeemail=1&amp;forward=1&amp;src=1';
                        urlDeleg = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=forward&amp;email_id=' + mailid;
                    } else {
//                        urlDeleg = '#{loginBean.emailURL}/action.php?action=' + act + '&amp;mail=' + mailid + '&amp;reqid=' + rq + '&amp;composeemail=1&amp;src=1';
                        if (act === 'reply') {
                            urlDeleg = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=reply&amp;email_id=' + mailid;
                        } else {
                            urlDeleg = '#{globalParams.emailUrl}?autocheckemail=1&amp;reqid=#{loginBean.randomUid}&amp;fn=replyall&amp;email_id=' + mailid;
                        }

                    }
                }

                document.getElementById('frmOpp:tabOpp:emulabel').innerHTML = getLabel(act);
                PF('wgdlgDeleg').show();

            } else {
                // email user not in deleg,sales team
                PF('grl').renderMessage({"summary": " No access to email ", "severity": "warn"});
            }

        }

        function openTab(url) {
            var win = window.open(url, '_blank');
      console.log(url);
            win.focus();
        }

        function getLabel(act) {
            var lblAction;
            switch (act) {
                case 'read':
                    lblAction = "View email as : ";
                    break;
                case 'reply':
                    lblAction = "Reply as : ";
                    break;
                case 'replyall':
                    lblAction = "s All as : ";
                    break;
                case 'forward':
                    lblAction = "Forward as : ";
                    break;
            }
            return lblAction;
        }


        function openEmailWin(url, winName, event) {
            this.target = '_blank';
            var win = window.open(url, winName);
            console.log(url);
            win.focus();
            var browser = navigator.userAgent.toLowerCase();
            if (browser.indexOf('firefox') > -1) {
                win.close();
                var win1 = window.open(url, winName);
                win1.focus();
            }
            event.preventDefault();

        }

    </script>
    <style>
        .emlpad{     
            padding: 0px 2px 0px 2px !important;
        }

    </style>


</ui:composition>


