<ui:composition  xmlns="http://www.w3.org/1999/xhtml"
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:p="http://primefaces.org/ui"
                 xmlns:f="http://java.sun.com/jsf/core">

    <!--<ui:include src="/opploop/jobs/Lookup/JobSpecifier.xhtml"/>-->
    <!--3027 Jobs > Multiple Specifiers-->
    <style>
        .ui-chkbox .ui-chkbox-label{
            margin-left: 10px !important;
        }    
    </style>
    <!--#806 - CRM-2685: jobs: feedback items - Follow Up date especially - 09-06-2020-sharvani-->
    <!--#886: Jobs > Specifier update issue - Debug and fix issue -->
    <!--3027 Jobs > Multiple Specifiers-->
    <p:remoteCommand name="applyDBDCust" actionListener="#{jobs.applyJobSpecifier(viewCompLookupService.selectedCompany)}"  update="jobDetailsButtonForm:tabviewJobs:tblOthrList" />
    <!--3027 Jobs > Multiple Specifiers-->
    <p:remoteCommand name="applyDBDCont" actionListener="#{jobs.applySpecifierCompanyContact(viewContLookupService.selectedContact)}"  update="jobDetailsButtonForm:tabviewJobs:tblOthrList" />



    <!--pnlCustomerName-->
    <!--861 CRM-2699: Jobs List view: changes-->
    <p:remoteCommand name="applyCust" actionListener="#{jobs.updateFieldsOnSelection(viewCompLookupService.selectedCompany)}" update="pnlAwardedToName"/>
    <!--1989 Jobs: Owner Company and Contact -->
    <p:remoteCommand name="applyComp" actionListener="#{jobs.updateOwnerCompanyOnSelection(viewCompLookupService.selectedCompany)}" update="pnlOwnerCompPnl pnlOwnerContPnl"/>
    <p:remoteCommand name="applyCont" actionListener="#{jobs.applyOwnerCompanyContact(viewContLookupService.selectedContact)}" update="pnlOwnerContPnl pnlOwnerCompPnl" />

    <h:panelGroup id="contPnl">
        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Basic Information</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            </div>
        </div> 


        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--#2543: CRM-3371: Fwd: RE: Repfabric- Sales Outsource Solutions-->
                <!--#7048 Jobs -(*) missing and owner company, Contact look up, specifiers, awardees shows error on cancel click-->
                <p:outputLabel value="Name" styleClass="required"  /> 
            </div>
            <div class="ui-sm-12 ui-md-10 ui-lg-10"> 
                <!--               Bug #6603 CRM-5075: Job name showing different in job list and inside a job-->
                <p:inputText 
                    id="itJobName" title="#{jobs.jobDescr}" 
                    value="#{jobs.jobDescr}" label="Job Name" 
                    disabled="#{jobs.disableFields}"/>
            </div>
            <!--                        <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                                    </div>  -->
            <!--            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                            #2543: CRM-3371: Fwd: RE: Repfabric- Sales Outsource Solutions
                            <p:outputLabel value="Specifier"  />
                        </div>-->
            <!--            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                            <p:outputPanel id="pnlCustomerName">
                                <h:panelGroup class="ui-inputgroup"  >
            
                                    <p:inputText readonly="true" value="#{jobs.jobSpecifierName}" 
                                                 id="txtJobspecName" 
                                                 placeholder="[Select]"
                                                 />
                                    #806 - CRM-2685: jobs: feedback items - Follow Up date especially - 09-06-2020-sharvani
                                                            <p:commandButton 
                                                                styleClass="btn-info btn-xs"
                                                                icon="fa fa-search" id="btnCustomerLookup1" 
                                                                actionListener="#{viewCompLookupService.listAll('applyComp')}"
                                                                immediate="true"
                                                                update=":formCompLookup" oncomplete="PF('lookupComp').show();" 
                                                                disabled="#{jobs.disableFields}"/>
                                    <p:commandButton 
                                        styleClass="btn-info btn-xs"
                                        icon="fa fa-search" id="btnCustomerLookup1" 
                                        actionListener="#{viewCompLookupService.listAll('applyDBDCust',2,0,1)}" 
                                        immediate="true"
                                        update=":formCompLookup" oncomplete="PF('lookupComp').show();" 
                                        disabled="#{jobs.disableFields}"/>
                                    <p:inputText type="hidden" value="#{jobs.jobSpecifier}" id="itJobSpcId"/>
                                </h:panelGroup>
                            </p:outputPanel>
                        </div>  -->
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--#2543: CRM-3371: Fwd: RE: Repfabric- Sales Outsource Solutions-->
                <!--1989 Jobs: Owner Company and Contact starts here-->
                <!--2980  Jobs: UI and Label Updates for Owner and Notify by Email-->
                <p:outputLabel value="Sales Person" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                <p:selectOneMenu value="#{jobs.jobOwner}" id="jobOwner" style="width: 100%" disabled="#{jobs.disableFields}">
                    <f:selectItem itemLabel="[Select]" itemValue=""/>
                    <!--3142 Jobs: Sales person should list all Users.-->
                    <f:selectItems value="#{users.dependentUserListForJobs}" var="user" 
                                   itemValue="#{user.userId}" itemLabel="#{user.userName}" />
                </p:selectOneMenu>


            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6"> 
                <!--1039 Jobs > Notify by Email-->
                <!-- 12-12-2020:TASK #2674-jobs >Notify By Email issue -->
                <!--1989 Jobs: Owner Company and Contact starts here--> 
                <!--2980  Jobs: UI and Label Updates for Owner and Notify by Email-->
                <!--2980 Jobs: UI and Label Updates for Owner and Notify by Email-->
                <!--                                Task#3554-Jobs > Label updates - Notify Sales Person :23-02-2021 by harshithad-->
                <!-- #4963: Jobs: Watchers: Email Notification on Update Job -->
                <p:selectBooleanCheckbox style="margin-top: 5px;" value="#{jobs.isNotifyByMail}" disabled="#{jobs.disableFields}" itemLabel="Notify Sales Person/Watchers" id="chkMailStatus"  >
                                   <!--<p:ajax update="chkStatus" listener="#{users.chkStatusAction()}"/>-->
                </p:selectBooleanCheckbox>   
            </div>
            <!--1989 Jobs: Owner Company and Contact-->
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--1989 Jobs: Owner Company and Contact starts here-->
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:outputLabel for="txtOwnerCompName" value="#{custom.labels.get('IDS_JOB_OWNER_COMP')}"  />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                <p:outputPanel id="pnlOwnerCompPnl">
                    <h:panelGroup class="ui-inputgroup"  >

                        <p:inputText readonly="true" value="#{jobs.jobOwnerCompany}" 
                                     id="txtOwnerCompName" 
                                     placeholder="[Select]"
                                     />
                          <!--actionListener="#{jobs.listLookUpvalues('applyComp')}"-->
                        <!--1989 Jobs: Owner Company and Contact-->
                        <p:commandButton 
                            styleClass="btn-info btn-xs"
                            icon="fa fa-search" id="btnOwnerCompLookup1" 
                            actionListener="#{viewCompLookupService.listAll('applyComp')}"                          
                            immediate="true"
                            update=":formCompLookup" oncomplete="PF('lookupComp').show();" 
                            disabled="#{jobs.disableFields}"/>
                        <p:inputText type="hidden"  value="#{jobs.jobOwnerCompId}"  id="itJobOwnerCompId"/>
                    </h:panelGroup>
                </p:outputPanel>
            </div>  
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--1989 Jobs: Owner Company and Contact starts here-->
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:outputLabel  for="inpTxtContName" value="#{custom.labels.get('IDS_JOB_OWNER_CONT')}"  />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                <p:outputPanel id="pnlOwnerContPnl">
                    <h:panelGroup class="ui-inputgroup"  >
                        <p:inputText  readonly="true"  id="inpTxtContName"  value="#{jobs.jobOwnerContact}"  placeholder="[Select]" />
                        <!--                        PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                        <p:commandButton  icon="fa fa-search" title="Choose Contact" immediate="true"    
                                          action="#{viewContLookupService.disableContNewBtn(jobs.jobOwnerCompId)}"
                                          actionListener="#{viewContLookupService.list('applyCont',0,jobs.jobOwnerCompId,1)}"                                         
                                          update=":formContLookup" oncomplete="PF('lookupCont').show()"  
                                          styleClass="btn-info btn-xs"  disabled="#{jobs.disableFields}"/>
                          <!--actionListener="#{viewContLookupService.listAll('applyCont')}"--> 
                    </h:panelGroup>
                </p:outputPanel>
            </div>













            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--#2543: CRM-3371: Fwd: RE: Repfabric- Sales Outsource Solutions-->
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:outputLabel value="#{custom.labels.get('IDS_JOB_TYPE')}" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                <p:selectOneMenu  id ="smjobType" value="#{jobs.jobType}" disabled="#{jobs.disableFields}">

                    <f:selectItems value="#{oppTypesMst.getOppTypes()}" 
                                   var="oppType"   
                                   itemValue="#{oppType.oppTypeId}"
                                   itemLabel="#{oppType.oppTypeName}"/>
                </p:selectOneMenu>
            </div>
            <!--#718 - subtables - job category changed from recId to ctgid-->
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--#2543: CRM-3371: Fwd: RE: Repfabric- Sales Outsource Solutions-->
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:outputLabel value="#{custom.labels.get('IDS_JOB_CATEGORY')}"  />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                <p:selectOneMenu id="smjobCategory" value="#{jobs.jobCategory}" disabled="#{jobs.disableFields}"  >
                    <f:selectItem itemLabel="[Select]" itemValue=""/>
                    <f:selectItems value="#{jobs.getJobCategoryList()}" 
                                   var="cat"   
                                   itemValue="#{cat.jobCatgId}"
                                   itemLabel="#{cat.jobCatgName}"/>
                </p:selectOneMenu>
            </div>

            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--#833:  CRM-2694: Sub Tables > Job Activity-->

                <p:outputLabel value="#{custom.labels.get('IDS_JOB_STAGE')}"  />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                <p:selectOneMenu id="smjobActivity" value="#{jobs.jobActivity}" disabled="#{jobs.disableFields}"  >

                    <!--#833:  CRM-2694: Sub Tables > Job Activity-->
                    <f:selectItems value="#{jobActivitiesMstService.getJobActivity()}" 
                                   var="act"   
                                   itemValue="#{act.recId}"
                                   itemLabel="#{act.actName}"/>
                </p:selectOneMenu>
            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:outputLabel value="#{custom.labels.get('IDS_JOB_VALUE')}" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                <p:inputText    value="#{jobs.jobvalue}"
                                disabled="#{jobs.disableFields}"
                                id="itjobVal"
                                converterMessage="not a number"  
                                maxlength="14">
                    <f:convertNumber   groupingUsed="true"                                                                                      
                                       maxIntegerDigits="15"  
                                       minFractionDigits="2" maxFractionDigits="2" />
                </p:inputText>
            </div>


            <!--#4948: Jobs > Watchers--> 
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:outputLabel  value="#{custom.labels.get('IDS_WATCHERS')}" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">   

                <h:inputHidden value="#{jobs.watcherListSize}" id="iTJobWatcher" />
                <!--#8538  CRM-5935  ranvier: tool tip for watcher in every form (opp, job, quote, etc)-->
                <!--#5033: Jobs : Watchers > Unable to add Users with '-->
                <p:tooltip value="Type in the @ symbol for a selection list" rendered="#{!jobs.disableFields}"  showEvent="mouseover"   hideEvent="mouseout"  for="jobDetailsButtonForm:tabviewJobs:panelWatchers" />
                <h:panelGroup  id="panelWatchers" style="margin-bottom: 10px">
                    <p:autoComplete   id="jobWatchers" widgetVar="jobWatcherswVar" maxlength="60" minQueryLength="1"  
                                      maxResults="10" styleClass="actCont" style="width: 100%;height:50px;"  
                                      value="#{jobs.jobWacthers}"
                                      completeMethod="#{jobs.completeJobWatcherName}" var="u"
                                      itemLabel="#{u}" itemValue="#{u}" multiple="true"  forceSelection="true" disabled="#{jobs.disableFields}">
                        <p:ajax event="itemSelect"  listener="#{jobs.getListValues}"  update="jobDetailsButtonForm:tabviewJobs:iTJobWatcher :jobDetailsButtonForm:tabviewJobs:jobWatchers"/>
                        <p:ajax event="itemUnselect" update="jobDetailsButtonForm:tabviewJobs:iTJobWatcher :jobDetailsButtonForm:tabviewJobs:jobWatchers"/>

                    </p:autoComplete>
                </h:panelGroup>
            </div>

            <!--26-12-2023 12761 CRM-7599   OASIS: Surface Project Rank for Quotes-->
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel  value="Rank" />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">   
                <p:inputText  value="#{jobs.jobRank}" disabled="#{jobs.disableFields}" id="itjobRank" maxlength="60" />
            </div>

        </div>



        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Specifiers</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6">
            </div>
        </div> 
        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                <p:dataList value="#{jobs.specifierList}" var="jbsp" id="tblOthrList" varStatus="status" style="border: none!important;"  >    
                    <div class="ui-g ui-fluid" >
                        <div class="ui-sm-12 ui-md-2 ui-lg-2" style="padding-left: 0px; margin-top: -10px; margin-bottom: -10px;">
                            <p:outputLabel value="#{jbsp.compTypeName}" id="lbl#{status.index}"></p:outputLabel>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4" style="padding-left: 4px; margin-top: -10px; margin-bottom: -10px;">
                            <p:inputText readonly="true" value="#{jbsp.compName}" id="txt#{status.index}"   /><p:spacer width="6"/> 
                        </div>
                        <div class="ui-sm-12 ui-md-2 ui-lg-2" style="padding-left: 10px; margin-top: -10px; margin-bottom: -10px;">
                            <p:outputLabel value="Contact">  </p:outputLabel>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4" style="padding-left: 4px; margin-top: -10px; margin-bottom: -10px;">
                            <h:panelGroup id="oppOthr#{status.index}"  class="ui-inputgroup">  
                                <p:inputText readonly="true" value="#{jbsp.contFullName}" id="txtCont#{status.index}"   /><p:spacer width="6"/>
                                <!--                                PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                                <p:commandButton icon="fa fa-search" title="Choose Contact" immediate="true"  style="margin-left: -5px;"  
                                                 action="#{viewContLookupService.disableContNewBtn(jbsp.compId)}"
                                                 actionListener="#{viewContLookupService.list('applyDBDCont',jbsp.compType,jbsp.compId,1)}"  disabled="#{jobs.disableFields}"
                                                 update=":formContLookup" oncomplete="PF('lookupCont').show()"  
                                                 styleClass="btn-info btn-xs" 
                                                 />
                                <p:commandLink actionListener="#{jobs.removeSpecifier}" update="jobDetailsButtonForm:tabviewJobs:tblOthrList" type="button" style="font-size: larger; color: red;" title="Remove Specifier" disabled="#{jobs.disableFields}">
                                    <f:param  name="indexPtr" value="#{status.index}" />
                                    <f:param  name="compTypeName" value="#{jbsp.compTypeName}" /> 
                                    <f:param  name="compId" value="#{jbsp.compId}" />
                                    <f:param  name="contId" value="#{jbsp.contId}" />
                                    <f:param  name="contFullName" value="#{jbsp.contFullName}" />
                                    <h:outputText value="X"/>
                                </p:commandLink>
                            </h:panelGroup>
                        </div> 
                    </div>
                </p:dataList>
            </div>
            <!--viewCompLookupService.listAll('applyDBDCust',2,0,1)-->
            <div class="ui-sm-12 ui-md-2 ui-lg-2" style="margin-top: -18px; "> 
                <!--3166 inactive companies displaying in job Specifier lookup-->
                <p:commandLink  style="font-size: x-large; color: #4ca75a;" title="Add Specifier companies" 
                                actionListener="#{viewCompLookupService.listCRM(2, 'applyDBDCust')}" 
                                immediate="true"
                                update=":formCompLookup" oncomplete="PF('lookupComp').show();" 
                                disabled="#{jobs.disableFields}">
                    <f:param  name="indexPtr" value="#{status.index}" />
                    <f:param  name="compTypeName" value="#{jbsp.compTypeName}" />
                    <f:param  name="compName" value="#{jbsp.compName}" />
                    <h:outputText value="+"/>
                </p:commandLink> 
            </div>
            <div class="ui-sm-12 ui-md-10 ui-lg-10"> 
            </div>
        </div>



        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Details</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            </div>
        </div> 
        <div class="ui-g ui-fluid">

            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--#2543: CRM-3371: Fwd: RE: Repfabric- Sales Outsource Solutions-->
                <p:outputLabel value="#{custom.labels.get('IDS_JOB')} No"/>
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                <p:inputText value="#{jobs.jobNo}" id="itJobno" maxlength="40"  disabled="#{jobs.disableFields}"/>
            </div>

            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--#2543: CRM-3371: Fwd: RE: Repfabric- Sales Outsource Solutions-->
                <p:outputLabel value="URL"/>
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                <!--3116 CRM-3794: BUG and FIX: jobs URL-->


                <!--Bug #3978:  https://pms.indeadesignsystems.com/task_management/tasks/3978-->
                <h:outputLink rendered="#{jobs.enableLinkURl}" value="#{jobs.fetchUrl()}"   style="color:#1891c1;text-decoration:none;"  target="_blank" >
                    #{jobs.trimUrlName(jobs.jobUrl)} 
                </h:outputLink>
                <p:inputText value="#{jobs.jobUrl}"  id="itJobUrl" maxlength="220"  disabled="#{jobs.disableFields}" rendered="#{jobs.enableURl}" />
            </div>


            <div class="ui-sm-12 ui-md-2 ui-lg-2">

                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:outputLabel value="#{custom.labels.get('IDS_JOB_BID_DATE')}"/>
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                <p:calendar  id="dtBidDate" value="#{jobs.jobBidDate}" class="calendarClass" disabled="#{jobs.disableFields}"
                             widgetVar="wobiddate"
                             pattern="#{globalParams.dateFormat}" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>
            </div>

            <!--                Ticket #139494 - jobs feedback-->
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--                22-01-2021:Task# #3131-CRM-3794: jobs edit  label -  Order Date-->
                <p:outputLabel value="#{custom.labels.get('IDS_JOB_ORDER_DATE')}"  />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                <p:calendar  id="dtprodDate" value="#{jobs.jobProdDate}"  class="calendarClass" disabled="#{jobs.disableFields}"
                             widgetVar="woproddate"
                             pattern="#{globalParams.dateFormat}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>
            </div>

            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel value="Street"  />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                <p:inputTextarea  style="resize: none" styleClass="tds1" rows="4" autoResize="false"  maxlength="180" value="#{jobs.jobAdress1}" disabled="#{jobs.disableFields}" />
            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel value="City"  />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                <p:inputText value="#{jobs.jobcity}"  maxlength="40"  id="itJobcity" disabled="#{jobs.disableFields}" />
            </div>

            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--Bug #2113: #2088: Pending Issues  : JOBS: State and ZIP- Custom fields-->
                <p:outputLabel value="#{custom.labels.get('IDS_STATE')}"  />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                <p:inputText value="#{jobs.jobState}" maxlength="40"  id="itJobState" disabled="#{jobs.disableFields}" />
            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--#2088: CRM-3042: Intralec: change labels for state and zip - Jobs-->
                <p:outputLabel value="#{custom.labels.get('IDS_ZIP')}"  />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                <p:inputText  maxlength="12" value="#{jobs.jobZipCode}" id="itJobZip" disabled="#{jobs.disableFields}"/>
            </div>

            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel value="Country"   />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                <p:inputText  value="#{jobs.jobCountryCode}" maxlength="30"  id="itJobCountryCode" disabled="#{jobs.disableFields}" />
            </div>
            <!--#806 - CRM-2685: jobs: feedback items - Follow Up date especially - 09-06-2020-sharvani-->
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <!--8622: 16/08/2022: CRM-5988  Jobs: Relabels not available / not connected (Analysis)-->
                <p:outputLabel value="#{custom.labels.get('IDS_JOB_FOLLOWUP')} " styleClass="required"  />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4">
                <p:calendar  id="itFollowUpDate" value="#{jobs.jobFollowUpDate}"  class="calendarClass" disabled="#{jobs.disableFields}"
                             widgetVar="wopfollow"
                             converterMessage="#{custom.labels.get('IDS_JOB_FOLLOWUP')} not valid" 
                             required="true" 
                             requiredMessage="#{custom.labels.get('IDS_JOB_FOLLOWUP')} not entered"
                             pattern="#{globalParams.dateFormat}">
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>

            </div>

        </div>
        <!--                CRM-1268: Add notes field to Jobs - 2145 - 30-10-2019-->
        <!--              CRM-8745/8783 task:15001 Jobs: "Notes" label needs to become "Description" in the label - 02-12-2024-->
        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">#{custom.labels.get('IDS_JOB_DESC')}</p:outputLabel>
            </div>
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
            </div>
        </div>  
        <div class="ui-g ui-fluid">
            <div class="ui-sm-12 ui-md-12 ui-lg-12">
                <p:inputTextarea placeholder="Add Description here"  
                                 rows="4"  autoResize="false" 
                                 style="width: 100%" 
                                 value="#{jobs.jobNotes}"
                                 id="jobNotesId" 
                                 disabled="#{jobs.disableFields}"/>
            </div>
        </div>
        <!--2904 CRM-2944: Jobs >  Awardees-->
        <div class="ui-g ui-fluid header-bar">
            <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Awardees</p:outputLabel>
                <p:spacer width="4"/>
                <!--2904   actionListener="#{jobs.listLookUpvalues('applyCust')}" update=":formCompLookup"--> 
<!--                <p:commandButton id="btnOppCustSearch" icon="fa fa-plus" title="Choose Company" immediate="true" disabled="#{jobs.disableFields}"
                                     actionListener="#{jobs.listLookUpvalues('applyCust')}" update=":formCompLookup" tabindex="4" 
                                     style="width: 22px; height: 22px;" styleClass="btn-info btn-xs" />-->
            </div>
            <!--<div class="ui-sm-12 ui-md-6 ui-lg-6 ">-->
            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                <p:outputLabel value="Awarded Date"  />
            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                <p:calendar  id="dtAwardedDate" value="#{jobs.jobAwardedDate}" class="calendarClass" disabled="#{jobs.disableFields}"
                             widgetVar="awdiddate"
                             pattern="#{globalParams.dateFormat}" >
                    <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                </p:calendar>
            </div>
            <!--</div>-->
        </div> 
        <div class="ui-g ui-fluid">
            <!--            <div class="ui-sm-12 ui-md-2 ui-lg-2">
                            <p:outputLabel value="Awarded To"  />
                        </div>-->
            <div class="ui-sm-12 ui-md-12 ui-lg-12">  
                <p:outputPanel id="pnlAwardedToName" class="ui-inputgroup">
                    <h:inputHidden value="#{jobs.jobAwardedToId}" id="itJobAwrdToId"/>
                    <h:inputHidden value="#{jobs.jobAwardedToName}" id="itJobAwrdToName"/>
                    <p:autoComplete  id="jbAwd"   multiple="true"  maxlength="60" disabled="#{jobs.disableFields}"
                                     value="#{jobs.jobAwardedToNameList}">
                        <!--<p:ajax event="itemSelect" listener="#{jobs.onSelectCustomer}" update="pnlAwardedToName"/>-->
                        <p:ajax event="itemUnselect" listener="#{jobs.onSelectCustomer}" update="pnlAwardedToName"/>
                    </p:autoComplete>

                </p:outputPanel>
            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2" >  
                <!--3103 CRM-3782: jobs: specifier/awarded inconsistent icons--> 
                <p:commandLink  style="font-size: x-large; color: #4ca75a;"  id="btnOppCustSearch"  title="Add awardees" 
                                actionListener="#{viewCompLookupService.listCRM(0,'applyCust')}" update=":formCompLookup"
                                oncomplete="PF('lookupComp').show();"
                                immediate="true"  
                                disabled="#{jobs.disableFields}"> 
                    <h:outputText value="+"/>
                </p:commandLink> 
            </div>

            <div class="ui-sm-12 ui-md-4 ui-lg-4">


            </div>
            <div class="ui-sm-12 ui-md-2 ui-lg-2">

            </div>
            <div class="ui-sm-12 ui-md-4 ui-lg-4"> 

            </div>
        </div> 

    </h:panelGroup>
    <style>
        .ui-state-disabled {
            opacity: 0.6 !important;
        }
        .grpBox  .ui-widget-content  ul{
            padding:0 20px!important;
            margin:0px!important;

        }

        /*3027 Jobs > Multiple Specifiers*/
        #jobDetailsButtonForm\:tabviewJobs\:tblOthrList_content{ 
            border: none!important; 
        }
        #jobDetailsButtonForm\:tabviewJobs\:tblOthrList_list{
            list-style-type: none!important;
        }
        .ui-datalist-empty-message{display: none!important;}
        ul, ol {list-style-type: none;}

        .ui-autocomplete-multiple-container>.ui-widget>.ui-inputfield>.ui-state-default>.ui-corner-all>.ui-state-disabled
        {display:  none!important;}
        .ui-autocomplete-multiple-container>.ui-widget>.ui-inputfield>.ui-state-default>.ui-corner-all
        {display:  none!important;}


        .ui-autocomplete-token>.ui-state-active>.ui-corner-all>.ui-state-hover{
            background: #8cb4cc!important;
        }

        body .ui-autocomplete li.ui-autocomplete-token:hover {
            background: #5f99bb!important; 

        }
        #jobDetailsButtonForm\:tabviewJobs\:jbAwd_input{display: none!important;}

        .ui-autocomplete-multiple-container {
            min-height: 34px;
        } 
        /*   .ui-autocomplete-multiple-container .ui-widget .ui-inputfield .ui-state-default .ui-corner-all .ui-state-disabled{
               min-height: 34px;
           } */

    </style>  
    <script>
        //        #704202: sierra: gui crmsync javalang
        function openSyncWin(url, winName, event) {
            //            #884407 - Spectrum - CRM Sync not working
            if (url.indexOf("http://") === - 1 &amp;&amp; url.indexOf("https://") === - 1) {
                url = "http://" + url;
            }
            this.target = '_blank';
            var win = window.open(url, winName);
            win.focus();
            var browser = navigator.userAgent.toLowerCase();
            if (browser.indexOf('firefox') > -1) {
                win.close();
                var win1 = window.open(url, winName);
                win1.focus();
            }
            event.preventDefault();

        }
    </script>
</ui:composition>
