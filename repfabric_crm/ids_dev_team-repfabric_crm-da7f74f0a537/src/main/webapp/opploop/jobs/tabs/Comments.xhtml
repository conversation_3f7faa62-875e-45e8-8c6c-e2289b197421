<ui:composition
   xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core">
<!--                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">-->

     <!--Load Comments -->


    <p:panel id="pnlCMNTS" class="pnls">
        <p:inputTextarea rows="10" cols="60" value="#{jobComments.jobCommText}" id="cmnt"  style="width:100% !important;resize: none" autoResize="false"/>
        <p:watermark for="cmnt" value="Enter comment here"/>
        <br></br>
        <p:commandButton  value="Save" 
                          action="#{jobComments.save()}"
                          update="dtCmnt cmnt :jobDetailsButtonForm:growlJobSave"
                          process=":jobDetailsButtonForm:tabviewJobs:pnlCMNTS"
                         styleClass="btn btn-success  btn-xs"
                        
                         />
        <!--        25-01-2021:Task#3164-CRM-2701: #3: Jobs & Quotes: notifications-->
          <p:spacer width="10px"/>
          <p:selectBooleanCheckbox style="margin-top: 5px;" value="#{jobComments.notifySalesPerson}"   itemLabel="Notify Sales Person" id="oneMenuNotifyMail"  >
                                                  
                </p:selectBooleanCheckbox>    
         <p:spacer width="30px"/>
         <div class="dtnoborder"  >
            <!--#807698 08/11/2017-->
            <p:dataTable  value="#{jobComments.listComments}" emptyMessage="No #{custom.labels.get('IDS_COMMENTS')} Added" 
                          var="cmnt" 
                          id="dtCmnt">
                <p:column style="padding: 0px;" >  
                    <div class="comment_div">  
                        <div>
                            
                            <!--#1200 - CRM-2917: Jobs - no way to know when a comment was added - when was Bill's comments? -20-07-2020 - sharvani-->
                            <h:outputLabel id="cmntJobsDate" value="#{jobComments.getFormattedDate(cmnt.jobcommDate, 'dt')}" style="float: right;"/>
                            <p:spacer width="4" style="float: right;"/>
                            <h:graphicImage library="images" name="dummy.jpg" width="20" height="20"/>                 
                            <h:outputLabel  value="#{users.getUserName(cmnt.jobCommUser)}" style="font-weight: bold"/>

                            <h:outputLabel class="dtcomnt"  value="#{params.convertFromUTC(cmnt.jobcommDate)}" >
                                <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                            </h:outputLabel>
                            <p:spacer width="4px"/>
                            <p:commandButton 
                                             rendered="#{cmnt.jobCommUser eq loginBean.userId}"
                                             styleClass="btn-danger btn-xs btn-icon"
                                             style="width: 20px;height: 10px;border: none; background: none;float: right"
                                             icon="fa fa-trash"
                                             title="Remove comment"
                                             process="@this" 
                                             actionListener="#{jobComments.delete(cmnt.recId)}" 
                                             update="dtCmnt " 
                                              />
                        </div>
                        <p:spacer width="4px"/>
                        <!-- //04-11-2024 : #14827 : ESCALATIONS CRM-8657   Jobs: Comments do not wrap and goes off screen-->
                        <div class="comment_cmnt" style="white-space: pre-wrap; word-wrap: break-word; overflow-wrap: break-word;">
                            <h:outputLabel id="cmnt" value="#{cmnt.jobCommText}" escape="false"/>  
                        </div>

                    </div>

                </p:column>

            </p:dataTable>   
        </div>
    </p:panel>
</ui:composition>



