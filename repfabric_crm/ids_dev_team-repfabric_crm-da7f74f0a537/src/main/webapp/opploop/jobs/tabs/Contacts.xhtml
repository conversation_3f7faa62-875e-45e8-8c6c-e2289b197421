<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui">
    
<!--#2793: Jobs > Job Contacts-->
    <h:panelGroup id="pnlJobContacts" class="pnls">


        <p:remoteCommand id="rcLoadCont" actionListener="#{jobContactService.loadJobsContacts(jobs.jobId)}" autoRun="true" name="loadJobConts" update="dTableJobCont"  />
        
        <p:remoteCommand id="rcLoadContLookUp" name="linkContact" 
                         actionListener="#{jobContactService.applyContactJob(viewContLookupService.selectedContact,jobs.jobId)}" oncomplete="loadJobConts();"/>



        <p:commandButton   id="btnJobLinkCont" value="Link" title="Link existing contact" 
                           immediate="true"
                           class="btn btn-primary btn-xs"
                           actionListener="#{viewContLookupService.listAll('linkContact')}" 
                           update=":formContLookup" oncomplete="PF('lookupCont').show()"/>
        <br></br>
        <p:spacer/>
        
        <p:dataTable id="dTableJobCont" value="#{jobContactService.jobContacts}"

                     var="cont" rowKey="#{cont.contId}"  paginator="true"
                     emptyMessage="No Contacts found" >


            
            <p:column id="colName"  headerText="Name" filterBy="#{cont.contFullName}" filterMatchMode="contains" sortBy="#{cont.contFullName}">

                
                <p:outputLabel id="oLblName" value="#{cont.contFullName}" />
            </p:column>
            
            <p:column id="colCompany" headerText="Company" filterBy="#{cont.contCompName}" filterMatchMode="contains" sortBy="#{cont.contCompName}" >
                
                <p:outputLabel id="oLblCompany" value="#{cont.contCompName}" />
            </p:column>
            
            <p:column id="colJobTitle" headerText="#{custom.labels.get('IDS_JOB_TITLE')}" filterBy="#{cont.contJobTitle}" filterMatchMode="contains" sortBy="#{cont.contJobTitle}">
                
                <p:outputLabel id="oLblJobTitle" value="#{cont.contJobTitle}" />
            </p:column>

            
            <p:column id="colPhone" headerText="Phone" filterBy="#{cont.displayPhones()}" filterMatchMode="contains" sortBy="#{cont.displayPhones()}">
                
                <p:outputLabel id="oLblPhone" value="#{cont.displayPhones()}" />
            </p:column>
            
            <p:column id="colEmail" headerText="Email" filterBy="#{cont.displayEmailAdd()}" filterMatchMode="contains" sortBy="#{cont.displayEmailAdd()}">
                
                <p:outputLabel id="oLblEmail" value="#{cont.contEmails}" />

            </p:column>
            <p:column width="5%" >
                <!--1531 CRM-3150: add "Confirm deletion" warning to Contacts in an Opp-->
                <p:commandButton id="btnRemoveCont"  title="Remove Contact" onclick="PF('confirmContact').show()"

                                 process="@this" 
                                 action="#{jobContactService.delContact(cont)}"                                   
                                 icon="fa fa-trash" class="btn-danger btn-xs" >
                    <!--<p:confirm header="Confirmation" message="Are you sure to delete?"  />--> 
                </p:commandButton>

            </p:column>
        </p:dataTable>
        
        <p:confirmDialog id="cnfrmDlgCont" header="Confirm deletion"  width="400" global="false"
                         message="Are you sure to delete this Contact? "
                         widgetVar="confirmContact" >
            <div class="div-center">
                
                <p:commandButton id="btnDelete" value="Delete" actionListener="#{jobContactService.removeContact(jobs.jobId)}" update="dTableJobCont" class="btn btn-danger btn-xs" process="@this"   
                                 oncomplete="PF('confirmContact').hide()"   />
                <p:spacer width="4"/>
                <p:commandButton id="btnCancel" value="Cancel"  class="btn btn-warning btn-xs"    onclick="PF('confirmContact').hide()" 
                                 type="button"  />
            </div>
        </p:confirmDialog>
    </h:panelGroup>

</ui:composition>
