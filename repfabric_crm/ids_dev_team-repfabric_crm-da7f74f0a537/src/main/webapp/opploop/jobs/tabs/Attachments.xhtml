<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">
    <p:remoteCommand autoRun="true" name="loadJobAtt" actionListener="#{jobAttachments.listAttcJobImp()}" update=":jobDetailsButtonForm:tabviewJobs:dtAtc"  oncomplete=""  /> 

    <h:panelGroup id="pnlATTACH" class="pnls">
      
<!--         PMS 3898: CRM-4046: PO/company/Opp/Job: ID drag and drop area and eliminate the "upload" button -->
        <p:fileUpload fileUploadListener="#{jobAttachments.uploadAttchments}" 
                      mode="advanced" dragDropSupport="true"  
                      auto="true"  
                      sizeLimit="31457280" label="Choose File" update="dtAtc" invalidSizeMessage="Exceeds maximum size limit of 30MB" />

        <p:dataTable  value="#{jobAttachments.listJobAttachments}"
                      style="width: 80%"
                      var="job" id="dtAtc">
            <p:column width="6%" >
                <p:commandButton actionListener="#{jobAttachments.delATt(job)}"
                                 update="jobDetailsButtonForm:tabviewJobs:dtAtc" onclick="PF('confirmations').show()"  
                                 process="@this" 
                                 icon="fa fa-trash" 
                                 title="Remove Attachment" 
                                 class="btn-xs btn-danger">
                </p:commandButton>

            </p:column>
            <p:column headerText="File Name" style="text-align: left">

                #{job.jobAttDispName}

                <div style="float: right;">
<!--                    <p:outputLabel value="#{job.jobAttSrc/1024}"   
                                   style="font-weight: lighter;font-size: smaller;" >
                        <f:convertNumber  maxFractionDigits="1" type="number"/>
                    </p:outputLabel>-->
<!--                    <p:outputLabel value=" kb" 
                                   style="font-weight: lighter;font-size: smaller;"/>-->
                </div>
            </p:column>
            <p:column>
                <p:commandButton   immediate="true" process="@none" title="Download Attachment"  class="btn-primary btn-xs"  icon="fa fa-download" 
                                   onclick="primeFaces.monitorDownload(start, stop)">
                    <p:fileDownload    value="#{job.file}"/>
                </p:commandButton>
            </p:column>


        </p:dataTable>
        <p:confirmDialog header="Confirm delete"  width="400" global="false"
                         message="Are you sure to delete attachment?" 
                         widgetVar="confirmations" >
            <div class="div-center">


                <p:commandButton value="Delete" actionListener="#{jobAttachments.delete()}" update="jobDetailsButtonForm:tabviewJobs:dtAtc"
                                 class="btn btn-danger btn-xs" process="@this"  oncomplete="PF('confirmations').hide()"     />
                <p:spacer width="4"/>
                <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"    onclick="PF('confirmations').hide()" 
                                 type="button"  />
            </div>
        </p:confirmDialog>
        
        <style>
            div.ui-fileupload .ui-fileupload-buttonbar span, div.ui-fileupload .ui-fileupload-buttonbar button {
                padding-bottom:27px !important;
            }
            
/*             PMS 3898: CRM-4046: PO/company/Opp/Job: ID drag and drop area and eliminate the "upload" button */
.ui-fileupload-content:before{
    content: "Please Drop Files Here";
    margin-left: 50vh;
    text-justify: auto;
    font-size: 20px;
    color:#b4c3d0;
    align-content: center;

} 

        </style>
    </h:panelGroup>

</ui:composition>

