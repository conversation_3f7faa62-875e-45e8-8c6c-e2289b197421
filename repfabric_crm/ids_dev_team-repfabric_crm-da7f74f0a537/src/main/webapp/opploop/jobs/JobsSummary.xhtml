<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric CRM
// Filename: JobsSummary.xhtml
// Author: Sharvani
//*********************************************************
/*
* Copyright (c) 2014 Indea Engineering, A Proprietary Firm, All Rights Reserved.
*/-->

<ui:composition    xmlns="http://www.w3.org/1999/xhtml"
                   xmlns:ui="http://java.sun.com/jsf/facelets"
                   xmlns:h="http://java.sun.com/jsf/html"
                   xmlns:p="http://primefaces.org/ui"
                   xmlns:f="http://java.sun.com/jsf/core">



    <div class="ui-g ui-fluid header-bar">
        <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
        </div>
        <div class="ui-sm-12 ui-md-6 ui-lg-6 " >
            <!--#2543: CRM-3371: Fwd: RE: Repfabric- Sales Outsource Solutions-->
            <!--3-12-2020 :TASK#2593 -Custom Labels ->jobs name change not affecting to job no and advanced search,close job -->
            <p:outputLabel styleClass="acct-name-hdr" style="width: 100px; white-space: normal" class="sub_title" >Summary</p:outputLabel>
        </div>
        <div class="ui-sm-12 ui-md-3 ui-lg-3 ">
        </div>
    </div>
    <!--3-12-2020 :TASK#2593 -Custom Labels ->jobs name change not affecting to job no and advanced search,close job -->
    <br></br>

    <!--<h1 class="hedding">Job Summary</h1>-->
    <h:form id="frmJobStatus">
        <table style="width: 100%;table-layout: fixed"> 
            <tr>
                <td style="text-align: right">
                    Open/Closed
                </td>
                <td>:</td>
                <td>
                    <!--//28-03-2023 : #10967 : CRM-6571   Jobs: Can NOT reopen a Job-->
                    <p:commandButton 
                        id="btnStatus"  rendered="#{jobs.recId>0}"
                        title="Closing Status" class="clos_btn btn_tabs"
                        value="#{jobs.jobCloseStatus == 0 ?'Open':'Closed'} "
                        oncomplete="PF('jobCloseDlg').show()"
                        actionListener="#{jobs.loadJobOpenCloseData(jobs.jobId)}"
                        update=":frmJobCloseDlg:outputPanelBtns :frmJobCloseDlg:calClose">
                    </p:commandButton>
                </td>
            </tr>

        </table>
    </h:form>
    <h:form id="frmJobSummary">
        <p:outputPanel id="jobsummary" style="text-align: center">
            <table style="width: 100%;background: white;table-layout: fixed;padding: 0px 6px;">
                <tr>
                    <td style="text-align: center">
                        <p:outputLabel value="#{jobs.jobDescr}" />
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center">
                        <p:outputLabel value="#{jobs.jobSpecifierName}" />
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center">
                        <p:outputLabel value="#{jobs.jobTypeName}" />
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center">
                        <p:outputLabel value="#{jobs.jobActivityName}" />
                    </td>
                </tr>
                <!--//27-12-2023 : #12778 : CRM-7619	Oasis Jobs: Surfacing Quote Number-->

                <tr >
                    <p:outputPanel id="linkedQuot">
                        <td style="display: flex;flex-direction: column;">
                            <p:outputPanel id="jobQuotIdrepeat1" style="text-align: left;">
                                <p:outputLabel value="Linked quotes: " id="linkedQuotes" style="#{viewJobs.jobQuotIdss.size() == null or viewJobs.jobQuotIdss.size() == 0 ? 'display:none' : ''}" />
                            </p:outputPanel>
                            <p:outputPanel id="jobQuotIdrepeat" style="text-align: left;">
                                <p:outputPanel style="#{viewJobs.jobQuotIdss.size() == null or viewJobs.jobQuotIdss.size() == 0 ? 'display:none' : ''}">
                                    <p:remoteCommand autoRun="true" onstart="type()" />
                                    <ui:repeat value="#{viewJobs.returnNumList(viewJobs.jobQuotIds)}" var="quotId" id="quotRepeater"
                                               varStatus="quotIdStatus">
                                        #{(quotIdStatus.index != 0) ? ', ' : ''}<h:outputLink style="display: inline" value="#{viewJobs.showAccessDenied(quotId,jobs.jobId)}" target="_blank" title="#{quotId}">#{quotId}</h:outputLink>
                                    </ui:repeat>
                                </p:outputPanel>
                                <script>
                                    function type() {
                                        let element = document.getElementById("frmJobSummary:jobQuotIdrepeat");
                                        if (element) {
                                            let updatedContent = element.innerHTML.replace(/\s*,/g, ',');
                                            element.innerHTML = updatedContent;
                                            document.getElementById("frmJobSummary:jobQuotIdrepeat").innerHTML = updatedContent
                                        }
                                    }
                                </script>
                            </p:outputPanel>
                        </td>


                    </p:outputPanel>
                </tr>

                <tr>
                    <td>
                        <!--<p:graphicImage alt="Address" title="Address" library="images" class="fa fa-map-marker"  style="height:16px;width: 16px;"/>-->
                        <p:graphicImage title="Address" library="images" class="fa fa-map-marker"  style="height:16px;width: 16px;" rendered="#{jobs.recId>0}"/>
                        <!--<p:outputLabel  value="#{jobs.jobFormattedAddr}" title="View in Google Maps"  />-->
                        <h:outputLink style="padding-left:7px;" value="#{jobs.redirect()}" target="_blank" >#{jobs.jobFormattedAddr}</h:outputLink>
                    </td>
                </tr>
            </table>      
            <!--#806 - CRM-2685: jobs: feedback items - Follow Up date especially - 09-06-2020-sharvani-->
            <br></br>
            <p:outputPanel  rendered="#{jobs.jobId>0}"  
                            style="font-size: 0.795em;
                            color: #6A6F73;">
                <h:outputLabel value="Created:"  style="font-weight: bold"  />   
                <p:spacer width="4"/>
                <!--Bug #6724 : CRM-5107: Review issues w Aspinall-->
                <h:outputLabel   value="#{rFUtilities.convertFromUTC(jobs.insDate)}" >
                    <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                </h:outputLabel>
                <br></br>
                by 
                <h:outputLabel   value=" #{jobs.insUserName}" />

                <br></br>
                <br></br>

                <h:outputLabel value="Last update:"  style="font-weight: bold"  />   
                <p:spacer width="4"/>
                <!--Bug #6724 : CRM-5107: Review issues w Aspinall-->
                <h:outputLabel   value="#{rFUtilities.convertFromUTC(jobs.updDate)}" >
                    <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                </h:outputLabel>

                <br></br>
                by 

                <h:outputLabel   value=" #{jobs.updUserName}" />
            </p:outputPanel>

        </p:outputPanel>
    </h:form>
    <!--//28-03-2023 : #10967 : CRM-6571   Jobs: Can NOT reopen a Job-->
    <p:dialog widgetVar="jobCloseDlg" header="Close #{custom.labels.get('IDS_JOB')}" modal="true" resizable="false">
        <h:form id="frmJobCloseDlg">
            <p:panel id="pnlClose" class="pnldlgs" style="border: none !important">
                <p:panelGrid id="grid" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8"
                             style="min-height: 50px"       layout="grid" styleClass="box-primary no-border ui-fluid">
                    <p:outputLabel value="Close Date:"></p:outputLabel>
                    <p:calendar id="calClose" value="#{jobs.jobCloseDate}" converterMessage="Invalid date format"  style="width: 100%" 
                                class="calendarClass" pattern="#{globalParams.dateFormat}" maxlength="10">
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                        <p:ajax event="change" update=":frmJobCloseDlg:calClose"/>
                        <p:ajax event="dateSelect" listener="#{jobs.onDateSelect}"/>
                    </p:calendar>

                    <p:outputLabel value="Reason:"></p:outputLabel>
                    <p:inputText value="#{jobs.jobCloseReason}" >
                    </p:inputText>
                </p:panelGrid>
            </p:panel>
            <!--SONARQUBE ISSUES - sharvani - 23-01-2020-->
            <!--<center>-->
            <p:outputPanel id="outputPanelBtns">

                <div align="center">
                    <!--//28-03-2023 : #10967 : CRM-6571   Jobs: Can NOT reopen a Job-->
                    <!--#15092 CRM-8805 10/12/2024  Jobs: Can Not Reopen Job.-->
                    <p:commandButton value="Reopen" styleClass="btn btn-success  btn-xs" rendered="#{jobs.jobCloseStatus == 1}"
                                      oncomplete="PF('confirmDialog').show()">      
                    </p:commandButton>
                    <p:commandButton value="Close" styleClass="btn btn-success  btn-xs" title="Closing Status" rendered="#{jobs.jobCloseStatus != 1}" 
                                     actionListener="#{jobs.saveCloseJob(jobs)}">
                    </p:commandButton>
                    <p:spacer width="8px" />
                    <p:commandButton value="Cancel" styleClass="btn btn-warning  btn-xs" oncomplete="PF('jobCloseDlg').hide()"/>
                </div>
            </p:outputPanel>
            <!--</center>-->              
        </h:form>
    <!--#15092 CRM-8805 10/12/2024 the following was misplaced after p:confirmDialog-->      
    </p:dialog>
    
    <!--//28-03-2023 : #10967 : CRM-6571   Jobs: Can NOT reopen a Job-->
    <!--#15092 CRM-8805 10/12/2024  Jobs: Can Not Reopen Job. Was not working due to incomplete p:confirmDialog and misplacement of p:dialog-->
    <h:form id="frmGoalGrpCnfrm">
        <p:confirmDialog global="true" header="Confirmation" widgetVar="confirmDialog"
                         showEffect="fade"
                         message="Are you sure to Reopen?" id="dlgAjConfirmation">
            <div class="div-center" >
                <!--14-12-2020:TASK#2714-Sub Tables > Goal Group, Interest - Add Ids -->
                <p:commandButton id="btnYes" value="Yes"
                                 styleClass="ui-confirmdialog-yes btn btn-success btn-xs"  
                                 action="#{jobs.updateStatus(jobs.recId,jobs.jobId)}"
                                 oncomplete="PF('confirmDialog').hide();"/>
                <p:spacer width="4px" />
                <!--14-12-2020:TASK#2714-Sub Tables > Goal Group, Interest - Add Ids -->
                <p:commandButton id="btnNo" value="No" styleClass="ui-confirmdialog-no btn btn-danger btn-xs"  
                                 onclick="PF('confirmDialog').hide()"/>
            </div>
        </p:confirmDialog>
    </h:form>

    <style>
        .ui-panelgrid tr , .ui-panelgrid td{
            border: 0!important;
        }
    </style>

</ui:composition>


