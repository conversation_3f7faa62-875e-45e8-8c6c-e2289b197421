<ui:composition  xmlns="http://www.w3.org/1999/xhtml" 
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:f="http://java.sun.com/jsf/core" 
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:p="http://primefaces.org/ui" 
                 xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                 template="#{layoutMB.template}">

    <h:head>
    </h:head>


    <!--#358 Page Title sharvanim - 03-05-2020-->

    <ui:define name="head">


        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>

        <title>Sample Details</title>
    </ui:define>

    <ui:define name="metadata">  

        <f:metadata>       

            <f:viewParam id="recId" name="recId" value="#{samplesHdr.recId}"   />
            <f:viewParam id="oppId" name="oppId" value="#{samplesHdr.oppId}"   />
            <f:viewAction action="#{samplesHdr.initializeSamples()}" />  
            <!--#1293: Add Page Access Check - Samples, Customer Exceptions, Projects-->
            <f:event listener="#{samplesHdr.isPageAccessible}" type="preRenderView"/>

            <!--2520 - Related - CRM-1521: Tutorial button landing urls - Quotes, Samples, Opportunities, Jobs, Projects-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('SAMP_DTL'))}" />
            <!-- #4644: handling mandatory field from settings -->
            <f:viewAction action="#{fieldsMstService.load('CONT_DTL')}"/>
            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('SAMP_DTL')}" />

        </f:metadata>

    </ui:define>

    <ui:define name="title">
        <div class="row">
            <div class="col-md-6">
                <p:outputLabel  value="#{samplesHdr.recId == 0 ? 'New' : ''}"   />
                Sample 
                <p:outputLabel  value="#{samplesHdr.recId > 0 ? 'Details' : ''}"   />
            </div>
            <div class="col-md-6" align="right">

            </div>
            <ui:include src="/help/Help.xhtml"/>
        </div>
    </ui:define>

    <ui:define name="menu">
    </ui:define>

    <ui:define name="body">
        <style>

        </style>
        <div class="box box-info box-body">
            <h:form id="samplefrom">
                <p:remoteCommand name="applyActivePrinci" autoRun="false" immediate="true" actionListener="#{samplesHdr.updateFieldsOnSelection(viewCompLookupService.selectedCompany)}"/>
                <p:remoteCommand name="applyCont1"  autoRun="false" immediate="true" actionListener="#{samplesHdr.applyContact1(viewContLookupService.selectedContact)}" />
                <p:remoteCommand name="applyCont2"  autoRun="false" immediate="true" actionListener="#{samplesHdr.applyContact2(viewContLookupService.selectedContact)}"/>
                <p:remoteCommand name="applyCont3"  autoRun="false" immediate="true" actionListener="#{samplesHdr.applyContact3(viewContLookupService.selectedContact)}"/> 
                <p:remoteCommand name="applyContCEM"  autoRun="false" immediate="true" actionListener="#{samplesHdr.applyContCEM(viewContLookupService.selectedContact)}"/> 
               <!--<p:remoteCommand name="applyComp" actionListener="#{example.applyCompany(viewCompLookupService.selectedCompany)}" update=":samplefrom:oppSecCustomer" />-->
                <p:remoteCommand name="applyComp" actionListener="#{samplesHdr.applyCompany(viewCompLookupService.selectedCompany)}" update=":samplefrom:oppSecCustomer" />

                <div class="box-header with-border">
                    <div class="row">
                        <div class="col-md-12" >
                            <p:outputPanel id="btns" style="display: inline-table"  >
                                <!--                 #789900: GES Ticket Assigned - Quotations-shreejith added update::dtForm:OppInq:tblQuotDtls-->
                                <!--1999 CRM-3393: Avoid double click - CRM-->
                                <p:commandButton value="#{samplesHdr.recId > 0 and samplesHdr.editable? 'Edit' : 'Save'}"
                                                 id="btnSaveSampl"       
                                                 action="#{samplesHdr.saveOrUpdate(samplesHdr.recId,'SAMPLE')}" 
                                                 update=":samplefrom" 
                                                 class="btn btn-success btn-xs" 
                                                 widgetVar="smplSaveVar"
                                                 onclick="PF('smplSaveVar').disable();"  oncomplete="PF('smplSaveVar').enable();"  />

                                <p:spacer width="10"/>
                                <p:commandButton value="Cancel"  id="btnCnclSampl"
                                                 class="btn btn-warning btn-xs"
                                                 action="#{samplesHdr.cancelEdit()}" 
                                                 />
                                <p:spacer width="10"/>
                                <p:commandButton value="Delete"  id="btnDelSampl"
                                                 class="btn btn-danger btn-xs" 
                                                 onclick="PF('confirmation').show()"  

                                                 rendered="#{samplesHdr.recId > 0}" 
                                                 disabled="#{!samplesHdr.editable}" 
                                                 >

                                </p:commandButton> 
                                <p:spacer width="10"/>
                                <p:commandButton  class="btn btn-primary btn-xs"  value="Generate PDF"  id="btnPdfGenSampl"
                                                  rendered="#{samplesHdr.recId > 0}" 
                                                  onclick="PF('pdf').show()"
                                                  />
                                <p:spacer width="10"/>


                                <p:spacer width="10"/>
                                <!--                                <h:outputLabel value="Last update "  style="font-size: 0.795em;
                                                                               color: #6A6F73;font-weight: bold"  rendered="#{quotesHdr.recId > 0}"/>   
                                                                <h:outputLabel   value="#{params.convertFromUTC(quotesHdr.updDate)}" style="font-size: 0.795em;
                                                                                 color: #6A6F73;" rendered="#{quotesHdr.recId > 0}">-->
                                <!--                                    <f:convertDateTime pattern="#{globalParams.dateTimeFormat}"/>
                                                                </h:outputLabel>-->
                                <!--                                <h:outputLabel   value=" by #{quotesHdr.getUserById(quotesHdr.updUser)}" style="font-size: 0.795em;
                                                                                 color: #6A6F73;" rendered="#{quotesHdr.recId > 0}"/>-->

                                <!--#7606 ys-204 opportunity icon-->
                                <p:commandButton   
                                    rendered="#{samplesHdr.disableOpp and samplesHdr.recId > 0}"
                                    disabled="#{!samplesHdr.editable}"
                                    styleClass="openorViewOpp" 
                                    id="vwlnk"  title="View #{custom.labels.get('IDS_OPP')}"  
                                    onclick="window.open('../opportunity/OpportunityView.xhtml?opid=#{samplesHdr.oppId}', '_self');"
                                    />
                                <!--button_add_opp-->
                                <!--#7606 ys-204 opportunity icon-->
                                <p:commandButton    
                                    rendered="#{!samplesHdr.disableOpp and samplesHdr.recId > 0}"
                                    styleClass="linkorCreateOpp"
                                    id="lnk"
                                    disabled="#{!samplesHdr.editable}" 
                                    actionListener="#{samplesHdr.loadOppList('SAMPLE',samplesHdr.sampPrincipal,samplesHdr.sampCustomer,samplesHdr.sampDistri)}"
                                    update=":frmSmOppMst:dtSmOppMstList"
                                    oncomplete="PF('oppSmMstDlg').show()"
                                    title="Link to #{custom.labels.get('IDS_OPP')}"   />

                            </p:outputPanel>

                        </div>
                    </div>
                </div>
                <p:panelGrid id="gridQuote" styleClass="panlGrid"><!--

                       _______________start opportunities______________________  
                    -->                                    <div class="ui-g ui-fluid header-bar">
                        <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                            <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Primary Information</p:outputLabel>
                        </div>
                        <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                        </div>
                    </div>  <!--

                    -->                                    <div class="ui-g ui-fluid">

                        <div class="ui-sm-12 ui-md-2 ui-lg-2">
                            <p:outputLabel value="#{custom.labels.get('IDS_PRINCI')}" for="oppPrincipal"  styleClass="required"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                            <h:panelGroup class="ui-inputgroup"  >

                                <p:inputText id="oppPrincipal" value="#{samplesHdr.oppPrincipalName}" readonly="true"  required="true" requiredMessage="#{custom.labels.get('IDS_PRINCI')} is required"/>
                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" disabled="#{samplesHdr.disableOpp || samplesHdr.editable}"
                                                  actionListener="#{samplesHdr.listLookUpvalues('applyActivePrinci', 1)}"
                                                  update=":formCompLookup" tabindex="2" id="btnOppPrinciSrch"
                                                  styleClass="btn-info btn-xs" />
                            </h:panelGroup>

                        </div>
                        <div class="ui-sm-12 ui-md-2 ui-lg-2 "> 
                            <p:outputLabel value="#{custom.labels.get('IDS_PRINCI')} Contact" for="oppPrincipalContact" />
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="oppPrincipalContact" readonly="true"  value="#{samplesHdr.oppPrincipalContName}"  />
                                <!--                                PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                                <p:commandButton id="btnOppPrinciContSrch" icon="fa fa-search" title="Choose Contact" immediate="true" disabled="#{samplesHdr.editable}"

                                                 actionListener="#{viewContLookupService.list('applyCont2',1,samplesHdr.sampPrincipal,1)}" 
                                                 action="#{viewContLookupService.disableContNewBtn(samplesHdr.sampPrincipal)}"
                                                 update=":formContLookup" oncomplete="PF('lookupCont').show()"  
                                                 styleClass="btn-info btn-xs" />

                            </h:panelGroup>
                        </div>

                        <div class="ui-sm-12 ui-md-2 ui-lg-2">
                            <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')}" for="oppCustomer"  styleClass="required"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--<h:inputHidden id="hdninputcName" value="#{commTransactionService.quotesService.trnhCustName}"  required="true" requiredMessage="#{custom.labels.get('IDS_CUSTOMER')} is required" />-->
                                <p:inputText id="oppCustomer" readonly="true"  value="#{samplesHdr.oppCustomerName}" required="true" requiredMessage="#{custom.labels.get('IDS_CUSTOMER')} is required" />
                                <p:commandButton id="btnOppCustSrch" icon="fa fa-search" title="Choose Company" immediate="true" disabled="#{samplesHdr.disableOpp || samplesHdr.editable}" 
                                                 actionListener="#{samplesHdr.listLookUpvalues('applyActivePrinci', 5)}"
                                                 update=":formCompLookup" tabindex="4" 
                                                 styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                        </div>
                        <div class="ui-sm-12 ui-md-2 ui-lg-2 ">
                            <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')} Contact" for="oppCustomerContact"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="oppCustomerContact" readonly="true"   value="#{samplesHdr.oppCustomerContName}"   />
                                <!--                                PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                                <p:commandButton  id="btnOppCustContSrch" icon="fa fa-search" title="Choose Contact" immediate="true" disabled="#{samplesHdr.editable}"
                                                  actionListener="#{viewContLookupService.list('applyCont1',2,samplesHdr.sampCustomer,1)}" 
                                                  action="#{viewContLookupService.disableContNewBtn(samplesHdr.sampCustomer)}"
                                                  update=":formContLookup :formNew" oncomplete="PF('lookupCont').show()"  
                                                  styleClass="btn-info btn-xs" />

                            </h:panelGroup>
                        </div>


                        <div class="ui-sm-12 ui-md-2 ui-lg-2">
                            <p:outputLabel value="#{custom.labels.get('IDS_DISTRI')}" for="oppDistributor"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--<h:inputHidden id="hdninputDistrName" value="#{commTransactionService.transHdr.trnhDistriName}"   />-->
                                <p:inputText id="oppDistributor" readonly="true"  value="#{samplesHdr.oppDistriName}" />
                                <p:commandButton id="btnOppDistriSrch" icon="fa fa-search" title="Choose Company" immediate="true"  disabled="#{samplesHdr.disableOpp || samplesHdr.editable}"
                                                 actionListener="#{samplesHdr.listLookUpvalues('applyActivePrinci', 3)}"
                                                 update=":formCompLookup" tabindex="3" 
                                                 styleClass="btn-info  btn-xs" />
                            </h:panelGroup>
                        </div>
                        <div class="ui-sm-12 ui-md-2 ui-lg-2 ">
                            <p:outputLabel value="#{custom.labels.get('IDS_DISTRI')} Contact"  for="oppDistributorContact"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText id="oppDistributorContact" readonly="true"   value="#{samplesHdr.oppDistriContName}"   />
                                <!--                                PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                                <p:commandButton  id="btnOppDistriContSrch"  icon="fa fa-search" title="Choose Contact" immediate="true" disabled="#{samplesHdr.editable}"
                                                  actionListener="#{viewContLookupService.list('applyCont3',3,samplesHdr.sampDistri,1)}" 
                                                  action="#{viewContLookupService.disableContNewBtn(samplesHdr.sampDistri)}"
                                                  update=":formContLookup" oncomplete="PF('lookupCont').show()"  
                                                  styleClass="btn-info btn-xs" />

                            </h:panelGroup>
                        </div>

                        <div class="ui-sm-12 ui-md-2 ui-lg-2">
                            <p:outputLabel  value="CEM" for="oppSecCustomer"  styleClass=""/>

                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4"> 
                            <h:panelGroup class="ui-inputgroup"  >
                                <!--<h:inputHidden id="hdninputcName" value="#{commTransactionService.transHdr.trnhCustName}"  required="true" requiredMessage="#{custom.labels.get('IDS_CUSTOMER')} is required" />-->
                                <!--2935 - CRM-1789: Hawkins: change quotes label - sharvani - 16-01-2020-->
                                <p:inputText id="oppSecCustomer" readonly="true"   value="#{samplesHdr.sampCemName}"/>
<!--                                                <p:commandButton  icon="fa fa-search" title="Choose Company" immediate="true" disabled="#{samplesHdr.disableOpp || samplesHdr.editable}" 
                                                  actionListener="#{samplesHdr.listLookUpvalues('applyActivePrinci',0)}"
                                                  update=":formCompLookup" tabindex="4" 
                                                  styleClass="btn-info btn-xs" />-->
                                <p:commandButton id="btnSecCustSrch"  icon="fa fa-search" title="Choose Company" immediate="true" 
                                                 disabled="#{samplesHdr.editable}"
                                                 actionListener="#{viewCompLookupService.listAll('applyComp')}"
                                                 update=":formCompLookup" oncomplete="PF('lookupComp').show();"  
                                                 styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                        </div>
                        <div class="ui-sm-12 ui-md-2 ui-lg-2 ">
                            <p:outputLabel value="CEM Contact"  for="oppSecCustomerContact"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText   readonly="true"  id="oppSecCustomerContact"  value="#{samplesHdr.sampCemContName}"/>
                                <!--                                PMS 3831:Person Company - Opp Contact Lookup - Hide New: Action added-->
                                <p:commandButton id="btnOppSecCustContSrch"  icon="fa fa-search" title="Choose Contact" immediate="true" disabled="#{samplesHdr.editable}" 
                                                 actionListener="#{viewContLookupService.list('applyContCEM',0,samplesHdr.sampCem,0)}" 
                                                 action="#{viewContLookupService.disableContNewBtn(samplesHdr.sampCem)}"
                                                 update=":formContLookup" oncomplete="PF('lookupCont').show()"  
                                                 styleClass="btn-info btn-xs" />
                            </h:panelGroup>
                        </div>
                    </div><!--

                    -->                                    <div class="ui-g ui-fluid header-bar">
                        <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                            <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Transaction Details</p:outputLabel>
                        </div>
                        <div class="ui-sm-12 ui-md-6 ui-lg-6 ">
                        </div>
                    </div>  <!--
                    -->                                    <div class="ui-g ui-fluid"><!--
                        -->                                        <div class="ui-sm-12 ui-md-2 ui-lg-2">    
                            <p:outputLabel value="#{custom.labels.get('IDS_PROGRAM')}"/>
                        </div><!--
                        -->                                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">     
                            <p:inputText value="#{samplesHdr.sampCustProgram}" style="width: 100%"  maxlength="120" disabled="#{samplesHdr.disableOpp || samplesHdr.editable}" />
                        </div><!--
                        -->                                        <div class="ui-sm-12 ui-md-2 ui-lg-2">                            
                        </div><!--
                        -->                                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">                             
                        </div>
                        <!--                         //Feature #6127 CRM-4717: KIMINPROGRESS: make sample fields available in "custom labels"-->
                        <div class="ui-sm-12 ui-md-2 ui-lg-2">   
                            <p:outputLabel value="#{custom.labels.get('IDS_SAMP_NUM')}"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">   
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText value="#{samplesHdr.sampOrdNum}" id="quotNumb"  disabled="#{samplesHdr.editable}" maxlength="30" />

                            </h:panelGroup>
                        </div><!--
                        -->                                        <div class="ui-sm-12 ui-md-2 ui-lg-2">     
                            <!--Task #15135: PRIORITY CRM-8820   Samples- Make Order Date a Required Field-->
                            <p:outputLabel value="#{custom.labels.get('IDS_REQ_DATE')}" styleClass="required"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">     
                            <p:calendar value="#{samplesHdr.sampOrdDate}"   pattern="#{globalParams.dateFormat}"  disabled="#{samplesHdr.editable}" >
                                <!--                                                 Sharvani - 16/05/2019 - CRM-526: Auto-populate quote expiration date on quote creation 
                                                                                <p:ajax event="dateSelect" listener="#{samplesHdr.expdateUpdate()}" update=" " /> -->
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                            </p:calendar>
                        </div><!--

                        -->                                        <div class="ui-sm-12 ui-md-2 ui-lg-2">    
                            <p:outputLabel value="#{custom.labels.get('IDS_SHIP_REF_NUM')}"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 "> 
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputText value="#{samplesHdr.sampShipRefNum}" maxlength="30"  disabled="#{samplesHdr.editable}"/>

                            </h:panelGroup>
                        </div><!--
                        -->                                        <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                            <p:outputLabel value="#{custom.labels.get('IDS_SHIP_ACCT_NUM')}"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">    
                            <p:inputText value="#{samplesHdr.sampShipAccNum}"   maxlength="30" disabled="#{samplesHdr.editable}" />
                        </div><!--

                        -->                                        <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                            <p:outputLabel value="#{custom.labels.get('IDS_SHIP_VIA')}"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">   
                            <p:inputText value="#{samplesHdr.sampShipMethod}"  maxlength="30" disabled="#{samplesHdr.editable}"/>
                        </div><!--
                        -->                                        <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                            <p:outputLabel value="Ship To"/>
                        </div>
                        <!--#8994 ESCALATIONS CRM-6125   Samples - limited character entry-->
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">  
                            <p:inputText value="#{samplesHdr.sampShipTo}" maxlength="120" disabled="#{samplesHdr.editable}" >
                                <!--                                                <f:convertNumber pattern="#0.00" />-->
                            </p:inputText>
                        </div><!--

                        -->                                        <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                            <p:outputLabel value="#{custom.labels.get('IDS_SHIP_STATUS')}"  />
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">  

                            <p:selectOneMenu id="quotOpenStatus" value="#{samplesHdr.sampShipStatus}"  disabled="#{samplesHdr.editable}">
                                <f:selectItem itemLabel="Open" itemValue="0" />
                                <f:selectItem itemLabel="Closed" itemValue="1" />
                            </p:selectOneMenu>
                            <!--Jira Task #14346:  CRM-8361 : Samples: add Shipping Cost field   by harshithad on 23/08/2024-->
                        </div>
                        <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                            <p:outputLabel value="Shipping Cost"  />
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 "> 

                            <p:inputText value="#{samplesHdr.sampShipCost}" maxlength="12" disabled="#{samplesHdr.editable}">
                                <f:convertNumber pattern="#0.00" />
                            </p:inputText>
                        </div>
                        <!--
                        -->                                        <div class="ui-sm-12 ui-md-2 ui-lg-2">  
                            <p:outputLabel value="#{custom.labels.get('IDS_SAMP_APP')}"/>
                        </div>
                        <!--#8994 ESCALATIONS CRM-6125   Samples - limited character entry-->
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">   

                            <p:inputText value="#{samplesHdr.sampApplication}"  maxlength="60"  disabled="#{samplesHdr.editable}"/>
                        </div>
                        <!--Jira Task #14346:  CRM-8361 : Samples: add Shipping Cost field--> 
                        <div class="ui-sm-12 ui-md-2 ui-lg-2">   
                            <p:outputLabel value="Value"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">     
                            <p:inputText value="#{samplesHdr.sampValue}" maxlength="12" disabled="#{samplesHdr.disableOpp || samplesHdr.editable}"  >
                                <f:convertNumber pattern="#0.00" />
                            </p:inputText>
                        </div>
                        <!--Jira Task #14346:  CRM-8361 : Samples: add Shipping Cost field--> 
                        <div class="ui-sm-12 ui-md-2 ui-lg-2">          
                            <p:outputLabel value="#{custom.labels.get('IDS_SAMP_REF_NUM')}"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">   
                            <p:inputText value="#{samplesHdr.sampRefNumber}"  maxlength="30"  disabled="#{samplesHdr.editable}"/>
                        </div><!--
                        -->              

                        <div class="ui-sm-12 ui-md-2 ui-lg-2">    
                            <p:outputLabel value="#{custom.labels.get('IDS_SAMP_STATUS')}"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">  
                            <p:selectOneMenu value="#{samplesHdr.sampDelivStatus}"  disabled="#{samplesHdr.editable}">
                                <f:selectItem itemLabel="Requested" itemValue="1" />
                                <f:selectItem itemLabel="Shipped" itemValue="2" />   
                                <f:selectItem itemLabel="Received" itemValue="3" />
                                <f:selectItem itemLabel="Delivered" itemValue="4" />  
                                <f:selectItem itemLabel="In Review" itemValue="5" /> 
                            </p:selectOneMenu>
                        </div>

                        <div class="ui-sm-12 ui-md-2 ui-lg-2">   
                            <p:outputLabel value="Date Required"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 "> 
                            <p:calendar value="#{samplesHdr.sampReqDate}"   pattern="#{globalParams.dateFormat}"  disabled="#{samplesHdr.editable}">
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                            </p:calendar>                           
                        </div>
                        <div class="ui-sm-12 ui-md-2 ui-lg-2">   
                            <p:outputLabel value="Date Received"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 "> 
                            <!--Jira Task #14346:  CRM-8361 : Samples: add Shipping Cost field--> 
                            <p:calendar value="#{samplesHdr.sampRecvdDate}"   pattern="#{globalParams.dateFormat}"  disabled="#{samplesHdr.editable}">
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                            </p:calendar>                           
                        </div>
                        <div class="ui-sm-12 ui-md-2 ui-lg-2">    
                            <p:outputLabel value="Follow Up"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 "> 
                            <p:calendar value="#{samplesHdr.sampFollowUp}"   pattern="#{globalParams.dateFormat}"  disabled="#{samplesHdr.editable}">
                                <f:convertDateTime pattern="#{globalParams.dateFormat}" /> 
                            </p:calendar>
                        </div>
  <!--15207 : 30/12/2024 : CRM-8799   Samples: Samples should have an owner for follow up reminders-->
                        <div class="ui-sm-12 ui-md-2 ui-lg-2">
                                    <p:outputLabel value="Owner"/>
                                </div>
                         <div class="ui-sm-12 ui-md-4 ui-lg-4">  
                              <!--     15250 :2/01/2025: List screen: Add column "Sample owner " after Sales team column -->
                                    <p:selectOneMenu value="#{samplesHdr.sampOwner}" id="ddOppOwner" style="width: 100%"  disabled="#{samplesHdr.editable}">
                                          <!-- 15337:14/01/2025  CRM-8799 Sample Details: While creating new sample by default fill logged user name as sample owner -->   
                                        <!--  <f:selectItem itemLabel="Please select a owner" itemValue="0"  />-->                      
                                        <f:selectItems value="#{users.dependentUserListForQuotes}" var="user" 
                                                       itemValue="#{user.userId}" itemLabel="#{user.userName}" />
                                    </p:selectOneMenu>
                                </div>
  
   
                       <div class="ui-sm-12 ui-md-2 ui-lg-2">    
                            <p:outputLabel value="Notes"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">  
                            <h:panelGroup class="ui-inputgroup"  >
                                <p:inputTextarea rows="2"  value="#{samplesHdr.sampComments}" style=" text-overflow: scroll" autoResize="false" disabled="#{samplesHdr.editable}" />
                            </h:panelGroup>    
                        </div>
                        <div class="ui-sm-12 ui-md-2 ui-lg-2">       
                            <p:outputLabel value="#{custom.labels.get('IDS_STATUS')}"/>
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 "> 
                            <p:selectOneMenu value="#{samplesHdr.sampOpenStatus}" disabled="#{samplesHdr.editable}" >
                                <f:selectItem itemLabel="Open" itemValue="0" />
                                <f:selectItem itemLabel="Closed" itemValue="1" />
                            </p:selectOneMenu>
                        </div>
                      
                        <!--
                        
                                                <div class="ui-sm-12 ui-md-2 ui-lg-2"> 
                                                    <p:outputLabel value="Notes"/>
                                                </div>
                                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">   
                                                    <h:panelGroup class="ui-inputgroup"  >
                                                        <p:inputTextarea id="itquotComments" rows="2"  value="#{samplesHdr.quotComments}"  style=" text-overflow: scroll" autoResize="false" disabled="#{samplesHdr.editable}"   />
                                                          Sharvani - 07-05-2019: CRM-464 :copy principal comments 
                                                        <p:commandButton style="width:23px; "   icon="fa fa-copy" title="Copy #{custom.labels.get('IDS_PRINCI')} comments" actionListener="#{samplesHdr.getCompanyComments(samplesHdr.oppPrincipal)}" update=" " disabled="#{samplesHdr.editable or samplesHdr.oppPrincipal==0}"  class="btn-primary btn-xs" id="btnPrincComment"/>
                                                    </h:panelGroup>
                                                </div>
                                                <div class="ui-sm-12 ui-md-2 ui-lg-2">                            
                                                </div>
                                                <div class="ui-sm-12 ui-md-4 ui-lg-4 ">                             
                                                </div>
                        
                        --> 
                         <!--15207 : 30/12/2024 : CRM-8799   Samples: Samples should have an owner for follow up reminders-->
                        <div class="ui-sm-12 ui-md-2 ui-lg-2">   

                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 "> 

                        </div>
                        <!--
                        <div class="ui-sm-12 ui-md-2 ui-lg-2">                            
                        </div>
                        <div class="ui-sm-12 ui-md-4 ui-lg-4 ">                             
                        </div>

                        -->                                    </div><!--

                    -->                    <div class="ui-g ui-fluid header-bar">
                        <div class="ui-sm-12 ui-md-11 ui-lg-11 ">
                            <p:outputLabel styleClass="acct-name-hdr" class="sub_title">Line Item Details</p:outputLabel>
                        </div>
                        
                        <div class="ui-sm-12 ui-md-1 ui-lg-1 ">
                            <p:commandButton value="Add" 
                                             resetValues="true" 
                                             update=":SampleDtlsFrm"
                                             style="float: right" class="btn btn-primary btn-xs"
                                             actionListener="#{samplesDtl.clearSampDtls()}"
                                             disabled="#{samplesHdr.recId == 0 || !samplesHdr.editable}" oncomplete="PF('dlgSampleDtls').show()"  id="addDtls" />                
                        </div>
                    </div>  
                </p:panelGrid>
                <!--                <p:confirmDialog header="Confirm delete"  width="400" global="false"
                                                 message="Are you sure to delete?"
                                                 widgetVar="confirmation" >
                                    <div class="div-center">
                                        <p:commandButton value="Delete" 
                                                         class="btn btn-danger btn-xs"  />
                                        <p:spacer width="4"/>
                                        <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"    onclick="PF('confirmation').hide()" 
                                                         type="button"  />
                                    </div>
                                </p:confirmDialog>-->

                <p:dataTable id="tblQuotDtls" widgetVar="tblQuotDtls1" paginator="true" 
                             value="#{samplesHdr.listSampDtls}"
                             var="quot"     
                             rowKey="#{quot.recId}"
                             selection="#{samplesDtl.selectedDtl}" 
                             selectionMode="single" 
                             paginatorAlwaysVisible="false"
                             paginatorPosition="top"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rows="5"   class="tblmain"                         
                             draggableColumns="true">
                    <!--                                                        <p:ajax event="rowSelect"                            
                                                                                    listener="#{quotesDtl.onRowDtl}"
                                                                                    update=" "
                                                                                    oncomplete=" "
                                                                                    />-->
                    <p:ajax event="rowSelect"                            
                            listener="#{samplesDtl.onRowDtl}"

                            />

                    <p:column headerText="#{custom.labels.get('IDS_MFG_PART_NUM')}"  >
                        #{quot.sampItemPartManf}
                    </p:column>

                    <p:column headerText="Qty" style="text-align: right" id="qtyDId"  >
                        #{quot.sampItemQnty}
                    </p:column>


                    <p:column headerText="EAU" >
                        #{quot.sampEau}                                  
                    </p:column>

                    <p:column headerText="Prod Line">
                        #{quot.sampProdLine}
                    </p:column>
                    <!--                         //Feature #6127 CRM-4717: KIMINPROGRESS: make sample fields available in "custom labels"-->
                    <p:column headerText="#{custom.labels.get('IDS_SAMP_STATUS')}" >
                        #{quot.sampStatus}
                    </p:column>



                </p:dataTable>

                <p:confirmDialog header="Confirm delete"  width="400" global="false"
                                 message="Are you sure to delete?"
                                 widgetVar="confirmation" >
                    <div class="div-center">
                        <p:commandButton value="Delete" actionListener="#{samplesHdr.deleteInquiry()}"
                                         class="btn btn-danger btn-xs" process="@this" oncomplete="window.open('./List.xhtml', '_self')"     />
                        <p:spacer width="4"/>
                        <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"    onclick="PF('confirmation').hide()" 
                                         type="button"  />
                    </div>
                </p:confirmDialog>


                <p:confirmDialog header="PDF" global="false" message="Do you want download or send the generated pdf by email?" widgetVar="pdf" > 
                    <div align="center">
                        <p:outputPanel style="text-align: center">      
                            <p:commandButton value="Download" class="btn btn-primary btn-xs " action="#{samplesHdr.createPDF(1)}" ajax="false" oncomplete="PF('pdf').hide()" /> 
                            <p:spacer width="4"/>
                            <!--Bug #9823:ESCALATIONS CRM-6384  PDF Sample pulling up with an error code by harshithad on 30/11/22-->
                            <p:commandButton id="cmdBtnSendEmail" value="Send by Email" widgetVar="sendEmailBtn" onclick="PF('sendEmailBtn').disable()" action="#{samplesHdr.createPDF(0)}" class="btn btn-primary btn-xs " oncomplete="PF('sendEmailBtn').enable(); PF('pdf').hide(); window.open('#{globalParams.emailUrl}?fn=sendsamples&amp;reqid=#{loginBean.randomUid}&amp;autocheckemail=1&amp;sample_id=#{samplesHdr.recId}', '_blank')"  
                                             rendered="#{samplesHdr.recId!=0}"   /> 
                        </p:outputPanel>
                    </div>
                </p:confirmDialog>



            </h:form>

            <!--            <ui:include src="../../lookup/OppQuoteLookUp.xhtml" />
            -->                        <ui:include src="../../lookup/CompanyLookupDlg.xhtml"/>
            <ui:include src="../../lookup/ContactLookupDlg.xhtml"/>
            <ui:include src="../../lookup/PartNumDlg.xhtml" />
            <ui:include src="dialog/SampleDtls.xhtml" />
            <ui:include src="dialog/OppSampleLookUp.xhtml" />
            <!--<ui:include src="../../../lookup/PartNumDlg.xhtml"/>-->
            <style>
                /*#7606 ys-204 opportunity icon*/
                .ui-button.ui-widget.ui-state-default.ui-corner-all.ui-button-text-only {
                    display: inline-block;
                    margin-bottom: 0;
                    text-align: center;
                    white-space: normal;

                }

                lab{
                    display: block;
                    width : 50px;
                }

                label {
                    font-weight: normal !important; 
                }
                /*.ui-panelgrid .ui-widget .panlGrid { display: none !important;}*/
                .calendarClass1 input{
                    width: 85% !important;
                }
                .panlGrid tr, .panlGrid td{
                    border:0!important;
                    padding: 1px 3px;
                }
                #quotefrom\:gridQuote{
                    display: none !important;
                }
                .button\.ui-state-default {
                    background-image: initial !important;
                }
                .button_add_opp{
                    padding: 10px 0px !important;
                    text-decoration: none;
                    color:#000;
                    font-size: 12px;
                    border: none;
                    border-radius: 20px;
                    border:0px solid #888 !important;
                    background: url("#{request.contextPath}/resources/images/link-add.png") !important;
                }
                /*#7606 ys-204 opportunity icon*/
                .linkorCreateOpp{
                    padding: 17px 0px !important;

                }
                .openorViewOpp{
                    padding: 20px 0px !important;

                }
            </style>
        </div>
    </ui:define>
</ui:composition>