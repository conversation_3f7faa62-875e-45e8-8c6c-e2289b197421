<ui:composition  xmlns="http://www.w3.org/1999/xhtml" 
                 xmlns:h="http://java.sun.com/jsf/html"
                 xmlns:f="http://java.sun.com/jsf/core" 
                 xmlns:ui="http://java.sun.com/jsf/facelets"
                 xmlns:p="http://primefaces.org/ui" 
                 xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                 template="#{layoutMB.template}">

    <h:head>
    </h:head>

    <!--#358 Page Title sharvanim - 03-05-2020-->

    <ui:define name="head">


        <link rel="shortcut icon" type="image/x-icon" href="#{resource['favicon/favicon.ico']}"/>

        <title>Sample List</title>
    </ui:define>

    <ui:define name="metadata">  
        <f:metadata>            
            <f:viewAction action="#{oppService.defaultList()}"/>     
            <f:viewAction action="#{samplesHdr.populateHdrList()}"/>   
            <!--#1293: Add Page Access Check - Samples, Customer Exceptions, Projects-->
            <f:event listener="#{samplesHdr.isPageAccessible}" type="preRenderView"/>
            <!--2520 - Related - CRM-1521: Tutorial button landing urls - Quotes, Samples, Opportunities, Jobs, Projects-->
            <f:viewAction action="#{tutorialsMstService.loadTutorial(globalParams.tutorials.get('SAMP_LIST'))}" />
            <!--29-09-2023 12191 CRM-6510 LMS Learning links-->
            <f:viewAction action="#{helpService.setPageTag('SAMP_LIST')}" />

        </f:metadata>
        <style>

        </style>

    </ui:define>

    <ui:define name="title">
        <div class="row">
            <div class="col-md-6">
                Samples List
            </div>
            <ui:include src="/help/Help.xhtml"/>
            <div class="col-md-6" align="right">

            </div>
        </div>
    </ui:define>

    <ui:define name="menu">
    </ui:define>

    <ui:define name="body">
        <style>

        </style>
        <div class="box box-info box-body">
            <h:form id="sampleLstForm">
                <!--<h:inputHidden value="#{quotesHdr.dlgName}" id="dlgname"/>-->
                <h:panelGroup id="add">
                    <div class="button_bar">
                        <p:commandButton  id="btnNwSampl"  value="New" ajax="false" process="none" title="New"  action="/opploop/samples/Sample.xhtml?faces-redirect=true"  styleClass="btn btn-primary btn-xs" /> 
                        <p:spacer width="4" />
                        <!--CRM-3737: Samples > Export-->
                        <p:commandButton  id="btnSamplExp"  value="Export" oncomplete="PF('dlgSampleExp').show();"   actionListener="#{sampleExportService.resetValues()}"  update=":frmSampleExp:sampleExpDlg"  styleClass="btn btn-primary btn-xs" />

                        <p:selectOneRadio id="sumdtl" value="#{samplesHdr.summaryDetails}"  style="float: right;width: 30%">
                            <f:selectItem itemLabel="Summary" itemValue="0" />
                            <f:selectItem itemLabel="Detailed" itemValue="1" />
                            <p:ajax event="change" listener="#{samplesHdr.viewDtl(samplesHdr.summaryDetails)}" update=":sampleLstForm"  />
                        </p:selectOneRadio>

                    </div>
                </h:panelGroup>
                <p:spacer/>


                <!--                first-->

                <p:dataTable id="tblOpp" widgetVar="tblOpp1" paginator="true" rendered="#{samplesHdr.summaryDetails==0}"
                             value="#{samplesHdr.listEnquiry}"
                             var="enq"     
                             rowKey="#{enq.recId}"
                             selection="#{samplesHdr.selectedHdr}" 
                             selectionMode="single" 
                             filteredValue="#{samplesHdr.listFilteredEnq}"                          
                             paginatorAlwaysVisible="false"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rows="50"   class="tblmain"                         
                             draggableColumns="true">  

                    <!--        <p:ajax event="rowSelect"                            
                                    listener="#{enquiry.onRowSelect1}"
                                    update=":sampleHdr 
                                    :sampDtls
                                    :dtForm:OppInq:dlgeQuote                           
                                    :dtForm:OppInq:dlgeDesign
                                    :dtForm:OppInq:dlgeRegistAcc
                                    :dtForm:OppInq:dlgeRegist
                                    :dtForm:OppInq:tblQuotDtls"
                                    oncomplete="showDlg()"
                                    />
                    
                            <f:facet name="header">
                                <p:inputText style="display: none"   id="globalFilter" onkeyup="PF('tblOpp1').filter()" />
                            </f:facet>-->

                    <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{enq.oppCustomerName}" sortBy="#{enq.oppCustomerName}" id="custH">
                        <h:link outcome="Sample.xhtml?recId=#{enq.recId}"  value="#{enq.oppCustomerName}"  />
                        <!--#{enq.oppCustomerName}-->
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{enq.oppPrincipalName}" sortBy="#{enq.oppPrincipalName}" id="prinH">
                        <h:link outcome="Sample.xhtml?recId=#{enq.recId}"  value="#{enq.oppPrincipalName}"  />
                        <!--#{enq.oppPrincipalName}-->                                  
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_SALES_TEAM')}" filterBy="#{enq.smanName}" sortBy="#{enq.smanName}" id="sman">
                        <h:link outcome="Sample.xhtml?recId=#{enq.recId}"  value="#{enq.smanName}"  />
                        <!--#{enq.oppPrincipalName}-->                                  
                    </p:column>
                  <!--15250 :2/01/2025: List screen: Add column "Sample owner " after Sales team column--> 
                    <p:column headerText="Sample Owner" filterBy="#{enq.userName}" sortBy="#{enq.userName}" id="sowner">
                        <h:link outcome="Sample.xhtml?recId=#{enq.recId}"  value="#{enq.userName}"  />
                        <!--#{enq.userName}-->                                  
                    </p:column>
<!--        <p:column headerText="#{enquiry.colHeader[9-5]}" filterBy="#{enq.colValue[9-5]}" rendered="#{enquiry.dlgName == 'quot'}"
                  id="distriHId">
            <h:link outcome="Sample.xhtml?recId=#{samplesHdr.recId}"  value="#{samplesHdr.colValue[9-5]}"  />
                    #{enq.colValue[9-5]}           
                </p:column>-->

<!--        <p:column headerText="#{enquiry.colHeader[10-5]}" filterBy="#{enq.colValue[10-5]}" rendered="#{enquiry.dlgName == 'quot'}"
                  id="secCustHId">
            <h:link outcome="Sample.xhtml?recId=#{samplesHdr.recId}"  value="#{samplesHdr.colValue[10-5]}"  />
                    #{enq.colValue[10-5]}           
                </p:column>-->

                    <p:column headerText="#{custom.labels.get('IDS_PROGRAM')}" filterBy="#{enq.sampCustProgram}" sortBy="#{enq.sampCustProgram}" id="prog">
                        <h:link outcome="Sample.xhtml?recId=#{enq.recId}"  value="#{enq.sampCustProgram}"  />
                        <!--#{enq.oppCustProgram}-->                               
                    </p:column>
                                      
<!--        <p:column headerText="#{custom.labels.get('IDS_MFG_PART_NUM')}" filterBy="#{enq.oppItemPartManf}" 
                  rendered="#{enquiry.dlgName != 'quot' and enquiry.dlgName != 'samp'}">
                <h:link outcome="Sample.xhtml?recId=#{samplesHdr.recId}"  value="#{samplesHdr.oppI}"  />
                    #{enq.oppItemPartManf}
                </p:column>-->

<!--        <p:column headerText="#{enquiry.colHeader[8-5]}" filterBy="#{enq.colValue[8-5]}" rendered="#{enquiry.dlgName == 'quot'}"
                  style="text-align: right" id="valHId">
                    #{enq.colValue[8-5]}           
                </p:column>-->

                    <p:column headerText="#{samplesHdr.colHeader[5-5]}" filterBy="#{enq.colValue[5-5]}" sortBy="#{enq.colValue[5-5]}" >
                        <h:link outcome="Sample.xhtml?recId=#{enq.recId}"  value="#{enq.colValue[5-5]}"  />
                        <!--#{enq.colValue[5-5]}-->
                    </p:column> 

                    <p:column headerText="#{samplesHdr.colHeader[7-5]}" filterBy="#{enq.colValue[7-5]}" sortBy="#{enq.colValue[7-5]}" id="statH">
                        <h:link outcome="Sample.xhtml?recId=#{enq.recId}"  value="#{enq.colValue[7-5]}"  />
                        <!--#{enq.colValue[7-5]}-->          
                    </p:column>
                    <!--TASK#2530 Samples: Request Date- Filter issue -->
                    <p:column headerText="#{samplesHdr.colHeader[6-5]}" filterBy="#{enq.colValue[6-5]==null?'':samplesHdr.getFormattedDate(samplesHdr.convertDate(enq.colValue[6-5]), 'da')}" filterMatchMode="contains" sortBy="#{enq.colValue[6-5]}" >
                        <h:link outcome="Sample.xhtml?recId=#{enq.recId}"  value="#{enq.colValue[6-5]==null?'':samplesHdr.getFormattedDate(samplesHdr.convertDate(enq.colValue[6-5]), 'da')}"  />

                        <!--#{enq.colValue[6-5]}-->
                    </p:column>
                   <!--CRM:8802: TASK#15090 Samples: added the new column of Shipping cost -->   
                      <p:column headerText="Shipping Cost" filterBy="#{enq.sampShipCost}" sortBy="#{enq.sampShipCost}" id="sCost">
                        <h:link outcome="Sample.xhtml?recId=#{enq.recId}"  value="#{enq.sampShipCost}"  />                                                   
                    </p:column>

                </p:dataTable>






                <!--second                -->
                <p:dataTable id="tbldtl" widgetVar="tbldtl1" paginator="true"  rendered="#{samplesHdr.summaryDetails==1}"
                             value="#{samplesDtl.listEnquiry}"
                             var="enq" rowIndexVar="rowIndex"
                             rowKey="#{enq.recId}"
                             selection="#{samplesDtl.selectedDtl}" tableStyle="width:auto"
                             selectionMode="single"  styleClass="detailed"
                             filteredValue="#{samplesDtl.listFilteredEnq}"                          
                             paginatorAlwaysVisible="false"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rows="50"   class="tblmain"                         
                             draggableColumns="true">  

                    <p:ajax event="rowSelect"                            
                            listener="#{samplesDtl.onRowDtl}"

                            />

                    <!--                            <f:facet name="header">
                                                    <p:inputText style="display: none"   id="globalFilter" onkeyup="PF('tbldtl1').filter()" />
                                                </f:facet>-->

                    <p:column headerText="#{samplesDtl.colHeader[0]}" filterBy="#{enq.colValue[0]}"  sortBy="#{enq.colValue[0]}" >
                        <p:outputLabel value="#{enq.colValue[0]}" style="text-align: right"/>
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_MFG_PART_NUM')}" filterBy="#{enq.sampItemPartManf}" sortBy="#{enq.sampItemPartManf}" >
                        #{enq.sampItemPartManf}
                    </p:column>


                    <p:column headerText="Qty" filterBy="#{enq.sampItemQnty}" sortBy="#{enq.sampItemQnty}" id="qtyId">
                        #{enq.sampItemQnty}
                    </p:column>

                    <p:column headerText="EAU" filterBy="#{enq.sampEau}" sortBy="#{enq.sampEau}"  id="eauId" >
                        #{enq.sampEau}                                  
                    </p:column>

<!--                    <p:column headerText="#{enquiry.colHeader[4]}" filterBy="#{enq.colValue[4]}" style="text-align: right" id="mulId" 
                              rendered="#{enquiry.dlgName == 'quot'}">
                    #{enq.colValue[4]}
                </p:column>-->

                    <p:column headerText="#{custom.labels.get('IDS_CUSTOMER')}" filterBy="#{enq.oppCustomerName}" sortBy="#{enq.oppCustomerName}" id="custD">
                        #{enq.oppCustomerName}                               
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_PRINCI')}" filterBy="#{enq.oppPrincipalName}" sortBy="#{enq.oppPrincipalName}" id="prinD" >
                        #{enq.oppPrincipalName}                               
                    </p:column>

                    <p:column headerText="#{custom.labels.get('IDS_SALES_TEAM')}" filterBy="#{enq.smanName}" sortBy="#{enq.smanName}" id="salesman" >
                        #{enq.smanName}                               
                    </p:column>

                    <p:column headerText="#{samplesDtl.colHeader[5]}" filterBy="#{enq.colValue[5]}" sortBy="#{enq.colValue[5]}"  id="distriD">
                        #{enq.colValue[5]}
                    </p:column>

<!--                    <p:column headerText="#{enquiry.colHeader[6]}" filterBy="#{enq.colValue[6]}" id="secCustD" rendered="#{enquiry.dlgName == 'quot'}">
                    #{enq.colValue[6]}
                </p:column>-->
                    <!--TASK#2530 Samples: Request Date- Filter issue -->
                    <p:column headerText="#{samplesDtl.colHeader[1]}" filterBy="#{enq.colValue[1]==null?'':samplesHdr.getFormattedDate(samplesHdr.convertDate(enq.colValue[1]), 'da')}" filterMatchMode="contains" sortBy="#{enq.colValue[1]}" id="qdateD" >

                        #{enq.colValue[1]==null?'':samplesHdr.getFormattedDate(samplesHdr.convertDate(enq.colValue[1]), 'da')}
                    </p:column>               
                    <p:column headerText="#{samplesDtl.colHeader[3]}" filterBy="#{enq.colValue[3]}" sortBy="#{enq.colValue[3]}" id="valId">
                        #{enq.colValue[3]}
                    </p:column>     


                </p:dataTable>


            </h:form>
            <ui:include src="dialog/SampleDtls.xhtml" />
            <ui:include src="../../lookup/PartNumDlg.xhtml" />
            <!--<ui:include src="../../../lookup/PartNumDlg.xhtml"/>-->
        <!--2981:    CRM-3737: Samples > Export-->
                            <ui:include src="dialog/SampleExportDlg.xhtml" />  
            <style>

                label {
                    font-weight: normal !important; 
                }
                /*.ui-panelgrid .ui-widget .panlGrid { display: none !important;}*/
                .calendarClass1 input{
                    width: 85% !important;
                }
                .panlGrid tr, .panlGrid td{
                    border:0!important;
                    padding: 1px 3px;
                }
                #quotefrom\:gridQuote{
                    display: none !important;
                }
                .button\.ui-state-default {
                    background-image: initial !important;
                }
                .button_add_opp{
                    padding: 10px 0px !important;
                    text-decoration: none;
                    color:#000;
                    font-size: 12px;
                    border: none;
                    border-radius: 20px;
                    border:0px solid #888 !important;
                    background: url("#{request.contextPath}/resources/images/link-add.png") !important;
                }
            </style>

        </div>
    </ui:define>
</ui:composition>