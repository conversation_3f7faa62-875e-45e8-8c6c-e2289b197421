<?xml version="1.0" encoding="UTF-8"?>
<!--
2981:    CRM-3737: Samples > Export
-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui" 
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">
    <h:form id="frmSampleExp" >
        <p:dialog widgetVar="dlgSampleExp" header="Samples Export" modal="true"  width="350px"  id="sampleExpDlg" class="dialogCSS"  resizable="false">
            <p:growl id="msgs"  showDetail="false" showSummary="true"  />

            <p:panelGrid id="grid" columns="2"  columnClasses="ui-grid-col-5,ui-grid-col-7"
                         style="min-height: 50px"       layout="grid" styleClass="box-primary no-border ui-fluid ">

<!--                         //Feature #6127 CRM-4717: KIMINPROGRESS: make sample fields available in "custom labels"-->

                <p:outputLabel value="Filter type"  id="oLblBackLogGrpBy"/>
<!--     CRM:8802: TASK#15090 Samples: added the new column of "Shipping Cost" in the Export  file-->
                <p:selectOneMenu  value="#{sampleExpService.filterBy}" style="width: 90%;" id="source">
                    <!--                            <f:selectItem itemLabel="[SELECT]" itemValue="0" />-->



                    <f:selectItem itemLabel="Date  Required" itemValue="1" />
                    <f:selectItem itemLabel="#{custom.labels.get('IDS_REQ_DATE')}" itemValue="2" />
                    <f:selectItem itemLabel="Date Received" itemValue="3" />                  
                </p:selectOneMenu> 





                <p:outputLabel value="From"/>


                <p:calendar pattern="#{globalParams.dateFormat}" value="#{sampleExpService.sampleFromDate}" converterMessage="Please enter valid date" autocomplete="off">

                </p:calendar>





                <p:outputLabel value="To"/>


                <p:calendar pattern="#{globalParams.dateFormat}"  value="#{sampleExpService.sampleToDate}"  converterMessage="Please enter valid date"  autocomplete="off"   >

                </p:calendar>



            </p:panelGrid >
            <div   class="div-center">

                  
                
                <p:commandButton value="Export" actionListener="#{sampleExpService.populateSampleRecords()}" action="#{sampleExpService.resetValues()}"  ajax="false" onclick="PrimeFaces.monitorDownload(startdlg, stopdlg);
                                 " styleClass="btn btn-primary btn-xs" validateClient="true"  oncomplete="PF('inSampleProgressDlg').hide();" />
                <p:spacer width="4" />
                <p:commandButton value="Cancel" 
                                 process="@this"  oncomplete="PF('dlgSampleExp').hide();"  styleClass="btn btn-warning btn-xs"/>

            </div> 



        </p:dialog>
    </h:form>

    <p:dialog widgetVar="inSampleProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
        <h:form>
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif"   />
                <p:spacer width="5" />
                <p:outputLabel value="Please wait...Exporting records" />
                <br /><br />
                <p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />
            </p:outputPanel>
        </h:form>
    </p:dialog>
    <script type="text/javascript">
        function startdlg() {

            PF('inSampleProgressDlg').show();
        }
        function stopdlg() {

            PF('inSampleProgressDlg').hide();
        }
    </script>



</ui:composition>