
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui">
    <!--<h4 class="sub_title">#{custom.labels.get('IDS_OPPS')} Information</h4>-->

    <p:dialog id="dlgsamploppMst" widgetVar="oppSmMstDlg"  modal="true" header="#{custom.labels.get('IDS_OPPS')} List"
              maximizable="true" closeOnEscape="true">
        <h:form id="frmSmOppMst">
            <p:growl id="msg2" showDetail="false"/>

            <p:commandButton class="btn btn-primary btn-xs" action="#{samplesHdr.createOpp()}" value="Convert to #{custom.labels.get('IDS_OPP')}" oncomplete="PF('oppSmMstDlg').hide()" update=":samplefrom:btns"/>
            <br/> 
   
            <p:dataTable id="dtSmOppMstList" widgetVar="oppMstListTable" value="#{samplesHdr.oppsList}" var = "opp" selectionMode="single" rowKey="#{opp.oppId}" selection="#{viewOppList.selectopp}" 
                         emptyMessage="No open #{custom.labels.get('IDS_OPPS')} found." paginator="true" rows="10"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="5,10,15" paginatorAlwaysVisible="false" class="tblmain">
                <!--filteredValue="#{quotesHdr.filteredOppsList}"-->
                <p:ajax event="rowSelect" listener="#{viewOppList.onselectSmOpps}"  update="samplefrom samplefrom:vwlnk samplefrom:lnk" />
                <p:column filterBy="#{opp.oppCustomer}" filterMatchMode="contains" sortBy="#{opp.oppCustomer}" >
                    <f:facet name="header">#{custom.labels.get('IDS_CUSTOMER')}</f:facet> 
                    #{opp.custName}
                </p:column>

                <p:column filterBy="#{opp.oppPrincipal}" filterMatchMode="contains" sortBy="#{opp.oppPrincipal}">
                    <f:facet name="header">#{custom.labels.get('IDS_PRINCI')}</f:facet> 
                    #{opp.principalName}
                </p:column>

                <p:column filterBy="#{opp.oppDistri}" filterMatchMode="contains" sortBy="#{opp.oppDistri}">
                    <f:facet name="header">#{custom.labels.get('IDS_DISTRI')}</f:facet> 
                    #{opp.distriName}
                </p:column>


                <p:column filterBy="#{opp.oppCustProgram}" filterMatchMode="contains" sortBy="#{opp.oppCustProgram}">
                    <f:facet name="header">#{custom.labels.get('IDS_PROGRAM')}</f:facet> 
                        #{opp.oppCustProgram}
                </p:column> 

                <p:column filterBy="#{opp.oppActivity}" filterMatchMode="contains" sortBy="#{opp.oppActivity}">
                    <f:facet name="header">#{custom.labels.get('IDS_ACTIVITY')}</f:facet>  
                        #{opp.oppActivity}
                </p:column>

                <p:column filterBy="#{opp.oppStatus}" filterMatchMode="contains" sortBy="#{opp.oppStatus}">
                    <f:facet name="header">Status</f:facet>  
                        #{opp.oppStatus}
                </p:column>

                <p:column filterBy="#{opp.oppFollowUp}" filterMatchMode="contains" sortBy="#{opp.oppFollowUp}">
                    <f:facet name="header">#{custom.labels.get('IDS_FOLLOW_UP')}</f:facet> 
                    <p:outputLabel value="#{opp.oppFollowUp}" >
                        <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                    </p:outputLabel>
                </p:column>
                <!--Feature #5025 RE: Repfabric / AVX CRMSync / Relabeling Fields / "Next Step" (Pending)-->
<!--10-07-2023 11557 CRM-7126 Increase the Next Steps field in the Opportunities Module from 120 characters to 250 charac-->
                <p:column filterBy="#{opp.oppNextStep}" filterMatchMode="contains" sortBy="#{opp.oppNextStep}" styleClass="might-overflow">
                    <f:facet name="header">#{custom.labels.get('IDS_NEXT_STEP')}</f:facet> 
                        #{opp.oppNextStep}
                </p:column>
            </p:dataTable>
        </h:form>
    </p:dialog>



</ui:composition>


