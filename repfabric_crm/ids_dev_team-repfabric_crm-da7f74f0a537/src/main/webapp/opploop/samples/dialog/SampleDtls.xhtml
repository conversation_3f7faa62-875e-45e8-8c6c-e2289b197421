<?xml version="1.0" encoding="UTF-8"?>
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html">

    <p:dialog   id="dlgSampleDtls" widgetVar="dlgSampleDtls" resizable="false"
                modal="true" style="text-overflow: scroll" header="#{custom.labels.get('IDS_SAMPLES')} Details" responsive="true" styleClass="dialogCSS">
        <h:form id="SampleDtlsFrm">
            <p:remoteCommand name="applyPartNum" immediate="true" autoRun="false" actionListener="#{samplesDtl.updateFieldsOnPartNum(viewProductLookUpService.selectedProduct)}" process="@this"/>
            <!--<p:remoteCommand name="applyProd" />-->
            <f:facet name="header">
                <h:outputLabel id="hdrText" value="#{custom.labels.get('IDS_SAMPLES')} Details" />
            </f:facet>

            <p:panelGrid id="gridQuoteDtls" styleClass="panlGrid">

                <p:row id="btns1">
                    <p:column colspan="4"> 
                        <p:commandButton value="#{samplesDtl.recId > 0 ? 'Update' : 'Save'}" 
                                         actionListener="#{samplesDtl.saveOrUpdateDetails()}"
                                         class="btn btn-xs btn-success"
                                         />

                        <p:spacer width="10"/>
                        <p:commandButton value="Cancel"    onclick="PF('dlgSampleDtls').hide()" 
                                         type="button" class="btn btn-xs btn-warning"  />
                        <p:spacer width="10"/>
                        <p:commandButton value="Delete" onclick="PF('confirmation1').show()"   
                                         update=" " rendered="#{samplesDtl.recId > 0}" class="btn btn-xs btn-danger" >
                            <!--<p:confirm header="Confirmation" message="Are you sure to delete?" icon="ui-icon-alert" />-->
                        </p:commandButton>

                        <p:spacer width="10" />
    <!--                    <p:commandButton class="btn btn-xs btn-primary"  value="Clone" actionListener="#{quotesDtl.cloneQuoteDtl(quotesDtl)}" rendered="#{quotesDtl.recId > 0}"
                                         update=":quotefrom:dlgSampleDtls" >
    
                        </p:commandButton>-->

                    </p:column>
                    <!--                <p:column>
                                        <h:outputText value="created by "
                                    </p:column>-->
                </p:row>
                <!--   _______________start opportunities______________________  -->
                <p:row>
                    <p:column>
                        <p:outputLabel value="Manuf. Part No" styleClass="required"/>
                    </p:column>
                    <p:column>
                         <h:panelGroup class="ui-inputgroup"  >  
                        <p:inputText id="oppItemPartManf" value="#{samplesDtl.sampItemPartManf}" styleClass="partNumBox" maxlength="60"  style="width: 85%"  tabindex="1">
                            <p:ajax process="@this" event="change" listener="#{samplesDtl.updateFieldsOnPartNumValue(samplesDtl.sampItemPartManf)}"  />
                        </p:inputText>
                        <p:commandButton  icon="fa fa-search" title="Choose #{custom.labels.get('IDS_PART_NUM')}" immediate="true" tabindex="2"
                                          actionListener="#{viewProductLookUpService.list('applyPartNum', samplesHdr.sampPrincipal)}"
                                          oncomplete="PF('dlgPartNum').show();" 
                                          update=":dtPartNumForm" 
                                          styleClass="btn-info btn-xs" />
                         </h:panelGroup>
                    </p:column>

                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_CUSTOMER')} Part No"/>
                    </p:column>
                    <p:column>
                        <p:inputText value="#{samplesDtl.sampItemPartCust}" id="oppItemPartCust" maxlength="60"  style="width: 97%"  />
                    </p:column>
                </p:row>

                <p:row>


                    <p:column>
                        <p:outputLabel value="Qty"/>
                    </p:column>
                    <p:column>
                        <!--#601237: thorsonrm - increase qty decimals: changed quantity length to 12-->
<!--                        <p:inputText value="#{samplesDtl.sampItemQnty}" id="oppItemQnty" style="width: 97%" maxlength="12" converterMessage="Must be a signed decimal number." >
                            <p:ajax event="keyup" listener="#{quotesDtl.calculateExtPrice(quotesDtl.oppItemQnty, quotesDtl.quotResale)}" 
                                    update=" " />
                        </p:inputText>-->
<!--                        //#3187 :Sample-> line item dialog :Validation
                        //Seema - 08/02/2020-->
                        <p:inputNumber value="#{samplesDtl.sampItemQnty}" id="oppItemQnty" style="width: 97%" maxlength="12" 
                                       maxValue="999999999"
                                       converterMessage="Must be a signed decimal number." decimalPlaces="3">      
                        </p:inputNumber>    
                    </p:column>

                    <p:column>
                        <p:outputLabel value="EAU"/>
                    </p:column>
                    <p:column>
<!--                        <p:inputText value="#{samplesDtl.sampEau}" id="oppEau" style="width: 97%" maxlength="9" immediate="true"
                                     converterMessage="Must be a number consisting of one or more digits." />-->
                        <!--                        //#3187 :Sample-> line item dialog :Validation
                        //Seema - 08/02/2020-->
                        <p:inputNumber value="#{samplesDtl.sampEau}" id="oppEau" style="width: 97%" maxlength="9" 
                                       maxValue="999999999"
                                       immediate="true" decimalPlaces="0"
                                       converterMessage="Must be a number consisting of one or more digits." >
                        </p:inputNumber>
                    </p:column>

                </p:row>
                <!--                         //Feature #6127 CRM-4717: KIMINPROGRESS: make sample fields available in "custom labels"-->

                <p:row>
                    <p:column>
                        <p:outputLabel value="#{custom.labels.get('IDS_SAMP_STATUS')}"/>
                    </p:column>
                    <p:column>
    <!--                    <p:inputText value="#{quotesDtl.oppItemCost}" id="oppItemCost" style="width: 97%" maxlength="12" styleClass="partNumBox" converterMessage="Must be a signed decimal number." >
                            Bug 1045 : #982069 :unit price decimals messing up report
                            <f:convertNumber minFractionDigits="3" maxFractionDigits="6"/>
                        </p:inputText>-->
                        <p:selectOneMenu value="#{samplesDtl.sampDelivStatus}" >
                            <f:selectItem itemLabel="Requested" itemValue="1" />
                            <f:selectItem itemLabel="Shipped" itemValue="2" />   
                            <f:selectItem itemLabel="Received" itemValue="3" />
                            <f:selectItem itemLabel="Delivered" itemValue="4" />  
                            <f:selectItem itemLabel="In Review" itemValue="5" /> 
                        </p:selectOneMenu>
                    </p:column>


                    <p:column>
                        <p:outputLabel value="Prod Line"/>
                    </p:column>
                    <p:column>
                        <p:inputText id="prodLine" value="#{samplesDtl.sampProdLine}" style="width: 97%"  maxlength="40" styleClass="partNumBox"  />
                    </p:column>
                </p:row>

                <!--Shreejith Bug 462 2018-06-21-->
                <!--            <p:row>
                                <p:column>
                                   <p:outputLabel value="Part Description"/>
                                </p:column>
                                <p:column>
                                           //    Bug 1266 -#193088 :EPIC/GOLDEN INSTANCE:part number UI
                                           <p:inputTextarea rows="2" value="#{samplesDtl.sampItemPartDesc}" style="width: 99%;text-overflow: scroll" styleClass="partNumBox" autoResize="false" maxlength="200">
                                          <p:ajax event="keyup" listener="#{transDtl.checkvalidate(enquiry.sampDtl.oppItemPartDesc,1)}"  update=":sampleHdr :dtForm:OppInq:tbldtl :dtForm:growl" />
                                        </p:inputTextarea>
                                </p:column>
                
                                <p:column>
                                    <p:outputLabel value="Multiplier:"/>
                                </p:column>
                                <p:column>
                                    <p:inputText value="#{quotesDtl.quotMultiplier}" id="quotMultiplier" style="width: 97%" maxlength="20" converterMessage="Must be a signed decimal number." >
                                        <p:ajax event="keyup" 
                                                listener="#{quotesDtl.calculateUnitPrice(quotesDtl.quotStdRate, quotesDtl.quotMultiplier, quotesDtl.oppItemQnty)}" 
                                                update=" " />
                                    </p:inputText>
                                </p:column>
                            </p:row>
                
                            <p:row>
                                <p:column>
                                    #586840
                                                        <p:outputLabel value="Unit Price:"/>
                                                        Seema - 22/04/2019
                                                        CRM 315:Gentsch :change quote labels
                                    <p:outputLabel value="#{custom.labels.get('IDS_UNIT_PRICE')}"/>
                
                                </p:column>
                                <p:column>
                                    <p:inputText value="#{quotesDtl.quotResale}"  style="width: 97%" id='unit' styleClass="partNumBox"  maxlength="12" converterMessage="Must be a signed decimal number." disabled="true">
                                        Bug 1045 : #982069 :unit price decimals messing up report
                                        <f:convertNumber minFractionDigits="3" maxFractionDigits="6"/>
                                    </p:inputText>
                                </p:column>
                
                                <p:column>
                                    <p:outputLabel value="Extended Price"/>
                                </p:column>
                                <p:column>
                                    <p:inputText value="#{quotesDtl.quotExtPrice}" id='extPrice' styleClass="partNumBox" style="width: 97%"  maxlength="60" disabled="true" >
                                    </p:inputText>
                
                                </p:column>
                            </p:row>
                
                            <p:row>
                                <p:column>
                                    <p:outputLabel value="Lead Time"/>
                                </p:column>
                                <p:column>
                                    <p:inputText value="#{quotesDtl.quotLeadTime}" style="width: 97%" maxlength="20" converterMessage="Must be a signed decimal number." styleClass="partNumBox"/>
                                </p:column>
                
                                <p:column>
                                    <p:outputLabel value="Prod Line"/>
                                </p:column>
                                <p:column>
                                    <p:inputText value="#{quotesDtl.quotProdLine}" style="width: 97%"  maxlength="30" styleClass="partNumBox" />
                                </p:column>
                            </p:row>-->


                <p:row>
                    <p:column>
                        <p:outputLabel value="Part Description"/>
                    </p:column>
                    <p:column colspan="3">
                         <!--#9319   CRMSYNC-56  STMicro Opportunity : specific p/n  Samples, Products-->
                        <!--//    Bug 1266 -#193088 :EPIC/GOLDEN INSTANCE:part number UI-->
                        <p:inputTextarea    id="oppItemPartDesc" value="#{samplesDtl.sampItemPartDesc}" style="width: 99%"  styleClass="partNumBox"  rows="4" autoResize="false"  maxlength="350">
                         
                           
                        </p:inputTextarea>
                    </p:column>
                </p:row>



            </p:panelGrid>

            <p:confirmDialog header="Confirm delete"  width="400" global="true"
                             message="Are you sure to delete?"
                             widgetVar="confirmation1" >
                <div class="div-center">
                    <p:commandButton value="Delete" action="#{samplesDtl.deleteSampDtls()}"
                                     class="btn btn-danger btn-xs" process="@this"   onclick="PF('confirmation1').hide();
                                             PF('dlgSampleDtls').hide();"   />
                    <p:spacer width="4"/>
                    <p:commandButton value="Cancel"  class="btn btn-warning btn-xs"    onclick="PF('confirmation1').hide()" 
                                     type="button"  />
                </div>
            </p:confirmDialog>
        </h:form>
    </p:dialog>
    <!--<ui:include src="../../../lookup/PartNumDlg.xhtml"/>-->

    <style>
        .pnl_cmndButton .ui-panelgrid .ui-panelgrid-cell {
            border:0!important;
            padding: 1px 3px;
            border-width: 1px;
            border-style: solid;
            border-color: inherit;
            padding: 4px 10px;
            width: 220px;
            width: 2;
        }
        .panlGrid tr, .panlGrid td{
            border:0!important;
            padding: 1px 3px;
        }
    </style>
</ui:composition>