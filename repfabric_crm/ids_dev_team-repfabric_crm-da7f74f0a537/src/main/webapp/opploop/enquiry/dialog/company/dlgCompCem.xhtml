<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui">


    <p:dialog     header="CEM Lookup List" widgetVar="dlgCem" 
                  id="dlgCem"
                  closeOnEscape="true"
                  modal="true">

        <p:dataTable  widgetVar="wdDlgCem" styleClass="cemupdate"
                      value="#{viewCompanyLookUp.companyList}" 
                      filteredValue="#{viewCompanyLookUp.filteredCompanyList}"
                      var="com"                            
                      paginator="true" 
                      paginatorPosition="top"  
                      rows="15" id="tblCem" >

            <p:column filterMatchMode="contains" filterBy="#{com.compName}"  headerText="CEM" >
                <p:commandLink process="@this"  value="#{com.compName}"
                               onclick="PF('dlgCem').hide()" 
                               update="@(.updateCustDlg)"
                               actionListener="#{viewCompanyLookUp.selectCompany(5,com.compId,com.compName,com.compSmanId, com.compType)}"/>

            </p:column>
            <p:column headerText="Address">
                <h:outputLabel value="#{com.compAddress1}" />
            </p:column>
            <p:column headerText="City">
                <h:outputLabel value="#{com.compCity}" />
            </p:column>
            <p:column headerText="Phone">
                <h:outputLabel value="#{com.compPhone1}" />
            </p:column>
            <p:column headerText="Region">
                <h:outputLabel value="#{com.compRegion}" />
            </p:column>
        </p:dataTable>

<!--        <p:commandButton icon="ui-icon-plus"  value="New Company" process="@this"  
                         onclick="PF('dlgCust').hide();" 
                         action="#{oppService.showCompCreateDlg(2)}" 
                         oncomplete="PF('dlgCompCreate').show();"               
                         class="button_top btn_tabs"  />-->

    </p:dialog>
</ui:composition>

