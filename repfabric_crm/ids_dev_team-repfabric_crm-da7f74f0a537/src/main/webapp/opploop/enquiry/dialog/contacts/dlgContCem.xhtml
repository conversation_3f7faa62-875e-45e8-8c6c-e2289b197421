<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui">

    <p:dialog     header="CEM Contact Lookup List" id="dlgContCem" widgetVar="dlgContCem"                   
                  closeOnEscape="true" position="200,50" maximizable="true" 
                  modal="true">
        <p:dataTable  id="dtCem"   value="#{viewContactList.listContLookup}" styleClass="cemContupdate"  
                      filteredValue="#{viewContactList.listContLookupFiltered}"  
                      var="con" 
                      paginatorAlwaysVisible="=false"
                      paginatorPosition="top"
                      paginator="true"  
                      rows="15" 
                      widgetVar="dtCem" >

            <p:column  filterMatchMode="contains"  filterBy="#{con.contFullName}" headerText="Full Name" >
                <p:commandLink value="#{con.contFullName}" 
                               process="@this"
                               onclick="PF('dlgContCem').hide();" 
                               update="@(.updateCustDlg)"             
                               oncomplete="PF('dtCem').clearFilters()"
                               actionListener="#{viewContactList.selectPrimaryContact(5, con.contId, con.contFullName, con.contCompany, con.contType)}" >
                </p:commandLink>

            </p:column>
            <p:column headerText="Company" filterMatchMode="contains"  filterBy="#{con.contCompany}">
                <h:outputLabel value="#{con.contCompany}" />

            </p:column>
            <p:column headerText="#{custom.labels.get('IDS_JOB_TITLE')}" filterMatchMode="contains"  filterBy="#{con.contJobTitle}">
                <h:outputLabel value="#{con.contJobTitle}" />

            </p:column>
            <p:column headerText="Email" filterMatchMode="contains"  filterBy="#{con.contEmailBusiness}">
                <h:outputLabel value="#{con.contEmailBusiness}" />
            </p:column>

            <p:column headerText="Phone" filterMatchMode="contains"  filterBy="#{con.contPhoneBusi}"> 
                <h:outputLabel  value="#{con.contPhoneBusi}" />
            </p:column>
        </p:dataTable>


<!--        <p:commandButton icon="ui-icon-plus"  
                         value="New Contact" 
                         onclick="PF('dlgContCust').hide();" 
                         process="@this" 
                         actionListener="#{oppService.initContact(2)}" 
                         oncomplete="PF('dlgContCreate').show();loadComp();"
                         update=":quickContForm"
                         class="button_top btn_tabs"  />-->

    </p:dialog>

</ui:composition>

