<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <p:dialog header="Saved Filters" widgetVar="dlgFilterExportDlg"  
              modal="true" dynamic="true" width="500" onShow="PF('tblFilLst').clearFilters();" >
        <h:form id="dtexprtFilFrm"> 
        <p:commandButton value="Load" action="#{contactExportFilterService.load()}" 
        update=":exportOppForm :exportOppForm:pnlGrdMoreOptions :exportOppForm:fieldTbl" styleClass=" btn btn-success btn-xs"  ></p:commandButton>
        <p:spacer  width="4" />
        <p:commandButton value="Delete" action="#{contactExportFilterService.delFilter()}" styleClass="btn btn-danger btn-xs"/>
            <p:dataTable var="lst" value="#{contactExportFilterService.contExprtFiltrList}" paginator="true"  rows="8"
                         rowKey="#{lst.recId}" selection="#{contactExportFilterService.selectedFilter}" widgetVar="tblFilLst" rowSelectMode="single">
               
                <p:column selectionMode="single" style="width: 37px;text-align: center;height:35px"/>
                <p:column headerText="Filter Name" filterMatchMode="contains" filterBy="#{lst.filterName}" >
                    <h:outputText value="#{lst.filterName}"/>
                </p:column>
            </p:dataTable>
        </h:form>
    </p:dialog>
    
    <p:dialog header="Save Options" widgetVar="dlgFilterExportSaveDlg"  
              modal="true" dynamic="true" >
        <h:form id="dtexprtFilSaveFrm"> 
            Filter Name<p:inputText value="#{contactExportFilterService.contExprtFiltr.filterName}" maxlength="120" style="width:100%"
            required="true" requiredMessage="Please Enter the Filter Name"></p:inputText>
            <br/><br />
            <div class="cntr">
            <p:commandButton value="Save" action="#{contactExportFilterService.save()}" update=":exportOppForm :exportOppForm:pnlGrdMoreOptions" styleClass="btn btn-success btn-xs" > </p:commandButton>
            <p:spacer  width="4" />
            <p:commandButton value="Cancel" onclick="PF('dlgFilterExportSaveDlg').hide()" styleClass="btn btn-warning  btn-xs" ></p:commandButton>
            </div>
        </h:form>
    </p:dialog>
    <p:dialog header="Confirmation" widgetVar="dlgConmFil"  
              modal="true" dynamic="true" >
        <h:form id="dtexprtFilConFrm"> 
            <p:outputLabel value="Filter name already exists. Do you want to replace existing one? "></p:outputLabel>
            <br/><br />
            <div class="cntr">
            <p:commandButton value="Save" action="#{contactExportFilterService.update()}"  styleClass="btn btn-success btn-xs" > </p:commandButton>
            <p:spacer  width="4" />
            <p:commandButton value="Cancel" onclick="PF('dlgConmFil').hide()" styleClass="btn btn-warning  btn-xs" ></p:commandButton>
            </div>
        </h:form>
    </p:dialog>
    
    <p:dialog header="Confirmation" widgetVar="dlgConmDel"  
              modal="true" dynamic="true" >
        <h:form id="dtexprtFilConDel"> 
            <p:outputLabel value="Are you sure to delete filter? "></p:outputLabel>
            <br/><br />
            <div class="cntr">
            <p:commandButton value="Yes" action="#{contactExportFilterService.delete()}" update=":dtexprtFilFrm" oncomplete="PF('tblFilLst').filter();" styleClass="btn btn-success btn-xs" > </p:commandButton>
            <p:spacer  width="4" />
            <p:commandButton value="No" onclick="PF('dlgConmDel').hide()" styleClass="btn btn-warning  btn-xs" ></p:commandButton>
            </div>
        </h:form>
    </p:dialog>

</ui:composition>