<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: OppExportDlg.xhtml
// Author: Sarayu
//*********************************************************
* Copyright (c) 2017 Indea Design Systems Private Limited, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">

    <style>
        .disable-scroll .ui-dialog-content{
            overflow: hidden!important;
        }
        .ui-panelgrid td,.ui-panelgrid tr {
            border-style: none !important
        }

    </style>
    <!--focus=":exportOppForm:expType"-->
    <!--styleClass="disable-scroll"-->
    <!--                        Feature #6021:CRM-4835: Contact Export 'New' Contacts HarshithaD on 15/10-/2021-->
    <p:dialog  id="oppExportDlg" resizable="false"  width="1100" height="475"  class="dialogCSS" modal="true" 
               widgetVar="dlgOppExport"  visible="#{facesContext.validationFailed}"
               focus="exportOppForm:expType"
               style="text-overflow: scroll"  responsive="true" >  
        <f:facet name="header" >
            <!--#838: Contact Export > Option to save the selected filter-->
            <h:outputText id="dlgheader" value="#{exportService.moduleName} Export" />
        </f:facet>
        <h:form id="exportOppForm">
            <p:growl id="message" showDetail="false"/>

            <p:panelGrid id="pnlFilter" >
                <p:row>
                    <p:column>
                        <!--                        //PMS:2675
                                                //Seema - 11/12/2020-->
                        <p:outputLabel  value="File Type:" id="lblFileType"/>
                    </p:column>
                    <p:column>
                        <p:selectOneRadio id="expType" value="#{exportService.fileType}" required="true" requiredMessage="Select any file type">
                            <f:selectItem itemLabel="Excel" itemValue="1"/>
                            <f:selectItem itemLabel="CSV" itemValue="2"/>
                        </p:selectOneRadio>  
                    </p:column>
                    <p:column colspan="3" ></p:column>
                    <!--                    PMS 647-->
                    <p:column colspan="6" style="float: right;margin-left:10px" >
                        <!--3859-->
                        <!--                        Feature #6021:CRM-4835: Contact Export 'New' Contacts HarshithaD on 15/10-/2021-->
                        <p:commandButton  value="Export" id="btnOppExport" ajax="false"
                                          action="#{exportService.exportData()}"  oncomplete="PF('inProgressDlg').hide();"
                                          disabled="#{exportService.selectedExportFields == null}" 
                                          update="message" onclick="PrimeFaces.monitorDownload(start, stop);" 
                                          styleClass="btn btn-primary btn-xs" >                            
                           </p:commandButton>
                        <p:spacer width="4px" />
                        <!--                        //PMS:2675
                                              //Seema - 11/12/2020-->
                        <p:commandButton   value="Close" 
                                           oncomplete="PF('dlgOppExport').hide()" 
                                           id="btnExpClose"
                                           styleClass="btn btn-danger btn-xs"/>

                        <!--#102 Email  blast list.-->

                        <p:spacer width="4px" />
                        <!--                        //PMS:2675
                                              //Seema - 11/12/2020-->
                        <p:commandButton   value="Email Blast List"  
                                           id="btnExpEmailBlastLst"
                                           onclick="PF('inProceedDlg').show();"  styleClass="btn btn-primary btn-xs" 
                                           rendered="#{exportService.moduleId !='COMP'}"/>
                        <!--                        PMS 647-->
                        <p:spacer width="4px" />
                        <!--PMS 988-->
                        <p:commandButton id="autoklse"  value="Send to Autoklose" action="#{autokloseService.clear()}" rendered="#{exportService.autokloseFlag and exportService.moduleId !='COMP'}"
                                         oncomplete="PF('addAutokloseDlg').show()" styleClass="btn btn-primary btn-xs" update=":autoFrm:pnl"  />


                    </p:column>
                </p:row>
                <p:row>
                    <!--                        //PMS:2675
                                              //Seema - 11/12/2020-->
                    <p:column><p:outputLabel  value="Last Modified From:"   id="lblAutokloseModifiedFrm"/></p:column>
                    <p:column>
                        <!--                        Feature #6021:CRM-4835: Contact Export 'New' Contacts by HarshithaD 08/10/2021-->
                        <p:calendar id="fromDate"  pattern="#{globalParams.dateFormat}"   converterMessage="Please enter valid date" 
                                    size="10" value="#{exportService.startDate}"  autocomplete="off" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </p:calendar> 
                    </p:column>
                    <!--                        //PMS:2675
                                           //Seema - 11/12/2020-->
                    <p:column><p:outputLabel  value="Last Modified To:" id="lblAutokloseModifiedTo"/></p:column>
                    <p:column >
                        <!--                        Feature #6021:CRM-4835: Contact Export 'New' Contacts by HarshithaD 08/10/2021-->
                        <p:calendar id="toDate"  pattern="#{globalParams.dateFormat}"  converterMessage="Please enter valid date"
                                    size="10" value="#{exportService.endDate}" autocomplete="off" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </p:calendar>
                    </p:column>
                    <p:column colspan="2"></p:column>
                </p:row>
                <!--                Feature #6021:CRM-4835: Contact Export 'New' Contacts by HarshithaD 08/10/2021-->
                <p:row>
                    <p:column><p:outputLabel  value="Creation Date From:"   id="otptLblCreationDateFrom"/></p:column>
                    <p:column>
                        <!--                        Feature #6021:CRM-4835: Contact Export 'New' Contacts by HarshithaD 08/10/2021-->
                        <p:calendar id="fromCreatDate"  pattern="#{globalParams.dateFormat}"  converterMessage="Please enter valid date"  
                                    size="10" value="#{exportService.creationDateFrom}"  autocomplete="off" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </p:calendar> 
                    </p:column>

                    <p:column><p:outputLabel  value="Creation Date To:" id="otptLblCreationDateTo"/></p:column>
                    <p:column >
                        <!--                        Feature #6021:CRM-4835: Contact Export 'New' Contacts by HarshithaD 08/10/2021-->
                        <p:calendar id="toCreatDate"  pattern="#{globalParams.dateFormat}"  converterMessage="Please enter valid date"
                                    size="10" value="#{exportService.creationDateTo}" autocomplete="off" >
                            <f:convertDateTime pattern="#{globalParams.dateFormat}" />
                        </p:calendar>
                    </p:column>
                    <p:column colspan="2"></p:column>
                </p:row>
            </p:panelGrid>

            <!--02/03/2018 - Custom Export: More Filter Options--> 
            <!--                        //PMS:2675
                                              //Seema - 11/12/2020-->
            <p:outputPanel rendered="#{exportService.isMoreOptionsRequired()}" id="oPnlExpMoreOptions">
                <ui:include src="MoreFilterOptions.xhtml" />
            </p:outputPanel>

            <!--scrollable="true" scrollHeight="300"--> 
            <p:dataTable var="expField" value="#{exportService.exportFields}" id="fieldTbl" widgetVar="fieldTbl"
                         rowKey="#{expField.fieldId}" selection="#{exportService.selectedExportFields}" rowSelectMode="add">
                <p:ajax event="rowSelect" listener="#{exportService.onFilterCheckboxSelect}" update=":exportOppForm:btnOppExport"/>
                <p:ajax event="rowSelectCheckbox" listener="#{exportService.onFilterCheckboxSelect}" update=":exportOppForm:btnOppExport"/>
                <p:ajax event="rowUnselectCheckbox" listener="#{exportService.onFilterCheckboxSelect}" update=":exportOppForm:btnOppExport"/>
                <p:ajax event="toggleSelect" listener="#{exportService.onFilterCheckboxSelect}" update=":exportOppForm:btnOppExport"/>
                <p:column selectionMode="multiple" style="width: 37px;text-align: center;height:35px"/>
                <!--                        //PMS:2675
                                               //Seema - 11/12/2020-->
                <p:column headerText="Fields" id="clmFields">
                    <h:outputText value="#{expField.fieldDisplayName}" id="oTxtFieldDisplayName"/>
                </p:column>
                <f:facet name="footer">
                    <p:outputLabel  value="Note: (C) indicates #{custom.labels.get('IDS_CUSTOM_FIELDS')}." id="oLblExpNote"/>
                </f:facet>
            </p:dataTable>
            <br/>
        </h:form>
    </p:dialog>
    <p:dialog widgetVar="inProgressDlg" closable="false" modal="true" header="Message" resizable="false" >
        <h:form>
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif"   />
                <p:spacer width="5" />
                <p:outputLabel value="Please wait...Exporting records" />
                <br /><br />
                <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
            </p:outputPanel>
        </h:form>
    </p:dialog>
    <!--#102 Email  blast list. start-->
    <p:dialog widgetVar="inProgressDlgCsv" closable="false" modal="true" header="Message" resizable="false" >
        <h:form>
            <p:outputPanel >
                <br />
                <h:graphicImage  library="images" name="ajax-loader.gif"   />
                <p:spacer width="5" />
                <p:outputLabel value="Please wait...Exporting records" />
                <br /><br />
                <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
            </p:outputPanel>
        </h:form>
    </p:dialog>
    <!--    PMS 1117-->
    <!--                        //PMS:2675
                                                  //Seema - 11/12/2020-->
    <p:dialog widgetVar="autoProcessDlg" dynamic="true" transient="true" responsive="true" closable="false" id="dlgAutoProcess"
              modal="true" header="Message" resizable="false"  onShow="PF('poll').start();"  >
        <h:form id="prcAutoDlg">
            <p:growl id="error" />


<!--            <p:progressBar id="pbAyto"  widgetVar="pbAjaxAuto" displayOnly="false"  ajax="true" value="#{autokloseService.progress}"  labelTemplate="Processing Batch #{autokloseService.batchNumber} of #{autokloseService.totalBatch}" interval="10000" >
</p:progressBar>-->
            <!--            <p:outputPanel id="pnl"  >-->
            <br />
            <h:graphicImage  library="images" name="ajax-loader.gif"   />
            <p:spacer width="5" />
            <p:outputLabel id="lbl" value="Processing Batch #{autokloseService.batchNumber} of #{autokloseService.totalBatch}" />
            <br /><br />

            <!--                <p:poll id="poll" widgetVar="poll" interval="1"  update="prcAutoDlg:pnl"   />-->
            <!--            </p:outputPanel>-->
            <p:poll id="poll" widgetVar="poll" async="true" partialSubmit="true" listener="#{autokloseService.lstnrPoll}" immediate="true"  interval="1"  update="lbl"   />
        </h:form>
    </p:dialog>



    <!--#102 Email  blast list. start-->
    <!--                        //PMS:2675
                                              //Seema - 11/12/2020-->
    <p:dialog widgetVar="inProceedDlg" closable="false" modal="true" header="Confirmation" 
              id="dlgProceed"
              resizable="false" width="300" >
        <h:form id="frmProceed">
            <p:outputPanel id="oPnlProceed">
                <div  class="div-center">
                    <p:outputLabel value="All visible contacts will be exported" id="oLblProceedMsg" />
                </div>
                <br />
                <!--<p:poll stop="#{exportStatus.isExportComplete()}"  interval="10" widgetVar="pollStat" autoStart="false"  />-->
                <div  class="div-center">
                    <p:commandButton   value="Proceed"  
                                       onclick="PrimeFaces.monitorDownload(startcsv, stopcsv);" 
                                       ajax="false" action="#{exportService.exportEmailBlastListRows()}"
                                       id="btnProceedDlgProceeed"
                                       styleClass="btn btn-primary btn-xs" 
                                       oncomplete="PF('inProceedDlg').hide();"/> 
                    <p:spacer width="4"   />
                    <p:commandButton  value="Cancel"  
                                      oncomplete="PF('inProceedDlg').hide();" 
                                      id="btnProceedDlgCancel"
                                      styleClass="btn btn-warning btn-xs" /> 
                </div>
            </p:outputPanel>
        </h:form>
    </p:dialog>
    <!--    PMS 647-->
    <p:dialog id="autokloseDlg" widgetVar="addAutokloseDlg" styleClass="dialogCSS" header="Add to campaign" modal="true" width="630"  resizable="false" >
        <h:form id="autoFrm">
            <!--            <div class="cntr">-->
            <p:panelGrid id="pnl" columns="2">
                <!--                        //PMS:2675
                                                  //Seema - 11/12/2020-->
                <p:outputLabel value="Filtered Contacts Count:" id="olblFltrdContCount"/>
                <p:outputLabel value="#{autokloseService.recepientContList.size()}" id="olblFltrdContCount1"/>
                <!--            PMS 1067-->
                <!--            <p:outputLabel value="Selected Fields Count:"/>
                            <p:outputLabel value="#{autokloseService.selectedExportFields.size()}" />-->
                <!--                PMS 3447 Contacts: Send to Autoklose : Send as User-->
                <p:outputLabel value="Send As:" id="opSendAsUser"/>
                <p:selectOneMenu tabindex="11" id ="oneMenuAutokloseUser" value="#{autokloseService.autokloseUserId}" >
                    <f:selectItems var="usr" value="#{autokloseService.autokloseUser}" itemLabel="#{usr.userName}" itemValue="#{usr.userId}" />
                    <p:ajax event="change" listener="#{autokloseService.validateAutokloseUser()}" />
                </p:selectOneMenu>
                <p:outputLabel value="Campaign:" id="lblCampaign"/>
                <p:outputPanel>
                    <p:inputText id="campnName" value="#{autokloseService.selectedCampgn.campaignName}" disabled="true" style="width: 350px;"/>
                    <p:spacer width="4" />
                    <!--                PMS 968-->
                    <!--                    PMS 3447 Contacts: Send to Autoklose : Send as User-->
                    <p:commandButton  icon="fa fa-search" title="Choose campaign" 
                                      id="btnChooseCampaign" actionListener="#{autokloseService.setListAllCmpgn(false)}"
                                      action="#{autokloseService.fetchCampaignsList()}" 
                                      oncomplete="PF('addAutokloseLst').show();"  update=":camTable"
                                      styleClass="btn-info btn-xs" />
                </p:outputPanel>
                <!--            PMS 1559-->
                <p:outputLabel value="Campaign Recipient Count:" id="oLblRecpntCount"/>
                <p:outputLabel value="#{autokloseService.recipentCount}" id="oLblRecpntCount1"/>
                <p:outputPanel>
                    <!--                PMS 1117-->
                    <!--                     PMS 3338:CRM-3880: Autoklose: sending contacts from one id to a campaign created by another has terrible mess-->
                    <p:commandButton value="Send"  disabled="#{autokloseService.selectedCampgn.campaignId == 0}"
                                     id="btnAddAutoklose"
                                     action="#{autokloseService.addToRecipient(autokloseService.recepientContList)}" 
                                     onclick="PF('autoProcessDlg').show();" update=":prcAutoDlg" 
                                     styleClass="btn btn-success btn-xs" />
                    <p:spacer width="4" />
                    <p:commandButton value="Cancel" action="#{autokloseService.clear()}" 
                                     id="btnCancelAutoklose"
                                     oncomplete="PF('addAutokloseDlg').hide();" 
                                     styleClass="btn btn-warning  btn-xs" />
                </p:outputPanel>
                <p:outputPanel>
                    <p:commandButton value="More Settings" 
                                     id="btnCampaignMoreSettings"
                                     styleClass="btn btn-primary  btn-xs"  
                                     update="autoSettingFrm1 :autoSubscibeFrm1:outPnl :autoSubscibeFrm1" 
                                     action="#{autokloseService.prepareData()}"  
                                     oncomplete="PF('addAutokloseSettingDlgs').show();" 
                                     style="float: right"/>
                    <p:spacer width="4" style="float: right"/>
                    <!--1121: Autoklose > Export Logs-->
                    <p:commandButton value="Export Log" 
                                     id="btnCampaignExportLog"
                                     styleClass="btn btn-primary  btn-xs" 
                                     style="float: right" 
                                     onclick="PrimeFaces.monitorDownload(start, stop);" 
                                     actionListener="#{autokloseLog.getAutokeyExport(autokloseService.selectedCampgn.campaignId ,autokloseService.selectedCampgn.campaignName )}" ajax="false" disabled="#{autokloseService.selectedCampgn.campaignId == 0}"/>
                </p:outputPanel>
            </p:panelGrid>
            <!--        PMS 1559-->
            <br/>
            <!--            PMS 3551:CRM-3959: Autoklose: exported contacts not sending all contacts in filter to the campaign-->
            <p:outputLabel value="Note:" id="oLblCampaignNote"/>
            <p style="text-align: justify;">
                <ul style="margin-left: 30px;">
                    <li>
                        Autoklose will skip contacts with blank or duplicate email IDs and contacts which are marked as bounced/do not email in Autoklose contacts list.
                    </li>
                    <li>
                        <!--//21-06-2023 : #11485 : Internal - CRM-7035: Create test page-->
                        Make sure to keep the 'Exclude Do not Email' checkbox checked if you do not wish to send #{fabricClient.clientName} contacts tagged as "Do not Email" to Autoklose.
                    </li>
                </ul>
            </p>

            <!--            </div>-->
            <div >

            </div>
        </h:form>

    </p:dialog>
    <!--                        //PMS:2675
                                                 //Seema - 11/12/2020-->
    <p:dialog id="autokloseLstDlg" widgetVar="addAutokloseLst" header="Campaign" modal="true" width="600" resizable="false" 
              onShow="PF('campLst').clearFilters();"   >
        <h:form id="camTable">
            <!--            PMS 1447-->
            <p:growl id="message" />
            <p:dataTable id="campLst" widgetVar="campLst" value="#{autokloseService.campList}"  selection="#{autokloseService.selectedCampgn}" var="camp" 
                         rowKey="#{camp.campaignId}" selectionMode="single"
                         paginator="true"  rows="8" >
                <p:ajax event="rowSelect" listener="#{autokloseService.onRowSelect()}" oncomplete="PF('addAutokloseLst').hide();" update=":autoFrm"  />
                <p:column headerText="Name" filterMatchMode="contains" filterBy="#{camp.campaignName}" id="clmnCampaignName">
                    #{camp.campaignName}
                </p:column>
                <p:column headerText="Status" filterMatchMode="contains" filterBy="#{camp.campSstatus}"  id="clmnCampStatus">
                    #{camp.campSstatus}
                </p:column>
                <p:column headerText="Start Date" filterMatchMode="contains" filterBy="#{camp.startDate}" id="clmnCampSDate">
                    #{camp.startDate}
                </p:column>

            </p:dataTable>
        </h:form>

    </p:dialog>
    <!--#102 Email  blast list. END-->
    <ui:include src="/lookup/TypesLookup.xhtml"/>
    <ui:include src="/lookup/SalesTeamsLookup.xhtml"/>
    <ui:include src="/lookup/CategoriesLookup.xhtml"/>
    <ui:include src="/lookup/PotentialsLookup.xhtml"/>
    <ui:include src="/lookup/ContactGroupsLookup.xhtml"/>
    <ui:include src="/lookup/CompaniesLookup.xhtml"/>
    <ui:include src="/lookup/CompClassLookup.xhtml"/>
    <ui:include src="/lookup/BuyingFromLookup.xhtml"/>
    <ui:include src="/lookup/ProductIntrestLookUp.xhtml"/>
    <!--Feature #10929:CRM-6763   Contact list: Export contacts to autoklose for contacts with opp at stage = A or B or C e   by harshithad on 23/03/23-->
    <ui:include src="/lookup/OppActivityLookUp.xhtml"/>
    <ui:include src="CustomFilterDlg.xhtml"/>
    <!--//#2872:Task :CRM-1799: Fwd: Filter Companies Export-->
    <ui:include src="/lookup/CityLookup.xhtml"/>
    <!--#78 Company region lookup-->
    <ui:include src="/lookup/CompanyRegionLookUp.xhtml" />
    <!--#342 CRM-2446: manu-tek: INDUSTRIES needs to be exportable in COMPANY EXPORT -->
    <ui:include src="/lookup/BuyingGroupLookupDlg.xhtml"/>
    <ui:include src="/lookup/IndustriesLookupDlg.xhtml"/>
    <ui:include src="/crmsync/AutokloseSettingDlg.xhtml"/>
    <ui:include src="/crmsync/AutokloseActionDlg.xhtml" />
    <ui:include src="/crmsync/AutokloseActionAddDlg.xhtml" />
    <ui:include src="/crmsync/AkActionDlg.xhtml" />

    <!--    <ui:include src="../lookup/CompanyLookUp.xhtml"/>
        <ui:include src="../lookup/TypesLookUp.xhtml"/>
        <ui:include src="../lookup/SalesTeamLookUp.xhtml"/>
        <ui:include src="../lookup/CategoryLookUp.xhtml"/>
        <ui:include src="../lookup/PotentialLookUp.xhtml"/>
        <ui:include src="../lookup/ContactGroupLookUp.xhtml"/>-->
    <script type="text/javascript">

        function start() {
            PF('inProgressDlg').show();
        }
        function stop() {
            PF('inProgressDlg').hide();
        }
<!--#102 Email  blast list. start-->
        function startcsv() {
            PF('inProceedDlg').hide();
            PF('inProgressDlgCsv').show();
        }
        function stopcsv() {
            PF('inProgressDlgCsv').hide();

        }






    </script>
    <style>
        /*        PMS 647*/
        .cntr{
            text-align:center;
            padding: 9px;
        }
    </style>

</ui:composition>
