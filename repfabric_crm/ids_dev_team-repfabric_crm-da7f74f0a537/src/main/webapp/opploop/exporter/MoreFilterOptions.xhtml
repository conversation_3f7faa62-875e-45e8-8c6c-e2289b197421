<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--//**********************************************************
// Program: RepFabric Sys
// Filename: MoreFilterOptions.xhtml
// Author: priyadarshini
//*********************************************************
* Copyright (c) 2018 Indea Design Systems Private Limited, All Rights Reserved.
*/-->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    xmlns:p="http://primefaces.org/ui"
    xmlns:f="http://xmlns.jcp.org/jsf/core">


    <p:remoteCommand name="applyComps" actionListener="#{lookupService.applyCompanies(lookupService.companies)}" update="@(.sel-comp)" />
    <p:remoteCommand name="applyBuyingFrm" actionListener="#{lookupService.applyBuyingFrm(lookupService.principals)}" update="@(.sel-buying-from)" />


    <p:panel collapsed="false" id="toggleable" header="More Filter Options" toggleable="true"   widgetVar="pnlMoreOptions" toggleTitle="Show/Hide Options">

        <p:ajax event="toggle" />
        <f:facet name="actions">
            <!--#838: Contact Export > Option to save the selected filter-->
            <!--            //PMS:2675
                        //Seema - 14/12/2020-->
            <p:commandButton id="btnCustomFilter" value="Saved Filters" 
                             styleClass="btn-success btn-xs" 
                             rendered="#{exportService.moduleId =='CONT'}"
                             action="#{contactExportFilterService.fetchList()}" 
                             update=":dtexprtFilFrm" 
                             oncomplete="PF('dlgFilterExportDlg').show()" >
            </p:commandButton>
            <p:commandLink id="btnClrFilter" actionListener="#{lookupService.clearContactsFilters()}" 
                           update="pnlGrdMoreOptions"  styleClass="ui-panel-titlebar-icon ui-corner-all ui-state-default filter-statusJ"><h:outputText styleClass="ui-icon ui-icon-arrowreturnthick-1-w" title="Clear Filter Options" /></p:commandLink>
        </f:facet>
          <!--Feature #10929:CRM-6763   Contact list: Export contacts to autoklose for contacts with opp at stage = A or B or C e   by harshithad on 23/03/23-->
        <p:panelGrid id="pnlGrdMoreOptions" columns="4"  columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4">

            <!--#838: Contact Export > Option to save the selected filter-->
            <p:outputLabel value="Filter Name" 
                           id="oLblFilterName"
                           rendered="#{exportService.moduleId =='CONT'}"/>
            <p:inputText placeholder="Unnamed" value="#{contactExportFilterService.selectedFilter.filterName}" 
                         id="filterName" style="width:100%;" rendered="#{exportService.moduleId =='CONT'}" disabled="true" ></p:inputText>
            <p:commandButton id="btnSaveFilter"  value="Save"  
                             styleClass="btn-success btn-xs" rendered="#{exportService.moduleId =='CONT'}"
                             update=":dtexprtFilSaveFrm"  onclick="PF('dlgFilterExportSaveDlg').show()" />
            <p:outputLabel rendered="#{exportService.moduleId =='CONT'}" id="oLblExportCont"/> 

            <p:outputLabel value="#{custom.labels.get('IDS_COMP_TYPE')}" id="oLblExportCompType"/>
            <h:panelGroup class="ui-inputgroup"  >
                <h:selectOneListbox size="2" style="width: 100%"  styleClass="sel-type" id="selType"  >
                    <f:selectItems value="#{lookupService.companyTypes}" var="ctype"  
                                   itemValue="#{ctype.compTypeId}"
                                   itemLabel="#{ctype.compTypeName}"  />
                </h:selectOneListbox>
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs" id="btnExportCompType"
                                 update=":formTypeLookup" title="Select #{custom.labels.get('IDS_COMP_TYPE')}"
                                 actionListener="#{lookupService.list('COMP_TYPE')}" oncomplete="PF('lookupType').show();" />
                <p:commandLink styleClass="sel-type" value="Clear" id="cndLnkExportCompTypeClear"
                               actionListener="#{lookupService.clear('COMP_TYPE')}" 
                               update="@(.sel-type)" disabled="#{!(lookupService.companyTypes!=null and lookupService.companyTypes.size() != 0)}" />
            </h:panelGroup>
            <p:outputLabel value="#{custom.labels.get('IDS_SALES_TEAM')}" id="oLblSalesTeam"/>
            <h:panelGroup class="ui-inputgroup"  >
                <h:selectOneListbox styleClass="sel-salesteam"  size="2" style="width: 100%" id="selSalesTeam">
                    <f:selectItems value="#{lookupService.salesTeams}" var="team"  
                                   itemValue="#{team.smanId}"
                                   itemLabel="#{team.smanName}"  />
                </h:selectOneListbox>
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs" 
                                 id="btnExportSalesTeam"
                                 title="Select #{custom.labels.get('IDS_SALES_TEAM')}" 
                                 actionListener="#{lookupService.list('SALES_TEAM')}" 
                                 update=":frmSalesTeamLookup" 
                                 oncomplete="PF('dlgSalesTeamsLookup').show();" />
                <p:commandLink styleClass="sel-salesteam" value="Clear"
                               id="cmndLbkExportSalesTeamClear"
                               actionListener="#{lookupService.clear('SALES_TEAM')}"
                               update="@(.sel-salesteam)" 
                               disabled="#{!(lookupService.salesTeams!=null and lookupService.salesTeams.size() != 0)}" />
            </h:panelGroup>
            <p:outputLabel value="#{custom.labels.get('IDS_CATEGORY')}" id="oLblExportCategory"/>
            <h:panelGroup class="ui-inputgroup"  >
                <h:selectOneListbox  styleClass="sel-catg"  size="2" style="width: 100%" id="selCatg"     >
                    <f:selectItems value="#{lookupService.companyCategories}" var="catg"  
                                   itemValue="#{catg.compCatgId}"
                                   itemLabel="#{catg.compCatgName}"  />
                </h:selectOneListbox>
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  
                                 id="btnExportCategory"
                                 title="Select #{custom.labels.get('IDS_CATEGORY')}" 
                                 actionListener="#{lookupService.list('CATEGORY')}" 
                                 update=":frmCategoryLookup" oncomplete="PF('dlgCategoryLookup').show();"  />
                <p:commandLink styleClass="sel-catg" value="Clear" 
                               actionListener="#{lookupService.clear('CATEGORY')}"
                               id="cndLnkExportCategoryClear"
                               update="@(.sel-catg)" disabled="#{!(lookupService.companyCategories!=null and lookupService.companyCategories.size() != 0)}" />
            </h:panelGroup>

            <p:outputLabel value="#{custom.labels.get('IDS_POTENTIALS')}" id="oLblExportPotential"/>
            <h:panelGroup class="ui-inputgroup"  >
                <h:selectOneListbox  styleClass="sel-potential"  size="2" style="width: 100%" id="selPotential"     >
                    <f:selectItems value="#{lookupService.potentials}" var="potential"  
                                   itemValue="#{potential.prodPoteName}"
                                   itemLabel="#{potential.prodPoteName}"  />

                </h:selectOneListbox>
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  
                                 id="btnExportPotentials"
                                 title="Select #{custom.labels.get('IDS_POTENTIALS')}" 
                                 actionListener="#{lookupService.list('POTENTIAL')}" update=":frmPotentialLookup" 
                                 oncomplete="PF('dlgPotentialLookup').show();"  />
                <p:commandLink styleClass="sel-potential" value="Clear" 
                               id="cmndLnkExportPotentialsClear"
                               actionListener="#{lookupService.clear('POTENTIAL')}" 
                               update="@(.sel-potential)" disabled="#{!(lookupService.potentials!=null and lookupService.potentials.size() != 0)}" />
            </h:panelGroup>
            <p:outputLabel value="Companies"  rendered="#{exportService.moduleId !='COMP'}" id="oLblCompanies"/>
            <h:panelGroup class="ui-inputgroup" rendered="#{exportService.moduleId !='COMP'}" >
                <h:selectOneListbox  size="2" style="width: 100%!important;" styleClass="sel-comp" id="selComp"   >
                    <f:selectItems value="#{lookupService.companies}" var="comp"  
                                   itemValue="#{comp.compId}"
                                   itemLabel="#{comp.compName}"  />

                </h:selectOneListbox>
                <!--Task #1342:contact->export->buying from and companies filter refresh issue-->
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs" 
                                 title="Select Company" id="btnExportCompanies"
                                 actionListener="#{viewCompLookupService.listAll('applyComps')}" 
                                 oncomplete="PF('lookupComps').show(); PF('dlgCompLookup').filter();"
                                 update=":formCompsLookup" />
                <p:commandLink styleClass="sel-comp" value="Clear" actionListener="#{lookupService.clearComps()}" 
                               id="cmndLnkExportCompaniesClear"
                               update="@(.sel-comp)"  disabled="#{!(lookupService.companies!=null and lookupService.companies.size() != 0)}" />
            </h:panelGroup> 


            <p:outputLabel value="#{custom.labels.get('IDS_CON_GRP')}" rendered="#{exportService.moduleId !='COMP'}"  id="oLblExportContGrp"/>
            <h:panelGroup class="ui-inputgroup"  rendered="#{exportService.moduleId !='COMP'}">
                <h:selectOneListbox  styleClass="sel-group"  size="2" style="width: 100%" id="selGroup"     >
                    <f:selectItems value="#{lookupService.contactGroups}" var="grp"  
                                   itemValue="#{grp.contGroupId}"
                                   itemLabel="#{grp.contGroupName}"  />
                </h:selectOneListbox>
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  
                                 id="btnExportContGrp"
                                 title="Select #{custom.labels.get('IDS_CON_GRP')}" actionListener="#{lookupService.list('CONT_GRP')}" 
                                 update=":frmContGrpLookup" oncomplete="PF('dlgContGrpLookup').show();"  />
                <p:commandLink styleClass="sel-group" value="Clear" actionListener="#{lookupService.clear('CONT_GRP')}"
                               id="cmndLnkExportContGrpClear"
                               update="@(.sel-group)" 
                               disabled="#{!(lookupService.contactGroups!=null and lookupService.contactGroups.size() != 0)}" />
            </h:panelGroup>



            <p:outputLabel value="City" rendered="#{exportService.moduleId !='CONT'}" id="oLblContacts"/>
            <h:panelGroup class="ui-inputgroup" rendered="#{exportService.moduleId !='CONT'}" >
                <h:selectOneListbox   size="2" style="width: 100%" id="selCity" styleClass="sel-city"   >
                    <f:selectItems value="#{lookupService.cities}" var="cty"  
                                   itemValue="#{cty.cityId}"
                                   itemLabel="#{cty.cityName}"  />
                </h:selectOneListbox>
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs" 
                                 id="btnExportContact"
                                 title="City" actionListener="#{lookupService.list('CITY')}" update=":frmCityLookup" 
                                 oncomplete="PF('dlgCityLookup').show();"  />
                <p:commandLink styleClass="sel-city" value="Clear" actionListener="#{lookupService.clear('CITY')}" 
                               id="cmndLnkExportContactClear"
                               update="@(.sel-city)" disabled="#{!(lookupService.cities!=null and lookupService.cities.size() != 0)}" />
            </h:panelGroup>
            <!--//      Bug #8408  ESCALATION CRM-5880: Company Export > Add Region Filter-->
            <!--78 : Add Company region filter-->
            <p:outputLabel value="#{custom.labels.get('IDS_COMP_REGION')}" id="oLblExportRegion" />
            <h:panelGroup class="ui-inputgroup" >
                <h:selectOneListbox  styleClass="sel-region"  size="2" style="width: 100%" id="selRegion"     >
                    <f:selectItems value="#{lookupService.compregionList}" var="reg"  
                                   itemValue="#{reg.recId}"
                                   itemLabel="#{reg.compRegion}"  />
                </h:selectOneListbox>
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  
                                 id="btnExportRegion"
                                 title="Select #{custom.labels.get('IDS_COMP_REGION')}" actionListener="#{lookupService.list('REGION')}" 
                                 update=":frmCompRegionLookup" oncomplete="PF('dlgRegionLookup').show();"  />
                <p:commandLink styleClass="sel-region" value="Clear" 
                               actionListener="#{lookupService.clear('REGION')}" 
                               id="cmndLnkExportRegionClear"
                               update="@(.sel-region)" disabled="#{!(lookupService.compregionList != null and lookupService.compregionList.size() != 0)}" />
            </h:panelGroup>
            <!--             Bug #4524 product potentials and other not working in contact export relabel-->
            <!--#342 : CRM-2446: manu-tek: INDUSTRIES needs to be exportable in COMPANY EXPORT-->
            <p:outputLabel value="#{custom.labels.get('IDS_INDUSTRIES')}"  id="oLblExportIndustries" />
            <h:panelGroup class="ui-inputgroup"  >
                <h:selectOneListbox  styleClass="sel-industries"  size="2" style="width: 100%" id="selIndus"     >
                    <f:selectItems value="#{lookupService.compIndustryList}" var="indus"  
                                   itemValue="#{indus.recID}"
                                   itemLabel="#{indus.industryName}"  />
                </h:selectOneListbox>
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  
                                 id="btnExportIndustries"
                                 title="Select #{custom.labels.get('IDS_INDUSTRIES')}" actionListener="#{lookupService.list('INDUSTRY')}"
                                 update=":frmIndustriesLookup" oncomplete="PF('dlgIndustriesLookup').show();"  />
                <p:commandLink styleClass="sel-industries" value="Clear" actionListener="#{lookupService.clear('INDUSTRY')}" 
                               id="cmndLnkExportIndustriesClear"
                               update="@(.sel-industries)" disabled="#{!(lookupService.compIndustryList != null and lookupService.compIndustryList.size() != 0)}" />
            </h:panelGroup>
<!--            Feature #5471 Company Export: Buying Group-->
            <p:outputLabel value="Buying Group"  id="oLblExportBuyingGroup" />
            <h:panelGroup class="ui-inputgroup"  >
                <h:selectOneListbox styleClass="sel-buyinggroup"   size="2" style="width: 100%" id="selBuyingGrp"     >
                    <f:selectItems   value="#{lookupService.companyBuyingLst}" var="buygrp"  
                                   itemValue="#{buygrp.recId}"
                                   itemLabel="#{buygrp.buyGroup}"  />
                </h:selectOneListbox>
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  
                                 id="btnExportBuyingGroup"
                                 title="Select Buying Group" actionListener="#{lookupService.list('BUYING_GROUP')}"
                                 update=":frmBuyingGrpLookup" oncomplete="PF('dlgBuyingGrpLookup').show();"  />
                <p:commandLink styleClass="sel-buyinggroup" value="Clear" actionListener="#{lookupService.clear('BUYING_GROUP')}" 
                               id="cmndLnkExportBuyingGrpClear"
                               update="@(.sel-buyinggroup)" disabled="#{!(lookupService.companyBuyingLst != null and lookupService.companyBuyingLst.size() != 0)}" />
            </h:panelGroup>
            
            <!--#772: Contact Export - Add filters - Industries, Buying From, Class, Product Interest, Exclude Do not Email-->
            <!--Buying From - Show Principal Look up-->
            <p:outputLabel for="listBuyingFrom" value="Buying From" rendered="#{exportService.moduleId =='CONT'}" id="oLblExportBuyingfrm"  />
            <h:panelGroup class="ui-inputgroup"  rendered="#{exportService.moduleId =='CONT'}">
                <h:selectOneListbox id="listBuyingFrom" styleClass="sel-buying-from"  size="2" style="width: 100%"     >
                    <!--                    PMS 838-->
                    <f:selectItems value="#{lookupService.buyingFrmList}"  var="buy" 
                                   itemValue="#{buy.compId}"
                                   itemLabel="#{buy.compName}"/>
                </h:selectOneListbox>
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select Buying From" 
                                 actionListener="#{lookupService.list('BUYING_FROM')}" 
                                 id="btnExportBuyingFrm"
                                 update=":formBuyingFromLookup" oncomplete="PF('lookupBuyingFrm').show();"  />
                <p:commandLink styleClass="sel-buying-from" value="Clear" actionListener="#{lookupService.clear('BUYING_FROM')}" 
                               id="cmndLnkExportBuyingfrmClear"
                               update="@(.sel-buying-from)" 
                               disabled="#{!(lookupService.buyingFrmList != null and lookupService.buyingFrmList.size() != 0)}" />
            </h:panelGroup>

            <!--Company Class-->
            <!--             Bug #4524 product potentials and other not working in contact export relabel-->
            <p:outputLabel for="listClass" value="#{custom.labels.get('IDS_CLASS')}" rendered="#{exportService.moduleId =='CONT'}"  id="oLblExportClass"/>
            <h:panelGroup class="ui-inputgroup"  rendered="#{exportService.moduleId =='CONT'}">
                <h:selectOneListbox id="listClass" styleClass="sel-class"  size="2" style="width: 100%"     >
                    <f:selectItems value="#{lookupService.classList}" var="cls"
                                   itemLabel="#{cls.compClassName}"
                                   itemValue="#{cls.compClassId}"/>
                </h:selectOneListbox>
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select #{custom.labels.get('IDS_CLASS')}"
                                 actionListener="#{lookupService.list('COMP_CLASS')}" update="formClassLookup" 
                                 id="btnExportClass"
                                 oncomplete="PF('lookupClass').show()"  />
                <p:commandLink styleClass="sel-class" value="Clear" actionListener="#{lookupService.clear('COMP_CLASS')}" 
                               update="@(.sel-class)" 
                               id="cmndLnkExportClassClear"
                               disabled="#{!(lookupService.classList != null and lookupService.classList.size() != 0)}" />
            </h:panelGroup>

            <!--Product Interest-->
            <!--             Bug #4524 product potentials and other not working in contact export relabel-->
            <p:outputLabel for="listProdInterest" value="#{custom.labels.get('IDS_PROD_INTEREST')}" rendered="#{exportService.moduleId =='CONT'}"  id="oLblProdIntrst"/>
            <h:panelGroup class="ui-inputgroup"  rendered="#{exportService.moduleId =='CONT'}">
                <h:selectOneListbox id="listProdInterest" styleClass="sel-prod-interest"  size="2" style="width: 100%"     >
                    <f:selectItems value="#{lookupService.compProdIntrstList}" var="prdIntrst"
                                   itemLabel="#{prdIntrst.prodName}"
                                   itemValue="#{prdIntrst.recId}"/>
                </h:selectOneListbox>
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select  #{custom.labels.get('IDS_PROD_INTEREST')}" 
                                 id="btnExportProdIntrst"
                                 actionListener="#{lookupService.list('PROD_INTREST')}" update=":frmProdIntrstLookup" oncomplete="PF('dlgProdIntrstLookup').show();"  />
                <p:commandLink styleClass="sel-prod-interest" value="Clear" actionListener="#{lookupService.clear('PROD_INTREST')}" 
                               update="@(.sel-prod-interest)"
                               id="cmndLnkExportProdInrstClear"
                               disabled="#{!(lookupService.compProdIntrstList != null and lookupService.compProdIntrstList.size() != 0)}" />
            </h:panelGroup>
            <!--Feature #10929:CRM-6763   Contact list: Export contacts to autoklose for contacts with opp at stage = A or B or C e   by harshithad on 23/03/23-->
             <p:outputLabel for="slctOneLstBox" value="#{custom.labels.get('IDS_OPP')} #{custom.labels.get('IDS_ACTIVITY')}" rendered="#{exportService.moduleId =='CONT'}"  id="otptLblOppStage"/>
             <h:panelGroup class="ui-inputgroup"  rendered="#{exportService.moduleId =='CONT'}">
                <h:selectOneListbox id="slctOneLstBox" styleClass="sel-prod-interest"  size="2" style="width: 100%"     >
                    <f:selectItems value="#{lookupService.compOppActivityList}" var="oppStages"
                                   itemLabel="#{oppStages.actName}"
                                   itemValue="#{oppStages.recId}"/>
                </h:selectOneListbox>
                   <!--Feature #10929:CRM-6763   Contact list: Export contacts to autoklose for contacts with opp at stage = A or B or C e   by harshithad on 23/03/23-->
                <p:commandButton icon="fa fa-search" immediate="true" styleClass="btn-info btn-xs"  title="Select  #{custom.labels.get('IDS_OPP')} #{custom.labels.get('IDS_ACTIVITY')}" 
                                 id="btnExprtAct" 
                                 actionListener="#{lookupService.list('ACT_NAME')}" update="frmOppActivity dlgOppAct" oncomplete="PF('dlgOppActivityLookup').show();"  />
                <p:commandLink styleClass="sel-prod-interest" value="Clear" actionListener="#{lookupService.clear('ACT_NAME')}" 
                               update="exportOppForm:slctOneLstBox exportOppForm:cmndLnkExportActClear"
                               id="cmndLnkExportActClear"
                               disabled="#{!(lookupService.compOppActivityList != null and lookupService.compOppActivityList.size() != 0)}" />
            </h:panelGroup>

            <!--Exclude do not email checkbox -  if flag checked, BOUNCE FLAG =0 -->
            <!--                 TASK#3469-CRM-3912: Need to be able to export PRIMARY CONTACTS   :22-02-2021 by harshithad-->
            <h:panelGroup class="ui-inputgroup"  rendered="#{exportService.moduleId =='CONT'}">
                <p:selectBooleanCheckbox value="#{lookupService.excludeDoNotEmail}" id="cbDoNotEmail"  rendered="#{exportService.moduleId =='CONT'}"/>
                <p:spacer width="4px"/>
                <p:outputLabel for="cbDoNotEmail" value="Exclude Do not Email" rendered="#{exportService.moduleId =='CONT'}"  id="oLblDonotEmail"/>
            </h:panelGroup>
<!--<p:commandButton class="btnlukup" title="Select #{custom.labels.get('IDS_CON_GRP')}" style="vertical-align: 100%" icon="ui-icon-search" onclick="loadGroup();" />-->
            <!--</h:panelGrid>-->
            <h:panelGroup class="ui-inputgroup"  rendered="#{exportService.moduleId =='CONT'}">


                <p:selectBooleanCheckbox value="#{lookupService.primaryOnly}" id="cbPrimaryOnly"  rendered="#{exportService.moduleId =='CONT'}"/>
                <p:spacer width="4px"/>
                 <!--11/11/2021: PMS 6312: CRM-4950: Be able to re-label "Primary" field to "RF Admin" in Prod1-->
                <p:outputLabel for="cbPrimaryOnly" value="#{custom.labels.get('IDS_PRIMARY_CONTACT')} Only" rendered="#{exportService.moduleId =='CONT'}"  id="oLblPrimary"/>
            </h:panelGroup>
            <!--#6328: CRM-4991: Contacts Exporter - Exporting 'Inactive' contacts - poornima -->
            <h:panelGroup class="ui-inputgroup"  rendered="#{exportService.moduleId =='CONT'}">
                <p:selectBooleanCheckbox value="#{lookupService.excludeInActive}" id="cbInActive"  rendered="#{exportService.moduleId =='CONT'}"/>
                <p:spacer width="4px"/>
                <p:outputLabel for="cbInActive" value="Exclude Inactive" rendered="#{exportService.moduleId =='CONT'}"  id="oLblInActive"/>
            </h:panelGroup>
            
            <h:panelGroup class="ui-inputgroup"  rendered="#{exportService.moduleId =='CONT'}">
                <p:selectBooleanCheckbox value="#{lookupService.excludeContsWthoutCompany}" id="cbContsWthoutCompany"  rendered="#{exportService.moduleId =='CONT'}"/>
                <p:spacer width="4px"/>
                <p:outputLabel for="cbContsWthoutCompany" value="Exclude contacts without company" rendered="#{exportService.moduleId =='CONT'}"  id="oLblContsWthoutCompany"/>
            </h:panelGroup>
        </p:panelGrid>
    </p:panel><!--
    <style>
        .first-col{
        }
        .second-col{
            width: 50%!important;
        }
    </style>-->

    <!--<ui:include src="/lookup/TypesLookup.xhtml"/>-->
</ui:composition>

