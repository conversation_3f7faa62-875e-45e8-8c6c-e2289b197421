/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.repfabric.bulkupdate.service;

import com.repfabric.common.dbutils.Dao;
import com.repfabric.common.dbutils.DashboardDao;
import com.repfabric.common.utils.Custom;
import com.repfabric.common.utils.Enums;
import com.repfabric.common.utils.FileUploadController;
import com.repfabric.common.utils.GetCurrentObject;
import com.repfabric.common.utils.GlobalParams;
import com.repfabric.common.utils.LoginBean;
import com.repfabric.common.utils.Message;
import com.repfabric.common.utils.PageNavigation;
import com.repfabric.common.utils.RFUtilities;
import com.repfabric.config.subtables.bean.CompBuyGroupMst;
import com.repfabric.config.subtables.bean.CompTierMst;
import com.repfabric.config.subtables.bean.ContactGroupMst;
import com.repfabric.config.subtables.bean.ProductInterestMst;
import com.repfabric.config.subtables.dao.CompBuyGroupMstDao;
import com.repfabric.config.subtables.dao.CompBuyGroupMstDaoImpl;
import com.repfabric.config.subtables.dao.CompTierMstDao;
import com.repfabric.config.subtables.dao.CompTierMstDaoImpl;
import com.repfabric.config.subtables.dao.CompanyRegionsMstDao;
import com.repfabric.config.subtables.dao.CompanyRegionsMstDaoImpl;
import com.repfabric.datamanagement.crmimport.Alias;
import com.repfabric.datamanagement.crmimport.EmailValidator;
import com.repfabric.datamanagement.products.bean.ProductsMst;
import com.repfabric.datamanagement.producustomerprice.bean.ProdCustomerPrice;
import com.repfabric.datamanagement.producustomerprice.dao.ProdCustPriceDao;
import com.repfabric.datamanagement.producustomerprice.dao.ProdCustPriceDaoImpl;
import com.repfabric.datamanagement.producustomerprice.service.ProdCustomerPriceService;
import com.repfabric.opploop.companies.bean.CompIndustries;
import com.repfabric.opploop.companies.bean.CompProdPotentials;
import com.repfabric.opploop.companies.bean.Companies;
import com.repfabric.opploop.companies.bean.ViewCompanies;
import com.repfabric.opploop.companies.dao.CompIndustriesDao;
import com.repfabric.opploop.companies.dao.CompIndustriesDaoImpl;
import com.repfabric.opploop.companies.dao.CompaniesDao;
import com.repfabric.opploop.companies.dao.CompaniesDaoImpl;
import com.repfabric.opploop.companies.dao.CompanyProductPotentialDao;
import com.repfabric.opploop.companies.service.CompanyListService;
import com.repfabric.opploop.contacts.bean.ContProdInterest;
import com.repfabric.opploop.contacts.bean.ContactGroups;
import com.repfabric.opploop.contacts.bean.Contacts;
import com.repfabric.opploop.contacts.bean.ViewContacts;
import com.repfabric.opploop.contacts.dao.ContProdInterestDao;
import com.repfabric.opploop.contacts.dao.ContProdInterestDaoImpl;
import com.repfabric.opploop.contacts.dao.ContactGroupsDao;
import com.repfabric.opploop.contacts.dao.ContactGroupsDaoImpl;
import com.repfabric.opploop.contacts.dao.ContactsDaoImpl;
import com.repfabric.opploop.contacts.dao.ViewContactsDao;
import com.repfabric.opploop.contacts.dao.ViewContactsDaoImpl;
import com.repfabric.opploop.contacts.service.ContactService;
import com.repfabric.opploop.contacts.service.ShortContListService;
import com.repfabric.opploop.exporter.ExportField;
import com.repfabric.opploop.exporter.ExportService;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.primefaces.PrimeFaces;
import org.primefaces.event.FileUploadEvent;

/**
 *
 * <AUTHOR>
 */
@ManagedBean
@ViewScoped
public class BulkUpdateService implements java.io.Serializable {

    private static final String FILE_URL = "/repfabfiles/companies/";
    private static final String TEMP_FILENAME = "records";
    private String moduleName;
    private Enums.MODULE moduleId;
    //09-01-2021:Task#2848:CRM-1756: Hofstetter: Custom Labels
    Custom customLabel = (Custom) GetCurrentObject.getBean("custom");
    //<!--#11944 CRM-7261   Customer Price Codes: Rename to "Pricing Multipliers"-->
    private String moduleEntered= customLabel.getLabels().get("IDS_CUSTOMER")+"'s" +" Pricing";

    public String getModuleEntered() {
        return moduleEntered;
    }

    public void setModuleEntered(String moduleEntered) {
        this.moduleEntered = moduleEntered;
    }
    //getter and setters
    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public Enums.MODULE getModuleId() {
        return moduleId;
    }

    public void setModuleId(Enums.MODULE moduleId) {
        this.moduleId = moduleId;
    }
    List<List> errorRecordList;
    List<Object> headerObj;
    Integer totalRecords = 0;
    Integer processedRecord = 0;
    private boolean sucess = true;
    // <!--bug #6640 address blank companies reimport map issue--
    private boolean setContFormatAddr = false;
    private boolean updateContGeoCoordinates = false;
    // 11-07-2022 : #8427 : ESCALATION CRM-5880: Company Re-Import; Update Contact Region on condition
    private Integer regionId;
    private String compCity;

    public String getCompCity() {
        return compCity;
    }

    public void setCompCity(String compCity) {
        this.compCity = compCity;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public boolean isUpdateContGeoCoordinates() {
        return updateContGeoCoordinates;
    }

    public void setUpdateContGeoCoordinates(boolean updateContGeoCoordinates) {
        this.updateContGeoCoordinates = updateContGeoCoordinates;
    }

    public boolean isSetContFormatAddr() {
        return setContFormatAddr;
    }

    public void setSetContFormatAddr(boolean setContFormatAddr) {
        this.setContFormatAddr = setContFormatAddr;
    }

    public boolean isSucess() {
        return sucess;
    }

    public void setSucess(boolean sucess) {
        this.sucess = sucess;
    }

    public List<Object> getHeaderObj() {
        return headerObj;
    }

    public void setHeaderObj(List<Object> headerObj) {
        this.headerObj = headerObj;
    }

    Map<Integer, ExportField> header = new HashMap<>();

    public Map<Integer, ExportField> getHeader() {
        return header;
    }

    public void setHeader(Map<Integer, ExportField> header) {
        this.header = header;
    }

    public List<List> getErrorRecordList() {
        return errorRecordList;
    }

    public void setErrorRecordList(List<List> errorRecordList) {
        this.errorRecordList = errorRecordList;
    }

    private EmailValidator emailValidator = new EmailValidator();
    LoginBean lb = (LoginBean) GetCurrentObject.getBean("loginBean");

    public Integer getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(Integer totalRecords) {
        this.totalRecords = totalRecords;
    }

    public Integer getProcessedRecord() {
        return processedRecord;
    }

    public void setProcessedRecord(Integer processedRecord) {
        this.processedRecord = processedRecord;
    }

    //methods
    public void initliazeSettings(String modName) {
        moduleId = Enums.MODULE.valueOf(modName);
        List<ExportField> fields;

        switch (moduleId) {
            case CONT:
                moduleName = "Contacts";
                PrimeFaces.current().executeScript("PF('blkUpdDlg').show();");
                break;
            case COMP:
                moduleName = "Companies";
//                PMS 1853
                PrimeFaces.current().executeScript("PF('blkUpdDlg').show();");
                break;
            case OPP:
                moduleName = customLabel.getLabels().get("IDS_OPPS");
                break;
                //#11050 CRM-6833   Customer Product Price: need excel export and an import button
                //<!--#11944 CRM-7261   Customer Price Codes: Rename to "Pricing Multipliers"-->
            case CUSTPRODPRICE:
                moduleName = customLabel.getLabels().get("IDS_CUSTOMER")+"'s" +" Pricing";
              
//                PMS 1853
                PrimeFaces.current().executeScript("PF('blkUpdDlg').show();");
                break;
            default:
                moduleName = "";
                break;

        }
    }

    public void batchUpdate(FileUploadEvent event) {
        List<Object[]> data = loadFile();

    }

    private List<Object[]> loadFile() {
        Contacts c = new Contacts();
        // this method should take the Excel file as input and get the 
        List<Object[]> data = new ArrayList<>();
        String filePath = FileUploadController.retrieveUploadLocation(FILE_URL).concat(TEMP_FILENAME + "." + "xlsx");
//        boolean importStatus = false;
        org.apache.poi.ss.usermodel.Workbook myWorkBook = null;
        try (FileInputStream fis = new FileInputStream(filePath)) {
            myWorkBook = new XSSFWorkbook(fis);
            org.apache.poi.ss.usermodel.Sheet ws = myWorkBook.getSheetAt(0);

            int rowNum = ws.getPhysicalNumberOfRows();
            totalRecords = 0;
            totalRecords = rowNum;
            int colNum = ws.getRow(0).getPhysicalNumberOfCells();
//            PMS 3996: CRM-3615: Company Re-import issue: Removing Error column if imported file has Error column
            Row headerRow = ws.getRow(0);
            Object[] head = getLine(headerRow, colNum);
            if ("Error".equals(head[colNum - 1].toString())) {
                colNum = colNum - 1;
            }
            for (int i = 0; i < rowNum; i++) {
                headerRow = ws.getRow(i);
                Object[] records = getLine(headerRow, colNum);
                if (records != null) {
                    data.add(records);
                }

            }
            totalRecords = data.size();

        } catch (FileNotFoundException e) {
//            this.errorCode = Enums.errCode.FILE_NOT_FOUND;
//            setFailedStatus();
        } catch (Exception e) {
//            this.errorCode = Enums.errCode.UPLOAD_FAILURE;
//            setFailedStatus();
            e.printStackTrace();
        }

        return data;
    }
//#11050 CRM-6833   Customer Product Price: need excel export and an import button
    private void processCustProductPrice(List<Object[]> records) {

        if (records.isEmpty()) {
            return;
        }

        header = new HashMap<>();
        boolean isHeader = true;
        processedRecord = 0;
        this.errorRecordList = new ArrayList<>();
        if (errorRecordList != null) {
            errorRecordList.clear();
        }
        this.headerObj = new ArrayList<>();
        for (Object[] record : records) {
            if (isHeader) {
                isHeader = false;
                header = getHeader(record);
                Object[] errorRecord = new Object[record.length + 1];
                System.arraycopy(record, 0, errorRecord, 0, record.length);
                errorRecord[record.length] = "Error";
                List<Object> list = Arrays.asList(errorRecord);

                headerObj = list;
            } else {
                if (header == null) {
                    PrimeFaces.current().executeScript("PF('reImportProcess').hide();");
                    return;
                }
                getCustomerProductPrice(record, header);
            }
            processedRecord++;
            //System.out.println("---error------" + errorRecordList.size());
           
        }
            Message.addMessage("Processed successfully", Message.severity.INFO);
            
            PrimeFaces.current().executeScript("updTable()");
           // PrimeFaces.current().executeScript("PF('custProdTbl').filter();");
//           PrimeFaces.current().ajax().update(":prodCustPriceForm:custProdDT");
//            PrimeFaces.current().ajax().update("prodCustPriceForm");
        
         if (this.errorRecordList != null && !this.errorRecordList.isEmpty()) {

                PrimeFaces.current().executeScript("PF('reImportError').show();");
                PrimeFaces.current().ajax().update(":reimportErrFrm:dtReImpErr:errCol");
                PrimeFaces.current().ajax().update(":reimportErrFrm");
            }
         
           
            PrimeFaces.current().ajax().update(":reimportErrFrm:dtReImpErr");
            PrimeFaces.current().executeScript("PF('blkUpdDlg').hide();");
            PrimeFaces.current().executeScript("PF('poll').stop();");
            PrimeFaces.current().executeScript("PF('reImportProcess').hide();");
            
           
            

    }

//    PMS 1853
    private void processCompData(List<Object[]> records) {

        if (records.isEmpty()) {
            return;
        }
        header = new HashMap<>();
        boolean isHeader = true;
        processedRecord = 0;

        this.errorRecordList = new ArrayList<>();
        errorRecordList = new ArrayList<>();
        if (errorRecordList != null) {
            errorRecordList.clear();
        }
        this.headerObj = new ArrayList<>();
        for (Object[] record : records) {
            if (isHeader) {
                isHeader = false;
                header = getHeader(record);
//                 System.out.println("header:::"+header);

//PMS 3996: CRM-3615: Company Re-import issue:
                Object[] errorRecord = new Object[record.length + 1];
                System.arraycopy(record, 0, errorRecord, 0, record.length);
                errorRecord[record.length] = "Error";
                List<Object> list = Arrays.asList(errorRecord);
                headerObj = list;

            } else {
                if (header == null) {
                    PrimeFaces.current().executeScript("PF('reImportProcess').hide();");
                    return;
                }
                getCompRows(record, header);

            }
            processedRecord++;
            PrimeFaces.current().ajax().update(":reimportProcessFrm");
            PrimeFaces.current().ajax().update(":reimportProcessFrm:error");

        }
//        PMS 2826: Code Optimization - Company List screen
        CompanyListService compListServ = (CompanyListService) GetCurrentObject.getBean("companyListService");
        compListServ.loadSubMenu();
//        CompanyList cmpLst = (CompanyList) GetCurrentObject.getBean("companyList");
//        
//        cmpLst.loadSubMenu();
//PMS 4395: Company Page Retention issue
//#5146: Companies > Implement lazy loading
        if (compListServ.getSelectedComapniesSaved() != null) {
            compListServ.getSelectedComapnies().clear();
            compListServ.setSelectedComapniesSaved(new ArrayList<>());
        }
        Integer pg = compListServ.getPageNum();
        PrimeFaces.current().executeScript("PF('companyListTable').filter();");
        PrimeFaces.current().ajax().update(":roleList");
        PrimeFaces.current().ajax().update(":compDetailForm:dtCompList");
        PrimeFaces.current().ajax().update("compDetailForm");

        Message.addMessage("Processed successfully", Message.severity.INFO);
//        PrimeFaces.current().executeScript("PF('dtReImpErr').clearFilters();");
        if (this.errorRecordList != null && !this.errorRecordList.isEmpty()) {

            PrimeFaces.current().executeScript("PF('reImportError').show();");
            PrimeFaces.current().ajax().update(":reimportErrFrm:dtReImpErr:errCol");
            PrimeFaces.current().ajax().update(":reimportErrFrm");
        }
        PrimeFaces.current().ajax().update(":reimportErrFrm:dtReImpErr");
        PrimeFaces.current().executeScript("PF('blkUpdDlg').hide();");
        PrimeFaces.current().executeScript("PF('poll').stop();");
        PrimeFaces.current().executeScript("PF('reImportProcess').hide();");

        PrimeFaces.current().ajax().update(":reimportErrFrm");
//        PMS 4395: Company Page Retention issue
        compListServ.setPageNum(pg);
        PrimeFaces.current().executeScript("rmPgBlkUpdSet()");

    }

    private void processData(List<Object[]> records) {
        if (records.isEmpty()) {
            return;
        }
        header = new HashMap<>();
        boolean isHeader = true;
        processedRecord = 0;

        this.errorRecordList = new ArrayList<>();
        errorRecordList = new ArrayList<>();
        if (errorRecordList != null) {
            errorRecordList.clear();
        }
        this.headerObj = new ArrayList<>();
        for (Object[] record : records) {
            if (isHeader) {
                isHeader = false;
                header = getHeader(record);

                Object[] errorRecord = record;
                errorRecord[record.length - 1] = "Error";
                List<Object> list = Arrays.asList(errorRecord);
                headerObj = list;
            } else {
                if (header == null) {
                    PrimeFaces.current().executeScript("PF('reImportProcess').hide();");
                    return;
                }
                getContRows(record, header);
            }
            processedRecord++;
            PrimeFaces.current().ajax().update(":reimportProcessFrm");
            PrimeFaces.current().ajax().update(":reimportProcessFrm:error");
        }

        if (this.errorRecordList != null && !this.errorRecordList.isEmpty()) {

            PrimeFaces.current().executeScript("PF('reImportError').show();");
            PrimeFaces.current().ajax().update(":reimportErrFrm:dtReImpErr:errCol");
            PrimeFaces.current().ajax().update(":reimportErrFrm");
        }
//PMS 3230:Contact List (Reduced List)
        GlobalParams gp = (GlobalParams) GetCurrentObject.getBean("globalParams");
        if (!gp.isRestrictiveContList()) {
            ContactService contServ = (ContactService) GetCurrentObject.getBean("contactService");
            contServ.loadContacts();
            PrimeFaces.current().executeScript("PF('contListTable').filter();");
            PrimeFaces.current().ajax().update(":contDetailForm");
        } else {
            ShortContListService contRedServ = (ShortContListService) GetCurrentObject.getBean("shortContListService");
            contRedServ.loadContactList();
            PrimeFaces.current().executeScript("PF('dtContLstTbl').filter();");
            PrimeFaces.current().ajax().update(":filterList");
        }

        Message.addMessage("Processed successfully", Message.severity.INFO);
//        PrimeFaces.current().executeScript("PF('dtReImpErr').clearFilters();");
        PrimeFaces.current().ajax().update(":reimportErrFrm:dtReImpErr");
        PrimeFaces.current().executeScript("PF('blkUpdDlg').hide();");
        PrimeFaces.current().executeScript("PF('poll').stop();");
        PrimeFaces.current().executeScript("PF('reImportProcess').hide();");

        PrimeFaces.current().ajax().update(":reimportErrFrm");

    }

    private Map<Integer, ExportField> getHeader(Object[] data) {
        Map<Integer, ExportField> header = new HashMap<>();
//        PMS 1853
        List<ExportField> fields = new ArrayList<>();
        ExportService exportService = new ExportService();
        switch (moduleId) {
            case CONT:
                fields = exportService.populateContExportFields();
                break;
            case COMP:
                fields = exportService.populateCompExportFields();

                break;
                //#11050 CRM-6833   Customer Product Price: need excel export and an import button
            case CUSTPRODPRICE:
                fields = exportService.populateCustProductPriceFields();

                break;
            case OPP:
                break;
            default:
                moduleName = "";
                break;

        }
        String id = (String) data[0];
        if ("id".equals(id.trim().toLowerCase())) {
            header.put(0, new ExportField(0, "Id", "contId"));

        } else {
            Message.addMessage(this.moduleName + " Id column is missing", Message.severity.ERR);
            return null;
        }

        for (int i = 1; i < data.length; i++) {
            for (ExportField field : fields) {

                if (field.getFieldDisplayName().equals((String) data[i])) {
                    header.put(i, field);
                    break;
                } else {
                    header.put(i, field);

                }
            }
        }
        return header;
    }

    public void startImport(FileUploadEvent event) {
        this.totalRecords = 0;
        this.processedRecord = 0;
        String impFilename = event.getFile().getFileName();
        String impFileExt = getFileExtension(impFilename);
        if (!"xlsx".equals(impFileExt)) {
            Message.addMessage("Invalid file Format", Message.severity.ERR);
            PrimeFaces.current().executeScript("PF('blkUpdDlg').hide();");
            PrimeFaces.current().executeScript("PF('poll').stop();");
            PrimeFaces.current().executeScript("PF('reImportProcess').hide();");
            return;
        }
        FileUploadController uploadController = new FileUploadController();
        uploadController.setDestination(FILE_URL);
        uploadController.setFilename(TEMP_FILENAME + "." + impFileExt);
        boolean uploadedStatus = uploadController.upload(event);
        List<Object[]> data = loadFile();

        switch (moduleId) {
            case CONT:
                processData(data);
                break;
            case COMP:
                processCompData(data);
                break;
            case OPP:
                break;
//#11050 CRM-6833   Customer Product Price: need excel export and an import button
            case CUSTPRODPRICE:
                processCustProductPrice(data);
                break;
            default:
                moduleName = "";
                break;

        }
    }

    public String getFileExtension(String name) {
        int lastIndexOf = name.lastIndexOf(".");
        if (lastIndexOf == -1) {
            return ""; //empty extension
        }
        return name.substring(lastIndexOf + 1).toLowerCase();
    }

    public String[] getLine(Row row, int numOfCol) {
        DecimalFormat dForm = new DecimalFormat("0.00");
        String line[] = new String[numOfCol];
        Date d = new Date();
        DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");
        try {
            Cell cell;
            for (int i = 0; i < numOfCol; i++) {
                if (row == null) {
                    return null;
                }
                cell = row.getCell(i);

                if (cell != null) {
                    if (cell.getCellType() == CellType.NUMERIC) {
                        if (HSSFDateUtil.isCellDateFormatted(cell)) {
                            line[i] = dateFormat.format(cell.getDateCellValue());
                        } else {
                            line[i] = cell.getNumericCellValue() % 1 == 0 ? String.valueOf((long) cell.getNumericCellValue()) : dForm.format(cell.getNumericCellValue());

                        }

                    } else {
                        cell.setCellType(CellType.STRING);

                        if (cell != null && cell.toString() != null) {
                            line[i] = cell.toString().trim();
                        }
                    }
                } else {
                    line[i] = "";
                }
            }
        } catch (Exception e) {
            Logger.getLogger(BulkUpdateService.class.getName()).log(Level.SEVERE, null, e);
        }

        return line;
    }

    private final static String updUser = ", updUser=";

    public void getContRows(Object[] record, Map<Integer, ExportField> header) {
        if (record.length <= 0 || header.keySet().isEmpty()) {
            return;
        }
        StringBuilder errorMsg = new StringBuilder();
        String city = new String();
        String state = new String();
        String zipCode = new String();
        String street = new String();
        String frmttdAddr = new String();
        Object[] errorRecord = new Object[record.length];
        boolean validation = true;
        boolean update = false;
        String fname = "";
//        PMS 2495 :contact reimport last name update issue
        String lname = null;
        Integer compId = null;
        String compName = null;
        Companies comp = null;
        boolean compUpdateFlag = false;
        boolean compIdUpdateFlag = false;
        if (record[0].toString() == null || record[0].toString().trim().length() == 0) {
            errorRecord = record;
            errorRecord[record.length - 1] = "Contact Id is Missing";
            List<Object> list = Arrays.asList(errorRecord);
            errorRecordList.add(list);
            return;
        }
        Integer contId = Integer.parseInt(record[0].toString());
        CompaniesDao compDao = new CompaniesDaoImpl();
        ViewContactsDao vConDao = new ViewContactsDaoImpl();
        ViewContacts cont = vConDao.find(contId);
        if (cont == null) {
            errorRecord = record;
            errorRecord[record.length - 1] = "Contact Id does not exist";
            List<Object> list = Arrays.asList(errorRecord);
            errorRecordList.add(list);
            return;
        }

        ContactGroupMst contGrpMst = null;
        ContactGroups contGrp = null;
        String groupName = null;
        String prodIntName = null;
        List<ContactGroupMst> lstcontGrpMst = null;
        List<ContactGroups> lstcontGrp = null;
        ContactGroupsDao groupDao = new ContactGroupsDaoImpl();
        StringBuilder qry = new StringBuilder();

        boolean frmttdAddrFlag = false;
        boolean primaryFlag = false;
        boolean bounceFlag = false;
        //           #10946  ESCALATIONS  CRM-6816   Contacts: Globally Visible setting
        boolean globalVisibileFlag = false;
//        PMS 4540: CRM-4321: synchrocare: reimport not working to clear the blanks
        Dao dao = new Dao();
        //    #5227: person company ->reimport-> some fields not updating
        Integer unifiedId = (Integer) dao.qryObject("Select contUnifiedId from Contacts where contId=" + contId);
        qry.append("update Contacts set");

        for (int i = 0; i < record.length; i++) {

            if (header.get(i) != null) {
//                    System.out.println(header.get(i).getFieldName());
//                    System.out.println(header.get(i).getFieldDisplayName());
            } else {
                break;
            }

            Object value = record[i];
            if (header.get(i).getType() == 1) {
//                System.out.println("========="+header.get(i).getFieldName());
            }

            switch (header.get(i).getFieldName()) {
                case "contFname":
                    //    #5227: person company ->reimport-> some fields not updating
                    //#5542: contact person company reimport>>first name and last name updating issue
                    if (cont.getContFname().equals(value.toString())) {
                        fname = cont.getContFname();
                        break;
                    }
                    update = true;
                    if (value.toString().trim().length() == 0) {
                        validation = false;
                        errorMsg.append("Contact first name is blank;");
                    }
                    if (validateLength(value.toString(), 30)) {
                        fname = value.toString().trim();
                        qry.append(" contFname= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Contact first name exceeds 30 characters; ");
                    }

                    break;
                case "contLname":
                    //    #5227: person company ->reimport-> some fields not updating
//                    #5542: contact person company reimport>>first name and last name updating issue
                    if (cont.getContLname().equals(value.toString())) {
                        lname = cont.getContLname();
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 30)) {
                        lname = value.toString().trim();
                        qry.append(" contLname= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Contact last name exceeds 30 characters; ");
                    }

                    break;
                case "contFullname":

                    break;
                case "compTypeName":

                    break;
                case "contGroup":
//                   PMS 1817: Contact Re-import Updates
                    String[] grps = value.toString().trim().split(",");

                    if (value.toString().trim().length() <= 0 || grps.length <= 0) {
                        groupName = "";
//                        PMS 4540: CRM-4321: synchrocare: reimport not working to clear the blanks
                        Long count = (Long) dao.qryObject(" select count(*) from ContactGroups where contGrpContId =" + contId);
                        if (count > 0) {
                            update = true;
                        }
                        break;
                    }
//                     PMS 1730
                    groupName = new String();
                    update = true;

                    for (int j = 0; j < grps.length; j++) {
                        contGrpMst = groupDao.retrieveContactGroupByName(grps[j].trim());

                        if (contGrpMst != null) {
                            if (j == 0) {
                                groupName = grps[j].trim();
                            } else {
                                groupName = groupName + "," + grps[j].trim();
                            }

                        } else {
                            validation = false;
                            errorMsg.append("Contact Group does not exist; ");
                        }

                    }
                    break;
                case "contJobTitle":
//                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContJobTitle())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 100)) {
                        qry.append(" contJobTitle= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Job title exceeds 100 characters; ");
                    }

                    break;
                case "compName":
                    compUpdateFlag = true;
                    update = true;
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    //    #5227: person company ->reimport-> some fields not updating
                    if (value.toString().trim().equals(cont.getCompName()) || (unifiedId != null && unifiedId > 0)) {
                        comp = compDao.getCompaniesByName(value.toString());
                        compName = value.toString().trim();
                        break;
                    }
                    compName = value.toString().trim();

//                    
//                    comp = compDao.getCompaniesByName(value.toString());
//                    if (comp != null) {
//                        qry.append(" contCompId= ").append(comp.getCompId()).append(",");
//
//                    } else {
//                        validation = false;
//                        errorMsg.append("Company does not exist;");
//                    }
                    break;

                case "compCallPattern":
                    break;
                case "contDept":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContDept())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 60)) {
                        qry.append(" contDept= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Department exceeds 60 characters; ");
                    }

                    break;
                case "contManagerName":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContManagerName())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 40)) {
                        qry.append(" contManagerName= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Manager name exceeds 40 characters; ");
                    }
                    break;
                case "contAssistantName":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContAssistantName())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 40)) {
                        qry.append(" contAssistantName= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Assistant name exceeds 40 characters; ");
                    }
                    break;
                case "contPhoneHome":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContPhoneHome())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 40)) {
                        qry.append(" contPhoneHome= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Home phone exceeds 40 characters; ");
                    }
                    break;
                case "contPhoneBusiness":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContPhoneBusiness())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 40)) {
                        qry.append(" contPhoneBusiness= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        //09-01-2021:Task#2848:CRM-1756: Hofstetter: Custom Labels
                        errorMsg.append(customLabel.getLabels().get("IDS_CONT_BIZ_PHONE")).append(" exceeds 40 characters; ");
                    }
                    break;
                case "contPhoneMobile":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContPhoneMobile())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 40)) {
                        qry.append(" contPhoneMobile= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Mobile phone exceeds 40 characters; ");
                    }
                    break;
                case "contPhoneAlternate":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContPhoneAlternate())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 40)) {
                        qry.append(" contPhoneAlternate= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Alternate phone exceeds 40 characters; ");
                    }
                    break;
                case "contFax":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContFax())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 20)) {
                        qry.append(" contFax= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Fax exceeds 20 characters; ");
                    }
                    break;
                case "contEmailPersonal":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContEmailPersonal())) {
                        break;
                    }
                    update = true;
                    if (value.toString().trim().length() == 0) {
                        qry.append(" contEmailPersonal= '").append("',");
                        break;
                    }
                    if (emailValidator.validate(value.toString())) {
                        if (validateLength(value.toString(), 100)) {
                            qry.append(" contEmailPersonal= '").append(Dao.removeQuote(value.toString())).append("',");
                        } else {
                            validation = false;
                            errorMsg.append("Personal email exceeds 100 characters; ");
                        }
                    } else {
                        validation = false;
                        errorMsg.append("Personal email format is invalid;");
                    }
                    break;
                case "contEmailBusiness":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContEmailBusiness())) {
                        break;
                    }
                    update = true;
                    if (value.toString().trim().length() == 0) {
                        qry.append(" contEmailBusiness= '").append("',");
                        break;
                    }
                    if (emailValidator.validate(value.toString())) {
                        if (validateLength(value.toString(), 100)) {
                            qry.append(" contEmailBusiness= '").append(Dao.removeQuote(value.toString())).append("',");
                        } else {
                            validation = false;
                            errorMsg.append("Business email exceeds 100 characters; ");
                        }
                    } else {
                        validation = false;
                        errorMsg.append("Business email format is invalid;");
                    }
                    break;
                case "contEmailAlternate":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContEmailAlternate())) {
                        break;
                    }
                    update = true;
                    if (value.toString().trim().length() == 0) {
                        qry.append(" contEmailAlternate= '").append("',");
                        break;
                    }
                    if (emailValidator.validate(value.toString())) {
                        if (validateLength(value.toString(), 100)) {
                            qry.append(" contEmailAlternate= '").append(Dao.removeQuote(value.toString())).append("',");
                        } else {
                            validation = false;
                            errorMsg.append("Alternate email exceeds 100 characters; ");
                        }
                    } else {
                        validation = false;
                        errorMsg.append("Alternate email format is invalid;");
                    }
                    break;
                case "contEmailBusiness2":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContEmailBusiness2())) {
                        break;
                    }
                    update = true;
                    if (value.toString().trim().length() == 0) {
                        qry.append(" contEmailBusiness2= '").append("',");
                        break;
                    }
                    if (emailValidator.validate(value.toString())) {
                        if (validateLength(value.toString(), 100)) {
                            qry.append(" contEmailBusiness2= '").append(Dao.removeQuote(value.toString())).append("',");
                        } else {
                            validation = false;
                            errorMsg.append("Business email 2 exceeds 100 characters; ");
                        }
                    } else {
                        validation = false;
                        errorMsg.append("Business email 2 format is invalid;");
                    }
                    break;
                case "regionName":
                    CompanyRegionsMstDao regDao = new CompanyRegionsMstDaoImpl();
//                    PMS 1817: Contact Re-import Updates
                    Integer id = regDao.compRegionByName(value.toString().trim());
//                    PMS 2727 :CRM-3630: Cannot Re-import - Error Region name does not exist
                    if ("".equals(value.toString().trim())) {
                        id = 0;
//                      13065  ESCALATIONS CRM-7744   Contacts: Re-import not working
                        if (!id.equals(cont.getContRegionId())) {
                            qry.append(" contRegionId= ").append(id).append(",");
                            update = true;
                        }
                    } else if (id > 0) {
                        // 13065  ESCALATIONS CRM-7744   Contacts: Re-import not working
                        if (!id.equals(cont.getContRegionId())) {
                            qry.append(" contRegionId= ").append(id).append(",");
                            update = true;
                        }
                    } else {
                        validation = false;
//                            PMS 1817: Contact Re-import Updates
                        errorMsg.append("Region name does not exist; ");
                    }
                    break;

                case "contHomeFormattedAddr":
                    break;
                case "contBusiFormattedAddr":
                    break;

                case "contReferredBy":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContReferredBy())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 40)) {
                        qry.append(" contReferredBy= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Referred by exceeds 40 characters; ");
                    }
                    break;
                case "contContext":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContContext())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 80)) {
                        qry.append(" contContext= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Context exceeds 80 characters; ");
                    }
                    break;
                case "smanName":
                    break;
                case "contNotes":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContNotes())) {
                        break;
                    }
                    update = true;
                    qry.append(" contNotes= '").append(Dao.removeQuote(value.toString())).append("',");
//                        if(validateLength(value.toString(), 80)){
//                           qry.append(" contNotes= '").append(Dao.removeQuote(value.toString())).append("',");
//                        }
//                        else{
//                            validation=false;
//                          errorMsg.append("Notes exceeds 80 character");
//                        }
                    break;
                case "insUserName":
                    break;
                case "insDate":
                    break;
                case "updUserName":

                    break;
                case "updDate":

//                    String date = value.toString();
//                    GlobalParams gp=(GlobalParams) GetCurrentObject.getBean("globalParams");
//                    SimpleDateFormat sdf = new SimpleDateFormat(gp.getDateTimeFormat());
//                     {
//                        try {
//                            Date dt = sdf.parse(date);
//                            Date d = RFUtilities.convertToUTC(dt);
//                            
//                            sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:s");
//                            d=sdf.parse(d.toString());
//                            qry.append(" updDate='").append(d).append("',");
//                        } catch (ParseException ex) {
//                            validation = false;
//                            errorMsg.append(" Invalid last modified date");
//                            Logger.getLogger(BulkUpdateService.class.getName()).log(Level.SEVERE, null, ex);
//                        }
//                    }
                    break;
                case "contBusiCity":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContBusiCity())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 40)) {
                        city = value.toString();
                        frmttdAddrFlag = true;
                        qry.append(" contBusiCity= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Contact Business City exceeds 40 characters; ");
                    }
                    break;
                case "contBusiState":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContBusiState())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 40)) {
                        state = value.toString();
                        frmttdAddrFlag = true;
                        qry.append(" contBusiState= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Contact Business State exceeds 40 characters; ");
                    }
                    break;

                case "contBusiZipCode":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContBusiZipCode())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 12)) {
                        zipCode = value.toString();
                        frmttdAddrFlag = true;
                        qry.append(" contBusiZipCode= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Contact Business Zip code exceeds 12 characters; ");
                    }
                    break;

                case "contCompId":
                    update = true;
                    if (value.toString().trim().length() == 0) {
                        compId = 0;
                        break;
                    }

                    //    #5227: person company ->reimport-> some fields not updating
                    if (cont.getContCompId().equals(Integer.parseInt(value.toString())) || (unifiedId != null && unifiedId > 0)) {
                        compId = cont.getContCompId();
                        break;
                    }
                    compIdUpdateFlag = true;
                    compId = Integer.parseInt(value.toString());
//                     compName=compDao.getCompanyNameById(compId);
//                     if(compName==null){
//                         validation = false;
//                         errorMsg.append(" Company Id doesn't exist;");
//                     }

                    break;
                case "contRefNo":
                    String val = new String();
                    if (cont.getContRefNo() == null) {
                        val = "";
                    } else {
                        val = cont.getContRefNo();
                    }
                    if (val.equals(value.toString())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 50)) {
                        qry.append(" contRefNo= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append(" Reference number exceeds 50 characters; ");
                    }
                    break;
                case "compHdayCardFlag":
                    break;
//                    case "callPattern":
//                        break;
                case "compClass":
                    break;
                case "compTags":
                    break;
                case "contBusiAddress1":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContBusiAddress1())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 180)) {
                        street = value.toString();
                        frmttdAddrFlag = true;
                        qry.append(" contBusiAddress1= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Contact Business street code exceeds 180 characters; ");
                    }
                    break;
                case "GetPrinciBuyingFrom":
                    break;
                case "MailAddress":
                    break;
                case "contBusiPoBox":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContBusiPoBox())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 20)) {

                        qry.append(" contBusiPoBox= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Contact Business PO box code exceeds 20 characters; ");
                    }
                    break;
                case "contHomePoBox":
                    //                   PMS 2474 CRM-3587: Re-inputting Contact Records
                    if (value.toString().equals(cont.getContHomePoBox())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 20)) {

                        qry.append(" contHomePoBox= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Contact Home PO box code exceeds 20 characters; ");
                    }
                    break;
                case "contProdInterest":
//                    PMS 1817: Contact Re-import Updates
                    String[] prdIntrst = value.toString().split(",");
                    if (value.toString().trim().length() <= 0 || prdIntrst.length <= 0) {
                        prodIntName = "";
                        break;
                    }
//                        PMS 1730
                    prodIntName = new String();
                    update = true;
                    ContProdInterestDao contProdDao = new ContProdInterestDaoImpl();
                    ProductInterestMst prdIntrstObj;
                    if (prdIntrst.length <= 0) {
                        prodIntName = "";
                        break;
                    }
                    for (int j = 0; j < prdIntrst.length; j++) {
                        prdIntrstObj = contProdDao.retrieveContactProdIntrstName(prdIntrst[j].trim());
                        if (prdIntrstObj != null) {
                            if (j == 0) {
                                prodIntName = prdIntrst[j].trim();
                            } else {
                                prodIntName = prodIntName + "," + prdIntrst[j];
                            }
                        } else {
                            validation = false;
                            errorMsg.append("Product interest Does not exist; ");
                        }

                    }
                    break;
                case "contBounceFlag":
                    int bounce = 0;

                    if ("yes".equals(value.toString().trim().toLowerCase())) {
                        bounce = 1;
                        bounceFlag = true;
                    } else if ("no".equals(value.toString().trim().toLowerCase()) || "".equals(value.toString().trim().toLowerCase())) {
                        bounce = 0;
                        bounceFlag = false;
                    }
                    if (cont.getContBounceFlag() == null) {
                        bounce = 0;
                        bounceFlag = false;
                    } else if (cont.getContBounceFlag() == bounce) {
                        break;
                    }
                    update = true;
                    if (bounceFlag) {
                        qry.append(" contBounceFlag=").append(1).append(",");
                    } else {
                        qry.append(" contBounceFlag=").append(0).append(",");
                    }
                    break;
                case "contPrimaryFlag":

                    int prim = 0;
                    if ("yes".equals(value.toString().trim().toLowerCase())) {
                        primaryFlag = true;
                        prim = 1;
                    } else if ("no".equals(value.toString().trim().toLowerCase()) || "".equals(value.toString().trim().toLowerCase())) {
                        prim = 0;
                        primaryFlag = false;
                    }
                    if (cont.getContPrimaryFlag() == prim) {
                        break;
                    }
                    update = true;
                    break;
//           #10946  ESCALATIONS  CRM-6816   Contacts: Globally Visible setting
                case "globalVisibleFlag":
                    int globalVisible = 0;
                    if ("yes".equals(value.toString().trim().toLowerCase())) {
                        globalVisibileFlag = true;
                        globalVisible = 1;
                    } else if ("no".equals(value.toString().trim().toLowerCase()) || "".equals(value.toString().trim())) {
                        globalVisibileFlag = false;
                        globalVisible = 0;
                    }
                    if (cont.getContGlobalFlag() == null) {
                        globalVisibileFlag = false;
                        globalVisible = 0;
                    } else if (cont.getContGlobalFlag() == globalVisible) {
                        break;
                    }
                    update = true;

                    if (globalVisibileFlag) {
                        qry.append(" contGlobalFlag=").append(1).append(",");
                    } else {
                        qry.append(" contGlobalFlag=").append(0).append(",");
                    }
                    break;

                //Custom Fields..
                default:
//                        System.out.println("Custom "+header.get(i).getFieldName());
//                        System.out.println("Custom "+header.get(i).getFieldDisplayName());
//                        validateCustomFiled();
//                        dghfgf;
                    break;
            }
        }
        if (compUpdateFlag || compIdUpdateFlag) {
            int compStatus = setCompany(compId, compName, cont);
            switch (compStatus) {
                case -4:
                    break;
                case -1:
                    errorMsg.append(" Company ID not found");
                    validation = false;
                    break;
                case -2:
//                    #5227 : person company ->reimport-> some fields not updating
                    errorMsg.append(" Company Not found");
                    validation = false;
                    break;
                case -3:
                    errorMsg.append(" Company ID/Name mismatch");
                    validation = false;
                    break;
                default:
                    qry.append(" contCompId=").append(compStatus).append(",");
                    break;

            }
        }
//            if(compId!=0 && comp!=null){
//                if(compUpdateFlag || compIdUpdateFlag){
//                if(!validateCompany(compId, compName)){
//                        validation=false;
//                    errorMsg.append("Company name and company Id mismatch;");
//                    
//                    
//                }
//                else if(comp==null){
//                    qry.append(" contCompId=").append(compId).append(",");
//                }else if(compId==null){
//                    qry.append(" contCompId=").append(comp.getCompId()).append(",");
//                }
//                else{
//                    qry.append(" contCompId=").append(comp.getCompId()).append(",");
//                }
//                }
//            }
        String fulName = generateFullName(fname, lname, cont);
        if (!cont.getContFullName().equals(fulName)) {
            ContactsDaoImpl contDaoImpl = new ContactsDaoImpl();
            ViewContacts cn = vConDao.find(fulName);
            if (!contDaoImpl.checkIfNameExists(fulName, cont.getContCompId())) {
                qry.append(" contFullName='").append(fulName).append("',");

            } else if (cn == null) {
            } else if (!cn.getContId().equals(cont.getContId())) {
                validation = false;
                errorMsg.append("Contact Name is duplicate;");
            } else {
            }
        } else {
//            qry.append(" contFullName='").append(fulName).append("',");
        }

//        if(contDaoImpl.checkIfNameExists(fulName, cont.getContCompId())){
//            qry.append(" contFullName='").append(fulName).append("',");
//        }
//        else{
//            errorMsg.append(" Contact Name exist for company").append(compName);
//        }
//                // <!--bug #6640 address blank companies reimport map issue--
        if (frmttdAddrFlag) {
            String addr = createFormattedAdress(street, city, state, zipCode, city, cont);
            // System.out.println("")  
            if (!addr.equals(cont.getContBusiFormattedAddr()) && !"".equals(addr)) {
                updateContGeoCoordinates = true;
                qry.append(" contBusiFormattedAddr='").append(addr).append("',");
            } else {
                Dao da = new Dao();

                StringBuilder q = new StringBuilder();

                updateContGeoCoordinates = false;
                setContFormatAddr = true;
                q.append(" update COMPANIES set COMP_GEO_LATI = null");
                q.append(", COMP_GEO_LONGI = null , COMP_ADDR_INVALID = 1 , COMP_FORMATTED_ADDR = '' where COMP_UNIFIED_ID  = ").append(cont.getContId());
                da.qryExecute(q.toString(), Dao.query.SQL);
                qry.append(" contBusiFormattedAddr= '', ");

            }
        }
        if (!validation) {
            errorRecord = record;

            errorRecord[record.length - 1] = errorMsg.toString();
            List<Object> list = Arrays.asList(record);
            errorRecordList.add(list);
        } else if (update) {
//PMS 4540: CRM-4321: synchrocare: reimport not working to clear the blanks

//            PMS 2006 CRM-3408: Re-import Contacts - locking up not working
//            int index = qry.lastIndexOf(",");
//            if(index>0){
//                qry.deleteCharAt(index);
//            }
//            qry.append(", updDate='").append(sdf.format(RFUtilities.getCurrentTimeZoneFomattedDate())).append("'");
            if (primaryFlag) {
                dao.qryExecute("UPDATE CONTACTS SET CONT_PRIMARY_FLAG=0 WHERE CONT_COMP_ID=" + cont.getContCompId(), Dao.query.SQL);
                qry.append(" contPrimaryFlag=").append(1);
            } else {
                qry.append(" contPrimaryFlag=").append(0);
            }

            qry.append(updUser).append(lb.getUserId());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String updDate = sdf.format(RFUtilities.getCurrentUTCDate());
            qry.append(", updDate='").append(updDate).append("'");
            qry.append(" where contId=").append(contId);

            if (!dao.updateQuery(qry.toString(), Dao.query.HQL)) {
                errorRecord = record;

                errorRecord[record.length - 1] = "Unable to process record";
                List<Object> list = Arrays.asList(record);
                errorRecordList.add(list);
            } else {
                if (groupName != null) {
                    updateContactGroup(contId, groupName);
                }
                if (prodIntName != null) {
                    updateProdIntrest(contId, prodIntName);
                }
                //    #5227: person company ->reimport-> some fields not updating
                if (unifiedId != null && unifiedId > 0) {
                    personCompUpdate(contId, true);
                }
                // <!--bug #6640 address blank companies reimport map issue-->
                if (updateContGeoCoordinates) {

                    updateGeoCordinates(unifiedId);
                }

            }
//            dao.qryExecute(qry.toString(), Dao.query.HQL);

        }
    }
//        public String validateCustomFileds(Integer contId,ExportField exportField){
//            ContCustomFieldsDao contCustDao=new ContCustomFieldsDaoImpl();
//            List<Object[]> list = contCustDao.fetchCustomFields(contId);
////        CustomFieldsMst cf=getContCustomFieleds(exportField.getFieldName());
//        String qry=new String();
//        if(cf==null){
//            return null;
//        }
//        
//                
//            
//            switch (CustomFieldsMstService.DATA_TYPES.valueOf(cf.getCustFieldType())) {
//                case tx:
//                    break;
//                case in:
//                    break;
//                case de:
//                     break;
//                case dd:
//                    break;  
//                case md:
//                    
//                    break;
//
//                case da:
//                    
//                    break;
//                case dt:
//                    
//                    break;
//                case cb:
//
//                    break;
//                case ta:
//                    break;
//                case lk:
//                    break;
//            }
//            
//        
//    
//            
//          return qry;  
//        }

    public void validateFiled() {

    }

    public boolean validateLength(String data, Integer length) {
        return data != null && data.length() <= length;
    }

    public ExportField validateCustomFiled(ExportField exportField) {
        if (exportField.getType() == 1) {
        }
        return exportField;

    }

    public boolean validateCompany(Integer compId, Companies comp, boolean idFlag, boolean nmFlag) {

        if (compId == 0 && !nmFlag) {
            return true;
        }
        if (comp == null && !idFlag) {
            return true;
        }
        if (comp == null && compId == 0) {
            return true;
        }
        if (comp == null && compId != 0) {
            return false;
        }
        if (Objects.equals(compId, comp.getCompId())) {
            return true;
        }
        return false;
    }

    public void updateContactGroup(Integer contId, String value) {
        String[] grps = value.split(",");
        ContactGroupMst contGrpMst = null;
        ContactGroups contGrp = null;
        Dao dao = new Dao();
        ContactGroupsDao groupDao = new ContactGroupsDaoImpl();
        groupDao.deleteByContactId(contId);
        if (value.trim().length() == 0) {
            return;
        }
        for (int j = 0; j < grps.length; j++) {
            contGrpMst = groupDao.retrieveContactGroupByName(grps[j]);
            if (contGrpMst != null) {
                contGrp = new ContactGroups();
                contGrp.setContGrpGroupId(contGrpMst.getContGroupId());
                contGrp.setContGrpContId(contId);
                dao.perform(contGrp, Dao.action.INSERT);
            }
//            PMS 1817: Contact Re-import Updates
//            else if(grps[j].trim().length()!=0){
//                ContactGroupMstService grpServ = new ContactGroupMstService();
//                int id = grpServ.saveNewContGrup(grps[j]);
//                if (id > 0) {
//                    contGrp = new ContactGroups();
//                    contGrp.setContGrpGroupId(id);
//                    contGrp.setContGrpContId(contId);
//                    dao.perform(contGrp, Dao.action.INSERT);
//                }
//
//            }
        }
    }

    public void updateProdIntrest(Integer contId, String value) {
        String[] prod = value.split(",");
        Dao dao = new Dao();
        ContProdInterestDao contProdDao = new ContProdInterestDaoImpl();
        ContProdInterest contProd = new ContProdInterest();
        contProdDao.deleteByContactId(contId);
        ProductInterestMst prdIntrst = null;
        for (int i = 0; i < prod.length; i++) {
            prdIntrst = contProdDao.retrieveContactProdIntrstName(prod[i].trim());
            if (prdIntrst != null) {
                contProd.setContId(contId);
                contProd.setContProdId(prdIntrst.getRecId());
                dao.perform(contProd, Dao.action.INSERT);
            }
//            PMS 1817: Contact Re-import Updates
//            else {
//                if(prod[i].trim().length()!=0){
//                    
//                
//                    prdIntrst = new ProductInterestMst();
//                    prdIntrst.setProdName(prod[i]);
//                    prdIntrst.setUpdDate(RFUtilities.getCurrentUTCDate());
//                    prdIntrst.setUpdUser(lb.getUserId());
//                    dao.getIdOnSave(prdIntrst, Dao.action.INSERT);
//                    prdIntrst = contProdDao.retrieveContactProdIntrstName(prod[i]);
//                    contProd = new ContProdInterest();
//                    contProd.setContId(contId);
//                    contProd.setContProdId(prdIntrst.getRecId());
//                    dao.perform(contProd, Dao.action.INSERT);
//                }
//            }
        }

    }

    public String generateFullName(String fName, String lName, ViewContacts cont) {
//        PMS 2321:CRM-3536: Reimport: contact name getting wiped out
        if (fName == null || fName.trim().length() == 0) {
            fName = cont.getContFname();
        }
//        PMS 2495 :contact reimport last name update issue
        if (lName == null) {
            lName = cont.getContLname();
        }
        String fullName = fName.concat(" ").concat(lName);
        if (cont.getContFullName().equals(fullName)) {
            return cont.getContFullName();
        }
        return fName.concat(" ").concat(lName);
    }

    public void lstnrPoll() {

        PrimeFaces.current().ajax().update(":reimportProcessFrm");
        PrimeFaces.current().ajax().update(":reimportProcessFrm:error");
    }

    public String createFormattedAdress(String street, String city, String state, String zipCode, String country, ViewContacts cont) {
        if (street != null && street.trim().length() > 0) {

        } else {
            street = cont.getContBusiAddress1();
        }
        if (city != null && city.trim().length() > 0) {

        } else {
            city = cont.getContBusiCity();
        }
        if (state != null && state.trim().length() > 0) {

        } else {
            state = cont.getContBusiState();
        }
        if (zipCode != null && zipCode.trim().length() > 0) {

        } else {
            zipCode = cont.getContBusiZipCode();
        }

        country = cont.getContBusiCountry();
        return Alias.createCombinedAddr(street, city, state, zipCode, country);
    }

    /* -4         - No need to update
     0 or > 0  - Update to this id
     -1         - Company ID not found
     -2         - Company does not exist
     -3         - Company ID/Name mismatch
     */
    private Integer setCompany(Integer ssCompId, String ssCompName, ViewContacts con) {

//        System.out.println(ssCompId + " / " + ssCompName);
        boolean updateCompId = false, updateCompName = false;
        int companyGiven = 0;

        if (ssCompId != null) {
            companyGiven = companyGiven + 1;
            if (ssCompId != con.getContCompId()) {
                updateCompId = true;
            }
        }

        if (ssCompName != null) {
            companyGiven = companyGiven + 2;
            if (!ssCompName.equals(con.getCompName())) {
                updateCompName = true;
            }
        }

        if (companyGiven == 0) {
            return -4;
        }

        if (!updateCompId && !updateCompName) {
            return -4;
        }
        CompaniesDao compDao = new CompaniesDaoImpl();

        String name;
        if (companyGiven == 1) {
            if (ssCompId == 0) { //if id is blank
                return ssCompId;
            }
            //check if company id exists and update
            name = compDao.getCompanyNameById(ssCompId);

            //if does not exist -Company ID not found
            if (name == null) {
                return -1;
            }

            return ssCompId;
        }

        Integer id;
        if (companyGiven == 2) {
            if ("".equals(ssCompName)) { // if name is blank
                return 0;
            }
            //check if company exists by name 
            id = compDao.getCompanyId(ssCompName);
            //if exists update
            //if not - Company does not exist
            if (id == 0) {
//                System.out.println("Company does not exist");
                return -2;
            }

            return id;
        }

        if (updateCompId = !updateCompName) {
            //then error out Company ID mismatch

            return -3;
        }

        if (ssCompId == 0 && "".equals(ssCompName)) { //if both are blank
            return 0;
        }

        //check if company exists by name and id
        boolean exists = compDao.isCompanyAvailable(ssCompId, ssCompName);
        if (!exists) {
            //if does not exist, then error out "Company ID mismatch"

            return -3;
        }

        //update contact company
        return ssCompId;
    }

    // 11-07-2022 : #8427 : ESCALATION CRM-5880: Company Re-Import; Update Contact Region on condition
    public void updateCompRegions(Integer compId, Integer region, String compCity) {
        LoginBean lb = (LoginBean) GetCurrentObject.getBean("loginBean");
        StringBuilder str = new StringBuilder("update Contacts set contRegionId=");
        str.append(region).append(" , updUser=").append(lb.getUserId()).append(" , updDate=UTC_TIMESTAMP() where contCompId=").append(compId)
                .append(" and ((contRegionId is null or contRegionId=0)").append(" or (contBusiCity='").append(Dao.removeQuote(compCity))
                .append("'))");
//        System.out.println("\n str="+str);
        new Dao().qryExecute(str.toString(), Dao.query.HQL);
    }
//#11050 CRM-6833   Customer Product Price: need excel export and an import button
    public ProductsMst checkPartNoExists(Integer princId, String partNumber) {
        DashboardDao daoo = new DashboardDao();
        StringBuilder queryStr = new StringBuilder("from ProductsMst ");
        queryStr.append("where prodPrinciId =").append(princId);

        queryStr.append(" and prodPrinciPartno = '").append(Dao.removeQuote(partNumber)).append("'");
        
        ProductsMst prod = (ProductsMst) daoo.qryUniqueResult(queryStr.toString(), DashboardDao.query.HQL);
        
        return prod;
    }
//#11050 CRM-6833   Customer Product Price: need excel export and an import button
    public void getCustomerProductPrice(Object[] record, Map<Integer, ExportField> header) {

        if (record.length <= 0 || header.keySet().isEmpty()) {
            return;
        }
        StringBuilder errorMsg = new StringBuilder();

        CompaniesDao compDao = new CompaniesDaoImpl();
        Object[] errorRecord = new Object[record.length + 1];

        if (record[0].toString() == null || record[0].toString().trim().length() == 0) {
//            PMS 3996: CRM-3615: Company Re-import issue:
            System.arraycopy(record, 0, errorRecord, 0, record.length);
            errorRecord[record.length] = "Id is Missing";
            List<Object> list = Arrays.asList(errorRecord);
            errorRecordList.add(list);
            return;
        }
        Integer recId = Integer.parseInt(record[0].toString());
        ProdCustPriceDao custPriceDao = new ProdCustPriceDaoImpl();
        ProdCustomerPrice custProdPri = custPriceDao.getProductCustomerPriceDtls(recId);

        if (custProdPri == null) {
//            PMS 3996: CRM-3615: Company Re-import issue:
            System.arraycopy(record, 0, errorRecord, 0, record.length);
            errorRecord[record.length] = "Id does not exist";
            List<Object> list = Arrays.asList(errorRecord);
            errorRecordList.add(list);
            return;
        }
        DashboardDao dao = new DashboardDao();

        int priceType = 0;
        StringBuilder qry1 = new StringBuilder();
        StringBuilder qry = new StringBuilder();
        int i = 1;
        Integer principalId = 0;
        String Partnumber = "";
        String princiName = "";
        boolean validation = true;
        boolean update = false;
        ProductsMst prod =new ProductsMst();
        boolean flag = false;
        BigDecimal qtyFrom = BigDecimal.ZERO;
        BigDecimal qtyTo = BigDecimal.ZERO;

        BigDecimal standardPrice = BigDecimal.ZERO;
        while (i < record.length) {

            StringBuilder str = new StringBuilder();

            Object value = record[i];
            //System.out.println("---------value.toString()-----"+value.toString()+"------i----"+i+"-------priceType--------"+priceType);
            switch (priceType) {
                case 1:
                    str.append("customer");

                    break;
                case 2:
                    str.append("buyingGroup");

                    break;
                case 3:
                    str.append("tier");

                    break;
                default:
                    str.append(header.get(i).getFieldName());
                    break;
            }
            //  System.out.println("--------str.toString()--------"+str.toString()+"-----------i---------"+record[i]);
            switch (str.toString()) {
                case "prodPrinci":

                    if (value.toString().trim().length() == 0) {
                        validation = false;
                        errorMsg.append("Manufacturer is Mandatory;");
                        i = i + 1;
                        break;
                    }
                    Companies c = compDao.getCompaniesByName(value.toString());
                    principalId = custProdPri.getProdPrinciId();
                    if (c != null && c.getCompId() == custProdPri.getProdPrinciId()) {

                        i = i + 1;
                        break;
                    }
                   
                    if (c != null) {
                           update = true;
                        if (validateLength(value.toString(), 80)) {
                            
                            princiName = c.getCompName();
                            principalId = c.getCompId();
                            qry.append(" prodPrinciId= ").append(c.getCompId()).append(",");

                        } else {
                            validation = false;
                            errorMsg.append(customLabel.getLabels().get("IDS_PRINCI")).append(" exceeds 80 characters; ");
                        }
                    } else {
                     
                        validation = false;
                        errorMsg.append(customLabel.getLabels().get("IDS_PRINCI")).append(" Does Not Exist ; ");

                    }
                    i = i + 1;

                    
                    break;

                case "prodPrinciPartNo":
                    if (value.toString().trim().equals(custProdPri.getProdPrinciPartno())) {
                        Partnumber = Dao.removeQuote(value.toString());
                        prod = checkPartNoExists(principalId, Partnumber);
                        
                         if (prod == null) {
                            flag = true;

                        }
                    
                        i = i + 1;
                        break;
                    }

                    if (value.toString().trim().length() == 0) {
                        validation = false;
                        errorMsg.append(customLabel.getLabels().get("IDS_PART_NUM")).append(" is Mandatory;");
                        i = i + 1;
                        break;
                    }

                    if (validateLength(value.toString(), 60)) {
                        update = true;
                        Partnumber = Dao.removeQuote(value.toString());
                        qry.append(" prodPrinciPartno ='").append(Partnumber).append("',");

                        prod = checkPartNoExists(principalId, Partnumber);
                    
                        if (prod == null) {
                            flag = true;

                        }
                        

                    } else {
                        validation = false;
                        errorMsg.append(customLabel.getLabels().get("IDS_PART_NUM")).append(" exceeds 60 characters; ");
                    }
                    i = i + 1;

                    break;

                case "priceType":

                    if (value.toString().trim().length() == 0) {
                        validation = false;
                        errorMsg.append("Price Type is Mandatory;");
                        i = i + 4;
                        break;
                    }

                    if (value.toString().equals(customLabel.getLabels().get("IDS_CUSTOMER"))) {
                        priceType = 1;
                        i = i + 1;
                        break;

                    } else if (value.toString().equals("Buying Group")) {
                        priceType = 2;
                        i = i + 2;
                        break;

                    } else if (value.toString().equals("Tier")) {
                        priceType = 3;
                        i = i + 3;
                        break;

                    } else {
                        validation = false;
                        errorMsg.append("Price Type is Does not Exist;");
                        i = i + 4;
                        break;
                    }

                case "customer":

                    if (value.toString().trim().length() == 0) {
                        validation = false;
                        errorMsg.append(customLabel.getLabels().get("IDS_CUSTOMER")).append(" is Mandatory;");
                        i = i + 3;
                        priceType = 0;
                        break;
                    }

                    Companies cmp = compDao.getCompaniesByName(value.toString());

                    if (cmp != null && value.toString().trim().equals(cmp.getCompName())) {

                        i = i + 3;
                        priceType = 0;
                        break;
                    }

                    if (cmp != null) {
                        update = true;
                        qry.append(" prodPriceBased =").append(priceType).append(",");
                        qry.append(" prodCustId =").append(cmp.getCompId()).append(",").append(" prodCustBuyGroup = 0 ").append(",").append(" prodCustTier =0").append(",");
                    } else {
                        validation = false;
                        errorMsg.append(customLabel.getLabels().get("IDS_CUSTOMER")).append(" Does Not Exist;");
                    }
                    i = i + 3;
                    priceType = 0;

                    break;
                case "buyingGroup":

                    CompBuyGroupMstDao buyGroupMstDao = new CompBuyGroupMstDaoImpl();
                    if (value.toString().trim().length() == 0) {
                        validation = false;
                        errorMsg.append("Buying Group is Mandatory;");
                        i = i + 2;
                        priceType = 0;
                        break;
                    }
                    CompBuyGroupMst compBuying = buyGroupMstDao.compBuyingGroupName(custProdPri.getProdCustBuyGroup());
                    // System.out.println("----value.toString().trim()-----" + custProdPri.getProdCustBuyGroup() + "-------custProdPri.getCustName()----" + value.toString() );
                    if (compBuying != null) {
                        
                        
                        if (value.toString().trim().equals(compBuying.getBuyGroup())) {
                            i = i + 2;
                            priceType = 0;
                            break;
                        }

                        if (compBuying.getRecId() != 0) {
                            update = true;
                            qry.append(" prodPriceBased =").append(priceType).append(",");
                            qry.append(" prodCustBuyGroup =").append(compBuying.getRecId()).append(",").append(" prodCustId =0").append(",").append(" prodCustTier =0").append(",");

                        } else {
                            validation = false;
                            errorMsg.append("Buying Group Does Not Exist;");
                        }
                        priceType = 0;
                        i = i + 2;

                    } else {

                        compBuying = compBuyingGroupName(value.toString());
                        if (compBuying != null) {
                            update = true;
                            qry.append(" prodPriceBased =").append(priceType).append(",");
                            qry.append(" prodCustBuyGroup =").append(compBuying.getRecId()).append(",").append(" prodCustId =0").append(",").append(" prodCustTier =0").append(",");

                        } else {
                            validation = false;
                            errorMsg.append("Buying Group Does Not Exist;");
                        }
                        priceType = 0;
                        i = i + 2;

                    }

                    break;

                case "tier":
                    CompTierMstDao compTierMstDao = new CompTierMstDaoImpl();
                    if (value.toString().trim().length() == 0) {
                        validation = false;
                        errorMsg.append("Tier is Mandatory;");
                        i = i + 1;
                        priceType = 0;
                        break;
                    }
                  
                    CompTierMst compTier = compTierMstDao.getCompTierName(custProdPri.getProdCustTier());
                   // System.out.println("----value.toString().trim()-----" + compTier +"----------value--------"+value.toString());

                    if (compTier != null) {
                        
                        if (value.toString().trim().equals(compTier.getCompTier())) {
                            i = i + 1;
                            priceType = 0;
                            break;
                        }

                        if (compTier.getRecId() != 0) {
                            update = true;
                            qry.append(" prodPriceBased =").append(priceType).append(",");
                            qry.append(" prodCustTier =").append(compTier.getRecId()).append(",").append(" prodCustBuyGroup = 0 ").append(",").append(" prodCustId =0").append(",");
                        } else {
                            validation = false;
                            errorMsg.append("Tier Does Not Exist;");
                        }
                        priceType = 0;

                        i = i + 1;
                    } else {
                        
                        compTier = getCompTierName(value.toString());
                         // System.out.println("----)-----" + compTier );
                        if (compTier != null) {
                            update = true;
                            qry.append(" prodPriceBased =").append(priceType).append(",");
                            qry.append(" prodCustTier =").append(compTier.getRecId()).append(",").append(" prodCustBuyGroup = 0 ").append(",").append(" prodCustId =0").append(",");
                        } else {
                            validation = false;
                            errorMsg.append("Tier Does Not Exist;");
                        }
                        
                        priceType = 0;

                        i = i + 1;

                    }
                    break;
                case "qtyfrom":
                    qtyFrom = new BigDecimal(value.toString());
                    if (custProdPri.getProdQtyFrom().compareTo(qtyFrom) == 0) {
                        // System.out.println("-------entered---------");

                        i = i + 1;
                        break;
                    }

                    update = true;
                    qry.append(" prodQtyFrom =").append(qtyFrom).append(",");
                    i = i + 1;

                    break;
                case "qtyTo":
                    qtyTo = new BigDecimal(value.toString());
                    if (custProdPri.getProdQtyTo().compareTo(qtyTo) == 0) {
                        //  System.out.println("-------entered 1---------");

                        i = i + 1;
                        break;
                    }
                    update = true;
                    qry.append(" prodQtyTo =").append(qtyTo).append(",");
                    i = i + 1;

                    break;
                case "customerPrice":
                    BigDecimal custPrice = new BigDecimal(value.toString());
                    if (custProdPri.getProdCustPrice().compareTo(custPrice) == 0) {

                        i = i + 1;
                        break;
                    }
                    update = true;
                    qry.append(" prodCustPrice =").append(custPrice).append(",");

                    i = i + 1;

                    break;
                case "standardPrice":
                    BigDecimal stdPrice = new BigDecimal(value.toString());
                    if (custProdPri.getProdStdPrice().compareTo(stdPrice) == 0) {
                        standardPrice = stdPrice;
                        i = i + 1;
                        break;
                    }

                    update = true;
                    qry.append(" prodStdPrice =").append(stdPrice).append(",");
                    standardPrice = stdPrice;

                    if (stdPrice.compareTo(BigDecimal.ZERO) == 1 && (prod != null && prod.getProdStdPrice() != stdPrice)) {
                        DashboardDao dashboardDao = new DashboardDao();
                        StringBuilder qu = new StringBuilder();
                        qu.append(" update ProductsMst set prodStdPrice =").append(stdPrice).append(" where prodPrinciId=").append(principalId).append(" and prodPrinciPartno='").append(Dao.removeQuote(Partnumber)).append("'");

                        dashboardDao.updateQuery(qu.toString(), DashboardDao.query.HQL);
                    }

                    i = i + 1;

                    break;

            }

           
           

        }
        
         if (qtyFrom.compareTo(qtyTo) == 1) {
                validation = false;
                errorMsg.append("Invalid Quantity Range ;");
            }
        
         if (flag) {
                
                DashboardDao dashboardDao = new DashboardDao();
                ProductsMst mst = new ProductsMst();
                mst.setProdPrinciId(principalId);
                mst.setProdPrinciName(princiName);
                mst.setProdPrinciPartno(Partnumber);
               
                mst.setProdStdPrice(standardPrice);
                mst.setProdCommRate(BigDecimal.ZERO);
                mst.setProdWieght(BigDecimal.ZERO);
                mst.setProdUnitCost(BigDecimal.ZERO);
                mst.setProdFamilyId(0);
                mst.setInsUser(lb.getUserId());
                mst.setUpdUser(lb.getUserId());
                mst.setInsDate(RFUtilities.getCurrentUTCDate());
                mst.setUpdDate(RFUtilities.getCurrentUTCDate());
                dashboardDao.perform(mst, DashboardDao.action.INSERT);
                

            }
        //  System.out.println("-----------validation--------" + validation + "-------update--------" + update);
        if (update) {
//            sdgfg;
            qry1 = new StringBuilder();
            qry1.append("update ProdCustomerPrice  set");
            qry.append(" updUser=").append(lb.getUserId());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String updDate = sdf.format(RFUtilities.getCurrentUTCDate());
            qry.append(", updDate='").append(updDate).append("'");
            qry.append(" where recId=").append(recId);

            qry1.append(qry);
            // System.out.println("-----------qry.toString()---------" + qry1.toString());
            if (!dao.updateQuery(qry1.toString(), DashboardDao.query.HQL)) {
                System.arraycopy(record, 0, errorRecord, 0, record.length);
                errorRecord[record.length] = "Unable to process record";
                List<Object> list = Arrays.asList(errorRecord);
                errorRecordList.add(list);
            }

        } 
        
        if (!validation) {
            System.arraycopy(record, 0, errorRecord, 0, record.length);
            errorRecord[record.length] = errorMsg.toString();
            List<Object> list = Arrays.asList(errorRecord);
            errorRecordList.add(list);
        }

    }
//#11050 CRM-6833   Customer Product Price: need excel export and an import button
    public CompBuyGroupMst compBuyingGroupName(String name) {
        Dao dao = new Dao();
        CompBuyGroupMst compBuyGroupMst = (CompBuyGroupMst) dao.qryUniqueResult("from CompBuyGroupMst where buyGroup='" + Dao.removeQuote(name) + "'", Dao.query.HQL);

        return compBuyGroupMst;
    }
//#11050 CRM-6833   Customer Product Price: need excel export and an import button
    public CompTierMst getCompTierName(String name) {
        Dao dao = new Dao();
        String qryStr = " from CompTierMst where compTier='" + Dao.removeQuote(name) + "'";
        CompTierMst compTierMst = (CompTierMst) dao.qryUniqueResult(qryStr, Dao.query.HQL);
        return compTierMst;
    }

//    PMS 1853
    public void getCompRows(Object[] record, Map<Integer, ExportField> header) {

        if (record.length <= 0 || header.keySet().isEmpty()) {
            return;
        }
        StringBuilder errorMsg = new StringBuilder();
//        PMS 3996: CRM-3615: Company Re-import issue:
        boolean mulFieldValidation = true;
        boolean mulFieldLenghtValidation = true;
        StringBuilder mulValue;
        StringBuilder mulValueLenghtFail;
        //#3864: CRM-4071: Invalid formatted address being saved if any of the address columns are missed
        String city = null;
        String state = null;
        String zipCode = null;
        String street = null;
        String country = null;
//        PMS 3996: CRM-3615: Company Re-import issue:
        Object[] errorRecord = new Object[record.length + 1];
        boolean validation = true;
        boolean update = false;
        boolean frmtAddrFlag = false;
        String parentCompName = null;
        boolean parentNameUpdateFlag = false;
        boolean parentCompIdUpdateFlag = false;
        List<CompProdPotentials> prodPotLst = new ArrayList<>();
        List<CompIndustries> compIndLst = new ArrayList<>();
        boolean updateGeoCor = false;
        boolean delProd = false;
        boolean delInd = false;
        boolean updProd = false;
        boolean updInd = false;

        if (record[0].toString() == null || record[0].toString().trim().length() == 0) {
//            PMS 3996: CRM-3615: Company Re-import issue:
            System.arraycopy(record, 0, errorRecord, 0, record.length);
            errorRecord[record.length] = "Company Id is Missing";
            List<Object> list = Arrays.asList(errorRecord);
            errorRecordList.add(list);
            return;
        }
        Integer compId = Integer.parseInt(record[0].toString());

        Integer parentCompId = 0;
        CompaniesDao compDao = new CompaniesDaoImpl();
        Companies cmp = compDao.getCompDtl(compId);
        ViewCompanies vComp;
        Companies comp = compDao.getCompDtl(compId);

        vComp = compDao.getViewCompDtls(compId);
        if (vComp == null) {
//            PMS 3996: CRM-3615: Company Re-import issue:
            System.arraycopy(record, 0, errorRecord, 0, record.length);
            errorRecord[record.length] = "Company Id does not exist";
            List<Object> list = Arrays.asList(errorRecord);
            errorRecordList.add(list);
            return;
        }
        StringBuilder qry = new StringBuilder();

        Dao dao = new Dao();
        qry.append("update Companies set");
        for (int i = 0; i < record.length; i++) {

            Object value = record[i];
            if (header.get(i).getType() == 1) {
//                System.out.println("========="+header.get(i).getFieldName());
            }

            switch (header.get(i).getFieldName()) {
                case "compName":
                    GlobalParams gp = (GlobalParams) GetCurrentObject.getBean("globalParams");
//                   #5486: normal company name not updating issue
                    if (gp.unifiedCompEnabled() && (!gp.hidePrsnCompInLst()) && ((cmp.getCompUnifiedId() != null) && (cmp.getCompUnifiedId() > 0))) {
                        break;
                    }
                    if (vComp.getCompName().equals(value.toString()) || ((cmp.getCompUnifiedId() != null) && (cmp.getCompUnifiedId() > 0))) {
                        break;
                    }
                    update = true;
                    if (value.toString().trim().length() == 0) {
                        validation = false;
                        errorMsg.append("Company name is blank;");
                    }
                    if (validateLength(value.toString(), 80)) {
                        Companies c = compDao.getCompaniesByName(value.toString());
                        if (c == null) {
                            qry.append(" compName= '").append(Dao.removeQuote(value.toString())).append("',");
                        } //                        PMS 3703CRM-4004: Re-Import - Says Company already exists
                        else if (c.getCompId().equals(vComp.getCompId())) {
                            qry.append(" compName= '").append(Dao.removeQuote(value.toString())).append("',");
                        } else {
                            validation = false;
                            errorMsg.append("Company name already exist; ");
                        }

                    } else {
                        validation = false;
                        errorMsg.append("Company name exceeds 80 characters; ");
                    }
                    break;
                case "compType":
                    break;
                case "compClass":
                    if (vComp.getClassName().equals(value.toString())) {
                        break;
                    }

                    if (value.toString().trim().length() == 0) {
                        if (vComp.getCompClass() != 0) {
                            update = true;
                            qry.append(" compClass= ").append(0).append(",");
                        }
                    } else if (validateLength(value.toString(), 80)) {
                        Integer id = (Integer) dao.qryUniqueResult("select compClassId from CompClassMst where compClassName = '" + Dao.removeQuote(value.toString()) + "'", Dao.query.HQL);
                        update = true;
                        if (id != null) {
                            qry.append(" compClass= ").append(id).append(",");
                        } else {
                            validation = false;
                            errorMsg.append("Class does not exist; ");
                        }

                    } else {
                        update = true;
                        validation = false;
                        errorMsg.append("Class exceeds 80 characters; ");
                    }
                    break;
                case "compCategory":
                    if (vComp.getCategoryName().equals(value.toString())) {
                        break;
                    }
                    if (value.toString().trim().length() == 0) {
                        if (vComp.getCompCategory() != 0) {
                            update = true;
                            qry.append(" compCategory= ").append(0).append(",");
                        }
                    } else if (validateLength(value.toString(), 40)) {
                        Integer id = (Integer) dao.qryUniqueResult("select compCatgId from CompanyCategoryMst where compCatgName = '" + Dao.removeQuote(value.toString()) + "'", Dao.query.HQL);
                        update = true;
                        if (id != null) {
                            qry.append(" compCategory= ").append(id).append(",");
                        } else {
                            validation = false;
                            errorMsg.append("Category does not exist; ");
                        }

                    } else {
                        validation = false;
                        errorMsg.append("Category exceeds 40 characters; ");
                    }
                    break;
                case "compStreet":
                    if (vComp.getCompAddress1() != null && value.toString().equals(vComp.getCompAddress1())) {

                        break;
                    }
                    update = true;
                    frmtAddrFlag = true;

                    street = value.toString().trim();
                    if (validateLength(value.toString(), 180)) {
                        qry.append(" compAddress1= '").append(Dao.removeQuote(street)).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Street exceeds 180 characters; ");
                    }
                    break;
                case "compCity":
                    if (vComp.getCompCity() != null && value.toString().equals(vComp.getCompCity())) {
                        // 11-07-2022 : #8427 : ESCALATION CRM-5880: Company Re-Import; Update Contact Region on condition
                        setCompCity(value.toString().trim());
//                        System.out.println("if condition is running city="+value.toString().trim());
                        break;
                    }
                    update = true;
                    frmtAddrFlag = true;

                    city = value.toString().trim();
                    if (validateLength(value.toString(), 40)) {
                        qry.append(" compCity= '").append(Dao.removeQuote(city)).append("',");
                        // 11-07-2022 : #8427 : ESCALATION CRM-5880: Company Re-Import; Update Contact Region on condition
                        setCompCity(city);
                    } else {
                        validation = false;
                        errorMsg.append("City exceeds 40 characters; ");
                    }
                    break;
                case "compState":
                    if (vComp.getCompState() != null && value.toString().trim().equals(vComp.getCompState())) {
                        break;
                    }

                    update = true;
                    frmtAddrFlag = true;

                    state = value.toString().trim();
                    if (validateLength(value.toString(), 40)) {
                        qry.append(" compState= '").append(Dao.removeQuote(state)).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("State exceeds 40 characters; ");
                    }
                    break;
                case "compZip":
                    if (vComp.getCompZipCode() != null && value.toString().equals(vComp.getCompZipCode())) {
                        break;
                    }
                    update = true;
                    frmtAddrFlag = true;

                    zipCode = value.toString().trim();
                    if (validateLength(value.toString(), 12)) {
                        qry.append(" compZipCode= '").append(Dao.removeQuote(zipCode)).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Zip code exceeds 12 characters; ");
                    }
                    break;
                case "compCountry":
                    if (vComp.getCompCountry() != null && value.toString().equals(vComp.getCompCountry())) {
                        break;
                    }
                    update = true;
                    frmtAddrFlag = true;
                    country = value.toString().trim();
                    if (validateLength(value.toString(), 40)) {
                        qry.append(" compCountryCode= '").append(Dao.removeQuote(country)).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Country exceeds 40 characters; ");
                    }
                    break;
                case "compCombinedAddr":
                    break;
                case "compPhone1":
                    if (vComp.getCompPhone1().equals(value.toString())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 40)) {
                        qry.append(" compPhone1= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        //09-01-2021:Task#2848:CRM-1756: Hofstetter: Custom Labels
                        errorMsg.append(customLabel.getLabels().get("IDS_COMP_PHONE1")).append("  exceeds 40 characters; ");
                    }
                    break;
                case "compPhone2":
                    if (vComp.getCompPhone2().equals(value.toString())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 40)) {
                        qry.append(" compPhone2= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        //09-01-2021:Task#2848:CRM-1756: Hofstetter: Custom Labels
                        errorMsg.append(customLabel.getLabels().get("IDS_COMP_PHONE2")).append(" exceeds 40 characters; ");
                    }
                    break;
                case "compFax":
                    if (vComp.getCompFax().equals(value.toString())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 20)) {
                        qry.append(" compFax= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Fax exceeds 20 characters; ");
                    }
                    break;
                case "compRegion":
                    CompanyRegionsMstDao regDao = new CompanyRegionsMstDaoImpl();
                    Integer id = regDao.compRegionByName(value.toString());
                    // 11-07-2022 : #8427 : ESCALATION CRM-5880: Company Re-Import; Update Contact Region on condition
                    setRegionId(id);
                    if ("".equals(value.toString().trim())) {
                        id = 0;
                        if (vComp.getCompRegionId() != 0) {
                            update = true;
                            qry.append(" compRegionId= ").append(id).append(",");
                        }
                    } else if (id > 0) {
                        if (vComp.getCompRegionId() != id) {
                            update = true;
                            qry.append(" compRegionId= ").append(id).append(",");
                        }

                    } else {
                        update = true;
                        validation = false;
                        errorMsg.append("Region name does not exist; ");

                    }
                    break;
                case "compWebSite":
                    if (vComp.getCompWebSite() != null && vComp.getCompWebSite().equals(value.toString())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 120)) {
                        String data = value.toString().trim();
                        data = formatWebSite(data);
                        if (validateWebSite(data)) {
                            qry.append(" compWebSite= '").append(Dao.removeQuote(data)).append("',");
                        } else if (value.toString().trim().length() == 0) {
                            qry.append(" compWebSite= '").append(Dao.removeQuote(data)).append("',");
                        } else {
                            validation = false;
                            errorMsg.append("Invalid Website; ");
                        }

                    } else {
                        validation = false;
                        errorMsg.append("Website exceeds 120 characters; ");
                    }
                    break;
                case "compCallPattern":

                    update = true;
                    if (value.toString().trim().length() == 0) {
                        update = true;
                        qry.append(" compCallPatternId= ").append(0).append(",");
                    } else if (validateLength(value.toString(), 30)) {
                        update = true;
                        Integer cId = (Integer) dao.qryUniqueResult("select recId from CallPatternsMst where callPattName='" + Dao.removeQuote(value.toString()) + "'", Dao.query.HQL);
                        if (cId != null) {
                            qry.append(" compCallPatternId= ").append(cId).append(",");
                        } else {
                            validation = false;
                            errorMsg.append("Call pattern does not exist; ");
                        }

                    } else {
                        validation = false;
                        errorMsg.append(" Call pattern 30 characters; ");
                    }
                    break;
                case "compParentId":
                    if (value.toString().trim().length() == 0) {
//                        parentCompId=compId;
                        parentCompIdUpdateFlag = true;
                        break;
                    }

                    parentCompIdUpdateFlag = true;
                    if (vComp.getCompId().equals(Integer.parseInt(value.toString()))) {
                        parentCompId = vComp.getCompId();
                        break;
                    }

                    parentCompId = Integer.parseInt(value.toString());
                    break;
                case "parentName":
                    if (value.toString().trim().length() == 0) {
                        parentCompName = value.toString().trim();
                        parentNameUpdateFlag = true;
                        break;
                    }
                    parentNameUpdateFlag = true;
                    update = true;
                    parentCompName = value.toString().trim();
                    break;
                case "compActiveFlag":
                    Integer actFlag = 0;
                    if ("active".equals(value.toString().trim().toLowerCase()) || value.toString().trim().length() == 0) {
                        actFlag = 1;
                    } else if ("inactive".equals(value.toString().trim().toLowerCase())) {
                        actFlag = 0;
                    }
                    if (!comp.getCompActiveFlag().equals(actFlag)) {
                        //PMS 3231: CRM-3849: TW TASK/BUG: reimport failed to take custom fields - no error message                        
                        update = true;
                        qry.append(" compActiveFlag= ").append(actFlag).append(",");
                    }
                    break;
//                    Feature #10976: CRM-6641   Update All Instances for RF Contacts - Update Golden Instance Contacts and Setting by harshithad on 29/03/23
                case "compForecastFlag":
                    Integer forecastFlag = 0;

                    if ("yes".equals(value.toString().trim().toLowerCase()) && comp.getCompTagged() == 1) {
                        forecastFlag = 1;
                    } else {
                        forecastFlag = 0;
                    }
                    if (!comp.getCompForecastFlag().equals(forecastFlag)) {
                        update = true;
                        qry.append(" compForecastFlag= ").append(forecastFlag).append(",");
                    }

                    break;
                case "compComments":
                    if (vComp.getCompComments().equals(value.toString())) {
                        break;
                    }
                    update = true;
                    qry.append(" compComments= '").append(Dao.removeQuote(value.toString())).append("',");
                    break;
                case "compTags":
                    if (vComp.getCompTags().equals(value.toString())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 150)) {
                        qry.append(" compTags= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Tags exceeds 150 characters; ");
                    }
                    break;
                case "compProdPotentials":
//                     System.out.println("value.toString():::"+value.toString());
                    String[] pot = value.toString().split(",");

//                    System.out.println("pot:::"+pot);
//                    PMS 3996: CRM-3615: Company Re-import issue:
                    mulValue = new StringBuilder();
                    mulValueLenghtFail = new StringBuilder();
                    mulFieldValidation = true;
                    mulFieldLenghtValidation = true;
                    update = true;
                    CompProdPotentials compPot = new CompProdPotentials();
                    if ("".equals(value.toString().trim())) {
                        delProd = true;
                        update = true;
                        break;
                    }
                    for (int j = 0; j < pot.length; j++) {
//                        if (vComp.getCallPattName().equals(value.toString())) {
//                            break;
//                        }
                        update = true;
                        delProd = true;
                        updProd = true;
                        if (validateLength(pot[j], 60)) {
                            Integer pId = (Integer) dao.qryUniqueResult("select recId from ProdPotentialsMst where prodPoteName='" + Dao.removeQuote(pot[j].trim()) + "'", Dao.query.HQL);
                            if (pId != null) {
                                compPot = new CompProdPotentials();
//                                prodPotQry=prodPotQry+pId+",";
                                compPot.setCompProdCompId(compId);
//                               Bug #5726:  CRM-4724: Company Sub-Type{s} - Import/Export Issue

                                compPot.setCompProdName(RFUtilities.trimSpaces(pot[j]));
                                compPot.setUpdUser(lb.getUserId());
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                String updDateProd = sdf.format(RFUtilities.getCurrentUTCDate());
                                try {
                                    compPot.setUpdDate(sdf.parse(updDateProd));

                                } catch (ParseException ex) {
                                    Logger.getLogger(BulkUpdateService.class
                                            .getName()).log(Level.SEVERE, null, ex);
                                }
                                prodPotLst.add(compPot);
                            } else {
//                                PMS 3996: CRM-3615: Company Re-import issue:
                                if (mulFieldValidation) {
                                    mulValue.append(pot[j].trim());
                                } else {
                                    mulValue.append(", ").append(pot[j].trim());
                                }
                                mulFieldValidation = false;

                            }

                        } else {
                            if (mulFieldLenghtValidation) {
                                mulValueLenghtFail.append(pot[j].trim());
                            } else {
                                mulValueLenghtFail.append(", ").append(pot[j].trim());
                            }
                            mulFieldLenghtValidation = false;

                        }
                    }
                    if (!mulFieldValidation) {
                        errorMsg.append(mulValue.toString()).append(" - Product Potential Does Not Exist;");
                        validation = false;
                    }
                    if (!mulFieldLenghtValidation) {
                        errorMsg.append(mulValueLenghtFail.toString()).append(" - Product Potential Exceeds 60 characters;");
                        validation = false;
                    }

                    break;
                case "compSalesTeam":
//                    if (vComp.getSmanName().equals(value.toString())) {
//                        break;
//                    }
//                     if (validateLength(value.toString(), 80)) {
//                        Integer id=(Integer)dao.qryUniqueResult("select compClassId from CompClassMst where compClassName = '" + value.toString()+"'", Dao.query.HQL);
//                         update = true;
//                        if(id!=null){
//                            qry.append(" compClass= ").append(id).append(",");
//                        }
//                        else{
//                            validation = false;
//                        errorMsg.append("Class does not exist; ");
//                        }
//                            
//                    } else {
//                         update = true;
//                        validation = false;
//                        errorMsg.append("Class exceeds 80 characters; ");
//                    }
                    break;
                case "compImpRefNo":
                    if (value.toString().equals(vComp.getCompRefNo())) {
                        break;
                    }
                    update = true;
                    if (validateLength(value.toString(), 50)) {
                        qry.append(" compImpRefNo= '").append(Dao.removeQuote(value.toString())).append("',");
                    } else {
                        validation = false;
                        errorMsg.append("Refference exceeds 50 characters; ");
                    }
                    break;
                case "insUser":
                    break;
                case "insDate":
                    break;
                case "updUser":
                    break;
                case "updDate":
                    break;
                case "compHdayCardFlag":
                    Integer hldFlag = 0;
                    if ("yes".equals(value.toString().trim().toLowerCase())) {
                        hldFlag = 1;
                    } else if ("no".equals(value.toString().trim().toLowerCase()) || value.toString().trim().length() == 0) {
                        hldFlag = 0;
                    }
                    if (!comp.getCompHdayCardFlag().equals(hldFlag)) {
//PMS 3231: CRM-3849: TW TASK/BUG: reimport failed to take custom fields - no error message
                        update = true;
                        qry.append(" compHdayCardFlag= ").append(hldFlag).append(",");
                    }
                    break;
                case "visitFrequency":
                    Integer visit;
                    if (value.toString().trim().length() == 0) {
                        visit = 0;
                    } else {
                        visit = Integer.parseInt(value.toString().trim());
                    }

                    if (cmp.getCompVisitFrequency() != null && cmp.getCompVisitFrequency().equals(visit)) {
                        break;
                    } else if (visit >= 0 && visit <= 999) {
                        update = true;
                        qry.append(" compVisitFrequency= ").append(visit).append(",");
                    } else {
                        validation = false;
                        errorMsg.append("Invalid visit frequency; ");
                    }
                    update = true;
                    break;
                case "compIndustryName":
                    String[] ind = value.toString().split(",");
//                    PMS 3996: CRM-3615: Company Re-import issue:
                    mulValue = new StringBuilder();
                    mulValueLenghtFail = new StringBuilder();
                    mulFieldValidation = true;
                    mulFieldLenghtValidation = true;
                    CompIndustries indust = new CompIndustries();

                    if ("".equals(value.toString().trim())) {
                        delInd = true;
                        update = true;
                        break;
                    }
                    for (int j = 0; j < ind.length; j++) {
//                        if (vComp.getCallPattName().equals(value.toString())) {
//                            break;
//                        }
                        delInd = true;
                        updInd = true;
                        update = true;
                        if (validateLength(ind[j], 60)) {
                            Integer pId = (Integer) dao.qryUniqueResult("select recID from IndustryMst where industryName='" + RFUtilities.trimSpaces(Dao.removeQuote(ind[j].trim())) + "'", Dao.query.HQL);
                            if (pId != null) {
//                                industryQry=industryQry+pId+",";
                                indust = new CompIndustries();
                                indust.setCompId(compId);
                                indust.setIndustryId(pId);
                                compIndLst.add(indust);
                            } else {
//                                PMS 3996: CRM-3615: Company Re-import issue:
                                if (mulFieldValidation) {
                                    mulValue.append(ind[j].trim());
                                } else {
                                    mulValue.append(", ").append(ind[j].trim());
                                }
                                mulFieldValidation = false;

                            }

                        } else {
                            if (mulFieldLenghtValidation) {
                                mulValueLenghtFail.append(ind[j].trim());
                            } else {
                                mulValueLenghtFail.append(", ").append(ind[j].trim());
                            }
                            mulFieldLenghtValidation = false;

                        }
                    }
                    if (!mulFieldValidation) {
                        errorMsg.append(mulValue.toString()).append(" - Industries Does Not Exist;");
                        validation = false;
                    }
                    if (!mulFieldLenghtValidation) {
                        errorMsg.append(mulValueLenghtFail.toString()).append(" - Industries Exceeds 60 characters;");
                        validation = false;
                    }

                    break;
                default:

                    break;

            }

        }

        if (frmtAddrFlag) {
            if (street == null) {
                street = vComp.getCompAddress1();
            }
            if (city == null) {
                city = vComp.getCompCity();
            }
            if (state == null) {
                state = vComp.getCompState();
            }
            if (zipCode == null) {
                zipCode = vComp.getCompZipCode();
            }
            if (country == null) {
                country = vComp.getCompCountry();
            }
            String addr1 = Alias.createCombinedAddr(street.trim(), city.trim(), state.trim(), zipCode.trim(), country.trim());
            //Bug #6640 address blank -->companies reimport map issue...

            if (!addr1.equals(vComp.getCompFormattedAddr()) && !"".equals(addr1)) {
                updateGeoCor = true;
                qry.append(" compFormattedAddr= '").append(Dao.removeQuote(addr1)).append("',");
            } else {

                Dao da = new Dao();
                updateGeoCor = false;
                StringBuilder q = new StringBuilder();

                q.append(" update COMPANIES set COMP_GEO_LATI = null");
                q.append(", COMP_GEO_LONGI = null , COMP_ADDR_INVALID = 1 , COMP_FORMATTED_ADDR = '' where COMP_ID = ").append(vComp.getCompId());
                da.qryExecute(q.toString(), Dao.query.SQL);

            }
        }

        if (parentNameUpdateFlag || parentCompIdUpdateFlag) {
            //PMS 3231: CRM-3849: TW TASK/BUG: reimport failed to take custom fields - no error message
            update = true;
            int compStatus = setCompany(parentCompId, parentCompName, vComp);
            switch (compStatus) {
                case -4:
                    qry.append(" compParentId=").append(compId).append(",");
                    break;
                case -1:
                    errorMsg.append(" Parent Company ID not found");
                    validation = false;
                    break;
                case -2:
                    errorMsg.append(" Parent Company not found");
                    validation = false;
                    break;
                case -3:
                    errorMsg.append(" Parent Company ID/Name mismatch");
                    validation = false;
                    break;
                case 0:
                    qry.append(" compParentId=").append(compId).append(",");
                    break;
                default:
                    qry.append(" compParentId=").append(compStatus).append(",");
                    break;

            }
        }

        if (validation && update) {
//            sdgfg;
            qry.append(" updUser=").append(lb.getUserId());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String updDate = sdf.format(RFUtilities.getCurrentUTCDate());
            qry.append(", updDate='").append(updDate).append("'");
            qry.append(" where compId=").append(compId);
            if (!dao.updateQuery(qry.toString(), Dao.query.HQL)) {
//PMS 3996: CRM-3615: Company Re-import issue:
                System.arraycopy(record, 0, errorRecord, 0, record.length);
                errorRecord[record.length] = "Unable to process record";
                List<Object> list = Arrays.asList(errorRecord);
                errorRecordList.add(list);
            } else {
                CompIndustriesDao indDao = new CompIndustriesDaoImpl();
                if (delInd) {
                    indDao.deleteById(compId);
                }
                if (delInd && updInd) {
                    indDao.save(compIndLst);
                }
                CompanyProductPotentialDao prodDao = new CompanyProductPotentialDao();
                if (delProd) {
                    prodDao.deleteByCompany(compId);
                }
                if (updProd && delProd) {
                    prodDao.addMultiplePotentials(prodPotLst);
                }
                if (updateGeoCor) {

                    updateGeoCordinates(compId);
                }
//    #5227: person company ->reimport-> some fields not updating
                if (cmp.getCompUnifiedId() != null && cmp.getCompUnifiedId() > 0) {
                    personCompUpdate(cmp.getCompId(), false);
                }
                // 11-07-2022 : #8427 : ESCALATION CRM-5880: Company Re-Import; Update Contact Region on condition
                if (!Objects.equals(vComp.getCompRegionId(), regionId)) {
                    updateCompRegions(compId, regionId, compCity);
                }
            }
//PMS 3231: CRM-3849: TW TASK/BUG: reimport failed to take custom fields - no error message
        } else if (update) {
//            PMS 3996: CRM-3615: Company Re-import issue:
            System.arraycopy(record, 0, errorRecord, 0, record.length);
            errorRecord[record.length] = errorMsg.toString();
            List<Object> list = Arrays.asList(errorRecord);
            errorRecordList.add(list);
        }

    }

    public void updateGeoCordinates(Integer companyId) {
        String[] arr;
        arr = lb.getEmailURL().split("/");
        String pageUrl = arr[0] + "//" + arr[2];

        String php_url = pageUrl + "/rf_api/update_comps_nearby.php?fn=1&compId=" + companyId;

        try {
            PageNavigation.requestPHP(php_url);

        } catch (Exception e) {
            Logger.getLogger(BulkUpdateService.class
                    .getName()).log(Level.SEVERE, null, e);
            //e.printStackTrace();
        }
    }

    private Integer setCompany(Integer ssCompId, String ssCompName, ViewCompanies vComp) {

//        System.out.println(ssCompId + " / " + ssCompName);
        boolean updateCompId = false, updateCompName = false;
        int companyGiven = 0;

        if (ssCompId != null) {
            companyGiven = companyGiven + 1;
            if (ssCompId != vComp.getCompId()) {
                updateCompId = true;
            }
        }

        if (ssCompName != null) {
            companyGiven = companyGiven + 2;
            if (!ssCompName.equals(vComp.getCompName())) {
                updateCompName = true;
            }
        }

        if (companyGiven == 0) {
            return -4;
        }

        if (!updateCompId && !updateCompName) {
            return -4;
        }
        CompaniesDao compDao = new CompaniesDaoImpl();

        String name;
        if (companyGiven == 1) {
            if (ssCompId == 0) { //if id is blank
                return ssCompId;
            }
            //check if company id exists and update
            name = compDao.getCompanyNameById(ssCompId);

            //if does not exist -Company ID not found
            if (name == null) {
                return -1;
            }

            return ssCompId;
        }

        Integer id;
        if (companyGiven == 2) {
            if ("".equals(ssCompName)) { // if name is blank
                return 0;
            }
            //check if company exists by name 
            id = compDao.getCompanyId(ssCompName);
            //if exists update
            //if not - Company does not exist
            if (id == 0) {
//                System.out.println("Company does not exist");
                return -2;
            }

            return id;
        }

        if (updateCompId = !updateCompName) {
            //then error out Company ID mismatch

            return -3;
        }

        if (ssCompId == 0 && "".equals(ssCompName)) { //if both are blank
            return 0;
        }

        //check if company exists by name and id
        boolean exists = compDao.isCompanyAvailable(ssCompId, ssCompName);
        if (!exists) {
            //if does not exist, then error out "Company ID mismatch"

            return -3;
        }

        //update contact company
        return ssCompId;
    }

    public String formatWebSite(String data) {
        if (data.contains("http://")) {
            data = data.replace("http://", "");
        }

        if (data.contains("https://")) {
            data = data.replace("https://", "");
        }

        int indx = data.indexOf('/');
        if (indx != -1 && indx == data.length() - 1) {
            data = data.substring(0, indx);
        }
        return data;
    }

    public boolean validateWebSite(String data) {
        return data.matches("(@)?(href=')?(HREF=')?(HREF=\")?(href=\")?(http://)?[a-zA-Z_0-9\\-]+(\\.\\w[a-zA-Z_0-9\\-]+)+(/[#&\\n\\-=?\\+\\%/\\.\\w]+)?");
    }

//    #5227: person company ->reimport-> some fields not updating
    private void personCompUpdate(Integer id, boolean isCont) {
        if (isCont) {
            Contacts contact = new ContactsDaoImpl().getContactDtls(id);
            StringBuilder qryUnified = new StringBuilder();
            qryUnified.append("update Companies set compName='").append(Dao.removeQuote(contact.getContFullName()))
                    .append("', compAddress1='").append(Dao.removeQuote(contact.getContBusiAddress1()))
                    .append("', compPoBox='").append(Dao.removeQuote(contact.getContBusiPoBox()))
                    .append("', compCity='").append(Dao.removeQuote(contact.getContBusiCity()))
                    .append("', compState='").append(Dao.removeQuote(contact.getContBusiState()))
                    .append("', compZipCode='").append(Dao.removeQuote(contact.getContBusiZipCode()))
                    .append("', compCountryCode='").append(Dao.removeQuote(contact.getContBusiCountry()))
                    .append("', compPhone1='").append(Dao.removeQuote(contact.getContPhoneBusiness()))
                    .append("', compPhone2='").append(Dao.removeQuote(contact.getContPhoneAlternate()))
                    .append("', compComments='").append(Dao.removeQuote(contact.getContNotes()))
                    .append("', compFax='").append(Dao.removeQuote(contact.getContFax()))
                    .append("', compFormattedAddr='").append(Dao.removeQuote(contact.getContBusiFormattedAddr()))
                    .append("', compRegionId=").append(contact.getContRegionId())
                    .append(updUser).append(lb.getUserId())
                    .append(", updDate=UTC_TIMESTAMP")
                    .append(" where compId=").append(contact.getContUnifiedId());
            new Dao().qryExecute(qryUnified.toString(), Dao.query.HQL);
        } else {
            Companies cmp = new CompaniesDaoImpl().getCompDtl(id);
            StringBuilder qryUnified = new StringBuilder();
            qryUnified.append("update Contacts set contBusiAddress1='").append(Dao.removeQuote(cmp.getCompAddress1()))
                    .append("', contBusiPoBox='").append(Dao.removeQuote(cmp.getCompPoBox()))
                    .append("', contBusiCity='").append(Dao.removeQuote(cmp.getCompCity()))
                    .append("', contBusiState='").append(Dao.removeQuote(cmp.getCompState()))
                    .append("', contBusiZipCode='").append(Dao.removeQuote(cmp.getCompZipCode()))
                    .append("', contBusiCountry='").append(Dao.removeQuote(cmp.getCompCountryCode()))
                    .append("', contPhoneBusiness='").append(Dao.removeQuote(cmp.getCompPhone1()))
                    .append("', contPhoneAlternate='").append(Dao.removeQuote(cmp.getCompPhone2()))
                    .append("', contNotes='").append(Dao.removeQuote(cmp.getCompComments()))
                    .append("', contFax='").append(Dao.removeQuote(cmp.getCompFax()))
                    .append("', contBusiFormattedAddr='").append(Dao.removeQuote(cmp.getCompFormattedAddr()))
                    .append("', contRegionId=").append(cmp.getCompRegionId())
                    .append(updUser).append(lb.getUserId())
                    .append(", updDate=UTC_TIMESTAMP")
                    .append(" where contId=").append(cmp.getCompUnifiedId());
            new Dao().qryExecute(qryUnified.toString(), Dao.query.HQL);
        }
    }
}
