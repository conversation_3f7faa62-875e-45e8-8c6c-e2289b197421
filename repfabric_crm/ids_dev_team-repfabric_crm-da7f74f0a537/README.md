Release notes:

Date : 10/12/2019
Version : V.3.0
commit id : 63015e87

**What's New:** 

- #2538: Related to CRM-1395: Contact Export: partial Christmas Card list
  #2490: CRM-1544: CRM Import: help file hidden
  #1379: MIGUAT-179: reports: relabel "User Activities" to "Salesperson Activity"
  #2521: Related - CRM-1521: Tutorial button landing urls - Sales Reports, Funnel Report
  #2485: Indicate Email Password Authentication Failure
  #2486: Updates to displaying change password on first login
  
- **BugFix**

  #2600: Transactions Import: Left Panel page navigation - filter retain issue in import log
  #2598: Updates to Import Log screen - Search filters
  #2021: CRM-1177 Doran associates: Remove link from printed reports
  #2603: CRM-1616 Wes-inc: ReAliasing distributor name doesn't change
  #2536: Contact Export - Remove duplicate Call Pattern
  #1695: Calendar>events>Invite> multiple growl message displayed
  #2535: Fix Issue - Flat menu is blank for new user

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-----------------------------------------------------------------------------------------------------------------------------------------------------------

Release notes:

Date : 11/12/2019
Version : V.3.0
commit id : af3bc8d1

**BugFix**

#2630:CRM-1628 Sa;es rep details report is a mess
#2638 CRM-1639 UnitySales: forecast missing quarterly date label on column header

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

--------------------------------------------------------------------------------------------------------------------------------------------------------

Release notes:

Date : 12/13/2019
Version : V.3.0
commit id : bcfc14ad

**BugFix**

#2666: CRM-1663: THORSON Fw: RF Report issues
#2623: Opportunity Report - Date conversion issue
#2258: CRM-1366: Fwd: Opportunity Reporting
#2181: Opp Reporting: Format issues

**What's New:** 

#2546: Updates to Re-Aliasing screen
#2650: CRM-1648: company phone2 deletions dont downflow if matching data

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-----------------------------------------------------------------------------------------------------------------------------------------------------------

Release notes:

Date : 12/14/2019
Version : V.3.0
commit id : 9103cf3a

**BugFix**

#2680:CRM-1688: Fwd: Import Transactions not working?

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

----------------------------------------------------------------------------------------------------------------------------------------------------------

Release notes:

Date : 12/17/2019
Version : V.3.0
commit id : 1ba81d30

**BugFix**

#2679  CRM-1691: Fwd: batch issues 
#2661: Import Log - Delete batch update issue
#2690: Opportunity - Emails -> Tool tip hidden
#2659: CRM-1641: prysm: contacts warning should show longer

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-----------------------------------------------------------------------------------------------------------------------------------------------------------

Release notes:

Date : 12/18/2019
Version : V.3.0
commit id : 947cb020

**BugFix**

#2665: Opportunity Details -> Opp Activity
#2645: CRM Import >Alias Contact: Filter not clear issue

**What's New:** 

#2720: CRM-1704: Fwd: Product Sales History screen
#2703: Related to CRM-1601: Who doesnt have sent items folder configured

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-----------------------------------------------------------------------------------------------------------------------------------------------------------

Release notes:

Date : 12/19/2019
Version : V.3.0
commit id : 602e2b38 

**BugFix**

#2728: CRM-1719: Salesforce CRMsync: Your new Your Developer Edition security token
#2590: Updates to Opp attachments - Delete Attachment

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-----------------------------------------------------------------------------------------------

Release notes:

Date : 12/24/2019
Version : V.3.0
commit id : b822ab27

**BugFix**

#2735: CRM-1726: Fwd: RE: Forecasts
#2741: CRM-1739 Fwd: cannot scroll down in emails??
#2749: CRM-1747: Fwd: Initial Caps on Email Addresses
#2751: CRM-1736: Autlogin for opps list page to learning.repfabric.com - please add
#2750: CRM-1741: Fwd: Opp - Q128793 Sentinel I28 Custom Configured Instrument - Urgent
#2647: CRM-1652: Fwd: Adding Contacts from AJ

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

---------------------------------------------------------------------------------------------

Release notes:

Date : 12/26/2019
Version : V.3.0
commit id : bbeeb5f4

**BugFix**

#2287: Data loaded confirmation coming up so early
#2668: Related to CRM-1662: prod1: can't find Rep Inc
#1800: Related to CRM-1040: FW: RepFabric 3.0 Issues
#1533: Companies> Website>Left panel>Company Summary design broken
#2677: Related to CRM-1661: MRBSales: hangs on sales team members
#2713: Data Management -> Import CRM Info -> Import Log
#2711: Opportunity Activity : Internal Task dialog unable to close when clicked on Cancel button

[ALREADY DEPLOYED]
#2627: Related to CRM-1177: Doran associates: Remove link from printed reports
#1552: INT_950- 1468 -Sales Teams>sales Team Split
#1338: MIGUAT-172: All pages:get create button on all pages
#2258: CRM-1366: Fwd: Opportunity Reporting

**What's New:** 

#2739: Related to CRM-1646: Golden updates needed: Dev Ops - Opportunity ‘Stage’
#2725: CRM-1627: Fwd: Shared Commission Splits 
#2755: CRM-1717: Golden Instance: change thumbs up to x

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-----------------------------------------------------------------------------------------------

Release notes:

Date : 12/31/2019
Version : V.3.0
commit id : 5fc7db02

**BugFix**

#2712: Opportunity Activity Actions: Unable to create task
#2770: Related to CRM-1652: Fwd: Adding Contacts from AJ
#2788: CRM Import - Absorb contacts
#2781: Application Settings > configuration and custom labels tab
#2771: Create> Email module : Open in new tab via mouse context menu navigates back to parent page
#2724: Alias Contacts Filter by Batch # : Filters records by previously selected batch #
#2738: CRM-1732: Soderholmrep: screen shakes
#2734: CRM-1728: Fwd: Adding a New Contact Record
[ALREADY DEPLOYED]
#2258: CRM-1366: Fwd: Opportunity Reporting

**What's New:** 

#2780: PMT-207 Fwd: Revising Company Records
#2733: CRM-1729: Fwd: Cosmetic Changes
#2727: Related to CRM-1646: Golden updates needed: Dev Ops - Default Sync+ Token
#2726: Related to CRM-1646: Golden updates needed: Dev Ops -Default user custom feature

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

------------------------------------------------------------------------------------------------

Release notes:

Date : 1/2/2020
Version : V.3.0
commit id : 814fb5d3

**BugFix**

#2734: CRM-1728: Fwd: Adding a New Contact Record
#2812: Contact Details>First Name, Last name Not auto capitalises first letter on user value entry

**What's New:** 

#2784: Add Projected Commission in Bulk Reconcile - Quick Entry dialog
#2330:Bulk Reconcile - Option to save selected records for a user
#2815: CRM-1767: Put 'Do not reply' message on password recovery email
#2780: PMT-207 Fwd: Revising Company Records
#2756: CRM-1746: Fwd: Upper/Lower Case Restriction
#2774: UI Updates for Comm. Data Overview
#2593: Updates to Commission Data Overview - Design Update
#2704: Data Management - Sales Data Overview

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-------------------------------------------------------------------------------------------------

Release notes:

Date : 1/6/2020
Version : V.3.0
commit id : 4312caaa

**BugFix**

#2848: CRM-1790: Hawkins: Fw: Quote total
#2826: Bulk Reconcile : Balance Amount calculation issue
#2830: Tasks: Column Filter issue

[already deployed]
2719: CRM-1711: Fwd: There's more

**What's New:** 

#2773: Related to CRM-1646: Golden updates needed: Dev Ops - Sales team Split

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-------------------------------------------------------------------------------------------------

Release notes:

Date : 1/7/2020
Version : V.3.0
commit id : 4312caaa

**What's New:** 

#2855: Related to CRM-1781: The Agency - Alias self created problem
#2742: ZOHO CRM REST API Integration into Repfabric

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-------------------------------------------------------------------------------------------------

Release notes:

Date : 1/10/2020
Version : V.3.0
commit id : add3ec87

**BugFix**

#2888: CRM-1810: Fwd: FW: Pre-Canned Pitch Image Issue
#2890: CRM-1808: Fwd: Silicon Labs - 6 lines stuck in Alias
#2835: CRM-1776: rbsales: cant edit quick entry commission
#2825: Commissionable Transaction: Line Items Number field input Restrictions

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

------------------------------------------------------------------------------------------------

Release notes:

Date : 1/13/2020
Version : V.3.0
commit id : 1e0e5839

**BugFix**

#2866: CRM-1793: Map feature when created from aliasing

**What's New:** 

#2764: Related to CRM-1710: Data Management -> Internal PN Splits -> Reprocess Split
#2858: CRM-1778: companylist: add active/inactive left side filter

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-------------------------------------------------------------------------------------------------

Release notes:

Date : 1/15/2020
Version : V.3.0
commit id : eb0475c0

**BugFix**

#2924: CRM-1830: Fwd: Update?
#2882: CRM-1802: Southwest: sync salesteams not showing all salesteam membership
#2922: Sales Team : Logged in user unable to select Sales team/view team members
#2914: CRM-1820: Fwd: Repfabric / Proxy Error When Trying to Sync with STMicro SFDC

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-------------------------------------------------------------------------------------------------

Release notes:

Date : 1/17/2020
Version : V.3.0
commit id : 322ca9aa

**BugFix**

#2931: CRM-1837: Rendell: secondary coming out of no where

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-------------------------------------------------------------------------------------------------



------------------------------------------------------------------------------------------------

Release notes:

Date : 1/20/2020
Version : V.3.0
commit id : 11587e95

**BugFix**

#2934: CRM-1841: Fwd: Sample Request form
#2940: CRM-1847: Golden: custom field with multi select is awkward
#2939: CRM-1846: Golden: opp stage doesn't follow from default list
#2893: Assign User -> Login Name - Remove lead and trailing spaces on save
#2898: CRM-1817: Fwd: Error with Quotations
#2887: Company Details -> Remove ajax on sales team selection if not necessary
#2954: Related to - CRM-1848: Hawkins: enable copy to quotes etc
#2919: Related to CRM-1819 Fwd: Email not working from app

**What's New:** 

#2872: CRM-1799: Fwd: Filter Companies Export
#2224:Updates to Sales Import - Comm. Rate, Invoice Date selector

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

------------------------------------------------------------------------------------------------

Release notes:

Date : 1/21/2020
Version : V.3.0
commit id : 3e5e4f50

**BugFix**

#2976: CRM-1879: Unable to add an unpaid invoice through Quick Entry

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-----------------------------------------------------------------------------------------------

Release notes:

Date : 1/22/2020
Version : V.3.0
commit id : b91f5550

**BugFix**

#2637: CRM-1638 UnitySales: project update wipes out values
#2986: Company Export Filter - Product Potentials in Export field list
#2985: Invoice number is not displaying in Commissionable Transaction> Line Items Detail

**What's New:** 

#2941: CRM-1845: Change wording on user custom features

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

-----------------------------------------------------------------------------------------------

Release notes:

Date : 1/23/2020
Version : V.3.0
commit id : b91f5550

**BugFix**

#2909: Sales Rep Details report : should not include an extra sales team filter.
#2884: Sales Rep Summary report : should not include an extra sales team filter.
#2943: Secondary Alias browser > Delete any record > Confirmation dialog
#2859: Import Transactions - Import Log - Refresh issue
#2421: Updates to Sales/Comm By Month Report

**What's New:** 

#2932: CRM-1815: Fwd: Sales Comparison Reports
#2975: #2975:Related to CRM-1872: Fwd: Repfabric / Re-Aliasing / Principal Lookup
#2842: Map Updates - Filters on Map
#2910: Contact Details: PO box clear issue
#2808: JSF - Add PO Box to Contact Import template.
#2806: JSF - Down flow of company PO Box to Contact Business PO Box
#2807: JSF - Add PO Box to Contact Export fields list
#2805: JSF - Add PO Box below Street in Contact Details -> Basic and Personal tab
#2754: Change Data source to WEBAPP on creation of Companies, Contacts, Opportunities
#2854: Related to CRM-1757: Hoffstetter: Product Potential vs. Contact Group
#2624: CRM-977: Kai: Allow for field relabel under custom relabel for Opp Owner
#2245: Label Update: Related to CRM-1349: 3.0 API potential % does not show in Sync+

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

--------------------------------------------------------------------------------------------

Release notes:

Date : 1/24/2020
Version : V.3.0
commit id : 0f8572b0

**BugFix**

#2963: CRM-1867: Fwd: FW: Issue with Parent Company Field Populating When Making Changes to a Customer
#2959: Company Details -> Check box value not sticking
#2860: CRM Sync : Growl Message, UI issue
#2844: CRM Sync: Delay loading opps and tab set to 'Basic Tab'
#2742: ZOHO CRM REST API Integration into Repfabric
#2328: Related to 1113 - 36 -2 - Activity Journals not listed for Owner
#2371: CRM-1452 RALUCASCO: Activity Journal view logic

**Above changes are deployed to the instances below:**

Repfabric-Production, demo

------------------------------------------------------------------------------------------------