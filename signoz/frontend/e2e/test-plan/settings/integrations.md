# Integrations E2E Scenarios (Updated)

## 1. View List of Available Integrations

- **Precondition:** User is logged in
- **Steps:**
  1. Navigate to Integrations
- **Expected:** List of integrations is displayed, each with a name, description, and 'Configure' button

## 2. Search Integrations by Name/Type

- **Precondition:** Integrations exist
- **Steps:**
  1. Enter search/filter criteria in the 'Search for an integration...' box
- **Expected:** Only matching integrations are shown

## 3. Connect a New Integration

- **Precondition:** User is admin
- **Steps:**
  1. Click 'Configure' for an integration
  2. Complete the configuration flow (modal or page, as available)
- **Expected:** Integration is connected/configured (UI feedback/confirmation should be checked)

## 4. Disconnect an Integration

- **Note:** No visible 'Disconnect' button in the main list. This may be available in the configuration flow for a connected integration.

## 5. Configure Integration Settings

- **Note:** Configuration is handled in the flow after clicking 'Configure' for an integration.

## 6. Test Integration Connection

- **Note:** No visible 'Test Connection' button in the main list. This may be available in the configuration flow.

## 7. View Integration Status/Logs

- **Note:** No visible status/logs section in the main list. This may be available in the configuration flow.

## 8. Filter Integrations by Category

- **Note:** No explicit category filter in the current UI, only a search box.

## 9. View Integration Documentation/Help

- **Note:** No visible 'Help/Docs' button in the main list. This may be available in the configuration flow.

## 10. Update Integration Configuration

- **Note:** Configuration is handled in the flow after clicking 'Configure' for an integration.
