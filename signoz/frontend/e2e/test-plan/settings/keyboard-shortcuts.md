# Keyboard Shortcuts E2E Scenarios (Updated)

## 1. View Keyboard Shortcuts

- **Precondition:** User is logged in
- **Steps:**
  1. Navigate to Keyboard Shortcuts
- **Expected:** Shortcuts are displayed in categorized tables (Global, Logs Explorer, Query Builder, Dashboard)

## 2. Customize Keyboard Shortcuts (if supported)

- **Note:** Customization is not available in the current UI. Shortcuts are view-only.

## 3. Use Keyboard Shortcuts for Navigation/Actions

- **Precondition:** User is logged in
- **Steps:**
  1. Use shortcut for navigation/action (e.g., shift+s for Services, cmd+enter for running query)
- **Expected:** Navigation/action is performed as per shortcut
