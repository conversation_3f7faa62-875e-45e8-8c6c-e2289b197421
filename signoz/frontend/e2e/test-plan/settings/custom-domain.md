# Custom Domain E2E Scenarios (Updated)

## 1. Add or Update Custom Domain

- **Precondition:** User is admin
- **Steps:**
  1. Click 'Customize team’s URL' button
  2. In the 'Customize your team’s URL' dialog, enter the preferred subdomain
  3. Click 'Apply Changes'
- **Expected:** Domain is set/updated for the team (UI feedback/confirmation should be checked)

## 2. Verify Domain Ownership

- **Note:** No explicit 'Verify' button or flow is present in the current UI. If verification is required, it may be handled automatically or via support.

## 3. Remove a Custom Domain

- **Note:** No explicit 'Remove' button or flow is present in the current UI. The only available action is to update the subdomain.
