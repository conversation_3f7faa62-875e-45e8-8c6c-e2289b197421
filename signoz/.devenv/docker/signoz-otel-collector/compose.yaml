services:
  signoz-otel-collector:
    image: signoz/signoz-otel-collector:v0.128.2
    container_name: signoz-otel-collector-dev
    command:
      - --config=/etc/otel-collector-config.yaml
      - --feature-gates=-pkg.translator.prometheus.NormalizeName
    volumes:
      - ./otel-collector-config.yaml:/etc/otel-collector-config.yaml
    environment:
      - OTEL_RESOURCE_ATTRIBUTES=host.name=signoz-host,os.type=linux
      - LOW_CARDINAL_EXCEPTION_GROUPING=false
    ports:
      - "4317:4317" # OTLP gRPC receiver
      - "4318:4318" # OTLP HTTP receiver
      - "13133:13133" # health check extension
    healthcheck:
      test:
        - CMD
        - wget
        - --spider
        - -q
        - localhost:13133
      interval: 30s
      timeout: 5s
      retries: 3
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"