name: jsci

on:
  pull_request:
    branches:
      - main
  pull_request_target:
    types:
      - labeled

jobs:
  tsc:
    if: |
      (github.event_name == 'pull_request' && ! github.event.pull_request.head.repo.fork && github.event.pull_request.user.login != 'dependabot[bot]' && ! contains(github.event.pull_request.labels.*.name, 'safe-to-test')) ||
      (github.event_name == 'pull_request_target' && contains(github.event.pull_request.labels.*.name, 'safe-to-test'))
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v4
      - name: install
        run: cd frontend && yarn install
      - name: tsc
        run: cd frontend && yarn tsc
  test:
    if: |
      (github.event_name == 'pull_request' && ! github.event.pull_request.head.repo.fork && github.event.pull_request.user.login != 'dependabot[bot]' && ! contains(github.event.pull_request.labels.*.name, 'safe-to-test')) ||
      (github.event_name == 'pull_request_target' && contains(github.event.pull_request.labels.*.name, 'safe-to-test'))
    uses: signoz/primus.workflows/.github/workflows/js-test.yaml@main
    secrets: inherit
    with:
      PRIMUS_REF: main
      JS_SRC: frontend
  fmt:
    if: |
      (github.event_name == 'pull_request' && ! github.event.pull_request.head.repo.fork && github.event.pull_request.user.login != 'dependabot[bot]' && ! contains(github.event.pull_request.labels.*.name, 'safe-to-test')) ||
      (github.event_name == 'pull_request_target' && contains(github.event.pull_request.labels.*.name, 'safe-to-test'))
    uses: signoz/primus.workflows/.github/workflows/js-fmt.yaml@main
    secrets: inherit
    with:
      PRIMUS_REF: main
      JS_SRC: frontend
  lint:
    if: |
      (github.event_name == 'pull_request' && ! github.event.pull_request.head.repo.fork && github.event.pull_request.user.login != 'dependabot[bot]' && ! contains(github.event.pull_request.labels.*.name, 'safe-to-test')) ||
      (github.event_name == 'pull_request_target' && contains(github.event.pull_request.labels.*.name, 'safe-to-test'))
    uses: signoz/primus.workflows/.github/workflows/js-lint.yaml@main
    secrets: inherit
    with:
      PRIMUS_REF: main
      JS_SRC: frontend
