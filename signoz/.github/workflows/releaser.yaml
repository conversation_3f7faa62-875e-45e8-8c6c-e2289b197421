name: releaser

on:
  # trigger on new latest release
  release:
    types: [published]

jobs:
  detect:
    if: ${{ !startsWith(github.event.release.tag_name, 'histogram-quantile/') }}
    runs-on: ubuntu-latest
    outputs:
      release_type: ${{ steps.find.outputs.release_type }}
    steps:
      - id: find
        name: find
        run: |
          release_tag=${{ github.event.release.tag_name }}
          patch_number=$(echo $release_tag | awk -F. '{print $3}')
          release_type="minor"
          if [[ $patch_number -ne 0 ]]; then
            release_type="patch"
          fi
          echo "release_type=${release_type}" >> "$GITHUB_OUTPUT"
  charts:
    if: ${{ !startsWith(github.event.release.tag_name, 'histogram-quantile/') }}
    uses: signoz/primus.workflows/.github/workflows/github-trigger.yaml@main
    secrets: inherit
    needs: [detect]
    with:
      PRIMUS_REF: main
      GITHUB_REPOSITORY_NAME: charts
      GITHUB_EVENT_NAME: prereleaser
      GITHUB_EVENT_PAYLOAD: "{\"release_type\": \"${{ needs.detect.outputs.release_type }}\"}"
