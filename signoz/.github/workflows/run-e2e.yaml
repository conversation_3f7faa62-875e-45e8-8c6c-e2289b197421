name: e2eci

on:
  workflow_dispatch:
    inputs:
      userRole:
        description: "Role of the user (ADMIN, EDITOR, VIEWER)"
        required: true
        type: choice
        options:
          - ADMIN
          - EDITOR
          - VIEWER

jobs:
  test:
    name: Run Playwright Tests
    runs-on: ubuntu-latest
    timeout-minutes: 60

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Mask secrets and input
        run: |
          echo "::add-mask::${{ secrets.BASE_URL }}"
          echo "::add-mask::${{ secrets.LOGIN_USERNAME }}"
          echo "::add-mask::${{ secrets.LOGIN_PASSWORD }}"
          echo "::add-mask::${{ github.event.inputs.userRole }}"

      - name: Install dependencies
        working-directory: frontend
        run: |
          npm install -g yarn
          yarn

      - name: Install Playwright Browsers
        working-directory: frontend
        run: yarn playwright install --with-deps

      - name: Run Playwright Tests
        working-directory: frontend
        run: |
          BASE_URL="${{ secrets.BASE_URL }}" \
          LOGIN_USERNAME="${{ secrets.LOGIN_USERNAME }}" \
          LOGIN_PASSWORD="${{ secrets.LOGIN_PASSWORD }}" \
          USER_ROLE="${{ github.event.inputs.userRole }}" \
          yarn playwright test

      - name: Upload Playwright Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: frontend/playwright-report/
          retention-days: 30
