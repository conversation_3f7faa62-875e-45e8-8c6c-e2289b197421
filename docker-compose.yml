services:
  mariadb:
    image: mariadb:latest
    container_name: repfab-mariadb
    environment:
      MYSQL_ROOT_PASSWORD: asdf1234
      MYSQL_DATABASE: lookup_db
      MYSQL_USER: admin
      MYSQL_PASSWORD: 1234
      MYSQL_INITDB_SKIP_TZINFO: 1 # Optional, skips time zone info to speed up initialization
    ports:
      - "3306:3306"
    volumes:
      - mariadb_data_14:/var/lib/mysql
      - "./rf_legacy_repo _src_code/Mariadb/init.sql:/docker-entrypoint-initdb.d/init.sql"
      - "./rf_legacy_repo _src_code/Mariadb/TEMPLATE_ARC_0.sql:/var/lib/mysql/TEMPLATE_ARC_0.sql"
      - "./rf_legacy_repo _src_code/Mariadb/TEMPLATE_CRM_0.sql:/var/lib/mysql/TEMPLATE_CRM_0.sql"
      - "./rf_legacy_repo _src_code/Mariadb/TEMPLATE_DBD_0.sql:/var/lib/mysql/TEMPLATE_DBD_0.sql"
      - "./rf_legacy_repo _src_code/Mariadb/TEMPLATE_EML_0.sql:/var/lib/mysql/TEMPLATE_EML_0.sql"
      - "./rf_legacy_repo _src_code/Mariadb/sandbox.local.RF_ARC_DB_0.sql:/var/lib/mysql/sandbox.local.RF_ARC_DB_0.sql"
      - "./rf_legacy_repo _src_code/Mariadb/sandbox.local.RF_CRM_DB_0.sql:/var/lib/mysql/sandbox.local.RF_CRM_DB_0.sql"
      - "./rf_legacy_repo _src_code/Mariadb/sandbox.local.RF_DBD_DB_0.sql:/var/lib/mysql/sandbox.local.RF_DBD_DB_0.sql"
      - "./rf_legacy_repo _src_code/Mariadb/sandbox.local.RF_EML_DB_0.sql:/var/lib/mysql/sandbox.local.RF_EML_DB_0.sql"
      - "./rf_legacy_repo _src_code/Mariadb/TEMPLATE_ARC_28.sql:/var/lib/mysql/TEMPLATE_ARC_28.sql"
      - "./rf_legacy_repo _src_code/Mariadb/TEMPLATE_CRM_28.sql:/var/lib/mysql/TEMPLATE_CRM_28.sql"
      - "./rf_legacy_repo _src_code/Mariadb/TEMPLATE_DBD_28.sql:/var/lib/mysql/TEMPLATE_DBD_28.sql"
      - "./rf_legacy_repo _src_code/Mariadb/TEMPLATE_EML_28.sql:/var/lib/mysql/TEMPLATE_EML_28.sql"
      - "./rf_legacy_repo _src_code/Mariadb/sandbox.local.RF_ARC_DB_28.sql:/var/lib/mysql/sandbox.local.RF_ARC_DB_28.sql"
      - "./rf_legacy_repo _src_code/Mariadb/sandbox.local.RF_CRM_DB_28.sql:/var/lib/mysql/sandbox.local.RF_CRM_DB_28.sql"
      - "./rf_legacy_repo _src_code/Mariadb/sandbox.local.RF_DBD_DB_28.sql:/var/lib/mysql/sandbox.local.RF_DBD_DB_28.sql"
      - "./rf_legacy_repo _src_code/Mariadb/sandbox.local.RF_EML_DB_28.sql:/var/lib/mysql/sandbox.local.RF_EML_DB_28.sql"
      - "./rf_legacy_repo _src_code/Mariadb/TEMPLATE_ARC_60.sql:/var/lib/mysql/TEMPLATE_ARC_60.sql"
      - "./rf_legacy_repo _src_code/Mariadb/TEMPLATE_CRM_60.sql:/var/lib/mysql/TEMPLATE_CRM_60.sql"
      - "./rf_legacy_repo _src_code/Mariadb/TEMPLATE_DBD_60.sql:/var/lib/mysql/TEMPLATE_DBD_60.sql"
      - "./rf_legacy_repo _src_code/Mariadb/TEMPLATE_EML_60.sql:/var/lib/mysql/TEMPLATE_EML_60.sql"
      - "./rf_legacy_repo _src_code/Mariadb/sandbox.local.RF_ARC_DB_60.sql:/var/lib/mysql/sandbox.local.RF_ARC_DB_60.sql"
      - "./rf_legacy_repo _src_code/Mariadb/sandbox.local.RF_CRM_DB_60.sql:/var/lib/mysql/sandbox.local.RF_CRM_DB_60.sql"
      - "./rf_legacy_repo _src_code/Mariadb/sandbox.local.RF_DBD_DB_60.sql:/var/lib/mysql/sandbox.local.RF_DBD_DB_60.sql"
      - "./rf_legacy_repo _src_code/Mariadb/sandbox.local.RF_EML_DB_60.sql:/var/lib/mysql/sandbox.local.RF_EML_DB_60.sql"
    networks:
      - repfabric

  ubuntu:
    container_name: repfab-java
    build:
      context: .
      dockerfile: Dockerfile_CRM
    ports:
      - "8080:8080"
      - "82:80"
    volumes:
      - ./repfabric_crm:/app/repfabric_crm
    networks:
      - repfabric

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: phpmyadmin
    restart: always
    ports:
      - "8083:80"
    environment:
      PMA_HOST: mariadb
      MYSQL_ROOT_PASSWORD: asdf1234
      PMA_ARBITRARY: 1
    depends_on:
      - mariadb
    networks:
      - repfabric

volumes:
  mariadb_data_14:

networks:
  repfabric:
    external: true
    driver: bridge
