// Database connection pool manager for multi-tenant setup using mysql2/promise
const mysql = require('mysql2/promise');
const { maskValue } = require('../src/middleware/tenant_dbconnection/TenantDbCommonFunctions');

/**
 * Sets up a MySQL connection pool for the current tenant.
 * Only logs DB variables in non-production environments for security.
 * @param {Object} dbVars - Database connection variables for the tenant.
 * @property {string} dbVars.HOST - Database host.
 * @property {string} dbVars.tenantId - Tenant identifier (used in DB name).
 * @property {string} dbVars.UNAME - Database username.
 * @property {string} dbVars.PASS - Database password.
 */
async function setTenantPool(dbVars)
{
    // Log masked DB variables only if not in production for debugging purposes
    if (process.env.NODE_ENV !== 'production')
    {
        // Mask sensitive values in dbVars for logging
        const maskedDbVars = { ...dbVars };
        
        if (maskedDbVars.PASS)
        {
            maskedDbVars.PASS = maskValue(maskedDbVars.PASS);
        }
        if (maskedDbVars.UNAME)
        {
            maskedDbVars.UNAME = maskValue(maskedDbVars.UNAME);
        }
        if (maskedDbVars.HOST)
        {
            maskedDbVars.HOST = maskValue(maskedDbVars.HOST);
        }
        if (maskedDbVars.tenantId)
        {
            maskedDbVars.tenantId = maskedDbVars.tenantId;
        }
        if (maskedDbVars.PORT)
        {
            maskedDbVars.PORT = maskValue(String(maskedDbVars.PORT));
        }

        // console.log('Setting tenant pool with masked DB variables:', maskedDbVars)
    }

    // Create a new MySQL connection pool for the tenant
    pool = mysql.createPool({
        host: dbVars.HOST,
        database: 'RF_CRM_DB_' + dbVars.tenantId,
        user: dbVars.UNAME,
        password: dbVars.PASS,
        waitForConnections: true,
        connectionLimit: 25,
        queueLimit: 0
    });
}

/**
 * Executes a SQL query using the current tenant's connection pool.
 * @param {string} sql - The SQL query to execute.
 * @param {Array} params - The parameters for the SQL query.
 * @returns {Promise<Array>} - The query results.
 */
async function query(sql, params)
{
    let connection;
    try
    {
        // Get a connection from the pool
        connection = await pool.getConnection();
        
        // Execute the query with provided parameters
        const [results] = await connection.execute(sql, params);
        return results;
    }
    catch (error)
    {
        // Propagate any errors
        throw error;
    }
    finally
    {
        // Always release the connection back to the pool
        if (connection)
        {
            connection.release();
        }
    }
}

// Export the pool setter and query executor for use in other modules
module.exports = {
    setTenantPool,
    query
};