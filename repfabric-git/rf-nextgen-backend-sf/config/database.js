require('dotenv').config()

module.exports = {
    development: {
        username: process.env.DB_USERNAME,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        host: process.env.DB_HOST,
        dialect: 'mysql',
        // dialectOptions: {
        //     typeCast: function (field, next) {
        //         if (field.type == 'DATETIME') {
        //             return new Date(field.string() + 'Z')
        //         }
        //         return next()
        //     },
        // },
        // eslint-disable-next-line
        logging: console.log,
    },
    test: {
        username: process.env.DB_USERNAME,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        host: process.env.DB_HOST,
        dialect: 'mysql',
        // dialectOptions: {
        //     typeCast: function (field, next) {
        //         if (field.type == 'DATETIME') {
        //             return new Date(field.string() + 'Z')
        //         }
        //         return next()
        //     },
        // },
        logging: false
    },
    production: {
        username: process.env.DB_USERNAME,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        host: process.env.DB_HOST,
        dialect: 'mysql',
        // dialectOptions: {
        //     typeCast: function (field, next) {
        //         if (field.type == 'DATETIME') {
        //             return new Date(field.string() + 'Z')
        //         }
        //         return next()
        //     }
        // },
        logging: false
    }
}
