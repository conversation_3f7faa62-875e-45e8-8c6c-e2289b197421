  name: Build and Deploy by Environment

  on:
    push:
      branches:
        - development
        - qa
        - release/*
        - main

  jobs:
    deploy:
      runs-on: eks-small-apg-runner-set
      permissions:
        id-token: write
        contents: write
      steps:
        - name: Checkout code
          uses: actions/checkout@v3

        - name: Set variables based on branch
          id: set-vars
          run: |
            BRANCH_NAME="${GITHUB_REF##refs/heads/}"
            echo "Detected branch: $BRANCH_NAME"

            if [[ "$BRANCH_NAME" == "development" ]]; then
              ENV="dev"
              AWS_ACCOUNT_ID="************"
              AWS_REGION="us-east-1"
              HELM_BRANCH="dev"
            elif [[ "$BRANCH_NAME" == "qa" ]]; then
              ENV="qa"
              AWS_ACCOUNT_ID="************"
              AWS_REGION="us-west-2"
              HELM_BRANCH="qa"
            elif [[ "$BRANCH_NAME" == release/* ]]; then
              ENV="stg"
              AWS_ACCOUNT_ID="************"
              AWS_REGION="us-west-2"
              HELM_BRANCH="stg"
            elif [[ "$BRANCH_NAME" == "main" ]]; then
              ENV="prod"
              AWS_ACCOUNT_ID="************"
              AWS_REGION="us-east-1"
              HELM_BRANCH="main"
            else
              echo "Unsupported branch: $BRANCH_NAME"
              exit 1
            fi

            ECR_REPO="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/rf-${ENV}-node"
            IMAGE_TAG="${ENV}-node-backend-$(date +%Y%m%d%H%M%S)"
            NODE_IMAGE="${ECR_REPO}:node-lts"

            echo "env_name=${ENV}" >> $GITHUB_OUTPUT
            echo "aws_region=${AWS_REGION}" >> $GITHUB_OUTPUT
            echo "ecr_repo=${ECR_REPO}" >> $GITHUB_OUTPUT
            echo "image_tag=${IMAGE_TAG}" >> $GITHUB_OUTPUT
            echo "aws_account_id=${AWS_ACCOUNT_ID}" >> $GITHUB_OUTPUT
            echo "helm_branch=${HELM_BRANCH}" >> $GITHUB_OUTPUT
            echo "node_image=${NODE_IMAGE}" >> $GITHUB_OUTPUT

        - name: Configure credentials for ${{ steps.set-vars.outputs.env_name }} environment
          uses: aws-actions/configure-aws-credentials@v2
          with:
            role-to-assume: arn:aws:iam::${{ steps.set-vars.outputs.aws_account_id }}:role/github-actions-role
            aws-region: ${{ steps.set-vars.outputs.aws_region }}
            role-session-name: github-actions-cd

        - name: Login to Amazon ECR
          uses: aws-actions/amazon-ecr-login@v1

        - name: Build Docker image
          run: |
            docker build -t ${{ steps.set-vars.outputs.ecr_repo }}:${{ steps.set-vars.outputs.image_tag }} --build-arg nodeImage=${{ steps.set-vars.outputs.node_image }} .

        - name: Push Docker image to ECR
          run: |
            docker push ${{ steps.set-vars.outputs.ecr_repo }}:${{ steps.set-vars.outputs.image_tag }}

        - name: Install yq
          run: |
            sudo curl -L https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -o /usr/bin/yq
            sudo chmod +x /usr/bin/yq

        - name: Check out my other private repo
          uses: actions/checkout@v4
          with:
            ref: ${{ steps.set-vars.outputs.helm_branch }}
            repository: Repfabric-Global/rf-helm-charts
            token: ${{ secrets.RFTERRAFORMPAT }}

        - name: Update Helm chart values
          run: |
            export NEW_TAG="${{ steps.set-vars.outputs.image_tag }}"
            cd charts/application-plane
            yq e '.nodeBackend.tag = strenv(NEW_TAG)' -i ${{ steps.set-vars.outputs.env_name }}-values.yaml

        - name: Commit and push updated values.yaml
          run: |
            git config user.name "github-actions"
            git config user.email "<EMAIL>"
            git add charts/application-plane/${{ steps.set-vars.outputs.env_name }}-values.yaml
            git commit -m "Update image tag for ${{ steps.set-vars.outputs.env_name }} to ${{ steps.set-vars.outputs.image_tag }}"
            git push origin ${{ steps.set-vars.outputs.helm_branch }}

        - name: Slack Notification with Details
          if: always()
          run: |
            STATUS="${{ job.status }}"
            REPO="${{ github.repository }}"
            BRANCH="${{ github.ref_name }}"
            COMMITTER="${{ github.actor }}"
            IMAGE_TAG="${{ steps.set-vars.outputs.image_tag }}"
            MESSAGE="📦 *GitHub Actions Deployment Notification*\n• *Status:* $STATUS\n• *Repository:* $REPO\n• *Branch:* $BRANCH\n• *Committer:* $COMMITTER\n• *Image Tag:* $IMAGE_TAG"
            curl -X POST -H 'Content-type: application/json' \
              --data "{\"text\":\"$MESSAGE\"}" \
              ${{ secrets.SLACK_WEBHOOK_URL }}
