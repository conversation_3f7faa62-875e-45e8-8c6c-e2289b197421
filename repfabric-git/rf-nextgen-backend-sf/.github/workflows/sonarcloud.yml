name: SonarCloud Scan
on:
  push:
    branches:
      - development
      - qa
      - release/*
      - main
    
  pull_request:
    types: [opened, synchronize, reopened]
jobs:
  sonarqube:
    name: SonarQube
    runs-on: ubuntu-latest
    timeout-minutes: 60
    env:
      SONAR_SCANNER_JAVA_OPTS: "-Xmx8g -Xms2g -XX:MaxRAMPercentage=80"
      SONAR_SCANNER_OPTS: "-Dsonar.verbose=true -Dsonar.scanAllFiles=true"
    
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0  
      - name: SonarQubeScan
        uses: SonarSource/sonarqube-scan-action@v4
        env: 
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_SCANNER_OPTS: "-Dsonar.verbose=true -Dsonar.scanAllFiles=true -Dsonar.scanner.timeout=3000"