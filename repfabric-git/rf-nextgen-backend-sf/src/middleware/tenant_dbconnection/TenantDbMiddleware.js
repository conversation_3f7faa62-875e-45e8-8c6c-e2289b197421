// TenantDbMiddleware.js
// Express middleware to set up tenant-specific Sequelize instance and models for each request
const dbConn = require('../../../config/dbConn'); // MySQL connection pool manager
const { Sequelize } = require('sequelize'); // Sequelize ORM
const { getTenantsDBDetails } = require('./TenantDbCommonFunctions'); // Utility to get tenant DB details
const defineModels = require('../../models/defineModels'); // Function to define all models

// Middleware function to attach tenant-specific Sequelize and models to the request
module.exports = async function tenantDbMiddleware(req, res, next) {
    try {
        const fullUrl = req.protocol + '://' + req.get('host') + req.originalUrl;
        console.log(`[INFO] Setting up tenant DB connection Full Request URL: `, fullUrl);
        
        // Get DB connection variables for the current tenant
        const dbVars = await getTenantsDBDetails(req);

        // Set up a MySQL connection pool for this tenant
        await dbConn.setTenantPool(dbVars);
        
        // Create a new Sequelize instance for the tenant
        req.tenantSequelize = new Sequelize(
            'RF_CRM_DB_' + dbVars.tenantId,
            dbVars.UNAME,
            dbVars.PASS,
            {
                host: dbVars.HOST,
                port: dbVars.PORT,
                dialect: 'mysql',
                logging: false
            }
        );

        // Attach tenant-specific models to the request
        req.tenantModels = defineModels(req.tenantSequelize, Sequelize.DataTypes);

        // Attach dbVars to the request object for downstream access
        req.dbVars = dbVars;

        // Attach subdomain for further use
        req.subDomain = dbVars.subDomain;

        // Continue to the next middleware or route handler
        next();
    } catch (err) {
        // Pass any errors to the error handler
        next(err);
    }
};