const jwt = require('jsonwebtoken')
const AppError = require('../utils/appError')
const aws = require('aws-sdk')

const getPagination = (page, size) => {
    const limit = size
    const offset = 0 + (page - 1) * limit
    return { limit, offset }
}

const getZeroBasedPagination = (page, size) => {
    const limit = size
    const offset =  page * limit
    return { limit, offset }
}

const getPagingData = (data, page, size) => {
    const { count: totalItems, rows: contents } = data
    const currentPage = page ? +page : 0
    const totalPages = Math.ceil(totalItems / size)
    return {
        totalItems,
        totalPages,
        currentPage,
        contents
    }
}

const getZeroBasedPaginationData = (data, page, size) => {
    const { count: totalItems, rows: contents } = data
    const currentPage = (page ? +page : 0) + 1
    const totalPages = Math.ceil(totalItems / size)
    return {
        totalItems,
        totalPages,
        currentPage,
        contents
    }
}

const userDetails = (req, _res, next) => {
    try {
        const token = req.cookies.token || ''
        return jwt.verify(token, process.env.JWT_KEY)
    } catch (error) {
        next(AppError.unAuthorized())
    }
}

const s3 = new aws.S3({
    region: process.env.AWS_REGION,
})

const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
    // These options are needed to round to whole numbers if that's what you want.
    //minimumFractionDigits: 0, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
    //maximumFractionDigits: 0, // (causes 2500.99 to be printed as $2,501)
})

const noRoundFormatter = (maxFractionDigits) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        maximumFractionDigits: maxFractionDigits
    })
}
module.exports = {
    getPagination,
    getPagingData,
    getZeroBasedPagination,
    getZeroBasedPaginationData,
    userDetails,
    s3,
    formatter,
    noRoundFormatter
}
