const puppeteer = require('puppeteer')
const hbs = require('handlebars')
const fs = require('fs-extra')
const path = require('path')
const dayjs = require('dayjs')
const jsonData = require('../utils/quoteData')
const { formatter, noRoundFormatter } = require('../helpers/index')
const upload = require('../utils/uploadQuoteLogo')
const { s3 } = require('../helpers/index')
const singleUpload = upload.array('logo')
const axios = require('axios')
const { sequelize } = require('../models')
const ExcelJs = require('exceljs')
const sizeOf = require('image-size')
const { getCompanyById } = require('./companyController')
const logger = require('../utils/logger')

const compile = async function (templateName, data) {
    const filePath = path.join(
        process.cwd(),
        'src/templates',
        `${templateName}.hbs`
    )
    const html = await fs.readFile(filePath, 'utf-8')
    return hbs.compile(html)(data)
}

hbs.registerHelper('withItem', function (object, options) {
    return options.fn(object[options.hash.key])
})

hbs.registerHelper('ifFirst', function (index, options) {
    if (index == 0) {
        return options.fn(this)
    } else {
        return options.inverse(this)
    }
})

hbs.registerHelper('breaklines', function (text) {
    text = hbs.Utils.escapeExpression(text)
    text = text.replace(/(\r\n|\n|\r)/gm, '<br>')
    return new hbs.SafeString(text)
})

hbs.registerHelper('inc', function (number, options) {
    if (typeof number === 'undefined' || number === null) return null
    return number + (options.hash.inc || 1)
})

hbs.registerHelper('ifCond', function (v1, v2, options) {
    if (v1 === v2) {
        return options.fn(this)
    }
    return options.inverse(this)
})

hbs.registerHelper('check', function (value, comparator) {
    return value === comparator ? 'No content' : value
})

hbs.registerHelper('formatDate', function (dateString) {
    return new hbs.SafeString(dayjs(dateString).format('MM-DD-YYYY'))
})

hbs.registerHelper('times', function (n, block) {
    let accum = ''
    for (let i = 0; i < n; ++i) accum += block.fn(i)
    return accum
})

hbs.registerHelper('ifNotEqual', function (arg1, arg2, options) {
    return arg1 == arg2 ? options.inverse(this) : options.fn(this)
})

hbs.registerHelper('splitIfLong', function (value) {
    if (value.length > 13) {
        return value.length > 26
            ? value.substring(0, 13) +
                  '<br>' +
                  value.substring(13, 26) +
                  '<br>' +
                  value.substring(26, 39) +
                  '<br>' +
                  value.substring(39)
            : value.substring(0, 13) + '<br>' + value.substring(13)
    }
    return value
})

const convertHtmlToPlainText = async (string) => {
    return string.toString().replace(/(?:\r\n|\r|\n)/g, '<br>')
}

const pdf = async (req, res, next) => {
    // let hostName = process.env.HOST_NAME
    try {
        const row = req.body
        if (!row.templateId) {
            next(AppError.badRequest())
        }

        const options = await getPdfTemplateById(row, res, next, req)

        // if (process.env.NODE_ENV != 'development') {
            let host = req.get('host')
            hostName = host.split('/')[0]
        // }

        let image = `https://${hostName}/rf_api/logo_image_url.php`

        const resImage = `<img src="${image}" alt="Logo" style='max-width: 200px; max-height: 130px; object-fit: contain;'/>`
        const defaultImgPath = resImage

        const imgPath = await getImageURL(options.logo)

        let headers = await getPdfHeaders(row, res, next, req)

        const secondaryCustomerLbl = await getSecondaryCustLable(req)

        const resultData = await getQuoteHeader(
            row.quoteNumber,
            row.templateId,
            options.superQuote,
            options.groupByManufacture,
            options.partLinkToSpec,
            req
        )
        let data = []
        const headerParams = await getQuoteHeaderParams(row.templateId, req)
        let defaultData =
            resultData.length == 0 &&
            options.partLinkToSpec &&
            !options.groupByManufacture
                ? await jsonDataWithLink(await jsonData())
                : await jsonData()

        data = resultData.length > 0 ? resultData : defaultData
        const quoteDetails = await pdfGridData(options, resultData, data)
        let isExcel = row.isExcel == undefined || !row.isExcel ? false : true
        if (headerParams.length == 0) {
            next(AppError.noContent())
        }

        if (headers.length == 0) {
            next(AppError.noContent())
        }

        if (options.length == 0) {
            next(AppError.noContent())
        }

        if (options.noteVisible) {
            headers.push({
                label: 'QUOT_NOTES',
                fieldName: 'Note',
                customeLabel: '',
                width: '',
                order: ''
            })
        }

        if (
            options.totalLabelForCustomer &&
            data[0].QUOT_RECEIPIENT == 'Customer'
        ) {
            headers = headers.map((item) => {
                if (item.label === 'QUOT_EXT_PRICE') {
                    return {
                        ...item,
                        fieldName: options.totalLabelForCustomer
                    }
                }
                return item
            })
        }
        if (
            options.totalLabelForDistributor &&
            data[0].QUOT_RECEIPIENT == 'Distributor'
        ) {
            headers = headers.map((item) => {
                if (item.label === 'QUOT_EXT_PRICE') {
                    return {
                        ...item,
                        fieldName: options.totalLabelForDistributor
                    }
                }
                return item
            })
        }
        let tableData = []

        let keys = headers.map((item) => item.label)
        let filterKeysData = await filterKeys(options, keys)
        keys = filterKeysData.keys
        let fromIndex = filterKeysData.fromIndex
        const excelDetail = quoteDetails
        const pick = (obj, length, ...args) => ({
            ...args.reduce(
                (_res, key) => ({
                    ..._res,
                    [key]: mapHeadersData(obj, key, options, fromIndex, length)
                }),
                {}
            )
        })

        if (!isExcel) {
            tableData = await getTableData(options, quoteDetails, keys, pick)
        }

        headers = await filterHeader(options, headers)
        headers = await addLineFieldIntoHeader(isExcel, options, headers)

        const browser = await puppeteer.launch({
            executablePath: '/usr/bin/chromium',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        let { preparedFor, companyDetail, customerDeatil } =
            await checkDistributor(data)

        let secondaryCustomerDetail = data[0].secondaryCustomerDetail

        const page = await browser.newPage()
        tableData = await dynamicSerialNumber(tableData, options)
        const preparedByData = await preparedBy(data[0].INS_USER, req)
        let nameAndSignatureData,
            signatureName,
            signatureUrl,
            isMedical = 0,
            distributor,
            doctor,
            manufacturer,
            caseNo
        if (options.salesOrder || options.medical) {
            const medicalDetail = await fetchMedicalDetail(row.quoteNumber)
            isMedical = medicalDetail.length > 0 ? 1 : 0
            if (isMedical) {
                manufacturer = await getCompanyById(medicalDetail[0].MED_PRINCI)
                nameAndSignatureData = await nameAndSignature(medicalDetail)
                signatureName = nameAndSignatureData.name
                signatureUrl = nameAndSignatureData.signature
                const medical = await medicalDistribuor(row.quoteNumber, req)
                distributor = medical.distributor
                doctor = medical.doctor
                caseNo = medical.caseNo
            }
        }
        const dynamicData = {
            users: tableData,
            headers,
            options,
            pdfData: data[0],
            headerCount: keys.length,
            preparedFor,
            companyDetail,
            customerDeatil,
            secondaryCustomerDetail,
            grandTotal: row.grandTotal,
            imgPath,
            defaultImgPath,
            address: headerParams[0],
            preparedByData,
            secondaryCustomerLbl,
            signatureName,
            signatureUrl,
            isMedical,
            distributor,
            doctor: doctor ? doctor : companyDetail,
            manufacturer,
            caseNo
        }

        req.image = image
        if (isExcel) {
            await pdfLikeExcel(
                dynamicData,
                excelDetail,
                image,
                imgPath,
                row.yoxel,
                res,
                next
            )
            return
        }

        const content = await compile(
            options.medical ? 'medical-case' : 'index',
            dynamicData
        )

        await page.setContent(content)

        const pdfFooter = await convertHtmlToPlainText(data[0].QUOT_COMMENTS)
        const totalLines = await lineCountFnc(pdfFooter)
        const result = await page.pdf({
            margin: {
                top: '30px',
                right: '30px',

                bottom: options.displayFooter
                    ? totalLines > 2
                        ? `${totalLines * 28}px`
                        : '60px'
                    : '30px',
                left: '30px'
            },
            printBackground: true,
            format: 'Letter',
            displayHeaderFooter: true,
            headerTemplate: `
                <div style="font-size: 10px; text-align: center;width:100%;">
                </div>
            `,

            footerTemplate:
                options.displayFooter != 1
                    ? ` <div style="font-size: 10px; text-align: center;width:100%;"></div>`
                    : options.footerNote && pdfFooter.length > 0
                    ? `
                        <div style="border-top:1px solid #ccc; width:100%">
                            <div style="font-size: 9px; font-weight:normal;text-align: left; padding-top: 5px;padding-left:27px; padding-right:20px; font-family: Arial, Helvetica, sans-serif !important;">
                                Note: ${pdfFooter}
                            </div>
                        </div>
                    `
                    : `
                    <div width:100%">
                        <div style="font-size: 10px; text-align: left; padding-top: 5px;padding-left:30px; padding-right:30px; font-family: Arial, Helvetica, sans-serif !important;">
                        </div>
                    </div>`
        })

        if (row.yoxel != undefined && row.yoxel) {
            const base64Data = result.toString('base64')
            return res.status(200).send(base64Data)
        }

        await browser.close()
        return res.contentType('application/pdf').status(200).send(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getSecondaryCustLable = async (req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let secondaryCustomer = 'Secondary Customer: '
    const data = await sequelize.query(
        `SELECT count(*) as count FROM CUSTOM_LABELS WHERE LBL_CUSTOM=LBL_DEFAULT and LBL_ID='IDS_SECOND_CUSTOMER'`
    )
    if (data.length > 0 && data[0].count > 0) {
        secondaryCustomer = 'On behalf of'
    } else {
        const result = await sequelize.query(
            `SELECT LBL_CUSTOM FROM CUSTOM_LABELS WHERE LBL_ID='IDS_SECOND_CUSTOMER'`
        )
        if (result.length > 0) {
            secondaryCustomer = result[0].LBL_CUSTOM
        }
    }
    return secondaryCustomer
}

const getLables = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        let data = await sequelize.query(
            `
            SELECT
            LBL_CUSTOM as name,
            (
                CASE
                    WHEN LBL_ID = 'IDS_CUST_PART' THEN 'QUOT_ITEM_PART_CUST'
                    WHEN LBL_ID = 'IDS_UNIT_PRICE' THEN 'QUOT_RESALE'
                    WHEN LBL_ID = 'IDS_PRINCI' THEN 'QUOT_PRINCIPAL'
                    WHEN LBL_ID = 'IDS_LOCATION' THEN 'QUOT_LOCATION'
                END
            ) as labelId
            FROM CUSTOM_LABELS
            WHERE LBL_ID IN('IDS_CUST_PART','IDS_UNIT_PRICE','IDS_PRINCI','IDS_LOCATION')
            `
        )

        let labels = [
            {
                id: 4,
                labelId: 'QUOT_ITEM_PART_MANF',
                name: 'Part No.'
            },
            {
                id: 5,
                labelId: 'QUOT_ITEM_QNTY',
                name: 'Quantity'
            },
            {
                id: 6,
                labelId: 'QUOT_UOM',
                name: 'Pricing UOM'
            },
            {
                id: 7,
                labelId: 'QUOT_EXT_PRICE',
                name: 'Total Amount'
            },
            {
                id: 8,
                labelId: 'QUOT_LEAD_TIME',
                name: 'Lead Time'
            },
            {
                id: 9,
                labelId: 'QUOT_WEIGHT',
                name: 'Weight'
            },
            {
                id: 10,
                labelId: 'GRID_DESC',
                name: 'Description'
            },
            {
                id: 11,
                labelId: 'QUOT_ITEM_PART_DESC',
                name: 'Description'
            },
            {
                id: 12,
                labelId: 'QUOT_NOTES',
                name: 'Note'
            },
            {
                id: 13,
                labelId: 'QUOT_IMG_URL',
                name: 'Image'
            }
        ]

        data.forEach((item, index) => {
            item.id = index + 1
        })

        for await (const item of labels) {
            data.push(item)
        }

        if (
            req.query.isSuperQuote != 'true' ||
            req.query.groupByManufactureNo == 'true'
        ) {
            data = data.filter((item) => item.labelId != 'QUOT_PRINCIPAL')
        }
        return res.status(200).json(data)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

async function getQuoteHeaderParams(templateId, req) {
    const imageUrl = process.env.IMAGE_URL
    let templt, parms, result

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    templt = await sequelize.query(
        `select QPDF_COMPANY_NAME as PARAM_SUBS_NAME,
        QPDF_ADDRESS as PARAM_SUBS_ADDRESS
        from QUOTE_PDF_TEMPLATES WHERE QPDF_TEMPLATE_ID = ${templateId}`
    )

    parms = await sequelize.query(
        `select IFNULL(PARAM_SUBS_NAME, '') as PARAM_SUBS_NAME,
            IFNULL(PARAM_SUBS_ADDRESS, '') as PARAM_SUBS_ADDRESS,
            concat('${imageUrl}',PARAM_SUBS_LOGO) as LOGO_IMAGE from PARAMS `
    )

    if (
        templt.length == 0 ||
        templt[0].PARAM_SUBS_NAME == null ||
        templt[0].PARAM_SUBS_NAME == ''
    ) {
        result = [
            {
                PARAM_SUBS_NAME: parms[0].PARAM_SUBS_NAME,
                PARAM_SUBS_ADDRESS: parms[0].PARAM_SUBS_ADDRESS,
                LOGO_IMAGE: parms[0].LOGO_IMAGE
            }
        ]
    } else {
        result = [
            {
                PARAM_SUBS_NAME: templt[0].PARAM_SUBS_NAME,
                PARAM_SUBS_ADDRESS: templt[0].PARAM_SUBS_ADDRESS,
                LOGO_IMAGE: templt[0].LOGO_IMAGE
            }
        ]
    }
    return result
}

async function getQuotePreparedFor(contactId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(
        `select IFNULL(CONT_FULL_NAME, '') as CONT_FULL_NAME, 
        IFNULL(CONT_COMP_ID, '') as CONT_COMP_ID  from 
         CONTACTS where  CONT_ID =${contactId}`
    )
}

async function getQuoteCompany(compId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(
        `select IFNULL(COMP_NAME, '') as COMP_NAME, 
        IFNULL(COMP_ADDRESS_1, '') as COMP_ADDRESS_1, 
        IFNULL(COMP_CITY, '') as COMP_CITY,
        IFNULL(COMP_STATE, '') as COMP_STATE, 
        IFNULL(COMP_ZIP_CODE, '') as COMP_ZIP_CODE  from  COMPANIES
         where COMP_ID =${compId}`
    )
}

async function getQuoteHeader(
    quoteNumber,
    templateId,
    superQuote,
    groupByManf,
    partToLinkSpec,
    req
) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let quoteDetails, terms
    const [quoteHeader] = await sequelize.query(
        `SELECT Q.REC_ID, Q.QUOT_NUMBER ,Q.QUOT_DATE,
        IFNULL(Q.QUOT_EXPIRY, '') as QUOT_EXPIRY,
        Q.QUOT_CUSTOMER_CONT, Q.QUOT_CUSTOMER,
        Q.QUOT_DISTRI_CONT, Q.QUOT_DISTRI ,
        Q.QUOT_SEC_CUSTOMER_CONT, Q.QUOT_SEC_CUSTOMER,
        IFNULL(Q.QUOT_CUST_PROGRAM, '') as QUOT_CUST_PROGRAM ,
        IFNULL(Q.QUOT_COMMENTS, '') as QUOT_COMMENTS ,
        IFNULL(Q.QUOT_PRINCIPAL, 0) as QUOT_PRINCIPAL ,
        IFNULL(Q.QUOT_RECEIPIENT, '') as QUOT_RECEIPIENT,
        IFNULL(C.COMP_NAME, '') as QUOT_MANUFACTURER,
        IFNULL(Q.INS_USER,-2) AS INS_USER,
        IFNULL(Q.QUOT_OWNER, IFNULL(Q.INS_USER,-2)) AS QUOT_OWNER
        from  QUOTES_HDR Q
        LEFT JOIN COMPANIES C ON C.COMP_ID=Q.QUOT_PRINCIPAL
        WHERE Q.REC_ID ='${quoteNumber}'`
    )

    return Promise.all(
        quoteHeader.map(async (item) => {
            const headerParams = await getQuoteHeaderParams(templateId, req)
            const [custCompDetail] = item.QUOT_CUSTOMER
                                    ? await getQuoteCompany(item.QUOT_CUSTOMER, req)
                                    : [];

            const [secondaryCustomer] = item.QUOT_SEC_CUSTOMER
                                    ? await getQuoteCompany(item.QUOT_SEC_CUSTOMER, req)
                                    : [];

            const [custContDetail] = item.QUOT_CUSTOMER_CONT
                                    ? await getQuoteCompany(item.QUOT_CUSTOMER_CONT, req)
                                    : [];   

            const [distriCompDetail] = item.QUOT_DISTRI
                                    ? await getQuoteCompany(item.QUOT_DISTRI, req)
                                    : [];   

            const [distriContDetail] = item.QUOT_DISTRI_CONT
                                    ? await getQuotePreparedFor(item.QUOT_DISTRI_CONT, req)
                                    : [];
            
            if (superQuote == 1) {
                quoteDetails = await getSuperQuoteDetail(
                    item.REC_ID,
                    groupByManf,
                    partToLinkSpec,
                    req
                )
                terms = await getTermsForSuperQuote(item.REC_ID, req)
            } else {
                quoteDetails = await getQuoteDetail(
                    item.REC_ID,
                    item.QUOT_PRINCIPAL,
                    partToLinkSpec,
                    req
                )
                terms = item.QUOT_PRINCIPAL
                                    ? await getTerms(item.QUOT_PRINCIPAL, req)
                                    : [];
            }

            const grandTotal = await quoteGrandTotal(item.REC_ID, req)
            const totalWeight = await quoteTotalWeight(item.REC_ID, req)
            const accountManagerDetail = await accountManager(item.REC_ID, req)

            return {
                ...item,
                PARAM_SUBS_NAME:
                    headerParams.length > 0
                        ? headerParams[0].PARAM_SUBS_NAME
                        : '',
                PARAM_SUBS_ADDRESS:
                    headerParams.length > 0
                        ? headerParams[0].PARAM_SUBS_ADDRESS
                        : '',
                LOGO_IMAGE:
                    headerParams.length > 0 ? headerParams[0].LOGO_IMAGE : '',
                QUOTE_TERMS:
                    superQuote == 1
                        ? terms
                        : terms.length > 0
                        ? terms[0].QUOTE_TERMS
                        : '',
                GRAND_TOTAL: formatter.format(grandTotal[0].GRAND_TOTAL),
                TOTAL_WEIGHT: totalWeight[0].TOTAL_WEIGHT,
                custCompanyDetail: custCompDetail,
                secondaryCustomerDetail: secondaryCustomer,
                custContactDetail: custContDetail,
                distriCompanyDetail: distriCompDetail,
                distriContactDetail: distriContDetail,
                quoteDetails: quoteDetails,
                accountManagerDetails: accountManagerDetail
            }
        })
    )
}

async function getQuoteDetail(quotHeaderId, principalId, partToLinkSpec, req) {
     // Defensive check: return empty array if IDs are missing
    if (!quotHeaderId || !principalId) {
        return [];
    }

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    const [data] = await sequelize.query(
        `SELECT IFNULL(QUOT_ITEM_PART_MANF, '') as QUOT_ITEM_PART_MANF,
        IFNULL(QUOT_ITEM_PART_CUST, '') as QUOT_ITEM_PART_CUST, 
        IFNULL(QUOT_ITEM_PART_DESC, '') as QUOT_ITEM_PART_DESC,
        IFNULL(QUOT_ITEM_PART_DESC, '') as GRID_DESC,
        IFNULL(FORMAT(QUOT_ITEM_QNTY,0),0) as QUOT_ITEM_QNTY,
        CASE
            WHEN QUOT_UOM IS NULL or QUOT_UOM = ''
            THEN 'EA'
            ELSE QUOT_UOM
        END AS QUOT_UOM,
        IFNULL(CONCAT('$',QUOT_RESALE),0.00) as QUOT_RESALE,
        IFNULL(CONCAT('$',FORMAT(QUOT_EXT_PRICE,2)),0.00) as QUOT_EXT_PRICE,
        IFNULL(QUOT_NOTES, '') as QUOT_NOTES,
        IFNULL(QUOT_LOCATION, '') as QUOT_LOCATION,
        IFNULL(QUOT_LEAD_TIME,'') as QUOT_LEAD_TIME,
        IFNULL(FORMAT(QUOT_WEIGHT,2),0.00) as QUOT_WEIGHT,
        QUOTES_DTL.QUOT_PRINCIPAL as QUOT_PRINCIPAL_ID,
        CASE 
        WHEN QUOT_IMG_URL IS NULL OR QUOT_IMG_URL = '' 
            THEN PM.PROD_IMG_URL 
            ELSE QUOT_IMG_URL 
        END AS QUOT_IMG_URL,
         CASE 
        WHEN QUOT_SPEC_URL IS NULL OR QUOT_SPEC_URL = '' 
        THEN PM.PROD_SPEC_URL 
        ELSE QUOT_SPEC_URL END AS PROD_SPEC_URL
        FROM QUOTES_DTL
        LEFT JOIN RF_DBD_DB_${tenantId}.PRODUCTS_MST PM
        ON PM.PROD_PRINCI_ID = ${principalId} AND
        PM.PROD_PRINCI_PARTNO = QUOTES_DTL.QUOT_ITEM_PART_MANF
        WHERE QUOT_HDR_ID =${quotHeaderId} order by QUOTES_DTL.QUOT_SEQ_NUM asc`
    )

    if (partToLinkSpec) {
        data.forEach(function (item) {
            item.QUOT_ITEM_PART_MANF = item.PROD_SPEC_URL
                ? `<a href="${item.PROD_SPEC_URL}" style="color:blue">` +
                  item.QUOT_ITEM_PART_MANF +
                  '</a>'
                : item.QUOT_ITEM_PART_MANF
        })
    }

    return data
}

async function getTerms(compId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const [result] = await sequelize.query(
        `select IFNULL(QUOTE_TERMS, '') as QUOTE_TERMS from COMP_DOC_NOTES 
         where COMP_ID =${compId}`
    )

    return result;
}

async function getTermsForSuperQuote(quotHeaderId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const [result] = await sequelize.query(
        `SELECT DISTINCT IFNULL(C.COMP_PRINT_NAME, '') as COMP_PRINT_NAME, 
         IFNULL(CDN.QUOTE_TERMS, '') as QUOTE_TERMS
         FROM QUOTES_DTL Q
         LEFT JOIN COMPANIES C ON C.COMP_ID = Q.QUOT_PRINCIPAL
         LEFT JOIN COMP_DOC_NOTES CDN ON CDN.COMP_ID = Q.QUOT_PRINCIPAL
         WHERE Q.QUOT_HDR_ID =${quotHeaderId} order by Q.QUOT_SEQ_NUM asc`
    )
    return result;
}

async function quoteGrandTotal(quotHdrId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const [total] = await sequelize.query(`
    select
    IFNULL(SUM(QUOT_EXT_PRICE),0.00) as GRAND_TOTAL
    from QUOTES_DTL
    where QUOT_HDR_ID = ${quotHdrId}
    `)

    return total;
}

async function quoteTotalWeight(quotHdrId,req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const [result] = await sequelize.query(`
    select
    IFNULL(SUM(QUOT_WEIGHT),0.000) as TOTAL_WEIGHT
    from QUOTES_DTL
    where QUOT_HDR_ID = ${quotHdrId}
    `)

    return result;
}

const savePdfConfig = async (req, res) => {
    try {
        const row = req.body
        const configRow = row.data
        const superQuote = row.superQuote ? row.superQuote : 0
        let pdfConfig = []
        //Generate QPDF_TEMPLATE_ID
        const templateId = await GenerateId('QPDFTEMP', req)

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const duplicateTemplate = await models.QUOTE_PDF_TEMPLATE.count({
            where: {
                QPDF_TEMPLATE_NAME: row.templateName.trim()
            }
        })

        if (duplicateTemplate > 0) {
            return res.status(200).json({
                error: true,
                message: 'Duplicate Entry'
            })
        }
        if (row.defaultFlag == 1) {
            await updatePrevDefaultTemplate(superQuote, req)
        }
        const template = {
            QPDF_TEMPLATE_ID: templateId,
            QPDF_TEMPLATE_NAME: row.templateName.trim(),
            QPDF_PRINCI_ID: 0,
            QPDF_DEFAULT_FLAG: row.defaultFlag,
            QPDF_TOPIC: row.topic,
            QPDF_FOOTER_NOTE: row.footerNote,
            QPDF_MNF_FOOTER_NOTE: row.manufacturerFooterNote,
            QPDF_LINEITEM_NOTE: row.lineItemNote,
            QPDF_DESCRIPTION: row.description,
            QPDF_DESCRIPTION_WRAP: row.descWrap,
            QPDF_COMPANY_NAME: row.companyName,
            QPDF_ADDRESS: row.companyAddress,
            QPDF_SUPER_QUOTE: row.superQuote,
            QPDF_GROUP_BY_MANUFACTURE: row.groupByManufacture,
            QPDF_SHOW_SECONDARY_CUST: row.showSecondaryCustomer,
            QPDF_COMP_INFO_ALIGN_RIGHT: row.compInfoAlignRight,
            QPDF_TITLE_SHOW_MANUFACTURER: row.titleShowManufacturer,
            QPDF_SHOW_PREPARED_BY: row.showPreparedBy,
            QPDF_SHOW_SALESTEAM: row.showSalesTeam,
            QPDF_SHOW_TOTAL_WEIGHT: row.showTotalWeight,
            QPDF_HIDE_LINE: row.hideLine,
            QPDF_SALES_ORDER: row.salesOrder,
            QPDF_INCLUDE_NAME: row.includeName,
            QPDF_INCLUDE_SIGNATURE: row.includeSignature,
            QPDF_MEDICAL: row.medical,
            QPDF_PART_LINK_TO_SPEC: row.partLinkToSpec,
            QPDF_DISPLAY_FOOTER: row.displayFooter,
            QPDF_TOTAL_LABEL_FOR_CUSTOMER: row.totalLabelForCustomer,
            QPDF_TOTAL_LABEL_FOR_DISTRIBUTOR: row.totalLabelForDistributor,
            QPDF_UNIT_PRICE_DECIMAL_DIGIT: row.unitPriceDecimalDigit,
            INS_USER: req.session.userId,
            UPD_USER: req.session.userId
        }

        if (configRow) {
            configRow.map(async (item) => {
                const data = {
                    QPDF_TEMPLATE_ID: templateId,
                    QPDF_FIELD: item.field,
                    QPDF_FIELD_NAME: item.fieldName,
                    QPDF_CUSTOM_LABEL: item.customLabel,
                    QPDF_TYPE: item.type,
                    QPDF_WIDTH: item.width,
                    QPDF_ORDER: item.order,
                    INS_USER: req.session.userId,
                    UPD_USER: req.session.userId
                }
                pdfConfig.push(data)
            })
        }
        let result = await models.QUOTE_PDF_TEMPLATE.create(template)
        result = result.get({ plain: true })
        await models.QUOTE_PDF_CONFIG.bulkCreate(pdfConfig)

        return res.status(200).json(result.QPDF_TEMPLATE_ID)
    } catch (error) {
        return res.status(500).json({
            message: error.errors ? error.errors[0].message : error.message
        })
    }
}

async function updatePrevDefaultTemplate(superQuote, req) {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    await models.QUOTE_PDF_TEMPLATE.update(
        {
            QPDF_DEFAULT_FLAG: 0
        },
        {
            where: {
                [Op.and]: {
                    QPDF_DEFAULT_FLAG: 1,
                    QPDF_SUPER_QUOTE: superQuote
                }
            }
        }
    )
}

const updatePdfConfig = async (req, res) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const row = req.body
        const superQuote = row.superQuote ? row.superQuote : 0
        const configRow = row.data
        let pdfConfig = []
        const { id } = req.params

        let query = `SELECT COUNT(QPDF_TEMPLATE_NAME) as COUNT FROM QUOTE_PDF_TEMPLATES
        WHERE QPDF_TEMPLATE_NAME NOT IN (SELECT QPDF_TEMPLATE_NAME FROM QUOTE_PDF_TEMPLATES 
            WHERE QPDF_TEMPLATE_ID = ?) AND  QPDF_TEMPLATE_NAME=?`

        const duplicateTemplate = await sequelize.query(query, [
            id,
            row.templateName.trim()
        ])

        if (duplicateTemplate[0].COUNT > 0) {
            return res.status(200).json({
                error: true,
                message: 'Duplicate Entry'
            })
        }
        if (row.defaultFlag == 1) {
            await updatePrevDefaultTemplate(superQuote, req)
        }

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.QUOTE_PDF_TEMPLATE.update(
            {
                QPDF_TEMPLATE_NAME: row.templateName.trim(),
                QPDF_PRINCI_ID: 0,
                QPDF_DEFAULT_FLAG: row.defaultFlag,
                QPDF_TOPIC: row.topic,
                QPDF_FOOTER_NOTE: row.footerNote,
                QPDF_MNF_FOOTER_NOTE: row.manufacturerFooterNote,
                QPDF_LINEITEM_NOTE: row.lineItemNote,
                QPDF_DESCRIPTION: row.description,
                QPDF_DESCRIPTION_WRAP: row.descWrap,
                QPDF_COMPANY_NAME: row.companyName,
                QPDF_ADDRESS: row.companyAddress,
                QPDF_SUPER_QUOTE: row.superQuote,
                QPDF_GROUP_BY_MANUFACTURE: row.groupByManufacture,
                QPDF_SHOW_SECONDARY_CUST: row.showSecondaryCustomer,
                QPDF_COMP_INFO_ALIGN_RIGHT: row.compInfoAlignRight,
                QPDF_TITLE_SHOW_MANUFACTURER: row.titleShowManufacturer,
                QPDF_SHOW_PREPARED_BY: row.showPreparedBy,
                QPDF_SHOW_SALESTEAM: row.showSalesTeam,
                QPDF_SHOW_TOTAL_WEIGHT: row.showTotalWeight,
                QPDF_HIDE_LINE: row.hideLine,
                QPDF_SALES_ORDER: row.salesOrder,
                QPDF_INCLUDE_NAME: row.includeName,
                QPDF_INCLUDE_SIGNATURE: row.includeSignature,
                QPDF_MEDICAL: row.medical,
                QPDF_PART_LINK_TO_SPEC: row.partLinkToSpec,
                QPDF_DISPLAY_FOOTER: row.displayFooter,
                QPDF_TOTAL_LABEL_FOR_CUSTOMER: row.totalLabelForCustomer,
                QPDF_TOTAL_LABEL_FOR_DISTRIBUTOR: row.totalLabelForDistributor,
                QPDF_UNIT_PRICE_DECIMAL_DIGIT: row.unitPriceDecimalDigit,
                INS_USER: req.session.userId,
                UPD_USER: req.session.userId
            },
            {
                where: {
                    QPDF_TEMPLATE_ID: id
                }
            }
        )

        if (configRow != undefined && configRow.length > 0) {
            await models.QUOTE_PDF_CONFIG.destroy({
                where: {
                    QPDF_TEMPLATE_ID: id
                }
            })

            configRow.map(async (item) => {
                const data = {
                    QPDF_TEMPLATE_ID: id,
                    QPDF_FIELD: item.field,
                    QPDF_FIELD_NAME: item.fieldName,
                    QPDF_CUSTOM_LABEL: item.customLabel,
                    QPDF_TYPE: item.type,
                    QPDF_WIDTH: item.width,
                    QPDF_ORDER: item.order,
                    INS_USER: req.session.userId,
                    UPD_USER: req.session.userId
                }
                pdfConfig.push(data)
            })

            await models.QUOTE_PDF_CONFIG.bulkCreate(pdfConfig)
            return res.status(200).json('Success')
        } else {
            return res.status(500).json('Nothing to update')
        }
    } catch (error) {
        return res.status(500).json({
            message: error.errors ? error.errors[0].message : 'Server Error'
        })
    }
}

const deletePdfConfig = async (req, res, next) => {
    try {
        const { id } = req.params
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const logo = await models.QUOTE_PDF_TEMPLATE.findOne({
            attributes: ['QPDF_LOGO'],
            where: {
                QPDF_TEMPLATE_ID: req.params.id
            },
            raw: true
        })

        if (logo.QPDF_LOGO) {
            if (fs.existsSync(`${process.env.IMG_PATH}/${logo.QPDF_LOGO}`)) {
                fs.unlinkSync(`${process.env.IMG_PATH}/${logo.QPDF_LOGO}`)
            }
        }

        await models.QUOTE_PDF_TEMPLATE.destroy({
            where: {
                QPDF_TEMPLATE_ID: id
            }
        })

        await models.QUOTE_PDF_CONFIG.destroy({
            where: {
                QPDF_TEMPLATE_ID: id
            }
        })

        return res.status(200).json('Success')
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getPdfConfig = async (req, res, next) => {
    const { id } = req.params
    let result = []

    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const data = await models.QUOTE_PDF_TEMPLATE.findOne({
            attributes: [
                ['REC_ID', 'id'],
                ['QPDF_TEMPLATE_ID', 'templateId'],
                ['QPDF_TEMPLATE_NAME', 'templateName'],
                ['QPDF_DEFAULT_FLAG', 'defaultFlag'],
                ['QPDF_TOPIC', 'topic'],
                ['QPDF_FOOTER_NOTE', 'footerNote'],
                ['QPDF_MNF_FOOTER_NOTE', 'manufacturerFooterNote'],
                ['QPDF_LINEITEM_NOTE', 'lineItem'],
                ['QPDF_DESCRIPTION', 'description'],
                ['QPDF_DESCRIPTION_WRAP', 'descWrap'],
                ['QPDF_LOGO', 'logo'],
                ['QPDF_COMPANY_NAME', 'companyName'],
                ['QPDF_ADDRESS', 'companyAddress'],
                ['QPDF_SUPER_QUOTE', 'superQuote'],
                ['QPDF_GROUP_BY_MANUFACTURE', 'groupByManufacture'],
                ['QPDF_SHOW_SECONDARY_CUST', 'showSecondaryCustomer'],
                ['QPDF_COMP_INFO_ALIGN_RIGHT', 'compInfoAlignRight'],
                ['QPDF_TITLE_SHOW_MANUFACTURER', 'titleShowManufacturer'],
                ['QPDF_SHOW_PREPARED_BY', 'showPreparedBy'],
                ['QPDF_SHOW_SALESTEAM', 'showSalesTeam'],
                ['QPDF_SHOW_TOTAL_WEIGHT', 'showTotalWeight'],
                ['QPDF_HIDE_LINE', 'hideLine'],
                ['QPDF_SALES_ORDER', 'salesOrder'],
                ['QPDF_INCLUDE_NAME', 'includeName'],
                ['QPDF_INCLUDE_SIGNATURE', 'includeSignature'],
                ['QPDF_MEDICAL', 'medical'],
                ['QPDF_PART_LINK_TO_SPEC', 'partLinkToSpec'],
                ['QPDF_DISPLAY_FOOTER', 'displayFooter'],
                ['QPDF_TOTAL_LABEL_FOR_CUSTOMER', 'totalLabelForCustomer'],
                [
                    'QPDF_TOTAL_LABEL_FOR_DISTRIBUTOR',
                    'totalLabelForDistributor'
                ],
                ['QPDF_UNIT_PRICE_DECIMAL_DIGIT', 'unitPriceDecimalDigit']
            ],
            where: {
                QPDF_TEMPLATE_ID: id
            }
        })

        if (data && data.dataValues.logo) {
            data.dataValues.logo = await getImageURL(data.dataValues.logo)
        }

        let data1 = await models.QUOTE_PDF_CONFIG.findAll({
            attributes: [
                ['REC_ID', 'id'],
                ['QPDF_TEMPLATE_ID', 'templateId'],
                ['QPDF_FIELD', 'field'],
                ['QPDF_FIELD_NAME', 'fieldName'],
                ['QPDF_CUSTOM_LABEL', 'customLabel'],
                ['QPDF_TYPE', 'type'],
                ['QPDF_WIDTH', 'width'],
                ['QPDF_ORDER', 'order']
            ],
            where: {
                QPDF_TEMPLATE_ID: id
            },
            order: [['QPDF_ORDER', 'asc']],
            raw: true
        })
        data1 = data1.filter((item) => item.field != 'QUOT_ITEM_PART_DESC')

        const labels = await sequelize.query(
            `SELECT
            LBL_CUSTOM as name,
            (
                CASE
                    WHEN LBL_ID = 'IDS_CUST_PART' THEN 'QUOT_ITEM_PART_CUST'
                    WHEN LBL_ID = 'IDS_UNIT_PRICE' THEN 'QUOT_RESALE'
                    WHEN LBL_ID = 'IDS_PRINCI' THEN 'QUOT_PRINCIPAL'
                    WHEN LBL_ID = 'IDS_LOCATION' THEN 'QUOT_LOCATION'
                END
            ) as labelId
            FROM CUSTOM_LABELS
            WHERE LBL_ID IN('IDS_CUST_PART','IDS_UNIT_PRICE','IDS_PRINCI','IDS_LOCATION')
            `
        )

        for await (let item of data1) {
            labels.forEach((item1) => {
                if (item.field == item1.labelId) {
                    item.fieldName = item1.name
                }
            })
        }

        result = { ...result, template: data, columns: data1 }

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getPdfTemplate = async (req, res, next) => {
    try {
        const { superQuote } = req.query

        let data
        
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.QUOTE_PDF_TEMPLATE.findAll({
            attributes: [
                ['REC_ID', 'id'],
                ['QPDF_TEMPLATE_ID', 'templateId'],
                ['QPDF_TEMPLATE_NAME', 'templateName'],
                ['QPDF_DEFAULT_FLAG', 'defaultFlag'],
                ['QPDF_LOGO', 'logo'],
                ['QPDF_TOPIC', 'topic'],
                ['QPDF_FOOTER_NOTE', 'footerNote'],
                ['QPDF_MNF_FOOTER_NOTE', 'manufacturerFooterNote'],
                ['QPDF_LINEITEM_NOTE', 'lineItem'],
                ['QPDF_DESCRIPTION', 'description'],
                ['QPDF_DESCRIPTION_WRAP', 'descWrap'],
                ['QPDF_SUPER_QUOTE', 'superQuote'],
                ['QPDF_SALES_ORDER', 'salesOrder'],
                ['QPDF_INCLUDE_NAME', 'includeName'],
                ['QPDF_INCLUDE_SIGNATURE', 'includeSignature'],
                ['QPDF_MEDICAL', 'medical'],
                ['QPDF_PART_LINK_TO_SPEC', 'partLinkToSpec'],
                ['QPDF_DISPLAY_FOOTER', 'displayFooter'],
                ['QPDF_TOTAL_LABEL_FOR_CUSTOMER', 'totalLabelForCustomer'],
                [
                    'QPDF_TOTAL_LABEL_FOR_DISTRIBUTOR',
                    'totalLabelForDistributor'
                ],
                ['QPDF_UNIT_PRICE_DECIMAL_DIGIT', 'unitPriceDecimalDigit']
            ],
            raw: true
        }).then((result) => {
            if (superQuote) {
                data = result.filter((item) => {
                    if (item.superQuote == superQuote) {
                        return item
                    }
                })
            } else {
                data = result
            }
        })

        return res.status(200).json(data)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getPdfTemplateById = async (row, res, next, req) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const [options] = await sequelize.query(
            `
                SELECT 
                REC_ID as id,
                QPDF_TEMPLATE_ID as templateId,
                QPDF_TEMPLATE_NAME as templateName,
                QPDF_DEFAULT_FLAG as defaultFlag,
                QPDF_LOGO as logo,
                QPDF_TOPIC as topicVisible,
                QPDF_FOOTER_NOTE as footerNote,
                QPDF_MNF_FOOTER_NOTE as manufacturerFooterNote,
                QPDF_DESCRIPTION as descBelowPartNo,
                QPDF_DESCRIPTION_WRAP as descWrap,
                IF(QPDF_LINEITEM_NOTE=1, 1, 0) noteVisible,
                QPDF_COMPANY_NAME as companyName,
                QPDF_ADDRESS as companyAddress,
                IFNULL(QPDF_SUPER_QUOTE,0) as superQuote,
                QPDF_GROUP_BY_MANUFACTURE as groupByManufacture,
                QPDF_SHOW_SECONDARY_CUST as showSecondaryCustomer,
                QPDF_COMP_INFO_ALIGN_RIGHT as compInfoAlignRight,
                QPDF_TITLE_SHOW_MANUFACTURER as titleShowManufacturer,
                QPDF_SHOW_PREPARED_BY as showPreparedBy,
                QPDF_SHOW_SALESTEAM as showSalesTeam,
                QPDF_SHOW_TOTAL_WEIGHT as showTotalWeight,
                QPDF_HIDE_LINE as hideLine,
                QPDF_SALES_ORDER as salesOrder,
                QPDF_INCLUDE_NAME as includeName,
                QPDF_INCLUDE_SIGNATURE as includeSignature,
                QPDF_MEDICAL as medical,
                QPDF_PART_LINK_TO_SPEC as partLinkToSpec,
                QPDF_DISPLAY_FOOTER as displayFooter,
                QPDF_TOTAL_LABEL_FOR_CUSTOMER as totalLabelForCustomer,
                QPDF_TOTAL_LABEL_FOR_DISTRIBUTOR as totalLabelForDistributor,
                QPDF_UNIT_PRICE_DECIMAL_DIGIT as unitPriceDecimalDigit
                from QUOTE_PDF_TEMPLATES
                where QPDF_TEMPLATE_ID = ${row.templateId}
            `
        )

        return options.length > 0 ? options[0] : []
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getPdfHeaders = async (row, res, next, req) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        let pdfConfig = await models.QUOTE_PDF_CONFIG.findAll({
            attributes: [
                ['QPDF_FIELD', 'label'],
                ['QPDF_FIELD_NAME', 'fieldName'],
                ['QPDF_CUSTOM_LABEL', 'customLabel'],
                ['QPDF_WIDTH', 'width'],
                ['QPDF_ORDER', 'order']
            ],
            where: {
                QPDF_TEMPLATE_ID: row.templateId
            },
            order: [['QPDF_ORDER', 'asc']],
            raw: true
        })

        const labels = await sequelize.query(
            `SELECT
            LBL_CUSTOM as name,
            (
                CASE
                    WHEN LBL_ID = 'IDS_CUST_PART' THEN 'QUOT_ITEM_PART_CUST'
                    WHEN LBL_ID = 'IDS_UNIT_PRICE' THEN 'QUOT_RESALE'
                    WHEN LBL_ID = 'IDS_PRINCI' THEN 'QUOT_PRINCIPAL'
                    WHEN LBL_ID = 'IDS_LOCATION' THEN 'QUOT_LOCATION'
                END
            ) as labelId
            FROM CUSTOM_LABELS
            WHERE LBL_ID IN('IDS_CUST_PART','IDS_UNIT_PRICE','IDS_PRINCI','IDS_LOCATION')
            `
        )

        pdfConfig.forEach((item) => {
            labels.forEach((item1) => {
                if (item.label == item1.labelId) {
                    item.fieldName = item1.name
                }
            })
        })

        return pdfConfig
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const imageUpload = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        singleUpload(req, res, async function (err) {
            if (err) {
                return res.status(501).json({
                    success: false,
                    errors: {
                        title: 'Image Upload Error',
                        detail: err.message,
                        error: err
                    }
                })
            }
            const logo = await models.QUOTE_PDF_TEMPLATE.findOne({
                attributes: ['QPDF_LOGO'],
                where: {
                    QPDF_TEMPLATE_ID: req.params.id
                },
                raw: true
            })
            if (logo != null && logo.QPDF_LOGO) {
                s3.deleteObject(
                    {
                        Bucket: process.env.S3_BUCKET,
                        Key: logo.QPDF_LOGO
                    },
                    function (error) {
                        if (error) {
                            return res.json({
                                success: false,
                                errors: {
                                    title: 'Image Update Error',
                                    detail: error.message,
                                    error: error
                                }
                            })
                        }
                    }
                )
            }
            let files = req.files

            await models.QUOTE_PDF_TEMPLATE.update(
                {
                    QPDF_LOGO: files[0].key
                },
                {
                    where: { QPDF_TEMPLATE_ID: req.params.id }
                }
            )

            await getPdfConfig(req, res, next)
        })
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

async function filterKeys(options, keys) {
    let descriptionBelowColSpan = 0
    let fromIndex = 0

    if (options.descBelowPartNo) {
        const partIndex = keys.indexOf('QUOT_ITEM_PART_DESC')
        if (partIndex > -1) {
            keys.splice(partIndex, 1)
        }
        keys.push('QUOT_ITEM_PART_DESC')
    }

    if (options.descBelowPartNo && options.noteVisible) {
        const descIndex = keys.indexOf('QUOT_ITEM_PART_DESC')
        if (descIndex > -1) {
            keys.splice(descIndex, 1)
        }

        fromIndex = keys.indexOf('QUOT_ITEM_PART_MANF') + 1
        keys = keys.filter((item) => item != 'QUOT_NOTES')
        let toIndex = keys.length
        descriptionBelowColSpan = await countNumber(fromIndex, toIndex)
        const noteIndex = keys.indexOf('QUOT_NOTES')

        if (noteIndex > -1) {
            keys.splice(noteIndex, 1)
        }

        keys.push('QUOT_ITEM_PART_DESC')
        keys.push('QUOT_NOTES')
    }

    if (options.descWrap) {
        keys = keys
            .filter((item) => {
                return !(options.descWrap && item == 'QUOT_ITEM_PART_DESC')
            })
            .map((item) => item)
    }

    if (options.groupByManufacture) {
        keys = keys
            .filter((item) => {
                return item != 'QUOT_PRINCIPAL'
            })
            .map((item) => item)
    }

    fromIndex = keys.indexOf('QUOT_ITEM_PART_MANF') + 1
    return { keys, descriptionBelowColSpan, fromIndex }
}

async function countNumber(a, b) {
    let count = 0
    for (let i = a; i < b; i++) {
        count++
    }
    return count
}

async function pdfGridData(options, resultData, data) {
    const groupByData = await groupByManufacture(options.partLinkToSpec)
    return options.superQuote &&
        options.groupByManufacture &&
        resultData.length == 0
        ? groupByData[0]
        : data[0].quoteDetails
}

function groupByDataMap(quoteDetails, keys, pick) {
    Object.keys(quoteDetails).forEach((item) => {
        quoteDetails[item].forEach((quoteDetail, index) => {
            for (const key of Object.keys(quoteDetail)) {
                if (key == 'QUOT_ITEM_PART_MANF' && index == 0) {
                    quoteDetails[item][0]['QUOT_ITEM_PART_MANF'] =
                        '<b>' +
                        '<span name="firstIndex">' +
                        `${item}` +
                        '</span></b>' +
                        '<br/>' +
                        '<div style="padding-top:10px">' +
                        quoteDetails[item][0]['QUOT_ITEM_PART_MANF'] +
                        '</div>'
                }
                if (
                    key != 'QUOT_ITEM_PART_MANF' &&
                    key != 'QUOT_ITEM_PART_DESC' &&
                    key != 'QUOT_NOTES' &&
                    key != 'QUOT_IMG_URL' &&
                    index == 0
                ) {
                    quoteDetails[item][0][
                        key
                    ] = `<div style="margin-top:10px"><br/>${quoteDetails[item][0][key]}</div>`
                }
            }
        })
    })

    let groupByData = []
    Object.keys(quoteDetails).forEach((item) => {
        for (let data of quoteDetails[item]) {
            groupByData.push(pick(data, keys.length, ...keys))
        }
    })

    return groupByData
}

function mapHeadersData(obj, key, options, fromIndex, length) {
    if (
        options.descWrap &&
        options.descBelowPartNo &&
        key == 'QUOT_ITEM_PART_MANF'
    ) {
        return wrapBelowPartNo(options, obj, key)
    }

    if (key == 'QUOT_NOTES') {
        return lineNotes(obj, key, length, options)
    }

    if (key == 'QUOT_ITEM_QNTY' || key == 'QUOT_UOM') {
        return fieldCenter(obj, key)
    }

    if (
        key == 'QUOT_ITEM_PART_DESC' &&
        !options.descWrap &&
        options.descBelowPartNo
    ) {
        return noWrapBelowPartNo(options, obj, key, fromIndex, length)
    }

    if (key == 'QUOT_EXT_PRICE' || key == 'QUOT_RESALE') {
        return dollarValues(obj, key, options)
    }

    if (key == 'GRID_DESC') {
        return gridDescription(obj)
    }

    if (key == 'QUOT_IMG_URL') {
        return displayImage(obj)
    }

    return otherFields(obj, key)
}

async function filterHeader(options, headers) {
    if (options.noteVisible) {
        headers = headers.filter((item) => item.label != 'QUOT_NOTES')
    }

    if (options.descWrap || options.descBelowPartNo) {
        headers.forEach((item) => {
            if (item.label == 'QUOT_ITEM_PART_DESC') {
                item.fieldName = ''
            }
        })
    }

    if (options.groupByManufacture) {
        headers.forEach((item) => {
            if (item.label == 'QUOT_PRINCIPAL') {
                item.fieldName = ''
            }
        })
    }

    headers = await alignHeader(headers)

    return headers
}

async function alignHeader(headers) {
    headers.forEach((item) => {
        if (item.label == 'QUOT_RESALE' || item.label == 'QUOT_EXT_PRICE') {
            item.align = 'right'
        } else if (item.label == 'QUOT_ITEM_QNTY' || item.label == 'QUOT_UOM') {
            item.align = 'center'
        } else {
            item.align = 'left'
        }
    })
    return headers
}

function wrapBelowPartNo(options, obj, key) {
    return (
        '<td>' +
        obj[key] +
        '<br/>' +
        obj['QUOT_ITEM_PART_DESC'].replace(/\r\n/g, '<br>') +
        '</td>'
    )
}

function displayImage(obj) {
    const image = obj['QUOT_IMG_URL']
        ? obj['QUOT_IMG_URL']
              .replace('<div style="margin-top:10px">', '')
              .replace(/,/g, '')
              .replace('$', '')
              .replace('<div style="margin-top:10px">', '')
              .replace('<br/>', '')
              .replace('</div>', '')
        : ''

    return image
        ? `<img src="${image}" style="width:100%;height:100%;object-fit: contain;"/>`
        : ''
}

function noWrapBelowPartNo(options, obj, key, fromIndex, length) {
    return (
        `<tr><td colspan=${fromIndex}></td><td colspan=${length}>` +
        obj['QUOT_ITEM_PART_DESC'].replace(/\r\n/g, '<br>') +
        '</td></tr>'
    )
}

function lineNotes(obj, key, length, options) {
    const note = obj[key] != '' && obj[key] != null ? 'Note: ' : ''
    if (options.hideLine == 1) {
        return (
            `<tr><td style="display:none"></td><td colspan=${length} style="font-style:italic!important;font-family:Times New Roman, Times, serif!important;font-size:14!important">` +
            note +
            obj[key] +
            '</td></tr>'
        )
    } else {
        return (
            `<tr><td></td><td colspan=${length} style="font-style:italic!important;font-family:Times New Roman, Times, serif!important;font-size:14!important">` +
            note +
            obj[key] +
            '</td></tr>'
        )
    }
}

function dollarValues(obj, key, options) {
    const divExists = obj[key].search('div')
    const num = +obj[key]
        .replace('<div style="margin-top:10px">', '')
        .replace(/,/g, '')
        .replace('$', '')
        .replace('<div style="margin-top:10px">', '')
        .replace('<br/>', '')
        .replace('</div>', '')
    let result = num.toFixed(
        Math.max(2, (num.toString().split('.')[1] || []).length)
    )

    // Sanitize unitPriceDecimalDigit
    let decimalDigits = parseInt(options.unitPriceDecimalDigit, 10)
    if (isNaN(decimalDigits) || decimalDigits < 0 || decimalDigits > 20) {
        decimalDigits = 2 // fallback to default
    }

    const formatter = noRoundFormatter(decimalDigits)
    result = formatter.format(result)
    const amount = key == 'QUOT_RESALE' ? result : obj[key]
    if (key == 'QUOT_RESALE' && divExists == 1) {
        return (
            `<td style='word-wrap:break-word; text-align:right;'><div style="margin-top:10px"><br/>` +
            amount +
            '</div></td>'
        )
    }

    return (
        `<td style='word-wrap:break-word; text-align:right'>` + amount + '</td>'
    )
}

function fieldCenter(obj, key) {
    return `<td style='text-align:center'>` + obj[key] + '</td>'
}

function gridDescription(obj) {
    return (
        `<td style='word-wrap:break-word; text-align:left'>` +
        obj['GRID_DESC'].replace(/\r\n/g, '<br>') +
        '</td>'
    )
}

function otherFields(obj, key) {
    return (
        `<td style='word-wrap:break-word; text-align:left'>` +
        obj[key] +
        '</td>'
    )
}

async function getSuperQuoteDetail(
    quotHeaderId,
    GroupByManfcture,
    partToLinkSpec,
    req
) {
    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let groups
    const orderBy =
        GroupByManfcture == 1
            ? 'ORDER BY QUOT_PRINCIPAL,Q.QUOT_SEQ_NUM ASC'
            : 'ORDER BY Q.QUOT_SEQ_NUM ASC'
    const [headerDtl] = await sequelize.query(
        `SELECT IFNULL(Q.QUOT_ITEM_PART_MANF, '') as QUOT_ITEM_PART_MANF,
        IFNULL(Q.QUOT_ITEM_PART_CUST, '') as QUOT_ITEM_PART_CUST, 
        IFNULL(Q.QUOT_ITEM_PART_DESC, '') as QUOT_ITEM_PART_DESC,
        IFNULL(Q.QUOT_ITEM_PART_DESC, '') as GRID_DESC,
        IFNULL(FORMAT(Q.QUOT_ITEM_QNTY,0),0) as QUOT_ITEM_QNTY,
        CASE
            WHEN Q.QUOT_UOM IS NULL or Q.QUOT_UOM = ''
            THEN 'EA'
            ELSE Q.QUOT_UOM
        END AS QUOT_UOM,
        IFNULL(CONCAT('$',Q.QUOT_RESALE),0.00) as QUOT_RESALE,
        IFNULL(CONCAT('$',FORMAT(Q.QUOT_EXT_PRICE,2)),0.00) as QUOT_EXT_PRICE,
        IFNULL(Q.QUOT_NOTES, '') as QUOT_NOTES,
        IFNULL(Q.QUOT_LOCATION, '') as QUOT_LOCATION,
        IFNULL(Q.QUOT_LEAD_TIME,'') as QUOT_LEAD_TIME,
        IFNULL(FORMAT(Q.QUOT_WEIGHT,2),0.00) as QUOT_WEIGHT,
        IFNULL(C.COMP_NAME, '') as QUOT_PRINCIPAL,
        Q.QUOT_PRINCIPAL as QUOT_PRINCIPAL_ID,
        CASE 
        WHEN Q.QUOT_IMG_URL IS NULL OR Q.QUOT_IMG_URL = '' 
        THEN PM.PROD_IMG_URL 
        ELSE Q.QUOT_IMG_URL END AS QUOT_IMG_URL,
         CASE 
        WHEN Q.QUOT_SPEC_URL IS NULL OR Q.QUOT_SPEC_URL = '' 
        THEN PM.PROD_SPEC_URL 
        ELSE Q.QUOT_SPEC_URL END AS PROD_SPEC_URL
        FROM QUOTES_DTL Q JOIN COMPANIES C ON Q.QUOT_PRINCIPAL =  C.COMP_ID
        LEFT JOIN RF_DBD_DB_${tenantId}.PRODUCTS_MST PM
        ON PM.PROD_PRINCI_ID = Q.QUOT_PRINCIPAL AND
        PM.PROD_PRINCI_PARTNO = Q.QUOT_ITEM_PART_MANF
        WHERE QUOT_HDR_ID =${quotHeaderId} ${orderBy}`
    )

    if (GroupByManfcture == 1) {
        groups = headerDtl.reduce(
            (arrayGroup, item) => ({
                ...arrayGroup,
                [item.QUOT_PRINCIPAL]: [
                    ...(arrayGroup[item.QUOT_PRINCIPAL] || []),
                    item
                ]
            }),
            {}
        )

        if (partToLinkSpec) {
            for (const key in groups) {
                var items = groups[key]
                for await (const item of items) {
                    item.QUOT_ITEM_PART_MANF = item.PROD_SPEC_URL
                        ? `<a href="${item.PROD_SPEC_URL}" style="color:blue">${item.QUOT_ITEM_PART_MANF}</a>`
                        : item.QUOT_ITEM_PART_MANF
                }
            }
        }
    } else {
        groups = headerDtl
        if (partToLinkSpec) {
            groups.forEach(function (item) {
                item.QUOT_ITEM_PART_MANF = item.PROD_SPEC_URL
                    ? `<a href="${item.PROD_SPEC_URL}" style="color:blue">` +
                      item.QUOT_ITEM_PART_MANF +
                      '</a>'
                    : item.QUOT_ITEM_PART_MANF
            })
        }
    }

    return groups
}

async function getImageURL(key) {
    if (key) {
        return s3.getSignedUrl('getObject', {
            Key: key,
            Bucket: process.env.S3_BUCKET,
            Expires: 3900
        })
    }
}

async function groupByManufacture(partLinkToSpec) {
    return [
        {
            Intp10092019: [
                {
                    QUOT_ITEM_PART_MANF:
                        partLinkToSpec == 1
                            ? `<a href="https://google.com">ENG 10 09 2019</a>`
                            : 'ENG 10 09 2019',
                    QUOT_ITEM_PART_CUST: '7657849',
                    QUOT_ITEM_PART_DESC: 'Axis Fixation Beam, 7.5 x 155 mm',
                    GRID_DESC: 'Axis Fixation Beam, 7.5 x 155 mm',
                    QUOT_ITEM_QNTY: '10',
                    QUOT_UOM: 'EA',
                    QUOT_RESALE: '$26,999.99',
                    QUOT_EXT_PRICE: '$269,999.99',
                    QUOT_NOTES:
                        'Et reiciendis similique sed quam aliquam et deleniti unde hic autem consequatur eos libero dignissimos. Ut sint animi aut voluptatem quia 33 natus quia.',
                    QUOT_LEAD_TIME: '100',
                    QUOT_WEIGHT: '0.00',
                    QUOT_PRINCIPAL: 'Intp10092019',
                    QUOT_LOCATION: 'WC-1',
                    QUOT_IMG_URL: 'https://picsum.photos/200/300',
                    PROD_SPEC_URL: 'https://google.com'
                },
                {
                    QUOT_ITEM_PART_MANF: partLinkToSpec
                        ? `<a href="https://google.com">130-75155</a>`
                        : '130-75155',
                    QUOT_ITEM_PART_CUST: '258369147',
                    QUOT_ITEM_PART_DESC: '7 SS fan blade M8 threading',
                    GRID_DESC: '7 SS fan blade M8 threading',
                    QUOT_ITEM_QNTY: '5',
                    QUOT_UOM: '',
                    QUOT_RESALE: '$125,000.99',
                    QUOT_EXT_PRICE: '$135,000.0',
                    QUOT_NOTES:
                        'Sed quasi velit aut quia tenetur est quia modi est optio repudiandae sed ipsum exercitationem. Cum eveniet dolor qui autem corrupti est voluptatem quae a veniam aperiam ut velit deleniti.',
                    QUOT_LEAD_TIME: '100',
                    QUOT_WEIGHT: '10.00',
                    QUOT_PRINCIPAL: 'Intp10092019',
                    QUOT_LOCATION: 'Sink-1',
                    QUOT_IMG_URL: 'https://picsum.photos/200',
                    PROD_SPEC_URL: 'https://google.com'
                },
                {
                    QUOT_ITEM_PART_MANF: partLinkToSpec
                        ? `<a href="https://google.com">893-1388</a>`
                        : '893-1388',
                    QUOT_ITEM_PART_CUST: '1010',
                    QUOT_ITEM_PART_DESC: 'Healing salve 4 x 4',
                    GRID_DESC: 'Healing salve 4 x 4',
                    QUOT_ITEM_QNTY: '10',
                    QUOT_UOM: '',
                    QUOT_RESALE: '$26,999.99',
                    QUOT_EXT_PRICE: '$269,999.99',
                    QUOT_NOTES:
                        "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.",
                    QUOT_LEAD_TIME: '100',
                    QUOT_WEIGHT: '20.55',
                    QUOT_PRINCIPAL: 'Intp10092019',
                    QUOT_LOCATION: 'WC-1',
                    QUOT_IMG_URL: 'https://picsum.photos/id/237/200/300',
                    PROD_SPEC_URL: 'https://google.com'
                },
                {
                    QUOT_ITEM_PART_MANF: partLinkToSpec
                        ? `<a href="https://google.com">HT5F9AS</a>`
                        : 'HT5F9AS',
                    QUOT_ITEM_PART_CUST: '5462',
                    QUOT_ITEM_PART_DESC: 'lenevo laptops description',
                    GRID_DESC: 'lenevo laptops description',
                    QUOT_ITEM_QNTY: '29',
                    QUOT_UOM: '',
                    QUOT_RESALE: '$25.00',
                    QUOT_EXT_PRICE: '$725.00',
                    QUOT_NOTES: '',
                    QUOT_LEAD_TIME: '100',
                    QUOT_WEIGHT: '30.00',
                    QUOT_PRINCIPAL: 'Intp10092019',
                    QUOT_LOCATION: 'Sink-1',
                    QUOT_IMG_URL: 'https://picsum.photos/seed/picsum/200/300',
                    PROD_SPEC_URL: 'https://google.com'
                },
                {
                    QUOT_ITEM_PART_MANF: partLinkToSpec
                        ? `<a href="https://google.com">ENG 10 09 2019</a>`
                        : 'ENG 10 09 2019',
                    QUOT_ITEM_PART_CUST: '54628',
                    QUOT_ITEM_PART_DESC: '  lenevo laptops description',
                    GRID_DESC: '  lenevo laptops description',
                    QUOT_ITEM_QNTY: '12',
                    QUOT_UOM: '',
                    QUOT_RESALE: '$26,999.99',
                    QUOT_EXT_PRICE: '$323,999.99',
                    QUOT_NOTES: '',
                    QUOT_LEAD_TIME: '100',
                    QUOT_WEIGHT: '12.33',
                    QUOT_PRINCIPAL: 'Intp10092019',
                    QUOT_LOCATION: 'Sink-1',
                    QUOT_IMG_URL: 'https://picsum.photos/200/300/?blur=2',
                    PROD_SPEC_URL: 'https://google.com'
                }
            ],
            P13092019: [
                {
                    QUOT_ITEM_PART_MANF: partLinkToSpec
                        ? `<a href="https://google.com">ABC-30938</a>`
                        : 'ABC-30938',
                    QUOT_ITEM_PART_CUST: '12579',
                    QUOT_ITEM_PART_DESC: 'Grip threading, 4x4',
                    GRID_DESC: 'Grip threading, 4x4',
                    QUOT_ITEM_QNTY: '8',
                    QUOT_UOM: 'EA',
                    QUOT_RESALE: '$26,999.12',
                    QUOT_EXT_PRICE: '$27,999.99',
                    QUOT_NOTES: '',
                    QUOT_LEAD_TIME: '100',
                    QUOT_WEIGHT: '23.22',
                    QUOT_PRINCIPAL: 'P13092019',
                    QUOT_LOCATION: 'WC-1',
                    QUOT_IMG_URL: 'https://picsum.photos/200/300/?blur=2',
                    PROD_SPEC_URL: 'https://google.com'
                }
            ]
        }
    ]
}

async function dynamicSerialNumber(tableData, options) {
    tableData.map((item, i) => {
        if (item['QUOT_ITEM_PART_MANF'] != undefined) {
            const firstIndex = item['QUOT_ITEM_PART_MANF'].search('firstIndex')
            if (options.hideLine == 1) {
                item.SN = '<td style="display: none;">' + `${i + 1}` + '</td>'
            } else {
                item.SN =
                    firstIndex == -1
                        ? '<td>' + `${i + 1}` + '</td>'
                        : '<td style="padding-top:24px!important">' +
                          `${i + 1}` +
                          '</td>'
            }
        } else {
            if (options.hideLine == 1) {
                item.SN = '<td style="display: none;">' + `${i + 1}` + '</td>'
            } else {
                item.SN = '<td>' + `${i + 1}` + '</td>'
            }
        }
    })
    return tableData
}

async function preparedBy(userId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let userName, email, phone, cell

    if (userId == -1) {
        return {
            userName: 'twdevelopment_user',
            email: '<EMAIL>',
            phone: '123456'
        }
    }

    const [userResult] = await sequelize.query(`SELECT U.USER_NAME, 
    IFNULL(UE.USER_EMAIL_LOGIN,'') as USER_EMAIL_LOGIN FROM USERS U
    LEFT JOIN USER_EMAIL_CONFIG UE ON UE.USER_EMAIL_USER_ID=U.USER_ID
     WHERE U.USER_ID=${userId}`)

    const [result] = await sequelize.query(`SELECT  CONT_FULL_NAME, 
    IFNULL(CONT_PHONE_BUSINESS,'') AS CONT_PHONE_BUSINESS, IFNULL(CONT_EMAIL_BUSINESS,'') AS CONT_EMAIL_BUSINESS, 
    IFNULL(CONT_PHONE_MOBILE,'') AS  CONT_PHONE_MOBILE FROM CONTACTS
     WHERE CONT_EMAIL_BUSINESS IN (SELECT USER_EMAIL_LOGIN FROM USER_EMAIL_CONFIG
     WHERE USER_EMAIL_USER_ID=${userId})`)

    if (result.length == 1) {
        userName = result[0].CONT_FULL_NAME
        email = result[0].CONT_EMAIL_BUSINESS
        phone = result[0].CONT_PHONE_BUSINESS
        cell = result[0].CONT_PHONE_MOBILE
    } else {
        userName = userResult.length > 0 ? userResult[0].USER_NAME : ''
        email = userResult.length > 0 ? userResult[0].USER_EMAIL_LOGIN : ''
        phone = ''
        cell = ''
    }
    return { userName, email, phone, cell }
}

async function accountManager(quotHdrId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let accountManagerDetails
    const [salesTeamMember] = await sequelize.query(`SELECT SGT_MEMBER_ID
    FROM QUOTES_HDR Q 
    JOIN COMPANIES C ON C.COMP_ID = Q.QUOT_CUSTOMER
    JOIN SALES_GROUP_TEAM S ON S.SGT_SMAN_ID =  C.COMP_SMAN_ID
    WHERE Q.REC_ID=${quotHdrId} AND S.SGT_LEADER_FLAG = 1`)

    if (salesTeamMember.length > 0) {
        accountManagerDetails = await preparedBy(
            salesTeamMember[0].SGT_MEMBER_ID, req
        )
    }

    return accountManagerDetails
}

const pdfLikeExcel = async (
    dynamicData,
    quoteDetails,
    image,
    imgPath,
    yoxel,
    res,
    next
) => {
    try {
        const {
            headers,
            address,
            pdfData,
            preparedFor,
            companyDetail,
            preparedByData,
            customerDeatil,
            secondaryCustomerDetail,
            options,
            secondaryCustomerLbl
        } = dynamicData

        const workbook = new ExcelJs.Workbook()
        let worksheet = await excelPageSetup(workbook)
        const showLine = options.hideLine == 1 ? 'B2:C5' : 'A2:B5'
        worksheet.addImage(
            await excelImage(workbook, image, imgPath),
            showLine,
            {
                editAs: 'oneCell'
            }
        )

        const heading1 = `${address.PARAM_SUBS_NAME}`
        const heading2 = 'Quotation'
        const preparedForData = await preparedForDataFnc(
            preparedFor,
            companyDetail
        )

        const quoteNoDateExp = [
            'Quote No.:',
            `${pdfData.QUOT_NUMBER}`,
            'Quote Date:',
            `${dayjs(pdfData.QUOT_DATE).format('MM-DD-YYYY')}`,
            'Expiration Date:',
            `${dayjs(pdfData.QUOT_EXPIRY).format('MM-DD-YYYY')}`
        ]

        let header = []
        headers.map((item) => {
            header.push(item.label)
        })

        if (options.superQuote && options.groupByManufacture) {
            Object.keys(quoteDetails).forEach((item) => {
                quoteDetails[item].forEach((item1) => {
                    pickExcel(item1, ...header)
                })
            })

            let { lineItemNote, noWrapDescription, principalDeatils, result } =
                await superQuoteDetailsExcel(quoteDetails, header, options)

            result.forEach((item, index) => {
                item.SN = index + 1
            })

            let columns = await excelColumns(headers)

            const headerObj = headers.reduce((acc, val) => {
                acc[val['label']] = val['fieldName']
                return acc
            }, {})
            result.unshift(headerObj)

            const grandTotal = 'Grand Total:'
            const totalVlaue = pdfData.GRAND_TOTAL

            const params = {
                res,
                image,
                heading1,
                heading2,
                preparedForData,
                quoteNoDateExp,
                grandTotal,
                totalVlaue,
                columns,
                quoteDetails,
                result,
                address,
                companyDetail,
                preparedByData,
                options,
                customerDeatil,
                secondaryCustomerDetail,
                pdfData,
                worksheet,
                workbook,
                noWrapDescription,
                header,
                principalDeatils,
                lineItemNote,
                secondaryCustomerLbl,
                yoxel
            }
            await customExportSuperQuotes(params)
        } else {
            quoteDetails.map((item) => pickExcel(item, ...header))
            let principalDeatils = []
            let { lineItemNote, noWrapDescription, result } =
                await normalQuoteDetailsExcel(quoteDetails, header, options)

            result.forEach((item, index) => {
                item.SN = index + 1
            })

            let columns = await excelColumns(headers)
            const headerObj = headers.reduce((acc, val) => {
                acc[val['label']] = val['fieldName']
                return acc
            }, {})

            result.unshift(headerObj)
            const grandTotal = 'Grand Total:'
            const totalVlaue = pdfData.GRAND_TOTAL

            const params = {
                res,
                image,
                heading1,
                heading2,
                preparedForData,
                quoteNoDateExp,
                grandTotal,
                totalVlaue,
                columns,
                quoteDetails,
                result,
                address,
                companyDetail,
                preparedByData,
                options,
                customerDeatil,
                secondaryCustomerDetail,
                pdfData,
                worksheet,
                workbook,
                noWrapDescription,
                header,
                headers,
                lineItemNote,
                principalDeatils,
                secondaryCustomerLbl,
                yoxel
            }

            await customExport(params)
        }
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const customExport = async (params) => {
    let {
        res,
        grandTotal,
        columns,
        result,
        options,
        pdfData,
        worksheet,
        workbook,
        noWrapDescription,
        header,
        lineItemNote,
        principalDeatils,
        yoxel
    } = params
    try {
        const { worksheetHeader } = await headerSection(params)
        worksheet = worksheetHeader
        worksheet.columns = columns

        result.forEach((quoteData) => {
            worksheet.addRow(quoteData)
        })

        let [lastItem] = result.slice(-1)
        lastItem.SN = lastItem.SN == 'Line' ? 0 : lastItem.SN
        let tableRowNumber = await tableRowNumberFnc(worksheet)
        const grandTotalRow =
            tableRowNumber +
            lastItem.SN +
            noWrapDescription.length +
            lineItemNote.length +
            2

        let dynamicTotalParams = {
            worksheet,
            tableRowNumber,
            lastItem,
            noWrapDescription,
            columns,
            grandTotal,
            grandTotalRow,
            lineItemNote,
            principalDeatils,
            result,
            options,
            pdfData,
            header
        }
        if (result.length <= 1) {
            let emptyTableParams = {
                res,
                worksheet,
                header,
                result,
                tableRowNumber,
                pdfData,
                options,
                lastItem,
                noWrapDescription,
                lineItemNote
            }

            worksheet = await displayEmptyTableDataExcel(emptyTableParams)
            worksheet = await dynamicTotalAmount(dynamicTotalParams)
            if (options.showTotalWeight) {
                worksheet = await dynamicTotalWeight(dynamicTotalParams)
            }
            worksheet.getRow(grandTotalRow).eachCell((cell) => {
                cell.font = { bold: true, name: 'Arial', family: 2, size: 12 }
            })
            worksheet = await removeDotFromNote(
                worksheet,
                options,
                pdfData,
                grandTotalRow
            )

            if (options.showTotalWeight) {
                const weightValueCell = worksheet.getColumn(
                    columns.length
                ).letter
                const weightCell = Number(grandTotalRow + 1)
                worksheet.getCell(`${weightValueCell}${weightCell}`).font = {
                    bold: true,
                    name: 'Arial',
                    family: 2,
                    size: 12
                }
            }
            await generateExcelFile(res, workbook, yoxel)
            return
        }

        let rowIndex = tableRowNumber + 2
        let lineItemRowIndex =
            options.superQuote && options.groupByManufacture
                ? tableRowNumber + 3
                : tableRowNumber + 2

        if (options.noteVisible) {
            let lineNoteParams = {
                worksheet,
                header,
                lineItemNote,
                lineItemRowIndex
            }
            const { worksheetLineNote } = await displayNoteExcel(lineNoteParams)

            worksheet = worksheetLineNote
        }

        if (noWrapDescription.length > 0) {
            let descParams = {
                worksheet,
                rowIndex,
                noWrapDescription,
                header,
                options,
                result,
                tableRowNumber,
                columns
            }
            const { workSheetDescriptionExcel } =
                await descriptionBelowPartNoExcel(descParams)
            worksheet = workSheetDescriptionExcel
        }

        let footerParams = {
            worksheet,
            header,
            grandTotalRow,
            pdfData,
            options
        }

        const { worksheetFooterExcel } = await footerExcel(footerParams)
        worksheet = worksheetFooterExcel
        worksheet = await dynamicTotalAmount(dynamicTotalParams)
        let dynamicUnitPriceParams = {
            worksheet,
            tableRowNumber,
            lastItem,
            noWrapDescription,
            lineItemNote,
            principalDeatils,
            result,
            header,
            options
        }
        worksheet = await dynamicUnitPrice(dynamicUnitPriceParams)

        if (options.partLinkToSpec) {
            let convertToLinkParams = {
                worksheet,
                tableRowNumber,
                lastItem,
                noWrapDescription,
                lineItemNote,
                principalDeatils,
                result,
                header
            }
            worksheet = await convertToLink(convertToLinkParams)
        }

        let convertImageParams = {
            worksheet,
            workbook,
            tableRowNumber,
            lastItem,
            noWrapDescription,
            lineItemNote,
            principalDeatils,
            result,
            header
        }
        worksheet = await converToImage(convertImageParams)

        if (options.showTotalWeight) {
            worksheet = await dynamicTotalWeight(dynamicTotalParams)
        }
        worksheet.eachRow(function (row, rowNumber) {
            if (rowNumber == tableRowNumber) {
                row.height = 20
            }
            if (rowNumber != 2) {
                row.eachCell(function (cell) {
                    cell.width = 50
                    cell.font = {
                        name: 'Arial',
                        family: 2,
                        size: 12
                    }
                })
            }
        })

        worksheet.getRow(grandTotalRow).eachCell((cell) => {
            cell.font = { bold: true, name: 'Arial', family: 2, size: 12 }
        })

        worksheet = await excelTabelHeader(
            worksheet,
            tableRowNumber,
            header,
            result
        )

        let alignExcelTableFieldParams = {
            worksheet,
            tableRowNumber,
            grandTotalRow,
            header,
            principalDeatils,
            result
        }
        worksheet = await mergeLineNoteRowExcel(worksheet)
        worksheet = await alignExcelTableFieldValues(alignExcelTableFieldParams)

        if (options.hideLine == 1) {
            worksheet.getColumn(1).hidden = true
        }

        worksheet = await hideEmptyLineNoteRow(worksheet)
        worksheet = await removeDotFromNote(
            worksheet,
            options,
            pdfData,
            grandTotalRow
        )
        if (options.showTotalWeight) {
            const weightValueCell = worksheet.getColumn(columns.length).letter
            const weightCell = Number(grandTotalRow + 1)
            worksheet.getCell(`${weightValueCell}${weightCell}`).font = {
                bold: true,
                name: 'Arial',
                family: 2,
                size: 12
            }
        }
        worksheet = await alignMergedLines(
            worksheet,
            grandTotalRow,
            tableRowNumber
        )
        worksheet = await removeEmptyLines(
            worksheet,
            grandTotalRow,
            tableRowNumber
        )
        await generateExcelFile(res, workbook, yoxel)
        return
    } catch (error) {
        res.status(200).json(error.message)
    }
}

const customExportSuperQuotes = async (params) => {
    let {
        res,
        grandTotal,
        columns,
        quoteDetails,
        result,
        options,
        pdfData,
        worksheet,
        workbook,
        noWrapDescription,
        header,
        principalDeatils,
        lineItemNote,
        yoxel
    } = params
    try {
        const { worksheetHeader } = await headerSection(params)
        worksheet = worksheetHeader
        worksheet.columns = columns
        result.forEach((quoteData) => {
            worksheet.addRow(quoteData)
        })

        let [lastItem] = result.slice(-1)
        lastItem.SN = lastItem.SN == 'Line' ? 0 : lastItem.SN
        let tableRowNumber = await tableRowNumberFnc(worksheet)
        const grandTotalRow =
            tableRowNumber +
            lastItem.SN +
            noWrapDescription.length +
            lineItemNote.length +
            principalDeatils.length +
            2

        let dynamicTotalParams = {
            worksheet,
            tableRowNumber,
            lastItem,
            noWrapDescription,
            columns,
            grandTotal,
            grandTotalRow,
            lineItemNote,
            principalDeatils,
            result,
            options,
            pdfData,
            header
        }
        if (result.length <= 1) {
            let emptyTableParams = {
                res,
                worksheet,
                header,
                result,
                tableRowNumber,
                pdfData,
                options,
                lastItem,
                noWrapDescription,
                lineItemNote
            }

            worksheet = await displayEmptyTableDataExcel(emptyTableParams)
            worksheet = await dynamicTotalAmount(dynamicTotalParams)
            if (options.showTotalWeight) {
                worksheet = await dynamicTotalWeight(dynamicTotalParams)
            }
            worksheet.getRow(grandTotalRow).eachCell((cell) => {
                cell.font = { bold: true, name: 'Arial', family: 2 }
            })
            worksheet = await removeDotFromNote(
                worksheet,
                options,
                pdfData,
                grandTotalRow
            )
            if (options.showTotalWeight) {
                const weightValueCell = worksheet.getColumn(
                    columns.length
                ).letter
                const weightCell = Number(grandTotalRow + 1)
                worksheet.getCell(`${weightValueCell}${weightCell}`).font = {
                    bold: true,
                    name: 'Arial',
                    family: 2,
                    size: 12
                }
            }

            await generateExcelFile(res, workbook, yoxel)
            return
        }
        let rowIndex = tableRowNumber + 2
        let principalRowIndex = tableRowNumber + 1
        let lineItemRowIndex = tableRowNumber + 2
        let principalIndexPositions = []

        if (options.noteVisible) {
            let lineNoteParams = {
                worksheet,
                header,
                lineItemNote,
                lineItemRowIndex
            }

            const { worksheetLineNote } = await displayNoteExcel(lineNoteParams)
            worksheet = worksheetLineNote
        }

        if (noWrapDescription.length > 0) {
            let descParams = {
                worksheet,
                rowIndex,
                noWrapDescription,
                header,
                options,
                result,
                tableRowNumber,
                columns
            }
            const { workSheetDescriptionExcel } =
                await descriptionBelowPartNoExcel(descParams)
            worksheet = workSheetDescriptionExcel
        }

        if (principalDeatils.length > 0) {
            let principalKeyParams = {
                worksheet,
                header,
                quoteDetails,
                principalDeatils,
                principalRowIndex,
                options
            }

            const { worksheetPrincipalKey, principalIndexPositionsfnc } =
                await principalDeatilKeyExcel(principalKeyParams)
            worksheet = worksheetPrincipalKey
            principalIndexPositions = principalIndexPositionsfnc
        }

        const partNoPosition = header.indexOf('QUOT_ITEM_PART_MANF')
        let firstColumnCharacter = String.fromCharCode(
            partNoPosition + 'A'.charCodeAt(0)
        )

        if (partNoPosition != -1) {
            worksheet = await principalIndexPositionsFnc(
                worksheet,
                principalIndexPositions,
                firstColumnCharacter
            )
        }
        let footerParams = {
            worksheet,
            header,
            grandTotalRow,
            pdfData,
            options
        }

        const { worksheetFooterExcel } = await footerExcel(footerParams)
        worksheet = worksheetFooterExcel
        worksheet = await dynamicTotalAmount(dynamicTotalParams)
        let dynamicUnitPriceParams = {
            worksheet,
            tableRowNumber,
            lastItem,
            noWrapDescription,
            lineItemNote,
            principalDeatils,
            result,
            header,
            options
        }
        worksheet = await dynamicUnitPrice(dynamicUnitPriceParams)

        if (options.partLinkToSpec) {
            let convertToLinkParams = {
                worksheet,
                tableRowNumber,
                lastItem,
                noWrapDescription,
                lineItemNote,
                principalDeatils,
                result,
                header
            }
            worksheet = await convertToLink(convertToLinkParams)
        }

        let convertImageParams = {
            worksheet,
            workbook,
            tableRowNumber,
            lastItem,
            noWrapDescription,
            lineItemNote,
            principalDeatils,
            result,
            header
        }
        worksheet = await converToImage(convertImageParams)

        if (options.showTotalWeight) {
            worksheet = await dynamicTotalWeight(dynamicTotalParams)
        }

        worksheet.eachRow(function (row, rowNumber) {
            if (rowNumber == tableRowNumber) {
                row.height = 20
            }
            if (rowNumber != 2 && rowNumber != grandTotalRow + 2) {
                row.eachCell(function (cell) {
                    cell.width = 80
                    cell.font = {
                        name: 'Arial',
                        family: 2,
                        size: 12
                    }
                })
            }
        })

        worksheet.getRow(grandTotalRow).eachCell((cell) => {
            cell.font = {
                bold: true,
                name: 'Arial',
                family: 2,
                size: 12,
                color: { argb: '000000' }
            }
        })

        worksheet = await excelTabelHeader(
            worksheet,
            tableRowNumber,
            header,
            result
        )

        let alignExcelTableFieldParams = {
            worksheet,
            tableRowNumber,
            grandTotalRow,
            header,
            principalDeatils,
            result
        }
        worksheet = await mergeLineNoteRowExcel(worksheet)
        worksheet = await alignExcelTableFieldValues(alignExcelTableFieldParams)
        if (options.hideLine == 1) {
            worksheet.getColumn(1).hidden = true
        }
        worksheet = await hideEmptyLineNoteRow(worksheet)
        worksheet = await removeDotFromNote(
            worksheet,
            options,
            pdfData,
            grandTotalRow
        )

        if (options.showTotalWeight) {
            const weightValueCell = worksheet.getColumn(columns.length).letter
            const weightCell = Number(grandTotalRow + 1)
            worksheet.getCell(`${weightValueCell}${weightCell}`).font = {
                bold: true,
                name: 'Arial',
                family: 2,
                size: 12
            }
        }

        worksheet = await alignMergedLines(
            worksheet,
            grandTotalRow,
            tableRowNumber
        )
        worksheet = await removeEmptyLines(
            worksheet,
            grandTotalRow,
            tableRowNumber
        )
        await generateExcelFile(res, workbook, yoxel)
        return
    } catch (error) {
        return res.status(500).json(error.message)
    }
}

async function excelImage(workbook, image, imgPath) {
    const imageLink = imgPath != undefined ? imgPath : image
    const response = await axios.get(imageLink, {
        responseType: 'arraybuffer'
    })
    const dimensions = sizeOf(response.data)
    return workbook.addImage({
        buffer: response.data,
        extension: dimensions.type
    })
}

async function getPreparedFor(counter, worksheet, preparedForData, showLine) {
    const firstCell = showLine ? 'A' : 'B'
    worksheet.getCell(`${firstCell}9`).value = preparedForData[0]
    if (preparedForData[1]) {
        counter += 1
        worksheet.getCell(`${firstCell}${9 + counter}`).value =
            preparedForData[1]
    }
    if (preparedForData[2]) {
        counter += 1
        worksheet.getCell(`${firstCell}${9 + counter}`).value =
            preparedForData[2]
    }

    if (preparedForData[3]) {
        counter += 1
        worksheet.getCell(`${firstCell}${9 + counter}`).value =
            preparedForData[3]
    }

    if (preparedForData[4]) {
        counter += 1
        worksheet.getCell(
            `${firstCell}${9 + counter}`
        ).value = `${preparedForData[4]}  ${preparedForData[5]}  ${preparedForData[6]}`
    }

    return {
        worksheetPreparedFor: worksheet,
        counterPreparedFor: counter
    }
}

async function getPdfQuotes(
    worksheet,
    quoteNoDateExp,
    headerRightCell,
    headerRightDataCell
) {
    worksheet.getCell(`${headerRightCell}9`).value = quoteNoDateExp[0]
    worksheet.getCell(`${headerRightCell}9`).alignment = {
        vertical: 'top',
        horizontal: 'right'
    }

    worksheet.getCell(`${headerRightDataCell}9`).value = {
        richText: [
            {
                font: {
                    bold: true,
                    name: 'Arial',
                    family: 2,
                    size: 12
                },
                text: quoteNoDateExp[1]
            }
        ]
    }
    worksheet.getCell(`${headerRightDataCell}9`).alignment = {
        vertical: 'top',
        horizontal: 'left'
    }
    worksheet.getCell(`${headerRightCell}10`).value = quoteNoDateExp[2]
    worksheet.getCell(`${headerRightCell}10`).alignment = {
        vertical: 'top',
        horizontal: 'right'
    }
    worksheet.getCell(`${headerRightDataCell}10`).value = quoteNoDateExp[3]
    worksheet.getCell(`${headerRightDataCell}10`).alignment = {
        vertical: 'top',
        horizontal: 'left'
    }
    worksheet.getCell(`${headerRightCell}11`).value = quoteNoDateExp[4]
    worksheet.getCell(`${headerRightCell}11`).alignment = {
        vertical: 'top',
        horizontal: 'right'
    }
    worksheet.getCell(`${headerRightDataCell}11`).value = quoteNoDateExp[5]
    worksheet.getCell(`${headerRightDataCell}11`).alignment = {
        vertical: 'top',
        horizontal: 'left'
    }
    return worksheet
}

async function getPreparedBy(worksheet, counter, preparedByData, showLine) {
    const firstCell = showLine ? 'A' : 'B'
    counter += 2
    worksheet.getCell(`${firstCell}${9 + counter}`).value = 'Prepared By: '
    if (preparedByData.userName) {
        counter += 1
        worksheet.getCell(`${firstCell}${9 + counter}`).value =
            preparedByData.userName
    }

    if (preparedByData.phone) {
        counter += 1
        worksheet.getCell(`${firstCell}${9 + counter}`).value =
            'Phone: ' + preparedByData.phone
    }

    if (preparedByData.email) {
        counter += 1
        worksheet.getCell(`${firstCell}${9 + counter}`).value =
            'Email: ' + preparedByData.email
    }

    return {
        worksheetPareparedBy: worksheet,
        counterPerparedBy: counter
    }
}

async function getCustomerDetails(
    worksheet,
    counter,
    customerDeatil,
    showLine
) {
    const firstCell = showLine ? 'A' : 'B'
    counter += 2
    worksheet.getCell(`${firstCell}${9 + counter}`).value = 'Customer: '
    counter += 1
    worksheet.getCell(`${firstCell}${9 + counter}`).value =
        customerDeatil[0].COMP_NAME

    if (customerDeatil[0].COMP_ADDRESS_1) {
        counter += 1
        worksheet.getCell(`${firstCell}${9 + counter}`).value =
            customerDeatil[0].COMP_ADDRESS_1
    }

    if (customerDeatil[0].COMP_CITY) {
        counter += 1
        worksheet.getCell(`${firstCell}${9 + counter}`).value =
            customerDeatil[0].COMP_CITY
    }

    if (customerDeatil[0].COMP_STATE) {
        counter += 1
        worksheet.getCell(`${firstCell}${9 + counter}`).value =
            customerDeatil[0].COMP_STATE
    }

    if (customerDeatil[0].COMP_ZIP_CODE) {
        counter += 1
        worksheet.getCell(`${firstCell}${9 + counter}`).value =
            customerDeatil[0].COMP_ZIP_CODE
    }

    return {
        worksheetCustomerDetails: worksheet,
        counterCustomerDetails: counter
    }
}

async function getSecondaryCustomer(
    worksheet,
    counter,
    secondaryCustomerDetail,
    showLine,
    secondaryCustomerLbl,
    options
) {
    const firstCell = showLine ? 'A' : 'B'

    counter = options.topicVisible == 1 ? counter + 1 : counter + 2
    worksheet.getCell(
        `${firstCell}${9 + counter}`
    ).value = `${secondaryCustomerLbl}: ${secondaryCustomerDetail[0].COMP_NAME}`

    return {
        worksheetSecondaryCustomer: worksheet,
        counterSecondaryCustomer: counter
    }
}

async function getTopicVisible(
    worksheet,
    counter,
    pdfData,
    showLine,
    addCounter
) {
    const firstCell = showLine ? 'A' : 'B'
    counter += 2
    counter += addCounter
    worksheet.getCell(`${firstCell}${9 + counter}`).value =
        'Topic: ' + pdfData.QUOT_CUST_PROGRAM
    return {
        worksheetTopicVisible: worksheet,
        counterTopicVisible: counter
    }
}

async function mapTotalValue(obj, key) {
    if (key == 'QUOT_EXT_PRICE') {
        obj['QUOT_EXT_PRICE'] = obj['QUOT_EXT_PRICE']
            ? parseFloat(
                  obj['QUOT_EXT_PRICE']
                      .toString()
                      .replace('$', '')
                      .replace(/,/g, '')
              )
            : 0
    }

    if (key == 'QUOT_RESALE') {
        obj['QUOT_RESALE'] = obj['QUOT_RESALE']
            ? parseFloat(
                  obj['QUOT_RESALE']
                      .toString()
                      .replace('$', '')
                      .replace(/,/g, '')
              )
            : 0
    }

    if (key == 'QUOT_WEIGHT') {
        obj['QUOT_WEIGHT'] =
            obj['QUOT_WEIGHT'] && obj['QUOT_WEIGHT'] != 0
                ? parseFloat(obj['QUOT_WEIGHT'].toString().replace(/,/g, ''))
                : 0
    }
}

async function dynamicTotalAmount(dynamicTotalParams) {
    let {
        worksheet,
        tableRowNumber,
        lastItem,
        noWrapDescription,
        columns,
        grandTotal,
        grandTotalRow,
        lineItemNote,
        principalDeatils,
        result,
        pdfData,
        header
    } = dynamicTotalParams
    const grandTotalLetter = worksheet.getColumn(columns.length - 1).letter
    const grandTotalValueLetter = worksheet.getColumn(columns.length).letter
    const grandTotalLabelCell = worksheet.getCell(
        `${grandTotalLetter}` + grandTotalRow
    )
    grandTotalLabelCell.value = grandTotal
    grandTotalLabelCell.alignment = {
        vertical: 'middle',
        horizontal: 'right'
    }

    if (header.includes('QUOT_EXT_PRICE')) {
        let foundCell = null
        worksheet.eachRow((row, rowIndex) => {
            row.eachCell((cell, colIndex) => {
                if (cell.value == result[0].QUOT_EXT_PRICE) {
                    foundCell = {
                        row: rowIndex,
                        col: colIndex,
                        character: cell.address.replace(/[^A-Z]/g, '')
                    }
                }
            })
        })

        const totalAmountRows =
            tableRowNumber +
            lastItem.SN +
            noWrapDescription.length +
            lineItemNote.length +
            principalDeatils.length +
            2

        const incrementRow = principalDeatils.length > 0 ? 2 : 1
        for (let i = foundCell.row + incrementRow; i < totalAmountRows; i++) {
            const cell = worksheet.getCell(`${foundCell.character}${i}`)
            cell.numFmt = '$#,##0.00#####'
        }

        worksheet.getCell(`${grandTotalValueLetter}` + grandTotalRow).value = {
            formula: `SUM(${foundCell.character}${
                foundCell.row + incrementRow
            }:${foundCell.character}${totalAmountRows - 1})`,
            style: { numberFormat: '[$$-en-US]#,##0.00#####' }
        }
        worksheet.getCell(`${grandTotalValueLetter}` + grandTotalRow).numFmt =
            '[$$-en-US]#,##0.00#####'
    } else {
        const grandTotalCell = worksheet.getCell(
            `${grandTotalValueLetter}${grandTotalRow}`
        )
        grandTotalCell.value = pdfData.GRAND_TOTAL.replace(/[$,]/g, '')
        const cleanedValue = pdfData.GRAND_TOTAL.replace(/[$,]/g, '')
        grandTotalCell.value = parseFloat(cleanedValue)
        grandTotalCell.numFmt = '[$$-en-US]#,##0.00#####'
    }

    return worksheet
}

async function dynamicUnitPrice(dynamicUnitPriceParams) {
    let {
        worksheet,
        tableRowNumber,
        lastItem,
        noWrapDescription,
        lineItemNote,
        principalDeatils,
        result,
        header,
        options
    } = dynamicUnitPriceParams
    if (header.includes('QUOT_RESALE')) {
        let foundCell = null
        worksheet.eachRow((row, rowIndex) => {
            row.eachCell((cell, colIndex) => {
                if (cell.value == result[0].QUOT_RESALE) {
                    foundCell = {
                        row: rowIndex,
                        col: colIndex,
                        character: cell.address.replace(/[^A-Z]/g, '')
                    }
                }
            })
        })

        const totalUnitPriceRows =
            tableRowNumber +
            lastItem.SN +
            noWrapDescription.length +
            lineItemNote.length +
            principalDeatils.length +
            2

        const incrementRow = principalDeatils.length > 0 ? 2 : 1
        for (
            let i = foundCell.row + incrementRow;
            i <= totalUnitPriceRows;
            i++
        ) {
            const cell = worksheet.getCell(`${foundCell.character}${i}`)
            const excelNumFmt = createExcelNumFmt(options)
            cell.numFmt = excelNumFmt
            cell.alignment = {
                vertical: 'bottom',
                horizontal: 'right'
            }
        }
    }

    return worksheet
}

const createExcelNumFmt = (options) => {
    const decimalDigits =
        options.unitPriceDecimalDigit >= 2
            ? options.unitPriceDecimalDigit - 2
            : options.unitPriceDecimalDigit
    return `"$"#,##0.${'00'}${'#'.repeat(decimalDigits)}`
}

async function dynamicTotalWeight(dynamicTotalWeightParams) {
    let {
        worksheet,
        tableRowNumber,
        lastItem,
        noWrapDescription,
        columns,
        grandTotalRow,
        lineItemNote,
        principalDeatils,
        result,
        header,
        pdfData
    } = dynamicTotalWeightParams
    const totalWeightLetter = worksheet.getColumn(columns.length - 1).letter
    const totalWeightValueLetter = worksheet.getColumn(columns.length).letter

    const totalWeightLabelCell = worksheet.getCell(
        `${totalWeightLetter}` + Number(grandTotalRow + 1)
    )

    totalWeightLabelCell.alignment = {
        vertical: 'middle',
        horizontal: 'right'
    }

    const totalWeightValueCell = worksheet.getCell(
        `${totalWeightValueLetter}${grandTotalRow + 1}`
    )

    totalWeightLabelCell.value = {
        richText: [
            {
                font: {
                    bold: true,
                    name: 'Arial',
                    family: 2,
                    size: 12
                },
                text: 'Total Weight:'
            }
        ]
    }

    if (header.includes('QUOT_WEIGHT')) {
        let foundCell = null
        worksheet.eachRow((row, rowIndex) => {
            row.eachCell((cell, colIndex) => {
                if (cell.value == result[0].QUOT_WEIGHT) {
                    foundCell = {
                        row: rowIndex,
                        col: colIndex,
                        character: cell.address.replace(/[^A-Z]/g, '')
                    }
                }
            })
        })

        const totalUnitPriceRows =
            tableRowNumber +
            lastItem.SN +
            noWrapDescription.length +
            lineItemNote.length +
            principalDeatils.length +
            1

        const incrementRow = principalDeatils.length > 0 ? 2 : 1
        for (
            let i = foundCell.row + incrementRow;
            i <= totalUnitPriceRows;
            i++
        ) {
            const cell = worksheet.getCell(`${foundCell.character}${i}`)
            cell.numFmt = '#,##0.00#####'
            cell.alignment = {
                vertical: 'bottom',
                horizontal: 'left'
            }
        }

        const sumFormula = `IF(SUM(${foundCell.character}${
            foundCell.row + incrementRow
        }:${foundCell.character}${
            tableRowNumber +
            lastItem.SN +
            noWrapDescription.length +
            lineItemNote.length +
            principalDeatils.length +
            1
        })=0,0,SUM(${foundCell.character}${foundCell.row + incrementRow}:${
            foundCell.character
        }${
            tableRowNumber +
            lastItem.SN +
            noWrapDescription.length +
            lineItemNote.length +
            principalDeatils.length +
            1
        }))`

        worksheet.getCell(
            `${totalWeightValueLetter}` + Number(grandTotalRow + 1)
        ).value = {
            formula: sumFormula
            // style: { numberFormat: '#,##0.########' },
        }
        worksheet.getCell(
            `${totalWeightValueLetter}` + Number(grandTotalRow + 1)
        ).numFmt = '#,##0.00#####'
    } else {
        totalWeightValueCell.value = {
            richText: [
                {
                    font: {
                        bold: true,
                        name: 'Arial',
                        family: 2,
                        size: 12
                    },
                    text: pdfData.TOTAL_WEIGHT
                }
            ]
        }
    }
    totalWeightValueCell.alignment = {
        vertical: 'middle',
        horizontal: 'left'
    }
    return worksheet
}

async function headerSection(params) {
    let {
        heading1,
        heading2,
        preparedForData,
        quoteNoDateExp,
        address,
        preparedByData,
        options,
        customerDeatil,
        secondaryCustomerDetail,
        pdfData,
        worksheet,
        header,
        secondaryCustomerLbl
    } = params

    let counter = 0
    let addCounter = 0

    const calcMiddleCell =
        header.length > 5 ? header.length / 2 - 1 : header.length / 2
    const middleCell = String.fromCharCode(calcMiddleCell + 'A'.charCodeAt(0))

    const head1RightCell = String.fromCharCode(
        header.length / 2 + 1 + 'A'.charCodeAt(0)
    )
    const head1ValueRightCell = String.fromCharCode(
        header.length / 2 + 3 + 'A'.charCodeAt(0)
    )

    const moveHeaderRightCell = header.length <= 5 ? 1 : 2
    const moveHeaderRightDataCell = header.length <= 5 ? 2 : 3
    const headerRightCell = String.fromCharCode(
        header.length / 2 + moveHeaderRightCell + 'A'.charCodeAt(0)
    )

    const headerRightDataCell = String.fromCharCode(
        header.length / 2 + moveHeaderRightDataCell + 'A'.charCodeAt(0)
    )

    worksheet.mergeCells(`${head1RightCell}2:${head1ValueRightCell}2`)
    const company = worksheet.getCell(`${head1RightCell}2`)
    company.value = {
        richText: [
            {
                font: {
                    bold: true,
                    name: 'Arial',
                    family: 2,
                    size: 12
                },
                text: heading1
            }
        ]
    }
    const alignCompany = options.compInfoAlignRight ? 'right' : 'left'
    company.width = 0
    company.size = 14
    company.alignment = {
        wrapText: true,
        vertical: 'top',
        horizontal: alignCompany
    }

    worksheet.mergeCells(`${head1RightCell}3:${head1ValueRightCell}3`)
    const mergedAddressCell = worksheet.getCell(`${head1RightCell}3`)
    mergedAddressCell.value = address.PARAM_SUBS_ADDRESS
    mergedAddressCell.width = 110

    const textLength = address.PARAM_SUBS_ADDRESS
        ? address.PARAM_SUBS_ADDRESS.length
        : 0
    const numberOfLines = Math.ceil(textLength / 30)
    worksheet.getRow(3).height = numberOfLines * 20

    mergedAddressCell.alignment = {
        wrapText: true,
        vertical: 'top',
        horizontal: alignCompany
    }

    const centerHeader =
        options.titleShowManufacturer && pdfData.QUOT_MANUFACTURER
            ? pdfData.QUOT_MANUFACTURER + ' - ' + heading2
            : heading2

    const centerHeaderCell = worksheet.getCell(`${middleCell}7`)
    centerHeaderCell.value = {
        richText: [
            {
                font: {
                    bold: true,
                    name: 'Arial',
                    family: 2,
                    size: 14
                },
                text: centerHeader
            }
        ]
    }

    worksheet = await getPdfQuotes(
        worksheet,
        quoteNoDateExp,
        headerRightCell,
        headerRightDataCell
    )

    if (options.showSalesTeam && pdfData.accountManagerDetails) {
        let rowCounter = 13
        let accountManagerDetailParams = {
            worksheet,
            headerRightCell,
            headerRightDataCell,
            rowCounter,
            pdfData
        }

        const { workSheetAccountDetail, lineCounter } =
            await accountManagerDetailFnc(accountManagerDetailParams)
        worksheet = workSheetAccountDetail
        addCounter = lineCounter
    }

    const showLine = options.hideLine == 0 ? true : false
    const { worksheetPreparedFor, counterPreparedFor } = await getPreparedFor(
        counter,
        worksheet,
        preparedForData,
        showLine
    )
    worksheet = worksheetPreparedFor
    counter = counterPreparedFor

    if (options.showPreparedBy && Object.keys(preparedByData).length > 0) {
        const { worksheetPareparedBy, counterPerparedBy } = await getPreparedBy(
            worksheet,
            counter,
            preparedByData,
            showLine
        )

        worksheet = worksheetPareparedBy
        counter = counterPerparedBy
    }
    if (customerDeatil) {
        const { worksheetCustomerDetails, counterCustomerDetails } =
            await getCustomerDetails(
                worksheet,
                counter,
                customerDeatil,
                showLine
            )
        worksheet = worksheetCustomerDetails
        counter = counterCustomerDetails
    }

    if (options.topicVisible) {
        const { worksheetTopicVisible, counterTopicVisible } =
            await getTopicVisible(
                worksheet,
                counter,
                pdfData,
                showLine,
                addCounter
            )
        worksheet = worksheetTopicVisible
        counter = counterTopicVisible
    }

    if (options.showSecondaryCustomer && secondaryCustomerDetail.length > 0) {
        const { worksheetSecondaryCustomer, counterSecondaryCustomer } =
            await getSecondaryCustomer(
                worksheet,
                counter,
                secondaryCustomerDetail,
                showLine,
                secondaryCustomerLbl,
                options
            )
        worksheet = worksheetSecondaryCustomer
        counter = counterSecondaryCustomer
    }

    return {
        worksheetHeader: worksheet,
        counterHeader: counter
    }
}

async function descriptionBelowPartNoExcel(descParams) {
    let {
        worksheet,
        rowIndex,
        noWrapDescription,
        header,
        options,
        result,
        tableRowNumber,
        columns
    } = descParams
    let rowIndexPositions = []
    const partNumPosIdx = header.indexOf('QUOT_ITEM_PART_MANF')
    let partNumPos
    const lastColumn = worksheet.getColumn(columns.length).letter

    if (partNumPosIdx != -1) {
        const row = worksheet.getRow(tableRowNumber)
        const partNoLabel = result[0].QUOT_ITEM_PART_MANF
        row.eachCell((c) => {
            if (c.value == partNoLabel) {
                partNumPos = worksheet.getColumn(4).letter
            }
        })
    }
    const merge = options.descWrap && options.descBelowPartNo ? 0 : 1

    for (const [index] of Object.entries(noWrapDescription)) {
        let newRow = {}
        header.map((h) => {
            if (h == 'QUOT_ITEM_PART_MANF') {
                newRow[h] = noWrapDescription[index]
            } else {
                newRow[h] = ''
            }
        })
        rowIndexPositions.push(rowIndex)

        worksheet.spliceRows(rowIndex, 0, newRow)
        if (merge) {
            worksheet.mergeCells(
                `${partNumPos}${rowIndex}:${lastColumn}${rowIndex}`
            )
        }
        rowIndex = options.noteVisible ? rowIndex + 3 : rowIndex + 2
    }

    return {
        workSheetDescriptionExcel: worksheet,
        rowIndexPositions,
        partNumPos
    }
}

async function displayNoteExcel(lineNoteParams) {
    let lineNoteIndexPositions = []
    let { worksheet, header, lineItemNote, lineItemRowIndex } = lineNoteParams

    for (let [index] of Object.entries(lineItemNote)) {
        let lineNote = {}
        header.map((h, i) => {
            if (i == 1) {
                lineNote[h] = 'Note: ' + lineItemNote[index]
            } else {
                lineNote[h] = ''
            }
        })

        lineNoteIndexPositions.push(lineItemRowIndex)
        worksheet.spliceRows(lineItemRowIndex, 0, lineNote)
        worksheet.getRow(lineItemRowIndex).alignment = {
            vertical: 'top',
            horizontal: 'left'
        }
        lineItemRowIndex = lineItemRowIndex + 2
    }

    return {
        worksheetLineNote: worksheet,
        lineNoteIndexPositionsfnc: lineNoteIndexPositions
    }
}

async function principalDeatilKeyExcel(principalKeyParams) {
    let {
        worksheet,
        header,
        quoteDetails,
        principalDeatils,
        principalRowIndex,
        options
    } = principalKeyParams
    let principalIndexPositions = []
    for (const [index, row] of Object.entries(principalDeatils)) {
        let newPrincipalRow = {}
        header.forEach((h) => {
            if (h == 'QUOT_ITEM_PART_MANF') {
                newPrincipalRow[h] = principalDeatils[index]
            } else {
                newPrincipalRow[h] = ''
            }
        })
        principalIndexPositions.push(principalRowIndex)

        worksheet.spliceRows(principalRowIndex, 0, newPrincipalRow)

        principalRowIndex = await principalDetailsDisplayRow(
            options,
            principalRowIndex,
            quoteDetails[row]
        )
    }

    return {
        worksheetPrincipalKey: worksheet,
        principalIndexPositionsfnc: principalIndexPositions
    }
}

async function footerExcel(footerParams) {
    let { worksheet, header, grandTotalRow, pdfData, options } = footerParams
    const firstCell = options.hideLine == 0 ? 'A' : 'B'
    const footerNoteRow = options.footerNote
        ? grandTotalRow + 2
        : grandTotalRow + 1
    const footerCommentsRow =
        options.footerNote && pdfData.QUOT_COMMENTS
            ? footerNoteRow + 2
            : footerNoteRow + 1

    let footerNoteCell = String.fromCharCode(
        header.length - 1 + 'A'.charCodeAt(0)
    )

    if (options.footerNote && pdfData.QUOT_COMMENTS) {
        worksheet.getCell(`${firstCell}${footerNoteRow}`).value =
            'Note.:' + pdfData.QUOT_COMMENTS

        worksheet.mergeCells(
            `${firstCell}${footerNoteRow}:${footerNoteCell}${footerNoteRow}`
        )
        worksheet.getCell(`${firstCell}${footerNoteRow}`).alignment = {
            wrapText: true
        }

        worksheet = await rowHeight(worksheet, footerNoteRow)
        worksheet = await alignMergedLines(
            worksheet,
            footerNoteRow,
            footerNoteRow
        )
    }

    if (options.manufacturerFooterNote) {
        if (options.superQuote == 1) {
            let nextCell = footerCommentsRow
            worksheet = await rowHeight(worksheet, footerNoteRow)
            worksheet = await alignMergedLines(
                worksheet,
                footerNoteRow,
                footerNoteRow
            )
            for (const terms of pdfData.QUOTE_TERMS) {
                const quoteTerms = terms.QUOTE_TERMS
                    ? terms.QUOTE_TERMS.trim()
                    : ''

                if (quoteTerms) {
                    worksheet.getCell(`${firstCell}${nextCell}`).value = {
                        richText: [
                            {
                                font: {
                                    bold: true,
                                    name: 'Arial',
                                    family: 2,
                                    size: 12
                                },
                                text: `${
                                    quoteTerms ? terms.COMP_PRINT_NAME : ''
                                }`
                            },
                            {
                                text: '\n'
                            },
                            {
                                font: {
                                    name: 'Arial',
                                    family: 2,
                                    size: 12
                                },
                                text: `${quoteTerms}`
                            }
                        ]
                    }
                    worksheet.mergeCells(
                        `${firstCell}${nextCell}:${footerNoteCell}${nextCell}`
                    )
                    worksheet.getCell(`${firstCell}${nextCell}`).alignment = {
                        wrapText: true
                    }

                    worksheet = await rowHeight(worksheet, nextCell)
                    worksheet = await alignMergedLines(
                        worksheet,
                        nextCell,
                        nextCell
                    )

                    nextCell += 2
                }
            }
        } else {
            worksheet = await rowHeight(worksheet, footerNoteRow)
            worksheet = await alignMergedLines(
                worksheet,
                footerNoteRow,
                footerNoteRow
            )

            worksheet.mergeCells(
                `${firstCell}${footerCommentsRow}:${footerNoteCell}${footerCommentsRow}`
            )
            worksheet.getCell(`${firstCell}${footerCommentsRow}`).alignment = {
                wrapText: true
            }

            const quoteTerms =
                typeof pdfData.QUOTE_TERMS == 'object'
                    ? pdfData.QUOTE_TERMS[0]['QUOTE_TERMS'].trim()
                    : pdfData.QUOTE_TERMS
                    ? pdfData.QUOTE_TERMS.trim()
                    : ''
            worksheet.getCell(`${firstCell}${footerCommentsRow}`).value =
                quoteTerms.trim()
            worksheet = await rowHeight(worksheet, footerCommentsRow)
            worksheet = await alignMergedLines(
                worksheet,
                footerCommentsRow,
                footerCommentsRow
            )
        }
    }

    return {
        worksheetFooterExcel: worksheet
    }
}

async function excelTabelHeader(worksheet, tableRowNumber, header, result) {
    const headerRow = worksheet.getRow(`${tableRowNumber}`)
    headerRow.eachCell((cell) => {
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'bdbdbd' }
        }
        cell.font = {
            size: 14
        }
        cell.font = {
            bold: true,
            name: 'Arial',
            family: 2,
            size: 13
        }
        if (
            cell.value == 'Line' ||
            (header.includes('QUOT_ITEM_QNTY') &&
                cell.value == result[0].QUOT_ITEM_QNTY) ||
            (header.includes('QUOT_UOM') && cell.value == result[0].QUOT_UOM)
        ) {
            cell.alignment = {
                vertical: 'middle',
                horizontal: 'center'
            }
        } else if (
            (header.includes('QUOT_RESALE') &&
                cell.value == result[0].QUOT_RESALE) ||
            (header.includes('QUOT_EXT_PRICE') &&
                cell.value == result[0].QUOT_EXT_PRICE)
        ) {
            cell.alignment = {
                vertical: 'middle',
                horizontal: 'right'
            }
        } else {
            cell.alignment = {
                vertical: 'middle',
                horizontal: 'left'
            }
        }
    })

    return worksheet
}

async function alignExcelTableFieldValues(alignExcelTableFieldParams) {
    let {
        worksheet,
        tableRowNumber,
        grandTotalRow,
        header,
        principalDeatils,
        result
    } = alignExcelTableFieldParams
    for (let i = tableRowNumber + 1; i <= grandTotalRow - 1; i++) {
        const cell = worksheet.getCell(`A${i}`)
        cell.alignment = {
            vertical: 'bottom',
            horizontal: 'center'
        }
    }

    if (header.includes('QUOT_ITEM_QNTY')) {
        worksheet = await centerAlignExcelFieldValues(
            worksheet,
            result[0].QUOT_ITEM_QNTY,
            principalDeatils,
            grandTotalRow
        )
    }

    if (header.includes('QUOT_UOM')) {
        worksheet = await centerAlignExcelFieldValues(
            worksheet,
            result[0].QUOT_UOM,
            principalDeatils,
            grandTotalRow
        )
    }
    return worksheet
}

async function centerAlignExcelFieldValues(
    worksheet,
    key,
    principalDeatils,
    grandTotalRow
) {
    let foundCell = null
    worksheet.eachRow((row, rowIndex) => {
        row.eachCell((cell, colIndex) => {
            if (cell.value == key) {
                foundCell = {
                    row: rowIndex,
                    col: colIndex,
                    character: cell.address.replace(/[^A-Z]/g, '')
                }
            }
        })
    })
    const incrementRow = principalDeatils.length > 0 ? 2 : 1

    for (let i = foundCell.row + incrementRow; i < grandTotalRow; i++) {
        const cell = worksheet.getCell(`${foundCell.character}${i}`)
        cell.alignment = {
            vertical: 'bottom',
            horizontal: 'center'
        }
    }
    return worksheet
}

async function preparedForDataFnc(preparedFor, companyDetail) {
    return [
        'Prepared For:',
        `${preparedFor.length > 0 ? preparedFor[0]['CONT_FULL_NAME'] : ''}`,
        `${companyDetail.length > 0 ? companyDetail[0]['COMP_NAME'] : ''}`,
        `${companyDetail.length > 0 ? companyDetail[0]['COMP_ADDRESS_1'] : ''}`,
        `${companyDetail.length > 0 ? companyDetail[0]['COMP_CITY'] : ''}`,
        `${companyDetail.length > 0 ? companyDetail[0]['COMP_STATE'] : ''}`,
        `${companyDetail.length > 0 ? companyDetail[0]['COMP_ZIP_CODE'] : ''}`
    ]
}

async function excelPageSetup(workbook) {
    return workbook.addWorksheet('Quote', {
        // pageSetup: {
        //     paperSize: 1,
        //     fitToPage: true,
        //     margins: {
        //         left: 0.3149606,
        //         right: 0.3149606,
        //         top: 0.3543307,
        //         bottom: 0.3543307,
        //     },
        // },
    })
}

async function excelColumns(headers) {
    let columns = []
    const customColumnWidths = {
        QUOT_EXT_PRICE: 15,
        QUOT_RESALE: 15
    }
    const padding = 2

    headers.forEach((header) => {
        const headerLength =
            customColumnWidths[header.label] ||
            (header.fieldName.length > header.label.length
                ? header.fieldName.length
                : header.label.length)

        const width = headerLength + padding

        columns.push({
            header: '',
            key: header.label,
            width: width,
            filterButton: false
        })
    })

    return columns
}

const pickExcel = (obj, ...args) => ({
    ...args.reduce(
        (_res, key) => ({
            ..._res,
            [key]: mapTotalValue(obj, key)
        }),
        {}
    )
})

async function principalIndexPositionsFnc(
    worksheet,
    principalIndexPositions,
    firstColumnCharacter
) {
    principalIndexPositions.forEach((i) => {
        const cellVal = worksheet.getCell(`${firstColumnCharacter}${i}`).value
        worksheet.getCell(`${firstColumnCharacter}${i}`).value = {
            richText: [
                {
                    font: {
                        bold: true,
                        name: 'Arial',
                        family: 2
                    },
                    text: cellVal
                }
            ]
        }
    })
    return worksheet
}

async function principalDetailsDisplayRow(options, principalRowIndex, row) {
    if (!options.descWrap && options.noteVisible) {
        principalRowIndex = principalRowIndex + row.length * 3 + 1
    }

    if (!options.descWrap && !options.noteVisible) {
        principalRowIndex = principalRowIndex + row.length + 1
    }

    if (options.descWrap && options.noteVisible) {
        principalRowIndex = principalRowIndex + row.length * 3 + 1
    }

    if (options.descWrap && !options.noteVisible) {
        principalRowIndex = principalRowIndex + row.length + 1
    }

    return principalRowIndex
}

async function tableRowNumberFnc(worksheet) {
    let tableRowNumber
    worksheet.eachRow(function (row, rowNumberNo) {
        if (row.getCell('A').value === 'Line') {
            tableRowNumber = rowNumberNo
            return false
        }
    })
    return tableRowNumber
}

async function superQuoteDetailsExcel(quoteDetails, header, options) {
    let result = []
    let noWrapDescription = []
    let principalDeatils = []
    let lineItemNote = []

    Object.keys(quoteDetails).forEach((item) => {
        quoteDetails[item].forEach((data) => {
            let ac = {}
            if (options.noteVisible) {
                lineItemNote.push(data['QUOT_NOTES'])
            }

            if (
                (!options.descWrap && options.descBelowPartNo) ||
                (options.descWrap && options.descBelowPartNo)
            ) {
                noWrapDescription.push(
                    data['QUOT_ITEM_PART_DESC']
                        ? data['QUOT_ITEM_PART_DESC'].trim()
                        : data['QUOT_ITEM_PART_DESC']
                )
            }

            header.forEach((column) => {
                ac[column] = data[column]
            })
            result.push(ac)
        })
        principalDeatils.push(item)
    })

    return {
        lineItemNote,
        noWrapDescription,
        principalDeatils,
        result
    }
}

async function normalQuoteDetailsExcel(quoteDetails, header, options) {
    let result = []
    let noWrapDescription = []
    let lineItemNote = []
    quoteDetails.map((data) => {
        let ac = {}
        if (
            (!options.descWrap && options.descBelowPartNo) ||
            (options.descWrap && options.descBelowPartNo)
        ) {
            noWrapDescription.push(
                data['QUOT_ITEM_PART_DESC']
                    ? data['QUOT_ITEM_PART_DESC'].trim()
                    : data['QUOT_ITEM_PART_DESC']
            )
        }

        if (options.noteVisible) {
            lineItemNote.push(data['QUOT_NOTES'])
        }

        header.forEach((column) => {
            ac[column] = data[column]
        })
        result.push(ac)
    })

    return {
        lineItemNote,
        noWrapDescription,
        result
    }
}

async function checkDistributor(data) {
    let preparedFor =
        data[0].QUOT_RECEIPIENT == 'Distributor'
            ? data[0].distriContactDetail
            : data[0].custContactDetail

    let companyDetail =
        data[0].QUOT_RECEIPIENT == 'Distributor'
            ? data[0].distriCompanyDetail
            : data[0].custCompanyDetail

    let customerDeatil =
        data[0].QUOT_RECEIPIENT == 'Distributor' &&
        data[0].custCompanyDetail.length > 0 &&
        data[0].custCompanyDetail[0].COMP_NAME !=
            (data[0].distriCompanyDetail.length > 0
                ? data[0].distriCompanyDetail[0].COMP_NAME
                : '')
            ? data[0].custCompanyDetail
            : ''

    return {
        preparedFor,
        companyDetail,
        customerDeatil
    }
}

async function getTableData(options, quoteDetails, keys, pick) {
    let tableData = []
    if (options.superQuote && options.groupByManufacture) {
        tableData = groupByDataMap(quoteDetails, keys, pick)
    } else {
        tableData = quoteDetails.map((item) => pick(item, keys.length, ...keys))
    }

    return tableData
}

async function addLineFieldIntoHeader(isExcel, options, headers) {
    if (isExcel || (!isExcel && options.hideLine == 0)) {
        headers.unshift({
            label: 'SN',
            fieldName: 'Line',
            customLabel: '0',
            width: '3',
            order: 0,
            align: 'center'
        })
    }

    return headers
}

async function mergeLineNoteRowExcel(worksheet) {
    const keyword = 'Note: '
    let cellArray = []
    worksheet.eachRow(function (noteRow) {
        noteRow.eachCell((noteCell) => {
            if (
                noteCell.value &&
                noteCell.value
                    .toString()
                    .toLowerCase()
                    .includes(keyword.toLowerCase())
            ) {
                cellArray.push(noteRow.getCell('B').address)
            }
        })
    })
    let uniqueArray = [...new Set(cellArray)]
    if (uniqueArray.length > 0) {
        uniqueArray.forEach((i) => {
            worksheet.getCell(i).alignment = {
                vertical: 'top',
                horizontal: 'left'
            }
            worksheet.getCell(i).font = {
                italic: true
            }
        })
    }

    return worksheet
}

async function displayEmptyTableDataExcel(emptyTableParams) {
    let {
        res,
        worksheet,
        header,
        result,
        tableRowNumber,
        pdfData,
        options,
        lastItem,
        noWrapDescription,
        lineItemNote
    } = emptyTableParams
    try {
        worksheet = await excelTabelHeader(
            worksheet,
            tableRowNumber,
            header,
            result
        )

        lastItem.SN = lastItem.SN == 'Line' ? 0 : lastItem.SN
        const grandTotalRow =
            tableRowNumber +
            lastItem.SN +
            noWrapDescription.length +
            lineItemNote.length +
            2

        let footerParams = {
            worksheet,
            header,
            grandTotalRow,
            pdfData,
            options
        }

        const { worksheetFooterExcel } = await footerExcel(footerParams)
        worksheet = worksheetFooterExcel
        if (options.hideLine == 1) {
            worksheet.getColumn(1).hidden = true
        }
        return worksheet
    } catch (error) {
        return res.status(500).json(error.message)
    }
}
async function generateExcelFile(res, workbook, yoxel) {
    const dateTime = new Date()
    const fileName =
        dateTime
            .toISOString()
            .replaceAll('.', ':')
            .replaceAll(':', '_')
            .replaceAll('-', '_') + '_quote.xlsx'

    res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    res.setHeader('Content-Disposition', 'attachment; filename=' + fileName)

    if (yoxel != undefined && yoxel) {
        const excelBuffer = await workbook.xlsx.writeBuffer()
        const base64Data = excelBuffer.toString('base64')
        return res.status(200).send(base64Data)
    }

    return workbook.xlsx.write(res).then(function () {
        res.status(200).end()
    })
}

async function hideEmptyLineNoteRow(worksheet) {
    const keyword = 'Note: '
    worksheet.eachRow(function (noteRow, noteRowNum) {
        noteRow.eachCell((noteCell) => {
            if (
                noteCell.value &&
                noteCell.value
                    .toString()
                    .toLowerCase()
                    .includes(keyword.toLowerCase())
            ) {
                noteCell.alignment = {
                    vertical: 'top'
                }
                if (noteCell.value.split(keyword)[1] == '') {
                    worksheet.getRow(noteRowNum).hidden = true
                }
            }
        })
    })
    return worksheet
}

async function removeDotFromNote(worksheet, options, pdfData, grandTotalRow) {
    if (options.footerNote && pdfData.QUOT_COMMENTS) {
        const footerNoteRow = options.footerNote
            ? grandTotalRow + 2
            : grandTotalRow + 1

        const showLine = options.hideLine == 0 ? true : false
        const firstCell = showLine ? 'A' : 'B'
        const footerNoteValue = worksheet
            .getCell(`${firstCell}${footerNoteRow}`)
            .value.replace('Note.:', 'Note: ')
        worksheet.getCell(`${firstCell}${footerNoteRow}`).value = {
            richText: [
                {
                    text: footerNoteValue
                }
            ]
        }
    }
    return worksheet
}

async function removeEmptyLines(worksheet, grandTotalRow, tableRowNumber) {
    let rowCount = grandTotalRow
    for (let i = rowCount; i >= tableRowNumber; i--) {
        const row = worksheet.getRow(i)
        let isEmpty = true
        row.eachCell((cell) => {
            if (
                cell.value !== null &&
                cell.value !== undefined &&
                cell.value.toString().trim() !== ''
            ) {
                isEmpty = false
                return false
            }
        })

        if (isEmpty) {
            worksheet.getRow(i).hidden = true
            rowCount--
        }
    }
}

async function alignMergedLines(worksheet, grandTotalRow, tableRowNumber) {
    let rowCount = grandTotalRow
    for (let i = rowCount; i >= tableRowNumber; i--) {
        const row = worksheet.getRow(i)
        row.eachCell((cell) => {
            if (cell.model.master != undefined) {
                worksheet.getCell(cell.model.master).alignment = {
                    horizontal: 'left',
                    vertical: 'top',
                    wrapText: true
                }
            }
        })
    }
    return worksheet
}

const defaultQuoteTemplate = async (req, res, next) => {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    try {
        const template = await models.QUOTE_PDF_TEMPLATE.findOne({
            attributes: [['QPDF_TEMPLATE_ID', 'templateId']],
            where: {
                QPDF_DEFAULT_FLAG: 1
            },
            raw: true
        })
        return res.status(200).json(template)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const fetchMedicalDetail = async (quoteId, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const query = `SELECT MED_SIGNED_NAME, MED_SIGNED_IMG, MED_ID, MED_PRINCI FROM MEDICAL_DTL WHERE MED_QUOTE_ID = $quoteId LIMIT 1`
    const [result] = await sequelize.query(query, {
        bind: {
            quoteId
        }
    })
    return result
}

const nameAndSignature = async (medicalDetail) => {
    const name = medicalDetail[0].MED_SIGNED_NAME
    const medicalSignature = medicalDetail[0].MED_SIGNED_IMG
    const signature = await getImageURL(medicalSignature)
    return {
        name,
        signature
    }
}

const medicalHeader = async (quoteId, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    const medicalDetail = await fetchMedicalDetail(quoteId, req)
    const query = `SELECT MED_ID,MED_FACILITY, MED_SPECIFIER,MED_CASE_NO FROM MEDICAL_HDR WHERE MED_ID = $medId`
    const medId = medicalDetail[0].MED_ID
    const [result] = await sequelize.query(query, {
        bind: {
            medId
        }
    })

    return result
}

const medicalDistribuor = async (quoteId, req) => {
    try {
        const medicalHeaderData = await medicalHeader(quoteId, req)
        const company = await getCompanyById(
            medicalHeaderData.length > 0 ? medicalHeaderData[0].MED_FACILITY : 0
        )
        const medicalDoctor = await getCompanyById(
            medicalHeaderData.length > 0
                ? medicalHeaderData[0].MED_SPECIFIER
                : 0
        )

        return {
            distributor: company.length > 0 ? company[0].COMP_NAME : [],
            doctor: medicalDoctor.length > 0 ? medicalDoctor : [],
            caseNo:
                medicalHeaderData.length > 0
                    ? medicalHeaderData[0].MED_CASE_NO
                    : ''
        }
    } catch (e) {
        throw new Error(e.message)
    }
}

const addPrinciDefaultTemplate = async (req, res, next) => {
    try {
        const data = req.body
        const item = {
            PRINCI_ID: data.princiId,
            DISTRIBUTOR_TEMPLATE_ID: data.distributorTemplateId,
            CUSTOMER_TEMPLATE_ID: data.customerTemplateId,
            INS_USER: req.session.userId,
            UPD_USER: req.session.userId
        }
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.PRINCI_DEFAULT_TEMPLATE.create(item)
        res.status(200).json('success')
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const updatePrinciDefaultTemplate = async (req, res, next) => {
    try {
        const { id } = req.params
        const data = req.body

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const ifExist = await models.PRINCI_DEFAULT_TEMPLATE.findOne({
            where: { REC_ID: id }
        })
        if (ifExist) {
            const item = {
                PRINCI_ID: data.princiId,
                DISTRIBUTOR_TEMPLATE_ID: data.distributorTemplateId,
                CUSTOMER_TEMPLATE_ID: data.customerTemplateId,
                UPD_USER: req.session.userId
            }

            await models.PRINCI_DEFAULT_TEMPLATE.update(item, {
                where: { REC_ID: id }
            })
            return res.status(200).json('success')
        } else {
            next(AppError.noContent())
            return
        }
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const deletePrinciDefaultTemplate = async (req, res, next) => {
    try {
        const { id } = req.params

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.PRINCI_DEFAULT_TEMPLATE.destroy({
            where: { REC_ID: id }
        })
        return res.status(200).json('success')
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getAllPrinciDefaultTemplate = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const result = await models.PRINCI_DEFAULT_TEMPLATE.findAll({
            attributes: [
                ['REC_ID', 'id'],
                ['PRINCI_ID', 'princiId'],
                ['DISTRIBUTOR_TEMPLATE_ID', 'distributorTemplateId'],
                ['CUSTOMER_TEMPLATE_ID', 'customerTemplateId']
            ],
            order: [['REC_ID', 'desc']]
        })
        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getPrinciDefaultTemplateByPrinciAndRecipient = async (req, res, next) => {
    try {
        let quoteRecipient = ''
        if (req.params.quoteRecipient == 'Distributor') {
            quoteRecipient = 'DISTRIBUTOR_TEMPLATE_ID'
        } else if (req.params.quoteRecipient == 'Customer') {
            quoteRecipient = 'CUSTOMER_TEMPLATE_ID'
        } else {
            req.query.superQuote = req.params.princiId == 0 ? '1' : '0'
            getPdfTemplate(req, res, next)
            return
        }

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const result = await models.PRINCI_DEFAULT_TEMPLATE.findOne({
            attributes: [[quoteRecipient, 'templateId']],
            where: {
                [Op.and]: {
                    PRINCI_ID: req.params.princiId
                },
                [quoteRecipient]: {
                    [Op.and]: {
                        [Op.ne]: 0, // Not equal to zero
                        [Op.not]: null // Not null
                    }
                }
            },
            raw: true
        })
        if (!result) {
            req.query.superQuote = req.params.princiId == 0 ? '1' : '0'
            getPdfTemplate(req, res, next)
        } else {
            let items = ''
            // Use the tenant-specific models for all DB operations in a multi-tenant environment
            const models = req.tenantModels;

            await models.QUOTE_PDF_TEMPLATE.findAll({
                attributes: [
                    ['REC_ID', 'id'],
                    ['QPDF_TEMPLATE_ID', 'templateId'],
                    ['QPDF_TEMPLATE_NAME', 'templateName'],
                    ['QPDF_LOGO', 'logo'],
                    ['QPDF_TOPIC', 'topic'],
                    ['QPDF_FOOTER_NOTE', 'footerNote'],
                    ['QPDF_MNF_FOOTER_NOTE', 'manufacturerFooterNote'],
                    ['QPDF_LINEITEM_NOTE', 'lineItem'],
                    ['QPDF_DESCRIPTION', 'description'],
                    ['QPDF_DESCRIPTION_WRAP', 'descWrap'],
                    ['QPDF_SUPER_QUOTE', 'superQuote'],
                    ['QPDF_SALES_ORDER', 'salesOrder'],
                    ['QPDF_INCLUDE_NAME', 'includeName'],
                    ['QPDF_INCLUDE_SIGNATURE', 'includeSignature'],
                    ['QPDF_MEDICAL', 'medical'],
                    ['QPDF_PART_LINK_TO_SPEC', 'partLinkToSpec'],
                    ['QPDF_DISPLAY_FOOTER', 'displayFooter'],
                    ['QPDF_TOTAL_LABEL_FOR_CUSTOMER', 'totalLabelForCustomer'],
                    [
                        'QPDF_TOTAL_LABEL_FOR_DISTRIBUTOR',
                        'totalLabelForDistributor'
                    ],
                    ['QPDF_UNIT_PRICE_DECIMAL_DIGIT', 'unitPriceDecimalDigit']
                ],
                where: { QPDF_SUPER_QUOTE: 0 },
                raw: true
            }).then((results) => {
                items = results.map((item) => {
                    if (item.templateId == result.templateId) {
                        return { ...item, defaultFlag: 1 }
                    } else {
                        return { ...item, defaultFlag: 0 }
                    }
                })
            })

            return res.status(200).json(items)
        }
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

async function converToImage(convertImageParams) {
    let {
        worksheet,
        workbook,
        tableRowNumber,
        lastItem,
        noWrapDescription,
        lineItemNote,
        principalDeatils,
        result,
        header
    } = convertImageParams

    if (header.includes('QUOT_IMG_URL')) {
        let foundCell = null
        worksheet.eachRow((row, rowIndex) => {
            row.eachCell((cell, colIndex) => {
                if (cell.value == result[0].QUOT_IMG_URL) {
                    foundCell = {
                        row: rowIndex,
                        col: colIndex,
                        character: cell.address.replace(/[^A-Z]/g, '')
                    }
                }
            })
        })

        const totalImageRows =
            tableRowNumber +
            lastItem.SN +
            noWrapDescription.length +
            lineItemNote.length +
            principalDeatils.length +
            2

        const rowNumbers = Array.from(
            { length: totalImageRows },
            (_, i) => i + foundCell.row
        )

        for await (const i of rowNumbers) {
            let cell = worksheet.getCell(`${foundCell.character}${i}`)
            const imageUrl = cell.value

            if (
                typeof imageUrl === 'string' &&
                imageUrl.startsWith('https://')
            ) {
                const response = await axios.get(imageUrl, {
                    responseType: 'arraybuffer'
                })

                const dimensions = sizeOf(response.data)

                const imageId = workbook.addImage({
                    base64: Buffer.from(response.data).toString('base64'),
                    extension: dimensions.type
                })

                const showLine = `${foundCell.character}${i}:${foundCell.character}${i}`

                worksheet.addImage(imageId, showLine, {
                    ext: {
                        width: 50,
                        height: 50
                    }
                })
                cell.value = null
            }
        }
    }

    return worksheet
}

async function convertToLink(convertToLinkParams) {
    let {
        worksheet,
        tableRowNumber,
        lastItem,
        noWrapDescription,
        lineItemNote,
        principalDeatils,
        result,
        header
    } = convertToLinkParams
    if (header.includes('QUOT_ITEM_PART_MANF')) {
        let foundCell = null
        worksheet.eachRow((row, rowIndex) => {
            row.eachCell((cell, colIndex) => {
                if (cell.value == result[0].QUOT_ITEM_PART_MANF) {
                    foundCell = {
                        row: rowIndex,
                        col: colIndex,
                        character: cell.address.replace(/[^A-Z]/g, '')
                    }
                }
            })
        })

        const totalLinkROws =
            tableRowNumber +
            lastItem.SN +
            noWrapDescription.length +
            lineItemNote.length +
            principalDeatils.length +
            2

        const rowNumbers = Array.from(
            { length: totalLinkROws },
            (_, i) => i + foundCell.row
        )

        for await (const i of rowNumbers) {
            let cell = worksheet.getCell(`${foundCell.character}${i}`)
            if (cell.value) {
                const cellValue = String(cell.value)
                const anchorTagRegex =
                    /<a\s+[^>]*href="([^"]+)"[^>]*>([^<]+)<\/a>/g

                if (anchorTagRegex.test(cellValue)) {
                    const modifiedValue = cellValue.replace(
                        anchorTagRegex,
                        (match, url, text) => {
                            return `${url}, ${text}`
                        }
                    )

                    cell.value = {
                        hyperlink: modifiedValue.split(',')[0],
                        text: modifiedValue.split(',')[1]
                    }

                    cell.style = {
                        font: {
                            color: { argb: '0000FF' }
                        },
                        underline: true
                    }
                }
            }
        }
    }

    return worksheet
}

async function jsonDataWithLink(data) {
    data[0].quoteDetails.map((item) => {
        item[
            'QUOT_ITEM_PART_MANF'
        ] = `<a href="${item['PROD_SPEC_URL']}" style="color:blue">${item['QUOT_ITEM_PART_MANF']}</a>`
    })
    return data
}

async function accountManagerDetailFnc(accountManagerDetailParams) {
    let {
        worksheet,
        headerRightCell,
        headerRightDataCell,
        rowCounter,
        pdfData
    } = accountManagerDetailParams
    let lineCounter = 0

    worksheet = await accountManagerDetailCustomFnc(
        worksheet,
        rowCounter,
        headerRightCell,
        headerRightDataCell,
        pdfData,
        'Account Manager',
        'userName'
    )

    if (pdfData.accountManagerDetails.phone) {
        rowCounter += 1
        lineCounter += 1
        worksheet = await accountManagerDetailCustomFnc(
            worksheet,
            rowCounter,
            headerRightCell,
            headerRightDataCell,
            pdfData,
            'Phone',
            'phone'
        )
    }

    if (pdfData.accountManagerDetails.cell) {
        rowCounter += 1
        lineCounter += 1
        worksheet = await accountManagerDetailCustomFnc(
            worksheet,
            rowCounter,
            headerRightCell,
            headerRightDataCell,
            pdfData,
            'Cell',
            'cell'
        )
    }

    if (pdfData.accountManagerDetails.email) {
        rowCounter += 1
        lineCounter += 1
        worksheet = await accountManagerDetailCustomFnc(
            worksheet,
            rowCounter,
            headerRightCell,
            headerRightDataCell,
            pdfData,
            'Email',
            'email'
        )
    }

    return { workSheetAccountDetail: worksheet, lineCounter }
}

async function accountManagerDetailCustomFnc(
    worksheet,
    rowCounter,
    headerRightCell,
    headerRightDataCell,
    pdfData,
    label,
    data
) {
    worksheet.getCell(`${headerRightCell}${rowCounter}`).value = {
        richText: await accountMangerDetailRichText(label)
    }
    worksheet.getCell(`${headerRightCell}${rowCounter}`).alignment = {
        vertical: 'top',
        horizontal: 'right'
    }

    worksheet.getCell(`${headerRightDataCell}${rowCounter}`).value =
        pdfData.accountManagerDetails[data]

    return worksheet
}

async function accountMangerDetailRichText(label) {
    return [
        {
            font: {
                name: 'Arial',
                family: 2,
                size: 12
            },
            text: `${label}: `
        }
    ]
}

async function lineCountFnc(pdfFooter) {
    const totalLines = Math.ceil(pdfFooter.length / 120)
    const lineCount = pdfFooter.split('<br>').length
    return lineCount > totalLines ? lineCount : totalLines
}

async function rowHeight(worksheet, row) {
    const rowTerms = worksheet.getRow(row)
    rowTerms.height = 15
    let isIdx1Processed = false
    rowTerms.eachCell((cellTerms, idx) => {
        cellTerms.alignment = { wrapText: true }
        if (
            (idx === 1 && !isIdx1Processed) ||
            (idx === 2 && !isIdx1Processed)
        ) {
            if (idx === 1) {
                isIdx1Processed = true
            }
            let cellText = ''
            if (cellTerms.value && cellTerms.value.richText) {
                cellTerms.value.richText.forEach((rt) => {
                    cellText += rt.text
                })
            } else {
                cellText = cellTerms.value ? cellTerms.value.toString() : ''
            }

            const lines = Math.ceil(cellText.length / 100)
            const newlineCount = (cellText.match(/\r\n/g) || []).length + 1
            const totalLines = lines + newlineCount

            const cellHeight = Math.ceil(rowTerms.height * totalLines)
            if (cellHeight > rowTerms.height) {
                rowTerms.height = cellHeight
            }
        }
    })
    return worksheet
}

module.exports = {
    pdf,
    getLables,
    getQuoteHeader,
    savePdfConfig,
    updatePdfConfig,
    deletePdfConfig,
    getPdfConfig,
    getPdfTemplate,
    imageUpload,
    getSuperQuoteDetail,
    defaultQuoteTemplate,
    addPrinciDefaultTemplate,
    updatePrinciDefaultTemplate,
    deletePrinciDefaultTemplate,
    getAllPrinciDefaultTemplate,
    getPrinciDefaultTemplateByPrinciAndRecipient
}
