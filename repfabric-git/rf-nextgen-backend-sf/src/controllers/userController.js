const jwt = require('jsonwebtoken')
const Token = require('../utils/jwtToken')
const sendEmail = require('../utils/sendEmail')
const dayjs = require('dayjs')
const utc = require('dayjs/plugin/utc')
const { sequelize } = require('../models')
const logger = require('../utils/logger')

// let tokenList = []
// Login Method
const Login = (req, res, next) => {
    try {
        const { loginId } = req.body
        const { password } = req.body
        let hex = ''
        let ascii
        let hexCode

        // Validaion
        if (loginId == '' || loginId === undefined) {
            next(AppError.badRequest('Please Enter Login ID'))
        }

        if (password == '' || password === undefined) {
            next(AppError.badRequest('Please Enter Password'))
        }

        // Password encryption
        for (let i = 0, len = password.length; i < len; i++) {
            ascii = password[i].charCodeAt()
            hexCode = Number(ascii + 69 + i * 2).toString(16)
            hex += hexCode.substring(-2)
        }

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        // Query to check lgin credentials from database
        models.USER.findOne({
            where: {
                [Op.and]: {
                    USER_LOGIN: loginId,
                    USER_PASSWORD: hex,
                    USER_AUTH_MODE: 1
                }
            }
        })
            .then((row) => {
                if (!row) {
                    next(AppError.unAuthorized())
                } else {
                    const userId = row.USER_ID
                    const userName = row.USER_NAME
                    const authMode = row.USER_AUTH_MODE

                    Token.jwtToken(
                        req,
                        res,
                        next,
                        loginId,
                        userName,
                        userId,
                        authMode
                    )
                }
            })
            .catch(() => {
                next(AppError.internal())
            })
    } catch (error) {
        next(AppError.internal())
    }
}

// Logout method
const Logout = async (req, res, next) => {
    try {
        delete req.session
        return res
            .clearCookie('JSESSIONID')
            .status(200)
            .json({ message: 'Successfully logged out' })
    } catch (error) {
        next(AppError.internal())
    }
}

const loginWithMagicLink = (req, res, next) => {
    try {
        const { email } = req.body

        // Validaion
        if (email == '' || email === undefined) {
            next(AppError.badRequest('Please enter your email id'))
        }

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        models.USER.findOne({
            where: {
                USER_LOGIN: email
            }
        })
            .then((row) => {
                if (!row) {
                    next(AppError.unAuthorized())
                } else {
                    const userName = row.USER_NAME

                    const token = jwt.sign(
                        {
                            loginId: email,
                            loginName: userName
                        },
                        process.env.JWT_KEY,
                        { expiresIn: 120 }
                    )

                    const html = `<a href=${process.env.BASE_URL}/authenticate?token=${token}>Click to Login</a>`
                    const subject = 'Login Link'
                    sendEmail(email, subject, html)
                    res.status(200).end()
                }
            })
            .catch(() => {
                next(AppError.internal())
            })
    } catch (error) {
        next(AppError.internal())
    }
}

//To fetch all user to Expenses user
const getUserList = async (req, res, next) => {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    try {
        const data = await models.USER.findAll({
            attributes: ['USER_NAME', 'USER_ID'],
            order: ['USER_NAME'],
            where: {
                USER_ID: { [Op.not]: 0 }
            }
        })
        return res.status(200).json(data)
    } catch (error) {
        next(AppError.internal())
    }
}

//To fetch all user to Expenses user
const getUserById = async (req, res, next) => {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    try {
        const data = await models.USER.findAll({
            attributes: ['USER_NAME', 'USER_ID'],
            where: {
                USER_ID: req.params.id
            }
        })
        return res.status(200).json(data)
    } catch (error) {
        next(AppError.internal())
    }
}
const authenticateMagicLink = async (req, res, next) => {
    if (req.query.token) {
        const { token } = req.query

        try {
            // Use the tenant-specific models for all DB operations in a multi-tenant environment
            const models = req.tenantModels;

            const decoded = jwt.verify(token, process.env.JWT_KEY)
            const userInfo = await models.USER.findOne({
                attributes: ['USER_ID', 'USER_EMAIL', 'USER_NAME'],
                where: { USER_LOGIN: decoded.loginId }
            })

            Token.jwtToken(
                req,
                res,
                next,
                userInfo.USER_EMAIL,
                userInfo.USER_NAME,
                userInfo.USER_ID
            )
        } catch (error) {
            next(AppError.unAuthorized())
        }
    } else {
        next(AppError.forBidden())
    }
}

const getEmailError = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const { currentDate } = req.params
        const formattedDate = dayjs(currentDate).format('YYYY-MM-DD')

        const result =
            await sequelize.query(`SELECT count(AUTH_STATUS) as error FROM EMAIL_AUTH_FAILURES WHERE 
            AUTH_USER_ID=${req.session.userId}
        and AUTH_DATE='${formattedDate}'`)

        if (result[0].error > 0) {
            return res.status(200).json('Email authentication failed')
        } else {
            return res.status(200).json('')
        }
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const changePassword = async (req, res, next) => {
    try {
        const row = req.body
        const userId = req.session.userId
        const currentPassword = row.currentPassword

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        if (req.session.userLogin === 'Admin') {
            if (
                currentPassword == undefined ||
                currentPassword == null ||
                currentPassword == ''
            ) {
                next(AppError.badRequest('Please enter valid current password'))
                return
            } else {
                const currentHexpswrd = await passwordToHex(currentPassword)

                const currentUser = await models.USER.findOne({
                    where: {
                        [Op.and]: {
                            USER_ID: userId,
                            USER_PASSWORD: currentHexpswrd
                        }
                    },
                    raw: true
                })
                if (!currentUser) {
                    next(AppError.internal('Current password mismatch'))
                    return
                }
            }
        }

        // Password encryption
        const hex = await passwordToHex(row.password)

        const user = await models.USER.findOne({
            where: {
                [Op.and]: {
                    USER_ID: userId
                    // USER_AUTH_MODE: 1
                }
            },
            raw: true
        })

        if (user && user != undefined && user != null) {
            await models.USER.update(
                {
                    USER_PASSWORD: hex
                },
                {
                    where: {
                        USER_ID: userId
                        // USER_AUTH_MODE: 1
                    }
                }
            )
            return res.status(200).json('Success')
        } else {
            next(AppError.internal('User not found'))
            return
        }
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

async function passwordToHex(password) {
    let hex = ''
    let ascii
    let hexCode

    // Password encryption
    for (let i = 0, len = password.length; i < len; i++) {
        ascii = password[i].charCodeAt()
        hexCode = Number(ascii + 69 + i * 2).toString(16)
        hex += hexCode.substring(-2)
    }
    return hex
}

const getDelegationUser = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    try {
        const { currentDate } = req.params
        const formattedDate = dayjs(currentDate).format('YYYY-MM-DD')

        const [result] =
            await sequelize.query(`select USER_ID as userId,USER_NAME userName
        from USERS where USER_ID= ${req.session.userId}
        UNION
        SELECT DELG_ID_FROM as userId, USER_NAME as userName FROM DELEGATIONS
        LEFT JOIN USERS on USER_ID  = DELG_ID_FROM
        WHERE DELG_ID_TO=${req.session.userId} and '${formattedDate}'
        between DELG_DATE_FROM and DELG_DATE_TO`)

        return res.status(200).json(result)
    } catch (error) {
        next(AppError.internal())
    }
}

const getUserListForSuperLogin = async (req, res, next) => {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    try {
        const users = await models.USER.findAll({
            attributes: [
                ['USER_LOGIN', 'user'],
                ['USER_NAME', 'userName'],
                ['USER_ID', 'userId']
            ],
            where: {
                [Op.and]: {
                    USER_NAME: { [Op.ne]: '<Un-Assigned>' },
                    USER_ID: { [Op.ne]: req.session.userId },
                    USER_LOGIN: { [Op.ne]: 'Admin' },
                    USER_STATUS: { [Op.ne]: 0 }
                }
            },

            order: [
                ['USER_ASSIGNED_FLAG', 'DESC'],
                ['USER_NAME', 'ASC']
            ]
        })

        return res.status(200).json(users)
    } catch (error) {
        next(AppError.internal())
    }
}

const loginAsSuperUser = async (req, res, next) => {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    try {
        const { loginId, userId } = req.body
        await models.USER.findOne({
            where: {
                [Op.and]: {
                    USER_LOGIN: loginId,
                    USER_ID: userId,
                    USER_AUTH_MODE: 1
                }
            }
        })
            .then(async (row) => {
                if (!row) {
                    next(AppError.unAuthorized())
                } else {
                    const userName = row.USER_NAME
                    const authMode = row.USER_AUTH_MODE
                    dayjs.extend(utc)
                    const timeStamps = dayjs().utc().format('YYYYMMDDHHmm')

                    await models.USER_SESSION.create({
                        SSN_USER_ID: userId,
                        SSN_USER_CATEGORY: null,
                        SSN_START_TIME: timeStamps,
                        SSN_SESSION: req.cookies.JSESSIONID
                    })

                    Token.jwtToken(
                        req,
                        res,
                        next,
                        loginId,
                        userName,
                        userId,
                        authMode
                    )
                }
            })
            .catch(() => {
                next(AppError.internal())
            })
    } catch (error) {
        next(AppError.internal())
    }
}

const upadateUserLastSid = async (req, res, next) => {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    try {
        await models.USER.update(
            {
                USER_LAST_SID: req.body.userLastSid
            },
            {
                where: {
                    USER_ID: req.params.id
                }
            }
        )

        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

const getSalesPerson = async (req, res, next) => {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    try {
        const users = await models.USER.findAll({
            attributes: [
                ['USER_LOGIN', 'user'],
                ['USER_NAME', 'userName'],
                ['USER_ID', 'userId']
            ],
            where: {
                [Op.and]: {
                    USER_ASSIGNED_FLAG: 1,
                    USER_ID: { [Op.gt]: 0 }
                }
            },

            order: [['userName', 'ASC']]
        })

        return res.status(200).json(users)
    } catch (error) {
        next(AppError.internal())
    }
}

const getUserEmailConfig = async (req, res, next) => {
    try {
        const userId = req.params.userId

        const [result] =
            await sequelize.query(`select USER_EMAIL_USER_ID as userId,USER_EMAIL_LOGIN as emailId,
            USER_EMAIL_SIGN as signature, USER_ACCESS_TOKEN as token
        from USER_EMAIL_CONFIG where USER_EMAIL_USER_ID= ${userId} limit 1`)
        if (result.length == 0) {
            next(AppError.notFound())
            return
        } else {
            return res.status(200).json(result[0])
        }
    } catch (error) {
        next(AppError.internal())
    }
}

module.exports = {
    Login,
    Logout,
    authenticateMagicLink,
    loginWithMagicLink,
    getUserList,
    getUserById,
    getEmailError,
    changePassword,
    getDelegationUser,
    getUserListForSuperLogin,
    loginAsSuperUser,
    upadateUserLastSid,
    getSalesPerson,
    getUserEmailConfig
}
