const AWS = require('aws-sdk')
const { sequelize } = require('../../models')
const { v4: uuidv4 } = require('uuid')
const dayjs = require('dayjs')
const utc = require('dayjs/plugin/utc')
const pino = require('pino')
const userMenuController = require('../userMenuController')

const sqsSendMessage = async (req, res, next) => {
    try {
        // Create an instance of the SQS service
        const logger = pino({
            transport: {
                target: 'pino-pretty'
            }
        })
        dayjs.extend(utc)
        const sqs = new AWS.SQS({
            region: process.env.SQSREGION,
            accessKeyId: process.env.SQSACCESSKEY,
            secretAccessKey: process.env.SQLSECRETKEY
        })

        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;
        
        const [syncStatus] = await sequelize.query(
            `SELECT DISTINCT ACTIVE AS active FROM NG_SYNC_STATUS`
        )
        if (!syncStatus.length > 0) {
            logger.info('CRONJOB is disabled')
            return res.status(200).json('Disabled')
        }
        if (syncStatus.length > 0 && !syncStatus[0].active) {
            logger.info('CRONJOB is disabled')
            return res.status(200).json('Disabled')
        }

        let [retentionDays] = await sequelize.query(
            `SELECT DISTINCT RETENTION_DAYS AS retentionDays FROM NG_SYNC_STATUS`
        )

        let retentionDaysValue = retentionDays[0]?.retentionDays || 0
         logger.info('Retention Days from table is: ' + retentionDaysValue)

        if (retentionDaysValue < 0) {
            retentionDaysValue = 0 // Default to 0 days if less than 1
        } else if (retentionDaysValue >= 30) {
            retentionDaysValue = 30 // Cap the retention days to a maximum of 30
        }

        logger.info('Final Retention Days set is: ' + retentionDaysValue)

        const retentionDaysAgo = dayjs
            .utc()
            .subtract(retentionDaysValue, 'day')
            .format('YYYY-MM-DD HH:mm:ss')

        await sequelize.query(
            `DELETE FROM NG_SYNC_PRO
            WHERE SYNC_FLAG=4 AND SYNC_ACTION_TIME < '${retentionDaysAgo}'`
        )

        const [paramsDetail] = await userMenuController.paramsQuery()
        const instanceId =
            paramsDetail.length > 0 ? Number(paramsDetail[0].PARAM_SUBS_ID) : 0
        const instanceName =
            paramsDetail.length > 0 ? paramsDetail[0].CLIENT : 0

        logger.info('Re-Migration started')
        await reMigraterequest(instanceId, instanceName, req)
        logger.info('Re-Migration request sent')

        // return res.status(200).json('Success')

        await sequelize.query(
            `UPDATE  NG_SYNC_PRO SET  SYNC_FLAG=2           
            WHERE (SYNC_FLAG=1 OR SYNC_FLAG=9) AND IFNULL(SYNC_ATTEMPTS,0)<=2 ORDER BY REC_ID LIMIT 500`
        )

        const [syncTable] = await sequelize.query(
            `SELECT DISTINCT SYNC_TABLE as syncTable,SYNC_TABLE_SEQ as seq, COUNT(SYNC_UUID) as count FROM NG_SYNC_PRO           
            WHERE SYNC_FLAG=2 AND IFNULL(SYNC_BATCH_UUID,'') = '' AND IFNULL(SYNC_ATTEMPTS,0)<=2 GROUP BY SYNC_TABLE ORDER BY SYNC_TABLE_SEQ LIMIT 500`
        )

        for await (const element of syncTable) {
            const records = element.count > 500 ? 500 : element.count
            const batchSize = 100

            for (let i = 0; i < records; i += batchSize) {
                const start = i
                const end = Math.min(i + batchSize, records)
                const subset = Array.from(
                    { length: end - start },
                    (_, j) => start + j
                )
                const myUuid = uuidv4()
                // console.log(myUuid, element.syncTable, subset.length, req)
                await sequelize.query(`UPDATE NG_SYNC_PRO
                    SET SYNC_BATCH_UUID='${myUuid}' 
                    WHERE SYNC_FLAG=2 AND SYNC_TABLE='${element.syncTable}' AND 
                    IFNULL(SYNC_ATTEMPTS,0)<=2 AND IFNULL(SYNC_BATCH_UUID,'') = ''
                    ORDER BY REC_ID
                    LIMIT ${subset.length}`)

                const result = await jsonObject(
                    element.syncTable,
                    myUuid,
                    element.seq,
                    req
                )
                if (result.length > 0) {
                    const unixTime = dayjs.utc().unix()
                    const message = {
                        HeaderUUID: myUuid,
                        instanceId: instanceId,
                        instanceName: instanceName,
                        table: element.syncTable,
                        userId: 0,
                        userName: 'System',
                        processedDate: unixTime.toString(),
                        body: result
                    }
                    const params = {
                        MessageBody: JSON.stringify(message),
                        QueueUrl: process.env.SQSQUEUEURL
                    }
                    const currentTime = dayjs()
                        .utc()
                        .format('YYYY-MM-DD HH:mm:ss')
                    sqs.sendMessage(params, async (err, data) => {
                        if (err) {
                            logger.info(
                                dayjs.utc().format('DDMMYYYY') +
                                    ' Error is ' +
                                    err
                            )
                            log(myUuid, err, false, 1, req)
                        } else {
                            logger.info(
                                dayjs.utc().format('DDMMYYYY') +
                                    ' Message id is ' +
                                    data.MessageId +
                                    'Batch UUI ' +
                                    myUuid
                            )
                            await sequelize.query(`UPDATE NG_SYNC_PRO
                            SET SYNC_FLAG=3,SYNC_TIME='${currentTime}'
                            WHERE SYNC_BATCH_UUID='${myUuid}'`)
                        }
                    })
                }
            }
        }
        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

const sqsReceiveMessage = async (_req, res, next) => {
    try {
        // Create an instance of the SQS service
        const sqs = new AWS.SQS({
            region: process.env.SQSREGION,
            accessKeyId: process.env.SQSACCESSKEY,
            secretAccessKey: process.env.SQLSECRETKEY
        })

        // Define the message parameters
        const params = {
            QueueUrl: process.env.SQSQUEUEURL,
            MaxNumberOfMessages: 10,
            VisibilityTimeout: 60,
            MessageAttributeNames: [
                'table',
                'instancetId',
                'userId',
                'processedDate',
                'UUID'
            ]
        }

        // Send the message to the SQS queue
        sqs.receiveMessage(params, async (err, data) => {
            if (err) {
                return res.status(500).json(err.code)
            } else if (data.Messages) {
                let result = []
                data.Messages.forEach(function (message) {
                    const body = message
                    //attributeOne.MessageAttributes.UUID.StringValue
                    //Delete message from queue
                    // const deleteParams = {
                    //     QueueUrl: process.env.SQSQUEUEURL,
                    //     ReceiptHandle: message.ReceiptHandle
                    // }
                    // sqs.deleteMessage(deleteParams, function (errorMessage) {
                    //     if (errorMessage) {
                    //         log(1, errorMessage.code, false, 1, req)
                    //     } else {
                    //          log(1, '1', false, 1, req)
                    //     }
                    // })
                    result.push(JSON.parse(body.Body))
                })
                return res.status(200).json(result)
            } else {
                return res.status(200).json('No message in the queue')
            }
        })
    } catch (error) {
        next(AppError.internal())
    }
}

async function jsonObject(tableName, batchUUID, seq, req) {
    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    let dataBase = `RF_CRM_DB_${tenantId}`
    let joinKey = 'T.REC_ID'

    if (seq >= 500) {
        dataBase = `RF_DBD_DB_${tenantId}`
        if (tableName == 'IMPORT_LOG') {
            joinKey = 'T.IMP_BATCH'
        }
    }

    // Use the tenant-specific Sequelize instances
    const sequelize = req.tenantSequelize;

    const [results] =
        await sequelize.query(`SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = '${tableName}' AND TABLE_SCHEMA='${dataBase}'`)
    if (!results.length > 0) {
        log(batchUUID, 'Error in table column fetch', false, 1, req)
        return []
    }

    const columnNames = results.map((result) => result.COLUMN_NAME)
    const tableAlias = 'T.'
    const jsonKeys = columnNames
        .map((columnName) => `'${columnName}', ${tableAlias}${columnName}`)
        .join(',')

    const query = `(SELECT SYNC_UUID as UUID, SYNC_ACTION as action, SYNC_EXT_ACTION, JSON_OBJECT(${jsonKeys}) AS data FROM ${dataBase}.${tableName} T JOIN NG_SYNC_PRO S ON
     ${joinKey} = S.SYNC_REC_ID 
    WHERE SYNC_TABLE  = '${tableName}' AND SYNC_BATCH_UUID='${batchUUID}' AND SYNC_ACTION!='DELETE' ORDER BY SYNC_REC_ID)
    UNION
    (SELECT SYNC_UUID as UUID, SYNC_ACTION as action, SYNC_EXT_ACTION, JSON_OBJECT('REC_ID',SYNC_REC_ID) AS data FROM NG_SYNC_PRO
    WHERE SYNC_TABLE  = '${tableName}' AND SYNC_BATCH_UUID='${batchUUID}' AND SYNC_ACTION='DELETE' ORDER BY SYNC_REC_ID)`

    let [queryResult] = await sequelize.query(query)

    if (!queryResult.length > 0) {
        log(
            batchUUID,
            'Record does not exist in table ' + tableName + '/NG_SYNC_PRO',
            false,
            1,
            req
        )
        return []
    }
    return queryResult.map((item) => {
        let affectHistory = 0 // Default to 0
        if (item.SYNC_EXT_ACTION === 'SALESTEAM_CHG_ALL') affectHistory = 1
        else if (item.SYNC_EXT_ACTION === 'SALESTEAM_CHG_CRM') affectHistory = 2
        return {
            ItemUUID: item.UUID,
            action: item.action,
            affectHistory,
            newData: JSON.parse(item.data)
        }
    })
}

async function log(uuid, message, isUUID, status, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let where
    const syncFlag = status == 0 ? 4 : 9
    if (isUUID) {
        where = `SYNC_UUID ='${uuid}'`
    } else {
        where = `SYNC_BATCH_UUID ='${uuid}'`
    }
    const msg = message + '-' + uuid

    await sequelize.query(`UPDATE NG_SYNC_PRO
                            SET SYNC_FLAG=${syncFlag},SYNC_ERROR_TEXT='${msg}', 
                            SYNC_ATTEMPTS=IFNULL(SYNC_ATTEMPTS,0)+1, SYNC_BATCH_UUID = CASE WHEN ${syncFlag} = 9 THEN '' ELSE SYNC_BATCH_UUID END
                            WHERE ${where}`)
}

const sqsErrorLog = async (req, res, next) => {
    try {
        const logger = pino({
            transport: {
                target: 'pino-pretty'
            }
        })
        dayjs.extend(utc)

        const data = req.body
        logger.info('Received a GET request for /api/sync/log')
        if (data.HeaderUUID && data.errorStatus != undefined) {
            const batchuuid = data.HeaderUUID
            const status = data.errorStatus
            const message = data.message == undefined ? '' : data.message
            logger.info(
                dayjs.utc().format('DDMMYYYY') + ' Batch_UUID is ' + batchuuid
            )

            if (status != 0 && status != 1) {
                next(
                    AppError.badRequest(
                        'Status should be 0 or 1 (0=success, 1=failure)'
                    )
                )
                return
            }

            // Use the tenant-specific Sequelize instance
            const sequelize = req.tenantSequelize;

            const [ifExist] = await sequelize.query(
                `SELECT SYNC_FLAG FROM NG_SYNC_PRO WHERE SYNC_BATCH_UUID='${batchuuid}' LIMIT 1`
            )
            if (ifExist.length > 0) {
                if (data.data && data.data.length > 0) {
                    updateLogForrow(data, batchuuid, req)
                } else {
                    log(batchuuid, message, false, status, req)
                }
            } else {
                next(AppError.notFound())
                return
            }
        } else {
            return res.status(400).json('Please include all mandatory fields')
        }
        logger.info('updated successfully')
        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

async function updateLogForrow(data, batchuuid, req) {
    for await (const row of data.data) {
        const uuid = row.ItemUUID == undefined ? 1 : row.ItemUUID
        const errorMessage = row.message == undefined ? '' : row.message
        log(uuid, errorMessage, true, 1, req)
    }

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(`UPDATE NG_SYNC_PRO
                SET SYNC_FLAG=4,SYNC_ERROR_TEXT='Success', 
                SYNC_ATTEMPTS=IFNULL(SYNC_ATTEMPTS,0)+1
                WHERE SYNC_FLAG!=9 AND SYNC_BATCH_UUID='${batchuuid}'`)
}

const getSyncTable = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const [result] = await sequelize.query(
            `SELECT REC_ID as id, NAME as name , ENABLED as enabled,SYNC_LIMIT as syncLimit FROM NG_SYNC_TABLES `
        )
        return res.status(200).json(result)
    } catch (error) {
        next(AppError.internal())
    }
}

const updateSyncTable = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const data = req.body
        for await (const item of data) {
            const syncLimit = item.syncLimit ? item.syncLimit : 0
            const enabled = item.enabled ? 1 : 0

            await sequelize.query(
                `UPDATE NG_SYNC_TABLES SET ENABLED = ${enabled}, SYNC_LIMIT=${syncLimit}  WHERE REC_ID='${item.id}'`
            )
        }
        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

const cronJobStatusUpdate = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const data = req.body
        await sequelize.query(
            `UPDATE NG_SYNC_STATUS SET ACTIVE = ${data.status}`
        )
        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

const clearSyncPro = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        await sequelize.query(`TRUNCATE TABLE NG_SYNC_PRO`)
        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

const retryUnprocessed = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        await sequelize.query(
            `UPDATE NG_SYNC_PRO SET SYNC_FLAG = 1 WHERE SYNC_FLAG = 3 `
        )
        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

const clearErrorSyncPro = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        await sequelize.query(`DELETE FROM NG_SYNC_PRO WHERE SYNC_FLAG = 9 `)
        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

const getSyncStatus = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;
        
        const [result] = await sequelize.query(
            `SELECT ACTIVE FROM NG_SYNC_STATUS`
        )
        const data = result.length > 0 ? result[0].ACTIVE : false
        return res.status(200).json(data)
    } catch (error) {
        next(AppError.internal())
    }
}

const reMigraterequest = async (instanceId, instanceName, req) => {
    const logger = pino({
        transport: {
            target: 'pino-pretty'
        }
    })

    const sqs = new AWS.SQS({
        region: process.env.SQSREGION,
        accessKeyId: process.env.SQSACCESSKEY,
        secretAccessKey: process.env.SQLSECRETKEY
    })
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const [result] = await sequelize.query(
            `SELECT SYNC_LIMIT, NAME FROM NG_SYNC_TABLES WHERE SYNC_LIMIT!=0 ORDER BY TABLE_SEQ`
        )
        for await (const item of result) {
            const [result1] = await sequelize.query(
                `SELECT SYNC_TABLE, COUNT(*) as count FROM NG_SYNC_PRO WHERE SYNC_TABLE='${item.NAME}' AND SYNC_FLAG=1
                GROUP BY SYNC_TABLE HAVING count > ${item.SYNC_LIMIT}`
            )
            //send the re migration info to sqs
            if (result1.length > 0) {
                const array = ['SALESTEAM_CHG_CRM', 'SALESTEAM_CHG_ALL', '']
                for (const action of array) {
                    const actionCondition =
                        action === ''
                            ? `(SYNC_EXT_ACTION IS NULL OR SYNC_EXT_ACTION = '')`
                            : `SYNC_EXT_ACTION='${action}'`

                    const [result2] = await sequelize.query(`
                        SELECT SYNC_ACTION_TIME as initialDate, SYNC_EXT_ACTION FROM NG_SYNC_PRO 
                        WHERE SYNC_TABLE='${item.NAME}' 
                        AND SYNC_FLAG=1 
                        AND ${actionCondition}
                        ORDER BY SYNC_ACTION_TIME ASC LIMIT 1`)

                    if (result2.length > 0) {
                        const myUuid = uuidv4()
                        logger.info('Re-Migration... ' + item.NAME)

                        const currentTime = dayjs()
                            .utc()
                            .format('YYYY-MM-DD HH:mm:ss')
                        let affectHistoryValue = 0
                        if (action === 'SALESTEAM_CHG_CRM') {
                            affectHistoryValue = 2
                        } else if (action === 'SALESTEAM_CHG_ALL') {
                            affectHistoryValue = 1
                        }

                        const message = {
                            HeaderUUID: myUuid,
                            instanceId: instanceId,
                            instanceName: instanceName,
                            table: item.NAME,
                            initialDate:
                                result2?.length > 0
                                    ? result2[0].initialDate
                                    : '',
                            affectHistory: affectHistoryValue,
                            sentDate: currentTime
                        }

                        const params = {
                            MessageBody: JSON.stringify(message),
                            QueueUrl: process.env.SQS_SECOND_QUEUEURL
                        }
                        logger.info('Re-Migration sending to SQS ')

                        const data = await sqs.sendMessage(params).promise()
                        logger.info(
                            `${dayjs.utc().format('DDMMYYYY')} Message id is ${
                                data.MessageId
                            } Batch UUID ${myUuid}`
                        )

                        // Update sync records
                        await sequelize.query(
                            `UPDATE NG_SYNC_PRO
                            SET SYNC_BATCH_UUID='${myUuid}', SYNC_FLAG=5, SYNC_TIME='${currentTime}', SYNC_ERROR_TEXT='Re-Migration', SYNC_ATTEMPTS=3
                            WHERE SYNC_FLAG=1 AND SYNC_TABLE='${item.NAME}' AND ${actionCondition}`
                        )
                    }
                }
            }
        }
        return true
    } catch (error) {
        logger.info(error)
        return false
    }
}

module.exports = {
    sqsSendMessage,
    sqsReceiveMessage,
    sqsErrorLog,
    getSyncTable,
    updateSyncTable,
    cronJobStatusUpdate,
    clearSyncPro,
    retryUnprocessed,
    getSyncStatus,
    clearErrorSyncPro
}
