const crypto = require('crypto')
const axios = require('axios')
const { sequelize } = require('../../models')
require('dotenv').config()

const ENCRYPTION_KEY = process.env.IDP_ENCRYPTION_KEY
const ENCRYPTION_IV = process.env.IDP_ENCRYPTION_IV
const ENCRYPTION_ALGORITHM = 'aes-256-cbc'
const AWS_API_URL = process.env.IDP_AWS_API_URL

const decryptToken = (encryptedToken) => {
    try {
        const decipher = crypto.createDecipheriv(
            ENCRYPTION_ALGORITHM,
            Buffer.from(ENCRYPTION_KEY, 'hex'),
            Buffer.from(ENCRYPTION_IV, 'hex')
        )
        let decrypted = decipher.update(encryptedToken, 'base64', 'utf8')
        decrypted += decipher.final('utf8')
        return JSON.parse(decrypted)
    } catch (error) {
        throw AppError.internal({ message: 'Invalid token' })
    }
}

const encryptToken = (tokenData) => {
    try {
        const cipher = crypto.createCipheriv(
            ENCRYPTION_ALGORITHM,
            Buffer.from(ENCRYPTION_KEY, 'hex'),
            Buffer.from(ENCRYPTION_IV, 'hex')
        )
        let encrypted = cipher.update(
            JSON.stringify(tokenData),
            'utf8',
            'base64'
        )
        encrypted += cipher.final('base64')

        return encrypted
    } catch (error) {
        throw AppError.internal({ message: 'Encryption failed' })
    }
}

const buildTokenData = ({ user_id, user_email, tenant_id, tenant_url }) => ({
    apikeys: [],
    user_id,
    user_email,
    tenant_id,
    tenant_url
})

const updateApiKeys = (apikeys = [], serviceName, key, expiration) => {
    const index = apikeys.findIndex((item) => item.serviceName === serviceName)
    if (index >= 0) {
        apikeys[index] = { serviceName, key, expiration }
    } else {
        apikeys.push({ serviceName, key, expiration })
    }
    return apikeys
}

const getTenantInfo = async () => {
    const result = await getDynamicParams(['PARAM_SUBS_ID', 'TENANT_URL'])
    if (!result?.PARAM_SUBS_ID || !result?.TENANT_URL) {
        throw AppError.internal({ message: 'Tenant information not available' })
    }
    return {
        tenant_id: result.PARAM_SUBS_ID,
        tenant_url: result.TENANT_URL
    }
}

const getIdp = async (req, res, next) => {
    try {
        const { service_name, token } = req.query
        const user_id = req.session?.userId

        if (!service_name) {
            return res.status(400).json({ message: 'service name is required' })
        }

        let tenant_id, tenant_url
        try {
            ;({ tenant_id, tenant_url } = await getTenantInfo())
        } catch (err) {
            return next(
                AppError.internal({
                    message: 'Tenant information fetch failed'
                })
            )
        }

        let userDetails
        let user_email
        const emailRegex =
            /^(?!.*\.\.)[a-zA-Z0-9_%+-]+(?:\.[a-zA-Z0-9_%+-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/

        try {
            ;[userDetails] = await userInfo(user_id, emailRegex, req)

            user_email = userDetails?.email
        } catch (err) {
            if (err instanceof AppError && err.code === 422) {
                return next(err)
            }

            return next(
                AppError.internal({
                    message: err ? err.message : 'User email fetch failed'
                })
            )
        }

        let tokenData
        if (token) {
            try {
                const decodedToken = decodeURIComponent(token)

                tokenData = decryptToken(decodedToken)

                await validateTokenData(
                    tokenData,
                    tenant_id,
                    tenant_url,
                    user_email,
                    emailRegex
                )
            } catch (err) {
                if (err instanceof AppError) {
                    return res.status(err.status || 403).json({
                        message:
                            typeof err.message === 'object'
                                ? err.message.message
                                : err.message
                    })
                }

                return res.status(400).json({ message: 'Invalid token format' })
            }
        } else {
            tokenData = buildTokenData({
                user_id,
                user_email,
                tenant_id,
                tenant_url
            })
        }

        const awsUrl = `${AWS_API_URL}/${service_name}?tenant_id=${tenant_id}`

        let awsResponse
        try {
            awsResponse = await axios.get(awsUrl, {
                headers: {
                    'x-api-key': process.env.IDP_AWS_X_API_KEY
                }
            })
        } catch (error) {
            if (error.response?.status === 403) {
                return next(
                    AppError.forBidden({ message: `Service not authorized` })
                )
            }
            return next(
                AppError.internal({
                    message: `API key not available for tenant_id ${tenant_id}`
                })
            )
        }

        const awsData = awsResponse?.data?.message

        if (!awsData?.apiKey.includes('|')) {
            return next(
                AppError.internal({ message: 'Invalid AWS API key format' })
            )
        }

        const [key, expiration] = awsData?.apiKey?.split('|') || []

        if (!key || !expiration) {
            return next(
                AppError.internal({ message: 'Invalid AWS API key format' })
            )
        }

        tokenData.apikeys = updateApiKeys(
            tokenData?.apikeys,
            service_name,
            key,
            expiration
        )

        const encryptedToken = encryptToken(tokenData)

        return res.status(200).json({ token: encryptedToken })
    } catch (error) {
        return next(AppError.internal(error?.message))
    }
}

const validateEmail = async (email, emailRegex) => {
    if (!email || typeof email !== 'string' || email.trim() === '') {
        throw AppError.validation({
            message: 'Email fetch failed: Missing or empty email'
        })
    }
    if (!emailRegex.test(email)) {
        throw AppError.validation({
            message: 'Email fetch failed: Invalid email format'
        })
    }
}

const validateTokenData = async (
    tokenData,
    tenant_id,
    tenant_url,
    user_email,
    emailRegex
) => {
    if (
        tokenData?.tenant_id !== tenant_id ||
        tokenData?.tenant_url !== tenant_url
    ) {
        throw AppError.forBidden('Token tenant mismatch - access denied')
    }

    if (
        !tokenData?.user_email ||
        typeof tokenData?.user_email !== 'string' ||
        tokenData?.user_email.trim() === ''
    ) {
        throw AppError.forBidden(
            'Token user email missing or invalid - access denied'
        )
    }

    if (!emailRegex.test(tokenData?.user_email)) {
        throw AppError.forBidden('Invalid email format - access denied')
    }

    if (tokenData?.user_email !== user_email) {
        throw AppError.forBidden('Token user email mismatch - access denied')
    }
}

async function userInfo(userId, emailRegex, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const [userEmailLogin] = await sequelize.query(
        `
            SELECT E.USER_EMAIL_LOGIN as email
            FROM USER_EMAIL_CONFIG E
            JOIN USERS U ON E.USER_EMAIL_USER_ID = U.USER_ID
            WHERE U.USER_ID=$userId 
            LIMIT 1`,
        { bind: { userId } }
    )
    
    let email = userEmailLogin?.[0]?.email

    try {
        await validateEmail(email, emailRegex)
        return [{ email }]
    } catch (error) {
        // If USER_EMAIL_LOGIN is invalid, proceed to check USER_LOGIN
    }

    const [userLoginResult] = await sequelize.query(
        `SELECT USER_LOGIN AS email FROM USERS WHERE USER_ID = $userId LIMIT 1`,
        { bind: { userId } }
    )

    email = userLoginResult?.[0]?.email

    try {
        await validateEmail(email, emailRegex)
        return [{ email }]
    } catch (error) {
        // If USER_LOGIN is invalid, proceed to check USER_EMAIL
    }

    const [userEmailResult] = await sequelize.query(
        `SELECT USER_EMAIL AS email FROM USERS WHERE USER_ID = $userId LIMIT 1`,
        { bind: { userId } }
    )

    email = userEmailResult?.[0]?.email

    try {
        await validateEmail(email, emailRegex)
    } catch (error) {
        throw AppError.validation({
            message:
                'We cannot find a valid email address for you, so we cannot continue. Please add an email address or contact support'
        })
    }
    return [{ email }]
}

module.exports = { getIdp }
