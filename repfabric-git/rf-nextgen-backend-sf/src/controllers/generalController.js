const logger = require('../utils/logger')
const { sequelize } = require('../models')
const {
    QuickSightClient,
    GenerateEmbedUrlForRegisteredUserCommand,
    RegisterUserCommand,
    CreateNamespaceCommand,
    DescribeNamespaceCommand
} = require('@aws-sdk/client-quicksight')
const AppError = require('../utils/appError')

const getCustomLabels = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const [result] = await sequelize.query(
            `
                SELECT
                LBL_ID as labelId,
                LBL_DEFAULT as labelDefault,
                LBL_CUSTOM as labelCustom,
                LBL_DESC as labelDesc
                FROM CUSTOM_LABELS
            `
        )

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getCustomUserParams = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let query = `SELECT 
        PARAM_ID userParamId, 
        CUSTOM_USER_PARAMS.USER_PARAM_VALUE userParamvalue, 
        ifnull(CUSTOM_USER_PARAMS.USER_PARAM_STATUS,0) userParamStatus 
        FROM CUSTOM_PARAMS_MST
        LEFT JOIN CUSTOM_USER_PARAMS on CUSTOM_USER_PARAMS.USER_PARAM_ID = PARAM_ID and CUSTOM_USER_PARAMS.USER_ID = :userId`

        const result = await sequelize.query(query, {
            replacements: { userId: req.session.userId },
            type: sequelize.QueryTypes.SELECT
        })

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getParamsConfig = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const { paramId } = req.params

        let query = `SELECT
        REC_ID as id, PARAM_VALUE as value, PARAM_ID as paramId
        FROM PARAMS_CONFIG where PARAM_ID=:paramId`

        const result = await sequelize.query(query, {
            replacements: { paramId },
            type: sequelize.QueryTypes.SELECT
        })
        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getPrinciCustomer = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        //id 2 is for tagged company and id -1 for parent company
        const { id } = req.params
        let field
        if (id == 2) {
            field = 'COMP_TAGGED = 1'
        } else {
            field =
                id == -1
                    ? `COMP_ID in (SELECT DISTINCT COMP_PARENT_ID FROM COMPANIES WHERE COMP_ID<>COMP_PARENT_ID)`
                    : `COMP_TYPE = ${id}`
        }

        let query = `SELECT
        COMP_ID as recId, COMP_NAME as compName,COMP_ADDRESS_1 as street, COMP_STATE as state, COMP_CITY as city
        FROM COMPANIES where ${field}`

        const [result] = await sequelize.query(query)

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getProdMastOld = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    const { princiId, filterVal } = req.query
    try {
        let filter
        let princi
        if (princiId) {
            princi = `PROD_PRINCI_ID=${princiId}`
        } else {
            princi = 1
        }
        if (filterVal && filterVal != null && filterVal != '') {
            const partValue = await getPartNumber(filterVal)
            if (partValue.Mode == 1) {
                filter = ` MATCH(PROD_PRINCI_PARTNO,PROD_DESC) AGAINST('${partValue.PartNumber}' IN NATURAL LANGUAGE MODE)`
            } else {
                filter = ` MATCH(PROD_PRINCI_PARTNO,PROD_DESC) AGAINST('${partValue.PartNumber}' IN BOOLEAN MODE)`
            }
            // filter = `PROD_PRINCI_PARTNO like '%${filterVal}%' OR PROD_DESC like '%${filterVal}%'`
        } else {
            filter = `1`
        }

        const query = `SELECT REC_ID as recId, PROD_PRINCI_ID as princiId, PRINCI_NAME as princiName, 
        PROD_PRINCI_PARTNO as princiPartNo, PROD_FAMILY as prodFamily, PROD_LINE as prodLine, 
        PROD_DESC as prodDesc, PROD_STD_PRICE as prodStdPrice, PROD_COMM_RATE as prodCommRate, 
        PROD_LEAD_TIME as prodLeadTime, CONCAT(TRIM(PROD_PRINCI_PARTNO)," ", 
        TRIM(PROD_DESC)) AS prodPartNoDesc, PROD_UOM as prodUOM
        FROM RF_DBD_DB_${tenantId}.VIEW_PRODUCTS_MST WHERE ${princi} AND
         ${filter}`

        const [result] = await sequelize.query(query)

        res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getProdMast = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    const { princiId, filterVal } = req.query

    try {
        // Initialize conditions array and replacements object
        let conditions = []
        let replacements = {}

        // Prepare princiId condition if provided
        if (princiId) {
            conditions.push('PROD_PRINCI_ID = :princiId')
            replacements.princiId = princiId
        }

        // Prepare filterVal condition if provided
        if (filterVal && filterVal !== null && filterVal !== '') {
            const partValue = await getPartNumber(filterVal)
            const mode =
                partValue.Mode === 1 ? 'NATURAL LANGUAGE MODE' : 'BOOLEAN MODE'
            conditions.push(
                `MATCH(PROD_PRINCI_PARTNO, PROD_DESC) AGAINST(:partNumber IN ${mode})`
            )
            replacements.partNumber = partValue.PartNumber
        }

        // Join conditions to form the WHERE clause
        const whereClause = conditions.join(' AND ')
        // Build the query
        const query = `
            SELECT 
                REC_ID as recId, 
                PROD_PRINCI_ID as princiId, 
                PRINCI_NAME as princiName, 
                PROD_PRINCI_PARTNO as princiPartNo, 
                PROD_FAMILY as prodFamily, 
                PROD_LINE as prodLine, 
                PROD_DESC as prodDesc, 
                PROD_STD_PRICE as prodStdPrice, 
                PROD_COMM_RATE as prodCommRate, 
                PROD_LEAD_TIME as prodLeadTime, 
                CONCAT(TRIM(PROD_PRINCI_PARTNO), " ", TRIM(PROD_DESC)) AS prodPartNoDesc, 
                PROD_UOM as prodUOM
            FROM RF_DBD_DB_${tenantId}.VIEW_PRODUCTS_MST
            WHERE ${whereClause}`

        // Execute the parameterized query using Sequelize
        const result = await sequelize.query(query, {
            replacements,
            type: sequelize.QueryTypes.SELECT
        })

        res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

async function getPartNumber(partNumber) {
    // Assuming you have received the partNumber parameter
    let modeFlag = 0
    let partNum
    let str = decodeURIComponent(partNumber.trim())
    let strFinalArray = []
    let newNumArray = []
    let val = ''

    let strArray = str.split(' ')

    for (let num of strArray) {
        if (!num.includes('-')) {
            val = '+' + num + '*'
        } else {
            modeFlag = 1
            let newStrArray = num.split('-')
            let newVal = '"' + num + '"'
            newNumArray.push(newVal)

            for (let newNum of newStrArray) {
                newVal = '+' + newNum
                newNumArray.push(newVal)
            }
        }

        if (val !== '') {
            strFinalArray.push(val)
        }
    }

    partNum =
        newNumArray.length === 0
            ? strFinalArray.join(' ')
            : strFinalArray.concat(newNumArray).join(' ')

    // Use the modified partNumber variable in your further code logic
    return { PartNumber: partNum, Mode: modeFlag }
}

const getCompanyType = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let query = `SELECT COMP_TYPE_ID as compTypeId, COMP_TYPE_NAME as compTypeName 
        FROM COMPANY_TYPES_MST WHERE COMP_TAGGED=1`
        const [result] = await sequelize.query(query)

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getStatus = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const source = req.query.source

        let query = `SELECT * FROM DATA_OPTIONS WHERE OPT_DATA_SOURCE='${source}' order by OPT_ORDER`
        const [result] = await sequelize.query(query)

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getJobActivityStage = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let query = `SELECT REC_ID as recId,ACT_NAME as actName,ACT_SORT_ID as actSortId,
        ACT_ISQUOT_DEFAULT as actIsQuotDefault FROM JOB_ACTIVITIES_MST ORDER BY ACT_SORT_ID`
        const [result] = await sequelize.query(query)

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const custPriceCalc = async (princiId, partNumber, id, qty) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    // const query = `select RF_DBD_DB_${tenantId}.GetCustomerPrice(${princiId},'${partNumber}',${id},${qty}) as price`
    // const [getPrice] = await sequelize.query(query)
    // return parseInt(getPrice[0][`price`])

    const query = `select RF_DBD_DB_${tenantId}.GetCustomerPrice(:princiId, :partNumber, :id, :qty) as price`
    const replacements = { princiId, partNumber, id, qty }

    const getPrice = await sequelize.query(query, {
        replacements,
        type: sequelize.QueryTypes.SELECT
    })

    return parseInt(getPrice[0].price, 10) // Parse price as an integer with base 10
}

const getCustomerPrice = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const { princiId, partNumber, customerId, distributorId, qty } =
            req.query
        let custPrice = 0
        const [result] =
            await sequelize.query(`SELECT IFNULL(APP_PARAM_STATUS,0) AS status FROM CUSTOM_APP_PARAMS
        WHERE APP_PARAM_ID='ENABLE_PERSON_COMPANY'`)
        const status = result.length > 0 ? result[0].status : 0

        if (princiId && partNumber && qty) {
            if (status == 1 && distributorId != 0) {
                custPrice = await custPriceCalc(
                    princiId,
                    partNumber,
                    distributorId ? distributorId : 0,
                    qty
                )
                if (custPrice == 0) {
                    custPrice = await custPriceCalc(
                        princiId,
                        partNumber,
                        customerId ? customerId : 0,
                        qty
                    )
                }
            } else {
                custPrice = await custPriceCalc(
                    princiId,
                    partNumber,
                    customerId ? customerId : 0,
                    qty
                )
            }
        } else {
            next(AppError.badRequest('Please fill all the mandatory fields'))
            return
        }
        return res.status(200).json(custPrice)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getYoxelToken = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const userId = req.session.userId

        let query = `SELECT ACCT_ACCESS_TOKEN as token FROM SYNC_ACCT_DTLS 
        WHERE USER_ID=${userId} AND ACCT_NAME='Yoxel' LIMIT 1`
        const [result] = await sequelize.query(query)

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getProdUOM = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    try {
        const { name } = req.params

        let query = `SELECT REC_ID as recId, UOM_NAME as uomName, UOM_UNITS as uomUnits 
        FROM RF_DBD_DB_${tenantId}.PROD_UOM 
        WHERE UOM_NAME='${name}'`
        const [result] = await sequelize.query(query)

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getPrinciCommRate = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const { princiId } = req.params

        // Construct the parameterized query
        const query = `
            SELECT COMM_PCT
            FROM PRINCI_COMM_DEFAULTS
            WHERE COMM_PRINCI_ID = :princiId
        `

        // Execute the parameterized query with Sequelize
        const result = await sequelize.query(query, {
            replacements: { princiId }, // Bind the princiId parameter
            type: sequelize.QueryTypes.SELECT // Ensure it's a SELECT query
        })

        // Extract the commRate from the result
        const commRate = result.length > 0 ? result[0].COMM_PCT : 0

        // Return the commRate as JSON response
        return res.status(200).json(commRate)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const medicalMenuEnabled = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let query = `SELECT count(*) as count FROM USER_MENU WHERE USRMENU_MENU_ID=87 AND USRMENU_MENU_ACCESS != 0`
        const [result] = await sequelize.query(query)

        const isEnable = result[0].count == 0 ? 0 : 1
        return res.status(200).json(isEnable)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const yoxelTokenForUser = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const { name } = req.query

        // Define the query with placeholders
        let query = `SELECT ACCT_ACCESS_TOKEN as token FROM SYNC_ACCT_DTLS WHERE ACCT_LOGIN = :name AND ACCT_NAME = 'Yoxel'`

        // Execute the query with bind parameters
        const result = await sequelize.query(query, {
            replacements: { name },
            type: sequelize.QueryTypes.SELECT
        })

        const token = result.length > 0 ? result[0] : ''
        return res.status(200).json(token)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const extendedReportUrl = async (_req, res, next) => {
    try {
        return res.status(200).json({
            url: process.env.EXTENDED_REPORT_URL
                ? process.env.EXTENDED_REPORT_URL
                : 'https://widgets-staging.repfabric.online/'
        })
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const quicksightEmbedUrl = async (req, res, next) => {
    // Use environment variables for AWS configuration
    const awsRegion = process.env.AWS_QUICKSIGHT_REGION || 'us-east-1';
    const awsAccountId = process.env.AWS_QUICKSIGHT_ACCOUNT_ID;

    // Validate required environment variables
    if (!awsAccountId) {
        logger.error('AWS_QUICKSIGHT_ACCOUNT_ID environment variable is not set');
        return next(AppError.internal('QuickSight configuration is missing'));
    }

    const client = new QuickSightClient({ region: awsRegion });

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const userId = req.session.userId;
        const tenantId = req.dbVars?.tenantId;

        if (!tenantId) {
            logger.error('Tenant ID not found in request');
            return next(AppError.internal('Tenant configuration is missing'));
        }

        // Get the logged-in user's email address
        const [userEmailResult] = await sequelize.query(
            `SELECT USER_EMAIL_LOGIN FROM USER_EMAIL_CONFIG WHERE USER_EMAIL_USER_ID = :userId LIMIT 1`,
            {
                replacements: { userId },
                type: sequelize.QueryTypes.SELECT
            }
        );

        if (!userEmailResult || !userEmailResult.USER_EMAIL_LOGIN) {
            logger.error('User email configuration not found for userId:', userId);
            return next(AppError.badRequest('User email configuration is not set'));
        }

        const userEmail = userEmailResult.USER_EMAIL_LOGIN;
        const namespace = `tenant-${tenantId}`;

        logger.info('QuickSight embed URL requested for user:', userId, 'email:', userEmail, 'namespace:', namespace);

        // Attempt to generate embed URL with proper error handling and user registration
        const embedUrl = await generateEmbedUrlWithRegistration(
            client,
            awsAccountId,
            awsRegion,
            userEmail,
            namespace,
            req
        );

        return res.status(200).json({
            url: embedUrl
        });
    } catch (error) {
        logger.error('Unexpected error in quicksightEmbedUrl:', error);
        next(AppError.internal('Failed to generate QuickSight embed URL'));
    }
}

const getPrincipals = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const userId = req.session.userId
        const privateTeamIds = await privateTeam(userId, req)

        let query = `SELECT
        COMP_ID as recId, COMP_NAME as compName,COMP_ADDRESS_1 as street, COMP_STATE as state, COMP_CITY as city
        FROM COMPANIES WHERE COMP_TYPE = 1 AND COMP_PRIV_TEAM IN(0,${privateTeamIds})`

        const [result] = await sequelize.query(query)

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getCustomers = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const userId = req.session.userId
        const salesTeamIds = await salesTeam(userId, req)
        const privateTeamIds = await privateTeam(userId, req)

        let query = `SELECT
        COMP_ID as recId, COMP_NAME as compName,COMP_ADDRESS_1 as street, COMP_STATE as state, COMP_CITY as city
        FROM COMPANIES WHERE COMP_TAGGED= 1 AND COMP_SMAN_ID IN (${salesTeamIds}) AND COMP_PRIV_TEAM IN(0,${privateTeamIds})`

        const [result] = await sequelize.query(query)

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getDivisionList = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const query = `SELECT DISTINCT DR.DIVISION_ID as divisionId,D.DIVISION_NAME as divisionName FROM DIVISION_REPS DR JOIN
        DIVISIONS_MST D ON D.DIVISION_ID = DR.DIVISION_ID 
        where case when ${req.session.roleId}=1 then 1 else DR.USER_ID=? end `

        let result = await sequelize.query(query, [req.session.userId])
        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const updateAllowDistriComm = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const authHeader = req.headers['authorization']
        if (!authHeader) {
            return res.status(401).send('Authorization header is missing')
        }

        const base64Credentials = authHeader.split(' ')[1]
        const credentials = Buffer.from(base64Credentials, 'base64').toString(
            'utf-8'
        )
        const [username, password] = credentials.split(':')

        // Replace these with your actual username and password
        const validUsername = 'admin'
        const validPassword =
            '48qRMLHzTDBMh44wlggLxVAQP40ywZtZmSEkfkO4CFWeCarIAZZaClHh4GxP5zta'

        if (username === validUsername && password === validPassword) {
            const clientType = process.env.FABRIC_CLIENT_TYPE
            if (clientType == 'Distifabric') {
                const query = `UPDATE CUSTOM_APP_PARAMS set APP_PARAM_NAME= 'Allow Rep Agency Commission' where APP_PARAM_ID='ALLOW_DISTRI_COMM'`

                await sequelize.query(query)
            } else {
                const query = `UPDATE CUSTOM_APP_PARAMS set APP_PARAM_NAME= 'Allow Distributor Commission' where APP_PARAM_ID='ALLOW_DISTRI_COMM'`

                await sequelize.query(query)
            }
            return res.status(200).json('success')
        } else {
            next(AppError.unAuthorized())
            return
        }
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const setToken = async (req, res, next) => {
    try {
        const { appToken } = req.query
        if (!appToken) {
            next(AppError.badRequest())
        }
        res.cookie('appSessionId', req.query.appToken, {
            httpOnly: true,
            path: '/',
            secure: true,
            sameSite: 'None'
        })

        return res.status(200).json({ message: 'Success' })
    } catch (e) {
        next(AppError.internal(e.message))
    }
}

const getDynamicGridColumns = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const userId = req.session.userId
        const { tableName } = req.params
        const query = `
                SELECT
                UTC.REC_ID AS recId,
                TABLE_NAME AS tableName,
                USER_ID AS userId,
                COLUMN_NAME AS columnName,
                COLUMN_POSITION AS columnPosition,
                COLUMN_VISIBLE_FLAG AS columnVisibleFlag,
                COLUMN_FILTER_MATCH_MODE AS columnFilterMatchMode,
                COLUMN_FILTER_VALUE AS columnFilterValue,
                COLUMN_WIDTH AS columnWidth,
                COLUMN_STYLE AS columnStyle,
                COLUMN_TYPE AS columnType,
                COLUMN_RENDERED AS columnRendered,
                CUSTOM_LBL_ID AS columnCustomLblId,
                CASE WHEN UTC.CUSTOM_LBL_ID IS NOT NULL AND UTC.CUSTOM_LBL_ID != ""
                    THEN CL.LBL_CUSTOM ELSE UTC.COLUMN_HEADER END AS columnHeader
                FROM USER_TABLE_COLUMNS UTC
                LEFT JOIN  CUSTOM_LABELS CL
                ON UTC.CUSTOM_LBL_ID = CL.LBL_ID
                WHERE UTC.TABLE_NAME = $tableName
                AND USER_ID = $userId
            `

        const [dynamicGridColumns] = await sequelize.query(query, {
            bind: { userId, tableName }
        })
        return res.status(200).json({ data: dynamicGridColumns })
    } catch (error) {
        next(AppError.internal())
    }
}

// Helper function to generate embed URL with automatic user registration and namespace creation
async function generateEmbedUrlWithRegistration(client, awsAccountId, awsRegion, userEmail, namespace, req) {
    try {
        // First, ensure the namespace exists
        await ensureNamespaceExists(client, awsAccountId, namespace);

        // Try to generate embed URL
        return await attemptGenerateEmbedUrl(client, awsAccountId, awsRegion, userEmail, namespace, req);
    } catch (error) {
        if (error.name === 'ResourceNotFoundException' && error.message.includes('User')) {
            logger.info(`User ${userEmail} not found in QuickSight. Attempting to register user.`);

            try {
                // Register the user
                await registerQuickSightUser(client, awsAccountId, userEmail, namespace);

                // Retry generating embed URL
                return await attemptGenerateEmbedUrl(client, awsAccountId, awsRegion, userEmail, namespace, req);
            } catch (registrationError) {
                logger.error('Failed to register user in QuickSight:', registrationError);
                throw new Error('Failed to register user in QuickSight');
            }
        } else {
            throw error;
        }
    }
}

// Helper function to ensure namespace exists
async function ensureNamespaceExists(client, awsAccountId, namespace) {
    try {
        // Check if namespace exists
        await client.send(new DescribeNamespaceCommand({
            AwsAccountId: awsAccountId,
            Namespace: namespace
        }));
        logger.info(`Namespace ${namespace} already exists`);
    } catch (error) {
        if (error.name === 'ResourceNotFoundException') {
            logger.info(`Namespace ${namespace} not found. Creating namespace.`);

            try {
                await client.send(new CreateNamespaceCommand({
                    AwsAccountId: awsAccountId,
                    Namespace: namespace,
                    Name: namespace,
                    IdentityStore: 'QUICKSIGHT',
                    Tags: [
                        {
                            Key: 'CreatedBy',
                            Value: 'RepFabric'
                        },
                        {
                            Key: 'Environment',
                            Value: process.env.NODE_ENV || 'development'
                        }
                    ]
                }));
                logger.info(`Successfully created namespace: ${namespace}`);
            } catch (createError) {
                logger.error('Failed to create namespace:', createError);
                throw new Error(`Failed to create namespace: ${namespace}`);
            }
        } else {
            logger.error('Error checking namespace:', error);
            throw error;
        }
    }
}

// Helper function to register a new QuickSight user
async function registerQuickSightUser(client, awsAccountId, userEmail, namespace) {
    const registerCommand = new RegisterUserCommand({
        AwsAccountId: awsAccountId,
        Namespace: namespace,
        Email: userEmail,
        IdentityType: 'QUICKSIGHT',
        UserRole: 'READER', // Default role as requested
        UserName: userEmail.split('@')[0], // Use email prefix as username
        SessionName: userEmail
    });

    const response = await client.send(registerCommand);
    logger.info(`Successfully registered user ${userEmail} in namespace ${namespace} with role READER`);
    return response;
}

// Helper function to attempt generating embed URL
async function attemptGenerateEmbedUrl(client, awsAccountId, awsRegion, userEmail, namespace, req) {
    // Determine the experience configuration based on environment or request parameters
    const embedType = req.query.embedType || process.env.AWS_QUICKSIGHT_EMBED_TYPE || 'dashboard';
    let experienceConfiguration;

    if (embedType === 'dashboard' && process.env.AWS_QUICKSIGHT_DASHBOARD_ID) {
        // Dashboard embed - cleaner, focused view without QuickSight console chrome
        experienceConfiguration = {
            Dashboard: {
                InitialDashboardId: process.env.AWS_QUICKSIGHT_DASHBOARD_ID,
                FeatureConfigurations: {
                    StatePersistence: {
                        Enabled: true
                    },
                    SharedView: {
                        Enabled: false
                    },
                    Bookmarks: {
                        Enabled: true
                    }
                }
            }
        };
    } else {
        // Console embed - full QuickSight experience (fallback)
        experienceConfiguration = {
            QuickSightConsole: {
                InitialPath: '/start',
                FeatureConfigurations: {
                    StatePersistence: {
                        Enabled: true
                    }
                }
            }
        };
    }

    const command = new GenerateEmbedUrlForRegisteredUserCommand({
        AwsAccountId: awsAccountId,
        UserArn: `arn:aws:quicksight:${awsRegion}:${awsAccountId}:user/${namespace}/${userEmail}`,
        SessionLifetimeInMinutes: 600,
        ExperienceConfiguration: experienceConfiguration,
        AllowedDomains: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : []
    });

    const response = await client.send(command);
    return response.EmbedUrl;
}

async function dynamicParamsQuery(fields = [], req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    if (!fields.length) return []

    const allowedFields = {
        PARAM_SUBS_KEY: "IFNULL(PARAM_SUBS_KEY, '') as PARAM_SUBS_KEY",
        PARAM_SUBS_NAME: "IFNULL(PARAM_SUBS_NAME, '') as PARAM_SUBS_NAME",
        PARAM_SUBS_ADDRESS:
            "IFNULL(PARAM_SUBS_ADDRESS, '') as PARAM_SUBS_ADDRESS",
        PARAM_DATE_FORMAT: 'IFNULL(PARAM_DATE_FORMAT, 1) as PARAM_DATE_FORMAT',
        PARAM_TIME_FORMAT: 'IFNULL(PARAM_TIME_FORMAT, 2) as PARAM_TIME_FORMAT',
        PARAM_TIMEZONE_ID: "IFNULL(PARAM_TIMEZONE_ID, '') as PARAM_TIMEZONE_ID",
        LOGO_IMAGE: `CONCAT('${process.env.IMAGE_URL}', PARAM_SUBS_LOGO) as LOGO_IMAGE`,
        PARAM_SUBS_ID: 'IFNULL(PARAM_SUBS_ID, 0) as PARAM_SUBS_ID',
        PARAM_DEF_QUOT_RECIPIENT: 'PARAM_DEF_QUOT_RECIPIENT',
        CLIENT: "substring(PARAM_URL_EMAIL, locate('//', PARAM_URL_EMAIL) + 2, locate('.r', PARAM_URL_EMAIL) - locate('//', PARAM_URL_EMAIL) - 2) as CLIENT",
        TENANT_URL: "SUBSTRING_INDEX(SUBSTRING_INDEX(PARAM_URL_EMAIL, '//', -1), '/', 1) AS TENANT_URL"
    }

    const selectedFields = fields
        .filter((field) => allowedFields[field])
        .map((field) => allowedFields[field])
        .join(', ')

    if (!selectedFields) return []

    return sequelize.query(`SELECT ${selectedFields} FROM PARAMS`)
}

module.exports = {
    getCustomLabels,
    getCustomUserParams,
    getParamsConfig,
    getPrinciCustomer,
    getProdMast,
    getCompanyType,
    getStatus,
    getJobActivityStage,
    getCustomerPrice,
    getYoxelToken,
    getProdUOM,
    getPrinciCommRate,
    medicalMenuEnabled,
    yoxelTokenForUser,
    getProdMastOld, //remove once deployed to production
    extendedReportUrl,
    quicksightEmbedUrl,
    getPrincipals,
    getCustomers,
    getDivisionList,
    updateAllowDistriComm,
    setToken,
    getDynamicGridColumns,
    dynamicParamsQuery
}
