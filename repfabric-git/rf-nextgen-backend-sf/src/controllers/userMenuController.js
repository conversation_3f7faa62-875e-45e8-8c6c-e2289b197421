const crypto = require('crypto')
const { sequelize } = require('../models')
const axios = require('axios')
const logger = require('../utils/logger')

const getUserMenus = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const userId = req.session.userId
        const userName = req.session.userName
        const userLogin = req.session.userLogin
        const isAdmin = req.session.isAdmin
        const userLastSid = req.session.userLastSid
        const userEmailConfigMessage = req.session.userEmailConfigMessage
        const isSuperUser = req.session.superUser
        const isOwner = req.session.roleId
        const ssnId = req.session.sessionId

        const emailUrl = await emailRandomidGenerator(userId)
        const emailurlConcat = ` concat(MENU_URL,'?fn=maillist&reqid=${emailUrl}', '&autocheckemail=1')`
        let url = ''
        const [emailURL] = await sequelize.query(
            `select ${emailurlConcat} as url from MENUS where MENU_ID = 7`
        )
        if (emailURL.length > 0) {
            url = emailURL[0].url
        }

        const query = `SELECT MENUS.MENU_ID as id,MENUS.MENU_TEXT as menu,
        case when MENUS.MENU_ID=7 then ${emailurlConcat} else MENUS.MENU_URL end as url,
        USER_FLATMENUS.SEQUENCE_NUM as sequence,
        Case when USER_FLATMENUS.FLATMENU_USER_ID is null then 0 else 1 end as isFlatMenu FROM  MENUS
        join USER_MENU on USER_MENU.USRMENU_MENU_ID=MENUS.MENU_ID
        left join USER_FLATMENUS on USRMENU_USER_ID=USER_FLATMENUS.FLATMENU_USER_ID and
        USRMENU_MENU_ID = USER_FLATMENUS.FLATMENU_MENU_ID WHERE USRMENU_USER_ID=$userId and MENUS.MENU_EXEC_FLAG=1`
        let [userMenus] = await sequelize.query(query, {
            bind: { userId }
        })

        let data = {
            userName: {
                userName,
                userLogin,
                userId,
                isAdmin,
                userLastSid,
                userEmailConfigMessage,
                domain: process.env.HOST_NAME,
                emailUrl: url,
                sessionId: ssnId,
                isSuperUser,
                isOwner
            },
            userMenus: userMenus
        }

        return res.status(200).json(data)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const updateFlatMenu = async (req, res, next) => {
    try {
        const userId = req.session.userId
        const userflatmenu = req.body
        let userFlatMenuData = []

        userflatmenu.forEach((row) => {
            const menu = {
                FLATMENU_USER_ID: userId,
                FLATMENU_MENU_ID: row.id,
                SEQUENCE_NUM: row.sequence,
                SHOW_ICON: 1
            }
            userFlatMenuData.push(menu)
        })

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.UserFlatMenu.destroy({
            where: {
                FLATMENU_USER_ID: userId
            }
        })
            .then(() => {
                models.UserFlatMenu.bulkCreate(userFlatMenuData)
                    .then(() => {
                        const response = {
                            status: 'updated successfully'
                        }
                        jsfMenuUpdate(req)
                        return res.status(200).json({ response })
                    })
                    .catch(() => {
                        next(AppError.internal())
                    })
            })
            .catch(() => {
                next(AppError.internal())
            })
    } catch (error) {
        next(AppError.internal())
    }
}

async function jsfMenuUpdate(req) {
    let hostName = process.env.HOST_NAME

    if (process.env.NODE_ENV != 'development') {
        let host = req.get('host')
        hostName = host.split('/')[0]
    }

    return axios.get(`https://${hostName}/RepfabricCRM/FlatMenuService.xhtml`, {
        responseType: 'json',
        withCredentials: true,
        headers: {
            Cookie: `JSESSIONID=${req.cookies.JSESSIONID}`
        }
    })
}

const getSideMenus = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const userId = req.session.userId
        let data = []
        let menus = []

        const emailUrl = await emailRandomidGenerator(userId)
        const emailurlConcat = ` concat(MENU_URL,'?fn=maillist&reqid=${emailUrl}', '&autocheckemail=1')`

        let [userMenus] = await sequelize.query(
            `SELECT MENU_ID as id,MENU_TEXT as menu,MENU_INDEX as menuIndex,
        case when MENU_ID=7 then ${emailurlConcat} 
         when MENUS.MENU_EXEC_FLAG<>1 then '#' 
        else MENU_URL end as url,
        null as subMenu  
        FROM MENUS JOIN USER_MENU on USER_MENU.USRMENU_MENU_ID=MENUS.MENU_ID 
        WHERE USRMENU_USER_ID = $userId AND MENU_LEVEL=0 ORDER BY MENU_INDEX`,
            {
                bind: { userId }
            }
        )

        Promise.all(
            userMenus.map(async (row) => {
                const mainMenuId = row.menuIndex.slice(0, 3)

                data = await getUserSubMenu(mainMenuId, userId, row, req)
                menus.push(data)
            })
        )
            .then(() => {
                menus.sort(
                    (a, b) => parseInt(a.menuIndex) - parseInt(b.menuIndex)
                )
                menus.forEach((item) => {
                    delete item.menuIndex
                })
                return res.status(200).json(menus)
            })
            .catch((error) => {
                logger.error(error)
                next(AppError.internal())
            })
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

//To Get SubMenu details (1st Level)
const getUserSubMenu = async (id, userId, row, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let [userSubMenus] = await sequelize.query(
        `SELECT MENU_ID as id,MENU_TEXT as menu,MENU_INDEX as menuindex,MENU_MAIN_ID as
    subMenuId,case when MENUS.MENU_EXEC_FLAG<>1 then '#' 
    else MENU_URL end as url  FROM MENUS JOIN USER_MENU on USER_MENU.USRMENU_MENU_ID=MENUS.MENU_ID
       WHERE USRMENU_USER_ID=$userId AND MENU_LEVEL=1 and MENU_MAIN_ID=$id ORDER BY MENU_INDEX`,
        {
            bind: { userId, id }
        }
    )
    let submenu = []
    userSubMenus.forEach(async (data) => {
        if (id == data.subMenuId) {
            submenu.push(data)
        }
    })
    row.subMenu = submenu
    await getUserChildMenu(userId, submenu, req)

    return row
}

async function emailRandomidGenerator(userId) {
    const firtsFive = crypto.randomInt(10000, 99999)
    const lastOne = crypto.randomInt(1, 9)
    return firtsFive + '' + userId + '' + lastOne
}

//To Get SubMenu details (2nd Level)
const getUserChildMenu = async (userId, data, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    for await (let item of data) {
        let childMenu = []
        const childId = item.menuindex.substring(3, 6)

        let subMenuId = item.subMenuId
        let [userSubMenus] = await sequelize.query(
            `SELECT MENU_ID as id,MENU_TEXT as menu,MENU_SUB_ID as childId,
            case when MENUS.MENU_EXEC_FLAG<>1 then '#' 
            else MENU_URL end as url
        FROM MENUS JOIN USER_MENU on USER_MENU.USRMENU_MENU_ID=MENUS.MENU_ID
        WHERE USRMENU_USER_ID=$userId AND MENU_LEVEL=2 and MENU_SUB_ID=$childId and MENU_MAIN_ID=$subMenuId order by MENU_INDEX`,
            {
                bind: { userId, childId, subMenuId }
            }
        )
        userSubMenus.map((row) => {
            if (childId == row.childId) {
                childMenu.push(row)
            }
        })
        childMenu.forEach((menuItem) => {
            delete menuItem.childId
        })
        item.subMenu = childMenu
    }

    data.forEach((item) => {
        delete item.subMenuId
        delete item.menuindex
    })

    return data
}

const getParams = async (req, res, next) => {
    try {
        const systemTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone
        let [params] = await paramsQuery(req)
        const paramsValue = params.length > 0 ? params[0] : ''
        const result = [
            {
                ...paramsValue,
                localTimezone: systemTimezone
            }
        ]
        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

async function paramsQuery(req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(` SELECT
    IFNULL(PARAM_SUBS_KEY,'') as PARAM_SUBS_KEY,
    IFNULL(PARAM_SUBS_NAME, '') as PARAM_SUBS_NAME,
    IFNULL(PARAM_SUBS_ADDRESS, '') as PARAM_SUBS_ADDRESS,
    IFNULL(PARAM_DATE_FORMAT, 1) as PARAM_DATE_FORMAT,
    IFNULL(PARAM_TIME_FORMAT, 2) as PARAM_TIME_FORMAT,
    IFNULL(PARAM_TIMEZONE_ID, '') as PARAM_TIMEZONE_ID,
    CONCAT('${process.env.IMAGE_URL}',PARAM_SUBS_LOGO) as LOGO_IMAGE,
    IFNULL(PARAM_SUBS_ID,0) as PARAM_SUBS_ID, PARAM_DEF_QUOT_RECIPIENT, 
    substring(PARAM_URL_EMAIL, locate('//', PARAM_URL_EMAIL)+2, locate('.r', PARAM_URL_EMAIL) - locate('//', PARAM_URL_EMAIL)-2) CLIENT FROM PARAMS
    `)
}

module.exports = {
    getUserMenus,
    updateFlatMenu,
    getSideMenus,
    getParams,
    paramsQuery
}
