const {
    getPagination,
    getPagingData,
    userDetails
} = require('../helpers/index')
const { sequelize } = require('../models')
const { PAGE, SIZE } = require('../constants/index')
const logger = require('../utils/logger')

const getCompanyDetails = async (req, res, next) => {
    try {
        const pageAsNumber = Number.parseInt(req.query.page)
        const sizeAsNumber = Number.parseInt(req.query.size)
        let sort = req.query.sort
        let order = req.query.order

        let page = PAGE
        if (!Number.isNaN(pageAsNumber) && pageAsNumber > PAGE) {
            page = pageAsNumber
        }

        let size = SIZE
        if (
            !Number.isNaN(sizeAsNumber) &&
            sizeAsNumber > 0 &&
            sizeAsNumber < SIZE
        ) {
            size = sizeAsNumber
        }

        const { limit, offset } = getPagination(page, size)

        let where = {}
        where = searchByFields(req.query)

        let sortOrderBy = ''
        sortOrderBy = [`COMP_NAME`]

        if (sort && order) {
            sortOrderBy = [`${sort}`, `${order}`]
        }

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const data = await models.VIEW_COMPANY_LOOKUP.findAndCountAll({
            attributes: [
                ['COMP_ID', 'id'],
                ['COMP_NAME', 'name'],
                ['COMP_TYPE', 'typeId'],
                ['COMP_TYPE_NAME', 'type'],
                ['COMP_SMAN_ID', 'salesTeamId'],
                ['SMAN_NAME', 'salesTeam'],
                ['COMP_REGION_ID', 'regionId'],
                ['REGION_NAME', 'region'],
                ['COMP_ADDRESS_1', 'street'],
                ['COMP_CITY', 'city'],
                ['COMP_STATE', 'state'],
                ['COMP_ZIP_CODE', 'zipCode'],
                ['COMP_PHONE_1', 'phone']
            ],
            where: {
                [Op.and]: [where]
            },
            order: [sortOrderBy],
            limit,
            offset
        })

        return res.status(200).json(getPagingData(data, page, size))
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const updateCompanyDetails = async (req, res, next) => {
    try {
        const user = userDetails(req, res, next)
        const company = req.body
        const compId = req.params.id

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.COMPANY.count({
            where: { COMP_ID: compId }
        })
            .then((count) => {
                if (count > 0) {
                    models.COMPANY.update(
                        {
                            COMP_NAME: company.compName,
                            COMP_ADDRESS_1: company.compAddress1,
                            COMP_CITY: company.compCity,
                            COMP_STATE: company.compState,
                            UPD_USER: user.userId
                        },
                        {
                            where: { COMP_ID: compId }
                        }
                    )
                        .then(() => res.status(200).json(count))
                        .catch(() => {
                            next(AppError.internal())
                        })
                } else {
                    return res.status(404).json(count)
                }
            })
            .catch(() => {
                next(AppError.internal())
            })
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}
function searchByFields(queryString) {
    const {
        name,
        street,
        type,
        salesTeam,
        region,
        city,
        state,
        zipCode,
        phone,
        typeId
    } = queryString

    const where = {}

    if (name) {
        where.COMP_NAME = { [Op.like]: `%${name}%` }
    }

    if (street) {
        where.COMP_ADDRESS_1 = { [Op.like]: `%${street}%` }
    }

    if (salesTeam) {
        where.SMAN_NAME = { [Op.like]: `%${salesTeam}%` }
    }

    if (type) {
        where.COMP_TYPE_NAME = { [Op.like]: `%${type}%` }
    }
    if (region) {
        where.REGION_NAME = { [Op.like]: `%${region}%` }
    }
    if (city) {
        where.COMP_CITY = { [Op.like]: `%${city}%` }
    }
    if (state) {
        where.COMP_STATE = { [Op.like]: `%${state}%` }
    }
    if (zipCode) {
        where.COMP_ZIP_CODE = { [Op.like]: `%${zipCode}%` }
    }
    if (phone) {
        where.COMP_PHONE_1 = { [Op.like]: `%${phone}%` }
    }
    if (typeId) {
        where.COMP_TYPE = { [Op.eq]: `${typeId}` }
    }
    return where
}

const getCompanyRegion = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const data = await sequelize.query(
            'SELECT REC_ID AS id, COMP_REGION AS region FROM COMPANY_REGIONS_MST'
        )

        return res.status(200).json(data)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getPersonCompanies = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const userId = req.session.userId
        const isOwner = req.session.roleId

        const salesTeamIds = await salesTeam(userId, req)
        const customUserParams = await getCustomUserParams(userId, req)

        let where = {}

        where = `COMP_ACTIVE_FLAG = 1 AND COMP_UNIFIED_ID>0 AND COMP_TAGGED= 1`

        if (
            isOwner != 1 &&
            (!customUserParams || customUserParams.length == 0)
        ) {
            where = where + ` AND COMP_SMAN_ID IN (${salesTeamIds})`
        }

        const [doctors] =
            await sequelize.query(`SELECT COMP_ID as id, COMP_NAME as name, COMP_TYPE as type, COMP_SMAN_ID as salesTeamId, COMP_REGION_ID as regionId, COMP_ADDRESS_1 as street, COMP_CITY as city, 
        COMP_STATE as state, COMP_ZIP_CODE as zipCode, COMP_PHONE_1 as phone, COMP_UNIFIED_ID as unifiedId, CONT_TITLE as title, CONT_LNAME as lastName FROM COMPANIES C 
        LEFT JOIN CONTACTS CA ON CA.CONT_COMP_ID = C.COMP_ID 
        WHERE ${where} ORDER BY COMP_NAME ASC`)

        return res.status(200).json(doctors)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getCompanyByPhoneNumber = async (voip_callerid) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize; 

    const query = `
        SELECT COMP_ID FROM COMPANIES
        WHERE  ( '${voip_callerid}' like CONCAT('%',REGEXP_REPLACE(COMP_PHONE_1, '[^0-9]+', '')) AND length(COMP_PHONE_1) > 7 AND REGEXP_REPLACE(COMP_PHONE_1, '[^0-9]+', '') not in ('','0'))
        OR ('${voip_callerid}' like  CONCAT('%',REGEXP_REPLACE(COMP_PHONE_2, '[^0-9]+', '')) AND length(COMP_PHONE_2) > 7 AND REGEXP_REPLACE(COMP_PHONE_2, '[^0-9]+', '') not in ('','0'))`
    const [result] = await sequelize.query(query)
    return result
}

const getCompanyById = async (companyId) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    const query = `
    SELECT
    COMP_ID,
    COMP_NAME,
    COMP_ADDRESS_1,
    COMP_CITY,
    COMP_STATE,
    COMP_ZIP_CODE,
    COMP_TYPE
    FROM COMPANIES WHERE COMP_ID=$companyId`
    const [result] = await sequelize.query(query, {
        bind: {
            companyId
        }
    })
    return result
}

const getCompany = async (req, res, next) => {
    try {
        const result = await getCompanyById(req.params.id)
        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

module.exports = {
    getCompanyDetails,
    updateCompanyDetails,
    getCompanyRegion,
    getPersonCompanies,
    getCompanyByPhoneNumber,
    getCompanyById,
    getCompany
}
