const { sequelize } = require('../../models')
const dayjs = require('dayjs')

const quoteList = async (req, res, next) => {
    try {
        const {
            fromDate,
            toDate,
            principals,
            customers,
            parentCompany,
            region,
            custCompType,
            quoteStatus,
            groupByPrinci
        } = req.query
        const userId = req.session.userId
        const roleId = req.session.roleId

        const salesTeamId = await salesTeam(userId, req)
        const privateTeamId = await privateTeam(userId, req)

        let where = {}
        let where1 = {}

        //Converting Date format
        const formattedFromDate = dayjs(fromDate).format('YYYY-MM-DD')
        const formattedToDate = dayjs(toDate).format('YYYY-MM-DD')

        where = ` (QUOT_DATE BETWEEN '${formattedFromDate}' AND '${formattedToDate}')`
        where1 = ` (QL.QUOT_DATE BETWEEN '${formattedFromDate}' AND '${formattedToDate}')`

        if (principals != undefined && principals != '') {
            where = ` ${where} AND QUOT_PRINCIPAL IN (${principals}) `
            where1 = ` ${where1} AND QD.QUOT_PRINCIPAL IN (${principals}) `
        }
        if (customers != undefined && customers != '') {
            where = ` ${where} AND QUOT_CUSTOMER IN (${customers}) `
            where1 = ` ${where1} AND QL.QUOT_CUSTOMER IN (${customers}) `
        }
        if (roleId != 1) {
            where = ` ${where} AND CUST_SMAN_ID IN (${salesTeamId}) `
            where1 = ` ${where1} AND QL.CUST_SMAN_ID IN (${salesTeamId}) `
        }
        if (parentCompany != undefined && parentCompany != '') {
            where = ` ${where} AND CUST_PARENT_ID IN (${parentCompany}) `
            where1 = ` ${where1} AND QL.CUST_PARENT_ID IN (${parentCompany}) `
        }
        if (region != undefined && region != '') {
            where = ` ${where} AND CUST_REGION_ID IN (${region}) `
            where1 = ` ${where1} AND QL.CUST_REGION_ID IN (${region}) `
        }
        if (custCompType != undefined && custCompType != '') {
            where = ` ${where} AND CUST_TYPE IN (${custCompType}) `
            where1 = ` ${where1} AND QL.CUST_TYPE IN (${custCompType}) `
        }
        if (quoteStatus != undefined && quoteStatus != '') {
            where = ` ${where} AND IFNULL(QUOT_DELIV_STATUS,0) IN (${quoteStatus}) `
            where1 = ` ${where1} AND IFNULL(QL.QUOT_DELIV_STATUS,0) IN (${quoteStatus}) `
        }

        where = `${where} AND 
        (CASE 
        WHEN QUOT_PRINCIPAL!=0 THEN 
        CUST_PRIV_TEAM  IN (0,${privateTeamId}) AND
        PRINCI_PRIV_TEAM  IN (0,${privateTeamId}) AND
        DISTRI_PRIV_TEAM  IN (0,${privateTeamId})
        ELSE 1=1
        END) `

        let query = ''
        if (groupByPrinci == true) {
            query = `SELECT REC_ID as id, OPP_ID as oppId, QUOT_NUMBER as quoteNumber, 
        QUOT_DATE as quoteDate, QUOT_PRINCIPAL as quotePrincipal, 
        PRINCI_NAME as princiName, QUOT_CUSTOMER as quoteCustomer, CUST_NAME as custName
        , CUST_REGION_ID as custRegionId, CUST_REGION_NAME as custRegionName, 
        CUST_PARENT_ID as custParentId, CUST_PARENT_NAME as custParentName, QUOT_DISTRI as quoteDistri
        , DISTRI_NAME as distriName, QUOT_OWNER as quoteOwner, 
        OWNER_NAME as ownerName, INS_USER as insUser, INS_USER_NAME as insUserName, 
        QUOT_VALUE as quoteValue, QUOT_NOTES as quoteNote, QUOT_OPEN_STATUS as quoteOpenStatus, 
        QUOT_OPEN_STATUS_NAME as quoteOpenStatusName, QUOT_DELIV_STATUS as quoteDelivStatus, 
        QUOT_DELIV_STATUS_NAME as quoteDelivStatusName, BUYING_GROUPS as quoteBuyingGroup, CUST_TYPE as custType
        FROM  VIEW_QUOTE_LIST  QL WHERE IFNULL(QUOT_PRINCIPAL,0)!=0 AND
        ${where}
        UNION 
        SELECT QD.QUOT_HDR_ID as id, QL.OPP_ID as oppId, QL.QUOT_NUMBER as quoteNumber, 
        QL.QUOT_DATE as quoteDate, QD.QUOT_PRINCIPAL as quotePrincipal, 
        QD.PRINCI_NAME as princiName, QL.QUOT_CUSTOMER as quoteCustomer, QL.CUST_NAME as custName
        , QL.CUST_REGION_ID as custRegionId, QL.CUST_REGION_NAME as custRegionName, 
        QL.CUST_PARENT_ID as custParentId, QL.CUST_PARENT_NAME as custParentName, QL.QUOT_DISTRI as quoteDistri
        , QL.DISTRI_NAME as distriName, QL.QUOT_OWNER as quoteOwner, 
        QL.OWNER_NAME as ownerName, QL.INS_USER as insUser, QL.INS_USER_NAME as insUserName, 
        SUM(QD.QUOT_EXT_PRICE) as quoteValue, QL.QUOT_NOTES as quoteNote, QL.QUOT_OPEN_STATUS as quoteOpenStatus, 
        QL.QUOT_OPEN_STATUS_NAME as quoteOpenStatusName, QL.QUOT_DELIV_STATUS as quoteDelivStatus, 
        QL.QUOT_DELIV_STATUS_NAME as quoteDelivStatusName,GetBuyingGroups(QD.QUOT_CUSTOMER,
        QD.QUOT_PRINCIPAL) AS quoteBuyingGroup, QL.CUST_TYPE as custType
        FROM  VIEW_QUOTE_LIST  QL
        JOIN VIEW_QUOTE_DTL QD ON QL.REC_ID =  QD.QUOT_HDR_ID
        WHERE IFNULL(QL.QUOT_PRINCIPAL,0)=0 AND
        ${where1}
        GROUP BY 
        QD.QUOT_HDR_ID,QL.OPP_ID,QL.QUOT_NUMBER,QL.QUOT_DATE,QD.QUOT_PRINCIPAL,QD.PRINCI_NAME, QL.QUOT_CUSTOMER, QL.CUST_NAME, QL.CUST_REGION_ID, 
        QL.CUST_REGION_NAME, QL.CUST_PARENT_ID, QL.CUST_PARENT_NAME, QL.QUOT_DISTRI, QL.DISTRI_NAME, QL.QUOT_OWNER, QL.OWNER_NAME, QL.INS_USER, 
        QL.INS_USER_NAME, QL.QUOT_NOTES, QL.QUOT_OPEN_STATUS, QL.QUOT_OPEN_STATUS_NAME, QL.QUOT_DELIV_STATUS, QL.QUOT_DELIV_STATUS_NAME, 
        QL.BUYING_GROUPS, QL.CUST_TYPE
        `
        } else {
            query = `SELECT REC_ID as id, OPP_ID as oppId, QUOT_NUMBER as quoteNumber, 
            QUOT_DATE as quoteDate, QUOT_PRINCIPAL as quotePrincipal, 
            PRINCI_NAME as princiName, QUOT_CUSTOMER as quoteCustomer, CUST_NAME as custName
            , CUST_REGION_ID as custRegionId, CUST_REGION_NAME as custRegionName, 
            CUST_PARENT_ID as custParentId, CUST_PARENT_NAME as custParentName, QUOT_DISTRI as quoteDistri
            , DISTRI_NAME as distriName, QUOT_OWNER as quoteOwner, 
            OWNER_NAME as ownerName, INS_USER as insUser, INS_USER_NAME as insUserName, 
            QUOT_VALUE as quoteValue, QUOT_NOTES as quoteNote, QUOT_OPEN_STATUS as quoteOpenStatus, 
            QUOT_OPEN_STATUS_NAME as quoteOpenStatusName, QUOT_DELIV_STATUS as quoteDelivStatus, 
            QUOT_DELIV_STATUS_NAME as quoteDelivStatusName, BUYING_GROUPS as quoteBuyingGroup, CUST_TYPE as custType
            FROM  VIEW_QUOTE_LIST WHERE
            ${where}`
        }

        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const [data] = await sequelize.query(query)

        const query1 = `SELECT ifnull(QUOT_DELIV_STATUS,0) as statusCode, 
        if(ifnull(QUOT_DELIV_STATUS,0)=0, '- No Status -', 
        QUOT_DELIV_STATUS_NAME) as status, sum(QUOT_VALUE) as value, 
        count(*) as count FROM VIEW_QUOTE_LIST 
        WHERE
        ${where} group by ifnull(QUOT_DELIV_STATUS,0) `
        const [rollup] = await sequelize.query(query1)

        return res.status(200).json({ rollups: rollup, gridData: data })
    } catch (error) {
        next(AppError.internal())
    }
}

const formatDate = (date) => dayjs(date).format('YYYY-MM-DD')

const addCondition = (condition, clause, value, replacements, key) => {
    if (value != undefined && value != '') {
        condition += ` AND ${clause} IN (:${key}) `
        replacements[key] = value.split(',') // Assume value is a comma-separated string
    }
    return condition
}

const getWhereClause = (params, replacements) => {
    let where = ` (QUOT_DATE BETWEEN :formattedFromDate AND :formattedToDate)`
    let where1 = ` (QL.QUOT_DATE BETWEEN :formattedFromDate AND :formattedToDate)`

    if (params.principals != undefined && params.principals != '') {
        where = addCondition(
            where,
            'QUOT_PRINCIPAL',
            params.principals,
            replacements,
            'principals'
        )
        where1 = addCondition(
            where1,
            'QD.QUOT_PRINCIPAL',
            params.principals,
            replacements,
            'principals'
        )
    }
    if (params.customers != undefined && params.customers != '') {
        where = addCondition(
            where,
            'QUOT_CUSTOMER',
            params.customers,
            replacements,
            'customers'
        )
        where1 = addCondition(
            where1,
            'QL.QUOT_CUSTOMER',
            params.customers,
            replacements,
            'customers'
        )
    }
    if (params.roleId != 1) {
        where += ` AND CUST_SMAN_ID IN (:salesTeamId) `
        where1 += ` AND QL.CUST_SMAN_ID IN (:salesTeamId) `
        if (params.salesTeamId != 0) {
            replacements.salesTeamId = params.salesTeamId.split(',')
        } else {
            replacements.salesTeamId = params.salesTeamId
        }
    }
    if (params.parentCompany != undefined && params.parentCompany != '') {
        where = addCondition(
            where,
            'CUST_PARENT_ID',
            params.parentCompany,
            replacements,
            'parentCompany'
        )
        where1 = addCondition(
            where1,
            'QL.CUST_PARENT_ID',
            params.parentCompany,
            replacements,
            'parentCompany'
        )
    }
    if (params.region != undefined && params.region != '') {
        where = addCondition(
            where,
            'CUST_REGION_ID',
            params.region,
            replacements,
            'region'
        )
        where1 = addCondition(
            where1,
            'QL.CUST_REGION_ID',
            params.region,
            replacements,
            'region'
        )
    }
    if (params.custCompType != undefined && params.custCompType != '') {
        where = addCondition(
            where,
            'CUST_TYPE',
            params.custCompType,
            replacements,
            'custCompType'
        )
        where1 = addCondition(
            where1,
            'QL.CUST_TYPE',
            params.custCompType,
            replacements,
            'custCompType'
        )
    }
    if (params.quoteStatus != undefined && params.quoteStatus != '') {
        where = addCondition(
            where,
            'IFNULL(QUOT_DELIV_STATUS,0)',
            params.quoteStatus,
            replacements,
            'quoteStatus'
        )
        where1 = addCondition(
            where1,
            'IFNULL(QL.QUOT_DELIV_STATUS,0)',
            params.quoteStatus,
            replacements,
            'quoteStatus'
        )
    }
    where += ` AND (CASE 
        WHEN QUOT_PRINCIPAL != 0 THEN 
        CUST_PRIV_TEAM IN (0, :privateTeamId) AND
        PRINCI_PRIV_TEAM IN (0, :privateTeamId) AND
        DISTRI_PRIV_TEAM IN (0, :privateTeamId)
        ELSE 1=1
        END) `
    if (params.privateTeamId != 0) {
        replacements.privateTeamId = params.privateTeamId.split(',')
    } else {
        replacements.privateTeamId = params.privateTeamId
    }

    if (params.showOnlyParentQuotes == 'true') {
        where += ` AND REC_ID IN (
            SELECT REC_ID FROM QUOTES_HDR WHERE REC_ID = QUOT_PARENT_ID
        )`

        where1 += ` AND QL.REC_ID IN (
            SELECT REC_ID FROM QUOTES_HDR WHERE REC_ID = QUOT_PARENT_ID
        )`
    }

    return { where, where1 }
}

const getQueries = (params, where, where1) => {
    let query = ''
    if (params.groupByPrinci == true) {
        query = `SELECT REC_ID as id, OPP_ID as oppId, QUOT_NUMBER as quoteNumber, 
            QUOT_DATE as quoteDate, QUOT_PRINCIPAL as quotePrincipal, 
            PRINCI_NAME as princiName, QUOT_CUSTOMER as quoteCustomer, CUST_NAME as custName,
            CUST_REGION_ID as custRegionId, CUST_REGION_NAME as custRegionName, 
            CUST_PARENT_ID as custParentId, CUST_PARENT_NAME as custParentName, QUOT_DISTRI as quoteDistri,
            DISTRI_NAME as distriName, QUOT_OWNER as quoteOwner, 
            OWNER_NAME as ownerName, INS_USER as insUser, INS_USER_NAME as insUserName, 
            QUOT_VALUE as quoteValue, QUOT_NOTES as quoteNote, QUOT_OPEN_STATUS as quoteOpenStatus, 
            QUOT_OPEN_STATUS_NAME as quoteOpenStatusName, QUOT_DELIV_STATUS as quoteDelivStatus, 
            QUOT_DELIV_STATUS_NAME as quoteDelivStatusName, BUYING_GROUPS as quoteBuyingGroup, CUST_TYPE as custType
            FROM VIEW_QUOTE_LIST QL WHERE IFNULL(QUOT_PRINCIPAL, 0) != 0 AND ${where}
            UNION 
            SELECT QD.QUOT_HDR_ID as id, QL.OPP_ID as oppId, QL.QUOT_NUMBER as quoteNumber, 
            QL.QUOT_DATE as quoteDate, QD.QUOT_PRINCIPAL as quotePrincipal, 
            QD.PRINCI_NAME as princiName, QL.QUOT_CUSTOMER as quoteCustomer, QL.CUST_NAME as custName,
            QL.CUST_REGION_ID as custRegionId, QL.CUST_REGION_NAME as custRegionName, 
            QL.CUST_PARENT_ID as custParentId, QL.CUST_PARENT_NAME as custParentName, QL.QUOT_DISTRI as quoteDistri,
            QL.DISTRI_NAME as distriName, QL.QUOT_OWNER as quoteOwner, 
            QL.OWNER_NAME as ownerName, QL.INS_USER as insUser, QL.INS_USER_NAME as insUserName, 
            SUM(QD.QUOT_EXT_PRICE) as quoteValue, QL.QUOT_NOTES as quoteNote, QL.QUOT_OPEN_STATUS as quoteOpenStatus, 
            QL.QUOT_OPEN_STATUS_NAME as quoteOpenStatusName, QL.QUOT_DELIV_STATUS as quoteDelivStatus, 
            QL.QUOT_DELIV_STATUS_NAME as quoteDelivStatusName, GetBuyingGroups(QD.QUOT_CUSTOMER, QD.QUOT_PRINCIPAL) AS quoteBuyingGroup, QL.CUST_TYPE as custType
            FROM VIEW_QUOTE_LIST QL
            JOIN VIEW_QUOTE_DTL QD ON QL.REC_ID = QD.QUOT_HDR_ID
            WHERE IFNULL(QL.QUOT_PRINCIPAL, 0) = 0 AND ${where1}
            GROUP BY QD.QUOT_HDR_ID, QL.OPP_ID, QL.QUOT_NUMBER, QL.QUOT_DATE, QD.QUOT_PRINCIPAL, QD.PRINCI_NAME, QL.QUOT_CUSTOMER, QL.CUST_NAME, QL.CUST_REGION_ID, 
            QL.CUST_REGION_NAME, QL.CUST_PARENT_ID, QL.CUST_PARENT_NAME, QL.QUOT_DISTRI, QL.DISTRI_NAME, QL.QUOT_OWNER, QL.OWNER_NAME, QL.INS_USER, 
            QL.INS_USER_NAME, QL.QUOT_NOTES, QL.QUOT_OPEN_STATUS, QL.QUOT_OPEN_STATUS_NAME, QL.QUOT_DELIV_STATUS, QL.QUOT_DELIV_STATUS_NAME, 
            QL.BUYING_GROUPS, QL.CUST_TYPE`
    } else {
        query = `SELECT REC_ID as id, OPP_ID as oppId, QUOT_NUMBER as quoteNumber, 
            QUOT_DATE as quoteDate, QUOT_PRINCIPAL as quotePrincipal, 
            PRINCI_NAME as princiName, QUOT_CUSTOMER as quoteCustomer, CUST_NAME as custName,
            CUST_REGION_ID as custRegionId, CUST_REGION_NAME as custRegionName, 
            CUST_PARENT_ID as custParentId, CUST_PARENT_NAME as custParentName, QUOT_DISTRI as quoteDistri,
            DISTRI_NAME as distriName, QUOT_OWNER as quoteOwner, 
            OWNER_NAME as ownerName, INS_USER as insUser, INS_USER_NAME as insUserName, 
            QUOT_VALUE as quoteValue, QUOT_NOTES as quoteNote, QUOT_OPEN_STATUS as quoteOpenStatus, 
            QUOT_OPEN_STATUS_NAME as quoteOpenStatusName, QUOT_DELIV_STATUS as quoteDelivStatus, 
            QUOT_DELIV_STATUS_NAME as quoteDelivStatusName, BUYING_GROUPS as quoteBuyingGroup, CUST_TYPE as custType
            FROM VIEW_QUOTE_LIST WHERE ${where}`
    }

    const query1 = `SELECT IFNULL(QUOT_DELIV_STATUS, 0) as statusCode, 
        IF(IFNULL(QUOT_DELIV_STATUS, 0) = 0, '- No Status -', QUOT_DELIV_STATUS_NAME) as status, 
        SUM(QUOT_VALUE) as value, COUNT(*) as count 
        FROM VIEW_QUOTE_LIST WHERE ${where} GROUP BY status`

    return { query, query1 }
}

const getQuotes = async (req, res, next) => {
    req.query.userId = req.session.userId
    req.query.roleId = req.session.roleId
    req.query.salesTeamId = await salesTeam(req.session.userId, req)
    req.query.privateTeamId = await privateTeam(req.session.userId, req)
    const params = req.query
    const replacements = {
        formattedFromDate: formatDate(params.fromDate),
        formattedToDate: formatDate(params.toDate)
    }

    const { where, where1 } = getWhereClause(params, replacements)
    const { query, query1 } = getQueries(params, where, where1)

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    try {
        const [data] = await sequelize.query(query, { replacements })
        const [rollup] = await sequelize.query(query1, { replacements })

        return res.status(200).json({ rollups: rollup, gridData: data })
    } catch (error) {
        next(AppError.internal())
    }
}
module.exports = { quoteList, getQuotes }
