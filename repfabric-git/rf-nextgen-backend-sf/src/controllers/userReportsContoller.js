const dayjs = require('dayjs')
const utc = require('dayjs/plugin/utc')
const { sequelize } = require('../models')
const logger = require('../utils/logger')

const saveUserReports = async (req, res, next) => {
    try {
        const row = req.body.layout
        let cardId

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        for await (const item of row) {
            if (item.id != '') {
                await models.USER_REPORT.update(
                    {
                        USER_ID: req.session.userId,
                        REPORT_ID: item.reportId,
                        DIMENSIONS: item.dimensions
                    },
                    {
                        where: {
                            REC_ID: item.id
                        }
                    }
                )
            } else {
                cardId = await models.USER_REPORT.create({
                    USER_ID: req.session.userId,
                    REPORT_ID: item.reportId
                })
                cardId = cardId.get('REC_ID')
                item.dimensions.i = cardId
                await models.USER_REPORT.update(
                    {
                        DIMENSIONS: item.dimensions
                    },
                    {
                        where: {
                            REC_ID: cardId
                        }
                    }
                )
            }
        }

        return res.status(200).json({
            message: 'Success',
            cardId
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const getUserReports = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const userId = req.session.userId
        let [data] = await sequelize.query(
            `
            SELECT
            UR.REC_ID as id,
            UR.REPORT_ID as reportId,
            UR.DIMENSIONS as dimensions,
            CASE
                WHEN R.CUSTOM_LABEL_ID IS NULL THEN R.REPORT_NAME
            ELSE
                CL.LBL_CUSTOM
            END AS report
            FROM USER_REPORTS UR
            JOIN REPORTS R
            ON R.REPORT_ID = UR.REPORT_ID
            LEFT JOIN CUSTOM_LABELS CL ON CL.LBL_ID = R.CUSTOM_LABEL_ID
            WHERE UR.USER_ID = $userId
        `,
            {
                bind: { userId }
            }
        )

        for await (let item of data) {
            item['dimensions'] = JSON.parse(item.dimensions)
        }

        return res.status(200).json({
            data
        })
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const deleteUserReport = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const isExist = await models.USER_REPORT.count({
            where: {
                REC_ID: req.params.id
            }
        })

        if (isExist == 0) {
            next(AppError.badRequest({ message: 'Record not found' }))
            return
        }

        await models.USER_REPORT.destroy({
            where: {
                REC_ID: req.params.id
            }
        })

        return res.status(200).json({
            message: 'Deleted successfully'
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const saveUserReportParams = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const row = req.body.params
        row.forEach(async (item) => {
            await models.USER_REPORT_PARAM.create({
                USER_REPORT_ID: req.body.userReportId,
                PARAM_ID: item.paramId,
                PARAM_NAME: item.name,
                MULTI_OR_SINGLE: item.multiOrSingle,
                PARAM_VALUE: item.value
            })
        })

        return res.status(200).json({
            message: 'Success'
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const updateUserReportParams = async (req, res, next) => {
    try {
        const row = req.body.params
        
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        row.forEach(async (item) => {
            if (item.id != '') {
                await models.USER_REPORT_PARAM.update(
                    {
                        PARAM_ID: item.paramId,
                        PARAM_NAME: item.name,
                        MULTI_OR_SINGLE: item.multiOrSingle,
                        PARAM_VALUE: item.value
                    },
                    {
                        where: {
                            REC_ID: item.id
                        }
                    }
                )
            } else {
                await models.USER_REPORT_PARAM.create({
                    USER_REPORT_ID: req.params.userReportId,
                    PARAM_ID: item.paramId,
                    PARAM_NAME: item.name,
                    MULTI_OR_SINGLE: item.multiOrSingle,
                    PARAM_VALUE: item.value
                })
            }
        })

        return res.status(200).json({
            message: 'Updated successfully'
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const userReportFilter = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;
    
    try {
        const row = req.body
        dayjs.extend(utc)
        const updDate = dayjs().utc().format('YYYY-MM-DD HH:mm:ss')

        if (row) {
            if (!row[0].rptUserId) {
                return res.status(500).json('Invalid Data')
            }

            // Use bind parameters with named replacements for safety and compatibility
            const deleteQuery = `DELETE FROM RF_DBD_DB_${tenantId}.USER_REPORT_FILTERS 
                WHERE RPT_USER_ID = :rptUserId AND RPT_ID = :rptId`;
                
            await sequelize.query(deleteQuery, {
                replacements: { rptUserId: row[0].rptUserId, rptId: row[0].rptId },
                type: sequelize.QueryTypes.DELETE
            })

            row.map(async (item) => {
                const rptUserId = item.rptUserId,
                    rptId = item.rptId,
                    rptFilterId = item.rptFilterId,
                    rptFilterValue = item.rptFilterValue ?? null,
                    rptFilterFlag = item.rptFilterFlag

                await sequelize.query(
                    `insert into RF_DBD_DB_${tenantId}.USER_REPORT_FILTERS (RPT_USER_ID, RPT_ID, 
                RPT_FILTER_ID, RPT_FILTER_VALUE, RPT_FILTER_FLAG,UPD_DATE) values ($rptUserId,
                    $rptId,$rptFilterId,$rptFilterValue,$rptFilterFlag,$updDate)`,
                    {
                        bind: {
                            rptUserId,
                            rptId,
                            rptFilterId,
                            rptFilterValue,
                            rptFilterFlag,
                            updDate
                        }
                    }
                )
            })
        }
        return res.status(200).json('success')
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getUserReportFilter = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    try {
        const row = req.query
        const rptUserId = row.rptUserId,
            rptId = row.rptId

        const query = `SELECT REC_ID as recId, RPT_USER_ID as rptUserId, RPT_ID as rptId, 
        RPT_FILTER_ID as rptFilterId, RPT_FILTER_VALUE as rptFilterValue,
         RPT_FILTER_FLAG as rptFilterFlag FROM RF_DBD_DB_${tenantId}.USER_REPORT_FILTERS 
         WHERE RPT_USER_ID=$rptUserId and RPT_ID=$rptId `

        const [result] = await sequelize.query(query, {
            bind: { rptUserId, rptId }
        })

        return res.status(200).json(result)
    } catch (error) {
        next(AppError.internal())
    }
}

const defaultUserReports = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;
    
    try {
        let cardId
        const userId = req.session.userId
        const isExists = await models.USER_REPORT.count({
            where: { USER_ID: userId }
        })

        if (isExists == 0) {
            let [userReports] = await sequelize.query(`
    select ${userId},REPORT_ID,DIMENSIONS,INS_DATE,UPD_DATE from USER_REPORTS where
    USER_ID=0`)
            userReports.forEach(async (item) => {
                const dimension = JSON.parse(item.DIMENSIONS)
                cardId = await models.USER_REPORT.create({
                    USER_ID: userId,
                    REPORT_ID: item.REPORT_ID,
                    DIMENSIONS: item.DIMENSIONS
                })

                cardId = cardId.get('REC_ID')
                dimension.i = `${cardId}`

                await models.USER_REPORT.update(
                    {
                        DIMENSIONS: dimension
                    },
                    {
                        where: {
                            REC_ID: cardId
                        }
                    }
                )
            })

            return res.status(200).json('Success')
        }
        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

module.exports = {
    saveUserReports,
    getUserReports,
    deleteUserReport,
    saveUserReportParams,
    updateUserReportParams,
    userReportFilter,
    getUserReportFilter,
    defaultUserReports
}
