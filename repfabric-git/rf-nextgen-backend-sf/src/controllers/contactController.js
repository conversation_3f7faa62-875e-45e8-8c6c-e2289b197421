const { getPagingData, userDetails } = require('../helpers/index')
const AppError = require('../utils/appError')
const { sequelize } = require('../models')
const { getCompanyByPhoneNumber } = require('../controllers/companyController')
const logger = require('../utils/logger')

const getContacts = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize; 

    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    try {
        const row = req.query
        const pageLimit = req.query.page ? req.query.page : PAGE
        const pagination = req.query.size ? req.query.size : SIZE
        let lowerlim = pagination * (pageLimit - 1)
        let upperLim = pagination
        const crmDb = `RF_CRM_DB_${tenantId}`;

        const limit = `LIMIT ${lowerlim},${upperLim}`
        const sort = req.query.sort ? req.query.sort : 'REC_ID'
        const order = req.query.order ? req.query.order : 'asc'
        const extension = `Order by ${sort} ${order}`

        let query = `SELECT count(*) over() as rowCount,
        REC_ID as id,
        CONT_ID as contactId,
        CONT_FULL_NAME as name,
        CONT_COMP_ID as contCompId,
        COMP_NAME as company,
        CONT_JOB_TITLE as jobTitle,
        CONT_EMAIL_BUSINESS as email,
        CONT_PHONES as phone,
        CONT_EMAIL_BUSINESS as businessEmail
        FROM ${crmDb}.VIEW_CONT_LOOKUP
        `
        let whereQuery = await searchFilter(row, query)

        if (Object.keys(whereQuery).length > 0) {
            query = whereQuery
        }

        const sqlQuery =
            row.id != undefined && row.id != ''
                ? `${query} ${limit}`
                : `${query} ${extension} ${limit}`
        const [result] = await sequelize.query(sqlQuery)
        const count = result.length > 0 ? result[0].rowCount : 0
        const rows = result.length > 0 ? result : []

        let data = {
            count,
            rows
        }

        return res.status(200).json(getPagingData(data, pageLimit, pagination))
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const searchFilter = async (row, query) => {
    let where = {}
    let field, value

    if (row.contId != undefined && row.contId != '') {
        field = 'CONT_ID'
        value = `${row.contId}`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'NUMERIC'
        )
    }

    if (row.name != undefined && row.name != '') {
        field = 'CONT_FULL_NAME'
        value = `'%${row.name}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }

    if (row.jobTitle != undefined && row.jobTitle != '') {
        field = 'CONT_JOB_TITLE'
        value = `'%${row.jobTitle}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }

    if (row.company != undefined && row.company != '') {
        field = 'COMP_NAME'
        value = `'%${row.company}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (row.jobTitle != undefined && row.jobTitle != '') {
        field = 'CONT_JOB_TITLE'
        value = `'%${row.jobTitle}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (row.email != undefined && row.email != '') {
        field = 'CONT_EMAIL_BUSINESS'
        value = `'%${row.email}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (row.phone != undefined && row.phone != '') {
        field = 'CONT_PHONES'
        value = `'%${row.phone}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (row.businessEmail != undefined && row.businessEmail != '') {
        field = 'CONT_EMAIL_BUSINESS'
        value = `'%${row.businessEmail}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    query = Object.keys(where).length > 0 ? query.concat(` where ${where}`) : ''

    return query
}

const updateContactDetails = async (req, res, next) => {
    try {
        const user = userDetails(req, res, next)
        const contact = req.body
        const contId = req.params.id

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.CONTACT.count({
            where: { CONT_ID: contId }
        })
            .then((count) => {
                if (count > 0) {
                    models.CONTACT.update(
                        {
                            CONT_FNAME: contact.contFName,
                            CONT_LNAME: contact.contLName,
                            CONT_FULL_NAME: contact.contFullName,
                            CONT_JOB_TITLE: contact.contJobTitle,
                            CONT_PHONE_BUSINESS: contact.contPhoneBusiness,
                            CONT_LOCATION: contact.contLocation,
                            UPD_USER: user.userId
                        },
                        {
                            where: { CONT_ID: contId }
                        }
                    )
                        .then(() => res.status(200).json(count))
                        .catch(() => {
                            next(AppError.internal())
                        })
                } else {
                    return res.status(404).json(count)
                }
            })
            .catch(() => {
                next(AppError.internal())
            })
    } catch (error) {
        next(AppError.internal())
    }
}

const filter = async (field, value, field1, value1, where, type) => {
    switch (type) {
        case 'DATE_RANGE':
            where =
                Object.keys(where).length !== 0
                    ? where +
                      ' AND ' +
                      field +
                      ' between ' +
                      value +
                      ' and ' +
                      value1
                    : field + ' between ' + value + ' and ' + value1
            return where
        case 'NUMERIC':
            where =
                Object.keys(where).length !== 0
                    ? where + ' AND ' + field + '=' + value
                    : field + '=' + value
            return where
        default:
            where =
                Object.keys(where).length !== 0
                    ? where + ' AND ' + field + ' like ' + value
                    : field + ' like ' + value
            return where
    }
}

// const getPrimaryContactForCompany1 = async (req, res, next) => {
// Use the tenant-specific Sequelize instance    
// const sequelize = req.tenantSequelize;
//     try {
//         const contId = req.query.contId ? req.query.contId : 0
//         let where = `1`
//         let personCompany, distributor
//         if (contId != 0) {
//             where = `CONT_ID = ${contId}`
//         } else {
//             personCompany = req.query.personCompany
//                 ? req.query.personCompany
//                 : 0
//             distributor = req.query.distributor ? req.query.distributor : 0
//             where = `CONT_COMP_ID in (${personCompany}, ${distributor}) AND CONT_PRIMARY_FLAG=1 `
//         }

//         const [result] =
//             await sequelize.query(`SELECT CONT_ID as contId, CONT_FULL_NAME as contName,
//         COMP_NAME as compName, CONT_EMAIL_BUSINESS as contEmail,
//         CONT_FAX as contFax FROM CONTACTS LEFT JOIN COMPANIES ON COMP_ID = CONT_COMP_ID
//         WHERE ${where}
//         ORDER BY CONTACTS.CONT_FULL_NAME ASC`)

//         return res.status(200).json(result)
//     } catch (error) {
//         logger.error(error)
//         next(AppError.internal())
//     }
// }

const getPrimaryContactForCompany = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const contId = req.query.contId ? req.query.contId : 0
        let whereClause = `1=1` // default where clause
        const replacements = {} // object to hold query parameter replacements

        if (contId != 0) {
            whereClause = `CONT_ID = :contId`
            replacements.contId = contId
        } else {
            const personCompany = req.query.personCompany
                ? req.query.personCompany
                : 0
            const distributor = req.query.distributor
                ? req.query.distributor
                : 0
            whereClause = `CONT_COMP_ID IN (:personCompany, :distributor) AND CONT_PRIMARY_FLAG=1`
            replacements.personCompany = personCompany
            replacements.distributor = distributor
        }

        const query = `
            SELECT 
                CONT_ID as contId, 
                CONT_FULL_NAME as contName, 
                COMP_NAME as compName, 
                CONT_EMAIL_BUSINESS as contEmail, 
                CONT_FAX as contFax 
            FROM CONTACTS 
            LEFT JOIN COMPANIES ON COMP_ID = CONT_COMP_ID 
            WHERE ${whereClause}
            ORDER BY CONTACTS.CONT_FULL_NAME ASC`

        const [result] = await sequelize.query(query, { replacements })

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getCompanyContacts = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize; 

    try {
        // const compId = req.query.compId ? req.query.compId : 0

        //Where condition removed for time being
        // WHERE CONT_COMP_ID IN (${compId})
        const [result] =
            await sequelize.query(`SELECT CONT_ID as contId, CONT_FULL_NAME as contName, 
        COMP_NAME as compName, CONT_EMAIL_BUSINESS as contEmail, 
        CONT_FAX as contFax FROM CONTACTS LEFT JOIN COMPANIES ON COMP_ID = CONT_COMP_ID 
        ORDER BY CONTACTS.CONT_FULL_NAME ASC`)

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getByPhoneNumber = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    try {
        const { voip_callerid } = req.query

        if (voip_callerid === undefined) {
            next()
            return;
        }

        let hostName

        if (process.env.NODE_ENV != 'development') {
            let host = req.get('host')
            hostName = host.split('/')[0]
        } else {
            hostName = 'twdev.repfabric.com'
        }

        const phoneNumber = await digitsOnly(voip_callerid)
        const query = `SELECT DISTINCT CONT_ID FROM CONTACTS
        WHERE
       ( '${phoneNumber}' like CONCAT('%',REGEXP_REPLACE(CONT_PHONE_HOME, '[^0-9]+', '')) AND length(CONT_PHONE_HOME) > 7 AND REGEXP_REPLACE(CONT_PHONE_HOME, '[^0-9]+', '') not in ('','0'))  
        OR ('${phoneNumber}' like  CONCAT('%',REGEXP_REPLACE(CONT_PHONE_BUSINESS, '[^0-9]+', '')) AND length(CONT_PHONE_BUSINESS) > 7 AND REGEXP_REPLACE(CONT_PHONE_BUSINESS, '[^0-9]+', '') not in ('','0'))
       OR ('${phoneNumber}' like  CONCAT('%',REGEXP_REPLACE(CONT_PHONE_MOBILE, '[^0-9]+', '')) AND length(CONT_PHONE_MOBILE) > 7 AND REGEXP_REPLACE(CONT_PHONE_MOBILE, '[^0-9]+', '') not in ('','0'))
       OR ('${phoneNumber}' like CONCAT('%',REGEXP_REPLACE(CONT_PHONE_ALTERNATE, '[^0-9]+', '')) AND length(CONT_PHONE_ALTERNATE) > 7 AND REGEXP_REPLACE(CONT_PHONE_ALTERNATE, '[^0-9]+', '') not in ('','0'))
       `

        const [contacts] = await sequelize.query(query)

        if (contacts.length == 1) {
            return res.redirect(
                `https://${hostName}/RepfabricCRM/opploop/contacts/ContactDetails.xhtml?id=${contacts[0].CONT_ID}`
            )
        }

        const company = await getCompanyByPhoneNumber(phoneNumber)
        if (company.length == 1) {
            return res.redirect(
                `https://${hostName}/RepfabricCRM/opploop/companies/CompanyDetails.xhtml?id=${company[0].COMP_ID}`
            )
        }

        return res.redirect(
            `https://${hostName}/RepfabricCRM/opploop/companies/List.xhtml`
        )
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

module.exports = {
    getContacts,
    updateContactDetails,
    getPrimaryContactForCompany,
    getCompanyContacts,
    getByPhoneNumber
}
