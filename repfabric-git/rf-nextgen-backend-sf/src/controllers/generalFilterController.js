const getExpenseFilter = async (req, res, next) => {
    try {
        const { filterName } = req.query

        if (!filterName) next(AppError.badRequest())

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;    

        const filerValue = await models.GENERALFILTER.findOne({
            attributes: [
                ['REC_ID', 'id'],
                ['FILTER_NAME', 'filterName'],
                ['FILTER_DATA', 'filterData']
            ],
            where: {
                INS_USER: req.session.userId,
                FILTER_NAME: filterName
            }
        })
        return res.status(200).json(filerValue)
    } catch (error) {
        next(AppError.internal())
    }
}

const createOrUpdateExpenseFilter = async (req, res, next) => {
    try {
        const row = req.body

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const ifExist = await models.GENERALFILTER.count({
            where: {
                FILTER_NAME: row.filterName,
                INS_USER: req.session.userId
            }
        })
        if (ifExist > 0) {
            await models.GENERALFILTER.update(
                {
                    FILTER_DATA: row.filterData
                },
                {
                    where: {
                        INS_USER: req.session.userId,
                        FILTER_NAME: row.filterName
                    }
                }
            )
        } else {
            await models.GENERALFILTER.create({
                FILTER_NAME: row.filterName,
                FILTER_DATA: row.filterData,
                INS_USER: req.session.userId
            })
        }
        return res.status(200).json('success')
    } catch (error) {
        next(AppError.internal())
    }
}

module.exports = {
    getExpenseFilter,
    createOrUpdateExpenseFilter
}
