const saveRoleReport = async (req, res, next) => {
    try {
        const row = req.body.layout
        let cardId

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        for await (const item of row) {
            if (item.id != '') {
                await models.ROLE_REPORT.update(
                    {
                        DIMENSIONS: item.dimensions
                    },
                    {
                        where: {
                            REC_ID: item.id
                        }
                    }
                )
            } else {
                cardId = await models.ROLE_REPORT.create({
                    ROLE_ID: req.session.roleId,
                    REPORT_ID: item.reportId
                })
                cardId = cardId.get('REC_ID')
                item.dimensions.i = cardId

                await models.ROLE_REPORT.update(
                    {
                        DIMENSIONS: item.dimensions
                    },
                    {
                        where: {
                            REC_ID: cardId
                        }
                    }
                )
            }
        }

        return res.status(200).json({
            message: 'Success',
            cardId
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const deleteRoleReport = async (req, res, next) => {
    try {
        const id = req.params.id

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;
        
        const isExist = await models.ROLE_REPORT.count({
            where: {
                REC_ID: id
            }
        })

        if (isExist == 0) {
            next(AppError.badRequest())
            return
        }

        await models.ROLE_REPORT.destroy({
            where: {
                REC_ID: id
            }
        })

        return res.status(200).json({
            message: 'Deleted successfully'
        })
    } catch (error) {
        next(AppError.internal())
    }
}

module.exports = {
    saveRoleReport,
    deleteRoleReport
}
