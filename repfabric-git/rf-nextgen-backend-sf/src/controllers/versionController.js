const { sequelize } = require('../models')
const logger = require('../utils/logger')

const getVersion = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    try {
        const [result] = await sequelize.query(
            `select Concat('Version',' ',VERSION) as version from VERSION_SETTINGS limit 1`
        )

        return res.status(200).json(result[0].version)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

module.exports = {
    getVersion
}
