const dayjs = require('dayjs')
const { sequelize } = require('../../models')

const getGoals = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let months = {
            '01': 'Jan',
            '02': 'Feb',
            '03': 'Mar',
            '04': 'Apr',
            '05': 'May',
            '06': 'Jun',
            '07': 'Jul',
            '08': 'Aug',
            '09': 'Sep',
            10: 'Oct',
            11: 'Nov',
            12: 'Dec'
        }

        const row = req.query
        row.userId = req.session.userId
        let query = `
            SELECT
            GH.GOAL_ID as goalId,
            GR.GOAL_ID as grGoalId,
            GH.GOAL_NAME as goalName,
            GH.GOAL_DESCRIPTION as goalDescription,
            GH.GOAL_OWNER as goalOwner,
            GH.GOAL_TYPE as goalType,
            GH.GOAL_PARENT_ID AS goalParentId,
            GH1.GOAL_NAME AS goalParent,
            GH.GOAL_CHILD_LEVEL as goalChildLevel,
            GH.GOAL_LINEAGE as goalLineage,
            GH.GOAL_GROUP as goalGroup,
            GH.GOAL_PERIOD as goalPeriod,
            GH.GOAL_PROGRESS_MIN as goalProgressMin,
            GH.GOAL_PROGRESS_MAX as goalProgressMax,
            GH.GOAL_ORIENTATION as goalOrientation,
            GH.GOAL_RUNRATE_FLAG as goalRunrateFlag,
            GH.GOAL_UOM as goalUOM,
            GH.GOAL_FILTER_ON as goalFilterOn,
            GH.GOAL_SPLIT_ON as goalSplitOn,
            GH.GOAL_ROLLUP_FLAG as goalRollupFlag,
            GH.GOAL_STATUS as goalStatus,
            GH.GOAL_BASE_GOAL as goalBaseGoal,
            GH.GOAL_HOME_PAGE_FLAG as goalHomePageFlag,
            GR.GOAL_YEAR as goalYear,
            GR.GOAL_PERIOD as grGoalPeriod,
            GR.GOAL_PERIOD_NUM as goalPeriodNum,
            DATE_FORMAT(GR.GOAL_BEG_DATE, "%Y-%m-%d") as goalBegDate,
            DATE_FORMAT(GR.GOAL_END_DATE, "%Y-%m-%d") as goalEndDate,
            GR.GOAL_SET as goalSet,
            GR.GOAL_RESULT as goalResult,
            GR.GOAL_POSITION as goalPosition
            FROM GOALS_HDR GH
            JOIN GOALS_RESULT GR
            ON GH.GOAL_ID = GR.GOAL_ID
            LEFT JOIN GOALS_HDR GH1
            ON GH1.GOAL_ID = GH.GOAL_PARENT_ID
        `

        // const extension = `GROUP BY GR.GOAL_ID, GR.GOAL_PERIOD_NUM ORDER BY GH.GOAL_LINEAGE ASC, GOAL_END_DATE DESC`

        //let whereQuery = await searchFilter1(row, query)

        // if (Object.keys(whereQuery).length > 0) {
        //     query = whereQuery
        // }

        // const sqlQuery = `${query} ${extension}`
        // let [result] = await sequelize.query(sqlQuery)

        const { whereQuery, params } = await searchFilter1(row, query, 1)

        const [result] = await sequelize.query(whereQuery, {
            replacements: params
        })
        if (row.goalPeriod == 3) {
            result.map((item) => {
                item.goalFrequency =
                    months[item.goalPeriodNum.substring(4, 6)] +
                    ' ' +
                    item.goalPeriodNum.substring(0, 4)
            })
        }

        if (row.goalPeriod == 4) {
            result.forEach((item) => {
                item.goalFrequency =
                    months[item.goalBegDate.substring(5, 7)] +
                    ' ' +
                    item.goalBegDate.substring(8, 10) +
                    '-' +
                    months[item.goalEndDate.substring(5, 7)] +
                    ' ' +
                    item.goalEndDate.substring(8, 10)
            })
        }

        return res.status(200).json(result)
    } catch (error) {
        next(AppError.internal(error.message))
    }
}

// const searchFilter = async (row, query) => {
//     let where = {}
//     let field, value

//     if (row.goalPeriod == 3) {
//         where = ` DATE(GR.GOAL_END_DATE) BETWEEN DATE_FORMAT(NOW() ,'%Y-%m-01') - INTERVAL 12 month AND DATE_FORMAT(NOW() ,'%Y-%m-01') + INTERVAL 1 MONTH`
//     }

//     if (row.goalPeriod == 4) {
//         where = `DATE(GR.GOAL_END_DATE) >= DATE_SUB(NOW(), INTERVAL 12 WEEK)`
//     }

//     if (row.goalPeriod != undefined && row.goalPeriod != '') {
//         field = 'GH.GOAL_PERIOD'
//         value = `${row.goalPeriod}`
//         where = await filter(
//             field,
//             value,
//             undefined,
//             undefined,
//             where,
//             'NUMERIC'
//         )
//     }

//     if (row.goalOwner != undefined && row.goalOwner != '') {
//         field = 'GH.GOAL_OWNER'
//         value = `${row.goalOwner}`
//         where = await filter(
//             field,
//             value,
//             undefined,
//             undefined,
//             where,
//             'NUMERIC'
//         )
//     } else {
//         field = 'GH.GOAL_OWNER'
//         value = row.userId
//         where = await filter(
//             field,
//             value,
//             undefined,
//             undefined,
//             where,
//             'NUMERIC'
//         )
//     }
//     query =
//         Object.keys(where).length > 0
//             ? query.concat(` WHERE ${where} AND  GH.GOAL_HOME_PAGE_FLAG = 1`)
//             : ''

//     return query
// }

// const filter = async (field, value, field1, value1, where, type) => {
//     switch (type) {
//         case 'DATE_RANGE':
//             where =
//                 Object.keys(where).length !== 0
//                     ? where +
//                       ' AND ' +
//                       field +
//                       ' between ' +
//                       value +
//                       ' and ' +
//                       value1
//                     : field + ' between ' + value + ' and ' + value1
//             return where
//         case 'NUMERIC':
//             where =
//                 Object.keys(where).length !== 0
//                     ? where + ' AND ' + field + '=' + value
//                     : field + '=' + value
//             return where
//         default:
//             where =
//                 Object.keys(where).length !== 0
//                     ? where + ' AND ' + field + ' like ' + value
//                     : field + ' like ' + value
//             return where
//     }
// }

const refreshGoal = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const row = req.query
        const [data] = await goalData(row, req)

        const ids = data.map((o) => o.goalId)
        const goal = data.filter(
            ({ goalId }, index) => !ids.includes(goalId, index + 1)
        )

        let goalIdParam

        for await (const item of goal) {
            goalIdParam =
                item.goalRollupFlag == 1 ? item.goalParentId : item.goalId
            let currDate
            for (let i = 0; i <= 12; i++) {
                if (row.goalPeriod == 3) {
                    currDate = dayjs(row.currDate)
                        .subtract(i, 'month')
                        .format('YYYY-MM-DD')
                }

                if (row.goalPeriod == 4) {
                    currDate = dayjs(row.currDate)
                        .subtract(i, 'week')
                        .format('YYYY-MM-DD')
                }
                // await sequelize.query(
                //     `CALL CalcGoalResults(${goalIdParam},'${currDate}')`
                // )
                await sequelize.query(
                    'CALL CalcGoalResults(:goalIdParam, :currDate)',
                    {
                        replacements: {
                            goalIdParam: goalIdParam,
                            currDate: currDate
                        }
                    }
                )
            }
        }

        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal(error.message))
    }
}

const goalData = async (row, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    let query = `SELECT
    GH.GOAL_ID as goalId,
    GH.GOAL_NAME as goalName,
    GH.GOAL_OWNER as goalOwner,
    GH.GOAL_TYPE as goalType,
    GH.GOAL_PARENT_ID AS goalParentId,
    GH.GOAL_CHILD_LEVEL as goalChildLevel,
    GH.GOAL_LINEAGE as goalLineage,
    GH.GOAL_GROUP as goalGroup,
    GH.GOAL_PERIOD as goalPeriod,
    GH.GOAL_ROLLUP_FLAG as goalRollupFlag
    FROM GOALS_HDR GH
    JOIN GOALS_RESULT GR
    ON GH.GOAL_ID = GR.GOAL_ID
    `
    let { whereQuery, params } = await searchFilter1(row, query, 0)
    if (Object.keys(whereQuery).length > 0) {
        query = whereQuery
    }
    return sequelize.query(query, {
        replacements: params
    })
}

const searchFilter1 = async (row, baseQuery, type) => {
    let whereClauses = []
    let params = []
    let field, value

    if (row.goalPeriod == 3) {
        whereClauses.push(
            `DATE(GR.GOAL_END_DATE) BETWEEN DATE_FORMAT(NOW(),'%Y-%m-01') - INTERVAL 12 MONTH AND DATE_FORMAT(NOW(),'%Y-%m-01') + INTERVAL 1 MONTH`
        )
    }

    if (row.goalPeriod == 4) {
        whereClauses.push(
            `DATE(GR.GOAL_END_DATE) >= DATE_SUB(NOW(), INTERVAL 12 WEEK)`
        )
    }

    if (row.goalPeriod !== undefined && row.goalPeriod !== '') {
        field = 'GH.GOAL_PERIOD'
        value = row.goalPeriod
        const { clause, param } = await filter1(field, value, 'NUMERIC')
        whereClauses.push(clause)
        params.push(param)
    }

    if (row.goalOwner !== undefined && row.goalOwner !== '') {
        field = 'GH.GOAL_OWNER'
        value = row.goalOwner
        const { clause, param } = await filter1(field, value, 'NUMERIC')
        whereClauses.push(clause)
        params.push(param)
    } else {
        field = 'GH.GOAL_OWNER'
        value = row.userId
        const { clause, param } = await filter1(field, value, 'NUMERIC')
        whereClauses.push(clause)
        params.push(param)
    }

    whereClauses.push('GH.GOAL_HOME_PAGE_FLAG = 1')
    const where =
        whereClauses.length > 0 ? ' WHERE ' + whereClauses.join(' AND ') : ''

    const groupBy = ' GROUP BY GR.GOAL_ID, GR.GOAL_PERIOD_NUM'
    const orderBy = ' ORDER BY GH.GOAL_LINEAGE ASC, GOAL_END_DATE DESC'
    let whereQuery
    if (type === 1) {
        whereQuery = baseQuery.concat(where, groupBy, orderBy)
    } else {
        whereQuery = baseQuery.concat(where)
    }

    return { whereQuery, params }
}

const filter1 = async (field, value, type) => {
    let clause
    let param = value

    switch (type) {
        case 'DATE_RANGE':
            // Assuming value and value1 are passed in the right format
            clause = `${field} BETWEEN ? AND ?`
            param = [value, value] // You will need to handle value1 separately
            break
        case 'NUMERIC':
            clause = `${field} = ?`
            break
        default:
            clause = `${field} LIKE ?`
            param = `%${value}%` // Assuming wildcard search
            break
    }

    return { clause, param }
}

module.exports = {
    getGoals,
    refreshGoal
}
