const { sequelize } = require('../../models')

const linkQuoteToPO = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    try {
        const { id } = req.params
        const row = req.body
        if (row.quoteRecId && row.quoteRecId != undefined) {
            await sequelize.query(`UPDATE PO_HDR SET PO_QUOTE_ID  =${row.quoteRecId} 
        WHERE PO_ID = ${id}`)
        } else {
            return res.status(200).json('Quote record id is required')
        }
        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

module.exports = {
    linkQuoteToPO
}
