const dayjs = require('dayjs')
const utc = require('dayjs/plugin/utc')
const { sequelize } = require('../../models')
const jwt = require('jsonwebtoken')
const logger = require('../../utils/logger')

const getActivityJournal = async (req, res, next) => {
    try {
        const row = req.query
        let order = row.order != undefined ? row.order : 'DESC'

        if (row.fromDate == undefined || !row.fromDate) {
            next(AppError.badRequest())
        }

        if (row.toDate == undefined || !row.toDate) {
            next(AppError.badRequest())
        }

        const formattedStartdate = dayjs(row.fromDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )

        const formattedEdnDate = dayjs(row.toDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )

        let query = `
                SELECT
                ACJD.REC_ID as acjdRecId,
                ACJD.ACJD_HDR_ID as acjdHeaderId,
                ACJD.ACJD_PRINCIPAL as acjdPrincipal,
                DATE_FORMAT(ACJD.ACJD_FOLLOWUP,'%Y-%m-%d') as acjdFollowup,
                ACJD.ACJD_OPP_FLAG as acjdOppFlag,
                ACJD.ACJD_OPP_ID as acjdOppId,
                ACJD.ACJD_COMMENT as acjdComment,
                PRIN.COMP_NAME as principalName,
                CUST.COMP_NAME as customerName,
                ACJH_TITLE as acjhTitle,
                ACJH_COMP_NAME as acjhCompanyName,
                ACJH_CONT_NAME as acjhContactName
                FROM ACTIVITY_JOURNAL_DTL ACJD
                LEFT OUTER JOIN ACTIVITY_JOURNAL_HDR ACJH ON ACJH.ACJH_ID = ACJD.ACJD_HDR_ID
                LEFT OUTER JOIN COMPANIES CUST ON ACJH.ACJH_CUSTOMER = CUST.COMP_ID
                LEFT OUTER JOIN COMPANIES PRIN ON ACJD.ACJD_PRINCIPAL = PRIN.COMP_ID
                WHERE ACJD.ACJD_OPP_FLAG = 0
            `

        let userCheck = await includeUserCheck(
            row.myActivityJournal,
            row.showAll,
            req.session.roleId
        )
        if (userCheck) {
            query = query.concat(`AND ACJH.ACJH_USER = ${req.session.userId} `)
        }
        const customUserParams = await getCustomUserParams(req.session.userId, req)
        if (req.session.roleId != 1 && customUserParams.length == 0) {
            // Use the tenant-specific Sequelize instance
            const sequelize = req.tenantSequelize;

            const sman = await sequelize.query(
                `
                    SELECT
                    GROUP_CONCAT(SGT_SMAN_ID) as id
                    FROM SALES_GROUP_TEAM
                    WHERE SGT_MEMBER_ID = ${req.session.userId}
                `
            )

            if (sman[0].id) {
                query = query.concat(
                    `AND CUST.COMP_SMAN_ID IN(0,${sman[0].id}) `
                )
            } else {
                query = query.concat('AND CUST.COMP_SMAN_ID IN(0, -1) ')
            }
        }

        if (row.showAll == 1) {
            query = query.concat(
                `AND ACJD.ACJD_FOLLOWUP IS NOT NULL ORDER BY ACJD.ACJD_FOLLOWUP ${order}`
            )
        } else {
            query = query.concat(
                `AND ACJD.ACJD_FOLLOWUP BETWEEN '${formattedStartdate}' AND '${formattedEdnDate}' ORDER BY ACJD.ACJD_FOLLOWUP ${order}`
            )
        }

        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const [result] = await sequelize.query(query)
        return res.status(200).json(result)
    } catch (error) {
        next(AppError.internal())
    }
}

const clearActivityJournal = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const id = req.params.id
        let isExistQuery = 'SELECT * FROM ACTIVITY_JOURNAL_DTL WHERE REC_ID = ?'
        const isExists = await sequelize.query(isExistQuery, [id])
        if (isExists.length == 0) {
            next(AppError.notFound('Record Not Found'))
            return
        }

        let query = `UPDATE ACTIVITY_JOURNAL_DTL
        SET ACJD_FOLLOWUP = ?,ACJD_OPP_ID = ?
        WHERE REC_ID = ?
        `
        await sequelize.query(query, [null, 0, id])
        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

const getEvents = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const row = req.query
        if (row.startDate == undefined || !row.startDate) {
            next(AppError.badRequest())
        }

        if (row.endDate == undefined || !row.endDate) {
            next(AppError.badRequest())
        }

        const formattedStartdate = dayjs(row.startDate).format(
            'YYYY-MM-DD HH:mm:ss'
        )

        const formattedEdnDate = dayjs(row.endDate).format(
            'YYYY-MM-DD HH:mm:ss'
        )

        const result = await sequelize.query(`
            SELECT
            EVENT_ID as eventId,
            DATE_FORMAT(EVENT_START_DATE,'%Y-%m-%dT%T%Z') as startDate,
            DATE_FORMAT(EVENT_END_DATE,'%Y-%m-%dT%T%Z') as endDate,
            EVENT_TITLE as title,
            EVENT_TYPE as type,
            EVENT_LOCATION as location
            FROM CALENDAR
            WHERE EVENT_USER_ID = ${req.session.userId}
            AND EVENT_START_DATE <= '${formattedEdnDate}'
            AND EVENT_END_DATE >= '${formattedStartdate}'
            ORDER BY EVENT_START_DATE
        `)

        return res.status(200).json(result)
    } catch (error) {
        next(AppError.internal())
    }
}

const getTasks = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        let query = `
            SELECT
            TASKS.REC_ID as id,
            TASK_USER_ID as userId,
            DATE_FORMAT(TASK_DATE,'%Y-%m-%d') as taskDate,
            TASK_TITLE as title,
            TASK_DESCRIPTION as description,
            TASK_PRIORITY as priority,
            TASK_DONE_FLAG as doneFlag,
            TASK_OPP_ID as oppId,
            TASK_DONE_NOTES as notes,
            TASK_AJH_ID as ajhId,
            TASK_AJD_ID as ajdId,
            TASK_PRINCI_ID as princiId,
            TASK_CUST_ID as custId,
            TASK_EMAIL_FLAG as emailFlag,
            TASK_REMINDER_STATUS as reminderStatus,
            DATE_FORMAT(TASK_REMINDER_DATE,'%Y-%m-%d') as reminderDate,
            TASK_JOB_ID as jobId,
            TASK_QUOTE_ID as quoteId,
            TASK_COMMENTS as comments,
            ACJH.ACJH_TITLE as ajTitle
            FROM TASKS
            LEFT JOIN ACTIVITY_JOURNAL_HDR ACJH
            ON ACJH.ACJH_ID = TASK_AJH_ID
            WHERE (
                TASKS.REC_ID IN(
                    SELECT TASK_ID
                    FROM TASK_ASSIGNEE
                    WHERE TASK_ASSIGNEE = ${req.session.userId}
                ) OR TASK_USER_ID = ${req.session.userId}
                )
            AND (TASK_DONE_FLAG = 2 OR TASK_DONE_FLAG = 0) ORDER BY TASK_DATE DESC
            `
        let result = await sequelize.query(query)
        return res.status(200).json(result)
    } catch (error) {
        next(AppError.internal())
    }
}

const updateTask = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const id = req.params.id
        let isExistQuery = 'SELECT * FROM TASKS WHERE REC_ID = ?'
        const isExists = await sequelize.query(isExistQuery, [id])
        if (isExists.length == 0) {
            next(AppError.notFound('Record Not Found'))
            return
        }
        let query = 'UPDATE TASKS SET TASK_DONE_FLAG = ? WHERE REC_ID = ?'
        await sequelize.query(query, [1, id])
        return res.status(200).json('Updated successfully')
    } catch (error) {
        next(AppError.internal())
    }
}

const getJobs = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const row = req.query
        if (row.fromDate == undefined || !row.fromDate) {
            next(AppError.badRequest())
        }

        if (row.toDate == undefined || !row.toDate) {
            next(AppError.badRequest())
        }

        const formattedStartdate = dayjs(row.fromDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )
        const formattedEdnDate = dayjs(row.toDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )

        const result = await sequelize.query(`
            SELECT
            JOB_ID as jobId,
            JOB_DESCR as jobDescr,
            JOB_TYPE as jobTypeId,
            JOB_SPECIFIER as jobSpecifier,
            JOB_VALUE as jobValue,
            JOB_ACTIVITY as jobActivity,
            JOB_NO as jobNo,
            JOB_URL as jobUrl,
            DATE_FORMAT(JOB_BID_DATE,'%Y-%m-%d') as jobBidDate,
            DATE_FORMAT(JOB_PROD_DATE,'%Y-%m-%d') as jobProdDate,
            JOB_CLOSE_STATUS as jobCloseStatus,
            DATE_FORMAT(JOB_CLOSE_DATE,'%Y-%m-%d') as jobCloseDate,
            JOB_CLOSE_REASON as jobCloseReason,
            JOB_ADDRESS_1 as jobAddress1,
            JOB_CITY as jobCity,
            JOB_STATE as jobState,
            JOB_ZIP_CODE as jobZipCode,
            JOB_COUNTRY_CODE as jobCountryCode,
            JOB_FORMATTED_ADDR as jobFormattedAddr,
            JOB_GEO_LATI as jobGeoLati,
            JOB_GEO_LONGI as jobGeoLongi,
            JOB_SPECIFIER_NAME as jobSpecifierName,
            JOB_TYPE_NAME as jobTypeName,
            JOB_ACTIVITIES_NAME as jobActivitiesName,
            RELATED_COMPANY as relatedCompany,
            JOB_STATUS as jobStatus,
            JOB_CATEGORY as jobCategoryID,
            JOB_CATG_NAME as jobCategory,
            JOB_OWNER as jobOwnerId,
            JOB_OWNER_NAME as jobOwner,
            JOB_EST_VALUE as jobEstValue,
            DATE_FORMAT(JOB_AWARDED_DATE,'%Y-%m-%d') as jobAwardedDate,
            JOB_AWARDED_TO_ID as jobAwardedToId,
            JOB_AWARDED_TO_NAME as jobAwardedTo,
            DATE_FORMAT(JOB_FOLLOW_UP,'%Y-%m-%d') as jobFollowUpDate,
            JOB_OWNER_COMP_ID as jobOwnerCompId,
            JOB_OWNER_COMPANY as jobOwnerCompany,
            JOB_OWNER_CONT_ID as jobOwnerContId,
            JOB_OWNER_CONTACT as jobOwnerContact
            FROM VIEW_JOBS
            WHERE JOB_FOLLOW_UP BETWEEN '${formattedStartdate}' AND '${formattedEdnDate}'
            AND JOB_OWNER = ${req.session.userId}
            AND JOB_CLOSE_STATUS = 0
            GROUP BY JOB_ID
            ORDER BY jobFollowUpDate DESC,
            jobDescr DESC

        `)

        return res.status(200).json(result)
    } catch (error) {
        next(AppError.internal())
    }
}

const getPurchaseOrder = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const row = req.query
        if (row.fromDate == undefined || !row.fromDate) {
            next(AppError.badRequest())
        }

        if (row.toDate == undefined || !row.toDate) {
            next(AppError.badRequest())
        }

        const formattedStartdate = dayjs(row.fromDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )
        const formattedEdnDate = dayjs(row.toDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )

        let query = `
            SELECT
            PO_ID as poId,
            PO_NUMBER as poNumber,
            DATE_FORMAT(PO_DATE,'%Y-%m-%d') as poDate,
            PO_PRINCI_ID as poPrinciId,
            PO_PRINCI_NAME as poPrinci,
            PO_CUST_ID as poCustId,
            PO_CUST_NAME as poCustName,
            PO_DISTRI_ID as poDistriId,
            PO_DISTRI_NAME as poDistriName,
            PO_SMAN_ID as poSmanId,
            PO_SMAN_NAME as poSmanName,
            PO_SECOND_CUST_ID as poSecondCustId,
            PO_SECOND_CUST_NAME as poSecondCustName,
            PO_PROGRAM as poProgram,
            PO_TOTAL_PRICE as poTotalPrice,
            PO_COMM_RATE as poCommRate,
            PO_COMM_PROJECTED as poCommProjected,
            PO_CLOSE_STATUS as poCloseStatus,
            PO_STATUS as poStatus,
            PO_SO_NUMBER as poSoNumber,
            DATE_FORMAT(PO_SO_DATE,'%Y-%m-%d') as poSoDate,
            PO_ATT_COUNT as poAttCount
            FROM VIEW_PO_HDR
            WHERE PO_DATE BETWEEN '${formattedStartdate}' AND '${formattedEdnDate}'
        `

        const customUserParams = await getCustomUserParams(req.session.userId, req)
        if (req.session.roleId != 1 && customUserParams.length == 0) {
            const sman = await sequelize.query(
                `
                    SELECT
                    GROUP_CONCAT(SGT_SMAN_ID) as id
                    FROM SALES_GROUP_TEAM
                    WHERE SGT_MEMBER_ID = ${req.session.userId}
                `
            )

            if (sman[0].id) {
                query = query.concat(`AND PO_SMAN_ID IN(0,${sman[0].id}) `)
            } else {
                query = query.concat('AND PO_SMAN_ID IN(0, -1) ')
            }
        }
        const [result] = await sequelize.query(query)
        return res.status(200).json(result)
    } catch (error) {
        next(AppError.internal())
    }
}

const getQuotes = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const row = req.query
        if (row.fromDate == undefined || !row.fromDate) {
            next(AppError.badRequest())
        }

        if (row.toDate == undefined || !row.toDate) {
            next(AppError.badRequest())
        }

        const formattedStartdate = dayjs(row.fromDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )
        const formattedEdnDate = dayjs(row.toDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )

        let query = `
        SELECT
        DATE_FORMAT(QUOT_FOLLOW_UP,'%Y-%m-%d') as quotFollowUpDate,
        PRINCI_NAME AS principal,
        CUST_NAME AS customer,
        QUOT_CUST_PROGRAM as quotCustProgram,
        QUOT_NUMBER as quotNumber,
        DATE_FORMAT(QUOT_DATE, '%Y-%m-%d') as quotDate,
        QUOT_VALUE as quotValue,
        REC_ID as id
        FROM VIEW_QUOTE_HDR
        WHERE QUOT_FOLLOW_UP BETWEEN '${formattedStartdate}' AND '${formattedEdnDate}'
        `

        let userCheck = await includeUserCheck(
            row.myQuotes,
            0,
            req.session.roleId
        )

        if (userCheck) {
            query = query.concat(`AND QUOT_OWNER = ${req.session.userId} `)
        }

        const customUserParams = await getCustomUserParams(req.session.userId, req)
        if (req.session.roleId != 1 && customUserParams.length == 0) {
            const sman = await sequelize.query(
                `
                    SELECT
                    GROUP_CONCAT(SGT_SMAN_ID) as id
                    FROM SALES_GROUP_TEAM
                    WHERE SGT_MEMBER_ID = ${req.session.userId}
                `
            )

            if (sman[0].id) {
                query = query.concat(`AND COMP_SMAN_ID IN(0,${sman[0].id})`)
            } else {
                query = query.concat('AND COMP_SMAN_ID IN(0, -1)')
            }
        }

        query = query.concat('ORDER BY QUOT_FOLLOW_UP ASC')
        const [result] = await sequelize.query(query)

        return res.status(200).json(result)
    } catch (error) {
        next(AppError.internal())
    }
}

const getSamples = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const row = req.query
        if (row.fromDate == undefined || !row.fromDate) {
            next(AppError.badRequest())
        }

        if (row.toDate == undefined || !row.toDate) {
            next(AppError.badRequest())
        }

        const formattedStartdate = dayjs(row.fromDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )
        const formattedEdnDate = dayjs(row.toDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )

        let query = `
        SELECT
        DATE_FORMAT(S.SAMP_FOLLOW_UP,'%Y-%m-%d') as sampleFollowUpDate,
        C.COMP_NAME as principal,
        C1.COMP_NAME as customer,
        S.SAMP_CUST_PROGRAM as sampleCustProgram,
        S.SAMP_ORD_NUM as sampleOrderNumber,
        DATE_FORMAT(S.SAMP_ORD_DATE, '%Y-%m-%d') as sampleOrdDate,
        S.SAMP_SHIP_COST as sampleShipCost,
        S.REC_ID as id
        FROM SAMPLES_HDR S
        LEFT JOIN COMPANIES C ON C.COMP_ID = S.SAMP_PRINCIPAL
        LEFT JOIN COMPANIES C1 ON C1.COMP_ID = S.SAMP_CUSTOMER
        WHERE SAMP_FOLLOW_UP BETWEEN '${formattedStartdate}' AND '${formattedEdnDate}'
        `

        let userCheck = await includeUserCheck(
            row.mySamples,
            0,
            req.session.roleId
        )

        if (userCheck) {
            query = query.concat(`AND SAMP_OWNER = ${req.session.userId} `)
        }

        const customUserParams = await getCustomUserParams(req.session.userId, req)
        if (req.session.roleId != 1 && customUserParams.length == 0) {
            const sman = await sequelize.query(
                `
                    SELECT
                    GROUP_CONCAT(SGT_SMAN_ID) as id
                    FROM SALES_GROUP_TEAM
                    WHERE SGT_MEMBER_ID = ${req.session.userId}
                `
            )

            if (sman[0].id) {
                query = query.concat(`AND C1.COMP_SMAN_ID IN(0,${sman[0].id})`)
            } else {
                query = query.concat('AND C1.COMP_SMAN_ID IN(0, -1)')
            }
        }

        query = query.concat('ORDER BY SAMP_FOLLOW_UP ASC')
        const [result] = await sequelize.query(query)

        return res.status(200).json(result)
    } catch (error) {
        next(AppError.internal(error.message))
    }
}

const getMessages = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const result = await sequelize.query(`
            SELECT
            REC_ID as id,
            MESS_ID as messageId,
            MESS_SENDER_ID as messSenderId,
            MESS_RECEIVER_ID as messReceiverId,
            DATE_FORMAT(MESS_DATE, '%Y-%m-%dT%T%Z') as messDate,
            MESS_TEXT as messText,
            MESS_FLAG as messFlag,
            MESS_SENDER_STATUS as messSenderStatus,
            MESS_RECEIVER_STATUS as messReceiverStatus
            FROM MESSAGES
            WHERE MESS_RECEIVER_ID = ${req.session.userId}
            AND MESS_RECEIVER_STATUS = 0
            ORDER BY messDate DESC
        `)
        return res.status(200).json(result)
    } catch (error) {
        next(AppError.internal())
    }
}

const markMessageAsTodo = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const row = req.body
        let taskTitle =
            row.description.length > 60
                ? row.description.substring(0, 60).trim()
                : row.description
        dayjs.extend(utc)
        const timeStamps = dayjs().utc().format('YYYY-MM-DD HH:mm:ss')
        const currDate = dayjs(row.taskDate).format('YYYY-MM-DD')

        let query = `INSERT INTO TASKS
        (
            TASK_USER_ID,
            TASK_DATE,
            TASK_TITLE,
            TASK_DESCRIPTION,
            TASK_PRIORITY,
            TASK_DONE_FLAG,
            TASK_OPP_ID,
            INS_USER,
            INS_DATE,
            UPD_USER,
            UPD_DATE
        )
        VALUES(?,?,?,?,?,?,?,?,?,?,?)`

        await sequelize.query(query, [
            req.session.userId,
            currDate,
            taskTitle,
            row.description,
            3,
            0,
            0,
            req.session.userId,
            timeStamps,
            req.session.userId,
            timeStamps
        ])

        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

const replyMessage = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const row = req.body
        dayjs.extend(utc)
        const timeStamps = dayjs().utc().format('YYYY-MM-DD HH:mm:ss')

        const updateMessageQuery = `UPDATE MESSAGES SET MESS_RECEIVER_STATUS = ? WHERE REC_ID = ?`
        await sequelize.query(updateMessageQuery, [1, row.id])
        let query = `INSERT INTO MESSAGES
        (
            MESS_ID,
            MESS_SENDER_ID,
            MESS_RECEIVER_ID,
            MESS_DATE,
            MESS_TEXT,
            MESS_FLAG,
            MESS_SENDER_STATUS,
            MESS_RECEIVER_STATUS
        )
        VALUES(?,?,?,?,?,?,?,?)`

        const data = await sequelize.query(query, [
            row.messageId,
            row.messSenderId,
            req.session.userId,
            timeStamps,
            row.messText,
            0,
            1,
            0
        ])

        const lastInsertedRecordQuery = `SELECT REC_ID as id,
        DATE_FORMAT(MESS_DATE, '%Y-%m-%dT%T%Z') as messDate
        FROM MESSAGES WHERE REC_ID = ?`

        const message = await sequelize.query(lastInsertedRecordQuery, [data.insertId])
        return res.status(200).json({
            message: 'Success',
            data: message
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const readMessage = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const id = req.params.id
        let isExistQuery = 'SELECT * FROM MESSAGES WHERE REC_ID = ?'
        const isExists = await sequelize.query(isExistQuery, [id])
        if (isExists.length == 0) {
            next(AppError.notFound('Record Not Found'))
            return
        }

        let query = `UPDATE MESSAGES SET MESS_RECEIVER_STATUS = ? WHERE REC_ID = ?`

        await sequelize.query(query, [1, id])
        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

const getOpportunities = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const row = req.query

        if (row.fromDate == undefined || !row.fromDate) {
            next(AppError.badRequest())
        }

        if (row.toDate == undefined || !row.toDate) {
            next(AppError.badRequest())
        }

        const formattedStartdate = dayjs(row.fromDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )
        const formattedEdnDate = dayjs(row.toDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )

        let query = `
            SELECT
            OPP_ID as oppId,
            DATE_FORMAT(OPP_DATE,'%Y-%m-%d') as oppDate,
            OPP_SMAN_ID as oppSmanId,
            OPP_PRINCIPAL as oppPrincipal,
            OPP_DISTRI as oppDistri,
            OPP_CUSTOMER as oppCustomer,
            OPP_CUST_TYPE as oppCustType,
            OPP_PRINCIPAL_CONT as oppPrincipalCont,
            OPP_DISTRI_CONT as oppDistriCont,
            OPP_CUSTOMER_CONT as oppCustomerCont,
            OPP_PRIORITY as oppPriority,
            OPP_ACTIVITY as oppActivity,
            OPP_STATUS as oppStatus,
            OPP_NEXT_STEP as oppNextStep,
            DATE_FORMAT(OPP_FOLLOW_UP,'%Y-%m-%d') as oppFollowUp,
            CUST_NAME as customer,
            PRINCI_NAME as principal,
            OPP_POTENTIAL as oppPotential,
            OPP_VALUE as oppValue,
            OPP_CUST_PROGRAM as oppCustProgram,
            OPP_ASSIGNED_TO as oppAssignedTo,
            OPP_EAU as oppEau,
            OPP_COMPETITOR_1 as oppCompetitor1,
            OPP_COMPETITOR_2 as oppCompetitor2,
            DATE_FORMAT(OPP_PROTO_DATE,'%Y-%m-%d') as oppProtoDate,
            DATE_FORMAT(OPP_PROD_DATE,'%Y-%m-%d') as oppProdDate,
            OPP_DESCR as oppDesc,
            OPP_RPT_COMMENTS as oppRptComments,
            OPP_CLOSE_STATUS as oppCloseStatus,
            DATE_FORMAT(OPP_CLOSE_DATE,'%Y-%m-%d') as oppCloseDate,
            OPP_CLOSE_REASON as oppCloseReason,
            OPP_REPORT_FLAG as oppReportFlag,
            OPP_OWNER as oppOwner,
            OPP_DISTRI_SMAN_ID as oppDistriSmanId,
            OPP_ENDUSER_ID as oppEnduserId,
            OPP_TYPE_ID as oppTypeId,
            OPP_LEAD_SRC_ID as oppLeadSrcId,
            OPP_FAILED_REASON as oppFailedReason
            FROM VIEW_OPPORTUNITIES
            WHERE OPP_FOLLOW_UP BETWEEN '${formattedStartdate}' AND '${formattedEdnDate}'
            AND OPP_CLOSE_STATUS = 0
            `

        let userCheck = await includeUserCheck(
            row.myOpportunities,
            0,
            req.session.roleId
        )

        if (userCheck) {
            query = query.concat(`AND OPP_OWNER = ${req.session.userId} `)
        }

        const customUserParams = await getCustomUserParams(req.session.userId, req)
        if (req.session.roleId != 1 && customUserParams.length == 0) {
            const sman = await sequelize.query(
                `
                    SELECT
                    GROUP_CONCAT(SGT_SMAN_ID) as id
                    FROM SALES_GROUP_TEAM
                    WHERE SGT_MEMBER_ID = ${req.session.userId}
                `
            )

            if (sman[0].id) {
                query = query.concat(`AND OPP_SMAN_ID IN(0,${sman[0].id}) `)
            } else {
                query = query.concat('AND OPP_SMAN_ID IN(0, -1) ')
            }
        }

        const privateTeamIds = await privateTeam(req.session.userId, req)

        if (privateTeamIds[0].ptmTeamId) {
            query = query.concat(
                `AND COMP_PRIV_TEAM IN(0,${privateTeamIds[0].ptmTeamId}) `
            )
        }
        query = query.concat('ORDER BY OPP_FOLLOW_UP DESC')

        const [result] = await sequelize.query(query)
        return res.status(200).json(result)
    } catch (error) {
        next(AppError.internal())
    }
}

const includeUserCheck = async (flag, showAll, roleId) => {
    let user = false
    if (flag == 0) {
        user = true
    }

    if (flag == 1 && roleId == 1 && showAll == 1) {
        user = true
    }

    return user
}

// Remove the legacy local privateTeam and replace with a multi-tenant version
const privateTeam = async (userId, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const sql = `SELECT GROUP_CONCAT(IFNULL(PTM_TEAM_ID,0)) as ptmTeamId FROM PRIVATE_TEAM_MEMBERS WHERE PTM_MEMBER_ID = $userId`;
    return sequelize.query(sql, { bind: { userId } });
}

const LMSSO = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    try {
        const userId = req.session.userId
        const [services] =
            await sequelize.query(`select SERVICE_KEY, SERVICE_SECRET, SERVICE_IV from SERVICES
         WHERE SERVICE_ID='LMSSSO' `)

        if (services.length == 0) {
            next(AppError.notFound('Company not found'))
            return
        }

        const [email] = await sequelize.query(
            `SELECT U.USER_NAME, E.USER_EMAIL_LOGIN
             FROM USER_EMAIL_CONFIG E
            JOIN USERS U ON E.USER_EMAIL_USER_ID = U.USER_ID
            WHERE U.USER_ID=$userId`,
            {
                bind: { userId }
            }
        )

        const company = services[0].SERVICE_KEY
        const key = process.env.LMSSOKEY

        const emailId = email[0].USER_EMAIL_LOGIN
        const userName = email[0].USER_NAME

        if (email.length == 0 || emailId == null || emailId == '') {
            next(AppError.notFound('Email configuration is not set'))
            return
        }

        const token = jwt.sign(
            {
                email: emailId,
                firstName: userName
            },
            key,
            { expiresIn: 60 }
        )
        res.redirect(
            'https://repfabric.360learning.com/?company=' +
                company +
                '&jwt=' +
                token
        )
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

module.exports = {
    getActivityJournal,
    getEvents,
    getTasks,
    getJobs,
    getPurchaseOrder,
    getQuotes,
    getSamples,
    getMessages,
    getOpportunities,
    updateTask,
    clearActivityJournal,
    readMessage,
    markMessageAsTodo,
    replyMessage,
    LMSSO
}
