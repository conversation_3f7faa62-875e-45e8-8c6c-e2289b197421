const logger = require('../../utils/logger')

const salesByMonth = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const row = req.query

        let months = [
            {
                month: 'JAN',
                total: 0.0,
                year: row.year,
                prevYear: row.year - 1,
                prevTotal: 0.0
            },
            {
                month: 'FEB',
                total: '0.00',
                year: row.year,
                prevYear: row.year - 1,
                prevTotal: 0.0
            },
            {
                month: 'MAR',
                total: 0.0,
                year: row.year,
                prevYear: row.year - 1,
                prevTotal: 0.0
            },
            {
                month: 'APR',
                total: 0.0,
                year: row.year,
                prevYear: row.year - 1,
                prevTotal: 0.0
            },
            {
                month: 'MAY',
                total: 0.0,
                year: row.year,
                prevYear: row.year - 1,
                prevTotal: 0.0
            },
            {
                month: 'JUN',
                total: 0.0,
                year: row.year,
                prevYear: row.year - 1,
                prevTotal: 0.0
            },
            {
                month: 'JUL',
                total: 0.0,
                year: row.year,
                prevYear: row.year - 1,
                prevTotal: 0.0
            },
            {
                month: 'AUG',
                total: 0.0,
                year: row.year,
                prevYear: row.year - 1,
                prevTotal: 0.0
            },
            {
                month: 'SEP',
                total: 0.0,
                year: row.year,
                prevYear: row.year - 1,
                prevTotal: 0.0
            },
            {
                month: 'OCT',
                total: 0.0,
                year: row.year,
                prevYear: row.year - 1,
                prevTotal: 0.0
            },
            {
                month: 'NOV',
                total: 0.0,
                year: row.year,
                prevYear: row.year - 1,
                prevTotal: 0.0
            },
            {
                month: 'DEC',
                total: 0.0,
                year: row.year,
                prevYear: row.year - 1,
                prevTotal: 0.0
            }
        ]
        let query

        const privateTeam = await sequelize.query(
            `
            SELECT GROUP_CONCAT(IFNULL(PTM_TEAM_ID,0)) as id FROM 
            PRIVATE_TEAM_MEMBERS WHERE PTM_MEMBER_ID = ${req.session.userId}
                `
        )

        let privateVal = privateTeam[0].id != null ? privateTeam[0].id : 0

        query = await SaleabyMonthQuery(
            req.session.roleId,
            req.session.userId,
            privateVal,
            row.saleTeam,
            req
        )

        const result1 = await sequelize.query(query, {
            replacements: { from: row.year - 1 + '01', till: row.year + '12' },
        })

        // Map the SQL query results (result1) to the months array.
        // For each month in the months array, check if there is a matching month in the result1 data (case-insensitive).
        //   - If the data's saleMonth year matches the requested year, set the month's total for the current year.
        //   - If the data's saleMonth exists but is not the current year, set the month's prevTotal for the previous year.
        // This ensures each month object contains both current and previous year totals, as available from the query results.
        months.forEach((month) => {
            result1.forEach((data) => {
                if (month.month && data.month && month.month.toLowerCase() === data.month.toLowerCase()) {
                    if (data.saleMonth && row.year == data.saleMonth.substring(0, 4)) {
                        month.month = data.month;
                        month.total = data.total;
                    } else if (data.saleMonth) {
                        month.month = data.month;
                        month.prevTotal = data.total;
                    }
                }
            });
        })

        return res.status(200).json(months)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

async function SaleabyMonthQuery(roleId, userId, privateVal, saleTeam, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    let query
    query = `SELECT IFNULL(SAL_MONTH,'') as saleMonth, ROUND(IFNULL(sum(SAL_TOTAL_PRICE),0),0) as total, 
    IFNULL(UPPER(DATE_FORMAT(CONCAT((SAL_MONTH),'01'),'%b')),'') as month
    FROM RF_DBD_DB_${tenantId}.SALES_MAIN M 
            left join RF_CRM_DB_${tenantId}.COMPANIES P ON (M.SAL_PRINCI_ID = P.COMP_ID)
            left join RF_CRM_DB_${tenantId}.COMPANIES C ON (M.SAL_CUST_ID = C.COMP_ID)
            left join RF_CRM_DB_${tenantId}.COMPANIES D ON (M.SAL_DISTRI_ID = D.COMP_ID)
    WHERE SAL_MONTH  
     between :from and :till
         and
         P.COMP_PRIV_TEAM in(0,${privateVal}) AND C.COMP_PRIV_TEAM in(0,${privateVal}) AND 
        (ifnull(D.COMP_PRIV_TEAM,0) in(0,${privateVal}))`

    if (roleId == 1) {
        if (saleTeam && saleTeam != 0) {
            query = query.concat(
                `and  SAL_SMAN_ID IN(${saleTeam}) group by SAL_MONTH`
            )
        } else {
            query = query.concat(` group by SAL_MONTH`)
        }
    } else {
        if (saleTeam && saleTeam != 0) {
            query = query.concat(
                ` AND SAL_SMAN_ID IN(${saleTeam})  group by SAL_MONTH`
            )
        } else {
            const sman = await sequelize.query(
                `
                    SELECT
                    GROUP_CONCAT(SGT_SMAN_ID) as id
                    FROM SALES_GROUP_TEAM
                    WHERE SGT_MEMBER_ID = ${userId}
                `
            )

            if (sman[0].id) {
                query = query.concat(
                    ` AND SAL_SMAN_ID IN(0,${sman[0].id})  group by SAL_MONTH`
                )
            } else {
                query = query.concat(
                    ` AND SAL_SMAN_ID IN(0, -1)  group by SAL_MONTH`
                )
            }
        }
    }
    return query
}

const salesTeam = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const result = await sequelize.query(
            `SELECT SMAN_ID, SMAN_NAME FROM SALESMEN_MST
            WHERE CASE WHEN ${req.session.roleId}!=1 THEN SMAN_ID IN (SELECT
                    SGT_SMAN_ID
                    FROM SALES_GROUP_TEAM
                    WHERE SGT_MEMBER_ID = ${req.session.userId}) 
                    ELSE 1 END
                `
        )

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

module.exports = { salesByMonth, salesTeam }
