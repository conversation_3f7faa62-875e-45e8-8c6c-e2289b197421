const logger = require('../../utils/logger')

const salesReports = async (req, res, next) => {
    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let data = []
        let months = [
            {
                REP_DATA_TYPE: 'MONTH',
                REP_DATA_NAME: 'JAN',
                REP_VALUE: '0.00'
            },
            {
                REP_DATA_TYPE: 'MONTH',
                REP_DATA_NAME: 'FEB',
                REP_VALUE: '0.00'
            },
            {
                REP_DATA_TYPE: 'MONTH',
                REP_DATA_NAME: 'MAR',
                REP_VALUE: '0.00'
            },
            {
                REP_DATA_TYPE: 'MONTH',
                REP_DATA_NAME: 'APR',
                REP_VALUE: '0.00'
            },
            {
                REP_DATA_TYPE: 'MONTH',
                REP_DATA_NAME: 'MAY',
                REP_VALUE: '0.00'
            },
            {
                REP_DATA_TYPE: 'MONTH',
                REP_DATA_NAME: 'JUN',
                REP_VALUE: '0.00'
            },
            {
                REP_DATA_TYPE: 'MONTH',
                REP_DATA_NAME: 'JUL',
                REP_VALUE: '0.00'
            },
            {
                REP_DATA_TYPE: 'MONTH',
                REP_DATA_NAME: 'AUG',
                REP_VALUE: '0.00'
            },
            {
                REP_DATA_TYPE: 'MONTH',
                REP_DATA_NAME: 'SEP',
                REP_VALUE: '0.00'
            },
            {
                REP_DATA_TYPE: 'MONTH',
                REP_DATA_NAME: 'OCT',
                REP_VALUE: '0.00'
            },
            {
                REP_DATA_TYPE: 'MONTH',
                REP_DATA_NAME: 'NOV',
                REP_VALUE: '0.00'
            },
            {
                REP_DATA_TYPE: 'MONTH',
                REP_DATA_NAME: 'DEC',
                REP_VALUE: '0.00'
            }
        ]

        const query = `SELECT REP_DATA_TYPE, REP_DATA_NAME, REP_VALUE FROM
        RF_DBD_DB_${tenantId}.SALES_DASHBOARD_REP
        WHERE REP_SESSION_ID = ? and REP_DATA_TYPE = ? order by REP_VALUE desc`

        const result = await sequelize.query(query, [req.session.sessionId, 'MONTH'])

        months.forEach((month) => {
            result.map((item) => {
                if (
                    month.REP_DATA_NAME.toLowerCase() ==
                    item.REP_DATA_NAME.toLowerCase()
                ) {
                    month.REP_VALUE = item.REP_VALUE
                }
            })
        })

        const totalSales = salesReportTotal(months)
        let salesReport = []
        salesReport.push(months, { totalSales })
        data.push({ salesReport })

        const query1 = `SELECT REP_DATA_TYPE, REP_DATA_NAME, REP_VALUE FROM 
        RF_DBD_DB_${tenantId}.SALES_DASHBOARD_REP
        WHERE REP_SESSION_ID = ? and REP_DATA_TYPE = ? order by REP_VALUE DESC LIMIT 10`
        const result1 = await sequelize.query(query1, [
            req.session.sessionId,
            'PRINCI'
        ])

        data.push({ manufactureReport: result1 })

        const query2 = `SELECT REP_DATA_TYPE, REP_DATA_NAME, REP_VALUE FROM 
        RF_DBD_DB_${tenantId}.SALES_DASHBOARD_REP
        WHERE REP_SESSION_ID = ? and REP_DATA_TYPE = ? order by REP_VALUE desc`
        const result2 = await sequelize.query(query2, [
            req.session.sessionId,
            'DIVISION'
        ])

        data.push({ divisionReport: result2 })

        const query3 = `SELECT REP_DATA_TYPE, REP_DATA_NAME, REP_VALUE FROM 
        RF_DBD_DB_${tenantId}.SALES_DASHBOARD_REP
        WHERE REP_SESSION_ID = ? and REP_DATA_TYPE = ? order by REP_VALUE DESC LIMIT 10`
        const result3 = await sequelize.query(query3, [req.session.sessionId, 'CUST'])

        data.push({ customerReport: result3 })
        return data
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getDivision = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        let resultData = []

        const query = `SELECT DISTINCT DR.DIVISION_ID,D.DIVISION_NAME FROM DIVISION_REPS DR JOIN
        DIVISIONS_MST D ON D.DIVISION_ID = DR.DIVISION_ID 
        where case when ${req.session.roleId}=1 then 1 else DR.USER_ID=? end `
        let result = await sequelize.query(query, [req.session.userId])

        for await (let element of result) {
            const query1 = `SELECT U.USER_ID as userId, U.USER_NAME AS userName FROM DIVISION_REPS D JOIN USERS U 
            ON D.USER_ID = U.USER_ID WHERE DIVISION_ID = ?`
            const data = await sequelize.query(query1, [element.DIVISION_ID])
            resultData.push({
                [element.DIVISION_ID]: data,
                name: element.DIVISION_NAME
            })
        }
        return res.status(200).json(resultData)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const callDivisionDashboard = async (req, res, next) => {
    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
        
    try {
        const row = req.body
        let query = `CALL RF_DBD_DB_${tenantId}.DivisionDashboard(?,?,?,?,?,?,?,?)`
        const sessionId = req.session.sessionId
        const year = row.year
        const quarter = row.quarter
        const month = row.month
        const principals = row.principals != undefined ? row.principals : ''
        const customers = row.customers != undefined ? row.customers : ''
        const regions = row.regions != undefined ? row.regions : ''
        const divisions = row.divisions != undefined ? row.divisions : ''

        await sequelize.query(query, [
            sessionId,
            year,
            quarter,
            month,
            principals,
            customers,
            regions,
            divisions
        ])

        await salesReports(req, res, next).then((data) => {
            if (data) {
                return res.status(200).json({
                    data
                })
            } else {
                next(AppError.internal())
            }
        })
    } catch (error) {
        logger.error(error)
        next(AppError.internal(error.message))
    }
}

function salesReportTotal(months) {
    let sum = 0.0
    months.forEach((item) => {
        sum = parseFloat(sum) + parseFloat(item.REP_VALUE)
    })
    return sum.toFixed(2)
}

const reprocessSplit = async (req, res, next) => {
    try {
        // Get tenantId from the request's dbVars for multi-tenant system
        const tenantId = req.dbVars?.tenantId;

        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const { fromDate, principals, customers, division } = req.body

        // Construct the SQL query with placeholders
        let query = `CALL RF_DBD_DB_${tenantId}.ReprocessDivisionSplit(?,?,?,?)`

        // Ensure default values for optional parameters and convert undefined to null
        const date = fromDate
        const params = [
            date,
            principals !== undefined ? principals : null,
            customers !== undefined ? customers : null,
            division !== undefined ? division : null
        ]

        // Execute the query asynchronously
        await sequelize.query(query, params)

        return res.status(200).json('success')
    } catch (error) {
        logger.error(error)
        next(AppError.internal(error.message + ' ' + req.body))
    }
}

module.exports = {
    salesReports,
    getDivision,
    callDivisionDashboard,
    reprocessSplit
}
