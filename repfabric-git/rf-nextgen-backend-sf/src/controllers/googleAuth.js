require('../middleware/passport')
const Token = require('../utils/jwtToken')

const LoginSuccess = async (req, res, next) => {
    try {
        if (!req.user) res.redirect(`${process.env.API_URL}/v1/failure`)

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;    

        models.USER.findOne({
            where: {
                [Op.and]: {
                    USER_LOGIN: req.user.email,
                    USER_AUTH_MODE: 2
                }
            }
        })
            .then((row) => {
                if (row) {
                    Token.jwtToken(
                        req,
                        res,
                        next,
                        req.user.email,
                        req.user.displayName,
                        row.USER_ID,
                        row.USER_AUTH_MODE
                    )
                } else {
                    // Get the request's origin header
                    const origin = req.get('origin');

                    // Parse allowed domain suffixes from ENDS_WITH env variable (comma-separated)
                    const allowedEndsWith = process.env.ENDS_WITH ? process.env.ENDS_WITH.split(',') : [];

                    // Redirect to the origin if it ends with any allowed suffix
                    if (
                        origin &&
                        allowedEndsWith.some(suffix => origin.endsWith(suffix))
                    ) {
                        return res.status(200).redirect(origin);
                    } else {
                        // Fallback: redirect to ALLOWED_ORIGINS if origin is not allowed
                        return res.status(200).redirect(process.env.ALLOWED_ORIGINS);
                    }
                }
            })
            .catch(() => {
                next(AppError.internal())
            })
    } catch (error) {
        next(AppError.internal())
    }
}
const LoginFailure = async (req, res, next) => {
    try {
        // Get the request's origin header
        const origin = req.get('origin');

        // Parse allowed domain suffixes from ENDS_WITH env variable (comma-separated)
        const allowedEndsWith = process.env.ENDS_WITH ? process.env.ENDS_WITH.split(',') : [];
        
        // Redirect to the origin if it ends with any allowed suffix
        if (
            origin &&
            allowedEndsWith.some(suffix => origin.endsWith(suffix))
        ) {
            return res.status(200).redirect(origin);
        } else {
            // Fallback: redirect to ALLOWED_ORIGINS if origin is not allowed
            return res.status(200).redirect(process.env.ALLOWED_ORIGINS);
        }
    } catch (error) {
        next(AppError.internal())
    }
}
module.exports = {
    LoginSuccess,
    LoginFailure
}
