const { sequelize } = require('../../models')
const upload = require('../../utils/uploadImage')
const { s3 } = require('../../helpers/index')
const singleUpload = upload.array('image')
const dayjs = require('dayjs')
const logger = require('../../utils/logger')
const axios = require('axios')
const utc = require('dayjs/plugin/utc')
const moment = require("moment");
dayjs.extend(utc)

const addExpense = async (req, res, next) => {
    let transaction = null

    try {
        const row = req.body
        let exp_id = 0

        //Generate EXP_ID
        exp_id = await GenerateId('EXPENSE', req)

        row.expId = exp_id
        //Converting Date format
        const formattedStartdate = dayjs
            .utc(row.expStartDate)
            .format('YYYY-MM-DD')
        const formattedEdnDate = dayjs.utc(row.expEndDate).format('YYYY-MM-DD')

        // Use the tenant-specific Sequelize instance for transaction
        const sequelize = req.tenantSequelize;
        transaction = await sequelize.transaction()

        // Create/Update and Return Merchant ID
        row.expMerchantId = await getMerchantId(
            row,
            req.session.userId,
            transaction,
            req
        )

        //Create Expense
        const details = {
            EXP_ID: row.expId,
            EXP_REPORT_ID: row.expReportId,
            EXP_REF_NO: row.expRefNo,
            EXP_AMOUNT: row.expAmount,
            EXP_CATEGORY_ID: row.expCategoryId,
            EXP_CREATED_FOR: row.expCreatedForId,
            EXP_DESCRIPTION: row.expDescription,
            EXP_START_DATE: formattedStartdate,
            EXP_END_DATE: formattedEdnDate,
            EXP_NIGHTS: row.expNights,
            EXP_FROM_PLACE: row.expFromPlace,
            EXP_TO_PLACE: row.expToPlace,
            EXP_MILES: row.expMiles,
            EXP_RATE: row.expRate,
            EXP_TRAVEL_MODE: row.expTravelMode,
            EXP_REIMBURSABLE: row.expReimbursable,
            INS_USER: req.session.userId,
            UPD_USER: req.session.userId,
            EXP_MERCHANT_ID: row.expMerchantId ? row.expMerchantId : null,
            EXP_TAX: row.expTax,
            EXP_TAX1: row.expTax1
        }

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.EXPENSE.create(details, { transaction })
        // Add Attendees
        await addParticipant(row, transaction, req)
        //Add Companies
        await addCompanyId(row, req.session.userId, req)

        await transaction.commit()

        return res.status(200).json(row.expId)
    } catch (error) {
        if (transaction) {
            await transaction.rollback()
        }
        logger.error(error)
        next(AppError.internal())
    }
}

const getExpenses = async (req, res, next) => {
    try {
        let { createType, expReportId, fromDate, toDate, acjhId, expId } = req.query;

        createType = createType ? createType : 'all';

        expReportId = expReportId ? expReportId : 0;

        if (acjhId != undefined) {
            createType = 'jsf';
        }

        // const result = await getExpenseList(
        //     createType,
        //     expReportId,
        //     fromDate,
        //     toDate,
        //     acjhId,
        //     expId,
        //     req
        // )
        const result = await getExpenseList1(
            createType,
            expReportId,
            fromDate,
            toDate,
            acjhId,
            expId,
            req
        );

        if (result) {
            if (result.length) {
                return res.status(200).json(result);
            } else {
                next(AppError.notFound());
            }
        } else {
            logger.error('Result is falsy');
            next(AppError.notFound());
        }
    } catch (error) {
        logger.error(error);
        next(AppError.internal());
    }
}

async function getExpswitchCase(createType, userId) {
    let defaultVal
    let field, field1
    let value
    let where = {}

    switch (createType) {
        case 'self':
            field = 'E.INS_USER'
            value = `${userId}`
            where =
                Object.keys(where).length !== 0
                    ? where + ' AND '
                    : field + '=' + value
            break
        case 'other':
            field = 'E.EXP_CREATED_FOR'
            field1 = 'E.INS_USER'
            value = `${userId}`
            where =
                Object.keys(where).length !== 0
                    ? where +
                      ' AND ' +
                      field +
                      ' = ' +
                      value +
                      ' AND ' +
                      field1 +
                      ' != ' +
                      value
                    : field + ' = ' + value + ' AND ' + field1 + ' != ' + value
            break
        case 'all':
            field = 'E.INS_USER'
            field1 = 'E.EXP_CREATED_FOR'
            value = `${userId}`
            where =
                '(' + field + '=' + value + ' OR ' + field1 + '=' + value + ')'
            break
        case 'jsf':
            break
        default:
            return defaultVal
    }
    return where
}

async function getExpswitchCase1(createType, userId) {
    let where = '';
    let replacements = {};

    switch (createType) {
        case 'self':
            where = 'E.INS_USER = :userId';
            replacements.userId = userId;
            break;

        case 'other':
            where = 'E.EXP_CREATED_FOR = :userId AND E.INS_USER != :userId';
            replacements.userId = userId;
            break;

        case 'all':
            where = '(E.INS_USER = :userId OR E.EXP_CREATED_FOR = :userId)';
            replacements.userId = userId;
            break;

        case 'jsf':
            // Handle this case as needed
            break;

        default:
            return { where: '', replacements: {} };
    }
    return { where, replacements };
}

async function dateCondition1(fromDate, toDate, where) {
    let replacements = {};

    if (fromDate && toDate) {
        const formattedStartdate = dayjs(fromDate, 'MM-DD-YYYY').format('YYYY-MM-DD');
        const formattedEndDate = dayjs(toDate, 'MM-DD-YYYY').format('YYYY-MM-DD');

        where += where
            ? ' AND (E.EXP_START_DATE BETWEEN :startDate AND :endDate OR E.EXP_END_DATE BETWEEN :startDate AND :endDate)'
            : '(E.EXP_START_DATE BETWEEN :startDate AND :endDate OR E.EXP_END_DATE BETWEEN :startDate AND :endDate)';

        replacements.startDate = formattedStartdate;
        replacements.endDate = formattedEndDate;
    }
    return { where, replacements };
}

async function dateCondition(fromDate, toDate, where) {
    let field, field1
    let value, value1

    if (fromDate != '' && fromDate != undefined) {
        const formattedStartdate = dayjs(fromDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )

        const formattedEdnDate = dayjs(toDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )

        field = 'E.EXP_START_DATE'
        field1 = 'E.EXP_END_DATE'

        value = `${formattedStartdate}`
        value1 = `${formattedEdnDate}`
        where =
            Object.keys(where).length !== 0
                ? where +
                  ' AND (' +
                  field +
                  ' between ' +
                  `'` +
                  value +
                  `'` +
                  ' AND ' +
                  `'` +
                  value1 +
                  `'` +
                  ' or ' +
                  field1 +
                  ' between ' +
                  `'` +
                  value +
                  `'` +
                  ' AND ' +
                  `'` +
                  value1 +
                  `'` +
                  ')'
                : ' (' +
                  field +
                  ' between ' +
                  `'` +
                  value +
                  `'` +
                  ' AND ' +
                  `'` +
                  value1 +
                  `'` +
                  ' or ' +
                  field1 +
                  ' between ' +
                  `'` +
                  value +
                  `'` +
                  ' AND ' +
                  `'` +
                  value1 +
                  `'` +
                  ')'
    }
    return where
}

async function getExpenseList(
    createType,
    expReportId,
    fromDate,
    toDate,
    acjhId,
    expId,
    req
) {
    let where = {}
    const userId = req.session.userId
    let field
    let value

    where = await getExpswitchCase(createType, userId)

    where = Object.values(where).length > 0 ? where + ' AND ' + 1 : 1
    
    // Fix: Only reset where to 1 if expReportId is explicitly provided and not 0
    if (expReportId && expReportId != 0 && expReportId !== undefined) {
        where = 1
    }
    
    if (expId) {
        field = 'E.EXP_ID'
        value = `${expId}`
        where =
            Object.keys(where).length !== 0
                ? where + ' AND ' + field + '=' + value
                : field + '=' + value
    }
    where = await dateCondition(fromDate, toDate, where)

    let query = ` SELECT  E.REC_ID as id,
    E.EXP_ID as expId,
    E.EXP_REPORT_ID as expReportId,
    E.EXP_REF_NO as expRefNo,
    E.EXP_AMOUNT as expAmount,
    E.EXP_TAX as expTax,
    E.EXP_TAX1 as expTax1,
    E.EXP_DESCRIPTION as expDescription,
    DATE_FORMAT(E.EXP_START_DATE,'%m-%d-%Y') as expStartDate,
    DATE_FORMAT(E.EXP_END_DATE,'%m-%d-%Y') as expEndDate,
    E.EXP_NIGHTS as expNights,
    E.EXP_FROM_PLACE as expFromPlace,
    E.EXP_TO_PLACE as expToPlace,
    E.EXP_MILES as expMiles,
    E.EXP_RATE as expRate,
    E.EXP_TRAVEL_MODE as expTravelMode,
    E.EXP_MERCHANT_ID as expMerchantId,
    EXP_REIMBURSABLE as expReimbursable,
    U.USER_NAME AS expCreatedBy ,
    UR.USER_NAME AS expCreatedFor,
    C.CATEGORY_NAME as expCategory,
    E.INS_USER as expInsUser,
    E.EXP_CREATED_FOR as expCreatedForId,
    E.EXP_CATEGORY_ID as expCategoryId,
    IFNULL(EM.NAME,'') as expMerchant,
    IFNULL(ER.EXP_STATUS,0) AS expStatus,
    C.CATEGORY_BASE_ID as expCategoryBaseId
    from EXPENSES E
    JOIN USERS U on U.USER_ID  = E.INS_USER
    JOIN USERS UR on UR.USER_ID  = E.EXP_CREATED_FOR
    JOIN EXPENSE_CATEGORIES C ON C.REC_ID = E.EXP_CATEGORY_ID
    LEFT JOIN EXP_MERCHANTS EM ON EM.REC_ID = E.EXP_MERCHANT_ID
    LEFT JOIN EXPENSE_REPORTS ER ON ER.EXP_REPORT_ID = E.EXP_REPORT_ID`

    if (acjhId) {
        query = query.concat(
            ' ',
            `LEFT JOIN EXP_LINKED_DOCS D ON D.EXP_ID = E.EXP_ID `
        )

        field = 'D.EXP_DOC_ID'
        value = `${acjhId}`
        where =
            Object.keys(where).length !== 0
                ? where + ' AND ' + field + ' = ' + value
                : field + '=' + value
    } else {
        // Fix: Only add EXP_REPORT_ID condition if expReportId is valid
        if (expReportId !== undefined && expReportId !== null) {
            field = 'E.EXP_REPORT_ID'
            value = `${expReportId}`
            where =
                Object.keys(where).length !== 0
                    ? where + ' AND ' + field + ' = ' + value
                    : field + '=' + value
        }
    }

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let [data] = await sequelize.query(
        query +
            ` WHERE ${where}
        ORDER BY E.REC_ID DESC
        `
    )

    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    return Promise.all(
        data.map(async (item) => {
            const expenseId = item.expId
            const contacts = await expenseAttendees(expenseId, req)

            const [companies] = await sequelize.query(`select
            C.COMP_NAME as companyName,
            EC.COMP_ID as companyId
            from
            EXP_COMPANIES EC
            join COMPANIES C on EC.COMP_ID = C.COMP_ID
            where EXP_ID = ${item.expId}`)

            const [linkedDocs] = await sequelize.query(`select
            ELD.EXP_DOC_ID as id,
            AJ.ACJH_TITLE as title,
            AJ.ACJH_COMP_NAME as companyName
            from
            ACTIVITY_JOURNAL_HDR AJ
            join EXP_LINKED_DOCS ELD on AJ.ACJH_ID = ELD.EXP_DOC_ID
            where EXP_ID = ${item.expId}`)

            const attachments = await models.EXPENSE_ATTACHMENT.count({
                where: {
                    EXP_ID: item.expId
                }
            })

            return {
                ...item,
                expContacts: contacts,
                expCompanies: companies,
                expLinkedDocs: linkedDocs,
                expAttchaments: attachments
            }
        })
    )
}

async function getExpenseList1(
    createType,
    expReportId,
    fromDate,
    toDate,
    acjhId,
    expId,
    req
) {
    let where = '',
        replacements = {};
    const userId = req.session.userId;
    let field;
    let value;

    let { where: switchCaseWhere, replacements: switchCaseReplacements } =
        await getExpswitchCase1(createType, userId);

    if (switchCaseWhere) {
        where += switchCaseWhere;
    }
    if (expReportId && expReportId != 0) {
        where = '';
    }
    if (expId) {
        field = 'E.EXP_ID';
        value = `${expId}`;
        where += where ? ' AND ' + field + '= :value' : field + '= :value';
        replacements.value = value;
    }

    let { where: dateConditionWhere, replacements: dateConditionReplacements } =
        await dateCondition1(fromDate, toDate, '');

    if (dateConditionWhere) {
        where += (dateConditionWhere ? ' AND ' : '') + dateConditionWhere;
    }

    replacements = {
        ...replacements,
        ...switchCaseReplacements,
        ...dateConditionReplacements
    };

    let query = `
        SELECT 
            E.REC_ID as id,
            E.EXP_ID as expId,
            E.EXP_REPORT_ID as expReportId,
            E.EXP_REF_NO as expRefNo,
            E.EXP_AMOUNT as expAmount,
            E.EXP_TAX as expTax,
            E.EXP_TAX1 as expTax1,
            E.EXP_DESCRIPTION as expDescription,
            DATE_FORMAT(E.EXP_START_DATE, '%m-%d-%Y') as expStartDate,
            DATE_FORMAT(E.EXP_END_DATE, '%m-%d-%Y') as expEndDate,
            E.EXP_NIGHTS as expNights,
            E.EXP_FROM_PLACE as expFromPlace,
            E.EXP_TO_PLACE as expToPlace,
            E.EXP_MILES as expMiles,
            E.EXP_RATE as expRate,
            E.EXP_TRAVEL_MODE as expTravelMode,
            E.EXP_MERCHANT_ID as expMerchantId,
            E.EXP_REIMBURSABLE as expReimbursable,
            U.USER_NAME AS expCreatedBy,
            UR.USER_NAME AS expCreatedFor,
            C.CATEGORY_NAME as expCategory,
            E.INS_USER as expInsUser,
            E.EXP_CREATED_FOR as expCreatedForId,
            E.EXP_CATEGORY_ID as expCategoryId,
            IFNULL(EM.NAME, '') as expMerchant,
            IFNULL(ER.EXP_STATUS, 0) AS expStatus,
            C.CATEGORY_BASE_ID as expCategoryBaseId
        FROM EXPENSES E
        JOIN USERS U on U.USER_ID = E.INS_USER
        JOIN USERS UR on UR.USER_ID = E.EXP_CREATED_FOR
        JOIN EXPENSE_CATEGORIES C ON C.REC_ID = E.EXP_CATEGORY_ID
        LEFT JOIN EXP_MERCHANTS EM ON EM.REC_ID = E.EXP_MERCHANT_ID
        LEFT JOIN EXPENSE_REPORTS ER ON ER.EXP_REPORT_ID = E.EXP_REPORT_ID`;

    if (acjhId) {
        query += ` LEFT JOIN EXP_LINKED_DOCS D ON D.EXP_ID = E.EXP_ID`;
        replacements.fieldValue = acjhId;
        where += where
            ? ' AND D.EXP_DOC_ID = :fieldValue'
            : 'D.EXP_DOC_ID = :fieldValue';
    } else {
        replacements.fieldValue = expReportId;
        where += where
            ? ' AND E.EXP_REPORT_ID = :fieldValue'
            : 'E.EXP_REPORT_ID = :fieldValue';
    }

    query += ` WHERE ${where} ORDER BY E.REC_ID DESC`;

    const sequelize = req.tenantSequelize;

    let data = await sequelize.query(query, {
        replacements,
        type: sequelize.QueryTypes.SELECT
    });

    const result = await Promise.all(
        data.map(async (item) => {
            const expenseId = item.expId;
            
            const contacts = await expenseAttendees(expenseId, req);
            
            const companies = await sequelize.query(
                `SELECT C.COMP_NAME as companyName, EC.COMP_ID as companyId
                 FROM EXP_COMPANIES EC
                 JOIN COMPANIES C ON EC.COMP_ID = C.COMP_ID
                 WHERE EXP_ID = :expenseId`,
                {
                    replacements: { expenseId },
                    type: sequelize.QueryTypes.SELECT
                }
            );
            
            const linkedDocs = await sequelize.query(
                `SELECT ELD.EXP_DOC_ID as id, AJ.ACJH_TITLE as title, AJ.ACJH_COMP_NAME as companyName
                 FROM ACTIVITY_JOURNAL_HDR AJ
                 JOIN EXP_LINKED_DOCS ELD ON AJ.ACJH_ID = ELD.EXP_DOC_ID
                 WHERE EXP_ID = :expenseId`,
                {
                    replacements: { expenseId },
                    type: sequelize.QueryTypes.SELECT
                }
            );
            
            const attachments = await req.tenantModels.EXPENSE_ATTACHMENT.count({
                where: { EXP_ID: expenseId }
            });

            return {
                ...item,
                expContacts: contacts,
                expCompanies: companies,
                expLinkedDocs: linkedDocs,
                expAttachments: attachments
            };
        })
    );
    return result;
}

const expenseCategory = async (req, res, next) => {
    try {
        const { excludeHiddenCategories, baseCategoryId } = req.query
        const whereCondition = {}
        if (excludeHiddenCategories && !baseCategoryId) {
            whereCondition.CATEGORY_HIDE_FLAG = 0
        }

        if (baseCategoryId) {
            whereCondition[Op.or] = [
                { CATEGORY_HIDE_FLAG: 0 },
                { CATEGORY_HIDE_FLAG: 1, CATEGORY_BASE_ID: baseCategoryId }
            ]
        }

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const data = await models.EXPENSE_CATEGORY.findAll({
            attributes: [
                'REC_ID',
                'CATEGORY_NAME',
                'CATEGORY_AJLINK_FLAG',
                'CATEGORY_AJLINK_AMT',
                'CATEGORY_DEFAULT_FLAG',
                'CATEGORY_BASE_ID',
                'CATEGORY_RECEIPT_REQ_FLAG',
                'CATEGORY_HIDE_FLAG'
            ],
            where: whereCondition,
            order: [['CATEGORY_NAME', 'ASC']]
        })
        return res.status(200).json(data)
    } catch (error) {
        next(AppError.internal())
    }
}

const expenseReportNumber = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const data = await models.EXPENSE_REPORT.findAll({
            attributes: [
                ['REC_ID', 'id'],
                ['EXP_REPORT_ID', 'expReportId'],
                ['EXP_DESCRIPTION', 'expDescription']
            ],
            where: {
                [Op.and]: {
                    EXP_CREATED_FOR: req.params.id,
                    EXP_STATUS: { [Op.in]: [0, 3] }
                },
                [Op.or]: [
                    { EXP_CREATED_FOR: { [Op.eq]: req.session.userId } },
                    { INS_USER: { [Op.eq]: req.session.userId } }
                ]
            }
        })
        return res.status(200).json(data)
    } catch (error) {
        next(AppError.internal())
    }
}

const getMerchantId = async (row, userId, transaction, req) => {
    let merchant

    if (row.expMerchant !== undefined) {
        if (row.expMerchant == '' || row.expMerchant == null) {
            return 0
        }

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const isExistMerchant = await models.EXP_MERCHANT.findOne({
            attributes: ['REC_ID'],
            where: {
                [Op.and]: {
                    INS_USER: userId,
                    NAME: row.expMerchant.trim()
                }
            }
        })
        if (isExistMerchant) {
            return isExistMerchant.dataValues.REC_ID
        } else {
            merchant = await models.EXP_MERCHANT.create(
                {
                    NAME: row.expMerchant,
                    INS_USER: userId
                },
                { transaction }
            )
            return merchant.dataValues.REC_ID
        }
    }
}

const addCompanyId = async (row, userId, req) => {
    let allCompanies = []
    if (row.expCompanies && row.expCompanies.length != 0) {
        const ids = row.expCompanies.map((o) => o.companyId)
        const filtered = row.expCompanies.filter(
            ({ companyId }, index) => !ids.includes(companyId, index + 1)
        )
        row.expCompanies = filtered

        for await (const item of row.expCompanies) {
            // row.expCompanies.map((item) => {
            const companies = {
                EXP_ID: row.expId,
                COMP_ID: item.companyId,
                INS_USER: userId
            }
            allCompanies.push(companies)
        }
        //)

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const isExistCompanies = await models.EXP_COMPANY.findOne({
            where: {
                EXP_ID: row.expId
            }
        })

        if (isExistCompanies) {
            await models.EXP_COMPANY.destroy({
                where: {
                    EXP_ID: row.expId
                }
            })
        }

        await models.EXP_COMPANY.bulkCreate(allCompanies)
    } else {
        if (row.expCompanies) {
            await models.EXP_COMPANY.destroy({
                where: {
                    EXP_ID: row.expId
                }
            })
        }
    }
    return true
}

const addParticipant = async (row, transaction, req) => {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    let allParticipants = []

    if (row.expContacts && row.expContacts.length != 0) {
        await Promise.all(
            row.expContacts.map(async (item) => {
                const participants = {
                    EXP_ID: row.expId,
                    EXP_CONTACT_ID: item.contactId,
                    EXP_CONTACT_NAME: item.contactName
                }

                allParticipants.push(participants)
            })
        )

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const isExistParticipants = await models.EXP_ATTENDEES.findOne({
            where: {
                EXP_ID: row.expId
            }
        })

        if (isExistParticipants) {
            await models.EXP_ATTENDEES.destroy(
                {
                    where: {
                        EXP_ID: row.expId
                    }
                },
                { transaction }
            )
        }

        await models.EXP_ATTENDEES.bulkCreate(allParticipants, {
            transaction
        })
    } else {
        if (row.expContacts) {
            await models.EXP_ATTENDEES.destroy(
                {
                    where: {
                        EXP_ID: row.expId
                    }
                },
                { transaction }
            )
        }
    }
}

const linkDoc = async (row, transaction, req) => {
    let allLinkedDocs = []
    if (row.expActivityJournal && row.expActivityJournal.length) {
        row.expActivityJournal.map((item) => {
            const linkedDocs = {
                EXP_ID: row.expId,
                EXP_DOC_TYPE: item.expDocType,
                EXP_DOC_ID: item.expActivityJournalId
            }
            allLinkedDocs.push(linkedDocs)
        })

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const isExistLinkedDocs = await models.EXPENSE_LINKED_DOCUMENT.findOne({
            where: {
                EXP_ID: row.expId
            }
        })

        if (isExistLinkedDocs) {
            await models.EXPENSE_LINKED_DOCUMENT.destroy(
                {
                    where: {
                        EXP_ID: row.expId
                    }
                },
                { transaction }
            )
        }
        await models.EXPENSE_LINKED_DOCUMENT.bulkCreate(allLinkedDocs, {
            transaction
        })
    }
}

const updateExpense = async (req, res, next) => {
    let transaction = null

    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    try {
        const expenseArray = req.body

        if (!expenseArray[0].id) {
            return res.status(500).json('Invalid Data')
        }

        // Use the tenant-specific Sequelize instance for transaction
        const sequelize = req.tenantSequelize;
        transaction = await sequelize.transaction()

        await Promise.all(
            expenseArray.map(async (row) => {
                const formattedStartdate = dayjs
                    .utc(row.expStartDate, 'MM-DD-YYYY')
                    .format('YYYY-MM-DD')

                const formattedEdnDate = dayjs
                    .utc(row.expEndDate, 'MM-DD-YYYY')
                    .format('YYYY-MM-DD')

                row.expMerchantId = await getMerchantId(
                    row,
                    req.session.userId,
                    transaction,
                    req
                )

                const expense = await models.EXPENSE.update(
                    {
                        EXP_REF_NO: row.expRefNo,
                        EXP_AMOUNT: row.expAmount,
                        EXP_CATEGORY_ID: row.expCategoryId,
                        EXP_CREATED_FOR: row.expCreatedForId,
                        EXP_DESCRIPTION: row.expDescription,
                        EXP_START_DATE: formattedStartdate,
                        EXP_END_DATE: formattedEdnDate,
                        EXP_NIGHTS: row.expNights,
                        EXP_FROM_PLACE: row.expFromPlace,
                        EXP_TO_PLACE: row.expToPlace,
                        EXP_MILES: row.expMiles,
                        EXP_TRAVEL_MODE: row.expTravelMode,
                        EXP_MERCHANT_ID: row.expMerchantId,
                        EXP_REIMBURSABLE: row.expReimbursable,
                        UPD_USER: req.session.userId,
                        EXP_TAX: row.expTax,
                        EXP_TAX1: row.expTax1
                    },
                    {
                        transaction,
                        where: {
                            REC_ID: row.id
                        }
                    }
                )

                if (expense) {
                    //add Participant
                    await addParticipant(row, transaction, req)
                    //Add Company
                    await addCompanyId(row, req.session.userId, req)
                    // Link Activity Journal
                    await linkDoc(row, transaction, req)
                }
            })
        ).then(async () => {
            await transaction.commit()
        })
        return res.status(200).json('Success')
    } catch (error) {
        if (transaction) {
            await transaction.rollback()
        }
        logger.error(error)
        next(AppError.internal())
    }
}

const deleteExpense = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const { expenseIds, expUnlink } = req.body
        const ids = await models.EXPENSE.findAll({
            attributes: ['EXP_ID'],
            where: {
                REC_ID: expenseIds
            },
            raw: true
        })

        ids.map(async (item) => {
            if (expUnlink && expUnlink == 1) {
                await models.EXPENSE.update(
                    {
                        EXP_REPORT_ID: 0
                    },
                    {
                        where: {
                            EXP_ID: item.EXP_ID
                        }
                    }
                )
            } else {
                await models.EXP_ATTENDEES.destroy({
                    where: {
                        EXP_ID: item.EXP_ID
                    }
                })

                await models.EXP_COMPANY.destroy({
                    where: {
                        EXP_ID: item.EXP_ID
                    }
                })

                await models.EXPENSE.destroy({
                    where: {
                        EXP_ID: item.EXP_ID
                    }
                })
            }
        })
        if (ids.length) {
            return res.status(200).json('Success')
        } else {
            next(AppError.notFound())
        }
    } catch (error) {
        next(AppError.internal())
    }
}

const linkToReport = async (req, res, next) => {
    let transaction = null
    const linkArray = req.body

    // Use the tenant-specific Sequelize instance for transaction
    const sequelize = req.tenantSequelize;
    transaction = await sequelize.transaction()

    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    await Promise.all(
        linkArray.map(async (row) => {
            await models.EXPENSE.update(
                {
                    EXP_REPORT_ID: row.expReportId
                },
                {
                    transaction,
                    where: {
                        REC_ID: row.id
                    }
                }
            )
        })
    )
        .then(async () => {
            await transaction.commit()
        })
        .catch(async () => {
            await transaction.rollback()
            next(AppError.internal())
        })
    return res.status(200).json('Success')
}

const getMerchants = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const merchants = await models.EXP_MERCHANT.findAll({
            attributes: [
                ['REC_ID', 'id'],
                ['NAME', 'merchant']
            ],
            order: ['NAME'],
            where: {
                INS_USER: req.session.userId
            }
        })

        return res.status(200).json(merchants)
    } catch (error) {
        next(AppError.internal())
    }
}

const getExpenseCoverage = async (req, res, next) => {
    try {
        const { id } = req.params
        const { type } = req.query
        let coverage

        if (!type) {
            next(AppError.badRequest())
            return
        }

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        //Get Maximum Date of EXP_END_DATE from EXPENSE Table
        if (type === 'expense') {
            coverage = await models.EXPENSE.findOne({
                attributes: [
                    [
                        Sequelize.fn('max', Sequelize.col('EXP_END_DATE')),
                        'fromDate'
                    ]
                ],
                where: {
                    EXP_CREATED_FOR: id
                }
            })
        }
        //Get Maximum Date of EXP_END_DATE from EXPENSE_REPORT Table
        if (type === 'expenseReport') {
            coverage = await models.EXPENSE_REPORT.findOne({
                attributes: [
                    [
                        Sequelize.fn('max', Sequelize.col('EXP_END_DATE')),
                        'fromDate'
                    ]
                ],
                where: {
                    EXP_CREATED_FOR: id
                }
            })
        }
        return res.status(200).json(coverage)
    } catch (error) {
        next(AppError.internal())
    }
}

//S3 Bucket upload
const uploadImage = async (req, res, next) => {
    try {
        singleUpload(req, res, async function (err) {
            if (err) {
                return res.status(501).json({
                    success: false,
                    errors: {
                        title: 'Image Upload Error',
                        detail: err.message,
                        error: err
                    }
                })
            }

            // Use the tenant-specific Sequelize instance
            const sequelize = req.tenantSequelize;

            const getId = await sequelize.query(
                `select count(*) as count from COUNTER where COUNTER_CODE='EXPATTCH' `
            )

            if (getId && getId[0].count === 0) {
                const expAttachId = await sequelize.query(
                    `select max(REC_ID)  + 1 as max_counter from COUNTER`
                )

                let sql = `INSERT INTO COUNTER
                (
                    REC_ID,COUNTER_CODE,COUNTER_COUNT
                )
                VALUES
                (
                    ?, ?, ?
                )`

                await sequelize.query(
                    sql,
                    [expAttachId[0].max_counter, 'EXPATTCH', 0],
                    function (error) {
                        if (error) {
                            next(AppError.internal())
                        }
                    }
                )
            }
            let data = []
            let files = req.files

            if (files == undefined || files.length == 0) {
                next(AppError.internal('Receipt upload failed'))
                return
            }

            await Promise.all(
                files.map(async (row) => {
                    const array = {
                        EXP_ID: req.params.id,
                        EXP_ATTACHMENT: row.key
                    }
                    data.push(array)
                })
            )

            // eslint-disable-next-line
            for await (const item of data) {
                const getIdentity = await sequelize.query(
                    `select GetNextId('EXPATTCH')`
                )
                item.EXP_ATTACHMENT_ID = getIdentity[0]["GetNextId('EXPATTCH')"]
            }

            // Use the tenant-specific models for all DB operations in a multi-tenant environment
            const models = req.tenantModels;

            await models.EXPENSE_ATTACHMENT.bulkCreate(data)

            res.status(200).json('success')
        })
    } catch (error) {
        next(AppError.internal())
    }
}

//Get Image from S3 bucket
const getImage = async (req, res, next) => {
    try {
        let key = req.params.id
        const isDownload = req.query?.download
        if (!key) {
            return ''
        }

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const result = await models.EXPENSE_ATTACHMENT.findAll({
            attributes: ['EXP_ATTACHMENT', 'REC_ID'],
            where: {
                EXP_ID: key
            },
            raw: true
        })
        let imageArray = []

        for await (const item of result) {
            const signedUrl = s3.getSignedUrl('getObject', {
                Key: item.EXP_ATTACHMENT,
                Bucket: process.env.S3_BUCKET,
                Expires: 3900
            })
            imageArray.push({
                id: item.REC_ID,
                imageUrl:
                    signedUrl && isDownload
                        ? await imageUrlToBase64(signedUrl)
                        : signedUrl
            })
        }
        return res.status(200).json(imageArray)
    } catch (error) {
        next(AppError.internal())
    }
}

async function imageUrlToBase64(url) {
    try {
        const response = await axios.get(url, { responseType: 'arraybuffer' })
        const base64 = Buffer.from(response.data).toString('base64')
        const mimeType = response.headers['content-type']
        return `data:${mimeType};base64,${base64}`
    } catch (error) {
        return null
    }
}

const getActivityJournal = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        let { date, createdForId, expId } = req.query;
        if (!date || !createdForId || !expId) {
            next(AppError.badRequest());
            return;
        }

        let ajArray;

        if (date && date !== undefined && date !== '') {
            // Calculate 1 month back from the given date
            const startDate = moment(date).subtract(1, "month").format("YYYY-MM-DD");

            const query = `
                SELECT DISTINCT ACJH_ID AS id, ACJH_TITLE AS title, ACJH_CUSTOMER AS compId,
                       ACJH_COMP_NAME AS companyName, ACJH_CONTACT AS contId, ACJH_CONT_NAME AS contName, ACJH_DATE AS date
                FROM ACTIVITY_JOURNAL_HDR A
                LEFT JOIN EXP_LINKED_DOCS E ON E.EXP_DOC_ID = A.ACJH_ID
                WHERE ((ACJH_DATE BETWEEN ? AND ? AND ACJH_USER = ?)
                       OR E.EXP_ID = ?)
                ORDER BY ACJH_TITLE
            `;

            ajArray = await sequelize.query(query, {
                replacements: [startDate, date, createdForId, expId],
                type: sequelize.QueryTypes.SELECT
            });
        } else {
            const query = `
                SELECT DISTINCT ACJH_ID AS id, ACJH_TITLE AS title, ACJH_CUSTOMER AS compId,
                       ACJH_COMP_NAME AS companyName, ACJH_CONTACT AS contId, ACJH_CONT_NAME AS contName, ACJH_DATE AS date
                FROM ACTIVITY_JOURNAL_HDR A
                JOIN EXP_LINKED_DOCS E ON E.EXP_DOC_ID = A.ACJH_ID
                WHERE E.EXP_ID = ?
                ORDER BY ACJH_TITLE
            `;

            ajArray = await sequelize.query(query, {
                replacements: [expId],
                type: sequelize.QueryTypes.SELECT
            });
        }

        return res.status(200).json(ajArray);
    } catch (error) {
        logger.error(error);
        next(AppError.internal());
    }
};

//Delete image from S3 bucket
const imageDelete = async (req, res, next) => {
    try {
        const id = req.params.id

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const result = await models.EXPENSE_ATTACHMENT.findAll({
            attributes: ['EXP_ATTACHMENT'],
            where: {
                REC_ID: id
            },
            raw: true
        })
        
        const deletePromises = result.map((item) => {
            return new Promise((resolve, reject) => {
                s3.deleteObject(
                    {
                        Bucket: process.env.S3_BUCKET,
                        Key: item.EXP_ATTACHMENT
                    },
                    function (err, data) {
                        if (err) {
                            reject(err);
                        } else {
                            resolve(data);
                        }
                    }
                )
            });
        });

        // Wait for all S3 deletions to complete
        await Promise.all(deletePromises);

        // Delete from database after S3 cleanup
        await models.EXPENSE_ATTACHMENT.destroy({
            where: {
                REC_ID: id
            }
        })

        return res.status(200).json({ success: true, message: 'Images deleted successfully' })

    } catch (error) {
        console.error('Error deleting images:', error);
        next(AppError.internal())
    }
}

//Adding Companies & Attendees while linking expense to AJ
const addCompandAttendeesfromAJ = async (expenseArray, userId, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    for await (const row of expenseArray) {
        const ajId = row.expActivityJournalId

        if (row.expActivityJournalId && row.expActivityJournalId != null) {
            const query = `SELECT ACJH_CUSTOMER
            FROM ACTIVITY_JOURNAL_HDR WHERE ACJH_ID = $ajId`

            const [result] = await sequelize.query(query, {
                bind: {
                    ajId
                }
            })

            if (
                result[0].ACJH_CUSTOMER != 0 &&
                result[0].ACJH_CUSTOMER != null
            ) {
                await models.EXP_COMPANY.count({
                    where: {
                        [Op.and]: {
                            COMP_ID: result[0].ACJH_CUSTOMER,
                            EXP_ID: row.expId
                        }
                    }
                }).then(async (count) => {
                    if (count == 0) {
                        await models.EXP_COMPANY.create({
                            EXP_ID: row.expId,
                            COMP_ID: result[0].ACJH_CUSTOMER,
                            INS_USER: userId
                        })
                    }
                })
            }
        }
    }
}

async function ajContacts(ajId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const query = `SELECT ACTH_JRNL_CONT_ID, ACTH_JRNL_CONT_NAME
             FROM ACTH_JRNL_CONTACTS WHERE ACTH_JRNL_ID = $ajId`

    return sequelize.query(query, {
        bind: {
            ajId
        }
    })
}

async function compAttendeesInsertion(item, row, userId, req) {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    //Fetch Companies from Contact Table and Update to expCompanies array
    const company = await models.CONTACT.findOne({
        attributes: [['CONT_COMP_ID', 'companyId']],
        where: {
            CONT_ID: item.ACTH_JRNL_CONT_ID
        },
        raw: true
    })
    if (company) {
        const isExistCompanies = await models.EXP_COMPANY.findOne({
            where: {
                COMP_ID: company.companyId,
                EXP_ID: row.expId
            },
            raw: true
        })
        if (!isExistCompanies) {
            await models.EXP_COMPANY.create({
                EXP_ID: row.expId,
                COMP_ID: company.companyId,
                INS_USER: userId
            })
        }
    }

    await models.EXP_ATTENDEES.create({
        EXP_ID: row.expId,
        EXP_CONTACT_ID: item.ACTH_JRNL_CONT_ID,
        EXP_CONTACT_NAME: item.ACTH_JRNL_CONT_NAME
    })
}

async function linkAjJournalContacts(expenseArray, userId, req) {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    for await (const row of expenseArray) {
        if (row.expActivityJournalId && row.expActivityJournalId != null) {
            const ajId = row.expActivityJournalId

            const [result] = await ajContacts(ajId, req)

            if (result.length > 0) {
                result.forEach(async (item) => {
                    await models.EXP_ATTENDEES.count({
                        where: {
                            [Op.and]: {
                                EXP_CONTACT_ID: item.ACTH_JRNL_CONT_ID,
                                EXP_CONTACT_NAME: item.ACTH_JRNL_CONT_NAME,
                                EXP_ID: row.expId
                            }
                        }
                    }).then(async (count) => {
                        if (count == 0) {
                            await compAttendeesInsertion(item, row, userId, req)
                        }
                    })
                })
            }
        }
    }
}

async function handleType(ajType, hyphen, typeName) {
    return ajType.length > 0 ? hyphen + typeName : ''
}

const linkActivityJournal = async (req, res, next) => {
    try {
        const expenseArray = req.body

        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;
        
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await Promise.all(
            expenseArray.map(async (item) => {
                const isExistLinkedDocs =
                    await models.EXPENSE_LINKED_DOCUMENT.findOne({
                        where: {
                            EXP_ID: item.expId
                        }
                    })

                if (isExistLinkedDocs) {
                    await models.EXPENSE_LINKED_DOCUMENT.destroy({
                        where: {
                            EXP_ID: item.expId
                        }
                    })
                }
                if (item.expDocType != undefined) {
                    await models.EXPENSE_LINKED_DOCUMENT.create({
                        EXP_ID: item.expId,
                        EXP_DOC_TYPE: item.expDocType,
                        EXP_DOC_ID: item.expActivityJournalId
                    })

                    const [ajCompAndTitle] =
                        await sequelize.query(`SELECT IFNULL(ACJH_COMP_NAME,'') as compName,
                    IFNULL(ACJH_TITLE,'') as title FROM ACTIVITY_JOURNAL_HDR 
                    WHERE ACJH_ID = ${item.expActivityJournalId}`)

                    const [ajType] =
                        await sequelize.query(`SELECT IFNULL(ACTH_JRNL_TYPE_NAME,'') as typeName
                     FROM ACTH_JRNL_TYPE_MST WHERE REC_ID in 
                     (SELECT ACTH_TYPE_ID FROM ACTIVITY_JOURNAL_HDR 
                        WHERE ACJH_ID = ${item.expActivityJournalId})`)

                    let pipe = ' | '
                    let hyphen = ' - '

                    if (
                        ajCompAndTitle[0].compName == '' ||
                        ajCompAndTitle[0].title == ''
                    ) {
                        pipe = ''
                    }
                    if (
                        ajCompAndTitle[0].compName == '' &&
                        ajCompAndTitle[0].title == ''
                    ) {
                        hyphen = ''
                    }

                    const typeName = await handleType(
                        ajType,
                        hyphen,
                        ajType[0].typeName
                    )

                    const description =
                        ajCompAndTitle[0].compName +
                        pipe +
                        ajCompAndTitle[0].title +
                        typeName

                    await models.EXPENSE.update(
                        {
                            EXP_DESCRIPTION: description
                        },
                        {
                            where: {
                                EXP_ID: item.expId,
                                [Op.or]: [
                                    { EXP_DESCRIPTION: { [Op.eq]: null } }, // Check if name is null
                                    { EXP_DESCRIPTION: { [Op.eq]: '' } } // Check if name is an empty string
                                ]
                            }
                        }
                    )
                }
            })
        )

        await addCompandAttendeesfromAJ(expenseArray, req.session.userId, req)
        await linkAjJournalContacts(expenseArray, req.session.userId, req)

        return res.status(200).json('Success')
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getAttendees = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const { id } = req.params
        const data = await models.EXP_ATTENDEES.findAll({
            attributes: [
                ['REC_ID', 'id'],
                ['EXP_CONTACT_ID', 'expContactId'],
                ['EXP_CONTACT_NAME', 'expContactName']
            ],
            order: ['EXP_CONTACT_NAME'],
            where: {
                EXP_ID: id
            },
            raw: true
        })

        data.forEach((item) => {
            if (item.expContactId == 0) {
                item.expContactId = `att${item.id}`
            }
        })

        res.status(200).json(data)
    } catch (error) {
        next(AppError.internal())
    }
}

const expenseReportGenerator = async (req, res, next) => {
    try {
        const row = req.query

        const result = await getExpenseList(
            'all',
            row.expReportId,
            row.fromDate,
            row.toDate,
            0,
            0,
            req
        )

        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const expenseAttendees = async (expId, req) => {
    const sequelize = req.tenantSequelize;

    const [contacts] = await sequelize.query(
        `SELECT
        REC_ID as recId,
        EXP_CONTACT_ID AS contactId,
        EXP_CONTACT_NAME as contactName FROM EXP_ATTENDEES WHERE EXP_ID = ?
    `,
        {
            replacements: [expId]
        }
    );

    return contacts;
}

const getAttendeesCompanies = async (req, res, next) => {
    // Use the tenant-specific Sequelize instances
    const sequelize = req.tenantSequelize;;

    try {
        let attendeesCompanies = []
        const row = req.query.attendees

        for await (let attendeesId of row.split(',')) {
            const company = await req.tenantModels.CONTACT.findOne({
                attributes: [['CONT_COMP_ID', 'companyId']],
                where: {
                    CONT_ID: attendeesId
                },
                raw: true
            })

            if (company) {
                const [companies] = await sequelize.query(`
                    SELECT
                    COMP_ID as companyId,
                    COMP_NAME as companyName
                    FROM COMPANIES
                    WHERE COMP_ID = ${company.companyId}
                `)

                attendeesCompanies.push(companies[0])
            }
        }

        const ids = attendeesCompanies.map((o) => o.companyId)
        const filtered = attendeesCompanies.filter(
            ({ companyId }, index) => !ids.includes(companyId, index + 1)
        )
        attendeesCompanies = filtered

        return res.status(200).json(attendeesCompanies)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getExpenseTaxEnabled = async (req, res, next) => {
    try {
        const [result] = await customAppParams(req)
        const status = result.length > 0 ? result[0].status : 0

        if (status == 1) {
            return res.status(200).json(true)
        } else {
            return res.status(200).json(false)
        }
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const customAppParams = async (req) => {
    // Use the tenant-specific Sequelize instances
    const sequelize = req.tenantSequelize;
    
    return sequelize.query(
        `SELECT IFNULL(APP_PARAM_STATUS,0) AS status
        FROM CUSTOM_APP_PARAMS
        WHERE APP_PARAM_ID='EXPENSE_ENABLE_TAX'`
    )
}

const getActivitlJournalDetails = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    try {
        let { id } = req.params
        const result = await sequelize.query(
            `SELECT A.REC_ID as id,
             IFNULL(C.COMP_NAME,'') as principal,
             IFNULL(A.ACJD_COMMENT,'') as comments,
             IFNULL(A.ACJD_FOLLOWUP,'') as followup
             FROM ACTIVITY_JOURNAL_DTL A 
             LEFT JOIN COMPANIES C ON A.ACJD_PRINCIPAL = C.COMP_ID
             WHERE A.ACJD_HDR_ID = :id`,
            {
                replacements: { id },
                type: sequelize.QueryTypes.SELECT
            }
        )
        return res.status(200).json(result)
    } catch (error) {
        logger.error(error)
        next(AppError.internal(error.message))
    }
}

module.exports = {
    addExpense,
    expenseCategory,
    expenseReportNumber,
    getExpenses,
    updateExpense,
    deleteExpense,
    linkToReport,
    getMerchants,
    getExpenseCoverage,
    uploadImage,
    getImage,
    getActivityJournal,
    imageDelete,
    linkActivityJournal,
    getAttendees,
    getExpenseList,
    expenseReportGenerator,
    getAttendeesCompanies,
    getExpenseTaxEnabled,
    customAppParams,
    getActivitlJournalDetails
}
