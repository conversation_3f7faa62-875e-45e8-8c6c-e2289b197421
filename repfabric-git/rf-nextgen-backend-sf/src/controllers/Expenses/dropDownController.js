const dropdowns = async (req, res) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    const crmDb = `RF_CRM_DB_${tenantId}`;

    const { type } = req.query
    let data = []
    if (type == 'contacts') {
        data = await sequelize.query(
            `
            select
            REC_ID as id,
            CONT_ID as contactId,
            CONT_FULL_NAME as contactName,
            COMP_NAME as companyName,
            CONT_JOB_TITLE as jobTitle
            from ${crmDb}.VIEW_CONT_LOOKUP
            order by CONT_FULL_NAME 
        `
        )
    }

    if (type == 'companies') {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;
        
        data = await models.COMPANY.findAll({
            attributes: [
                ['COMP_NAME', 'companyName'],
                ['COMP_ADDRESS_1', 'companyAddress'],
                ['COMP_CITY', 'companyCity'],
                ['COMP_STATE', 'companyState'],
                ['COMP_ID', 'companyId']
            ],
            order: ['COMP_NAME']
        })
    }
    return res.status(200).json(data)
}

module.exports = {
    dropdowns
}
