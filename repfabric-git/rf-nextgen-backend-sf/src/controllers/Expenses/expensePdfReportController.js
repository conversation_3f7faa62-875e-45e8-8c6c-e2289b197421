const puppeteer = require('puppeteer')
const hbs = require('handlebars')
const fs = require('fs-extra')
const path = require('path')
const dayjs = require('dayjs')
const utc = require('dayjs/plugin/utc')
const axios = require('axios')
const { s3 } = require('../../helpers/index')
const { formatter } = require('../../helpers')
const { sequelize } = require('../../models')
const PDFTK = require('node-pdftk')
const { customAppParams } = require('../Expenses/expenseController')
const logger = require('../../utils/logger')
const { QueryTypes } = require('sequelize')

const compile = async function (templateName, data) {
    const filePath = path.join(
        process.cwd(),
        'src/templates',
        `${templateName}.hbs`
    )
    const html = await fs.readFile(filePath, 'utf-8')
    return hbs.compile(html)(data)
}

hbs.registerHelper('formatDate', function (dateString) {
    return new hbs.SafeString(dayjs(dateString).format('MM-DD-YYYY'))
})

hbs.registerHelper('formatCurrrency', function (amount) {
    return Number(amount).toLocaleString('en-US', {
        style: 'currency',
        currency: 'USD'
    })
})

hbs.registerHelper('inc', function (number, options) {
    if (typeof number === 'undefined' || number === null) return null
    return number + (options.hash.inc || 1)
})

const expensePdfReport = async (req, res, next) => {
    try {
        const row = req.body
        let hostName = process.env.HOST_NAME

        const browser = await puppeteer.launch({
            executablePath: '/usr/bin/chromium',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        const page = await browser.newPage()

        if (process.env.NODE_ENV != 'development') {
            let host = req.get('host')
            hostName = host.split('/')[0]
        }

        const baseURL = process.env.INTERNAL_API_BASE_URL;

        const expenseDetails = await axios.get(
            `${baseURL}/api/v1/expense-report-details?
            createType=${row.createType}
            &expReportId=${row.expReportId}
            &userId=${req.session.userId}
           `,
            {
                'Content-Type': 'application/json',
                Accept: 'application/json',
                withCredentials: true,
                headers: {
                    Cookie: `JSESSIONID=${req.cookies.JSESSIONID}`,
                    'subDomain': req.subDomain,
                }
            }
        )

        const expenseReportsTotalAmt = await expenseReportsTotal(req, res, next)
        const [headers] = await headerDetails(req)
        const expReportDetail = await expReport(req, res, next)
        const imageArray = await expenseAttachments(req, res, next)
        const date = await expMinMaxDate(req, res, next)

        const javaBaseUrl = process.env.JAVA_BASE_URL;
        const arrayBuffer = await axios.get(
            `${javaBaseUrl}/RepfabricCRM/images/dynamic/repfabfiles/logo.png?pfdrid_c=true`,
            {
                responseType: 'arraybuffer',
                withCredentials: true,
                headers: {
                    Cookie: `JSESSIONID=${req.cookies.JSESSIONID}`
                }
            }
        )

        let buffer = Buffer.from(arrayBuffer.data, 'binary').toString('base64')
        let image = `data:${arrayBuffer.headers['content-type']};base64,${buffer}`
        const resImage = `<img src="${image}" alt="Logo" style='max-width: 200px; max-height: 130px; object-fit: contain;'/>`
        let pdfArray = []
        for await (let [index, item] of Object.entries(expenseDetails.data)) {
            pdfArray = []
            for await (const img of imageArray) {
                const a = img.key.split('?')[0]
                const extension = path.extname(a).slice(1)

                if (extension == 'pdf') {
                    let params = { Bucket: process.env.S3_BUCKET, Key: img.key }
                    const data = await s3.getObject(params).promise()
                    pdfArray.push(data.Body)
                }

                if (item.expId == img.expId) {
                    img.rowNo = Number(index) + 1
                }
            }
        }

        dayjs.extend(utc)
        const currentDate = dayjs().utc().format('MM-DD-YYYY')

        const filteredImage = imageArray.filter((imageData) => {
            if (path.extname(imageData.key).slice(1) != 'pdf') {
                return imageData
            }
        })
        const [customAppParamResults] = await customAppParams(req)
        const taxEnabled =
            customAppParamResults.length > 0
                ? customAppParamResults[0].status
                : 0

        const dynamicData = {
            user: expReportDetail.userName,
            expReportDetail,
            expData: expenseDetails.data,
            expenseReportsTotalAmt: formatter.format(
                Number(expenseReportsTotalAmt.totalExpenses)
            ),
            expenseReportsReimburseAmt: formatter.format(
                Number(expenseReportsTotalAmt.totalReimburse)
            ),
            expenseReportsTotalTax: formatter.format(
                Number(expenseReportsTotalAmt.totalTax)
            ),
            expenseReportsTotalTax1: formatter.format(
                Number(expenseReportsTotalAmt.totalTax1)
            ),
            expenseReportTaxTotal: formatter.format(
                Number(expenseReportsTotalAmt.taxTotal)
            ),
            currentDate,
            fromDate: date[0].fromDate,
            toDate: date[0].toDate,
            headerDetail: headers[0],
            logo: resImage,
            attachments: filteredImage,
            displayReceipts: row.displayReceipts,
            taxEnabled,
            taxLabel: await customLabels('IDS_EXP_TAX2', req),
            taxLabel1: await customLabels('IDS_EXP_TAX1', req)
        }
        const content = await compile('expense-pdf-report', dynamicData)
        await page.setContent(content)

        const result = await page.pdf({
            margin: {
                top: '30px',
                right: '50px',
                bottom: '30px',
                left: '50px'
            },
            printBackground: true,
            format: 'Letter'
        })

        if (row.displayReceipts) {
            pdfArray.unshift(result)
            PDFTK.input(pdfArray)
                .output()
                .then((buf) => {
                    res.contentType('application/pdf')
                    return res.send(buf)
                })
        } else {
            return res.contentType('appliaction/pdf').status(200).send(result)
        }
    } catch (error) {
        logger.error(error)
        next(AppError.internal(error.message))
    }
}

const expenseReportsTotal = async (req, res, next) => {
    try {
        const { expReportId } = req.body
        let queryTotal = `select
        IFNULL(SUM(EXP_AMOUNT),0) as total
        FROM EXPENSES
        WHERE EXP_REPORT_ID = $expReportId`

        let queryReimburse = `select
        (IFNULL(SUM(EXP_AMOUNT),0)+IFNULL(SUM(EXP_TAX),0)+IFNULL(SUM(EXP_TAX1),0)) as total
        FROM EXPENSES
        WHERE EXP_REPORT_ID = $expReportId
        AND EXP_REIMBURSABLE = 1`

        let queryTax = `select
        IFNULL(SUM(EXP_TAX),0) as total
        FROM EXPENSES
        WHERE EXP_REPORT_ID = $expReportId`

        let queryTax1 = `select
        IFNULL(SUM(EXP_TAX1),0) as total
        FROM EXPENSES
        WHERE EXP_REPORT_ID = $expReportId`

        let queryTaxTotal = `select
        (IFNULL(SUM(EXP_TAX),0)+IFNULL(SUM(EXP_TAX1),0)) as total
        FROM EXPENSES
        WHERE EXP_REPORT_ID = $expReportId`

        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const [totalExpenses] = await sequelize.query(queryTotal, {
            bind: {
                expReportId
            }
        })

        const [totalReimburse] = await sequelize.query(queryReimburse, {
            bind: {
                expReportId
            }
        })

        const [totalTax] = await sequelize.query(queryTax, {
            bind: {
                expReportId
            }
        })

        const [totalTax1] = await sequelize.query(queryTax1, {
            bind: {
                expReportId
            }
        })

        const [taxTotal] = await sequelize.query(queryTaxTotal, {
            bind: {
                expReportId
            }
        })

        return {
            totalExpenses: totalExpenses[0].total,
            totalReimburse: totalReimburse[0].total,
            totalTax: totalTax[0].total,
            totalTax1: totalTax1[0].total,
            taxTotal: taxTotal[0].total
        }
    } catch (error) {
        next(AppError.internal())
    }
}

const headerDetails = async (req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const imageUrl = process.env.IMAGE_URL
    return sequelize.query(` SELECT
        IFNULL(PARAM_SUBS_NAME, '') as PARAM_SUBS_NAME,
        IFNULL(PARAM_SUBS_ADDRESS, '') as PARAM_SUBS_ADDRESS,
        IFNULL(PARAM_DATE_FORMAT, 1) as PARAM_DATE_FORMAT,
        IFNULL(PARAM_TIME_FORMAT, 2) as PARAM_TIME_FORMAT,
        IFNULL(PARAM_TIMEZONE_ID, '') as PARAM_TIMEZONE_ID,
        CONCAT('${imageUrl}',PARAM_SUBS_LOGO) as LOGO_IMAGE from PARAMS`)
}

const expenseAttachments = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let { expReportId } = req.body
        let query = `SELECT GROUP_CONCAT(IFNULL(EXP_ID,0)) as ids
        FROM EXPENSES WHERE EXP_REPORT_ID = $expReportId`
        let [expIds] = await sequelize.query(query, {
            bind: { expReportId }
        })
        const [result] = await sequelize.query(
            `SELECT EA.EXP_ATTACHMENT, EA.REC_ID,EA.EXP_ID,E.EXP_DESCRIPTION FROM EXP_ATTACHMENTS EA JOIN
            EXPENSES E ON E.EXP_ID = EA.EXP_ID
            WHERE EA.EXP_ID IN (${expIds[0].ids}) ORDER BY E.REC_ID DESC`
        )
        let imageArray = []
        result.map((item) => {
            const signedUrl = s3.getSignedUrl('getObject', {
                Key: item.EXP_ATTACHMENT,
                Bucket: process.env.S3_BUCKET,
                Expires: 3900
            })
            imageArray.push({
                id: item.REC_ID,
                imageUrl: signedUrl,
                expId: item.EXP_ID,
                desc: item.EXP_DESCRIPTION,
                key: item.EXP_ATTACHMENT
            })
        })

        return imageArray
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const expMinMaxDate = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const { expReportId } = req.body
        let maxDate = await models.EXPENSE.findOne({
            attributes: [
                [Sequelize.fn('max', Sequelize.col('EXP_END_DATE')), 'toDate']
            ],
            where: {
                EXP_REPORT_ID: expReportId
            },
            raw: true
        })

        let minDate = await models.EXPENSE.findOne({
            attributes: [
                [
                    Sequelize.fn('min', Sequelize.col('EXP_START_DATE')),
                    'fromDate'
                ]
            ],
            where: {
                EXP_REPORT_ID: expReportId
            },
            raw: true
        })

        return [
            {
                fromDate: minDate.fromDate,
                toDate: maxDate.toDate
            }
        ]
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const expReport = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const id = req.body.expReportId
        const result = await sequelize.query(
            `SELECT EXP_DESCRIPTION as expReportDescription, IFNULL(U.USER_NAME,'') as userName FROM EXPENSE_REPORTS ER 
            LEFT JOIN USERS U ON U.USER_ID = ER.EXP_CREATED_FOR 
            WHERE ER.EXP_REPORT_ID = :id`,
            {
                type: QueryTypes.SELECT,
                replacements: { id }
            }
        )

        // Check if result is an array and has at least one element
        return result.length > 0 ? result[0] : ''
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const customLabels = async (id, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        // Await the query execution and specify QueryTypes.SELECT to parse the results correctly
        const result = await sequelize.query(
            `SELECT LBL_CUSTOM FROM CUSTOM_LABELS WHERE LBL_ID = :id`,
            {
                type: QueryTypes.SELECT,
                replacements: { id }
            }
        )

        // Check if result is an array and has at least one element
        return result.length > 0 ? result[0].LBL_CUSTOM : ''
    } catch (error) {
        return '' // Handle the error as appropriate for your application
    }
}

module.exports = {
    expensePdfReport
}
