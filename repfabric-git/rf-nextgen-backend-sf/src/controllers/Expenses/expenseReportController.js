const dayjs = require('dayjs')
const { sequelize } = require('../../models')
const { getExpenseList } = require('../Expenses/expenseController')
const ExcelJS = require('exceljs')
const logger = require('../../utils/logger')
const { QueryTypes } = require('sequelize')

const addExpenseReport = async (req, res, next) => {
    try {
        let row = []
        row = req.body
        
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const isExistExpense = await models.EXPENSE_REPORT.count({
            where: { EXP_DESCRIPTION: req.body.description }
        })

        if (isExistExpense > 0) {
            next(AppError.badRequest('Duplicate Expense Report'))
            return
        }
        //generate EXP_REPORT_ID
        let exp_id = 0

        exp_id = await GenerateId('EXPRPRT', req)

        const formattedStartdate = dayjs(req.body.startDate).format(
            'YYYY-MM-DD'
        )
        const formattedEdnDate = dayjs(req.body.endDate).format('YYYY-MM-DD')

        const expenseReport = {
            EXP_REPORT_ID: exp_id,
            EXP_DESCRIPTION: req.body.description,
            EXP_START_DATE: formattedStartdate,
            EXP_END_DATE: formattedEdnDate,
            EXP_STATUS: 0,
            EXP_CREATED_FOR: req.body.createdFor,
            EXP_TOTAL_AMOUNT: req.body.total,
            INS_USER: req.session.userId,
            UPD_USER: req.session.userId
        }
        const result = await models.EXPENSE_REPORT.create(expenseReport)

        if (result) {
            //IF AJ is linked while Creating New Expense Report then update AJ id to Report
            if (row.isLink != undefined && row.isLink.length > 0) {
                await linkToExpense(exp_id, row.isLink, req)
            }
            return res.status(200).json('Success')
        } else {
            next(AppError.internal())
        }
    } catch (error) {
        logger.error('Expense API error:', error);
        if (error.stack) logger.error(error.stack);
        next(AppError.internal(error.message));
        next(AppError.internal())
    }
}

async function linkToExpense(exp_id, data, req) {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    data.map(async (row) => {
        await models.EXPENSE.update(
            {
                EXP_REPORT_ID: exp_id
            },
            {
                where: {
                    EXP_ID: row.expId
                }
            }
        )
    })
}

const getExpenseReport = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const { createType, fromDate, toDate, status } = req.query
        let where = {}

        let field, field1
        let value, value1
        const userId = req.session.userId
        const expStatus = status == undefined ? 0 : status
        //self - createdBy logged in user , other - created for logged in user, created by and created for logged in user

        where = await getExpRprtSwitchCase(createType, userId, expStatus, req)

        where = Object.values(where).length > 0 ? where + ' AND ' + 1 : 1
        if (fromDate != '' && fromDate != undefined) {
            const formattedStartdate = dayjs(fromDate, 'MM-DD-YYYY').format(
                'YYYY-MM-DD'
            )
            const formattedEdnDate = dayjs(toDate, 'MM-DD-YYYY').format(
                'YYYY-MM-DD'
            )

            field = 'ER.EXP_START_DATE'
            field1 = 'ER.EXP_END_DATE'

            value = `${formattedStartdate}`
            value1 = `${formattedEdnDate}`
            where =
                Object.keys(where).length !== 0
                    ? where +
                      ' AND (' +
                      field +
                      ' between ' +
                      `'` +
                      value +
                      `'` +
                      ' AND ' +
                      `'` +
                      value1 +
                      `'` +
                      ' or ' +
                      field1 +
                      ' between ' +
                      `'` +
                      value +
                      `'` +
                      ' AND ' +
                      `'` +
                      value1 +
                      `'` +
                      ')'
                    : '(' +
                      field +
                      ' between ' +
                      `'` +
                      value +
                      `'` +
                      ' AND ' +
                      `'` +
                      value1 +
                      `'` +
                      ' or ' +
                      field1 +
                      ' between ' +
                      `'` +
                      value +
                      `'` +
                      ' AND ' +
                      `'` +
                      value1 +
                      `'` +
                      ')'
        }

        where =
            expStatus == 0 ? where : where + ' AND EXP_STATUS = ' + expStatus

        const result = await sequelize.query(`
            select
            ER.REC_ID as id,
            ER.EXP_REPORT_ID as expReportId,
            ER.EXP_DESCRIPTION as description,
            DATE_FORMAT(ER.EXP_START_DATE,'%m-%d-%Y') as startDate,
            DATE_FORMAT(ER.EXP_END_DATE,'%m-%d-%Y') as endDate,
            ER.EXP_STATUS as status,
            ER.EXP_CREATED_FOR as createdFor,
            UC.USER_NAME as userName,
            IFNULL(ER.EXP_TOTAL_AMOUNT,0) as total,
            ER.INS_USER as insUser,
            U.USER_NAME as createdBy,
            ER.UPD_USER as updUser,
            case
                when ER.EXP_STATUS = 1 then 'SUBMITTED'
                when ER.EXP_STATUS = 2 then 'APPROVED'
                when ER.EXP_STATUS = 3 then 'REJECTED'
                when ER.EXP_STATUS = 4 then 'PAID'
                else 'OPEN' end
            as status
            from EXPENSE_REPORTS ER
            join USERS U
            on U.USER_ID = ER.INS_USER
            join USERS UC
            on UC.USER_ID = ER.EXP_CREATED_FOR
            where ${where}
            ORDER BY ER.REC_ID DESC
        `, { type: QueryTypes.SELECT })

        if (result.length) {
            const data = await totalAndApproverDetail(result, req)
            return res.status(200).json({ data, totalCount: data.length })
        } else {
            next(AppError.notFound())
        }
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

function totalAndApproverDetail(result, req) {
    return Promise.all(
        result.map(async (item) => {
            const [totalAmount] = await expenseReportsTotal(item.expReportId, req)
            const [apprvrName] = await expenseApprover(item.createdFor, req)

            return {
                ...item,
                total: totalAmount.length > 0 ? totalAmount[0].total : 0,
                totalTax: totalAmount.length > 0 ? totalAmount[0].totalTax : 0,
                approver: apprvrName.length > 0 ? apprvrName : [],
                approverName:
                    apprvrName.length > 0 ? apprvrName[0].approver : '',
                approverId:
                    apprvrName.length > 0 ? apprvrName[0].approverId : '',
                expenseCount:
                    totalAmount.length > 0 ? totalAmount[0].expCount : 0
            }
        })
    )
}

const updateExpenseReport = async (req, res, next) => {
    try {
        const { description, startDate, endDate, createdFor, id } = req.body

        const formattedStartdate = dayjs(startDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )
        const formattedEdnDate = dayjs(endDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const expenseReport = await models.EXPENSE_REPORT.update(
            {
                EXP_DESCRIPTION: description,
                EXP_START_DATE: formattedStartdate,
                EXP_END_DATE: formattedEdnDate,
                EXP_CREATED_FOR: createdFor,
                UPD_USER: req.session.userId
            },
            {
                where: {
                    REC_ID: id
                }
            }
        )

        if (expenseReport) {
            return res.status(200).json('Success')
        } else {
            next(AppError.internal())
        }
    } catch (error) {
        next(AppError.internal())
    }
}

const deleteExpenseReport = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;
        
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const { expReportId } = req.params
        let query = `SELECT * FROM EXPENSES E
        JOIN EXPENSE_REPORTS ER ON E.EXP_REPORT_ID = ER.EXP_REPORT_ID
        WHERE ER.EXP_REPORT_ID = $expReportId AND ER.EXP_STATUS != 2
        `
        const [linkedExpenses] = await sequelize.query(query, {
            bind: {
                expReportId
            }
        })

        if (linkedExpenses.length > 0) {
            await models.EXPENSE.update(
                {
                    EXP_REPORT_ID: 0
                },
                {
                    where: {
                        EXP_REPORT_ID: expReportId
                    }
                }
            )
        }

        const deleteItem = await models.EXPENSE_REPORT.destroy({
            where: {
                [Op.and]: {
                    EXP_REPORT_ID: expReportId,
                    EXP_STATUS: { [Op.ne]: 2 }
                }
            }
        })

        if (deleteItem) {
            return res.status(200).json('Success')
        } else {
            next(AppError.internal())
        }
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

async function expenseReportsTotal(reportId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Add validation to prevent undefined reportId
    if (!reportId || reportId === undefined || reportId === null) {
        // Return default values if reportId is invalid
        return [[{ total: 0, totalTax: 0, expCount: 0 }]];
    }

    return sequelize.query(`select
    IFNULL(SUM(EXP_AMOUNT),0) as total,(IFNULL(SUM(EXP_TAX),0)+IFNULL(SUM(EXP_TAX1),0)) as totalTax,count(*) as expCount
    from EXPENSES
    where EXP_REPORT_ID = :reportId`, {
        replacements: { reportId },
        type: QueryTypes.SELECT
    });
}

async function expenseApprover(userId, req) {
    // Prevent query execution if userId is invalid
    if (!userId) {
        return [[]]; // return empty result
    }

    const sequelize = req.tenantSequelize;

    return sequelize.query(
        `select IFNULL(U.USER_NAME,0) as approver,
                IFNULL(EA.APPROVER_ID,0) as approverId
         from USERS U
         join EXP_APPROVER EA on EA.APPROVER_ID = U.USER_ID
         where EA.USER_ID = :userId`,
        {
            replacements: { userId },
            type: QueryTypes.SELECT
        }
    );
}

async function expenseCompanies(expId, req) {
    // Prevent query execution if userId is invalid
    if (!expId) {
        return [[]]; // return empty result
    }

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(`select
    IFNULL(GROUP_CONCAT(C.COMP_NAME),'') as companyName
    from
    EXP_COMPANIES EC
    join COMPANIES C on EC.COMP_ID = C.COMP_ID
    where EXP_ID = ${expId}`)
}

const expenseAttendees = async (expId, req) => {
    // Prevent query execution if userId is invalid
    if (!expId) {
        return [[]]; // return empty result
    }
    
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(
        `SELECT
        GROUP_CONCAT(EXP_CONTACT_NAME,' ',NULLIF(CONCAT('(',CONT_JOB_TITLE,')'),'')) 
        AS attendeeswithJobTitle
        FROM EXP_ATTENDEES 
        LEFT JOIN CONTACTS ON CONT_ID = EXP_CONTACT_ID
        WHERE EXP_ID = $expId
    `,
        {
            bind: { expId }
        }
    )
}

async function expReportForApprover(expStatus, where, value, req) {
    if (expStatus == 1 || expStatus == 2) {
        where = where.concat(
            ` OR EXP_CREATED_FOR IN (SELECT USER_ID FROM EXP_APPROVER WHERE APPROVER_ID = ${value}))`
        )
    }
    else {
        const [allowApproversToView] = await allowApproversViewExpense(req)
        const isAllowAccess = allowApproversToView
            ? allowApproversToView[0].APP_PARAM_STATUS
            : 0
        if (isAllowAccess == 1) {
            where = where.concat(
                ` OR EXP_CREATED_FOR IN (SELECT USER_ID FROM EXP_APPROVER WHERE APPROVER_ID = ${value}) AND EXP_STATUS IN (2, 3,4))`
            )
        } else {
            where = where.concat(
                ` OR ER.EXP_REPORT_ID IN (SELECT EXP_REPORT_ID FROM EXP_APPROVAL_HISTORY WHERE APPROVED_BY = ${value} AND APPROVED_STATUS IN (2,3,4)))`
            )
        }
    }
    return where
}

const allowApproversViewExpense = async (req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(
        `SELECT APP_PARAM_STATUS FROM CUSTOM_APP_PARAMS WHERE APP_PARAM_ID='ALLOW_APPROVERS_VIEW_EXPENSE'`
    )
}

const getExpRprtSwitchCase = async (createType, userId, expStatus, req) => {
    let field, field1
    let value
    let where = {}
    switch (createType) {
        case 'self':
            field = 'INS_USER'
            value = userId
            where =
                // expStatus == 1 ?
                '(' + field + '=' + value
            // : field + '=' + value
            where = await expReportForApprover(expStatus, where, value, req)
            break
        case 'other':
            field = 'EXP_CREATED_FOR'
            field1 = 'INS_USER'
            value = userId
            where =
                // expStatus == 1 ?
                '((' +
                field +
                '=' +
                value +
                ' AND ' +
                field1 +
                '!=' +
                value +
                ')'
            // : field + '=' + value + ' AND ' + field1 + '!=' + value
            where = await expReportForApprover(expStatus, where, value, req)
            break
        default:
            field = 'INS_USER'
            field1 = 'EXP_CREATED_FOR'
            value = userId
            where =
                // expStatus == 1 ?
                '(' +
                '(' +
                field +
                '=' +
                value +
                ' OR ' +
                field1 +
                '=' +
                value +
                ')'
            // : '(' +
            //   field +
            //   '=' +
            //   value +
            //   ' OR ' +
            //   field1 +
            //   '=' +
            //   value +
            //   ')'
            where = await expReportForApprover(expStatus, where, value, req)
    }

    return where
}

const exportToExcelData = async (fromDate, toDate, categoryId, userId, req) => {
    let where = {}
    let field, field1, value, value1
    where = 1

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    if (fromDate != '' && fromDate != undefined) {
        const formattedStartdate = dayjs(fromDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )
        const formattedEdnDate = dayjs(toDate, 'MM-DD-YYYY').format(
            'YYYY-MM-DD'
        )

        field = 'ER.EXP_START_DATE'
        field1 = 'ER.EXP_END_DATE'

        value = `${formattedStartdate}`
        value1 = `${formattedEdnDate}`

        where =
            Object.keys(where).length !== 0
                ? where +
                  ' AND (' +
                  field +
                  ' between ' +
                  `'` +
                  value +
                  `'` +
                  ' AND ' +
                  `'` +
                  value1 +
                  `'` +
                  ' or ' +
                  field1 +
                  ' between ' +
                  `'` +
                  value +
                  `'` +
                  ' AND ' +
                  `'` +
                  value1 +
                  `'` +
                  ')'
                : '(' +
                  field +
                  ' between ' +
                  `'` +
                  value +
                  `'` +
                  ' AND ' +
                  `'` +
                  value1 +
                  `'` +
                  ' or ' +
                  field1 +
                  ' between ' +
                  `'` +
                  value +
                  `'` +
                  ' AND ' +
                  `'` +
                  value1 +
                  `'` +
                  ')'
    }
    if (userId !== undefined && userId !== null && userId !== '' && userId != 0) {
        where =
            where +
            'AND (ER.EXP_CREATED_FOR IN (' +
            userId +
            ') OR ER.INS_USER IN (' +
            userId +
            ') OR ER.EXP_CREATED_FOR IN (SELECT USER_ID FROM EXP_APPROVER WHERE APPROVER_ID in (' +
            userId +
            ')))'
    }

    const result = await sequelize.query(`
    select
    ER.REC_ID as id,
    ER.EXP_REPORT_ID as expReportId,
    ER.EXP_DESCRIPTION as description,
    DATE_FORMAT(ER.EXP_START_DATE,'%Y-%m-%d') as startDate,
    DATE_FORMAT(ER.EXP_END_DATE,'%Y-%m-%d') as endDate,
    ER.EXP_STATUS as status,
    ER.EXP_CREATED_FOR as createdFor,
    UC.USER_NAME as userName,
    IFNULL(ER.EXP_TOTAL_AMOUNT,0) as total,
    ER.INS_USER as insUser,
    U.USER_NAME as createdBy,
    ER.UPD_USER as updUser,
    case
        when ER.EXP_STATUS = 1 then 'SUBMITTED'
        when ER.EXP_STATUS = 2 then 'APPROVED'
        when ER.EXP_STATUS = 3 then 'REJECTED'
        when ER.EXP_STATUS = 4 then 'PAID'
        else 'OPEN' end
    as status
    from EXPENSE_REPORTS ER
    join USERS U
    on U.USER_ID = ER.INS_USER
    join USERS UC
    on UC.USER_ID = ER.EXP_CREATED_FOR
    where ${where} 
    ORDER BY ER.REC_ID DESC
`)
    let response = []
    if (result.length) {
        const data = await totalAndApproverDetail(result, req)

        for await (let item of data) {
            let expenseResult = []
            expenseResult = await getExpenseList(
                'all',
                item.expReportId,
                '',
                '',
                '',
                0,
                req
            )
            let expResult
            if (categoryId != undefined && categoryId != 0) {
                expResult = expenseResult.filter(
                    (data) => data.expCategoryId == categoryId
                )
            } else {
                expResult = expenseResult
            }

            expResult = await attendeesAndCompanies(expResult, req)
            const resultData = {
                ...item,
                detailData: expResult
            }
            response.push(resultData)
        }

        return response.filter((data) => data.detailData.length > 0)
    } else {
        return null
    }
}

function attendeesAndCompanies(result, req) {
    return Promise.all(
        result.map(async (item) => {
            const [companies] = await expenseCompanies(item.expId, req)
            const [attendees] = await expenseAttendees(item.expId, req)

            return {
                ...item,
                companies: companies.length > 0 ? companies[0].companyName : '',
                attendees:
                    attendees.length > 0
                        ? attendees[0].attendeeswithJobTitle
                        : ''
            }
        })
    )
}

const exportToExcel = async (req, res, next) => {
    try {
        let user = 0
        if (req.session.roleId == 1 || req.session.isAdmin == 1) {
            if (req.query.userId == undefined || req.query.userId == 0) {
                user = 0
            } else {
                user = req.query.userId
            }
        } else {
            user = req.session.userId
        }

        const taxLabel = await customLabels('IDS_EXP_TAX2', req)
        const taxLabel1 = await customLabels('IDS_EXP_TAX1', req)

        const data = await exportToExcelData(
            req.query.fromDate,
            req.query.toDate,
            req.query.category,
            user,
            req
        )
        const isUnformatted =
            req.query.isUnFormatted == undefined
                ? true
                : req.query.isUnFormatted
        // Create a new workbook
        const workbook = new ExcelJS.Workbook()
        if (isUnformatted == true) {
            const unformattedHeaders = [
                'Description',
                'Start Date',
                'End Date',
                'Status',
                'Mileage',
                'Total',
                'Created For',
                'Description',
                'Category',
                'Merchant',
                'Attendees',
                'Companies',
                'Reimbursable',
                'Date',
                'Total Amount',
                taxLabel,
                taxLabel1,
                'AJ-Name',
                'Customer'
            ]

            const worksheetUnformatted = workbook.addWorksheet('Unformatted')
            const headerRowUnformatted =
                worksheetUnformatted.addRow(unformattedHeaders)

            headerRowUnformatted.eachCell((cell, index) => {
                cell.font = { bold: true } // Apply bold font to header row
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'B0C4DE' }
                } // Apply yellow fill color
                cell.alignment = { horizontal: 'center' } // Center align text
                if (index == 1) {
                    worksheetUnformatted.getColumn(index).width = 45
                } else if (index == 6 || index == 8 || index == 9) {
                    worksheetUnformatted.getColumn(index).width = 15
                } else {
                    worksheetUnformatted.getColumn(index).width = 25
                }
            })

            // Add the data from your JSON to the worksheet
            if (!data) {
                next(AppError.noContent())
                return
            }
            data.forEach((unformattedItem) => {
                // Add subrows for detailData under the header row
                unformattedItem.detailData.forEach((detail) => {
                    // Add a header row for each item
                    let ajName = ''
                    let customer = ''
                    detail.expLinkedDocs.forEach((ajData) => {
                        ajName =
                            ajName == ''
                                ? ajData.title
                                : ajName + ', ' + ajData.title
                        customer =
                            customer == ''
                                ? ajData.companyName
                                : customer + ', ' + ajData.companyName
                    })
                    const unformattedHeaderRow = [
                        unformattedItem.description,
                        unformattedItem.startDate,
                        unformattedItem.endDate,
                        unformattedItem.status,
                        detail.expMiles,
                        unformattedItem.total,
                        detail.expCreatedFor,
                        detail.expDescription,
                        detail.expCategory,
                        detail.expMerchant,
                        detail.attendees == null
                            ? ''
                            : detail.attendees.replace(/\(\s*\)/g, ''),
                        detail.companies,
                        detail.expReimbursable == 1 ? 'Yes' : 'No',
                        detail.expCategoryBaseId == 1
                            ? detail.expStartDate + ' - ' + detail.expEndDate
                            : detail.expStartDate,
                        detail.expAmount,
                        detail.expTax,
                        detail.expTax1,
                        ajName,
                        customer
                    ]

                    worksheetUnformatted.addRow(unformattedHeaderRow)
                })
            })
        } else {
            const worksheet = workbook.addWorksheet('Formatted')

            // Define your headers
            const headers = [
                'Description',
                'Start Date',
                'End Date',
                'Status',
                'Total',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                ''
            ]
            const detailHeaderRowHeaders = [
                '',
                'Created For',
                'Description',
                'Category',
                'Merchant',
                'Attendees',
                'Companies',
                'Reimbursable',
                'Date',
                'Mileage',
                'Total Amount',
                taxLabel,
                taxLabel1,
                'AJ-Name',
                'Customer'
            ]
            // Add the headers to the worksheet
            const headerRow = worksheet.addRow(headers)
            headerRow.eachCell((cell, index) => {
                cell.font = { bold: true } // Apply bold font to header row
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'B0C4DE' }
                } // Apply yellow fill color
                cell.alignment = { horizontal: 'center' } // Center align text
                if (index == 1) {
                    worksheet.getColumn(index).width = 30
                } else if (index == 6 || index == 8 || index == 9) {
                    worksheet.getColumn(index).width = 15
                } else {
                    worksheet.getColumn(index).width = 25
                }
            })
            // Add the data from your JSON to the worksheet
            if (!data) {
                next(AppError.noContent())
                return
            }

            data.forEach((item) => {
                // Add a header row for each item
                const headerRow = [
                    item.description,
                    item.startDate,
                    item.endDate,
                    item.status,
                    item.total
                ]
                worksheet.addRow(headerRow)
                const detailRow = worksheet.addRow(detailHeaderRowHeaders)
                detailRow.eachCell((cell, index) => {
                    if (index !== 1) {
                        cell.font = { bold: true } // Apply bold font to header row
                        cell.fill = {
                            type: 'pattern',
                            pattern: 'solid',
                            fgColor: { argb: 'C6E1F7' }
                        } // Apply yellow fill color
                        cell.alignment = { horizontal: 'left' } // Center align text
                    }
                })
                // Add subrows for detailData under the header row
                item.detailData.forEach((detail) => {
                    let ajName = ''
                    let customer = ''
                    detail.expLinkedDocs.forEach((ajData) => {
                        ajName =
                            ajName == ''
                                ? ajData.title
                                : ajName + ', ' + ajData.title
                        customer =
                            customer == ''
                                ? ajData.companyName
                                : customer + ', ' + ajData.companyName
                    })
                    const detailRow = [
                        '', // Leave an empty cell for indentation
                        detail.expCreatedFor,
                        detail.expDescription,
                        detail.expCategory,
                        detail.expMerchant,
                        detail.attendees == null
                            ? ''
                            : detail.attendees.replace(/\(\s*\)/g, ''),
                        detail.companies,
                        detail.expReimbursable == 1 ? 'Yes' : 'No',
                        detail.expCategoryBaseId == 1
                            ? detail.expStartDate + ' - ' + detail.expEndDate
                            : detail.expStartDate,
                        detail.expMiles,
                        detail.expAmount,
                        detail.expTax,
                        detail.expTax1,
                        ajName,
                        customer
                    ]
                    worksheet.addRow(detailRow)
                })

                //worksheet.addRow([])
                worksheet.addRows([{ outlineLevel: 1 }], 'bottom')
            })
        }
        // Define your headers
        workbook.xlsx
            .writeBuffer()
            .then((buffer) => {
                // Set the response headers
                res.setHeader(
                    'Content-Type',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                res.setHeader(
                    'Content-Disposition',
                    'attachment; filename="Expense_Report.xlsx"'
                )

                // Send the Excel file as the response
                return res.send(buffer)
            })
            .catch((error) => {
                return res
                    .status(500)
                    .json('Error generating Excel file' + error.message)
            })
        return
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const customLabels = async (id, req) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        // Await the query execution and specify QueryTypes.SELECT to parse the results correctly
        const result = await sequelize.query(
            `SELECT LBL_CUSTOM FROM CUSTOM_LABELS WHERE LBL_ID = :id`,
            {
                type: QueryTypes.SELECT,
                replacements: { id }
            }
        )

        // Check if result is an array and has at least one element
        return result.length > 0 ? result[0].LBL_CUSTOM : ''
    } catch (error) {
        return '' // Handle the error as appropriate for your application
    }
}
module.exports = {
    addExpenseReport,
    getExpenseReport,
    updateExpenseReport,
    deleteExpenseReport,
    exportToExcel
}
