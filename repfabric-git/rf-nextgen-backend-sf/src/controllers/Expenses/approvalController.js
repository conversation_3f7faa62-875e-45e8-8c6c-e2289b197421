const dayjs = require('dayjs')
const utc = require('dayjs/plugin/utc')
const { sequelize } = require('../../models')
const logger = require('../../utils/logger')

const addApprover = async (req, res, next) => {
    try {
        const row = req.body
        const approverDetail = {
            USER_ID: row.userId,
            APPROVER_ID: row.approverId,
            UPD_USER: req.session.userId
        }
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.EXPENSEAPPROVER.count({
            where: { USER_ID: row.userId }
        }).then(async (count) => {
            if (count === 0) {
                await models.EXPENSEAPPROVER.create(approverDetail)
                return res.status(200).json('Success')
            } else {
                next(
                    AppError.badRequest('Approver already exist for this user')
                )
            }
        })
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const submitValidation = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        let raw = { isWarning: false, message: '' }

        const query = `SELECT count(e.EXP_ID) as count FROM EXPENSE_CATEGORIES c 
        join EXPENSES e on e.EXP_CATEGORY_ID=c.REC_ID
        left join EXP_LINKED_DOCS a on a.EXP_ID = e.EXP_ID
        where e.EXP_REPORT_ID = ? and c.CATEGORY_AJLINK_FLAG = 1 and a.EXP_ID is null
        AND e.EXP_AMOUNT > IFNULL(c.CATEGORY_AJLINK_AMT,0)`

        const data = await sequelize.query(query, [req.params.expReportId])

        if (data[0].count > 0) {
            raw.isWarning = true

            if (data[0].count === 1) {
                raw.message =
                    'There is 1 expense that is not linked to AJ ..!! Do you want to continue?'
            } else {
                raw.message =
                    'There are ' +
                    data[0].count +
                    ' expenses that are not linked to AJ ..!! Do you want to continue?'
            }
            return res.status(200).json(raw)
        }
        return res.status(200).json(raw)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const linkToExpenseReportSubmitValidation = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let raw = { isError: false, message: '' }
        const [data] = await sequelize.query(
            `
            SELECT COUNT(e.EXP_ID) AS count
        FROM EXPENSE_CATEGORIES c
        JOIN EXPENSES e ON e.EXP_CATEGORY_ID = c.REC_ID
        LEFT JOIN EXP_ATTACHMENTS ea ON ea.EXP_ID = e.EXP_ID
        WHERE e.EXP_REPORT_ID = ${req.query.expReportId}
        AND c.CATEGORY_RECEIPT_REQ_FLAG = 1
        AND ea.EXP_ID IS NULL
        AND e.EXP_AMOUNT > IFNULL(c.CATEGORY_AJLINK_AMT, 0)
        `,
            {
                type: Sequelize.QueryTypes.SELECT
            }
        )

        if (data.count > 0) {
            raw.isError = true

            if (data.count === 1) {
                raw.message =
                    'There is one expense that does not have a receipt attached.'
            } else {
                raw.message = `There are ${data.count} expenses that do not have receipts attached.`
            }
            return res.status(200).json(raw)
        }
        return res.status(200).json(raw)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const linkExpenseSubmitValidation = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    try {
        let raw = { isError: false, message: '' }
        const [data] = await sequelize.query(
            `
            SELECT COUNT(e.EXP_ID) AS count
        FROM EXPENSE_CATEGORIES c
        JOIN EXPENSES e ON e.EXP_CATEGORY_ID = c.REC_ID
        LEFT JOIN EXP_ATTACHMENTS ea ON ea.EXP_ID = e.EXP_ID
        WHERE e.EXP_ID IN (${req.body.expIds})
        AND c.CATEGORY_RECEIPT_REQ_FLAG = 1
        AND ea.EXP_ID IS NULL
        AND e.EXP_AMOUNT > IFNULL(c.CATEGORY_AJLINK_AMT, 0)
        `,
            {
                type: Sequelize.QueryTypes.SELECT
            }
        )

        if (data.count > 0) {
            raw.isError = true

            if (data.count === 1) {
                raw.message =
                    'There is one expense that does not have a receipt attached.'
            } else {
                raw.message = `There are ${data.count} expenses that do not have receipts attached.`
            }
            return res.status(200).json(raw)
        }
        return res.status(200).json(raw)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const sendForApproval = async (req, res, next) => {
    try {
        const userId = req.session.userId
        const row = req.body
        let approvedBy, submittedUTCTime, approvedUTCTime
        dayjs.extend(utc)

        if (!req.body.createdFor) {
            next(AppError.badRequest('createdFor is missing..!!'))
            return
        }

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const subMittedTo = await models.EXPENSEAPPROVER.findOne({
            attributes: ['APPROVER_ID'],
            where: {
                USER_ID: req.body.createdFor
            }
        })

        if (!subMittedTo) {
            next(AppError.badRequest('Approver not found'))
            return
        }

        await models.EXPENSE_REPORT.update(
            {
                EXP_STATUS: row.approvedStatus
            },
            {
                where: {
                    REC_ID: row.id
                }
            }
        ).then(async () => {
            if (row.approvedStatus == 1) {
                submittedUTCTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
            }
            if (
                row.approvedStatus == 2 ||
                row.approvedStatus == 3 ||
                row.approvedStatus == 4
            ) {
                approvedBy = userId

                approvedUTCTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
            }

            const approverDetail = {
                EXP_REPORT_ID: row.expReportId,
                SUBMITTED_BY: userId,
                SUBMITTED_TO: subMittedTo.APPROVER_ID,
                SUBMITTED_DATE: submittedUTCTime,
                APPROVED_BY: approvedBy,
                APPROVED_DATE: approvedUTCTime,
                APPROVED_STATUS: row.approvedStatus,
                COMMENTS: row.comments
            }
            await models.EXPENSE_APPROVAL_HISTORY.create(approverDetail)
        })

        return res.status(200).json('success')
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const getApprover = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const userId = req.session.userId
        const approver = await models.EXPENSEAPPROVER.findOne({
            attributes: ['APPROVER_ID'],
            where: {
                USER_ID: userId
            }
        })

        const data = {
            approverId: approver.APPROVER_ID
        }

        return res.status(200).json(data)
    } catch (error) {
        next(AppError.internal())
    }
}

const getExpenseSummary = async (req, res, next) => {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    try {
        const userId = req.session.userId
        const { createType, createdFor } = req.query
        let where = {}
        const [allowApproversToView] = await allowApproversViewExpense(req)

        const isAllowAccess = allowApproversToView
            ? allowApproversToView[0].APP_PARAM_STATUS
            : 0
        let expCondition

        if (isAllowAccess) {
            expCondition = {
                [Op.or]: [
                    { EXP_CREATED_FOR: userId },
                    { INS_USER: userId },
                    {
                        EXP_CREATED_FOR: {
                            [Op.in]: Sequelize.literal(
                                `(SELECT USER_ID FROM EXP_APPROVER WHERE APPROVER_ID = ${userId} AND EXP_STATUS IN (2, 3, 4))`
                            )
                        }
                    }
                ]
            }
        } else {
            {
                expCondition = {
                    [Op.or]: [
                        { EXP_CREATED_FOR: userId },
                        { INS_USER: userId },
                        {
                            EXP_REPORT_ID: {
                                [Op.in]: Sequelize.literal(
                                    `(SELECT EXP_REPORT_ID FROM EXP_APPROVAL_HISTORY WHERE APPROVED_BY = ${userId} AND APPROVED_STATUS IN (2, 3, 4))`
                                )
                            }
                        }
                    ]
                }
            }
        }

        switch (createType) {
            case 'self':
                where.INS_USER = { [Op.eq]: userId }
                break
            case 'other':
                where.EXP_CREATED_FOR = { [Op.eq]: createdFor }
                break
            default:
                where = expCondition
                break
        }
        const open = await models.EXPENSE_REPORT.count({
            where: {
                [Op.and]: where,
                EXP_STATUS: 0
            }
        })

        const submitted = await models.EXPENSE_REPORT.count({
            where: { [Op.and]: where, EXP_STATUS: 1 }
        })

        const approved = await models.EXPENSE_REPORT.count({
            where: { [Op.and]: where, EXP_STATUS: 2 }
        })

        const rejected = await models.EXPENSE_REPORT.count({
            where: { [Op.and]: where, EXP_STATUS: 3 }
        })

        const paid = await models.EXPENSE_REPORT.count({
            where: { [Op.and]: where, EXP_STATUS: 4 }
        })

        const data = [
            {
                open: open,
                submitted: submitted,
                approved: approved,
                paid: paid,
                rejected: rejected
            }
        ]

        return await res.status(200).json(data)
    } catch (error) {
        next(AppError.internal())
    }
}

const allowApproversViewExpense = async (req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(
        `SELECT APP_PARAM_STATUS FROM CUSTOM_APP_PARAMS WHERE APP_PARAM_ID='ALLOW_APPROVERS_VIEW_EXPENSE'`
    )
}

const getApproverHistory = async (req, res, next) => {
    try {
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        const { id } = req.params

        const query = `SELECT distinct u.USER_NAME as submittedBy,
         ua.USER_NAME as submittedTo,
         up.USER_NAME as approvedBy, 
         date_format(ah.SUBMITTED_DATE,'%Y-%m-%dT%T%Z') as submittedDate,
         date_format(ah.APPROVED_DATE,'%Y-%m-%dT%T%Z') as approvedDate,
         case 
         when ah.APPROVED_STATUS = 1 then 'SUBMITTED'
         when ah.APPROVED_STATUS = 2 then 'APPROVED'
         when ah.APPROVED_STATUS = 3 then 'REJECTED'
         when ah.APPROVED_STATUS = 4 then 'PAID'
         else 'OPEN' end as status,
         ah.COMMENTS as comments
          FROM EXP_APPROVAL_HISTORY ah
        left join USERS u on ah.SUBMITTED_BY=u.USER_ID
        left join USERS ua on ah.SUBMITTED_TO = ua.USER_ID
        left join USERS up on ah.APPROVED_BY=up.USER_ID
        where ah.EXP_REPORT_ID = ? `

        const data = await sequelize.query(query, [id])

        res.status(200).json(data)
    } catch (error) {
        next(AppError.internal())
    }
}

const enabledAJExpense = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    try {
        const [result] =
            await sequelize.query(`SELECT APP_PARAM_STATUS AS status FROM CUSTOM_APP_PARAMS
        WHERE APP_PARAM_ID='ENABLE_AJ_EXPENSE'`)

        const [data] =
            await sequelize.query(`SELECT count(*) as status FROM USER_MENU WHERE USRMENU_MENU_ID=80 and 
            USRMENU_USER_ID=${req.session.userId}`)

        let status = 0
        const enableAJ = result.length > 0 ? result[0].status : 0
        const menuId = data.length > 0 ? data[0].status : 0
        if (enableAJ == 1 && menuId > 0) {
            status = 1
        }

        return res.status(200).json(status)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

module.exports = {
    addApprover,
    sendForApproval,
    getApprover,
    getExpenseSummary,
    getApproverHistory,
    submitValidation,
    enabledAJExpense,
    linkToExpenseReportSubmitValidation,
    linkExpenseSubmitValidation
}
