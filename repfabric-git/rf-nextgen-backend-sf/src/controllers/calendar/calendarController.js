const dayjs = require('dayjs')
const utc = require('dayjs/plugin/utc')
const logger = require('../../utils/logger')

const addCalendar = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        dayjs.extend(utc)

        const row = req.body

        const eventId = await GenerateId('EVENT', req)

        const calendarDetails = {
            EVENT_ID: eventId,
            EVENT_USER_ID: row.userId,
            EVENT_START_DATE: dayjs(row.medicalScheduled).format(
                'YYYY-MM-DD HH:mm:ss'
            ),
            EVENT_END_DATE: dayjs(row.medicalScheduledTo).format(
                'YYYY-MM-DD HH:mm:ss'
            ),
            EVENT_TITLE: row.caseName,
            EVENT_TYPE: 1,
            EVENT_ACCOUNT: 'Medical',
            EVENT_DESC: row.description,
            EVENT_VISIBLITY: 1,
            EVENT_LOCATION: row.location
        }
        await models.CALENDAR.create(calendarDetails)
        return res.status(200).json(eventId)
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const updateCalendar = async (req, res, next) => {
    try {
        const { id } = req.params
        const row = req.body

        const calendarDetails = {
            EVENT_USER_ID: row.userId,
            EVENT_START_DATE: dayjs(row.medicalScheduled).format(
                'YYYY-MM-DD HH:mm:ss'
            ),
            EVENT_END_DATE: dayjs(row.medicalScheduledTo).format(
                'YYYY-MM-DD HH:mm:ss'
            ),
            EVENT_TITLE: row.caseName,
            EVENT_DESC: row.description,
            EVENT_LOCATION: row.location
        }

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.CALENDAR.update(calendarDetails, {
            where: {
                EVENT_ID: id
            }
        })

        return res.status(200).json('Success')
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const validateSalesPerson = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const { id, fromDate, toDate } = req.query
        const field = 'EVENT_START_DATE'
        const field1 = 'EVENT_END_DATE'
        const value = fromDate
        const value1 = toDate

        if (id && fromDate && toDate) {
            const where = `(:value1 > ${field} AND :value < ${field1})
            OR (:value < ${field1} AND :value1 > ${field})
            OR (:value < ${field} AND :value1 > ${field1})`

            const result = await sequelize.query(
                `SELECT REC_ID FROM CALENDAR WHERE EVENT_USER_ID = :id AND (${where})`,
                {
                    replacements: {
                        id: id,
                        value: value,
                        value1: value1
                    },
                    type: Sequelize.QueryTypes.SELECT
                }
            )
            if (result.length > 0) {
                return res.status(200).json(1)
            } else {
                return res.status(200).json(0)
            }
        } else {
            next(
                AppError.badRequest(
                    'Query parameter id,fromDate & todate are mandatory'
                )
            )
        }
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const deleteCalendar = async (req, res, next) => {
    try {
        const id = req.params.id

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.CALENDAR.destroy({
            where: {
                EVENT_ID: id
            }
        })
        return res.status(200).json('success')
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

module.exports = {
    addCalendar,
    updateCalendar,
    validateSalesPerson,
    deleteCalendar
}
