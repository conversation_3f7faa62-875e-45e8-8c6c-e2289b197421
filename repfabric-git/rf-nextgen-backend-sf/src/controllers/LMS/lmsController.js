const crypto = require('crypto')
const { sequelize } = require('../../models')
const axios = require('axios')
const jwt = require('jsonwebtoken')
const algorithm = 'aes-256-ctr'
const secretKey = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3'
const logger = require('../../utils/logger')

const LMSSO = async (req, res, next) => {
    try {
        const userId = req.session.userId
        const subTag = req.query.subTag
        const [services] = await getService(req)

        if (services.length == 0) {
            next(AppError.notFound('Company not found'))
            return
        }

        const [email] = await getUserEmail(userId, req)

        const company = services[0].SERVICE_KEY

        req.query.localEncrypt = true
        req.query.lmsType = 'LMSSSO'
        const key = await aesDecrypt(req, res, next)

        req.query.localEncrypt = true
        req.query.lmsType = 'LMSAPI360'
        const apiKey = await aesDecrypt(req, res, next)

        if (email.length == 0) {
            return res.status(200).json('https://repfabric.360learning.com')
        }

        const emailId = email[0].USER_EMAIL_LOGIN
        //const userName = email[0].USER_NAME

        if (emailId == null || emailId == '') {
            return res.status(200).json('https://repfabric.360learning.com')
        }
        await axios
            .get(
                `https://repfabric.360learning.com/api/v1/users/${emailId}?company=${company}&apiKey=${apiKey}`,
                {
                    responseType: 'json'
                }
            )
            .then(async () => {
                const token = jwt.sign(
                    {
                        email: emailId
                        //firstName: userName
                    },
                    key,
                    { expiresIn: 60 }
                )
                const centralHostURL = process.env.CENTRAL_HOST_URL
                await axios
                    .get(`${centralHostURL}/lms_links?sub_tag=${subTag}`, {
                        responseType: 'json',
                        // withCredentials: true,
                        auth: {
                            username: `VP882-UT29QZ48JN`
                        }
                    })
                    .then(async (data) => {
                        if (
                            data.data['lms-url'] &&
                            data.data['lms-url'] != ''
                        ) {
                            let originalURL =
                                data.data['lms-url'] + '&jwt=' + token
                            let modifiedURL = originalURL.replace(
                                '?q=',
                                '?q=%20'
                            )
                            modifiedURL = modifiedURL.replace('/&jwt=', '?jwt=')

                            return res.status(200).json(modifiedURL)
                        } else {
                            return res
                                .status(200)
                                .json(
                                    'https://repfabric.360learning.com?&jwt=' +
                                        token
                                )
                        }
                    })
                    .catch(async () => {
                        return res
                            .status(200)
                            .json(
                                'https://repfabric.360learning.com?&jwt=' +
                                    token
                            )
                    })
            })
            .catch(async () => {
                return res.status(200).json('https://repfabric.360learning.com')
            })
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

async function passwordDecryption(userId, req) {
    let hex = '',
        result

    // Use the tenant-specific Sequelize instance    
    const sequelize = req.tenantSequelize;

    const [encryptedPswd] = await sequelize.query(
        'SELECT USER_PASSWORD AS password FROM USERS WHERE USER_ID=$userId',
        {
            bind: { userId }
        }
    )
    const pass = encryptedPswd[0].password

    for (let i = 0; i < pass.length; i += 2) {
        result = pass.substr(i, 2)

        hex += String.fromCharCode(parseInt(result, 16) - 69 - i)
    }
    return hex
}

const aesEncrypt = async (req, res, next) => {
    try {
        const row = req.body

        const [services] = await getService(req)

        if (services.length == 0) {
            next(AppError.notFound('Company not found'))
            return
        }
        const iv = crypto.randomBytes(16)

        const cipher = crypto.createCipheriv(algorithm, secretKey, iv)

        const encrypted = Buffer.concat([
            cipher.update(row.key),
            cipher.final()
        ])
        const encryptedValue = encrypted.toString('hex')
        const ivVal = iv.toString('hex')

        let where = {}
        if (row.lmsType == 'LMSSSO') {
            where = `SERVICE_ID='LMSSSO'`
        } else if (row.lmsType == 'LMSAPI360') {
            where = `SERVICE_ID='LMSAPI360'`
        } else {
            next(AppError.notFound('Invalid LMS Type'))
            return
        }

        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        await sequelize.query(
            `UPDATE SERVICES SET SERVICE_SECRET=$encryptedValue, SERVICE_IV=$ivVal
         where ${where} `,
            { bind: { encryptedValue, ivVal } }
        )

        return res.status(200).json({
            iv: iv.toString('hex'),
            content: encrypted.toString('hex')
        })
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const aesDecrypt = async (req, res, next) => {
    const row = req.query

    let where = {}
    if (row.lmsType == 'LMSSSO') {
        where = `SERVICE_ID='LMSSSO'`
    } else if (row.lmsType == 'LMSAPI360') {
        where = `SERVICE_ID='LMSAPI360'`
    } else {
        next(AppError.notFound('Invalid LMS Type'))
        return
    }

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const [services] =
        await sequelize.query(`SELECT SERVICE_KEY, SERVICE_SECRET, SERVICE_IV from SERVICES
    WHERE ${where} `)

    if (services.length == 0) {
        next(AppError.notFound('Company not found'))
        return
    }

    const decipher = crypto.createDecipheriv(
        algorithm,
        'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3',
        Buffer.from(services[0].SERVICE_IV, 'hex')
    )

    const decrpyted = Buffer.concat([
        decipher.update(Buffer.from(services[0].SERVICE_SECRET, 'hex')),

        decipher.final()
    ])

    if (req.query.localEncrypt == undefined) {
        return res.status(200).json(decrpyted.toString())
    } else {
        return decrpyted.toString()
    }
}

async function getService(req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(`SELECT SERVICE_KEY, SERVICE_SECRET, SERVICE_IV from SERVICES
    WHERE SERVICE_ID='LMSSSO' `)
}

async function getUserEmail(userId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(
        `SELECT U.USER_NAME, E.USER_EMAIL_LOGIN
         FROM USER_EMAIL_CONFIG E
        JOIN USERS U ON E.USER_EMAIL_USER_ID = U.USER_ID
        WHERE U.USER_ID=$userId`,
        {
            bind: { userId }
        }
    )
}

const updateUser = async (req, res, next) => {
    try {
        const { emailId, id } = req.params
        const userId = id

        const [services] = await getService(req)
        const company = services[0].SERVICE_KEY
        const [email] = await getUserEmail(userId, req)

        let params
        let firstName = ''
        let lastName = ''
        const fullName = email[0].USER_NAME.split(' ')
        const password = await passwordDecryption(userId, req)
        if (fullName.length > 1) {
            firstName = fullName[0]
            lastName = fullName[1]
        } else {
            firstName = email[0].USER_NAME
        }
        if (emailId == email[0].USER_EMAIL_LOGIN) {
            params = {
                firstName: firstName,
                lastName: lastName
                //password: await passwordDecryption(userId, req),
            }
        } else {
            params = {
                mail: email[0].USER_EMAIL_LOGIN,
                firstName: firstName,
                lastName: lastName
                //password: await passwordDecryption(userId, req),
            }
        }
        if (password) {
            params.password = password
        }

        req.query.localEncrypt = true
        req.query.lmsType = 'LMSAPI360'
        const apiKey = await aesDecrypt(req, res, next)

        await axios
            .put(
                `https://repfabric.360learning.com/api/v1/users/${emailId}?company=${company}&apiKey=${apiKey}`,
                params,
                {
                    sendCredentials: false,
                    responseType: 'application/json'
                }
            )
            .then((result) => {
                return res.status(200).json(result.data)
            })
            .catch((error) => {
                logger.error(error)
                next(AppError.internal())
            })
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

const deleteUser = async (req, res, next) => {
    try {
        const { emailId } = req.params
        const [services] = await getService(req)
        const company = services[0].SERVICE_KEY

        req.query.localEncrypt = true
        req.query.lmsType = 'LMSAPI360'
        const apiKey = await aesDecrypt(req, res, next)

        await axios
            .delete(
                `https://repfabric.360learning.com/api/v1/users/${emailId}?company=${company}&apiKey=${apiKey}`
            )
            .then(() => {
                return res.status(200).json('Success')
            })
            .catch((error) => {
                next(
                    AppError.internal('User not found (' + error.message + ')')
                )
            })
    } catch (error) {
        logger.error(error)
        next(AppError.internal(error))
    }
}

const create = async (req, res, next) => {
    try {
        const row = req.body
        let userId

        if (row.userId == undefined) {
            userId = req.session.userId
        } else {
            userId = row.userId
        }

        const [services] = await getService(req)

        if (services.length == 0) {
            next(AppError.notFound('Company not found'))
            return
        }

        const [email] = await getUserEmail(userId, req)

        const company = services[0].SERVICE_KEY

        req.query.localEncrypt = true
        req.query.lmsType = 'LMSAPI360'
        const apiKey = await aesDecrypt(req, res, next)

        if (email.length == 0) {
            next(AppError.notFound('Email configuration is not set'))
            return
        }

        const emailId = email[0].USER_EMAIL_LOGIN
        const userName = email[0].USER_NAME
        const password = await passwordDecryption(userId, req)

        let firstName = ''
        let lastName = ''
        const fullName = userName.split(' ')
        if (fullName.length > 1) {
            firstName = fullName[0]
            lastName = fullName[1]
        } else {
            firstName = userName
        }
        if (emailId == null || emailId == '') {
            next(AppError.notFound('Email configuration is not set'))
            return
        }
        axios
            .post(
                `https://repfabric.360learning.com/api/v1/users?company=${company}&apiKey=${apiKey}`,
                {
                    mail: emailId,
                    password: password,
                    firstName: firstName,
                    lastName: lastName
                }
            )
            .then((result) => {
                return res.status(200).json(result.data)
            })
            .catch(async (error) => {
                next(AppError.internal(error))
            })
    } catch (error) {
        next(AppError.internal())
    }
}

const getUser = async (req, res, next) => {
    try {
        const { emailId } = req.params

        const [services] = await getService(req)

        if (services.length == 0) {
            next(AppError.notFound('Company not found'))
            return
        }

        const company = services[0].SERVICE_KEY

        req.query.localEncrypt = true
        req.query.lmsType = 'LMSAPI360'
        const apiKey = await aesDecrypt(req, res, next)

        axios
            .get(
                `https://repfabric.360learning.com/api/v1/users/${emailId}?company=${company}&apiKey=${apiKey}`
            )
            .then((result) => {
                return res.status(200).json(result.data)
            })
            .catch(async (error) => {
                if (error.response.status == 400) {
                    next(AppError.notFound(error.response.data))
                } else {
                    next(AppError.internal())
                }
            })
    } catch (error) {
        logger.error(error)
        next(AppError.internal())
    }
}

module.exports = {
    LMSSO,
    aesEncrypt,
    aesDecrypt,
    updateUser,
    deleteUser,
    create,
    getUser
}
