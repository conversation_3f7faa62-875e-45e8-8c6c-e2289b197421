const { sequelize } = require('../../models')
const dayjs = require('dayjs')
const logger = require('../../utils/logger')

const emailContacts = async (req, res, next) => {
    try {
        let row = req.body
        logger.info(
            'time ' +
                dayjs().format('YYYY-MM-DD HH:mm:ss') +
                ' email ids ' +
                row
        )

        if (row.emailIds === undefined) {
            return res.status(422).json({
                status: 'error',
                message: 'Email Ids are missing'
            })
        }

        const emailIds = row.emailIds
        const response = await getEmailIdDetails(emailIds, req)
        const transformedData = response.map((item) => {
            if (item.ContactId === null && item.UserId === null) {
                const replacement = {
                    EmailId: item.EmailId,
                    ContactId: item.ContactId,
                    UserId: item.UserId
                }
                return replacement
            } else if (item.ContactId === null) {
                const replacement = {
                    EmailId: item.EmailId,
                    ContactId: item.ContactId,
                    UserId: item.UserId,
                    IsPrivateEmail: item.IsPrivateEmail,
                    IsOwner: item.IsOwner
                }
                return replacement
            } else if (item.UserId === null) {
                const replacement = {
                    EmailId: item.EmailId,
                    ContactId: item.ContactId,
                    CompanyId: item.CompanyId,
                    CompanyTypeId: item.CompanyTypeId,
                    SalesTeamId: item.SalesTeamId,
                    PrivateTeamId: item.PrivateTeamId,
                    UserId: item.UserId
                }
                return replacement
            }
            return item
        })
        return res.status(200).json(transformedData)
    } catch (error) {
        next(AppError.internal(error.message))
    }
}

async function getEmailIdDetails(emails, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const emailList = emails
        .map((email) => `SELECT '${email}' AS EmailId`)
        .join(' UNION ALL ')
    let query = `
                WITH EmailList AS (
                    ${emailList}
                ),
                RecentContacts AS (
                    SELECT 
                        EL.EmailId,
                        CONT.CONT_ID AS ContactId,
                        CONT.CONT_COMP_ID AS CompanyId,
                        CASE 
                            WHEN COMP.COMP_TAGGED = 1 THEN 2
                            ELSE COMP.COMP_TYPE
                        END AS CompanyTypeId,
                        COMP.COMP_SMAN_ID AS SalesTeamId,
                        COMP.COMP_PRIV_TEAM AS PrivateTeamId,
                        ROW_NUMBER() OVER (
                            PARTITION BY EL.EmailId
                            ORDER BY CONT.INS_DATE DESC
                        ) AS RowNum
                    FROM EmailList EL
                    LEFT JOIN CONTACTS CONT 
                        ON EL.EmailId IN (
                            CONT.CONT_EMAIL_BUSINESS, 
                            CONT.CONT_EMAIL_BUSINESS2, 
                            CONT.CONT_EMAIL_ALTERNATE
                        )
                    LEFT JOIN COMPANIES COMP 
                        ON CONT.CONT_COMP_ID = COMP.COMP_ID
                ),
                FilteredContacts AS (
                    SELECT *
                    FROM RecentContacts
                    WHERE RowNum = 1
                ),
                RecentUsers AS (
                    SELECT 
                        EL.EmailId,
                        NULL AS ContactId,
                        NULL AS CompanyId,
                        NULL AS CompanyTypeId,
                        NULL AS SalesTeamId,
                        NULL AS PrivateTeamId,
                        U.USER_ID AS UserId,
                        CASE 
                            WHEN U.USER_PRIVATE_EMAIL_FLAG = 1 THEN 'True'
                            ELSE 'False'
                        END AS IsPrivateEmail,
                        CASE 
                            WHEN U.USER_CATEGORY = 1 THEN 'True'
                            ELSE 'False'
                        END AS IsOwner,
                        ROW_NUMBER() OVER (
                            PARTITION BY EL.EmailId
                            ORDER BY UC.USER_EMAIL_USER_ID ASC
                        ) AS RowNum,
                        UC.USER_EMAIL_USER_ID AS UserEmailUserId
                    FROM EmailList EL
                    LEFT JOIN USER_EMAIL_CONFIG UC 
                        ON EL.EmailId = UC.USER_EMAIL_LOGIN
                    LEFT JOIN USERS U 
                        ON UC.USER_EMAIL_USER_ID = U.USER_ID
                ),
                FilteredUsers AS (
                    SELECT *
                    FROM RecentUsers
                    WHERE RowNum = 1
                ),
                CombinedResults AS (
                    SELECT 
                        FC.EmailId,
                        FC.ContactId,
                        FC.CompanyId,
                        FC.CompanyTypeId,
                        FC.SalesTeamId,
                        FC.PrivateTeamId,
                        FU.UserId,
                        FU.IsPrivateEmail,
                        FU.IsOwner,
                        FU.UserEmailUserId
                    FROM FilteredContacts FC
                    LEFT JOIN FilteredUsers FU 
                        ON FC.EmailId = FU.EmailId

                    UNION ALL

                    SELECT 
                        FU.EmailId,
                        NULL AS ContactId,
                        NULL AS CompanyId,
                        NULL AS CompanyTypeId,
                        NULL AS SalesTeamId,
                        NULL AS PrivateTeamId,
                        FU.UserId,
                        FU.IsPrivateEmail,
                        FU.IsOwner,
                        FU.UserEmailUserId
                    FROM FilteredUsers FU
                    WHERE FU.EmailId NOT IN (
                        SELECT EmailId FROM FilteredContacts
                    )
                )
                SELECT DISTINCT 
                    EmailId,
                    ContactId,
                    CompanyId,
                    CompanyTypeId,
                    SalesTeamId,
                    PrivateTeamId,
                    UserId,
                    IsPrivateEmail,
                    IsOwner
                FROM CombinedResults
                ORDER BY UserEmailUserId ASC`

    const [result] = await sequelize.query(query)
    return result
}

module.exports = {
    emailContacts
}
