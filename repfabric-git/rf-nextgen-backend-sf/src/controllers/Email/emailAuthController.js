const { sequelize } = require('../../models')
const emailAuth = async (req, res) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let row = req.body

        const authheader = req.headers.authorization
        const auth = Buffer.from(authheader.split(' ')[1], 'base64')
            .toString()
            .split(':')
        const token = auth[0]
        const password = auth[1]

        let syncTokenWhere = `AND ACCT_NAME ='Yoxel' AND ACCT_ACCESS_TOKEN = '${token}' LIMIT 1`
        const syncTokenResult = await requestQuery(
            'SYNC_ACCT_DTLS',
            syncTokenWhere,
            req
        )

        const servicesWhere = `AND SERVICE_ID='$$EMLSTRNGLR' AND SERVICE_KEY = '${password}'`
        const servicesResult = await requestQuery(
            'SERVICES',
            servicesWhere,
            req
        )

        if (syncTokenResult.length === 0 || servicesResult.length === 0 || !authheader) {
            return res.status(401).json({
                status: 'error',
                message: 'Invalid Token'
            })
        }

        if (row.tenantId === undefined) {
            return res.status(422).json({
                status: 'error',
                message: 'Tenant Id is missing'
            })
        }

        if (row.userId === undefined) {
            return res.status(422).json({
                status: 'error',
                message: 'User Id is missing'
            })
        }

        let teanatIdWhere = `AND PARAM_SUBS_ID = ${row.tenantId} LIMIT 1`
        const tenantIdResult = await requestQuery('PARAMS', teanatIdWhere, req)
        if (tenantIdResult.length === 0) {
            return res.status(401).json({
                status: 'error',
                message: 'Tenant ID does not match'
            })
        }

        const userIdResult = await userExist(row.userId, req)
        if (
            userIdResult.length === 0 ||
            row.userId != syncTokenResult[0].USER_ID
        ) {
            return res.status(401).json({
                status: 'error',
                message: 'Invalid User'
            })
        }

        if (
            tenantIdResult.length > 0 &&
            syncTokenResult.length > 0 &&
            userIdResult.length > 0
        ) {
            let query = `SELECT USER_ACCESS_TOKEN FROM USER_EMAIL_CONFIG WHERE USER_EMAIL_USER_ID = ${row.userId}`
            const [result] = await sequelize.query(query)
            let userAccessToken =
                result.length > 0 ? result[0].USER_ACCESS_TOKEN : ''

            return res.status(200).json({
                status: 'success',
                message: 'Authentication successful',
                tenantId: row.tenantId,
                userId: row.userId,
                accessToken: userAccessToken
            })
        }
    } catch (error) {
        return res.status(500).json({
            status: 'error',
            message: 'Internal server error'
        })
    }
}

async function requestQuery(tableName, where, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let query = `SELECT * FROM ${tableName} WHERE 1 ${where}`
    const [result] = await sequelize.query(query)
    return result
}

async function userExist(uId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let query = `SELECT S.* FROM SYNC_ACCT_DTLS S JOIN USERS U ON S.USER_ID = U.USER_ID WHERE 1 AND S.ACCT_NAME ='Yoxel' AND S.USER_ID = ${uId} LIMIT 1`
    const [result] = await sequelize.query(query)
    return result
}

module.exports = {
    emailAuth
}
