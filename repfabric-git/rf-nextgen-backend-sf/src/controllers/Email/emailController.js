const { sequelize } = require('../../models')
const dayjs = require('dayjs')
const upload = require('../../utils/uploadEmailAttachment')
const singleUpload = upload.array('image')
const { s3 } = require('../../helpers/index')
const { simpleParser } = require('mailparser')
const fs = require('fs')
const path = require('path')
const logger = require('../../utils/logger')
const axios = require('axios')

const downloadEmail = async (req, res, next) => {
    let transaction = null

    // Use the tenant-specific Sequelize instance for transaction
    const sequelize = req.tenantSequelize;
    transaction = await sequelize.transaction();

    let attachment_folder = null
    let time1 = ''
    let date, time
    let user_id = req.session.userId
    try {
        let row = req.body
        // logger.info(
        //     'time ' +
        //         dayjs().format('YYYY-MM-DD HH:mm:ss') +
        //         ' email post ' +
        //         JSON.stringify(row)
        // )
        if (typeof row.msgId == 'undefined') {
            return res.status(501).json({
                error: 'MsgId is required'
            })
        }
        if (typeof row.folder == 'undefined') {
            return res.status(501).json({
                error: 'Folder top is required'
            })
        }
        if (typeof row.fromAddress == 'undefined') {
            return res.status(501).json({
                error: 'From is required'
            })
        }
        if (typeof row.sentAt == 'undefined') {
            if (typeof row.messageDate == 'undefined') {
                if (typeof row.messageDate == 'undefined') {
                    return res.status(501).json({
                        error: 'SentAt is required'
                    })
                }
            } else {
                row.sentAt = row.messageDate
            }
        } else if (typeof row.messageDate == 'undefined') {
            row.messageDate = row.sentAt
        }

        if (typeof row.receivedAt == 'undefined') {
            if (typeof row.messageDate == 'undefined') {
                return res.status(501).json({
                    error: 'ReceivedAt is required'
                })
            } else {
                row.receivedAt = row.messageDate
            }
        }

        //Handled for task 14501
        if (typeof row.htmlBody != 'undefined') {
            if (/<img[^>]+>/i.test(row.htmlBody)) {
                if (typeof row.attachments == 'undefined') {
                    return res.status(422).json({
                        error: 'Inline attachment is missing'
                    })
                }
            }
        }

        let is_chat = row.folder == 'chat' ? 1 : 0
        let is_draft = row.folder == 'draft' ? 1 : 0

        if (!is_chat && !is_draft) {
            let email_from = row.fromAddress
            let folder = row.folder
            if (folder == 'sent') {
                date = dayjs.utc(row.messageDate).format('YYYY-MM-DD')
                time = dayjs.utc(row.messageDate).format('HH:mm:ss')
                time1 = dayjs.utc(row.receivedAt).format('HH:mm:ss')
            } else {
                date = dayjs.utc(row.receivedAt).format('YYYY-MM-DD')
                time = dayjs.utc(row.receivedAt).format('HH:mm:ss')
            }
            let opp_id = 0
            let po_id = 0
            let quote_id = 0
            let job_id = 0
            let plan_id = 0

            if (typeof row.relatedTo != 'undefined') {
                opp_id =
                    typeof row.relatedTo.opportunityId != 'undefined'
                        ? await checkIdExist(
                            row.relatedTo.opportunityId,
                            'OPPORTUNITIES',
                            res,
                            req
                        )
                        : 0
                po_id =
                    typeof row.relatedTo.purchaseOrderId != 'undefined'
                        ? await checkIdExist(
                            row.relatedTo.purchaseOrderId,
                            'PO_HDR',
                            res,
                            req
                        )
                        : 0
                quote_id =
                    typeof row.relatedTo.quoteId != 'undefined'
                        ? await checkIdExist(
                            row.relatedTo.quoteId,
                            'QUOTES_HDR',
                            res,
                            req
                        )
                        : 0
                job_id =
                    typeof row.relatedTo.jobId != 'undefined'
                        ? await checkIdExist(row.relatedTo.jobId, 'JOBS', res, req)
                        : 0
                plan_id =
                    typeof row.relatedTo.planId != 'undefined'
                        ? await checkIdExist(
                            row.relatedTo.planId,
                            'PLANNER',
                            res,
                            req
                        )
                        : 0
            }

            let email_data = await emailDataManagement(row, req)

            if (
                (await isEmailExist(
                    email_from,
                    date,
                    time,
                    replaceFromMsgId(row.messageIdentifier),
                    row.msgId,
                    folder,
                    row.subject,
                    time1,
                    email_data.to_address,
                    user_id,
                    req
                )) == 0
            ) {
                let email_header = await insertEmail(
                    email_data,
                    attachment_folder,
                    opp_id,
                    job_id,
                    po_id,
                    quote_id,
                    plan_id,
                    user_id,
                    req
                )

                // Use the tenant-specific models for all DB operations in a multi-tenant environment
                const models = req.tenantModels;

                const inserted = await models.EMAIL_HEADER.create(email_header)
                const htmlContent =
                    typeof row.htmlBody != 'undefined'
                        ? row.htmlBody
                        : row.textBody
                if (inserted) {
                    if (typeof htmlContent != 'undefined') {
                        await insertUpdateContent(
                            email_header.EMAIL_ID,
                            row.attachments,
                            htmlContent,
                            req
                        )
                    }
                    if (typeof row.attachments != 'undefined') {
                        downloadAttachmentFromAurinko(
                            row.attachments,
                            row.msgId,
                            user_id,
                            email_header.EMAIL_ID,
                            req
                        )
                    }
                    let to_address = getEmailList(row.toAddress)
                    let cc_address = getEmailList(row.ccAddress)
                    let bcc_address = getEmailList(row.bccAddress)
                    let from_addr = Array.from([email_from]) //converting into array
                    if (folder == 'inbox') {
                        const addresses = [
                            ...new Set([...cc_address, ...from_addr])
                        ]
                        if (opp_id != 0) {
                            updateContact(addresses, opp_id, 'OPP_CONTACTS', req)
                        }
                        if (job_id != 0) {
                            updateContact(addresses, job_id, 'JOB_CONTACTS', req)
                        }
                        await updateToContactEmails(
                            date,
                            time,
                            email_from,
                            to_address,
                            cc_address,
                            email_data.email_id,
                            email_data.subject,
                            email_data.folder,
                            user_id,
                            req
                        )
                        await enablePrivFlg(
                            user_id,
                            email_from,
                            email_data.email_id,
                            to_address,
                            cc_address,
                            bcc_address,
                            req
                        )
                    } else {
                        await setPrivPropSentMail(
                            to_address,
                            email_data.email_id,
                            user_id,
                            email_from,
                            cc_address,
                            bcc_address,
                            req
                        )
                        await updateToContactEmails(
                            date,
                            time,
                            email_from,
                            to_address,
                            cc_address,
                            email_data.email_id,
                            email_data.subject,
                            email_data.folder,
                            user_id,
                            req
                        )
                    }
                    if(opp_id == 0){
                        await autoAssociateBusinessObjects('EMAIL_OPP_ID', email_data.thread_id, email_data.reference_id, email_data.email_id, email_data.parent_id, req);
                    }
                    
                    if(po_id == 0){
                        await autoAssociateBusinessObjects('EMAIL_PO_ID', email_data.thread_id, email_data.reference_id, email_data.email_id, email_data.parent_id, req);
                    }

                    if(quote_id == 0){
                        await autoAssociateBusinessObjects('EMAIL_QUOTE_ID', email_data.thread_id, email_data.reference_id, email_data.email_id, email_data.parent_id, req);
                    }

                    if(job_id == 0){
                        await autoAssociateBusinessObjects('EMAIL_JOB_ID', email_data.thread_id, email_data.reference_id, email_data.email_id, email_data.parent_id, req);
                    }

                } else {
                    logger.info(
                        'Failed to insert ' +
                        dayjs().format('YYYY-MM-DD HH:mm:ss') +
                        ' email post ' +
                        JSON.stringify(row)
                    )
                }
                let response = {
                    emailId: email_header.EMAIL_ID,
                    msgId: row.msgId,
                    subject: row.subject
                }
                return res.status(200).json(response)
            } else if (folder == 'sent' || folder == 'inbox') {
                //updated the logic based on discussion with mangaer
                let reference_id = row.referenceId
                    ? replaceFromMsgId(getFirstParent(row.referenceId))
                    : ''
                let parent_id = row.rfc822MsgId
                    ? replaceFromMsgId(row.rfc822MsgId)
                    : ''
                let updatedData = await updateEmailHeaderForSentEmail(
                    email_from,
                    date,
                    row.subject,
                    replaceFromMsgId(row.messageIdentifier),
                    reference_id,
                    user_id,
                    0,
                    row.msgId,
                    parent_id,
                    row.syncThreadId,
                    opp_id,
                    job_id,
                    quote_id,
                    po_id,
                    plan_id,
                    folder,
                    req
                )

                if (updatedData != undefined) {
                    if (updatedData.EMAIL_ID === null) {
                        logger.info(
                            'Failed to update ' +
                            dayjs().format('YYYY-MM-DD HH:mm:ss') +
                            ' email post ' +
                            JSON.stringify(row)
                        )
                    }
                    let response = {
                        emailId: updatedData.EMAIL_ID,
                        msgId: row.msgId,
                        subject: row.subject
                    }
                    return res.status(200).json(response)
                } else {
                    return res.status(400).json({
                        error: 'Failed to Update email'
                    })
                }
            } else {
                return res.status(400).json({
                    error: 'Failed to Update email'
                })
            }
        }
    } catch (error) {
        if (transaction) {
            await transaction.rollback()
        }
        next(AppError.internal(error.message))
    }
}

async function emailDataManagement(email) {
    let email_id = await GenerateId('EMAIL', req)
    let attach_flag = 0
    let reply_to_address = ''
    let to_address = ''
    let cc_address = ''
    let bcc_address = ''
    let date = ''
    let time = ''

    attach_flag = !email.hasAttachments ? 0 : 1

    const email_from = email.fromAddress
    let display_name =
        typeof email.fromName == 'undefined'
            ? email_from
            : email.fromName + '<' + email_from + '>'

    if (typeof email.toAddress != 'undefined') {
        to_address = formatEmailAddress(email.toAddress)
    }

    if (typeof email.ccAddress != 'undefined') {
        cc_address = formatEmailAddress(email.ccAddress)
    }

    if (typeof email.bccAddress != 'undefined') {
        bcc_address = formatEmailAddress(email.bccAddress)
    }

    if (typeof email.replyToEmailMessageId != 'undefined') {
        reply_to_address = formatEmailAddress(email.replyToEmailMessageId)
    }

    const folder = email.folder == 'sent' ? 'sent' : 'inbox'
    let email_body = email.textBody ? email.textBody : ''
    email_body = email_body.length > 500 ? email_body.slice(0, 500) : email_body
    const attachments = email.attachFlag

    if (folder == 'sent') {
        date = dayjs.utc(email.messageDate).format('YYYY-MM-DD')
        time = dayjs.utc(email.messageDate).format('HH:mm:ss')
    } else {
        date = dayjs.utc(email.receivedAt).format('YYYY-MM-DD')
        time = dayjs.utc(email.receivedAt).format('HH:mm:ss')
    }

    const email_data = {
        email_id: email_id,
        subject: email.subject,
        email_uid: email.msgId,
        thread_id: email.syncThreadId,
        date: date,
        time: time,
        has_attachments: attach_flag,
        to_address: to_address,
        cc_address: cc_address,
        bcc_address: bcc_address,
        reply_to_address: reply_to_address,
        from_address: email_from,
        email_size: 0,
        priority: 5,
        inet_message_id: email.messageIdentifier || '',
        reference_id: email.referenceId || '',
        parent_id: email.rfc822MsgId || '',
        display_name: display_name,
        folder: folder,
        content: email_body,
        attachments: attachments
    }
    return email_data
}

function formatEmailAddress(addresses) {
    const email_addresses = []
    let addr = ''

    addresses.forEach((address) => {
        if (address.name) {
            addr =
                address.name.indexOf('<') >= 0
                    ? address.name
                    : address.name + '<' + address.address + '>'
        } else {
            addr = address.address
        }
        email_addresses.push(addr)
    })

    const formatted_addresses = email_addresses.join(', ')
    return formatted_addresses
}

async function isEmailExist(
    from,
    date,
    time,
    message_id,
    uid,
    folder,
    subject,
    time1,
    to,
    user_id,
    req
) {
    let new_message_id = ''
    if (message_id == null) {
        new_message_id = dayjs.utc(date) + '_' + dayjs.utc(time) + '_' + uid
    } else {
        new_message_id = message_id
    }
    let count = 0

    let query =
        'SELECT count(*) row_count FROM EMAIL_HEADER WHERE EMAIL_USER_ID = ' +
        user_id +
        " AND EMAIL_FROM = '" +
        from +
        "' AND EMAIL_DATE = '" +
        date +
        "'"
    /*
    if (folder == 'inbox') {
        query =
            query +
            " AND EMAIL_MSG_ID = '" +
            new_message_id +
            "' AND EMAIL_FOLDER_TOP != 'sent'"
    } else {
        query =
            query +
            " AND EMAIL_TO = '" +
            to +
            "' AND EMAIL_SUBJECT LIKE '%" +
            subject +
            "' AND EMAIL_FOLDER_TOP = 'sent' AND EMAIL_UID = '" +
            uid +
            "'"
    }
    */
    query =
        query +
        " AND EMAIL_MSG_ID = '" +
        new_message_id +
        "' AND EMAIL_FOLDER_TOP = '" +
        folder +
        "'"
    
    // Use the tenant-specific Sequelize instance    
    const sequelize = req.tenantSequelize;

    const [result] = await sequelize.query(query)

    count = result[0].row_count
    return count
}

async function insertEmail(
    email_data,
    attachment_folder,
    opp_id,
    job_id,
    po_id,
    quote_id,
    plan_id,
    user_id,
    req
) {
    let from = email_data.from_address

    //Generate EMAIL_ID
    let email_id = email_data.email_id
    let private_team_id = getContactPrivTeam(from, req)
    let reference_id = replaceFromMsgId(getFirstParent(email_data.reference_id))
    let to_email = replaceQuotes(email_data.to_address.trim())
    let cc_email = replaceQuotes(email_data.cc_address.trim())
    let linked_flag =
        opp_id != 0 || job_id != 0 || po_id != 0 || quote_id != 0 || plan_id != 0 ? 1 : 0

    const email_header = {
        EMAIL_USER_ID: user_id,
        EMAIL_SUBJECT: email_data.subject,
        EMAIL_SIZE: '',
        EMAIL_DATE: email_data.date,
        EMAIL_FROM: from,
        EMAIL_UID: email_data.email_uid,
        EMAIL_THREAD_ID: email_data.thread_id,
        EMAIL_READ_FLAG: 0,
        EMAIL_TIME: email_data.time,
        EMAIL_FOLDER_TOP: email_data.folder,
        EMAIL_TO: to_email,
        EMAIL_ID: email_id,
        EMAIL_MSG_ID: replaceFromMsgId(email_data.inet_message_id),
        EMAIL_REF_ID: reference_id,
        EMAIL_PARENT_ID: replaceFromMsgId(email_data.parent_id),
        EMAIL_ATTACH_FLAG: email_data.has_attachments,
        EMAIL_CC: cc_email,
        EMAIL_BCC: replaceQuotes(email_data.bcc_address.trim()),
        EMAIL_DISPLAY_NAME: replaceQuotes(email_data.display_name.trim()),
        EMAIL_PRIV_TEAM: parseInt(private_team_id),
        EMAIL_ARCHIVE: 0,
        EMAIL_ARCHIVE_FOLDER: '',
        EMAIL_FOLDER_SUB: '',
        EMAIL_FOLLOWUP_FLAG: 0,
        EMAIL_CONTENT_PLAIN: email_data.content,
        EMAIL_OPP_ID: opp_id,
        EMAIL_JOB_ID: job_id,
        EMAIL_PO_ID: po_id,
        EMAIL_QUOTE_ID: quote_id,
        EMAIL_PLAN_ID: plan_id,
        EMAIL_LINKED_FLAG: linked_flag
    }
    return email_header
}

async function getContactPrivTeam(email_address, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const result = await sequelize.query(
        ` SELECT COMP_PRIV_TEAM FROM VIEW_CONTACTS WHERE CONT_EMAIL_BUSINESS = '${email_address}'`
    )
    let private_team = result[0].COMP_PRIV_TEAM ? result[0].COMP_PRIV_TEAM : 0
    return private_team
}

function replaceFromMsgId(msgId) {
    const m = msgId.replace('<', '').replace('>', '')
    return m
}

function getFirstParent(msgId) {
    const m = msgId.split('>', 2)
    const first = m[0]
    return first
}

function replaceQuotes(str) {
    const healthy = ['"', "'", ',,', '&quot;']
    const yummy = ['', '', ',', '']

    if (typeof str === 'string') {
        for (let i = 0; i < healthy.length; i++) {
            str = str.replace(new RegExp(healthy[i], 'g'), yummy[i])
        }
    }
    return str
}

function getEmailList(addresses) {
    const email_addresses = []
    let addr = ''

    if (typeof addresses != 'undefined') {
        addresses.forEach((address) => {
            addr = address.address
            email_addresses.push(addr)
        })
    }
    return email_addresses
}

function updateContact(email_addresses, id, table, req) {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    email_addresses.forEach(async (address) => {
        let contact_details = await getContactByEmail(address, req)
        let contact_id = contact_details ? contact_details.CONT_ID : 0
        if (contact_id != 0 && contact_id != null && contact_id != '') {
            if ((await checkContactExist(id, contact_id, table, req)) <= 0) {
                //Add Opp contact
                if (table == 'OPP_CONTACTS') {
                    const details = {
                        OPP_CONT_OPP_ID: id,
                        OPP_CONT_ID: contact_id
                    }
                    await models.OPP_CONTACTS.create(details)
                } else {
                    const details = {
                        JOB_ID: id,
                        JOB_CONT_ID: contact_id
                    }
                    await models.JOB_CONTACTS.create(details)
                }
            }
        }
    })
}

async function checkContactExist(id, contact_id, table, row) {
    let count = 0
    let id_col = table == 'OPP_CONTACTS' ? 'OPP_CONT_OPP_ID' : 'JOB_ID'
    let contact_col = table == 'OPP_CONTACTS' ? 'OPP_CONT_ID' : 'JOB_CONT_ID'
    let query = `SELECT count(*) row_count FROM ${table} WHERE ${id_col} = ${id} AND ${contact_col} = ${contact_id}`

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const [result] = await sequelize.query(query)
    count = result[0].row_count
    return count
}

async function getContactByEmail(email_address, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let query = `SELECT CONT_ID, CONT_COMP_ID, COMP_TYPE_NAME, COMP_TYPE AS CONT_TYPE, COMP_SMAN_ID, COMP_PRIV_TEAM FROM VIEW_CONTACTS WHERE CONT_EMAIL_BUSINESS = '${email_address}' OR CONT_EMAIL_ALTERNATE = '${email_address}' LIMIT 1`
    const [result] = await sequelize.query(query)
    return result[0]
}

async function getContactsByEmail(email_address, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let query = `SELECT CONT_ID, CONT_COMP_ID, COMP_TYPE_NAME, COMP_TYPE AS CONT_TYPE, COMP_SMAN_ID, COMP_PRIV_TEAM FROM VIEW_CONTACTS WHERE CONT_EMAIL_BUSINESS = '${email_address}' OR CONT_EMAIL_ALTERNATE = '${email_address}'`
    const [result] = await sequelize.query(query)
    return result
}

async function updateToContactEmails(
    date,
    time,
    from,
    to,
    cc,
    email_id,
    subject,
    folder,
    user_id,
    req
) {
    let email_date = date + ' ' + time
    let from_addr = Array.from([from]) //converting into array
    const addresses = [...new Set([...to, ...cc, ...from_addr])] //array merge and removing duplicates
    let email_type = folder == 'sent' ? 1 : 0

    //updated the code based on task 14463
    let email_contacts = await Promise.all(
        addresses.map(async (address) => {
            if (address) {
                let cont_details = await getContactsByEmail(address, req)

                if (cont_details && cont_details.length > 0) {
                    return cont_details.map((cont_detail) => {
                        let contact_id = cont_detail.CONT_ID
                        let contact_sman_id = cont_detail.COMP_SMAN_ID

                        return {
                            CONT_USER_ID: user_id,
                            CONT_EMAIL_ID: email_id,
                            CONT_EMAIL_TYPE: email_type,
                            CONT_EMAIL_ADDRESS: address.trim(),
                            CONT_EMAIL_SUBJECT: subject,
                            CONT_EMAIL_DATE: email_date,
                            CONT_ID: contact_id,
                            CONT_SMAN_ID: contact_sman_id
                        }
                    })
                }
            }
        })
    )

    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    if (email_contacts.length > 0) {
        let new_email_contacts = []
        new_email_contacts = email_contacts.filter(
            (value) => value !== undefined
        )
        await models.CONTACTS_EMAILS.bulkCreate(new_email_contacts.flat())
    }

    return true
}

async function enablePrivFlg(user_id, from, eid, to, cc, bcc, req) {
    let user_priv_flg_dtl = await getUserPrivateFalg(user_id, req)
    let user_priv_flag = 0
    let user_category = 0
    let from_contact_type = 0
    let form_data = {}
    if (user_priv_flg_dtl) {
        user_priv_flag = user_priv_flg_dtl.USER_PRIVATE_EMAIL_FLAG
        user_category = user_priv_flg_dtl.USER_CATEGORY
    }
    let from_contact = await getContactByEmail(from, req)
    let is_user_contact = await isEmailBelongToUser(from, to, cc, bcc, req)
    if (from_contact) {
        from_contact_type = from_contact.CONT_TYPE
    }

    logger.info('user_priv_flag' + user_priv_flag)
    logger.info('is_user_contact' + is_user_contact)
    logger.info('user_category' + user_category)
    logger.info('from_contact_type' + from_contact_type)
    logger.info('from_contact' + from_contact)
    if (
        user_priv_flag == '1' ||
        is_user_contact == '1' ||
        (user_category == '1' && from_contact_type == '1') ||
        typeof from_contact == 'undefined'
    ) {
        form_data.EMAIL_PRIVATE_FLAG = 1
    } else {
        form_data.EMAIL_PRIVATE_FLAG = 0
    }
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    await models.EMAIL_HEADER.update(form_data, {
        where: {
            EMAIL_ID: eid
        }
    })
}

async function getUserPrivateFalg(user_id, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let query = `SELECT USER_CATEGORY, USER_PRIVATE_EMAIL_FLAG FROM USERS WHERE USER_ID = '${user_id}' LIMIT 1`
    const [result] = await sequelize.query(query)
    return result[0]
}

async function isEmailBelongToUser(
    from_address,
    to_address,
    cc_address,
    bcc_address,
    req
) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let from_addr = Array.from([from_address]) //converting into array
    const addresses = [
        ...new Set([
            ...to_address,
            ...cc_address,
            ...from_addr,
            ...bcc_address
        ])
    ] //array merge and removing duplicates
    // if (to_address.length == 1) {
    //     let to = to_address[0]
    //     let query1 = `SELECT count(*) row_count FROM USER_EMAIL_CONFIG WHERE USER_EMAIL_LOGIN = '${from_address}'`
    //     const [result1] = await sequelize.query(query1)
    //     let query2 = `SELECT count(*) row_count FROM USER_EMAIL_CONFIG WHERE USER_EMAIL_LOGIN = '${to}'`
    //     const [result2] = await sequelize.query(query2)
    //     flag = result1[0].row_count > 0 && result2[0].row_count > 0 ? '1' : '0'
    // }

    if (addresses.length === 0) {
        return 0 // No emails to check
    }

    const placeholders = await addresses.map(() => '?').join(',')
    const sql = `
      SELECT COUNT(*) AS found_count
      FROM USER_EMAIL_CONFIG
      WHERE USER_EMAIL_LOGIN IN (${placeholders})
    `
    const rows = await sequelize.query(sql, {
        replacements: addresses,
        type: sequelize.QueryTypes.SELECT
    })
    const foundCount = rows[0]?.found_count || 0
    return foundCount === addresses.length ? '1' : '0'
}

async function updateEmailHeaderForSentEmail(
    from,
    date,
    subject,
    msgId,
    reference_id,
    user_id,
    size,
    uid,
    parent_id,
    syncThreadId,
    opp_id,
    job_id,
    quote_id,
    po_id,
    plan_id,
    folder,
    req
) {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    const updatedData = await models.EMAIL_HEADER.findOne({
        where: {
            EMAIL_USER_ID: user_id,
            EMAIL_FROM: from,
            EMAIL_DATE: date,
            EMAIL_FOLDER_TOP: folder,
            EMAIL_MSG_ID: msgId
        }
    })
    if(updatedData != undefined){
        opp_id = opp_id == 0 ? updatedData.EMAIL_OPP_ID : opp_id;
        job_id = job_id == 0 ? updatedData.EMAIL_JOB_ID : job_id;
        po_id = po_id == 0 ? updatedData.EMAIL_PO_ID : po_id;
        quote_id = quote_id == 0 ? updatedData.EMAIL_QUOTE_ID : quote_id;
        plan_id = plan_id == 0 ? updatedData.EMAIL_PLAN_ID : plan_id;
    }
    let form_data = {
        EMAIL_OPP_ID: opp_id,
        EMAIL_JOB_ID: job_id,
        EMAIL_PO_ID: po_id,
        EMAIL_QUOTE_ID: quote_id,
        EMAIL_PLAN_ID: plan_id
    }
    await models.EMAIL_HEADER.update(form_data, {
        where: {
            EMAIL_USER_ID: user_id,
            EMAIL_FROM: from,
            EMAIL_DATE: date,
            EMAIL_FOLDER_TOP: folder,
            EMAIL_MSG_ID: msgId
        }
    })
    return updatedData
}

async function setPrivPropSentMail(
    to_address,
    email_id,
    user_id,
    from_address,
    cc_address,
    bcc_address,
    req
) {
    await enablePrivFlg(
        user_id,
        from_address,
        email_id,
        to_address,
        cc_address,
        bcc_address,
        req
    )
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    for (const address of to_address) {
        let cont_detail = await getContactByEmail(replaceQuotes(address.trim()), req)

        let private_team = 0
        if (cont_detail !== undefined) {
            private_team = cont_detail.CONT_PRIV_TEAM
        }

        if (private_team !== 0) {
            let form_data = {
                EMAIL_PRIV_TEAM: parseInt(private_team)
            }
            await models.EMAIL_HEADER.update(form_data, {
                where: {
                    EMAIL_ID: email_id
                }
            })
        }
    }
}

async function checkIdExist(id, table, res, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let column_name =
        table == 'OPPORTUNITIES'
            ? 'OPP_ID'
            : table == 'JOBS'
                ? 'JOB_ID'
                : table == 'QUOTES_HDR'
                    ? 'REC_ID'
                    : table == 'PO_HDR'
                        ? 'PO_ID'
                        : 'PLAN_ID'
    let msg =
        table == 'OPPORTUNITIES'
            ? 'Opportunity'
            : table == 'JOBS'
                ? 'Job'
                : table == 'QUOTES_HDR'
                    ? 'Quote'
                    : table == 'PO_HDR'
                        ? 'PO'
                        : 'Plan'
    if (id > 0 && id != '') {
        let query =
            'SELECT count(*) row_count FROM ' +
            table +
            ' WHERE ' +
            column_name +
            ' = ' +
            id
        const [result] = await sequelize.query(query)

        if (result[0].row_count > 0) {
            return id
        } else {
            return res.status(500).json(msg + ' id does not exist')
        }
    } else {
        return id
    }
}

async function insertUpdateContent(email_id, attachments, html_content, req) {
    let email_content = await replaceEmbeddedContentFromAPI(
        html_content,
        attachments
    )

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Get tenantId from the request's dbVars for multi-tenant system
    const tenantId = req.dbVars?.tenantId;

    const result = await sequelize.query(
        `SELECT EMAIL_CONT_CONTENT FROM RF_EML_DB_${tenantId}.EMAIL_CONTENT WHERE EMAIL_CONT_EMAIL_ID = '${email_id}'`
    )

    let email_content_exist = result[0].EMAIL_CONT_CONTENT
        ? result[0].EMAIL_CONT_CONTENT
        : ''
    let content_data = {
        EMAIL_CONT_CONTENT: email_content
    }

    if (result[0].length == 0) {
        content_data.EMAIL_CONT_EMAIL_ID = email_id
        await sequelize.query(
            `insert into RF_EML_DB_${tenantId}.EMAIL_CONTENT (EMAIL_CONT_EMAIL_ID, EMAIL_CONT_CONTENT) values ($email_id,
            $email_content)`,
            {
                bind: {
                    email_id,
                    email_content
                }
            }
        )
    } else if (email_content_exist == '') {
        let query =
            `UPDATE RF_EML_DB_${tenantId}.EMAIL_CONTENT SET EMAIL_CONT_CONTENT = ? WHERE EMAIL_CONT_EMAIL_ID = ?`
        await sequelize.query(query, [email_content, email_id])
    }
}

async function replaceEmbeddedContentFromAPI(email_body, attachments) {
    if (/<img[^>]+>/i.test(email_body)) {
        const matches = email_body.match(/<img[^>]+>/gi)
        for (const img of matches) {
            const contentIdMatch = /src="cid:(.*?)"/.exec(img)

            if (contentIdMatch) {
                const contentId = contentIdMatch[1]
                const fileNameMatch = /alt="(.*?)"/.exec(img)
                const fileName = fileNameMatch ? fileNameMatch[1] : null
                if (contentId && fileName) {
                    const contentData = await getEmbeddedContentFromAPI(
                        attachments,
                        contentId
                    )
                    if (contentData) {
                        email_body = email_body.replace(
                            contentIdMatch[0],
                            contentData
                        )
                    }
                }
            }
        }
    }
    return email_body
}

async function getEmbeddedContentFromAPI(attachments, contentId) {
    for (const attach of attachments) {
        let inline = attach.inline
        let attachmentContentId = attach.contentId
        let mimeType = attach.mimeType
        let attachmentContent = attach.content
        if (inline && attachmentContentId === contentId) {
            return `src="data:${mimeType};base64,${attachmentContent}"`
        }
    }
}

async function downloadAttachmentFromAurinko(
    attachments,
    msg_id,
    user_id,
    email_id,
    req
) {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    for (const attach of attachments) {
        let inline = attach.inline
        if (!inline) {
            let attachment_details = await getAttachmentDetailsFromAurinko(
                attach.id,
                msg_id,
                user_id,
                req
            )
            let content = Buffer.from(attachment_details.content, 'base64')
            let file_name = attach.name
            let email_header = await getEmaild(email_id, req)
            if (email_header == undefined) {
                logger.info(
                    'Email does not exist. Email id: ' +
                    email_id +
                    ' and attachment id ' +
                    attach.id
                )
            }
            email_id = email_header['EMAIL_ID']
            //req.body.email_id = email_id

            let params = await getParam(req)
            let base_folder = params['PARAM_BASE_FOLDER'] + 'attachment'
            //req.body.baseFolder = base_folder
            if (
                attach.mimeType === 'multipart/signed' ||
                attach.mimeType === 'application/pkcs7-mime'
            ) {
                const parsed = await simpleParser(
                    Buffer.from(attachment_details.content, 'base64')
                )
                let signAttachmentCount = 0

                parsed.attachments.forEach((attachment) => {
                    if (
                        attachment.contentDisposition === 'attachment' &&
                        attachment.contentType !== 'application/pkcs7-signature'
                    ) {
                        signAttachmentCount++
                        content = attachment.content
                        file_name = attachment.filename
                        //req.body.name = file_name
                    }
                })

                if (signAttachmentCount === 0) {
                    let form_data = {
                        EMAIL_ATTACH_FLAG: 0
                    }
                    await models.EMAIL_HEADER.update(form_data, {
                        where: {
                            EMAIL_ID: email_id
                        }
                    })
                }
            }

            // Convert the content to a file and store it locally

            let user_detail = await getUserDetails(user_id, req)
            let user_attach_folder = user_detail['USER_EMAIL_ATTA_FOLDER']
            //req.body.userAttachFolder = user_attach_folder
            const dirPath = path.join(
                base_folder,
                user_id.toString(),
                user_attach_folder.toString()
            )
            const filePath = path.join(dirPath, `${email_id}-${file_name}`)

            // Ensure the directory exists
            fs.mkdirSync(dirPath, { recursive: true })

            // Write file to local storage
            fs.writeFile(filePath, content, async (writeErr) => {
                if (writeErr) {
                    let form_data = {
                        EMAIL_ERROR_MSG: 1
                    }
                    await models.EMAIL_HEADER.update(form_data, {
                        where: {
                            EMAIL_ID: email_id
                        }
                    })
                    logger.info(
                        'Error saving attachment to rf server. Email id: ' +
                        email_id +
                        ' and attachment id ' +
                        attach.id +
                        ' error message is ' +
                        writeErr.message
                    )
                }

                let form_data = {
                    EMAIL_ERROR_MSG: 0
                }
                await models.EMAIL_HEADER.update(form_data, {
                    where: {
                        EMAIL_ID: email_id
                    }
                })

                file_name = `${email_id}-${file_name}`

                if ((await isAttachExist(file_name, req)) == 0) {
                    const form_data = {
                        EMAIL_ATTA_EMAIL_ID: email_id,
                        EMAIL_ATTA_NAME: file_name,
                        EMAIL_ATTA_LOCATION: `attachment/${user_id}/${user_attach_folder}/`
                    }

                    const inserted = await models.EMAIL_ATTACHMENT.create(
                        form_data
                    )
                    if (inserted) {
                        let form_data = {
                            EMAIL_ATTACH_FLAG: 1
                        }
                        await models.EMAIL_HEADER.update(form_data, {
                            where: {
                                EMAIL_ID: email_id
                            }
                        })

                        let query = `UPDATE USER_EMAIL_CONFIG
                            SET USER_EMAIL_ATTA_COUNT = USER_EMAIL_ATTA_COUNT + 1
                            WHERE USER_EMAIL_USER_ID = ?
                            `
                        await sequelize.query(query, [null, user_id])

                        if (user_detail['USER_EMAIL_ATTA_FOLDER'] == '999') {
                            let query = `UPDATE USER_EMAIL_CONFIG
                                SET USER_EMAIL_ATTA_FOLDER = USER_EMAIL_ATTA_FOLDER + 1
                                WHERE USER_EMAIL_USER_ID = ?
                                `
                            await sequelize.query(query, [null, user_id])
                        }
                    }
                }
            })
        } else {
            logger.info(
                'Invalid file. Email id: ' +
                email_id +
                ' and attachment id ' +
                attach.id
            )
        }
    }
}

async function getAttachmentDetailsFromAurinko(attach_id, message_id, user_id, req) {
    try {
        const url = `https://api.aurinko.io/v1/email/messages/${message_id}/attachments/${attach_id}`
        let user_detail = await getUserDetails(user_id, req)
        let user_access_token = user_detail['USER_ACCESS_TOKEN']
        const headers = {
            Authorization: `Bearer ${user_access_token}`
        }

        const response = await axios.get(url, { headers })
        return response.data
    } catch (error) {
        logger.info(
            'Error while downloading from aurinko. Message id: ' +
            message_id +
            ' and attachment id ' +
            attach_id +
            ' error message is ' +
            error.message
        )
    }
}

const downloadEmailAttachment = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        if (!req.body.inline) {
            let content = Buffer.from(req.body.content, 'base64')
            let file_name = req.body.name
            let email_header = await getEmaild(req.params.id, req)
            if (email_header == undefined) {
                return res.status(501).json({
                    error: 'Email does not exist'
                })
            }
            let email_id = email_header['EMAIL_ID']
            req.body.email_id = email_id
            if (
                req.body.mimeType === 'multipart/signed' ||
                req.body.mimeType === 'application/pkcs7-mime'
            ) {
                const parsed = await simpleParser(
                    Buffer.from(req.body.content, 'base64')
                )
                let signAttachmentCount = 0

                parsed.attachments.forEach((attachment) => {
                    if (
                        attachment.contentDisposition === 'attachment' &&
                        attachment.contentType !== 'application/pkcs7-signature'
                    ) {
                        signAttachmentCount++
                        content = attachment.content
                        file_name = attachment.filename
                        req.body.name = file_name
                    }
                })

                if (signAttachmentCount === 0) {
                    let form_data = {
                        EMAIL_ATTACH_FLAG: 0
                    }
                    await models.EMAIL_HEADER.update(form_data, {
                        where: {
                            EMAIL_ID: email_id
                        }
                    })
                }
            }

            singleUpload(req, res, async function (err) {
                if (err) {
                    let form_data = {
                        EMAIL_ERROR_MSG: 1
                    }
                    await models.EMAIL_HEADER.update(form_data, {
                        where: {
                            EMAIL_ID: email_id
                        }
                    })
                    return res.status(501).json({
                        error: err.message
                    })
                }
                const user_id = req.session.userId
                let host_name = 'twdev'

                const filePath =
                    host_name +
                    '/EmailAttachment/' +
                    user_id +
                    '/' +
                    email_id +
                    '_' +
                    file_name
                const params = {
                    Bucket: process.env.S3_BUCKET,
                    Key: filePath,
                    Body: content,
                    ContentType: 'application/octet-stream'
                }

                const data = await s3.upload(params).promise()
                if (data) {
                    const signedUrl = s3.getSignedUrl('getObject', {
                        Key: data.key,
                        Bucket: process.env.S3_BUCKET,
                        Expires: 3900
                    })
                    let response = {
                        title: 'File uploaded successfully',
                        url: signedUrl
                    }

                    let form_data = {
                        EMAIL_ERROR_MSG: 0
                    }
                    await models.EMAIL_HEADER.update(form_data, {
                        where: {
                            EMAIL_ID: email_id
                        }
                    })
                    file_name = email_id + '_' + file_name
                    if ((await isAttachExist(file_name, req)) == 0) {
                        const form_data = {
                            EMAIL_ATTA_EMAIL_ID: email_id,
                            EMAIL_ATTA_NAME: file_name,
                            EMAIL_ATTA_LOCATION: data.key
                        }
                        const inserted = await models.EMAIL_ATTACHMENT.create(
                            form_data
                        )
                        if (inserted) {
                            let form_data = {
                                EMAIL_ATTACH_FLAG: 1
                            }
                            await models.EMAIL_HEADER.update(form_data, {
                                where: {
                                    EMAIL_ID: email_id
                                }
                            })

                            let query = `UPDATE USER_EMAIL_CONFIG
                            SET USER_EMAIL_ATTA_COUNT = USER_EMAIL_ATTA_COUNT + 1 
                            WHERE USER_EMAIL_USER_ID = ?
                            `
                            await sequelize.query(query, [null, user_id])

                            let user_detail = getUserDetails(user_id, req)
                            if (
                                user_detail['USER_EMAIL_ATTA_FOLDER'] == '999'
                            ) {
                                let query = `UPDATE USER_EMAIL_CONFIG
                                SET USER_EMAIL_ATTA_FOLDER = USER_EMAIL_ATTA_FOLDER + 1 
                                WHERE USER_EMAIL_USER_ID = ?
                                `
                                await sequelize.query(query, [null, user_id])
                            }
                        }
                    }
                    res.status(200).json(response)
                }
            })
        } else {
            return res.status(501).json({
                error: 'Invalid file'
            })
        }
    } catch (error) {
        next(AppError.internal())
    }
}

const downloadEmailAttachmentToRF = async (req, res) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        if (!req.body.inline) {
            let content = Buffer.from(req.body.content, 'base64')
            let file_name = req.body.name
            let email_header = await getEmaild(req.params.id, req)
            if (email_header == undefined) {
                return res.status(501).json({
                    error: 'Email does not exist'
                })
            }
            let email_id = email_header['EMAIL_ID']
            req.body.email_id = email_id

            let params = await getParam(req)
            let base_folder = params['PARAM_BASE_FOLDER'] + 'attachment'
            req.body.baseFolder = base_folder
            if (
                req.body.mimeType === 'multipart/signed' ||
                req.body.mimeType === 'application/pkcs7-mime'
            ) {
                const parsed = await simpleParser(
                    Buffer.from(req.body.content, 'base64')
                )
                let signAttachmentCount = 0

                parsed.attachments.forEach((attachment) => {
                    if (
                        attachment.contentDisposition === 'attachment' &&
                        attachment.contentType !== 'application/pkcs7-signature'
                    ) {
                        signAttachmentCount++
                        content = attachment.content
                        file_name = attachment.filename
                        req.body.name = file_name
                    }
                })

                if (signAttachmentCount === 0) {
                    let form_data = {
                        EMAIL_ATTACH_FLAG: 0
                    }
                    await models.EMAIL_HEADER.update(form_data, {
                        where: {
                            EMAIL_ID: email_id
                        }
                    })
                }
            }

            // Convert the content to a file and store it locally
            const user_id = req.session.userId
            let user_detail = await getUserDetails(user_id, req)
            let user_attach_folder = user_detail['USER_EMAIL_ATTA_FOLDER']
            req.body.userAttachFolder = user_attach_folder
            const dirPath = path.join(
                base_folder,
                user_id.toString(),
                user_attach_folder.toString()
            )
            const filePath = path.join(dirPath, `${email_id}-${file_name}`)

            // Ensure the directory exists
            fs.mkdirSync(dirPath, { recursive: true })

            // Write file to local storage
            fs.writeFile(filePath, content, async (writeErr) => {
                if (writeErr) {
                    let form_data = {
                        EMAIL_ERROR_MSG: 1
                    }
                    await models.EMAIL_HEADER.update(form_data, {
                        where: {
                            EMAIL_ID: email_id
                        }
                    })
                    return res.status(501).json({
                        error: writeErr.message
                    })
                }

                let form_data = {
                    EMAIL_ERROR_MSG: 0
                }
                await models.EMAIL_HEADER.update(form_data, {
                    where: {
                        EMAIL_ID: email_id
                    }
                })

                file_name = `${email_id}-${file_name}`
                let response = {
                    title: 'Attachment downloaded successfully',
                    file: file_name
                }
                if ((await isAttachExist(file_name, req)) == 0) {
                    const form_data = {
                        EMAIL_ATTA_EMAIL_ID: email_id,
                        EMAIL_ATTA_NAME: file_name,
                        EMAIL_ATTA_LOCATION: `attachment/${user_id}/${user_attach_folder}/`
                    }

                    const inserted = await models.EMAIL_ATTACHMENT.create(
                        form_data
                    )
                    if (inserted) {
                        let form_data = {
                            EMAIL_ATTACH_FLAG: 1
                        }
                        await models.EMAIL_HEADER.update(form_data, {
                            where: {
                                EMAIL_ID: email_id
                            }
                        })

                        let query = `UPDATE USER_EMAIL_CONFIG
                        SET USER_EMAIL_ATTA_COUNT = USER_EMAIL_ATTA_COUNT + 1 
                        WHERE USER_EMAIL_USER_ID = ?
                        `
                        await sequelize.query(query, [null, user_id])

                        if (user_detail['USER_EMAIL_ATTA_FOLDER'] == '999') {
                            let query = `UPDATE USER_EMAIL_CONFIG
                            SET USER_EMAIL_ATTA_FOLDER = USER_EMAIL_ATTA_FOLDER + 1 
                            WHERE USER_EMAIL_USER_ID = ?
                            `
                            await sequelize.query(query, [null, user_id])
                        }
                    }
                }
                res.status(200).json(response)
            })
        } else {
            return res.status(501).json({
                error: 'Invalid file'
            })
        }
    } catch (error) {
        return res.status(501).json({
            error: 'Failed to download attachment'
        })
    }
}

const getImage = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let rec_id = req.params.id
        let query = `SELECT EMAIL_ATTA_LOCATION FROM EMAIL_ATTACHMENT WHERE REC_ID = '${rec_id}' LIMIT 1`
        const [result] = await sequelize.query(query)
        const signedUrl = s3.getSignedUrl('getObject', {
            Key: result[0].EMAIL_ATTA_LOCATION,
            Bucket: process.env.S3_BUCKET
        })
        return res.status(200).json(signedUrl)
    } catch (error) {
        next(AppError.internal())
    }
}

async function getEmaild(email_id, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let query = `SELECT EMAIL_ID FROM EMAIL_HEADER WHERE EMAIL_ID = '${email_id}' LIMIT 1`
    const [result] = await sequelize.query(query)
    return result[0]
}

async function isAttachExist(file_name, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let count = 0
    let query = `SELECT count(*) row_count FROM EMAIL_ATTACHMENT WHERE EMAIL_ATTA_NAME = '${file_name}'`
    const [result] = await sequelize.query(query)
    count = result[0].row_count
    return count
}

async function getParam(req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let query = 'SELECT * FROM PARAMS LIMIT 1'
    const [result] = await sequelize.query(query)
    return result[0]
}

async function getUserDetails(user_id,req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let query = `SELECT * FROM USER_EMAIL_CONFIG WHERE USER_EMAIL_USER_ID = '${user_id}' LIMIT 1`
    const [result] = await sequelize.query(query)
    return result[0]
}

const getEmailById = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    try {
        let email_id = req.params.id
        let query = `SELECT * FROM email_header WHERE EMAIL_ID = '${email_id}' LIMIT 1`
        const [result] = await sequelize.query(query)
        if (result.length > 0) {
            let email = result[0]
            let response = {
                emailId: email.EMAIL_ID,
                msgId: email.EMAIL_UID,
                subject: email.subject
            }
            return res.status(200).json(response)
        } else {
            return res.status(401).json({
                status: 'error',
                message: 'Email id not found'
            })
        }
    } catch (error) {
        next(AppError.internal())
    }
}

async function autoAssociateBusinessObjects(businessObject, threadId, referenceId, emailId, parentId, req) {
    const record = await queryEmailHeader(businessObject, threadId, referenceId, parentId, req);
    let entityCount = 0;
    if(businessObject === 'EMAIL_OPP_ID'){
        entityCount = await getEmailEntityCount(businessObject, threadId, referenceId, req);
    }
    if (record && entityCount <= 1) {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.EMAIL_HEADER.update(
            {
                [businessObject]: record[businessObject],
                EMAIL_LINKED_FLAG: 1
            },
            { where: { EMAIL_ID: emailId } }
        );

        // Add status update only for Opportunity
        if (businessObject === 'EMAIL_OPP_ID') {
            await updateOppStatusToNeedsReview(record[businessObject], req);
        }
    }
}

async function getModifiedId(email) {
    return email.includes('@') ? email.split('@')[0] : email;
}

async function queryEmailHeader(businessObject, threadId, referenceId, parentId, req) {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    const whereClause = {
        [businessObject]: { [Op.ne]: 0 }
    };

    const refIdModified = referenceId ? await getModifiedId(referenceId) : null;
    parentId = parentId ? await replaceFromMsgId(parentId) : null;

    const orConditions = [];

    if (referenceId) {
        orConditions.push({ EMAIL_REF_ID: { [Op.in]: [referenceId, refIdModified] } });
        orConditions.push({ EMAIL_MSG_ID: { [Op.in]: [referenceId, refIdModified] } });
    }

    if (threadId) {
        orConditions.push({ EMAIL_THREAD_ID: threadId });
    }

    if (orConditions.length > 0) {
        whereClause[Op.or] = orConditions;
    }

    let result = await models.EMAIL_HEADER.findOne({
        where: whereClause,
        order: [[businessObject, 'DESC']]
    });

    // Fallback to parent ID query if no result
    if (!result && parentId) {
        result = await models.EMAIL_HEADER.findOne({
            where: {
                EMAIL_PARENT_ID: parentId,
                [businessObject]: { [Op.ne]: 0 }
            },
            order: [[businessObject, 'DESC']]
        });
    }

    console.log("opp linking result", result);
    return result;
}

async function getEmailEntityCount(businessObject, threadId, referenceId, req) {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    let where = {};
    if (referenceId) {
        const refIdModified = await getModifiedId(referenceId);
        where = {
            [Op.or]: [
                { EMAIL_REF_ID: { [Op.in]: [referenceId, refIdModified] } },
                { EMAIL_MSG_ID: { [Op.in]: [referenceId, refIdModified] } }
            ],
            [businessObject]: { [Op.ne]: 0 }
        };
    } else if (threadId) {
        where = {
            EMAIL_THREAD_ID: threadId,
            [businessObject]: { [Op.ne]: 0 }
        };
    }

    const count = await models.EMAIL_HEADER.count({ where, distinct: true, col: businessObject });
    return count;
}

async function updateOppStatusToNeedsReview(opp_id, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let query = `UPDATE OPPORTUNITIES
                        SET OPP_STATUS = 'Needs Review'
                        WHERE OPP_ID  = ?
                        `
    await sequelize.query(query, [null, opp_id])
}


module.exports = {
    downloadEmail,
    downloadEmailAttachment,
    getImage,
    downloadEmailAttachmentToRF,
    getEmailById
}
