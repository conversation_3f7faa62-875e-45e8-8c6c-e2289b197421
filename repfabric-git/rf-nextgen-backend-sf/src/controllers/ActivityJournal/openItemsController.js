const { QueryTypes } = require('sequelize')
const { SIZE } = require('../../constants')
const {
    getZeroBasedPagination,
    getZeroBasedPaginationData
} = require('../../helpers')
const { sequelize, Sequelize } = require('../../models')
const { default: axios } = require('axios')
const dayjs = require('dayjs')
const utc = require('dayjs/plugin/utc')
const logger = require('../../utils/logger')

let opportunityCustomLabel
let jobCustomLabel

async function initialize() {
    const customLabels = await getCustomLabels(req)

    opportunityCustomLabel = getLabel('IDS_OPP', 'Opportunity', customLabels)
    jobCustomLabel = getLabel('IDS_JOB', 'Job', customLabels)
}

const IsEditable = {
    opportunity: {
        typeEditable: false,
        topicEditable: true,
        distributorEditable: false,
        stageEditable: true,
        valueEditable: true
    },
    job: {
        typeEditable: false,
        topicEditable: true,
        distributorEditable: false,
        stageEditable: true,
        valueEditable: true
    }
}

//Maintaining this to prevent sql injection
const allowedFieldsToSort = [
    'id',
    'type',
    'topic',
    'distributor',
    'stage',
    'value'
]

const generateQueryPart = (type, whereClause) => {
    const selectQuery = {
        opportunity: `
            SELECT 
                OP.OPP_ID AS id, 
                'Opportunity' AS type, 
                OP.OPP_PRINCIPAL AS principalId, 
                TRIM(OP.OPP_CUST_PROGRAM) AS topic, 
                TRIM(C.COMP_NAME) AS distributor,
                OP.OPP_DISTRI AS distributorId,
                COALESCE(OAM.REC_ID, OAM_Fallback.REC_ID, OAM_Fallback_Default.REC_ID) AS stage_recId,
                COALESCE(OAM.ACT_NAME, OAM_Fallback.ACT_NAME, OAM_Fallback_Default.ACT_NAME) AS stage,
                OP.OPP_VALUE AS value 
            FROM OPPORTUNITIES OP
            LEFT JOIN COMPANIES C
                ON C.COMP_ID = OP.OPP_DISTRI 
            LEFT JOIN OPP_ACTIVITIES_MST OAM 
                ON BINARY TRIM(OP.OPP_ACTIVITY) = BINARY TRIM(OAM.ACT_NAME)
                AND OP.OPP_PRINCIPAL = OAM.ACT_COMP_ID
            LEFT JOIN OPP_ACTIVITIES_MST OAM_Fallback 
                ON BINARY TRIM(OP.OPP_ACTIVITY) = BINARY TRIM(OAM_Fallback.ACT_NAME)
                AND (OAM.REC_ID IS NULL OR OAM.ACT_COMP_ID != OP.OPP_PRINCIPAL)
                AND OAM_Fallback.ACT_COMP_ID = 0
            LEFT JOIN (
                SELECT * 
                FROM OPP_ACTIVITIES_MST 
                WHERE ACT_COMP_ID = 0
                ORDER BY REC_ID ASC
                LIMIT 1
            ) OAM_Fallback_Default 
            ON OAM.REC_ID IS NULL AND OAM_Fallback.REC_ID IS NULL`,

        job: `
            SELECT 
            DISTINCT JOB.JOB_ID as id, 
                'Job' as type, 
                NULL as principalId,  
                TRIM(JOB.JOB_DESCR) as topic, 
                NULL as distributor,   
                NULL as distributorId,
                JAM.REC_ID as stage_recId,
                JAM.ACT_NAME as stage,
                JOB.JOB_VALUE as value 
            FROM JOBS JOB
            JOIN JOB_RELTD_COMPS JRC1 ON JRC1.JOB_ID = JOB.JOB_ID
            JOIN JOB_RELTD_COMPS JRC2 ON JRC2.JOB_ID = JOB.JOB_ID
            LEFT JOIN JOB_ACTIVITIES_MST JAM ON JAM.REC_ID = JOB.JOB_ACTIVITY`
    }

    return `${selectQuery[type]} ${whereClause}`
}

const generateCountQueryPart = (type, whereClause, includeJoins = false) => {
    const baseQuery = {
        opportunity: `
            SELECT OP.OPP_ID as id
            FROM OPPORTUNITIES OP`,

        job: `
            SELECT DISTINCT JOB.JOB_ID as id
            FROM JOBS JOB
            JOIN JOB_RELTD_COMPS JRC1 ON JRC1.JOB_ID = JOB.JOB_ID
            JOIN JOB_RELTD_COMPS JRC2 ON JRC2.JOB_ID = JOB.JOB_ID`
    }

    const extraJoins = {
        opportunity: `
            LEFT JOIN COMPANIES C
                ON C.COMP_ID = OP.OPP_DISTRI 
            LEFT JOIN OPP_ACTIVITIES_MST OAM 
                ON BINARY TRIM(OP.OPP_ACTIVITY) = BINARY TRIM(OAM.ACT_NAME)
                AND OP.OPP_PRINCIPAL = OAM.ACT_COMP_ID
            LEFT JOIN OPP_ACTIVITIES_MST OAM_Fallback 
                ON BINARY TRIM(OP.OPP_ACTIVITY) = BINARY TRIM(OAM_Fallback.ACT_NAME)
                AND (OAM.REC_ID IS NULL OR OAM.ACT_COMP_ID != OP.OPP_PRINCIPAL)
                AND OAM_Fallback.ACT_COMP_ID = 0
            LEFT JOIN (
                SELECT * 
                FROM OPP_ACTIVITIES_MST 
                WHERE ACT_COMP_ID = 0
                ORDER BY REC_ID ASC
                LIMIT 1
            ) OAM_Fallback_Default 
            ON OAM.REC_ID IS NULL AND OAM_Fallback.REC_ID IS NULL`,

        job: `
            LEFT JOIN JOB_ACTIVITIES_MST JAM ON JAM.REC_ID = JOB.JOB_ACTIVITY`
    }

    return `${baseQuery[type]} ${
        includeJoins ? extraJoins[type] : ''
    } ${whereClause}`
}

const extractPaginationDetails = async (pagination = {}) => {
    const { page, size } = pagination
    const pageAsNumber = page ? Number.parseInt(page) : 0
    const sizeAsNumber = size ? Number.parseInt(size) : SIZE

    let finalPage =
        !Number.isNaN(pageAsNumber) && pageAsNumber > 0 ? pageAsNumber : 0
    let finalSize =
        !Number.isNaN(sizeAsNumber) && sizeAsNumber > 0 && sizeAsNumber <= SIZE
            ? sizeAsNumber
            : SIZE

    return { finalPage, finalSize }
}

const modifyRecords = (openItems) => {
    const stageDropDownApi = process.env.STAGE_DROPDOWN_API_ROUTE

    if (!openItems || openItems.length === 0) {
        return []
    }

    return openItems.map(({ stage_recId, stage, ...record }) => {
        const recordType = record.type?.toLowerCase()
        const isOpportunity = recordType === 'opportunity'
        const isJob = recordType === 'job'

        let stageDropDownUrl = null
        let viewApiUrl = null
        if (isOpportunity) {
            stageDropDownUrl = `${stageDropDownApi}/${recordType}/${record.principalId}`
            viewApiUrl = `/opploop/opportunity/OpportunityView.xhtml?opid=${record.id}`
        } else if (isJob) {
            stageDropDownUrl = `${stageDropDownApi}/${recordType}`
            viewApiUrl = `/opploop/jobs/JobsView.xhtml?id=${record.id}`
        }

        return {
            ...record,
            ...(IsEditable?.[recordType] || {}),
            type: isOpportunity ? opportunityCustomLabel : jobCustomLabel,
            recordType: recordType,
            stage: stage_recId ? { recId: stage_recId, name: stage } : null,
            stageDropDownApi: stageDropDownUrl,
            viewApi: viewApiUrl
        }
    })
}

const generateOrderByClause = (gridSort, allowedFieldsToSort) => {
    if (!gridSort || gridSort.length === 0) {
        return 'ORDER BY id'
    }

    const orderConditions = gridSort
        .filter((g) => allowedFieldsToSort.includes(g.field))
        .map(
            (g) =>
                `${g.field} ${g.sort.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'}`
        )

    return orderConditions.length > 0
        ? `ORDER BY ${orderConditions.join(', ')}`
        : 'ORDER BY id'
}

const retrieveFilteredOpenItems = async (
    principalId,
    customerId,
    columnFilters,
    limit,
    offset,
    orderByClause,
    logicOperator,
    misc,
    req
) => {
    let openItems = []
    let totalItems = 0

    if (columnFilters?.items?.length > 0) {
        ;({ openItems, totalItems } = await fetchFilterOpenItems(
            principalId,
            customerId,
            columnFilters,
            limit,
            offset,
            orderByClause,
            logicOperator,
            misc,
            req
        ))

        if (
            columnFilters?.quickFilterValues?.length > 0 &&
            openItems?.length > 0
        ) {
            ;({ openItems, totalItems } = await quickFilter(
                principalId,
                customerId,
                columnFilters,
                limit,
                offset,
                orderByClause,
                openItems,
                req
            ))
        }
    } else if (columnFilters?.quickFilterValues?.length > 0) {
        ;({ openItems, totalItems } = await quickFilter(
            principalId,
            customerId,
            columnFilters,
            limit,
            offset,
            orderByClause,
            false,
            req
        ))
    } else {
        ;({ openItems, totalItems } = await fetchUnfilteredOpenItems(
            principalId,
            customerId,
            limit,
            offset,
            orderByClause,
            misc,
            req
        ))
    }

    return { openItems, totalItems }
}

//Main Func starts from here
const getOpenItems = async (req, res, next) => {
    try {
        const { columnFilters, grid, pagination, gridSort } = req.body
        const userId = req.session.userId

        initialize(req).catch((error) => {
            logger.error(
                'error in retrieving custom labels hence using fallback:',
                error
            )
            opportunityCustomLabel = getLabel('IDS_OPP', 'Opportunity', [])
            jobCustomLabel = getLabel('IDS_JOB', 'Job', [])
        })

        if (!grid?.openItems?.princiId && !grid?.openItems?.custId) {
            next(AppError.badRequest())
        }
        const principalId = grid.openItems.princiId
        const customerId = grid.openItems.custId

        const logicOperator =
            columnFilters?.items?.length === 1
                ? 'AND'
                : columnFilters?.logicOperator?.toUpperCase() || 'AND'

        const { finalPage, finalSize } = await extractPaginationDetails(
            pagination
        )

        const { limit, offset } = getZeroBasedPagination(finalPage, finalSize)

        let misc = {}

        misc.checkPageAccess = await checkPageAccess(userId, req)

        let orderByClause = generateOrderByClause(gridSort, allowedFieldsToSort)
        const { openItems, totalItems } = await retrieveFilteredOpenItems(
            principalId,
            customerId,
            columnFilters,
            limit,
            offset,
            orderByClause,
            logicOperator,
            misc,
            req
        )

        let modifiedRecords = modifyRecords(openItems)

        const rows =
            modifiedRecords && modifiedRecords.length > 0 ? modifiedRecords : []
        const paginateData = { count: totalItems, rows }

        if (rows && rows.length > 0) {
            return res
                .status(200)
                .json(
                    getZeroBasedPaginationData(
                        paginateData,
                        finalPage,
                        finalSize
                    )
                )
        } else {
            return res.status(200).json([])
        }
    } catch (error) {
        next(AppError.internal())
    }
}

const fetchUnfilteredOpenItems = async (
    principalId,
    customerId,
    limit,
    offset,
    orderByClause,
    misc,
    req
) => {
    const replacements = { principalId, customerId, limit, offset }

    const filters = {
        opportunity: `WHERE OP.OPP_CLOSE_STATUS = 0 AND OP.OPP_PRINCIPAL = :principalId AND OP.OPP_CUSTOMER = :customerId`,
        job: `WHERE JOB.JOB_CLOSE_STATUS = 0 AND JRC1.JOB_RELT_COMP_ID = :principalId AND JRC2.JOB_RELT_COMP_ID = :customerId`
    }

    let queryParts = Object.keys(filters)
        .filter((key) => misc?.checkPageAccess?.[key] !== 0)
        .map((key) => generateQueryPart(key, filters[key]))

    let totalCountQuery = Object.keys(filters)
        .filter((key) => misc?.checkPageAccess?.[key] !== 0)
        .map(
            (key) =>
                `SELECT COUNT(*) AS total_records FROM (${generateCountQueryPart(
                    key,
                    filters[key],
                    false
                )}) AS subquery`
        )

    if (queryParts.length === 0) {
        return []
    }

    let finalQuery = `
        SELECT *
        FROM (${queryParts.join(' UNION ')}) AS unfiltered_results
        ${orderByClause}
        LIMIT :limit OFFSET :offset
    `

    let totalCountQueryString = `
        SELECT SUM(total_records) AS total_records FROM (${totalCountQuery.join(
            ' UNION '
        )}) AS count_query
    `
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let [openItems, totalCountResult] = await Promise.all([
        sequelize.query(finalQuery, {
            replacements,
            type: sequelize.QueryTypes.SELECT
        }),
        sequelize.query(totalCountQueryString, {
            replacements,
            type: sequelize.QueryTypes.SELECT
        })
    ])

    return {
        openItems,
        totalItems: Number(totalCountResult[0]?.total_records) || 0
    }
}

const totalCount = async (query, replacements, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const totalCountResult = await sequelize.query(query, {
        replacements: replacements,
        type: sequelize.QueryTypes.SELECT
    })

    return totalCountResult
}

const buildSearchCondition = (fields, searchValues, logicOperator) => {
    if (!searchValues.length) return ''

    const conditions = searchValues.map((value, index) => {
        const fieldConditions = fields.map(
            (field) => `LOWER(${field}) LIKE :searchValue${index}`
        )
        return `(${fieldConditions.join(' OR ')})`
    })

    return logicOperator === 'AND'
        ? conditions.join(' AND ')
        : conditions.join(' OR ')
}

const quickFilter = async (
    principalId,
    customerId,
    columnFilters,
    limit,
    offset,
    orderByClause,
    filteredData = null,
    req
) => {
    try {
        const searchValues = columnFilters.quickFilterValues || []
        const logicOperator =
            columnFilters.quickFilterLogicOperator?.toUpperCase() === 'AND'
                ? 'AND'
                : 'OR'

        if (searchValues.length === 0) {
            return { openItems: [], totalItems: 0 }
        }

        if (filteredData) {
            const openItems = filteredData.filter((item) => {
                const matches = searchValues.map((searchValue) => {
                    const searchRegex = new RegExp(searchValue, 'i')
                    return (
                        Object.values(item).some((val) =>
                            searchRegex.test(String(val))
                        ) || searchRegex.test(item.type)
                    )
                })

                return logicOperator === 'AND'
                    ? matches.every(Boolean)
                    : matches.some(Boolean)
            })

            return { openItems, totalItems: openItems.length }
        }

        const sanitizedSearchValues = searchValues.map((val) =>
            sanitizeSearchValue(val.toLowerCase())
        )

        const replacements = {
            principalId,
            customerId,
            limit,
            offset
        }

        const filters = {
            opportunity: `WHERE OP.OPP_CLOSE_STATUS = 0 AND OP.OPP_PRINCIPAL = :principalId AND OP.OPP_CUSTOMER = :customerId`,
            job: `WHERE JOB.JOB_CLOSE_STATUS = 0 AND JRC1.JOB_RELT_COMP_ID = :principalId AND JRC2.JOB_RELT_COMP_ID = :customerId`
        }

        const searchConditions = {
            opportunity: `AND (${buildSearchCondition(
                [
                    'OPP_CUST_PROGRAM',
                    'C.COMP_NAME',
                    'OPP_ACTIVITY',
                    'CAST(OPP_VALUE AS CHAR)'
                ],
                sanitizedSearchValues,
                logicOperator
            )})`,
            job: `AND (${buildSearchCondition(
                [
                    'JOB_DESCR',
                    'C.COMP_NAME',
                    'JAM.ACT_NAME',
                    'CAST(JOB_VALUE AS CHAR)'
                ],
                sanitizedSearchValues,
                logicOperator
            )})`
        }

        sanitizedSearchValues.forEach(
            (val, index) => (replacements[`searchValue${index}`] = `%${val}%`)
        )

        let queryParts = Object.keys(filters).map((key) =>
            generateQueryPart(key, filters[key] + searchConditions[key])
        )

        let totalCountQuery = queryParts.map(
            (query) =>
                `SELECT COUNT(*) AS total_records FROM (${query}) AS subquery`
        )

        if (queryParts.length === 0) {
            return { openItems: [], totalItems: 0 }
        }

        let finalQuery = `
            SELECT *
            FROM (${queryParts.join(' UNION ')}) AS filtered_results
            ${orderByClause}
            LIMIT :limit OFFSET :offset;
        `

        let totalCountQueryString = `
            SELECT SUM(total_records) AS total_records FROM (${totalCountQuery.join(
                ' UNION '
            )}) AS count_query;
        `
        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;

        let [filteredItems, totalCountResult] = await Promise.all([
            sequelize.query(finalQuery, {
                replacements,
                type: sequelize.QueryTypes.SELECT
            }),
            sequelize.query(totalCountQueryString, {
                replacements,
                type: sequelize.QueryTypes.SELECT
            })
        ])

        return {
            openItems: filteredItems,
            totalItems: Number(totalCountResult[0]?.total_records) || 0
        }
    } catch (error) {
        throw AppError.internal()
    }
}

const sanitizeSearchValue = (value) => {
    return value.replace(/[%_]/g, '\\$&') // Escape % and _ for SQL LIKE
}

const handleCondition = (mappedColumn, operator, value) => {
    const safeColumn = mappedColumn.replace(/\./g, '_')
    const sanitizedOperator = operator.replace(/[^a-zA-Z0-9]/g, '') // Remove special characters
    const paramName = `${safeColumn}_${sanitizedOperator}_${Date.now()}`
    let timestamp = 0
    let baseParamName = ''
    let paramValues = {}
    let paramKey = ''
    let whereClause = ''
    let whereClauses = '' //Multiple clauses

    switch (operator) {
        case 'equals':
        case '=':
            whereClause = `${mappedColumn} = :${paramName}`
            break
        case 'doesNotEqual':
        case '!=':
            whereClause = `${mappedColumn} != :${paramName}`
            break
        case 'lessthan':
        case '<':
            whereClause = `${mappedColumn} < :${paramName}`
            break
        case 'lessthanequal':
        case '<=':
            whereClause = `${mappedColumn} <= :${paramName}`
            break
        case 'greaterthan':
        case '>':
            whereClause = `${mappedColumn} > :${paramName}`
            break
        case 'greaterthanequal':
        case '>=':
            whereClause = `${mappedColumn} >= :${paramName}`
            break
        case 'contains':
            whereClause = `${mappedColumn} LIKE :${paramName}`
            break
        case 'doesNotContain':
            whereClause = `${mappedColumn} NOT LIKE :${paramName}`
            break
        case 'startsWith':
            whereClause = `${mappedColumn} LIKE :${paramName}`
            break
        case 'endsWith':
            whereClause = `${mappedColumn} LIKE :${paramName}`
            break
        case 'isEmpty':
            whereClause = `(${mappedColumn} IS NULL OR ${mappedColumn} = '')`
            break
        case 'isNotEmpty':
            whereClause = `${mappedColumn} IS NOT NULL AND ${mappedColumn} != ''`
            break
        case 'isAnyOf':
            if (!Array.isArray(value) || value.length === 0) {
                return { whereClause: '', paramName: '' }
            }
            timestamp = Date.now()
            baseParamName = `${safeColumn}_${operator}_${timestamp}`
            paramValues = {}

            whereClauses = value.map((val, index) => {
                paramKey = `${baseParamName}_${index}`
                paramValues[paramKey] = `%${val}%`
                return `${mappedColumn} LIKE :${paramKey}`
            })

            whereClause = `(${whereClauses.join(' OR ')})`

            return { whereClause, paramName: baseParamName, paramValues }
        default:
            break
    }

    return { whereClause, paramName }
}

const fetchFilterOpenItems = async (
    principalId,
    customerId,
    columnFilters,
    limit,
    offset,
    orderByClause,
    logicOperator,
    misc,
    req
) => {
    const replacements = { principalId, customerId, limit, offset }

    let oppWhereClause = `WHERE OP.OPP_CLOSE_STATUS = 0 AND OP.OPP_PRINCIPAL = :principalId AND OP.OPP_CUSTOMER = :customerId`
    let jobWhereClause = `WHERE JOB.JOB_CLOSE_STATUS = 0 AND JRC1.JOB_RELT_COMP_ID = :principalId AND JRC2.JOB_RELT_COMP_ID = :customerId`

    // Mapping fields to columns
    const mapOppsToColumnName = {
        topic: 'OPP_CUST_PROGRAM',
        distributor: 'C.COMP_NAME',
        stage: 'OPP_ACTIVITY',
        value: 'OPP_VALUE'
    }
    const mapJobsToColumnName = {
        topic: 'JOB_DESCR',
        stage: 'JAM.ACT_NAME',
        value: 'JOB_VALUE'
    }

    let opps = !!(misc?.checkPageAccess?.opportunity ?? 1)
    let job = !!(misc?.checkPageAccess?.job ?? 1)

    if (!opps && !job) return { openItems: [], totalItems: 0 }

    // Handling filters, especially for the 'type' filter
    const evaluateCondition = (typeString, filter) => {
        const filterValues = Array.isArray(filter.value)
            ? filter.value.map((v) => String(v).toLowerCase())
            : [String(filter.value).toLowerCase()]

        if (filter.operator === 'isAnyOf' && filterValues.length === 0) {
            return true
        }

        switch (filter.operator) {
            case 'contains':
            case 'isAnyOf':
                return filterValues.some((val) => typeString.includes(val))
            case 'doesNotContain':
                return filterValues.every((val) => !typeString.includes(val))
            case 'equals':
                return filterValues.includes(typeString)
            case 'doesNotEqual':
                return !filterValues.includes(typeString)
            case 'startsWith':
                return filterValues.some((val) => typeString.startsWith(val))
            case 'endsWith':
                return filterValues.some((val) => typeString.endsWith(val))
            case 'isEmpty':
                return false
            case 'isNotEmpty':
                return true
            default:
                return true
        }
    }

    if (Array.isArray(columnFilters?.items) && columnFilters.items.length > 0) {
        const entityFilters = { opp: [], job: [] }
        const filterCounts = {}
        let filterIndex = 0

        columnFilters.items.forEach((filter) => {
            const { field, operator, value } = filter

            if (
                (!value || String(value).trim() === '') &&
                operator !== 'isEmpty' &&
                operator !== 'isNotEmpty' &&
                field !== 'value'
            ) {
                return
            }

            if (field === 'value') {
                if (
                    (value === undefined || value === '') &&
                    operator !== 'isEmpty' &&
                    operator !== 'isNotEmpty'
                ) {
                    return
                }
            }

            // Track count per field-operator combo for uniqueness
            filterCounts[field] = (filterCounts[field] || 0) + 1
            const uniqueId = `${field}_${operator}_${filterCounts[field]}`
            filterIndex++

            // Assign values based on operator
            switch (operator) {
                case 'contains':
                case 'doesNotContain':
                    replacements[uniqueId] = `%${escapeSQLLike(value)}%`
                    break
                case 'startsWith':
                    replacements[uniqueId] = `${escapeSQLLike(value)}%`
                    break
                case 'endsWith':
                    replacements[uniqueId] = `%${escapeSQLLike(value)}`
                    break
                default:
                    replacements[uniqueId] = value
            }

            const entities = [
                { key: 'opp', enabled: opps, map: mapOppsToColumnName },
                { key: 'job', enabled: job, map: mapJobsToColumnName }
            ]

            entities.forEach(({ key, enabled, map }) => {
                if (enabled && map[field]) {
                    const mappedColumn = map[field]

                    if (
                        operator === 'isAnyOf' &&
                        (!Array.isArray(value) || value.length === 0)
                    ) {
                        return
                    }

                    let { whereClause, paramName, paramValues } =
                        handleCondition(mappedColumn, operator, value)

                    if (operator === 'isAnyOf') {
                        Object.assign(replacements, paramValues)
                        entityFilters[key].push(whereClause)
                    } else {
                        const uniqueParamName = `${paramName}_${filterIndex}`
                        replacements[uniqueParamName] = replacements[uniqueId]
                        entityFilters[key].push(
                            whereClause.replace(paramName, uniqueParamName)
                        )
                    }
                } else if (field === 'type') {
                    // Handle type filter explicitly
                    const typeMatch = evaluateCondition(
                        key === 'opp'
                            ? opportunityCustomLabel?.toLowerCase()
                            : jobCustomLabel?.toLowerCase(),
                        filter
                    )

                    if (typeMatch) {
                        entityFilters[key].push('1=1') // Always true for matching type
                    } else {
                        entityFilters[key].push('1=0') // Always false for non-matching type
                    }
                }

                // Apply type-based filtering logic
                const keyBasedFilter = (key, field, operator) => {
                    // Type-based filtering logic
                    if (key === 'job') {
                        if (field === 'distributor') {
                            if (operator === 'isEmpty') {
                                job = true // Include jobs with NULL distributors
                                entityFilters.job.push(`(1=1)`)
                            } else if (
                                operator === 'isNotEmpty' &&
                                logicOperator === 'OR'
                            ) {
                                job = true
                            } else if (logicOperator === 'OR') {
                                job = true
                            } else {
                                job = false // Exclude jobs entirely for other operators
                            }
                        }
                    }
                }

                keyBasedFilter(key, field, operator)
            })
        })

        // Combine additional conditions
        if (opps) {
            if (entityFilters.opp.length) {
                const additionalConditions = entityFilters.opp.join(
                    ` ${logicOperator} `
                )
                oppWhereClause +=
                    (oppWhereClause ? ' AND ' : ' WHERE ') +
                    `(${additionalConditions})`
            } else {
                oppWhereClause = oppWhereClause || 'WHERE 1=1'
            }
        }

        if (job) {
            if (entityFilters.job.length) {
                const additionalConditions = entityFilters.job.join(
                    ` ${logicOperator} `
                )
                jobWhereClause +=
                    (jobWhereClause ? ' AND ' : ' WHERE ') +
                    `(${additionalConditions})`
            } else {
                jobWhereClause = jobWhereClause || 'WHERE 1=1'
            }
        }
    }

    //Final query
    let queryParts = []
    let totalCountQuery = []

    if (opps) {
        queryParts.push(generateQueryPart('opportunity', oppWhereClause))
        totalCountQuery.push(
            `SELECT COUNT(*) AS total_records FROM (${generateCountQueryPart(
                'opportunity',
                oppWhereClause,
                true
            )}) AS subquery`
        )
    }

    if (job) {
        queryParts.push(generateQueryPart('job', jobWhereClause))
        totalCountQuery.push(
            `SELECT COUNT(*) AS total_records FROM (${generateCountQueryPart(
                'job',
                jobWhereClause,
                true
            )}) AS subquery`
        )
    }

    if (queryParts.length === 0) {
        return []
    }

    const finalCountQuery = `
        SELECT SUM(total_records) AS total_records
        FROM (${totalCountQuery.join(' UNION ')}) AS query_count
    `

    const totalCountResult = await totalCount(finalCountQuery, replacements, req)
    const totalItems = totalCountResult.length
        ? Number(totalCountResult[0].total_records)
        : 0

    const finalQuery = `
        SELECT *
        FROM (${queryParts.join(' UNION ')})
        AS filtered_results
        ${orderByClause}
        LIMIT :limit OFFSET :offset
    `

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const filteredItems = await sequelize.query(finalQuery, {
        replacements,
        type: sequelize.QueryTypes.SELECT
    })

    return { openItems: filteredItems, totalItems }
}

const escapeSQLLike = (str) => {
    return String(str).replace(/[\\%_]/g, (char) => '\\' + char)
}

const updateTable = async (
    tableName,
    idField,
    id,
    fields,
    columnMap,
    userId = null,
    updDate = null,
    req
) => {
    if (!id || (Array.isArray(id) && id.length === 0)) {
        throw AppError.badRequest()
    }

    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    if (userId && updDate) {
        fields.userId = userId
        fields.updDate = updDate
        columnMap.userId = 'UPD_USER'
        columnMap.updDate = 'UPD_DATE'
    }

    if (fields?.value) {
        fields.value = fields.value.replace(/,/g, '')
    }

    const idList = Array.isArray(id) ? id : [id]

    // Get only the fields that exist in columnMap
    const validFields = Object.keys(fields).filter(
        (key) => columnMap[key] !== undefined
    )

    // Generate query parts only for valid fields
    const updates = validFields.map((key) => `${columnMap[key]} = ?`)

    //If the only updates are UPD_USER and UPD_DATE, return early and not calling updateQuery
    if (
        updates.length === 2 &&
        updates.includes('UPD_USER = ?') &&
        updates.includes('UPD_DATE = ?')
    ) {
        return 0
    }

    const updateQuery = `
        UPDATE ${tableName}
        SET ${updates.join(', ')}
        WHERE ${idField} ${
        idList.length > 1 ? `IN (${idList.map(() => '?').join(', ')})` : '= ?'
    }
    `

    // Extract values **in the correct order** as its using sequelize.query and not sequelize
    const values = validFields.map((key) => fields[key]).concat(idList)

    const updateResult = await sequelize.query(updateQuery, values)

    return updateResult?.affectedRows || 0
}

const updateOpportunityTable = async (id, fields, userId, updDate, req) => {
    const mapOppsToColumnName = {
        topic: 'OPP_CUST_PROGRAM',
        value: 'OPP_VALUE'
    }
    await updateTable(
        'OPPORTUNITIES',
        'OPP_ID',
        id,
        fields,
        mapOppsToColumnName,
        userId,
        updDate,
        req
    )
}

const updateOppActivityOppTable = async (id, recId, userId, updDate, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    if (!id || !userId) throw AppError.badRequest()

    const result = await sequelize.query(
        `
            SELECT REC_ID,
                ACT_NAME,
                ACT_STATUS,
                ACT_POTENTIAL,
                ACT_CLOSE_STATUS
            FROM OPP_ACTIVITIES_MST
            WHERE REC_ID = :recId`,
        { replacements: { recId }, type: QueryTypes.SELECT }
    )

    if (!result.length || !result[0].REC_ID || !result[0].ACT_NAME) {
        throw AppError.internal()
    }

    const actName = result[0].ACT_NAME
    const actStatus = result[0].ACT_STATUS
    const actPotential = result[0].ACT_POTENTIAL

    const updateResult = await updateTable(
        'OPPORTUNITIES',
        'OPP_ID',
        id,
        { actName, actStatus, actPotential },
        {
            actName: 'OPP_ACTIVITY',
            actStatus: 'OPP_STATUS',
            actPotential: 'OPP_POTENTIAL'
        },
        userId,
        updDate,
        req
    )

    if (!updateResult || updateResult.affectedRows === 0) {
        return
    }

    const hostName = process.env.HOST_NAME

    const url = `https://${hostName}/rf_api/activityActionHandler.php?opp_id=${id}&user_id=${userId}`
    try {
        await axios.get(url)
    } catch (error) {
        throw AppError.internal()
    }
}

const updateJobTable = async (id, fields, userId, updDate, req) => {
    const mapJobsToColumnName = {
        topic: 'JOB_DESCR',
        value: 'JOB_VALUE'
    }
    await updateTable(
        'JOBS',
        'JOB_ID',
        id,
        fields,
        mapJobsToColumnName,
        userId,
        updDate,
        req
    )
}

const updateJobActivitiyJobTable = async (id, recId, userId, updDate, req) => {
    if (!id) throw AppError.badRequest()

    // Use the tenant-specific Sequelize instance    
    const sequelize = req.tenantSequelize;    

    const result = await sequelize.query(
        `
        SELECT REC_ID as recId
        FROM JOB_ACTIVITIES_MST
        WHERE REC_ID = :recId
        `,
        { replacements: { recId }, type: QueryTypes.SELECT }
    )

    if (!result.length) {
        throw AppError.internal()
    }

    const actNameId = result[0].recId

    await updateTable(
        'JOBS',
        'JOB_ID',
        id,
        { actNameId },
        { actNameId: 'JOB_ACTIVITY' },
        userId,
        updDate,
        req
    )
}

const updateQuoteTable = async (recId, fields, userId, updDate, req) => {
    const mapQuotesToColumnName = {
        topic: 'QUOT_CUST_PROGRAM',
        value: 'QUOT_VALUE'
    }
    await updateTable(
        'QUOTES_HDR',
        'REC_ID',
        recId,
        fields,
        mapQuotesToColumnName,
        userId,
        updDate,
        req
    )
}

const processOpportunityUpdate = (id, fields, userId, updDate, req) => {
    const updates = []

    if (fields.stage?.recId && fields.stage?.name) {
        updates.push(
            updateOppActivityOppTable(id, fields.stage.recId, userId, updDate, req)
        )
    }

    if (fields) {
        updates.push(updateOpportunityTable(id, fields, userId, updDate, req))
    }

    return updates
}

const processJobUpdate = async (id, fields, userId, updDate, req) => {
    const updates = [updateJobTable(id, fields, userId, updDate, req)]

    if (fields.stage?.recId && fields.stage?.name) {
        updates.push(
            updateJobActivitiyJobTable(id, fields.stage.recId, userId, updDate, req)
        )
    }

    if (fields.checkBoxEnabled) {
        await checkLinkedRecords(id, fields, userId, updDate, req)
    }

    return updates
}

const processUpdateByType = async (entityType, id, fields, userId, updDate) => {
    switch (entityType) {
        case 'opportunity':
            return processOpportunityUpdate(id, fields, userId, updDate, req)
        case 'job':
            return await processJobUpdate(id, fields, userId, updDate, req)
        default:
            return []
    }
}

const updateOpenItems = async (req, res, next) => {
    try {
        const { recordType, id, ...fields } = req.body
        const userId = req.session.userId

        dayjs.extend(utc)
        const updDate = dayjs().utc().format('YYYY-MM-DD HH:mm:ss')

        if (!id || !recordType || !Object.keys(fields).length) {
            return next(AppError.badRequest())
        }

        const entityType = String(recordType).toLowerCase()
        const updates = await processUpdateByType(
            entityType,
            id,
            fields,
            userId,
            updDate,
            req
        )

        if (!updates.length) {
            return next(AppError.badRequest())
        }

        await Promise.all(updates)

        return res.status(200).json({ message: 'Updated successfully' })
    } catch (error) {
        return next(AppError.internal())
    }
}

const getStageList = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let gridName = req.params.gridName?.toUpperCase()
        let principalId = req.params.principalId

        if (!gridName) {
            return next(AppError.badRequest())
        }

        let dropdownList = []
        let query = ''

        switch (gridName) {
            case 'JOB':
                query = `
                    SELECT 
                        REC_ID as recId,
                        ACT_NAME as name
                    FROM JOB_ACTIVITIES_MST
                    ORDER BY ACT_SORT_ID ASC
                `

                dropdownList = await sequelize.query(query, {
                    type: QueryTypes.SELECT
                })
                break

            case 'OPPORTUNITY':
                if (!principalId) {
                    return next(AppError.badRequest())
                }

                query = `
                    SELECT 
                        REC_ID as recId,
                        ACT_NAME as name
                    FROM OPP_ACTIVITIES_MST
                    WHERE ACT_COMP_ID = :principalId
                    ORDER BY ACT_SORT_ID ASC
                `

                dropdownList = await sequelize.query(query, {
                    replacements: { principalId },
                    type: QueryTypes.SELECT
                })

                //If No Results, Call Fallback Default Query
                if (dropdownList.length === 0) {
                    query = `
                        SELECT 
                            REC_ID as recId,
                            ACT_NAME as name
                        FROM OPP_ACTIVITIES_MST
                        WHERE ACT_COMP_ID = 0
                        ORDER BY ACT_SORT_ID ASC
                    `

                    dropdownList = await sequelize.query(query, {
                        type: QueryTypes.SELECT
                    })
                }

                break

            default:
                return next(AppError.badRequest())
        }

        return res.status(200).json(dropdownList)
    } catch (error) {
        return next(AppError.internal())
    }
}

const checkOpportunityRecord = async (body, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let { id, topic, distributorId, customerId, principalId } = body
        let replacements = { topic, distributorId, customerId, principalId, id }

        const result = await sequelize.query(
            `
            SELECT 
                REC_ID as recId
            FROM OPPORTUNITIES
            WHERE OPP_CUST_PROGRAM = :topic
            AND OPP_DISTRI = :distributorId
            AND OPP_CUSTOMER = :customerId
            AND OPP_PRINCIPAL = :principalId
            AND REC_ID != :id
            `,
            {
                replacements: replacements,
                type: sequelize.QueryTypes.SELECT
            }
        )

        return result.length > 0
    } catch (error) {
        throw AppError.internal()
    }
}

const checkLinkedRecords = async (id, fields, userId, updDate, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        if (!id || !fields || !fields.checkBoxEnabled) {
            throw AppError.badRequest()
        }

        const enabledEntities = fields.checkBoxEnabled
            .filter((entry) => {
                const key = Object.keys(entry).find((k) => k !== 'recordType')
                return entry[key] === true
            })
            .map((entry) => entry.recordType.toLowerCase())
            .filter((type) => type === 'opportunity' || type === 'quotes')

        let results = {}

        for (const type of enabledEntities) {
            let query = ''
            let replacements = { id }

            if (type === 'opportunity') {
                query = `SELECT JOB_OPP_ID FROM JOB_OPPS WHERE JOB_ID = :id`
            } else if (type === 'quotes') {
                query = `SELECT JOB_QUOT_ID FROM JOB_QUOTES WHERE JOB_ID = :id`
            } else {
                continue
            }

            const result = await sequelize.query(query, {
                replacements,
                type: sequelize.QueryTypes.SELECT
            })

            results[type] = result.map(
                (row) =>
                    row[type === 'opportunity' ? 'JOB_OPP_ID' : 'JOB_QUOT_ID']
            )
        }

        if (results['opportunity']?.length > 0) {
            await updateOpportunityTable(
                results['opportunity'],
                fields,
                userId,
                updDate,
                req
            )
        }
        if (results['quotes']?.length > 0) {
            await updateQuoteTable(results['quotes'], fields, userId, updDate, req)
        }

        return results
    } catch (error) {
        throw AppError.internal()
    }
}

const preUpdateValidation = async (req, res, next) => {
    try {
        const body = req.body
        const recordType = body?.recordType?.toLowerCase()

        let recordExists = false

        const recordCheckers = {
            opportunity: checkOpportunityRecord,
            job: async () => true
        }

        let response = {
            message: 'Record can be updated',
            duplicateRecordExist: false,
            canUpdate: true,
            displaySoftWarning: false,
            displayError: false,
            popUpHeader: null,
            checkBoxEnabled: null
        }

        if (!recordCheckers[recordType]) {
            return res.status(200).json(response)
        }

        recordExists = await recordCheckers[recordType](body, req)

        const customLabels = await getCustomLabels(req)

        const manufacturerCustomLabel = getLabel(
            'IDS_PRINCI',
            'Manufacturer',
            customLabels
        )
        const customerCustomLabel = getLabel(
            'IDS_CUSTOMER',
            'Customer',
            customLabels
        )
        const distributorCustomLabel = getLabel(
            'IDS_DISTRI',
            'Distributor',
            customLabels
        )
        const jobCustomLabel = getLabel('IDS_JOB', 'Job', customLabels)

        if (recordType === 'opportunity' && recordExists) {
            response.message = `Opportunity with same ${manufacturerCustomLabel}, ${customerCustomLabel}, ${distributorCustomLabel}, and Topic already exists`
            response.duplicateRecordExist = true
            response.canUpdate = false
            response.displayError = true
        } else if (recordType === 'job') {
            response.message = `${jobCustomLabel} Topic is changed. Do you want to update the Topic of the linked records?`
            response.canUpdate = true
            response.displaySoftWarning = true
            response.popUpHeader = `Confirmation for down flow of ${jobCustomLabel} Topic`
            response.checkBoxEnabled = [
                {
                    Opportunities: true,
                    recordType: 'Opportunity'
                },
                {
                    Quotes: true,
                    recordType: 'Quotes'
                }
            ]
        }

        const keyMappings = {
            Opportunities: getLabel('IDS_OPPS', 'Opportunities', customLabels),
            Quotes: getLabel('IDS_QUOTES', 'Quotes', customLabels)
        }

        if (response.checkBoxEnabled) {
            replaceKeys(response.checkBoxEnabled, keyMappings)
        }

        return res.status(200).json(response)
    } catch (error) {
        next(AppError.internal())
    }
}

function replaceKeys(arr, keyMappings) {
    arr.forEach((obj) => {
        let updatedObj = {}

        Object.keys(obj).forEach((key) => {
            if (key === 'recordType') {
                updatedObj[key] = obj[key]
            } else {
                const mappedKey = keyMappings[key] || key
                updatedObj[mappedKey] = obj[key]
            }
        })

        Object.keys(obj).forEach((key) => delete obj[key])
        Object.assign(obj, updatedObj)
    })
}

async function getCustomLabels(req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const [customLabels] = await sequelize.query(`
            SELECT
                LBL_ID as labelId,
                LBL_DEFAULT as labelDefault,
                LBL_CUSTOM as labelCustom,
                LBL_DESC as labelDesc
            FROM CUSTOM_LABELS
        `)
        return customLabels
    } catch (error) {
        throw new AppError.internal()
    }
}
const getLabel = (id, fallback, customLabels) => {
    return (
        customLabels
            ?.find((label) => label.labelId === id)
            ?.labelCustom?.trim() || fallback
    )
}

const getOpenItemHeaderDetail = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const openItemHeaderDetail = await sequelize.query(
            `SELECT C.COMP_NAME as principal, AJ.ACJH_COMP_NAME as companyName
            FROM ACTIVITY_JOURNAL_DTL AJD JOIN ACTIVITY_JOURNAL_HDR AJ ON AJ.ACJH_ID = AJD.ACJD_HDR_ID
            LEFT JOIN COMPANIES C ON C.COMP_ID = AJD.ACJD_PRINCIPAL
            WHERE AJD.REC_ID = :ajDtlId`,
            {
                replacements: {
                    ajDtlId: req.params.id
                },
                type: sequelize.QueryTypes.SELECT
            }
        )

        res.status(200).json(openItemHeaderDetail)
    } catch (error) {
        next(AppError.internal())
    }
}

const checkPageAccess = async (userId, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const entities = {
            opportunity: '*********',
            job: '*********'
        }

        let result = {}

        for (const entityType in entities) {
            let menuIndex = entities[entityType]
            const query = `
                SELECT
                HasPageAccess($menuIndex, $userId) AS hasAccess
            `

            const [queryResult] = await sequelize.query(query, {
                bind: { userId, menuIndex },
                type: Sequelize.QueryTypes.SELECT
            })

            result[entityType] = queryResult.hasAccess
        }

        return result
    } catch (error) {
        throw new AppError.internal()
    }
}

module.exports = {
    getOpenItems,
    getOpenItemHeaderDetail,
    fetchFilterOpenItems,
    updateOpenItems,
    getStageList,
    preUpdateValidation
}
