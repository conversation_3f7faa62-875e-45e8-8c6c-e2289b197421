const { sequelize } = require('../../models')
require('dotenv').config()
const dayjs = require('dayjs')
const utc = require('dayjs/plugin/utc')
const logger = require('../../utils/logger')
const AppError = require('../../utils/appError')
dayjs.extend(utc)

const defaultCustomGrid = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    try {
        const userId = req.session.userId

        const gridNames = req.query.GRID_NAME.trim()
        const gridNamesArray = gridNames ? gridNames.split(',') : []

        const existingGrids = await models.CUSTOM_GRID.findAll({
            where: {
                USER_ID: userId,
                GRID_NAME: { [Op.in]: gridNamesArray }
            },
            attributes: ['GRID_NAME']
        })

        const existingGridNames = existingGrids.map((grid) =>
            grid.GRID_NAME.toLowerCase()
        )

        const missingGridNames = gridNamesArray
            .map((name) => name.toLowerCase())
            .filter((name) => !existingGridNames.includes(name))

        if (missingGridNames.length > 0) {
            const [defaultCustomGrid] = await sequelize.query(
                `
                SELECT GRID_NAME as grid, 
                FIELD as field, 
                SUPPORTED_FILTERS_BY_DATA_TYPES as supportedFilters,
                CUSTOM_LABEL_ID as customLabelId
                FROM CUSTOM_GRIDS
                WHERE USER_ID = 0
                AND GRID_NAME IN (:missingGridNames)
            `,
                {
                    replacements: { missingGridNames }
                }
            )

            const newGrids = defaultCustomGrid.map((item) => ({
                USER_ID: userId,
                GRID_NAME: item.grid,
                FIELD: JSON.parse(item.field),
                SUPPORTED_FILTERS_BY_DATA_TYPES: JSON.parse(
                    item.supportedFilters
                ),
                CUSTOM_LABEL_ID: item.customLabelId
            }))

            if (newGrids.length > 0) {
                await models.CUSTOM_GRID.bulkCreate(newGrids)
            }
        }

        //To sync/Handle new column or updates from legacy
        await syncNewFields(req, gridNamesArray)

        return res.status(200).json('Success')
    } catch (error) {
        console.error('CustomGrid Error:', error);
        next(AppError.internal(error.message || error));
    }
}

const getAllCustomGrid = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const userId = req.session.userId

        const [customGrid] = await sequelize.query(
            `
            SELECT 
            REC_ID AS recId,
            GRID_NAME AS grid,
            FIELD AS field,
            SUPPORTED_FILTERS_BY_DATA_TYPES as supportedFilters,
            CUSTOM_LABEL_ID as customLabelId
            FROM CUSTOM_GRIDS
            WHERE USER_ID = $userId
        `,
            {
                bind: { userId }
            }
        )

        let parsedRecords = []

        if (customGrid.length > 0) {
            parsedRecords = customGrid.map((record) => {
                return {
                    ...record,
                    field: JSON.parse(record.field),
                    supportedFilters: JSON.parse(record.supportedFilters)
                }
            })
        }
        return res.status(200).json(parsedRecords)
    } catch (error) {
        next(AppError.internal())
    }
}

const getCustomGridByGridName = async (req, res, next) => {
    const sequelize = req.tenantSequelize;

    try {
        const gridName = req.params.gridName;
        const userId = req.session.userId;

        const [customGridByGridName] = await sequelize.query(
            `
            SELECT
            CUSTOM_GRIDS.REC_ID AS recId,
            CASE
                WHEN CUSTOM_GRIDS.CUSTOM_LABEL_ID IS NOT NULL
                    AND CAST(CUSTOM_GRIDS.CUSTOM_LABEL_ID AS UNSIGNED) <> 0
                THEN CL.LBL_CUSTOM
                ELSE NULL
            END AS title,
            GRID_NAME AS grid,
            FIELD AS field,
            SUPPORTED_FILTERS_BY_DATA_TYPES AS supportedFilters
            FROM CUSTOM_GRIDS
            LEFT JOIN CUSTOM_LABELS CL
                ON CL.LBL_ID = CAST(CUSTOM_GRIDS.CUSTOM_LABEL_ID AS UNSIGNED)
            WHERE GRID_NAME = ?
            AND USER_ID = ?;
            `,
            {
                replacements: [gridName, userId],
                type: sequelize.QueryTypes.SELECT
            }
        );

        let parsedRecords = {};
        if (customGridByGridName) {
            try {
                parsedRecords = {
                    ...customGridByGridName,
                    field: JSON.parse(customGridByGridName.field),
                    supportedFilters: JSON.parse(customGridByGridName.supportedFilters)
                };
            } catch (parseError) {
                console.error('getCustomGridByGridName: JSON parse error:', parseError);
            }
        }
        return res.status(200).json(parsedRecords);
    } catch (error) {
        console.error('CustomGrid Error:', error);
        next(AppError.internal());
    }
};

const createCustomGrid = async (req, res, next) => {
    try {
        const row = req.body
        const userId = req.session.userId

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.CUSTOM_GRID.create({
            USER_ID: userId,
            GRID_NAME: row.gridName,
            FIELD: row.field,
            SUPPORTED_FILTERS_BY_DATA_TYPES: row.supportedFilters,
            CUSTOM_LABEL_ID: row.customLabelId
        })

        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

const updateCustomGrid = async (req, res, next) => {
    try {
        const row = req.body

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const isExist = await models.CUSTOM_GRID.count({
            where: {
                REC_ID: req.params.recId
            }
        })

        if (isExist === 0) {
            next(AppError.badRequest())
            return
        }

        await models.CUSTOM_GRID.update(
            {
                GRID_NAME: row.gridName,
                FIELD: row.field,
                SUPPORTED_FILTERS_BY_DATA_TYPES: row.supportedFilters,
                CUSTOM_LABEL_ID: row.customLabelId
            },
            {
                where: {
                    REC_ID: req.params.recId
                }
            }
        )

        return res.status(200).json('Updated successfully')
    } catch (error) {
        next(AppError.internal())
    }
}

const deleteCustomGrid = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const isExist = await models.CUSTOM_GRID.count({
            where: {
                REC_ID: req.params.recId
            }
        })

        if (isExist === 0) {
            next(AppError.badRequest({ message: 'Record not found' }))
            return
        }

        await models.CUSTOM_GRID.destroy({
            where: {
                REC_ID: req.params.recId
            }
        })

        return res.status(200).json('Deleted successfully')
    } catch (error) {
        next(AppError.internal())
    }
}

const customGridFilters = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let [customGridFilters] = await sequelize.query(`
            SELECT CGF.REC_ID as recId,
            CASE
                WHEN CGF.CUSTOM_LABEL_ID IS NOT NULL AND CGF.CUSTOM_LABEL_ID != '' AND CGF.PREFIX = 0 AND CGF.SUFFIX = 0 THEN CL.LBL_CUSTOM
                WHEN CGF.CUSTOM_LABEL_ID IS NOT NULL AND CGF.CUSTOM_LABEL_ID != '' AND CGF.PREFIX = 1 THEN CONCAT(CGF.NAME, ' ', CL.LBL_CUSTOM)
                WHEN CGF.CUSTOM_LABEL_ID IS NOT NULL AND CGF.CUSTOM_LABEL_ID != '' AND CGF.SUFFIX = 1 THEN CONCAT(CL.LBL_CUSTOM, ' ', CGF.NAME)
                ELSE CGF.NAME
            END AS filterName,
            CUSTOM_LABEL_ID as customLabelId,
            DEFAULT_LABEL_ID as defaultLabelId,
            GRID_NAME as grid,
            FILTER_TYPE as filterType,
            DISPLAY_FLAG as displayFlag,
            SUB_FILTER as subFilter,
            IS_SUB_FILTER_STATIC as isSubFilterStatic,
            SUB_FILTER_SELECT_TYPE as subFilterSelectType
            FROM CUSTOM_GRIDS_FILTERS CGF
            LEFT JOIN CUSTOM_LABELS CL
            ON CL.LBL_ID = CGF.CUSTOM_LABEL_ID;
        `)

        return res.status(200).json(customGridFilters)
    } catch (error) {
        next(AppError.internal())
    }
}

const getCustomGridFiltersByGridName = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const gridName = req.params.gridName

        const customFeatures = await userCustomFeatures(
            req.session.userId,
            'SHOW_MY_AJ_JOBS_OPPS',
            req
        )

        const [customGridFiltersByGridName] = await sequelize.query(
            `
            SELECT
            CGF.REC_ID as recId,
            CASE
                WHEN CGF.CUSTOM_LABEL_ID IS NOT NULL AND CAST(CGF.CUSTOM_LABEL_ID AS UNSIGNED) <> 0 
                    AND CGF.PREFIX = 0 AND CGF.SUFFIX = 0 THEN CL.LBL_CUSTOM
                WHEN CGF.CUSTOM_LABEL_ID IS NOT NULL AND CAST(CGF.CUSTOM_LABEL_ID AS UNSIGNED) <> 0 
                    AND CGF.PREFIX = 1 THEN CONCAT(CGF.NAME, ' ', CL.LBL_CUSTOM)
                WHEN CGF.CUSTOM_LABEL_ID IS NOT NULL AND CAST(CGF.CUSTOM_LABEL_ID AS UNSIGNED) <> 0 
                    AND CGF.SUFFIX = 1 THEN CONCAT(CL.LBL_CUSTOM, ' ', CGF.NAME)
                ELSE CGF.NAME
            END AS filterName,
            CGF.CUSTOM_LABEL_ID as customLabelId,
            CGF.DEFAULT_LABEL_ID as defaultLabelId,
            CGF.GRID_NAME as grid,
            CGF.FILTER_TYPE as filterType,
            CGF.DISPLAY_FLAG as displayFlag,
            CGF.SUB_FILTER as subFilter,
            CGF.IS_SUB_FILTER_STATIC as isSubFilterStatic,
            CGF.SUB_FILTER_SELECT_TYPE as subFilterSelectType
            FROM CUSTOM_GRIDS_FILTERS CGF
            LEFT JOIN CUSTOM_LABELS CL
                ON CL.LBL_ID = CAST(CGF.CUSTOM_LABEL_ID AS UNSIGNED)
            WHERE CGF.GRID_NAME = $gridName;`,
            {
                bind: { gridName }
            }
        )

        let modifiedRecords = []
        const subFilterApiRoute = process.env.SUB_FILTER_API_ROUTE

        if (customGridFiltersByGridName.length > 0) {
            modifiedRecords = customGridFiltersByGridName.map((record) => {
                const { subFilter, isSubFilterStatic, ...rest } = record
                let additionalFields = {}

                if (subFilter === 1) {
                    if (isSubFilterStatic === 1) {
                        additionalFields = {
                            subFilterApi: `${subFilterApiRoute}/${record.defaultLabelId}`
                        }
                    } else {
                        additionalFields = {
                            subFilterApi: `${record.grid}/${record.defaultLabelId}`
                        }
                    }
                } else {
                    additionalFields = { mainDataApi: `${record.grid}` }
                }

                return { ...rest, ...additionalFields }
            })
        }

        let defaultFilter

        const filterLists = modifiedRecords.map((filter) => {
            if (
                customFeatures.userParamStatus === 0 &&
                filter.defaultLabelId === 'NONE' &&
                filter.filterType == 'TOP'
            ) {
                defaultFilter = filter.recId
            } else if (
                customFeatures.userParamStatus === 1 &&
                filter.defaultLabelId === 'MY_OPPORTUNITIES'
            ) {
                defaultFilter = filter.recId
            }
            return filter
        })

        return res.status(200).json({ defaultFilter, filterLists })
    } catch (error) {
        console.error('CustomFilter Error:', error);
        next(AppError.internal())
    }
}

const createCustomGridFilters = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const row = req.body
        await models.CUSTOM_GRID_FILTERS.create({
            NAME: row.filterName,
            DEFAULT_LABEL_ID: row.defaultLabelId,
            CUSTOM_LABEL_ID: row.customLabelId,
            GRID_NAME: row.gridName,
            FILTER_TYPE: row.filterType,
            DISPLAY_FLAG: row.displayFlag,
            SUB_FILTER: row.subFilter,
            IS_SUB_FILTER_STATIC: row.isSubFilterStatic,
            SUB_FILTER_SELECT_TYPE: row.subFilterSelectType
        })

        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

const updateCustomGridFilter = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const row = req.body
        const isExist = await models.CUSTOM_GRID_FILTERS.count({
            where: {
                REC_ID: req.params.recId
            }
        })

        if (isExist === 0) {
            next(AppError.badRequest())
            return
        }

        await models.CUSTOM_GRID_FILTERS.update(
            {
                NAME: row.filterName,
                DEFAULT_LABEL_ID: row.defaultLabelId,
                CUSTOM_LABEL_ID: row.customLabelId,
                GRID_NAME: row.gridName,
                FILTER_TYPE: row.filterType,
                DISPLAY_FLAG: row.displayFlag,
                SUB_FILTER: row.subFilter,
                IS_SUB_FILTER_STATIC: row.isSubFilterStatic,
                SUB_FILTER_SELECT_TYPE: row.subFilterSelectType
            },
            {
                where: {
                    REC_ID: req.params.recId
                }
            }
        )

        return res.status(200).json('Updated successfully')
    } catch (error) {
        next(AppError.internal())
    }
}

const deleteCustomGridFilter = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const isExist = await models.CUSTOM_GRID_FILTERS.count({
            where: {
                REC_ID: req.params.recId
            }
        })

        if (isExist === 0) {
            next(AppError.badRequest({ message: 'Record not found' }))
            return
        }

        await models.CUSTOM_GRID_FILTERS.destroy({
            where: {
                REC_ID: req.params.recId
            }
        })

        return res.status(200).json('Deleted successfully')
    } catch (error) {
        next(AppError.internal())
    }
}

const customGridSubFilters = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        let [customGridSubFilters] = await sequelize.query(`
            SELECT 
            CGSF.REC_ID AS recId,
            CGSF.CUSTOM_GRID_FILTER_ID AS customGridFilterId,
            CGSF.DISPLAY_FLAG AS displayFlag,
            CGSF.DEFAULT_LABEL_ID AS defaultLabelId,
            CGF.NAME AS filterName,
            CASE 
            WHEN CGSF.CUSTOM_LABEL_ID IS NOT NULL AND CGSF.CUSTOM_LABEL_ID != "" 
            THEN CL.LBL_CUSTOM
            ELSE CGSF.NAME
            END AS subFilterName
            FROM CUSTOM_GRIDS_SUB_FILTERS CGSF
            LEFT JOIN CUSTOM_GRIDS_FILTERS CGF 
                ON CGF.REC_ID = CGSF.CUSTOM_GRID_FILTER_ID
            LEFT JOIN CUSTOM_LABELS CL 
                ON CL.LBL_ID = CGSF.CUSTOM_LABEL_ID
        `)

        return res.status(200).json(customGridSubFilters)
    } catch (error) {
        next(AppError.internal())
    }
}

const getCustomGridSubFiltersByLabelId = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const defaultLabelId = req.params.defaultLabelId

        const [customGridSubFiltersByLabel] = await sequelize.query(
            `
            SELECT 
            CGSF.REC_ID AS recId,
            CGSF.CUSTOM_GRID_FILTER_ID AS customGridFilterId,
            CGSF.DISPLAY_FLAG AS displayFlag,
            CGSF.DEFAULT_LABEL_ID AS defaultLabelId,
            CGF.NAME AS filterName,
            CASE 
            WHEN CGSF.CUSTOM_LABEL_ID IS NOT NULL AND CGSF.CUSTOM_LABEL_ID != "" 
            THEN CL.LBL_CUSTOM
            ELSE CGSF.NAME
            END AS subFilterName
            FROM CUSTOM_GRIDS_SUB_FILTERS CGSF
            LEFT JOIN CUSTOM_GRIDS_FILTERS CGF 
                ON CGF.REC_ID = CGSF.CUSTOM_GRID_FILTER_ID
            LEFT JOIN CUSTOM_LABELS CL 
                ON CL.LBL_ID = CGSF.CUSTOM_LABEL_ID
            WHERE CGSF.DEFAULT_LABEL_ID = $defaultLabelId
        `,
            {
                bind: { defaultLabelId }
            }
        )

        return res.status(200).json(customGridSubFiltersByLabel)
    } catch (error) {
        next(AppError.internal())
    }
}

const createCustomGridSubFilters = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const row = req.body

        await models.CUSTOM_GRID_SUB_FILTERS.create({
            NAME: row.subFilterName,
            CUSTOM_LABEL_ID: row.customLabelId,
            CUSTOM_GRID_FILTER_ID: row.customGridFilterId,
            DEFAULT_LABEL_ID: row.defaultLabelId,
            SUB_FILTER_LABEL_ID: row.subFilterLabelId,
            DISPLAY_FLAG: row.displayFlag
        })

        return res.status(200).json('Success')
    } catch (error) {
        next(AppError.internal())
    }
}

const updateCustomGridSubFilter = async (req, res, next) => {
    try {
        const row = req.body
        
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const isExist = await models.CUSTOM_GRID_SUB_FILTERS.count({
            where: {
                REC_ID: req.params.recId
            }
        })

        if (isExist === 0) {
            next(AppError.badRequest())
            return
        }

        await models.CUSTOM_GRID_SUB_FILTERS.update(
            {
                NAME: row.subFilterName,
                CUSTOM_LABEL_ID: row.customLabelId,
                CUSTOM_GRID_FILTER_ID: row.customGridFilterId,
                DEFAULT_LABEL_ID: row.defaultLabelId,
                SUB_FILTER_LABEL_ID: row.subFilterLabelId,
                DISPLAY_FLAG: row.displayFlag
            },
            {
                where: {
                    REC_ID: req.params.recId
                }
            }
        )

        return res.status(200).json('Updated successfully')
    } catch (error) {
        next(AppError.internal())
    }
}

const deleteCustomGridSubFilter = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const isExist = await models.CUSTOM_GRID_SUB_FILTERS.count({
            where: {
                REC_ID: req.params.recId
            }
        })

        if (isExist === 0) {
            next(AppError.badRequest({ message: 'Record not found' }))
            return
        }

        await models.CUSTOM_GRID_SUB_FILTERS.destroy({
            where: {
                REC_ID: req.params.recId
            }
        })

        return res.status(200).json('Deleted successfully')
    } catch (error) {
        next(AppError.internal())
    }
}

const getGridFilterNameByDefaultLabelId = async (
    defaultLabelId,
    gridName,
    next,
    req
) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    try {
        const [gridFilterName] = await sequelize.query(
            `
            SELECT 
            REC_ID as customGridFilterId,
            NAME as filterName
            FROM CUSTOM_GRIDS_FILTERS
            WHERE GRID_NAME = $gridName
            AND DEFAULT_LABEL_ID = $defaultLabelId
        `,
            {
                bind: { defaultLabelId, gridName }
            }
        )

        return gridFilterName
    } catch (error) {
        return next(AppError.internal())
    }
}

const syncNewFields = async (req, gridNamesArray) => {
    try {
        const userId = req.session.userId

        for (const grid of gridNamesArray) {
            const gridName = grid
            const tableName = grid?.replace(/_/g, '')

            const userTableColumns = await getUserTableColumns(
                userId,
                tableName,
                req
            )
            if (!userTableColumns.length) continue

            const customGrid = await getCustomGrid(userId, gridName, req)
            if (!customGrid.length || !customGrid[0]?.field) continue

            sortGridData(userTableColumns, customGrid)

            const parsedCustomGridFields = parseCustomGridFields(
                customGrid[0]?.field
            )
            if (!parsedCustomGridFields) continue

            const updatedFields = mapUpdatedFields(
                parsedCustomGridFields,
                userTableColumns
            )
            await updateCustomGridTable(gridName, userId, updatedFields, req)
        }
    } catch (error) {
        logger.error(error)
        throw AppError.internal()
    }
}

const getUserTableColumns = async (userId, tableName, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    const query = `
        SELECT 
            UTC.REC_ID,
            UTC.TABLE_NAME,
            UTC.USER_ID,
            UTC.COLUMN_NAME,
            UTC.COLUMN_POSITION,
            UTC.COLUMN_VISIBLE_FLAG,
            UTC.COLUMN_FILTER_MATCH_MODE,
            UTC.COLUMN_FILTER_VALUE,
            UTC.COLUMN_WIDTH,
            UTC.COLUMN_STYLE,
            UTC.COLUMN_TYPE,
            UTC.COLUMN_RENDERED,
            UTC.CUSTOM_LBL_ID,
            CASE 
                WHEN UTC.CUSTOM_LBL_ID IS NOT NULL AND UTC.CUSTOM_LBL_ID != ''
                THEN CL.LBL_CUSTOM ELSE UTC.COLUMN_HEADER 
            END AS COLUMN_HEADER
        FROM USER_TABLE_COLUMNS UTC
        LEFT JOIN CUSTOM_LABELS CL 
            ON UTC.CUSTOM_LBL_ID = CL.LBL_ID
        WHERE LOWER(UTC.TABLE_NAME) = LOWER(:tableName)
        AND UTC.USER_ID = :userId;
    `

    const [userTableColumns] = await sequelize.query(query, {
        replacements: { userId, tableName }
    })

    if (!userTableColumns.length) {
        logger.warn(`No user table columns found for table: ${tableName}`)
    }

    return userTableColumns
}

const getCustomGrid = async (userId, gridName, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const query = `
        SELECT 
            GRID_NAME as grid, 
            FIELD as field, 
            SUPPORTED_FILTERS_BY_DATA_TYPES as supportedFilters
        FROM CUSTOM_GRIDS
        WHERE USER_ID = :userId
        AND GRID_NAME = :gridName;
    `

    const [customGrid] = await sequelize.query(query, {
        replacements: { userId, gridName }
    })

    if (!customGrid.length || !customGrid[0]?.field) {
        logger.warn(`No field data found for grid: ${gridName}`)
    }

    return customGrid
}

const sortGridData = (userTableColumns, customGrid) => {
    userTableColumns.sort((a, b) => a.COLUMN_NAME.localeCompare(b.COLUMN_NAME))

    try {
        customGrid.sort((a, b) => a.field.localeCompare(b.field))
    } catch (error) {
        logger.warn('Sorting error in customGrid:', error)
    }
}

const parseCustomGridFields = (fieldData) => {
    try {
        return Array.isArray(fieldData)
            ? fieldData
            : JSON.parse(fieldData.trim())
    } catch (error) {
        logger.error('Error parsing customGrid field:', error)
        return null
    }
}

const mapUpdatedFields = (parsedCustomGridFields, userTableColumns) => {
    return parsedCustomGridFields.map((item) => {
        const matchingColumn = userTableColumns.find(
            (column) => column.COLUMN_NAME === item.field
        )

        if (!matchingColumn) {
            logger.warn(
                `No matching user table column for field: ${item.field}`
            )
        }

        return {
            ...item,
            hidden: matchingColumn
                ? matchingColumn.COLUMN_VISIBLE_FLAG === 0
                : item.hidden,
            width: matchingColumn
                ? percentToPixels(matchingColumn.COLUMN_WIDTH)
                : item.width,
            order: matchingColumn?.COLUMN_POSITION ?? item.order
        }
    })
}

const percentToPixels = (percent) => {
    const numericValue = percent.replace(/[^\d.]/g, '')
    return numericValue * 10
}

const updateCustomGridAndUserTableColumns = async (req, res, next) => {
    try {
        const { grid, columns, columnsOrder, columnWidths } = req.body
        const userId = req.session.userId

        await validateInput(grid, userId, columns, columnsOrder, columnWidths)

        const customGrid = await fetchCustomGridOrFail(grid, userId, req)

        const { type, updates } = await buildUpdates(
            columns,
            columnsOrder,
            columnWidths
        )

        await processUpdates(type, updates, grid, userId, customGrid, req)

        return res.status(200).json({ message: 'Success' })
    } catch (error) {
        console.error('Error:', error);
        next(AppError.internal(error.message))
    }
}

const validateInput = async (
    grid,
    userId,
    columns,
    columnsOrder,
    columnWidths
) => {
    if (!grid || !userId) {
        throw new Error('Grid and userId are required')
    }

    if (!columns && !columnsOrder && !columnWidths) {
        throw new Error(
            'At least one of columns, columnsOrder, or columnWidths must be provided'
        )
    }
}

const fetchCustomGridOrFail = async (grid, userId, req) => {
    const customGrid = await fetchCustomGrid(grid, userId, req)
    if (!customGrid) {
        throw new Error('Custom grid not found')
    }
    return customGrid
}

const buildUpdates = async (columns, columnsOrder, columnWidths) => {
    const updates = {}
    let type
    if (columns) {
        Object.assign(
            updates,
            Object.fromEntries(
                Object.entries(columns).map(([k, v]) => [k, { hidden: !v }])
            )
        )
        type = 'columns'
    }

    if (columnsOrder) {
        Object.assign(
            updates,
            Object.fromEntries(
                columnsOrder.map((fieldName, index) => [
                    fieldName,
                    { order: index }
                ])
            )
        )
        type = 'columnsOrder'
    }

    if (columnWidths) {
        Object.assign(
            updates,
            Object.fromEntries(
                Object.entries(columnWidths).map(([k, v]) => [k, { width: v }])
            )
        )
        type = 'columnWidths'
    }

    return { type, updates }
}

const processUpdates = async (type, updates, grid, userId, customGrid, req) => {
    const userTableColumns = await fetchUserTableColumns(grid, userId, req)
    if (type == 'columns') {
        await processColumnVisibility(
            grid,
            userId,
            updates,
            customGrid,
            userTableColumns,
            req
        )
    }

    if (type == 'columnsOrder') {
        await processColumnOrder(
            grid,
            userId,
            updates,
            customGrid,
            userTableColumns,
            req
        )
    }

    if (type == 'columnWidths') {
        await processColumnWidths(
            grid,
            userId,
            updates,
            customGrid,
            userTableColumns,
            req
        )
    }
}

const processColumnVisibility = async (
    grid,
    userId,
    columns,
    customGrid,
    userTableColumns,
    req
) => {
    const updatedFieldData = await updateFieldData(
        'columns',
        customGrid.FIELD,
        columns
    )

    await updateCustomGridTable(grid, userId, updatedFieldData, req)
    await updateUserTableColumns(
        'columns',
        grid,
        userId,
        columns,
        userTableColumns,
        req
    )
}

const processColumnOrder = async (
    grid,
    userId,
    columnsOrder,
    customGrid,
    userTableColumns,
    req
) => {
    const updatedFieldData = await updateFieldData(
        'columnsOrder',
        customGrid.FIELD,
        columnsOrder
    )

    await updateCustomGridTable(grid, userId, updatedFieldData, req)
    await updateUserTableColumns(
        'columnsOrder',
        grid,
        userId,
        columnsOrder,
        userTableColumns,
        req
    )
}

const processColumnWidths = async (
    grid,
    userId,
    columnWidths,
    customGrid,
    userTableColumns,
    req
) => {
    const updatedFieldData = await updateFieldData(
        'columnWidths',
        customGrid.FIELD,
        columnWidths
    )
    await updateCustomGridTable(grid, userId, updatedFieldData, req)
    await updateUserTableColumns(
        'columnWidths',
        grid,
        userId,
        columnWidths,
        userTableColumns,
        req
    )
}

const updateUserTableColumns = async (
    updateType,
    grid,
    userId,
    columns,
    userTableColumns,
    req
) => {
    const updateConfigs = {
        columns: {
            columnToUpdate: 'COLUMN_VISIBLE_FLAG',
            valueMapper: async (value) => {
                return value.hidden == false ? 1 : 0
            },
            fieldMapper: async (fieldName) => fieldName
        },
        columnWidths: {
            columnToUpdate: 'COLUMN_WIDTH',
            valueMapper: async (value) => await convertToPercentage(value),
            fieldMapper: async (fieldName) => fieldName
        },
        columnsOrder: {
            columnToUpdate: 'COLUMN_POSITION',
            valueMapper: async (value) => {
                let updatedValue = Number(value.order)
                return Number(updatedValue)
            },
            fieldMapper: async (fieldName) => fieldName
        }
    }
    const config = updateConfigs[updateType]

    if (!config) {
        throw new Error(`Invalid update type: ${updateType}`)
    }

    for await (const [fieldName, value] of Object.entries(columns)) {
        const field = await config.fieldMapper(fieldName, value)
        const column = userTableColumns.find((col) => col.COLUMN_NAME == field)
        if (column) {
            const valueToUpdate = await config.valueMapper(value)
            await executeColumnUpdate(
                grid,
                userId,
                field,
                config.columnToUpdate,
                valueToUpdate,
                req
            )
        }
    }
}

const executeColumnUpdate = async (
    grid,
    userId,
    field,
    columnToUpdate,
    valueToUpdate,
    req
) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    await sequelize.query(
        `UPDATE USER_TABLE_COLUMNS
        SET ${columnToUpdate} = :valueToUpdate
        WHERE LOWER(TABLE_NAME) = LOWER(:grid)
        AND COLUMN_NAME = :field
        AND USER_ID = :userId`,
        {
            replacements: { grid, field, valueToUpdate, userId }
        }
    )
}

const fetchCustomGrid = async (grid, userId, req) => {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    const [customGrid] = await models.CUSTOM_GRID.findAll({
        where: { GRID_NAME: grid, USER_ID: userId },
        raw: true
    })
    return customGrid
}

const updateFieldData = async (type, fieldDataString, updates) => {
    const fieldData = JSON.parse(fieldDataString)
    for await (const [fieldName, newValues] of Object.entries(updates)) {
        const column = fieldData.find((col) => col.field === fieldName)

        if (column) {
            if (type === 'columnsOrder') {
                column.order = Number(newValues.order)
            } else if (type === 'columns') {
                column.hidden = newValues.hidden
            } else if (type === 'columnWidths') {
                column.width = newValues.width
            }
        }
    }

    return fieldData
}

const updateCustomGridTable = async (grid, userId, fieldData, req) => {
    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    await models.CUSTOM_GRID.update(
        {
            FIELD: fieldData,
            updDate: dayjs().utc().format('YYYY-MM-DD HH:mm:ss')
        },
        { where: { GRID_NAME: grid, USER_ID: userId } }
    )
}

const fetchUserTableColumns = async (grid, userId, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const [userTableColumns] = await sequelize.query(
        `
            SELECT
            TABLE_NAME,
            USER_ID,
            COLUMN_NAME,
            COLUMN_VISIBLE_FLAG,
            COLUMN_POSITION,
            COLUMN_WIDTH
            FROM USER_TABLE_COLUMNS
            WHERE LOWER(TABLE_NAME) = LOWER(:grid)
            AND USER_ID = :userId
        `,
        { replacements: { grid, userId } }
    )
    return userTableColumns
}

const convertToPercentage = async (value) => {
    const pixel = value.width != 0 ? value.width : 100
    return `${Math.floor(pixel / 10)}%`
}

module.exports = {
    defaultCustomGrid,
    getAllCustomGrid,
    getCustomGridByGridName,
    createCustomGrid,
    updateCustomGrid,
    deleteCustomGrid,
    customGridFilters,
    createCustomGridFilters,
    getCustomGridFiltersByGridName,
    updateCustomGridFilter,
    deleteCustomGridFilter,
    customGridSubFilters,
    getCustomGridSubFiltersByLabelId,
    updateCustomGridSubFilter,
    createCustomGridSubFilters,
    deleteCustomGridSubFilter,
    getGridFilterNameByDefaultLabelId,
    updateCustomGridAndUserTableColumns
}
