const {
    getZeroBasedPagination,
    getZeroBasedPaginationData,
    noRoundFormatter
} = require('../../helpers')
const { sequelize } = require('../../models')
const { SIZE } = require('../../constants')
const AppError = require('../../utils/appError')
const dayjs = require('dayjs')
const {
    getGridFilterNameByDefaultLabelId
} = require('../CustomGrid/customGridController')
const customParseFormat = require('dayjs/plugin/customParseFormat')
const { exportData } = require('../../utils/exportData')
const logger = require('../../utils/logger')

const columnMap = {
    recId: 'REC_ID',
    oppId: 'OPP_ID',
    oppDate: 'OPP_DATE',
    oppSmanId: 'OPP_SMAN_ID',
    oppPrincipal: 'OPP_PRINCIPAL',
    oppDistri: 'OPP_DISTRI',
    oppCustomer: 'OPP_CUSTOMER',
    compPrivTeam: 'COMP_PRIV_TEAM',
    oppCloseStatus: 'OPP_CLOSE_STATUS',
    oppCloseDate: 'OPP_CLOSE_DATE',
    oppOwner: 'OPP_OWNER',
    oppTypeId: 'OPP_TYPE_ID',
    oppLeadSrcId: 'OPP_LEAD_SRC_ID',
    oppReportFlag: 'OPP_REPORT_FLAG',
    custName: 'CUST_NAME',
    principalName: 'PRINCI_NAME',
    distriName: 'DISTRI_NAME',
    oppSmanName: 'SMAN_NAME',
    oppActivity: 'OPP_ACTIVITY',
    oppStatus: 'OPP_STATUS',
    oppNextStep: 'OPP_NEXT_STEP',
    oppFollowUp: 'OPP_FOLLOW_UP',
    oppPriority: 'OPP_PRIORITY',
    oppPotential: 'OPP_POTENTIAL',
    oppValue: 'OPP_VALUE',
    oppCustProgram: 'OPP_CUST_PROGRAM',
    oppEau: 'OPP_EAU',
    oppProtoDate: 'OPP_PROTO_DATE',
    oppProdDate: 'OPP_PROD_DATE',
    oppCloseStatusLabel: 'OPP_CLOSE_STATUS_LABEL',
    ownerName: 'OWNER_NAME',
    oppParties: 'OPP_PARTIES',
    oppLines: 'OPP_LINES',
    oppCloseReason: 'OPP_CLOSE_REASON',
    custCity: 'CUST_CITY',
    custState: 'CUST_STATE',
    insDate: 'INS_DATE',
    oppLeadSrcName: 'OPP_LEAD_SRC_NAME',
    updUserName: 'UPD_USER_NAME',
    updDate: 'UPD_DATE',
    oppCustomerContName: 'OPP_CUSTOMER_CONT_NAME',
    oppTypeName: 'OPP_TYPE_NAME',
    oppDistributorContName: 'OPP_DISTRIBUTOR_CONT_NAME'
}

const getOpportunities = async (req, res, next) => {
    try {
        const sequelize = req.tenantSequelize;
        const row = req.body;
        const userId = req.session.userId;
        const { page, size } = await extractPaginationDetails(row);
        const { limit, offset } = getZeroBasedPagination(page, size);

        const viewAllCRMData = await userCustomFeatures(
            userId,
            'VIEW_ALL_CRM_DATA',
            req
        );

        const userCategory = await getUserCategory(userId, req);
        const privateTeamIds = await privateTeam(userId, req);
        const salesTeamIds = await salesTeam(userId, req);

        // const isAdmin = await isAdminCheck(userId, req)

        const baseQuery = await buildBaseQuery(
            userId,
            viewAllCRMData,
            userCategory,
            privateTeamIds,
            salesTeamIds
        );

        const filteredQuery = await applyFilters(baseQuery, row, userId, req);

        if (row?.export?.fileType) {
            let { fileType, fileName } = row.export;
            fileName = fileName || 'Opportunity_List';

            const queryWithSort = await addSortingAndPagination(
                filteredQuery,
                row,
                null,
                null,
                true
            );

            const data = await getDataToExport(queryWithSort, userId, req);

            const style = {
                columnWidths: 30,
                headerStyle: {
                    alignment: { horizontal: 'center' }
                }
            };

            const { content, contentType } = await exportData(
                data,
                fileType,
                fileName,
                style
            );

            const extension = fileType === 'xls' ? 'xlsx' : fileType;
            const completeFileName = `${fileName}.${extension}`;

            res.setHeader('Content-Type', contentType);
            res.setHeader(
                'Content-Disposition',
                `attachment; filename="${completeFileName}"`
            );

            res.send(Buffer.isBuffer(content) ? content : Buffer.from(content));
            return;
        }

        const finalQuery = await addSortingAndPagination(
            filteredQuery,
            row,
            limit,
            offset
        );

        const opportunities = await sequelize.query(finalQuery, [
            userId,
            limit,
            offset
        ]);

        const formattedData = await mapAndFormatData(opportunities);

        const paginateData = await preparePaginatedResponse(
            opportunities,
            formattedData
        );

        const opportunitiesData = {
            ...getZeroBasedPaginationData(paginateData, page, size),
            showTotal: await showOppValueTotal(filteredQuery, userId, req)
        };

        if (
            Array.isArray(opportunitiesData.contents) &&
            opportunitiesData.contents.length > 0 &&
            opportunitiesData.contents.every(item => item.recId === undefined)
        ) {
            opportunitiesData.contents.forEach((item, index) => {
                item.recId = index + 1; // assign 1, 2, 3, ...
            });
        }

        return res.status(200).json(opportunitiesData);
    } catch (error) {
        next(AppError.internal());
    }
}

const extractPaginationDetails = async (row) => {
    const page = row.pagination?.page ? Number.parseInt(row.pagination.page) : 0
    const sizeAsNumber = row.pagination?.size
        ? Number.parseInt(row.pagination.size)
        : SIZE

    let size = SIZE
    if (
        !Number.isNaN(sizeAsNumber) &&
        sizeAsNumber > 0 &&
        sizeAsNumber < SIZE
    ) {
        size = sizeAsNumber
    }

    return { page, size }
}

const buildBaseQuery = async (
    userId,
    viewAllCRMData,
    userCategory,
    privateTeamIds,
    salesTeamIds
) => {
    const allPrivateTeamIds = `0,${privateTeamIds}`
    let query = `
        SELECT
        REC_ID,
        OPP_ID,
        CAST(OPP_DATE AS CHAR) AS OPP_DATE,
        OPP_SMAN_ID,
        OPP_PRINCIPAL,
        OPP_DISTRI,
        OPP_CUSTOMER,
        COMP_PRIV_TEAM,
        OPP_CLOSE_STATUS,
        CAST(OPP_CLOSE_DATE AS CHAR) AS OPP_CLOSE_DATE,
        OPP_OWNER,
        OPP_TYPE_ID,
        OPP_LEAD_SRC_ID,
        OPP_REPORT_FLAG,
        CUST_NAME,
        PRINCI_NAME,
        DISTRI_NAME,
        SMAN_NAME,
        OPP_ACTIVITY,
        OPP_STATUS,
        OPP_NEXT_STEP,
        CAST(OPP_FOLLOW_UP AS CHAR) AS OPP_FOLLOW_UP,
        OPP_PRIORITY,
        OPP_POTENTIAL,
        OPP_VALUE,
        OPP_CUST_PROGRAM,
        OPP_EAU,
        CAST(OPP_PROTO_DATE AS CHAR) AS OPP_PROTO_DATE,
        CAST(OPP_PROD_DATE AS CHAR) AS OPP_PROD_DATE,
        OPP_CLOSE_STATUS_LABEL,
        OWNER_NAME,
        OPP_PARTIES,
        OPP_LINES,
        OPP_CLOSE_REASON,
        CUST_CITY,
        CUST_STATE,
        INS_DATE,
        OPP_LEAD_SRC_NAME,
        UPD_USER_NAME,
        UPD_DATE,
        OPP_CUSTOMER_CONT_NAME,
        OPP_TYPE_NAME,
        OPP_DISTRIBUTOR_CONT_NAME,
          COUNT(*) OVER() AS total_records FROM VIEW_OPP_LIST
          WHERE (
              OPP_ID IN (
                  SELECT
                  OPP_ID
                  FROM OPP_WATCHERS
                  WHERE OPP_USER_ID = ${userId}
                  OR
                  OPP_GROUP_ID IN (
                      SELECT WATCHER_GROUP_ID
                      FROM WATCHER_GROUP_MEMBERS
                  )
              )
      `

    if (viewAllCRMData.userParamStatus || userCategory.category == 1) {
        query += ` OR (1=1 AND COMP_PRIV_TEAM IN (${allPrivateTeamIds}))`
    } else {
        query += ` OR (OPP_SMAN_ID IN (${salesTeamIds}) AND COMP_PRIV_TEAM IN (${allPrivateTeamIds}))`
    }

    query += `)`
    return query
}

const applyFilters = async (query, row, userId, req) => {
    const paramsSetting = await getDynamicParams([
        'PARAM_DATE_FORMAT',
        'PARAM_TIMEZONE_ID',
        'PARAM_TIME_FORMAT'
    ], req)

    if (row.filter) {
        query = await searchFilter(row, userId, query, req)
    } else {
        query += ` AND OPP_CLOSE_STATUS = 0`
    }

    if (row.columnFilters?.items?.length) {
        query = await columnFilters(
            row,
            dateFormatSelected(paramsSetting),
            query
        )
    }

    if (row.columnFilters?.quickFilterValues?.length) {
        query = await globalFilter(
            query,
            row.columnFilters.quickFilterValues[0],
            paramsSetting
        )
    }

    return query
}

const addSortingAndPagination = (
    query,
    row,
    limit,
    offset,
    exportData = false
) => {
    const field = row.gridSort?.[0]?.field
        ? columnMap[row.gridSort[0].field]
        : 'OPP_FOLLOW_UP'
    const sort = row.gridSort?.[0]?.sort ? row.gridSort[0].sort : `DESC`
    if (exportData) {
        query += `
        ORDER BY ${field} ${sort}, REC_ID ${sort}`
    } else {
        query += `
        ORDER BY ${field} ${sort}, REC_ID ${sort}
        LIMIT ${limit} OFFSET ${offset};`
    }

    return query
}

const mapAndFormatData = async (opportunities) => {
    const data = await mapOpportunity(opportunities)
    return data.map((item) => ({
        ...item,
        viewApi: `/opploop/opportunity/OpportunityView.xhtml?opid=${item.oppId}`
    }))
}

const preparePaginatedResponse = async (opportunities, formattedData) => {
    return {
        count: opportunities.length > 0 ? opportunities[0].total_records : 0,
        rows: formattedData.length > 0 ? formattedData : [],
        formattedData
    }
}

const showOppValueTotal = async (query, userId, req) => {
    const sequelize = req.tenantSequelize;

    const [result] = await sequelize.query(query, { replacements: { userId } })
    const total = result.reduce((acc, curr) => {
        return acc + Number(curr.OPP_VALUE)
    }, 0)
    const formatter = noRoundFormatter(2)
    return formatter.format(total)
}

const searchFilter = async (row, userId, query, req) => {
    const filters = {
        NEEDS_REVIEW: () => ` AND OPP_STATUS = "Needs Review"`,
        CUSTOMER: (values) =>
            values.length ? ` AND OPP_CUSTOMER IN (${values.join(',')})` : '',
        PRINCIPAL: (values) =>
            values.length ? ` AND OPP_PRINCIPAL IN (${values.join(',')})` : '',
        DISTRIBUTOR: (values) =>
            values.length ? ` AND OPP_DISTRI IN (${values.join(',')})` : '',
        CONTACT: (values) =>
            values.length
                ? ` AND OPP_ID IN (SELECT OPP_CONT_OPP_ID FROM OPP_CONTACTS WHERE OPP_CONT_ID IN (${values.join(
                      ','
                  )}))`
                : '',
        SALES_TEAM: (values) =>
            values.length ? ` AND OPP_SMAN_ID IN (${values.join(',')})` : '',
        MY_SALES_TEAM: async () => {
            const salesTeamIds = await salesTeam(userId)
            return ` AND OPP_SMAN_ID IN (${salesTeamIds})`
        },
        FOLLOWUP_DATE: (values) =>
            values.length
                ? ` AND MONTH(OPP_FOLLOW_UP) IN (${values.join(',')})`
                : '',
        ACTIVITY_STATUS: (values) =>
            values.length
                ? ` AND OPP_STATUS IN (SELECT STAT_NAME FROM OPP_STATUS_MST WHERE REC_ID IN(${values.join(
                      ','
                  )}))`
                : '',
        CALL_PATTERN: (values) =>
            values.length
                ? ` AND OPP_CUSTOMER IN (SELECT COMP_ID FROM COMPANIES WHERE COMP_CALL_PATTERN_ID IN (${values.join(
                      ','
                  )}))`
                : '',
        CLOSED: async (values) => {
            if (!values.length) return ''
            const closedType = await subFilter(values, false, req)
            const conditions = {
                ALL: ` AND OPP_CLOSE_STATUS > 0`,
                WON: ` AND OPP_CLOSE_STATUS = 1`,
                LOST: ` AND OPP_CLOSE_STATUS = 2`
            }
            return conditions[closedType] || ''
        },
        ACTIVITY: (values) =>
            values.length
                ? ` AND OPP_ACTIVITY IN (SELECT ACT_NAME FROM OPP_ACTIVITIES_MST WHERE REC_ID IN(${values.join(
                      ','
                  )}))`
                : '',
        PRODUCTION_DATE: (values) =>
            values.length
                ? ` AND MONTH(OPP_PROD_DATE) IN(${values.join(',')})`
                : '',
        PROTOTYPE_DATE: (values) =>
            values.length
                ? ` AND MONTH(OPP_PROTO_DATE) IN (${values.join(',')})`
                : '',
        REPORTING: async (values) => {
            if (!values.length) return ''
            const reportType = await subFilter(values, false, req)
            const mappedReportType = reportType.map((type) =>
                type === 'Included' ? 1 : 0
            )
            return ` AND OPP_REPORT_FLAG IN(${mappedReportType.join(',')})`
        },
        PRIOIRITY: async (values) => {
            if (!values.length) return ''
            const priority = await subFilter(values, false, req)
            return ` AND OPP_PRIORITY IN (${priority.join(',')})`
        },
        POTENTIAL: async (values) => {
            if (!values.length) return ''
            const potential = await subFilter(values, true, req)
            return getSubFilterPotential(potential)
        },
        OPP_OWNER: () => ` AND OPP_OWNER = ${userId}`,
        MY_OPPORTUNITIES: () => ` AND OPP_OWNER = ${userId}`,
        WATCHERS: (values) =>
            values.length
                ? ` AND OPP_PRINCIPAL IN(SELECT OPP_ID FROM OPP_WATCHERS WHERE OPP_USER_ID IN (${values.join(
                      ','
                  )}))`
                : '',
        OPP_WITH_ACTIONS: () =>
            ` AND OPP_ID IN(SELECT OPP_ID FROM OPP_ACTIVITY_ACTIONS)`,
        CRM_SYNCED_OPPS: () =>
            ` AND OPP_ID IN (SELECT CRMSYNC_RF_OPP_ID FROM CRMSYNC_OPP_LINKS WHERE CRMSYNC_TS_OPP_ID IS NOT NULL AND CRMSYNC_TS_OPP_ID != '')`,
        OPP_TYPE: (values) =>
            values.length ? ` AND OPP_TYPE IN (${values.join(',')})` : '',
        CRM_UNSYNCED_OPPS: () => `
              AND OPP_PRINCIPAL IN (SELECT CRMSYNC_PRINCI_ID FROM CRMSYNC_USERS WHERE CRMSYNC_USER_ID = 1 AND CRMSYNC_PRINCI_ID > 0)
              AND (OPP_ID NOT IN(SELECT CRMSYNC_RF_OPP_ID FROM CRMSYNC_OPP_LINKS WHERE CRMSYNC_TS_OPP_ID != ''))
              AND (OPP_ID IN(SELECT OPP_ID FROM OPPORTUNITIES WHERE OPP_CRM_LINK IS NULL OR OPP_CRM_LINK = ''))
          `,
        LEAD_SOURCE: (values) =>
            values.length
                ? ` AND OPP_LEAD_SRC_ID IN (${values.join(',')})`
                : '',
        OPPORTUNITY_STAGE: (values) =>
            values.length
                ? ` AND OPP_ACTIVITY IN (
              SELECT ACT_NAME FROM OPP_ACTIVITIES_MST WHERE ACT_STAGE_NAME = (
              SELECT ACT_STAGE_NAME FROM OPP_ACTIVITY_STAGE_MST WHERE OPP_ACT_STAGE_ID IN (${values.join(
                  ','
              )})))`
                : '',
        OPPORTUNITY_WITH_SAMPLES: () =>
            ` AND OPP_ID IN(SELECT OPP_ID FROM SAMPLES_HDR)`,
        OPPORTUNITY_WITH_PENDING_SAMPLES: () =>
            ` AND OPP_ID IN(SELECT OPP_ID FROM OPP_ACTIVITY_ACTIONS WHERE ACTION_STATUS=0)`,
        FUTURE_OPP_FOLLOW_UP_DATE: () => ` AND OPP_FOLLOW_UP >= curdate()`,
        OPP_FOLLOW_UP_TODAY_OR_OLDER: () => ` AND OPP_FOLLOW_UP <= curdate()`
    }

    const filtersInput = row.filter

    const filterPromises = Object.entries(filtersInput).map(
        async ([key, values]) => {
            if (filters[key]) {
                const filter = await filters[key](values || [])
                query += filter
            }
        }
    )
    await Promise.all(filterPromises)

    if (!filtersInput.ALL_OPPORTUNITIES && !filtersInput.CLOSED) {
        query += ` AND OPP_CLOSE_STATUS = 0`
    }

    return query
}

dayjs.extend(customParseFormat)

const convertToUTC = async (searchValue, paramsSetting) => {
    if (!searchValue || typeof searchValue !== 'string') {
        return { searchKey: searchValue, isTimeStamp: false }
    }

    let parsedDate
    const format = `${dateFormat[paramsSetting.PARAM_DATE_FORMAT]} ${
        timeZoneFormat[paramsSetting.PARAM_TIME_FORMAT]
    }`

    if (!dayjs(searchValue, `${format}`).isValid()) {
        return { searchKey: searchValue, isTimeStamp: false }
    }

    try {
        parsedDate = dayjs.tz(
            searchValue.trim(),
            `${format}`,
            paramsSetting.PARAM_TIMEZONE_ID
        )
    } catch (e) {
        return null
    }

    if (!parsedDate?.isValid()) {
        return null
    }

    return {
        searchKey: parsedDate.utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
        isTimeStamp: true
    }
}

const isValidDate = async (value, dateFormat) => {
    return dayjs(value, dateFormat, true).isValid()
}

const globalFilter = async (query, searchValue, paramsSetting) => {
    const { searchKey, isTimeStamp } = await convertToUTC(
        searchValue,
        paramsSetting
    )

    const dateSearch = searchKey
    const cleanedValue = searchValue.replace(/[^a-zA-Z0-9]/g, '')
    query += `
          AND
          (
              REGEXP_REPLACE(OPP_PRINCIPAL, '[^a-zA-Z0-9]', '') LIKE CONCAT('%', '${cleanedValue}', '%')
              OR REGEXP_REPLACE(CUST_NAME, '[^a-zA-Z0-9]', '') LIKE CONCAT('%', '${cleanedValue}', '%')
              OR REGEXP_REPLACE(OPP_CUST_PROGRAM, '[^a-zA-Z0-9]', '') LIKE CONCAT('%', '${cleanedValue}', '%')
              OR REGEXP_REPLACE(OPP_DISTRI, '[^a-zA-Z0-9]', '') LIKE CONCAT('%', '${cleanedValue}', '%')
              OR REGEXP_REPLACE(PRINCI_NAME, '[^a-zA-Z0-9]', '') LIKE CONCAT('%', '${cleanedValue}', '%')
              OR SMAN_NAME LIKE CONCAT('%', '${searchValue}', '%')
              OR OPP_ACTIVITY LIKE CONCAT('%', '${searchValue}', '%')
              OR OPP_STATUS LIKE CONCAT('%', '${searchValue}', '%')
              OR OPP_NEXT_STEP LIKE CONCAT('%', '${searchValue}', '%')
              OR CAST(OPP_CLOSE_STATUS_LABEL AS CHAR) LIKE CONCAT('%', '${searchValue}', '%')
              OR OWNER_NAME LIKE CONCAT('%', '${searchValue}', '%')
              OR OPP_PARTIES LIKE CONCAT('%', '${searchValue}', '%')
              OR OPP_LINES LIKE CONCAT('%', '${searchValue}', '%')
              OR OPP_CLOSE_REASON LIKE CONCAT('%', '${searchValue}', '%')
              OR CUST_CITY LIKE CONCAT('%', '${searchValue}', '%')
              OR CUST_STATE LIKE CONCAT('%', '${searchValue}', '%')
              OR OPP_LEAD_SRC_NAME LIKE CONCAT('%', '${searchValue}', '%')
              OR UPD_USER_NAME LIKE CONCAT('%', '${searchValue}', '%')
              OR DISTRI_NAME LIKE CONCAT('%', '${searchValue}', '%')
              OR OPP_CUSTOMER_CONT_NAME LIKE CONCAT('%', '${searchValue}', '%')
              OR OPP_TYPE_NAME LIKE CONCAT('%', '${searchValue}', '%')
              OR CAST(OPP_VALUE AS CHAR) LIKE CONCAT('%', '${searchValue}', '%')
              OR CAST(OPP_PRIORITY AS CHAR) LIKE CONCAT('%', '${searchValue}', '%')
              OR CAST(OPP_POTENTIAL AS CHAR) LIKE CONCAT('%', '${searchValue}', '%')
              OR CAST(OPP_EAU AS CHAR) LIKE CONCAT('%', '${searchValue}', '%')
          `

    const isDate = await isValidDate(
        searchValue,
        dateFormat[paramsSetting.PARAM_DATE_FORMAT]
    )

    if (isDate) {
        const formattedDate = dayjs(
            dateSearch,
            dateFormat[paramsSetting.PARAM_DATE_FORMAT]
        ).format('YYYY-MM-DD')
        query += `OR CAST(OPP_FOLLOW_UP AS CHAR) LIKE CONCAT('%', '${formattedDate}', '%')
                  OR CAST(OPP_PROD_DATE AS CHAR) LIKE CONCAT('%', '${formattedDate}', '%')
                  OR CAST(OPP_PROTO_DATE AS CHAR) LIKE CONCAT('%', '${formattedDate}', '%')
                  OR CAST(OPP_DATE AS CHAR) LIKE CONCAT('%', '${formattedDate}', '%')
                  OR CAST(OPP_CLOSE_DATE AS CHAR) LIKE CONCAT('%', '${formattedDate}', '%')
                  `
    }

    if (isTimeStamp) {
        const endTime = new Date(
            new Date(dateSearch).getTime() + 2 * 60 * 1000
        ).toISOString()
        query += `OR (INS_DATE BETWEEN '${dateSearch}' AND '${endTime}')
                  OR (UPD_DATE BETWEEN '${dateSearch}' AND '${endTime}')`
    }

    query += `)`
    return query
}

const mapOpportunity = async (opportunities) => {
    const transformedData = opportunities.map((record) => {
        const transformedRecord = {}
        Object.keys(columnMap).forEach((columnName) => {
            transformedRecord[columnName] = record[columnMap[columnName]]
        })
        return transformedRecord
    })

    return transformedData
}

const subFilter = async (recIds, multiFilter, req) => {
    const sequelize = req.tenantSequelize;

    const [data] = await sequelize.query(
        `SELECT SUB_FILTER_LABEL_ID FROM CUSTOM_GRIDS_SUB_FILTERS WHERE REC_ID IN (:recIds)`,
        {
            replacements: {
                recIds: recIds
            }
        }
    )
    return getSubFilterValues(data, multiFilter)
}

const getSubFilterPotential = (subFilterValues) => {
    const conditions = subFilterValues
        .map((value) => {
            const [min, max] = value.split('_')
            return `(OPP_POTENTIAL >= ${min} AND OPP_POTENTIAL <= ${max})`
        })
        .join(' OR ')

    return subFilterValues.length === 1
        ? ` AND ${conditions}`
        : ` AND (${conditions})`
}

const getSubFilterValues = (data, multiFilter) =>
    data.map(({ SUB_FILTER_LABEL_ID }) => {
        const parts = SUB_FILTER_LABEL_ID.split('_')
        return multiFilter ? parts.slice(1).join('_') : parts[1]
    })

const columnFilters = async (row, dateFormatSelected, query) => {
    const conditions = []
    row.columnFilters.items.forEach(async (filter) => {
        const mappedField = columnMap[filter.field]
        let field

        if (['INS_DATE', 'UPD_DATE'].includes(mappedField)) {
            field = `DATE_FORMAT(${mappedField}, '%Y-%m-%d %H:%i:00')`
        } else {
            field = mappedField
        }

        let value

        if (
            filter.value == undefined &&
            filter.operator != 'isEmpty' &&
            filter.operator != 'isNotEmpty'
        ) {
            return query
        }

        if (['INS_DATE', 'UPD_DATE'].includes(mappedField)) {
            const date = new Date(filter.value)
            date.setSeconds(0, 0)
            value = filter.value
                ? date.toISOString().slice(0, 19).replace('T', ' ')
                : ''
        } else if (
            [
                'OPP_FOLLOW_UP',
                'OPP_PROD_DATE',
                'OPP_PROTO_DATE',
                'OPP_DATE',
                'OPP_CLOSE_DATE'
            ].includes(mappedField)
        ) {
            const dateOnly = dayjs(filter.value, dateFormatSelected)
                .utc()
                .format('YYYY-MM-DD')
            value = dateOnly
        } else {
            value = filter.value
        }

        const applyCollation = (sql, fieldName) => {
            let needsCollation = ['OPP_CLOSE_STATUS_LABEL'].includes(fieldName)
            return needsCollation
                ? `${sql.replace("%'%", "%''%")} COLLATE utf8mb4_unicode_ci`
                : sql
        }

        let condition
        switch (filter.operator) {
            case 'equals':
            case 'is':
            case '=':
                condition = applyCollation(`${field} = '${value}'`, mappedField)
                break
            case 'doesNotEqual':
            case '!=':
                condition = applyCollation(
                    `${field} != '${value}'`,
                    mappedField
                )
                break
            case 'lessthan':
            case 'before':
            case '<':
                condition = `${field} < '${value}'`
                break
            case 'lessthanequal':
            case 'onOrBefore':
            case '<=':
                condition = `${field} <= '${value}'`
                break
            case 'greaterthan':
            case 'after':
            case '>':
                condition = `${field} > '${value}'`
                break
            case 'greaterthanequal':
            case 'onOrAfter':
            case '>=':
                condition = `${field} >= '${value}'`
                break
            case 'contains':
                if (mappedField === 'OPP_CLOSE_STATUS_LABEL') {
                    condition = applyCollation(
                        `${field} LIKE '%${value}%'`,
                        mappedField
                    )
                } else {
                    condition = regexFunction(field, value)
                }
                break
            case 'doesNotContain':
                condition = applyCollation(
                    `${field} NOT LIKE '%${value}%'`,
                    mappedField
                )
                break
            case 'startsWith':
                condition = applyCollation(
                    `${field} LIKE '${value}%'`,
                    mappedField
                )
                break
            case 'endsWith':
                condition = applyCollation(
                    `${field} LIKE '%${value}'`,
                    mappedField
                )
                break
            case 'isEmpty':
                condition = `(${field} IS NULL OR ${field} = '')`
                break
            case 'isNotEmpty':
                condition = `(${field} IS NOT NULL AND ${field} != '')`
                break
            case 'isAnyOf':
                if (Array.isArray(value) && value.length > 0) {
                    const conditions = value.map((v) => {
                        const escapedValue = v.replace(/'/g, "''")
                        if (mappedField === 'OPP_CLOSE_STATUS_LABEL') {
                            return applyCollation(
                                `${field} LIKE '%${escapedValue}%'`,
                                mappedField
                            )
                        }
                        return regexFunction(field, escapedValue)
                    })
                    condition = `(${conditions.join(' OR ')})`
                } else {
                    condition = ''
                }
                break
            default:
                condition = ''
        }
        if (condition) conditions.push(condition)
    })

    const logicOperator = row.columnFilters.logicOperator
        ? row.columnFilters.logicOperator.toUpperCase()
        : 'AND'

    let whereClause = ''
    if (conditions.length > 0) {
        const joinedConditions = conditions.join(` ${logicOperator} `)
        whereClause = ` AND (${joinedConditions})`
    }

    query += whereClause
    return query
}

const regexFunction = (field, value) => {
    return `REGEXP_REPLACE(${field}, '[^a-zA-Z0-9]', '') LIKE '%${value.replace(
        /[^a-zA-Z0-9]/g,
        ''
    )}%'`
}

const getOppsDistinctFieldByDefaultLabelId = async (req, res, next) => {
    try {
        let defaultLabelId = req.params.defaultLabelId

        const mapToColumnName = {
            CUSTOMER: 'COMP_NAME',
            PRINCIPAL: 'COMP_NAME',
            DISTRIBUTOR: 'COMP_NAME',
            CONTACT: 'CONT_FULL_NAME',
            SALES_TEAM: 'SMAN_NAME',
            ACTIVITY_STATUS: 'STAT_NAME',
            CALL_PATTERN: 'CALL_PATT_NAME',
            ACTIVITY: 'ACT_NAME',
            OPP_OWNER: 'USER_NAME',
            WATCHERS: 'USER_NAME',
            OPP_TYPE: 'OPP_TYPE_NAME',
            LEAD_SOURCE: 'OPP_LEAD_SRC_NAME',
            OPPORTUNITY_STAGE: 'OPP_ACT_STAGE_NAME',
            UPDATE_OPP_OWNER: 'USER_NAME'
        }

        let columnName = mapToColumnName[defaultLabelId]
        if (!columnName) {
            return next(AppError.internal())
        }

        let queryData = additionalQuery(defaultLabelId)
        if (!queryData?.sql) {
            return next(AppError.internal())
        }

        const [oppsFilterResult] = await getGridFilterNameByDefaultLabelId(
            defaultLabelId,
            'OPPORTUNITIES',
            next
        )

        if (!oppsFilterResult) {
            return next(AppError.internal())
        }

        let { sql, bindValues } = queryData

        const sequelize = req.tenantSequelize;

        let query = `SELECT DISTINCT REC_ID as recId, ${columnName} as subFilterName FROM ${sql}`

        const [queryResult] = await sequelize.query(query, {
            bind: bindValues
        })

        const result = queryResult.map((record) => ({
            ...record,
            defaultLabelId: defaultLabelId,
            ...oppsFilterResult,
            displayFlag: 1
        }))

        return res.status(200).json(result)
    } catch (error) {
        return next(AppError.internal())
    }
}

const additionalQuery = (defaultLabelId) => {
    switch (defaultLabelId) {
        case 'CUSTOMER':
            return {
                sql: `COMPANIES WHERE COMP_TYPE = $compType AND COMP_TAGGED = $compTagged`,
                bindValues: { compType: 2, compTagged: 1 }
            }
        case 'PRINCIPAL':
            return {
                sql: `COMPANIES WHERE COMP_TYPE = $compType`,
                bindValues: { compType: 1 }
            }
        case 'DISTRIBUTOR':
            return {
                sql: `COMPANIES WHERE COMP_TYPE = $compType`,
                bindValues: { compType: 3 }
            }
        case 'CONTACT':
            return {
                sql: `VIEW_CONT_LOOKUP`,
                bindValues: {}
            }
        case 'SALES_TEAM':
            return {
                sql: `SALESMEN_MST`,
                bindValues: {}
            }
        case 'ACTIVITY_STATUS':
            return {
                sql: `OPP_STATUS_MST`,
                bindValues: {}
            }
        case 'CALL_PATTERN':
            return {
                sql: `CALL_PATTERNS_MST`,
                bindValues: {}
            }
        case 'ACTIVITY':
            return {
                sql: `(SELECT MIN(REC_ID) AS REC_ID, ACT_NAME FROM OPP_ACTIVITIES_MST GROUP BY ACT_NAME) AS activities`,
                bindValues: {}
            }
        case 'OPP_OWNER':
        case 'UPDATE_OPP_OWNER':
        case 'WATCHERS':
            return {
                sql: `USERS WHERE USER_ID != $userId`,
                bindValues: { userId: 0 }
            }
        case 'OPP_TYPE':
            return {
                sql: `OPP_TYPES_MST`,
                bindValues: {}
            }
        case 'LEAD_SOURCE':
            return {
                sql: `OPP_LEAD_SOURCE_MST`,
                bindValues: {}
            }
        case 'OPPORTUNITY_STAGE':
            return {
                sql: `OPP_ACTIVITY_STAGE_MST`,
                bindValues: {}
            }
        default:
            return null
    }
}

const updateSelectedOpportunities = async (req, res, next) => {
    const userId = req.session.userId
    const { selectedDate, selectedOppIds, filter, selectedOppOwner } = req.body
    try {
        const filterMap = {
            UPDATE_OPP_FOLLOW_UP_DATE: 'OPP_FOLLOW_UP',
            UPDATE_OPP_PRODUCTION_DATE: 'OPP_PROD_DATE',
            UPDATE_OPP_PROTO_TYPE_DATE: 'OPP_PROTO_DATE',
            UPDATE_OPP_OWNER: 'OPP_OWNER'
        }

        if (!filterMap[filter]) {
            return res.status(400).json({ message: 'Invalid filter' })
        }

        let query = `
              UPDATE OPPORTUNITIES
              SET UPD_USER = :userId,
                  UPD_DATE = :updatedDate,
                  ${filterMap[filter]} = :updateValue
              WHERE OPP_ID IN (:selectedOppIds)`

        const replacements = {
            userId,
            updatedDate: new Date(),
            updateValue:
                filter === 'UPDATE_OPP_OWNER' ? selectedOppOwner : selectedDate,
            selectedOppIds
        }

        const sequelize = req.tenantSequelize;

        await sequelize.query(query, {
            replacements,
            type: Sequelize.QueryTypes.UPDATE
        })
        return res.status(200).json({ message: 'Success' })
    } catch (error) {
        return next(AppError.internal())
    }
}

const checkPageAccess = async (req, res, next) => {
    try {
        const userId = req.session.userId
        const query = `
            SELECT
            HasPageAccess(200000000, $userId) AS hasAccess
        `

        const sequelize = req.tenantSequelize;

        const [result] = await sequelize.query(query, {
            bind: { userId },
            type: Sequelize.QueryTypes.SELECT
        })

        return res.status(200).json(result)
    } catch (error) {
        return next(AppError.internal())
    }
}

const getCustomGridByGridName = async (userId, gridName, req) => {
    try {
        const sequelize = req.tenantSequelize;

        const [customGridByGridName] = await sequelize.query(
            `
            SELECT
            CUSTOM_GRIDS.REC_ID AS recId,
            CASE
                WHEN CUSTOM_GRIDS.CUSTOM_LABEL_ID IS NOT NULL
                    AND CAST(CUSTOM_GRIDS.CUSTOM_LABEL_ID AS UNSIGNED) <> 0
                THEN CL.LBL_CUSTOM
                ELSE NULL
            END AS title,
            GRID_NAME AS grid,
            FIELD AS field,
            SUPPORTED_FILTERS_BY_DATA_TYPES AS supportedFilters
            FROM CUSTOM_GRIDS
            LEFT JOIN CUSTOM_LABELS CL
                ON CL.LBL_ID = CAST(CUSTOM_GRIDS.CUSTOM_LABEL_ID AS UNSIGNED)
            
            WHERE GRID_NAME = $gridName
            AND USER_ID = $userId
        `,
            {
                bind: { gridName, userId },
                type: sequelize.QueryTypes.SELECT
            }
        );

        let parsedRecords = {};
        if (customGridByGridName) {
            try {
                parsedRecords = {
                    ...customGridByGridName,
                    field: JSON.parse(customGridByGridName.field),
                    supportedFilters: JSON.parse(
                        customGridByGridName.supportedFilters
                    )
                };
            } catch (parseError) {
                console.error('getCustomGridByGridName: JSON parse error', parseError);
            }
        }
        return parsedRecords;
    } catch (error) {
        throw AppError.internal();
    }
}

const formatDate = async (dateString, paramsSetting, dataType) => {
    if (!dateString) return null

    const datePart = dateFormat[paramsSetting.PARAM_DATE_FORMAT]
    const timePart = timeZoneFormat[paramsSetting.PARAM_TIME_FORMAT]
    const format = `${datePart} ${timePart}`

    let formattedDate = dateString

    if (dataType === 'date') {
        formattedDate = dayjs.utc(dateString).format(datePart)
    } else if (dataType === 'dateTime') {
        formattedDate = dayjs
            .utc(dateString)
            .tz(paramsSetting.PARAM_TIMEZONE_ID)
            .format(format)
    }

    return formattedDate
}

const replaceWithCustomLabel = async (formattedData, userId, req) => {
    try {
        const customGrid = await getCustomGridByGridName(
            userId,
            'opportunities',
            req
        );

        let gridFields = customGrid?.field || [];

        const recIdField = {
            field: 'oppId',
            data_type: 'number',
            label: 'Id',
            hidden: false,
            order: -1
        };

        if (!gridFields.some((col) => col.field === 'oppId')) {
            gridFields.push(recIdField);
        }

        gridFields = gridFields
            .filter((col) => !col.hidden)
            .sort((a, b) => a.order - b.order);

        const labelIds = gridFields
            .filter((col) => col.custom_label_id)
            .map((col) => col.custom_label_id);

        let labelMap = {};
        const sequelize = req.tenantSequelize;

        if (labelIds.length) {
            const customLabels = await sequelize.query(
                `
                SELECT LBL_ID as labelId, 
                LBL_CUSTOM as labelCustom 
                FROM CUSTOM_LABELS 
                WHERE LBL_ID IN (:labelIds)`,
                {
                    replacements: { labelIds },
                    type: sequelize.QueryTypes.SELECT
                }
            );

            customLabels.forEach((lbl) => {
                labelMap[lbl.labelId] = lbl.labelCustom;
            });
        }

        const fieldToLabel = {};
        gridFields.forEach((col) => {
            const customLabel =
                col.custom_label_id && labelMap[col.custom_label_id];
            fieldToLabel[col.field] = customLabel || col.label;
        });

        const paramsSetting = await getDynamicParams([
            'PARAM_DATE_FORMAT',
            'PARAM_TIMEZONE_ID',
            'PARAM_TIME_FORMAT'
        ], req);

        const newData = await Promise.all(
            formattedData.map(async (row) => {
                const seenLabels = {};

                const entries = await Promise.all(
                    gridFields.map(async (col) => {
                        const key = col.field;
                        let label = fieldToLabel[key] || key;

                        if (seenLabels[label]) {
                            let counter = 2;
                            let newLabel = `${label} (${counter})`;
                            while (seenLabels[newLabel]) {
                                counter++;
                                newLabel = `${label} (${counter})`;
                            }
                            label = newLabel;
                        }

                        seenLabels[label] = true;

                        let value = row[key];

                        if (
                            value &&
                            (col.data_type === 'dateTime' ||
                                col.data_type === 'date')
                        ) {
                            value = await formatDate(
                                value,
                                paramsSetting,
                                col.data_type
                            );
                        } else {
                            value = value ?? null;
                        }

                        return [label, value];
                    })
                );

                const orderedRow = Object.fromEntries(entries);
                return orderedRow;
            })
        );

        return newData;
    } catch (error) {
        throw AppError.internal();
    }
}

const getDataToExport = async (queryWithSort, userId, req) => {
    try {
        if (!queryWithSort) {
            throw AppError.badRequest();
        }

        const sequelize = req.tenantSequelize;

        let data = await sequelize.query(queryWithSort, [userId]);

        if (data?.length === 0) {
            const headers = Object.keys(data[0] || {});
            data = [headers.reduce((obj, key) => ({ ...obj, [key]: '' }), {})];
        }

        const formattedData = await mapAndFormatData(data);

        const finalData = await replaceWithCustomLabel(formattedData, userId, req);

        return finalData;
    } catch (error) {
        throw AppError.internal();
    }
}

module.exports = {
    getOpportunities,
    getOppsDistinctFieldByDefaultLabelId,
    updateSelectedOpportunities,
    checkPageAccess
}
