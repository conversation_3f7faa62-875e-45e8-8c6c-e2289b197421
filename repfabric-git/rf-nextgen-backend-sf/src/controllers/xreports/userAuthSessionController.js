const crypto = require('crypto')
const dayjs = require('dayjs')
const utc = require('dayjs/plugin/utc')
const { sequelize } = require('../../models')
const AppError = require('../../utils/appError')

const userToken = async (req, res, next) => {
    try {
        const token = await generateToke()
        const ssnId = req.session.sessionId
        const userId = req.session.userId
        const startTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
        const startTime1 = dayjs(startTime)
        const expiryTime = startTime1.add(8, 'hour')
        const [users] = await isAdmin(userId, req)
        const [params] = await instanceDetails(req)

        const details = {
            AUTH_SOURCE: 'NEXTGEN_REPORT',
            AUTH_USER_ID: userId,
            AUTH_SSN_ID: ssnId,
            AUTH_SESSION_TOKEN: token,
            AUTH_SESSION_START_TIME: startTime,
            AUTH_SESSION_EXPIRY_TIME: expiryTime,
            INSTANCE_ID: params[0].PARAM_SUBS_ID,
            INSTANCE_NAME: params[0].CLIENT,
            IS_ADMIN: users[0].USER_ADMIN_FLAG == 1 ? 1 : 0
        }

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
const models = req.tenantModels;

        await models.USER_AUTH_SESSION.create(details)

        return res.status(200).json(token)
    } catch (error) {
        next(AppError.internal())
    }
}

const AuthRefresh = async (req, res, next) => {
    try {
        dayjs.extend(utc)

        const startTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
        const startTime1 = dayjs(startTime)
        const expiryTime = startTime1.add(8, 'hour')

        const { userId, sessionId, token } = req.query

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.USER_AUTH_SESSION.findOne({
            where: {
                AUTH_USER_ID: userId,
                AUTH_SSN_ID: sessionId,
                AUTH_SESSION_TOKEN: token
            }
        }).then(async (row) => {
            if (row) {
                const expireTime = row.AUTH_SESSION_START_TIME

                const utcTime = startTime1.utc().format('YYYY-MM-DD HH:mm:ss')
                const hoursDiff = dayjs(utcTime).diff(dayjs(expireTime), 'hour')

                if (hoursDiff >= 24) {
                    next(AppError.unAuthorized('Token expired'))
                    return
                }

                const newtoken = await generateToke()
                await models.USER_AUTH_SESSION.update(
                    {
                        AUTH_SESSION_TOKEN: newtoken,
                        AUTH_SESSION_START_TIME: startTime,
                        AUTH_SESSION_EXPIRY_TIME: expiryTime
                    },
                    {
                        where: {
                            REC_ID: row.REC_ID
                        }
                    }
                )
                const [result] = await userDetails(userId, req)
                const [crmOwner] = await crmOwnerFlag(userId, req)

                const salesteam = await salesTeamWithCrmFlag(userId, req)
                const privateteam = await privateTeam(userId, req)

                const isOwnerPrevilage =
                    crmOwner.length > 0 ? crmOwner[0].status : 0

                const data = {
                    token: newtoken,
                    userName: result[0].userName,
                    userId: userId,
                    emailId: result[0].email,
                    userCategory: result[0].category,
                    salesTeam: salesteam,
                    privateTeam: privateteam,
                    isAdmin: row.IS_ADMIN,
                    instanceId: row.INSTANCE_ID,
                    instanceName: row.INSTANCE_NAME,
                    expiryTime: expiryTime,
                    isCrmOwner: isOwnerPrevilage
                }
                return res.status(200).json(data)
            } else {
                next(AppError.unAuthorized())
            }
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const AuthUser = async (req, res, next) => {
    try {
        const { userId, sessionId, token } = req.query
        dayjs.extend(utc)

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        await models.USER_AUTH_SESSION.findOne({
            where: {
                AUTH_USER_ID: userId,
                AUTH_SSN_ID: sessionId,
                AUTH_SESSION_TOKEN: token
            }
        }).then(async (row) => {
            if (row) {
                const utcTime = dayjs().utc().format()

                const expiryTime = dayjs(row.AUTH_SESSION_EXPIRY_TIME)
                if (dayjs(utcTime).isAfter(dayjs(expiryTime))) {
                    next(
                        AppError.unAuthorized(
                            'Token expired, Please refresh the token'
                        )
                    )
                    return
                }

                const [result] = await userDetails(userId, req)
                const [crmOwner] = await crmOwnerFlag(userId, req)

                const salesteam = await salesTeamWithCrmFlag(userId, req)
                const privateteam = await privateTeam(userId, req)

                const isOwnerPrevilage =
                    crmOwner.length > 0 ? crmOwner[0].status : 0

                const data = {
                    userName: result[0].userName,
                    userId: userId,
                    emailId: result[0].email,
                    userCategory: result[0].category,
                    salesTeam: salesteam,
                    privateTeam: privateteam,
                    isAdmin: row.IS_ADMIN,
                    instanceId: row.INSTANCE_ID,
                    instanceName: row.INSTANCE_NAME,
                    expiryTime: expiryTime,
                    isCrmOwner: isOwnerPrevilage
                }
                return res.status(200).json(data)
            } else {
                next(AppError.unAuthorized())
            }
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const salesTeamWithCrmFlag = async (userId, req) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    const sql = `SELECT GROUP_CONCAT(CONCAT(SGT_SMAN_ID, ':', SGT_CRM_ONLY_FLAG) ORDER BY SGT_SMAN_ID) as id FROM SALES_GROUP_TEAM 
    WHERE SALES_GROUP_TEAM.SGT_MEMBER_ID = $userId`
    const [salesTeam] = await sequelize.query(sql, {
        bind: {
            userId
        }
    })
    return salesTeam[0].id != null ? salesTeam[0].id : 0
}

async function crmOwnerFlag(userId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(
        `SELECT USER_PARAM_STATUS AS status FROM CUSTOM_USER_PARAMS
        WHERE USER_ID = $userId AND USER_PARAM_ID = 'VIEW_ALL_CRM_DATA'`,
        {
            bind: { userId }
        }
    )
}

async function userDetails(userId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(
        `SELECT U.USER_NAME AS userName,USER_CATEGORY AS category,E.USER_EMAIL_LOGIN AS email
         FROM USERS U
        LEFT JOIN USER_EMAIL_CONFIG E ON E.USER_EMAIL_USER_ID = U.USER_ID
        WHERE U.USER_ID=$userId LIMIT 1`,
        {
            bind: { userId }
        }
    )
}

async function generateToke() {
    const token = crypto.randomBytes(32)
    // Convert the string of bytes to a hexadecimal string
    return token.toString('hex').substring(0, 50)
}

async function isAdmin(userId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(
        `
    SELECT
    USERS.USER_ADMIN_FLAG
    FROM USERS
    WHERE USER_ID = $userId
    `,
        {
            bind: {
                userId
            }
        }
    )
}

async function instanceDetails(req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    return sequelize.query(
        `SELECT PARAM_SUBS_ID,substring(PARAM_URL_EMAIL, locate('//', PARAM_URL_EMAIL)+2, locate('.r', PARAM_URL_EMAIL) - locate('//', PARAM_URL_EMAIL)-2) CLIENT FROM PARAMS`
    )
}

module.exports = { userToken, AuthUser, AuthRefresh }
