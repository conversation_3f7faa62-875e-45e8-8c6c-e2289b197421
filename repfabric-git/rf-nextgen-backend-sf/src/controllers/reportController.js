const { sequelize } = require('../models')
const saveGeneralSettings = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    // Use the tenant-specific models for all DB operations in a multi-tenant environment
    const models = req.tenantModels;

    try {
        const row = req.body
        const [max] = await sequelize.query(
            'SELECT MAX(REPORT_ID) as reportId FROM REPORTS'
        )
        let reportId = max[0].reportId != null ? max[0].reportId + 1 : 1

        await models.REPORT.create({
            REPORT_ID: reportId,
            REPORT_NAME: row.name,
            REPORT_TYPE: row.type,
            CUSTOM_LABEL_ID: row.customLabelId
        })

        return res.status(200).json({
            message: 'Sucess'
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const saveReportParams = async (req, res, next) => {
    try {
        const row = req.body
        
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        row.params.forEach(async (item) => {
            await models.REPORT_PARAM.create({
                REPORT_ID: row.reportId,
                PARAM_ID: item.paramId,
                PARAM_NAME: item.name,
                MULTI_OR_SINGLE: item.multiOrSingle,
                PARAM_VALUE: item.value
            })
        })

        return res.status(200).json({
            message: 'Success'
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const setUrl = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        let data = await models.REPORT_PARAM.findAll({
            where: {
                REPORT_ID: req.params.id
            },
            raw: true
        })
        let url = process.env.REPORT_URL

        data.forEach((item, index) => {
            item['PARAM_NAME'] = item['PARAM_NAME']
                .replace(' ', '_')
                .toLowerCase()
            url =
                index == 0
                    ? url + item['PARAM_NAME'] + '=' + item['PARAM_VALUE']
                    : url + '&' + item['PARAM_NAME'] + '=' + item['PARAM_VALUE']
        })

        await models.REPORT.update(
            {
                BASE_URL: url
            },
            {
                where: {
                    REPORT_ID: req.params.id
                }
            }
        )
        return res.status(200).json({
            message: 'Sucess',
            url
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const getReports = async (req, res, next) => {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;
    
    try {
        let [results] = await sequelize.query(`
            SELECT
            REC_ID as id,
            REPORT_ID as reportId,
            REPORT_NAME as name,
            CASE
                WHEN REPORT_NAME = 'Events' THEN 'EVENTS'
                WHEN REPORT_NAME = 'Messages' THEN 'MSG'
                WHEN REPORT_NAME = 'My Quotes' THEN 'MY_QTS'
                WHEN REPORT_NAME = 'Sales By Month' THEN 'SALES_BY_MONTH'
                WHEN REPORT_NAME = 'Monthly Goal' THEN 'MONTHLY_GOAL'
                WHEN REPORT_NAME = 'Weekly Goal' THEN 'WEEKLY_GOAL'
            ELSE CUSTOM_LABEL_ID
            END as customLabelId,
            REPORT_TYPE as type,
            BASE_URL as url
            FROM REPORTS
        `)

        let dashboard = []
        let reports = []
        results.forEach((item) => {
            if (item.type == 'Dashboard') {
                dashboard.push(item)
            }
            if (item.type == 'Report') {
                reports.push(item)
            }
        })
        return res.status(200).json({
            dashboard,
            reports
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const getReportParams = async (req, res, next) => {
    try {
        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const params = await models.REPORT_PARAM.findAll({
            attributes: [
                ['REC_ID', 'id'],
                ['REPORT_ID', 'reportId'],
                ['PARAM_ID', 'paramId'],
                ['PARAM_NAME', 'name'],
                ['PARAM_VALUE', 'value'],
                ['MULTI_OR_SINGLE', 'multiOrSingle']
            ],
            where: {
                REPORT_ID: req.params.id
            }
        })
        return res.status(200).json({
            params
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const updateReports = async (req, res, next) => {
    try {
        const row = req.body

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        const isExist = await models.REPORT.count({
            where: {
                REPORT_ID: req.params.id
            }
        })

        if (isExist === 0) {
            next(AppError.badRequest())
            return
        }

        await models.REPORT.update(
            {
                REPORT_NAME: row.name,
                REPORT_TYPE: row.type,
                CUSTOM_LABEL_ID: row.customLabelId
            },
            {
                where: {
                    REPORT_ID: req.params.id
                }
            }
        )

        return res.status(200).json({
            message: 'Updated successfully'
        })
    } catch (error) {
        next(AppError.internal())
    }
}

const updateReportParams = async (req, res, next) => {
    try {
        const data = req.body.params

        // Use the tenant-specific models for all DB operations in a multi-tenant environment
        const models = req.tenantModels;

        data.forEach(async (item) => {
            await models.REPORT_PARAM.update(
                {
                    PARAM_ID: item.paramId,
                    PARAM_NAME: item.name,
                    MULTI_OR_SINGLE: item.multiOrSingle,
                    PARAM_VALUE: item.value
                },
                {
                    where: {
                        REPORT_ID: req.params.id,
                        REC_ID: item.id
                    }
                }
            )
        })

        return res.status(200).json({
            message: 'Updated successfully'
        })
    } catch (error) {
        next(AppError.internal())
    }
}

module.exports = {
    saveGeneralSettings,
    saveReportParams,
    setUrl,
    getReports,
    getReportParams,
    updateReports,
    updateReportParams
}
