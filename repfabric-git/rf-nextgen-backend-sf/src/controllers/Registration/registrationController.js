const { getPagingData } = require('../../helpers/index')

const AppError = require('../../utils/appError')

const getRegistration = async (req, res, next) => {
    try {
        const pageAsNumber = Number.parseInt(req.query.page)
        const sizeAsNumber = Number.parseInt(req.query.size)

        const sort = req.query.sort ? req.query.sort : 'item.OPP_ITEM_REGNUM'
        const order = req.query.order ? req.query.order : 'asc'

        let lowerlim = sizeAsNumber * (pageAsNumber - 1)
        let upperLim = sizeAsNumber

        let where = {}
        where = await searchByFields(req.query)
        if (Object.keys(where).length == 0) {
            where = 1
        }

        const smanId = await salesTeam(req.session.userId, req)

        // Use the tenant-specific Sequelize instance
        const sequelize = req.tenantSequelize;
    

        const privateTeam = await sequelize.query(
            `
            SELECT GROUP_CONCAT(IFNULL(PTM_TEAM_ID,0)) as id FROM 
            PRIVATE_TEAM_MEMBERS WHERE PTM_MEMBER_ID = ${req.session.userId}
                `
        )

        let privateVal = privateTeam[0].id != null ? privateTeam[0].id : 0

        console.log(`userId`, req.session.userId)

        console.log(`query`,`SELECT count(*) over() as rowCount,
            item.REC_ID as recId, 
            item.OPP_ITEM_ID as oppItemId, 
            item.OPP_ITEM_OPP_ID as oppItemOppId, 
            reg.REGI_REG_NUMBER as regNumber,
            princi.COMP_ID  as principalId,
            princi.COMP_NAME  as principal,
            princi.COMP_PRIV_TEAM  as privateTeam,
            cust.COMP_ID  as customerId,
            cust.COMP_NAME as customer,
            ifnull(distri.COMP_ID,0) as distributorId,
            ifnull(distri.COMP_NAME,'')  as distributor,
            reg.REGI_APPROVE_DATE as approvedDate,
            reg.REGI_APPROVED_BY as approvedBy,
            reg.REGI_EXPIRY_DATE expiryDate,
            reg.REGI_STATUS as regStatus,
            CASE
                WHEN reg.REGI_STATUS = 1  THEN "Approved"
                WHEN reg.REGI_STATUS = 2 THEN "Pending"
                WHEN reg.REGI_STATUS = 3 THEN "Denied"
                ELSE "Requested"
            END as status,
            item.OPP_ITEM_PART_MANF as itemPartMnf, 
            item.OPP_ITEM_PART_DESC as itemPartDesc,
            opp.OPP_SMAN_ID as smanId,
            opp.OPP_CUST_PROGRAM as itemCustProg,
            opp.OPP_VALUE as oppValue,
            ifnull(team.SMAN_NAME,'') as salesTeam,                        -- new field: Sales Team
            ifnull(cont.CONT_FULL_NAME,'') as distriContName,              -- new field: Distributor Contact
            ifnull(user.USER_NAME,'') as oppOwnerName                      -- new field: Opp Owner
            FROM OPP_LINE_ITEMS item
            LEFT JOIN OPP_REGISTRATIONS reg on reg.REGI_REG_NUMBER = item.OPP_ITEM_REGNUM AND  
            reg.REGI_OPP_ID = item.OPP_ITEM_OPP_ID
            LEFT JOIN OPPORTUNITIES opp on opp. OPP_ID = item.OPP_ITEM_OPP_ID
            LEFT JOIN COMPANIES princi on princi.COMP_ID  = opp.OPP_PRINCIPAL
            LEFT JOIN COMPANIES cust on cust.COMP_ID = opp.OPP_CUSTOMER
            LEFT JOIN COMPANIES distri on distri.COMP_ID = opp.OPP_DISTRI
            LEFT JOIN SALESMEN_MST team on team.SMAN_ID = opp.OPP_SMAN_ID   -- join Sales Team table
            LEFT JOIN CONTACTS cont on cont.CONT_ID = opp.OPP_DISTRI_CONT   -- join Contacts table
            LEFT JOIN USERS user on user.USER_ID = opp.OPP_OWNER            -- join Users table
            where reg.REGI_REG_NUMBER is not null and reg.REGI_REG_NUMBER <>''
            AND ${smanId} and princi.COMP_PRIV_TEAM in(0,${privateVal}) and ${where} 
            order by ${sort} ${order}
            limit ${lowerlim},${upperLim}`)

        const [result] =
            await sequelize.query(`SELECT count(*) over() as rowCount,
            item.REC_ID as recId, 
            item.OPP_ITEM_ID as oppItemId, 
            item.OPP_ITEM_OPP_ID as oppItemOppId, 
            reg.REGI_REG_NUMBER as regNumber,
            princi.COMP_ID  as principalId,
            princi.COMP_NAME  as principal,
            princi.COMP_PRIV_TEAM  as privateTeam,
            cust.COMP_ID  as customerId,
            cust.COMP_NAME as customer,
            ifnull(distri.COMP_ID,0) as distributorId,
            ifnull(distri.COMP_NAME,'')  as distributor,
            reg.REGI_APPROVE_DATE as approvedDate,
            reg.REGI_APPROVED_BY as approvedBy,
            reg.REGI_EXPIRY_DATE expiryDate,
            reg.REGI_STATUS as regStatus,
            CASE
                WHEN reg.REGI_STATUS = 1  THEN "Approved"
                WHEN reg.REGI_STATUS = 2 THEN "Pending"
                WHEN reg.REGI_STATUS = 3 THEN "Denied"
                ELSE "Requested"
            END as status,
            item.OPP_ITEM_PART_MANF as itemPartMnf, 
            item.OPP_ITEM_PART_DESC as itemPartDesc,
            opp.OPP_SMAN_ID as smanId,
            opp.OPP_CUST_PROGRAM as itemCustProg,
            opp.OPP_VALUE as oppValue,
            ifnull(team.SMAN_NAME,'') as salesTeam,                        -- new field: Sales Team
            ifnull(cont.CONT_FULL_NAME,'') as distriContName,              -- new field: Distributor Contact
            ifnull(user.USER_NAME,'') as oppOwnerName                      -- new field: Opp Owner
            FROM OPP_LINE_ITEMS item
            LEFT JOIN OPP_REGISTRATIONS reg on reg.REGI_REG_NUMBER = item.OPP_ITEM_REGNUM AND  
            reg.REGI_OPP_ID = item.OPP_ITEM_OPP_ID
            LEFT JOIN OPPORTUNITIES opp on opp. OPP_ID = item.OPP_ITEM_OPP_ID
            LEFT JOIN COMPANIES princi on princi.COMP_ID  = opp.OPP_PRINCIPAL
            LEFT JOIN COMPANIES cust on cust.COMP_ID = opp.OPP_CUSTOMER
            LEFT JOIN COMPANIES distri on distri.COMP_ID = opp.OPP_DISTRI
            LEFT JOIN SALESMEN_MST team on team.SMAN_ID = opp.OPP_SMAN_ID   -- join Sales Team table
            LEFT JOIN CONTACTS cont on cont.CONT_ID = opp.OPP_DISTRI_CONT   -- join Contacts table
            LEFT JOIN USERS user on user.USER_ID = opp.OPP_OWNER            -- join Users table
            where reg.REGI_REG_NUMBER is not null and reg.REGI_REG_NUMBER <>''
            AND ${smanId} and princi.COMP_PRIV_TEAM in(0,${privateVal}) and ${where} 
            order by ${sort} ${order}
            limit ${lowerlim},${upperLim}
        `)

        const count = result.length > 0 ? result[0].rowCount : 0
        const rows = result.length > 0 ? result : []

        let data = {
            count,
            rows
        }

        return res
            .status(200)
            .json(getPagingData(data, pageAsNumber, sizeAsNumber))
    } catch (error) {
        console.error('RegistrationController Error:', {
            message: error.message,
            stack: error.stack,
            error
        });
        next(AppError.internal(error.message));
    }
}

const updateRegistration = async (req, res, next) => {
    try {
        const row = req.body

        row.forEach(async (element) => {
            const oppItemOppId = element.oppItemOppId
            const regNumber = element.regNumber
            const field = element.field
            const value = element.value

            const query = `update OPP_REGISTRATIONS set ${field}=$value where 
                REGI_OPP_ID= $oppItemOppId and REGI_REG_NUMBER = $regNumber`

            // Use the tenant-specific Sequelize instance    
            const sequelize = req.tenantSequelize;

            await sequelize.query(query, {
                bind: { oppItemOppId, regNumber, value }
            })
        })
        res.status(200).json('success')
    } catch (error) {
        next(AppError.internal())
    }
}

const deleteRegistration = async (req, res, next) => {
    try {
        const row = req.body
        row.forEach(async (element) => {
            const oppItemOppId = element.oppItemOppId
            const regNumber = element.regNumber

            const query = `delete FROM OPP_REGISTRATIONS WHERE REGI_OPP_ID = $oppItemOppId and 
            REGI_REG_NUMBER=$regNumber`

            const query1 = `update OPP_LINE_ITEMS set OPP_ITEM_REGNUM='' where 
            OPP_ITEM_OPP_ID= $oppItemOppId and OPP_ITEM_REGNUM = $regNumber`

            // Use the tenant-specific Sequelize instance
            const sequelize = req.tenantSequelize;
            
            await sequelize.query(query, {
                bind: {
                    oppItemOppId,
                    regNumber
                }
            })

            await sequelize.query(query1, {
                bind: {
                    oppItemOppId,
                    regNumber
                }
            })
        })
        res.status(200).json('success')
    } catch (error) {
        next(AppError.internal())
    }
}

async function compandStatusFilter(
    principal,
    customer,
    distributor,
    status,
    salesTeam,
    distriContName,
    oppOwnerName
) {
    let where = {}
    let field, value

    if (principal != undefined && principal != '') {
        field = 'princi.COMP_NAME'
        value = `'%${principal}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (customer != undefined && customer != '') {
        field = 'cust.COMP_NAME'
        value = `'%${customer}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (distributor != undefined && distributor != '') {
        field = 'distri.COMP_NAME'
        value = `'%${distributor}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (status != undefined && status != '') {
        value = `'%${status}%'`
        where = `CASE WHEN reg.REGI_STATUS = 1  THEN "Approved" WHEN reg.REGI_STATUS = 2 THEN 
        "Pending" WHEN reg.REGI_STATUS = 3 THEN "Denied" ELSE "Requested" END like ${value}`
    }
    if (salesTeam != undefined && salesTeam != '') {
        field = 'team.SMAN_NAME'
        value = `'%${salesTeam}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (distriContName != undefined && distriContName != '') {
        field = 'cont.CONT_FULL_NAME'
        value = `'%${distriContName}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (oppOwnerName != undefined && oppOwnerName != '') {
        field = 'user.USER_NAME'
        value = `'%${oppOwnerName}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    return where
}

async function searchByFields(queryString) {
    const {
        regNumber,
        principal,
        customer,
        distributor,
        approvedDate,
        expiryDate,
        status,
        itemPartMnf,
        itemPartDesc,
        itemCustProg,
        oppValue,
        salesTeam,
        distriContName,
        oppOwnerName
    } = queryString

    let where = {}
    let field, value

    where = await compandStatusFilter(
        principal,
        customer,
        distributor,
        status,
        salesTeam,
        distriContName,
        oppOwnerName
    )

    if (regNumber != undefined && regNumber != '') {
        field = 'item.OPP_ITEM_REGNUM'
        value = `'%${regNumber}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (approvedDate != undefined && approvedDate != '') {
        field = 'reg.REGI_APPROVE_DATE'
        value = `'%${approvedDate}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (expiryDate != undefined && expiryDate != '') {
        field = 'reg.REGI_EXPIRY_DATE'
        value = `'%${expiryDate}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (itemPartMnf != undefined && itemPartMnf != '') {
        field = 'item.OPP_ITEM_PART_MANF'
        value = `'%${itemPartMnf}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (itemPartDesc != undefined && itemPartDesc != '') {
        field = 'item.OPP_ITEM_PART_DESC'
        value = `'%${itemPartDesc}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (itemCustProg != undefined && itemCustProg != '') {
        field = 'opp.OPP_CUST_PROGRAM'
        value = `'%${itemCustProg}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'STRING'
        )
    }
    if (oppValue != undefined && oppValue != '') {
        field = 'opp.OPP_VALUE'
        value = `'%${oppValue}%'`
        where = await filter(
            field,
            value,
            undefined,
            undefined,
            where,
            'NUMERIC'
        )
    }
    return where
}

async function salesTeam(userId, req) {
    // Use the tenant-specific Sequelize instance
    const sequelize = req.tenantSequelize;

    let query
    const sman = await sequelize.query(
        `
            SELECT
            GROUP_CONCAT(SGT_SMAN_ID) as id
            FROM SALES_GROUP_TEAM
            WHERE SGT_MEMBER_ID = ${userId}
        `
    )

    if (sman[0].id) {
        query = ` OPP_SMAN_ID IN(0,${sman[0].id})`
    } else {
        query = `OPP_SMAN_ID IN(0, -1)`
    }

    return query
}

const filter = async (field, value, field1, value1, where, type) => {
    switch (type) {
        case 'DATE_RANGE':
            where =
                Object.keys(where).length !== 0
                    ? where +
                      ' AND ' +
                      field +
                      ' between ' +
                      value +
                      ' and ' +
                      value1
                    : field + ' between ' + value + ' and ' + value1
            return where
        case 'NUMERIC':
            where =
                Object.keys(where).length !== 0
                    ? where + ' AND CAST(' + field + ' AS CHAR) LIKE ' + value
                    : ' CAST(' + field + ' AS CHAR) LIKE ' + value
            return where
        default:
            where =
                Object.keys(where).length !== 0
                    ? where + ' AND ' + field + ' like ' + value
                    : field + ' like ' + value
            return where
    }
}

module.exports = {
    getRegistration,
    updateRegistration,
    deleteRegistration
}
