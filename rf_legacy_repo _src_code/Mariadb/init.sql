CREATE DATABASE IF NOT EXISTS RF_CRM_DB;
CREATE DATABASE IF NOT EXISTS RF_DBD_DB;
CREATE DATABASE IF NOT EXISTS RF_ARC_DB;
CREATE DATABASE IF NOT EXISTS RF_EML_DB;

CREATE DATABASE IF NOT EXISTS RF_CRM_DB_28;
CREATE DATABASE IF NOT EXISTS RF_DBD_DB_28;
CREATE DATABASE IF NOT EXISTS RF_ARC_DB_28;
CREATE DATABASE IF NOT EXISTS RF_EML_DB_28;

CREATE DATABASE IF NOT EXISTS RF_CRM_DB_60;
CREATE DATABASE IF NOT EXISTS RF_DBD_DB_60;
CREATE DATABASE IF NOT EXISTS RF_ARC_DB_60;
CREATE DATABASE IF NOT EXISTS RF_EML_DB_60;

CREATE USER IF NOT EXISTS 'rfinternal'@'%' IDENTIFIED BY 'new_password!';
CREATE USER IF NOT EXISTS 'rfinternal'@'localhost' IDENTIFIED BY 'new_password!';
GRANT ALL PRIVILEGES on *.* to 'rfinternal'@'%';
GRANT ALL PRIVILEGES on *.* to 'rfinternal'@'localhost';
GRANT ALL PRIVILEGES on *.* to 'admin'@'%';
--grant all privileges on *.* to 'rfinternal'@'*' identified by 'new_password!' with grant option;

USE RF_CRM_DB;
SOURCE /var/lib/mysql/TEMPLATE_CRM_0.sql;
USE RF_DBD_DB;
SOURCE /var/lib/mysql/TEMPLATE_DBD_0.sql;
USE RF_ARC_DB;
SOURCE /var/lib/mysql/TEMPLATE_ARC_0.sql;
USE RF_EML_DB;
SOURCE /var/lib/mysql/TEMPLATE_EML_0.sql;

USE RF_CRM_DB;
SOURCE /var/lib/mysql/sandbox.local.RF_CRM_DB_0.sql;
USE RF_DBD_DB;
SOURCE /var/lib/mysql/sandbox.local.RF_DBD_DB_0.sql;
USE RF_ARC_DB;
SOURCE /var/lib/mysql/sandbox.local.RF_ARC_DB_0.sql;
USE RF_EML_DB;
SOURCE /var/lib/mysql/sandbox.local.RF_EML_DB_0.sql;

USE RF_CRM_DB_28;
SOURCE /var/lib/mysql/TEMPLATE_CRM_28.sql;
USE RF_DBD_DB_28;
SOURCE /var/lib/mysql/TEMPLATE_DBD_28.sql;
USE RF_ARC_DB_28;
SOURCE /var/lib/mysql/TEMPLATE_ARC_28.sql;
USE RF_EML_DB_28;
SOURCE /var/lib/mysql/TEMPLATE_EML_28.sql;

USE RF_CRM_DB_28;
SOURCE /var/lib/mysql/sandbox.local.RF_CRM_DB_28.sql;
USE RF_DBD_DB_28;
SOURCE /var/lib/mysql/sandbox.local.RF_DBD_DB_28.sql;
USE RF_ARC_DB_28;
SOURCE /var/lib/mysql/sandbox.local.RF_ARC_DB_28.sql;
USE RF_EML_DB_28;
SOURCE /var/lib/mysql/sandbox.local.RF_EML_DB_28.sql;

USE RF_CRM_DB_60;
SOURCE /var/lib/mysql/TEMPLATE_CRM_60.sql;
USE RF_DBD_DB_60;
SOURCE /var/lib/mysql/TEMPLATE_DBD_60.sql;
USE RF_ARC_DB_60;
SOURCE /var/lib/mysql/TEMPLATE_ARC_60.sql;
USE RF_EML_DB_60;
SOURCE /var/lib/mysql/TEMPLATE_EML_60.sql;

USE RF_CRM_DB_60;
SOURCE /var/lib/mysql/sandbox.local.RF_CRM_DB_60.sql;
USE RF_DBD_DB_60;
SOURCE /var/lib/mysql/sandbox.local.RF_DBD_DB_60.sql;
USE RF_ARC_DB_60;
SOURCE /var/lib/mysql/sandbox.local.RF_ARC_DB_60.sql;
USE RF_EML_DB_60;
SOURCE /var/lib/mysql/sandbox.local.RF_EML_DB_60.sql;