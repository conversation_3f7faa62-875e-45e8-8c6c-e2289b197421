<?php
/*
 * Added to sync between centralhost and other APIs
 * 27.03.2019
 */
//error_reporting(E_ALL);
//ini_set('display_errors', 1);
//echo "Domain : ". $_SERVER['SERVER_NAME'];

function call_instance_sync_service($method,$user_email,$service_id,$domain_name,$password,$user_id) {
   // echo "sync";
    //echo "domain:".$domain_name;
    $protocol = 'https';
    $domain = $domain_name;
    //$domain ="centralhost.repfabric.com";

    //echo PHP_EOL. "sync central".PHP_EOL;
    // Updated for Aurinko #600
    //define('INSTANCE_SYNC_URL',"${protocol}://${domain}" . "/rf_api/client_user.php?service_id=");
    
    $auth = 'YOXEL';   
    switch ($method) {
        case 'AURINKO':
            $auth ="AURINKO";
            $instance_url=$protocol."://".$domain."/email/aurinko_apis/get_sync_token.php?service_id=";
            $resp=execute_curl($method,$user_email,$service_id,$auth,$password,$user_id, $instance_url);
            return $resp;
            break;
        case "YOXEL":  
            $instance_url=$protocol."://".$domain."/rf_api/client_user.php?service_id=";
            $resp=execute_curl($method,$user_email,$service_id,$auth,$password,$user_id, $instance_url);
            return $resp;
            break;
        default:
            return;
    }
}

function execute_curl($method,$user_email,$service_id,$auth,$password,$user_id, $instance_url) {
   // echo "method:".$method.":user_email:".$user_email.":service_id:".$service_id.":auth:".$auth.":password:".$password.PHP_EOL;
    $curl = curl_init();
    //$url = INSTANCE_SYNC_URL . $method;
    $url = $instance_url . $method;
    if ($user_email != "") {
        $url.= "&emailId=" . urlencode($user_email);
    }
//    if ($service_id != "") {
//        $url.= "&service_id=" . $service_id;
//    }
    if ($auth != "") {
        $url.= "&user_auth=" . $auth;
    }
    if ($password != "") {
        $url.= "&password=" . urlencode($password);
    }
    if ($user_id != "") {
        $url.= "&user_id=" . $user_id;
    }
    //echo $url.PHP_EOL;
    curl_setopt_array($curl, array(
        CURLOPT_RETURNTRANSFER => 1,
        CURLOPT_URL => $url
    ));
    //var_dump($curl);
    
    $resp = curl_exec($curl);
   // var_dump($resp);die;
    curl_close($curl);

    return $resp;
}
