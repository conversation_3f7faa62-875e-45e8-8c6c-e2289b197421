<?php

/* 
 * File contains functions which are used in all over application
 * Created On 22.02.2019
 * 
 */

header("Access-Control-Allow-Origin:*");
include "../config/db_settings.php";
function db_connect() {
    $db = mysqli_connect(HOST, UNAME, PASS, DB);
    //For fixing character enconding issue
    mysqli_set_charset($db, "utf8");
    if (!$db) {
        die('could not connect:' . mysql_errno());
    }
    return $db;
}

function execute_query($qry) {
    $db = db_connect();
    $res = mysqli_query($db, $qry);
    if($res){
        return "success";
    }else{
       return "error"; 
    }
    mysqli_close($db);

}

function query_object($query) {
    $db = db_connect();
    $result = mysqli_query($db, $query);
    $output = mysqli_fetch_array($result);
    mysqli_close($db);
    return $output;
}

function query_list($query) {
    $db = db_connect();
    $result =[];
    $res = mysqli_query($db, $query) or die(mysqli_error($db));
    while($output = mysqli_fetch_array($res)){
        $result[]=$output;       
    }
    mysqli_close($db);
    return $result;
}

function query_check($query) {
    $db = db_connect();
    $result = mysqli_query($db, $query);
    $output = mysqli_num_rows($result);
    mysqli_close($db);
    return $output;
}

function RFResponse($code, $text) {
    $response['status'] = array(
        'status-code' => $code,
        'status-text' => $text
    );
    return $response;
}


function ValidateToken($serviceKey){
    $query = "SELECT SERVICE_ID FROM SERVICES WHERE SERVICE_KEY= '".$serviceKey."'";
    $result = query_object($query);
    return $result['SERVICE_ID'];
}

function execute($response) {
    if ($response != null && $response != "error") {
        $jsonResponse = json_encode($response, JSON_PRETTY_PRINT);
        header('Content-type: application/json');
        exit($jsonResponse);
    }else{
        echo json_encode($response);
    }
}

function curlPostRequestBasicAuth($username, $password, $url, $post_data) {
    header('Content-Type: application/json'); 
    $ch = curl_init();
    //echo $url;
    //echo $post_data;
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    if($username!="" && $password!=""){
        curl_setopt($ch, CURLOPT_USERPWD, $username. ":". $password);
    }
    curl_setopt($ch, CURLOPT_POST, 1); 
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data); 
    $result = curl_exec($ch); 
    curl_close($ch); 
    //var_dump($result);
    $sync_data_1 = json_decode($result);
    $sync_data = json_decode(json_encode($sync_data_1), true);
    return $sync_data;
}

function getCrmUserData($user_id, $client_id){
    $query = "SELECT USER_EMAIL, USER_ID, USER_CLIENT_ID, USER_BASE_URL,USER_LOGIN_NAME "
            . "FROM USER_INSTANCE WHERE USER_ID= ".$user_id." AND USER_CLIENT_ID=".$client_id;
    $result = query_object($query);
    return $result;
}

function getUserInfoByEmail($email, $client_id, $req_user_id){
    $user_qry ="";
    if($req_user_id!=""){
        $user_qry =" AND USER_ID= ".$req_user_id;
    }
    $query = "SELECT USER_EMAIL, USER_ID, USER_CLIENT_ID, USER_BASE_URL,USER_LOGIN_NAME "
            . "FROM USER_INSTANCE WHERE (USER_LOGIN_NAME='".$email. "' OR USER_EMAIL='".$email."')".$user_qry." AND USER_CLIENT_ID=".$client_id. " LIMIT 1";
    /*$query = "SELECT USER_EMAIL, USER_ID, USER_CLIENT_ID, USER_BASE_URL,USER_LOGIN_NAME "
            . "FROM USER_INSTANCE WHERE USER_LOGIN_NAME='".$email. "'".$user_qry." AND USER_CLIENT_ID=".$client_id. " LIMIT 1";*/
    $result = query_object($query);
    error_log(PHP_EOL."USER info query ".$query,3,"error_log.txt");
    return $result;
}

function base64UrlEncode($inputStr){
    return strtr(base64_encode($inputStr), '+/=', '-_,');
}

function base64UrlDecode($inputStr){
    return base64_decode(strtr($inputStr, '-_,', '+/='));
}

function  insertUserAuthType($user_id,$base_url, $client, $login_name,$userEmail, $auth_type){
    if(!authUserExists($userEmail,$auth_type)){
       $qry = "INSERT INTO USER_AUTH_TYPES "
                . "(USER_ID,USER_CLIENT_ID,USER_BASE_URL,USER_LOGIN_NAME, USER_LOGIN_EMAIL, USER_AUTH_TYPE) "
                . "VALUES (". $user_id . "," . $client . ",'" . $base_url . "','" . $login_name . "','".$userEmail."','". $auth_type."')";
        execute_query($qry);
    }
}

function authUserExists($userEmail,$auth_type){
    $query = "SELECT USER_ID FROM USER_AUTH_TYPES "
            . "WHERE USER_LOGIN_EMAIL='" . $userEmail . "' AND USER_AUTH_TYPE='" . $auth_type . "'";
    $result = query_check($query);
    return $result==0 ? false : true;    
}

function getBaseUrlByClientId($client_id){
    $query = "SELECT USER_BASE_URL "
            . "FROM USER_INSTANCE WHERE USER_CLIENT_ID=".$client_id. " LIMIT 1";
    $result = query_object($query);
    return $result['USER_BASE_URL'];
}

function postUserAuthData($base_url, $user_id, $login_name, $user_token, $serviceType){
    $url = 'https://'. $base_url."/rf_api/post_user_auth.php";       

    $post_data = json_encode(array('login-name'=> urldecode($login_name), 'token'=>$user_token, 'service-type'=>$serviceType,
                'user-id'=>$user_id));
    $username =$password="";
    curlPostRequestBasicAuth($username, $password, $url, $post_data);
}

function authUserExistsByInstance($userId, $clientId){
    $query = "SELECT USER_ID FROM USER_AUTH_TYPES "
            . "WHERE USER_ID=" . $userId . " AND USER_CLIENT_ID=".$clientId;
    $result = query_check($query);
    return $result==0 ? false : true;    
}

function deleteAuthUser($userId, $clientId){
    $qry_1 ="DELETE FROM USER_AUTH_TYPES WHERE USER_ID=".$userId." AND USER_CLIENT_ID=".$clientId;
    return execute_query($qry_1);
}

function curlGetRequestWithBToken($token, $url) {
    header('Content-Type: application/json');
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    $authorization = "Authorization: Bearer " . $token; 
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', $authorization)); 
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    $upd_res = curl_exec($ch);
    $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    $data = json_decode($upd_res);
    $get_data = json_decode(json_encode($data), true);
    $get_data['status_code']=  $httpcode;
    return $get_data;
}

function recursiveCurlGetBToken($token, $url, $count){
    $sync_data = curlGetRequestWithBToken($token, $url);
    if ($sync_data['status_code']==200) {
        return $sync_data;
    } else if($sync_data['status_code']==429 && $count<=2) {
        $count++;
        sleep($count);
        recursiveCurlGetBToken($token, $url, $count);    
    } else {
        return $sync_data;
    }
}

function escapeQuote($value) {
    $db = db_connect();
    $value1 = mysqli_real_escape_string($db, $value);
    return $value1;
}

function ValidateTokenAndPassword($serviceKey,$password){
    $query = "SELECT SERVICE_ID FROM SERVICES WHERE SERVICE_KEY= '".$serviceKey."' AND SERVICE_PASSWORD ='".$password."'";
    $result = query_object($query);
    return $result['SERVICE_ID'];
}
