<?php

/* 
 * To handle auth process from google or outlook
 * 20.12.2021
 * 
 */

//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$clientId = filter_input(INPUT_GET, 'clientId');
$userId = filter_input(INPUT_GET, 'userId');
//$authType = filter_input(INPUT_GET, 'authType');
$base_url = $_SERVER[REQUEST_URI];
$fn = filter_input(INPUT_GET, 'fn');
$currentTimestamp = intval(microtime(true) * 1000);

error_log(PHP_EOL."fn: ".$fn." clientId: ".$clientId." Timestamp: ".$currentTimestamp." BASE_URL: ".$base_url." User Id ".$userId,3,"error_log.txt");

switch($fn){
    case 1:
        require 'gauth_process.php';
        break;
    case 2:
        $clientId = filter_input(INPUT_GET, 'clientId');
        $vldCode = filter_input(INPUT_GET, 'vldCode');
        $reqType = filter_input(INPUT_GET, 'reqType');
        $params_1 =["clientId" => $clientId , "vldCode" => $vldCode, "reqType"=>$reqType, "uid"=>$userId,"fn"=>$fn];
        $params = base64UrlEncodeOutlook(json_encode($params_1));

        $client_id = "62693173-7b61-40ec-a332-2f4c6dc64176";
        $redirect_url = urlencode("https://centralhost.repfabric.com/rf-auth-dev/olauth_process");
        //$scope = urlencode("https://graph.microsoft.com/user.read");
        $scope= "user.read+openid+profile+email";
    //    $microsoft_url= "https://login.microsoftonline.com/common/oauth2/v2.0/authorize?response_type=id_token%20token"
    //            . "&response_mode=form_post&client_id=".$client_id."&scope=".$scope."&state=".$params."&nonce=12".$clientId."0"
    //             . "&redirect_uri=".$redirect_url;
        $microsoft_url= "https://login.microsoftonline.com/common/oauth2/v2.0/authorize?response_type=code"
                . "&response_mode=query&client_id=".$client_id."&scope=".$scope."&state=".$params.""
                . "&redirect_uri=".$redirect_url;
        $state="val";
        //echo $microsoft_url;die;
        error_log(PHP_EOL."microsoft url: ".$microsoft_url,3,"error_log.txt");
        header("location:" . $microsoft_url);
        break;
    case 3:
        include_once "../common/common.php";
        if(authUserExistsByInstance($userId, $clientId)){
           $result= deleteAuthUser($userId, $clientId); 
        }else{
            $result="User not found";
        }
        
        if($result=='success'){
            echo json_encode(['status'=>1,'message'=>"Success"]);
        }else{
            echo json_encode(['status'=>0,'message'=>$result]);
        }
        break;
    default :
        echo "Function not defined";
        break;
}
/*if($fn==1){
    require 'gauth_process.php';
}else if($fn==2){
   // require 'olauth_process.php';
    $clientId = filter_input(INPUT_GET, 'clientId');
    $vldCode = filter_input(INPUT_GET, 'vldCode');
    $reqType = filter_input(INPUT_GET, 'reqType');
    $params_1 =["clientId" => $clientId , "vldCode" => $vldCode, "reqType"=>$reqType];
    $params = base64UrlEncodeOutlook(json_encode($params_1));
    
    $client_id = "62693173-7b61-40ec-a332-2f4c6dc64176";
    $redirect_url = urlencode("https://centralhost.repfabric.com/rf-auth-dev/olauth_process");
    //$scope = urlencode("https://graph.microsoft.com/user.read");
    $scope= "user.read+openid+profile+email";
//    $microsoft_url= "https://login.microsoftonline.com/common/oauth2/v2.0/authorize?response_type=id_token%20token"
//            . "&response_mode=form_post&client_id=".$client_id."&scope=".$scope."&state=".$params."&nonce=12".$clientId."0"
//             . "&redirect_uri=".$redirect_url;
    $microsoft_url= "https://login.microsoftonline.com/common/oauth2/v2.0/authorize?response_type=code"
            . "&response_mode=query&client_id=".$client_id."&scope=".$scope."&state=".$params.""
            . "&redirect_uri=".$redirect_url;
    $state="val";
    //echo $microsoft_url;die;
    header("location:" . $microsoft_url);
}*/


function base64UrlEncodeOutlook($inputStr){
    return strtr(base64_encode($inputStr), '+/=', '-_,');
}


