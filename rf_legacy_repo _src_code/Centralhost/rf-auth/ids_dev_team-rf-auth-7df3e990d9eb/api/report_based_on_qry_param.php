<?php
// error_reporting(E_ALL);
// ini_set('display_errors', 1);

ini_set('memory_limit', '-1');

include "../common/common.php";
include_once 'HttpResponseCode.php';
    
    $serviceKey = $_SERVER['PHP_AUTH_USER'];

	// Takes raw data from the request
    $json = file_get_contents('php://input');

    // Converts it into a PHP object
    $data = json_decode($json,true);
    if(!isJSON($json)){
        http_response_code(422);
        $response = RFResponse(422, 'Failed to parse the JSON.');
        execute($response);
    }

    function isJSON($string){
       return is_string($string)  && (json_last_error() == JSON_ERROR_NONE) ? true : false;
    }
    $qry = preg_replace('/\s+/',' ',trim($data['qry']));
    $post_db =  $data['db'];
    $user_category = 1;
    $service_id = ValidateToken($serviceKey);

    if ($service_id == "" || $service_id == 'NULL') {

        http_response_code(401);
        $response = RFResponse(401, 'Unauthorized Access');
        execute($response);

    }
    else if(strpos(strtolower($qry),'select *') !== false){  #12719
        http_response_code(400);
        $response = RFResponse(400, 'Unrestricted Query. Not allowed');
        execute($response);
    }
    else 
    {
        
        $query = "SELECT DISTINCT USER_BASE_URL, USER_CLIENT_ID FROM USER_INSTANCE";
        $instances = query_list($query);
       // echo json_encode($instances);
       //echo count($instances);
        // $reports = [];


        if(count($instances)>0)
        {
            
            foreach ($instances as $key => $instance) {
                
                $qry_encode = base64_encode($qry);
                $base_url = $instance['USER_BASE_URL'];
                $url ='https://'.$base_url.'/api/public/v1.0/query_result?qry='.$qry_encode.'&db='.$post_db;
                //$url ='https://twdev.repfabric.com/api/public/v1.0/query_result?qry='.$qry_encode.'&db='.$post_db;
                $client_id = $instance['USER_CLIENT_ID'];
                $result = curlRequest($url,$client_id);
                
                $res_array = json_decode($result["data"], true);
                if(count($res_array) > 0){
                    
                    $res_array = array_map(function($a) use($base_url){
                        $a += ['INSTANCE' => $base_url];
                        return $a;
                    }, $res_array);
                    $multi_result[] = $res_array;
                }

            }
            $reports = call_user_func_array("array_merge", $multi_result);
            
        }

        $filename = 'report.csv';
        header("Content-type: text/csv");
        header("Accept: text/csv");
        header("Content-Disposition: attachment; filename=$filename");

        $output = fopen("php://output", "w");
        $header = array_keys($reports[0]);
        fputcsv($output,$header);

        foreach ($reports as $report) {
            fputcsv($output,$report);
        }

        fclose($output);

    }

    function curlRequest($url,$client_id){
        date_default_timezone_set("UTC");
        $username= $client_id.date("j"); 
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_HTTPAUTH,CURLAUTH_BASIC);
        curl_setopt($curl, CURLOPT_USERPWD,$username);
        curl_setopt($curl, CURLOPT_URL, $url);
       
        $output = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        
        if(curl_errno($curl))
        {
            //echo 'error';
        }

        $resp = curl_getinfo($curl,CURLINFO_HTTP_CODE);
        curl_close($curl);
        $result['data'] = $output;
        $result['status_code'] = $httpCode;
        return $result;
    }

?>
