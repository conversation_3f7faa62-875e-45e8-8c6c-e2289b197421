<?php

/*
 * To handle office365 sign in process
 * 22.12.2021
 * 
 */
//error_reporting(E_ALL);
//ini_set('display_errors', 1);
include "../common/common.php";

if (isset($_GET["code"])) {
    error_log(PHP_EOL."code:".$_GET["code"],3,"error_log.txt");
    $code = $_GET["code"];
    $client_id = "62693173-7b61-40ec-a332-2f4c6dc64176";
    // $client_secrete = "****************************************";

    $client_secrete = "****************************************";
    //$client_secrete = "c288e946-66ea-4ea6-9196-9ed8add82698";

    //"****************************************"; //"****************************************";
    $scope = "user.read+openid+profile+email";
    $redirect_url = urlencode("https://centralhost.repfabric.com/rf-auth-dev/olauth_process");
    $posturl = "https://login.microsoftonline.com/common/oauth2/v2.0/token";
    $post_data = "client_id=" . $client_id . "&scope=" . $scope . "&code=" . $code . "&redirect_uri=" . $redirect_url . "&grant_type=authorization_code&client_secret=" . $client_secrete;
    
    //var_dump($post_data);
    $token_data = curlPostRequestOutlook($posturl, $post_data);
    error_log(PHP_EOL."Token_data: ".$token_data,3,"error_log.txt");
    $token_array = stringToArrConvertion($token_data);
    if (filter_input(INPUT_GET, 'state') != null) {
        $rf_data_json = base64_decode(strtr(filter_input(INPUT_GET, 'state'), '-_,', '+/='));
        $rf_data = json_decode($rf_data_json);
        $clientId = $rf_data->clientId;
        $vld_code = $rf_data->vldCode;
        $req_type = $rf_data->reqType;
        $req_user_id = $rf_data->uid;
        $fn = $rf_data->fn;
        //error_log(PHP_EOL."state:",3,"error_log.txt");
         //error_log(PHP_EOL."c id:".$clientId.",vld:". $vld_code.",req_type:".$req_type,3,"error_log.txt");
    }
     if(filter_input(INPUT_GET, 'error'))
    {
        $error_description = filter_input(INPUT_GET, 'error_description');
         error_log(PHP_EOL."return url:".$error_description ,3,"error_log.txt");
    }
    if (isset($token_array['access_token'])) {
        $access_token = $token_array['access_token'];
         //error_log(PHP_EOL."Outlook-Token: ".$access_token,3,"error_log.txt");
        $getmeUrl = 'https://graph.microsoft.com/beta/me';
        $user_info = curlGetRequestOultook($access_token, $getmeUrl);
        //var_dump($user_info);
        if($user_info['mail']!="" || $user_info['mail']!=NULl){
            $userEmail = $user_info['mail'];
        }else{
            $userEmail = $user_info['userPrincipalName'];
        }       
        $user_data = getUserInfoByEmail($userEmail, $clientId, $req_user_id);
        $str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        $user_token = substr(str_shuffle($str_result), 0, 9);

        if (!empty($user_data)) {
            $base_url = $user_data['USER_BASE_URL'];
            if ($user_data['USER_ID'] != "" || $user_data['USER_ID'] != null) {
                //error_log(PHP_EOL."user_date:". json_encode($user_data),3,"error_log.txt");
                $status = 1;
                $user_id = $user_data['USER_ID'];
                $client = $user_data['USER_CLIENT_ID'];
                $login_name = $user_data['USER_LOGIN_NAME'];
                $auth_type = "Microsoft";
                if ($req_type == 3) {
                     postUserAuthData($base_url, $user_id, $login_name, $user_token, 3);
                }
                //deleting based on #6745 new reuirement
                /*if ($req_type == 1) {
                    //save data to USER_AUTH_TYPES table
                    insertUserAuthType($user_id, $base_url, $client, $login_name, $userEmail, $auth_type);
                } else if ($req_type == 2) {
                    //save data to USER_AUTH_TYPES table
                    insertUserAuthType($user_id, $base_url, $client, $login_name, $userEmail, $auth_type);
                } else if ($req_type == 3) {
                    //verify whether user exists in  USER_AUTH_TYPES table and based on thta set loginStatus
                    if (!authUserExists($userEmail, $auth_type)) {
                        //error_log(PHP_EOL."user_date: not found in new table",3,"error_log.txt");
                        $status = 0;
                    } else {
                        //error_log(PHP_EOL."user_date: found post data to instance",3,"error_log.txt");
                        postUserAuthData($base_url, $user_id, $login_name, $user_token, 3);
                    }
                }*/
            } else {
                // error_log(PHP_EOL."user_date: not found",3,"error_log.txt");
                $status = 0;
            }
            $returnUrl = getReturnUrlOlauth($req_type, $base_url, $vld_code, $user_token, $status, $user_id, $req_user_id,$fn);
        } else {
            $base_url = getBaseUrlByClientId($clientId);
            $returnUrl = getReturnUrlOlauth($req_type, $base_url, $vld_code, $user_token, 0, 0, $req_user_id,$fn);
        }
    } else {
        $base_url = getBaseUrlByClientId($clientId);
        $returnUrl = getReturnUrlOlauth($req_type, $base_url, $vld_code, $user_token, 0, 0, $req_user_id,$fn);
    }

    //echo $returnUrl;    die;
    header("location:" . $returnUrl);
}
else{
     if(filter_input(INPUT_GET, 'error'))
    {
        if (filter_input(INPUT_GET, 'state') != null) {
        $rf_data_json = base64_decode(strtr(filter_input(INPUT_GET, 'state'), '-_,', '+/='));
        $rf_data = json_decode($rf_data_json);
        $clientId = $rf_data->clientId;
        $vld_code = $rf_data->vldCode;
        $req_type = $rf_data->reqType;
        $req_user_id = $rf_data->uid;
        $fn = $rf_data->fn;
        $user_token= "";
         //error_log(PHP_EOL."c id:".$clientId.",vld:". $vld_code.",req_type:".$req_type,3,"error_log.txt");
        $base_url = getBaseUrlByClientId($clientId);
        $returnUrl = getReturnUrlOlauth($req_type, $base_url, $vld_code, $user_token, 0, 0, $req_user_id,$fn);
        //error_log(PHP_EOL."state:",3,"error_log.txt");
    }
        
    }
     header("location:" . $returnUrl);
}

function curlPostRequestOutlook($url, $post_data) {
    header('Content-Type: application/json');
    $ch = curl_init();
    //echo $url;
    //echo $post_data;
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
    curl_setopt($ch, CURLOPT_HEADER, array('Content-Type: application/x-www-form-urlencoded', 'Accept: application/json'));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $result = curl_exec($ch);
    curl_close($ch);
    return $result;
}

function stringToArrConvertion($token_data) {
    $resp_json = explode('{', $token_data, 2);
    $resp_data = explode(',', rtrim($resp_json[1], '}'));
    $tok_data = [];
    foreach ($resp_data as $token) {
        $data1 = explode(":", $token);
        $tok_data[str_replace('"', '', $data1[0])] = str_replace('"', '', $data1[1]);
        // $lastdata[]=$tok_data;
    }
    return $tok_data;
}

function curlGetRequestOultook($token, $url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    $authorization = "Authorization: Bearer " . $token;
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', $authorization));
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    $upd_res = curl_exec($ch);
    curl_close($ch);
    $data = json_decode($upd_res);
    $get_data = json_decode(json_encode($data), true);
    return $get_data;
}

function getReturnUrlOlauth($req_type, $base_url, $vld_code, $user_token, $loginStatus, $user_id, $req_user_id,$fn) {
    if ($req_type == 1) {
        $returnUrl = "https://" . $base_url . "/RepfabricCRM/UserEnrollment.xhtml?vldCode=" . $vld_code . "&tabIndex=2&loginStatus=" . $loginStatus . "&serviceType=2";
    } else if ($req_type == 2) {
        $returnUrl = "https://" . $base_url . "/RepfabricCRM/config/Setup.xhtml?usrEnrlId=" . $req_user_id . "&enrollmentStepId=3&loginStatus=" . $loginStatus . "&serviceType=2";
    } else if ($req_type == 3) {
        $returnUrl =  "https://" . $base_url . "/RepfabricCRM/UserAuth.xhtml?fn=".$fn."&loginStatus=" . $loginStatus . "&token=" . $user_token;  /*"https://" . $base_url . "/rfnextgen/index.xhtml?fn=".$fn."&loginStatus=" . $loginStatus . "&token=" . $user_token;*/ 
    }
    return $returnUrl;
}
