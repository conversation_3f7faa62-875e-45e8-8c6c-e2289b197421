<?php

/* 
 * To authenticate users from all repfabric instances
 * Created On 22.02.2019
 * 
 */

header("Access-Control-Allow-Origin:*");
include "../common/common.php";

echo "This is to authenticate user.. TODO";

echo $db=db_connect();

/*
 * Function to authenticate user by recievinr request
 * Request may contain User_email, Password(encoded), and service_type(mobile,yoxel,jsf etc)
 * Need to decide request type,encryption methods etc
 * Returns Json object as output
 */


?>