<?php

/*
 * To authenticate users for alexa
 * Created On 22.02.2019
 * 
 */

//error_reporting(E_ALL);
//ini_set('display_errors', 1);

include "../common/common.php";
include_once 'HttpResponseCode.php';

//DB connction
$db = db_connect();


//get emailid and service key
$serviceKey = $_SERVER['PHP_AUTH_USER'];
//$userToken = filter_input(INPUT_GET, 'userToken');
$emailId = filter_input(INPUT_GET, 'emailId');

$password = $_SERVER['PHP_AUTH_PW'];

$service_id = ValidateToken($serviceKey);

//$filename = "error_log.txt"; 
//$tz = "UTC";
//$timestamp_start = time();
//$dt = new DateTime("now", new DateTimeZone($tz));
//$dt->setTimestamp($timestamp_start);
//$start_time = $dt->format("Y/m/d H:i:s");
//error_log(PHP_EOL."-------------------".$start_time."----------------------------".PHP_EOL,3,$filename);
//error_log(PHP_EOL."Email Id:".$emailId.PHP_EOL,3,$filename);
//error_log(PHP_EOL."service_id:".$service_id.PHP_EOL,3,$filename);

if ($service_id == "" || $service_id == 'NULL') {
    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);
}  else {
    if ($emailId != '') {
        // Update is based on #675
       // $query = "SELECT USER_EMAIL, USER_ID, USER_CLIENT_ID, USER_BASE_URL,USER_TOKEN,ALEXA_PIN "
        //        . "FROM  ALEXA_USERS WHERE USER_EMAIL='" . $emailId . "' ";
        $query ="SELECT USER_EMAIL,USER_ID, USER_CLIENT_ID, USER_BASE_URL,USER_LOGIN_NAME FROM  USER_INSTANCE "
                . "WHERE USER_EMAIL='" . $emailId . "'";
        $res = query_list($query);
        $res1 = array();
        if (empty($res)) {
            http_response_code(404);
            $response = RFResponse(404, 'User not found');
            execute($response);
        } else {
            foreach ($res as $result) {
                $result1['email'] = $result['USER_EMAIL'];
                $result1['login-id'] = $result['USER_LOGIN_NAME'];
                $result1['client-id'] = (int) $result['USER_CLIENT_ID'];
                $result1['user-id'] = (int) $result['USER_ID'];
                $result1['client-url'] = $result['USER_BASE_URL'];
//                $result1['user-token'] = $result['USER_TOKEN'];
//                $result1['alexa-pin'] = $result['ALEXA_PIN'];
                $res = $result1;
            }
        }
        execute($res);
        //echo json_encode($res1);
    } else {
        http_response_code(404);
        $response = RFResponse(404, 'Token not found');
        execute($response);
    }
}
