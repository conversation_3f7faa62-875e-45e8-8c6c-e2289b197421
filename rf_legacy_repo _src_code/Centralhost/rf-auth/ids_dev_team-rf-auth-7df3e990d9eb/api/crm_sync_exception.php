<?php
/*
 * Task id: 14418
 */
include "../common/common.php";
include_once 'HttpResponseCode.php';
// error_reporting(E_ALL);
// ini_set("display_errors", 1);

$crmSyncId = filter_input(INPUT_GET, 'crmsyncId');
$date = filter_input(INPUT_GET, 'date');
$url = $_SERVER[REQUEST_URI];
$method = $_SERVER[REQUEST_METHOD];
$serviceKey = $_SERVER['PHP_AUTH_USER'];
$password = $_SERVER['PHP_AUTH_PW'];

error_log(PHP_EOL."exception crmSyncId ".$crmSyncId,3,"error_log.txt");

if($method == "POST"){
    $service_id = ValidateToken($serviceKey);
}
else{
    $service_id = ValidateTokenAndPassword($serviceKey,$password);
}


if ($service_id == "" || $service_id == 'NULL') {
    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);
}else if ($service_id == 'MOBAPP' || $service_id=='CRMPORTAL') {

	switch ($method) {
	    case "POST":
	        $postfp = fopen("php://input", "r");
	        $postdata = '';
	        while ($data = fread($postfp, 1024)) {
	            $postdata .= $data;
	        }
	        createException($postdata);
	        break;
	    case "GET":
	        if($service_id=='CRMPORTAL'){
    	        if (strpos($url, "?")) {
    	            foreach ($_GET as $key => $value) {
    	                if ($key != "date" && $key != "crmsyncId") {
    	                    RFResponse(403, 'Incorrect Parameter');
    	                }
    	            }
    	            
    	            $response = getException($crmSyncId, $date);
    	            
    	        } else {
    	            $response = getException($crmSyncId, $date);
    	        }
            }
            else{
                http_response_code(401);
                $response = RFResponse(401, 'Unauthorized Access');
                execute($response);
            }
	        break;
	    default:
	       excecuteResponses(405,METHOD_NOT_ALLOWED,$url,$method,null);
	       break;
	}
}else{
    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);
}

function createException($json) {
    date_default_timezone_set("UTC");
    $data = json_decode($json, true);
    if ($data == null && json_last_error() !== JSON_ERROR_NONE) {    
        RFResponse(400, 'Invalid JSON');
    }

    $clientId = $data['client-id'];
    $clientName = $data['client-name'];
    $crmSyncId = $data['crmsync-id'];
    $crmSyncName = $data['crmsync-name'];
    $crmSyncUserId = $data['crmsync-user-id'];
    $crmSyncUserName = $data['crmsync-user-name'];
    $error = $data['error'];
    $timestamp = $data['timestamp'] ? gmdate('Y-m-d H:i:s',strtotime($data['timestamp'])) : gmdate('Y-m-d H:i:s');    
   	$qry = "INSERT INTO CRMSYNC_EXCEPTIONS "
                . "(CLIENT_ID,CLIENT_NAME ,CRMSYNC_SYS_ID, 	CRMSYNC_SYS_NAME, CRMSYNC_USER_ID, CRMSYNC_USER_NAME, CRMSYNC_TIMESTAMP, CRMSYNC_ERROR) "
                . "VALUES (" . $clientId . ",'" . $clientName . "'," . $crmSyncId . ",'" . $crmSyncName . "',".$crmSyncUserId.",'".$crmSyncUserName."','".$timestamp."','".$error ."')";
    $res = execute_query($qry);
    if($res=="success"){
        //error_log(PHP_EOL."User-id ".$user_id." who has email-id ".$user_email." added successfully",3,"error_log.txt");
        $response = RFResponse(201, "Crmsyn exception created");
        
    }else{
        //error_log(PHP_EOL."User-id ".$user_id." who has email-id ".$user_email." failed to insert",3,"error_log.txt");
         $response = RFResponse(400, "Failed to insert");
           
    }

    execute($response);
  
}

function getException($crmSyncId, $date){

	$whereCond =" WHERE 1 ";

    if(isset($crmSyncId)){
        $whereCond .="AND CRMSYNC_SYS_ID=$crmSyncId";
    }

    if(isset($date)){
        $whereCond .="AND DATE(CRMSYNC_TIMESTAMP)='$date'";
    }

    $query = "SELECT * FROM CRMSYNC_EXCEPTIONS".$whereCond ;


    $results = query_list($query);
    if(empty($results)){
        http_response_code(404);
        $response = RFResponse(404, 'No data found');
        execute($response);
    }else{
        $exceptionRes=[];
        foreach ($results as $res){
            $exception=[
                "client-id"=>intval($res['CLIENT_ID']),
                "client-name"=>$res['CLIENT_NAME'],
                "crmsync-id"=>intval($res['CRMSYNC_SYS_ID']),
                "crmsync-name"=>$res['CRMSYNC_SYS_NAME'],
                "crmsync-user-id"=>intval($res['CRMSYNC_USER_ID']),
                "crmsync-user-name"=>$res['CRMSYNC_USER_NAME'],
                "timestamp"=>$res['CRMSYNC_TIMESTAMP'],
                "error"=>$res['CRMSYNC_ERROR']
                ];
           
            $exceptionRes[]=$exception; 
                              
        }
        execute($exceptionRes);
    }
}

