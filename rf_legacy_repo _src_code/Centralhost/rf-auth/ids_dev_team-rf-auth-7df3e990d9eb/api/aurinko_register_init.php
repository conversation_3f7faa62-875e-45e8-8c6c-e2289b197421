<?php

/* 
 * To get aurinko token using aurinko api
 * Save update into requeted instace
 * 15.12.2021
 */
//error_reporting(E_ALL);
//ini_set("display_errors", 1);
include "../common/common.php";

$clientId = filter_input(INPUT_GET, 'clientId');
$userId = filter_input(INPUT_GET, 'userId');
$vldCode = filter_input(INPUT_GET, 'vldCode');
$tabIndex = filter_input(INPUT_GET, 'tabIndex');

$appCleintId= 'e6be0d0c8537927c3f39bb262e9007e';
if($tabIndex == 3){
    $serviceType = 'Google';
    //to handle google accounts since it is not confirmed by aurinko
    /*$base_url = getBaseUrlByClientId($clientId);
    if($vldCode!=""){
        $returnUrl = 'https://'. $base_url . "/RepfabricCRM/UserEnrollment.xhtml?vldCode=".$vldCode. "&tabIndex=".$tabIndex;
    }else{
        $returnUrl = 'https://'. $base_url . "/RepfabricCRM/config/Setup.xhtml?tabIndex=".$tabIndex."&usrEnrlId=".$userId. "&enrollmentStepId=4";
    }
    $sType=1;
    //echo $returnUrl."&status=".$status;die;
    header("location:".$returnUrl."&loginStatus=2"."&serviceType=".$sType);*/
}else if($tabIndex == 4){
    $serviceType = 'Office365';
}
    $scopes = 'Mail.Read Mail.Send Mail.ReadWrite';
    $responseType= 'token';
    //$responseType= 'code';
    $uriParts = explode('?', $_SERVER['REQUEST_URI'], 2);
    $host_url = explode('/', $_SERVER['REQUEST_URI']);
    $url_first= $host_url[1];
    //$returnUrl = $_SERVER['REQUEST_SCHEME']. '://'. $_SERVER['HTTP_HOST']. $uriParts[0];
    $user_email= "";
    $user_data = getCrmuserData($userId, $clientId);
    if(!empty($user_data)){
        $user_email = $user_data['USER_EMAIL'];
    }
    $rf_data= ['id'=>$userId,'cid'=>$clientId,'email'=>urlencode($user_email), 'tabCode'=>$vldCode, 'tabIndex'=>$tabIndex ];
    $returnUrl = $_SERVER['REQUEST_SCHEME']. '://'. $_SERVER['HTTP_HOST'] .'/'.$url_first.'/aurinko_register?rfData='.base64UrlEncode(json_encode($rf_data));

    /*$aurinkoUrl = 'https://api.aurinko.io/v1/auth/authorize?clientId='.$appCleintId.'&serviceType='.$serviceType.''
            . '&scopes='.urlencode($scopes).'&responseType='.$responseType.'&userId='.$userId.'&clientOrgId='.$clientId. '&returnUrl='.urlencode($returnUrl).'&prompt=select_account';*/
    if($tabIndex == 3){
    $aurinkoUrl = 'https://aurinko.repfabric.info/v1/auth/authorize?clientId='.$appCleintId.'&serviceType='.$serviceType.''
            . '&scopes='.urlencode($scopes).'&responseType='.$responseType.'&userId='.$userId.'&clientOrgId='.$clientId. '&returnUrl='.urlencode($returnUrl).'&prompt=select_account';
        }else if($tabIndex == 4){
     $aurinkoUrl = 'https://api.aurinko.io/v1/auth/authorize?clientId='.$appCleintId.'&serviceType='.$serviceType.''
            . '&scopes='.urlencode($scopes).'&responseType='.$responseType.'&userId='.$userId.'&clientOrgId='.$clientId. '&returnUrl='.urlencode($returnUrl).'&prompt=select_account';
    }
    else
    {
           $aurinkoUrl = 'https://api.aurinko.io/v1/auth/authorize?clientId='.$appCleintId.'&serviceType='.$serviceType.''
            . '&scopes='.urlencode($scopes).'&responseType='.$responseType.'&userId='.$userId.'&clientOrgId='.$clientId. '&returnUrl='.urlencode($returnUrl).'&prompt=select_account';
    }

    error_log(PHP_EOL."Aurinko url: ".$aurinkoUrl,3,"error_log.txt");
    error_log(PHP_EOL."return Url: ".$returnUrl,3,"error_log.txt");
    $resp= curlGetRequestStartAuth($aurinkoUrl);
//}

function curlGetRequestStartAuth($url) {
    header('Content-Type: application/json'); 
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HEADER, TRUE);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
    $upd_res = curl_exec($ch); 
    $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    if($httpcode=="200"){
        $redirectUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        curl_close($ch);
        header("location:".$redirectUrl);
        //curlGetRequestStartAuthNew($redirectUrl);
    }else{
        curl_close($ch);
    }  
}
