<?php

/*
 *  getting the user details and doing insert, update, delete operation.
 * First checking for serviece id, if the requested service id matches then check for aurthentication, once the authentication matches 
 * allow to do insert, delete and update operation.
 */
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

include "../common/common.php";
include_once 'HttpResponseCode.php';

$base_url = filter_input(INPUT_GET, 'base_url');
$client_id = filter_input(INPUT_GET, 'client_id');
$user_id = filter_input(INPUT_GET, 'user_id');
$users_data = json_decode(filter_input(INPUT_GET, 'users_data'),true);
$service = filter_input(INPUT_GET, 'service');
$method = filter_input(INPUT_GET, 'method');

if ($service != "Custom_Update") {
    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);
}else{
     switch ($method) {
        case 'insert':
            echo insert_user($users_data,$client_id,$base_url);
            break;
        case 'update':
            echo update_user($users_data,$client_id);
            break;
        case 'delete':    
           echo delete_user($client_id,$user_id);
        default:
            break;
     }
}

function insert_user($user_data,$client_id,$base_url){
    $user_id=$user_data['USER_ID'];
    $user_email=$user_data['USER_EMAIL'];
    $user_login = $user_data['USER_LOGIN_ID'];
    $query = "SELECT USER_EMAIL FROM USER_INSTANCE WHERE USER_LOGIN_NAME='" . $user_login . "' AND USER_CLIENT_ID='" . $client_id . "'";
    $result = query_check($query);
    if ($result==0) {
        $qry = "INSERT INTO USER_INSTANCE "
                . "(USER_EMAIL,USER_ID,USER_CLIENT_ID,USER_BASE_URL, USER_LOGIN_NAME) "
                . "VALUES ('" . $user_email . "'," . $user_id . "," . $client_id . ",'" . $base_url . "','".$user_login."')";
        $res = execute_query($qry);
        error_log(PHP_EOL."User-insertion query ".$qry,3,"error_log.txt");
        if($res=="success"){
            error_log(PHP_EOL."User-id ".$user_id." who has email-id ".$user_email." added successfully",3,"error_log.txt");
            return "1";
        }else{
            error_log(PHP_EOL."User-id ".$user_id." who has email-id ".$user_email." failed to insert",3,"error_log.txt");
            return "0"; 
        }
    } else{
        return "0"; 
    }    
}

function delete_user($client_id, $user_id){   
    $qry ="DELETE FROM USER_INSTANCE WHERE USER_ID=".$user_id." AND USER_CLIENT_ID=".$client_id;
    error_log(PHP_EOL."User-deletion query ".$qry,3,"error_log.txt");
    $res = execute_query($qry);
    if($res=="success"){
        error_log(PHP_EOL."User-id ".$user_id." deleted successfully.",3,"error_log.txt");
        $qry_1 ="DELETE FROM USER_AUTH_TYPES WHERE USER_ID=".$user_id." AND USER_CLIENT_ID=".$client_id;
        execute_query($qry_1);
        return "1";
    }else{
        error_log(PHP_EOL."User-id ".$user_id." failed to delete.",3,"error_log.txt");
        return "0"; 
    }
}

function update_user($user_data,$client_id){
    $user_id=$user_data['USER_ID'];
    $user_email=$user_data['USER_EMAIL'];
    $user_login = $user_data['USER_LOGIN_ID'];
    $query = "SELECT USER_EMAIL FROM USER_INSTANCE WHERE USER_LOGIN_NAME='" . $user_login . "' AND USER_CLIENT_ID='" . $client_id . "'";
    $result = query_check($query);
    if ($result!=0) {
       $qry = "UPDATE USER_INSTANCE SET USER_EMAIL = '".$user_email."' WHERE USER_ID=".$user_id." AND USER_CLIENT_ID=".$client_id;
        error_log(PHP_EOL."User-Updation query ".$qry,3,"error_log.txt");
        $res = execute_query($qry);
        if($res=="success"){
            error_log(PHP_EOL."User email for User-id ".$user_id." updated successfully",3,"error_log.txt");
            return "1";
        }else{
            error_log(PHP_EOL."Failed to update User email for User-id ".$user_id,3,"error_log.txt");
            return "0"; 
        }
    } else{
        return "0"; 
    }     
}



?>