<?php

/*
 * To Bulk insert user data
 * 26.04.2019
 */
// error_reporting(E_ALL);
// ini_set('display_errors', 1);

include "../common/common.php";
include_once 'HttpResponseCode.php';

// $base_url = filter_input(INPUT_GET, 'base_url');
// $client_id = filter_input(INPUT_GET, 'client_id');
// $users_data = json_decode(filter_input(INPUT_GET, 'users_data'),true);
// $service = filter_input(INPUT_GET, 'service');

$post_fp = fopen("php://input", "r");
$post_data = '';
while ($data = fread($post_fp, 1024)) {
    $post_data .= $data;
}
error_log(PHP_EOL."bulk insert : ".$post_data,3,"error_log.txt");

$data = json_decode($post_data);
if ($data == null && json_last_error() !== JSON_ERROR_NONE) {
    echo  "Invalid Json Data.";
}

//echo $data->users_data;
$base_url = $data->base_url;
$client_id = $data->client_id;
$users_data = $data->users_data;
$service = $data->service;


if ($service != "Bulk_Insert") {
    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);
}else{
    if(deleteExisting($client_id,$base_url)=="success"){
        if(empty($users_data)){
            echo "Something went wrong!Please try again!!";
        }else{                    
            foreach($users_data as $user){
                 $user_id=$user->USER_ID;
                 $user_email= escapeQuote(urldecode($user->USER_EMAIL));
                 $user_login = escapeQuote(urldecode($user->USER_LOGIN_ID));
                // error_log(PHP_EOL."user id ".$user_id." user email : ".$user_login,3,"error_log.txt");
                 $query = "SELECT USER_EMAIL FROM USER_INSTANCE WHERE USER_LOGIN_NAME='" . $user_login . "' AND USER_CLIENT_ID='" . $client_id . "'";
                 
                 $result = query_check($query);
                 if ($result==0) {
                     $qry = "INSERT INTO USER_INSTANCE "
                             . "(USER_EMAIL,USER_ID,USER_CLIENT_ID,USER_BASE_URL,USER_LOGIN_NAME) "
                             . "VALUES ('" . $user_email . "'," . $user_id . "," . $client_id . ",'" . $base_url . "','".$user_login."')";
                             //error_log(PHP_EOL."user id ".$user_id." user email : ".$qry,3,"error_log.txt");
                     $res = execute_query($qry);
                 }         
             }
             echo "Users imported successfully";
        }
        
    }else{
        echo "Something went wrong!Please try again!!";
    }
}



function deleteExisting($client_id,$base_url){
   $qry ="DELETE FROM `USER_INSTANCE` WHERE `USER_CLIENT_ID`='$client_id' AND `USER_BASE_URL`='$base_url'"; 
   $result= execute_query($qry);
   return $result;
}


