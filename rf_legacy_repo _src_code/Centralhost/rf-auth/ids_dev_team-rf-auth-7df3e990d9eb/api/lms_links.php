<?php
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

include "../common/common.php";
include_once 'HttpResponseCode.php';

$serviceKey = $_SERVER['PHP_AUTH_USER'];
$sub_tag = filter_input(INPUT_GET, 'sub_tag');
$password = $_SERVER['PHP_AUTH_PW'];

$service_id = ValidateToken($serviceKey);

if ($service_id == "" || $service_id == 'NULL') {

    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);

} else if ($service_id == 'MOBAPP' || $service_id=='CRMPORTAL') {

    $whereCond =" WHERE 1 AND LMS_URL is not null AND LMS_URL<>''";

    if(isset($sub_tag)){
        $whereCond .="AND TUTORIAL_SUB_TAG='$sub_tag'";
    }

    $query = "SELECT REC_ID, TUTORIAL_TAG_ID, TUTORIAL_TAG, TUTORIAL_SUB_TAG_ID, TUTORIAL_SUB_TAG, "
            . "LMS_URL FROM TUTORIALS_MST".$whereCond ;
   // echo $query;

    $results = query_list($query);
    if(empty($results)){
        http_response_code(404);
        $response = RFResponse(404, 'No data found');
        execute($response);
    }else{
        $tut_res=[];
        foreach ($results as $res){
            $tuts=[
                "id"=>intval($res['REC_ID']),
                "tag-id"=>intval($res['TUTORIAL_TAG_ID']),
                "tag"=>$res['TUTORIAL_TAG'],
                "sub-tag-id"=>intval($res['TUTORIAL_SUB_TAG_ID']),
                "sub-tag"=>$res['TUTORIAL_SUB_TAG'],
                "lms-url"=>$res['LMS_URL'],
                ];
            if(isset($sub_tag)){
                 $tut_res=$tuts; 
            }else{
               $tut_res[]=$tuts; 
            }                      
        }
        execute($tut_res);
    }
}else{
    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);
}
    