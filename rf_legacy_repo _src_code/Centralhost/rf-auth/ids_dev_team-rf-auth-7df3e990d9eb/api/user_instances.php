<?php

/*
 * To authenticate users from all repfabric instances
 * Created On 22.02.2019
 * 
 */

//error_reporting(E_ALL);
//ini_set('display_errors', 1);

include "../common/common.php";
include_once 'HttpResponseCode.php';
include "../common/sync_instnace.php";

//DB connction
$db = db_connect();
//get emailid and service key
$serviceKey = $_SERVER['PHP_AUTH_USER'];
$emailId = filter_input(INPUT_GET, 'emailId');
$password = $_SERVER['PHP_AUTH_PW'];

$service_id = ValidateToken($serviceKey);

$filename = "error_log.txt"; 
$tz = "UTC";
$timestamp_start = time();
$dt = new DateTime("now", new DateTimeZone($tz));
$dt->setTimestamp($timestamp_start);
$start_time = $dt->format("Y/m/d H:i:s");
error_log(PHP_EOL."-------------------".$start_time."----------------------------".PHP_EOL,3,$filename);
error_log(PHP_EOL."Email Id:".$emailId.PHP_EOL,3,$filename);
error_log(PHP_EOL."service_id:".$service_id.PHP_EOL,3,$filename);
//error_log(PHP_EOL."Password:".$password.PHP_EOL,3,$filename);

if ($service_id == "" || $service_id == 'NULL') {
    error_log(PHP_EOL."unauthorize".PHP_EOL,3,$filename);
    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);
} else if ($service_id == 'YOXEL') {
    if ($emailId != '') {
        $query = "SELECT USER_ID, USER_CLIENT_ID, USER_BASE_URL,USER_LOGIN_NAME FROM  USER_INSTANCE WHERE USER_LOGIN_NAME='" . $emailId . "'";
        $res = query_list($query);

        if (count($res) > 1) {
            http_response_code(409);
            $response = RFResponse(409, 'Multiple users found');
            execute($response);
        } else if (empty($res)) {
            http_response_code(401);
            $response = RFResponse(401, 'User not found');
           // $response = [];
            execute($response);
        } else {
            $resp = call_instance_sync_service('YOXEL', $emailId, $service_id, $res[0]['USER_BASE_URL'], $password,$res[0]['USER_ID']);
            $yoxel_data = json_decode($resp, TRUE);

            if ($yoxel_data['Token Data']['status'] != "") {
                http_response_code(403);
                $response = RFResponse(403, 'Incorrect EmailId or Password');
                execute($response);
            } else {
                // echo $yoxel_data['Token Data']['ACCT_ACCESS_TOKEN'];
                if (empty($yoxel_data['Token Data'])) {
                    http_response_code(403);
                    $response = RFResponse(403, 'Sync+ token not set');
                    execute($response);
                } else if ($yoxel_data['Token Data']['ACCT_SYNC_ENABLED'] == 0) {
                    http_response_code(403);
                    $response = RFResponse(403, 'Sync+ not enabled');
                    execute($response);
                } else if ($yoxel_data['Token Data']['ACCT_ACCESS_TOKEN'] == "" || $yoxel_data['Token Data']['ACCT_ACCESS_TOKEN'] == NULL) {
                    http_response_code(403);
                    $response = RFResponse(403, 'Sync+ token not set');
                    execute($response);
                } else {
                    if ($yoxel_data['Token Data']['ACCT_SYNC_ENABLED'] == '1') {
                        $sync = true;
                        $usertoken = $yoxel_data['Token Data']['ACCT_ACCESS_TOKEN'];
                    } else {
                        $sync = false;
                        $usertoken = '';
                    }


                    $res1 = array('email' => $emailId,
                        'login-id' => $res[0]['USER_LOGIN_NAME'],
                        'client-id' => (int) $res[0]['USER_CLIENT_ID'],
                        'client-url' => $res[0]['USER_BASE_URL'],
                        'user-id' => (int) $res[0]['USER_ID'],
                        'sync-enabled' => $sync,
                        'user-token' => $usertoken
                    );
                    execute($res1);
                    //echo json_encode($res1);
                }
            }
        }
    } else {
        http_response_code(404);
        $response = RFResponse(404, 'EmailId not found');
        execute($response);
    }
} else {
    error_log(PHP_EOL."for mobile".PHP_EOL,3,$filename);
    if ($emailId != '') {
        $query = "SELECT USER_EMAIL, USER_ID, USER_CLIENT_ID, USER_BASE_URL,USER_LOGIN_NAME FROM  USER_INSTANCE WHERE USER_LOGIN_NAME='" . $emailId . "' ";
        $res = query_list($query);
        $res1 = array();
        if (count($res) > 1) {
            error_log(PHP_EOL."multiple user".PHP_EOL,3,$filename);
            http_response_code(409);
            $response = RFResponse(409, 'Multiple users found');
            execute($response);
        } else if (empty($res)) {
            error_log(PHP_EOL."user not found".PHP_EOL,3,$filename);
            http_response_code(404);
            $response = RFResponse(404, 'User not found');
            execute($response);
        } else {
            error_log(PHP_EOL."success".PHP_EOL,3,$filename);
            foreach ($res as $result) {
                $result1['email'] = $result['USER_EMAIL'];
                $result1['login-id'] = $result['USER_LOGIN_NAME'];
                $result1['client-id'] = (int) $result['USER_CLIENT_ID'];
                $result1['user-id'] = (int) $result['USER_ID'];
                $result1['client-url'] = $result['USER_BASE_URL'];
                $res = $result1;
            }
        }
        execute($res);
        //echo json_encode($res1);
    } else {
        error_log(PHP_EOL."email id not found".PHP_EOL,3,$filename);
        http_response_code(404);
        $response = RFResponse(404, 'EmailId not found');
        execute($response);
    }
}


