<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

$status = filter_input(INPUT_GET, 'status');
$rf_data_json = base64_decode(strtr(filter_input(INPUT_GET, 'rfData'), '-_,', '+/='));
$rf_data = json_decode($rf_data_json);

$base_url = $rf_data->instance_url;
$user_id = $rf_data->id;
$sys_id= ($rf_data->sys_id=="")?11:$rf_data->sys_id;
if($status == "success"){
    $status =1;
}else{
    $status=0;
}

$returnUrl = "https://". $base_url . "/RepfabricCRM/crmsync/Setup.xhtml?crmsyncUserId=".$user_id."&crmsyncSysId=".$sys_id."&activation_status=".$status;

header("location:".$returnUrl);
