<?php

/*
 * To handle google sign in process
 * 21.12.2021
 * 
 */
//error_reporting(E_ALL);
//ini_set('display_errors', 1);
include "../common/common.php";
require '../config/google-api-client/vendor/autoload.php';
$clientId = filter_input(INPUT_GET, 'clientId');
$vldCode = filter_input(INPUT_GET, 'vldCode');
$reqType = filter_input(INPUT_GET, 'reqType');
$userId = filter_input(INPUT_GET, 'userId');
$fn = filter_input(INPUT_GET, 'fn');

$params_1 = ["clientId" => $clientId, "vldCode" => $vldCode, "reqType" => $reqType, "uid"=>$userId,"fn"=>$fn];
$params = base64UrlEncode(json_encode($params_1));

$client = new Google_Client();
$client->setClientId('************-csrdua61nd8h459a0ss44g5227ac7urj.apps.googleusercontent.com');
$client->setClientSecret('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');
$client->setRedirectUri('https://centralhost.repfabric.com/rf-auth-dev/gauth_process');
$client->addScope("email");
$client->addScope("profile");
$client->setState($params);
error_log(PHP_EOL."Code: ".$_GET['code'] ,3,"error_log.txt");

if (isset($_GET['code'])) {
    $token = $client->fetchAccessTokenWithAuthCode($_GET['code']);
    $client->setAccessToken($token['access_token']);
    $google_oauth = new Google_Service_Oauth2($client);
    $google_account_info = $google_oauth->userinfo->get();
    $userEmail = $google_account_info->email;

    error_log(PHP_EOL."State: ".$_GET['state'] ,3,"error_log.txt");
   // error_log(PHP_EOL."User email: ".$userEmail ,3,"error_log.txt");
    ;
    if (filter_input(INPUT_GET, 'state') != null) {
        $rf_data_json = base64_decode(strtr(filter_input(INPUT_GET, 'state'), '-_,', '+/='));
        error_log(PHP_EOL."RF data ". json_encode($rf_data_json),3,"error_log.txt");
        $rf_data = json_decode($rf_data_json);
        $clientId = $rf_data->clientId;
        $vld_code = $rf_data->vldCode;
        $req_type = $rf_data->reqType;
        $req_user_id = $rf_data->uid;
        $fn = $rf_data->fn;
    }
    $user_data = getUserInfoByEmail($userEmail, $clientId, $req_user_id);
    error_log(PHP_EOL."User data ". json_encode($user_data),3,"error_log.txt");
    $str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    $user_token = substr(str_shuffle($str_result), 0, 9);
    
    if (!empty($user_data)) {
        $base_url = $user_data['USER_BASE_URL'];
        if ($user_data['USER_ID'] != "" || $user_data['USER_ID'] != null) {
            $status = 1;
            $user_id = $user_data['USER_ID'];
            $client = $user_data['USER_CLIENT_ID'];
            $login_name = $user_data['USER_LOGIN_NAME'];
            $auth_type = "Google";
            if($req_type == 3) {
                postUserAuthData($base_url, $user_id, $login_name, $user_token, 2);
            }
            //deleting based on #6745 new reuirement
           /* if ($req_type == 1) {
                //save data to USER_AUTH_TYPES table
                insertUserAuthType($user_id, $base_url, $client, $login_name, $userEmail, $auth_type);
                //$returnUrl = "https://" . $base_url . "/RepfabricCRM/UserEnrollment.xhtml?vldCode=" . $vld_code . "&tabIndex=1&loginStatus=" . $status . "&serviceType=1";
            } else if ($req_type == 2) {
                insertUserAuthType($user_id, $base_url, $client, $login_name, $userEmail, $auth_type);
                //$returnUrl = "https://" . $base_url . "/RepfabricCRM/config/Setup.xhtml?usrEnrlId=" . $user_data['USER_ID'] . "&enrollmentStepId=3&loginStatus=" . $status . "&serviceType=1";
            } else if ($req_type == 3) {
                //verify whether user exists in  USER_AUTH_TYPES table and based on thta set loginStatus
                if (!authUserExists($userEmail, $auth_type)) {
                    $status = 0;
                } else {
                    postUserAuthData($base_url, $user_id, $login_name, $user_token, 2);
                }
               // $returnUrl = "https://" . $base_url . "/RepfabricCRM/index.xhtml?loginStatus=" . $status . "&token=" . $user_token;
            }*/
        } else {
            $status = 0;
        } 
        error_log(PHP_EOL."req_type: ".$req_type." base_url: ".$base_url." vld_code: ".$vld_code." user_token: ".$user_token. "status ".$status. " user_id ".$user_id. " req_user_id ". $req_user_id. " fn ".$fn ,3,"error_log.txt");          
        $returnUrl= getReturnUrlGauth($req_type,$base_url,$vld_code,$user_token,$status, $user_id, $req_user_id,$fn);
    }else{
       $base_url= getBaseUrlByClientId($clientId);
       error_log(PHP_EOL."req_type: ".$req_type." base_url: ".$base_url." vld_code: ".$vld_code." user_token: ".$user_token. "status ".$status. " user_id ".$user_id. " req_user_id ". $req_user_id. " fn ".$fn ,3,"error_log.txt");    
       $returnUrl= getReturnUrlGauth($req_type,$base_url,$vld_code,$user_token,0, 0, $req_user_id,$fn);
    }
   // echo $returnUrl;die;
    error_log(PHP_EOL." returnUrl ".$returnUrl ,3,"error_log.txt"); 
    error_log(PHP_EOL."------------ends---------------:",3,"error_log.txt"); 
    header("location:" . $returnUrl);
} else {

    $url = $client->createAuthUrl();
    error_log(PHP_EOL." Url ".$url ,3,"error_log.txt");  
    error_log(PHP_EOL."------------ends---------------:",3,"error_log.txt");
    header("location:" . $url);
}

function getReturnUrlGauth($req_type,$base_url,$vld_code,$user_token,$loginStatus, $user_id, $req_user_id,$fn){
    //based on discussion on 17.01.2022 sending requested user id in req 2
    if ($req_type == 1) {
        $returnUrl = "https://" . $base_url . "/RepfabricCRM/UserEnrollment.xhtml?vldCode=" . $vld_code . "&tabIndex=1&loginStatus=".$loginStatus."&serviceType=1";
    } else if ($req_type == 2) {
        $returnUrl = "https://" . $base_url . "/RepfabricCRM/config/Setup.xhtml?usrEnrlId=".$req_user_id."&enrollmentStepId=3&loginStatus=".$loginStatus."&serviceType=1";
    } else if ($req_type == 3) {         
        $returnUrl = "https://" . $base_url . "/RepfabricCRM/UserAuth.xhtml?fn=".$fn."&loginStatus=".$loginStatus."&token=" . $user_token; /*"https://" . $base_url . "/rfnextgen/index.xhtml?fn=".$fn."&loginStatus=".$loginStatus."&token=" . $user_token;*/
    }
    error_log(PHP_EOL."Return URL here: ".$returnUrl ,3,"error_log.txt");
    return $returnUrl;
}
