<?php

include "../common/common.php";
include_once 'HttpResponseCode.php';
include "../common/sync_instnace.php";

//DB connction
$db = db_connect();

$serviceKey = $_SERVER['PHP_AUTH_USER'];
$loginId = filter_input(INPUT_GET, 'loginId');
$userId = filter_input(INPUT_GET, 'userId');
$instance = filter_input(INPUT_GET, 'instance');

$service_id = ValidateToken($serviceKey);

if ($service_id == "" || $service_id == 'NULL') {
    error_log(PHP_EOL."unauthorize".PHP_EOL,3,$filename);
    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);
} else if ($service_id == 'MANAGEMENT') {
    foreach ($_GET as $key => $value) {
        if ($key != "loginId" && $key != "userId" && $key != "instance") {                 
            http_response_code(403);
            $response = RFResponse(403, 'Incorrect Parameter');
            execute($response);
        }
    }
    $query = "SELECT USER_EMAIL, USER_ID, USER_CLIENT_ID, USER_BASE_URL, USER_LOGIN_NAME FROM  USER_INSTANCE WHERE 1 ";
    if($loginId!=""){
        $query.=" AND USER_LOGIN_NAME='".$loginId."'";
    }
    if($userId!=""){
        $query.=" AND USER_ID=".$userId;
    }
    if($instance!=""){
        $query.=" AND USER_BASE_URL LIKE '%".$instance."%'";
    }
   // echo $query;
    $res = query_list($query);
    $res1 = array();
    if(empty($res)){
        http_response_code(404);
        $response = RFResponse(404, 'Users not found');
        execute($response);
    }else{
        foreach ($res as $result) {
            $result1['email'] = $result['USER_EMAIL'];
            $result1['login-id'] = $result['USER_LOGIN_NAME'];
            $result1['client-id'] = (int) $result['USER_CLIENT_ID'];
            $result1['user-id'] = (int) $result['USER_ID'];
            $result1['client-url'] = $result['USER_BASE_URL'];
            $res1[] = $result1;
        }
        execute($res1);
    }
}else{
    error_log(PHP_EOL."unauthorize".PHP_EOL,3,$filename);
    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);
}

