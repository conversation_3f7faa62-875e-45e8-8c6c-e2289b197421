<?php

/* 
 * To get aurinko token using aurinko api
 * Save update into requeted instace
 * 15.12.2021
 */
//error_reporting(E_ALL);
//ini_set("display_errors", 1);
include "../common/common.php";

$token = filter_input(INPUT_GET, 'token');
$rf_data_json = base64_decode(strtr(filter_input(INPUT_GET, 'rfData'), '-_,', '+/='));
$rf_data = json_decode($rf_data_json);
$username = 'AURINKO';
$password ='RK945-WA28VC79KJ';

$rf_email =urldecode($rf_data->email);
$base_url = getBaseUrlByClientId($rf_data->cid);//getCrmUserData($rf_data->id, $rf_data->cid);
$count =0;
$account_url = "https://api.aurinko.io/v1/account";
$account_resp = recursiveCurlGetBToken($token, $account_url, $count);
$loggedinEmail= urldecode($account_resp['email']);

if(strtolower($loggedinEmail)!=strtolower($rf_email)){
    $status=5;
}else{
    $url = 'https://'. $base_url."/email/access_token";       
    $post_data = json_encode(array('email-id'=> $loggedinEmail, 'token'=>$token));
    $resp = curlPostRequestBasicAuth($username, $password, $url, $post_data);

    if($resp['status']['status-code']=='200'){
        $status =1;
    }else if($resp['status']['status-code']=='409'){
        $status=3;
    }else if($resp['status']['status-code']=='404'){
        $status=4;
    }else{
        $status=0;
    }
}

if($rf_data->tabCode!=""){
    $returnUrl = 'https://'. $base_url . "/RepfabricCRM/UserEnrollment.xhtml?vldCode=".$rf_data->tabCode. "&tabIndex=".$rf_data->tabIndex;
}else{
    $returnUrl = 'https://'. $base_url . "/RepfabricCRM/config/Setup.xhtml?tabIndex=".$rf_data->tabIndex."&usrEnrlId=".$rf_data->id. "&enrollmentStepId=4";
}
$sType=2;
if($rf_data->tabIndex=="3"){
    $sType=1;
}

error_log(PHP_EOL."URL : ". $returnUrl."&loginStatus=".$status."&serviceType=".$sType ,3,"error_log.txt");
//echo $returnUrl."&loginStatus=".$status."&serviceType=".$sType;
header("location:".$returnUrl."&loginStatus=".$status."&serviceType=".$sType);
