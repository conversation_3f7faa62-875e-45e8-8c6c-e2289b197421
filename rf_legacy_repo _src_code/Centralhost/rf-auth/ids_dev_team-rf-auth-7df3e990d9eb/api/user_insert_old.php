<?php

/*
 *  getting the user details and doing insert, update, delete operation.
 * First checking for serviece id, if the requested service id matches then check for aurthentication, once the authentication matches 
 * allow to do insert, delete and update operation.
 */
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

include "../common/common.php";
include_once 'HttpResponseCode.php';
//DB connction
$db = db_connect();


//get the required data
$serviceKey = $_SERVER['PHP_AUTH_USER'];
$auth = $_SERVER['PHP_AUTH_PW'];


$emailId = filter_input(INPUT_GET, 'emailId');
$userId = filter_input(INPUT_GET, 'userId');
$clientId = filter_input(INPUT_GET, 'clientId');
// $serviceKey = filter_input(INPUT_GET,'serviceKey');
// $auth = filter_input(INPUT_GET,'auth');
$method = filter_input(INPUT_GET, 'method');




$service_key = ValidateToken($serviceKey);

if ($service_key > 0) {
    if ($auth == '*RF_KEY@2019*') {
        switch ($method) {

            case 'INS':
                if ($emailId == '' || $userId == '' || $clientId == '') {
                    http_response_code(403);
                    $response = RFResponse(403, 'Bad Request');
                    execute($response);
                }
                if ($emailId) {
                    $baseURL = getBaseURL($clientId);
                    insertUserMail($emailId, $userId, $clientId, $baseURL);
                }
                break;
            case 'UPD':
                if ($emailId == '' || $userId == '' || $clientId == '') {
                    http_response_code(403);
                    $response = RFResponse(403, 'Bad Request');
                    execute($response);
                }

                $qry = "UPDATE USER_INSTANCE SET USER_EMAIL='" . $emailId . "' WHERE USER_CLIENT_ID='" . $clientId . "' AND USER_ID='" . $userId . "' ";
                $res = execute_query($qry);
                http_response_code(200);
                $response = RFResponse(200, 'User Email Updated');
                execute($response);


                break;
            case 'DEL':
                if ($userId == '' || $clientId == '') {
                    http_response_code(403);
                    $response = RFResponse(403, 'Bad Request');
                    execute($response);
                }
                $qry = "DELETE FROM USER_INSTANCE WHERE USER_CLIENT_ID='" . $clientId . "' AND USER_ID='" . $userId . "' ";
                $res = execute_query($qry);
                http_response_code(200);
                $response = RFResponse(200, 'User Deleted');
                execute($response);


                break;

            default:
                break;
        }
    }
} else {
    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);
}

function getBaseURL($clientId) {
    $query = "SELECT INSTANCE_NAME FROM INSTANCES WHERE CLIENT_ID= $clientId";
    $result = query_object($query);
    $baseURL = $result['INSTANCE_NAME'] . '.' . repfabric . '.' . com;
    return $baseURL;
}

function insertUserMail($emailId, $userId, $clientId, $baseURL) {
    $query = "SELECT USER_EMAIL FROM USER_INSTANCE WHERE USER_EMAIL='" . $emailId . "' AND USER_CLIENT_ID='" . $clientId . "'";
    $result = query_check($query);
    if ($result > 0) {
        http_response_code(406);
        $response = RFResponse(406, 'Email Id Already Exist');
        execute($response);
    } else {
        $qry = "INSERT INTO USER_INSTANCE (USER_EMAIL,USER_ID,USER_CLIENT_ID,USER_BASE_URL) VALUES('" . $emailId . "','" . $userId . "','" . $clientId . "','" . $baseURL . "')";

        $res = execute_query($qry);
        http_response_code(200);
        $response = RFResponse(200, 'User Added Successfully');
        execute($response);
    }
}

