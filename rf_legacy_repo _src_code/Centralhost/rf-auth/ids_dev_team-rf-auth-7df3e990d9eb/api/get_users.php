<?php 
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

include "../common/common.php";
include_once 'HttpResponseCode.php';
//DB connction
$db=db_connect();

 $user =$_SERVER['PHP_AUTH_USER'];
 $pass =$_SERVER['PHP_AUTH_PW'];
//get the clientId and user detail from user and pharms table

 $result= array();
 $query = "SELECT u.USER_EMAIL, u.USER_ID, p.PARAM_SUBS_ID FROM USERS u, PARAMS p"; 
 $res = query_list($query);

 //echo json_decode($res);

 echo json_encode($res);

?>