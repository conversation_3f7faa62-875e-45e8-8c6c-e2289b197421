<?php

/*
 * To authenticate users from all repfabric instances
 * Created On 22.02.2019
 * 
 */

//error_reporting(E_ALL);
//ini_set('display_errors', 1);

include "../common/common.php";
include_once 'HttpResponseCode.php';
include "../common/sync_instnace.php";

//DB connction
$db = db_connect();

//get emailid and service key
$serviceKey = $_SERVER['PHP_AUTH_USER'];
$emailId = filter_input(INPUT_GET, 'emailId');
$password = $_SERVER['PHP_AUTH_PW'];
$service_id = ValidateToken($password);

if ($service_id == "" || $service_id == 'NULL') {
    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);
} else if ($service_id == 'AURINKO' && strtolower($serviceKey) == "aurinko") {
    if ($emailId != '') {
        $query = "SELECT USER_EMAIL, USER_ID, USER_CLIENT_ID, USER_BASE_URL, USER_LOGIN_NAME FROM  USER_INSTANCE WHERE USER_EMAIL='" . $emailId . "' ";
        $res = query_list($query);
        $res1 = array();
        if (empty($res)) {
            http_response_code(200);
            //$response = RFResponse(404, 'User not found');
            //$response = new stdClass();
            $response = [];
            execute($response);
        } else {
            foreach ($res as $result) {
                //var_dump($result);
                $resp = call_instance_sync_service('AURINKO', $emailId, $service_id, $result['USER_BASE_URL'], $password, $result['USER_ID']);
                $aurinko_data = json_decode($resp, TRUE);
                //var_dump($aurinko_data);
                if ($aurinko_data['Token Data']['ACCT_SYNC_ENABLED'] == '1') {
                    $sync = true;
                } else {
                    $sync = false;
                }
                $usertoken = "";
                if (isset($aurinko_data['Token Data']['ACCT_ACCESS_TOKEN'])) {
                    $usertoken = $aurinko_data['Token Data']['ACCT_ACCESS_TOKEN'];
                }
                $resultNew[] = array('email' => $emailId,
                    'login-id' => $result['USER_LOGIN_NAME'],
                    'client-id' => (int) $result['USER_CLIENT_ID'],
                    'client-url' => $result['USER_BASE_URL'],
                    'user-id' => (int) $result['USER_ID'],
                    'sync-enabled' => $sync,
                    'user-token' => $usertoken
                );
            }
            // if(query_check($query)=="1"){
            execute($resultNew);
            //}else{
            // $response22=['Records'=>$resultNew];
            //execute($response22);
            // }
        }
    } else {
        http_response_code(404);
        $response = RFResponse(404, 'EmailId not found');
        execute($response);
    }
} else {
    http_response_code(404);
    $response = RFResponse(404, 'Unauthorized access');
    execute($response);
}


