<?php
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

include "../common/common.php";
include_once 'HttpResponseCode.php';

$serviceKey = $_SERVER['PHP_AUTH_USER'];
$emailId = filter_input(INPUT_GET, 'emailId');
$password = $_SERVER['PHP_AUTH_PW'];

$service_id = ValidateToken($serviceKey);

if ($service_id == "" || $service_id == 'NULL') {
    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);
} else if ($service_id == 'MOBAPP' || $service_id=='CRMPORTAL') {
    $query = "SELECT REC_ID, TUTORIAL_TAG_ID, TUTORIAL_TAG, TUTORIAL_SUB_TAG_ID, TUTORIAL_SUB_TAG, "
            . "TUTORIAL_TITLE, TUTORIAL_URL FROM TUTORIALS_MST WHERE 1";
    $results = query_list($query);
    if(empty($results)){
        http_response_code(404);
        $response = RFResponse(404, 'No data found');
        execute($response);
    }else{
        $tut_res=[];
        foreach ($results as $res){
            $tuts=[
                "id"=>intval($res['REC_ID']),
                "tag-id"=>intval($res['TUTORIAL_TAG_ID']),
                "tag"=>$res['TUTORIAL_TAG'],
                "sub-tag-id"=>intval($res['TUTORIAL_SUB_TAG_ID']),
                "sub-tag"=>$res['TUTORIAL_SUB_TAG'],
                "title"=>$res['TUTORIAL_TITLE'],
                "url"=>$res['TUTORIAL_URL'],
                ];
            $tut_res[]=$tuts;
        }
        execute($tut_res);
    }
}else{
    http_response_code(401);
    $response = RFResponse(401, 'Unauthorized Access');
    execute($response);
}
    